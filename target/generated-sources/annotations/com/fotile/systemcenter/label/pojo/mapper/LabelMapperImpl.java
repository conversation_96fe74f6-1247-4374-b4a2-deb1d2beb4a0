package com.fotile.systemcenter.label.pojo.mapper;

import com.fotile.systemcenter.label.pojo.dto.FindLabelByNameInDto;
import com.fotile.systemcenter.label.pojo.dto.FindLabelByNameOutDto;
import com.fotile.systemcenter.label.pojo.entity.Label;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-06-28T09:53:54+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class LabelMapperImpl implements LabelMapper {

    @Override
    public FindLabelByNameOutDto findLabelByNameOutDtoToLabel(Label label) {
        if ( label == null ) {
            return null;
        }

        FindLabelByNameOutDto findLabelByNameOutDto = new FindLabelByNameOutDto();

        findLabelByNameOutDto.setName( label.getName() );
        findLabelByNameOutDto.setSourceTableName( label.getSourceTableName() );
        findLabelByNameOutDto.setId( label.getId() );

        return findLabelByNameOutDto;
    }

    @Override
    public Label labelToFindLabelNameInDto(FindLabelByNameInDto findLabelByNameInDto) {
        if ( findLabelByNameInDto == null ) {
            return null;
        }

        Label label = new Label();

        label.setId( findLabelByNameInDto.getId() );
        label.setName( findLabelByNameInDto.getName() );
        label.setSourceTableName( findLabelByNameInDto.getSourceTableName() );

        return label;
    }
}
