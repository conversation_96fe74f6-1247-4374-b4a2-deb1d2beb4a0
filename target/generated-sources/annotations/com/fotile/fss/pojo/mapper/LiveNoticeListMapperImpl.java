package com.fotile.fss.pojo.mapper;

import com.fotile.fss.pojo.dto.live.LiveNoticeListInsertDTO;
import com.fotile.fss.pojo.dto.live.LiveNoticeListSelectDTO;
import com.fotile.fss.pojo.dto.live.LiveNoticeListSetDTO;
import com.fotile.fss.pojo.entity.live.LiveNoticeList;
import com.fotile.fss.pojo.entity.live.LiveNoticeList.LiveNoticeListBuilder;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-02-18T17:59:40+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.41.0.z20250115-2156, environment: Java 21.0.5 (Eclipse Adoptium)"
)
public class LiveNoticeListMapperImpl implements LiveNoticeListMapper {

    @Override
    public LiveNoticeList LiveNoticeListInsertDTOTOLiveNoticeList(LiveNoticeListInsertDTO liveNoticeListInsertDTO) {
        if ( liveNoticeListInsertDTO == null ) {
            return null;
        }

        LiveNoticeListBuilder liveNoticeList = LiveNoticeList.builder();

        liveNoticeList.appInfoId( liveNoticeListInsertDTO.getAppInfoId() );
        liveNoticeList.liveEndDate( liveNoticeListInsertDTO.getLiveEndDate() );
        liveNoticeList.liveStartDate( liveNoticeListInsertDTO.getLiveStartDate() );
        liveNoticeList.locationAddress( liveNoticeListInsertDTO.getLocationAddress() );
        liveNoticeList.logo( liveNoticeListInsertDTO.getLogo() );
        liveNoticeList.masterUserId( liveNoticeListInsertDTO.getMasterUserId() );
        liveNoticeList.masterUserName( liveNoticeListInsertDTO.getMasterUserName() );
        liveNoticeList.mobile( liveNoticeListInsertDTO.getMobile() );
        liveNoticeList.siteId( liveNoticeListInsertDTO.getSiteId() );
        liveNoticeList.siteName( liveNoticeListInsertDTO.getSiteName() );
        liveNoticeList.state( liveNoticeListInsertDTO.getState() );
        liveNoticeList.storeId( liveNoticeListInsertDTO.getStoreId() );
        liveNoticeList.storeName( liveNoticeListInsertDTO.getStoreName() );
        liveNoticeList.title( liveNoticeListInsertDTO.getTitle() );

        return liveNoticeList.build();
    }

    @Override
    public LiveNoticeListSelectDTO LiveNoticeListTOLiveNoticeListUpdateAndSelectDTO(LiveNoticeList liveNoticeList) {
        if ( liveNoticeList == null ) {
            return null;
        }

        LiveNoticeListSelectDTO liveNoticeListSelectDTO = new LiveNoticeListSelectDTO();

        liveNoticeListSelectDTO.setAppInfoId( liveNoticeList.getAppInfoId() );
        liveNoticeListSelectDTO.setLocationAddress( liveNoticeList.getLocationAddress() );
        liveNoticeListSelectDTO.setLogo( liveNoticeList.getLogo() );
        liveNoticeListSelectDTO.setMasterUserId( liveNoticeList.getMasterUserId() );
        liveNoticeListSelectDTO.setMasterUserName( liveNoticeList.getMasterUserName() );
        liveNoticeListSelectDTO.setMobile( liveNoticeList.getMobile() );
        liveNoticeListSelectDTO.setSiteId( liveNoticeList.getSiteId() );
        liveNoticeListSelectDTO.setSiteName( liveNoticeList.getSiteName() );
        liveNoticeListSelectDTO.setState( liveNoticeList.getState() );
        liveNoticeListSelectDTO.setStoreId( liveNoticeList.getStoreId() );
        liveNoticeListSelectDTO.setStoreName( liveNoticeList.getStoreName() );
        liveNoticeListSelectDTO.setTitle( liveNoticeList.getTitle() );

        return liveNoticeListSelectDTO;
    }

    @Override
    public LiveNoticeList LiveNoticeListSetDTOTOLiveNoticeList(LiveNoticeListSetDTO liveNoticeListSetDTO) {
        if ( liveNoticeListSetDTO == null ) {
            return null;
        }

        LiveNoticeListBuilder liveNoticeList = LiveNoticeList.builder();

        liveNoticeList.locationAddress( liveNoticeListSetDTO.getLocationAddress() );
        liveNoticeList.masterUserId( liveNoticeListSetDTO.getMasterUserId() );
        liveNoticeList.masterUserName( liveNoticeListSetDTO.getMasterUserName() );
        liveNoticeList.mobile( liveNoticeListSetDTO.getMobile() );
        liveNoticeList.state( liveNoticeListSetDTO.getState() );
        liveNoticeList.storeId( liveNoticeListSetDTO.getStoreId() );
        liveNoticeList.storeName( liveNoticeListSetDTO.getStoreName() );

        return liveNoticeList.build();
    }
}
