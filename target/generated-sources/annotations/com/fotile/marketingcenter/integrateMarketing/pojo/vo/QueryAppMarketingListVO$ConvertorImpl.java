package com.fotile.marketingcenter.integrateMarketing.pojo.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.marketingcenter.integrateMarketing.pojo.entity.ImIntegrateMarketingEntity;
import com.fotile.marketingcenter.integrateMarketing.pojo.vo.QueryAppMarketingListVO.Convertor;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T11:06:42+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
public class QueryAppMarketingListVO$ConvertorImpl implements Convertor {

    @Override
    public ImIntegrateMarketingEntity to(QueryAppMarketingListVO target) {
        if ( target == null ) {
            return null;
        }

        ImIntegrateMarketingEntity imIntegrateMarketingEntity = new ImIntegrateMarketingEntity();

        imIntegrateMarketingEntity.setId( target.getId() );
        imIntegrateMarketingEntity.setCreatedDate( target.getCreatedDate() );
        imIntegrateMarketingEntity.setIntegrateCode( target.getIntegrateCode() );
        imIntegrateMarketingEntity.setAuditStatus( target.getAuditStatus() );
        imIntegrateMarketingEntity.setOnlineStatus( target.getOnlineStatus() );
        imIntegrateMarketingEntity.setOnlineTime( target.getOnlineTime() );
        imIntegrateMarketingEntity.setOfflineTime( target.getOfflineTime() );
        imIntegrateMarketingEntity.setIntegrateType( target.getIntegrateType() );
        imIntegrateMarketingEntity.setIntegrateSecondTitle( target.getIntegrateSecondTitle() );
        imIntegrateMarketingEntity.setCoverUrl( target.getCoverUrl() );
        imIntegrateMarketingEntity.setPolicyComment( target.getPolicyComment() );
        imIntegrateMarketingEntity.setValidStartTime( target.getValidStartTime() );
        imIntegrateMarketingEntity.setValidEndTime( target.getValidEndTime() );
        imIntegrateMarketingEntity.setCreateSourceType( target.getCreateSourceType() );
        imIntegrateMarketingEntity.setLandingApplet( target.getLandingApplet() );

        return imIntegrateMarketingEntity;
    }

    @Override
    public PageInfo<QueryAppMarketingListVO> toPageInfo(PageInfo<ImIntegrateMarketingEntity> entityPageInfo) {
        if ( entityPageInfo == null ) {
            return null;
        }

        PageInfo<QueryAppMarketingListVO> pageInfo = new PageInfo<QueryAppMarketingListVO>();

        pageInfo.setPages( entityPageInfo.getPages() );
        pageInfo.setRecords( from( entityPageInfo.getRecords() ) );
        pageInfo.setTotal( entityPageInfo.getTotal() );
        pageInfo.setSize( entityPageInfo.getSize() );
        pageInfo.setCurrent( entityPageInfo.getCurrent() );
        List<OrderItem> list1 = entityPageInfo.getOrders();
        if ( list1 != null ) {
            pageInfo.setOrders( new ArrayList<OrderItem>( list1 ) );
        }
        pageInfo.setSearchCount( entityPageInfo.isSearchCount() );
        if ( pageInfo.getList() != null ) {
            List<QueryAppMarketingListVO> list2 = from( entityPageInfo.getList() );
            if ( list2 != null ) {
                pageInfo.getList().addAll( list2 );
            }
        }

        return pageInfo;
    }

    @Override
    public List<QueryAppMarketingListVO> from(List<ImIntegrateMarketingEntity> entityPageInfo) {
        if ( entityPageInfo == null ) {
            return null;
        }

        List<QueryAppMarketingListVO> list = new ArrayList<QueryAppMarketingListVO>( entityPageInfo.size() );
        for ( ImIntegrateMarketingEntity imIntegrateMarketingEntity : entityPageInfo ) {
            list.add( from( imIntegrateMarketingEntity ) );
        }

        return list;
    }

    @Override
    public QueryAppMarketingListVO from(ImIntegrateMarketingEntity entity) {
        if ( entity == null ) {
            return null;
        }

        QueryAppMarketingListVO queryAppMarketingListVO = new QueryAppMarketingListVO();

        queryAppMarketingListVO.setTitle( entity.getIntegrateTitle() );
        queryAppMarketingListVO.setId( entity.getId() );
        queryAppMarketingListVO.setIntegrateCode( entity.getIntegrateCode() );
        queryAppMarketingListVO.setIntegrateType( entity.getIntegrateType() );
        queryAppMarketingListVO.setValidStartTime( entity.getValidStartTime() );
        queryAppMarketingListVO.setOnlineStatus( entity.getOnlineStatus() );
        queryAppMarketingListVO.setValidEndTime( entity.getValidEndTime() );
        queryAppMarketingListVO.setOnlineTime( entity.getOnlineTime() );
        queryAppMarketingListVO.setOfflineTime( entity.getOfflineTime() );
        queryAppMarketingListVO.setPolicyComment( entity.getPolicyComment() );
        queryAppMarketingListVO.setIntegrateSecondTitle( entity.getIntegrateSecondTitle() );
        queryAppMarketingListVO.setCreatedDate( entity.getCreatedDate() );
        queryAppMarketingListVO.setCoverUrl( entity.getCoverUrl() );
        queryAppMarketingListVO.setCreateSourceType( entity.getCreateSourceType() );
        queryAppMarketingListVO.setLandingApplet( entity.getLandingApplet() );
        queryAppMarketingListVO.setAuditStatus( entity.getAuditStatus() );

        return queryAppMarketingListVO;
    }
}
