package com.fotile.marketingcenter.integrateMarketing.pojo.dto;

import com.fotile.marketingcenter.integrateMarketing.pojo.dto.AddReadRecordDTO.Convertor;
import com.fotile.marketingcenter.integrateMarketing.pojo.entity.ImContentReadRecordEntity;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T11:06:42+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
public class AddReadRecordDTO$ConvertorImpl implements Convertor {

    @Override
    public ImContentReadRecordEntity from(AddReadRecordDTO source) {
        if ( source == null ) {
            return null;
        }

        ImContentReadRecordEntity imContentReadRecordEntity = new ImContentReadRecordEntity();

        imContentReadRecordEntity.setType( source.getType() );
        imContentReadRecordEntity.setCommonContentId( source.getCommonContentId() );
        imContentReadRecordEntity.setPayActivityId( source.getPayActivityId() );
        imContentReadRecordEntity.setTemplateId( source.getTemplateId() );
        imContentReadRecordEntity.setReadUser( source.getReadUser() );
        imContentReadRecordEntity.setSalesmanId( source.getSalesmanId() );
        imContentReadRecordEntity.setPlatformType( source.getPlatformType() );
        imContentReadRecordEntity.setSubPlatformType( source.getSubPlatformType() );
        imContentReadRecordEntity.setPhone( source.getPhone() );
        imContentReadRecordEntity.setChannelType( source.getChannelType() );
        imContentReadRecordEntity.setChannelCode( source.getChannelCode() );

        return imContentReadRecordEntity;
    }

    @Override
    public AddReadRecordDTO to(ImContentReadRecordEntity target) {
        if ( target == null ) {
            return null;
        }

        AddReadRecordDTO addReadRecordDTO = new AddReadRecordDTO();

        addReadRecordDTO.setType( target.getType() );
        addReadRecordDTO.setCommonContentId( target.getCommonContentId() );
        addReadRecordDTO.setPayActivityId( target.getPayActivityId() );
        addReadRecordDTO.setTemplateId( target.getTemplateId() );
        addReadRecordDTO.setReadUser( target.getReadUser() );
        addReadRecordDTO.setSalesmanId( target.getSalesmanId() );
        addReadRecordDTO.setPlatformType( target.getPlatformType() );
        addReadRecordDTO.setSubPlatformType( target.getSubPlatformType() );
        addReadRecordDTO.setPhone( target.getPhone() );
        addReadRecordDTO.setChannelType( target.getChannelType() );
        addReadRecordDTO.setChannelCode( target.getChannelCode() );

        return addReadRecordDTO;
    }
}
