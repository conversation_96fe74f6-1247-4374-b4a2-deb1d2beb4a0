package com.fotile.marketingcenter.userClues.transfer;

import com.fotile.marketingcenter.userClues.pojo.dto.LeaveFundsAdDetailDTO;
import com.fotile.marketingcenter.userClues.pojo.entity.LeaveFundsAdDetail;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-05T11:06:42+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class LeaveFundsAdDetailTransferImpl implements LeaveFundsAdDetailTransfer {

    @Override
    public LeaveFundsAdDetail dtoToDomain(LeaveFundsAdDetailDTO leaveFundsAdDetailDTO) {
        if ( leaveFundsAdDetailDTO == null ) {
            return null;
        }

        LeaveFundsAdDetail leaveFundsAdDetail = new LeaveFundsAdDetail();

        leaveFundsAdDetail.setId( leaveFundsAdDetailDTO.getId() );
        leaveFundsAdDetail.setIsDeleted( leaveFundsAdDetailDTO.getIsDeleted() );
        leaveFundsAdDetail.setCreatedBy( leaveFundsAdDetailDTO.getCreatedBy() );
        leaveFundsAdDetail.setModifiedBy( leaveFundsAdDetailDTO.getModifiedBy() );
        leaveFundsAdDetail.setCreatedDate( leaveFundsAdDetailDTO.getCreatedDate() );
        leaveFundsAdDetail.setModifiedDate( leaveFundsAdDetailDTO.getModifiedDate() );
        leaveFundsAdDetail.setWechatOpenid( leaveFundsAdDetailDTO.getWechatOpenid() );
        leaveFundsAdDetail.setWechatUnionid( leaveFundsAdDetailDTO.getWechatUnionid() );
        leaveFundsAdDetail.setWechatAppId( leaveFundsAdDetailDTO.getWechatAppId() );
        leaveFundsAdDetail.setActionType( leaveFundsAdDetailDTO.getActionType() );
        leaveFundsAdDetail.setClickId( leaveFundsAdDetailDTO.getClickId() );

        return leaveFundsAdDetail;
    }

    @Override
    public List<LeaveFundsAdDetail> dtoToDomain(List<LeaveFundsAdDetailDTO> leaveFundsAdDetailDTO) {
        if ( leaveFundsAdDetailDTO == null ) {
            return null;
        }

        List<LeaveFundsAdDetail> list = new ArrayList<LeaveFundsAdDetail>( leaveFundsAdDetailDTO.size() );
        for ( LeaveFundsAdDetailDTO leaveFundsAdDetailDTO1 : leaveFundsAdDetailDTO ) {
            list.add( dtoToDomain( leaveFundsAdDetailDTO1 ) );
        }

        return list;
    }

    @Override
    public LeaveFundsAdDetailDTO domainToDto(LeaveFundsAdDetail leaveFundsAdDetail) {
        if ( leaveFundsAdDetail == null ) {
            return null;
        }

        LeaveFundsAdDetailDTO leaveFundsAdDetailDTO = new LeaveFundsAdDetailDTO();

        leaveFundsAdDetailDTO.setId( leaveFundsAdDetail.getId() );
        leaveFundsAdDetailDTO.setIsDeleted( leaveFundsAdDetail.getIsDeleted() );
        leaveFundsAdDetailDTO.setCreatedBy( leaveFundsAdDetail.getCreatedBy() );
        leaveFundsAdDetailDTO.setModifiedBy( leaveFundsAdDetail.getModifiedBy() );
        leaveFundsAdDetailDTO.setCreatedDate( leaveFundsAdDetail.getCreatedDate() );
        leaveFundsAdDetailDTO.setModifiedDate( leaveFundsAdDetail.getModifiedDate() );
        leaveFundsAdDetailDTO.setWechatOpenid( leaveFundsAdDetail.getWechatOpenid() );
        leaveFundsAdDetailDTO.setWechatUnionid( leaveFundsAdDetail.getWechatUnionid() );
        leaveFundsAdDetailDTO.setWechatAppId( leaveFundsAdDetail.getWechatAppId() );
        leaveFundsAdDetailDTO.setActionType( leaveFundsAdDetail.getActionType() );
        leaveFundsAdDetailDTO.setClickId( leaveFundsAdDetail.getClickId() );

        return leaveFundsAdDetailDTO;
    }

    @Override
    public List<LeaveFundsAdDetailDTO> domainToDto(List<LeaveFundsAdDetail> leaveFundsAdDetail) {
        if ( leaveFundsAdDetail == null ) {
            return null;
        }

        List<LeaveFundsAdDetailDTO> list = new ArrayList<LeaveFundsAdDetailDTO>( leaveFundsAdDetail.size() );
        for ( LeaveFundsAdDetail leaveFundsAdDetail1 : leaveFundsAdDetail ) {
            list.add( domainToDto( leaveFundsAdDetail1 ) );
        }

        return list;
    }
}
