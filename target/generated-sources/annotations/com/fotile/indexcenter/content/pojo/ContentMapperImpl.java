package com.fotile.indexcenter.content.pojo;

import com.fotile.indexcenter.content.pojo.dto.ContentInfoFromDB;
import com.fotile.indexcenter.content.pojo.dto.ESContentFavorite;
import com.fotile.indexcenter.content.pojo.dto.ESMaterialContent;
import com.fotile.indexcenter.content.pojo.dto.ESMaterialFusionInfo;
import com.fotile.indexcenter.content.pojo.entity.CmsContentFavoriteMapping;
import com.fotile.indexcenter.content.pojo.entity.SpecialInfo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-02-18T17:59:48+0800",
    comments = "version: 1.4.1.Final, compiler: Eclipse JDT (IDE) 3.41.0.z20250115-2156, environment: Java 21.0.5 (Eclipse Adoptium)"
)
@Component
public class ContentMapperImpl implements ContentMapper {

    @Override
    public ESMaterialContent dbContent2ESContent(ContentInfoFromDB db) {
        if ( db == null ) {
            return null;
        }

        ESMaterialContent eSMaterialContent = new ESMaterialContent();

        eSMaterialContent.setTags( str2List( db.getTags() ) );
        eSMaterialContent.setCaseProperties( str2List( db.getCaseProperties() ) );
        eSMaterialContent.setCaseType( db.getCaseType() );
        eSMaterialContent.setContent( db.getContent() );
        eSMaterialContent.setContentId( db.getContentId() );
        eSMaterialContent.setLinkDescription( db.getLinkDescription() );
        eSMaterialContent.setMiniProgram( db.getMiniProgram() );
        eSMaterialContent.setProductModels( str2List( db.getProductModels() ) );
        eSMaterialContent.setProductNames( str2List( db.getProductNames() ) );
        eSMaterialContent.setContentTitle( db.getContentTitle() );
        eSMaterialContent.setContentType( db.getContentType() );

        return eSMaterialContent;
    }

    @Override
    public ESMaterialFusionInfo createESMaterialFusion(SpecialInfo entity) {
        if ( entity == null ) {
            return null;
        }

        ESMaterialFusionInfo eSMaterialFusionInfo = new ESMaterialFusionInfo();

        eSMaterialFusionInfo.setOriginalId( entity.getId() );
        eSMaterialFusionInfo.setCreateTime( entity.getCreatedDate() );
        eSMaterialFusionInfo.setTitle( entity.getTitle() );
        eSMaterialFusionInfo.setEffectiveStartTime( entity.getValidStartTime() );
        eSMaterialFusionInfo.setEffectiveEndTime( entity.getValidEndTime() );

        eSMaterialFusionInfo.setId( ContentMapper.getSpecialInfoESId(entity.getId()) );

        return eSMaterialFusionInfo;
    }

    @Override
    public ESContentFavorite createESContentFavorite(CmsContentFavoriteMapping entity) {
        if ( entity == null ) {
            return null;
        }

        ESContentFavorite eSContentFavorite = new ESContentFavorite();

        eSContentFavorite.setFavStatus( entity.getFavStatus() );
        if ( entity.getId() != null ) {
            eSContentFavorite.setId( String.valueOf( entity.getId() ) );
        }
        eSContentFavorite.setUserId( entity.getUserId() );

        eSContentFavorite.setCollectionTime( this.getCollectionTime(entity) );

        return eSContentFavorite;
    }
}
