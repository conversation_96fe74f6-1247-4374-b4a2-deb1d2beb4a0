package com.fotile.indexcenter.marketingcenter.userclues.pojo.dto;

import com.fotile.indexcenter.marketingcenter.userclues.pojo.dto.QuerySalesLeadsOutDto.Convertor;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2024-05-28T10:11:37+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
public class QuerySalesLeadsOutDto$ConvertorImpl implements Convertor {

    @Override
    public QuerySalesLeadsOutDto from(QuerySalesLeadsOutDto source) {
        if ( source == null ) {
            return null;
        }

        QuerySalesLeadsOutDto querySalesLeadsOutDto = new QuerySalesLeadsOutDto();

        querySalesLeadsOutDto.setDecorateProgres( source.getDecorateProgres() );
        querySalesLeadsOutDto.setTotalScore( source.getTotalScore() );
        querySalesLeadsOutDto.setFollowUpEndTime( source.getFollowUpEndTime() );
        querySalesLeadsOutDto.setFollowUpUntreatedTime( source.getFollowUpUntreatedTime() );
        querySalesLeadsOutDto.setProvinceId( source.getProvinceId() );
        querySalesLeadsOutDto.setCityId( source.getCityId() );
        querySalesLeadsOutDto.setCountyId( source.getCountyId() );
        querySalesLeadsOutDto.setProvinceName( source.getProvinceName() );
        querySalesLeadsOutDto.setCityName( source.getCityName() );
        querySalesLeadsOutDto.setCountyName( source.getCountyName() );
        querySalesLeadsOutDto.setAddress( source.getAddress() );
        querySalesLeadsOutDto.setChargeUserId( source.getChargeUserId() );
        querySalesLeadsOutDto.setAddressId( source.getAddressId() );
        querySalesLeadsOutDto.setUserCluesId( source.getUserCluesId() );
        querySalesLeadsOutDto.setIsAssist( source.getIsAssist() );
        querySalesLeadsOutDto.setContentText( source.getContentText() );
        querySalesLeadsOutDto.setOrgId( source.getOrgId() );
        querySalesLeadsOutDto.setAbbreviation( source.getAbbreviation() );
        querySalesLeadsOutDto.setAssistUserName( source.getAssistUserName() );
        querySalesLeadsOutDto.setAssistUserId( source.getAssistUserId() );
        querySalesLeadsOutDto.setServiceAction( source.getServiceAction() );
        querySalesLeadsOutDto.setVillageId( source.getVillageId() );
        querySalesLeadsOutDto.setCluesSource( source.getCluesSource() );
        querySalesLeadsOutDto.setCluesSourceValue( source.getCluesSourceValue() );
        querySalesLeadsOutDto.setDecorateDesignerJson( source.getDecorateDesignerJson() );
        querySalesLeadsOutDto.setDecorateDesignerId( source.getDecorateDesignerId() );
        querySalesLeadsOutDto.setVillageName( source.getVillageName() );
        querySalesLeadsOutDto.setIsIntoStores( source.getIsIntoStores() );
        querySalesLeadsOutDto.setIsMakeBargain( source.getIsMakeBargain() );
        querySalesLeadsOutDto.setIsFriend( source.getIsFriend() );
        querySalesLeadsOutDto.setExternalUserid( source.getExternalUserid() );
        querySalesLeadsOutDto.setFollowCount( source.getFollowCount() );
        querySalesLeadsOutDto.setGender( source.getGender() );
        querySalesLeadsOutDto.setHouseType( source.getHouseType() );
        querySalesLeadsOutDto.setDecorateType( source.getDecorateType() );
        querySalesLeadsOutDto.setLastFollowTime( source.getLastFollowTime() );
        querySalesLeadsOutDto.setCreateUserId( source.getCreateUserId() );
        querySalesLeadsOutDto.setCreateUserName( source.getCreateUserName() );
        querySalesLeadsOutDto.setBuilding( source.getBuilding() );
        querySalesLeadsOutDto.setUnit( source.getUnit() );
        querySalesLeadsOutDto.setHouseNumber( source.getHouseNumber() );
        querySalesLeadsOutDto.setUserAddressType( source.getUserAddressType() );
        querySalesLeadsOutDto.setSalesmanName( source.getSalesmanName() );
        querySalesLeadsOutDto.setCluesLevel( source.getCluesLevel() );
        querySalesLeadsOutDto.setCluesLevelValue( source.getCluesLevelValue() );
        querySalesLeadsOutDto.setColor( source.getColor() );
        querySalesLeadsOutDto.setId( source.getId() );
        querySalesLeadsOutDto.setCustomerName( source.getCustomerName() );
        querySalesLeadsOutDto.setCustomerPhone( source.getCustomerPhone() );
        querySalesLeadsOutDto.setCreatedDate( source.getCreatedDate() );
        querySalesLeadsOutDto.setCreatedBy( source.getCreatedBy() );
        querySalesLeadsOutDto.setCompanyId( source.getCompanyId() );
        querySalesLeadsOutDto.setStroeId( source.getStroeId() );
        querySalesLeadsOutDto.setStroeCode( source.getStroeCode() );
        querySalesLeadsOutDto.setStroeName( source.getStroeName() );
        querySalesLeadsOutDto.setActivityId( source.getActivityId() );
        querySalesLeadsOutDto.setActivityName( source.getActivityName() );
        querySalesLeadsOutDto.setChargeUserName( source.getChargeUserName() );
        querySalesLeadsOutDto.setCompanyName( source.getCompanyName() );
        querySalesLeadsOutDto.setStatus( source.getStatus() );
        querySalesLeadsOutDto.setUnderTheLineRevisitDays( source.getUnderTheLineRevisitDays() );
        querySalesLeadsOutDto.setOnLineRevisitDays( source.getOnLineRevisitDays() );
        querySalesLeadsOutDto.setCustomServiceCode( source.getCustomServiceCode() );
        querySalesLeadsOutDto.setChargeCode( source.getChargeCode() );
        querySalesLeadsOutDto.setFollowUpStatus( source.getFollowUpStatus() );
        querySalesLeadsOutDto.setVisitTime( source.getVisitTime() );
        querySalesLeadsOutDto.setUtmSource( source.getUtmSource() );
        querySalesLeadsOutDto.setAuditStatus( source.getAuditStatus() );
        querySalesLeadsOutDto.setFundingTime( source.getFundingTime() );
        querySalesLeadsOutDto.setProvinceCode( source.getProvinceCode() );
        querySalesLeadsOutDto.setProvince( source.getProvince() );
        querySalesLeadsOutDto.setCityCode( source.getCityCode() );
        querySalesLeadsOutDto.setCity( source.getCity() );
        querySalesLeadsOutDto.setAreaCode( source.getAreaCode() );
        querySalesLeadsOutDto.setArea( source.getArea() );
        querySalesLeadsOutDto.setStoreTypeCode( source.getStoreTypeCode() );
        querySalesLeadsOutDto.setStoreTypeName( source.getStoreTypeName() );
        querySalesLeadsOutDto.setIsComeDevise( source.getIsComeDevise() );
        querySalesLeadsOutDto.setChannelCategoryCode( source.getChannelCategoryCode() );
        querySalesLeadsOutDto.setChannelCategoryName( source.getChannelCategoryName() );
        querySalesLeadsOutDto.setChannelSubdivideCode( source.getChannelSubdivideCode() );
        querySalesLeadsOutDto.setChannelSubdivideName( source.getChannelSubdivideName() );
        querySalesLeadsOutDto.setReviseTime( source.getReviseTime() );
        querySalesLeadsOutDto.setScreenshots( source.getScreenshots() );
        querySalesLeadsOutDto.setAuditTime( source.getAuditTime() );
        querySalesLeadsOutDto.setFollowEfficiencyCategoryValue( source.getFollowEfficiencyCategoryValue() );
        querySalesLeadsOutDto.setFollowEfficiencyTime( source.getFollowEfficiencyTime() );
        querySalesLeadsOutDto.setDecorateCompanyName( source.getDecorateCompanyName() );
        querySalesLeadsOutDto.setDesignerName( source.getDesignerName() );
        querySalesLeadsOutDto.setDecorateCompanyType( source.getDecorateCompanyType() );

        return querySalesLeadsOutDto;
    }

    @Override
    public QuerySalesLeadsOutDto to(QuerySalesLeadsOutDto target) {
        if ( target == null ) {
            return null;
        }

        QuerySalesLeadsOutDto querySalesLeadsOutDto = new QuerySalesLeadsOutDto();

        querySalesLeadsOutDto.setDecorateProgres( target.getDecorateProgres() );
        querySalesLeadsOutDto.setTotalScore( target.getTotalScore() );
        querySalesLeadsOutDto.setFollowUpEndTime( target.getFollowUpEndTime() );
        querySalesLeadsOutDto.setFollowUpUntreatedTime( target.getFollowUpUntreatedTime() );
        querySalesLeadsOutDto.setProvinceId( target.getProvinceId() );
        querySalesLeadsOutDto.setCityId( target.getCityId() );
        querySalesLeadsOutDto.setCountyId( target.getCountyId() );
        querySalesLeadsOutDto.setProvinceName( target.getProvinceName() );
        querySalesLeadsOutDto.setCityName( target.getCityName() );
        querySalesLeadsOutDto.setCountyName( target.getCountyName() );
        querySalesLeadsOutDto.setAddress( target.getAddress() );
        querySalesLeadsOutDto.setChargeUserId( target.getChargeUserId() );
        querySalesLeadsOutDto.setAddressId( target.getAddressId() );
        querySalesLeadsOutDto.setUserCluesId( target.getUserCluesId() );
        querySalesLeadsOutDto.setIsAssist( target.getIsAssist() );
        querySalesLeadsOutDto.setContentText( target.getContentText() );
        querySalesLeadsOutDto.setOrgId( target.getOrgId() );
        querySalesLeadsOutDto.setAbbreviation( target.getAbbreviation() );
        querySalesLeadsOutDto.setAssistUserName( target.getAssistUserName() );
        querySalesLeadsOutDto.setAssistUserId( target.getAssistUserId() );
        querySalesLeadsOutDto.setServiceAction( target.getServiceAction() );
        querySalesLeadsOutDto.setVillageId( target.getVillageId() );
        querySalesLeadsOutDto.setCluesSource( target.getCluesSource() );
        querySalesLeadsOutDto.setCluesSourceValue( target.getCluesSourceValue() );
        querySalesLeadsOutDto.setDecorateDesignerJson( target.getDecorateDesignerJson() );
        querySalesLeadsOutDto.setDecorateDesignerId( target.getDecorateDesignerId() );
        querySalesLeadsOutDto.setVillageName( target.getVillageName() );
        querySalesLeadsOutDto.setIsIntoStores( target.getIsIntoStores() );
        querySalesLeadsOutDto.setIsMakeBargain( target.getIsMakeBargain() );
        querySalesLeadsOutDto.setIsFriend( target.getIsFriend() );
        querySalesLeadsOutDto.setExternalUserid( target.getExternalUserid() );
        querySalesLeadsOutDto.setFollowCount( target.getFollowCount() );
        querySalesLeadsOutDto.setGender( target.getGender() );
        querySalesLeadsOutDto.setHouseType( target.getHouseType() );
        querySalesLeadsOutDto.setDecorateType( target.getDecorateType() );
        querySalesLeadsOutDto.setLastFollowTime( target.getLastFollowTime() );
        querySalesLeadsOutDto.setCreateUserId( target.getCreateUserId() );
        querySalesLeadsOutDto.setCreateUserName( target.getCreateUserName() );
        querySalesLeadsOutDto.setBuilding( target.getBuilding() );
        querySalesLeadsOutDto.setUnit( target.getUnit() );
        querySalesLeadsOutDto.setHouseNumber( target.getHouseNumber() );
        querySalesLeadsOutDto.setUserAddressType( target.getUserAddressType() );
        querySalesLeadsOutDto.setSalesmanName( target.getSalesmanName() );
        querySalesLeadsOutDto.setCluesLevel( target.getCluesLevel() );
        querySalesLeadsOutDto.setCluesLevelValue( target.getCluesLevelValue() );
        querySalesLeadsOutDto.setColor( target.getColor() );
        querySalesLeadsOutDto.setId( target.getId() );
        querySalesLeadsOutDto.setCustomerName( target.getCustomerName() );
        querySalesLeadsOutDto.setCustomerPhone( target.getCustomerPhone() );
        querySalesLeadsOutDto.setCreatedDate( target.getCreatedDate() );
        querySalesLeadsOutDto.setCreatedBy( target.getCreatedBy() );
        querySalesLeadsOutDto.setCompanyId( target.getCompanyId() );
        querySalesLeadsOutDto.setStroeId( target.getStroeId() );
        querySalesLeadsOutDto.setStroeCode( target.getStroeCode() );
        querySalesLeadsOutDto.setStroeName( target.getStroeName() );
        querySalesLeadsOutDto.setActivityId( target.getActivityId() );
        querySalesLeadsOutDto.setActivityName( target.getActivityName() );
        querySalesLeadsOutDto.setChargeUserName( target.getChargeUserName() );
        querySalesLeadsOutDto.setCompanyName( target.getCompanyName() );
        querySalesLeadsOutDto.setStatus( target.getStatus() );
        querySalesLeadsOutDto.setUnderTheLineRevisitDays( target.getUnderTheLineRevisitDays() );
        querySalesLeadsOutDto.setOnLineRevisitDays( target.getOnLineRevisitDays() );
        querySalesLeadsOutDto.setCustomServiceCode( target.getCustomServiceCode() );
        querySalesLeadsOutDto.setChargeCode( target.getChargeCode() );
        querySalesLeadsOutDto.setFollowUpStatus( target.getFollowUpStatus() );
        querySalesLeadsOutDto.setVisitTime( target.getVisitTime() );
        querySalesLeadsOutDto.setUtmSource( target.getUtmSource() );
        querySalesLeadsOutDto.setAuditStatus( target.getAuditStatus() );
        querySalesLeadsOutDto.setFundingTime( target.getFundingTime() );
        querySalesLeadsOutDto.setProvinceCode( target.getProvinceCode() );
        querySalesLeadsOutDto.setProvince( target.getProvince() );
        querySalesLeadsOutDto.setCityCode( target.getCityCode() );
        querySalesLeadsOutDto.setCity( target.getCity() );
        querySalesLeadsOutDto.setAreaCode( target.getAreaCode() );
        querySalesLeadsOutDto.setArea( target.getArea() );
        querySalesLeadsOutDto.setStoreTypeCode( target.getStoreTypeCode() );
        querySalesLeadsOutDto.setStoreTypeName( target.getStoreTypeName() );
        querySalesLeadsOutDto.setIsComeDevise( target.getIsComeDevise() );
        querySalesLeadsOutDto.setChannelCategoryCode( target.getChannelCategoryCode() );
        querySalesLeadsOutDto.setChannelCategoryName( target.getChannelCategoryName() );
        querySalesLeadsOutDto.setChannelSubdivideCode( target.getChannelSubdivideCode() );
        querySalesLeadsOutDto.setChannelSubdivideName( target.getChannelSubdivideName() );
        querySalesLeadsOutDto.setReviseTime( target.getReviseTime() );
        querySalesLeadsOutDto.setScreenshots( target.getScreenshots() );
        querySalesLeadsOutDto.setAuditTime( target.getAuditTime() );
        querySalesLeadsOutDto.setFollowEfficiencyCategoryValue( target.getFollowEfficiencyCategoryValue() );
        querySalesLeadsOutDto.setFollowEfficiencyTime( target.getFollowEfficiencyTime() );
        querySalesLeadsOutDto.setDecorateCompanyName( target.getDecorateCompanyName() );
        querySalesLeadsOutDto.setDesignerName( target.getDesignerName() );
        querySalesLeadsOutDto.setDecorateCompanyType( target.getDecorateCompanyType() );

        return querySalesLeadsOutDto;
    }
}
