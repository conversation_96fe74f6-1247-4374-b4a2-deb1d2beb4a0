package com.fotile.usercenter.menu.pojo;

import com.fotile.framework.data.auth.dataAuthor.pojo.Menu;
import com.fotile.usercenter.menu.pojo.dto.QueryAllMenuByRoleNamesOutDto;
import com.fotile.usercenter.menu.pojo.dto.QueryAllOutDto;
import com.fotile.usercenter.menu.pojo.dto.QueryByParentIdOutDto;
import com.fotile.usercenter.menu.pojo.dto.QueryMenuByRoleIdOutDto;
import com.fotile.usercenter.menu.pojo.dto.SaveInDto;
import com.fotile.usercenter.menu.pojo.dto.SaveOutDto;
import com.fotile.usercenter.menu.pojo.dto.UpdateInDto;
import com.fotile.usercenter.menu.pojo.entity.MenuEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-06-28T09:53:57+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class MenuMapperImpl implements MenuMapper {

    @Override
    public List<QueryMenuByRoleIdOutDto> menuEntityListToQueryMenuByRoleIdOutDto(List<MenuEntity> menuList) {
        if ( menuList == null ) {
            return null;
        }

        List<QueryMenuByRoleIdOutDto> list = new ArrayList<QueryMenuByRoleIdOutDto>( menuList.size() );
        for ( MenuEntity menuEntity : menuList ) {
            list.add( menuEntityToQueryMenuByRoleIdOutDto( menuEntity ) );
        }

        return list;
    }

    @Override
    public List<QueryAllMenuByRoleNamesOutDto> menuEntityListToQueryAllMenuByRoleNamesOutDto(List<MenuEntity> menuList) {
        if ( menuList == null ) {
            return null;
        }

        List<QueryAllMenuByRoleNamesOutDto> list = new ArrayList<QueryAllMenuByRoleNamesOutDto>( menuList.size() );
        for ( MenuEntity menuEntity : menuList ) {
            list.add( menuEntityToQueryAllMenuByRoleNamesOutDto( menuEntity ) );
        }

        return list;
    }

    @Override
    public MenuEntity saveInDtoToMenuEntity(SaveInDto saveInDto) {
        if ( saveInDto == null ) {
            return null;
        }

        MenuEntity menuEntity = new MenuEntity();

        menuEntity.setId( saveInDto.getId() );
        menuEntity.setIsDeleted( saveInDto.getIsDeleted() );
        menuEntity.setCreatedBy( saveInDto.getCreatedBy() );
        menuEntity.setModifiedBy( saveInDto.getModifiedBy() );
        menuEntity.setCreatedDate( saveInDto.getCreatedDate() );
        menuEntity.setModifiedDate( saveInDto.getModifiedDate() );
        menuEntity.setParentId( saveInDto.getParentId() );
        menuEntity.setFullPathId( saveInDto.getFullPathId() );
        menuEntity.setFullPathName( saveInDto.getFullPathName() );
        menuEntity.setLevel( saveInDto.getLevel() );
        menuEntity.setLeaf( saveInDto.getLeaf() );
        menuEntity.setName( saveInDto.getName() );
        menuEntity.setOpenType( saveInDto.getOpenType() );
        menuEntity.setUri( saveInDto.getUri() );
        menuEntity.setIcon( saveInDto.getIcon() );
        menuEntity.setScript( saveInDto.getScript() );
        menuEntity.setResourceId( saveInDto.getResourceId() );
        menuEntity.setResourceName( saveInDto.getResourceName() );
        menuEntity.setType( saveInDto.getType() );
        menuEntity.setSort( saveInDto.getSort() );
        menuEntity.setMenuType( saveInDto.getMenuType() );

        return menuEntity;
    }

    @Override
    public SaveOutDto menuEntityToSaveOutDto(MenuEntity menuEntity) {
        if ( menuEntity == null ) {
            return null;
        }

        SaveOutDto saveOutDto = new SaveOutDto();

        saveOutDto.setId( menuEntity.getId() );
        saveOutDto.setIsDeleted( menuEntity.getIsDeleted() );
        saveOutDto.setCreatedBy( menuEntity.getCreatedBy() );
        saveOutDto.setModifiedBy( menuEntity.getModifiedBy() );
        saveOutDto.setCreatedDate( menuEntity.getCreatedDate() );
        saveOutDto.setModifiedDate( menuEntity.getModifiedDate() );
        saveOutDto.setParentId( menuEntity.getParentId() );
        saveOutDto.setFullPathId( menuEntity.getFullPathId() );
        saveOutDto.setFullPathName( menuEntity.getFullPathName() );
        saveOutDto.setLevel( menuEntity.getLevel() );
        saveOutDto.setLeaf( menuEntity.getLeaf() );
        saveOutDto.setName( menuEntity.getName() );
        saveOutDto.setOpenType( menuEntity.getOpenType() );
        saveOutDto.setUri( menuEntity.getUri() );
        saveOutDto.setIcon( menuEntity.getIcon() );
        saveOutDto.setScript( menuEntity.getScript() );
        saveOutDto.setResourceId( menuEntity.getResourceId() );
        saveOutDto.setResourceName( menuEntity.getResourceName() );
        saveOutDto.setType( menuEntity.getType() );
        saveOutDto.setSort( menuEntity.getSort() );

        return saveOutDto;
    }

    @Override
    public MenuEntity updateInDtoToMenuEntity(UpdateInDto updateInDto) {
        if ( updateInDto == null ) {
            return null;
        }

        MenuEntity menuEntity = new MenuEntity();

        menuEntity.setId( updateInDto.getId() );
        menuEntity.setIsDeleted( updateInDto.getIsDeleted() );
        menuEntity.setCreatedBy( updateInDto.getCreatedBy() );
        menuEntity.setModifiedBy( updateInDto.getModifiedBy() );
        menuEntity.setCreatedDate( updateInDto.getCreatedDate() );
        menuEntity.setModifiedDate( updateInDto.getModifiedDate() );
        menuEntity.setParentId( updateInDto.getParentId() );
        menuEntity.setFullPathId( updateInDto.getFullPathId() );
        menuEntity.setFullPathName( updateInDto.getFullPathName() );
        menuEntity.setLevel( updateInDto.getLevel() );
        menuEntity.setLeaf( updateInDto.getLeaf() );
        menuEntity.setName( updateInDto.getName() );
        menuEntity.setOpenType( updateInDto.getOpenType() );
        menuEntity.setUri( updateInDto.getUri() );
        menuEntity.setIcon( updateInDto.getIcon() );
        menuEntity.setScript( updateInDto.getScript() );
        menuEntity.setResourceId( updateInDto.getResourceId() );
        menuEntity.setResourceName( updateInDto.getResourceName() );
        menuEntity.setType( updateInDto.getType() );
        menuEntity.setSort( updateInDto.getSort() );
        menuEntity.setMenuType( updateInDto.getMenuType() );

        return menuEntity;
    }

    @Override
    public List<QueryAllOutDto> menuEntityListToQueryAllOutDto(List<MenuEntity> menuList) {
        if ( menuList == null ) {
            return null;
        }

        List<QueryAllOutDto> list = new ArrayList<QueryAllOutDto>( menuList.size() );
        for ( MenuEntity menuEntity : menuList ) {
            list.add( menuEntityToQueryAllOutDto( menuEntity ) );
        }

        return list;
    }

    @Override
    public QueryAllOutDto menuEntityToQueryAllOutDto(MenuEntity menu) {
        if ( menu == null ) {
            return null;
        }

        QueryAllOutDto queryAllOutDto = new QueryAllOutDto();

        queryAllOutDto.setId( menu.getId() );
        queryAllOutDto.setIsDeleted( menu.getIsDeleted() );
        queryAllOutDto.setCreatedBy( menu.getCreatedBy() );
        queryAllOutDto.setModifiedBy( menu.getModifiedBy() );
        queryAllOutDto.setCreatedDate( menu.getCreatedDate() );
        queryAllOutDto.setModifiedDate( menu.getModifiedDate() );
        queryAllOutDto.setParentId( menu.getParentId() );
        queryAllOutDto.setFullPathId( menu.getFullPathId() );
        queryAllOutDto.setFullPathName( menu.getFullPathName() );
        queryAllOutDto.setLevel( menu.getLevel() );
        queryAllOutDto.setLeaf( menu.getLeaf() );
        queryAllOutDto.setName( menu.getName() );
        queryAllOutDto.setOpenType( menu.getOpenType() );
        queryAllOutDto.setUri( menu.getUri() );
        queryAllOutDto.setIcon( menu.getIcon() );
        queryAllOutDto.setScript( menu.getScript() );
        queryAllOutDto.setResourceId( menu.getResourceId() );
        queryAllOutDto.setResourceName( menu.getResourceName() );
        queryAllOutDto.setType( menu.getType() );
        queryAllOutDto.setSort( menu.getSort() );
        queryAllOutDto.setMenuType( menu.getMenuType() );

        return queryAllOutDto;
    }

    @Override
    public List<QueryByParentIdOutDto> menuEntityListToQueryByParentIdOutDto(List<MenuEntity> menuList) {
        if ( menuList == null ) {
            return null;
        }

        List<QueryByParentIdOutDto> list = new ArrayList<QueryByParentIdOutDto>( menuList.size() );
        for ( MenuEntity menuEntity : menuList ) {
            list.add( menuEntityToQueryByParentIdOutDto( menuEntity ) );
        }

        return list;
    }

    @Override
    public List<Menu> menuEntityListToMenu(List<MenuEntity> menuList) {
        if ( menuList == null ) {
            return null;
        }

        List<Menu> list = new ArrayList<Menu>( menuList.size() );
        for ( MenuEntity menuEntity : menuList ) {
            list.add( menuEntityToMenu( menuEntity ) );
        }

        return list;
    }

    protected QueryMenuByRoleIdOutDto menuEntityToQueryMenuByRoleIdOutDto(MenuEntity menuEntity) {
        if ( menuEntity == null ) {
            return null;
        }

        QueryMenuByRoleIdOutDto queryMenuByRoleIdOutDto = new QueryMenuByRoleIdOutDto();

        queryMenuByRoleIdOutDto.setName( menuEntity.getName() );
        queryMenuByRoleIdOutDto.setOpenType( menuEntity.getOpenType() );
        queryMenuByRoleIdOutDto.setUri( menuEntity.getUri() );
        queryMenuByRoleIdOutDto.setIcon( menuEntity.getIcon() );
        queryMenuByRoleIdOutDto.setScript( menuEntity.getScript() );
        queryMenuByRoleIdOutDto.setResourceId( menuEntity.getResourceId() );
        queryMenuByRoleIdOutDto.setResourceName( menuEntity.getResourceName() );
        queryMenuByRoleIdOutDto.setType( menuEntity.getType() );
        queryMenuByRoleIdOutDto.setSort( menuEntity.getSort() );
        queryMenuByRoleIdOutDto.setMenuType( menuEntity.getMenuType() );

        return queryMenuByRoleIdOutDto;
    }

    protected QueryAllMenuByRoleNamesOutDto menuEntityToQueryAllMenuByRoleNamesOutDto(MenuEntity menuEntity) {
        if ( menuEntity == null ) {
            return null;
        }

        QueryAllMenuByRoleNamesOutDto queryAllMenuByRoleNamesOutDto = new QueryAllMenuByRoleNamesOutDto();

        queryAllMenuByRoleNamesOutDto.setId( menuEntity.getId() );
        queryAllMenuByRoleNamesOutDto.setIsDeleted( menuEntity.getIsDeleted() );
        queryAllMenuByRoleNamesOutDto.setCreatedBy( menuEntity.getCreatedBy() );
        queryAllMenuByRoleNamesOutDto.setModifiedBy( menuEntity.getModifiedBy() );
        queryAllMenuByRoleNamesOutDto.setCreatedDate( menuEntity.getCreatedDate() );
        queryAllMenuByRoleNamesOutDto.setModifiedDate( menuEntity.getModifiedDate() );
        queryAllMenuByRoleNamesOutDto.setParentId( menuEntity.getParentId() );
        queryAllMenuByRoleNamesOutDto.setFullPathId( menuEntity.getFullPathId() );
        queryAllMenuByRoleNamesOutDto.setFullPathName( menuEntity.getFullPathName() );
        queryAllMenuByRoleNamesOutDto.setLevel( menuEntity.getLevel() );
        queryAllMenuByRoleNamesOutDto.setLeaf( menuEntity.getLeaf() );
        queryAllMenuByRoleNamesOutDto.setName( menuEntity.getName() );
        queryAllMenuByRoleNamesOutDto.setOpenType( menuEntity.getOpenType() );
        queryAllMenuByRoleNamesOutDto.setUri( menuEntity.getUri() );
        queryAllMenuByRoleNamesOutDto.setIcon( menuEntity.getIcon() );
        queryAllMenuByRoleNamesOutDto.setScript( menuEntity.getScript() );
        queryAllMenuByRoleNamesOutDto.setResourceId( menuEntity.getResourceId() );
        queryAllMenuByRoleNamesOutDto.setResourceName( menuEntity.getResourceName() );
        queryAllMenuByRoleNamesOutDto.setType( menuEntity.getType() );
        queryAllMenuByRoleNamesOutDto.setSort( menuEntity.getSort() );
        queryAllMenuByRoleNamesOutDto.setMenuType( menuEntity.getMenuType() );

        return queryAllMenuByRoleNamesOutDto;
    }

    protected QueryByParentIdOutDto menuEntityToQueryByParentIdOutDto(MenuEntity menuEntity) {
        if ( menuEntity == null ) {
            return null;
        }

        QueryByParentIdOutDto queryByParentIdOutDto = new QueryByParentIdOutDto();

        queryByParentIdOutDto.setId( menuEntity.getId() );
        queryByParentIdOutDto.setIsDeleted( menuEntity.getIsDeleted() );
        queryByParentIdOutDto.setCreatedBy( menuEntity.getCreatedBy() );
        queryByParentIdOutDto.setModifiedBy( menuEntity.getModifiedBy() );
        queryByParentIdOutDto.setCreatedDate( menuEntity.getCreatedDate() );
        queryByParentIdOutDto.setModifiedDate( menuEntity.getModifiedDate() );
        queryByParentIdOutDto.setParentId( menuEntity.getParentId() );
        queryByParentIdOutDto.setFullPathId( menuEntity.getFullPathId() );
        queryByParentIdOutDto.setFullPathName( menuEntity.getFullPathName() );
        queryByParentIdOutDto.setLevel( menuEntity.getLevel() );
        queryByParentIdOutDto.setLeaf( menuEntity.getLeaf() );
        queryByParentIdOutDto.setName( menuEntity.getName() );
        queryByParentIdOutDto.setOpenType( menuEntity.getOpenType() );
        queryByParentIdOutDto.setUri( menuEntity.getUri() );
        queryByParentIdOutDto.setIcon( menuEntity.getIcon() );
        queryByParentIdOutDto.setScript( menuEntity.getScript() );
        queryByParentIdOutDto.setResourceId( menuEntity.getResourceId() );
        queryByParentIdOutDto.setResourceName( menuEntity.getResourceName() );
        queryByParentIdOutDto.setType( menuEntity.getType() );
        queryByParentIdOutDto.setSort( menuEntity.getSort() );
        queryByParentIdOutDto.setMenuType( menuEntity.getMenuType() );

        return queryByParentIdOutDto;
    }

    protected Menu menuEntityToMenu(MenuEntity menuEntity) {
        if ( menuEntity == null ) {
            return null;
        }

        Menu menu = new Menu();

        menu.setName( menuEntity.getName() );
        menu.setOpenType( menuEntity.getOpenType() );
        menu.setUri( menuEntity.getUri() );
        menu.setIcon( menuEntity.getIcon() );
        menu.setScript( menuEntity.getScript() );
        menu.setResourceId( menuEntity.getResourceId() );
        menu.setResourceName( menuEntity.getResourceName() );
        menu.setType( menuEntity.getType() );
        menu.setSort( menuEntity.getSort() );
        menu.setMenuType( menuEntity.getMenuType() );

        return menu;
    }
}
