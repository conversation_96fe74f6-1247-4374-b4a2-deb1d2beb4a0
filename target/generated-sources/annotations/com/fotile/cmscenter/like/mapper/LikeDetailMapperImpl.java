package com.fotile.cmscenter.like.mapper;

import com.fotile.cmscenter.like.pojo.ExportLikeDto;
import com.fotile.cmscenter.like.pojo.LikeDetail;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T14:49:53+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class LikeDetailMapperImpl implements LikeDetailMapper {

    @Override
    public List<ExportLikeDto> detailToExcelDto(List<LikeDetail> likeDetails) {
        if ( likeDetails == null ) {
            return null;
        }

        List<ExportLikeDto> list = new ArrayList<ExportLikeDto>( likeDetails.size() );
        for ( LikeDetail likeDetail : likeDetails ) {
            list.add( likeDetailToExportLikeDto( likeDetail ) );
        }

        return list;
    }

    protected ExportLikeDto likeDetailToExportLikeDto(LikeDetail likeDetail) {
        if ( likeDetail == null ) {
            return null;
        }

        ExportLikeDto exportLikeDto = new ExportLikeDto();

        exportLikeDto.setId( likeDetail.getId() );
        exportLikeDto.setSourceId( likeDetail.getSourceId() );
        exportLikeDto.setSourceTableAlias( likeDetail.getSourceTableAlias() );
        exportLikeDto.setSourceName( likeDetail.getSourceName() );
        exportLikeDto.setType( likeDetail.getType() );
        exportLikeDto.setLikeDate( likeDetail.getLikeDate() );
        exportLikeDto.setUserId( likeDetail.getUserId() );
        exportLikeDto.setNickname( likeDetail.getNickname() );
        exportLikeDto.setPhone( likeDetail.getPhone() );

        return exportLikeDto;
    }
}
