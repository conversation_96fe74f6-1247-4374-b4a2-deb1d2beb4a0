package com.fotile.cmscenter.material.dao;

import com.fotile.cmscenter.material.pojo.dto.BasicMaterialContentQueryOutDTO;
import com.fotile.cmscenter.material.pojo.dto.BasicMaterialExcelErrorDTO;
import com.fotile.cmscenter.material.pojo.dto.BasicMaterialImportExcelDTO;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryOutputDTO;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryOutputDTO.MaterialEventLogQueryOutputDTOBuilder;
import com.fotile.cmscenter.material.pojo.entity.BasicMaterialContent;
import com.fotile.cmscenter.material.pojo.entity.MaterialEventLog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T14:49:53+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class BasicMaterialMapStructMapperImpl implements BasicMaterialMapStructMapper {

    @Override
    public BasicMaterialExcelErrorDTO castBasicMaterialExcel(BasicMaterialImportExcelDTO dto) {
        if ( dto == null ) {
            return null;
        }

        BasicMaterialExcelErrorDTO basicMaterialExcelErrorDTO = new BasicMaterialExcelErrorDTO();

        basicMaterialExcelErrorDTO.setName( dto.getName() );
        basicMaterialExcelErrorDTO.setCategoryName( dto.getCategoryName() );
        basicMaterialExcelErrorDTO.setContentType1( dto.getContentType1() );
        basicMaterialExcelErrorDTO.setContent1( dto.getContent1() );
        basicMaterialExcelErrorDTO.setContentType2( dto.getContentType2() );
        basicMaterialExcelErrorDTO.setContent2( dto.getContent2() );
        basicMaterialExcelErrorDTO.setContentType3( dto.getContentType3() );
        basicMaterialExcelErrorDTO.setContent3( dto.getContent3() );
        basicMaterialExcelErrorDTO.setContentType4( dto.getContentType4() );
        basicMaterialExcelErrorDTO.setContent4( dto.getContent4() );
        basicMaterialExcelErrorDTO.setContentType5( dto.getContentType5() );
        basicMaterialExcelErrorDTO.setContent5( dto.getContent5() );
        basicMaterialExcelErrorDTO.setSalesmanCode( dto.getSalesmanCode() );
        basicMaterialExcelErrorDTO.setSourceDescNames( dto.getSourceDescNames() );
        basicMaterialExcelErrorDTO.setRemarks( dto.getRemarks() );
        basicMaterialExcelErrorDTO.setStatusName( dto.getStatusName() );

        return basicMaterialExcelErrorDTO;
    }

    @Override
    public BasicMaterialContentQueryOutDTO bmcToBmcDTO(BasicMaterialContent bmc) {
        if ( bmc == null ) {
            return null;
        }

        BasicMaterialContentQueryOutDTO basicMaterialContentQueryOutDTO = new BasicMaterialContentQueryOutDTO();

        basicMaterialContentQueryOutDTO.setContentId( bmc.getId() );
        basicMaterialContentQueryOutDTO.setType( bmc.getType() );
        basicMaterialContentQueryOutDTO.setRefId( bmc.getRefId() );
        basicMaterialContentQueryOutDTO.setRefCode( bmc.getRefCode() );
        basicMaterialContentQueryOutDTO.setRefTitle( bmc.getRefTitle() );
        basicMaterialContentQueryOutDTO.setMaterialName( bmc.getMaterialName() );
        basicMaterialContentQueryOutDTO.setMaterialCoverUrl( bmc.getMaterialCoverUrl() );
        basicMaterialContentQueryOutDTO.setMaterialUrl( bmc.getMaterialUrl() );
        basicMaterialContentQueryOutDTO.setLinkAddress( bmc.getLinkAddress() );
        basicMaterialContentQueryOutDTO.setContent( bmc.getContent() );
        basicMaterialContentQueryOutDTO.setShowPlatform( bmc.getShowPlatform() );
        basicMaterialContentQueryOutDTO.setShowPlatformName( bmc.getShowPlatformName() );
        basicMaterialContentQueryOutDTO.setLandingPage( bmc.getLandingPage() );
        basicMaterialContentQueryOutDTO.setSort( bmc.getSort() );

        return basicMaterialContentQueryOutDTO;
    }

    @Override
    public List<BasicMaterialContentQueryOutDTO> bmcToBmcDTOs(List<BasicMaterialContent> list) {
        if ( list == null ) {
            return null;
        }

        List<BasicMaterialContentQueryOutDTO> list1 = new ArrayList<BasicMaterialContentQueryOutDTO>( list.size() );
        for ( BasicMaterialContent basicMaterialContent : list ) {
            list1.add( bmcToBmcDTO( basicMaterialContent ) );
        }

        return list1;
    }

    @Override
    public List<MaterialEventLogQueryOutputDTO> materialEventLogToDtos(List<MaterialEventLog> list) {
        if ( list == null ) {
            return null;
        }

        List<MaterialEventLogQueryOutputDTO> list1 = new ArrayList<MaterialEventLogQueryOutputDTO>( list.size() );
        for ( MaterialEventLog materialEventLog : list ) {
            list1.add( materialEventLogToMaterialEventLogQueryOutputDTO( materialEventLog ) );
        }

        return list1;
    }

    protected MaterialEventLogQueryOutputDTO materialEventLogToMaterialEventLogQueryOutputDTO(MaterialEventLog materialEventLog) {
        if ( materialEventLog == null ) {
            return null;
        }

        MaterialEventLogQueryOutputDTOBuilder materialEventLogQueryOutputDTO = MaterialEventLogQueryOutputDTO.builder();

        materialEventLogQueryOutputDTO.id( materialEventLog.getId() );
        materialEventLogQueryOutputDTO.eventType( materialEventLog.getEventType() );
        if ( materialEventLog.getMaterialId() != null ) {
            materialEventLogQueryOutputDTO.materialId( String.valueOf( materialEventLog.getMaterialId() ) );
        }
        materialEventLogQueryOutputDTO.treeId( materialEventLog.getTreeId() );
        materialEventLogQueryOutputDTO.nodeId( materialEventLog.getNodeId() );
        materialEventLogQueryOutputDTO.userId( materialEventLog.getUserId() );
        materialEventLogQueryOutputDTO.userName( materialEventLog.getUserName() );
        materialEventLogQueryOutputDTO.firstName( materialEventLog.getFirstName() );
        materialEventLogQueryOutputDTO.areaCode( materialEventLog.getAreaCode() );
        materialEventLogQueryOutputDTO.areaName( materialEventLog.getAreaName() );
        materialEventLogQueryOutputDTO.companyId( materialEventLog.getCompanyId() );
        materialEventLogQueryOutputDTO.companyName( materialEventLog.getCompanyName() );
        materialEventLogQueryOutputDTO.storeId( materialEventLog.getStoreId() );
        materialEventLogQueryOutputDTO.storeCode( materialEventLog.getStoreCode() );
        materialEventLogQueryOutputDTO.storeName( materialEventLog.getStoreName() );
        materialEventLogQueryOutputDTO.chargeUserId( materialEventLog.getChargeUserId() );
        materialEventLogQueryOutputDTO.chargeUserName( materialEventLog.getChargeUserName() );
        materialEventLogQueryOutputDTO.chargeCode( materialEventLog.getChargeCode() );
        materialEventLogQueryOutputDTO.chargePhone( materialEventLog.getChargePhone() );
        materialEventLogQueryOutputDTO.station( materialEventLog.getStation() );
        materialEventLogQueryOutputDTO.createdDate( materialEventLog.getCreatedDate() );
        materialEventLogQueryOutputDTO.objectUserId( materialEventLog.getObjectUserId() );
        materialEventLogQueryOutputDTO.bizType( materialEventLog.getBizType() );
        materialEventLogQueryOutputDTO.contentCode( materialEventLog.getContentCode() );
        materialEventLogQueryOutputDTO.contentName( materialEventLog.getContentName() );
        materialEventLogQueryOutputDTO.relationParentId( materialEventLog.getRelationParentId() );
        materialEventLogQueryOutputDTO.relationSourceType( materialEventLog.getRelationSourceType() );
        materialEventLogQueryOutputDTO.bizDetailType( materialEventLog.getBizDetailType() );

        return materialEventLogQueryOutputDTO.build();
    }
}
