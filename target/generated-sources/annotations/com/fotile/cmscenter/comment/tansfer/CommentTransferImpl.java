package com.fotile.cmscenter.comment.tansfer;

import com.fotile.cmscenter.comment.pojo.CommentInfo;
import com.fotile.cmscenter.comment.pojo.Img;
import com.fotile.cmscenter.comment.pojo.PublishContentDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T14:49:53+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class CommentTransferImpl implements CommentTransfer {

    @Override
    public CommentInfo contentToCommentInfo(PublishContentDto dto) {
        if ( dto == null ) {
            return null;
        }

        CommentInfo commentInfo = new CommentInfo();

        commentInfo.setId( dto.getId() );
        commentInfo.setSourceId( dto.getSourceId() );
        commentInfo.setCommentContent( dto.getCommentContent() );
        commentInfo.setUserName( dto.getUserName() );
        commentInfo.setSourceTableName( dto.getSourceTableName() );
        commentInfo.setSourceTableAlias( dto.getSourceTableAlias() );
        List<Img> list = dto.getImgList();
        if ( list != null ) {
            commentInfo.setImgList( new ArrayList<Img>( list ) );
        }

        return commentInfo;
    }
}
