package com.fotile.orgcenter.village.pojo;

import com.fotile.orgcenter.houseType.pojo.dto.HouseTypeOutDto;
import com.fotile.orgcenter.houseType.pojo.dto.UpdateVillageDto;
import com.fotile.orgcenter.houseType.pojo.dto.UpdateVillageDto2;
import com.fotile.orgcenter.village.pojo.dto.AddVillageInDto;
import com.fotile.orgcenter.village.pojo.dto.AddVillageOutDto;
import com.fotile.orgcenter.village.pojo.dto.FindVillageByIdOutDto;
import com.fotile.orgcenter.village.pojo.dto.FindVillagePageAllByCityOutDto;
import com.fotile.orgcenter.village.pojo.dto.FindVillagePageAllInDto;
import com.fotile.orgcenter.village.pojo.dto.FindVillagePageAllOutDto;
import com.fotile.orgcenter.village.pojo.dto.QueryVillageDto;
import com.fotile.orgcenter.village.pojo.dto.UpdateVillageInDto;
import com.fotile.orgcenter.village.pojo.dto.VillageDetailDto;
import com.fotile.orgcenter.village.pojo.entity.VillageEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-18T14:09:48+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class VillageMapperImpl implements VillageMapper {

    @Override
    public VillageEntity addVillageInDtoToVillageEntity(AddVillageInDto addVillageInDto) {
        if ( addVillageInDto == null ) {
            return null;
        }

        VillageEntity villageEntity = new VillageEntity();

        villageEntity.setName( addVillageInDto.getName() );
        villageEntity.setProvicenId( addVillageInDto.getProvicenId() );
        villageEntity.setCityId( addVillageInDto.getCityId() );
        villageEntity.setCountyId( addVillageInDto.getCountyId() );
        villageEntity.setAddress( addVillageInDto.getAddress() );
        villageEntity.setFinishTime( addVillageInDto.getFinishTime() );
        villageEntity.setDeveloper( addVillageInDto.getDeveloper() );
        villageEntity.setNum( addVillageInDto.getNum() );
        villageEntity.setNote( addVillageInDto.getNote() );
        villageEntity.setStoreId( addVillageInDto.getStoreId() );
        villageEntity.setProvicenName( addVillageInDto.getProvicenName() );
        villageEntity.setCityName( addVillageInDto.getCityName() );
        villageEntity.setCountyName( addVillageInDto.getCountyName() );
        villageEntity.setLongitude( addVillageInDto.getLongitude() );
        villageEntity.setLatitude( addVillageInDto.getLatitude() );
        villageEntity.setAvgPrice( addVillageInDto.getAvgPrice() );
        villageEntity.setBuildingNum( addVillageInDto.getBuildingNum() );
        villageEntity.setChannelCode( addVillageInDto.getChannelCode() );
        villageEntity.setVillageManagers( addVillageInDto.getVillageManagers() );
        villageEntity.setParentId( addVillageInDto.getParentId() );
        villageEntity.setFloorState( addVillageInDto.getFloorState() );
        villageEntity.setUseBattleMap( addVillageInDto.getUseBattleMap() );
        villageEntity.setLevel( addVillageInDto.getLevel() );
        villageEntity.setBelongPlate( addVillageInDto.getBelongPlate() );
        villageEntity.setHouseTypeRemark( addVillageInDto.getHouseTypeRemark() );
        villageEntity.setEngineeringProgress( addVillageInDto.getEngineeringProgress() );
        villageEntity.setRightYear( addVillageInDto.getRightYear() );
        villageEntity.setSalesStatus( addVillageInDto.getSalesStatus() );
        villageEntity.setStartDate( addVillageInDto.getStartDate() );
        villageEntity.setSourceFlag( addVillageInDto.getSourceFlag() );
        villageEntity.setBlankNum( addVillageInDto.getBlankNum() );
        villageEntity.setHardcoverNum( addVillageInDto.getHardcoverNum() );
        villageEntity.setIsMaster( addVillageInDto.getIsMaster() );
        villageEntity.setVillageType( addVillageInDto.getVillageType() );
        villageEntity.setReceiveDate( addVillageInDto.getReceiveDate() );
        villageEntity.setOpenDate( addVillageInDto.getOpenDate() );
        villageEntity.setManageCompany( addVillageInDto.getManageCompany() );
        villageEntity.setVillageSize( addVillageInDto.getVillageSize() );
        villageEntity.setBrandAbility( addVillageInDto.getBrandAbility() );
        villageEntity.setPotential( addVillageInDto.getPotential() );
        villageEntity.setDecorationType( addVillageInDto.getDecorationType() );
        List<Long> list = addVillageInDto.getOrderStoreIdList();
        if ( list != null ) {
            villageEntity.setOrderStoreIdList( new ArrayList<Long>( list ) );
        }

        return villageEntity;
    }

    @Override
    public AddVillageOutDto villageEntityToAddVillageOutDto(VillageEntity villageEntity) {
        if ( villageEntity == null ) {
            return null;
        }

        AddVillageOutDto addVillageOutDto = new AddVillageOutDto();

        addVillageOutDto.setId( villageEntity.getId() );
        addVillageOutDto.setName( villageEntity.getName() );
        addVillageOutDto.setProvicenId( villageEntity.getProvicenId() );
        addVillageOutDto.setCityId( villageEntity.getCityId() );
        addVillageOutDto.setCountyId( villageEntity.getCountyId() );
        addVillageOutDto.setAddress( villageEntity.getAddress() );
        addVillageOutDto.setDeveloper( villageEntity.getDeveloper() );
        addVillageOutDto.setFinishTime( villageEntity.getFinishTime() );
        addVillageOutDto.setNote( villageEntity.getNote() );
        addVillageOutDto.setNum( villageEntity.getNum() );
        addVillageOutDto.setStoreId( villageEntity.getStoreId() );
        addVillageOutDto.setProvicenName( villageEntity.getProvicenName() );
        addVillageOutDto.setCityName( villageEntity.getCityName() );
        addVillageOutDto.setCountyName( villageEntity.getCountyName() );
        addVillageOutDto.setLongitude( villageEntity.getLongitude() );
        addVillageOutDto.setLatitude( villageEntity.getLatitude() );
        addVillageOutDto.setParentId( villageEntity.getParentId() );
        addVillageOutDto.setFloorState( villageEntity.getFloorState() );
        addVillageOutDto.setUseBattleMap( villageEntity.getUseBattleMap() );
        addVillageOutDto.setLevel( villageEntity.getLevel() );
        addVillageOutDto.setBelongPlate( villageEntity.getBelongPlate() );
        addVillageOutDto.setHouseTypeRemark( villageEntity.getHouseTypeRemark() );
        addVillageOutDto.setEngineeringProgress( villageEntity.getEngineeringProgress() );
        addVillageOutDto.setRightYear( villageEntity.getRightYear() );
        addVillageOutDto.setSalesStatus( villageEntity.getSalesStatus() );
        addVillageOutDto.setStartDate( villageEntity.getStartDate() );
        addVillageOutDto.setSourceFlag( villageEntity.getSourceFlag() );
        addVillageOutDto.setBlankNum( villageEntity.getBlankNum() );
        addVillageOutDto.setHardcoverNum( villageEntity.getHardcoverNum() );

        return addVillageOutDto;
    }

    @Override
    public VillageEntity updateVillageInDtoToVillageEntity(UpdateVillageInDto updateVillageInDto) {
        if ( updateVillageInDto == null ) {
            return null;
        }

        VillageEntity villageEntity = new VillageEntity();

        villageEntity.setId( updateVillageInDto.getId() );
        villageEntity.setIsDeleted( updateVillageInDto.getIsDeleted() );
        villageEntity.setCreatedBy( updateVillageInDto.getCreatedBy() );
        villageEntity.setModifiedBy( updateVillageInDto.getModifiedBy() );
        villageEntity.setCreatedDate( updateVillageInDto.getCreatedDate() );
        villageEntity.setModifiedDate( updateVillageInDto.getModifiedDate() );
        villageEntity.setName( updateVillageInDto.getName() );
        villageEntity.setProvicenId( updateVillageInDto.getProvicenId() );
        villageEntity.setCityId( updateVillageInDto.getCityId() );
        villageEntity.setCountyId( updateVillageInDto.getCountyId() );
        villageEntity.setAddress( updateVillageInDto.getAddress() );
        villageEntity.setFinishTime( updateVillageInDto.getFinishTime() );
        villageEntity.setDeveloper( updateVillageInDto.getDeveloper() );
        villageEntity.setNum( updateVillageInDto.getNum() );
        villageEntity.setNote( updateVillageInDto.getNote() );
        villageEntity.setStoreId( updateVillageInDto.getStoreId() );
        villageEntity.setProvicenName( updateVillageInDto.getProvicenName() );
        villageEntity.setCityName( updateVillageInDto.getCityName() );
        villageEntity.setCountyName( updateVillageInDto.getCountyName() );
        villageEntity.setLongitude( updateVillageInDto.getLongitude() );
        villageEntity.setLatitude( updateVillageInDto.getLatitude() );
        villageEntity.setAvgPrice( updateVillageInDto.getAvgPrice() );
        villageEntity.setBuildingNum( updateVillageInDto.getBuildingNum() );
        villageEntity.setChannelCode( updateVillageInDto.getChannelCode() );
        villageEntity.setVillageManagers( updateVillageInDto.getVillageManagers() );
        villageEntity.setHaveHouseType( updateVillageInDto.getHaveHouseType() );
        villageEntity.setParentId( updateVillageInDto.getParentId() );
        villageEntity.setFloorState( updateVillageInDto.getFloorState() );
        villageEntity.setUseBattleMap( updateVillageInDto.getUseBattleMap() );
        villageEntity.setLevel( updateVillageInDto.getLevel() );
        villageEntity.setBelongPlate( updateVillageInDto.getBelongPlate() );
        villageEntity.setHouseTypeRemark( updateVillageInDto.getHouseTypeRemark() );
        villageEntity.setEngineeringProgress( updateVillageInDto.getEngineeringProgress() );
        villageEntity.setRightYear( updateVillageInDto.getRightYear() );
        villageEntity.setSalesStatus( updateVillageInDto.getSalesStatus() );
        villageEntity.setStartDate( updateVillageInDto.getStartDate() );
        villageEntity.setSourceFlag( updateVillageInDto.getSourceFlag() );
        villageEntity.setBlankNum( updateVillageInDto.getBlankNum() );
        villageEntity.setHardcoverNum( updateVillageInDto.getHardcoverNum() );
        villageEntity.setIsMaster( updateVillageInDto.getIsMaster() );
        villageEntity.setVillageType( updateVillageInDto.getVillageType() );
        villageEntity.setReceiveDate( updateVillageInDto.getReceiveDate() );
        villageEntity.setOpenDate( updateVillageInDto.getOpenDate() );
        villageEntity.setManageCompany( updateVillageInDto.getManageCompany() );
        villageEntity.setVillageSize( updateVillageInDto.getVillageSize() );
        villageEntity.setBrandAbility( updateVillageInDto.getBrandAbility() );
        villageEntity.setPotential( updateVillageInDto.getPotential() );
        villageEntity.setDecorationType( updateVillageInDto.getDecorationType() );
        List<Long> list = updateVillageInDto.getOrderStoreIdList();
        if ( list != null ) {
            villageEntity.setOrderStoreIdList( new ArrayList<Long>( list ) );
        }

        return villageEntity;
    }

    @Override
    public VillageEntity findVillagePageAllInDtoToVillageEntity(FindVillagePageAllInDto findVillagePageAllInDto) {
        if ( findVillagePageAllInDto == null ) {
            return null;
        }

        VillageEntity villageEntity = new VillageEntity();

        villageEntity.setName( findVillagePageAllInDto.getName() );
        villageEntity.setProvicenId( findVillagePageAllInDto.getProvicenId() );
        villageEntity.setCityId( findVillagePageAllInDto.getCityId() );
        villageEntity.setCountyId( findVillagePageAllInDto.getCountyId() );
        villageEntity.setStoreId( findVillagePageAllInDto.getStoreId() );
        villageEntity.setFinishTimeStart( findVillagePageAllInDto.getFinishTimeStart() );
        villageEntity.setFinishTimeEnd( findVillagePageAllInDto.getFinishTimeEnd() );
        villageEntity.setDeveloper( findVillagePageAllInDto.getDeveloper() );
        villageEntity.setAddress( findVillagePageAllInDto.getAddress() );
        villageEntity.setKeyWord( findVillagePageAllInDto.getKeyWord() );
        villageEntity.setHaveHouseType( findVillagePageAllInDto.getHaveHouseType() );
        villageEntity.setUseBattleMap( findVillagePageAllInDto.getUseBattleMap() );
        villageEntity.setLevel( findVillagePageAllInDto.getLevel() );
        villageEntity.setSourceFlag( findVillagePageAllInDto.getSourceFlag() );
        villageEntity.setIsMaster( findVillagePageAllInDto.getIsMaster() );
        villageEntity.setVillageType( findVillagePageAllInDto.getVillageType() );
        villageEntity.setParentName( findVillagePageAllInDto.getParentName() );
        villageEntity.setMatchingFlag( findVillagePageAllInDto.getMatchingFlag() );
        villageEntity.setOpenDateStart( findVillagePageAllInDto.getOpenDateStart() );
        villageEntity.setOpenDateEnd( findVillagePageAllInDto.getOpenDateEnd() );
        villageEntity.setReceiveDateStart( findVillagePageAllInDto.getReceiveDateStart() );
        villageEntity.setReceiveDateEnd( findVillagePageAllInDto.getReceiveDateEnd() );
        villageEntity.setStartDateStart( findVillagePageAllInDto.getStartDateStart() );
        villageEntity.setStartDateEnd( findVillagePageAllInDto.getStartDateEnd() );
        List<String> list = findVillagePageAllInDto.getVillageTypeList();
        if ( list != null ) {
            villageEntity.setVillageTypeList( new ArrayList<String>( list ) );
        }
        List<Integer> list1 = findVillagePageAllInDto.getLevelList();
        if ( list1 != null ) {
            villageEntity.setLevelList( new ArrayList<Integer>( list1 ) );
        }
        List<Integer> list2 = findVillagePageAllInDto.getSourceFlagList();
        if ( list2 != null ) {
            villageEntity.setSourceFlagList( new ArrayList<Integer>( list2 ) );
        }
        List<Integer> list3 = findVillagePageAllInDto.getDecorationTypeList();
        if ( list3 != null ) {
            villageEntity.setDecorationTypeList( new ArrayList<Integer>( list3 ) );
        }
        List<Long> list4 = findVillagePageAllInDto.getDecorateCompanyIdList();
        if ( list4 != null ) {
            villageEntity.setDecorateCompanyIdList( new ArrayList<Long>( list4 ) );
        }
        villageEntity.setDecorateCompanyIdIsNUll( findVillagePageAllInDto.getDecorateCompanyIdIsNUll() );
        villageEntity.setNamesFlag( findVillagePageAllInDto.getNamesFlag() );
        List<Long> list5 = findVillagePageAllInDto.getOrderStoreIdList();
        if ( list5 != null ) {
            villageEntity.setOrderStoreIdList( new ArrayList<Long>( list5 ) );
        }

        return villageEntity;
    }

    @Override
    public VillageEntity QueryVillageDtoToVillageEntity(QueryVillageDto queryVillageDto) {
        if ( queryVillageDto == null ) {
            return null;
        }

        VillageEntity villageEntity = new VillageEntity();

        villageEntity.setName( queryVillageDto.getName() );
        villageEntity.setProvicenId( queryVillageDto.getProvicenId() );
        villageEntity.setCityId( queryVillageDto.getCityId() );
        villageEntity.setCountyId( queryVillageDto.getCountyId() );
        villageEntity.setExcludeId( queryVillageDto.getExcludeId() );

        return villageEntity;
    }

    @Override
    public List<FindVillagePageAllOutDto> villageEntityListToFindVillagePageAllOutDtoList(List<VillageEntity> villageEntityList) {
        if ( villageEntityList == null ) {
            return null;
        }

        List<FindVillagePageAllOutDto> list = new ArrayList<FindVillagePageAllOutDto>( villageEntityList.size() );
        for ( VillageEntity villageEntity : villageEntityList ) {
            list.add( villageEntityToFindVillagePageAllOutDto( villageEntity ) );
        }

        return list;
    }

    @Override
    public FindVillagePageAllOutDto villageEntityToFindVillagePageAllOutDto(VillageEntity villageEntity) {
        if ( villageEntity == null ) {
            return null;
        }

        FindVillagePageAllOutDto findVillagePageAllOutDto = new FindVillagePageAllOutDto();

        findVillagePageAllOutDto.setId( villageEntity.getId() );
        findVillagePageAllOutDto.setName( villageEntity.getName() );
        findVillagePageAllOutDto.setProvicenId( villageEntity.getProvicenId() );
        findVillagePageAllOutDto.setCityId( villageEntity.getCityId() );
        findVillagePageAllOutDto.setCountyId( villageEntity.getCountyId() );
        findVillagePageAllOutDto.setFinishTime( villageEntity.getFinishTime() );
        findVillagePageAllOutDto.setDeveloper( villageEntity.getDeveloper() );
        findVillagePageAllOutDto.setNum( villageEntity.getNum() );
        findVillagePageAllOutDto.setLongitude( villageEntity.getLongitude() );
        findVillagePageAllOutDto.setLatitude( villageEntity.getLatitude() );
        findVillagePageAllOutDto.setProvicenName( villageEntity.getProvicenName() );
        findVillagePageAllOutDto.setCityName( villageEntity.getCityName() );
        findVillagePageAllOutDto.setCountyName( villageEntity.getCountyName() );
        findVillagePageAllOutDto.setStoreId( villageEntity.getStoreId() );
        findVillagePageAllOutDto.setStoreName( villageEntity.getStoreName() );
        findVillagePageAllOutDto.setAddress( villageEntity.getAddress() );
        findVillagePageAllOutDto.setNote( villageEntity.getNote() );
        findVillagePageAllOutDto.setVillageManagers( villageEntity.getVillageManagers() );
        findVillagePageAllOutDto.setAvgPrice( villageEntity.getAvgPrice() );
        findVillagePageAllOutDto.setBuildingNum( villageEntity.getBuildingNum() );
        findVillagePageAllOutDto.setVillageType( villageEntity.getVillageType() );
        if ( villageEntity.getUseBattleMap() != null ) {
            findVillagePageAllOutDto.setUseBattleMap( String.valueOf( villageEntity.getUseBattleMap() ) );
        }
        if ( villageEntity.getIsMaster() != null ) {
            findVillagePageAllOutDto.setIsMaster( String.valueOf( villageEntity.getIsMaster() ) );
        }
        findVillagePageAllOutDto.setParentId( villageEntity.getParentId() );
        findVillagePageAllOutDto.setParentName( villageEntity.getParentName() );
        findVillagePageAllOutDto.setStreetId( villageEntity.getStreetId() );
        findVillagePageAllOutDto.setManageCompany( villageEntity.getManageCompany() );
        findVillagePageAllOutDto.setBlankNum( villageEntity.getBlankNum() );
        findVillagePageAllOutDto.setHardcoverNum( villageEntity.getHardcoverNum() );
        findVillagePageAllOutDto.setFloorState( villageEntity.getFloorState() );
        findVillagePageAllOutDto.setBelongPlate( villageEntity.getBelongPlate() );
        findVillagePageAllOutDto.setHouseTypeRemark( villageEntity.getHouseTypeRemark() );
        findVillagePageAllOutDto.setEngineeringProgress( villageEntity.getEngineeringProgress() );
        findVillagePageAllOutDto.setRightYear( villageEntity.getRightYear() );
        if ( villageEntity.getSalesStatus() != null ) {
            findVillagePageAllOutDto.setSalesStatus( String.valueOf( villageEntity.getSalesStatus() ) );
        }
        findVillagePageAllOutDto.setStartDate( villageEntity.getStartDate() );
        findVillagePageAllOutDto.setReceiveDate( villageEntity.getReceiveDate() );
        findVillagePageAllOutDto.setOpenDate( villageEntity.getOpenDate() );
        if ( villageEntity.getLevel() != null ) {
            findVillagePageAllOutDto.setLevel( String.valueOf( villageEntity.getLevel() ) );
        }
        findVillagePageAllOutDto.setVillageSize( villageEntity.getVillageSize() );
        findVillagePageAllOutDto.setBrandAbility( villageEntity.getBrandAbility() );
        findVillagePageAllOutDto.setPotential( villageEntity.getPotential() );
        findVillagePageAllOutDto.setSourceFlag( villageEntity.getSourceFlag() );
        findVillagePageAllOutDto.setDecorationType( villageEntity.getDecorationType() );
        findVillagePageAllOutDto.setDecorationTypeName( villageEntity.getDecorationTypeName() );

        return findVillagePageAllOutDto;
    }

    @Override
    public FindVillageByIdOutDto villageEntityToFindVillageByIdOutDto(VillageEntity villageEntity) {
        if ( villageEntity == null ) {
            return null;
        }

        FindVillageByIdOutDto findVillageByIdOutDto = new FindVillageByIdOutDto();

        findVillageByIdOutDto.setId( villageEntity.getId() );
        findVillageByIdOutDto.setName( villageEntity.getName() );
        findVillageByIdOutDto.setProvicenId( villageEntity.getProvicenId() );
        findVillageByIdOutDto.setProvicenName( villageEntity.getProvicenName() );
        findVillageByIdOutDto.setCityId( villageEntity.getCityId() );
        findVillageByIdOutDto.setCityName( villageEntity.getCityName() );
        findVillageByIdOutDto.setCountyId( villageEntity.getCountyId() );
        findVillageByIdOutDto.setCountyName( villageEntity.getCountyName() );
        findVillageByIdOutDto.setAddress( villageEntity.getAddress() );
        findVillageByIdOutDto.setFinishTime( villageEntity.getFinishTime() );
        findVillageByIdOutDto.setDeveloper( villageEntity.getDeveloper() );
        findVillageByIdOutDto.setNum( villageEntity.getNum() );
        findVillageByIdOutDto.setNote( villageEntity.getNote() );
        findVillageByIdOutDto.setStoreId( villageEntity.getStoreId() );
        findVillageByIdOutDto.setLongitude( villageEntity.getLongitude() );
        findVillageByIdOutDto.setLatitude( villageEntity.getLatitude() );
        if ( villageEntity.getAvgPrice() != null ) {
            findVillageByIdOutDto.setAvgPrice( villageEntity.getAvgPrice().toString() );
        }
        findVillageByIdOutDto.setBuildingNum( villageEntity.getBuildingNum() );
        findVillageByIdOutDto.setVillageManagers( villageEntity.getVillageManagers() );
        findVillageByIdOutDto.setHaveHouseType( villageEntity.getHaveHouseType() );
        findVillageByIdOutDto.setParentId( villageEntity.getParentId() );
        findVillageByIdOutDto.setFloorState( villageEntity.getFloorState() );
        findVillageByIdOutDto.setUseBattleMap( villageEntity.getUseBattleMap() );
        findVillageByIdOutDto.setLevel( villageEntity.getLevel() );
        findVillageByIdOutDto.setBelongPlate( villageEntity.getBelongPlate() );
        findVillageByIdOutDto.setHouseTypeRemark( villageEntity.getHouseTypeRemark() );
        findVillageByIdOutDto.setEngineeringProgress( villageEntity.getEngineeringProgress() );
        findVillageByIdOutDto.setRightYear( villageEntity.getRightYear() );
        findVillageByIdOutDto.setSalesStatus( villageEntity.getSalesStatus() );
        findVillageByIdOutDto.setStartDate( villageEntity.getStartDate() );
        findVillageByIdOutDto.setSourceFlag( villageEntity.getSourceFlag() );
        findVillageByIdOutDto.setBlankNum( villageEntity.getBlankNum() );
        findVillageByIdOutDto.setHardcoverNum( villageEntity.getHardcoverNum() );
        findVillageByIdOutDto.setIsMaster( villageEntity.getIsMaster() );
        findVillageByIdOutDto.setReceiveDate( villageEntity.getReceiveDate() );
        findVillageByIdOutDto.setOpenDate( villageEntity.getOpenDate() );
        findVillageByIdOutDto.setManageCompany( villageEntity.getManageCompany() );
        findVillageByIdOutDto.setParentName( villageEntity.getParentName() );
        findVillageByIdOutDto.setVillageType( villageEntity.getVillageType() );
        findVillageByIdOutDto.setVillageSize( villageEntity.getVillageSize() );
        findVillageByIdOutDto.setBrandAbility( villageEntity.getBrandAbility() );
        findVillageByIdOutDto.setPotential( villageEntity.getPotential() );
        findVillageByIdOutDto.setDecorationType( villageEntity.getDecorationType() );
        findVillageByIdOutDto.setDecorationTypeName( villageEntity.getDecorationTypeName() );

        return findVillageByIdOutDto;
    }

    @Override
    public List<FindVillagePageAllByCityOutDto> villageEntityListToFindVillagePageAllByCityOutDtoList(List<VillageEntity> villageEntityList) {
        if ( villageEntityList == null ) {
            return null;
        }

        List<FindVillagePageAllByCityOutDto> list = new ArrayList<FindVillagePageAllByCityOutDto>( villageEntityList.size() );
        for ( VillageEntity villageEntity : villageEntityList ) {
            list.add( villageEntityToFindVillagePageAllByCityOutDto( villageEntity ) );
        }

        return list;
    }

    @Override
    public FindVillagePageAllByCityOutDto villageEntityToFindVillagePageAllByCityOutDto(VillageEntity villageEntity) {
        if ( villageEntity == null ) {
            return null;
        }

        FindVillagePageAllByCityOutDto findVillagePageAllByCityOutDto = new FindVillagePageAllByCityOutDto();

        findVillagePageAllByCityOutDto.setId( villageEntity.getId() );
        findVillagePageAllByCityOutDto.setName( villageEntity.getName() );
        findVillagePageAllByCityOutDto.setProvicenId( villageEntity.getProvicenId() );
        findVillagePageAllByCityOutDto.setCityId( villageEntity.getCityId() );
        findVillagePageAllByCityOutDto.setCountyId( villageEntity.getCountyId() );
        findVillagePageAllByCityOutDto.setProvicenName( villageEntity.getProvicenName() );
        findVillagePageAllByCityOutDto.setCityName( villageEntity.getCityName() );
        findVillagePageAllByCityOutDto.setCountyName( villageEntity.getCountyName() );
        findVillagePageAllByCityOutDto.setAddress( villageEntity.getAddress() );

        return findVillagePageAllByCityOutDto;
    }

    @Override
    public UpdateVillageDto2 updateVillageDtoTransfer(UpdateVillageDto updateVillageDto) {
        if ( updateVillageDto == null ) {
            return null;
        }

        UpdateVillageDto2 updateVillageDto2 = new UpdateVillageDto2();

        updateVillageDto2.setVillageId( updateVillageDto.getVillageId() );
        updateVillageDto2.setProvicenId( updateVillageDto.getProvicenId() );
        updateVillageDto2.setCityId( updateVillageDto.getCityId() );
        updateVillageDto2.setCountyId( updateVillageDto.getCountyId() );
        updateVillageDto2.setProvicenName( updateVillageDto.getProvicenName() );
        updateVillageDto2.setCityName( updateVillageDto.getCityName() );
        updateVillageDto2.setCountyName( updateVillageDto.getCountyName() );
        updateVillageDto2.setAddress( updateVillageDto.getAddress() );
        updateVillageDto2.setModifiedBy( updateVillageDto.getModifiedBy() );
        updateVillageDto2.setIsAddressUpdate( updateVillageDto.getIsAddressUpdate() );

        return updateVillageDto2;
    }

    @Override
    public UpdateVillageDto transferVillageDto(VillageDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        UpdateVillageDto updateVillageDto = new UpdateVillageDto();

        updateVillageDto.setId( dto.getId() );
        updateVillageDto.setName( dto.getName() );
        updateVillageDto.setLongitude( dto.getLongitude() );
        updateVillageDto.setLatitude( dto.getLatitude() );
        updateVillageDto.setIsMaster( dto.getIsMaster() );
        updateVillageDto.setVillageId( dto.getVillageId() );
        updateVillageDto.setVillageName( dto.getVillageName() );
        updateVillageDto.setProvicenId( dto.getProvicenId() );
        updateVillageDto.setCityId( dto.getCityId() );
        updateVillageDto.setCountyId( dto.getCountyId() );
        updateVillageDto.setProvicenName( dto.getProvicenName() );
        updateVillageDto.setCityName( dto.getCityName() );
        updateVillageDto.setCountyName( dto.getCountyName() );
        updateVillageDto.setAddress( dto.getAddress() );
        updateVillageDto.setNum( dto.getNum() );
        updateVillageDto.setBlankNum( dto.getBlankNum() );
        updateVillageDto.setHardcoverNum( dto.getHardcoverNum() );
        updateVillageDto.setFloorState( dto.getFloorState() );
        updateVillageDto.setHouseTypeRemark( dto.getHouseTypeRemark() );
        updateVillageDto.setDeveloper( dto.getDeveloper() );
        updateVillageDto.setStartDate( dto.getStartDate() );
        updateVillageDto.setFinishTime( dto.getFinishTime() );
        updateVillageDto.setOpenDate( dto.getOpenDate() );
        updateVillageDto.setReceiveDate( dto.getReceiveDate() );
        updateVillageDto.setBuildingNum( dto.getBuildingNum() );
        updateVillageDto.setAvgPrice( dto.getAvgPrice() );
        updateVillageDto.setVillageType( dto.getVillageType() );
        updateVillageDto.setManageCompany( dto.getManageCompany() );
        List<HouseTypeOutDto> list = dto.getHouseTypeList();
        if ( list != null ) {
            updateVillageDto.setHouseTypeList( new ArrayList<HouseTypeOutDto>( list ) );
        }
        updateVillageDto.setRightYear( dto.getRightYear() );
        updateVillageDto.setEngineeringProgress( dto.getEngineeringProgress() );
        updateVillageDto.setBelongPlate( dto.getBelongPlate() );
        updateVillageDto.setSalesStatus( dto.getSalesStatus() );
        updateVillageDto.setSalesStatusValue( dto.getSalesStatusValue() );
        updateVillageDto.setLevel( dto.getLevel() );
        updateVillageDto.setModifiedBy( dto.getModifiedBy() );
        updateVillageDto.setNote( dto.getNote() );
        updateVillageDto.setUseBattleMap( dto.getUseBattleMap() );
        updateVillageDto.setLevelValue( dto.getLevelValue() );
        updateVillageDto.setVillageTypeValue( dto.getVillageTypeValue() );
        updateVillageDto.setIsAddressUpdate( dto.getIsAddressUpdate() );
        updateVillageDto.setDecorationType( dto.getDecorationType() );
        updateVillageDto.setDecorationTypeName( dto.getDecorationTypeName() );

        return updateVillageDto;
    }
}
