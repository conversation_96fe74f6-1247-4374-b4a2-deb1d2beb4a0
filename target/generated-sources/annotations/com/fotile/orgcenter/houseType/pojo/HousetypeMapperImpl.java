package com.fotile.orgcenter.houseType.pojo;

import com.fotile.orgcenter.houseType.pojo.dto.AddHousetypeInDto;
import com.fotile.orgcenter.houseType.pojo.dto.AddHousetypeOutDto;
import com.fotile.orgcenter.houseType.pojo.dto.DelHousetypeInDto;
import com.fotile.orgcenter.houseType.pojo.dto.FindHousetypeByIdOutDto;
import com.fotile.orgcenter.houseType.pojo.dto.FindHousetypePageAllOutDto;
import com.fotile.orgcenter.houseType.pojo.dto.UpdateHousetypeInDto;
import com.fotile.orgcenter.houseType.pojo.entity.HousetypeEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-18T14:09:48+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class HousetypeMapperImpl implements HousetypeMapper {

    @Override
    public HousetypeEntity addHousetypeInDtoToHousetypeEntity(AddHousetypeInDto addHousetypeInDto) {
        if ( addHousetypeInDto == null ) {
            return null;
        }

        HousetypeEntity housetypeEntity = new HousetypeEntity();

        housetypeEntity.setId( addHousetypeInDto.getId() );
        housetypeEntity.setVillageId( addHousetypeInDto.getVillageId() );
        housetypeEntity.setPic( addHousetypeInDto.getPic() );
        housetypeEntity.setHouseType( addHousetypeInDto.getHouseType() );
        housetypeEntity.setHouseArea( addHousetypeInDto.getHouseArea() );
        housetypeEntity.setKitchenType( addHousetypeInDto.getKitchenType() );
        housetypeEntity.setKitchenArea( addHousetypeInDto.getKitchenArea() );
        housetypeEntity.setCode( addHousetypeInDto.getCode() );
        housetypeEntity.setFullViewPic( addHousetypeInDto.getFullViewPic() );

        return housetypeEntity;
    }

    @Override
    public AddHousetypeOutDto housetypeEntityToAddHousetypeOutDto(HousetypeEntity housetypeEntity) {
        if ( housetypeEntity == null ) {
            return null;
        }

        AddHousetypeOutDto addHousetypeOutDto = new AddHousetypeOutDto();

        addHousetypeOutDto.setId( housetypeEntity.getId() );
        addHousetypeOutDto.setVillageId( housetypeEntity.getVillageId() );
        addHousetypeOutDto.setPic( housetypeEntity.getPic() );
        addHousetypeOutDto.setHouseType( housetypeEntity.getHouseType() );
        addHousetypeOutDto.setHouseArea( housetypeEntity.getHouseArea() );
        addHousetypeOutDto.setKitchenType( housetypeEntity.getKitchenType() );
        addHousetypeOutDto.setKitchenArea( housetypeEntity.getKitchenArea() );
        addHousetypeOutDto.setCode( housetypeEntity.getCode() );

        return addHousetypeOutDto;
    }

    @Override
    public HousetypeEntity updateHousetypeInDtoToHousetypeEntity(UpdateHousetypeInDto updateHousetypeInDto) {
        if ( updateHousetypeInDto == null ) {
            return null;
        }

        HousetypeEntity housetypeEntity = new HousetypeEntity();

        housetypeEntity.setCreatedBy( updateHousetypeInDto.getCreatedBy() );
        housetypeEntity.setModifiedBy( updateHousetypeInDto.getModifiedBy() );
        housetypeEntity.setCreatedDate( updateHousetypeInDto.getCreatedDate() );
        housetypeEntity.setId( updateHousetypeInDto.getId() );
        housetypeEntity.setVillageId( updateHousetypeInDto.getVillageId() );
        housetypeEntity.setIsDeleted( updateHousetypeInDto.getIsDeleted() );
        housetypeEntity.setPic( updateHousetypeInDto.getPic() );
        housetypeEntity.setHouseType( updateHousetypeInDto.getHouseType() );
        housetypeEntity.setHouseArea( updateHousetypeInDto.getHouseArea() );
        housetypeEntity.setKitchenType( updateHousetypeInDto.getKitchenType() );
        housetypeEntity.setKitchenArea( updateHousetypeInDto.getKitchenArea() );
        housetypeEntity.setCode( updateHousetypeInDto.getCode() );
        housetypeEntity.setModifiedDate( updateHousetypeInDto.getModifiedDate() );
        housetypeEntity.setFullViewPic( updateHousetypeInDto.getFullViewPic() );

        return housetypeEntity;
    }

    @Override
    public List<FindHousetypePageAllOutDto> housetypeEntityListToFindHousetypePageAllOutDtoList(List<HousetypeEntity> housetypeEntityList) {
        if ( housetypeEntityList == null ) {
            return null;
        }

        List<FindHousetypePageAllOutDto> list = new ArrayList<FindHousetypePageAllOutDto>( housetypeEntityList.size() );
        for ( HousetypeEntity housetypeEntity : housetypeEntityList ) {
            list.add( housetypeEntityToFindHousetypePageAllOutDto( housetypeEntity ) );
        }

        return list;
    }

    @Override
    public FindHousetypePageAllOutDto housetypeEntityToFindHousetypePageAllOutDto(HousetypeEntity housetypeEntity) {
        if ( housetypeEntity == null ) {
            return null;
        }

        FindHousetypePageAllOutDto findHousetypePageAllOutDto = new FindHousetypePageAllOutDto();

        findHousetypePageAllOutDto.setId( housetypeEntity.getId() );
        findHousetypePageAllOutDto.setPic( housetypeEntity.getPic() );
        findHousetypePageAllOutDto.setFullViewPic( housetypeEntity.getFullViewPic() );
        findHousetypePageAllOutDto.setHouseType( housetypeEntity.getHouseType() );
        findHousetypePageAllOutDto.setHouseArea( housetypeEntity.getHouseArea() );
        findHousetypePageAllOutDto.setKitchenType( housetypeEntity.getKitchenType() );
        findHousetypePageAllOutDto.setKitchenArea( housetypeEntity.getKitchenArea() );
        findHousetypePageAllOutDto.setCode( housetypeEntity.getCode() );
        findHousetypePageAllOutDto.setVillageId( housetypeEntity.getVillageId() );
        findHousetypePageAllOutDto.setModifiedDate( housetypeEntity.getModifiedDate() );

        return findHousetypePageAllOutDto;
    }

    @Override
    public FindHousetypeByIdOutDto housetypeEntityToFindHousetypeByIdOutDto(HousetypeEntity housetypeEntity) {
        if ( housetypeEntity == null ) {
            return null;
        }

        FindHousetypeByIdOutDto findHousetypeByIdOutDto = new FindHousetypeByIdOutDto();

        findHousetypeByIdOutDto.setId( housetypeEntity.getId() );
        findHousetypeByIdOutDto.setPic( housetypeEntity.getPic() );
        findHousetypeByIdOutDto.setHouseType( housetypeEntity.getHouseType() );
        findHousetypeByIdOutDto.setHouseArea( housetypeEntity.getHouseArea() );
        findHousetypeByIdOutDto.setKitchenType( housetypeEntity.getKitchenType() );
        findHousetypeByIdOutDto.setKitchenArea( housetypeEntity.getKitchenArea() );
        findHousetypeByIdOutDto.setCode( housetypeEntity.getCode() );
        findHousetypeByIdOutDto.setVillageId( housetypeEntity.getVillageId() );

        return findHousetypeByIdOutDto;
    }

    @Override
    public HousetypeEntity delHousetypeInDtoToHousetypeEntity(DelHousetypeInDto delHousetypeInDto) {
        if ( delHousetypeInDto == null ) {
            return null;
        }

        HousetypeEntity housetypeEntity = new HousetypeEntity();

        housetypeEntity.setCreatedBy( delHousetypeInDto.getCreatedBy() );
        housetypeEntity.setModifiedBy( delHousetypeInDto.getModifiedBy() );
        housetypeEntity.setCreatedDate( delHousetypeInDto.getCreatedDate() );
        housetypeEntity.setId( delHousetypeInDto.getId() );
        housetypeEntity.setIsDeleted( delHousetypeInDto.getIsDeleted() );
        housetypeEntity.setModifiedDate( delHousetypeInDto.getModifiedDate() );

        return housetypeEntity;
    }
}
