package com.fotile.orgcenter.operatorFollowOrgLog.pojo;

import com.fotile.orgcenter.operatorFollowOrgLog.pojo.dto.OperatorFollowOrgLogInsertDTO;
import com.fotile.orgcenter.operatorFollowOrgLog.pojo.dto.OperatorFollowOrgLogSelectDTO;
import com.fotile.orgcenter.operatorFollowOrgLog.pojo.dto.SelectByOperatorFollowOrgLogOutDTO;
import com.fotile.orgcenter.operatorFollowOrgLog.pojo.entity.OperatorFollowOrgLog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-18T14:09:48+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class OperatorFollowOrgLogMapperImpl implements OperatorFollowOrgLogMapper {

    @Override
    public OperatorFollowOrgLog OperatorFollowOrgLogInsertDTOTOOperatorFollowOrgLog(OperatorFollowOrgLogInsertDTO operatorFollowOrgLogInsertDTO) {
        if ( operatorFollowOrgLogInsertDTO == null ) {
            return null;
        }

        OperatorFollowOrgLog operatorFollowOrgLog = new OperatorFollowOrgLog();

        operatorFollowOrgLog.setRecordCreatedName( operatorFollowOrgLogInsertDTO.getRecordCreatedName() );
        operatorFollowOrgLog.setRecordCreatedTime( operatorFollowOrgLogInsertDTO.getRecordCreatedTime() );
        operatorFollowOrgLog.setSourceTableName( operatorFollowOrgLogInsertDTO.getSourceTableName() );
        operatorFollowOrgLog.setSourceId( operatorFollowOrgLogInsertDTO.getSourceId() );
        operatorFollowOrgLog.setRecordType( operatorFollowOrgLogInsertDTO.getRecordType() );
        operatorFollowOrgLog.setRecordCode( operatorFollowOrgLogInsertDTO.getRecordCode() );
        operatorFollowOrgLog.setRecordName( operatorFollowOrgLogInsertDTO.getRecordName() );
        operatorFollowOrgLog.setContentText( operatorFollowOrgLogInsertDTO.getContentText() );
        operatorFollowOrgLog.setOldData( operatorFollowOrgLogInsertDTO.getOldData() );
        operatorFollowOrgLog.setNewData( operatorFollowOrgLogInsertDTO.getNewData() );
        operatorFollowOrgLog.setPictureUrl1( operatorFollowOrgLogInsertDTO.getPictureUrl1() );
        operatorFollowOrgLog.setPictureUrl2( operatorFollowOrgLogInsertDTO.getPictureUrl2() );
        operatorFollowOrgLog.setPictureUrl3( operatorFollowOrgLogInsertDTO.getPictureUrl3() );
        operatorFollowOrgLog.setAddress( operatorFollowOrgLogInsertDTO.getAddress() );

        return operatorFollowOrgLog;
    }

    @Override
    public OperatorFollowOrgLogSelectDTO OperatorFollowOrgLogTOOperatorFollowOrgLogUpdateAndSelectDTO(OperatorFollowOrgLog operatorFollowOrgLog) {
        if ( operatorFollowOrgLog == null ) {
            return null;
        }

        OperatorFollowOrgLogSelectDTO operatorFollowOrgLogSelectDTO = new OperatorFollowOrgLogSelectDTO();

        operatorFollowOrgLogSelectDTO.setIsDeleted( operatorFollowOrgLog.getIsDeleted() );
        operatorFollowOrgLogSelectDTO.setCreatedBy( operatorFollowOrgLog.getCreatedBy() );
        operatorFollowOrgLogSelectDTO.setCreatedDate( operatorFollowOrgLog.getCreatedDate() );
        operatorFollowOrgLogSelectDTO.setModifiedBy( operatorFollowOrgLog.getModifiedBy() );
        operatorFollowOrgLogSelectDTO.setModifiedDate( operatorFollowOrgLog.getModifiedDate() );
        operatorFollowOrgLogSelectDTO.setRecordCreatedName( operatorFollowOrgLog.getRecordCreatedName() );
        operatorFollowOrgLogSelectDTO.setRecordCreatedTime( operatorFollowOrgLog.getRecordCreatedTime() );
        operatorFollowOrgLogSelectDTO.setSourceTableName( operatorFollowOrgLog.getSourceTableName() );
        operatorFollowOrgLogSelectDTO.setSourceId( operatorFollowOrgLog.getSourceId() );
        operatorFollowOrgLogSelectDTO.setRecordType( operatorFollowOrgLog.getRecordType() );
        operatorFollowOrgLogSelectDTO.setRecordCode( operatorFollowOrgLog.getRecordCode() );
        operatorFollowOrgLogSelectDTO.setRecordName( operatorFollowOrgLog.getRecordName() );
        operatorFollowOrgLogSelectDTO.setContentText( operatorFollowOrgLog.getContentText() );
        operatorFollowOrgLogSelectDTO.setOldData( operatorFollowOrgLog.getOldData() );
        operatorFollowOrgLogSelectDTO.setNewData( operatorFollowOrgLog.getNewData() );
        operatorFollowOrgLogSelectDTO.setPictureUrl1( operatorFollowOrgLog.getPictureUrl1() );
        operatorFollowOrgLogSelectDTO.setPictureUrl2( operatorFollowOrgLog.getPictureUrl2() );
        operatorFollowOrgLogSelectDTO.setPictureUrl3( operatorFollowOrgLog.getPictureUrl3() );

        return operatorFollowOrgLogSelectDTO;
    }

    @Override
    public SelectByOperatorFollowOrgLogOutDTO SelectByOperatorFollowOrgLogOutDTOTOOperatorFollowOrgLog(OperatorFollowOrgLog operatorFollowOrgLog) {
        if ( operatorFollowOrgLog == null ) {
            return null;
        }

        SelectByOperatorFollowOrgLogOutDTO selectByOperatorFollowOrgLogOutDTO = new SelectByOperatorFollowOrgLogOutDTO();

        selectByOperatorFollowOrgLogOutDTO.setOperatorId( operatorFollowOrgLog.getId() );
        selectByOperatorFollowOrgLogOutDTO.setRecordCreatedName( operatorFollowOrgLog.getRecordCreatedName() );
        selectByOperatorFollowOrgLogOutDTO.setRecordCreatedTime( operatorFollowOrgLog.getRecordCreatedTime() );
        selectByOperatorFollowOrgLogOutDTO.setSourceTableName( operatorFollowOrgLog.getSourceTableName() );
        selectByOperatorFollowOrgLogOutDTO.setSourceId( operatorFollowOrgLog.getSourceId() );
        selectByOperatorFollowOrgLogOutDTO.setRecordType( operatorFollowOrgLog.getRecordType() );
        selectByOperatorFollowOrgLogOutDTO.setRecordCode( operatorFollowOrgLog.getRecordCode() );
        selectByOperatorFollowOrgLogOutDTO.setRecordName( operatorFollowOrgLog.getRecordName() );
        selectByOperatorFollowOrgLogOutDTO.setContentText( operatorFollowOrgLog.getContentText() );
        selectByOperatorFollowOrgLogOutDTO.setOldData( operatorFollowOrgLog.getOldData() );
        selectByOperatorFollowOrgLogOutDTO.setNewData( operatorFollowOrgLog.getNewData() );
        selectByOperatorFollowOrgLogOutDTO.setPictureUrl1( operatorFollowOrgLog.getPictureUrl1() );
        selectByOperatorFollowOrgLogOutDTO.setPictureUrl2( operatorFollowOrgLog.getPictureUrl2() );
        selectByOperatorFollowOrgLogOutDTO.setPictureUrl3( operatorFollowOrgLog.getPictureUrl3() );
        selectByOperatorFollowOrgLogOutDTO.setAddress( operatorFollowOrgLog.getAddress() );

        return selectByOperatorFollowOrgLogOutDTO;
    }

    @Override
    public List<SelectByOperatorFollowOrgLogOutDTO> pageInfoSelectByOperatorFollowOrgLogOutDTOTOPageInfoOperatorFollowOrgLog(List<OperatorFollowOrgLog> pageInfo) {
        if ( pageInfo == null ) {
            return null;
        }

        List<SelectByOperatorFollowOrgLogOutDTO> list = new ArrayList<SelectByOperatorFollowOrgLogOutDTO>( pageInfo.size() );
        for ( OperatorFollowOrgLog operatorFollowOrgLog : pageInfo ) {
            list.add( SelectByOperatorFollowOrgLogOutDTOTOOperatorFollowOrgLog( operatorFollowOrgLog ) );
        }

        return list;
    }

    @Override
    public OperatorFollowOrgLog OperatorFollowOrgLogInsertDTOTOOperatorFollowOrgLogSelectDTO(OperatorFollowOrgLogSelectDTO operatorFollowOrgLogSelectDTO) {
        if ( operatorFollowOrgLogSelectDTO == null ) {
            return null;
        }

        OperatorFollowOrgLog operatorFollowOrgLog = new OperatorFollowOrgLog();

        operatorFollowOrgLog.setIsDeleted( operatorFollowOrgLogSelectDTO.getIsDeleted() );
        operatorFollowOrgLog.setCreatedBy( operatorFollowOrgLogSelectDTO.getCreatedBy() );
        operatorFollowOrgLog.setModifiedBy( operatorFollowOrgLogSelectDTO.getModifiedBy() );
        operatorFollowOrgLog.setCreatedDate( operatorFollowOrgLogSelectDTO.getCreatedDate() );
        operatorFollowOrgLog.setModifiedDate( operatorFollowOrgLogSelectDTO.getModifiedDate() );
        operatorFollowOrgLog.setRecordCreatedName( operatorFollowOrgLogSelectDTO.getRecordCreatedName() );
        operatorFollowOrgLog.setRecordCreatedTime( operatorFollowOrgLogSelectDTO.getRecordCreatedTime() );
        operatorFollowOrgLog.setSourceTableName( operatorFollowOrgLogSelectDTO.getSourceTableName() );
        operatorFollowOrgLog.setSourceId( operatorFollowOrgLogSelectDTO.getSourceId() );
        operatorFollowOrgLog.setRecordType( operatorFollowOrgLogSelectDTO.getRecordType() );
        operatorFollowOrgLog.setRecordCode( operatorFollowOrgLogSelectDTO.getRecordCode() );
        operatorFollowOrgLog.setRecordName( operatorFollowOrgLogSelectDTO.getRecordName() );
        operatorFollowOrgLog.setContentText( operatorFollowOrgLogSelectDTO.getContentText() );
        operatorFollowOrgLog.setOldData( operatorFollowOrgLogSelectDTO.getOldData() );
        operatorFollowOrgLog.setNewData( operatorFollowOrgLogSelectDTO.getNewData() );
        operatorFollowOrgLog.setPictureUrl1( operatorFollowOrgLogSelectDTO.getPictureUrl1() );
        operatorFollowOrgLog.setPictureUrl2( operatorFollowOrgLogSelectDTO.getPictureUrl2() );
        operatorFollowOrgLog.setPictureUrl3( operatorFollowOrgLogSelectDTO.getPictureUrl3() );

        return operatorFollowOrgLog;
    }
}
