package com.fotile.orgcenter.store.pojo;

import com.fotile.orgcenter.store.pojo.dto.AddStoreInDto;
import com.fotile.orgcenter.store.pojo.dto.AddStoreOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindNearestStoreOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindStoreByDistributorOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindStoreByIdOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindStoreByNameOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindStoreByOrgIdOutDto;
import com.fotile.orgcenter.store.pojo.dto.FindStoreListAllInDto;
import com.fotile.orgcenter.store.pojo.dto.FindStorePageAllInDto;
import com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportEmailAdviser;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportEmailArea;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportEmailCompany;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportEmailCustomerManager;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportEmailInDto;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportInDto;
import com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportOutDto;
import com.fotile.orgcenter.store.pojo.dto.SyncStoreDto;
import com.fotile.orgcenter.store.pojo.dto.UpdateByChangeProc;
import com.fotile.orgcenter.store.pojo.dto.UpdateStoreInDto;
import com.fotile.orgcenter.store.pojo.entity.StoreEntity;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-27T15:29:14+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class StoreMapperImpl implements StoreMapper {

    @Override
    public StoreEntity addStoreInDtoToStoreEntity(AddStoreInDto addStoreInDto) {
        if ( addStoreInDto == null ) {
            return null;
        }

        StoreEntity storeEntity = new StoreEntity();

        storeEntity.setId( addStoreInDto.getId() );
        storeEntity.setChannelCategoryId( addStoreInDto.getChannelCategoryId() );
        storeEntity.setChannelCategoryCode( addStoreInDto.getChannelCategoryCode() );
        storeEntity.setChannelCategoryName( addStoreInDto.getChannelCategoryName() );
        storeEntity.setChannelSubdivideId( addStoreInDto.getChannelSubdivideId() );
        storeEntity.setChannelSubdivideCode( addStoreInDto.getChannelSubdivideCode() );
        storeEntity.setChannelSubdivideName( addStoreInDto.getChannelSubdivideName() );
        storeEntity.setPlanCode( addStoreInDto.getPlanCode() );
        storeEntity.setOrgId( addStoreInDto.getOrgId() );
        storeEntity.setKeyWord( addStoreInDto.getKeyWord() );
        storeEntity.setCode( addStoreInDto.getCode() );
        storeEntity.setName( addStoreInDto.getName() );
        storeEntity.setCoverurl( addStoreInDto.getCoverurl() );
        storeEntity.setProvicenId( addStoreInDto.getProvicenId() );
        storeEntity.setProvicenName( addStoreInDto.getProvicenName() );
        storeEntity.setCityId( addStoreInDto.getCityId() );
        storeEntity.setCityName( addStoreInDto.getCityName() );
        storeEntity.setCountyId( addStoreInDto.getCountyId() );
        storeEntity.setCountyName( addStoreInDto.getCountyName() );
        storeEntity.setAddress( addStoreInDto.getAddress() );
        storeEntity.setAddress2( addStoreInDto.getAddress2() );
        storeEntity.setShoppingStart( addStoreInDto.getShoppingStart() );
        storeEntity.setShoppingEnd( addStoreInDto.getShoppingEnd() );
        storeEntity.setTel( addStoreInDto.getTel() );
        storeEntity.setLeaderId( addStoreInDto.getLeaderId() );
        storeEntity.setAlipayno( addStoreInDto.getAlipayno() );
        storeEntity.setAlipaynourl( addStoreInDto.getAlipaynourl() );
        storeEntity.setWechatno( addStoreInDto.getWechatno() );
        storeEntity.setWechatnourl( addStoreInDto.getWechatnourl() );
        storeEntity.setNote( addStoreInDto.getNote() );
        storeEntity.setMarketCapacity( addStoreInDto.getMarketCapacity() );
        storeEntity.setPopulation( addStoreInDto.getPopulation() );
        storeEntity.setStoreLevel( addStoreInDto.getStoreLevel() );
        storeEntity.setIsUnmanned( addStoreInDto.getIsUnmanned() );
        storeEntity.setSignShape( addStoreInDto.getSignShape() );
        storeEntity.setSignSize( addStoreInDto.getSignSize() );
        storeEntity.setSignCost( addStoreInDto.getSignCost() );
        storeEntity.setIsUniform( addStoreInDto.getIsUniform() );
        storeEntity.setUniformPic( addStoreInDto.getUniformPic() );
        storeEntity.setTopKitchens( addStoreInDto.getTopKitchens() );
        storeEntity.setDoorSize( addStoreInDto.getDoorSize() );
        storeEntity.setStatus( addStoreInDto.getStatus() );
        storeEntity.setLongitude( addStoreInDto.getLongitude() );
        storeEntity.setLatitude( addStoreInDto.getLatitude() );
        if ( addStoreInDto.getIsOpenKpoint() != null ) {
            storeEntity.setIsOpenKpoint( Integer.parseInt( addStoreInDto.getIsOpenKpoint() ) );
        }
        storeEntity.setCompanyId( addStoreInDto.getCompanyId() );
        storeEntity.setLiveDemo( addStoreInDto.getLiveDemo() );
        storeEntity.setLayoutCode( addStoreInDto.getLayoutCode() );
        storeEntity.setCompanyName( addStoreInDto.getCompanyName() );
        storeEntity.setParentId( addStoreInDto.getParentId() );
        storeEntity.setShoppingRemark( addStoreInDto.getShoppingRemark() );
        storeEntity.setTelRemark( addStoreInDto.getTelRemark() );
        storeEntity.setEmail( addStoreInDto.getEmail() );
        storeEntity.setEmailRemark( addStoreInDto.getEmailRemark() );
        storeEntity.setWechatRemark( addStoreInDto.getWechatRemark() );
        storeEntity.setAlipayRemark( addStoreInDto.getAlipayRemark() );
        storeEntity.setChannel( addStoreInDto.getChannel() );
        storeEntity.setWechat( addStoreInDto.getWechat() );
        storeEntity.setIsToDrp( addStoreInDto.getIsToDrp() );
        storeEntity.setToDrpStage( addStoreInDto.getToDrpStage() );
        storeEntity.setStoreType( addStoreInDto.getStoreType() );
        storeEntity.setCluesMsgUser( addStoreInDto.getCluesMsgUser() );
        storeEntity.setAbbreviation( addStoreInDto.getAbbreviation() );
        storeEntity.setDistributorId( addStoreInDto.getDistributorId() );
        storeEntity.setStoreTypeName( addStoreInDto.getStoreTypeName() );
        storeEntity.setArea( addStoreInDto.getArea() );
        storeEntity.setAreaCode( addStoreInDto.getAreaCode() );
        storeEntity.setAreaName( addStoreInDto.getAreaName() );
        storeEntity.setDevelopSalesmanId( addStoreInDto.getDevelopSalesmanId() );
        storeEntity.setDevelopSalesmanCode( addStoreInDto.getDevelopSalesmanCode() );
        storeEntity.setDevelopSalesmanName( addStoreInDto.getDevelopSalesmanName() );
        storeEntity.setDevelopSalesmanPhone( addStoreInDto.getDevelopSalesmanPhone() );
        storeEntity.setDistributorName( addStoreInDto.getDistributorName() );
        storeEntity.setPreAudit( addStoreInDto.getPreAudit() );
        storeEntity.setIntro( addStoreInDto.getIntro() );
        storeEntity.setConsultationPc( addStoreInDto.getConsultationPc() );
        storeEntity.setConsultationM( addStoreInDto.getConsultationM() );
        storeEntity.setDistributorCode( addStoreInDto.getDistributorCode() );
        storeEntity.setIsTransfer( addStoreInDto.getIsTransfer() );
        storeEntity.setIsToCheck( addStoreInDto.getIsToCheck() );
        storeEntity.setIfDecorationStore( addStoreInDto.getIfDecorationStore() );
        storeEntity.setDecorateStatus( addStoreInDto.getDecorateStatus() );
        storeEntity.setOpenDate( addStoreInDto.getOpenDate() );
        storeEntity.setCloseDate( addStoreInDto.getCloseDate() );
        storeEntity.setTerminalCheckDateAgain( addStoreInDto.getTerminalCheckDateAgain() );
        storeEntity.setTerminalCheckDate( addStoreInDto.getTerminalCheckDate() );
        storeEntity.setStoreMarketGrade( addStoreInDto.getStoreMarketGrade() );
        storeEntity.setStoreChannelCode( addStoreInDto.getStoreChannelCode() );
        storeEntity.setDistributorChannel( addStoreInDto.getDistributorChannel() );
        storeEntity.setDistributorChannelCode( addStoreInDto.getDistributorChannelCode() );
        storeEntity.setRelatedStoreId( addStoreInDto.getRelatedStoreId() );
        storeEntity.setIsVirtual( addStoreInDto.getIsVirtual() );
        storeEntity.setNeedTerminalBuild( addStoreInDto.getNeedTerminalBuild() );
        storeEntity.setManagerName( addStoreInDto.getManagerName() );
        storeEntity.setManagerPhone( addStoreInDto.getManagerPhone() );
        storeEntity.setStoreBizType( addStoreInDto.getStoreBizType() );
        storeEntity.setStaffs( addStoreInDto.getStaffs() );
        storeEntity.setPropertyRight( addStoreInDto.getPropertyRight() );
        storeEntity.setLeaseTerm( addStoreInDto.getLeaseTerm() );
        storeEntity.setBpmType( addStoreInDto.getBpmType() );
        storeEntity.setUsableArea( addStoreInDto.getUsableArea() );
        storeEntity.setWholeUsableArea( addStoreInDto.getWholeUsableArea() );
        storeEntity.setOpenType( addStoreInDto.getOpenType() );
        storeEntity.setEstimatedAnnualSales( addStoreInDto.getEstimatedAnnualSales() );
        storeEntity.setCompanyPkId( addStoreInDto.getCompanyPkId() );
        storeEntity.setStoreSubChannelCode( addStoreInDto.getStoreSubChannelCode() );
        storeEntity.setOutsideStoreName( addStoreInDto.getOutsideStoreName() );
        storeEntity.setEpsUsableArea( addStoreInDto.getEpsUsableArea() );
        storeEntity.setStadiumsIntroduce( addStoreInDto.getStadiumsIntroduce() );
        storeEntity.setIsOpenRent( addStoreInDto.getIsOpenRent() );
        storeEntity.setTravelTips( addStoreInDto.getTravelTips() );
        storeEntity.setAnnualRent( addStoreInDto.getAnnualRent() );
        storeEntity.setCluesOverdue( addStoreInDto.getCluesOverdue() );
        storeEntity.setDepositCluesOverdue( addStoreInDto.getDepositCluesOverdue() );
        storeEntity.setUsePos( addStoreInDto.getUsePos() );
        storeEntity.setTerminalImageScore( addStoreInDto.getTerminalImageScore() );
        storeEntity.setIsSceneFlag( addStoreInDto.getIsSceneFlag() );
        storeEntity.setSceneSuit( addStoreInDto.getSceneSuit() );
        storeEntity.setVersion( addStoreInDto.getVersion() );
        List<String> list = addStoreInDto.getRealKeyWordList();
        if ( list != null ) {
            storeEntity.setRealKeyWordList( new ArrayList<String>( list ) );
        }
        storeEntity.setOpenWyChannel( addStoreInDto.getOpenWyChannel() );
        storeEntity.setOpenZqChannel( addStoreInDto.getOpenZqChannel() );
        storeEntity.setOpenRqChannel( addStoreInDto.getOpenRqChannel() );
        storeEntity.setOldCompensateMerchantCode( addStoreInDto.getOldCompensateMerchantCode() );
        storeEntity.setTiktokCode( addStoreInDto.getTiktokCode() );
        storeEntity.setTiktokName( addStoreInDto.getTiktokName() );

        return storeEntity;
    }

    @Override
    public AddStoreOutDto storeEntityToAddStoreOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        AddStoreOutDto addStoreOutDto = new AddStoreOutDto();

        addStoreOutDto.setId( storeEntity.getId() );
        addStoreOutDto.setOrgId( storeEntity.getOrgId() );
        addStoreOutDto.setCode( storeEntity.getCode() );
        addStoreOutDto.setName( storeEntity.getName() );
        addStoreOutDto.setKeyWord( storeEntity.getKeyWord() );
        addStoreOutDto.setCoverurl( storeEntity.getCoverurl() );
        addStoreOutDto.setAddress( storeEntity.getAddress() );
        addStoreOutDto.setProvicenId( storeEntity.getProvicenId() );
        addStoreOutDto.setCityId( storeEntity.getCityId() );
        addStoreOutDto.setCountyId( storeEntity.getCountyId() );
        addStoreOutDto.setShoppingStart( storeEntity.getShoppingStart() );
        addStoreOutDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        addStoreOutDto.setTel( storeEntity.getTel() );
        addStoreOutDto.setLeaderId( storeEntity.getLeaderId() );
        addStoreOutDto.setAlipayno( storeEntity.getAlipayno() );
        addStoreOutDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        addStoreOutDto.setWechatno( storeEntity.getWechatno() );
        addStoreOutDto.setWechatnourl( storeEntity.getWechatnourl() );
        addStoreOutDto.setNote( storeEntity.getNote() );
        addStoreOutDto.setStatus( storeEntity.getStatus() );
        addStoreOutDto.setProvicenName( storeEntity.getProvicenName() );
        addStoreOutDto.setCityName( storeEntity.getCityName() );
        addStoreOutDto.setCountyName( storeEntity.getCountyName() );
        addStoreOutDto.setCompanyId( storeEntity.getCompanyId() );
        addStoreOutDto.setAddress2( storeEntity.getAddress2() );
        addStoreOutDto.setParentId( storeEntity.getParentId() );
        addStoreOutDto.setLongitude( storeEntity.getLongitude() );
        addStoreOutDto.setLatitude( storeEntity.getLatitude() );
        addStoreOutDto.setShoppingRemark( storeEntity.getShoppingRemark() );
        addStoreOutDto.setTelRemark( storeEntity.getTelRemark() );
        addStoreOutDto.setEmail( storeEntity.getEmail() );
        addStoreOutDto.setEmailRemark( storeEntity.getEmailRemark() );
        addStoreOutDto.setWechatRemark( storeEntity.getWechatRemark() );
        addStoreOutDto.setAlipayRemark( storeEntity.getAlipayRemark() );
        addStoreOutDto.setChannel( storeEntity.getChannel() );
        addStoreOutDto.setAbbreviation( storeEntity.getAbbreviation() );
        addStoreOutDto.setDistributorId( storeEntity.getDistributorId() );

        return addStoreOutDto;
    }

    @Override
    public StoreEntity updateStoreInDtoToStoreEntity(UpdateStoreInDto updateStoreInDto) {
        if ( updateStoreInDto == null ) {
            return null;
        }

        StoreEntity storeEntity = new StoreEntity();

        storeEntity.setId( updateStoreInDto.getId() );
        storeEntity.setIsDeleted( updateStoreInDto.getIsDeleted() );
        storeEntity.setCreatedBy( updateStoreInDto.getCreatedBy() );
        storeEntity.setModifiedBy( updateStoreInDto.getModifiedBy() );
        storeEntity.setModifiedDate( updateStoreInDto.getModifiedDate() );
        storeEntity.setOrgId( updateStoreInDto.getOrgId() );
        storeEntity.setKeyWord( updateStoreInDto.getKeyWord() );
        storeEntity.setCode( updateStoreInDto.getCode() );
        storeEntity.setName( updateStoreInDto.getName() );
        storeEntity.setCoverurl( updateStoreInDto.getCoverurl() );
        storeEntity.setProvicenId( updateStoreInDto.getProvicenId() );
        storeEntity.setProvicenName( updateStoreInDto.getProvicenName() );
        storeEntity.setCityId( updateStoreInDto.getCityId() );
        storeEntity.setCityName( updateStoreInDto.getCityName() );
        storeEntity.setCountyId( updateStoreInDto.getCountyId() );
        storeEntity.setCountyName( updateStoreInDto.getCountyName() );
        storeEntity.setAddress( updateStoreInDto.getAddress() );
        storeEntity.setAddress2( updateStoreInDto.getAddress2() );
        storeEntity.setShoppingStart( updateStoreInDto.getShoppingStart() );
        storeEntity.setShoppingEnd( updateStoreInDto.getShoppingEnd() );
        storeEntity.setTel( updateStoreInDto.getTel() );
        storeEntity.setLeaderId( updateStoreInDto.getLeaderId() );
        storeEntity.setAlipayno( updateStoreInDto.getAlipayno() );
        storeEntity.setAlipaynourl( updateStoreInDto.getAlipaynourl() );
        storeEntity.setWechatno( updateStoreInDto.getWechatno() );
        storeEntity.setWechatnourl( updateStoreInDto.getWechatnourl() );
        storeEntity.setNote( updateStoreInDto.getNote() );
        storeEntity.setMarketCapacity( updateStoreInDto.getMarketCapacity() );
        storeEntity.setPopulation( updateStoreInDto.getPopulation() );
        storeEntity.setStoreLevel( updateStoreInDto.getStoreLevel() );
        storeEntity.setIsUnmanned( updateStoreInDto.getIsUnmanned() );
        storeEntity.setSignShape( updateStoreInDto.getSignShape() );
        storeEntity.setSignSize( updateStoreInDto.getSignSize() );
        storeEntity.setSignCost( updateStoreInDto.getSignCost() );
        storeEntity.setIsUniform( updateStoreInDto.getIsUniform() );
        storeEntity.setUniformPic( updateStoreInDto.getUniformPic() );
        storeEntity.setTopKitchens( updateStoreInDto.getTopKitchens() );
        storeEntity.setDoorSize( updateStoreInDto.getDoorSize() );
        storeEntity.setStatus( updateStoreInDto.getStatus() );
        storeEntity.setLongitude( updateStoreInDto.getLongitude() );
        storeEntity.setLatitude( updateStoreInDto.getLatitude() );
        storeEntity.setCompanyId( updateStoreInDto.getCompanyId() );
        storeEntity.setLiveDemo( updateStoreInDto.getLiveDemo() );
        storeEntity.setLayoutCode( updateStoreInDto.getLayoutCode() );
        storeEntity.setCharacter( updateStoreInDto.getCharacter() );
        storeEntity.setParentId( updateStoreInDto.getParentId() );
        storeEntity.setShoppingRemark( updateStoreInDto.getShoppingRemark() );
        storeEntity.setTelRemark( updateStoreInDto.getTelRemark() );
        storeEntity.setEmail( updateStoreInDto.getEmail() );
        storeEntity.setEmailRemark( updateStoreInDto.getEmailRemark() );
        storeEntity.setWechatRemark( updateStoreInDto.getWechatRemark() );
        storeEntity.setAlipayRemark( updateStoreInDto.getAlipayRemark() );
        storeEntity.setChannel( updateStoreInDto.getChannel() );
        storeEntity.setWechat( updateStoreInDto.getWechat() );
        storeEntity.setIsToDrp( updateStoreInDto.getIsToDrp() );
        storeEntity.setIsManagerCluesIntoSea( updateStoreInDto.getIsManagerCluesIntoSea() );
        storeEntity.setToDrpStage( updateStoreInDto.getToDrpStage() );
        storeEntity.setStoreType( updateStoreInDto.getStoreType() );
        storeEntity.setCluesMsgUser( updateStoreInDto.getCluesMsgUser() );
        storeEntity.setAbbreviation( updateStoreInDto.getAbbreviation() );
        storeEntity.setDistributorId( updateStoreInDto.getDistributorId() );
        storeEntity.setDevelopSalesmanId( updateStoreInDto.getDevelopSalesmanId() );
        storeEntity.setDevelopSalesmanCode( updateStoreInDto.getDevelopSalesmanCode() );
        storeEntity.setDevelopSalesmanName( updateStoreInDto.getDevelopSalesmanName() );
        storeEntity.setDevelopSalesmanPhone( updateStoreInDto.getDevelopSalesmanPhone() );
        storeEntity.setPreAudit( updateStoreInDto.getPreAudit() );
        storeEntity.setIntro( updateStoreInDto.getIntro() );
        storeEntity.setConsultationPc( updateStoreInDto.getConsultationPc() );
        storeEntity.setConsultationM( updateStoreInDto.getConsultationM() );
        storeEntity.setIsTransfer( updateStoreInDto.getIsTransfer() );
        storeEntity.setIsToCheck( updateStoreInDto.getIsToCheck() );
        storeEntity.setIfDecorationStore( updateStoreInDto.getIfDecorationStore() );
        storeEntity.setDecorateStatus( updateStoreInDto.getDecorateStatus() );
        storeEntity.setOpenDate( updateStoreInDto.getOpenDate() );
        storeEntity.setCloseDate( updateStoreInDto.getCloseDate() );
        storeEntity.setTerminalCheckDateAgain( updateStoreInDto.getTerminalCheckDateAgain() );
        storeEntity.setTerminalCheckDate( updateStoreInDto.getTerminalCheckDate() );
        storeEntity.setStoreMarketGrade( updateStoreInDto.getStoreMarketGrade() );
        storeEntity.setStoreChannelCode( updateStoreInDto.getStoreChannelCode() );
        storeEntity.setDistributorChannel( updateStoreInDto.getDistributorChannel() );
        storeEntity.setRelatedStoreId( updateStoreInDto.getRelatedStoreId() );
        storeEntity.setIsVirtual( updateStoreInDto.getIsVirtual() );
        storeEntity.setNeedTerminalBuild( updateStoreInDto.getNeedTerminalBuild() );
        storeEntity.setManagerName( updateStoreInDto.getManagerName() );
        storeEntity.setManagerPhone( updateStoreInDto.getManagerPhone() );
        storeEntity.setStoreBizType( updateStoreInDto.getStoreBizType() );
        storeEntity.setStaffs( updateStoreInDto.getStaffs() );
        storeEntity.setPropertyRight( updateStoreInDto.getPropertyRight() );
        storeEntity.setLeaseTerm( updateStoreInDto.getLeaseTerm() );
        storeEntity.setBpmType( updateStoreInDto.getBpmType() );
        storeEntity.setWholeUsableArea( updateStoreInDto.getWholeUsableArea() );
        storeEntity.setOpenType( updateStoreInDto.getOpenType() );
        storeEntity.setEstimatedAnnualSales( updateStoreInDto.getEstimatedAnnualSales() );
        storeEntity.setCreatedDate( updateStoreInDto.getCreatedDate() );
        storeEntity.setStoreSubChannelCode( updateStoreInDto.getStoreSubChannelCode() );
        storeEntity.setOutsideStoreName( updateStoreInDto.getOutsideStoreName() );
        storeEntity.setEpsUsableArea( updateStoreInDto.getEpsUsableArea() );
        storeEntity.setCluesFollow( updateStoreInDto.getCluesFollow() );
        storeEntity.setCluesLose( updateStoreInDto.getCluesLose() );
        storeEntity.setAnnualRent( updateStoreInDto.getAnnualRent() );
        storeEntity.setCluesOverdue( updateStoreInDto.getCluesOverdue() );
        storeEntity.setDepositCluesOverdue( updateStoreInDto.getDepositCluesOverdue() );
        storeEntity.setUsePos( updateStoreInDto.getUsePos() );
        storeEntity.setTerminalImageScore( updateStoreInDto.getTerminalImageScore() );
        storeEntity.setIsSceneFlag( updateStoreInDto.getIsSceneFlag() );
        storeEntity.setSceneSuit( updateStoreInDto.getSceneSuit() );
        storeEntity.setVersion( updateStoreInDto.getVersion() );
        List<String> list = updateStoreInDto.getRealKeyWordList();
        if ( list != null ) {
            storeEntity.setRealKeyWordList( new ArrayList<String>( list ) );
        }
        List<String> list1 = updateStoreInDto.getPosKeyList();
        if ( list1 != null ) {
            storeEntity.setPosKeyList( new ArrayList<String>( list1 ) );
        }
        storeEntity.setIsCashierEnabled( updateStoreInDto.getIsCashierEnabled() );
        storeEntity.setIzImPayActivityEnabled( updateStoreInDto.getIzImPayActivityEnabled() );
        storeEntity.setMerchantCode( updateStoreInDto.getMerchantCode() );
        storeEntity.setMerchantName( updateStoreInDto.getMerchantName() );
        storeEntity.setMerchantCodeOnline( updateStoreInDto.getMerchantCodeOnline() );
        storeEntity.setCashierChargeMapping( updateStoreInDto.getCashierChargeMapping() );
        storeEntity.setOpenWyChannel( updateStoreInDto.getOpenWyChannel() );
        storeEntity.setOpenZqChannel( updateStoreInDto.getOpenZqChannel() );
        storeEntity.setOpenRqChannel( updateStoreInDto.getOpenRqChannel() );
        storeEntity.setIsSmartBadge( updateStoreInDto.getIsSmartBadge() );
        storeEntity.setOldCompensateMerchantCode( updateStoreInDto.getOldCompensateMerchantCode() );
        storeEntity.setCluesCheckPhone( updateStoreInDto.getCluesCheckPhone() );
        storeEntity.setCluesProcessResult( updateStoreInDto.getCluesProcessResult() );
        storeEntity.setCluesCheckScope( updateStoreInDto.getCluesCheckScope() );
        storeEntity.setCluesCheckPeriod( updateStoreInDto.getCluesCheckPeriod() );
        storeEntity.setCluesFollowStatus( updateStoreInDto.getCluesFollowStatus() );
        storeEntity.setTiktokCode( updateStoreInDto.getTiktokCode() );
        storeEntity.setTiktokName( updateStoreInDto.getTiktokName() );

        return storeEntity;
    }

    @Override
    public List<FindStoreByNameOutDto> storeEntityListToFindStoreByNameOutDtoList(List<StoreEntity> storeEntityList) {
        if ( storeEntityList == null ) {
            return null;
        }

        List<FindStoreByNameOutDto> list = new ArrayList<FindStoreByNameOutDto>( storeEntityList.size() );
        for ( StoreEntity storeEntity : storeEntityList ) {
            list.add( storeEntityToFindStoreByNameOutDto( storeEntity ) );
        }

        return list;
    }

    @Override
    public FindStoreByNameOutDto storeEntityToFindStoreByNameOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindStoreByNameOutDto findStoreByNameOutDto = new FindStoreByNameOutDto();

        findStoreByNameOutDto.setId( storeEntity.getId() );
        findStoreByNameOutDto.setOrgId( storeEntity.getOrgId() );
        findStoreByNameOutDto.setKeyWord( storeEntity.getKeyWord() );
        findStoreByNameOutDto.setCode( storeEntity.getCode() );
        findStoreByNameOutDto.setName( storeEntity.getName() );
        findStoreByNameOutDto.setCoverurl( storeEntity.getCoverurl() );
        findStoreByNameOutDto.setAddress( storeEntity.getAddress() );
        findStoreByNameOutDto.setProvicenId( storeEntity.getProvicenId() );
        findStoreByNameOutDto.setCityId( storeEntity.getCityId() );
        findStoreByNameOutDto.setCountyId( storeEntity.getCountyId() );
        findStoreByNameOutDto.setLongitude( storeEntity.getLongitude() );
        findStoreByNameOutDto.setLatitude( storeEntity.getLatitude() );
        findStoreByNameOutDto.setShoppingStart( storeEntity.getShoppingStart() );
        findStoreByNameOutDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        findStoreByNameOutDto.setTel( storeEntity.getTel() );
        findStoreByNameOutDto.setLeaderId( storeEntity.getLeaderId() );
        findStoreByNameOutDto.setAlipayno( storeEntity.getAlipayno() );
        findStoreByNameOutDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        findStoreByNameOutDto.setWechatno( storeEntity.getWechatno() );
        findStoreByNameOutDto.setWechatnourl( storeEntity.getWechatnourl() );
        findStoreByNameOutDto.setNote( storeEntity.getNote() );
        findStoreByNameOutDto.setStatus( storeEntity.getStatus() );
        findStoreByNameOutDto.setParentId( storeEntity.getParentId() );
        findStoreByNameOutDto.setFullPathName( storeEntity.getFullPathName() );
        findStoreByNameOutDto.setFullPathId( storeEntity.getFullPathId() );
        findStoreByNameOutDto.setAddress2( storeEntity.getAddress2() );

        return findStoreByNameOutDto;
    }

    @Override
    public StoreEntity findStorePageAllInDtoToStoreEntity(FindStorePageAllInDto findStorePageAllInDto) {
        if ( findStorePageAllInDto == null ) {
            return null;
        }

        StoreEntity storeEntity = new StoreEntity();

        storeEntity.setCode( findStorePageAllInDto.getCode() );
        storeEntity.setName( findStorePageAllInDto.getName() );
        storeEntity.setParentId( findStorePageAllInDto.getParentId() );
        storeEntity.setCompanyId( findStorePageAllInDto.getCompanyId() );
        if ( findStorePageAllInDto.getLeaderId() != null ) {
            storeEntity.setLeaderId( findStorePageAllInDto.getLeaderId().intValue() );
        }
        storeEntity.setStatus( findStorePageAllInDto.getStatus() );
        List<Long> list = findStorePageAllInDto.getNotInCompanyIds();
        if ( list != null ) {
            storeEntity.setNotInCompanyIds( new ArrayList<Long>( list ) );
        }
        storeEntity.setKeyWord( findStorePageAllInDto.getKeyWord() );
        storeEntity.setChannelCategoryCode( findStorePageAllInDto.getChannelCategoryCode() );
        storeEntity.setChannelSubdivideCode( findStorePageAllInDto.getChannelSubdivideCode() );
        storeEntity.setChannelCategoryIds( findStorePageAllInDto.getChannelCategoryIds() );
        storeEntity.setChannelSubdivideIds( findStorePageAllInDto.getChannelSubdivideIds() );
        storeEntity.setOrgId( findStorePageAllInDto.getOrgId() );
        List<String> list1 = findStorePageAllInDto.getCodes();
        if ( list1 != null ) {
            storeEntity.setCodes( new ArrayList<String>( list1 ) );
        }
        storeEntity.setNameOrCode( findStorePageAllInDto.getNameOrCode() );
        storeEntity.setProvicenId( findStorePageAllInDto.getProvicenId() );
        storeEntity.setCityId( findStorePageAllInDto.getCityId() );
        storeEntity.setCountyId( findStorePageAllInDto.getCountyId() );
        storeEntity.setAddress( findStorePageAllInDto.getAddress() );
        storeEntity.setIsUnmanned( findStorePageAllInDto.getIsUnmanned() );
        storeEntity.setIsUniform( findStorePageAllInDto.getIsUniform() );
        Set<Integer> set = findStorePageAllInDto.getStatusSet();
        if ( set != null ) {
            storeEntity.setStatusSet( new HashSet<Integer>( set ) );
        }
        storeEntity.setCompanyIds( findStorePageAllInDto.getCompanyIds() );
        storeEntity.setIsToDrp( findStorePageAllInDto.getIsToDrp() );
        storeEntity.setToDrpStage( findStorePageAllInDto.getToDrpStage() );
        storeEntity.setStoreType( findStorePageAllInDto.getStoreType() );
        storeEntity.setChannelCode( findStorePageAllInDto.getChannelCode() );
        storeEntity.setCluesMsgUser( findStorePageAllInDto.getCluesMsgUser() );
        if ( findStorePageAllInDto.getDistributorId() != null ) {
            storeEntity.setDistributorId( findStorePageAllInDto.getDistributorId().longValue() );
        }
        storeEntity.setDistributorIds( findStorePageAllInDto.getDistributorIds() );
        storeEntity.setChannelSubdivide( findStorePageAllInDto.getChannelSubdivide() );
        storeEntity.setChannelCategory( findStorePageAllInDto.getChannelCategory() );
        storeEntity.setArea( findStorePageAllInDto.getArea() );
        storeEntity.setAreas( findStorePageAllInDto.getAreas() );
        storeEntity.setDevelopSalesmanId( findStorePageAllInDto.getDevelopSalesmanId() );
        storeEntity.setPreAudit( findStorePageAllInDto.getPreAudit() );
        storeEntity.setOrderByCode( findStorePageAllInDto.getOrderByCode() );
        storeEntity.setCreatedDateStart( findStorePageAllInDto.getCreatedDateStart() );
        storeEntity.setCreatedDateEnd( findStorePageAllInDto.getCreatedDateEnd() );
        storeEntity.setDecorateStatus( findStorePageAllInDto.getDecorateStatus() );
        storeEntity.setStoreChannelCode( findStorePageAllInDto.getStoreChannelCode() );
        storeEntity.setIsVirtual( findStorePageAllInDto.getIsVirtual() );
        storeEntity.setNeedTerminalBuild( findStorePageAllInDto.getNeedTerminalBuild() );
        storeEntity.setStoreKeyword( findStorePageAllInDto.getStoreKeyword() );
        storeEntity.setStoreKeywordType( findStorePageAllInDto.getStoreKeywordType() );
        storeEntity.setDevelopSalesman( findStorePageAllInDto.getDevelopSalesman() );
        storeEntity.setOpenStartTime( findStorePageAllInDto.getOpenStartTime() );
        storeEntity.setOpenEndTime( findStorePageAllInDto.getOpenEndTime() );
        storeEntity.setCloseStartTime( findStorePageAllInDto.getCloseStartTime() );
        storeEntity.setCloseEndTime( findStorePageAllInDto.getCloseEndTime() );
        storeEntity.setCreatedStartTime( findStorePageAllInDto.getCreatedStartTime() );
        storeEntity.setCreatedEndTime( findStorePageAllInDto.getCreatedEndTime() );
        storeEntity.setStatusMulti( findStorePageAllInDto.getStatusMulti() );
        storeEntity.setStoreId( findStorePageAllInDto.getStoreId() );
        List<Long> list2 = findStorePageAllInDto.getOrgIds();
        if ( list2 != null ) {
            storeEntity.setOrgIds( new ArrayList<Long>( list2 ) );
        }
        List<Long> list3 = findStorePageAllInDto.getIds();
        if ( list3 != null ) {
            storeEntity.setIds( new ArrayList<Long>( list3 ) );
        }
        storeEntity.setEmptyPlanCode( findStorePageAllInDto.getEmptyPlanCode() );
        storeEntity.setUsableAreaLower( findStorePageAllInDto.getUsableAreaLower() );
        storeEntity.setUsableAreaUpper( findStorePageAllInDto.getUsableAreaUpper() );
        Set<Integer> set1 = findStorePageAllInDto.getFloorSet();
        if ( set1 != null ) {
            storeEntity.setFloorSet( new HashSet<Integer>( set1 ) );
        }
        Set<String> set2 = findStorePageAllInDto.getStoreLevels();
        if ( set2 != null ) {
            storeEntity.setStoreLevels( new HashSet<String>( set2 ) );
        }
        storeEntity.setDepOrgIds( findStorePageAllInDto.getDepOrgIds() );
        storeEntity.setWholeUsableAreaBegin( findStorePageAllInDto.getWholeUsableAreaBegin() );
        storeEntity.setWholeUsableAreaEnd( findStorePageAllInDto.getWholeUsableAreaEnd() );
        storeEntity.setTerminalCheckDateStart( findStorePageAllInDto.getTerminalCheckDateStart() );
        storeEntity.setTerminalCheckDateEnd( findStorePageAllInDto.getTerminalCheckDateEnd() );
        storeEntity.setTerminalCheckDateAgainStart( findStorePageAllInDto.getTerminalCheckDateAgainStart() );
        storeEntity.setTerminalCheckDateAgainEnd( findStorePageAllInDto.getTerminalCheckDateAgainEnd() );
        List<Long> list4 = findStorePageAllInDto.getRealKeyWordStoreIdList();
        if ( list4 != null ) {
            storeEntity.setRealKeyWordStoreIdList( new ArrayList<Long>( list4 ) );
        }
        storeEntity.setIsCashierEnabled( findStorePageAllInDto.getIsCashierEnabled() );
        storeEntity.setIsSmartBadge( findStorePageAllInDto.getIsSmartBadge() );
        List<Integer> list5 = findStorePageAllInDto.getOpenModelList();
        if ( list5 != null ) {
            storeEntity.setOpenModelList( new ArrayList<Integer>( list5 ) );
        }
        List<Long> list6 = findStorePageAllInDto.getStoreOrgIdList();
        if ( list6 != null ) {
            storeEntity.setStoreOrgIdList( new ArrayList<Long>( list6 ) );
        }

        return storeEntity;
    }

    @Override
    public StoreEntity findStoreListAllInDtoToStoreEntity(FindStoreListAllInDto findStoreListAllInDto) {
        if ( findStoreListAllInDto == null ) {
            return null;
        }

        StoreEntity storeEntity = new StoreEntity();

        storeEntity.setChannelCategoryCode( findStoreListAllInDto.getChannelCategoryCode() );
        storeEntity.setChannelSubdivideCode( findStoreListAllInDto.getChannelSubdivideCode() );
        storeEntity.setChannelCategoryIds( findStoreListAllInDto.getChannelCategoryIds() );
        storeEntity.setChannelSubdivideIds( findStoreListAllInDto.getChannelSubdivideIds() );
        storeEntity.setOrgId( findStoreListAllInDto.getOrgId() );
        storeEntity.setKeyWord( findStoreListAllInDto.getKeyWord() );
        storeEntity.setCode( findStoreListAllInDto.getCode() );
        List<String> list = findStoreListAllInDto.getCodes();
        if ( list != null ) {
            storeEntity.setCodes( new ArrayList<String>( list ) );
        }
        storeEntity.setName( findStoreListAllInDto.getName() );
        storeEntity.setNameOrCode( findStoreListAllInDto.getNameOrCode() );
        storeEntity.setProvicenId( findStoreListAllInDto.getProvicenId() );
        storeEntity.setCityId( findStoreListAllInDto.getCityId() );
        storeEntity.setCountyId( findStoreListAllInDto.getCountyId() );
        storeEntity.setAddress( findStoreListAllInDto.getAddress() );
        if ( findStoreListAllInDto.getLeaderId() != null ) {
            storeEntity.setLeaderId( findStoreListAllInDto.getLeaderId().intValue() );
        }
        storeEntity.setIsUnmanned( findStoreListAllInDto.getIsUnmanned() );
        storeEntity.setIsUniform( findStoreListAllInDto.getIsUniform() );
        storeEntity.setStatus( findStoreListAllInDto.getStatus() );
        Set<Integer> set = findStoreListAllInDto.getStatusSet();
        if ( set != null ) {
            storeEntity.setStatusSet( new HashSet<Integer>( set ) );
        }
        storeEntity.setCompanyId( findStoreListAllInDto.getCompanyId() );
        storeEntity.setCompanyIds( findStoreListAllInDto.getCompanyIds() );
        storeEntity.setParentId( findStoreListAllInDto.getParentId() );
        storeEntity.setIsToDrp( findStoreListAllInDto.getIsToDrp() );
        storeEntity.setToDrpStage( findStoreListAllInDto.getToDrpStage() );
        storeEntity.setStoreType( findStoreListAllInDto.getStoreType() );
        storeEntity.setChannelCode( findStoreListAllInDto.getChannelCode() );
        storeEntity.setCluesMsgUser( findStoreListAllInDto.getCluesMsgUser() );
        if ( findStoreListAllInDto.getDistributorId() != null ) {
            storeEntity.setDistributorId( findStoreListAllInDto.getDistributorId().longValue() );
        }
        storeEntity.setDistributorIds( findStoreListAllInDto.getDistributorIds() );
        storeEntity.setChannelSubdivide( findStoreListAllInDto.getChannelSubdivide() );
        storeEntity.setChannelCategory( findStoreListAllInDto.getChannelCategory() );
        storeEntity.setArea( findStoreListAllInDto.getArea() );
        storeEntity.setAreas( findStoreListAllInDto.getAreas() );
        storeEntity.setDevelopSalesmanId( findStoreListAllInDto.getDevelopSalesmanId() );
        storeEntity.setPreAudit( findStoreListAllInDto.getPreAudit() );
        storeEntity.setOrderByCode( findStoreListAllInDto.getOrderByCode() );
        storeEntity.setCreatedDateStart( findStoreListAllInDto.getCreatedDateStart() );
        storeEntity.setCreatedDateEnd( findStoreListAllInDto.getCreatedDateEnd() );
        storeEntity.setDecorateStatus( findStoreListAllInDto.getDecorateStatus() );
        storeEntity.setStoreChannelCode( findStoreListAllInDto.getStoreChannelCode() );
        storeEntity.setIsVirtual( findStoreListAllInDto.getIsVirtual() );
        storeEntity.setNeedTerminalBuild( findStoreListAllInDto.getNeedTerminalBuild() );
        storeEntity.setStoreKeyword( findStoreListAllInDto.getStoreKeyword() );
        storeEntity.setStoreKeywordType( findStoreListAllInDto.getStoreKeywordType() );
        storeEntity.setDevelopSalesman( findStoreListAllInDto.getDevelopSalesman() );
        storeEntity.setOpenStartTime( findStoreListAllInDto.getOpenStartTime() );
        storeEntity.setOpenEndTime( findStoreListAllInDto.getOpenEndTime() );
        storeEntity.setCloseStartTime( findStoreListAllInDto.getCloseStartTime() );
        storeEntity.setCloseEndTime( findStoreListAllInDto.getCloseEndTime() );
        storeEntity.setCreatedStartTime( findStoreListAllInDto.getCreatedStartTime() );
        storeEntity.setCreatedEndTime( findStoreListAllInDto.getCreatedEndTime() );
        storeEntity.setStatusMulti( findStoreListAllInDto.getStatusMulti() );
        storeEntity.setStoreId( findStoreListAllInDto.getStoreId() );
        List<Long> list1 = findStoreListAllInDto.getOrgIds();
        if ( list1 != null ) {
            storeEntity.setOrgIds( new ArrayList<Long>( list1 ) );
        }
        List<Long> list2 = findStoreListAllInDto.getIds();
        if ( list2 != null ) {
            storeEntity.setIds( new ArrayList<Long>( list2 ) );
        }
        storeEntity.setEmptyPlanCode( findStoreListAllInDto.getEmptyPlanCode() );
        storeEntity.setUsableAreaLower( findStoreListAllInDto.getUsableAreaLower() );
        storeEntity.setUsableAreaUpper( findStoreListAllInDto.getUsableAreaUpper() );
        Set<Integer> set1 = findStoreListAllInDto.getFloorSet();
        if ( set1 != null ) {
            storeEntity.setFloorSet( new HashSet<Integer>( set1 ) );
        }
        Set<String> set2 = findStoreListAllInDto.getStoreLevels();
        if ( set2 != null ) {
            storeEntity.setStoreLevels( new HashSet<String>( set2 ) );
        }
        storeEntity.setDepOrgIds( findStoreListAllInDto.getDepOrgIds() );
        storeEntity.setWholeUsableAreaBegin( findStoreListAllInDto.getWholeUsableAreaBegin() );
        storeEntity.setWholeUsableAreaEnd( findStoreListAllInDto.getWholeUsableAreaEnd() );
        storeEntity.setTerminalCheckDateStart( findStoreListAllInDto.getTerminalCheckDateStart() );
        storeEntity.setTerminalCheckDateEnd( findStoreListAllInDto.getTerminalCheckDateEnd() );
        storeEntity.setTerminalCheckDateAgainStart( findStoreListAllInDto.getTerminalCheckDateAgainStart() );
        storeEntity.setTerminalCheckDateAgainEnd( findStoreListAllInDto.getTerminalCheckDateAgainEnd() );
        List<Long> list3 = findStoreListAllInDto.getRealKeyWordStoreIdList();
        if ( list3 != null ) {
            storeEntity.setRealKeyWordStoreIdList( new ArrayList<Long>( list3 ) );
        }

        return storeEntity;
    }

    @Override
    public List<FindStorePageAllOutDto> storeEntityListToFindStorePageAllOutDtoList(List<StoreEntity> storeEntityList) {
        if ( storeEntityList == null ) {
            return null;
        }

        List<FindStorePageAllOutDto> list = new ArrayList<FindStorePageAllOutDto>( storeEntityList.size() );
        for ( StoreEntity storeEntity : storeEntityList ) {
            list.add( storeEntityToFindStorePageAllOutDto( storeEntity ) );
        }

        return list;
    }

    @Override
    public FindStorePageAllOutDto storeEntityToFindStorePageAllOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindStorePageAllOutDto findStorePageAllOutDto = new FindStorePageAllOutDto();

        findStorePageAllOutDto.setId( storeEntity.getId() );
        findStorePageAllOutDto.setOrgId( storeEntity.getOrgId() );
        findStorePageAllOutDto.setKeyWord( storeEntity.getKeyWord() );
        findStorePageAllOutDto.setCode( storeEntity.getCode() );
        findStorePageAllOutDto.setName( storeEntity.getName() );
        findStorePageAllOutDto.setCoverurl( storeEntity.getCoverurl() );
        findStorePageAllOutDto.setAddress( storeEntity.getAddress() );
        findStorePageAllOutDto.setProvicenId( storeEntity.getProvicenId() );
        findStorePageAllOutDto.setProvicenName( storeEntity.getProvicenName() );
        findStorePageAllOutDto.setCityId( storeEntity.getCityId() );
        findStorePageAllOutDto.setCloseDate( storeEntity.getCloseDate() );
        findStorePageAllOutDto.setCityName( storeEntity.getCityName() );
        findStorePageAllOutDto.setCountyId( storeEntity.getCountyId() );
        findStorePageAllOutDto.setCountyName( storeEntity.getCountyName() );
        findStorePageAllOutDto.setTerminalCheckDateAgain( storeEntity.getTerminalCheckDateAgain() );
        findStorePageAllOutDto.setTerminalCheckDate( storeEntity.getTerminalCheckDate() );
        findStorePageAllOutDto.setLongitude( storeEntity.getLongitude() );
        findStorePageAllOutDto.setLatitude( storeEntity.getLatitude() );
        findStorePageAllOutDto.setShoppingStart( storeEntity.getShoppingStart() );
        findStorePageAllOutDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        findStorePageAllOutDto.setTel( storeEntity.getTel() );
        findStorePageAllOutDto.setLeaderId( storeEntity.getLeaderId() );
        findStorePageAllOutDto.setAlipayno( storeEntity.getAlipayno() );
        findStorePageAllOutDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        findStorePageAllOutDto.setWechatno( storeEntity.getWechatno() );
        findStorePageAllOutDto.setWechatnourl( storeEntity.getWechatnourl() );
        findStorePageAllOutDto.setNote( storeEntity.getNote() );
        findStorePageAllOutDto.setStatus( storeEntity.getStatus() );
        findStorePageAllOutDto.setParentId( storeEntity.getParentId() );
        findStorePageAllOutDto.setParentName( storeEntity.getParentName() );
        findStorePageAllOutDto.setParentCode( storeEntity.getParentCode() );
        findStorePageAllOutDto.setFullPathName( storeEntity.getFullPathName() );
        findStorePageAllOutDto.setFullPathId( storeEntity.getFullPathId() );
        findStorePageAllOutDto.setAddress2( storeEntity.getAddress2() );
        findStorePageAllOutDto.setCompanyId( storeEntity.getCompanyId() );
        findStorePageAllOutDto.setCompanyName( storeEntity.getCompanyName() );
        findStorePageAllOutDto.setCompanyCode( storeEntity.getCompanyCode() );
        findStorePageAllOutDto.setStoreType( storeEntity.getStoreType() );
        findStorePageAllOutDto.setChannelCode( storeEntity.getChannelCode() );
        findStorePageAllOutDto.setChannelName( storeEntity.getChannelName() );
        findStorePageAllOutDto.setStoreTypeName( storeEntity.getStoreTypeName() );
        findStorePageAllOutDto.setUsableArea( storeEntity.getUsableArea() );
        findStorePageAllOutDto.setOpenDate( storeEntity.getOpenDate() );
        findStorePageAllOutDto.setEpsUsableArea( storeEntity.getEpsUsableArea() );
        findStorePageAllOutDto.setWholeUsableArea( storeEntity.getWholeUsableArea() );
        findStorePageAllOutDto.setStoreBizType( storeEntity.getStoreBizType() );
        findStorePageAllOutDto.setStoreLevel( storeEntity.getStoreLevel() );
        findStorePageAllOutDto.setIsUnmanned( storeEntity.getIsUnmanned() );
        findStorePageAllOutDto.setIsUniform( storeEntity.getIsUniform() );
        findStorePageAllOutDto.setFloors( storeEntity.getFloors() );
        findStorePageAllOutDto.setAbbreviation( storeEntity.getAbbreviation() );
        findStorePageAllOutDto.setDistributorName( storeEntity.getDistributorName() );
        findStorePageAllOutDto.setDevelopSalesmanId( storeEntity.getDevelopSalesmanId() );
        findStorePageAllOutDto.setDevelopSalesmanCode( storeEntity.getDevelopSalesmanCode() );
        findStorePageAllOutDto.setDevelopSalesmanName( storeEntity.getDevelopSalesmanName() );
        findStorePageAllOutDto.setDevelopSalesmanPhone( storeEntity.getDevelopSalesmanPhone() );
        findStorePageAllOutDto.setCutTime( storeEntity.getCutTime() );
        findStorePageAllOutDto.setIsToDrp( storeEntity.getIsToDrp() );
        findStorePageAllOutDto.setArea( storeEntity.getArea() );
        findStorePageAllOutDto.setAreas( storeEntity.getAreas() );
        findStorePageAllOutDto.setAreaCode( storeEntity.getAreaCode() );
        findStorePageAllOutDto.setChannelSubdivides( storeEntity.getChannelSubdivides() );
        findStorePageAllOutDto.setDistributorCode( storeEntity.getDistributorCode() );
        findStorePageAllOutDto.setDistributorId( storeEntity.getDistributorId() );
        findStorePageAllOutDto.setDecorateStatus( storeEntity.getDecorateStatus() );
        findStorePageAllOutDto.setCharacter( storeEntity.getCharacter() );
        findStorePageAllOutDto.setCreatedDate( storeEntity.getCreatedDate() );
        findStorePageAllOutDto.setStatusName( storeEntity.getStatusName() );
        findStorePageAllOutDto.setChannelCategoryCode( storeEntity.getChannelCategoryCode() );
        findStorePageAllOutDto.setChannelCategoryName( storeEntity.getChannelCategoryName() );
        findStorePageAllOutDto.setStoreTypeCode( storeEntity.getStoreTypeCode() );
        findStorePageAllOutDto.setIsVirtual( storeEntity.getIsVirtual() );

        return findStorePageAllOutDto;
    }

    @Override
    public FindStoreByIdOutDto storeEntityToFindStoreByIdOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindStoreByIdOutDto findStoreByIdOutDto = new FindStoreByIdOutDto();

        findStoreByIdOutDto.setId( storeEntity.getId() );
        findStoreByIdOutDto.setOrgId( storeEntity.getOrgId() );
        findStoreByIdOutDto.setCode( storeEntity.getCode() );
        findStoreByIdOutDto.setName( storeEntity.getName() );
        findStoreByIdOutDto.setKeyWord( storeEntity.getKeyWord() );
        findStoreByIdOutDto.setCoverurl( storeEntity.getCoverurl() );
        findStoreByIdOutDto.setAddress( storeEntity.getAddress() );
        findStoreByIdOutDto.setProvicenId( storeEntity.getProvicenId() );
        findStoreByIdOutDto.setProvicenName( storeEntity.getProvicenName() );
        findStoreByIdOutDto.setCityId( storeEntity.getCityId() );
        findStoreByIdOutDto.setCityName( storeEntity.getCityName() );
        findStoreByIdOutDto.setCountyName( storeEntity.getCountyName() );
        findStoreByIdOutDto.setCountyId( storeEntity.getCountyId() );
        findStoreByIdOutDto.setLongitude( storeEntity.getLongitude() );
        findStoreByIdOutDto.setLatitude( storeEntity.getLatitude() );
        findStoreByIdOutDto.setShoppingStart( storeEntity.getShoppingStart() );
        findStoreByIdOutDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        findStoreByIdOutDto.setTel( storeEntity.getTel() );
        findStoreByIdOutDto.setLeaderId( storeEntity.getLeaderId() );
        findStoreByIdOutDto.setAlipayno( storeEntity.getAlipayno() );
        findStoreByIdOutDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        findStoreByIdOutDto.setWechatno( storeEntity.getWechatno() );
        findStoreByIdOutDto.setWechatnourl( storeEntity.getWechatnourl() );
        findStoreByIdOutDto.setNote( storeEntity.getNote() );
        findStoreByIdOutDto.setParentId( storeEntity.getParentId() );
        findStoreByIdOutDto.setFullPathId( storeEntity.getFullPathId() );
        findStoreByIdOutDto.setFullPathName( storeEntity.getFullPathName() );
        findStoreByIdOutDto.setCompanyId( storeEntity.getCompanyId() );
        findStoreByIdOutDto.setAddress2( storeEntity.getAddress2() );
        findStoreByIdOutDto.setShoppingRemark( storeEntity.getShoppingRemark() );
        findStoreByIdOutDto.setTelRemark( storeEntity.getTelRemark() );
        findStoreByIdOutDto.setEmail( storeEntity.getEmail() );
        findStoreByIdOutDto.setEmailRemark( storeEntity.getEmailRemark() );
        findStoreByIdOutDto.setWechatRemark( storeEntity.getWechatRemark() );
        findStoreByIdOutDto.setAlipayRemark( storeEntity.getAlipayRemark() );
        findStoreByIdOutDto.setChannel( storeEntity.getChannel() );
        findStoreByIdOutDto.setIsOpenRent( storeEntity.getIsOpenRent() );
        if ( storeEntity.getIsOpenKpoint() != null ) {
            findStoreByIdOutDto.setIsOpenKpoint( String.valueOf( storeEntity.getIsOpenKpoint() ) );
        }
        if ( storeEntity.getStatus() != null ) {
            findStoreByIdOutDto.setStatus( String.valueOf( storeEntity.getStatus() ) );
        }
        findStoreByIdOutDto.setWechat( storeEntity.getWechat() );
        if ( storeEntity.getDistance() != null ) {
            findStoreByIdOutDto.setDistance( Long.parseLong( storeEntity.getDistance() ) );
        }
        findStoreByIdOutDto.setOutsideStoreName( storeEntity.getOutsideStoreName() );
        findStoreByIdOutDto.setDecorateStatus( storeEntity.getDecorateStatus() );
        findStoreByIdOutDto.setIsVirtual( storeEntity.getIsVirtual() );
        findStoreByIdOutDto.setNeedTerminalBuild( storeEntity.getNeedTerminalBuild() );
        findStoreByIdOutDto.setCreatedDate( storeEntity.getCreatedDate() );
        findStoreByIdOutDto.setIsToDrp( storeEntity.getIsToDrp() );
        findStoreByIdOutDto.setToDrpStage( storeEntity.getToDrpStage() );
        findStoreByIdOutDto.setStoreType( storeEntity.getStoreType() );
        findStoreByIdOutDto.setCluesMsgUser( storeEntity.getCluesMsgUser() );
        findStoreByIdOutDto.setDecorationType( storeEntity.getDecorationType() );
        findStoreByIdOutDto.setIsFrontage( storeEntity.getIsFrontage() );
        findStoreByIdOutDto.setAbbreviation( storeEntity.getAbbreviation() );
        findStoreByIdOutDto.setDistributorId( storeEntity.getDistributorId() );
        findStoreByIdOutDto.setChannelSubdivide( storeEntity.getChannelSubdivide() );
        findStoreByIdOutDto.setDevelopSalesmanId( storeEntity.getDevelopSalesmanId() );
        findStoreByIdOutDto.setDevelopSalesmanCode( storeEntity.getDevelopSalesmanCode() );
        findStoreByIdOutDto.setDevelopSalesmanName( storeEntity.getDevelopSalesmanName() );
        findStoreByIdOutDto.setDevelopSalesmanPhone( storeEntity.getDevelopSalesmanPhone() );
        findStoreByIdOutDto.setPreAudit( storeEntity.getPreAudit() );
        findStoreByIdOutDto.setIntro( storeEntity.getIntro() );
        findStoreByIdOutDto.setConsultationPc( storeEntity.getConsultationPc() );
        findStoreByIdOutDto.setConsultationM( storeEntity.getConsultationM() );
        findStoreByIdOutDto.setChannelCategoryId( storeEntity.getChannelCategoryId() );
        findStoreByIdOutDto.setArea( storeEntity.getArea() );
        findStoreByIdOutDto.setAreaCode( storeEntity.getAreaCode() );
        findStoreByIdOutDto.setAreaName( storeEntity.getAreaName() );
        findStoreByIdOutDto.setCompanyPkId( storeEntity.getCompanyPkId() );
        findStoreByIdOutDto.setStoreBizType( storeEntity.getStoreBizType() );
        findStoreByIdOutDto.setPropertyRight( storeEntity.getPropertyRight() );
        findStoreByIdOutDto.setLeaseTerm( storeEntity.getLeaseTerm() );
        findStoreByIdOutDto.setAnnualRent( storeEntity.getAnnualRent() );
        findStoreByIdOutDto.setEstimatedAnnualSales( storeEntity.getEstimatedAnnualSales() );
        findStoreByIdOutDto.setManagerName( storeEntity.getManagerName() );
        findStoreByIdOutDto.setManagerPhone( storeEntity.getManagerPhone() );
        findStoreByIdOutDto.setMarketCapacity( storeEntity.getMarketCapacity() );
        findStoreByIdOutDto.setPopulation( storeEntity.getPopulation() );
        findStoreByIdOutDto.setTopKitchens( storeEntity.getTopKitchens() );
        findStoreByIdOutDto.setPlanCode( storeEntity.getPlanCode() );
        findStoreByIdOutDto.setChannelSubdivideId( storeEntity.getChannelSubdivideId() );
        findStoreByIdOutDto.setOpenDate( storeEntity.getOpenDate() );
        findStoreByIdOutDto.setStoreMarketGrade( storeEntity.getStoreMarketGrade() );
        findStoreByIdOutDto.setCloseDate( storeEntity.getCloseDate() );
        findStoreByIdOutDto.setChannelCategory( storeEntity.getChannelCategory() );
        findStoreByIdOutDto.setChannelCategoryCode( storeEntity.getChannelCategoryCode() );
        findStoreByIdOutDto.setChannelCategoryName( storeEntity.getChannelCategoryName() );
        findStoreByIdOutDto.setChannelSubdivideCode( storeEntity.getChannelSubdivideCode() );
        findStoreByIdOutDto.setChannelSubdivideName( storeEntity.getChannelSubdivideName() );
        findStoreByIdOutDto.setStoreTypeCode( storeEntity.getStoreTypeCode() );
        findStoreByIdOutDto.setStoreTypeName( storeEntity.getStoreTypeName() );
        findStoreByIdOutDto.setDistributorCode( storeEntity.getDistributorCode() );
        findStoreByIdOutDto.setDistributorName( storeEntity.getDistributorName() );
        findStoreByIdOutDto.setIsTransfer( storeEntity.getIsTransfer() );
        findStoreByIdOutDto.setIsToCheck( storeEntity.getIsToCheck() );
        findStoreByIdOutDto.setStoreSubChannelCode( storeEntity.getStoreSubChannelCode() );
        findStoreByIdOutDto.setCompanyName( storeEntity.getCompanyName() );
        findStoreByIdOutDto.setEpsUsableArea( storeEntity.getEpsUsableArea() );
        findStoreByIdOutDto.setWholeUsableArea( storeEntity.getWholeUsableArea() );
        findStoreByIdOutDto.setUsableArea( storeEntity.getUsableArea() );
        findStoreByIdOutDto.setStoreLevel( storeEntity.getStoreLevel() );
        findStoreByIdOutDto.setCharacter( storeEntity.getCharacter() );
        findStoreByIdOutDto.setIsUnmanned( storeEntity.getIsUnmanned() );
        findStoreByIdOutDto.setIfDecorationStore( storeEntity.getIfDecorationStore() );
        findStoreByIdOutDto.setStoreId( storeEntity.getStoreId() );
        findStoreByIdOutDto.setCluesOverdue( storeEntity.getCluesOverdue() );
        findStoreByIdOutDto.setDepositCluesOverdue( storeEntity.getDepositCluesOverdue() );
        findStoreByIdOutDto.setOrderCheckChannel( storeEntity.getOrderCheckChannel() );
        findStoreByIdOutDto.setIsCashierEnabled( storeEntity.getIsCashierEnabled() );
        findStoreByIdOutDto.setIzImPayActivityEnabled( storeEntity.getIzImPayActivityEnabled() );
        findStoreByIdOutDto.setOpenWyChannel( storeEntity.getOpenWyChannel() );
        findStoreByIdOutDto.setOpenZqChannel( storeEntity.getOpenZqChannel() );
        findStoreByIdOutDto.setOpenRqChannel( storeEntity.getOpenRqChannel() );
        findStoreByIdOutDto.setCashierChargeMapping( storeEntity.getCashierChargeMapping() );

        return findStoreByIdOutDto;
    }

    @Override
    public List<FindStoreByIdOutDto> storeEntityListToFindStoreByIdOutDtoList(List<StoreEntity> storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        List<FindStoreByIdOutDto> list = new ArrayList<FindStoreByIdOutDto>( storeEntity.size() );
        for ( StoreEntity storeEntity1 : storeEntity ) {
            list.add( storeEntityToFindStoreByIdOutDto( storeEntity1 ) );
        }

        return list;
    }

    @Override
    public List<FindNearestStoreOutDto> storeEntityListToFindNearestStoreOutDtoList(List<StoreEntity> storeEntityList) {
        if ( storeEntityList == null ) {
            return null;
        }

        List<FindNearestStoreOutDto> list = new ArrayList<FindNearestStoreOutDto>( storeEntityList.size() );
        for ( StoreEntity storeEntity : storeEntityList ) {
            list.add( storeEntityToFindNearestStoreOutDto( storeEntity ) );
        }

        return list;
    }

    @Override
    public FindNearestStoreOutDto storeEntityToFindNearestStoreOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindNearestStoreOutDto findNearestStoreOutDto = new FindNearestStoreOutDto();

        findNearestStoreOutDto.setDistance( storeEntity.getDistance() );
        findNearestStoreOutDto.setId( storeEntity.getId() );
        findNearestStoreOutDto.setName( storeEntity.getName() );
        findNearestStoreOutDto.setLongitude( storeEntity.getLongitude() );
        findNearestStoreOutDto.setLatitude( storeEntity.getLatitude() );
        findNearestStoreOutDto.setOrgId( storeEntity.getOrgId() );
        findNearestStoreOutDto.setAddress( storeEntity.getAddress() );

        return findNearestStoreOutDto;
    }

    @Override
    public FindStoreByOrgIdOutDto storeEntityToFindStoreByOrgIdOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindStoreByOrgIdOutDto findStoreByOrgIdOutDto = new FindStoreByOrgIdOutDto();

        findStoreByOrgIdOutDto.setId( storeEntity.getId() );
        findStoreByOrgIdOutDto.setOrgId( storeEntity.getOrgId() );
        findStoreByOrgIdOutDto.setCode( storeEntity.getCode() );
        findStoreByOrgIdOutDto.setPlanCode( storeEntity.getPlanCode() );
        findStoreByOrgIdOutDto.setName( storeEntity.getName() );
        findStoreByOrgIdOutDto.setKeyWord( storeEntity.getKeyWord() );
        findStoreByOrgIdOutDto.setCoverurl( storeEntity.getCoverurl() );
        findStoreByOrgIdOutDto.setAddress( storeEntity.getAddress() );
        findStoreByOrgIdOutDto.setProvicenId( storeEntity.getProvicenId() );
        findStoreByOrgIdOutDto.setProvicenName( storeEntity.getProvicenName() );
        findStoreByOrgIdOutDto.setCityId( storeEntity.getCityId() );
        findStoreByOrgIdOutDto.setCityName( storeEntity.getCityName() );
        findStoreByOrgIdOutDto.setCountyId( storeEntity.getCountyId() );
        findStoreByOrgIdOutDto.setCountyName( storeEntity.getCountyName() );
        findStoreByOrgIdOutDto.setCharacter( storeEntity.getCharacter() );
        findStoreByOrgIdOutDto.setLongitude( storeEntity.getLongitude() );
        findStoreByOrgIdOutDto.setLatitude( storeEntity.getLatitude() );
        findStoreByOrgIdOutDto.setShoppingStart( storeEntity.getShoppingStart() );
        findStoreByOrgIdOutDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        findStoreByOrgIdOutDto.setLeaderId( storeEntity.getLeaderId() );
        findStoreByOrgIdOutDto.setAlipayno( storeEntity.getAlipayno() );
        findStoreByOrgIdOutDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        findStoreByOrgIdOutDto.setWechatno( storeEntity.getWechatno() );
        findStoreByOrgIdOutDto.setWechatnourl( storeEntity.getWechatnourl() );
        findStoreByOrgIdOutDto.setNote( storeEntity.getNote() );
        findStoreByOrgIdOutDto.setMarketCapacity( storeEntity.getMarketCapacity() );
        findStoreByOrgIdOutDto.setPopulation( storeEntity.getPopulation() );
        findStoreByOrgIdOutDto.setStoreLevel( storeEntity.getStoreLevel() );
        findStoreByOrgIdOutDto.setIsUnmanned( storeEntity.getIsUnmanned() );
        findStoreByOrgIdOutDto.setSignShape( storeEntity.getSignShape() );
        findStoreByOrgIdOutDto.setSignSize( storeEntity.getSignSize() );
        findStoreByOrgIdOutDto.setSignCost( storeEntity.getSignCost() );
        findStoreByOrgIdOutDto.setDoorSize( storeEntity.getDoorSize() );
        findStoreByOrgIdOutDto.setIsUniform( storeEntity.getIsUniform() );
        findStoreByOrgIdOutDto.setUniformPic( storeEntity.getUniformPic() );
        findStoreByOrgIdOutDto.setTopKitchens( storeEntity.getTopKitchens() );
        findStoreByOrgIdOutDto.setParentId( storeEntity.getParentId() );
        findStoreByOrgIdOutDto.setTel( storeEntity.getTel() );
        findStoreByOrgIdOutDto.setFullPathId( storeEntity.getFullPathId() );
        findStoreByOrgIdOutDto.setFullPathName( storeEntity.getFullPathName() );
        findStoreByOrgIdOutDto.setAddress2( storeEntity.getAddress2() );
        findStoreByOrgIdOutDto.setCompanyId( storeEntity.getCompanyId() );
        findStoreByOrgIdOutDto.setCompanyName( storeEntity.getCompanyName() );
        findStoreByOrgIdOutDto.setArea( storeEntity.getArea() );
        findStoreByOrgIdOutDto.setAreaCode( storeEntity.getAreaCode() );
        findStoreByOrgIdOutDto.setAreaName( storeEntity.getAreaName() );
        findStoreByOrgIdOutDto.setIsToDrp( storeEntity.getIsToDrp() );
        findStoreByOrgIdOutDto.setIsManagerCluesIntoSea( storeEntity.getIsManagerCluesIntoSea() );
        findStoreByOrgIdOutDto.setToDrpStage( storeEntity.getToDrpStage() );
        findStoreByOrgIdOutDto.setStoreType( storeEntity.getStoreType() );
        findStoreByOrgIdOutDto.setStoreTypeName( storeEntity.getStoreTypeName() );
        findStoreByOrgIdOutDto.setIfDecorationStore( storeEntity.getIfDecorationStore() );
        findStoreByOrgIdOutDto.setAnnualRent( storeEntity.getAnnualRent() );
        findStoreByOrgIdOutDto.setStoreTypeCode( storeEntity.getStoreTypeCode() );
        findStoreByOrgIdOutDto.setStatus( storeEntity.getStatus() );
        findStoreByOrgIdOutDto.setStatusName( storeEntity.getStatusName() );
        findStoreByOrgIdOutDto.setAbbreviation( storeEntity.getAbbreviation() );
        findStoreByOrgIdOutDto.setPreAudit( storeEntity.getPreAudit() );
        findStoreByOrgIdOutDto.setIsTransfer( storeEntity.getIsTransfer() );
        findStoreByOrgIdOutDto.setIsToCheck( storeEntity.getIsToCheck() );
        findStoreByOrgIdOutDto.setDistributorId( storeEntity.getDistributorId() );
        findStoreByOrgIdOutDto.setDistributorCode( storeEntity.getDistributorCode() );
        findStoreByOrgIdOutDto.setDistributorName( storeEntity.getDistributorName() );
        findStoreByOrgIdOutDto.setReceiptType( storeEntity.getReceiptType() );
        findStoreByOrgIdOutDto.setFinanceAuditFlag( storeEntity.getFinanceAuditFlag() );
        findStoreByOrgIdOutDto.setDecorateStatus( storeEntity.getDecorateStatus() );
        findStoreByOrgIdOutDto.setOpenDate( storeEntity.getOpenDate() );
        findStoreByOrgIdOutDto.setCloseDate( storeEntity.getCloseDate() );
        findStoreByOrgIdOutDto.setCloseStartDate( storeEntity.getCloseStartDate() );
        findStoreByOrgIdOutDto.setStoreMarketGrade( storeEntity.getStoreMarketGrade() );
        findStoreByOrgIdOutDto.setStoreChannelCode( storeEntity.getStoreChannelCode() );
        findStoreByOrgIdOutDto.setStoreChannelId( storeEntity.getStoreChannelId() );
        findStoreByOrgIdOutDto.setStoreChannelName( storeEntity.getStoreChannelName() );
        findStoreByOrgIdOutDto.setDistributorChannel( storeEntity.getDistributorChannel() );
        findStoreByOrgIdOutDto.setDistributorChannelCode( storeEntity.getDistributorChannelCode() );
        findStoreByOrgIdOutDto.setRelatedStoreId( storeEntity.getRelatedStoreId() );
        findStoreByOrgIdOutDto.setIsVirtual( storeEntity.getIsVirtual() );
        findStoreByOrgIdOutDto.setNeedTerminalBuild( storeEntity.getNeedTerminalBuild() );
        findStoreByOrgIdOutDto.setManagerName( storeEntity.getManagerName() );
        findStoreByOrgIdOutDto.setManagerPhone( storeEntity.getManagerPhone() );
        findStoreByOrgIdOutDto.setStoreBizType( storeEntity.getStoreBizType() );
        findStoreByOrgIdOutDto.setStaffs( storeEntity.getStaffs() );
        findStoreByOrgIdOutDto.setPropertyRight( storeEntity.getPropertyRight() );
        findStoreByOrgIdOutDto.setLeaseTerm( storeEntity.getLeaseTerm() );
        findStoreByOrgIdOutDto.setBpmType( storeEntity.getBpmType() );
        findStoreByOrgIdOutDto.setUsableArea( storeEntity.getUsableArea() );
        findStoreByOrgIdOutDto.setWholeUsableArea( storeEntity.getWholeUsableArea() );
        findStoreByOrgIdOutDto.setOpenType( storeEntity.getOpenType() );
        findStoreByOrgIdOutDto.setEstimatedAnnualSales( storeEntity.getEstimatedAnnualSales() );
        findStoreByOrgIdOutDto.setShoppingRemark( storeEntity.getShoppingRemark() );
        findStoreByOrgIdOutDto.setTelRemark( storeEntity.getTelRemark() );
        findStoreByOrgIdOutDto.setEmail( storeEntity.getEmail() );
        findStoreByOrgIdOutDto.setEmailRemark( storeEntity.getEmailRemark() );
        findStoreByOrgIdOutDto.setWechatRemark( storeEntity.getWechatRemark() );
        findStoreByOrgIdOutDto.setAlipayRemark( storeEntity.getAlipayRemark() );
        findStoreByOrgIdOutDto.setChannel( storeEntity.getChannel() );
        findStoreByOrgIdOutDto.setWechat( storeEntity.getWechat() );
        findStoreByOrgIdOutDto.setChannelCode( storeEntity.getChannelCode() );
        findStoreByOrgIdOutDto.setChannelName( storeEntity.getChannelName() );
        findStoreByOrgIdOutDto.setIntro( storeEntity.getIntro() );
        findStoreByOrgIdOutDto.setConsultationPc( storeEntity.getConsultationPc() );
        findStoreByOrgIdOutDto.setConsultationM( storeEntity.getConsultationM() );
        findStoreByOrgIdOutDto.setDevelopSalesmanId( storeEntity.getDevelopSalesmanId() );
        findStoreByOrgIdOutDto.setDevelopSalesmanCode( storeEntity.getDevelopSalesmanCode() );
        findStoreByOrgIdOutDto.setDevelopSalesmanName( storeEntity.getDevelopSalesmanName() );
        findStoreByOrgIdOutDto.setDevelopSalesmanPhone( storeEntity.getDevelopSalesmanPhone() );
        findStoreByOrgIdOutDto.setCompanyPkId( storeEntity.getCompanyPkId() );
        findStoreByOrgIdOutDto.setChannelCategoryId( storeEntity.getChannelCategoryId() );
        findStoreByOrgIdOutDto.setChannelCategoryCode( storeEntity.getChannelCategoryCode() );
        findStoreByOrgIdOutDto.setChannelCategoryName( storeEntity.getChannelCategoryName() );
        findStoreByOrgIdOutDto.setChannelSubdivideId( storeEntity.getChannelSubdivideId() );
        findStoreByOrgIdOutDto.setChannelSubdivideCode( storeEntity.getChannelSubdivideCode() );
        findStoreByOrgIdOutDto.setChannelSubdivideName( storeEntity.getChannelSubdivideName() );
        findStoreByOrgIdOutDto.setCluesMsgUser( storeEntity.getCluesMsgUser() );
        findStoreByOrgIdOutDto.setStoreSubChannelCode( storeEntity.getStoreSubChannelCode() );
        findStoreByOrgIdOutDto.setOutsideStoreName( storeEntity.getOutsideStoreName() );
        findStoreByOrgIdOutDto.setEpsUsableArea( storeEntity.getEpsUsableArea() );
        findStoreByOrgIdOutDto.setCluesFollow( storeEntity.getCluesFollow() );
        findStoreByOrgIdOutDto.setCluesLose( storeEntity.getCluesLose() );
        findStoreByOrgIdOutDto.setCluesOverdue( storeEntity.getCluesOverdue() );
        findStoreByOrgIdOutDto.setDepositCluesOverdue( storeEntity.getDepositCluesOverdue() );
        findStoreByOrgIdOutDto.setOrderCheckChannel( storeEntity.getOrderCheckChannel() );
        findStoreByOrgIdOutDto.setUsePos( storeEntity.getUsePos() );
        findStoreByOrgIdOutDto.setTerminalImageScore( storeEntity.getTerminalImageScore() );
        findStoreByOrgIdOutDto.setIsSceneFlag( storeEntity.getIsSceneFlag() );
        findStoreByOrgIdOutDto.setSceneSuit( storeEntity.getSceneSuit() );
        findStoreByOrgIdOutDto.setLiveDemo( storeEntity.getLiveDemo() );
        findStoreByOrgIdOutDto.setLayoutCode( storeEntity.getLayoutCode() );
        findStoreByOrgIdOutDto.setTerminalCheckDateAgain( storeEntity.getTerminalCheckDateAgain() );
        findStoreByOrgIdOutDto.setTerminalCheckDate( storeEntity.getTerminalCheckDate() );
        findStoreByOrgIdOutDto.setLeaderName( storeEntity.getLeaderName() );
        findStoreByOrgIdOutDto.setVersion( storeEntity.getVersion() );
        List<String> list = storeEntity.getRealKeyWordList();
        if ( list != null ) {
            findStoreByOrgIdOutDto.setRealKeyWordList( new ArrayList<String>( list ) );
        }
        List<String> list1 = storeEntity.getPosKeyList();
        if ( list1 != null ) {
            findStoreByOrgIdOutDto.setPosKeyList( new ArrayList<String>( list1 ) );
        }
        findStoreByOrgIdOutDto.setIsCashierEnabled( storeEntity.getIsCashierEnabled() );
        findStoreByOrgIdOutDto.setIzImPayActivityEnabled( storeEntity.getIzImPayActivityEnabled() );
        findStoreByOrgIdOutDto.setMerchantCode( storeEntity.getMerchantCode() );
        findStoreByOrgIdOutDto.setMerchantName( storeEntity.getMerchantName() );
        findStoreByOrgIdOutDto.setMerchantCodeOnline( storeEntity.getMerchantCodeOnline() );
        findStoreByOrgIdOutDto.setCashierChargeMapping( storeEntity.getCashierChargeMapping() );
        findStoreByOrgIdOutDto.setOpenWyChannel( storeEntity.getOpenWyChannel() );
        findStoreByOrgIdOutDto.setOpenZqChannel( storeEntity.getOpenZqChannel() );
        findStoreByOrgIdOutDto.setOpenRqChannel( storeEntity.getOpenRqChannel() );
        findStoreByOrgIdOutDto.setIsSmartBadge( storeEntity.getIsSmartBadge() );
        findStoreByOrgIdOutDto.setDepartmentCode( storeEntity.getDepartmentCode() );
        findStoreByOrgIdOutDto.setDepartmentName( storeEntity.getDepartmentName() );
        findStoreByOrgIdOutDto.setDepartmentStatus( storeEntity.getDepartmentStatus() );
        findStoreByOrgIdOutDto.setOldCompensateMerchantCode( storeEntity.getOldCompensateMerchantCode() );
        findStoreByOrgIdOutDto.setCluesCheckPhone( storeEntity.getCluesCheckPhone() );
        findStoreByOrgIdOutDto.setCluesProcessResult( storeEntity.getCluesProcessResult() );
        findStoreByOrgIdOutDto.setCluesCheckScope( storeEntity.getCluesCheckScope() );
        findStoreByOrgIdOutDto.setCluesCheckPeriod( storeEntity.getCluesCheckPeriod() );
        findStoreByOrgIdOutDto.setCluesFollowStatus( storeEntity.getCluesFollowStatus() );
        findStoreByOrgIdOutDto.setTiktokCode( storeEntity.getTiktokCode() );
        findStoreByOrgIdOutDto.setTiktokName( storeEntity.getTiktokName() );

        return findStoreByOrgIdOutDto;
    }

    @Override
    public FindStoreByDistributorOutDto storeEntityToFindStoreByDistributorOutDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        FindStoreByDistributorOutDto findStoreByDistributorOutDto = new FindStoreByDistributorOutDto();

        findStoreByDistributorOutDto.setId( storeEntity.getId() );
        findStoreByDistributorOutDto.setOrgId( storeEntity.getOrgId() );
        findStoreByDistributorOutDto.setCode( storeEntity.getCode() );
        findStoreByDistributorOutDto.setName( storeEntity.getName() );
        findStoreByDistributorOutDto.setStoreType( storeEntity.getStoreType() );
        findStoreByDistributorOutDto.setStoreTypeName( storeEntity.getStoreTypeName() );
        findStoreByDistributorOutDto.setAbbreviation( storeEntity.getAbbreviation() );
        findStoreByDistributorOutDto.setPreAudit( storeEntity.getPreAudit() );
        findStoreByDistributorOutDto.setDistributorId( storeEntity.getDistributorId() );
        findStoreByDistributorOutDto.setDistributorCode( storeEntity.getDistributorCode() );
        findStoreByDistributorOutDto.setDistributorName( storeEntity.getDistributorName() );
        findStoreByDistributorOutDto.setStatus( storeEntity.getStatus() );
        findStoreByDistributorOutDto.setStatusName( storeEntity.getStatusName() );

        return findStoreByDistributorOutDto;
    }

    @Override
    public List<FindStoreByDistributorOutDto> storeEntityListToFindStoreByDistributorOutDtoList(List<StoreEntity> storeEntityList) {
        if ( storeEntityList == null ) {
            return null;
        }

        List<FindStoreByDistributorOutDto> list = new ArrayList<FindStoreByDistributorOutDto>( storeEntityList.size() );
        for ( StoreEntity storeEntity : storeEntityList ) {
            list.add( storeEntityToFindStoreByDistributorOutDto( storeEntity ) );
        }

        return list;
    }

    @Override
    public StoreSalesmanReportInDto storeSalesmanReportEmailInDtoToStoreSalesmanReportInDto(StoreSalesmanReportEmailInDto storeSalesmanReportEmailInDto) {
        if ( storeSalesmanReportEmailInDto == null ) {
            return null;
        }

        StoreSalesmanReportInDto storeSalesmanReportInDto = new StoreSalesmanReportInDto();

        storeSalesmanReportInDto.setReportDate( storeSalesmanReportEmailInDto.getReportDate() );
        storeSalesmanReportInDto.setType( storeSalesmanReportEmailInDto.getType() );
        storeSalesmanReportInDto.setParentId( storeSalesmanReportEmailInDto.getParentId() );
        storeSalesmanReportInDto.setIsCumulative( storeSalesmanReportEmailInDto.getIsCumulative() );
        List<Long> list = storeSalesmanReportEmailInDto.getStoreTypes();
        if ( list != null ) {
            storeSalesmanReportInDto.setStoreTypes( new ArrayList<Long>( list ) );
        }
        List<String> list1 = storeSalesmanReportEmailInDto.getKeyWords();
        if ( list1 != null ) {
            storeSalesmanReportInDto.setKeyWords( new ArrayList<String>( list1 ) );
        }
        List<String> list2 = storeSalesmanReportEmailInDto.getStoreTypeCodes();
        if ( list2 != null ) {
            storeSalesmanReportInDto.setStoreTypeCodes( new ArrayList<String>( list2 ) );
        }
        storeSalesmanReportInDto.setIsVirtual( storeSalesmanReportEmailInDto.getIsVirtual() );
        storeSalesmanReportInDto.setAreaId( storeSalesmanReportEmailInDto.getAreaId() );
        storeSalesmanReportInDto.setCompanyId( storeSalesmanReportEmailInDto.getCompanyId() );
        storeSalesmanReportInDto.setDevelopSalesmanId( storeSalesmanReportEmailInDto.getDevelopSalesmanId() );
        storeSalesmanReportInDto.setStartDate( storeSalesmanReportEmailInDto.getStartDate() );
        storeSalesmanReportInDto.setEndDate( storeSalesmanReportEmailInDto.getEndDate() );
        storeSalesmanReportInDto.setReportDateSchemaTableName( storeSalesmanReportEmailInDto.getReportDateSchemaTableName() );
        storeSalesmanReportInDto.setStartDateSchemaTableName( storeSalesmanReportEmailInDto.getStartDateSchemaTableName() );
        storeSalesmanReportInDto.setEndDateSchemaTableName( storeSalesmanReportEmailInDto.getEndDateSchemaTableName() );

        return storeSalesmanReportInDto;
    }

    @Override
    public List<StoreSalesmanReportEmailArea> storeSalesmanReportOutDtoListToStoreSalesmanReportEmailAreaList(List<StoreSalesmanReportOutDto> storeSalesmanReportOutDtoList) {
        if ( storeSalesmanReportOutDtoList == null ) {
            return null;
        }

        List<StoreSalesmanReportEmailArea> list = new ArrayList<StoreSalesmanReportEmailArea>( storeSalesmanReportOutDtoList.size() );
        for ( StoreSalesmanReportOutDto storeSalesmanReportOutDto : storeSalesmanReportOutDtoList ) {
            list.add( storeSalesmanReportOutDtoToStoreSalesmanReportEmailArea( storeSalesmanReportOutDto ) );
        }

        return list;
    }

    @Override
    public StoreSalesmanReportEmailArea storeSalesmanReportOutDtoToStoreSalesmanReportEmailArea(StoreSalesmanReportOutDto storeSalesmanReportOutDto) {
        if ( storeSalesmanReportOutDto == null ) {
            return null;
        }

        StoreSalesmanReportEmailArea storeSalesmanReportEmailArea = new StoreSalesmanReportEmailArea();

        storeSalesmanReportEmailArea.setAreaName( storeSalesmanReportOutDto.getAreaName() );
        storeSalesmanReportEmailArea.setStoreNum( storeSalesmanReportOutDto.getStoreNum() );
        storeSalesmanReportEmailArea.setShopownerNum( storeSalesmanReportOutDto.getShopownerNum() );
        storeSalesmanReportEmailArea.setAdviserNum( storeSalesmanReportOutDto.getAdviserNum() );
        storeSalesmanReportEmailArea.setCustomerManagerNum( storeSalesmanReportOutDto.getCustomerManagerNum() );

        return storeSalesmanReportEmailArea;
    }

    @Override
    public List<StoreSalesmanReportEmailCompany> storeSalesmanReportOutDtoListToStoreSalesmanReportEmailCompanyList(List<StoreSalesmanReportOutDto> storeSalesmanReportOutDtoList) {
        if ( storeSalesmanReportOutDtoList == null ) {
            return null;
        }

        List<StoreSalesmanReportEmailCompany> list = new ArrayList<StoreSalesmanReportEmailCompany>( storeSalesmanReportOutDtoList.size() );
        for ( StoreSalesmanReportOutDto storeSalesmanReportOutDto : storeSalesmanReportOutDtoList ) {
            list.add( storeSalesmanReportOutDtoToStoreSalesmanReportEmailCompany( storeSalesmanReportOutDto ) );
        }

        return list;
    }

    @Override
    public StoreSalesmanReportEmailCompany storeSalesmanReportOutDtoToStoreSalesmanReportEmailCompany(StoreSalesmanReportOutDto storeSalesmanReportOutDto) {
        if ( storeSalesmanReportOutDto == null ) {
            return null;
        }

        StoreSalesmanReportEmailCompany storeSalesmanReportEmailCompany = new StoreSalesmanReportEmailCompany();

        storeSalesmanReportEmailCompany.setCompanyName( storeSalesmanReportOutDto.getCompanyName() );
        storeSalesmanReportEmailCompany.setStoreNum( storeSalesmanReportOutDto.getStoreNum() );
        storeSalesmanReportEmailCompany.setShopownerNum( storeSalesmanReportOutDto.getShopownerNum() );
        storeSalesmanReportEmailCompany.setAdviserNum( storeSalesmanReportOutDto.getAdviserNum() );
        storeSalesmanReportEmailCompany.setCustomerManagerNum( storeSalesmanReportOutDto.getCustomerManagerNum() );

        return storeSalesmanReportEmailCompany;
    }

    @Override
    public List<StoreSalesmanReportEmailAdviser> storeSalesmanReportOutDtoListToStoreSalesmanReportEmailAdviserList(List<StoreSalesmanReportOutDto> storeSalesmanReportOutDtoList) {
        if ( storeSalesmanReportOutDtoList == null ) {
            return null;
        }

        List<StoreSalesmanReportEmailAdviser> list = new ArrayList<StoreSalesmanReportEmailAdviser>( storeSalesmanReportOutDtoList.size() );
        for ( StoreSalesmanReportOutDto storeSalesmanReportOutDto : storeSalesmanReportOutDtoList ) {
            list.add( storeSalesmanReportOutDtoToStoreSalesmanReportEmailAdviser( storeSalesmanReportOutDto ) );
        }

        return list;
    }

    @Override
    public StoreSalesmanReportEmailAdviser storeSalesmanReportOutDtoToStoreSalesmanReportEmailAdviser(StoreSalesmanReportOutDto storeSalesmanReportOutDto) {
        if ( storeSalesmanReportOutDto == null ) {
            return null;
        }

        StoreSalesmanReportEmailAdviser storeSalesmanReportEmailAdviser = new StoreSalesmanReportEmailAdviser();

        storeSalesmanReportEmailAdviser.setDevelopSalesmanName( storeSalesmanReportOutDto.getDevelopSalesmanName() );
        storeSalesmanReportEmailAdviser.setStoreNum( storeSalesmanReportOutDto.getStoreNum() );
        storeSalesmanReportEmailAdviser.setShopownerNum( storeSalesmanReportOutDto.getShopownerNum() );
        storeSalesmanReportEmailAdviser.setAdviserNum( storeSalesmanReportOutDto.getAdviserNum() );
        storeSalesmanReportEmailAdviser.setCustomerManagerNum( storeSalesmanReportOutDto.getCustomerManagerNum() );

        return storeSalesmanReportEmailAdviser;
    }

    @Override
    public List<StoreSalesmanReportEmailCustomerManager> storeSalesmanReportOutDtoListToStoreSalesmanReportEmailCustomerManagerList(List<StoreSalesmanReportOutDto> storeSalesmanReportOutDtoList) {
        if ( storeSalesmanReportOutDtoList == null ) {
            return null;
        }

        List<StoreSalesmanReportEmailCustomerManager> list = new ArrayList<StoreSalesmanReportEmailCustomerManager>( storeSalesmanReportOutDtoList.size() );
        for ( StoreSalesmanReportOutDto storeSalesmanReportOutDto : storeSalesmanReportOutDtoList ) {
            list.add( storeSalesmanReportOutDtoToStoreSalesmanReportEmailCustomerManager( storeSalesmanReportOutDto ) );
        }

        return list;
    }

    @Override
    public StoreSalesmanReportEmailCustomerManager storeSalesmanReportOutDtoToStoreSalesmanReportEmailCustomerManager(StoreSalesmanReportOutDto storeSalesmanReportOutDto) {
        if ( storeSalesmanReportOutDto == null ) {
            return null;
        }

        StoreSalesmanReportEmailCustomerManager storeSalesmanReportEmailCustomerManager = new StoreSalesmanReportEmailCustomerManager();

        storeSalesmanReportEmailCustomerManager.setAbbreviation( storeSalesmanReportOutDto.getAbbreviation() );
        storeSalesmanReportEmailCustomerManager.setStoreNum( storeSalesmanReportOutDto.getStoreNum() );
        storeSalesmanReportEmailCustomerManager.setShopownerNum( storeSalesmanReportOutDto.getShopownerNum() );
        storeSalesmanReportEmailCustomerManager.setAdviserNum( storeSalesmanReportOutDto.getAdviserNum() );
        storeSalesmanReportEmailCustomerManager.setCustomerManagerNum( storeSalesmanReportOutDto.getCustomerManagerNum() );

        return storeSalesmanReportEmailCustomerManager;
    }

    @Override
    public SyncStoreDto entity2SyncDto(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        SyncStoreDto syncStoreDto = new SyncStoreDto();

        syncStoreDto.setId( storeEntity.getId() );
        syncStoreDto.setIsDeleted( storeEntity.getIsDeleted() );
        syncStoreDto.setCreatedBy( storeEntity.getCreatedBy() );
        syncStoreDto.setModifiedBy( storeEntity.getModifiedBy() );
        syncStoreDto.setModifiedDate( storeEntity.getModifiedDate() );
        syncStoreDto.setChannelCategoryId( storeEntity.getChannelCategoryId() );
        syncStoreDto.setChannelCategoryCode( storeEntity.getChannelCategoryCode() );
        syncStoreDto.setChannelCategoryName( storeEntity.getChannelCategoryName() );
        syncStoreDto.setChannelSubdivideId( storeEntity.getChannelSubdivideId() );
        syncStoreDto.setChannelSubdivideCode( storeEntity.getChannelSubdivideCode() );
        syncStoreDto.setChannelSubdivideName( storeEntity.getChannelSubdivideName() );
        syncStoreDto.setPlanCode( storeEntity.getPlanCode() );
        syncStoreDto.setChannelCategoryIds( storeEntity.getChannelCategoryIds() );
        syncStoreDto.setChannelSubdivideIds( storeEntity.getChannelSubdivideIds() );
        syncStoreDto.setOrgId( storeEntity.getOrgId() );
        syncStoreDto.setKeyWord( storeEntity.getKeyWord() );
        syncStoreDto.setCode( storeEntity.getCode() );
        List<String> list = storeEntity.getCodes();
        if ( list != null ) {
            syncStoreDto.setCodes( new ArrayList<String>( list ) );
        }
        syncStoreDto.setName( storeEntity.getName() );
        syncStoreDto.setNameOrCode( storeEntity.getNameOrCode() );
        syncStoreDto.setCoverurl( storeEntity.getCoverurl() );
        syncStoreDto.setProvicenId( storeEntity.getProvicenId() );
        syncStoreDto.setProvicenName( storeEntity.getProvicenName() );
        syncStoreDto.setCityId( storeEntity.getCityId() );
        syncStoreDto.setCityName( storeEntity.getCityName() );
        syncStoreDto.setCountyId( storeEntity.getCountyId() );
        syncStoreDto.setCountyName( storeEntity.getCountyName() );
        syncStoreDto.setAddress( storeEntity.getAddress() );
        syncStoreDto.setAddress2( storeEntity.getAddress2() );
        syncStoreDto.setShoppingStart( storeEntity.getShoppingStart() );
        syncStoreDto.setShoppingEnd( storeEntity.getShoppingEnd() );
        syncStoreDto.setTel( storeEntity.getTel() );
        syncStoreDto.setLeaderId( storeEntity.getLeaderId() );
        syncStoreDto.setLeaderName( storeEntity.getLeaderName() );
        syncStoreDto.setAlipayno( storeEntity.getAlipayno() );
        syncStoreDto.setAlipaynourl( storeEntity.getAlipaynourl() );
        syncStoreDto.setWechatno( storeEntity.getWechatno() );
        syncStoreDto.setWechatnourl( storeEntity.getWechatnourl() );
        syncStoreDto.setNote( storeEntity.getNote() );
        syncStoreDto.setMarketCapacity( storeEntity.getMarketCapacity() );
        syncStoreDto.setPopulation( storeEntity.getPopulation() );
        syncStoreDto.setStoreLevel( storeEntity.getStoreLevel() );
        syncStoreDto.setIsUnmanned( storeEntity.getIsUnmanned() );
        syncStoreDto.setSignShape( storeEntity.getSignShape() );
        syncStoreDto.setSignSize( storeEntity.getSignSize() );
        syncStoreDto.setSignCost( storeEntity.getSignCost() );
        syncStoreDto.setIsUniform( storeEntity.getIsUniform() );
        syncStoreDto.setUniformPic( storeEntity.getUniformPic() );
        syncStoreDto.setTopKitchens( storeEntity.getTopKitchens() );
        syncStoreDto.setDoorSize( storeEntity.getDoorSize() );
        syncStoreDto.setStatus( storeEntity.getStatus() );
        Set<Integer> set = storeEntity.getStatusSet();
        if ( set != null ) {
            syncStoreDto.setStatusSet( new HashSet<Integer>( set ) );
        }
        syncStoreDto.setLongitude( storeEntity.getLongitude() );
        syncStoreDto.setLatitude( storeEntity.getLatitude() );
        syncStoreDto.setDistance( storeEntity.getDistance() );
        syncStoreDto.setIsOpenKpoint( storeEntity.getIsOpenKpoint() );
        syncStoreDto.setCompanyId( storeEntity.getCompanyId() );
        syncStoreDto.setCompanyCode( storeEntity.getCompanyCode() );
        syncStoreDto.setLiveDemo( storeEntity.getLiveDemo() );
        syncStoreDto.setLayoutCode( storeEntity.getLayoutCode() );
        syncStoreDto.setCompanyIds( storeEntity.getCompanyIds() );
        syncStoreDto.setCompanyName( storeEntity.getCompanyName() );
        syncStoreDto.setCharacter( storeEntity.getCharacter() );
        syncStoreDto.setDecorationType( storeEntity.getDecorationType() );
        syncStoreDto.setParentId( storeEntity.getParentId() );
        syncStoreDto.setParentName( storeEntity.getParentName() );
        syncStoreDto.setParentCode( storeEntity.getParentCode() );
        syncStoreDto.setFullPathName( storeEntity.getFullPathName() );
        syncStoreDto.setFullPathId( storeEntity.getFullPathId() );
        syncStoreDto.setShoppingRemark( storeEntity.getShoppingRemark() );
        syncStoreDto.setTelRemark( storeEntity.getTelRemark() );
        syncStoreDto.setEmail( storeEntity.getEmail() );
        syncStoreDto.setEmailRemark( storeEntity.getEmailRemark() );
        syncStoreDto.setWechatRemark( storeEntity.getWechatRemark() );
        syncStoreDto.setAlipayRemark( storeEntity.getAlipayRemark() );
        syncStoreDto.setChannel( storeEntity.getChannel() );
        syncStoreDto.setWechat( storeEntity.getWechat() );
        syncStoreDto.setIsToDrp( storeEntity.getIsToDrp() );
        syncStoreDto.setIsManagerCluesIntoSea( storeEntity.getIsManagerCluesIntoSea() );
        syncStoreDto.setToDrpStage( storeEntity.getToDrpStage() );
        syncStoreDto.setStoreType( storeEntity.getStoreType() );
        syncStoreDto.setChannelCode( storeEntity.getChannelCode() );
        syncStoreDto.setChannelName( storeEntity.getChannelName() );
        syncStoreDto.setCluesMsgUser( storeEntity.getCluesMsgUser() );
        syncStoreDto.setAbbreviation( storeEntity.getAbbreviation() );
        syncStoreDto.setDistributorId( storeEntity.getDistributorId() );
        syncStoreDto.setDistributorIds( storeEntity.getDistributorIds() );
        syncStoreDto.setChannelSubdivide( storeEntity.getChannelSubdivide() );
        syncStoreDto.setCutTime( storeEntity.getCutTime() );
        syncStoreDto.setStoreTypeName( storeEntity.getStoreTypeName() );
        syncStoreDto.setStoreTypeCode( storeEntity.getStoreTypeCode() );
        syncStoreDto.setChannelCategory( storeEntity.getChannelCategory() );
        syncStoreDto.setArea( storeEntity.getArea() );
        syncStoreDto.setAreaCode( storeEntity.getAreaCode() );
        syncStoreDto.setAreaName( storeEntity.getAreaName() );
        syncStoreDto.setAreas( storeEntity.getAreas() );
        syncStoreDto.setDevelopSalesmanId( storeEntity.getDevelopSalesmanId() );
        syncStoreDto.setDevelopSalesmanCode( storeEntity.getDevelopSalesmanCode() );
        syncStoreDto.setDevelopSalesmanName( storeEntity.getDevelopSalesmanName() );
        syncStoreDto.setDevelopSalesmanPhone( storeEntity.getDevelopSalesmanPhone() );
        syncStoreDto.setDistributorName( storeEntity.getDistributorName() );
        syncStoreDto.setReceiptType( storeEntity.getReceiptType() );
        syncStoreDto.setFinanceAuditFlag( storeEntity.getFinanceAuditFlag() );
        syncStoreDto.setPreAudit( storeEntity.getPreAudit() );
        syncStoreDto.setIntro( storeEntity.getIntro() );
        syncStoreDto.setConsultationPc( storeEntity.getConsultationPc() );
        syncStoreDto.setConsultationM( storeEntity.getConsultationM() );
        syncStoreDto.setChannelSubdivides( storeEntity.getChannelSubdivides() );
        syncStoreDto.setDistributorCode( storeEntity.getDistributorCode() );
        syncStoreDto.setIsTransfer( storeEntity.getIsTransfer() );
        syncStoreDto.setIsToCheck( storeEntity.getIsToCheck() );
        syncStoreDto.setOrderByCode( storeEntity.getOrderByCode() );
        syncStoreDto.setIfDecorationStore( storeEntity.getIfDecorationStore() );
        syncStoreDto.setCreatedDateStart( storeEntity.getCreatedDateStart() );
        syncStoreDto.setCreatedDateEnd( storeEntity.getCreatedDateEnd() );
        syncStoreDto.setDecorateStatus( storeEntity.getDecorateStatus() );
        syncStoreDto.setOpenDate( storeEntity.getOpenDate() );
        syncStoreDto.setCloseDate( storeEntity.getCloseDate() );
        syncStoreDto.setTerminalCheckDateAgain( storeEntity.getTerminalCheckDateAgain() );
        syncStoreDto.setTerminalCheckDate( storeEntity.getTerminalCheckDate() );
        syncStoreDto.setCloseStartDate( storeEntity.getCloseStartDate() );
        syncStoreDto.setOldStoreId( storeEntity.getOldStoreId() );
        syncStoreDto.setStoreMarketGrade( storeEntity.getStoreMarketGrade() );
        syncStoreDto.setStoreChannelCode( storeEntity.getStoreChannelCode() );
        syncStoreDto.setStoreChannelId( storeEntity.getStoreChannelId() );
        syncStoreDto.setStoreChannelName( storeEntity.getStoreChannelName() );
        syncStoreDto.setDistributorChannel( storeEntity.getDistributorChannel() );
        syncStoreDto.setDistributorChannelCode( storeEntity.getDistributorChannelCode() );
        syncStoreDto.setRelatedStoreId( storeEntity.getRelatedStoreId() );
        syncStoreDto.setIsVirtual( storeEntity.getIsVirtual() );
        syncStoreDto.setNeedTerminalBuild( storeEntity.getNeedTerminalBuild() );
        syncStoreDto.setManagerName( storeEntity.getManagerName() );
        syncStoreDto.setManagerPhone( storeEntity.getManagerPhone() );
        syncStoreDto.setStoreBizType( storeEntity.getStoreBizType() );
        syncStoreDto.setStaffs( storeEntity.getStaffs() );
        syncStoreDto.setPropertyRight( storeEntity.getPropertyRight() );
        syncStoreDto.setLeaseTerm( storeEntity.getLeaseTerm() );
        syncStoreDto.setBpmType( storeEntity.getBpmType() );
        syncStoreDto.setUsableArea( storeEntity.getUsableArea() );
        syncStoreDto.setWholeUsableArea( storeEntity.getWholeUsableArea() );
        syncStoreDto.setOpenType( storeEntity.getOpenType() );
        syncStoreDto.setEstimatedAnnualSales( storeEntity.getEstimatedAnnualSales() );
        syncStoreDto.setCreatedDate( storeEntity.getCreatedDate() );
        syncStoreDto.setStoreKeyword( storeEntity.getStoreKeyword() );
        syncStoreDto.setStoreKeywordType( storeEntity.getStoreKeywordType() );
        syncStoreDto.setDevelopSalesman( storeEntity.getDevelopSalesman() );
        syncStoreDto.setOpenStartTime( storeEntity.getOpenStartTime() );
        syncStoreDto.setOpenEndTime( storeEntity.getOpenEndTime() );
        syncStoreDto.setCloseStartTime( storeEntity.getCloseStartTime() );
        syncStoreDto.setCloseEndTime( storeEntity.getCloseEndTime() );
        syncStoreDto.setCreatedStartTime( storeEntity.getCreatedStartTime() );
        syncStoreDto.setCreatedEndTime( storeEntity.getCreatedEndTime() );
        syncStoreDto.setCompanyPkId( storeEntity.getCompanyPkId() );
        syncStoreDto.setStatusMulti( storeEntity.getStatusMulti() );
        syncStoreDto.setStoreSubChannelCode( storeEntity.getStoreSubChannelCode() );
        syncStoreDto.setOutsideStoreName( storeEntity.getOutsideStoreName() );
        syncStoreDto.setStoreId( storeEntity.getStoreId() );
        List<Long> list1 = storeEntity.getOrgIds();
        if ( list1 != null ) {
            syncStoreDto.setOrgIds( new ArrayList<Long>( list1 ) );
        }
        List<Long> list2 = storeEntity.getIds();
        if ( list2 != null ) {
            syncStoreDto.setIds( new ArrayList<Long>( list2 ) );
        }
        syncStoreDto.setStatusName( storeEntity.getStatusName() );
        syncStoreDto.setEpsUsableArea( storeEntity.getEpsUsableArea() );
        syncStoreDto.setStadiumsIntroduce( storeEntity.getStadiumsIntroduce() );
        syncStoreDto.setIsOpenRent( storeEntity.getIsOpenRent() );
        syncStoreDto.setTravelTips( storeEntity.getTravelTips() );
        syncStoreDto.setFloors( storeEntity.getFloors() );
        syncStoreDto.setFloorHeight( storeEntity.getFloorHeight() );
        syncStoreDto.setEmptyPlanCode( storeEntity.getEmptyPlanCode() );
        syncStoreDto.setUsableAreaLower( storeEntity.getUsableAreaLower() );
        syncStoreDto.setUsableAreaUpper( storeEntity.getUsableAreaUpper() );
        Set<Integer> set1 = storeEntity.getFloorSet();
        if ( set1 != null ) {
            syncStoreDto.setFloorSet( new HashSet<Integer>( set1 ) );
        }
        Set<String> set2 = storeEntity.getStoreLevels();
        if ( set2 != null ) {
            syncStoreDto.setStoreLevels( new HashSet<String>( set2 ) );
        }
        syncStoreDto.setCluesFollow( storeEntity.getCluesFollow() );
        syncStoreDto.setCluesLose( storeEntity.getCluesLose() );
        syncStoreDto.setAnnualRent( storeEntity.getAnnualRent() );
        syncStoreDto.setCluesOverdue( storeEntity.getCluesOverdue() );
        syncStoreDto.setDepositCluesOverdue( storeEntity.getDepositCluesOverdue() );
        syncStoreDto.setOrderCheckChannel( storeEntity.getOrderCheckChannel() );
        syncStoreDto.setIsFrontage( storeEntity.getIsFrontage() );
        syncStoreDto.setUsePos( storeEntity.getUsePos() );
        syncStoreDto.setTerminalImageScore( storeEntity.getTerminalImageScore() );
        syncStoreDto.setDepOrgIds( storeEntity.getDepOrgIds() );
        syncStoreDto.setWholeUsableAreaBegin( storeEntity.getWholeUsableAreaBegin() );
        syncStoreDto.setWholeUsableAreaEnd( storeEntity.getWholeUsableAreaEnd() );
        syncStoreDto.setIsSceneFlag( storeEntity.getIsSceneFlag() );
        syncStoreDto.setSceneSuit( storeEntity.getSceneSuit() );
        syncStoreDto.setTerminalCheckDateStart( storeEntity.getTerminalCheckDateStart() );
        syncStoreDto.setTerminalCheckDateEnd( storeEntity.getTerminalCheckDateEnd() );
        syncStoreDto.setTerminalCheckDateAgainStart( storeEntity.getTerminalCheckDateAgainStart() );
        syncStoreDto.setTerminalCheckDateAgainEnd( storeEntity.getTerminalCheckDateAgainEnd() );
        syncStoreDto.setVersion( storeEntity.getVersion() );
        List<String> list3 = storeEntity.getRealKeyWordList();
        if ( list3 != null ) {
            syncStoreDto.setRealKeyWordList( new ArrayList<String>( list3 ) );
        }
        List<Long> list4 = storeEntity.getRealKeyWordStoreIdList();
        if ( list4 != null ) {
            syncStoreDto.setRealKeyWordStoreIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = storeEntity.getPosKeyList();
        if ( list5 != null ) {
            syncStoreDto.setPosKeyList( new ArrayList<String>( list5 ) );
        }
        List<Long> list6 = storeEntity.getNotInCompanyIds();
        if ( list6 != null ) {
            syncStoreDto.setNotInCompanyIds( new ArrayList<Long>( list6 ) );
        }
        syncStoreDto.setIsCashierEnabled( storeEntity.getIsCashierEnabled() );
        syncStoreDto.setIzImPayActivityEnabled( storeEntity.getIzImPayActivityEnabled() );
        syncStoreDto.setMerchantCode( storeEntity.getMerchantCode() );
        syncStoreDto.setMerchantName( storeEntity.getMerchantName() );
        syncStoreDto.setMerchantCodeOnline( storeEntity.getMerchantCodeOnline() );
        syncStoreDto.setCashierChargeMapping( storeEntity.getCashierChargeMapping() );
        syncStoreDto.setOpenWyChannel( storeEntity.getOpenWyChannel() );
        syncStoreDto.setOpenZqChannel( storeEntity.getOpenZqChannel() );
        syncStoreDto.setOpenRqChannel( storeEntity.getOpenRqChannel() );
        syncStoreDto.setIsSmartBadge( storeEntity.getIsSmartBadge() );
        syncStoreDto.setDepartmentCode( storeEntity.getDepartmentCode() );
        syncStoreDto.setDepartmentName( storeEntity.getDepartmentName() );
        syncStoreDto.setDepartmentStatus( storeEntity.getDepartmentStatus() );
        syncStoreDto.setOldCompensateMerchantCode( storeEntity.getOldCompensateMerchantCode() );
        List<Integer> list7 = storeEntity.getOpenModelList();
        if ( list7 != null ) {
            syncStoreDto.setOpenModelList( new ArrayList<Integer>( list7 ) );
        }
        syncStoreDto.setCluesCheckPhone( storeEntity.getCluesCheckPhone() );
        syncStoreDto.setCluesProcessResult( storeEntity.getCluesProcessResult() );
        syncStoreDto.setCluesCheckScope( storeEntity.getCluesCheckScope() );
        syncStoreDto.setCluesCheckPeriod( storeEntity.getCluesCheckPeriod() );
        syncStoreDto.setCluesFollowStatus( storeEntity.getCluesFollowStatus() );
        syncStoreDto.setTiktokCode( storeEntity.getTiktokCode() );
        syncStoreDto.setTiktokName( storeEntity.getTiktokName() );
        List<Long> list8 = storeEntity.getStoreOrgIdList();
        if ( list8 != null ) {
            syncStoreDto.setStoreOrgIdList( new ArrayList<Long>( list8 ) );
        }

        return syncStoreDto;
    }

    @Override
    public UpdateByChangeProc entity2ChangeProc(StoreEntity storeEntity) {
        if ( storeEntity == null ) {
            return null;
        }

        UpdateByChangeProc updateByChangeProc = new UpdateByChangeProc();

        updateByChangeProc.setStoreId( storeEntity.getId() );
        updateByChangeProc.setStoreChannelCodeName( storeEntity.getStoreTypeName() );
        updateByChangeProc.setIsVirtual( storeEntity.getIsVirtual() );
        updateByChangeProc.setNeedTerminalBuild( storeEntity.getNeedTerminalBuild() );
        updateByChangeProc.setStatus( storeEntity.getStatus() );
        updateByChangeProc.setProvicenId( storeEntity.getProvicenId() );
        updateByChangeProc.setCityId( storeEntity.getCityId() );
        updateByChangeProc.setCountyId( storeEntity.getCountyId() );
        updateByChangeProc.setProvicenName( storeEntity.getProvicenName() );
        updateByChangeProc.setCityName( storeEntity.getCityName() );
        updateByChangeProc.setCountyName( storeEntity.getCountyName() );
        updateByChangeProc.setAddress( storeEntity.getAddress() );
        updateByChangeProc.setLongitude( storeEntity.getLongitude() );
        updateByChangeProc.setLatitude( storeEntity.getLatitude() );
        updateByChangeProc.setName( storeEntity.getName() );
        updateByChangeProc.setStoreBizType( storeEntity.getStoreBizType() );
        updateByChangeProc.setStoreChannelCode( storeEntity.getStoreChannelCode() );
        updateByChangeProc.setStoreType( storeEntity.getStoreType() );
        updateByChangeProc.setStoreMarketGrade( storeEntity.getStoreMarketGrade() );
        updateByChangeProc.setStoreSubChannelCode( storeEntity.getStoreSubChannelCode() );
        updateByChangeProc.setStoreLevel( storeEntity.getStoreLevel() );
        updateByChangeProc.setWholeUsableArea( storeEntity.getWholeUsableArea() );
        updateByChangeProc.setCharacter( storeEntity.getCharacter() );
        updateByChangeProc.setStatusName( storeEntity.getStatusName() );

        return updateByChangeProc;
    }
}
