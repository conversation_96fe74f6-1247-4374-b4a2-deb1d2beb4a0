package com.fotile.datacenter.onePage.pojo.mapper;

import com.fotile.datacenter.onePage.pojo.dto.CluesFollowCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.CluesLevelCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.ComeDeviseCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.DrainageChannelCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.KeyCluesCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.LostOrderCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.OnePgCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.OrderCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.PersonAbnormalCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.PersonEnterDivorceCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.ProductMixCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.SalesmanRankCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.SceneCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.TargetCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.TrafficCashCardInDTO;
import com.fotile.datacenter.onePage.pojo.dto.TrafficTrendCardInDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-29T17:59:57+0800",
    comments = "version: 1.4.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class OnePgCardMapperImpl implements OnePgCardMapper {

    @Override
    public TrafficTrendCardInDTO onePgCardInDTOToTrafficTrendCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        TrafficTrendCardInDTO trafficTrendCardInDTO = new TrafficTrendCardInDTO();

        trafficTrendCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        trafficTrendCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        trafficTrendCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        trafficTrendCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        trafficTrendCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        trafficTrendCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        trafficTrendCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        trafficTrendCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            trafficTrendCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            trafficTrendCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            trafficTrendCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            trafficTrendCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        trafficTrendCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        trafficTrendCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        trafficTrendCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        trafficTrendCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        trafficTrendCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        trafficTrendCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        trafficTrendCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            trafficTrendCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            trafficTrendCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return trafficTrendCardInDTO;
    }

    @Override
    public TargetCardInDTO onePgCardInDTOToTargetCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        TargetCardInDTO targetCardInDTO = new TargetCardInDTO();

        targetCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        targetCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        targetCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        targetCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        targetCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        targetCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        targetCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        targetCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            targetCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            targetCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            targetCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            targetCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        targetCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        targetCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        targetCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        targetCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        targetCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        targetCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        targetCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            targetCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            targetCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return targetCardInDTO;
    }

    @Override
    public CluesLevelCardInDTO onePgCardInDTOToCluesLevelCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        CluesLevelCardInDTO cluesLevelCardInDTO = new CluesLevelCardInDTO();

        cluesLevelCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        cluesLevelCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        cluesLevelCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        cluesLevelCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        cluesLevelCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        cluesLevelCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        cluesLevelCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        cluesLevelCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            cluesLevelCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            cluesLevelCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            cluesLevelCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            cluesLevelCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        cluesLevelCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        cluesLevelCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        cluesLevelCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        cluesLevelCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        cluesLevelCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        cluesLevelCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        cluesLevelCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            cluesLevelCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            cluesLevelCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return cluesLevelCardInDTO;
    }

    @Override
    public DrainageChannelCardInDTO onePgCardInDTOToDrainageChannelCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        DrainageChannelCardInDTO drainageChannelCardInDTO = new DrainageChannelCardInDTO();

        drainageChannelCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        drainageChannelCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        drainageChannelCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        drainageChannelCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        drainageChannelCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        drainageChannelCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        drainageChannelCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        drainageChannelCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            drainageChannelCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            drainageChannelCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            drainageChannelCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            drainageChannelCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        drainageChannelCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        drainageChannelCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        drainageChannelCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        drainageChannelCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        drainageChannelCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        drainageChannelCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        drainageChannelCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            drainageChannelCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            drainageChannelCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return drainageChannelCardInDTO;
    }

    @Override
    public TrafficCashCardInDTO onePgCardInDTOToTrafficCashCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        TrafficCashCardInDTO trafficCashCardInDTO = new TrafficCashCardInDTO();

        trafficCashCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        trafficCashCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        trafficCashCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        trafficCashCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        trafficCashCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        trafficCashCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        trafficCashCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        trafficCashCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            trafficCashCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            trafficCashCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            trafficCashCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            trafficCashCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        trafficCashCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        trafficCashCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        trafficCashCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        trafficCashCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        trafficCashCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        trafficCashCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        trafficCashCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            trafficCashCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            trafficCashCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return trafficCashCardInDTO;
    }

    @Override
    public ProductMixCardInDTO onePgCardInDTOToProductMixCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        ProductMixCardInDTO productMixCardInDTO = new ProductMixCardInDTO();

        productMixCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        productMixCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        productMixCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        productMixCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        productMixCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        productMixCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        productMixCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        productMixCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            productMixCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            productMixCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            productMixCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            productMixCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        productMixCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        productMixCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        productMixCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        productMixCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        productMixCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        productMixCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        productMixCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            productMixCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            productMixCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return productMixCardInDTO;
    }

    @Override
    public SceneCardInDTO onePgCardInDTOToSceneCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        SceneCardInDTO sceneCardInDTO = new SceneCardInDTO();

        sceneCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        sceneCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        sceneCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        sceneCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        sceneCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        sceneCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        sceneCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        sceneCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            sceneCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            sceneCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            sceneCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            sceneCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        sceneCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        sceneCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        sceneCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        sceneCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        sceneCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        sceneCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        sceneCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            sceneCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            sceneCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return sceneCardInDTO;
    }

    @Override
    public CluesFollowCardInDTO onePgCardInDTOToCluesFollowCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        CluesFollowCardInDTO cluesFollowCardInDTO = new CluesFollowCardInDTO();

        cluesFollowCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        cluesFollowCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        cluesFollowCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        cluesFollowCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        cluesFollowCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        cluesFollowCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        cluesFollowCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        cluesFollowCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            cluesFollowCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            cluesFollowCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            cluesFollowCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            cluesFollowCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        cluesFollowCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        cluesFollowCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        cluesFollowCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        cluesFollowCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        cluesFollowCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        cluesFollowCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        cluesFollowCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            cluesFollowCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            cluesFollowCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return cluesFollowCardInDTO;
    }

    @Override
    public OrderCardInDTO onePgCardInDTOToOrderCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        OrderCardInDTO orderCardInDTO = new OrderCardInDTO();

        orderCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        orderCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        orderCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        orderCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        orderCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        orderCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        orderCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        orderCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            orderCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            orderCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            orderCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            orderCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        orderCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        orderCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        orderCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        orderCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        orderCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        orderCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        orderCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            orderCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            orderCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return orderCardInDTO;
    }

    @Override
    public ComeDeviseCardInDTO onePgCardInDTOToComeDeviseCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        ComeDeviseCardInDTO comeDeviseCardInDTO = new ComeDeviseCardInDTO();

        comeDeviseCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        comeDeviseCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        comeDeviseCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        comeDeviseCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        comeDeviseCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        comeDeviseCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        comeDeviseCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        comeDeviseCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            comeDeviseCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            comeDeviseCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            comeDeviseCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            comeDeviseCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        comeDeviseCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        comeDeviseCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        comeDeviseCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        comeDeviseCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        comeDeviseCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        comeDeviseCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        comeDeviseCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            comeDeviseCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            comeDeviseCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return comeDeviseCardInDTO;
    }

    @Override
    public KeyCluesCardInDTO onePgCardInDTOToKeyCluesCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        KeyCluesCardInDTO keyCluesCardInDTO = new KeyCluesCardInDTO();

        keyCluesCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        keyCluesCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        keyCluesCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        keyCluesCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        keyCluesCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        keyCluesCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        keyCluesCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        keyCluesCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            keyCluesCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            keyCluesCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            keyCluesCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            keyCluesCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        keyCluesCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        keyCluesCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        keyCluesCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        keyCluesCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        keyCluesCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        keyCluesCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        keyCluesCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            keyCluesCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            keyCluesCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return keyCluesCardInDTO;
    }

    @Override
    public LostOrderCardInDTO onePgCardInDTOToLostOrderCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        LostOrderCardInDTO lostOrderCardInDTO = new LostOrderCardInDTO();

        lostOrderCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        lostOrderCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        lostOrderCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        lostOrderCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        lostOrderCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        lostOrderCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        lostOrderCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        lostOrderCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            lostOrderCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            lostOrderCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            lostOrderCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            lostOrderCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        lostOrderCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        lostOrderCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        lostOrderCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        lostOrderCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        lostOrderCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        lostOrderCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        lostOrderCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            lostOrderCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            lostOrderCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return lostOrderCardInDTO;
    }

    @Override
    public PersonEnterDivorceCardInDTO onePgCardInDTOToPersonEnterDivorceCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        PersonEnterDivorceCardInDTO personEnterDivorceCardInDTO = new PersonEnterDivorceCardInDTO();

        personEnterDivorceCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        personEnterDivorceCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        personEnterDivorceCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        personEnterDivorceCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        personEnterDivorceCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        personEnterDivorceCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        personEnterDivorceCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        personEnterDivorceCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            personEnterDivorceCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            personEnterDivorceCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            personEnterDivorceCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            personEnterDivorceCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        personEnterDivorceCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        personEnterDivorceCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        personEnterDivorceCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        personEnterDivorceCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        personEnterDivorceCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        personEnterDivorceCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        personEnterDivorceCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            personEnterDivorceCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            personEnterDivorceCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return personEnterDivorceCardInDTO;
    }

    @Override
    public PersonAbnormalCardInDTO onePgCardInDTOToPersonAbnormalCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        PersonAbnormalCardInDTO personAbnormalCardInDTO = new PersonAbnormalCardInDTO();

        personAbnormalCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        personAbnormalCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        personAbnormalCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        personAbnormalCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        personAbnormalCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        personAbnormalCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        personAbnormalCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        personAbnormalCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            personAbnormalCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            personAbnormalCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            personAbnormalCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            personAbnormalCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        personAbnormalCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        personAbnormalCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        personAbnormalCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        personAbnormalCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        personAbnormalCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        personAbnormalCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        personAbnormalCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            personAbnormalCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            personAbnormalCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return personAbnormalCardInDTO;
    }

    @Override
    public SalesmanRankCardInDTO onePgCardInDTOToSalesmanRankCardInDTO(OnePgCardInDTO onePgCardInDTO) {
        if ( onePgCardInDTO == null ) {
            return null;
        }

        SalesmanRankCardInDTO salesmanRankCardInDTO = new SalesmanRankCardInDTO();

        salesmanRankCardInDTO.setCardCode( onePgCardInDTO.getCardCode() );
        salesmanRankCardInDTO.setDateType( onePgCardInDTO.getDateType() );
        salesmanRankCardInDTO.setPrefixDateCode( onePgCardInDTO.getPrefixDateCode() );
        salesmanRankCardInDTO.setSuffixDateCode( onePgCardInDTO.getSuffixDateCode() );
        salesmanRankCardInDTO.setPrefixStartDateStr( onePgCardInDTO.getPrefixStartDateStr() );
        salesmanRankCardInDTO.setPrefixEndDateStr( onePgCardInDTO.getPrefixEndDateStr() );
        salesmanRankCardInDTO.setSuffixStartDateStr( onePgCardInDTO.getSuffixStartDateStr() );
        salesmanRankCardInDTO.setSuffixEndDateStr( onePgCardInDTO.getSuffixEndDateStr() );
        List<Long> list = onePgCardInDTO.getStoreOrgIdList();
        if ( list != null ) {
            salesmanRankCardInDTO.setStoreOrgIdList( new ArrayList<Long>( list ) );
        }
        List<String> list1 = onePgCardInDTO.getStoreOrgIdStrList();
        if ( list1 != null ) {
            salesmanRankCardInDTO.setStoreOrgIdStrList( new ArrayList<String>( list1 ) );
        }
        List<Long> list2 = onePgCardInDTO.getSalesmanIdList();
        if ( list2 != null ) {
            salesmanRankCardInDTO.setSalesmanIdList( new ArrayList<Long>( list2 ) );
        }
        List<String> list3 = onePgCardInDTO.getSalesmanIdStrList();
        if ( list3 != null ) {
            salesmanRankCardInDTO.setSalesmanIdStrList( new ArrayList<String>( list3 ) );
        }
        salesmanRankCardInDTO.setStoreKeyWordsStr( onePgCardInDTO.getStoreKeyWordsStr() );
        salesmanRankCardInDTO.setAuthType( onePgCardInDTO.getAuthType() );
        salesmanRankCardInDTO.setSelfSalesmanId( onePgCardInDTO.getSelfSalesmanId() );
        salesmanRankCardInDTO.setSelfSalesmanIdStr( onePgCardInDTO.getSelfSalesmanIdStr() );
        salesmanRankCardInDTO.setCurrentUserId( onePgCardInDTO.getCurrentUserId() );
        salesmanRankCardInDTO.setIzAllStoreAuthor( onePgCardInDTO.isIzAllStoreAuthor() );
        salesmanRankCardInDTO.setIzAllCompanyAuthor( onePgCardInDTO.isIzAllCompanyAuthor() );
        List<Long> list4 = onePgCardInDTO.getCompanyAuthorIdList();
        if ( list4 != null ) {
            salesmanRankCardInDTO.setCompanyAuthorIdList( new ArrayList<Long>( list4 ) );
        }
        List<String> list5 = onePgCardInDTO.getMenuUriList();
        if ( list5 != null ) {
            salesmanRankCardInDTO.setMenuUriList( new ArrayList<String>( list5 ) );
        }

        return salesmanRankCardInDTO;
    }
}
