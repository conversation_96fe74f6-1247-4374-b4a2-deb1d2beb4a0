package com.fotile.omscenter.dryProcess.pojo;

import com.fotile.omscenter.dryProcess.pojo.bo.DecOrderMatchBO;
import com.fotile.omscenter.dryProcess.pojo.dto.DecOrderMatchDTO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-07-01T15:31:30+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class DryProcessMapperImpl implements DryProcessMapper {

    @Override
    public DecOrderMatchBO DecOrderMatchDTOToDecOrderMatchBO(DecOrderMatchDTO dto) {
        if ( dto == null ) {
            return null;
        }

        DecOrderMatchBO decOrderMatchBO = new DecOrderMatchBO();

        decOrderMatchBO.setOrderId( dto.getOrderId() );
        decOrderMatchBO.setCluesId( dto.getCluesId() );
        decOrderMatchBO.setProductTotalNum( dto.getProductTotalNum() );
        decOrderMatchBO.setProductNum( dto.getProductNum() );
        decOrderMatchBO.setOrderTotalPrice( dto.getOrderTotalPrice() );
        decOrderMatchBO.setDeliveryTime( dto.getDeliveryTime() );
        decOrderMatchBO.setDrainageChannel( dto.getDrainageChannel() );
        decOrderMatchBO.setPurchaseReason( dto.getPurchaseReason() );
        decOrderMatchBO.setIsStoreDeal( dto.getIsStoreDeal() );
        decOrderMatchBO.setCreatedDate( dto.getCreatedDate() );

        return decOrderMatchBO;
    }
}
