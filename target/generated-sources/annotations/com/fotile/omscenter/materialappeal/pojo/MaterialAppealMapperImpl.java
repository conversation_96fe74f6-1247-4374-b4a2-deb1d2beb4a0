package com.fotile.omscenter.materialappeal.pojo;

import com.fotile.omscenter.materialappeal.pojo.dto.AppealInDTO;
import com.fotile.omscenter.materialappeal.pojo.dto.AppealPageInDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-07-01T15:31:30+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class MaterialAppealMapperImpl implements MaterialAppealMapper {

    @Override
    public AppealPageInDTO appealInDTOToAppealPageInDTO(AppealInDTO inDTO) {
        if ( inDTO == null ) {
            return null;
        }

        AppealPageInDTO appealPageInDTO = new AppealPageInDTO();

        appealPageInDTO.setAppealId( inDTO.getAppealId() );
        appealPageInDTO.setType( inDTO.getType() );
        appealPageInDTO.setTitle( inDTO.getTitle() );
        appealPageInDTO.setStatus( inDTO.getStatus() );
        appealPageInDTO.setMaterialCode( inDTO.getMaterialCode() );
        appealPageInDTO.setBeginSubmitTime( inDTO.getBeginSubmitTime() );
        appealPageInDTO.setEndSubmitTime( inDTO.getEndSubmitTime() );
        List<Long> list = inDTO.getIds();
        if ( list != null ) {
            appealPageInDTO.setIds( new ArrayList<Long>( list ) );
        }
        List<Long> list1 = inDTO.getCompanyAuthorIds();
        if ( list1 != null ) {
            appealPageInDTO.setCompanyAuthorIds( new ArrayList<Long>( list1 ) );
        }

        return appealPageInDTO;
    }
}
