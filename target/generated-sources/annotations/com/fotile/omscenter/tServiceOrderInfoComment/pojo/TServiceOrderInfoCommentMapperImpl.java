package com.fotile.omscenter.tServiceOrderInfoComment.pojo;

import com.fotile.omscenter.tServiceOrderInfoComment.pojo.dto.TServiceOrderInfoCommentInsertDTO;
import com.fotile.omscenter.tServiceOrderInfoComment.pojo.dto.TServiceOrderInfoCommentViewOutDto;
import com.fotile.omscenter.tServiceOrderInfoComment.pojo.entity.TServiceOrderInfoComment;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-07-01T15:31:30+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class TServiceOrderInfoCommentMapperImpl implements TServiceOrderInfoCommentMapper {

    @Override
    public TServiceOrderInfoComment TServiceOrderInfoCommentInsertDTOTOTServiceOrderInfoComment(TServiceOrderInfoCommentInsertDTO tServiceOrderInfoCommentInsertDTO) {
        if ( tServiceOrderInfoCommentInsertDTO == null ) {
            return null;
        }

        TServiceOrderInfoComment tServiceOrderInfoComment = new TServiceOrderInfoComment();

        tServiceOrderInfoComment.setRemark( tServiceOrderInfoCommentInsertDTO.getRemark() );
        tServiceOrderInfoComment.setCompanyId( tServiceOrderInfoCommentInsertDTO.getCompanyId() );
        tServiceOrderInfoComment.setCompanyName( tServiceOrderInfoCommentInsertDTO.getCompanyName() );
        tServiceOrderInfoComment.setCommunityStoreId( tServiceOrderInfoCommentInsertDTO.getCommunityStoreId() );
        tServiceOrderInfoComment.setCommunityStoreName( tServiceOrderInfoCommentInsertDTO.getCommunityStoreName() );
        tServiceOrderInfoComment.setServiceOrderId( tServiceOrderInfoCommentInsertDTO.getServiceOrderId() );
        tServiceOrderInfoComment.setTotalPoints( tServiceOrderInfoCommentInsertDTO.getTotalPoints() );
        tServiceOrderInfoComment.setAttitudePoints( tServiceOrderInfoCommentInsertDTO.getAttitudePoints() );
        tServiceOrderInfoComment.setProfessionalPonits( tServiceOrderInfoCommentInsertDTO.getProfessionalPonits() );
        tServiceOrderInfoComment.setTimeKeepingPonits( tServiceOrderInfoCommentInsertDTO.getTimeKeepingPonits() );
        tServiceOrderInfoComment.setOrderType( tServiceOrderInfoCommentInsertDTO.getOrderType() );
        tServiceOrderInfoComment.setAppraiseTime( tServiceOrderInfoCommentInsertDTO.getAppraiseTime() );
        tServiceOrderInfoComment.setCommunityMemberId( tServiceOrderInfoCommentInsertDTO.getCommunityMemberId() );
        tServiceOrderInfoComment.setCustomerId( tServiceOrderInfoCommentInsertDTO.getCustomerId() );
        tServiceOrderInfoComment.setCustomerNickname( tServiceOrderInfoCommentInsertDTO.getCustomerNickname() );
        tServiceOrderInfoComment.setCustomerPhone( tServiceOrderInfoCommentInsertDTO.getCustomerPhone() );
        tServiceOrderInfoComment.setStatus( tServiceOrderInfoCommentInsertDTO.getStatus() );
        tServiceOrderInfoComment.setRecommendFlag( tServiceOrderInfoCommentInsertDTO.getRecommendFlag() );
        tServiceOrderInfoComment.setAppraiseContent( tServiceOrderInfoCommentInsertDTO.getAppraiseContent() );
        tServiceOrderInfoComment.setHeadPortrait( tServiceOrderInfoCommentInsertDTO.getHeadPortrait() );
        tServiceOrderInfoComment.setDicValueCode( tServiceOrderInfoCommentInsertDTO.getDicValueCode() );
        tServiceOrderInfoComment.setDicValue( tServiceOrderInfoCommentInsertDTO.getDicValue() );

        return tServiceOrderInfoComment;
    }

    @Override
    public TServiceOrderInfoCommentViewOutDto TServiceOrderInfoCommentViewOutDtoTOTServiceOrderInfoComment(TServiceOrderInfoComment tServiceOrderInfoComment) {
        if ( tServiceOrderInfoComment == null ) {
            return null;
        }

        TServiceOrderInfoCommentViewOutDto tServiceOrderInfoCommentViewOutDto = new TServiceOrderInfoCommentViewOutDto();

        tServiceOrderInfoCommentViewOutDto.setRemark( tServiceOrderInfoComment.getRemark() );
        tServiceOrderInfoCommentViewOutDto.setCompanyId( tServiceOrderInfoComment.getCompanyId() );
        tServiceOrderInfoCommentViewOutDto.setCompanyName( tServiceOrderInfoComment.getCompanyName() );
        tServiceOrderInfoCommentViewOutDto.setCommunityStoreId( tServiceOrderInfoComment.getCommunityStoreId() );
        tServiceOrderInfoCommentViewOutDto.setCommunityStoreName( tServiceOrderInfoComment.getCommunityStoreName() );
        tServiceOrderInfoCommentViewOutDto.setServiceOrderId( tServiceOrderInfoComment.getServiceOrderId() );
        tServiceOrderInfoCommentViewOutDto.setTotalPoints( tServiceOrderInfoComment.getTotalPoints() );
        tServiceOrderInfoCommentViewOutDto.setAttitudePoints( tServiceOrderInfoComment.getAttitudePoints() );
        tServiceOrderInfoCommentViewOutDto.setProfessionalPonits( tServiceOrderInfoComment.getProfessionalPonits() );
        tServiceOrderInfoCommentViewOutDto.setTimeKeepingPonits( tServiceOrderInfoComment.getTimeKeepingPonits() );
        tServiceOrderInfoCommentViewOutDto.setOrderType( tServiceOrderInfoComment.getOrderType() );
        tServiceOrderInfoCommentViewOutDto.setAppraiseTime( tServiceOrderInfoComment.getAppraiseTime() );
        tServiceOrderInfoCommentViewOutDto.setCommunityMemberId( tServiceOrderInfoComment.getCommunityMemberId() );
        tServiceOrderInfoCommentViewOutDto.setCustomerId( tServiceOrderInfoComment.getCustomerId() );
        tServiceOrderInfoCommentViewOutDto.setCustomerNickname( tServiceOrderInfoComment.getCustomerNickname() );
        tServiceOrderInfoCommentViewOutDto.setCustomerPhone( tServiceOrderInfoComment.getCustomerPhone() );
        tServiceOrderInfoCommentViewOutDto.setStatus( tServiceOrderInfoComment.getStatus() );
        tServiceOrderInfoCommentViewOutDto.setRecommendFlag( tServiceOrderInfoComment.getRecommendFlag() );
        tServiceOrderInfoCommentViewOutDto.setAppraiseContent( tServiceOrderInfoComment.getAppraiseContent() );
        tServiceOrderInfoCommentViewOutDto.setId( tServiceOrderInfoComment.getId() );
        tServiceOrderInfoCommentViewOutDto.setIsDeleted( tServiceOrderInfoComment.getIsDeleted() );
        tServiceOrderInfoCommentViewOutDto.setHeadPortrait( tServiceOrderInfoComment.getHeadPortrait() );
        tServiceOrderInfoCommentViewOutDto.setDicValueCode( tServiceOrderInfoComment.getDicValueCode() );
        tServiceOrderInfoCommentViewOutDto.setDicValue( tServiceOrderInfoComment.getDicValue() );

        return tServiceOrderInfoCommentViewOutDto;
    }
}
