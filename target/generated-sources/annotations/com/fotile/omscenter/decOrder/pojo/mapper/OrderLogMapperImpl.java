package com.fotile.omscenter.decOrder.pojo.mapper;

import com.fotile.omscenter.decOrder.pojo.dto.export.DecOrderLogExportOutDraftDto;
import com.fotile.omscenter.decOrder.pojo.dto.export.DecOrderLogExportOutDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2022-07-01T15:31:29+0800",
    comments = "version: 1.3.1.Final, compiler: javac, environment: Java 11.0.14 (Oracle Corporation)"
)
@Component
public class OrderLogMapperImpl implements OrderLogMapper {

    @Override
    public List<DecOrderLogExportOutDraftDto> decOrderLogExportOutDraft(List<DecOrderLogExportOutDto> decOrderLogExportOutDtos) {
        if ( decOrderLogExportOutDtos == null ) {
            return null;
        }

        List<DecOrderLogExportOutDraftDto> list = new ArrayList<DecOrderLogExportOutDraftDto>( decOrderLogExportOutDtos.size() );
        for ( DecOrderLogExportOutDto decOrderLogExportOutDto : decOrderLogExportOutDtos ) {
            list.add( decOrderLogExportOutDtoToDecOrderLogExportOutDraftDto( decOrderLogExportOutDto ) );
        }

        return list;
    }

    protected DecOrderLogExportOutDraftDto decOrderLogExportOutDtoToDecOrderLogExportOutDraftDto(DecOrderLogExportOutDto decOrderLogExportOutDto) {
        if ( decOrderLogExportOutDto == null ) {
            return null;
        }

        DecOrderLogExportOutDraftDto decOrderLogExportOutDraftDto = new DecOrderLogExportOutDraftDto();

        decOrderLogExportOutDraftDto.setIndex( decOrderLogExportOutDto.getIndex() );
        decOrderLogExportOutDraftDto.setOrderType( decOrderLogExportOutDto.getOrderType() );
        decOrderLogExportOutDraftDto.setOrderId( decOrderLogExportOutDto.getOrderId() );
        decOrderLogExportOutDraftDto.setDrpOrderId( decOrderLogExportOutDto.getDrpOrderId() );
        decOrderLogExportOutDraftDto.setOperatorName( decOrderLogExportOutDto.getOperatorName() );
        decOrderLogExportOutDraftDto.setOperatorType( decOrderLogExportOutDto.getOperatorType() );
        decOrderLogExportOutDraftDto.setCreatedDate( decOrderLogExportOutDto.getCreatedDate() );
        decOrderLogExportOutDraftDto.setMsg( decOrderLogExportOutDto.getMsg() );

        return decOrderLogExportOutDraftDto;
    }
}
