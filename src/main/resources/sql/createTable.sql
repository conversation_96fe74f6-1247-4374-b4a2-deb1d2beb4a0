
DROP database IF EXISTS depcenter;
create database IF Not Exists depcenter Character Set UTF8;
use depcenter;

SET FOREIGN_KEY_CHECKS=0;


/**平台表***/
DROP TABLE IF EXISTS `dep_platform`;
create TABLE `dep_platform` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) DEFAULT NULL COMMENT '平台编码',
  `name` varchar(10) DEFAULT NULL COMMENT '平台名称',
  `host` varchar(200) DEFAULT NULL COMMENT '请求主机',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
    UNIQUE KEY `unique_code_index` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='平台表';
INSERT INTO dep_platform(code,name,host)
VALUES('MDM','主数据','https://hscom.efotile.com'),('CEM','CEM','http://*************:4101/cemapitest2'),
('FOTILE-DRP','方太DRP','http://web.fotile.com.cn/DRP_DIP_Test'),('GD-DRP','广东DRP','https://hscom.efotile.com'),
('MSP','营销中台','https://parallel-hstest.efotile.com'),('ECS','ECS','http://***************:8081'),
('FANGRUI','方瑞','http://fangrui.admin.ihalma.com:9999');

/****接口表***/
DROP TABLE IF EXISTS `dep_interface`;
CREATE TABLE `dep_interface` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '接口编码',
     `name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口名称',
     `platform_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台编码',
     `module_id` int(10) NULL DEFAULT NULL COMMENT '模块分类id:1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
     `module` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块分类名',
     `in_out` tinyint(3) NULL DEFAULT 1 COMMENT '出入站：1-入站，2-出站',
     `data_type` tinyint(3) NULL DEFAULT 1 COMMENT '格式类型：1-JSON，2-XML',
     `remarks` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
     `uri` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口uri地址',
     `http_method` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'POST' COMMENT '出站请求方式；POST；GET',
     `callback_url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口回调url地址',
     `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     `is_deleted` bigint(20) NOT NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE INDEX `unique_code_index`(`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='接口表';
/**MDM接口**/
/**客户**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MDM-IN-01','客户数据入站','MDM',1,1,'MDM推送客户数据',1,'主数据',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-01','客户数据出站','FANGRUI',2,1,'方瑞接收客户数据',1,'主数据','/datahub/asyncHubBase/customerIn');

/**门店**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MDM-IN-02','门店数据入站','MDM',1,1,'MDM推送门店数据',1,'主数据',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-02','门店数据出站','FANGRUI',2,1,'方瑞接收门店数据',1,'主数据','/datahub/asyncHubBase/storeIn');

/**物料**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MDM-IN-03','物料数据入站','MDM',1,1,'MDM推送物料数据',1,'主数据',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-03','物料数据出站','FANGRUI',2,1,'方瑞接收物料数据',1,'主数据','/datahub/asyncHubBase/productIn');

/**行政区域**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MDM-IN-04','行政区域数据入站','MDM',1,1,'MDM推送行政区域数据',1,'主数据',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-04','行政区域数据出站','FANGRUI',2,1,'方瑞接收行政区域数据',1,'主数据','/datahub/asyncHubBase/areaIn');

/**员工区域**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MDM-IN-05','员工数据入站','MDM',1,1,'MDM推送员工数据',1,'主数据',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-05','员工数据出站','FANGRUI',2,1,'方瑞接收员工数据',1,'主数据','/datahub/asyncHubBase/staffIn');

/****DRP***/
/**物流**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-IN-06','物流数据入站','FOTILE-DRP',1,1,'DRP推送物流数据',5,'物流模块',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-06','物流数据出站','FANGRUI',2,1,'方瑞接收物流数据',5,'物流模块','/datahub/asyncHubOrder/transIn');

/**发票**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-IN-07','发票数据入站','FOTILE-DRP',1,1,'DRP推送发票数据',6,'结算模块',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-07','发票数据出站','FANGRUI',2,1,'方瑞接收发票数据',6,'结算模块','/datahub/asyncHubFinance/invoiceIn');

/**商品**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-IN-08','商品核算价数据入站','FOTILE-DRP',1,1,'DRP推送商品核算价数据',6,'结算模块',null);
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-08','商品核算价数据出站','FANGRUI',2,1,'方瑞接收商品核算价数据',6,'结算模块','/datahub/asyncHubBase/verifypriceIn');

/**物流信息**/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-09','订单信息变更数据入站','FANGRUI',1,1,'方瑞推送订单信息变更数据',3,'订单模块',null);
/**备注：2021-09-17 德俊说drp和msp只需要订单审核，不需要订单物流**/
/*INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-OUT-09','订单信息变更数据出站','FOTILE-DRP',2,1,'DRP接收订单信息变更数据',3,'订单模块','/api/values/DIPtoGDDRP_OrderModified');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MSP-OUT-09','订单信息变更数据出站','MSP',2,1,'MSP接收订单信息变更数据',3,'订单模块',null);
*/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-OUT-09','订单信息变更数据出站','CEM',2,2,'CEM接收订单信息变更数据',3,'订单模块','/start.swe?SWEExtSource=SecureWebService&SWEExtCmd=Execute');

/****MSP****/
/***订单管理***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-10','订单管理数据入站至方瑞','MSP',1,1,'MSP推送订单数据',3,'订单模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-26','订单管理数据入站至DRP','MSP',1,1,'MSP推送订单数据',3,'订单模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-27','订单管理数据入站至CEM','MSP',1,1,'MSP推送订单数据',3,'订单模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-10','订单管理数据出站','FANGRUI',2,1,'方瑞接收订单数据',3,'订单模块','/datahub/asyncHubOrder/orderIn');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-OUT-10','订单管理数据出站','FOTILE-DRP',2,1,'DRP接收订单数据',3,'订单模块','/api/values/DIPtoGDDRP_OrderCreate');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-OUT-10','订单管理数据出站','CEM',2,2,'CEM接收订单数据',3,'订单模块','/start.swe?SWEExtSource=SecureWebService&SWEExtCmd=Execute');

/***开/闭店申请清单管理***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-11','开/闭店申请清单管理数据入站','MSP',1,1,'MSP推送开/闭店申请清单数据',4,'门店模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-11','开/闭店申请清单管理数据出站','FANGRUI',2,1,'方瑞接收开/闭店申请清单数据',4,'门店模块','/datahub/asyncHubOther/sapplyIn');

/***终端建设申请清单管理***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-12','终端建设申请清单管理数据入站','MSP',1,1,'MSP推送终端建设申请清单数据',4,'门店模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-12','终端建设申请清单管理数据出站','FANGRUI',2,1,'方瑞接收终端建设申请清单数据',4,'门店模块','/datahub/asyncHubOther/tapplyIn');

/***业务员数据同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-13','业务员数据入站','MSP',1,1,'MSP推送业务员数据',1,'主数据',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-13','业务员数据出站','FANGRUI',2,1,'方瑞接收业务员数据',1,'主数据','/datahub/asyncHubBase/salesmanIn');

/****引流渠道***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri,callback_url)
VALUES('MSP-IN-22','引流渠道数据入站','MSP',1,1,'MSP推送引流渠道数据',2,'线索模块',null,'https://parallel-hstest.efotile.com/org-center/api/msp/callback');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-22','引流渠道数据出站','FANGRUI',2,1,'方瑞接收引流渠道数据',2,'线索模块','/datahub/asyncHubBase/drainageChannelIn');

/****CEM****/
/***送货诉求信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-IN-14','送货诉求信息数据入站','CEM',1,1,'CEM推送送货诉求信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-14','送货诉求信息数据出站','FANGRUI',2,1,'方瑞接收送货诉求信息数据',7,'服务诉求','/datahub/asyncHubAppeal/deliveryIn');


/***报装诉求信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-IN-16','报装诉求信息数据入站','CEM',1,1,'CEM推送报装诉求信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-16','报装诉求信息数据出站','FANGRUI',2,1,'方瑞接收报装诉求信息数据',7,'服务诉求','/datahub/asyncHubAppeal/decorateIn');


/***报修诉求信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-IN-17','报修诉求信息数据入站','CEM',1,1,'CEM推送报修诉求信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-17','报修诉求信息数据出站','FANGRUI',2,1,'方瑞接收报修诉求信息数据',7,'服务诉求','/datahub/asyncHubAppeal/repairIn');


/***退货诉求信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-IN-18','退货诉求信息数据入站','CEM',1,1,'CEM推送退货诉求信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-18','退货诉求信息数据出站','FANGRUI',2,1,'方瑞接收退货诉求信息数据',7,'服务诉求','/datahub/asyncHubAppeal/returnGoodsIn');


/***换货诉求信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-IN-19','换货诉求信息数据入站','CEM',1,1,'CEM推送换货诉求信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-OUT-19','换货诉求信息数据出站','FANGRUI',2,1,'方瑞接收换货诉求信息数据',7,'服务诉求','/datahub/asyncHubAppeal/exchangeGoodsIn');


/*****方瑞****/
/***送货诉求状态信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-20','送货诉求状态信息数据入站','FANGRUI',1,1,'方瑞推送送货诉求状态信息数据',7,'服务诉求',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-OUT-20','送货诉求状态信息数据出站','CEM',2,2,'CEM接收送货诉求状态信息数据',7,'服务诉求','/start.swe?SWEExtSource=SecureWebService&SWEExtCmd=Execute');

/***物流单据(采购单)信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-21','物流单据(采购单)信息数据入站','FANGRUI',1,1,'方瑞推送物流单据(采购单)信息数据',5,'物流模块',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-OUT-21','物流单据(采购单)信息数据出站','FOTILE-DRP',2,1,'DRP接收物流单据(采购单)信息数据',5,'物流模块','/api/values/DIPtoDRP_Trans');

/***订单审核信息同步***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-23','订单审核数据入站','FANGRUI',1,1,'方瑞推送订单审核数据',3,'订单模块',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('DRP-OUT-23','订单审核数据出站','FOTILE-DRP',2,1,'DRP接收订单审核数据',3,'订单模块','/api/values/DIPtoGDDRP_OrderModified');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-OUT-23','订单审核数据出站','CEM',2,2,'CEM接收订单审核数据',3,'订单模块','/start.swe?SWEExtSource=SecureWebService&SWEExtCmd=Execute');

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('MSP-OUT-23','订单审核数据出站','MSP',2,1,'MSP接收订单审核数据',3,'订单模块','/oms-center/api/orderMsp/orderAudit');

/***物流信息回传(送货诉求)同步*
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-24','物流信息回传(送货诉求)数据入站','FANGRUI',1,1,'方瑞推送物流信息回传(送货诉求)数据',5,'物流模块',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('CEM-OUT-24','物流信息回传(送货诉求)数据出站','CEM',2,2,'CEM接收物流信息回传(送货诉求)数据',5,'物流模块',null);
**/

/***ECS费用申请相关***/
INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('FANGRUI-IN-25','ECS费用申请数据入站','FANGRUI',1,1,'方瑞推送ECS费用申请数据',6,'结算模块',null);

INSERT INTO dep_interface(code,name,platform_code,in_out,data_type,remarks,module_id,module,uri)
VALUES('ECS-OUT-25','ECS费用申请数据出站','ECS',2,2,'ECS接收费用申请数据',6,'结算模块','/ems/mstService');

UPDATE depcenter.dep_interface
SET modified_date=NOW();

/***队列***/
DROP TABLE IF EXISTS `dep_queue`;
CREATE TABLE `dep_queue` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(20) DEFAULT NULL COMMENT '队列编码',
  `name` varchar(30) DEFAULT NULL COMMENT '队列名称',
  `in_interface_code` varchar(20) NOT NULL COMMENT '入站接口编码',
  `out_interface_code` varchar(20) NOT NULL COMMENT '出站接口编码',
  `module_id` int(10) DEFAULT NULL COMMENT '模块分类id:1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
  `module` varchar(50) DEFAULT NULL COMMENT '模块分类名',
  `status` tinyint(3) DEFAULT 0 COMMENT '状态：0:启用;1:禁用',
  `last_log_date` datetime DEFAULT NULL COMMENT '最后一次运行时间',
  `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '修改人',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` bigint(20) NOT NULL DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_code_index` (`code`) USING BTREE,
  UNIQUE KEY `unique_incode_outcode` (`in_interface_code`,`out_interface_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='队列表';

/**MDM接口**/
/**客户**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q001','MDM推送客户数据至方瑞','MDM-IN-01','FANGRUI-OUT-01',1,'主数据');

/**门店**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q002','MDM推送门店数据至方瑞','MDM-IN-02','FANGRUI-OUT-02',1,'主数据');

/**物料**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q003','MDM推送物料数据至方瑞','MDM-IN-03','FANGRUI-OUT-03',1,'主数据');

/**行政区域**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q004','MDM推送行政区域数据至方瑞','MDM-IN-04','FANGRUI-OUT-04',1,'主数据');

/**员工**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q005','MDM推送员工数据至方瑞','MDM-IN-05','FANGRUI-OUT-05',1,'主数据');

/******DRP****/
/**物流**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q006','DRP推送物流数据至方瑞','DRP-IN-06','FANGRUI-OUT-06',5,'物流模块');

/**发票**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q007','DRP推送发票数据至方瑞','DRP-IN-07','FANGRUI-OUT-07',6,'结算模块');

/**商品核算价**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q008','DRP推送商品核算价数据至方瑞','DRP-IN-08','FANGRUI-OUT-08',6,'结算模块');



/*****MSP****/
/**订单管理**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q012','MSP推送订单数据至方瑞','MSP-IN-10','FANGRUI-OUT-10',3,'订单模块');

INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q013','MSP推送订单数据至DRP','MSP-IN-26','DRP-OUT-10',3,'订单模块');

INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q014','MSP推送订单数据至CEM','MSP-IN-27','CEM-OUT-10',3,'订单模块');

/**开/闭店申请清单管理**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q015','MSP推送开/闭店申请清单数据至方瑞','MSP-IN-11','FANGRUI-OUT-11',4,'门店模块');

/**终端建设申请清单管理**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q016','MSP推送终端建设申请清单数据至方瑞','MSP-IN-12','FANGRUI-OUT-12',4,'订单模块');

/**业务员数据同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q017','MSP推送业务员数据至方瑞','MSP-IN-13','FANGRUI-OUT-13',1,'主数据');

/**引流渠道数据同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q027','MSP推送引流渠道数据至方瑞','MSP-IN-22','FANGRUI-OUT-22',2,'线索模块');

/***CEM***/
/**送货诉求信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q018','CEM推送送货诉求信息数据至方瑞','CEM-IN-14','FANGRUI-OUT-14',7,'服务诉求');


/**报装诉求信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q020','CEM推送报装诉求信息数据至方瑞','CEM-IN-16','FANGRUI-OUT-16',7,'服务诉求');

/**报修诉求信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q021','CEM推送报修诉求信息数据至方瑞','CEM-IN-17','FANGRUI-OUT-17',7,'服务诉求');

/**退货诉求信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q022','CEM推送退货诉求信息数据至方瑞','CEM-IN-18','FANGRUI-OUT-18',7,'服务诉求');

/**换货诉求信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q023','CEM推送换货诉求信息数据至方瑞','CEM-IN-19','FANGRUI-OUT-19',7,'服务诉求');

/*****方瑞*****/
/**送货诉求状态信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q024','方瑞推送送货诉求状态信息数据至CEM','FANGRUI-IN-20','CEM-OUT-20',7,'服务诉求');

/**物流单据(采购单)入库信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q025','方瑞推送物流单据(采购单)入库信息数据至DRP','FANGRUI-IN-21','DRP-OUT-21',5,'物流模块');

/**物流单据(采购单)出库信息同步**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q026','方瑞推送物流单据(采购单)出库信息数据至DRP','FANGRUI-IN-22','DRP-OUT-22',5,'物流模块');

/**方瑞订单物流**/
/**备注：drp说只需要订单修改回传***/
/*INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q009','方瑞推送订单物流数据至DRP','FANGRUI-IN-09','DRP-OUT-09',3,'订单模块');
*/

/**备注：2021-09-17 德俊说drp和msp只需要订单审核，不需要订单物流***/
/*INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q010','方瑞推送订单物流数据至MSP','FANGRUI-IN-09','MSP-OUT-09',3,'订单模块');
*/

INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q011','方瑞推送订单物流数据至CEM','FANGRUI-IN-09','CEM-OUT-09',3,'订单模块');

/**方瑞订单审核**/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q028','方瑞推送订单审核数据至DRP','FANGRUI-IN-23','DRP-OUT-23',3,'订单模块');

INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q029','方瑞推送订单审核数据至MSP','FANGRUI-IN-23','MSP-OUT-23',3,'订单模块');

INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q030','方瑞推送订单审核数据至CEM','FANGRUI-IN-23','CEM-OUT-23',3,'订单模块');

/**物流信息回传(送货诉求)同步
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q031','方瑞推送物流信息回传(送货诉求)数据至CEM','FANGRUI-IN-24','CEM-OUT-24',5,'物流模块');
**/
/***ECS费用申请相关***/
INSERT INTO depcenter.dep_queue(code,name,in_interface_code,out_interface_code,module_id,module)
VALUES('Q032','方瑞推送费用申请数据至ECS','FANGRUI-IN-25','ECS-OUT-25',6,'结算模块');

/*****入站全量运行日志表****/
DROP TABLE IF EXISTS `dep_in_log`;
CREATE TABLE `dep_in_log`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `in_interface_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '入站接口编码',
   `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批次号',
   `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID：业务系统每次分发产生新的分发ID',
   `is_success` tinyint(1) NULL DEFAULT NULL COMMENT '是否通过校验：0-通过 1-未通过',
   `check_msg` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基础校验信息',
   `call_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求报文',
   `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人',
   `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人',
   `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   `is_deleted` bigint(20) NOT NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
   PRIMARY KEY (`id`) USING BTREE,
   INDEX `idx_dep_in_log_in_interface_code`(`in_interface_code`) USING BTREE,
   INDEX `idx_dep_in_log_batch_no`(`batch_no`) USING BTREE,
   INDEX `idx_dep_in_log_distribute_no`(`distribute_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '入站全量运行日志表' ROW_FORMAT = COMPACT;

/*****日志表****/
DROP TABLE IF EXISTS `dep_log`;
CREATE TABLE `dep_log`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `queue_id` bigint(20) NULL DEFAULT NULL COMMENT '队列定义表主键ID（外键）',
    `queue_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段，可以用process_id关联到',
    `queue_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '冗余字段，可以用process_id关联到',
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID：业务系统每次分发产生新的分发ID',
    `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `is_deleted` bigint(20) NOT NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_dep_log_queue_code`(`queue_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运行日志表' ROW_FORMAT = COMPACT;

alter table depcenter.dep_log add index idx_dep_log_batch_no (batch_no);
alter table depcenter.dep_log add index idx_dep_log_distribute_no (distribute_no);
alter table depcenter.dep_log add index idx_dep_log_created_date (created_date);



/***日志明细表***/
DROP TABLE IF EXISTS `dep_log_detail`;
CREATE TABLE `dep_log_detail`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `log_id` bigint(20) NULL DEFAULT NULL COMMENT '关联dep_process_log表的id',
   `status` tinyint(3) NULL DEFAULT NULL COMMENT '状态：1-完成，2-错误',
   `stage` tinyint(3) NULL DEFAULT NULL COMMENT '作业阶段：1-入站，2-转换，3-出站，4-出站回调',
   `remarks` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
   `call_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '完整URL',
   `call_http_method` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调用url请求类型：GET，POST',
   `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
   `retry` tinyint(1) NULL DEFAULT NULL COMMENT '是否是重试：0：否，1：是（入站的重试），2：是（转换或出站的重试）',
   `retry_num` tinyint(1) NULL DEFAULT NULL COMMENT '第几次重试',
   `whole_false_flag` tinyint(1) NULL DEFAULT NULL COMMENT '针对出站批量发送数据（如CEM），是否整体错误标识：1-错误',
   `callback_whole_false_flag` tinyint(1) NULL DEFAULT NULL COMMENT '针对出站回调批量发送数据（如CEM），是否整体错误标识：1-错误',
   `biz_data_flag` tinyint(1) DEFAULT NULL COMMENT '业务数据是否成功插入： 1-成功 2-失败',
   `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人',
   `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人',
   `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   `is_deleted` bigint(20) NOT NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
   PRIMARY KEY (`id`) USING BTREE,
   INDEX `idx_dep_log_detail_log_id`(`log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运行日志详情表' ROW_FORMAT = COMPACT;

alter table depcenter.dep_log_detail add index idx_dep_log_detail_created_date (created_date);

/***日志明细表-报文/错误信息***/
DROP TABLE IF EXISTS `dep_log_detail_body`;
CREATE TABLE `dep_log_detail_body`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `log_detail_id` bigint(20) NULL DEFAULT NULL COMMENT '关联dep_log_detail表的id',
    `call_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求报文',
    `response_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应报文',
    `error_msg` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误信息',
    `process_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '过程信息，用于重试',
    `retry_class` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重试的类名',
    `retry_method` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重试的方法',
    `created_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '创建人',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '修改人',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `is_deleted` bigint(20) NOT NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `idx_unique_dep_log_detail_body_log_detail_id`(`log_detail_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '日志明细表-报文/错误信息' ROW_FORMAT = COMPACT;

alter table depcenter.dep_log_detail_body add index idx_dep_log_detail_body_created_date (created_date);
alter table depcenter.dep_log_detail_body add index idx_dep_log_detail_body_log_detail_id(log_detail_id);


DROP TABLE IF EXISTS `dep_queue_module`;
CREATE TABLE `dep_queue_module` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` int(10) DEFAULT NULL COMMENT '模块编码：1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
  `name` varchar(50) DEFAULT NULL COMMENT '模块名称：1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_code_index` (`code`) USING BTREE,
  UNIQUE KEY `unique_name_index` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='队列模块表';

-- ----------------------------
-- Records of dep_queue_module
-- ----------------------------
INSERT INTO `dep_queue_module` VALUES ('1', '1', '主数据', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('2', '2', '线索模块', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('3', '3', '订单模块', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('4', '4', '门店模块', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('5', '5', '物流模块', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('6', '6', '结算模块', '0', null, null, null, null);
INSERT INTO `dep_queue_module` VALUES ('7', '7', '服务诉求', '0', null, null, null, null);

DROP TABLE IF EXISTS `dep_interface_module`;
CREATE TABLE `dep_interface_module` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` int(10) DEFAULT NULL COMMENT '模块编码：1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
  `name` varchar(50) DEFAULT NULL COMMENT '模块名称：1：主数据；2：线索；3：订单模块；4：门店模块；5：物流模块；6：结算模块；7：服务诉求',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_code_index` (`code`) USING BTREE,
  UNIQUE KEY `unique_name_index` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='接口模块表';

-- ----------------------------
-- Records of dep_interface_module
-- ----------------------------
INSERT INTO `dep_interface_module` VALUES ('1', '1', '主数据', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('2', '2', '线索模块', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('3', '3', '订单模块', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('4', '4', '门店模块', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('5', '5', '物流模块', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('6', '6', '结算模块', '0', null, null, null, null);
INSERT INTO `dep_interface_module` VALUES ('7', '7', '服务诉求', '0', null, null, null, null);

/*********MDM相关*****/
/**客户入表***/
DROP TABLE IF EXISTS `mdm_in_customer`;
CREATE TABLE `mdm_in_customer`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `KUNNR` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP客户编码',
    `NAME1` varchar(35) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP客户名称',
    `SORTL` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP客户简称',
    `ZZSTCD51` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户税号',
    `ZZSF1` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省',
    `ZZCS1` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '市',
    `ZZQX1` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区',
    `STRAS` varchar(35) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '注册地址',
    `TELF1` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话',
    `TELFX` varchar(31) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '传真',
    `PSTLZ` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮政编码',
    `BANKA` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开户行,客户开户行名称',
    `BANKN` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账号,客户开户行账号',
    `BANKS` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行国家',
    `BANKL` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '银行代码',
    `XEZER` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '托收权限',
    `BKREF` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参考',
    `KOINH` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '帐户持有人',
    `BZIRK` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP销售区域',
    `VKBUR` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP销售部门',
    `VTWEG` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP分销渠道',
    `ZYWLX` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'SAP业务类型',
    `ZKHH` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'DRP客户编码',
    `ZKHMC` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'DRP客户名称',
    `ZFXQD` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分销渠道',
    `ZXSMX` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '销售模式',
    `ZKHZT` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户状态',
    `ZMRCK` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '默认仓库',
    `ZSFZGJ` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否专供机',
    `ZSFTYJS` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '统一结算',
    `ZSFTSKH` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否特殊客户',
    `ZJSFS` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '计算方式',
    `ZKHKD` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户扣点',
    `ZSSDBC` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '办事处/片区',
    `ZKFXM` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人姓名',
    `ZKFZJ` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人证件',
    `ZKFDH` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人电话',
    `ZQDDL` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道大类',
    `VTXT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道大类描述，V1.0(7)',
    `ZQDXL` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道明细',
    `DTXT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道明细描述，V1.0(7)',
    `ZSF` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份',
    `ZSFN` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份名称',
    `ZCS` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市',
    `ZCSN` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市名称',
    `ZQX` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区县',
    `ZQXN` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区县名称',
    `ZCUSNO` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属客户',
    `ZGCCS` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工程城市',
    `ZYHLX` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'DRP银行类型 10退款行，20收款行',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MDM客户入表' ROW_FORMAT = DYNAMIC;




/**门店入表***/
DROP TABLE IF EXISTS `mdm_in_store`;
CREATE TABLE `mdm_in_store`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `ZDMDBM` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店编码',
     `ZDCS` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市',
     `ZECS_TXT` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市 文本',
     `ZDFZR` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人',
     `ZEJD` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经度',
     `ZELXFS` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系方式',
     `ZDMDCQ` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店产权',
     `ZDMDMC` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店名称',
     `ZDMDQD` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店渠道',
     `MTXT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店渠道描述，V1.0(7)',
     `ZDMDQDXF` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店渠道细分',
     `XTXT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店渠道细分描述，V1.0(7)',
     `ZDMDXZ` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店性质',
     `ZDMDYWLX` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店业务类型',
     `ZDMDZJDH` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店座机电话',
     `ZDMDZT` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店状态',
     `ZDMDZXJD` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门店装修进度',
     `ZDQX` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区县',
     `ZEQX_TXT` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '区县 文本',
     `ZDSF` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份',
     `ZDSFXNMD` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否虚拟门店',
     `ZESF_TXT` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份 文本',
     `ZDSSKH` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属客户',
     `ZEWD` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纬度',
     `ZEXXDZ` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '详细地址',
     `ZEYX` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
     `ZEZYNX` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '租用年限',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；其他：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MDM门店入表' ROW_FORMAT = DYNAMIC;



/**MDM产品&配件入表***/
DROP TABLE IF EXISTS `mdm_in_product`;
CREATE TABLE `mdm_in_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `MATNR` varchar(18) DEFAULT NULL COMMENT '物料',
  `ZECNMS` varchar(40) DEFAULT NULL COMMENT '描述',
  `LVORM` varchar(1) DEFAULT NULL COMMENT '集团级的DF',
  `BISMT` varchar(18) DEFAULT NULL COMMENT '旧物料号',
  `BRGEW` varchar(13) DEFAULT NULL COMMENT '毛重',
  `NTGEW` varchar(13) DEFAULT NULL COMMENT '净重',
  `GEWEI` varchar(3) DEFAULT NULL COMMENT '重量单位',
  `VOLUM` varchar(13) DEFAULT NULL COMMENT '业务量',
  `VOLEH` varchar(3) DEFAULT NULL COMMENT '体积单位',
  `SPART` varchar(20) DEFAULT NULL COMMENT '产品组',
  `LAENG` varchar(13) DEFAULT NULL COMMENT '长度',
  `BREIT` varchar(13) DEFAULT NULL COMMENT '宽度',
  `HOEHE` varchar(13) DEFAULT NULL COMMENT '高度',
  `ZFWPJ` varchar(1) DEFAULT NULL COMMENT '是否服务配件',
  `ZCPXT` varchar(50) DEFAULT NULL COMMENT '产品品类描述',
  `ZCPXT_NE` varchar(40) DEFAULT NULL COMMENT '产品线描述',
  `ZCPXLT` varchar(40) DEFAULT NULL COMMENT '产品小类描述',
  `ZJHXH` varchar(50) DEFAULT NULL COMMENT '简化型号',
  `ZZCPZQZT` varchar(12) DEFAULT NULL COMMENT '产品生命周期状态',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；其他：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MDM产品&配件入表';




/**MDM行政区域入表***/
DROP TABLE IF EXISTS `mdm_in_area`;
CREATE TABLE `mdm_in_area` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `ZPROVINCE` varchar(2) DEFAULT NULL COMMENT '省',
  `ZCITY` varchar(4) DEFAULT NULL COMMENT '市',
  `ZREGION` varchar(6) DEFAULT NULL COMMENT '区县',
  `ZPROVINCE_T` varchar(50) DEFAULT NULL COMMENT '省描述',
  `ZCITY_T` varchar(50) DEFAULT NULL COMMENT '市描述',
  `ZREGION_T` varchar(50) DEFAULT NULL COMMENT '区县描述',
  `ZDELFLAG` varchar(1) DEFAULT NULL COMMENT '删除标识',
  `EFDAT` date DEFAULT NULL COMMENT '省市区生效日期',
  `ENDAT` date DEFAULT NULL COMMENT '省市区失效日期',
  `ZPRERG` varchar(6) DEFAULT NULL COMMENT '前一版本区县值',
  `ZSSBSC` varchar(4) DEFAULT NULL COMMENT '所属办事处',
  `ZSCDJ` varchar(50) DEFAULT NULL COMMENT '市场等级',
  `ZSSDBC` varchar(4) DEFAULT NULL COMMENT '办事处/片区',
  `ZSSDBCMS` varchar(20) DEFAULT NULL COMMENT '办事处',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；其他：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MDM行政区域入表';



/**MDM员工入表***/
DROP TABLE IF EXISTS `mdm_in_staff`;
create TABLE `mdm_in_staff` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `ZHRP001` varchar(10) DEFAULT NULL COMMENT '员工工号',
  `ZHRP002` varchar(30) DEFAULT NULL COMMENT '姓名',
  `ZHRP033` varchar(24) DEFAULT NULL COMMENT '手机号',
  `ZHRP035` varchar(70) DEFAULT NULL COMMENT '公司邮箱',
  `ZHRP056` varchar(1) DEFAULT NULL COMMENT '员工当前状态',
  `ZHRP057` varchar(30) DEFAULT NULL COMMENT '员工当前状态描述',
  `ZHRP063` varchar(6) DEFAULT NULL COMMENT '组织ID',
  `ZHRP064` varchar(30) DEFAULT NULL COMMENT '组织名称',
  `ZHRP067` varchar(6) DEFAULT NULL COMMENT '职位ID',
  `ZHRP068` varchar(30) DEFAULT NULL COMMENT '职位描述',
  `ZHRP083` date DEFAULT NULL COMMENT '入职日期',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；其他：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MDM员工入表';




/*********DRP相关*****/
/**DRP物流单入表***/
DROP TABLE IF EXISTS `drp_in_trans`;
create TABLE `drp_in_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `transguid` varchar(36) DEFAULT NULL COMMENT '物流单guid',
  `transno` varchar(50) DEFAULT NULL COMMENT '物流单NO',
  `transtype` tinyint(4) DEFAULT NULL COMMENT '物流类型',
  `branchname` varchar(10) DEFAULT NULL COMMENT '办事处名称',
  `fromwarehouseno` varchar(12) DEFAULT NULL COMMENT '出库仓NO',
  `fromlocationtype` int(11) DEFAULT NULL COMMENT '出库储位',
  `towarehouseno` varchar(12) DEFAULT NULL COMMENT '入库仓NO',
  `towarehousename` int(11) DEFAULT NULL COMMENT '入库储位',
  `approvedon` datetime DEFAULT NULL COMMENT '审核时间',
  `approvedname` varchar(50) DEFAULT NULL COMMENT '审核人',
  `confirmedfromon` datetime DEFAULT NULL COMMENT '出库时间',
  `confirmedfromname` varchar(50) DEFAULT NULL COMMENT '出库人',
  `confirmedtoon` datetime DEFAULT NULL COMMENT '入库时间',
  `confirmedtoname` varchar(50) DEFAULT NULL COMMENT '入库人',
  `totalnum` int(11) DEFAULT NULL COMMENT '总数量',
  `demo` varchar(1000) DEFAULT NULL COMMENT '备注',
  `state` tinyint(4) DEFAULT NULL COMMENT '状态',
  `status` tinyint(4) DEFAULT NULL COMMENT '物流状态',
  `createdon` datetime DEFAULT NULL COMMENT '创建时间',
  `createdname` varchar(50) DEFAULT NULL COMMENT '创建人',
  `modifiedon` datetime DEFAULT NULL COMMENT '修改时间',
  `modifiedname` varchar(50) DEFAULT NULL COMMENT '修改人',
  `sendinfo` varchar(200) DEFAULT NULL COMMENT '地址信息',
  `needdate` datetime DEFAULT NULL COMMENT '提货时间',
  `submitstatus` int(11) DEFAULT NULL COMMENT '提交状态',
  `companyid` varchar(36) DEFAULT NULL COMMENT '物流公司ID',
  `caompanyname` varchar(50) DEFAULT NULL COMMENT '物流公司名称',
  `projectName` varchar(150) DEFAULT NULL COMMENT '项目名称',
  `pzzs` numeric(18,4) DEFAULT NULL COMMENT '配载总数',
  `transtype2` tinyint(4) DEFAULT NULL COMMENT '退货类型',
  `parentNo` varchar(50) DEFAULT NULL COMMENT '原始单号',
  `refundDate` datetime DEFAULT NULL COMMENT '退货发生日期',
  `projectCode` varchar(100) DEFAULT NULL COMMENT '项目编号',
  `contractsCode` varchar(300) DEFAULT NULL COMMENT '合同编号',
  `WarehouseStatus` tinyint(4) DEFAULT NULL COMMENT '确认状态',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP物流单入表';

/**DRP物流单行入表***/
DROP TABLE IF EXISTS `drp_in_transline`;
create TABLE `drp_in_transline` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `transguid` varchar(36) DEFAULT NULL COMMENT '物流单guid',
  `translineguid` varchar(36) DEFAULT NULL COMMENT '物流单行guid',
  `itemno` varchar(30) DEFAULT NULL COMMENT '商品NO',
  `itemnamefull` varchar(160) DEFAULT NULL COMMENT '商品名称',
  `planNum` int(11) DEFAULT NULL COMMENT '计划数量/要货数量',
  `num` int(11) DEFAULT NULL COMMENT '数量/发货数量',
  `demo` varchar(1000) DEFAULT NULL COMMENT '备注',
  `state` tinyint(4) DEFAULT NULL COMMENT '状态',
  `pzxs` numeric(10,3) DEFAULT NULL COMMENT '配载系数',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP物流单行入表';

/**DRP发票入表***/
DROP TABLE IF EXISTS `drp_in_invoice`;
create TABLE `drp_in_invoice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `invoiceguid` varchar(36) DEFAULT NULL COMMENT '发票GUID',
  `invoiceno` varchar(50) DEFAULT NULL COMMENT '发票NO',
  `virtualinvoiceno` varchar(50) DEFAULT NULL COMMENT '虚拟发票NO',
  `amounttax` decimal(18,2) DEFAULT NULL COMMENT '税额',
  `amount` decimal(18,2) DEFAULT NULL COMMENT '金额',
  `amountdiscount` decimal(18,2) DEFAULT NULL COMMENT '折扣',
  `demo` varchar(200) DEFAULT NULL COMMENT '备注',
  `invoicetype` tinyint(4) DEFAULT NULL COMMENT '类别',
  `attribution` tinyint(4) DEFAULT NULL COMMENT '归属',
  `ratetax` decimal(18,2) DEFAULT NULL COMMENT '税率',
  `approvedate` datetime DEFAULT NULL COMMENT '开票日期',
  `mdmid` varchar(50) DEFAULT NULL COMMENT '客户ID',
  `status` tinyint(4) DEFAULT NULL COMMENT '发票状态',
  `modifiedon` datetime DEFAULT NULL COMMENT '修改时间',
  `state` tinyint(4) DEFAULT NULL COMMENT '状态 （逻辑删除）',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP发票入表';


/**DRP发票行入表***/
DROP TABLE IF EXISTS `drp_in_invoice_line`;
create TABLE `drp_in_invoice_line` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `invoiceguid` varchar(36) DEFAULT NULL COMMENT '发票GUID',
  `lineid` int(11) DEFAULT NULL COMMENT '行ID',
  `dimproduct` varchar(10) DEFAULT NULL COMMENT '产品类别',
  `packageName` varchar(50) DEFAULT NULL COMMENT '套餐',
  `itemno` varchar(50) DEFAULT NULL COMMENT '商品NO',
  `itemnamefull` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `numsettle` decimal(18,6) DEFAULT NULL COMMENT '数量',
  `pricecost` decimal(18,2) DEFAULT NULL COMMENT '核算价',
  `pricesettle` decimal(18,6) DEFAULT NULL COMMENT '结算价',
  `pricecostAdjust` decimal(18,2) DEFAULT NULL COMMENT '核算价调整',
  `Zcprice` decimal(18,6) DEFAULT NULL COMMENT '政策基准价',
  `amount` decimal(18,2) DEFAULT NULL COMMENT '金额',
  `amounttax` decimal(18,2) DEFAULT NULL COMMENT '税额',
  `Hsamount` decimal(18,2) DEFAULT NULL COMMENT '含税金额',
  `amountProfit` decimal(18,2) DEFAULT NULL COMMENT '超价',
  `amountprofitadjust` decimal(18,4) DEFAULT NULL COMMENT '超价调整',
  `demo` varchar(50) DEFAULT NULL COMMENT '备注',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP发票行入表';

/**DRP折扣明细入表***/
DROP TABLE IF EXISTS `drp_in_invoice_discount`;
create TABLE `drp_in_invoice_discount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `discountId` int(11) NOT NULL COMMENT '唯一ID',
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `invoiceguid` varchar(36) DEFAULT NULL COMMENT '发票GUID',
  `lx` int(11) DEFAULT NULL COMMENT '区分',
  `OaapplyNo` varchar(100) DEFAULT NULL COMMENT 'OA申请单号',
  `Hh` varchar(100) DEFAULT NULL COMMENT '行号',
  `FeeName` varchar(100) DEFAULT NULL COMMENT '费用类别',
  `amount` decimal(18,2) DEFAULT NULL COMMENT '金额',
  `Approvalje` decimal(18,2) DEFAULT NULL COMMENT 'OA审批金额（可用）',
  `demo` varchar(2048) DEFAULT NULL COMMENT '备注',
  `FeeType` tinyint(4) DEFAULT NULL COMMENT '报告审批类别',
  `Budgetbm` varchar(100) DEFAULT NULL COMMENT '预算占用部门',
  `BudgetItem` varchar(100) DEFAULT NULL COMMENT '预算项',
  `Hguid` varchar(36) DEFAULT NULL COMMENT '行GUID',
  `type` tinyint(4) DEFAULT NULL COMMENT '费用归属',
  `Sqdate` datetime DEFAULT NULL COMMENT 'OA审批日期',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP折扣明细入表';


/**DRP商品核算价入表***/
DROP TABLE IF EXISTS `drp_in_verifyPrice`;
create TABLE `drp_in_verifyPrice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `itemguid` varchar(36) DEFAULT NULL COMMENT '商品guid',
  `itemno` varchar(30) DEFAULT NULL COMMENT '商品NO',
  `itemnamefull` varchar(160) DEFAULT NULL COMMENT '商品名称',
  `pricecost` decimal(18,2) DEFAULT NULL COMMENT '核算价',
  `state` tinyint(4) DEFAULT NULL COMMENT '状态',
  `channelType` tinyint(4) DEFAULT NULL COMMENT '渠道',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='DRP商品核算价入表';



/***********CEM相关*******/
/***CEM送货诉求-诉求信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_appeal`;
create TABLE `cem_in_delivery_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) DEFAULT NULL COMMENT '诉求ID',
  `FollowOrganization` varchar(100) DEFAULT NULL COMMENT '跟进组织',
  `ContactId` varchar(30) DEFAULT NULL COMMENT '联系人ID',
  `SRNumber` varchar(64) DEFAULT NULL COMMENT '诉求单编号',
  `SRType` varchar(30) DEFAULT NULL COMMENT '诉求类型',
  `Source` varchar(30) DEFAULT NULL COMMENT '诉求来源',
  `Status` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `FTCallerName` varchar(50) DEFAULT NULL COMMENT '呼入人',
  `FTCallerPhone` varchar(30) DEFAULT NULL COMMENT '呼入号码',
  `Severity` varchar(30) DEFAULT NULL COMMENT '紧急程度',
  `FTUrgentSRProcessStatus` varchar(50) DEFAULT NULL COMMENT '紧急诉求处理状态',
  `FTEmergencyHander` varchar(30) DEFAULT NULL COMMENT '紧急诉求处理人',
  `FTExpectTime` datetime DEFAULT NULL COMMENT '客户期望时间',
  `FTFirstAppointmentTime` datetime DEFAULT NULL COMMENT '首次预约时间',
  `FTLastAppointmentTime` datetime DEFAULT NULL COMMENT '最后预约时间',
  `FTSRCancelReason` varchar(50) DEFAULT NULL COMMENT '取消原因',
  `FTReminderType` varchar(30) DEFAULT NULL COMMENT '催单类型',
  `FTCollectionContact` varchar(30) DEFAULT NULL COMMENT '催单联系人',
  `FTCollectionResult` varchar(30) DEFAULT NULL COMMENT '催单处理结果',
  `FTCollectionNum` varchar(30) DEFAULT NULL COMMENT '催单号码',
  `Description` varchar(2000) DEFAULT NULL COMMENT '详细描述',
  `FTIFDeliveryInstall` varchar(1) DEFAULT NULL COMMENT '是否送安同天',
  `FTRelatedSRNumber` varchar(64) DEFAULT NULL COMMENT '关联单号',
  `FTReminderCount` varchar(30) DEFAULT NULL COMMENT '催单次数',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `FTProvince` varchar(30) DEFAULT NULL COMMENT '省',
  `FTCity` varchar(30) DEFAULT NULL COMMENT '市',
  `DistrictCode` varchar(30) DEFAULT NULL COMMENT '区县',
  `FTAddr` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-诉求信息入表';

/***CEM送货诉求-用户信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_user`;
create TABLE `cem_in_delivery_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `user_id` varchar(30) NOT NULL COMMENT '联系人ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求ID',
  `LastName` varchar(50) DEFAULT NULL COMMENT '姓名',
  `CellularPhone` varchar(40) DEFAULT NULL COMMENT '手机号码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB   AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-用户信息入表';

/***CEM送货诉求-产品信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_product`;
create TABLE `cem_in_delivery_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求ID',
  `FTOrderItemId` varchar(50) DEFAULT NULL COMMENT '订单行号',
  `FTProductCategory` varchar(30) DEFAULT NULL COMMENT '产品组',
  `FTProductLine` varchar(30) DEFAULT NULL COMMENT '产品系列',
  `FTProductModel` varchar(50) DEFAULT NULL COMMENT '产品简化型号',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-产品信息入表';



/***CEM送货诉求-跟进信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_follow`;
create TABLE `cem_in_delivery_follow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `follow_id` varchar(30) DEFAULT NULL COMMENT '跟进记录Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求ID',
  `FollowContent` varchar(2000) DEFAULT NULL COMMENT '跟进内容',
  `Type` varchar(30) DEFAULT NULL COMMENT '跟进类型',
  `Channel` varchar(30) DEFAULT NULL COMMENT '跟进渠道',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `FTCreatdBy` varchar(50) DEFAULT NULL COMMENT '创建人',
  `FollowNumber` varchar(50) DEFAULT NULL COMMENT '跟进编码',
  `FollowPerson` varchar(30) DEFAULT NULL COMMENT '跟进人',
  `Status` varchar(30) DEFAULT NULL COMMENT '跟进状态',
  `Updated` datetime DEFAULT NULL COMMENT '更新时间',
  `FTUpdateBy` varchar(50) DEFAULT NULL COMMENT '更新人',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-跟进信息入表';

ALTER TABLE depcenter.cem_in_delivery_follow MODIFY `FollowContent` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '跟进内容';
ALTER TABLE depcenter.`cem_in_delivery_follow` modify `FollowContent` varchar(4000) character set utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跟进内容';

/***CEM送货诉求-联系人信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_contact`;
create TABLE `cem_in_delivery_contact` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求ID',
  `FTContact` varchar(50) DEFAULT NULL COMMENT '姓名',
  `FTContactNum` varchar(50) DEFAULT NULL COMMENT '电话',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-联系人信息入表';



/***CEM送货诉求-Voc信息入表***/
DROP TABLE IF EXISTS `cem_in_delivery_voc`;
create TABLE `cem_in_delivery_voc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求ID',
  `FTVOC1` varchar(30) DEFAULT NULL COMMENT 'VOC一级分类',
  `FTVOC2` varchar(30) DEFAULT NULL COMMENT 'VOC二级分类',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM送货诉求-Voc信息入表';


/***CEM报装信息-诉求信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_appeal`;
create TABLE `cem_in_decorate_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) DEFAULT NULL COMMENT '诉求ID',
  `FollowOrganization` varchar(100) DEFAULT NULL COMMENT '服务中心',
  `ContactId` varchar(30) DEFAULT NULL COMMENT '联系人ID',
  `SRNumber` varchar(64) DEFAULT NULL COMMENT '诉求单编号',
  `SRType` varchar(30) DEFAULT NULL COMMENT '诉求类型',
  `Source` varchar(30) DEFAULT NULL COMMENT '诉求来源',
  `Status` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `FTCallerName` varchar(50) DEFAULT NULL COMMENT '呼入人',
  `FTCallerPhone` varchar(30) DEFAULT NULL COMMENT '呼入号码',
  `Severity` varchar(30) DEFAULT NULL COMMENT '紧急程度',
  `FTUrgentSRProcessStatus` varchar(50) DEFAULT NULL COMMENT '紧急诉求处理状态',
  `FTEmergencyHander` varchar(30) DEFAULT NULL COMMENT '紧急诉求处理人',
  `FTNotMatchCondition` varchar(50) DEFAULT NULL COMMENT '不满足安装条件',
  `FTExpectTime` datetime DEFAULT NULL COMMENT '客户期望时间',
  `FTFirstAppointmentTime` datetime DEFAULT NULL COMMENT '首次预约时间',
  `FTLastAppointmentTime` datetime DEFAULT NULL COMMENT '最后预约时间',
  `FTSRCancelReason` varchar(50) DEFAULT NULL COMMENT '取消原因',
  `FTReminderType` varchar(30) DEFAULT NULL COMMENT '催单类型',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-诉求信息入表';


/***CEM报装信息-用户信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_user`;
create TABLE `cem_in_decorate_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `user_id` varchar(30) NOT NULL COMMENT '联系人ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `LastName` varchar(50) DEFAULT NULL COMMENT '姓名',
  `CellularPhone` varchar(40) DEFAULT NULL COMMENT '手机号码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-用户信息入表';

ALTER TABLE depcenter.`cem_in_decorate_user` modify `LastName` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '姓名';


/***CEM报装信息-地址信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_address`;
create TABLE `cem_in_decorate_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `address_id` varchar(30) NOT NULL COMMENT '地址ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `Province` varchar(50) DEFAULT NULL COMMENT '省',
  `City` varchar(50) DEFAULT NULL COMMENT '市',
  `District` varchar(50) DEFAULT NULL COMMENT '区',
  `StreetAddress` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1   DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-地址信息入表';

ALTER TABLE depcenter.cem_in_decorate_address MODIFY `StreetAddress` varchar(200) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址';

/***CEM报装信息-Voc信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_voc`;
create TABLE `cem_in_decorate_voc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FTVOC1` varchar(30) DEFAULT NULL COMMENT 'VOC一级分类',
  `FTVOC2` varchar(30) DEFAULT NULL COMMENT 'VOC二级分类',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-Voc信息入表';



/***CEM报装信息-跟进信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_follow`;
create TABLE `cem_in_decorate_follow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `follow_id` varchar(30) NOT NULL COMMENT '跟进记录Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FollowContent` varchar(2000) DEFAULT NULL COMMENT '跟进内容',
  `Type` varchar(30) DEFAULT NULL COMMENT '跟进类型',
  `Updated` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-跟进信息入表';

ALTER TABLE depcenter.cem_in_decorate_follow MODIFY `FollowContent` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '跟进内容';
ALTER TABLE depcenter.cem_in_decorate_follow MODIFY `FollowContent` varchar(4000) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跟进内容';

/***CEM报装信息-工单信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_order`;
create TABLE `cem_in_decorate_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `order_id` varchar(30) NOT NULL COMMENT '工单Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `AddressId` varchar(15) DEFAULT NULL COMMENT '地址ID',
  `AssetId` varchar(15) DEFAULT NULL COMMENT '资产ID',
  `FTServiceCategoryId` varchar(15) DEFAULT NULL COMMENT '服务项目ID',
  `OrderCode` varchar(30) DEFAULT NULL COMMENT '工单编号',
  `FTSROrderType` varchar(30) DEFAULT NULL COMMENT '工单类型',
  `ServiceCenter` varchar(100) DEFAULT NULL COMMENT '服务中心',
  `FTSRNumber` varchar(64) DEFAULT NULL COMMENT '诉求编号',
  `FTProductCategory` varchar(30) DEFAULT NULL COMMENT '产品组',
  `FTProductModel` varchar(100) DEFAULT NULL COMMENT '简化型号',
  `FTServiceCategory` varchar(100) DEFAULT NULL COMMENT '服务项目',
  `ServiceNetwork` varchar(50) DEFAULT NULL COMMENT '服务网点',
  `FTSRStatus` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `ServiceEngineer` varchar(30) DEFAULT NULL COMMENT '服务工程师',
  `OrderStatus` varchar(30) DEFAULT NULL COMMENT '工单状态',
  `CancelReason` varchar(2000) DEFAULT NULL COMMENT '取消原因',
  `FTFaultLargeCategory` varchar(100) DEFAULT NULL COMMENT '故障大类',
  `FTFaultSmallCategory` varchar(100) DEFAULT NULL COMMENT '故障小类',
  `InstallReason` varchar(30) DEFAULT NULL COMMENT '不能安装原因',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-工单信息入表';



/***CEM报装信息-资产信息入表***/
DROP TABLE IF EXISTS `cem_in_decorate_asset`;
create TABLE `cem_in_decorate_asset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `asset_id` varchar(30) NOT NULL COMMENT '资产Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FTXZpp` varchar(4) DEFAULT NULL COMMENT '品牌',
  `FTAssetType` varchar(50) DEFAULT NULL COMMENT '产品组',
  `FTAssetModel` varchar(100) DEFAULT NULL COMMENT '资产型号',
  `FTPurchaseChannel` varchar(30) DEFAULT NULL COMMENT '购买渠道',
  `AssetNumber` varchar(100) DEFAULT NULL COMMENT '产品编号',
  `PersonalAddressId` varchar(15) DEFAULT NULL COMMENT '地址ID',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报装信息-资产信息入表';

ALTER TABLE depcenter.cem_in_decorate_asset MODIFY `FTXZpp` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '品牌';


/***CEM报修信息-诉求信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_appeal`;
create TABLE `cem_in_repair_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FollowOrganization` varchar(100) DEFAULT NULL COMMENT '服务中心',
  `ContactId` varchar(30) DEFAULT NULL COMMENT '联系人ID',
  `SRNumber` varchar(64) DEFAULT NULL COMMENT '诉求单编号',
  `SRType` varchar(30) DEFAULT NULL COMMENT '诉求类型',
  `Source` varchar(30) DEFAULT NULL COMMENT '诉求来源',
  `Status` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `FTCallerName` varchar(50) DEFAULT NULL COMMENT '呼入人',
  `FTCallerPhone` varchar(30) DEFAULT NULL COMMENT '呼入号码',
  `Severity` varchar(30) DEFAULT NULL COMMENT '紧急程度',
  `FTUrgentSRProcessStatus` varchar(50) DEFAULT NULL COMMENT '紧急诉求处理状态',
  `FTEmergencyHander` varchar(30) DEFAULT NULL COMMENT '紧急诉求处理人',
  `FTNotMatchCondition` varchar(50) DEFAULT NULL COMMENT '不满足安装条件',
  `FTExpectTime` datetime DEFAULT NULL COMMENT '客户期望时间',
  `FTFirstAppointmentTime` datetime DEFAULT NULL COMMENT '首次预约时间',
  `FTLastAppointmentTime` datetime DEFAULT NULL COMMENT '最后预约时间',
  `FTSRCancelReason` varchar(50) DEFAULT NULL COMMENT '取消原因',
  `FTReminderType` varchar(30) DEFAULT NULL COMMENT '催单类型',
  `FTProvince` varchar(30) DEFAULT NULL COMMENT '省',
  `FTCity` varchar(30) DEFAULT NULL COMMENT '市',
  `FTDistrict` varchar(50) DEFAULT NULL COMMENT '区',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-诉求信息入表';

ALTER TABLE depcenter.cem_in_repair_appeal MODIFY `FTCallerName` varchar(50) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '呼入人';

/***CEM报修信息-用户信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_user`;
create TABLE `cem_in_repair_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `user_id` varchar(30) NOT NULL COMMENT '联系人Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `LastName` varchar(50) DEFAULT NULL COMMENT '姓名',
  `CellularPhone` varchar(40) DEFAULT NULL COMMENT '手机号',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-用户信息入表';

ALTER TABLE depcenter.cem_in_repair_user MODIFY `LastName` varchar(50) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名';


/***CEM报修信息-地址信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_address`;
create TABLE `cem_in_repair_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `address_id` varchar(30) NOT NULL COMMENT '地址ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `Province` varchar(50) DEFAULT NULL COMMENT '省',
  `City` varchar(50) DEFAULT NULL COMMENT '市',
  `District` varchar(50) DEFAULT NULL COMMENT '区',
  `StreetAddress` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-地址信息入表';

ALTER TABLE depcenter.cem_in_repair_address MODIFY `StreetAddress` varchar(200) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址';

/***CEM报修信息-Voc信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_voc`;
create TABLE `cem_in_repair_voc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FTVOC1` varchar(30) DEFAULT NULL COMMENT 'VOC一级分类',
  `FTVOC2` varchar(30) DEFAULT NULL COMMENT 'VOC二级分类',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-Voc信息入表';



/***CEM报修信息-跟进信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_follow`;
create TABLE `cem_in_repair_follow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `follow_id` varchar(30) NOT NULL COMMENT '跟进记录Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FollowContent` varchar(2000) DEFAULT NULL COMMENT '跟进内容',
  `Type` varchar(30) DEFAULT NULL COMMENT '跟进类型',
  `Updated` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-跟进信息入表';

ALTER TABLE depcenter.cem_in_repair_follow MODIFY `FollowContent` varchar(4000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '跟进内容';
ALTER TABLE depcenter.`cem_in_repair_follow` modify `FollowContent` varchar(4000) character set utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跟进内容';

/***CEM报修信息-工单信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_order`;
create TABLE `cem_in_repair_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `order_id` varchar(30) NOT NULL COMMENT '工单Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `AddressId` varchar(15) DEFAULT NULL COMMENT '地址ID',
  `AssetId` varchar(15) DEFAULT NULL COMMENT '资产ID',
  `FTServiceCategoryId` varchar(15) DEFAULT NULL COMMENT '服务项目ID',
  `OrderCode` varchar(30) DEFAULT NULL COMMENT '工单编号',
  `FTSROrderType` varchar(30) DEFAULT NULL COMMENT '工单类型',
  `ServiceCenter` varchar(100) DEFAULT NULL COMMENT '服务中心',
  `FTSRNumber` varchar(64) DEFAULT NULL COMMENT '诉求编号',
  `FTProductCategory` varchar(30) DEFAULT NULL COMMENT '产品组',
  `FTProductModel` varchar(100) DEFAULT NULL COMMENT '简化型号',
  `FTServiceCategory` varchar(100) DEFAULT NULL COMMENT '服务项目',
  `ServiceNetwork` varchar(50) DEFAULT NULL COMMENT '服务网点',
  `FTSRStatus` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `ServiceEngineer` varchar(30) DEFAULT NULL COMMENT '服务工程师',
  `OrderStatus` varchar(30) DEFAULT NULL COMMENT '工单状态',
  `CancelReason` varchar(2000) DEFAULT NULL COMMENT '取消原因',
  `FTFaultLargeCategory` varchar(100) DEFAULT NULL COMMENT '故障大类',
  `FTFaultSmallCategory` varchar(100) DEFAULT NULL COMMENT '故障小类',
  `InstallReason` varchar(30) DEFAULT NULL COMMENT '不能安装原因',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-工单信息入表';


/***CEM报修信息-资产信息入表***/
DROP TABLE IF EXISTS `cem_in_repair_asset`;
create TABLE `cem_in_repair_asset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `asset_id` varchar(30) NOT NULL COMMENT '资产Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FTXZpp` varchar(4) DEFAULT NULL COMMENT '品牌',
  `FTAssetType` varchar(50) DEFAULT NULL COMMENT '产品组产品组',
  `FTAssetModel` varchar(100) DEFAULT NULL COMMENT '资产型号',
  `FTPurchaseChannel` varchar(30) DEFAULT NULL COMMENT '购买渠道',
  `AssetNumber` varchar(100) DEFAULT NULL COMMENT '产品编号',
  `PersonalAddressId` varchar(15) DEFAULT NULL COMMENT '地址ID',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM报修信息-资产信息入表';

ALTER TABLE depcenter.cem_in_repair_asset MODIFY `FTXZpp` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '品牌';

/***CEM退货信息-诉求信息入表***/
DROP TABLE IF EXISTS `cem_in_return_goods_appeal`;
create TABLE `cem_in_return_goods_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FollowOrganization` varchar(100) DEFAULT NULL COMMENT '跟进组织',
  `ContactId` varchar(30) DEFAULT NULL COMMENT '联系人ID',
  `SRNumber` varchar(64) DEFAULT NULL COMMENT '诉求单编号',
  `SRType` varchar(30) DEFAULT NULL COMMENT '诉求类型',
  `Source` varchar(30) DEFAULT NULL COMMENT '诉求来源',
  `Status` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `FTExpectTime` datetime DEFAULT NULL COMMENT '客户期望时间',
  `FTFirstAppointmentTime` datetime DEFAULT NULL COMMENT '首次预约时间',
  `FTLastAppointmentTime` datetime DEFAULT NULL COMMENT '最后预约时间',
  `FTSRCancelReason` varchar(50) DEFAULT NULL COMMENT '取消原因',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `FTComplaintContent` varchar(4000) DEFAULT NULL COMMENT '详细信息',
  `FTBuyChannel` varchar(30) DEFAULT NULL COMMENT '购买渠道',
  `FTProvince` varchar(30) DEFAULT NULL COMMENT '省',
  `FTCity` varchar(30) DEFAULT NULL COMMENT '市',
  `DistrictCode` varchar(30) DEFAULT NULL COMMENT '区县',
  `FTAddr` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM退货信息-诉求信息入表';

/***CEM退货信息-用户信息入表***/
DROP TABLE IF EXISTS `cem_in_return_goods_user`;
create TABLE `cem_in_return_goods_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `user_id` varchar(30) NOT NULL COMMENT '联系人Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `LastName` varchar(50) DEFAULT NULL COMMENT '姓名',
  `CellularPhone` varchar(40) DEFAULT NULL COMMENT '手机号码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM退货信息-用户信息入表';



/***CEM换货信息-诉求信息入表***/
DROP TABLE IF EXISTS `cem_in_exchange_goods_appeal`;
create TABLE `cem_in_exchange_goods_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `FollowOrganization` varchar(100) DEFAULT NULL COMMENT '跟进组织',
  `ContactId` varchar(30) DEFAULT NULL COMMENT '联系人ID',
  `SRNumber` varchar(64) DEFAULT NULL COMMENT '诉求单编号',
  `SRType` varchar(30) DEFAULT NULL COMMENT '诉求类型',
  `Source` varchar(30) DEFAULT NULL COMMENT '诉求来源',
  `Status` varchar(30) DEFAULT NULL COMMENT '诉求状态',
  `FTExpectTime` datetime DEFAULT NULL COMMENT '客户期望时间',
  `FTFirstAppointmentTime` datetime DEFAULT NULL COMMENT '首次预约时间',
  `FTLastAppointmentTime` datetime DEFAULT NULL COMMENT '最后预约时间',
  `FTSRCancelReason` varchar(50) DEFAULT NULL COMMENT '取消原因',
  `Created` datetime DEFAULT NULL COMMENT '创建时间',
  `FTComplaintContent` varchar(4000) DEFAULT NULL COMMENT '详细信息',
  `FTBuyChannel` varchar(30) DEFAULT NULL COMMENT '购买渠道',
  `FTProvinceFTProvince` varchar(30) DEFAULT NULL COMMENT '省',
  `FTCity` varchar(30) DEFAULT NULL COMMENT '市',
  `DistrictCode` varchar(30) DEFAULT NULL COMMENT '区县',
  `FTAddr` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM换货信息-诉求信息入表';

ALTER TABLE depcenter.cem_in_exchange_goods_appeal MODIFY `FTComplaintContent` varchar(4000) character set utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细信息';


/***CEM换货信息-用户信息入表***/
DROP TABLE IF EXISTS `cem_in_exchange_goods_user`;
create TABLE `cem_in_exchange_goods_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `user_id` varchar(30) NOT NULL COMMENT '联系人Id',
  `SRId` varchar(30) NOT NULL COMMENT '诉求Id',
  `LastName` varchar(50) DEFAULT NULL COMMENT '姓名',
  `CellularPhone` varchar(40) DEFAULT NULL COMMENT '手机号码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='CEM换货信息-用户信息入表';





/*****MSP*****/

/**MSP终端建设申请入表**/
DROP TABLE IF EXISTS `msp_in_tapply`;
CREATE TABLE `msp_in_tapply`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `apply_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请id',
  `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '标题',
  `design_supplier_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '设计供应商编码',
  `production_supplier_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '制作供应商编码',
  `pay_amount` decimal(10, 0) NULL DEFAULT NULL COMMENT '实际支付金额/含税（元）',
  `type` tinyint(2) NULL DEFAULT 3 COMMENT '申请类型(3:常规门店终端建设,4:大型门店终端建设,5:维修申请,6:整改申请)',
  `wf_instance_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '终端建设申请工作流编码',
  `region_name` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '大区名称',
  `company_id` int(11) NULL DEFAULT NULL COMMENT '分公司id',
  `company_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分公司名称',
  `store_id` int(11) NULL DEFAULT NULL COMMENT '门店id',
  `store_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店编码',
  `store_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店名称',
  `store_channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店渠道编码，关联字典表',
  `store_market_grade` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店市场等级，关联字典表',
  `channel_category_id` bigint(20) NULL DEFAULT NULL COMMENT '客户渠道大类Id',
  `channel_category_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户渠道大类编码',
  `channel_category_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户渠道大类名称',
  `channel_subdivide_id` bigint(20) NULL DEFAULT NULL COMMENT '客户渠道细分Id',
  `channel_subdivide_code` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户渠道细分编码',
  `channel_subdivide_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户渠道细分名称',
  `eps_no_design` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'eps设计单号',
  `eps_no_accept` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'eps验收单号',
  `decorate_budge` decimal(12, 2) NULL DEFAULT NULL COMMENT '施工预算（元,取整）',
  `budget_center` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预算中心',
  `budget_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预算项，关联字段表',
  `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP终端建设申请入表' ROW_FORMAT = DYNAMIC;

/**MSP终端建设申请-大型门店入表**/
DROP TABLE IF EXISTS `msp_in_tapply_large`;
CREATE TABLE `msp_in_tapply_large`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `apply_large_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请-大型门店表id',
    `apply_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请id',
    `decorate_type` tinyint(2) NULL DEFAULT NULL COMMENT '装修类型，1-新装，2.重装',
    `annual_profit_estimation` decimal(10, 2) NULL DEFAULT NULL COMMENT '年度盈利设想(万元)',
    `special_requirement` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店特殊要求',
    `market_grade_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '市场等级，关联字典表',
    `decorate_version` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '终端装修版本，关联字典表',
    `usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '初测使用面积',
    `width` decimal(6, 2) NULL DEFAULT NULL COMMENT '门店宽度',
    `depth` decimal(6, 2) NULL DEFAULT NULL COMMENT '门店深度',
    `property_right` tinyint(2) NULL DEFAULT NULL COMMENT '门店产权类型，1-自有，2-租赁',
    `door_size` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门头尺寸',
    `layout_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '户型编码，关联字典表',
    `floors` tinyint(2) NULL DEFAULT NULL COMMENT '层数',
    `floor_height` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '层高',
    `scene_demo` tinyint(2) NULL DEFAULT NULL COMMENT '是否支持场景演示，默认0，1表示支持',
    `biz_district` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '所属商圈，关联字典表',
    `designer` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '设计师',
    `begin_date` datetime NULL DEFAULT NULL COMMENT '开工时间',
    `finish_date` datetime NULL DEFAULT NULL COMMENT '制作完成时间',
    `acceptance_finish_date` datetime NULL DEFAULT NULL COMMENT '验收完成时间',
    `acceptance_score` decimal(5, 2) NULL DEFAULT NULL COMMENT '验收得分',
    `applied_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '申请金额',
    `verification_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '核销金额',
    `standard_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '标准件费用',
    `paid_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '已支付预付款',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP终端建设申请-大型门店入表' ROW_FORMAT = DYNAMIC;


/**MSP终端建设申请-常规门店入表**/
DROP TABLE IF EXISTS `msp_in_tapply_normal`;
CREATE TABLE `msp_in_tapply_normal`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `apply_normal_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请-常规门店表id',
     `apply_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请id',
     `decorate_type` tinyint(2) NULL DEFAULT NULL COMMENT '装修类型，1-新装，2.重装',
     `budget_item` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '预算项目',
     `supplier_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '供应商编码，关联供应商表',
     `special_requirement` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店特殊要求',
     `decorate_version` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '终端版本，关联字典表',
     `usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际使用面积',
     `eps_usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT 'EPS回传实际使用面积',
     `door_size` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门头尺寸',
     `layout_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '户型编码，关联字典表',
     `floors` tinyint(2) NULL DEFAULT NULL COMMENT '层数',
     `floor_height` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '层高',
     `live_demo` tinyint(2) NULL DEFAULT NULL COMMENT '是否支持现场演示，0-不支持，1-支持',
     `non_standard_design` tinyint(2) NULL DEFAULT NULL COMMENT '非标设计，0-无，1-有',
     `non_standard_design_desc` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '非标内容，非标设计说明',
     `finish_date` datetime NULL DEFAULT NULL COMMENT '完成时间',
     `acceptance_finish_date` datetime NULL DEFAULT NULL COMMENT '验收完成时间',
     `tax_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '税点',
     `applied_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额',
     `applied_amount_with_tax` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额(含税)',
     `approved_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总部批准金额',
     `special_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '特殊费用',
     `standard_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '标准件费用含税',
     `diff_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '差异率(%)',
     `decorate_avg_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '门店建设均价，(实付+标准件)/施工面积',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP终端建设申请-常规门店入表' ROW_FORMAT = DYNAMIC;

ALTER TABLE depcenter.msp_in_tapply_normal MODIFY non_standard_design_desc varchar(2000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '非标内容，非标设计说明';

/**MSP终端建设申请-整改入表**/
DROP TABLE IF EXISTS `msp_in_tapply_reform`;
CREATE TABLE `msp_in_tapply_reform`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `apply_reform_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请-整改表id',
     `apply_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请id',
     `supplier_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '供应商编码，关联供应商表',
     `usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '使用面积',
     `door_size` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门头尺寸',
     `previous_decorate_version` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '原门店装修版本',
     `decorate_version` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店装修版本',
     `reform_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '整改描述',
     `non_standard_design` tinyint(2) NULL DEFAULT NULL COMMENT '非标设计，0-无，1-有',
     `non_standard_design_desc` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '非标内容，非标设计说明',
     `finish_date` datetime NULL DEFAULT NULL COMMENT '整改完成时间',
     `acceptance_finish_date` datetime NULL DEFAULT NULL COMMENT '验收完成时间',
     `tax_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '税点',
     `applied_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额',
     `applied_amount_with_tax` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额(含税)',
     `approved_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总部批准金额',
     `special_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '特殊费用',
     `standard_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '标准件费用含税',
     `diff_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '差异率(%)',
     `decorate_avg_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '门店建设均价',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP终端建设申请-整改入表' ROW_FORMAT = DYNAMIC;

ALTER TABLE depcenter.msp_in_tapply_reform MODIFY non_standard_design_desc varchar(2000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '非标内容，非标设计说明';
ALTER TABLE depcenter.msp_in_tapply_reform MODIFY `reform_desc` varchar(2000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '整改描述';

/**MSP终端建设申请-维修入表**/
DROP TABLE IF EXISTS `msp_in_tapply_repair`;
CREATE TABLE `msp_in_tapply_repair`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `apply_repair_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请-维修表id',
     `apply_id` int(11) NULL DEFAULT NULL COMMENT '终端建设申请id',
     `supplier_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '供应商编码，关联供应商表',
     `remark` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '维修信息备注',
     `applied_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额',
     `applied_amount_with_tax` decimal(10, 2) NULL DEFAULT NULL COMMENT '实地申请金额(含税)',
     `approved_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总部批准金额',
     `special_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '特殊费用',
     `standard_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '标准件费用含税',
     `diff_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '差异率(%)',
     `tax_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '税点',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP终端建设申请-维修入表' ROW_FORMAT = DYNAMIC;

ALTER TABLE depcenter.msp_in_tapply_repair MODIFY `remark` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '维修信息备注';

/**MSP业务员入表***/
DROP TABLE IF EXISTS `msp_in_salesman`;
CREATE TABLE `msp_in_salesman`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `salesman_id` int(10) NULL DEFAULT NULL COMMENT '业务员主键id,此处将文档中id修改为salesman_id',
    `parent_id` int(10) NULL DEFAULT NULL COMMENT '上级id',
    `portrait` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员头像',
    `station` int(10) NULL DEFAULT NULL COMMENT '岗位:1:客户经理；2：厨电顾问',
    `wechatno` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信号',
    `status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '员工状态，0：禁用；1：启用',
    `term_start` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '有效期-开始时间',
    `term_end` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '有效期-结束时间',
    `store_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属门店编码',
    `store_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属门店名称',
    `dep_code` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门名称',
    `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员手机号',
    `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员编码',
    `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务员名称',
    `disable_date` datetime NULL DEFAULT NULL COMMENT '禁用时间',
    `entry_date` datetime NULL DEFAULT NULL COMMENT '入职日期',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'MSP业务员入表' ROW_FORMAT = DYNAMIC;

/**MSP开/闭店申请清单管理-门店申请入表**/
DROP TABLE IF EXISTS `msp_in_s_apply`;
CREATE TABLE `msp_in_s_apply`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
   `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
   `apply_id` int(11) NULL DEFAULT NULL COMMENT '门店申请表主键',
   `wf_instance_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店申请工作流编码',
   `apply_type` tinyint(2) NULL DEFAULT 1 COMMENT '申请类型，1-开店，2-闭店',
   `apply_status` tinyint(2) NULL DEFAULT 0 COMMENT '申请状态，0-待提交，1-处理中，2-已结束，3-已取消',
   `store_id` int(11) NULL DEFAULT NULL COMMENT '门店id',
   `store_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店编码',
   `store_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店名称',
   `store_type` tinyint(2) NULL DEFAULT NULL COMMENT '门店类型，1-实体店，2-虚拟店',
   `store_channel_id` int(11) NULL DEFAULT NULL COMMENT '门店渠道id',
   `store_channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店渠道编码，channel_category',
   `store_sub_channel_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店细分渠道编码',
   `store_status` tinyint(2) NULL DEFAULT 1 COMMENT '门店状态，0：禁用；1：启用；2：筹备；3：清退',
   `region_id` int(11) NULL DEFAULT NULL COMMENT '大区id',
   `region_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '大区名称',
   `company_id` int(20) NULL DEFAULT NULL COMMENT '分公司id',
   `company_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '分公司名称',
   `distributor_id` int(11) NULL DEFAULT NULL COMMENT '经销商id / 所属客户id',
   `distributor_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '经销商 / 客户编码',
   `distributor_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '经销商名称 / 所属客户',
   `distributor_channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户渠道编码',
   `distributor_sub_channel` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '客户二级渠道编码',
   `open_type` tinyint(2) NULL DEFAULT 3 COMMENT '开店模式(1:专卖店/KA门店且是实体门店,2:社区服务店且是实体门店,3:其他渠道[默认])',
   `bpm_type` tinyint(2) NULL DEFAULT 0 COMMENT '流程类型(1:电商/米博/办事处;2:经销商且KA/家装;3:经销商且实体)',
   `decorate_status` tinyint(2) NULL DEFAULT NULL COMMENT '装修状态，默认1，1-装修，2-验收',
   `distributor_changed` tinyint(2) NULL DEFAULT 0 COMMENT '门店是否更换过门店所属客户(1:是,0:否,默认0)',
   `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
   `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
   `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
   `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP开/闭店申请清单管理-门店申请入表' ROW_FORMAT = Dynamic;

/**MSP开/闭店申请清单管理-门店申请-闭店申请入表***/
DROP TABLE IF EXISTS `msp_in_s_apply_close`;
CREATE TABLE `msp_in_s_apply_close`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `apply_close_id` int(11) NULL DEFAULT NULL COMMENT '门店申请表主键',
     `apply_id` int(11) NULL DEFAULT NULL COMMENT '申请id',
     `total_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '门店总面积',
     `down_time` datetime NULL DEFAULT NULL COMMENT '门店停用时间',
     `close_reason` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '闭店原因',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP开/闭店申请清单管理-门店申请-闭店申请入表' ROW_FORMAT = Dynamic;

/**MSP开/闭店申请清单管理-门店申请-开店申请入表***/
DROP TABLE IF EXISTS `msp_in_s_apply_open`;
CREATE TABLE `msp_in_s_apply_open`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `apply_open_id` int(11) NULL DEFAULT NULL COMMENT '门店申请表主键',
    `apply_id` int(11) NULL DEFAULT NULL COMMENT '开闭店申请id',
    `property_right` tinyint(2) NULL DEFAULT NULL COMMENT '门店产权类型，1-自有，2-租赁',
    `need_terminal_build` tinyint(2) NULL DEFAULT 1 COMMENT '是否需要终端建设，0-不需要，1-需要',
    `province_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-省id',
    `province_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '地址-省名称',
    `city_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-市id',
    `city_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '地址-市名称',
    `county_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-县id',
    `county_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '地址-县名称',
    `store_market_grade` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店市场等级',
    `store_biz_type` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店业务类型，BBC，B2B，B2C',
    `longitude` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '经度',
    `latitude` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '纬度',
    `address` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '地图选择地址',
    `address2` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '用户输入地址',
    `manager_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店负责人姓名',
    `manager_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '门店负责人联系方式',
    `lease_term` tinyint(2) UNSIGNED NULL DEFAULT NULL COMMENT '租用年限',
    `annual_rent` decimal(6, 2) UNSIGNED NULL DEFAULT NULL COMMENT '年租金',
    `usable_area` decimal(10, 2) NULL DEFAULT NULL COMMENT '使用面积',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP开/闭店申请清单管理-门店申请-开店申请入表' ROW_FORMAT = Dynamic;

ALTER TABLE depcenter.msp_in_s_apply_open MODIFY `annual_rent` decimal(20,2) unsigned DEFAULT NULL COMMENT '年租金';

/**MSP引流渠道管理-家装公司入表***/
DROP TABLE IF EXISTS `msp_in_t_decorate_company`;
CREATE TABLE `msp_in_t_decorate_company`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `decorate_company_id` bigint(20) NULL DEFAULT NULL COMMENT '家装公司表主键ID',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '分公司ID',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分公司名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家装公司编码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家装公司名称',
  `full_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家装公司全称',
  `business_license_code` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '营业执照编码',
  `category` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司分类,1：家装 2：橱柜 3：定制',
  `province_id` bigint(20) NULL DEFAULT NULL COMMENT '公司-省ID',
  `provice_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司-省名称',
  `city_id` bigint(20) NULL DEFAULT NULL COMMENT '公司-市ID',
  `city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司-市名称',
  `county_id` bigint(20) NULL DEFAULT NULL COMMENT '公司-区ID',
  `county_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司-区名称',
  `address` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '详细地址',
  `contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人电话',
  `charge_user_id` bigint(20) NULL DEFAULT NULL COMMENT '业务员ID',
  `charge_user_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员编码',
  `charge_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员名称',
  `charge_user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员手机号',
  `dealers_user_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商编码',
  `dealers_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商名称',
  `dealers_user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商手机号',
  `valid_start_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效期开始时间',
  `valid_end_time` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '有效期截止时间',
  `status` int(1) NULL DEFAULT 1 COMMENT '状态 0：禁用；1：启用',
  `remark` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `store_id` bigint(20) NULL DEFAULT NULL COMMENT '所属门店id(orgId)',
  `store_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属门店编码',
  `store_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '所属门店名称',
  `brand_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '品牌所属类别',
  `co_decorate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '合作家装公司',
  `link_diffIndustry` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联异业三工',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `sex` int(10) NULL DEFAULT NULL COMMENT '性别 0：男；1：女',
  `employ_year` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '从业年限',
  `station_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '岗位工种',
  `main_attack` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主攻方向',
  `work_store_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作门店名称',
  `work_store_brand_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作门店id品牌名称',
  `type` int(10) NULL DEFAULT 1 COMMENT '类型 1：家装；2：异业三工',
  `designer_category` bigint(20) NULL DEFAULT NULL COMMENT '设计师分类',
  `designer_category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师分类名称',
  `kitchen_cooperation_brand` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '厨电合作品牌',
  `link_diff_store` bigint(20) NULL DEFAULT NULL COMMENT '关联异业门店(t_decorate_company.id)',
  `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP引流渠道管理-家装公司入表' ROW_FORMAT = Dynamic;

/**MSP引流渠道管理-设计师入表***/
DROP TABLE IF EXISTS `msp_in_t_designer`;
CREATE TABLE `msp_in_t_designer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `designer_id` bigint(20) NULL DEFAULT NULL COMMENT '设计师表主键',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师编码',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师姓名',
  `id_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '身份证号',
  `employed_age` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '从业年限',
  `designer_category` int(2) NULL DEFAULT NULL COMMENT '设计师分类 1:家装设计师 2：橱柜设计师 3：定制设计师',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `company_id` bigint(20) NULL DEFAULT NULL COMMENT '分公司ID',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分公司名称',
  `charge_user_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员编码',
  `charge_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员名称',
  `charge_user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务员手机号',
  `dealers_user_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商编码',
  `dealers_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商名称',
  `dealers_user_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商手机号',
  `main_attack` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主攻方向',
  `personal_tags` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '个性标签',
  `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP引流渠道管理-设计师入表' ROW_FORMAT = Dynamic;

/**MSP引流渠道管理-设计师家装公司关系入表***/
DROP TABLE IF EXISTS `msp_in_t_designer_decorate_company_mapping`;
CREATE TABLE `msp_in_t_designer_decorate_company_mapping`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
   `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
   `designer_decorate_company_mapping_id` bigint(20) NULL DEFAULT NULL COMMENT '设计师家装公司关系表主键',
   `designer_id` bigint(20) NULL DEFAULT NULL COMMENT '设计师ID,t_designer.id',
   `decorate_company_id` bigint(20) NULL DEFAULT NULL COMMENT '家装公司ID,t_decorate_company.id',
   `food_sort` bigint(20) NULL DEFAULT NULL COMMENT '家装公司顺序',
   `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
   `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
   `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
   `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP引流渠道管理-设计师家装公司关系入表' ROW_FORMAT = Dynamic;

/**MSP订单管理-报单表入表***/
DROP TABLE IF EXISTS `msp_in_declaration_info`;
CREATE TABLE `msp_in_declaration_info`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `declaration_info_id` bigint(20) NULL DEFAULT NULL COMMENT '报单表主键ID',
    `user_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '买家姓名',
    `company_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '分公司ID',
    `store_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '门店ID',
    `store_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店code',
    `company_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司code',
    `company_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公司名称',
    `store_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店名称',
    `approve_status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '订单审批状态 01:未审批 02：审批通过  03：审批不通过',
    `order_create_time` datetime NULL DEFAULT NULL COMMENT '订单生成时间',
    `contact_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人姓名',
    `contact_mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收货人手机号',
    `provice_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-省ID(去掉后面的6个0)',
    `city_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-市ID(去掉后面的6个0)',
    `county_id` bigint(20) NULL DEFAULT NULL COMMENT '地址-区ID(去掉后面的6个0)',
    `provice_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '地址-省名称',
    `city_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '地址-市名称',
    `county_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '地址-区名称',
    `delivery_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人地址',
    `delivery_date` datetime NULL DEFAULT NULL COMMENT '送货时间',
    `user_note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '买家留言',
    `order_note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
    `goods_num` int(11) NULL DEFAULT 0 COMMENT '商品总数量',
    `goods_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '商品总价格',
    `buy_reason` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '购买原因 01：毛坯新装 02：厨电更新 03：全屋重装',
    `business_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT 'BBC' COMMENT '业务类型 默认值：BBC',
    `adviser_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '厨电顾问ID',
    `adviser_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '厨电顾问姓名',
    `input_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '厨电顾问(录入人)id',
    `input_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '厨电顾问(录入人)名称',
    `manager_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '客户经理ID',
    `manager_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客户经理名称',
    `settlement_ticket` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结算小票号',
    `install_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '安装单号',
    `contact_mobile_bak` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系方式',
    `stage` tinyint(4) UNSIGNED NULL DEFAULT 1 COMMENT '审核状态 1：待审核；2：审核通过；3：审核拒绝',
    `drp_stage` tinyint(4) UNSIGNED NULL DEFAULT 1 COMMENT '同步drp状态 1：未处理；2：同步成功；3：同步失败',
    `drp_orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'drp订单号',
    `is_to_drp` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP,4：同步至事业上DRP',
    `to_drp_stage` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '订单传送至DRP状态：1：需要审核 默认值1；2：不需要预审核',
    `decorate_company_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家装公司编码',
    `decorate_company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '家装公司名称',
    `designer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师编码',
    `designer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师名称',
    `designer_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设计师手机号',
    `cash_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '现金金额',
    `transfer_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '转账金额',
    `card_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '刷卡金额',
    `order_type` int(5) UNSIGNED NULL DEFAULT 1 COMMENT '订单类型 1：经销/零售；2：定金',
    `order_stage` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT 'DRP订单状态 0:无  1:打开 2:审核 3:关闭 4:作废 5:中止',
    `stransfer_stage` tinyint(4) UNSIGNED NULL DEFAULT 1 COMMENT '定金转单状态 1：未转单；2：已转单\' 3：已作废\'',
    `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '关联定金订单号',
    `earnest_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '定金金额',
    `village_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小区名称',
    `to_drp_msg` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '同步至drp返回信息',
    `distributor_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '经销商id',
    `distributor_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商名称',
    `distributor_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商编码',
    `channel_category_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '渠道大类编码',
    `channel_category_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '渠道大类名称',
    `channel_subdivide_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '渠道细分编码',
    `channel_subdivide_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '渠道细分名称',
    `store_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店类型',
    `ware_house` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '默认仓库',
    `examine_date` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '财务审核时间',
    `pre_examine` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '预审核状态',
    `pre_examine_date` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '预审核时间',
    `settlement_stage` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结算状态',
    `audit_date` datetime NULL DEFAULT NULL COMMENT '审核时间',
    `order_date` date NULL DEFAULT NULL COMMENT '订购时间',
    `decorate_company_type` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '类型 1：家装；2：异业三工,3 设计师',
    `app_pre_audit` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT '是否在云管理预审核 0：否；1：是 2:预审通过',
    `app_pre_examine_date` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '云管理预审核时间',
    `app_pre_examine` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '云管理预审核 0:是  1:否  订单为单位 0是已经预审过了， 1是还未预审 订单单位',
    `order_channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '订单渠道',
    `is_inStore` tinyint(4) UNSIGNED NULL DEFAULT 1 COMMENT '是否店内成交 0：否；1：是',
    `original_orderId` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '原销售订单',
    `goods_num_canEnter` int(2) NULL DEFAULT NULL COMMENT '产品数量是否可输入 0：否；1：是',
    `out_orderId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '外部订单号',
    `first_return_reason` bigint(20) NULL DEFAULT NULL COMMENT '一级退货原因id',
    `first_return_reason_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '退货原因-一级退货原因',
    `sec_return_reason` bigint(20) NULL DEFAULT NULL COMMENT '二级退货原因id',
    `sec_return_reason_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '二级退货原因名称',
    `oa_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'OA编号',
    `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '原因描述',
    `decorate_category` bigint(20) NULL DEFAULT NULL COMMENT '引流渠道类型',
    `decorate_category_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '引流渠道类型名称',
    `is_check_price` int(2) NULL DEFAULT 1 COMMENT '是否检查价格 0：否；1：是',
    `sample_type` tinyint(2) NULL DEFAULT NULL COMMENT '出样类型 0 非上样订单;5 上样订单;3 特价订单',
    `is_transfer` tinyint(2) NULL DEFAULT 1 COMMENT '是否通过drp转单 0：否；1：是',
    `central_purchase` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '集采单号,多个值逗号分隔',
    `store_type_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店类型code',
    `store_type_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '门店类型名称',
    `dep_id` bigint(20) NULL DEFAULT NULL COMMENT '部门id',
    `salesman_dep_id` bigint(20) NULL DEFAULT NULL COMMENT '业务员所属部门id 1：广东-东莞 2：广东-佛山 3：广东-广州 4：广东-海南 5：广东-惠州 6：广东-汕头 7：广东-深圳 8：广东-粤北 9：广东-中山',
    `lat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小区收货地址纬度',
    `lng` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小区收货地址经度',
    `order_img` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '图片',
    `sales_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '经销商销售模式，传10/20/30',
    `customer_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '客户名称',
    `clues_source_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '线索来源',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP订单管理-报单表入表' ROW_FORMAT = DYNAMIC;

ALTER TABLE depcenter.msp_in_declaration_info MODIFY `order_img` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '图片';
ALTER TABLE depcenter.msp_in_declaration_info MODIFY `user_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '买家姓名';

/**MSP订单管理-报单商品入表***/
DROP TABLE IF EXISTS `msp_in_goods_declaration`;
CREATE TABLE `msp_in_goods_declaration`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `goods_declaration_id` bigint(20) NULL DEFAULT NULL COMMENT '报单商品表主键id',
     `order_id` bigint(20) NULL DEFAULT NULL COMMENT '订单ID',
     `goods_category_code` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品分类编码',
     `goods_category_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品分类名称',
     `goods_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品名称',
     `goods_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品编号',
     `goods_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '商品单价',
     `goods_num` int(11) NULL DEFAULT NULL COMMENT '商品数量',
     `goods_total_price` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '商品价格(总) =商品单价*商品数量',
     `sort` smallint(5) UNSIGNED NULL DEFAULT NULL COMMENT '商品排序',
     `goods_type` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '商品类型 1-实物单品 2-实物组合 3-虚拟商品',
     `goods_note` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '商品备注',
     `is_gift` tinyint(4) UNSIGNED NULL DEFAULT 0 COMMENT '是否赠品 1：赠品；0：非赠品',
     `logistics_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '物流单号',
     `logistics_status` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '物流状态 ',
     `location_type` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '默认储位',
     `order_status` tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '订单状态',
     `guid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'guid',
     `delivery_date` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '送货时间',
     `source_guid` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '源订单商品的guid',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP订单管理-报单商品入表' ROW_FORMAT = Dynamic;

ALTER TABLE depcenter.msp_in_goods_declaration MODIFY `goods_category_code` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品分类编码';
ALTER TABLE depcenter.msp_in_goods_declaration MODIFY `goods_category_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品分类名称';

/**MSP订单管理-产品明细入表***/
DROP TABLE IF EXISTS `msp_in_goods_declaration_supple`;
CREATE TABLE `msp_in_goods_declaration_supple`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
    `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
    `goods_declaration_supple_id` bigint(20) NULL DEFAULT NULL COMMENT '主键id',
    `goods_declaration_id` bigint(20) NULL DEFAULT NULL COMMENT 'goods_declaration表的id',
    `pos_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'POS编码',
    `ticket_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '小票编码',
    `check_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '校验码',
    `oms_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'OMS号码',
    `is_show` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样',
    `handsel_off` decimal(12, 2) NULL DEFAULT NULL COMMENT '定金优惠',
    `plummet_off` decimal(12, 2) NULL DEFAULT NULL COMMENT '直降优惠',
    `special_off` decimal(12, 2) NULL DEFAULT NULL COMMENT '特批优惠',
    `special` decimal(12, 2) NULL DEFAULT NULL COMMENT '特批',
    `meal` decimal(12, 2) NULL DEFAULT NULL COMMENT '套餐',
    `red_card` decimal(12, 2) NULL DEFAULT NULL COMMENT '红卡',
    `market_red` decimal(12, 2) NULL DEFAULT NULL COMMENT '市调红',
    `market_blue` decimal(12, 2) NULL DEFAULT NULL COMMENT '市调蓝',
    `non_standard2` decimal(12, 2) NULL DEFAULT NULL COMMENT '非标2',
    `plummet` decimal(12, 2) NULL DEFAULT NULL COMMENT '直降',
    `use_ticket` decimal(12, 2) NULL DEFAULT NULL COMMENT '用券',
    `discount` decimal(12, 2) NULL DEFAULT NULL COMMENT '优惠',
    `total_blue` decimal(12, 2) NULL DEFAULT NULL COMMENT '总额蓝',
    `manager_card` decimal(12, 2) NULL DEFAULT NULL COMMENT '经理卡',
    `meal_model` decimal(12, 2) NULL DEFAULT NULL COMMENT '套餐机型',
    `points` decimal(12, 2) NULL DEFAULT NULL COMMENT '积分',
    `return_ticket` decimal(12, 2) NULL DEFAULT NULL COMMENT '返券',
    `col_return_ticket` decimal(18, 2) NULL DEFAULT NULL COMMENT '收返券/返利卡',
    `e_ticket` decimal(12, 2) NULL DEFAULT NULL COMMENT '电子券/积分',
    `delivery_card` decimal(12, 2) NULL DEFAULT NULL COMMENT '提货卡/联心卡/福卡',
    `return_ticket2` decimal(12, 2) NULL DEFAULT NULL COMMENT '返券2',
    `sig_card` decimal(12, 2) NULL DEFAULT NULL COMMENT '签售卡',
    `wealth_card` decimal(12, 2) NULL DEFAULT NULL COMMENT '财富增值卡',
    `tow_voucher` decimal(12, 2) NULL DEFAULT NULL COMMENT '二次券',
    `gift` decimal(12, 2) NULL DEFAULT NULL COMMENT '赠品',
    `delay_guarantee` decimal(12, 2) NULL DEFAULT NULL COMMENT '延保',
    `other_promotion` decimal(12, 2) NULL DEFAULT NULL COMMENT '其他促销费',
    `order_id` bigint(20) NULL DEFAULT NULL COMMENT '订单id',
    `goods_id` bigint(20) NULL DEFAULT NULL COMMENT '商品id',
    `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
    `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
    `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MSP订单管理-产品明细入表' ROW_FORMAT = Dynamic;

/**根据0406上线修改字段 **/
ALTER TABLE `depcenter`.`msp_in_goods_declaration_supple`
    MODIFY COLUMN `plummet` decimal(18, 2) NULL DEFAULT NULL COMMENT '直降' ,
    MODIFY COLUMN `plummet_off` decimal(18, 2) NULL DEFAULT NULL COMMENT '直降优惠' ,
    MODIFY COLUMN `market_blue` decimal(18, 2) NULL DEFAULT NULL COMMENT '市调蓝' ,
    MODIFY COLUMN `discount` decimal(18, 2) NULL DEFAULT NULL COMMENT '优惠' ,
    MODIFY COLUMN `return_ticket` decimal(18, 2) NULL DEFAULT NULL COMMENT '返券',
    MODIFY COLUMN `tow_voucher` decimal(18, 2) NULL DEFAULT NULL COMMENT '二次券' ,
    MODIFY COLUMN `gift` decimal(18, 2) NULL DEFAULT NULL COMMENT '赠品' ,
    MODIFY COLUMN `delay_guarantee` decimal(18, 2) NULL DEFAULT NULL COMMENT '延保' ,
    MODIFY COLUMN `other_promotion` decimal(18, 2) NULL DEFAULT NULL COMMENT '其他促销费' ;

/*********方瑞*****/
/***方瑞送货诉求-诉求状态信息入表***/
DROP TABLE IF EXISTS `fr_in_delivery_status`;
create TABLE `fr_in_delivery_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `appealId` varchar(30) DEFAULT NULL COMMENT '送货诉求Id',
  `processResults` varchar(30) DEFAULT NULL COMMENT '催单处理结果',
  `describe` text DEFAULT NULL COMMENT '描述',
  `processTime` datetime DEFAULT NULL COMMENT '处理时间',
  `status` varchar(30) DEFAULT NULL COMMENT '状态',
  `processStatus` varchar(30) DEFAULT NULL COMMENT '紧急诉求处理状态',
  `followType` varchar(30) DEFAULT NULL COMMENT '跟进类型',
  `followContent` text DEFAULT NULL COMMENT '跟进内容',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞送货诉求-诉求状态信息入表';

ALTER TABLE depcenter.`fr_in_delivery_status` modify `FollowContent` text character set utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跟进内容';

/***方瑞物流单据(采购单)信息入表***/
DROP TABLE IF EXISTS `fr_in_trans`;
create TABLE `fr_in_trans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `checkStatus` int(2) DEFAULT NULL COMMENT '审核状态',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞物流单据(采购单)信息入表';

/***方瑞物流单据(采购单)信息-商品行入表***/
DROP TABLE IF EXISTS `fr_in_trans_goods`;
create TABLE `fr_in_trans_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `uuid` varchar(50) DEFAULT NULL COMMENT 'uuid',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `code` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `name` varchar(100) DEFAULT NULL COMMENT '商品名称',
  `model` varchar(255) DEFAULT NULL COMMENT '商品型号',
  `productNum` int(11) DEFAULT NULL COMMENT '商品数量',
  `productPrice` decimal(20,2) DEFAULT NULL COMMENT '商品单价',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞物流单据(采购单)信息-商品行入表';


/***方瑞订单审核数据入表***/
DROP TABLE IF EXISTS `fr_in_orderaudit`;
CREATE TABLE `fr_in_orderaudit`  (
     `id` bigint(20) NOT NULL AUTO_INCREMENT,
     `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
     `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
     `orderId` int(11) NULL DEFAULT NULL COMMENT '订单头id',
     `checkStatus` tinyint(2) NULL DEFAULT NULL COMMENT '订单状态',
     `frOrderId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '方瑞订单号',
     `checkOperatorName` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核人（财务审核人）',
     `remark` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
     `checkTime` datetime NULL DEFAULT NULL COMMENT '审核时间',
     `advanceCheckStatus` tinyint(1) NULL DEFAULT NULL COMMENT '预审核状态 0：否；1：是 2:预审通过',
     `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
     `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
     `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
     `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
     `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '方瑞订单审核数据入表' ROW_FORMAT = DYNAMIC;

/***方瑞订单审核数据-商品行入表***/
DROP TABLE IF EXISTS `fr_in_orderaudit_goods`;
CREATE TABLE `fr_in_orderaudit_goods`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `batch_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '交互平台生成的批次号',
   `distribute_no` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
   `orderId` int(11)  NULL DEFAULT NULL COMMENT '订单头id',
   `guid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单行ID',
   `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品NO',
   `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品名称',
   `productSettlementPrice` decimal(20, 2) NULL DEFAULT NULL COMMENT '结算价',
   `price` decimal(20, 2) NULL DEFAULT NULL COMMENT '信用价',
   `creditPrice` decimal(20, 2) NULL DEFAULT NULL COMMENT '合同价',
   `outNum` int(11) NULL DEFAULT NULL COMMENT '已出库数量',
   `clearingStatus` int(11) NULL DEFAULT NULL COMMENT '结算状态 LOV值其他:0开票: 4',
   `status` int(11) NULL DEFAULT NULL COMMENT '物流状态 LOV值无:0出库: 3关闭：4',
   `remark` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
   `is_deleted` bigint(20) NULL DEFAULT 0 COMMENT '是否删除 0：否；1：是',
   `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
   `created_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改者',
   `modified_date` datetime NULL DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '方瑞订单审核数据-商品行入表' ROW_FORMAT = DYNAMIC;


/***方瑞订单物流数据入表***/
DROP TABLE IF EXISTS `fr_in_ordertrans`;
create TABLE `fr_in_ordertrans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` int(11) DEFAULT NULL COMMENT 'guid',
  `orderSn` varchar(30) DEFAULT NULL COMMENT '订单编号',
  `describe` varchar(100) DEFAULT NULL COMMENT '订单头ID',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `province` varchar(50) DEFAULT NULL COMMENT '省',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `consignee` varchar(63) DEFAULT NULL COMMENT '收货人名称',
  `mobile` varchar(63) DEFAULT NULL COMMENT '收货人手机号',
  `reserveDatetime` bigint(11) DEFAULT NULL COMMENT '客户预约时间',
  `shipMode` varchar(20) DEFAULT NULL COMMENT '运输方式',
  `expressCompany` varchar(100) DEFAULT NULL COMMENT '快递公司',
  `expressNo` varchar(100) DEFAULT NULL COMMENT '快递单号',
  `driverName` varchar(50) DEFAULT NULL COMMENT '司机名称',
  `driverPhone` varchar(20) DEFAULT NULL COMMENT '司机电话号码',
  `arrivedTime` bigint(11) DEFAULT NULL COMMENT '送达时间',
  `logisticsRemark` varchar(255) DEFAULT NULL COMMENT '物流备注',
  `status` varchar(11) DEFAULT NULL COMMENT '物流状态',
  `contactNumber` varchar(20) DEFAULT NULL COMMENT '收件人座机',
  `shipTime` Date DEFAULT NULL COMMENT '实际送货日期',
  `communityName` varchar(50) DEFAULT NULL COMMENT '楼盘/小区名',
  `channelName` varchar(100) DEFAULT NULL COMMENT '购买渠道',
  `appealId` varchar(30) DEFAULT NULL COMMENT '送货诉求Id',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞订单物流数据入表';

/***方瑞订单物流数据-商品行入表***/
DROP TABLE IF EXISTS `fr_in_ordertrans_goods`;
create TABLE `fr_in_ordertrans_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` int(11) DEFAULT NULL COMMENT '物流单头ID',
  `uuid` varchar(50) DEFAULT NULL COMMENT '订单行ID',
  `ouid` int(11) DEFAULT NULL COMMENT '物流行ID',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `code` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `name` varchar(100) DEFAULT NULL COMMENT '商品名称',
  `model` varchar(255) DEFAULT NULL COMMENT '商品型号',
  `productNum` int(11) DEFAULT NULL COMMENT '商品数量',
  `productPrice` decimal(20,2) DEFAULT NULL COMMENT '商品单价',
  `barCode` varchar(100) DEFAULT NULL COMMENT '商品条码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞订单物流数据-商品行入表';


/***方瑞物流信息回传(送货诉求)数据入表***/
DROP TABLE IF EXISTS `fr_in_delivery`;
create TABLE `fr_in_delivery` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `appealId` varchar(30) DEFAULT NULL COMMENT '送货诉求Id',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `orderSn` varchar(30) DEFAULT NULL COMMENT '订单编号',
  `describe` varchar(100) DEFAULT NULL COMMENT '订单头ID',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `province` varchar(50) DEFAULT NULL COMMENT '省',
  `city` varchar(50) DEFAULT NULL COMMENT '市',
  `area` varchar(50) DEFAULT NULL COMMENT '区',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `consignee` varchar(63) DEFAULT NULL COMMENT '收货人名称',
  `mobile` varchar(63) DEFAULT NULL COMMENT '收货人手机号',
  `reserveDatetime` bigint(11) DEFAULT NULL COMMENT '客户预约时间',
  `shipMode` varchar(20) DEFAULT NULL COMMENT '运输方式',
  `expressCompany` varchar(100) DEFAULT NULL COMMENT '快递公司',
  `expressNo` varchar(100) DEFAULT NULL COMMENT '快递单号',
  `driverName` varchar(50) DEFAULT NULL COMMENT '司机名称',
  `driverPhone` varchar(20) DEFAULT NULL COMMENT '司机电话号码',
  `arrivedTime` bigint(11) DEFAULT NULL COMMENT '送达时间',
  `logisticsRemark` varchar(255) DEFAULT NULL COMMENT '物流备注',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞物流信息回传(送货诉求)数据入表';

/***方瑞物流信息回传(送货诉求)-商品行入表***/
DROP TABLE IF EXISTS `fr_in_delivery_goods`;
create TABLE `fr_in_delivery_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `storageSn` varchar(30) DEFAULT NULL COMMENT '库存单号',
  `code` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `name` varchar(100) DEFAULT NULL COMMENT '商品名称',
  `model` varchar(255) DEFAULT NULL COMMENT '商品型号',
  `productNum` int(11) DEFAULT NULL COMMENT '商品数量',
  `productPrice` decimal(20,2) DEFAULT NULL COMMENT '商品单价',
  `barCode` varchar(100) DEFAULT NULL COMMENT '商品条码',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞物流信息回传(送货诉求)-商品行入表';



/****ECS费用申请***/
/***方瑞推送费用申请至ECS-申请头入表***/
DROP TABLE IF EXISTS `fr_in_feeapply`;
create TABLE `fr_in_feeapply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `APPLY_LDAP_CODE` varchar(20) DEFAULT NULL COMMENT '申请人',
  `ORG_LDAP_CODE` varchar(20) DEFAULT NULL COMMENT '行政组织',
  `APPLY_DATE` datetime DEFAULT NULL COMMENT '申请日期(预算占用日期)',
  `APPLY_AMOUNT` decimal(20,2) DEFAULT NULL COMMENT '申请金额',
  `CURRENCY_CODE` varchar(10) DEFAULT NULL COMMENT '币种编码',
  `ORDER_TYPE` varchar(10) DEFAULT NULL COMMENT '单据类型',
  `REASON_DESC` varchar(500) DEFAULT NULL COMMENT '业务描述',
  `IS_MORE_APPROVE` varchar(4) DEFAULT NULL COMMENT '是否用于多次报销',
  `SOURCE_SYSTEM` varchar(10) DEFAULT NULL COMMENT '来源系统',
  `SOURCE_ORDER_ID` varchar(50) DEFAULT NULL COMMENT '来源单据ID',
  `SOURCE_ORDER_CODE` varchar(50) DEFAULT NULL COMMENT '来源单据编码',
  `YEAR` varchar(10) DEFAULT NULL COMMENT '年份',
  `MONTH` varchar(10) DEFAULT NULL COMMENT '月份',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '流程类型',
  `NOTE` varchar(500) DEFAULT NULL COMMENT '说明',
  `ATTRIBUTE1` varchar(100) DEFAULT NULL COMMENT '扩展字段1',
  `ATTRIBUTE2` varchar(100) DEFAULT NULL COMMENT '扩展字段2',
  `ATTRIBUTE3` varchar(100) DEFAULT NULL COMMENT '扩展字段3',
  `ATTRIBUTE4` varchar(100) DEFAULT NULL COMMENT '扩展字段4',
  `ATTRIBUTE5` varchar(100) DEFAULT NULL COMMENT '扩展字段5',
  `ATTRIBUTE6` varchar(100) DEFAULT NULL COMMENT '扩展字段6',
  `ATTRIBUTE7` varchar(100) DEFAULT NULL COMMENT '扩展字段7',
  `ATTRIBUTE8` varchar(100) DEFAULT NULL COMMENT '扩展字段8',
  `ATTRIBUTE9` varchar(100) DEFAULT NULL COMMENT '扩展字段9',
  `ATTRIBUTE10` varchar(100) DEFAULT NULL COMMENT '扩展字段10',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞推送费用申请至ECS-申请头入表';

/***方瑞推送费用申请至ECS-申请行入表***/
DROP TABLE IF EXISTS `fr_in_feeapply_line`;
create TABLE `fr_in_feeapply_line` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(60) DEFAULT NULL COMMENT '交互平台生成的批次号',
  `distribute_no` varchar(60) DEFAULT NULL COMMENT '分发ID,MDM每次分发主数据产生新的分发ID',
  `guid` varchar(50) DEFAULT NULL COMMENT 'guid',
  `CURRENCY_CODE` varchar(10) DEFAULT NULL COMMENT '币种',
  `CONVERSION_RATE` varchar(10) DEFAULT NULL COMMENT '汇率',
  `APPLY_AMOUNT` decimal(20,2) DEFAULT NULL COMMENT '申请金额',
  `FEE_ITEM_CODE` varchar(20) DEFAULT NULL COMMENT '费用类别',
  `FEE_TYPE_PARENT_CODE` varchar(20) DEFAULT NULL COMMENT '预算项目',
  `BUSI_ORG_CODE` varchar(20) DEFAULT NULL COMMENT '预算中心',
  `MARKET_CHANNEL` varchar(20) DEFAULT NULL COMMENT '渠道',
  `STORE_CODE` varchar(20) DEFAULT NULL COMMENT '门店',
  `OFFICE` varchar(20) DEFAULT NULL COMMENT '办事处',
  `SOURCE_ORDER_LINE_ID` varchar(50) DEFAULT NULL COMMENT '来源明细ID',
  `ATTRIBUTE1` varchar(100) DEFAULT NULL COMMENT '扩展字段1',
  `ATTRIBUTE2` varchar(100) DEFAULT NULL COMMENT '扩展字段2',
  `ATTRIBUTE3` varchar(100) DEFAULT NULL COMMENT '扩展字段3',
  `ATTRIBUTE4` varchar(100) DEFAULT NULL COMMENT '扩展字段4',
  `ATTRIBUTE5` varchar(100) DEFAULT NULL COMMENT '扩展字段5',
  `ATTRIBUTE6` varchar(100) DEFAULT NULL COMMENT '扩展字段6',
  `ATTRIBUTE7` varchar(100) DEFAULT NULL COMMENT '扩展字段7',
  `ATTRIBUTE8` varchar(100) DEFAULT NULL COMMENT '扩展字段8',
  `ATTRIBUTE9` varchar(100) DEFAULT NULL COMMENT '扩展字段9',
  `ATTRIBUTE10` varchar(100) DEFAULT NULL COMMENT '扩展字段10',
  `is_deleted` bigint(20) DEFAULT '0' COMMENT '是否删除 0：否；1：是',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `created_date` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(50) DEFAULT NULL COMMENT '修改者',
  `modified_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='方瑞推送费用申请至ECS-申请行入表';
