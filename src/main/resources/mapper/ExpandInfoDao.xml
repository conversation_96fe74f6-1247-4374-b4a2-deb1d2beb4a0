<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.expand.dao.ExpandInfoDao">

    <resultMap type="com.fotile.cmscenter.expand.entity.ExpandInfo" id="ExpandInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="yardInfoId" column="yard_info_id" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="notice" column="notice" jdbcType="VARCHAR"/>
        <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
        <result property="squareUrl" column="square_url" jdbcType="VARCHAR"/>
        <result property="detailUrl" column="detail_url" jdbcType="VARCHAR"/>
        <result property="contentText" column="content_text" jdbcType="VARCHAR"/>
        <result property="learningText" column="learning_text" jdbcType="VARCHAR"/>
        <result property="courseMode" column="course_mode" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="priceType" column="price_type" jdbcType="INTEGER"/>
        <result property="optionalContentType" column="optional_content_type" jdbcType="INTEGER"/>
        <result property="optionalContentNum" column="optional_content_num" jdbcType="INTEGER"/>
        <result property="sellingPrice" column="selling_price" jdbcType="VARCHAR"/>
        <result property="linePrice" column="line_price" jdbcType="VARCHAR"/>
        <result property="markNum" column="mark_num" jdbcType="INTEGER"/>
        <result property="daysMarkNum" column="days_mark_num" jdbcType="INTEGER"/>
        <result property="sampleClock" column="sample_clock" jdbcType="VARCHAR"/>
        <result property="textRequire" column="text_require" jdbcType="INTEGER"/>
        <result property="pictureRequire" column="picture_require" jdbcType="INTEGER"/>
        <result property="isCashback" column="is_cashback" jdbcType="INTEGER"/>
        <result property="point" column="point" jdbcType="VARCHAR"/>
        <result property="onlineStatus" column="online_status" jdbcType="INTEGER"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="enrollment" column="enrollment" jdbcType="INTEGER"/>
        <result property="onlineTime" column="online_time" jdbcType="TIMESTAMP"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="challengeSuccessNum" column="challenge_success_num" jdbcType="INTEGER"/>
        <result property="challengeFailureNum" column="challenge_failure_num" jdbcType="INTEGER"/>
        <result property="signUpEndTime" column="sign_up_end_time" jdbcType="TIMESTAMP"/>
        <result property="takingClassesTime" column="taking_classes_time" jdbcType="TIMESTAMP"/>
        <result property="surveyUrl" column="survey_url" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ExpandInfoMap">
        select
            id, is_deleted, created_by, modified_by, created_date, modified_date, type, yard_info_id, code, title, summary,notice, cover_url, square_url, detail_url, content_text, learning_text, course_mode, start_time, end_time, price_type, optional_content_type, optional_content_num, selling_price, line_price, mark_num, days_mark_num, sample_clock, text_require, picture_require, is_cashback, point, online_status, enrollment, online_time, sort, challenge_success_num, challenge_failure_num, sign_up_end_time, taking_classes_time,survey_url
        from cmscenter.x_expand_info
        where id = #{id}
    </select>

    <!--分页查询-->
    <select id="queryAllByLimit" resultType="com.fotile.cmscenter.expand.dto.ExpandInfoListOutDto">
        select
        id, title, type, code, enrollment, sort, created_date createdDate, online_time onlineTime,online_status onlineStatus,
        challenge_success_num challengeSuccessNum,challenge_failure_num challengeFailureNum
        from cmscenter.x_expand_info
        <where>
             is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="title != null and title != ''">
                and title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="code != null and code != ''">
                and code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="startTime != null and endTime != null">
                and created_date between #{startTime} and #{endTime}
            </if>
            <if test="onlineStatus != null">
                and online_status = #{onlineStatus}
            </if>
        </where>
        order by sort desc,created_date desc
        limit #{offset}, #{size}
    </select>

    <!--分页查询统计总行数-->
    <select id="queryAllByCount" resultType="java.lang.Long">
        select count(1)
        from cmscenter.x_expand_info
        <where>
            and is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="title != null and title != ''">
                and title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="code != null and code != ''">
                and code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="startTime != null and endTime != null">
                and created_date between #{startTime} and #{endTime}
            </if>
            <if test="onlineStatus != null">
                and online_status = #{onlineStatus}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from cmscenter.x_expand_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="yardInfoId != null">
                and yard_info_id = #{yardInfoId}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="summary != null and summary != ''">
                and summary = #{summary}
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                and cover_url = #{coverUrl}
            </if>
            <if test="detailUrl != null and detailUrl != ''">
                and detail_url = #{detailUrl}
            </if>
            <if test="contentText != null and contentText != ''">
                and content_text = #{contentText}
            </if>
            <if test="learningText != null and learningText != ''">
                and learning_text = #{learningText}
            </if>
            <if test="courseMode != null">
                and course_mode = #{courseMode}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="priceType != null">
                and price_type = #{priceType}
            </if>
            <if test="optionalContentType != null">
                and optional_content_type = #{optionalContentType}
            </if>
            <if test="optionalContentNum != null">
                and optional_content_num = #{optionalContentNum}
            </if>
            <if test="sellingPrice != null">
                and selling_price = #{sellingPrice}
            </if>
            <if test="linePrice != null">
                and line_price = #{linePrice}
            </if>
            <if test="markNum != null">
                and mark_num = #{markNum}
            </if>
            <if test="daysMarkNum != null">
                and days_mark_num = #{daysMarkNum}
            </if>
            <if test="sampleClock != null and sampleClock != ''">
                and sample_clock = #{sampleClock}
            </if>
            <if test="contentType != null">
                and content_type = #{contentType}
            </if>
            <if test="contentRequire != null">
                and content_require = #{contentRequire}
            </if>
            <if test="point != null and point != ''">
                and point = #{point}
            </if>
            <if test="onlineStatus != null">
                and online_status = #{onlineStatus}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="enrollment != null">
                and enrollment = #{enrollment}
            </if>
            <if test="onlineTime != null">
                and online_time = #{onlineTime}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cmscenter.x_expand_info(is_deleted, created_by, modified_by, created_date, modified_date, type, yard_info_id, title, summary, notice, cover_url, square_url, detail_url, content_text, learning_text, course_mode, start_time, end_time, price_type, optional_content_type, optional_content_num, selling_price, line_price, mark_num, days_mark_num, sample_clock, text_require, picture_require, point, online_status, code, enrollment, online_time, sort,is_cashback,challenge_success_num, challenge_failure_num, sign_up_end_time, taking_classes_time, survey_url)
        values (0, #{createdBy}, #{modifiedBy}, now(), now(), #{type}, #{yardInfoId}, #{title}, #{summary}, #{notice}, #{coverUrl}, #{squareUrl}, #{detailUrl}, #{contentText}, #{learningText}, #{courseMode}, #{startTime}, #{endTime}, #{priceType}, #{optionalContentType}, #{optionalContentNum}, #{sellingPrice}, #{linePrice}, #{markNum}, #{daysMarkNum}, #{sampleClock}, #{textRequire}, #{pictureRequire}, #{point}, #{onlineStatus}, #{code}, #{enrollment}, now(), 1,#{isCashback},#{challengeSuccessNum},#{challengeFailureNum},#{signUpEndTime},#{takingClassesTime}, #{surveyUrl})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into cmscenter.x_expand_info(is_deleted, created_by, modified_by, created_date, modified_date, type, yard_info_id, title, summary, notice, cover_url, square_url, detail_url, content_text, learning_text, course_mode, start_time, end_time, price_type, optional_content_type, optional_content_num, selling_price, line_price, mark_num, days_mark_num, sample_clock, text_require, picture_require, point, online_status, code, enrollment, online_time, sort,is_cashback,challenge_success_num, challenge_failure_num, sign_up_end_time, taking_classes_time, survey_url)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.isDeleted}, #{entity.createdBy}, #{entity.modifiedBy}, #{entity.createdDate}, #{entity.modifiedDate}, #{entity.type}, #{entity.yardInfoId}, #{entity.title}, #{entity.summary}, #{entity.coverUrl}, #{entity.squareUrl}, #{entity.detailUrl}, #{entity.contentText}, #{entity.learningText}, #{entity.courseMode}, #{entity.startTime}, #{entity.endTime}, #{entity.priceType}, #{entity.optionalContentType}, #{entity.optionalContentNum}, #{entity.sellingPrice}, #{entity.linePrice}, #{entity.markNum}, #{entity.daysMarkNum}, #{entity.sampleClock}, #{entity.textRequire}, #{entity.pictureRequire}, #{entity.point}, #{entity.onlineStatus}, #{entity.code}, #{entity.enrollment}, #{entity.onlineTime}, #{entity.sort},#{entity.isCashback},#{entity.challengeSuccessNum},#{entity.challengeFailureNum},#{entity.signUpEndTime},#{entity.takingClassesTime}, #{entity.surveyUrl})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cmscenter.x_expand_info
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
                modified_date = now(),
            <if test="type != null">
                type = #{type},
            </if>
                yard_info_id = #{yardInfoId},
            <if test="title != null and title != ''">
                title = #{title},
            </if>
                summary = #{summary},
                notice = #{notice},
            <if test="coverUrl != null and coverUrl != ''">
                cover_url = #{coverUrl},
            </if>
                square_url = #{squareUrl},
                detail_url = #{detailUrl},
                content_text = #{contentText},
                learning_text = #{learningText},
            <if test="courseMode != null">
                course_mode = #{courseMode},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="priceType != null">
                price_type = #{priceType},
            </if>
            <if test="optionalContentType != null">
                optional_content_type = #{optionalContentType},
            </if>
                optional_content_num = #{optionalContentNum},
            <if test="sellingPrice != null">
                selling_price = #{sellingPrice},
            </if>
                line_price = #{linePrice},
                mark_num = #{markNum},
                days_mark_num = #{daysMarkNum},
                sample_clock = #{sampleClock},
                text_require = #{textRequire},
                picture_require = #{pictureRequire},
                point = #{point},
            <if test="onlineStatus != null">
                online_status = #{onlineStatus},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="isCashback != null">
                is_cashback = #{isCashback},
            </if>
            <if test="signUpEndTime != null">
                sign_up_end_time = #{signUpEndTime},
            </if>
            <if test="takingClassesTime != null">
                taking_classes_time = #{takingClassesTime},
            </if>
            survey_url = #{surveyUrl}
        </set>
        where id = #{id}
    </update>

    <update id="updateCode">
        update cmscenter.x_expand_info
        <set>
            <if test="code != null and code != ''">
                code = #{code}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键修改上线状态-->
    <update id="updateStatus">
        update cmscenter.x_expand_info
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            modified_date = now(),
            <if test="onlineStatus != null">
                online_status = #{onlineStatus},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cmscenter.x_expand_info where id = #{id}
    </delete>


    <select id="selectCountByIds" resultType="com.fotile.cmscenter.expand.dto.SelectCountDto">
        SELECT
	        e.id expandInfoId,count(l.id) progress
        FROM
            x_expand_info e
        LEFT JOIN x_course_catalog c on e.id=c.expand_info_id and c.is_deleted = 0
        LEFT JOIN x_learning_task l on c.id=l.course_catalog_id and l.is_deleted = 0
        WHERE
            e.is_deleted = 0
            and e.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            GROUP BY e.id
    </select>

    <!--根据IDs查询-->
    <select id="selectExpandByIds" resultType="com.fotile.cmscenter.channel.pojo.dto.ChannelExpandDto">
        select
        id, title, type, code, enrollment, sort, created_date createdDate, online_time onlineTime,online_status onlineStatus,
        challenge_success_num challengeSuccessNum,challenge_failure_num challengeFailureNum
        from cmscenter.x_expand_info
        <where>
            is_deleted = 0
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by sort desc,created_date desc
    </select>

    <!--幸福测试详情页面使用-->
    <select id="queryExpandList" resultType="com.fotile.cmscenter.happinessTest.pojo.dto.GetContentSpecialsListOutDto">
        select
        id, title, code, summary, cover_url coverUrl, square_url squareUrl
        from cmscenter.x_expand_info
        <where>
            is_deleted = 0 and online_status = 1
            <if test="keywords != null and keywords != ''">
                and (
                concat('',id) like concat('%',#{keywords},'%')
                or title like CONCAT('%',#{keywords},'%')
                or code LIKE CONCAT('%', #{keywords}, '%')
                )
            </if>
        </where>
        order by sort desc,created_date desc
        limit #{offset}, #{size}
    </select>

    <!--幸福测试详情页面使用-->
    <select id="queryExpandCount" resultType="java.lang.Integer">
        select count(1)
        from cmscenter.x_expand_info
        <where>
            is_deleted = 0 and online_status = 1
            <if test="keywords != null and keywords != ''">
                and (
                concat('',id) like concat('%',#{keywords},'%')
                or title like CONCAT('%',#{keywords},'%')
                or code LIKE CONCAT('%', #{keywords}, '%')
                )
            </if>
        </where>
    </select>

    <select id="queryByContentIds" resultType="com.fotile.cmscenter.happinessTest.pojo.dto.GetContentSpecialsListOutDto">
        select
        id, title, code, summary, cover_url coverUrl, square_url squareUrl
        from cmscenter.x_expand_info
        <where>
            is_deleted = 0 and online_status = 1
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCompoundInfoTitle" resultType="com.fotile.cmscenter.compound.pojo.dto.CompoundInfoTitleOutDto">
        SELECT
            id,
            title
        FROM
            x_expand_info
        WHERE
            is_deleted = 0
          and id  = #{id}
    </select>


</mapper>

