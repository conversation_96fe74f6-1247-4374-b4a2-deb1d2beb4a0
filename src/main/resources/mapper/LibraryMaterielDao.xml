<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.test.dao.LibraryMaterielDao">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.test.pojo.entity.LibraryMateriel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="department_id" jdbcType="INTEGER" property="departmentId" />
    <result column="thumb" jdbcType="VARCHAR" property="thumb" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="download_thumb_1" jdbcType="VARCHAR" property="downloadThumb1" />
    <result column="download_thumb_2" jdbcType="VARCHAR" property="downloadThumb2" />
    <result column="download_thumb_3" jdbcType="VARCHAR" property="downloadThumb3" />
    <result column="download_count" jdbcType="INTEGER" property="downloadCount" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="mode" jdbcType="INTEGER" property="mode" />
    <result column="expire_start_time" jdbcType="DATE" property="expireStartTime" />
    <result column="expire_end_time" jdbcType="DATE" property="expireEndTime" />
    <result column="is_lease" jdbcType="INTEGER" property="isLease" />
    <result column="release_version" jdbcType="INTEGER" property="releaseVersion" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="auth_info" jdbcType="VARCHAR" property="authInfo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielWithBLOBs">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="download_msg_1" jdbcType="LONGVARCHAR" property="downloadMsg1" />
    <result column="download_msg_2" jdbcType="LONGVARCHAR" property="downloadMsg2" />
    <result column="download_msg_3" jdbcType="LONGVARCHAR" property="downloadMsg3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, code, department_id, thumb, title, keywords, remark, download_thumb_1, 
    download_thumb_2, download_thumb_3, download_count, sort, `mode`, expire_start_time, 
    expire_end_time, is_lease, release_version, operator_id, created_at, updated_at, 
    `status`, auth_info
  </sql>
  <sql id="Blob_Column_List">
    description, download_msg_1, download_msg_2, download_msg_3
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from library_materiel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from library_materiel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from library_materiel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from library_materiel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielExample">
    delete from library_materiel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielWithBLOBs" useGeneratedKeys="true">
    insert into library_materiel (user_id, code, department_id, 
      thumb, title, keywords, 
      remark, download_thumb_1, download_thumb_2, 
      download_thumb_3, download_count, sort, 
      `mode`, expire_start_time, expire_end_time, 
      is_lease, release_version, operator_id, 
      created_at, updated_at, `status`, 
      auth_info, description, download_msg_1, 
      download_msg_2, download_msg_3)
    values (#{userId,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{departmentId,jdbcType=INTEGER}, 
      #{thumb,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{keywords,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{downloadThumb1,jdbcType=VARCHAR}, #{downloadThumb2,jdbcType=VARCHAR}, 
      #{downloadThumb3,jdbcType=VARCHAR}, #{downloadCount,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, 
      #{mode,jdbcType=INTEGER}, #{expireStartTime,jdbcType=DATE}, #{expireEndTime,jdbcType=DATE}, 
      #{isLease,jdbcType=INTEGER}, #{releaseVersion,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, 
      #{authInfo,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}, #{downloadMsg1,jdbcType=LONGVARCHAR}, 
      #{downloadMsg2,jdbcType=LONGVARCHAR}, #{downloadMsg3,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielWithBLOBs" useGeneratedKeys="true">
    insert into library_materiel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="thumb != null">
        thumb,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="keywords != null">
        keywords,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="downloadThumb1 != null">
        download_thumb_1,
      </if>
      <if test="downloadThumb2 != null">
        download_thumb_2,
      </if>
      <if test="downloadThumb3 != null">
        download_thumb_3,
      </if>
      <if test="downloadCount != null">
        download_count,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="mode != null">
        `mode`,
      </if>
      <if test="expireStartTime != null">
        expire_start_time,
      </if>
      <if test="expireEndTime != null">
        expire_end_time,
      </if>
      <if test="isLease != null">
        is_lease,
      </if>
      <if test="releaseVersion != null">
        release_version,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="authInfo != null">
        auth_info,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="downloadMsg1 != null">
        download_msg_1,
      </if>
      <if test="downloadMsg2 != null">
        download_msg_2,
      </if>
      <if test="downloadMsg3 != null">
        download_msg_3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="thumb != null">
        #{thumb,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb1 != null">
        #{downloadThumb1,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb2 != null">
        #{downloadThumb2,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb3 != null">
        #{downloadThumb3,jdbcType=VARCHAR},
      </if>
      <if test="downloadCount != null">
        #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        #{mode,jdbcType=INTEGER},
      </if>
      <if test="expireStartTime != null">
        #{expireStartTime,jdbcType=DATE},
      </if>
      <if test="expireEndTime != null">
        #{expireEndTime,jdbcType=DATE},
      </if>
      <if test="isLease != null">
        #{isLease,jdbcType=INTEGER},
      </if>
      <if test="releaseVersion != null">
        #{releaseVersion,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="authInfo != null">
        #{authInfo,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg1 != null">
        #{downloadMsg1,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg2 != null">
        #{downloadMsg2,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg3 != null">
        #{downloadMsg3,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielExample" resultType="java.lang.Long">
    select count(*) from library_materiel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update library_materiel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=INTEGER},
      </if>
      <if test="record.thumb != null">
        thumb = #{record.thumb,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.keywords != null">
        keywords = #{record.keywords,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadThumb1 != null">
        download_thumb_1 = #{record.downloadThumb1,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadThumb2 != null">
        download_thumb_2 = #{record.downloadThumb2,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadThumb3 != null">
        download_thumb_3 = #{record.downloadThumb3,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadCount != null">
        download_count = #{record.downloadCount,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.mode != null">
        `mode` = #{record.mode,jdbcType=INTEGER},
      </if>
      <if test="record.expireStartTime != null">
        expire_start_time = #{record.expireStartTime,jdbcType=DATE},
      </if>
      <if test="record.expireEndTime != null">
        expire_end_time = #{record.expireEndTime,jdbcType=DATE},
      </if>
      <if test="record.isLease != null">
        is_lease = #{record.isLease,jdbcType=INTEGER},
      </if>
      <if test="record.releaseVersion != null">
        release_version = #{record.releaseVersion,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.authInfo != null">
        auth_info = #{record.authInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.downloadMsg1 != null">
        download_msg_1 = #{record.downloadMsg1,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.downloadMsg2 != null">
        download_msg_2 = #{record.downloadMsg2,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.downloadMsg3 != null">
        download_msg_3 = #{record.downloadMsg3,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update library_materiel
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      code = #{record.code,jdbcType=VARCHAR},
      department_id = #{record.departmentId,jdbcType=INTEGER},
      thumb = #{record.thumb,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      download_thumb_1 = #{record.downloadThumb1,jdbcType=VARCHAR},
      download_thumb_2 = #{record.downloadThumb2,jdbcType=VARCHAR},
      download_thumb_3 = #{record.downloadThumb3,jdbcType=VARCHAR},
      download_count = #{record.downloadCount,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      `mode` = #{record.mode,jdbcType=INTEGER},
      expire_start_time = #{record.expireStartTime,jdbcType=DATE},
      expire_end_time = #{record.expireEndTime,jdbcType=DATE},
      is_lease = #{record.isLease,jdbcType=INTEGER},
      release_version = #{record.releaseVersion,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER},
      auth_info = #{record.authInfo,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR},
      download_msg_1 = #{record.downloadMsg1,jdbcType=LONGVARCHAR},
      download_msg_2 = #{record.downloadMsg2,jdbcType=LONGVARCHAR},
      download_msg_3 = #{record.downloadMsg3,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update library_materiel
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      code = #{record.code,jdbcType=VARCHAR},
      department_id = #{record.departmentId,jdbcType=INTEGER},
      thumb = #{record.thumb,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      download_thumb_1 = #{record.downloadThumb1,jdbcType=VARCHAR},
      download_thumb_2 = #{record.downloadThumb2,jdbcType=VARCHAR},
      download_thumb_3 = #{record.downloadThumb3,jdbcType=VARCHAR},
      download_count = #{record.downloadCount,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      `mode` = #{record.mode,jdbcType=INTEGER},
      expire_start_time = #{record.expireStartTime,jdbcType=DATE},
      expire_end_time = #{record.expireEndTime,jdbcType=DATE},
      is_lease = #{record.isLease,jdbcType=INTEGER},
      release_version = #{record.releaseVersion,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER},
      auth_info = #{record.authInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielWithBLOBs">
    update library_materiel
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="thumb != null">
        thumb = #{thumb,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        keywords = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb1 != null">
        download_thumb_1 = #{downloadThumb1,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb2 != null">
        download_thumb_2 = #{downloadThumb2,jdbcType=VARCHAR},
      </if>
      <if test="downloadThumb3 != null">
        download_thumb_3 = #{downloadThumb3,jdbcType=VARCHAR},
      </if>
      <if test="downloadCount != null">
        download_count = #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        `mode` = #{mode,jdbcType=INTEGER},
      </if>
      <if test="expireStartTime != null">
        expire_start_time = #{expireStartTime,jdbcType=DATE},
      </if>
      <if test="expireEndTime != null">
        expire_end_time = #{expireEndTime,jdbcType=DATE},
      </if>
      <if test="isLease != null">
        is_lease = #{isLease,jdbcType=INTEGER},
      </if>
      <if test="releaseVersion != null">
        release_version = #{releaseVersion,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="authInfo != null">
        auth_info = #{authInfo,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg1 != null">
        download_msg_1 = #{downloadMsg1,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg2 != null">
        download_msg_2 = #{downloadMsg2,jdbcType=LONGVARCHAR},
      </if>
      <if test="downloadMsg3 != null">
        download_msg_3 = #{downloadMsg3,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielWithBLOBs">
    update library_materiel
    set user_id = #{userId,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=INTEGER},
      thumb = #{thumb,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      keywords = #{keywords,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      download_thumb_1 = #{downloadThumb1,jdbcType=VARCHAR},
      download_thumb_2 = #{downloadThumb2,jdbcType=VARCHAR},
      download_thumb_3 = #{downloadThumb3,jdbcType=VARCHAR},
      download_count = #{downloadCount,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      `mode` = #{mode,jdbcType=INTEGER},
      expire_start_time = #{expireStartTime,jdbcType=DATE},
      expire_end_time = #{expireEndTime,jdbcType=DATE},
      is_lease = #{isLease,jdbcType=INTEGER},
      release_version = #{releaseVersion,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      auth_info = #{authInfo,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      download_msg_1 = #{downloadMsg1,jdbcType=LONGVARCHAR},
      download_msg_2 = #{downloadMsg2,jdbcType=LONGVARCHAR},
      download_msg_3 = #{downloadMsg3,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMateriel">
    update library_materiel
    set user_id = #{userId,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=INTEGER},
      thumb = #{thumb,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      keywords = #{keywords,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      download_thumb_1 = #{downloadThumb1,jdbcType=VARCHAR},
      download_thumb_2 = #{downloadThumb2,jdbcType=VARCHAR},
      download_thumb_3 = #{downloadThumb3,jdbcType=VARCHAR},
      download_count = #{downloadCount,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      `mode` = #{mode,jdbcType=INTEGER},
      expire_start_time = #{expireStartTime,jdbcType=DATE},
      expire_end_time = #{expireEndTime,jdbcType=DATE},
      is_lease = #{isLease,jdbcType=INTEGER},
      release_version = #{releaseVersion,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      auth_info = #{authInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectMaterielList" resultType="com.fotile.resourcescenter.test.pojo.entity.MaterielVO">
    SELECT
        m.id, r.new_id user_id, code, nr.new_id department_id, thumb, title, keywords, remark, download_thumb_1,
    download_thumb_2, download_thumb_3, download_count, m.sort, `mode`, if(expire_start_time =0,null ,expire_start_time) expire_start_time,
    if(expire_end_time =0,null,expire_end_time) expire_end_time, is_lease, release_version, m.operator_id, m.created_at, m.updated_at,
    m.`status`, auth_info, u.name userName, d.name departmentName
    FROM
        library_materiel m
    LEFT JOIN user_new_recording r ON m.user_id = r.user_id
    left join department_new_recording nr on nr.department_id = m.department_id
	left join library_materiel_new_recording lm on lm.materiel_id = m.id
	left join user u on u.id = m.user_id
	left join department d on d.id = m.department_id
	where lm.id is null
	limit 1000
  </select>
  <select id="selectMaterielGroupList" resultType="com.fotile.resourcescenter.test.pojo.entity.MaterielListVO">
    SELECT
        l.id,
        d.new_id department_id,
        t.name departmentName,
        u.new_id user_id,
        user.name userName,
        l.title,
        l.description,
        l.sort,
        l.is_top,
        l.operator_id,
        l.created_at,
        l.updated_at,
        l.`status`
    FROM
        library_materiel_list l
    LEFT JOIN department_new_recording d ON d.department_id = l.department_id
    LEFT JOIN user_new_recording u ON u.user_id = l.user_id
    left join user user on user.id = l.user_id
    left join t_org t on t.id = d.new_id
    left join library_materiel_list_new_recording ln on ln.materiel_id = l.id
    WHERE
        ln.new_id IS  NULL
  </select>

  <select id="selectMaterielGroupItem" resultType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielListItem">
    SELECT
        item.id,
        item.materiel_list_id,
        mn.new_id materiel_id,
        item.sort,
        item.operator_id,
        item.created_at,
        item.updated_at,
        item.status
    FROM
        library_materiel_list_item item
    left join library_materiel_new_recording mn on mn.materiel_id = item.materiel_id
    WHERE
        materiel_list_id = #{materielListId}

  </select>
</mapper>