<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.materiel.dao.MaterielYearSummarizationMapper">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.materiel.pojo.entity.MaterielYearSummarizationEntity">
    <!--@mbg.generated-->
    <!--@Table `resourcescenter`.`materiel_year_summarization`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="usage_type" jdbcType="TINYINT" property="usageType" />
    <result column="num" jdbcType="INTEGER" property="num" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, 
    `date`, `usage_type`, `num`
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `resourcescenter`.`materiel_year_summarization`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.date != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.date,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`num` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_year_summarization`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `date`, 
      `usage_type`, `num`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.date,jdbcType=VARCHAR}, 
        #{item.usageType,jdbcType=TINYINT}, #{item.num,jdbcType=INTEGER})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from `resourcescenter`.`materiel_year_summarization` where `id` in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update `resourcescenter`.`materiel_year_summarization`
      <set>
        <if test="item.isDeleted != null">
          `is_deleted` = #{item.isDeleted,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null and item.createdBy != ''">
          `created_by` = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdDate != null">
          `created_date` = #{item.createdDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifiedBy != null and item.modifiedBy != ''">
          `modified_by` = #{item.modifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.modifiedDate != null">
          `modified_date` = #{item.modifiedDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.date != null and item.date != ''">
          `date` = #{item.date,jdbcType=VARCHAR},
        </if>
        <if test="item.usageType != null">
          `usage_type` = #{item.usageType,jdbcType=TINYINT},
        </if>
        <if test="item.num != null">
          `num` = #{item.num,jdbcType=INTEGER},
        </if>
      </set>
      where `id` = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_year_summarization`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `date`, 
      `usage_type`, `num`)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.isDeleted != null">
          #{item.isDeleted,jdbcType=BIGINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdDate != null">
          #{item.createdDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.modifiedBy != null">
          #{item.modifiedBy,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.modifiedDate != null">
          #{item.modifiedDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.date != null">
          #{item.date,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageType != null">
          #{item.usageType,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.num != null">
          #{item.num,jdbcType=INTEGER}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materiel.pojo.entity.MaterielYearSummarizationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_year_summarization`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `date`,
      `usage_type`,
      `num`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{date,jdbcType=VARCHAR},
      #{usageType,jdbcType=TINYINT},
      #{num,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `date` = #{date,jdbcType=VARCHAR},
      `usage_type` = #{usageType,jdbcType=TINYINT},
      `num` = #{num,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materiel.pojo.entity.MaterielYearSummarizationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_year_summarization`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="date != null and date != ''">
        `date`,
      </if>
      <if test="usageType != null">
        `usage_type`,
      </if>
      <if test="num != null">
        `num`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="date != null and date != ''">
        #{date,jdbcType=VARCHAR},
      </if>
      <if test="usageType != null">
        #{usageType,jdbcType=TINYINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="date != null and date != ''">
        `date` = #{date,jdbcType=VARCHAR},
      </if>
      <if test="usageType != null">
        `usage_type` = #{usageType,jdbcType=TINYINT},
      </if>
      <if test="num != null">
        `num` = #{num,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>