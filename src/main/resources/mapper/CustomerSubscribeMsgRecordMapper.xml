<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.customercenter.fission.dao.CustomerSubscribeMsgRecordMapper" >
  <resultMap id="BaseResultMap" type="com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="customer_id" property="customerId" jdbcType="BIGINT" />
    <result column="source_table" property="sourceTable" jdbcType="VARCHAR" />
    <result column="source_id" property="sourceId" jdbcType="BIGINT" />
    <result column="company_id" property="companyId" jdbcType="BIGINT" />
    <result column="store_id" property="storeId" jdbcType="BIGINT" />
    <result column="remind_flag" property="remindFlag" jdbcType="TINYINT" />
    <result column="is_deleted" property="isDeleted" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, customer_id, source_table, source_id, company_id, store_id, remind_flag, is_deleted, 
    created_by, created_date, modified_by, modified_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from customer_subscribe_msg_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from customer_subscribe_msg_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord" >
    insert into customer_subscribe_msg_record ( customer_id, source_table,
      source_id, company_id, store_id, 
      is_deleted, created_by, created_date
      )
    values ( #{customerId,jdbcType=BIGINT}, #{sourceTable,jdbcType=VARCHAR},
      #{sourceId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      0, #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord" >
    insert into customer_subscribe_msg_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="customerId != null" >
        customer_id,
      </if>
      <if test="sourceTable != null" >
        source_table,
      </if>
      <if test="sourceId != null" >
        source_id,
      </if>
      <if test="companyId != null" >
        company_id,
      </if>
      <if test="storeId != null" >
        store_id,
      </if>
      <if test="remindFlag != null" >
        remind_flag,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="customerId != null" >
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="sourceTable != null" >
        #{sourceTable,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="remindFlag != null" >
        #{remindFlag,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord" >
    update customer_subscribe_msg_record
    <set >
      <if test="customerId != null" >
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="sourceTable != null" >
        source_table = #{sourceTable,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null" >
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="remindFlag != null" >
        remind_flag = #{remindFlag,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord" >
    update customer_subscribe_msg_record
    set customer_id = #{customerId,jdbcType=BIGINT},
      source_table = #{sourceTable,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      remind_flag = #{remindFlag,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCustomerAndStoreId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from customer_subscribe_msg_record
    where is_deleted = 0 and source_table = 'cmscenter.im_content_template'
    and customer_id = #{customerId}
    and store_id = #{storeId}
    order by id asc limit 1
  </select>

  <select id="queryNeedSubscribe" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    min(id), customer_id, source_table, source_id, company_id, store_id, remind_flag
    from customer_subscribe_msg_record
    where is_deleted = 0 and source_table = 'cmscenter.im_content_template'
    and company_id in
    <foreach collection="companyOrgIds" item="companyId" separator="," open="(" close=")">
        #{companyId}
    </foreach>
    group by customer_id
  </select>
</mapper>