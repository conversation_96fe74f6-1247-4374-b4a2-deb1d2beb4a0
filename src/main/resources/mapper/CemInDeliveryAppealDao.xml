<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.dep.provider.cem.dao.CemInDeliveryAppealDao">
    <sql id="Base_Column_List">
        batch_no,
        distribute_no,
        SRId,
        FollowOrganization,
        ContactId,
        SRNumber,
        SRType,
        `Source`,
        `Status`,
        FTCallerName,
        FTCallerPhone,
        Severity,
        FTUrgentSRProcessStatus,
        FTEmergencyHander,
        FTExpectTime,
        FTFirstAppointmentTime,
        FTLastAppointmentTime,
        FTSRCancelReason,
        FTReminderType,
        FTCollectionContact,
        FTCollectionResult,
        FTCollectionNum,
        Description,
        FTIFDeliveryInstall,
        FTRelatedSRNumber,
        FTReminderCount,
        Created,
        FTProvince,
        FTCity,
        DistrictCode,
        FTAddr,
        created_by,
        modified_by,
        created_date,
        modified_date,
        id,
        is_deleted
    </sql>

    <resultMap id="BaseResultMap"
               type="com.fotile.dep.provider.cem.pojo.entity.CemInDeliveryAppeal">
        <result column="batch_no" property="batchNo"/>
        <result column="distribute_no" property="distributeNo"/>
        <result column="SRId" property="SRId"/>
        <result column="FollowOrganization" property="FollowOrganization"/>
        <result column="ContactId" property="ContactId"/>
        <result column="SRNumber" property="SRNumber"/>
        <result column="SRType" property="SRType"/>
        <result column="Source" property="Source"/>
        <result column="Status" property="Status"/>
        <result column="FTCallerName" property="FTCallerName"/>
        <result column="FTCallerPhone" property="FTCallerPhone"/>
        <result column="Severity" property="Severity"/>
        <result column="FTUrgentSRProcessStatus" property="FTUrgentSRProcessStatus"/>
        <result column="FTEmergencyHander" property="FTEmergencyHander"/>
        <result column="FTExpectTime" property="FTExpectTime"/>
        <result column="FTFirstAppointmentTime" property="FTFirstAppointmentTime"/>
        <result column="FTLastAppointmentTime" property="FTLastAppointmentTime"/>
        <result column="FTSRCancelReason" property="FTSRCancelReason"/>
        <result column="FTReminderType" property="FTReminderType"/>
        <result column="FTCollectionContact" property="FTCollectionContact"/>
        <result column="FTCollectionResult" property="FTCollectionResult"/>
        <result column="FTCollectionNum" property="FTCollectionNum"/>
        <result column="Description" property="Description"/>
        <result column="FTIFDeliveryInstall" property="FTIFDeliveryInstall"/>
        <result column="FTRelatedSRNumber" property="FTRelatedSRNumber"/>
        <result column="FTReminderCount" property="FTReminderCount"/>
        <result column="Created" property="Created"/>
        <result column="FTProvince" property="FTProvince"/>
        <result column="FTCity" property="FTCity"/>
        <result column="DistrictCode" property="DistrictCode"/>
        <result column="FTAddr" property="FTAddr"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="findByBatchNoAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from depcenter.cem_in_delivery_appeal
        where batch_no = #{batchNo}
          and is_deleted = #{isDeleted}
    </select>
</mapper>