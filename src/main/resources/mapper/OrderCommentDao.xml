<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.shopordercenter.order.dao.OrderCommentDao">
    <resultMap id="BaseResultMap" type="com.fotile.shopordercenter.order.pojo.entity.OrderComment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="goods_model" jdbcType="VARCHAR" property="goodsModel"/>
        <result column="goods_image" jdbcType="VARCHAR" property="goodsImage"/>
        <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount"/>
        <result column="specification_attributes" jdbcType="VARCHAR" property="specificationAttributes"/>
        <result column="comment_time" jdbcType="TIMESTAMP" property="commentTime"/>
        <result column="comment_user_id" jdbcType="BIGINT" property="commentUserId"/>
        <result column="score" jdbcType="VARCHAR" property="score"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="video" jdbcType="VARCHAR" property="video"/>
        <result column="review_id" jdbcType="BIGINT" property="reviewId"/>
        <result column="is_review" jdbcType="BIGINT" property="isReview"/>
        <result column="audit_status" jdbcType="BIGINT" property="auditStatus"/>
        <result column="is_reply" jdbcType="BIGINT" property="isReply"/>
        <result column="reply_content" jdbcType="VARCHAR" property="replyContent"/>
        <result column="reply_time" jdbcType="TIMESTAMP" property="replyTime"/>
        <result column="created_by_name" jdbcType="VARCHAR" property="createdByName"/>
        <result column="modified_by_name" jdbcType="VARCHAR" property="modifiedByName"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="is_hide" jdbcType="BIGINT" property="isHide"/>
        <result column="is_recommend" jdbcType="BIGINT" property="isRecommend"/>
        <result column="is_can_after_review" jdbcType="BIGINT" property="isCanAfterReview"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, order_no, order_item_id, goods_code,goods_image,paid_amount,specification_attributes, goods_name, goods_model, comment_time, comment_user_id,
    score, content, image, video, is_review, audit_status, is_reply, reply_content, reply_time, 
    created_by_name, modified_by_name, is_deleted, created_by, created_date, modified_by, 
    modified_date,nick_name,head_portrait,grade_id,is_hide,is_recommend,goods_type,goods_category_name,is_can_after_review
  </sql>

    <sql id="commentTime_formatting">
        ,(
        CASE
        WHEN (
        CONVERT (
        substring(
        timediff(now(), comment_time),
        1,
        5
        ),
        UNSIGNED INTEGER
        ) > 24 = 0
        ) THEN
        CONCAT(
        CONVERT (
        substring(
        timediff(now(), comment_time),
        1,
        3
        ),
        UNSIGNED INTEGER
        ),
        '小时前'
        )
        WHEN (
        CONVERT (
        TIMESTAMPDIFF(DAY, comment_time, now()),
        UNSIGNED INTEGER
        ) > 365 = 0
        ) THEN
        CONCAT(
        CONVERT (
        TIMESTAMPDIFF(DAY, comment_time, now()),
        UNSIGNED INTEGER
        ),
        '天前'
        )
        WHEN (
        CONVERT (
        TIMESTAMPDIFF(YEAR, comment_time, now()),
        UNSIGNED INTEGER
        ) > 0
        ) THEN
        CONCAT(
        CONVERT (
        TIMESTAMPDIFF(YEAR, comment_time, now()),
        UNSIGNED INTEGER
        ),
        '年前'
        )
        ELSE
        ''
        END
        ) AS 'commentTimeFormatting',
        (CASE  grade_id  WHEN '2' THEN '白银会员' WHEN '3' THEN '黄金会员' WHEN '4' THEN '钻石会员' ELSE '普通会员' END) as gradeName
    </sql>
    <select id="selectAll" resultType="com.fotile.shopordercenter.order.pojo.dto.OrderCommentOutDto">
        select
        <include refid="Base_Column_List"/>
        from order_comment
        where is_deleted = 0
        <if test="orderCommentInDto.commentType != null ">and comment_type = #{orderCommentInDto.commentType}</if>
        <if test="orderCommentInDto.orderNo != null and orderCommentInDto.orderNo != ''">and order_no = #{orderCommentInDto.orderNo}</if>

        <if test="orderCommentInDto.commentUserId != null ">and comment_user_id = #{orderCommentInDto.commentUserId}
        </if>
        <if test="orderCommentInDto.goodsCode != null and orderCommentInDto.goodsCode != '' ">and goods_code = #{orderCommentInDto.goodsCode}</if>
        <if test="orderCommentInDto.image != null ">and image is not null</if>

        <if test="orderCommentInDto.auditStatus != null ">and audit_status = #{orderCommentInDto.auditStatus}</if>
        <if test="orderCommentInDto.content != null and orderCommentInDto.content != ''">and content like CONCAT('%',#{orderCommentInDto.content},'%')</if>
        <if test="orderCommentInDto.isReview != null ">and is_review = #{orderCommentInDto.isReview}</if>
        <if test="orderCommentInDto.isReply != null ">and is_reply = #{orderCommentInDto.isReply}</if>
        <if test="orderCommentInDto.commentStartTime != null ">and comment_time >=
            #{orderCommentInDto.commentStartTime}
        </if>
        <if test="orderCommentInDto.commentEndTime != null ">and #{orderCommentInDto.commentEndTime} >= comment_time
        </if>
        ORDER BY
        comment_time DESC
        limit #{orderCommentInDto.page},#{orderCommentInDto.size}
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(1)
        from order_comment
        where is_deleted = 0
        <if test="orderCommentInDto.commentType != null ">and comment_type = #{orderCommentInDto.commentType}</if>
        <if test="orderCommentInDto.orderNo != null and orderCommentInDto.orderNo != ''">and order_no = #{orderCommentInDto.orderNo}</if>
        <if test="orderCommentInDto.commentUserId != null ">and comment_user_id = #{orderCommentInDto.commentUserId}
        </if>
        <if test="orderCommentInDto.goodsCode != null and orderCommentInDto.goodsCode != '' ">and goods_code = #{orderCommentInDto.goodsCode}</if>
        <if test="orderCommentInDto.image != null ">and image is not null</if>
        <if test="orderCommentInDto.auditStatus != null ">and audit_status = #{orderCommentInDto.auditStatus}</if>
        <if test="orderCommentInDto.content != null and orderCommentInDto.content != ''">and content like CONCAT('%',#{orderCommentInDto.content},'%')</if>
        <if test="orderCommentInDto.isReview != null ">and is_review = #{orderCommentInDto.isReview}</if>
        <if test="orderCommentInDto.isReply != null ">and is_reply = #{orderCommentInDto.isReply}</if>
        <if test="orderCommentInDto.commentStartTime != null ">and comment_time >=
            #{orderCommentInDto.commentStartTime}
        </if>
        <if test="orderCommentInDto.commentEndTime != null ">and #{orderCommentInDto.commentEndTime} >= comment_time
        </if>
    </select>

    <select id="selectAppraiseCount" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            goods_order_item goi
        LEFT JOIN order_record o ON o.order_no = goi.order_no
        WHERE
            goi.is_deleted = 0
        AND goi.sub_goods_type != 2 and goi.sub_goods_type is not null
        AND goi.order_item_status = 5 AND o.buy_user_id = #{orderCommentInDto.commentUserId}
        AND goi.goods_code NOT IN (
            SELECT
                goods_code
            FROM
                order_comment
            WHERE
                goods_code = goi.goods_code
        )
    </select>

    <select id="selectAppraiseList" resultType="com.fotile.shopordercenter.order.pojo.dto.ReAppraiseOrderVO">
        SELECT
            goi.id as orderItemId,
            goi.goods_code as goodsCode,
            goi.goods_name as goodsName,
            goi.goods_image as goodsImage,
            goi.paid_amount as paidAmount,
            goi.specification_attributes as specificationAttributes,
            o.order_no AS orderNo

        FROM
            goods_order_item goi
        LEFT JOIN order_record o ON o.order_no = goi.order_no
        WHERE
            goi.is_deleted = 0
        AND goi.sub_goods_type != 2 and goi.sub_goods_type is not null
        AND goi.order_item_status = 5 AND o.buy_user_id = #{orderCommentInDto.commentUserId}
        AND goi.goods_code NOT IN (
            SELECT
                goods_code
            FROM
                order_comment
            WHERE
                goods_code = goi.goods_code AND order_item_id = goi.id
        )
        AND goi.id NOT IN (
            SELECT
                id
            FROM
                goods_order_item
            WHERE
                is_deleted = 0
            AND sub_goods_type = 1
            AND parent_item_id IS NOT NULL
        )
        order by o.create_order_time DESC,o.order_no DESC
    </select>

    <select id="selectIsReviewById" resultType="com.fotile.shopordercenter.order.pojo.dto.OrderCommentOutDto">
        select
        <include refid="Base_Column_List"/>
        <include refid="commentTime_formatting"/>
        from order_comment
        where is_deleted = 0
        <if test="flag == 1">and (audit_status = 2 and is_hide = 0
            <if test="userId != null">
                or (audit_status = 1 and comment_user_id = #{userId})
            </if>
            )
        </if>
        and is_review = 1 and review_id = #{id}
    </select>

    <select id="selectAllById" resultType="com.fotile.shopordercenter.order.pojo.dto.OrderCommentOutDto">
        select
        <include refid="Base_Column_List"/>
        from order_comment
        where is_deleted = 0 and id = #{id}
    </select>

    <select id="queryCommentList" resultType="com.fotile.shopordercenter.order.pojo.dto.OrderCommentOutDto">
        select
        <include refid="Base_Column_List"/>
        <include refid="commentTime_formatting"/>
        from order_comment
        where is_deleted = 0 and is_review = 0 and content != '系统默认好评'
        <if test="queryCommentInDto.isReview != null and queryCommentInDto.isReview == 0">
            AND id NOT IN (
            SELECT
            review_id
            FROM
            order_comment
            WHERE
            is_review = 1
            )
        </if>
        <if test="queryCommentInDto.isReview != null and queryCommentInDto.isReview == 1">
            AND
            id IN (
            SELECT
            review_id
            FROM
            order_comment
            WHERE
            is_review = 1
            <choose>
                <when test="queryCommentInDto.isLogin == 1">
                    <choose>
                        <when test="queryCommentInDto.goodsCode != null">AND (audit_status = 2 and is_hide = 0 OR
                            (audit_status = 1  AND comment_user_id = #{queryCommentInDto.commentUserId}))
                        </when>
                        <otherwise>AND comment_user_id = #{queryCommentInDto.commentUserId}</otherwise>
                    </choose>
                </when>
                <otherwise>AND audit_status = 2 and is_hide = 0</otherwise>
            </choose>
            )

        </if>
        <if test="queryCommentInDto.goodsCode != null">
            AND goods_code = #{queryCommentInDto.goodsCode}
        </if>
        <choose>
            <when test="queryCommentInDto.isLogin == 1">
                <choose>
                    <when test="queryCommentInDto.goodsCode != null">AND (audit_status = 2 and is_hide = 0 OR (audit_status = 1 AND
                        comment_user_id = #{queryCommentInDto.commentUserId}))
                    </when>
                    <otherwise>AND comment_user_id = #{queryCommentInDto.commentUserId} </otherwise>
                </choose>
            </when>
            <otherwise>AND audit_status = 2 and is_hide = 0</otherwise>
        </choose>
        <if test="queryCommentInDto.image != null">
            <choose>
                <when test="queryCommentInDto.image == 1">
                    AND image is not null and image != ''
                </when>
                <otherwise>
                    AND image is null or image = ''
                </otherwise>
            </choose>
        </if>
        order by is_recommend DESC,comment_time DESC
        limit #{queryCommentInDto.page},#{queryCommentInDto.size}
    </select>
    <select id="queryCommentCount" resultType="java.lang.Long">
        select count(1)
        from order_comment
        where is_deleted = 0 and is_review = 0 and content != '系统默认好评'
        <if test="queryCommentInDto.isReview != null and queryCommentInDto.isReview == 0">
            AND id NOT IN (
            SELECT
            review_id
            FROM
            order_comment
            WHERE
            is_review = 1
            )
        </if>
        <if test="queryCommentInDto.isReview != null and queryCommentInDto.isReview == 1">
            AND
            id IN (
            SELECT
            review_id
            FROM
            order_comment
            WHERE
            is_review = 1
            <choose>
                <when test="queryCommentInDto.isLogin == 1">
                    <choose>
                        <when test="queryCommentInDto.goodsCode != null">AND (audit_status = 2 and is_hide = 0 OR
                            (audit_status = 1  AND comment_user_id = #{queryCommentInDto.commentUserId}))
                        </when>
                        <otherwise>AND comment_user_id = #{queryCommentInDto.commentUserId} </otherwise>
                    </choose>
                </when>
                <otherwise>AND audit_status = 2 and is_hide = 0</otherwise>
            </choose>
            )

        </if>
        <if test="queryCommentInDto.goodsCode != null">
            AND goods_code = #{queryCommentInDto.goodsCode}
        </if>
        <choose>
            <when test="queryCommentInDto.isLogin == 1">
                <choose>
                    <when test="queryCommentInDto.goodsCode != null">AND (audit_status = 2 and is_hide = 0 OR (audit_status = 1  AND
                        comment_user_id = #{queryCommentInDto.commentUserId}))
                    </when>
                    <otherwise>AND comment_user_id = #{queryCommentInDto.commentUserId} </otherwise>
                </choose>
            </when>
            <otherwise>AND audit_status = 2 and is_hide = 0</otherwise>
        </choose>
        <if test="queryCommentInDto.image != null">
            <choose>
                <when test="queryCommentInDto.image == 1">
                    AND image is not null
                </when>
                <otherwise>
                    AND image is null
                </otherwise>
            </choose>
        </if>
        order by is_recommend DESC,comment_time DESC
    </select>

    <select id="queryComment" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        order_comment
        <where>
            is_deleted = 0
            and content != '系统默认好评'
            <if test="goodsCode != null and goodsCode != ''">
                AND goods_code = #{goodsCode}
            </if>
            AND (audit_status = 2 AND is_hide = 0
            <if test="userId != null">
                OR (audit_status = 1  AND comment_user_id = #{userId})
            </if>
            )
        </where>
    </select>
    <select id="selectCountByOrderItemId" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        order_comment
        WHERE
        order_item_id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_review = 0 AND is_deleted = 0
    </select>

    <select id="findReviewTotal" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        order_comment
        WHERE
        is_deleted = 0
        and is_review = 1
        AND review_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryAlreadyCommentedByItemIds" resultType="long">
        SELECT
            count(1)
        FROM
            `order_comment`
        WHERE
            is_deleted = 0
        AND is_can_after_review = 0
        AND order_item_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>