<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.family.dao.FamilyDao">
    <resultMap id="BaseResultMap" type="com.fotile.customercenter.family.pojo.dto.out.FamilyDetailOutDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="created_name" jdbcType="VARCHAR" property="createdName"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <collection property="familyMemberDto" ofType="com.fotile.customercenter.family.pojo.dto.FamilyMemberDto">
            <id column="membersId" jdbcType="BIGINT" property="membersId"/>
            <result column="family_name" jdbcType="VARCHAR" property="familyName"/>
            <result column="memberCustomerId" jdbcType="BIGINT" property="memberCustomerId"/>
            <result column="memberPhone" jdbcType="VARCHAR" property="memberPhone"/>
            <result column="join_family_date" jdbcType="TIMESTAMP" property="joinFamilyDate"/>
            <result column="member" jdbcType="VARCHAR" property="member" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
            <association property="customerInfoDto"
                         javaType="com.fotile.customercenter.family.pojo.dto.CustomerNameDto">
                <result column="name" property="name" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
            </association>
        </collection>
        <collection property="familyMemorialDayDto"
                    ofType="com.fotile.customercenter.family.pojo.dto.FamilyMemorialDayDto">
            <id column="memorialDayId" jdbcType="BIGINT" property="memorialDayId"/>
            <result column="memorial_name" jdbcType="VARCHAR" property="memorialName"/>
            <result column="memorial_date" jdbcType="TIMESTAMP" property="memorialDate"/>
            <result column="memorialCreatedBy" jdbcType="VARCHAR" property="memorialCreatedBy" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
            <result column="creatMemoriaDay" jdbcType="TIMESTAMP" property="creatMemoriaDay"/>
            <collection property="memberName" ofType="com.fotile.customercenter.family.pojo.dto.MemberName">
                <result column="member" jdbcType="VARCHAR" property="aboutMember" typeHandler="com.fotile.framework.data.secure.util.AESTypeHandler"/>
            </collection>
        </collection>

    </resultMap>
    <resultMap id="res" type="com.fotile.customercenter.family.pojo.dto.FamilyMemorialDayDto">
        <id column="memorialDayId" jdbcType="BIGINT" property="memorialDayId"/>
        <result column="memorial_name" jdbcType="VARCHAR" property="memorialName"/>
        <result column="memorial_date" jdbcType="TIMESTAMP" property="memorialDate"/>
        <result column="memorialCreatedBy" jdbcType="VARCHAR" property="memorialCreatedBy"/>
        <result column="creatMemoriaDay" jdbcType="TIMESTAMP" property="creatMemoriaDay"/>
        <collection property="memberName" ofType="com.fotile.customercenter.family.pojo.dto.MemberName">
            <result column="member" jdbcType="VARCHAR" property="aboutMember"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
    id, customer_id, user_entity_id, phone, nickname, is_deleted, created_name, created_person, 
    created_by, created_date, modified_by, modified_date
  </sql>
    <select id="selectFamilyList" resultType="com.fotile.customercenter.family.pojo.dto.out.FamilyListOutDto">
        SELECT
        f.id,
        f.nickname,
        f.phone,
        f.customer_id,
        f.created_date,
        count(t.id) as familyMemberNumber
        FROM family_information f
        LEFT JOIN family_members t
        on f.id=t.family_id
        <where>
            f.is_deleted = 0 and t.is_deleted = 0
            <if test="id != null">
                and f.id = #{id}
            </if>
            <if test="nickname != null and nickname != '' ">
                and f.nickname = #{nickname,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="phone != null and phone != '' ">
                and f.phone = #{phone,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="customerId != null ">
                and f.customer_id = #{customerId}
            </if>
            <if test="createdStart != null ">
                and f.created_date <![CDATA[ >= ]]> #{createdStart}
            </if>
            <if test="createdEnd != null ">
                and f.created_date <![CDATA[ <= ]]> #{createdEnd}
            </if>
        </where>
        GROUP BY f.id
        limit #{offset},#{size}
    </select>

    <!-- 分页查询家庭信息count查询 -->
    <select id="selectPropertyCount" resultType="java.lang.Long">
        SELECT
        count(1) from (
            select f.id FROM family_information f
            LEFT JOIN family_members t
            on f.id=t.family_id
            <where>
                f.is_deleted = 0 and t.is_deleted = 0
                <if test="id != null">
                    and f.id = #{id}
                </if>
                <if test="nickname != null and nickname != '' ">
                    and f.nickname like'%${nickname}%'
                </if>
                <if test="phone != null and phone != '' ">
                    and f.phone like '%${phone}%'
                </if>
                <if test="customerId != null ">
                    and f.customer_id = #{customerId}
                </if>
                <if test="createdStart != null ">
                    and f.created_date <![CDATA[ >= ]]> #{createdStart}
                </if>
                <if test="createdEnd != null ">
                    and f.created_date <![CDATA[ <= ]]> #{createdEnd}
                </if>
            </where>
            GROUP BY f.id)a
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
      a.id,
      a.customer_id,
      a.phone,
      a.nickname,
      a.created_name,
      a.created_date,
      b.family_id,
      b.family_name,
      b.id membersId,
      b.customer_id memberCustomerId,
      b.phone memberPhone,
      e.name name,
      b.nickname member,
      b.join_family_date
     from family_information a
    left join family_members b on  b.is_deleted = 0 and a.id = b.family_id
    left join customer_info e on e.is_deleted = 0  and b.customer_id = e.id
    where a.is_deleted=0 and a.id= #{id}
  </select>
    <select id="selectByFamilyId" parameterType="java.lang.Long" resultMap="res">
    select
      b.nickname member,
      c.id memorialDayId,
      c.memorial_date,
      c.memorial_name,
      c.created_date creatMemoriaDay,
      c.created_name memorialCreatedBy
     from family_memorial_day c
    left join  family_memorial_day_mapping a on  a.is_deleted = 0 and a.memorial_day_id = c.id
    left join family_members b on b.is_deleted = 0  and b.id = a.members_id
    where c.is_deleted=0 and c.family_id= #{id}
  </select>
</mapper>