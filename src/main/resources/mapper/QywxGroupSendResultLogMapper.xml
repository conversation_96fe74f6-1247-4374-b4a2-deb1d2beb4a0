<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.groupmsg.dao.QywxGroupSendResultLogMapper">
  <resultMap id="BaseResultMap" type="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="group_msg_id" jdbcType="BIGINT" property="groupMsgId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="qywx_msg_id" jdbcType="BIGINT" property="qywxMsgId" />
    <result column="external_userid" jdbcType="VARCHAR" property="externalUserid" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  
	  	  	      	id,
       	  	      	group_msg_id,
       	  	      	user_id,
       	  	      	qywx_msg_id,
       	  	      	external_userid,
       	  	      	status,
       	  	      	send_time,
       	  	      	is_deleted,
       	  	      	created_by,
       	  	      	created_date,
       	  	      	modified_by,
       	  	    	  	modified_date
       	  	
  </sql>
  

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_qywx_group_send_result_log
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectAllBySendSuccessCount"
            resultType="com.fotile.customercenter.groupmsg.pojo.entity.SendResultLogDto">
      select group_msg_id, count(1) as sendCount
      from t_qywx_group_send_result_log
      where is_deleted = 0
      and group_msg_id in
      <foreach collection="list" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
      group by group_msg_id
    </select>

  <select id="selectAll"
          resultType="com.fotile.customercenter.groupmsg.pojo.entity.SendResultLogDto">
    select group_msg_id, count(1) as sendCount
    from t_qywx_group_send_result_log
    where is_deleted = 0 and status = 1
    and group_msg_id in
    <foreach collection="list" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    group by group_msg_id
  </select>


    <insert id="insert" parameterType="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    insert into t_qywx_group_send_result_log (
      	  	       id,
       	  	       group_msg_id,
       	  	       user_id,
       	  	       qywx_msg_id,
       	  	       external_userid,
       	  	       status,
       	  	       send_time,
       	  	       is_deleted,
       	  	       created_by,
       	  	       created_date,
       	  	       modified_by,
       	  	    	  modified_date
       	    )
    values (
	  	  	      	#{id,jdbcType=BIGINT},
       	  	      	#{groupMsgId,jdbcType=BIGINT},
       	  	      	#{userId,jdbcType=VARCHAR},
       	  	      	#{qywxMsgId,jdbcType=BIGINT},
       	  	      	#{externalUserid,jdbcType=VARCHAR},
       	  	      	#{status,jdbcType=TINYINT},
       	  	      	#{sendTime,jdbcType=TIMESTAMP},
       	  	      	#{isDeleted,jdbcType=TINYINT},
       	  	      	#{createdBy,jdbcType=VARCHAR},
       	  	      	#{createdDate,jdbcType=TIMESTAMP},
       	  	      	#{modifiedBy,jdbcType=VARCHAR},
       	  	    	  #{modifiedDate,jdbcType=TIMESTAMP}
       	    )
  </insert>


  <insert id="insertBatch" parameterType="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    insert into t_qywx_group_send_result_log (
      group_msg_id,
      user_id,
      qywx_msg_id,
      external_userid,
      status,
      send_time,
      is_deleted,
      created_by,
      created_date,
      modified_by,
      modified_date
    )
    values
    <foreach collection="list" item="entity" separator=",">
           (
             #{entity.groupMsgId,jdbcType=BIGINT},
             #{entity.userId,jdbcType=VARCHAR},
             #{entity.qywxMsgId,jdbcType=BIGINT},
             #{entity.externalUserid,jdbcType=VARCHAR},
             #{entity.status,jdbcType=TINYINT},
             #{entity.sendTime,jdbcType=TIMESTAMP},
             0,
             'kafka',
             now(),
             'kafka',
             now()
           )
    </foreach>
  </insert>

  
  <insert id="insertSelective" parameterType="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    insert into t_qywx_group_send_result_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
          
      <if test="id != null">
        id,
      </if>
         
      <if test="groupMsgId != null">
        group_msg_id,
      </if>
         
      <if test="userId != null">
        user_id,
      </if>
         
      <if test="qywxMsgId != null">
        qywx_msg_id,
      </if>
         
      <if test="externalUserid != null">
        external_userid,
      </if>
         
      <if test="status != null">
        status,
      </if>
         
      <if test="sendTime != null">
        send_time,
      </if>
         
      <if test="isDeleted != null">
        is_deleted,
      </if>
         
      <if test="createdBy != null">
        created_by,
      </if>
         
      <if test="createdDate != null">
        created_date,
      </if>
         
      <if test="modifiedBy != null">
        modified_by,
      </if>
         
      <if test="modifiedDate != null">
        modified_date,
      </if>
         </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
          <if test="groupMsgId != null">
        #{groupMsgId,jdbcType=BIGINT},
      </if>
          <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
          <if test="qywxMsgId != null">
        #{qywxMsgId,jdbcType=BIGINT},
      </if>
          <if test="externalUserid != null">
        #{externalUserid,jdbcType=VARCHAR},
      </if>
          <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
          <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
          <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
          <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
          <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
          <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
          <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
        </trim>
  </insert>
  

  

  
  
      <update id="updateByPrimaryKeySelective" parameterType="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    update t_qywx_group_send_result_log
    <set>
                          
      <if test="groupMsgId != null">
        group_msg_id = #{groupMsgId,jdbcType=BIGINT},
      </if>
                      
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
                      
      <if test="qywxMsgId != null">
        qywx_msg_id = #{qywxMsgId,jdbcType=BIGINT},
      </if>
                      
      <if test="externalUserid != null">
        external_userid = #{externalUserid,jdbcType=VARCHAR},
      </if>
                      
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
                      
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
                      
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
                      
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
                      
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
                      
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
                      
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
              </set>
    
    where 
            
    id = #{id,jdbcType=BIGINT}
                                                                                                  
  </update>

      	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	

  <update id="updateByPrimaryKey" parameterType="com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupSendResultLog">
    update t_qywx_group_send_result_log
    set 
    		 		 	  	  
		group_msg_id = #{groupMsgId,jdbcType=BIGINT},
	  	 		 	  	  
		user_id = #{userId,jdbcType=VARCHAR},
	  	 		 	  	  
		qywx_msg_id = #{qywxMsgId,jdbcType=BIGINT},
	  	 		 	  	  
		external_userid = #{externalUserid,jdbcType=VARCHAR},
	  	 		 	  	  
		status = #{status,jdbcType=TINYINT},
	  	 		 	  	  
		send_time = #{sendTime,jdbcType=TIMESTAMP},
	  	 		 	  	  
		is_deleted = #{isDeleted,jdbcType=TINYINT},
	  	 		 	  	  
		created_by = #{createdBy,jdbcType=VARCHAR},
	  	 		 	  	  
		created_date = #{createdDate,jdbcType=TIMESTAMP},
	  	 		 	  	  
		modified_by = #{modifiedBy,jdbcType=VARCHAR},
	  	 		 	  		
		modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
	  	 		
    where 
            
    id = #{id,jdbcType=BIGINT}
                                                                                                    
  </update>
  
                                                                
</mapper>