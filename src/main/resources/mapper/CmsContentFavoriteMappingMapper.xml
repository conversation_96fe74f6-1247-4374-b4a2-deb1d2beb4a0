<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.favorite.dao.CmsContentFavoriteMappingMapper">
  <resultMap id="BaseResultMap" type="com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="source_table_name" jdbcType="VARCHAR" property="sourceTableName" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="charge_user_id" jdbcType="BIGINT" property="chargeUserId" />
    <result column="fav_status" jdbcType="TINYINT" property="favStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  
     	id,
     	source_table_name,
     	source_id,
     	source_type,
     	user_id,
     	fav_status,
     	created_by,
     	created_date,
     	modified_by,
     	modified_date,
   	  	is_deleted,
        charge_user_id
  	
  </sql>
  

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cmscenter.cms_content_favorite_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="queryFavoriteCountBySpecialId" resultType="java.lang.Long">
      select count(1)
      from cms_content_favorite_mapping
      where is_deleted = 0 and source_id = #{specialId} and source_table_name = 'special_info' and fav_status = 1
    </select>
  <select id="queryFavoriteCountByUserId" resultType="java.lang.Integer">
    select count(1)
    from cms_content_favorite_mapping
    where is_deleted = 0 and source_id = #{specialId} and user_id = #{userId} and source_table_name = 'special_info' and fav_status = 1
  </select>

    <insert id="insert" parameterType="com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping">
    insert into cmscenter.cms_content_favorite_mapping (
      id,
      source_table_name,
      source_id,
      source_type,
      user_id,
      charge_user_id,
      fav_status,
      created_by,
      created_date,
      modified_by,
      modified_date,
   	  is_deleted
    )
    values (
     	#{id,jdbcType=INTEGER},
     	#{sourceTableName,jdbcType=VARCHAR},
     	#{sourceId,jdbcType=BIGINT},
     	#{sourceType,jdbcType=TINYINT},
     	#{userId,jdbcType=VARCHAR},
      #{chargeUserId,jdbcType=BIGINT},
     	#{favStatus,jdbcType=TINYINT},
     	#{createdBy,jdbcType=VARCHAR},
     	#{createdDate,jdbcType=TIMESTAMP},
     	#{modifiedBy,jdbcType=VARCHAR},
     	#{modifiedDate,jdbcType=TIMESTAMP},
   	  #{isDeleted,jdbcType=TINYINT}
    )
  </insert>
  
  <insert id="insertSelective" parameterType="com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping">
    insert into cmscenter.cms_content_favorite_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
    
      <if test="id != null">
        id,
      </if>
    
      <if test="sourceTableName != null">
        source_table_name,
      </if>
    
      <if test="sourceId != null">
        source_id,
      </if>
    
      <if test="sourceType != null">
        source_type,
      </if>
    
      <if test="userId != null">
        user_id,
      </if>
      <if test="chargeUserId != null">
        charge_user_id,
      </if>

      <if test="favStatus != null">
        fav_status,
      </if>
    
      <if test="createdBy != null">
        created_by,
      </if>
    
      <if test="createdDate != null">
        created_date,
      </if>
    
      <if test="modifiedBy != null">
        modified_by,
      </if>
    
      <if test="modifiedDate != null">
        modified_date,
      </if>
    
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sourceTableName != null">
        #{sourceTableName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="chargeUserId != null">
        #{chargeUserId,jdbcType=BIGINT},
      </if>
      <if test="favStatus != null">
        #{favStatus,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  


  
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping">
    update cmscenter.cms_content_favorite_mapping
    <set>
      
      <if test="sourceTableName != null and sourceTableName != ''">
        source_table_name = #{sourceTableName,jdbcType=VARCHAR},
      </if>
      
      <if test="sourceId != null and sourceId != ''">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      
      <if test="sourceType != null and sourceType != ''">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      
      <if test="userId != null and userId != ''">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>

      <if test="chargeUserId != null and chargeUserId != ''">
        charge_user_id = #{chargeUserId,jdbcType=BIGINT},
      </if>
      
      <if test="favStatus != null and favStatus != ''">
        fav_status = #{favStatus,jdbcType=TINYINT},
      </if>
      
      <if test="createdBy != null and createdBy != ''">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      
      <if test="createdDate != null and createdDate != ''">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      
      <if test="modifiedBy != null and modifiedBy != ''">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      
      <if test="modifiedDate != null and modifiedDate != ''">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      
      <if test="isDeleted != null and isDeleted != ''">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    
    where 
    
    id = #{id,jdbcType=INTEGER}
  
  </update>

	

  <update id="updateByPrimaryKey" parameterType="com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping">
    update cmscenter.cms_content_favorite_mapping
    set 
	  
		source_table_name = #{sourceTableName,jdbcType=VARCHAR},
	  
		source_id = #{sourceId,jdbcType=BIGINT},
	  
		source_type = #{sourceType,jdbcType=TINYINT},
	  
		user_id = #{userId,jdbcType=VARCHAR},

        charge_user_id = #{chargeUserId,jdbcType=BIGINT},
	  
		fav_status = #{favStatus,jdbcType=TINYINT},
	  
		created_by = #{createdBy,jdbcType=VARCHAR},
	  
		created_date = #{createdDate,jdbcType=TIMESTAMP},
	  
		modified_by = #{modifiedBy,jdbcType=VARCHAR},
	  
		modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
		
		is_deleted = #{isDeleted,jdbcType=TINYINT}
	
    where 
    
    id = #{id,jdbcType=INTEGER}
    
  </update>
  
   
</mapper>