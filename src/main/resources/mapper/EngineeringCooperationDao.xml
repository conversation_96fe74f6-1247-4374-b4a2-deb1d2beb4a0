<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.engineering.dao.EngineeringCooperationDao">

    <!--  官网工程合作分页查询  -->
    <select id="queryEngineeringByParams"
            resultType="com.fotile.cmscenter.engineering.pojo.dto.EngineeringCooperationOutDto">
        select * from cmscenter.engineering_cooperation
        <where>
            is_deleted = 0
            <if test="id != null ">
                and id = #{id}
            </if>
            <if test="title != null and title != '' ">
                and title = #{title}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="stage != null and stage != '' ">
                and stage = #{stage}
            </if>
        </where>
        group by id
        order by created_date desc
        limit #{offset},#{size}
    </select>

    <!--  官网工程合作分页查询  -->
    <select id="queryEngineeringByType"
            resultType="com.fotile.cmscenter.engineering.pojo.entity.EngineeringCooperation">
        select * from cmscenter.engineering_cooperation
        where
        is_deleted = 0
        and type = #{type}
        and stage = 1
        <if test="year!=null and year !=''">
            and particular_year = #{year}
        </if>
        <if test="excludeCaseId != null">
            and id != #{excludeCaseId}
        </if>
        <if test="keyword != null and keyword != ''">
            and (
                title like concat('%',#{keyword},'%')
                or developers like concat('%',#{keyword},'%')
                or id in (
                    select engineering_cooperation_id from engineering_cooperation_goods_mapping
                    where is_deleted = 0
                    and goods_name like concat('%',#{keyword},'%')
                )
            )
        </if>
        <choose>
            <when test="type==1">
                order by awards_date desc,sort desc,created_date desc
            </when>
            <otherwise>
                order by sort desc,created_date desc
            </otherwise>
        </choose>
        <if test="offset != null and size != null">
            limit #{offset},#{size}
        </if>
    </select>

    <select id="queryParticularYear"
            resultType="string">
        SELECT
            particular_year
        FROM
            cmscenter.engineering_cooperation
        WHERE
            is_deleted = 0
            AND type = 2
        GROUP BY
            particular_year
        ORDER BY
            particular_year ASC
    </select>

    <!--  根据id查询官网工程合作详情  -->
    <select id="getEngineeringById" resultType="com.fotile.cmscenter.engineering.pojo.dto.GetEngineeringOutDto">
        select * from cmscenter.engineering_cooperation
        where
            is_deleted = 0
            and id = #{id}
        limit 1
    </select>


    <!-- 官网工程合作上下架 -->
    <update id="updateStage">
        update cmscenter.engineering_cooperation
        set
        modified_date = now(),
        modified_by = #{modifiedBy},
        <if test="stage != null ">stage = #{stage}</if>
        where
        is_deleted = 0
        and id = #{id};
    </update>


    <!-- 官网工程合作删除 -->
    <update id="deleteEngineering">
        update cmscenter.engineering_cooperation
        set
        modified_date = now(),
        modified_by = #{modifiedBy},
        is_deleted = #{id}
        where
        is_deleted = 0
        and id = #{id};
    </update>


    <!-- 更新排序 -->
    <update id="updateSort">
        update cmscenter.engineering_cooperation
        <set>
            modified_date = now(),
            modified_by = #{modifiedBy},
            <if test="sort != null ">sort = #{sort}</if>
        </set>
        where
        is_deleted = 0
        and id = #{id}
    </update>


    <!--  新增官网工程合作  -->
    <insert id="insertEngineering" useGeneratedKeys="true" keyProperty="id">
        insert into cmscenter.engineering_cooperation (
        type,
        title,
        subtitle,
        awards_date,
        awards_company,
        awards_place,
        image_url,
        particular_year,
        city,
        developers,
        sort,
        stage,
        relation_goods,
        note,
        detail_url,
        address,
        single_area,
        sample_desc,
        description,
        is_deleted,
        modified_by,
        modified_date,
        created_date,
        created_by
        )values (
        #{type},
        #{title},
        #{subtitle},
        #{awardsDate},
        #{awardsCompany},
        #{awardsPlace},
        #{imageUrl},
        #{particularYear},
        #{city},
        #{developers},
        #{sort},
        #{stage},
        #{relationGoods},
        #{note},
        #{detailUrl},
        #{address},
        #{singleArea},
        #{sampleDesc},
        #{description},
        0,
        #{modifiedBy},
        #{modifiedDate},
        #{createdDate},
        #{createdBy}
        )
    </insert>


    <!--  编辑官网工程合作  -->
    <update id="updateEngineering">
        update cmscenter.engineering_cooperation
        <set>
            modified_date = now(),
            <if test="type != null ">type = #{type},</if>
            <if test="title != null and title != '' ">title = #{title},</if>
            <if test="subtitle != null and subtitle != '' ">subtitle = #{subtitle},</if>
            <if test="awardsDate != null ">awards_date = #{awardsDate},</if>
            <if test="awardsCompany != null and awardsCompany != '' ">awards_company = #{awardsCompany},</if>
            <if test="awardsPlace != null and awardsPlace != '' ">awards_place = #{awardsPlace},</if>
            <if test="imageUrl != null and imageUrl != '' ">image_url = #{imageUrl},</if>
            <if test="particularYear != null and particularYear != '' ">particular_year = #{particularYear},</if>
            <if test="city != null and city != '' ">city = #{city},</if>
            <if test="developers != null and developers != '' ">developers = #{developers},</if>
            <if test="sort != null ">sort = #{sort},</if>
            <if test="stage != null ">stage = #{stage},</if>
            <if test="relationGoods != null and note != '' ">relation_goods = #{relationGoods},</if>
            <if test="note != null and note != '' ">note = #{note},</if>
            detail_url = #{detailUrl},
            description = #{description},
            address = #{address},
            single_area = #{singleArea},
            sample_desc = #{sampleDesc},
            <if test="modifiedBy != null">modified_by=#{modifiedBy},</if>
            <if test="modifiedDate != null">modified_date=#{modifiedDate},</if>
        </set>
        where
        is_deleted = 0
        and id = #{id}
    </update>


    <!--添加轮播图关系-->
    <insert id="addImgMapperList">
        insert into cmscenter.picture_cms_mapping(source_table_name, source_id, picture_cms_id,sort,
        is_deleted,modified_by,modified_date,created_date,created_by)values
        <foreach item="item" collection="imgList" separator=",">
            ('engineering_cooperation',#{id},#{item.id},0,'0',#{item.modifiedBy},#{item.modifiedDate},#{item.createdDate},#{item.createdBy})
        </foreach>
    </insert>

    <!--删除轮播图-->
    <update id="deleteImg">
        update cmscenter.picture_cms
        <set>
            modified_date = now(),
            modified_by = #{modifiedBy},
            is_deleted = #{id}
        </set>
        WHERE
        id IN
        (SELECT picture_cms_id FROM cmscenter.picture_cms_mapping
        WHERE is_deleted = 0
        and source_table_name='engineering_cooperation'
        AND source_id=#{id})
    </update>

    <!--删除轮播图关系表-->
    <update id="deleteImgMapper">
        update cmscenter.picture_cms_mapping
        <set>
            modified_date = now(),
            modified_by = #{modifiedBy},
            is_deleted = #{id}
        </set>
        WHERE source_id =#{id} and source_table_name='question_info'
    </update>


    <select id="findUrl" resultType="com.fotile.cmscenter.comment.pojo.Img">
        select id,name,cover_url1 url from cmscenter.picture_cms
        where is_deleted = 0
        and id IN
        (SELECT picture_cms_id FROM cmscenter.picture_cms_mapping
        WHERE is_deleted = 0
        and source_table_name='engineering_cooperation'
        AND source_id=#{id})
    </select>

    <!-- 根据ids批量查询轮播图 -->
    <select id="findUrlByIds" resultType="com.fotile.cmscenter.comment.pojo.Img">
        select pc.id,pc.name,pcm.source_id sourceId,pc.cover_url1 url from cmscenter.picture_cms_mapping pcm
        left join cmscenter.picture_cms pc on pc.id = pcm.picture_cms_id and pc.is_deleted = 0
        WHERE pcm.is_deleted = 0
        and pcm.source_table_name = 'engineering_cooperation'
        AND pcm.source_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--  官网工程合作分页查询  -->
    <select id="queryEngineeringByParamsCount"
            resultType="long">
        select count(distinct id) from cmscenter.engineering_cooperation
        <where>
            is_deleted = 0
            <if test="id != null ">
                and id = #{id}
            </if>
            <if test="title != null and title != '' ">
                and title = #{title}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="stage != null and stage != '' ">
                and stage = #{stage}
            </if>
        </where>
        order by created_date desc
    </select>

    <!--  官网工程合作分页查询  -->
    <select id="queryEngineeringByTypeCount"
            resultType="long">
        select count(distinct (id)) from cmscenter.engineering_cooperation
        where
        is_deleted = 0
        and type = #{type}
        <if test="keyword != null and keyword != ''">
            and (
                title like concat('%',#{keyword},'%')
                or developers like concat('%',#{keyword},'%')
                or id in (
                    select engineering_cooperation_id from engineering_cooperation_goods_mapping
                    where is_deleted = 0
                    and goods_name like concat('%',#{keyword},'%')
                )
            )
        </if>
        and stage = 1
        <if test="year!=null and year !=''">
            and particular_year = #{year}
        </if>
        <if test="excludeCaseId != null">
            and id != #{excludeCaseId}
        </if>

        order by created_date desc
    </select>


    <!-- 更新排序 -->
    <update id="updateBrowseCount">
        update cmscenter.engineering_cooperation
        <set>
            modified_date = now(),
            modified_by = #{modifiedBy},
            browse_count = browse_count + 1
        </set>
        where
        is_deleted = 0
        and id = #{id}
    </update>

    <!--  官网工程合作分页查询  -->
    <select id="getBrowseList"
            resultType="com.fotile.cmscenter.engineering.pojo.dto.EngineeringCooperationDto">
        select * from cmscenter.engineering_cooperation
        where
        is_deleted = 0
        <if test="type!=null">
            and type = #{type}
        </if>
        and stage = 1
        <if test="year!=null and year !=''">
            and particular_year = #{year}
        </if>
        group by id
        order by browse_count desc
        limit #{offset},#{size}
    </select>

    <select id="getBrowseListCount"
            resultType="long">
        select count(1) from cmscenter.engineering_cooperation
        where
        is_deleted = 0
        <if test="type!=null">
            and type = #{type}
        </if>
        and stage = 1
        <if test="year!=null and year !=''">
            and particular_year = #{year}
        </if>

    </select>


</mapper>