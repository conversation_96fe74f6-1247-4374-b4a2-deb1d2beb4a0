<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.materielfilerelevance.dao.MaterielFileRelevanceDao">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevance">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="materiel_id" jdbcType="VARCHAR" property="materielId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_size" jdbcType="INTEGER" property="fileSize" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="folder_id" jdbcType="VARCHAR" property="folderId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="download_count" jdbcType="INTEGER" property="downloadCount" />
    <result column="oss_url" jdbcType="VARCHAR" property="ossUrl" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_del" jdbcType="VARCHAR" property="isDel" />
    <result column="reserve_item1" jdbcType="VARCHAR" property="reserveItem1" />
    <result column="reserve_item2" jdbcType="VARCHAR" property="reserveItem2" />
    <result column="reserve_item3" jdbcType="VARCHAR" property="reserveItem3" />
    <result column="reserve_item4" jdbcType="VARCHAR" property="reserveItem4" />
    <result column="reserve_item5" jdbcType="VARCHAR" property="reserveItem5" />
    <result column="reserve_item6" jdbcType="VARCHAR" property="reserveItem6" />
    <result column="reserve_item7" jdbcType="VARCHAR" property="reserveItem7" />
    <result column="reserve_item8" jdbcType="VARCHAR" property="reserveItem8" />
    <result column="reserve_item9" jdbcType="VARCHAR" property="reserveItem9" />
    <result column="reserve_item10" jdbcType="VARCHAR" property="reserveItem10" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, materiel_id, file_name, file_size, file_type, folder_id, user_id, download_count, 
    oss_url, `status`, ctime, creator, mtime, modifier, is_del, reserve_item1, reserve_item2, 
    reserve_item3, reserve_item4, reserve_item5, reserve_item6, reserve_item7, reserve_item8, 
    reserve_item9, reserve_item10
  </sql>
  <select id="selectByExample" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from materiel_file_relevance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from materiel_file_relevance
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from materiel_file_relevance
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevanceExample">
    delete from materiel_file_relevance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevance" useGeneratedKeys="true">
    insert into materiel_file_relevance (id, materiel_id, file_name, file_size,
      file_type, folder_id, user_id, 
      download_count, oss_url, `status`, 
      ctime, creator, mtime, 
      modifier, is_del, reserve_item1, 
      reserve_item2, reserve_item3, reserve_item4, 
      reserve_item5, reserve_item6, reserve_item7, 
      reserve_item8, reserve_item9, reserve_item10
      )
    values (#{id,jdbcType=VARCHAR}, #{materielId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT},
      #{fileType,jdbcType=VARCHAR}, #{folderId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{downloadCount,jdbcType=INTEGER}, #{ossUrl,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{mtime,jdbcType=TIMESTAMP}, 
      #{modifier,jdbcType=VARCHAR}, #{isDel,jdbcType=VARCHAR}, #{reserveItem1,jdbcType=VARCHAR}, 
      #{reserveItem2,jdbcType=VARCHAR}, #{reserveItem3,jdbcType=VARCHAR}, #{reserveItem4,jdbcType=VARCHAR}, 
      #{reserveItem5,jdbcType=VARCHAR}, #{reserveItem6,jdbcType=VARCHAR}, #{reserveItem7,jdbcType=VARCHAR}, 
      #{reserveItem8,jdbcType=VARCHAR}, #{reserveItem9,jdbcType=VARCHAR}, #{reserveItem10,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevance" useGeneratedKeys="true">
    insert into materiel_file_relevance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materielId != null">
        materiel_id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="folderId != null">
        folder_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="downloadCount != null">
        download_count,
      </if>
      <if test="ossUrl != null">
        oss_url,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="reserveItem1 != null">
        reserve_item1,
      </if>
      <if test="reserveItem2 != null">
        reserve_item2,
      </if>
      <if test="reserveItem3 != null">
        reserve_item3,
      </if>
      <if test="reserveItem4 != null">
        reserve_item4,
      </if>
      <if test="reserveItem5 != null">
        reserve_item5,
      </if>
      <if test="reserveItem6 != null">
        reserve_item6,
      </if>
      <if test="reserveItem7 != null">
        reserve_item7,
      </if>
      <if test="reserveItem8 != null">
        reserve_item8,
      </if>
      <if test="reserveItem9 != null">
        reserve_item9,
      </if>
      <if test="reserveItem10 != null">
        reserve_item10,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materielId != null">
        #{materielId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="folderId != null">
        #{folderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="downloadCount != null">
        #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null">
        #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        #{reserveItem10,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevanceExample" resultType="java.lang.Long">
    select count(*) from materiel_file_relevance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update materiel_file_relevance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.materielId != null">
        materiel_id = #{record.materielId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileSize != null">
        file_size = #{record.fileSize,jdbcType=INTEGER},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.folderId != null">
        folder_id = #{record.folderId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadCount != null">
        download_count = #{record.downloadCount,jdbcType=INTEGER},
      </if>
      <if test="record.ossUrl != null">
        oss_url = #{record.ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem1 != null">
        reserve_item1 = #{record.reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem2 != null">
        reserve_item2 = #{record.reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem3 != null">
        reserve_item3 = #{record.reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem4 != null">
        reserve_item4 = #{record.reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem5 != null">
        reserve_item5 = #{record.reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem6 != null">
        reserve_item6 = #{record.reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem7 != null">
        reserve_item7 = #{record.reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem8 != null">
        reserve_item8 = #{record.reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem9 != null">
        reserve_item9 = #{record.reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem10 != null">
        reserve_item10 = #{record.reserveItem10,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update materiel_file_relevance
    set id = #{record.id,jdbcType=VARCHAR},
      materiel_id = #{record.materielId,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_size = #{record.fileSize,jdbcType=INTEGER},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      folder_id = #{record.folderId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      download_count = #{record.downloadCount,jdbcType=INTEGER},
      oss_url = #{record.ossUrl,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_del = #{record.isDel,jdbcType=VARCHAR},
      reserve_item1 = #{record.reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{record.reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{record.reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{record.reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{record.reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{record.reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{record.reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{record.reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{record.reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{record.reserveItem10,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevance">
    update materiel_file_relevance
    <set>
      <if test="materielId != null">
        materiel_id = #{materielId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="folderId != null">
        folder_id = #{folderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="downloadCount != null">
        download_count = #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null">
        oss_url = #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        reserve_item10 = #{reserveItem10,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.MaterielFileRelevance">
    update materiel_file_relevance
    set materiel_id = #{materielId,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=INTEGER},
      file_type = #{fileType,jdbcType=VARCHAR},
      folder_id = #{folderId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      download_count = #{downloadCount,jdbcType=INTEGER},
      oss_url = #{ossUrl,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=VARCHAR},
      reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{reserveItem10,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="insertBatch" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
    insert into materiel_file_relevance (id, materiel_id, file_name, file_size,
      file_type, folder_id, user_id,
      download_count, oss_url, `status`,
      ctime, creator, mtime,
      modifier, is_del, reserve_item1,
      reserve_item2, reserve_item3, reserve_item4,
      reserve_item5, reserve_item6, reserve_item7,
      reserve_item8, reserve_item9, reserve_item10
      )
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (
      #{item.id,jdbcType=VARCHAR},
      #{item.materielId,jdbcType=VARCHAR},
      #{item.fileName,jdbcType=VARCHAR},
      #{item.fileSize,jdbcType=INTEGER},
      #{item.fileType,jdbcType=VARCHAR},
      #{item.folderId,jdbcType=VARCHAR},
      #{item.userId,jdbcType=VARCHAR},
      #{item.downloadCount,jdbcType=INTEGER},
      #{item.ossUrl,jdbcType=VARCHAR},
      #{item.status,jdbcType=VARCHAR},
      #{item.ctime,jdbcType=TIMESTAMP},
      #{item.creator,jdbcType=VARCHAR},
      #{item.mtime,jdbcType=TIMESTAMP},
      #{item.modifier,jdbcType=VARCHAR},
      #{item.isDel,jdbcType=VARCHAR},
      #{item.reserveItem1,jdbcType=VARCHAR, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler},
      #{item.reserveItem2,jdbcType=VARCHAR, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler},
      #{item.reserveItem3,jdbcType=VARCHAR},
      #{item.reserveItem4,jdbcType=VARCHAR},
      #{item.reserveItem5,jdbcType=VARCHAR},
      #{item.reserveItem6,jdbcType=VARCHAR},
      #{item.reserveItem7,jdbcType=VARCHAR},
      #{item.reserveItem8,jdbcType=VARCHAR},
      #{item.reserveItem9,jdbcType=VARCHAR},
      #{item.reserveItem10,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <resultMap id="resultMaterielFileList" type="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.GetMaterielFileListVO">
    <result property="id" column="id"></result>
    <result property="materielId" column="materielId"></result>
    <result property="fileName" column="fileName"></result>
    <result property="fileSize" column="fileSize"></result>
    <result property="fileType" column="fileType"></result>
    <result column="ossUrl" property="ossUrl"></result>
    <result property="time" column="time"></result>
    <collection property="fileList" ofType="com.fotile.resourcescenter.materielfilerelevance.pojo.entity.GetMaterielFileListVO" javaType="java.util.List" column="id">
      <result column="subId" property="id" javaType="string" />
      <result column="subMaterielId" property="materielId" javaType="string" />
      <result column="subFileName" property="fileName" javaType="string" />
      <result column="subFileSize" property="fileSize" javaType="long" />
      <result column="subFileType" property="fileType" javaType="string" />
      <result column="subOssUrl" property="ossUrl" javaType="string" />
      <result column="subTime" property="time" javaType="string" />
    </collection>
  </resultMap>
  <select id="selectMaterielFileList"    resultMap="resultMaterielFileList">
    SELECT
        A.id,
        A.materiel_id materielId,
        A.file_name fileName,
        A.file_size fileSize,
        A.file_type fileType,
        A.oss_url ossUrl,
        A.ctime time,
        B.id subId,
        B.materiel_id subMaterielId,
        B.file_name subFileName,
        B.file_size subFileSize,
        B.file_type subFileType,
        B.oss_url subOssUrl,
        B.ctime subTime
    FROM
        materiel_file_relevance A
    left join materiel_file_relevance B on A.id = B.folder_id and B.materiel_id = #{materielId}
    WHERE
        A.materiel_id = #{materielId}
    AND (
        A.folder_id IS NULL
        OR A.file_type = 'folder'
    )
    order by A.ctime desc , B.ctime desc
  </select>
  <delete id="deleteByIdOrMaterielId" parameterType="map">
    DELETE
    FROM
        materiel_file_relevance
    WHERE
        (
            id IN
            <foreach collection="fileIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
            </foreach>
            AND materiel_id = 0 )

        OR materiel_id = #{materielId}
  </delete>

  <select id="selectByMaterielId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from materiel_file_relevance
    where materiel_id = #{materielId,jdbcType=VARCHAR} and is_del = '2'
  </select>
</mapper>