<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.pointshopcenter.benefits.dao.BenefitsEolinkLogDao">
  <resultMap id="BaseResultMap" type="com.fotile.pointshopcenter.benefits.pojo.entity.BenefitsEolinkLog">
    <!--@mbg.generated-->
    <!--@Table pointshopcenter.benefits_eolink_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="notice_id" jdbcType="BIGINT" property="noticeId" />
    <result column="notice_type" jdbcType="INTEGER" property="noticeType" />
    <result column="content_json" jdbcType="LONGVARCHAR" property="contentJson" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="notice_create_time" jdbcType="TIMESTAMP" property="noticeCreateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="response" jdbcType="LONGVARCHAR" property="response" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, notice_id, notice_type, content_json, order_id, notice_create_time, `status`, 
    response, create_time, modified_date
  </sql>
</mapper>