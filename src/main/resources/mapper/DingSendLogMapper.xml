<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.shopordercenter.order.dao.DingSendLogMapper">
  <resultMap id="BaseResultMap" type="com.fotile.shopordercenter.order.pojo.entity.DingSendLog">
    <!--@mbg.generated-->
    <!--@Table shopordercenter.ding_send_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_table" jdbcType="VARCHAR" property="sourceTable" />
    <result column="assignee" jdbcType="VARCHAR" property="assignee" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_param" jdbcType="LONGVARCHAR" property="requestParam" />
    <result column="response_code" jdbcType="VARCHAR" property="responseCode" />
    <result column="response_result" jdbcType="LONGVARCHAR" property="responseResult" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, source_id, source_table, assignee, request_url, request_param, response_code, 
    response_result, create_time
  </sql>
</mapper>