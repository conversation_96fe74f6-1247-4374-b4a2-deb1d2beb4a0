<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.materiel.dao.MaterielUsageHistoryMapper">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.materiel.pojo.entity.MaterielUsageHistoryEntity">
    <!--@mbg.generated-->
    <!--@Table `resourcescenter`.`materiel_usage_history`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="materiel_id" jdbcType="VARCHAR" property="materielId" />
    <result column="usage_user_entity_id" jdbcType="VARCHAR" property="usageUserEntityId" />
    <result column="usage_user_name" jdbcType="VARCHAR" property="usageUserName" />
    <result column="usage_type" jdbcType="TINYINT" property="usageType" />
    <result column="usage_target_user_entity_id" jdbcType="VARCHAR" property="usageTargetUserEntityId" />
    <result column="usage_target_user_name" jdbcType="VARCHAR" property="usageTargetUserName" />
    <result column="charge_user_id" jdbcType="BIGINT" property="chargeUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, 
    `materiel_id`, `usage_user_entity_id`, `usage_user_name`, `usage_type`, `usage_target_user_entity_id`, 
    `usage_target_user_name`, `charge_user_id`
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `resourcescenter`.`materiel_usage_history`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`materiel_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.materielId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.materielId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_user_entity_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageUserEntityId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageUserEntityId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_user_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageUserName != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_target_user_entity_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageTargetUserEntityId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageTargetUserEntityId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`usage_target_user_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageTargetUserName != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.usageTargetUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`charge_user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.chargeUserId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.chargeUserId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_usage_history`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `materiel_id`, 
      `usage_user_entity_id`, `usage_user_name`, `usage_type`, `usage_target_user_entity_id`, 
      `usage_target_user_name`, `charge_user_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.materielId,jdbcType=VARCHAR}, 
        #{item.usageUserEntityId,jdbcType=VARCHAR}, #{item.usageUserName,jdbcType=VARCHAR}, 
        #{item.usageType,jdbcType=TINYINT}, #{item.usageTargetUserEntityId,jdbcType=VARCHAR}, 
        #{item.usageTargetUserName,jdbcType=VARCHAR}, #{item.chargeUserId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from `resourcescenter`.`materiel_usage_history` where `id` in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update `resourcescenter`.`materiel_usage_history`
      <set>
        <if test="item.isDeleted != null">
          `is_deleted` = #{item.isDeleted,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null and item.createdBy != ''">
          `created_by` = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdDate != null">
          `created_date` = #{item.createdDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifiedBy != null and item.modifiedBy != ''">
          `modified_by` = #{item.modifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.modifiedDate != null">
          `modified_date` = #{item.modifiedDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.materielId != null and item.materielId != ''">
          `materiel_id` = #{item.materielId,jdbcType=VARCHAR},
        </if>
        <if test="item.usageUserEntityId != null and item.usageUserEntityId != ''">
          `usage_user_entity_id` = #{item.usageUserEntityId,jdbcType=VARCHAR},
        </if>
        <if test="item.usageUserName != null and item.usageUserName != ''">
          `usage_user_name` = #{item.usageUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.usageType != null">
          `usage_type` = #{item.usageType,jdbcType=TINYINT},
        </if>
        <if test="item.usageTargetUserEntityId != null and item.usageTargetUserEntityId != ''">
          `usage_target_user_entity_id` = #{item.usageTargetUserEntityId,jdbcType=VARCHAR},
        </if>
        <if test="item.usageTargetUserName != null and item.usageTargetUserName != ''">
          `usage_target_user_name` = #{item.usageTargetUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.chargeUserId != null">
          `charge_user_id` = #{item.chargeUserId,jdbcType=BIGINT},
        </if>
      </set>
      where `id` = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_usage_history`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `materiel_id`, 
      `usage_user_entity_id`, `usage_user_name`, `usage_type`, `usage_target_user_entity_id`, 
      `usage_target_user_name`, `charge_user_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.isDeleted != null">
          #{item.isDeleted,jdbcType=BIGINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createdDate != null">
          #{item.createdDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.modifiedBy != null">
          #{item.modifiedBy,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.modifiedDate != null">
          #{item.modifiedDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.materielId != null">
          #{item.materielId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageUserEntityId != null">
          #{item.usageUserEntityId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageUserName != null">
          #{item.usageUserName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageType != null">
          #{item.usageType,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageTargetUserEntityId != null">
          #{item.usageTargetUserEntityId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.usageTargetUserName != null">
          #{item.usageTargetUserName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.chargeUserId != null">
          #{item.chargeUserId,jdbcType=BIGINT}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materiel.pojo.entity.MaterielUsageHistoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_usage_history`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `materiel_id`,
      `usage_user_entity_id`,
      `usage_user_name`,
      `usage_type`,
      `usage_target_user_entity_id`,
      `usage_target_user_name`,
      `charge_user_id`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{materielId,jdbcType=VARCHAR},
      #{usageUserEntityId,jdbcType=VARCHAR},
      #{usageUserName,jdbcType=VARCHAR},
      #{usageType,jdbcType=TINYINT},
      #{usageTargetUserEntityId,jdbcType=VARCHAR},
      #{usageTargetUserName,jdbcType=VARCHAR},
      #{chargeUserId,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `materiel_id` = #{materielId,jdbcType=VARCHAR},
      `usage_user_entity_id` = #{usageUserEntityId,jdbcType=VARCHAR},
      `usage_user_name` = #{usageUserName,jdbcType=VARCHAR},
      `usage_type` = #{usageType,jdbcType=TINYINT},
      `usage_target_user_entity_id` = #{usageTargetUserEntityId,jdbcType=VARCHAR},
      `usage_target_user_name` = #{usageTargetUserName,jdbcType=VARCHAR},
      `charge_user_id` = #{chargeUserId,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materiel.pojo.entity.MaterielUsageHistoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `resourcescenter`.`materiel_usage_history`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="materielId != null and materielId != ''">
        `materiel_id`,
      </if>
      <if test="usageUserEntityId != null and usageUserEntityId != ''">
        `usage_user_entity_id`,
      </if>
      <if test="usageUserName != null and usageUserName != ''">
        `usage_user_name`,
      </if>
      <if test="usageType != null">
        `usage_type`,
      </if>
      <if test="usageTargetUserEntityId != null and usageTargetUserEntityId != ''">
        `usage_target_user_entity_id`,
      </if>
      <if test="usageTargetUserName != null and usageTargetUserName != ''">
        `usage_target_user_name`,
      </if>
      <if test="chargeUserId != null">
        `charge_user_id`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="materielId != null and materielId != ''">
        #{materielId,jdbcType=VARCHAR},
      </if>
      <if test="usageUserEntityId != null and usageUserEntityId != ''">
        #{usageUserEntityId,jdbcType=VARCHAR},
      </if>
      <if test="usageUserName != null and usageUserName != ''">
        #{usageUserName,jdbcType=VARCHAR},
      </if>
      <if test="usageType != null">
        #{usageType,jdbcType=TINYINT},
      </if>
      <if test="usageTargetUserEntityId != null and usageTargetUserEntityId != ''">
        #{usageTargetUserEntityId,jdbcType=VARCHAR},
      </if>
      <if test="usageTargetUserName != null and usageTargetUserName != ''">
        #{usageTargetUserName,jdbcType=VARCHAR},
      </if>
      <if test="chargeUserId != null">
        #{chargeUserId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="materielId != null and materielId != ''">
        `materiel_id` = #{materielId,jdbcType=VARCHAR},
      </if>
      <if test="usageUserEntityId != null and usageUserEntityId != ''">
        `usage_user_entity_id` = #{usageUserEntityId,jdbcType=VARCHAR},
      </if>
      <if test="usageUserName != null and usageUserName != ''">
        `usage_user_name` = #{usageUserName,jdbcType=VARCHAR},
      </if>
      <if test="usageType != null">
        `usage_type` = #{usageType,jdbcType=TINYINT},
      </if>
      <if test="usageTargetUserEntityId != null and usageTargetUserEntityId != ''">
        `usage_target_user_entity_id` = #{usageTargetUserEntityId,jdbcType=VARCHAR},
      </if>
      <if test="usageTargetUserName != null and usageTargetUserName != ''">
        `usage_target_user_name` = #{usageTargetUserName,jdbcType=VARCHAR},
      </if>
      <if test="chargeUserId != null">
        `charge_user_id` = #{chargeUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <select id="dashboard" resultType="com.fotile.resourcescenter.materiel.pojo.vo.MaterielDashboardVO">
    select sum(case when usage_type = 30 then 1 end)                                                    totalVisitCount,
           sum(case
                 when usage_type = 30 and created_date &gt;= #{firstDayOfMonth} and created_date <![CDATA[<=]]> #{lastDayOfMonth}
    then 1 end)                                                                      monthlyVisitCount,
    sum(case
    when usage_type = 30 and created_date &gt;= #{start} and created_date <![CDATA[<=]]> #{end}
    then 1 end)                                                                      yesterdayVisitCount,
    sum(case
    when usage_type = 30 and created_date &gt;= #{startOfDay} and created_date <![CDATA[<=]]> #{endOfDay}
    then 1 end)                                                                      dailyVisitCount,
    sum(case when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) then 1 end)            totalUseCount,
    sum(case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{firstDayOfMonth} and created_date <![CDATA[<=]]> #{lastDayOfMonth}
    then 1 end)                                                                      monthlyUseCount,
    sum(case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{start} and created_date <![CDATA[<=]]> #{end}
    then 1 end)                                                                      yesterdayUseCount,
    sum(case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{startOfDay} and created_date <![CDATA[<=]]> #{endOfDay}
    then 1 end)                                                                      dailyUseCount,
    count(distinct case when usage_type = 30 then usage_user_entity_id end)                      totalVisitor,
    count(distinct case
    when usage_type = 30 and created_date &gt;= #{firstDayOfMonth} and
    created_date <![CDATA[<=]]> #{lastDayOfMonth}
    then usage_user_entity_id end)                                        monthlyVisitor,
    count(distinct case
    when usage_type = 30 and created_date &gt;= #{start} and
    created_date <![CDATA[<=]]> #{end}
    then usage_user_entity_id end)                                        yesterdayVisitor,
    count(distinct case
    when usage_type = 30 and created_date &gt;= #{startOfDay} and
    created_date <![CDATA[<=]]> #{endOfDay}
    then usage_user_entity_id end)                                        dailyVisitor,
    count(distinct case when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) then usage_user_entity_id end) totalUser,
    count(distinct case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{firstDayOfMonth} and created_date <![CDATA[<=]]> #{lastDayOfMonth}
    then usage_user_entity_id end)                                        monthlyUser,
    count(distinct case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{start} and created_date <![CDATA[<=]]> #{end}
    then usage_user_entity_id end)                                        yesterdayUser,
    count(distinct case
    when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) and
    created_date &gt;= #{startOfDay} and created_date <![CDATA[<=]]> #{endOfDay}
    then usage_user_entity_id end)                                        dailyUser,
    count(distinct case
    when (usage_type = 20)
    then usage_user_entity_id end)                                        totalUploader,
    count(distinct case
    when (usage_type = 20)
    and
    created_date &gt;= #{startOfDay} and created_date <![CDATA[<=]]> #{endOfDay}
    then usage_user_entity_id end)                                        dailyUploader,
    sum( case
    when (usage_type = 20 )
    and
    created_date &gt;= #{startOfDay} and created_date <![CDATA[<=]]> #{endOfDay}
    then 1 end)                                                             dailyUploadCount
    from resourcescenter.materiel_usage_history;
  </select>

  <select id="detailListCount" resultType="java.lang.Integer">
      select count(1)
      from resourcescenter.materiel_usage_history muh
      left join resourcescenter.materiel m on m.id = muh.materiel_id
    left join resourcescenter.materiel_category mc on mc.id = m.materiel_category_id

    <if test="goodsName != null and goodsName.size() != 0">
      inner join( select distinct materiel_id from  `resourcescenter`.`materiel_goods_relevance` mgr
      <where>
        mgr.is_deleted=0

        and mgr.goods_code
        <foreach close=")" collection="goodsName" item="item" open="in (" separator=",">
          #{item}
        </foreach>

      </where>
      )mgr
      on m.id = mgr.materiel_id
    </if>

      where muh.is_deleted = 0 and muh.usage_type in (10,11,12,13,14)
    <if test="title != null  and title != ''">
      and m.title like concat('%',#{title},'%')
    </if>
      <if test="materielId != null and  materielId != ''">
        and m.id like  concat('%',#{materielId},'%')
      </if>
      <if test="uploadUser != null and uploadUser != '' ">
          and   AES_DECRYPT(UNHEX(m.reserve_item1), #{aesKey}) like concat('%',#{uploadUser},'%')
      </if>
      <if test="startUploadTime != null and startUploadTime != ''">
          and m.ctime &gt;= #{startUploadTime}
      </if>
      <if test="endUploadTime != null and endUploadTime != ''">
          and m.ctime <![CDATA[<=]]> #{endUploadTime}
      </if>
      <if test="startUseTime != null and startUseTime != ''">
          and muh.created_date <![CDATA[>=]]> #{startUseTime}
      </if>
      <if test="endUseTime != null and endUseTime != ''">
          and muh.created_date <![CDATA[<=]]> #{endUseTime}
      </if>
      <if test="materielCategoryPropertyDicIds != null and materielCategoryPropertyDicIds.size() != 0">
          and m.materiel_category_property_dic_id
          <foreach close=")" collection="materielCategoryPropertyDicIds" item="item" open="in (" separator=",">
              #{item}
          </foreach>
      </if>
    <if test="categoryId != null and categoryId != ''">
      and concat('-',mc.full_path_id,'-') like  concat('%-',#{categoryId,jdbcType=VARCHAR},'-%')
    </if>
  </select>

  <select id="detailList" resultType="com.fotile.resourcescenter.materiel.pojo.vo.MaterielDetailVO">
<!--    /**-->
<!--    * 素材ID-->
<!--    */-->
<!--    private String materielId;-->
<!--    /**-->
<!--    * 标题-->
<!--    */-->
<!--    private String materielTitle;-->
<!--    /**-->
<!--    * l1-l4素材分类-->
<!--    */-->
<!--    private String materielCategory;-->
<!--    /**-->
<!--    * l5素材明细-->
<!--    */-->
<!--    private String materielCategoryPropertyDic;-->
<!--    /**-->
<!--    * 产品型号-->
<!--    */-->
<!--    private String goodsName;-->
<!--    /**-->
<!--    * 使用人-->
<!--    */-->
<!--    private String useUser;-->
<!--    /**-->
<!--    * 使用人信息-->
<!--    */-->
<!--    private String useUserInfo;-->
<!--    /**-->
<!--    * 操作动作-->
<!--    */-->
<!--    private String action;-->
<!--    /**-->
<!--    * 操作duixiang-->
<!--    */-->
<!--    private String actionObject;-->
<!--    /**-->
<!--    * 操作时间-->
<!--    */-->
<!--    private String actionTime;-->
<!--    /**-->
<!--    * 发布时间-->
<!--    */-->
<!--    private String publishTime;-->
<!--    /**-->
<!--    * 上传人-->
<!--    */-->
<!--    private String uploadUser;-->
    select m.id materielId,
    m.title materielTitle,
    mc.full_path_name materielCategory,
    mcp.propertyName materielCategoryPropertyDic,
    m.reserve_item1 uploadUser
    ,muh.usage_user_name useUser,
    mgr.goodsName goodsName,
<!--    10.点赞  11.分享 12.下载  20 上传  30访问-->
    case  muh.usage_type when 10 then '点赞'
    when 11 then '分享'
    when 12 then '下载'
    when 14 then '收藏'
    when 20 then '上传'
    when 30 then '访问' end action,
    muh.usage_target_user_name actionObject,
    muh.created_date actionTime,
    m.ctime publishTime,
    muh.charge_user_id chargeUserId
    from resourcescenter.materiel_usage_history muh
    left join resourcescenter.materiel m on m.id = muh.materiel_id
    left join resourcescenter.materiel_category mc on mc.id = m.materiel_category_id
    left join (select group_concat(distinct mcp.property_name) propertyName, dic_id
    from resourcescenter.materiel_category_property mcp
    where mcp.is_deleted = 0
    group by mcp.dic_id) mcp on mcp.dic_id = m.materiel_category_property_dic_id
    left join (select group_concat(mgr.goods_name)  goodsName,materiel_id from resourcescenter.materiel_goods_relevance mgr where  mgr.is_deleted = 0 group by mgr.materiel_id) mgr on m.id = mgr.materiel_id

    <if test="goodsName != null and goodsName.size() != 0">
    inner join( select distinct materiel_id  from  `resourcescenter`.`materiel_goods_relevance` mgr
    <where>
      mgr.is_deleted=0

        and mgr.goods_code
        <foreach close=")" collection="goodsName" item="item" open="in (" separator=",">
          #{item}
        </foreach>

    </where>
    )mgr1
    on m.id = mgr1.materiel_id
    </if>
    where muh.is_deleted = 0 and muh.usage_type in (10,11,12,13,14)
    <if test="materielId != null  and  materielId != ''">
      and m.id like  concat('%',#{materielId},'%')
    </if>
    <if test="title != null  and title != ''">
      and m.title like concat('%',#{title},'%')
    </if>
    <if test="uploadUser != null and uploadUser != '' ">
      and   AES_DECRYPT(UNHEX(m.reserve_item1), #{aesKey}) like concat('%',#{uploadUser},'%')
    </if>
    <if test="startUploadTime != null and startUploadTime != ''">
      and m.ctime &gt;= #{startUploadTime}
    </if>
    <if test="endUploadTime != null and endUploadTime != ''">
      and m.ctime <![CDATA[<=]]> #{endUploadTime}
    </if>
    <if test="startUseTime != null and startUseTime != ''">
      and muh.created_date <![CDATA[>=]]> #{startUseTime}
    </if>
    <if test="endUseTime != null and endUseTime != ''">
      and muh.created_date <![CDATA[<=]]> #{endUseTime}
    </if>
    <if test="materielCategoryPropertyDicIds != null and materielCategoryPropertyDicIds.size() != 0">
      and m.materiel_category_property_dic_id
      <foreach close=")" collection="materielCategoryPropertyDicIds" item="item" open="in (" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="categoryId != null and categoryId != ''">
      and concat('-',mc.full_path_id,'-') like  concat('%-',#{categoryId,jdbcType=VARCHAR},'-%')
    </if>
    order by muh.created_date desc
    limit #{offset},#{pageSize}
  </select>

  <select id="materielDetailSummarization" resultType="com.fotile.resourcescenter.materiel.pojo.vo.MaterielDetailPageInfoVO">
    select count(distinct case when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) then usage_user_entity_id end) totalUser,
    sum(case when (usage_type = 10 or usage_type = 11 or usage_type = 12 or usage_type = 14) then 1 end)            totalUseCount,
    count(distinct case
    when (usage_type = 20)
    then usage_user_entity_id end)                                        totalUploader,
    sum( case
    when (usage_type = 20 )
    then 1 end)                                                             totalUploaderCount
    from resourcescenter.materiel_usage_history muh
    left join resourcescenter.materiel m on m.id = muh.materiel_id
    <if test="goodsName != null and goodsName.size() != 0">
      inner join( select * from  `resourcescenter`.`materiel_goods_relevance` mgr
      <where>
        mgr.is_deleted=0

        and mgr.goods_code
        <foreach close=")" collection="goodsName" item="item" open="in (" separator=",">
          #{item}
        </foreach>

      </where>
      )mgr
      on m.id = mgr.materiel_id
    </if>

    where muh.is_deleted = 0
    <if test="title != null  and title != ''">
      and m.title like concat('%',#{title},'%')
    </if>
    <if test="materielId != null and  materielId != ''">
      and m.id = #{materielId}
    </if>
    <if test="uploadUser != null and uploadUser != '' ">
      and m.reserve_item1 = #{uploadUser}
    </if>
    <if test="startUploadTime != null and startUploadTime != ''">
      and m.ctime &gt;= #{startUploadTime}
    </if>
    <if test="endUploadTime != null and endUploadTime != ''">
      and m.ctime <![CDATA[<=]]> #{endUploadTime}
    </if>
    <if test="startUseTime != null and startUseTime != ''">
      and muh.created_date <![CDATA[>=]]> #{startUseTime}
    </if>
    <if test="endUseTime != null and endUseTime != ''">
      and muh.created_date <![CDATA[<=]]> #{endUseTime}
    </if>
    <if test="materielCategoryPropertyDicIds != null and materielCategoryPropertyDicIds.size() != 0">
      and m.materiel_category_property_dic_id
      <foreach close=")" collection="materielCategoryPropertyDicIds" item="item" open="in (" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="goodsName != null and goodsName.size() != 0">
      and mgr.goods_code
      <foreach close=")" collection="goodsName" item="item" open="in (" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="categoryId != null and categoryId != ''">
      and m.materiel_category_id = #{categoryId}
    </if>
  </select>
</mapper>