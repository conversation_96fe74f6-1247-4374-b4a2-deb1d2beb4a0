<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.groupmsg.dao.TQywxGroupEffectCustomerMappingBakDao">
  <resultMap id="BaseResultMap" type="com.fotile.customercenter.groupmsg.pojo.entity.TQywxGroupEffectCustomerMappingBak">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_msg_id" jdbcType="BIGINT" property="groupMsgId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="external_userid" jdbcType="VARCHAR" property="externalUserid" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="charge_user_id" jdbcType="BIGINT" property="chargeUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, group_msg_id, account_id, user_id, external_userid, is_deleted, created_by, created_date, 
    modified_by, modified_date, charge_user_id
  </sql>

    <insert id="insertBatch">
      <foreach collection="list" item="item" index="index" separator=";">
        insert into t_qywx_group_effect_customer_mapping_bak
        (group_msg_id, account_id, user_id, external_userid, is_deleted, created_by, created_date, modified_by, modified_date, charge_user_id)
        values
        (#{item.groupMsgId,jdbcType=BIGINT}, #{item.accountId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.externalUserid,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=TINYINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.chargeUserId,jdbcType=BIGINT})
      </foreach>
    </insert>
</mapper>