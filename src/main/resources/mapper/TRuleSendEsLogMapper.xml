<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.sopcenter.rule.dao.TRuleSendEsLogMapper">
  <resultMap id="BaseResultMap" type="com.fotile.sopcenter.rule.pojo.entity.TRuleSendEsLogEntity">
    <!--@mbg.generated-->
    <!--@Table `msgcenter`.`t_rule_send_es_log`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="failure_desc" jdbcType="VARCHAR" property="failureDesc" />
    <result column="clues_id" jdbcType="BIGINT" property="cluesId" />
    <result column="external_userid" jdbcType="VARCHAR" property="externalUserid" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="param" jdbcType="VARCHAR" property="param" />
    <result column="send_category" jdbcType="INTEGER" property="sendCategory" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, 
    `template_id`, `send_time`, `status`, `failure_desc`, `clues_id`, `external_userid`, 
    `user_id`, `param`, `send_category`, `rule_id`, `account_id`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_rule_send_es_log`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`template_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`send_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.sendTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`failure_desc` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.failureDesc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`clues_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.cluesId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`external_userid` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.externalUserid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`param` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.param,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`send_category` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.sendCategory,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`account_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.accountId,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_rule_send_es_log`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`template_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`send_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sendTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.sendTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`failure_desc` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.failureDesc != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.failureDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`clues_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.cluesId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.cluesId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`external_userid` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.externalUserid != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.externalUserid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`param` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.param != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.param,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`send_category` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sendCategory != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.sendCategory,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`account_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.accountId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_send_es_log`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `template_id`, 
      `send_time`, `status`, `failure_desc`, `clues_id`, `external_userid`, `user_id`, 
      `param`, `send_category`, `rule_id`, `account_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.templateId,jdbcType=BIGINT}, 
        #{item.sendTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.failureDesc,jdbcType=VARCHAR}, 
        #{item.cluesId,jdbcType=BIGINT}, #{item.externalUserid,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.param,jdbcType=VARCHAR}, #{item.sendCategory,jdbcType=INTEGER}, #{item.ruleId,jdbcType=BIGINT}, 
        #{item.accountId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.rule.pojo.entity.TRuleSendEsLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_send_es_log`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `template_id`,
      `send_time`,
      `status`,
      `failure_desc`,
      `clues_id`,
      `external_userid`,
      `user_id`,
      `param`,
      `send_category`,
      `rule_id`,
      `account_id`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{templateId,jdbcType=BIGINT},
      #{sendTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER},
      #{failureDesc,jdbcType=VARCHAR},
      #{cluesId,jdbcType=BIGINT},
      #{externalUserid,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR},
      #{param,jdbcType=VARCHAR},
      #{sendCategory,jdbcType=INTEGER},
      #{ruleId,jdbcType=BIGINT},
      #{accountId,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `template_id` = #{templateId,jdbcType=BIGINT},
      `send_time` = #{sendTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      `failure_desc` = #{failureDesc,jdbcType=VARCHAR},
      `clues_id` = #{cluesId,jdbcType=BIGINT},
      `external_userid` = #{externalUserid,jdbcType=VARCHAR},
      `user_id` = #{userId,jdbcType=VARCHAR},
      `param` = #{param,jdbcType=VARCHAR},
      `send_category` = #{sendCategory,jdbcType=INTEGER},
      `rule_id` = #{ruleId,jdbcType=BIGINT},
      `account_id` = #{accountId,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.rule.pojo.entity.TRuleSendEsLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_send_es_log`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="templateId != null">
        `template_id`,
      </if>
      <if test="sendTime != null">
        `send_time`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="failureDesc != null and failureDesc != ''">
        `failure_desc`,
      </if>
      <if test="cluesId != null">
        `clues_id`,
      </if>
      <if test="externalUserid != null and externalUserid != ''">
        `external_userid`,
      </if>
      <if test="userId != null and userId != ''">
        `user_id`,
      </if>
      <if test="param != null and param != ''">
        `param`,
      </if>
      <if test="sendCategory != null">
        `send_category`,
      </if>
      <if test="ruleId != null">
        `rule_id`,
      </if>
      <if test="accountId != null and accountId != ''">
        `account_id`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="failureDesc != null and failureDesc != ''">
        #{failureDesc,jdbcType=VARCHAR},
      </if>
      <if test="cluesId != null">
        #{cluesId,jdbcType=BIGINT},
      </if>
      <if test="externalUserid != null and externalUserid != ''">
        #{externalUserid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != ''">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="param != null and param != ''">
        #{param,jdbcType=VARCHAR},
      </if>
      <if test="sendCategory != null">
        #{sendCategory,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null and accountId != ''">
        #{accountId,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="templateId != null">
        `template_id` = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="sendTime != null">
        `send_time` = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="failureDesc != null and failureDesc != ''">
        `failure_desc` = #{failureDesc,jdbcType=VARCHAR},
      </if>
      <if test="cluesId != null">
        `clues_id` = #{cluesId,jdbcType=BIGINT},
      </if>
      <if test="externalUserid != null and externalUserid != ''">
        `external_userid` = #{externalUserid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != ''">
        `user_id` = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="param != null and param != ''">
        `param` = #{param,jdbcType=VARCHAR},
      </if>
      <if test="sendCategory != null">
        `send_category` = #{sendCategory,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        `rule_id` = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null and accountId != ''">
        `account_id` = #{accountId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>