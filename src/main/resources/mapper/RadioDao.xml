<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.orgcenter.radio.mapper.RadioDao" >
   
  <!-- 新增频道 -->
  <insert id="addRadio" parameterType="radioEntity" useGeneratedKeys="true" keyProperty="id">
    	INSERT INTO orgcenter.t_radio(is_deleted,code,name,note,channel_id,status,created_by,created_date,modified_by,modified_date)
		VALUES(0,#{code},#{name},#{note},#{channelId},#{status},#{createdBy},now(),#{modifiedBy},now())  
  </insert>
  
   <!-- 分页查询(根据渠道id查询频道) -->
   <sql id="findPageAllWHERE">
   		WHERE r.is_deleted=0 
  	<if test="channelId !=null">
  		AND r.channel_id=#{channelId}
  	</if>
   </sql>
   <select id="findPageAllTotal" resultType="int">
  	SELECT COUNT(r.id)
  	FROM orgcenter.t_radio r
  	<include refid="findPageAllWHERE"/>
  </select>
  <select id="findPageAll" resultType="com.fotile.orgcenter.radio.pojo.dto.FindRadioPageAllOutDto">
  	SELECT r.id,r.code,r.name,r.note,CASE r.status WHEN 0 THEN '禁用' WHEN 1 THEN '启用' END statusName,r.status
  	FROM orgcenter.t_radio r
  	<include refid="findPageAllWHERE"/>
  	ORDER BY r.created_date DESC
  	<if test="pg!=null">
  	limit #{pg.offset},#{pg.size}
  	</if>
  </select>
  
  <select id="findPageAll2" resultType="com.fotile.orgcenter.radio.pojo.dto.FindRadioPageAllOutDto">
  	SELECT r.id,r.code,r.name,r.note,CASE r.status WHEN 0 THEN '禁用' WHEN 1 THEN '启用' END statusName,r.status
  	FROM orgcenter.t_radio r
  	WHERE r.is_deleted=0 AND r.status=1 
  	<if test="channelId !=null">
  		AND r.channel_id=#{channelId}
  	</if>
  	ORDER BY r.created_date DESC
  	<if test="pg!=null">
  	limit #{pg.offset},#{pg.size}
  	</if>
  </select>
  <!-- 根据id查询 -->
  <select id="findById" resultType="com.fotile.orgcenter.radio.pojo.entity.RadioEntity">
  	SELECT r.id,r.code,r.name,r.note,r.status
  	FROM orgcenter.t_radio r
  	WHERE r.id =#{id} AND r.is_deleted=0
  </select>
  
  <!-- 根据编码查询 -->
  <select id="findByCode" resultType="com.fotile.orgcenter.radio.pojo.entity.RadioEntity">
  	SELECT r.id,r.code,r.name,r.note,r.status
  	FROM orgcenter.t_radio r
  	WHERE r.code = #{code} AND r.is_deleted=0
  </select>
  
  <!-- 根据编码集合查询渠道 -->
  <select id="findByCodes" resultType="com.fotile.orgcenter.radio.pojo.dto.FindRadioAndChannelByCodesOutDto">
	SELECT c.id channelId,c.name channelName,c.code channelCode,r.id radioId,r.name radioName,r.code radioCode
	FROM orgcenter.t_channel c
	INNER JOIN orgcenter.t_radio r ON c.id=r.channel_id
	WHERE c.is_deleted=0 AND r.is_deleted=0
	<if test="codeList !=null and codeList.size>0">
		 AND r.code IN
		<foreach collection="codeList" item="code" open="(" separator="," close=")">
			#{code}
		</foreach>
	</if>
  </select>
  
  <!-- 修改 -->
  <update id="update" parameterType="radioEntity">
  	UPDATE orgcenter.t_radio
  	<set>
  		modified_date=now(),modified_by=#{modifiedBy}
  		<if test="code !=null and code !=''"><!-- 频道编码 -->
  			,code=#{code}
  		</if>
  		<if test="name !=null "><!-- 频道名称 -->
  			,name=#{name}
  		</if>
  		<if test="note !=null "><!-- 备注 -->
  			,note=#{note}
  		</if>
  		<if test="status !=null "><!-- 状态 -->
  			,status=#{status}
  		</if>
  	</set>
  	WHERE id=#{id} AND is_deleted=0
  </update>
  
   <!-- 启用/禁用 -->
  <update id="enable">
  	UPDATE orgcenter.t_radio
  	SET status=#{status},modified_by=#{modifiedBy},modified_date=now()
  	WHERE	id IN
  	<foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
  		#{id}
  	</foreach>
  </update>

  <select id="getAllRadioInfoList"
		  resultType="com.fotile.orgcenter.radio.pojo.dto.RadioInfo">
	  select tr.id, tr.`code`, tr.`name`, tr.channel_id, tr.`status`
	  from orgcenter.t_radio tr
	  where tr.is_deleted=0
  </select>

  <select id="getAllChannelRadios" resultType="com.fotile.orgcenter.radio.pojo.dto.FindRadioByCodeOutDto">
	  SELECT
		  *
	  FROM
		  orgcenter.t_radio
	  WHERE
		  channel_id = #{channelId}
	  AND `status`=1
		AND is_deleted = 0;
    </select>
</mapper>