<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.customer.dao.CustomerAddressDao">


    <select id="queryAddressListByParams" resultType="com.fotile.customercenter.customer.pojo.entity.UserAddress">
        select a.*
        from customercenter.user_address a
        <where>
            a.is_deleted = 0
            <if test="customerId!=null">
                and a.customer_info_id = #{customerId}
            </if>
        </where>
        order by created_date desc,id desc
    </select>



    <update id="updateOtherNoDefalut">
        update customercenter.user_address set is_default=0 where is_deleted = 0 and  customer_info_id = #{customerId} and  id != #{id}
    </update>

    <select id="findCountByCustomerId" resultType="java.lang.Long">
        select count(0)
        from customercenter.user_address
        <where>
            is_deleted = 0
            <if test="id != null">
                and id != #{id}
            </if>
            and customer_info_id = #{customerId}
        </where>
    </select>
</mapper>