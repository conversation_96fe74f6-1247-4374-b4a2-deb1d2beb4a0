<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.label.dao.LabelMappingDao">

    <delete id="delLabelMapping">
        delete from customercenter.label_customer_mapping
        where source_table_name = #{sourceTableName} and source_id = #{sourceId}
    </delete>
    <resultMap id="FindLabelByNameOutDto" type="com.fotile.customercenter.label.pojo.dto.FindLabelBySourceIdOutDto">
        <result property="id" column="id"/>
        <result property="sourceTableName" column="sourceTableName"/>
        <result property="sourceId" column="sourceId"/>
        <result property="labelSort" column="labelSort"/>
        <result property="labelId" column="labelId"/>
        <result property="name" column="name"/>
    </resultMap>

    <select id="findLabelBySourceId" resultType="com.fotile.customercenter.label.pojo.dto.FindLabelBySourceIdOutDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,slm.label_sort labelSort,slm.label_id labelId,sl.name name
        from customercenter.label_customer_mapping slm
        left join customercenter.label_customer sl on sl.id =slm.label_id
        where slm.source_table_name = #{sourceTableName} and
        slm.source_id = #{sourceId} and slm.is_deleted = 0
    </select>

    <select id="findSourceIdByLabelId"  resultType="java.lang.String" >
        select
             scm.source_id
        from customercenter.label_customer_mapping scm
        where scm.source_table_name = #{sourceTableName}
        and   scm.label_id = #{sourceId} and scm.is_deleted = 0
    </select>

    <select id="findSourceIdByLabelIds" resultType="java.lang.Long">
        select
        scm.source_id
        from customercenter.label_customer_mapping scm
        where scm.source_table_name = #{sourceTableName}
        and scm.is_deleted = 0
        and scm.label_id in
        <foreach collection="labelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into customercenter.label_customer_mapping (
        created_by, created_date, modified_by, modified_date, source_id, source_table_name, label_id, label_sort
        ) values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.createdBy},
        now(),
        #{entity.modifiedBy},
        now(),
        #{entity.sourceId},
        #{entity.sourceTableName},
        #{entity.labelId},
        #{entity.labelSort}
        )
        </foreach>
    </insert>


    <select id="findLabelBySource" resultType="com.fotile.customercenter.label.pojo.dto.LabelSourceInDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,slm.label_sort labelSort,slm.label_id labelId,sl.name labelName
        from customercenter.label_customer_mapping slm
        left join customercenter.label_customer sl on sl.id =slm.label_id and sl.is_deleted = 0
        where slm.is_deleted = 0
        and slm.source_table_name = #{sourceTableName}
        <if test="sourceIds != null and sourceIds.size > 0">
            and slm.source_id in
            <foreach collection="sourceIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findLabelBySource2" resultType="com.fotile.customercenter.label.pojo.dto.LabelSourceInDto">
        select
        slm.source_table_name sourceTableName ,
        slm.id id ,
        slm.source_id sourceId,slm.label_sort labelSort,slm.label_id labelId,sl.name labelName
        from customercenter.label_customer_mapping slm
        left join customercenter.label_customer sl on sl.id =slm.label_id and sl.is_deleted = 0
        where slm.is_deleted = 0
        and slm.source_table_name = #{sourceTableName}
        and slm.source_id = #{sourceId}
    </select>

    <delete id="delLabelMapping2">
        delete from customercenter.label_customer_mapping
        where source_table_name = #{sourceTableName}
        and source_id in
        <foreach collection="sourceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="insertBatch2" keyProperty="id" useGeneratedKeys="true">
        insert into customercenter.label_customer_mapping (
        created_by, created_date, modified_by, modified_date, source_id, source_table_name, label_id, label_sort
        ) values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.createdBy},
            now(),
            #{entity.modifiedBy},
            now(),
            #{entity.sourceId},
            #{entity.sourceTableName},
            #{entity.labelId},
            #{entity.labelSort}
            )
        </foreach>
    </insert>

</mapper>