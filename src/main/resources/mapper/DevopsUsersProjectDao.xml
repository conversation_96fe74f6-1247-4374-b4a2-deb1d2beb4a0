<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.devops.devopsUsers.dao.DevopsUsersProjectDao">
  <resultMap id="BaseResultMap" type="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="INTEGER" property="tenantId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, project_id, user_id, created_by, created_date, modified_by, modified_date, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from devops_users_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from devops_users_project
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from devops_users_project
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProjectExample">
    delete from devops_users_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <delete id="deleteByUserId">
    delete from devops_users_project
    where user_id = #{userId,jdbcType=BIGINT};
  </delete>
  <insert id="myInsert" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject" useGeneratedKeys="true">
    insert into devops_users_project (tenant_id, project_id, user_id, 
      created_by, created_date, modified_by, 
      modified_date, is_deleted)
    values (#{tenantId,jdbcType=INTEGER}, #{projectId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="imyIsertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject" useGeneratedKeys="true">
    insert into devops_users_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into devops_users_project (tenant_id, project_id, user_id,
                                      created_by, created_date, is_deleted)
    values
           <foreach collection="list" separator=","  item="dto">
                (#{dto.tenantId,jdbcType=INTEGER}, #{dto.projectId,jdbcType=BIGINT}, #{dto.userId,jdbcType=BIGINT},
                #{dto.createdBy,jdbcType=VARCHAR}, #{dto.createdDate,jdbcType=TIMESTAMP}, #{dto.isDeleted,jdbcType=TINYINT})
           </foreach>
  </insert>
  <select id="countByExample" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProjectExample" resultType="java.lang.Long">
    select count(*) from devops_users_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="queryByKeyClockId" resultType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject">
      SELECT
        dup.*
      FROM
        devops_users_project dup
          LEFT JOIN devops_users du ON du.id = dup.user_id
          AND dup.is_deleted = 0
      WHERE
        du.keycloak_id = #{keyColckId}
        and du.is_deleted = 0
    </select>
    <select id="pageCount" resultType="java.lang.Integer">
      SELECT
        COUNT(du.id)
      FROM
        devops_users_project dup
          LEFT JOIN devops_users du ON dup.user_id = du.id
        <include refid="pageWhere" />
    </select>
  <select id="getProjectUsers" resultType="com.fotile.devops.devopsUsers.pojo.dto.DevopsUserSelectOutDTO">
    SELECT
      du.*,
    dup.id projectUserId
    FROM
      devops_users_project dup
        LEFT JOIN devops_users du ON dup.user_id = du.id
    <include refid="pageWhere" />
    ORDER BY
    dup.id DESC
    limit #{pg.offset},#{pg.size}
  </select>

  <sql id="pageWhere">
    <where>
      dup.is_deleted = 0
      AND du.is_deleted = 0
      <if test="dto.projectId != null">
        AND dup.project_id = #{dto.projectId}
      </if>
    </where>
  </sql>

    <update id="updateByExampleSelective" parameterType="map">
    update devops_users_project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=INTEGER},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null">
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null">
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null">
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update devops_users_project
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=INTEGER},
      project_id = #{record.projectId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="myUpdateByPrimaryKeySelective" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject">
    update devops_users_project
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="myUpdateByPrimaryKey" parameterType="com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject">
    update devops_users_project
    set tenant_id = #{tenantId,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>