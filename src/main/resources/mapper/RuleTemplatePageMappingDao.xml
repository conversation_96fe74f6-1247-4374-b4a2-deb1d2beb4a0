<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.sopcenter.rule.dao.RuleTemplatePageMappingDao">
    <update id="updateRuleTemplatePage">
        UPDATE msgcenter.rule_template_page_mapping
        SET is_deleted=1,
        modified_by = #{modifiedBy},
        modified_date = now()
        where source_table_name=#{sourceTable} and source_id=#{sourceId}
    </update>
</mapper>