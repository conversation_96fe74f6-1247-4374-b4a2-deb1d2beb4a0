<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.toolscenter.configurationuserinfo.dao.DtToolConfigurationUserinfoDao">
  <resultMap id="BaseResultMap" type="com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="no" jdbcType="VARCHAR" property="no" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="tank_length" jdbcType="DECIMAL" property="tankLength" />
    <result column="tank_width" jdbcType="DECIMAL" property="tankWidth" />
    <result column="overall_height" jdbcType="DECIMAL" property="overallHeight" />
    <result column="overall_width" jdbcType="DECIMAL" property="overallWidth" />
    <result column="overall_deep" jdbcType="DECIMAL" property="overallDeep" />
    <result column="is_water_separator" jdbcType="VARCHAR" property="isWaterSeparator" />
    <result column="overall_big_distance" jdbcType="DECIMAL" property="overallBigDistance" />
    <result column="is_cabinet_angle_valve" jdbcType="VARCHAR" property="isCabinetAngleValve" />
    <result column="angle_big_distance" jdbcType="DECIMAL" property="angleBigDistance" />
    <result column="wall_height" jdbcType="DECIMAL" property="wallHeight" />
    <result column="is_socket" jdbcType="VARCHAR" property="isSocket" />
    <result column="is_gas" jdbcType="VARCHAR" property="isGas" />
    <result column="gas_big_distance" jdbcType="DECIMAL" property="gasBigDistance" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_del" jdbcType="VARCHAR" property="isDel" />
    <result column="reserve_item1" jdbcType="VARCHAR" property="reserveItem1" />
    <result column="reserve_item2" jdbcType="VARCHAR" property="reserveItem2" />
    <result column="reserve_item3" jdbcType="VARCHAR" property="reserveItem3" />
    <result column="reserve_item4" jdbcType="VARCHAR" property="reserveItem4" />
    <result column="reserve_item5" jdbcType="VARCHAR" property="reserveItem5" />
    <result column="reserve_item6" jdbcType="VARCHAR" property="reserveItem6" />
    <result column="reserve_item7" jdbcType="VARCHAR" property="reserveItem7" />
    <result column="reserve_item8" jdbcType="VARCHAR" property="reserveItem8" />
    <result column="reserve_item9" jdbcType="VARCHAR" property="reserveItem9" />
    <result column="reserve_item10" jdbcType="VARCHAR" property="reserveItem10" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `no`, customer_name, customer_phone, user_id, tank_length, tank_width, overall_height, 
    overall_width, overall_deep, is_water_separator, overall_big_distance, is_cabinet_angle_valve, 
    angle_big_distance, wall_height, is_socket, is_gas, gas_big_distance, ctime, creator, 
    mtime, modifier, is_del, reserve_item1, reserve_item2, reserve_item3, reserve_item4, 
    reserve_item5, reserve_item6, reserve_item7, reserve_item8, reserve_item9, reserve_item10
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dt_tool_configuration_userinfo
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from dt_tool_configuration_userinfo
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo" useGeneratedKeys="true">
    insert into dt_tool_configuration_userinfo (id,`no`, customer_name, customer_phone,
      user_id, tank_length, tank_width, 
      overall_height, overall_width, overall_deep, 
      is_water_separator, overall_big_distance, is_cabinet_angle_valve, 
      angle_big_distance, wall_height, is_socket, 
      is_gas, gas_big_distance, ctime, 
      creator, mtime, modifier, 
      is_del, reserve_item1, reserve_item2, 
      reserve_item3, reserve_item4, reserve_item5, 
      reserve_item6, reserve_item7, reserve_item8, 
      reserve_item9, reserve_item10)
    values (#{id,jdbcType=VARCHAR},#{no,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerPhone,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR}, #{tankLength,jdbcType=DECIMAL}, #{tankWidth,jdbcType=DECIMAL}, 
      #{overallHeight,jdbcType=DECIMAL}, #{overallWidth,jdbcType=DECIMAL}, #{overallDeep,jdbcType=DECIMAL}, 
      #{isWaterSeparator,jdbcType=VARCHAR}, #{overallBigDistance,jdbcType=DECIMAL}, #{isCabinetAngleValve,jdbcType=VARCHAR}, 
      #{angleBigDistance,jdbcType=DECIMAL}, #{wallHeight,jdbcType=DECIMAL}, #{isSocket,jdbcType=VARCHAR}, 
      #{isGas,jdbcType=VARCHAR}, #{gasBigDistance,jdbcType=DECIMAL}, #{ctime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{mtime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=VARCHAR}, #{reserveItem1,jdbcType=VARCHAR}, #{reserveItem2,jdbcType=VARCHAR}, 
      #{reserveItem3,jdbcType=VARCHAR}, #{reserveItem4,jdbcType=VARCHAR}, #{reserveItem5,jdbcType=VARCHAR}, 
      #{reserveItem6,jdbcType=VARCHAR}, #{reserveItem7,jdbcType=VARCHAR}, #{reserveItem8,jdbcType=VARCHAR}, 
      #{reserveItem9,jdbcType=VARCHAR}, #{reserveItem10,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo" useGeneratedKeys="true">
    insert into dt_tool_configuration_userinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="no != null">
        `no`,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="tankLength != null">
        tank_length,
      </if>
      <if test="tankWidth != null">
        tank_width,
      </if>
      <if test="overallHeight != null">
        overall_height,
      </if>
      <if test="overallWidth != null">
        overall_width,
      </if>
      <if test="overallDeep != null">
        overall_deep,
      </if>
      <if test="isWaterSeparator != null">
        is_water_separator,
      </if>
      <if test="overallBigDistance != null">
        overall_big_distance,
      </if>
      <if test="isCabinetAngleValve != null">
        is_cabinet_angle_valve,
      </if>
      <if test="angleBigDistance != null">
        angle_big_distance,
      </if>
      <if test="wallHeight != null">
        wall_height,
      </if>
      <if test="isSocket != null">
        is_socket,
      </if>
      <if test="isGas != null">
        is_gas,
      </if>
      <if test="gasBigDistance != null">
        gas_big_distance,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="reserveItem1 != null">
        reserve_item1,
      </if>
      <if test="reserveItem2 != null">
        reserve_item2,
      </if>
      <if test="reserveItem3 != null">
        reserve_item3,
      </if>
      <if test="reserveItem4 != null">
        reserve_item4,
      </if>
      <if test="reserveItem5 != null">
        reserve_item5,
      </if>
      <if test="reserveItem6 != null">
        reserve_item6,
      </if>
      <if test="reserveItem7 != null">
        reserve_item7,
      </if>
      <if test="reserveItem8 != null">
        reserve_item8,
      </if>
      <if test="reserveItem9 != null">
        reserve_item9,
      </if>
      <if test="reserveItem10 != null">
        reserve_item10,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="no != null">
        #{no,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tankLength != null">
        #{tankLength,jdbcType=DECIMAL},
      </if>
      <if test="tankWidth != null">
        #{tankWidth,jdbcType=DECIMAL},
      </if>
      <if test="overallHeight != null">
        #{overallHeight,jdbcType=DECIMAL},
      </if>
      <if test="overallWidth != null">
        #{overallWidth,jdbcType=DECIMAL},
      </if>
      <if test="overallDeep != null">
        #{overallDeep,jdbcType=DECIMAL},
      </if>
      <if test="isWaterSeparator != null">
        #{isWaterSeparator,jdbcType=VARCHAR},
      </if>
      <if test="overallBigDistance != null">
        #{overallBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="isCabinetAngleValve != null">
        #{isCabinetAngleValve,jdbcType=VARCHAR},
      </if>
      <if test="angleBigDistance != null">
        #{angleBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="wallHeight != null">
        #{wallHeight,jdbcType=DECIMAL},
      </if>
      <if test="isSocket != null">
        #{isSocket,jdbcType=VARCHAR},
      </if>
      <if test="isGas != null">
        #{isGas,jdbcType=VARCHAR},
      </if>
      <if test="gasBigDistance != null">
        #{gasBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        #{reserveItem10,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo">
    update dt_tool_configuration_userinfo
    <set>
      <if test="no != null">
        `no` = #{no,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tankLength != null">
        tank_length = #{tankLength,jdbcType=DECIMAL},
      </if>
      <if test="tankWidth != null">
        tank_width = #{tankWidth,jdbcType=DECIMAL},
      </if>
      <if test="overallHeight != null">
        overall_height = #{overallHeight,jdbcType=DECIMAL},
      </if>
      <if test="overallWidth != null">
        overall_width = #{overallWidth,jdbcType=DECIMAL},
      </if>
      <if test="overallDeep != null">
        overall_deep = #{overallDeep,jdbcType=DECIMAL},
      </if>
      <if test="isWaterSeparator != null">
        is_water_separator = #{isWaterSeparator,jdbcType=VARCHAR},
      </if>
      <if test="overallBigDistance != null">
        overall_big_distance = #{overallBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="isCabinetAngleValve != null">
        is_cabinet_angle_valve = #{isCabinetAngleValve,jdbcType=VARCHAR},
      </if>
      <if test="angleBigDistance != null">
        angle_big_distance = #{angleBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="wallHeight != null">
        wall_height = #{wallHeight,jdbcType=DECIMAL},
      </if>
      <if test="isSocket != null">
        is_socket = #{isSocket,jdbcType=VARCHAR},
      </if>
      <if test="isGas != null">
        is_gas = #{isGas,jdbcType=VARCHAR},
      </if>
      <if test="gasBigDistance != null">
        gas_big_distance = #{gasBigDistance,jdbcType=DECIMAL},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        reserve_item10 = #{reserveItem10,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo">
    update dt_tool_configuration_userinfo
    set `no` = #{no,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      tank_length = #{tankLength,jdbcType=DECIMAL},
      tank_width = #{tankWidth,jdbcType=DECIMAL},
      overall_height = #{overallHeight,jdbcType=DECIMAL},
      overall_width = #{overallWidth,jdbcType=DECIMAL},
      overall_deep = #{overallDeep,jdbcType=DECIMAL},
      is_water_separator = #{isWaterSeparator,jdbcType=VARCHAR},
      overall_big_distance = #{overallBigDistance,jdbcType=DECIMAL},
      is_cabinet_angle_valve = #{isCabinetAngleValve,jdbcType=VARCHAR},
      angle_big_distance = #{angleBigDistance,jdbcType=DECIMAL},
      wall_height = #{wallHeight,jdbcType=DECIMAL},
      is_socket = #{isSocket,jdbcType=VARCHAR},
      is_gas = #{isGas,jdbcType=VARCHAR},
      gas_big_distance = #{gasBigDistance,jdbcType=DECIMAL},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=VARCHAR},
      reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{reserveItem10,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getMyRecordList"   parameterType="com.fotile.toolscenter.configurationuserinfo.pojo.dto.GetMyRecordListRequestTO"  resultType="com.fotile.toolscenter.configurationuserinfo.pojo.dto.GetMyRecordListVO">
    SELECT
       A.id,
       A.`no`,
       A.customer_name customerName,
       A.customer_phone customerPhone,
       A.ctime
    FROM
      dt_tool_configuration_userinfo A
    where
      A.user_id = #{requestTO.mobile,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
    <if test="requestTO.keyword != null and requestTO.keyword != ''">
      and A.customer_phone = #{requestTO.keyword,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
    </if>
    order by A.ctime desc
  </select>

</mapper>