<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.sopcenter.rule.dao.TTemplateFrequencyMapper">
  <resultMap id="BaseResultMap" type="com.fotile.sopcenter.rule.pojo.entity.TTemplateFrequencyEntity">
    <!--@mbg.generated-->
    <!--@Table `msgcenter`.`t_template_frequency`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="frequency_type" jdbcType="INTEGER" property="frequencyType" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, 
    `frequency_type`, `day`, `number`, `template_id`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_template_frequency`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`frequency_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.frequencyType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`day` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.day,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`number` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.number,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`template_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_template_frequency`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`frequency_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.frequencyType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.frequencyType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`day` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.day != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.day,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`number` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.number != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.number,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`template_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.templateId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.templateId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_template_frequency`
    (`is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `frequency_type`, 
      `day`, `number`, `template_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.frequencyType,jdbcType=INTEGER}, 
        #{item.day,jdbcType=INTEGER}, #{item.number,jdbcType=INTEGER}, #{item.templateId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.rule.pojo.entity.TTemplateFrequencyEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_template_frequency`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `frequency_type`,
      `day`,
      `number`,
      `template_id`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{frequencyType,jdbcType=INTEGER},
      #{day,jdbcType=INTEGER},
      #{number,jdbcType=INTEGER},
      #{templateId,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `frequency_type` = #{frequencyType,jdbcType=INTEGER},
      `day` = #{day,jdbcType=INTEGER},
      `number` = #{number,jdbcType=INTEGER},
      `template_id` = #{templateId,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.rule.pojo.entity.TTemplateFrequencyEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_template_frequency`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="frequencyType != null">
        `frequency_type`,
      </if>
      <if test="day != null">
        `day`,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="templateId != null">
        `template_id`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="frequencyType != null">
        #{frequencyType,jdbcType=INTEGER},
      </if>
      <if test="day != null">
        #{day,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="frequencyType != null">
        `frequency_type` = #{frequencyType,jdbcType=INTEGER},
      </if>
      <if test="day != null">
        `day` = #{day,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        `template_id` = #{templateId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>