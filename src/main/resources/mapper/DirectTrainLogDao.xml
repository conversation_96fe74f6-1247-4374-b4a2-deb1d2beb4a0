<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.devops.issue.dao.DirectTrainLogDao">
  <resultMap id="BaseResultMap" type="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="response_code" jdbcType="VARCHAR" property="responseCode" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="request_params" jdbcType="LONGVARCHAR" property="requestParams" />
    <result column="response_result" jdbcType="LONGVARCHAR" property="responseResult" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_url, response_code, created_time
  </sql>
  <sql id="Blob_Column_List">
    request_params, response_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from direct_train_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from direct_train_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from direct_train_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from direct_train_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLogExample">
    delete from direct_train_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="myInsert" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLog" useGeneratedKeys="true">
    insert into direct_train_log (request_url, response_code, created_time, 
      request_params, response_result)
    values (#{requestUrl,jdbcType=VARCHAR}, #{responseCode,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{requestParams,jdbcType=LONGVARCHAR}, #{responseResult,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="myInsertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLog" useGeneratedKeys="true">
    insert into direct_train_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="responseCode != null">
        response_code,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="requestParams != null">
        request_params,
      </if>
      <if test="responseResult != null">
        response_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null">
        #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestParams != null">
        #{requestParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseResult != null">
        #{responseResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLogExample" resultType="java.lang.Long">
    select count(*) from direct_train_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update direct_train_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.responseCode != null">
        response_code = #{record.responseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestParams != null">
        request_params = #{record.requestParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.responseResult != null">
        response_result = #{record.responseResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update direct_train_log
    set id = #{record.id,jdbcType=BIGINT},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      response_code = #{record.responseCode,jdbcType=VARCHAR},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      request_params = #{record.requestParams,jdbcType=LONGVARCHAR},
      response_result = #{record.responseResult,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update direct_train_log
    set id = #{record.id,jdbcType=BIGINT},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      response_code = #{record.responseCode,jdbcType=VARCHAR},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="myUpdateByPrimaryKeySelective" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    update direct_train_log
    <set>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="responseCode != null">
        response_code = #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestParams != null">
        request_params = #{requestParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseResult != null">
        response_result = #{responseResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    update direct_train_log
    set request_url = #{requestUrl,jdbcType=VARCHAR},
      response_code = #{responseCode,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      request_params = #{requestParams,jdbcType=LONGVARCHAR},
      response_result = #{responseResult,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="myUpdateByPrimaryKey" parameterType="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    update direct_train_log
    set request_url = #{requestUrl,jdbcType=VARCHAR},
      response_code = #{responseCode,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectErrorSyncTasks" resultType="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    SELECT
      dtl2.*
    FROM
      direct_train_log dtl2
        INNER JOIN issue i ON dtl2.issue_id=i.id AND i.tenant_id=1   AND i.is_deleted = 0
        AND i.type = '20'
        AND i.parent_id IS NOT NULL
    WHERE
      dtl2.id IN (
        SELECT
          MAX( dtl1.id )
        FROM
          direct_train_log dtl1
        WHERE
          dtl1.issue_id IS NOT NULL
          AND dtl1.request_url = #{url}
        GROUP BY
          dtl1.issue_id
      )
      AND dtl2.response_code != '200'
  </select>

  <select id="selectErrorSyncBugs" resultType="com.fotile.devops.issue.pojo.entity.DirectTrainLog">
    SELECT
      dtl2.*
    FROM
      direct_train_log dtl2
        INNER JOIN issue i ON dtl2.issue_id=i.id AND i.tenant_id=1   AND i.is_deleted = 0
        AND i.type = '30'
    WHERE
      dtl2.id IN
       <foreach collection="logIds" open="(" close=")" item="logId" separator=",">
         #{logId}
       </foreach>

      AND dtl2.response_code != '200'
  </select>

  <select id="getErrorSyncBugLogIds" resultType="java.lang.Long">
    SELECT
      MAX( dtl1.id )
    FROM
      direct_train_log dtl1
    WHERE
      dtl1.issue_id IS NOT NULL
      AND dtl1.request_url = #{upsertBugs}
    GROUP BY
      dtl1.issue_id;
  </select>
</mapper>