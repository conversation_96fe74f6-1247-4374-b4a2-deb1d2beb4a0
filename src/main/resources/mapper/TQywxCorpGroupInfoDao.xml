<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.qywx.dao.TQywxCorpGroupInfoDao">

    <resultMap type="com.fotile.customercenter.qywx.pojo.entity.TQywxCorpGroupInfo" id="TQywxCorpGroupInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="wxGroupId" column="wx_group_id" jdbcType="VARCHAR"/>
        <result property="wxGroupName" column="wx_group_name" jdbcType="VARCHAR"/>
        <result property="wxGroupCreateTime" column="wx_group_create_time" jdbcType="TIMESTAMP"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TQywxCorpGroupInfoMap">
        select
            id, is_deleted, created_by, created_date, modified_by, modified_date, account_id, wx_group_id, wx_group_name, wx_group_create_time,sort
        from t_qywx_corp_group_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TQywxCorpGroupInfoMap">
        select
        id, is_deleted, created_by, created_date, modified_by, modified_date, account_id, wx_group_id, wx_group_name, wx_group_create_time,sort
        from t_qywx_corp_group_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="accountId != null and accountId != ''">
                and account_id = #{accountId}
            </if>
            <if test="wxGroupId != null and wxGroupId != ''">
                and wx_group_id = #{wxGroupId}
            </if>
            <if test="wxGroupName != null and wxGroupName != ''">
                and wx_group_name = #{wxGroupName}
            </if>
            <if test="wxGroupCreateTime != null">
                and wx_group_create_time = #{wxGroupCreateTime}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_qywx_corp_group_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="accountId != null and accountId != ''">
                and account_id = #{accountId}
            </if>
            <if test="wxGroupId != null and wxGroupId != ''">
                and wx_group_id = #{wxGroupId}
            </if>
            <if test="wxGroupName != null and wxGroupName != ''">
                and wx_group_name = #{wxGroupName}
            </if>
            <if test="wxGroupCreateTime != null">
                and wx_group_create_time = #{wxGroupCreateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_qywx_corp_group_info(is_deleted, created_by, created_date, modified_by, modified_date, account_id, wx_group_id, wx_group_name, wx_group_create_time,sort)
        values (#{isDeleted}, #{createdBy}, #{createdDate}, #{modifiedBy}, #{modifiedDate}, #{accountId}, #{wxGroupId}, #{wxGroupName}, #{wxGroupCreateTime},#{sort})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_qywx_corp_group_info(is_deleted, created_by, created_date, modified_by, modified_date, account_id, wx_group_id, wx_group_name, wx_group_create_time,sort)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDeleted}, #{entity.createdBy}, #{entity.createdDate}, #{entity.modifiedBy}, #{entity.modifiedDate}, #{entity.accountId}, #{entity.wxGroupId}, #{entity.wxGroupName}, #{entity.wxGroupCreateTime},#{entity.sort})
        </foreach>
    </insert>

    <update id="update">
        update t_qywx_corp_group_info
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate},
            </if>
            <if test="accountId != null and accountId != ''">
                account_id = #{accountId},
            </if>
            <if test="wxGroupId != null and wxGroupId != ''">
                wx_group_id = #{wxGroupId},
            </if>
            <if test="wxGroupName != null and wxGroupName != ''">
                wx_group_name = #{wxGroupName},
            </if>
            <if test="wxGroupCreateTime != null">
                wx_group_create_time = #{wxGroupCreateTime},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from t_qywx_corp_group_info where id = #{id}
    </delete>

    <!--查询对应账户下的标签组-->
    <select id="queryByAccountId" resultMap="TQywxCorpGroupInfoMap">
        select
            id, is_deleted, created_by, created_date, modified_by, modified_date, account_id, wx_group_id, wx_group_name, wx_group_create_time,sort
        from t_qywx_corp_group_info
        where is_deleted = 0 and account_id = #{accountId}
    </select>

    <!--逻辑删除全部数据-->
    <delete id="deleteAll">
        delete from t_qywx_corp_group_info where is_deleted = 0 and account_id = #{accountId}
    </delete>

</mapper>

