<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.msg.rule.dao.TRuleLogMapper">
  <resultMap id="BaseResultMap" type="com.fotile.msg.rule.pojo.entity.TRuleLogEntity">
    <!--@mbg.generated-->
    <!--@Table `msgcenter`.`t_rule_log`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `operator_name`, `description`, `is_deleted`, `created_by`, `created_date`, 
    `modified_by`, `modified_date`, `rule_id`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_rule_log`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`operator_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.operatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`description` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `msgcenter`.`t_rule_log`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`operator_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operatorName != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.operatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`description` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.ruleId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_log`
    (`operator_name`, `description`, `is_deleted`, `created_by`, `created_date`, `modified_by`, 
      `modified_date`, `rule_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.operatorName,jdbcType=VARCHAR}, #{item.description,jdbcType=LONGVARCHAR}, 
        #{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.ruleId,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.msg.rule.pojo.entity.TRuleLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_log`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `operator_name`,
      `description`,
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `rule_id`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{operatorName,jdbcType=VARCHAR},
      #{description,jdbcType=LONGVARCHAR},
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{ruleId,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      `operator_name` = #{operatorName,jdbcType=VARCHAR},
      `description` = #{description,jdbcType=LONGVARCHAR},
      `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `rule_id` = #{ruleId,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.msg.rule.pojo.entity.TRuleLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `msgcenter`.`t_rule_log`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="operatorName != null and operatorName != ''">
        `operator_name`,
      </if>
      <if test="description != null and description != ''">
        `description`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="ruleId != null">
        `rule_id`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null and operatorName != ''">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null and operatorName != ''">
        `operator_name` = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        `description` = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleId != null">
        `rule_id` = #{ruleId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>