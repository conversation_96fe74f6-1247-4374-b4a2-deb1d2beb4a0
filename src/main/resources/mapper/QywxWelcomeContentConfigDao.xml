<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.qywxWelcome.dao.QywxWelcomeContentConfigDao">

    <resultMap type="com.fotile.customercenter.qywxWelcome.entity.QywxWelcomeContentConfig" id="QywxWelcomeContentConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="welcomeMainId" column="welcome_main_id" jdbcType="INTEGER"/>
        <result property="msgType" column="msg_type" jdbcType="INTEGER"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="imageMediaId" column="image_media_id" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="videoMediaId" column="video_media_id" jdbcType="VARCHAR"/>
        <result property="linkPicUrl" column="link_pic_url" jdbcType="VARCHAR"/>
        <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
        <result property="linkTitle" column="link_title" jdbcType="VARCHAR"/>
        <result property="miniprogramAppid" column="miniprogram_appid" jdbcType="VARCHAR"/>
        <result property="miniprogramPage" column="miniprogram_page" jdbcType="VARCHAR"/>
        <result property="miniprogramParam" column="miniprogram_param" jdbcType="VARCHAR"/>
        <result property="miniprogramPicUrl" column="miniprogram_pic_url" jdbcType="VARCHAR"/>
        <result property="miniprogramPicMediaId" column="miniprogram_pic_media_id" jdbcType="VARCHAR"/>
        <result property="mediaUploadDate" column="media_upload_date" jdbcType="TIMESTAMP"/>
        <result property="miniprogramTitle" column="miniprogram_title" jdbcType="VARCHAR"/>
        <result property="isAssignSharer" column="is_assign_sharer" jdbcType="INTEGER"/>
        <result property="miniprogramContentTemplateId" column="miniprogram_content_template_id" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="VARCHAR"/>
        <result property="videoPictureUrl" column="video_picture_url" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="QywxWelcomeContentConfigMap">
        select
          id, is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer,sort,video_picture_url
        from customercenter.t_qywx_welcome_content_config
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="QywxWelcomeContentConfigMap">
        select
          id, is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer
        from customercenter.t_qywx_welcome_content_config
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="welcomeMainId != null">
                and welcome_main_id = #{welcomeMainId}
            </if>
            <if test="msgType != null">
                and msg_type = #{msgType}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="imageMediaId != null and imageMediaId != ''">
                and image_media_id = #{imageMediaId}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl}
            </if>
            <if test="videoMediaId != null and videoMediaId != ''">
                and video_media_id = #{videoMediaId}
            </if>
            <if test="linkPicUrl != null and linkPicUrl != ''">
                and link_pic_url = #{linkPicUrl}
            </if>
            <if test="linkUrl != null and linkUrl != ''">
                and link_url = #{linkUrl}
            </if>
            <if test="miniprogramAppid != null and miniprogramAppid != ''">
                and miniprogram_appid = #{miniprogramAppid}
            </if>
            <if test="miniprogramPage != null and miniprogramPage != ''">
                and miniprogram_page = #{miniprogramPage}
            </if>
            <if test="miniprogramParam != null and miniprogramParam != ''">
                and miniprogram_param = #{miniprogramParam}
            </if>
            <if test="miniprogramPicUrl != null and miniprogramPicUrl != ''">
                and miniprogram_pic_url = #{miniprogramPicUrl}
            </if>
            <if test="miniprogramPicMediaId != null and miniprogramPicMediaId != ''">
                and miniprogram_pic_media_id = #{miniprogramPicMediaId}
            </if>
            <if test="mediaUploadDate != null">
                and media_upload_date = #{mediaUploadDate}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from customercenter.t_qywx_welcome_content_config
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="welcomeMainId != null">
                and welcome_main_id = #{welcomeMainId}
            </if>
            <if test="msgType != null">
                and msg_type = #{msgType}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="imageMediaId != null and imageMediaId != ''">
                and image_media_id = #{imageMediaId}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl}
            </if>
            <if test="videoMediaId != null and videoMediaId != ''">
                and video_media_id = #{videoMediaId}
            </if>
            <if test="linkPicUrl != null and linkPicUrl != ''">
                and link_pic_url = #{linkPicUrl}
            </if>
            <if test="linkUrl != null and linkUrl != ''">
                and link_url = #{linkUrl}
            </if>
            <if test="miniprogramAppid != null and miniprogramAppid != ''">
                and miniprogram_appid = #{miniprogramAppid}
            </if>
            <if test="miniprogramPage != null and miniprogramPage != ''">
                and miniprogram_page = #{miniprogramPage}
            </if>
            <if test="miniprogramParam != null and miniprogramParam != ''">
                and miniprogram_param = #{miniprogramParam}
            </if>
            <if test="miniprogramPicUrl != null and miniprogramPicUrl != ''">
                and miniprogram_pic_url = #{miniprogramPicUrl}
            </if>
            <if test="miniprogramPicMediaId != null and miniprogramPicMediaId != ''">
                and miniprogram_pic_media_id = #{miniprogramPicMediaId}
            </if>
            <if test="mediaUploadDate != null">
                and media_upload_date = #{mediaUploadDate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into customercenter.t_qywx_welcome_content_config(is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id,video_picture_url, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer,sort)
        values (#{isDeleted}, #{createdBy}, #{createdDate}, #{modifiedBy}, #{modifiedDate}, #{welcomeMainId}, #{msgType}, #{imageUrl}, #{imageMediaId}, #{videoUrl}, #{videoMediaId},#{videoPictureUrl}, #{linkPicUrl}, #{linkUrl}, #{linkTitle}, #{miniprogramAppid}, #{miniprogramPage}, #{miniprogramParam}, #{miniprogramPicUrl}, #{miniprogramPicMediaId}, #{mediaUploadDate}, #{miniprogramTitle}, #{isAssignSharer},#{sort})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into customercenter.t_qywx_welcome_content_config(is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id,video_picture_url, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer, miniprogram_content_template_id,sort)
        values
        <foreach collection="entities" item="entity" separator=",">
        (0, #{entity.createdBy}, now(), #{entity.modifiedBy}, now(), #{entity.welcomeMainId}, #{entity.msgType}, #{entity.imageUrl}, #{entity.imageMediaId}, #{entity.videoUrl}, #{entity.videoMediaId}, #{entity.videoPictureUrl}, #{entity.linkPicUrl}, #{entity.linkUrl}, #{entity.linkTitle}, #{entity.miniprogramAppid}, #{entity.miniprogramPage}, #{entity.miniprogramParam}, #{entity.miniprogramPicUrl}, #{entity.miniprogramPicMediaId}, #{entity.mediaUploadDate}, #{entity.miniprogramTitle}, #{entity.isAssignSharer}, #{entity.miniprogramContentTemplateId}, #{entity.sort})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into customercenter.t_qywx_welcome_content_config(is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id, video_picture_url,link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date,sort)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDeleted}, #{entity.createdBy}, #{entity.createdDate}, #{entity.modifiedBy}, #{entity.modifiedDate}, #{entity.welcomeMainId}, #{entity.msgType}, #{entity.imageUrl}, #{entity.imageMediaId}, #{entity.videoUrl}, #{entity.videoMediaId},#{entity.videoPictureUrl},  #{entity.linkPicUrl}, #{entity.linkUrl}, #{entity.linkTitle}, #{entity.miniprogramAppid}, #{entity.miniprogramPage}, #{entity.miniprogramParam}, #{entity.miniprogramPicUrl}, #{entity.miniprogramPicMediaId}, #{entity.mediaUploadDate}, #{entity.sort})
        </foreach>
        on duplicate key update
        is_deleted = values(is_deleted),
        created_by = values(created_by),
        created_date = values(created_date),
        modified_by = values(modified_by),
        modified_date = values(modified_date),
        welcome_main_id = values(welcome_main_id),
        msg_type = values(msg_type),
        image_url = values(image_url),
        image_media_id = values(image_media_id),
        video_url = values(video_url),
        video_media_id = values(video_media_id),
        link_pic_url = values(link_pic_url),
        link_url = values(link_url),
        link_title = values(linkTitle),
        miniprogram_appid = values(miniprogram_appid),
        miniprogram_page = values(miniprogram_page),
        miniprogram_param = values(miniprogram_param),
        miniprogram_pic_url = values(miniprogram_pic_url),
        miniprogram_pic_media_id = values(miniprogram_pic_media_id),
        media_upload_date = values(media_upload_date),
        sort = values(sort),
        video_picture_url = values(video_picture_url),
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update customercenter.t_qywx_welcome_content_config
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate},
            </if>
            <if test="welcomeMainId != null">
                welcome_main_id = #{welcomeMainId},
            </if>
            <if test="msgType != null">
                msg_type = #{msgType},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="imageMediaId != null and imageMediaId != ''">
                image_media_id = #{imageMediaId},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url = #{videoUrl},
            </if>
            <if test="videoMediaId != null and videoMediaId != ''">
                video_media_id = #{videoMediaId},
            </if>
            <if test="videoPictureUrl != null and videoPictureUrl != ''">
                video_picture_url = #{videoPictureUrl},
            </if>
            <if test="linkPicUrl != null and linkPicUrl != ''">
                link_pic_url = #{linkPicUrl},
            </if>
            <if test="linkUrl != null and linkUrl != ''">
                link_url = #{linkUrl},
            </if>
            <if test="linkTitle != null and linkTitle != ''">
                link_title = #{linkTitle},
            </if>
            <if test="miniprogramAppid != null and miniprogramAppid != ''">
                miniprogram_appid = #{miniprogramAppid},
            </if>
            <if test="miniprogramPage != null and miniprogramPage != ''">
                miniprogram_page = #{miniprogramPage},
            </if>
            <if test="miniprogramParam != null and miniprogramParam != ''">
                miniprogram_param = #{miniprogramParam},
            </if>
            <if test="miniprogramPicUrl != null and miniprogramPicUrl != ''">
                miniprogram_pic_url = #{miniprogramPicUrl},
            </if>
            <if test="miniprogramPicMediaId != null and miniprogramPicMediaId != ''">
                miniprogram_pic_media_id = #{miniprogramPicMediaId},
            </if>
            <if test="mediaUploadDate != null">
                media_upload_date = #{mediaUploadDate},
            </if>
            <if test="miniprogramTitle != null and miniprogramTitle != ''">
                miniprogram_title = #{miniprogramTitle},
            </if>
            <if test="isAssignSharer != null">
                is_assign_sharer = #{isAssignSharer},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from customercenter.t_qywx_welcome_content_config where id = #{id}
    </delete>

    <update id="updateBatch" keyProperty="id" useGeneratedKeys="true">
        <foreach collection="list" item="item">
            <if test="item.id != null">
                update customercenter.t_qywx_welcome_content_config
                <set>
                    <if test="item.modifiedBy != null and item.modifiedBy != ''">
                        modified_by = #{item.modifiedBy},
                    </if>
                        modified_date = now(),
                    <if test="item.welcomeMainId != null">
                        welcome_main_id = #{item.welcomeMainId},
                    </if>
                    <if test="item.msgType != null">
                        msg_type = #{item.msgType},
                    </if>
                        image_url = #{item.imageUrl},
                        image_media_id = #{item.imageMediaId},
                        video_url = #{item.videoUrl},
                        video_media_id = #{item.videoMediaId},
                    video_picture_url = #{videoPictureUrl},
                        link_pic_url = #{item.linkPicUrl},
                        link_url = #{item.linkUrl},
                        link_title = #{item.linkTitle},
                        miniprogram_appid = #{item.miniprogramAppid},
                        miniprogram_page = #{item.miniprogramPage},
                        miniprogram_param = #{item.miniprogramParam},
                        miniprogram_pic_url = #{item.miniprogramPicUrl},
                        miniprogram_pic_media_id = #{item.miniprogramPicMediaId},
                        media_upload_date = #{item.mediaUploadDate},
                        miniprogram_title = #{item.miniprogramTitle},
                        is_assign_sharer = #{item.isAssignSharer},
                        sort = #{sort}
                </set>
                where id = #{item.id};
            </if>
        </foreach>
    </update>

    <select id="queryByMainId" resultMap="QywxWelcomeContentConfigMap">
        select
          id, is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer, miniprogram_content_template_id,sort,video_picture_url
        from customercenter.t_qywx_welcome_content_config
        where welcome_main_id = #{welcomeMainId} and is_deleted = 0
    </select>

    <select id="queryByMainIds" resultMap="QywxWelcomeContentConfigMap">
        select
            id, is_deleted, created_by, created_date, modified_by, modified_date, welcome_main_id, msg_type, image_url, image_media_id, video_url, video_media_id, link_pic_url, link_url, link_title, miniprogram_appid, miniprogram_page, miniprogram_param, miniprogram_pic_url, miniprogram_pic_media_id, media_upload_date, miniprogram_title, is_assign_sharer, miniprogram_content_template_id,sort,video_picture_url
        from customercenter.t_qywx_welcome_content_config
        where is_deleted = 0
        and welcome_main_id in
            <foreach collection="welcomeMainIds" item="welcomeMainId" open="(" separator="," close=")">
                #{welcomeMainId}
            </foreach>
    </select>

    <select id="getNeedUpdateMediaIdList"
            resultType="com.fotile.customercenter.qywxWelcome.dto.QywxWelcomeContentConfigDto">
        SELECT
            tqwmi.account_id,
            tqwcc.*
        FROM
            t_qywx_welcome_content_config tqwcc
                LEFT JOIN t_qywx_welcome_main_info tqwmi ON tqwmi.id=tqwcc.welcome_main_id
        WHERE
            tqwcc.is_deleted = 0
          AND tqwcc.msg_type != 2
	AND tqwmi.is_deleted=0
	AND (
		( tqwcc.media_upload_date IS NULL )
	OR ( tqwcc.media_upload_date IS NOT NULL AND TIMESTAMPDIFF( HOUR, tqwcc.media_upload_date, NOW())> 48 )
	)
    </select>

    <!--通过主键删除-->
    <delete id="deleteByMainId">
        delete from customercenter.t_qywx_welcome_content_config where welcome_main_id = #{welcomeMainId}
    </delete>
</mapper>

