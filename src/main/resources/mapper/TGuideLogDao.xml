<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.guide.dao.TGuideLogDao">
  <resultMap id="BaseResultMap" type="com.fotile.cmscenter.guide.pojo.entity.TGuideLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="result" jdbcType="VARCHAR" property="result" />
  </resultMap>
  <sql id="Base_Column_List">
    id, is_deleted, created_by, created_date, modified_by, modified_date, source_id, 
    `type`, `result`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_guide_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_guide_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.cmscenter.guide.pojo.entity.TGuideLog" useGeneratedKeys="true">
    insert into t_guide_log (is_deleted, created_by, created_date, 
      modified_by, modified_date, source_id, 
      `type`, `result`)
    values (#{isDeleted,jdbcType=BIGINT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{sourceId,jdbcType=BIGINT}, 
      #{type,jdbcType=BOOLEAN}, #{result,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.cmscenter.guide.pojo.entity.TGuideLog" useGeneratedKeys="true">
    insert into t_guide_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="result != null">
        `result`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.cmscenter.guide.pojo.entity.TGuideLog">
    update t_guide_log
    <set>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.cmscenter.guide.pojo.entity.TGuideLog">
    update t_guide_log
    set is_deleted = #{isDeleted,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=BOOLEAN},
      `result` = #{result,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>