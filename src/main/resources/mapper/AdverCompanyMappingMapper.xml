<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.cmscenter.adver.dao.AdverCompanyMappingMapper" >
  <resultMap id="BaseResultMap" type="com.fotile.cmscenter.adver.pojo.entity.AdverCompanyMapping" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="adver_id" property="adverId" jdbcType="BIGINT" />
    <result column="company_id" property="companyId" jdbcType="BIGINT" />
    <result column="is_deleted" property="isDeleted" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, adver_id, company_id, is_deleted, created_date, created_by, modified_date, modified_by
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_adver_company_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_adver_company_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.fotile.cmscenter.adver.pojo.entity.AdverCompanyMapping" >
    insert into t_adver_company_mapping (id, adver_id, company_id, 
      is_deleted, created_date, created_by, 
      modified_date, modified_by)
    values (#{id,jdbcType=BIGINT}, #{adverId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fotile.cmscenter.adver.pojo.entity.AdverCompanyMapping" >
    insert into t_adver_company_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="adverId != null" >
        adver_id,
      </if>
      <if test="companyId != null" >
        company_id,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adverId != null" >
        #{adverId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.cmscenter.adver.pojo.entity.AdverCompanyMapping" >
    update t_adver_company_mapping
    <set >
      <if test="adverId != null" >
        adver_id = #{adverId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null" >
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.cmscenter.adver.pojo.entity.AdverCompanyMapping" >
    update t_adver_company_mapping
    set adver_id = #{adverId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" >
    insert into t_adver_company_mapping (
      adver_id, company_id,
      created_date, created_by
    )
    values
    <foreach collection="companyIdList" separator="," item="companyId" >
      (
        #{adverId}, #{companyId},
        now(), #{userId}
      )
    </foreach>

  </insert>

  <update id="deleteByAdverId" >
    update t_adver_company_mapping
    set
      is_deleted = 1,
      modified_date = now(),
      modified_by = #{userId}
    where is_deleted = 0
    and adver_id = #{adverId}
  </update>

  <select id="selectByAdverId" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select
    company_id
    from t_adver_company_mapping
    where is_deleted = 0
    and adver_id = #{adverId}
  </select>

</mapper>