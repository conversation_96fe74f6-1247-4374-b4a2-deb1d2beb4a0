<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.materielgrouplist.dao.MaterielGroupListDao">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="department_id" jdbcType="VARCHAR" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="materiel_num" jdbcType="VARCHAR" property="materielNum" />
    <result column="download_num" jdbcType="VARCHAR" property="downloadNum" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_topping" jdbcType="VARCHAR" property="isTopping" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_del" jdbcType="VARCHAR" property="isDel" />
    <result column="reserve_item1" jdbcType="VARCHAR" property="reserveItem1" />
    <result column="reserve_item2" jdbcType="VARCHAR" property="reserveItem2" />
    <result column="reserve_item3" jdbcType="VARCHAR" property="reserveItem3" />
    <result column="reserve_item4" jdbcType="VARCHAR" property="reserveItem4" />
    <result column="reserve_item5" jdbcType="VARCHAR" property="reserveItem5" />
    <result column="reserve_item6" jdbcType="VARCHAR" property="reserveItem6" />
    <result column="reserve_item7" jdbcType="VARCHAR" property="reserveItem7" />
    <result column="reserve_item8" jdbcType="VARCHAR" property="reserveItem8" />
    <result column="reserve_item9" jdbcType="VARCHAR" property="reserveItem9" />
    <result column="reserve_item10" jdbcType="VARCHAR" property="reserveItem10" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList">
    <result column="remok" jdbcType="LONGVARCHAR" property="remok" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, department_id, department_name, materiel_num, download_num, `status`, 
    sort, is_topping, ctime, creator, mtime, modifier, is_del, reserve_item1, reserve_item2, 
    reserve_item3, reserve_item4, reserve_item5, reserve_item6, reserve_item7, reserve_item8, 
    reserve_item9, reserve_item10
  </sql>
  <sql id="Blob_Column_List">
    remok
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupListExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from materiel_group_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from materiel_group_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from materiel_group_list
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from materiel_group_list
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupListExample">
    delete from materiel_group_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList" useGeneratedKeys="true">
    insert into materiel_group_list (id,title, department_id, department_name,
      materiel_num, download_num, `status`, 
      sort, is_topping, ctime, 
      creator, mtime, modifier, 
      is_del, reserve_item1, reserve_item2, 
      reserve_item3, reserve_item4, reserve_item5, 
      reserve_item6, reserve_item7, reserve_item8, 
      reserve_item9, reserve_item10, remok
      )
    values (#{id,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR}, #{departmentId,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR},
      #{materielNum,jdbcType=VARCHAR}, #{downloadNum,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{isTopping,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{mtime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=VARCHAR}, #{reserveItem1,jdbcType=VARCHAR}, #{reserveItem2,jdbcType=VARCHAR}, 
      #{reserveItem3,jdbcType=VARCHAR}, #{reserveItem4,jdbcType=VARCHAR}, #{reserveItem5,jdbcType=VARCHAR}, 
      #{reserveItem6,jdbcType=VARCHAR}, #{reserveItem7,jdbcType=VARCHAR}, #{reserveItem8,jdbcType=VARCHAR}, 
      #{reserveItem9,jdbcType=VARCHAR}, #{reserveItem10,jdbcType=VARCHAR}, #{remok,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList" useGeneratedKeys="true">
    insert into materiel_group_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null">
        title,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="materielNum != null">
        materiel_num,
      </if>
      <if test="downloadNum != null">
        download_num,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="isTopping != null">
        is_topping,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="reserveItem1 != null">
        reserve_item1,
      </if>
      <if test="reserveItem2 != null">
        reserve_item2,
      </if>
      <if test="reserveItem3 != null">
        reserve_item3,
      </if>
      <if test="reserveItem4 != null">
        reserve_item4,
      </if>
      <if test="reserveItem5 != null">
        reserve_item5,
      </if>
      <if test="reserveItem6 != null">
        reserve_item6,
      </if>
      <if test="reserveItem7 != null">
        reserve_item7,
      </if>
      <if test="reserveItem8 != null">
        reserve_item8,
      </if>
      <if test="reserveItem9 != null">
        reserve_item9,
      </if>
      <if test="reserveItem10 != null">
        reserve_item10,
      </if>
      <if test="remok != null">
        remok,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="materielNum != null">
        #{materielNum,jdbcType=VARCHAR},
      </if>
      <if test="downloadNum != null">
        #{downloadNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isTopping != null">
        #{isTopping,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        #{reserveItem10,jdbcType=VARCHAR},
      </if>
      <if test="remok != null">
        #{remok,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupListExample" resultType="java.lang.Long">
    select count(*) from materiel_group_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update materiel_group_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentName != null">
        department_name = #{record.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.materielNum != null">
        materiel_num = #{record.materielNum,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadNum != null">
        download_num = #{record.downloadNum,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.isTopping != null">
        is_topping = #{record.isTopping,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem1 != null">
        reserve_item1 = #{record.reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem2 != null">
        reserve_item2 = #{record.reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem3 != null">
        reserve_item3 = #{record.reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem4 != null">
        reserve_item4 = #{record.reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem5 != null">
        reserve_item5 = #{record.reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem6 != null">
        reserve_item6 = #{record.reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem7 != null">
        reserve_item7 = #{record.reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem8 != null">
        reserve_item8 = #{record.reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem9 != null">
        reserve_item9 = #{record.reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveItem10 != null">
        reserve_item10 = #{record.reserveItem10,jdbcType=VARCHAR},
      </if>
      <if test="record.remok != null">
        remok = #{record.remok,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update materiel_group_list
    set id = #{record.id,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      department_id = #{record.departmentId,jdbcType=VARCHAR},
      department_name = #{record.departmentName,jdbcType=VARCHAR},
      materiel_num = #{record.materielNum,jdbcType=VARCHAR},
      download_num = #{record.downloadNum,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      is_topping = #{record.isTopping,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_del = #{record.isDel,jdbcType=VARCHAR},
      reserve_item1 = #{record.reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{record.reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{record.reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{record.reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{record.reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{record.reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{record.reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{record.reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{record.reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{record.reserveItem10,jdbcType=VARCHAR},
      remok = #{record.remok,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update materiel_group_list
    set id = #{record.id,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      department_id = #{record.departmentId,jdbcType=VARCHAR},
      department_name = #{record.departmentName,jdbcType=VARCHAR},
      materiel_num = #{record.materielNum,jdbcType=VARCHAR},
      download_num = #{record.downloadNum,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      is_topping = #{record.isTopping,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_del = #{record.isDel,jdbcType=VARCHAR},
      reserve_item1 = #{record.reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{record.reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{record.reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{record.reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{record.reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{record.reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{record.reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{record.reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{record.reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{record.reserveItem10,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList">
    update materiel_group_list
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="materielNum != null">
        materiel_num = #{materielNum,jdbcType=VARCHAR},
      </if>
      <if test="downloadNum != null">
        download_num = #{downloadNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isTopping != null">
        is_topping = #{isTopping,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem1 != null">
        reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem2 != null">
        reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem3 != null">
        reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem4 != null">
        reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem5 != null">
        reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem6 != null">
        reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem7 != null">
        reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem8 != null">
        reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem9 != null">
        reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      </if>
      <if test="reserveItem10 != null">
        reserve_item10 = #{reserveItem10,jdbcType=VARCHAR},
      </if>
      <if test="remok != null">
        remok = #{remok,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList">
    update materiel_group_list
    set title = #{title,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=VARCHAR},
      department_name = #{departmentName,jdbcType=VARCHAR},
      materiel_num = #{materielNum,jdbcType=VARCHAR},
      download_num = #{downloadNum,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      is_topping = #{isTopping,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=VARCHAR},
      reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{reserveItem10,jdbcType=VARCHAR},
      remok = #{remok,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList">
    update materiel_group_list
    set title = #{title,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=VARCHAR},
      department_name = #{departmentName,jdbcType=VARCHAR},
      materiel_num = #{materielNum,jdbcType=VARCHAR},
      download_num = #{downloadNum,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      is_topping = #{isTopping,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=VARCHAR},
      reserve_item1 = #{reserveItem1,jdbcType=VARCHAR},
      reserve_item2 = #{reserveItem2,jdbcType=VARCHAR},
      reserve_item3 = #{reserveItem3,jdbcType=VARCHAR},
      reserve_item4 = #{reserveItem4,jdbcType=VARCHAR},
      reserve_item5 = #{reserveItem5,jdbcType=VARCHAR},
      reserve_item6 = #{reserveItem6,jdbcType=VARCHAR},
      reserve_item7 = #{reserveItem7,jdbcType=VARCHAR},
      reserve_item8 = #{reserveItem8,jdbcType=VARCHAR},
      reserve_item9 = #{reserveItem9,jdbcType=VARCHAR},
      reserve_item10 = #{reserveItem10,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getMaterielGroupList"
          resultType="com.fotile.resourcescenter.materielgrouplist.pojo.dto.GetMaterielGroupDto">
    SELECT
      DISTINCT A.id,
      A.title,
      A.department_id departmentId,
      A.department_name departmentName,
      A.materiel_num materielNum,
      A.download_num downloadNum,
      A.`status`,
      A.sort,
      ifnull(A.is_topping,'2') isTopping,
      A.ctime,
      A.creator,
      A.mtime,
      A.modifier,
      A.is_del isDel,
      A.reserve_item1 as creatorName,
      A.reserve_item2,
      A.reserve_item3,
      A.reserve_item4,
      A.reserve_item5,
      A.reserve_item6,
      A.reserve_item7,
      A.reserve_item8,
      A.reserve_item9,
      A.reserve_item10,
      A.remok
    FROM
        materiel_group_list A
    <if test="params.categoryId != null || params.usesId != null || params.productCategoryId != null">
      LEFT JOIN materiel_group_list_relevance mglr ON A.id = mglr.group_id
    </if>
    <if test="params.categoryId!= null">
      LEFT JOIN materiel_category_relevance mcr ON mglr.materiel_id = mcr.materiel_id
    </if>
    <if test="params.usesId!= null">
      LEFT JOIN materiel_uses_relevance mur ON mglr.materiel_id = mur.materiel_id
    </if>
    <if test="params.productCategoryId!= null">
      LEFT JOIN materiel_product_category_relevance mpcr ON mglr.materiel_id = mpcr.materiel_id
    </if>
    <where>
      1=1
      <if test="params.id!= null and params.id != '' ">
        AND A.id like '%${params.id}%'
      </if>
      <if test="params.title!= null and params.title!=''">
        AND A.title like '%${params.title}%'
      </if>
      <if test="params.creatorName!= null and params.creatorName!=''">
        AND A.reserve_item1 = #{params.creatorName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
      </if>
      <if test="params.status!= null and params.status!=''">
        AND A.status =#{params.status}
      </if>
      <if test="params.startTime!= null and params.startTime!=''">
        AND A.ctime >=#{params.startTime}
      </if>
      <if test="params.endTime!= null and params.endTime!=''">
        AND #{params.endTime} >=A.ctime
      </if>
      <if test="params.departmentId!= null">
        AND A.department_id =#{params.departmentId}
      </if>
      <if test="params.categoryId != null">
        AND (mcr.category_id = #{params.categoryId}
            OR mcr.reserve_item4 LIKE CONCAT('%_',#{params.categoryId},'_%')
            OR mcr.reserve_item4 LIKE CONCAT('%',#{params.categoryId},'_%')
            OR mcr.reserve_item4 LIKE  CONCAT('%_',#{params.categoryId},'%'))
      </if>
      <if test="params.usesId!= null">
        AND (mur.uses_id = #{params.usesId}
            OR mur.reserve_item4 LIKE CONCAT('%_',#{params.usesId},'_%')
            OR mur.reserve_item4 LIKE CONCAT('%',#{params.usesId},'_%')
            OR mur.reserve_item4 LIKE  CONCAT('%_',#{params.usesId},'%'))
      </if>
      <if test="params.productCategoryId!= null">
        AND (mpcr.category_id = #{params.productCategoryId}
            OR mpcr.reserve_item4 LIKE CONCAT('%_',#{params.productCategoryId},'_%')
            OR mpcr.reserve_item4 LIKE CONCAT('%',#{params.productCategoryId},'_%')
            OR mpcr.reserve_item4 LIKE  CONCAT('%_',#{params.productCategoryId},'%'))
      </if>
    </where>
    ORDER BY A.is_topping IS NULL, A.is_topping ASC, A.ctime DESC
  </select>
</mapper>