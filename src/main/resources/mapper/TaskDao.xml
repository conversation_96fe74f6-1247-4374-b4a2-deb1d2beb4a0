<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.taskcenter.task.dao.TaskDao">
    <!--task entity from datatable-->
    <resultMap id="taskEntity" type="com.fotile.taskcenter.task.pojo.dto.out.TaskOutDto">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="subtitle" column="subtitle"/>
        <result property="category_code" column="category_code"/>
        <result property="channel_code" column="channel_code"/>
        <result property="type_code" column="type_code"/>
        <result property="jump_type" column="jump_type"/>
        <result property="jump_app_code" column="jump_app_code"/>
        <result property="jump_app_paramid" column="jump_app_paramid"/>
        <result property="jump_outer_url" column="jump_outer_url"/>
        <result property="valid_start_time" column="valid_start_time"/>
        <result property="valid_end_time" column="valid_end_time"/>
        <result property="status" column="status"/>
        <result property="send_point_checked" column="send_point_checked"/>
        <result property="send_point_value" column="send_point_value"/>
        <result property="send_point_limit" column="send_point_limit"/>
        <result property="send_growup_checked" column="send_growup_checked"/>
        <result property="send_growup_value" column="send_growup_value"/>
        <result property="send_growup_limit" column="send_growup_limit"/>
        <result property="group_name" column="group_name"/>
        <result property="remark" column="remark"/>
        <result property="sort" column="sort"/>
        <result property="created_name" column="created_name"/>
        <result property="modified_name" column="modified_name"/>
        <result property="send_lottery_checked" column="send_lottery_checked"/>
        <result property="send_lottery_value" column="send_lottery_value"/>
        <result property="send_lottery_limit" column="send_lottery_limit"/>
        <result property="wechat_app_code" column="wechat_app_code"/>
        <result property="wechat_app_path" column="wechat_app_path"/>
        <result property="wechat_app_params" column="wechat_app_params"/>
    </resultMap>

    <sql id="queryTaskWhere">
        <if test="query.id != null">
            and `t`.`id` = #{query.id}
        </if>
        <if test="query.name != null and query.name != ''">
            and `t`.`name` like '%${query.name}%'
        </if>
        <if test="query.channel_code != null and query.channel_code != ''">
            and `t`.`channel_code` = #{query.channel_code}
        </if>
        <if test="query.category_code != null and query.category_code != ''">
            and `t`.`category_code` = #{query.category_code}
        </if>
        <if test="query.type_code != null and query.type_code != ''">
            and `t`.`type_code` = #{query.type_code}
        </if>
        <if test="query.status != null">
            and `t`.`status` = #{query.status}
        </if>
        <if test="query.last_operator != null and query.last_operator != ''">
            and `t`.`modified_name` = #{query.last_operator}
        </if>
        <if test="query.last_operate_starttime != null">
            and `t`.`modified_date` &gt;= #{query.last_operate_starttime}
        </if>
        <if test="query.last_operate_endtime != null">
            and `t`.`modified_date` &lt;= #{query.last_operate_endtime}
        </if>
        <if test="query.channelCodes != null and query.channelCodes.size() != 0">
            and `t`.`channel_code` in
            <foreach collection="query.channelCodes" separator="," close=")" open="(" item="item">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="query.valid_start_time != null and query.valid_end_time != null">
            and `t`.`valid_start_time` &lt;= #{query.valid_end_time} and `t`.`valid_end_time` &gt;= #{query.valid_start_time}
        </if>
    </sql>

    <select id="getTaskTotalRows"
            resultType="java.lang.Integer"
            parameterType="com.fotile.taskcenter.task.pojo.dto.QueryTaskDto">
        select count(`t`.id)
        from `taskcenter`.`task` t
        where `t`.`is_deleted` = 0
        <include refid="queryTaskWhere"/>
    </select>

    <!--查询任务列表-->
    <select id="queryTask"
            parameterType="com.fotile.taskcenter.task.pojo.dto.QueryTaskDto"
            resultMap="taskEntity">
        select `t`.*
        from `taskcenter`.`task` t
        where `t`.`is_deleted` = 0
        <include refid="queryTaskWhere"/>
        order by ${query.sort_by} ${query.sort_mode}
        limit #{pageInfo.offset},#{pageInfo.size}
    </select>

    <!--通过任务id获取任务详情-->
    <select id="getTaskById" resultMap="taskEntity">
        select `t`.*
        from `taskcenter`.`task` t
        where `t`.`id` = #{id};
    </select>

    <!--新增任务记录-->
    <insert id="addTask"
            parameterType="com.fotile.taskcenter.task.pojo.dto.EditTaskDto"
            keyProperty="id"
            useGeneratedKeys="true">
        insert into `taskcenter`.`task`
        (`name`,
         `subtitle`,
         `category_code`,
         `channel_code`,
         `type_code`,
         `jump_type`,
         `jump_app_code`,
         `jump_app_paramid`,
         `jump_outer_url`,
         `valid_start_time`,
         `valid_end_time`,
         `status`,
         `send_point_checked`,
         `send_point_value`,
         `send_point_limit`,
         `send_growup_checked`,
         `send_growup_value`,
         `send_growup_limit`,
         `send_lottery_checked`,
         `send_lottery_value`,
         `send_lottery_limit`,
         `wechat_app_code`,
         `wechat_app_path`,
         `wechat_app_params`,
         `group_name`,
         `remark`,
         `sort`,
         `created_date`,
         `created_by`,
         `created_name`)
        values (#{name},
                #{subtitle},
                #{category_code},
                #{channel_code},
                #{type_code},
                #{jump_type},
                #{jump_app_code},
                #{jump_app_paramid},
                #{jump_outer_url},
                #{valid_start_time},
                #{valid_end_time},
                #{status},
                #{send_point_checked},
                #{send_point_value},
                #{send_point_limit},
                #{send_growup_checked},
                #{send_growup_value},
                #{send_growup_limit},
                #{send_lottery_checked},
                #{send_lottery_value},
                #{send_lottery_limit},
                #{wechat_app_code},
                #{wechat_app_path},
                #{wechat_app_params},
                #{group_name},
                #{remark},
                #{sort},
                now(),
                #{operator_id},
                #{operator_name}
        );
    </insert>

    <!--编辑任务记录-->
    <update id="updateTask" parameterType="com.fotile.taskcenter.task.pojo.dto.EditTaskDto">
        update `taskcenter`.`task`
        set `name`=#{name},
            `subtitle`=#{subtitle},
            `category_code`=#{category_code},
            `channel_code`=#{channel_code},
            `type_code`=#{type_code},
            `jump_type`=#{jump_type},
            `jump_app_code`=#{jump_app_code},
            `jump_app_paramid`=#{jump_app_paramid},
            `jump_outer_url`=#{jump_outer_url},
            `valid_start_time`=#{valid_start_time},
            `valid_end_time`=#{valid_end_time},
            `status`=#{status},
            `send_point_checked`=#{send_point_checked},
            `send_point_value`=#{send_point_value},
            `send_point_limit`=#{send_point_limit},
            `send_growup_checked`=#{send_growup_checked},
            `send_growup_value`=#{send_growup_value},
            `send_growup_limit`=#{send_growup_limit},
            `send_lottery_checked`=#{send_lottery_checked},
            `send_lottery_value`=#{send_lottery_value},
            `send_lottery_limit`=#{send_lottery_limit},
            `wechat_app_code`=#{wechat_app_code},
            `wechat_app_path`=#{wechat_app_path},
            `wechat_app_params`=#{wechat_app_params},
            `group_name`=#{group_name},
            `remark`=#{remark},
            `modified_date`=now(),
            `modified_by`=#{operator_id},
            `modified_name`=#{operator_name}
        where `id` = #{id}
    </update>

    <!--编辑任务排序-->
    <update id="editTaskSort"
            parameterType="com.fotile.taskcenter.task.pojo.dto.KeyValueEntry">
        update `taskcenter`.`task`
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="sort =case" suffix="end,">
                <foreach collection="edit.sorted_list" item="sort" index="index">
                    when id=#{sort.key} then #{sort.value}
                </foreach>
            </trim>
        </trim>
        ,modified_date=now(), modified_by=#{edit.operatorId}, modified_name=#{edit.operatorName}
        where id in
        <foreach collection="edit.sorted_list" index="index" item="sort" separator="," open="(" close=")">
            #{sort.key}
        </foreach>
    </update>

    <select id="queryTaskDropList" resultMap="taskEntity">
            select `t`.`id`, `t`.`name`
        from `taskcenter`.`task` t
        where `t`.`is_deleted` = 0
        and `t`.`status` = 1
        <if test="query.category_code != null and query.category_code != ''">
        and `t`.`category_code` = #{query.category_code}
        </if>
        <if test="query.channelCodes != null and query.channelCodes.size() != 0">
            and `t`.`channel_code` in
            <foreach collection="query.channelCodes" separator="," close=")" open="(" item="item">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="query.name != null and query.name != ''">
            and (`t`.`name` like concat('%',#{query.name},'%') or `t`.`id` = #{query.name})
        </if>

        limit #{pageInfo.offset},#{pageInfo.size}

    </select>

    <select id="queryTaskDropListRows" resultType="int">
        select COUNT(1)
        from `taskcenter`.`task` t
        where `t`.`is_deleted` = 0
        and `t`.`status` = 1
        <if test="query.category_code != null and query.category_code != ''">
            and `t`.`category_code` = #{query.category_code}
        </if>
        <if test="query.channel_code != null and query.channel_code != ''">
            and `t`.`channel_code` = #{query.channel_code}
        </if>
        <if test="query.name != null and query.name != ''">
            and (`t`.`name` like concat('%',#{query.name},'%') or `t`.`id` = #{query.name})
        </if>
    </select>

    <select id="queryTaskByIds" resultMap="taskEntity">
        select `t`.`id`, `t`.`name`
        from `taskcenter`.`task` t
        where `t`.`is_deleted` = 0
        <if test="taskIds != null and taskIds.size() != 0">
            and `t`.`id` in
            <foreach collection="taskIds" item="taskId" index="index" separator="," open="(" close=")">
                #{taskId}
            </foreach>
        </if>
    </select>
</mapper>