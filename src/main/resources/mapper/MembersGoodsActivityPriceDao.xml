<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.member.Dao.MembersGoodsActivityPriceDao">
    <resultMap type="com.fotile.cmscenter.member.pojo.MembersGoodsActivityPrice" id="MembersGoodsActivityPriceResult">
        <result property="id"    column="id"    />
        <result property="memberGoodsId"    column="member_goods_id"    />
        <result property="activityPrice"    column="activity_price"    />
        <result property="activityStartTime"    column="activity_start_time"    />
        <result property="activityEndTime"    column="activity_end_time"    />
        <result property="createdDate"    column="created_date"    />
        <result property="createdBy"    column="created_by"    />
        <result property="modifiedDate"    column="modified_date"    />
        <result property="modifiedBy"    column="modified_by"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>


    <select id="findAllByGoodsId" resultMap="MembersGoodsActivityPriceResult">
        select id,member_goods_id,activity_price,activity_start_time,activity_end_time
        from members_goods_activity_price
        where is_deleted=0
        <if test="goodsId != null">
            and member_goods_id=#{goodsId}
        </if>
    </select>

    <update id="updateActivityPriceStage">
        update members_goods_activity_price
        set is_deleted=#{isDeleted}
        where member_goods_id=#{memberGoodsId}
    </update>
</mapper>