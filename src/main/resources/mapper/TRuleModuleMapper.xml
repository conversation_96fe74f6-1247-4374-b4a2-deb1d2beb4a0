<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.msg.rule.dao.TRuleModuleMapper">
  <resultMap id="BaseResultMap" type="com.fotile.msg.rule.pojo.entity.TRuleModule">
    <!--@mbg.generated-->
    <!--@Table msgcenter.t_rule_module-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="module_name" jdbcType="VARCHAR" property="moduleName" />
    <result column="module_code" jdbcType="VARCHAR" property="moduleCode" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_deleted, created_by, created_date, modified_by, modified_date, module_name, 
    module_code, `enable`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update msgcenter.t_rule_module
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="created_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="modified_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="modified_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="module_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.moduleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="module_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.moduleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.enable,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update msgcenter.t_rule_module
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="modified_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="modified_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="module_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.moduleName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.moduleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="module_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.moduleCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.moduleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enable != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.enable,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into msgcenter.t_rule_module
    (is_deleted, created_by, created_date, modified_by, modified_date, module_name, module_code, 
      `enable`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.moduleName,jdbcType=VARCHAR}, 
        #{item.moduleCode,jdbcType=VARCHAR}, #{item.enable,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.msg.rule.pojo.entity.TRuleModule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into msgcenter.t_rule_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      is_deleted,
      created_by,
      created_date,
      modified_by,
      modified_date,
      module_name,
      module_code,
      `enable`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{moduleName,jdbcType=VARCHAR},
      #{moduleCode,jdbcType=VARCHAR},
      #{enable,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      module_name = #{moduleName,jdbcType=VARCHAR},
      module_code = #{moduleCode,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.msg.rule.pojo.entity.TRuleModule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into msgcenter.t_rule_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
      <if test="moduleName != null">
        module_name,
      </if>
      <if test="moduleCode != null">
        module_code,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleName != null">
        #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="moduleCode != null">
        #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="moduleName != null">
        module_name = #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="moduleCode != null">
        module_code = #{moduleCode,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>