<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.orgcenter.store.mapper.StoreDao">

    <!-- 新增门店-->
    <insert id="addStore" parameterType="storeEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO orgcenter.t_store<!--czw加密已处理-->
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="latitude != null and latitude !=''">
                latitude,
            </if>
            <if test="longitude != null and longitude !=''">
                longitude,
            </if>
            <if test="planCode != null and planCode !=''">
                plan_code,
            </if>
            <if test="wechat != null and wechat !=''">
                wechat,
            </if>
            <if test="shoppingRemark != null and shoppingRemark !=''">
                shopping_remark,
            </if>
            <if test="telRemark != null and telRemark !=''">
                tel_remark,
            </if>
            <if test="email != null and email !=''">
                email,
            </if>
            <if test="emailRemark != null and emailRemark !=''">
                email_remark,
            </if>
            <if test="wechatRemark != null and  wechatRemark !=''">
                wechat_remark,
            </if>

            <if test="alipayRemark != null and  alipayRemark !=''">
                alipay_remark,
            </if>
            <if test="channel != null and channel !=''">
                channel,
            </if>
            <if test="orgId != null ">
                org_id,
            </if>
            <if test="keyWord != null and keyWord !=''">
                key_word,
            </if>
            <if test="coverurl != null and coverurl !=''">
                coverurl,
            </if>
            <if test="address != null and address !=''">
                address,
            </if>
            <if test="provicenId != null ">
                provicen_id,
            </if>
            <if test="cityId != null ">
                city_id,
            </if>
            <if test="countyId != null ">
                county_id,
            </if>
            <if test="shoppingStart != null and shoppingStart !=''">
                shopping_start,
            </if>
            <if test="shoppingEnd != null and shoppingEnd !=''">
                shopping_end,
            </if>
            <if test="tel != null and tel !=''">
                tel,
            </if>
            <if test="leaderId != null ">
                leader_id,
            </if>
            <if test="alipayno != null and alipayno !=''">
                alipayno,
            </if>
            <if test="alipaynourl != null and alipaynourl !=''">
                alipaynourl,
            </if>
            <if test="wechatno != null and wechatno !=''">
                wechatno,
            </if>
            <if test="wechatnourl != null and wechatnourl !=''">
                wechatnourl,
            </if>
            <if test="note != null and note !=''">
                note,
            </if>
            <if test="status != null ">
                status,
            </if>
            <if test="provicenName != null and provicenName !=''">
                provicen_name,
            </if>
            <if test="cityName != null and cityName !=''">
                city_name,
            </if>
            <if test="countyName != null and countyName !=''">
                county_name,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="address2 != null and address2 !=''">
                address2,
            </if>
            <if test="isToDrp != null and isToDrp !=''">
                is_to_drp,
            </if>
            <if test="isManagerCluesIntoSea != null">
                is_manager_clues_into_sea,
            </if>
            <if test="toDrpStage != null and toDrpStage !=''">
                to_drp_stage,
            </if>
            <if test="storeType != null ">
                store_type,
            </if>
            <if test="cluesMsgUser != null and cluesMsgUser !=''">
                clues_msg_user,
            </if>
            <if test="createdBy != null and createdBy !=''">
                created_by,
            </if>
            <if test="modifiedBy != null and modifiedBy !=''">
                modified_by,
            </if>
            <if test="abbreviation != null and abbreviation !=''">
                abbreviation,
            </if>
            <if test="distributorId != null ">
                distributor_id,
            </if>
            <if test="developSalesmanId != null ">
                develop_salesman_id,
            </if>
            <if test="developSalesmanName != null and developSalesmanName !=''">
                develop_salesman_name,
            </if>
            <if test="developSalesmanCode != null and developSalesmanCode !=''">
                develop_salesman_code,
            </if>
            <if test="developSalesmanPhone != null and developSalesmanPhone !=''">
                develop_salesman_phone,
            </if>
            <if test="preAudit != null ">
                app_pre_audit,
            </if>
            <if test="intro != null and intro !=''">
                intro,
            </if>
            <if test="consultationPc != null and consultationPc !=''">
                consultation_pc,
            </if>
            <if test="consultationM != null and consultationM !=''">
                consultation_m,
            </if>
            <if test="isTransfer != null ">
                is_transfer,
            </if>
            <if test="isToCheck != null ">
                is_to_check,
            </if>
            is_open_kpoint,
            is_deleted,
            created_date,
            modified_date,
            <if test="decorateStatus != null ">
                decorate_status,
            </if>
            <if test="openDate != null ">
                open_date,
            </if>
            <if test="closeDate != null ">
                close_date,
            </if>
            <if test="terminalCheckDate != null ">
                terminal_check_date,
            </if>
            <if test="terminalCheckDateAgain != null ">
                terminal_check_date_again,
            </if>
            <if test="storeMarketGrade != null and storeMarketGrade !=''">
                store_market_grade,
            </if>
            <if test="storeChannelCode != null and storeChannelCode !=''">
                store_channel_code,
            </if>
            <if test="distributorChannel != null and distributorChannel !=''">
                distributor_channel,
            </if>
            <if test="relatedStoreId != null and relatedStoreId !=''">
                related_store_id,
            </if>
            <if test="isVirtual != null ">
                is_virtual,
            </if>
            <if test="needTerminalBuild != null ">
                need_terminal_build,
            </if>
            <if test="managerName != null and managerName !=''">
                manager_name,
            </if>
            <if test="managerPhone != null and managerPhone !=''">
                manager_phone,
            </if>
            <if test="storeBizType != null and storeBizType !=''">
                store_biz_type,
            </if>
            <if test="staffs != null and staffs !=''">
                staffs,
            </if>
            <if test="propertyRight != null ">
                property_right,
            </if>
            <if test="leaseTerm != null ">
                lease_term,
            </if>
            <if test="annualRent != null ">
                annual_rent,
            </if>
            <if test="ifDecorationStore != null ">
                if_decoration_store,
            </if>
            <if test="bpmType != null">
                bpm_type,
            </if>


            <if test="marketCapacity != null">
                market_capacity,
            </if>
            <if test="population != null">
                population,
            </if>
            <if test="storeLevel != null and storeLevel !=''">
                store_level,
            </if>
            <if test="isUnmanned != null ">
                is_unmanned,
            </if>
            <if test="isUniform != null ">
                is_uniform,
            </if>
            <if test="uniformPic != null and uniformPic!=''">
                uniform_pic,
            </if>
            <if test="topKitchens != null and topKitchens!=''">
                top_kitchens,
            </if>
            <if test="signShape != null and signShape!=''">
                sign_shape,
            </if>
            <if test="signSize != null ">
                sign_size,
            </if>
            <if test="signCost != null ">
                sign_cost,
            </if>
            <if test="epsUsableArea != null ">
                eps_usable_area,
            </if>


            <if test="openType != null">
                open_type,
            </if>
            <if test="estimatedAnnualSales != null">
                estimated_annual_sales,
            </if>
            <if test="usableArea != null">
                usable_area,
            </if>
            <if test="storeSubChannelCode != null and storeSubChannelCode !=''">
                store_sub_channel_code,
            </if>
            outside_store_name,
            <if test="cluesOverdue != null">
                clues_overdue,
            </if>
            <if test="depositCluesOverdue != null">
                deposit_clues_overdue,
            </if>
            <if test="usePos != null">
                use_pos,
            </if>
            <if test="terminalImageScore != null">
                terminal_image_score,
            </if>
            <!--<if test="isSceneFlag != null">
                is_scene_flag,
            </if>-->
            <if test="wholeUsableArea != null">
                whole_usable_area,
            </if>
            <if test="liveDemo != null">
                live_demo,
            </if>
            <if test="layoutCode != null">
                layout_code,
            </if>
            <if test="sceneSuit != null">
                scene_suit,
            </if>
            <if test="tiktokCode !=null and tiktokCode !=''">
                tiktok_code,
            </if>
            <if test="tiktokName !=null and tiktokName !=''">
                tiktok_name,
            </if>
            old_compensate_merchant_code,
            open_wy_channel,
            open_zq_channel,
            open_rq_channel
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="latitude != null and latitude !=''">
                #{latitude},
            </if>
            <if test="longitude != null and longitude !=''">
                #{longitude},
            </if>
            <if test="planCode != null and planCode !=''">
                #{planCode},
            </if>
            <if test="wechat != null and wechat !=''">
                #{wechat},
            </if>
            <if test="shoppingRemark != null and shoppingRemark !=''">
                #{shoppingRemark},
            </if>
            <if test="telRemark != null and telRemark !=''">
                #{telRemark},
            </if>
            <if test="email != null and email !=''">
                #{email},
            </if>
            <if test="emailRemark != null and emailRemark !=''">
                #{emailRemark},
            </if>
            <if test="wechatRemark != null and  wechatRemark !=''">
                #{wechatRemark},
            </if>

            <if test="alipayRemark != null and  alipayRemark !=''">
                #{alipayRemark},
            </if>
            <if test="channel != null and channel !=''">
                #{channel},
            </if>
            <if test="orgId != null ">
                #{orgId},
            </if>
            <if test="keyWord != null and keyWord !=''">
                #{keyWord},
            </if>
            <if test="coverurl != null and coverurl !=''">
                #{coverurl},
            </if>
            <if test="address != null and address !=''">
                #{address},
            </if>
            <if test="provicenId != null ">
                #{provicenId},
            </if>
            <if test="cityId != null ">
                #{cityId},
            </if>
            <if test="countyId != null ">
                #{countyId},
            </if>
            <if test="shoppingStart != null and shoppingStart !=''">
                #{shoppingStart},
            </if>
            <if test="shoppingEnd != null and shoppingEnd !=''">
                #{shoppingEnd},
            </if>
            <if test="tel != null and tel !=''">
                #{tel},
            </if>
            <if test="leaderId != null ">
                #{leaderId},
            </if>
            <if test="alipayno != null and alipayno !=''">
                #{alipayno},
            </if>
            <if test="alipaynourl != null and alipaynourl !=''">
                #{alipaynourl},
            </if>
            <if test="wechatno != null and wechatno !=''">
                #{wechatno},
            </if>
            <if test="wechatnourl != null and wechatnourl !=''">
                #{wechatnourl},
            </if>
            <if test="note != null and note !=''">
                #{note},
            </if>
            <if test="status != null ">
                #{status},
            </if>
            <if test="provicenName != null and provicenName !=''">
                #{provicenName},
            </if>
            <if test="cityName != null and cityName !=''">
                #{cityName},
            </if>
            <if test="countyName != null and countyName !=''">
                #{countyName},
            </if>
            <if test="companyId != null ">
                #{companyId},
            </if>
            <if test="address2 != null and address2 !=''">
                #{address2},
            </if>
            <if test="isToDrp != null and isToDrp !=''">
                #{isToDrp},
            </if>
            <if test="isManagerCluesIntoSea != null">
                #{isManagerCluesIntoSea},
            </if>
            <if test="toDrpStage != null and toDrpStage !=''">
                #{toDrpStage},
            </if>
            <if test="storeType != null ">
                #{storeType},
            </if>
            <if test="cluesMsgUser != null and cluesMsgUser !=''">
                #{cluesMsgUser},
            </if>
            <if test="createdBy != null and createdBy !=''">
                #{createdBy},
            </if>
            <if test="modifiedBy != null and modifiedBy !=''">
                #{modifiedBy},
            </if>
            <if test="abbreviation != null and abbreviation !=''">
                #{abbreviation},
            </if>
            <if test="distributorId != null ">
                #{distributorId},
            </if>
            <if test="developSalesmanId != null ">
                #{developSalesmanId},
            </if>
            <if test="developSalesmanName != null and developSalesmanName !=''">
                #{developSalesmanName},
            </if>
            <if test="developSalesmanCode != null and developSalesmanCode !=''">
                #{developSalesmanCode},
            </if>
            <if test="developSalesmanPhone != null and developSalesmanPhone !=''">
                #{developSalesmanPhone},
            </if>
            <if test="preAudit != null ">
                #{preAudit},
            </if>
            <if test="intro != null and intro !=''">
                #{intro},
            </if>
            <if test="consultationPc != null and consultationPc !=''">
                #{consultationPc},
            </if>
            <if test="consultationM != null and consultationM !=''">
                #{consultationM},
            </if>
            <if test="isTransfer != null ">
                #{isTransfer},
            </if>
            <if test="isToCheck != null ">
                #{isToCheck},
            </if>
            '0',
            0,
            now(),
            now(),
            <if test="decorateStatus != null ">
                #{decorateStatus},
            </if>
            <if test="openDate != null ">
                #{openDate},
            </if>
            <if test="closeDate != null ">
                #{closeDate},
            </if>
            <if test="terminalCheckDate != null ">
                #{terminalCheckDate},
            </if>
            <if test="terminalCheckDateAgain != null ">
                #{terminalCheckDateAgain},
            </if>
            <if test="storeMarketGrade != null and storeMarketGrade !=''">
                #{storeMarketGrade},
            </if>
            <if test="storeChannelCode != null and storeChannelCode !=''">
                #{storeChannelCode},
            </if>
            <if test="distributorChannel != null and distributorChannel !=''">
                #{distributorChannel},
            </if>
            <if test="relatedStoreId != null and relatedStoreId !=''">
                #{relatedStoreId},
            </if>
            <if test="isVirtual != null ">
                #{isVirtual},
            </if>
            <if test="needTerminalBuild != null ">
                #{needTerminalBuild},
            </if>
            <if test="managerName != null and managerName !=''">
                #{managerName},
            </if>
            <if test="managerPhone != null and managerPhone !=''">
                #{managerPhone},
            </if>
            <if test="storeBizType != null and storeBizType !=''">
                #{storeBizType},
            </if>
            <if test="staffs != null and staffs !=''">
                #{staffs},
            </if>
            <if test="propertyRight != null ">
                #{propertyRight},
            </if>
            <if test="leaseTerm != null ">
                #{leaseTerm},
            </if>
            <if test="annualRent != null ">
                #{annualRent},
            </if>
            <if test="ifDecorationStore != null ">
                #{ifDecorationStore},
            </if>
            <if test="bpmType != null ">
                #{bpmType},
            </if>


            <if test="marketCapacity != null">
                #{marketCapacity},
            </if>
            <if test="population != null">
                #{population},
            </if>
            <if test="storeLevel != null and storeLevel !=''">
                #{storeLevel},
            </if>
            <if test="isUnmanned != null ">
                #{isUnmanned},
            </if>
            <if test="isUniform != null ">
                #{isUniform},
            </if>
            <if test="uniformPic != null and uniformPic!=''">
                #{uniformPic},
            </if>
            <if test="topKitchens != null and topKitchens!=''">
                #{topKitchens},
            </if>
            <if test="signShape != null and signShape!=''">
                #{signShape},
            </if>
            <if test="signSize != null ">
                #{signSize},
            </if>
            <if test="signCost != null ">
                #{signCost},
            </if>
            <if test="epsUsableArea != null ">
                #{epsUsableArea},
            </if>


            <if test="openType != null ">
                #{openType},
            </if>
            <if test="estimatedAnnualSales != null">
                #{estimatedAnnualSales},
            </if>
            <if test="usableArea != null">
                #{usableArea},
            </if>
            <if test="storeSubChannelCode != null and storeSubChannelCode !=''">
                #{storeSubChannelCode},
            </if>
            #{outsideStoreName},
            <if test="cluesOverdue != null">
                #{cluesOverdue},
            </if>
            <if test="depositCluesOverdue != null">
                #{depositCluesOverdue},
            </if>
            <if test="usePos != null">
                #{usePos},
            </if>
            <if test="terminalImageScore != null">
                #{terminalImageScore},
            </if>
            <!--<if test="isSceneFlag != null">
                #{isSceneFlag},
            </if>-->
            <if test="wholeUsableArea != null">
                #{wholeUsableArea},
            </if>
            <if test="liveDemo != null">
                #{liveDemo},
            </if>
            <if test="layoutCode != null">
                #{layoutCode},
            </if>
            <if test="sceneSuit != null">
                #{sceneSuit},
            </if>
            <if test="tiktokCode !=null and tiktokCode !=''">
                #{tiktokCode},
            </if>
            <if test="tiktokName !=null and tiktokName !=''">
                #{tiktokName},
            </if>
            #{oldCompensateMerchantCode},
            #{openWyChannel},
            #{openZqChannel},
            #{openRqChannel}
        </trim>
    </insert>
    <!-- 新增门店(excel) -->
    <insert id="addStoreFromExcel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO orgcenter.t_store<!--czw加密已处理-->(org_id, key_word, coverurl, address, provicen_id, city_id, county_id,
                                      shopping_start, shopping_end, tel,
                                      leader_id, alipayno, alipaynourl, wechatno, wechatnourl, note, is_deleted, status,
                                      provicen_name, city_name, county_name, company_id, address2, created_by,
                                      created_date, modified_by, modified_date, intro, consultation_pc, consultation_m)
        VALUES (#{orgId}, #{keyWord}, #{coverurl}, #{address}, #{provicenId}, #{cityId}, #{countyId}, #{shoppingStart},
                #{shoppingEnd}, #{tel,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler},
                #{leaderId}, #{alipayno}, #{alipaynourl}, #{wechatno}, #{wechatnourl}, #{note}, 0, #{status},
                #{provicenName}, #{cityName}, #{countyName}, #{companyId}, #{address2}, #{createdBy}, now(),
                #{modifiedBy}, now(), #{intro}, #{consultationPc}, #{consultationM})
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        UPDATE orgcenter.t_store<!--czw加密已处理版本-->
        <set>
            close_date=#{closeDate},
            terminal_check_date=#{terminalCheckDate},
            terminal_check_date_again=#{terminalCheckDateAgain},
            close_start_date=#{closeStartDate},
            modified_date=now(),modified_by=#{modifiedBy},`version`=`version`+1
            <!-- 关键字 -->
            ,key_word=#{keyWord}
            <!--<if test="coverurl !=null ">-->
                ,coverurl=#{coverurl}<!-- 封面图 -->
            <!--</if>-->
            ,provicen_id=#{provicenId}
            ,city_id=#{cityId}
            ,county_id=#{countyId}
            ,whole_usable_area=#{wholeUsableArea}
            ,address=#{address}
            <!--<if test="shoppingStart !=null ">-->
                ,shopping_start=#{shoppingStart}<!-- 营业时间-start -->
            <!--</if>-->
            <!--<if test="shoppingEnd !=null ">-->
                ,shopping_end=#{shoppingEnd}<!-- 营业时间-end -->
            <!--</if>-->
            <!-- 电话 -->
            ,tel=#{tel}
            <!-- 总经理 -->
            ,leader_id=#{leaderId}
            <!-- 支付宝收款账号 -->
            ,alipayno=#{alipayno}
            <!-- 支付宝收款账号二维码 -->
            ,alipaynourl=#{alipaynourl}
            <!-- 微信收款账号 -->
            ,wechatno=#{wechatno}
            <!-- 微信收款账号二维码 -->
            ,wechatnourl=#{wechatnourl}
            <!-- 备注 -->
            ,note=#{note}
            ,provicen_name=#{provicenName}
            ,city_name=#{cityName}
            ,county_name=#{countyName}
            <if test="companyId !=null ">
                ,company_id=#{companyId}<!-- 所属公司 -->
            </if>
            <if test="status !=null "><!-- 状态 -->
                ,status=#{status}
            </if>

            <if test="address2 !=null ">
                ,address2=#{address2}<!-- 地址2 -->
            </if>
            <if test="shoppingRemark !=null ">
                ,shopping_remark=#{shoppingRemark}
            </if>
            ,tel_remark =#{telRemark}<!--czw加密已处理4-->
            ,email =#{email}
            ,email_remark =#{emailRemark}
            ,wechat_remark =#{wechatRemark}<!--czw加密已处理4-->
            ,alipay_remark =#{alipayRemark}
            <if test="channel !=null ">
                ,channel=#{channel}
            </if>
            <if test="wechat !=null ">
                ,wechat=#{wechat}
            </if>
            <if test="latitude !=null ">
                ,latitude=#{latitude}
            </if>
            <if test="longitude !=null ">
                ,longitude=#{longitude}
            </if>
            <if test="isToDrp !=null "><!-- 是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP -->
                ,is_to_drp=#{isToDrp}
            </if>
            <if test="isManagerCluesIntoSea !=null ">
                ,is_manager_clues_into_sea=#{isManagerCluesIntoSea}
            </if>
            <if test="toDrpStage !=null "><!-- 订单传送至DRP状态：1：打开；2：审核 默认值1 -->
                ,to_drp_stage=#{toDrpStage}
            </if>
            <!-- 门店类型，1：专卖店；2：KA店；3：社区店 -->
            ,store_type=#{storeType}
            <if test="cluesMsgUser !=null"><!-- 线索下发通知账号 -->
                ,clues_msg_user=#{cluesMsgUser}
            </if>
            <if test="abbreviation !=null">
                ,abbreviation=#{abbreviation}<!-- 门店简称 -->
            </if>
            <if test="intro !=null">
                ,intro=#{intro}
            </if>
            <if test="consultationPc !=null">
                ,consultation_pc=#{consultationPc}
            </if>
            <if test="consultationM !=null">
                ,consultation_m=#{consultationM}
            </if>
            <!-- 经销商id -->
            ,distributor_id=#{distributorId}
            <!-- 门店业务发展主管 -->
            ,develop_salesman_id=#{developSalesmanId}
            ,develop_salesman_code=#{developSalesmanCode}
            ,develop_salesman_name=#{developSalesmanName}
            ,develop_salesman_phone=#{developSalesmanPhone}
            <!-- 是否在云管理预审核 0：否；1：是 -->
            ,app_pre_audit=#{preAudit}
            <if test="isTransfer !=null">
                ,is_transfer=#{isTransfer}
            </if>
            <if test="isToCheck !=null">
                ,is_to_check=#{isToCheck}
            </if>

            <if test="decorateStatus != null ">
                ,decorate_status=#{decorateStatus}
            </if>
            <if test="openDate != null ">
                ,open_date=#{openDate}
            </if>
            <if test="closeDate != null ">
                ,close_date=#{closeDate}
            </if>
            <if test="storeMarketGrade != null and storeMarketGrade !=''">
                ,store_market_grade=#{storeMarketGrade}
            </if>
            ,store_channel_code=#{storeChannelCode}
            ,outside_store_name=#{outsideStoreName}
            <if test="distributorChannel != null and distributorChannel !=''">
                ,distributor_channel=#{distributorChannel}
            </if>
            ,related_store_id=#{relatedStoreId}
            <if test="isVirtual != null ">
                ,is_virtual=#{isVirtual}
            </if>
            <if test="needTerminalBuild != null ">
                ,need_terminal_build=#{needTerminalBuild}
            </if>

                ,manager_name=#{managerName}


                ,manager_phone=#{managerPhone}

            <!--<if test="storeBizType != null and storeBizType !=''">-->
                ,store_biz_type=#{storeBizType}
            <!--</if>-->
            <if test="staffs != null and staffs !=''">
                ,staffs=#{staffs}
            </if>
            <!--<if test="propertyRight != null ">-->
                ,property_right=#{propertyRight}
            <!--</if>-->

                ,lease_term=#{leaseTerm}

            <if test="ifDecorationStore !=null">
                ,if_decoration_store=#{ifDecorationStore}
            </if>

                ,annual_rent=#{annualRent}

            <if test="bpmType != null ">
                ,bpm_type=#{bpmType}
            </if>
            <if test="openType != null ">
                ,open_type=#{openType}
            </if>

                ,market_capacity=#{marketCapacity}

           <!-- <if test="storeLevel != null and storeLevel!=''"></if>-->
                ,store_level=#{storeLevel}

            <!--<if test="isUnmanned != null ">-->
                ,is_unmanned=#{isUnmanned}
            <!--</if>-->

            ,population=#{population}

            <!--<if test="signShape != null and signShape !=''">
            </if>-->
                ,sign_shape=#{signShape}


                ,sign_size=#{signSize}

                ,sign_cost=#{signCost}
            <if test="isUniform != null ">
                ,is_uniform=#{isUniform}
            </if>
            ,uniform_pic=#{uniformPic}
            ,top_kitchens=#{topKitchens}

            <if test="epsUsableArea!=null">
                ,eps_usable_area=#{epsUsableArea}
            </if>

            ,estimated_annual_sales=#{estimatedAnnualSales}
            ,store_sub_channel_code=#{storeSubChannelCode}
            <if test="cluesFollow !=null "><!-- 是否进行线索跟进审核 0：否；1：是 -->
                ,clues_follow=#{cluesFollow}
            </if>
            <if test="cluesLose !=null "><!-- 是否进行线索丢掉审核 0：否；1：是 -->
                ,clues_lose=#{cluesLose}
            </if>
            ,clues_overdue=#{cluesOverdue}
            ,deposit_clues_overdue=#{depositCluesOverdue}
            ,use_pos=#{usePos}
            ,live_demo=#{liveDemo}
            ,layout_code=#{layoutCode}
            ,terminal_image_score=#{terminalImageScore}
            ,scene_suit=#{sceneSuit}
            <!--,is_scene_flag=#{isSceneFlag}-->
            ,`version`=`version`+1
            <if test="isCashierEnabled != null">
                ,is_cashier_enabled=#{isCashierEnabled}
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                ,merchant_code=#{merchantCode}
            </if>
            <if test="izImPayActivityEnabled != null">
                ,iz_im_pay_activity_enabled=#{izImPayActivityEnabled}
            </if>
            <if test="isSmartBadge != null">
                ,is_smart_badge=#{isSmartBadge}
            </if>
            <if test="merchantName != null and merchantName != ''">
                ,merchant_name=#{merchantName}
            </if>
            <if test="merchantCodeOnline != null and merchantCodeOnline != ''">
                ,merchant_code_online=#{merchantCodeOnline}
            </if>
            ,old_compensate_merchant_code=#{oldCompensateMerchantCode}
            ,cashier_charge_mapping=#{cashierChargeMapping}
            ,open_wy_channel=#{openWyChannel}
            ,open_zq_channel=#{openZqChannel}
            ,open_rq_channel=#{openRqChannel}
<!--            '是否开启手机号检查 0:否；1.手机号检查 2.地址检查 12.手机号及地址检查',-->
            ,    clues_check_phone = #{cluesCheckPhone}
<!--            '处理结果 1：提示并保存成功；2：提示并保存失败',-->
            , clues_process_result = #{cluesProcessResult}
<!--            '查重范围 1：门店；2：客户；3：公司',-->
          ,  clues_check_scope  = #{cluesCheckScope}
<!--            '查重范围 1：7天以内；2：30天以内；3：90天以内；4:180天以内',-->
          , clues_check_period   = #{cluesCheckPeriod}
<!--            '查重范围 1：未跟进；2：已跟进；3：已进店；4:已成交；5：已丢单';-->
           , clues_follow_status  = #{cluesFollowStatus}
            ,`tiktok_code`=#{tiktokCode}
            ,`tiktok_name`=#{tiktokName}
        </set>
        WHERE id=#{id}
    </update>

    <!-- 根据门店id查询门店信息 新增的字段一定要在这里加上-->
    <select id="findById" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,
                s.plan_code,
               s.is_open_kpoint,
               s.annual_rent,
               s.is_open_rent,
               s.stadiums_introduce,
               s.travel_tips,
               s.`status`,
               s.wechat,
               s.shopping_remark,
               s.tel_remark<!--czw加密已处理4-->,
               s.email,
               s.email_remark,
               s.wechat_remark<!--czw加密已处理4-->,
               s.alipay_remark,
               s.channel,
               s.org_id                                orgId,
               o.CODE,
               o.NAME,
               s.key_word                              keyWord,
               s.coverurl,
               s.address,
               s.provicen_id                           provicenId,
               s.provicen_name,
               s.city_id                               cityId,
               s.city_name,
               s.county_id                             countyId,
               s.county_name,
               s.longitude,
               s.latitude,
               s.shopping_start                        shoppingStart,
               s.shopping_end                          shoppingEnd,
               s.tel<!--czw加密已处理-->,
               s.leader_id                             leaderId,
               s.alipayno,
               s.alipaynourl,
               s.wechatno,
               s.wechatnourl,
               s.note,
               o.parent_id                             parentId,
               o.full_path_id                          fullPathId,
               o.full_path_name                        fullPathName,
               s.company_id,
               s.address2,
               s.is_to_drp                             isToDrp,
               s.is_manager_clues_into_sea             isManagerCluesIntoSea,
               s.to_drp_stage                          toDrpStage,
               s.store_type                            storeType,
               s.clues_msg_user                        cluesMsgUser,
               s.abbreviation,
               cc.`name`                               storeTypeName,
               cc.code                                 storeTypeCode,
               s.intro,
               s.consultation_pc,
               s.consultation_m,
               (select d.channel_subdivide
                from orgcenter.t_distributor d
                WHERE d.id = s.distributor_id)         channelSubdivide,
               (SELECT m.distributor_id
                FROM orgcenter.t_distributor_mapping m
                WHERE m.is_deleted = 0
                  AND m.type = 3
                  AND s.org_id = m.org_id)             distributorId,
               s.develop_salesman_id                   developSalesmanId,
               s.develop_salesman_code                 developSalesmanCode,
               s.develop_salesman_name                 developSalesmanName,
               s.develop_salesman_phone                developSalesmanPhone,
               s.app_pre_audit                         preAudit,
               s.is_transfer                           isTransfer,
               s.is_to_check                           isToCheck,
               s.decorate_status                       decorateStatus,
               s.open_date                             openDate,
               s.close_date                            closeDate,
               s.terminal_check_date                    terminalCheckDate,
                s.terminal_check_date_again      terminalCheckDateAgain,
               s.is_scene_flag,
               s.created_date,
               s.store_market_grade                    storeMarketGrade,
               cc.code                                 storeChannelCode,
               s.distributor_channel                   distributorChannel,
               s.related_store_id                      relatedStoreId,
               s.is_virtual                            isVirtual,
               s.need_terminal_build                   needTerminalBuild,
               s.manager_name<!--czw加密已处理-->                          managerName,
               s.manager_phone                         managerPhone,
               s.store_biz_type                        storeBizType,
               s.staffs                                staffs,
               s.property_right                        propertyRight,
               s.lease_term                            leaseTerm,
               s.bpm_type,
               s.usable_area,
               s.open_type,
               s.live_demo                             liveDemo,
               s.layout_code                           layoutCode,
               s.if_decoration_store,
               c.area,
               c.area_code                             areaCode,
               c.area_name                             areaName,
               td.id                                as distributorId,
               td.code                              as distributorCode,
               td.name                              as distributorName,
               (select ccc.`code`
                from orgcenter.channel_category ccc
                where ccc.id = td.channel_category) as distributorChannelCode,
               s.open_type,
               (select `name`
                from orgcenter.t_org cpy
                where cpy.is_deleted = 0
                  and cpy.type = 1
                  and cpy.id = s.company_id)        as companyName,
               s.estimated_annual_sales,
               c.id                                 as companyPkId,
               (select `code`
                from orgcenter.t_org cpy
                where cpy.is_deleted = 0
                  and cpy.type = 1
                  and cpy.id = s.company_id)        as companyCode,
               s.store_sub_channel_code,
               cc1.id as channelCategoryId,
               cc1.code as channelCategoryCode,
               cc1.name as channelCategoryName,
               cc2.id as channelSubdivideId,
               cc2.code as channelSubdivideCode,
               cc2.name as channelSubdivideName,
               s.eps_usable_area,
               s.whole_usable_area,
               s.market_capacity,
               s.population,
               s.store_level,
               s.is_unmanned,
               s.sign_shape,
               s.use_pos,
                s.clues_lose,
               s.clues_follow,
               s.store_channel_code,
               s.close_start_date,
               s.modified_date,
               s.modified_by,
               s.created_by,
               s.is_deleted,
               s.`version`,

               s.sign_size,
               s.sign_cost,
               S.terminal_image_score,
               s.is_uniform,
               s.outside_store_name,
               s.uniform_pic,
               s.scene_suit,
               s.top_kitchens,
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END

        ) as `character`,
               (
                    case when s.open_type=1 and s.is_virtual!=1
                            then (SELECT tsfn.decoration_type FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
                         when s.open_type=2 and s.is_virtual!=1
                            then (SELECT tsfc.decoration_type FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
                    end
               ) as decorationType,
               (
                    case when s.open_type=1 and s.is_virtual!=1
                            then (SELECT tsfn.door_size FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
                         when s.open_type=2 and s.is_virtual!=1
                            then (SELECT tsfc.door_size FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
                    end
               ) as doorSize
                ,s.clues_overdue cluesOverdue
        ,s.deposit_clues_overdue depositCluesOverdue
        ,s.iz_im_pay_activity_enabled as izImPayActivityEnabled
        ,s.is_cashier_enabled
        ,s.merchant_code
        ,s.merchant_name
        ,s.merchant_code_online
        ,s.cashier_charge_mapping
        ,s.open_wy_channel
        ,s.open_zq_channel
        ,s.open_rq_channel
        ,s.tiktok_code
        ,s.tiktok_name
        ,s.clues_check_period
        ,s.clues_follow_status
        ,s.clues_check_phone
        ,s.clues_check_scope
        ,s.clues_process_result

        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
                 LEFT JOIN orgcenter.channel_category cc ON cc.is_deleted = 0 AND s.store_type = cc.id
                 LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
                 LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type = 3 and tdm.is_deleted = 0 and tdm.org_id = s.org_id
                 LEFT JOIN orgcenter.t_distributor td on td.id = tdm.distributor_id and td.is_deleted = 0
                 LEFT JOIN orgcenter.channel_category cc1 on cc1.id=td.channel_category
                 LEFT JOIN orgcenter.channel_category cc2 on cc2.id=td.channel_subdivide
        WHERE s.id = #{id}
          AND o.is_deleted = 0
          AND s.is_deleted = 0
    </select>

    <sql id="findByIdsIdList">
        (
        <foreach collection="idList" item="id" separator="union">
            select @findByIdsIdList:=#{id} as id
        </foreach>
        ) as findByIdsIdList
    </sql>
    <!-- 根据门店id集合查询门店信息 -->
    <select id="findByIds" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.is_open_kpoint,
        s.is_open_rent,
        s.`status`,
        s.wechat,
        s.shopping_remark,
        s.tel_remark<!--czw加密已处理4-->,
        s.email,
        s.email_remark,
        s.wechat_remark<!--czw加密已处理4-->,
        s.alipay_remark,
        s.channel,
        s.org_id orgId,
        o. code,
        o. name,
        s.key_word keyWord,
        s.terminal_check_date                    terminalCheckDate,
        s.terminal_check_date_again      terminalCheckDateAgain,
        s.coverurl,
        s.address,
        s.provicen_id provicenId,
        s.city_id cityId,
        s.county_id countyId,
        o.longitude,
        o.latitude,
        s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,
        s.tel<!--czw加密已处理-->,
        s.leader_id leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        o.parent_id parentId,
        s.tel<!--czw加密已处理-->,
        o.full_path_id fullPathId,
        o.full_path_name fullPathName,
        s.company_id companyId,
        s.address2,
        s.is_to_drp isToDrp,s.to_drp_stage toDrpStage,s.abbreviation,
        s.is_manager_clues_into_sea isManagerCluesIntoSea,
        s.intro,s.consultation_pc,s.consultation_m,
        s.develop_salesman_id developSalesmanId,s.develop_salesman_code developSalesmanCode,s.develop_salesman_name
        developSalesmanName,s.develop_salesman_phone developSalesmanPhone,s.app_pre_audit preAudit,s.store_type
        storeType,c.area,c.area_code areaCode, c.area_name areaName,
        (SELECT cc.`name` FROM orgcenter.channel_category cc WHERE cc.is_deleted=0 AND cc.`level`=3 AND
        cc.id=s.store_type) storeTypeName
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        <if test="idList !=null and idList.size>0">
            inner join
            <include refid="findByIdsIdList"/>
            on findByIdsIdList.id=s.id
        </if>
        WHERE
        o.is_deleted=0 AND s.is_deleted=0
        ORDER BY o.code ASC
    </select>
    <!-- 根据门店orgid查询门店信息 -->
    <select id="findByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,
        s.org_id orgId,
        s.plan_code,
        o.code,
        o.name,
        o.created_date,
        (
            case when s.open_type=1 and s.is_virtual!=1
                    then (SELECT tsfn.door_size FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
                when s.open_type=2 and s.is_virtual!=1
                    then (SELECT tsfc.door_size FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
            end
        ) as doorSize,
        s.key_word as keyWord,
        s.coverurl,
        s.address,
        s.provicen_id provicenId,
        s.city_id cityId,
        s.county_id countyId,
        s.longitude,
        s.latitude,
        s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,
        s.tel<!--czw加密已处理-->,
        s.leader_id leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        s.live_demo                             liveDemo,
        s.layout_code                           layoutCode,
        s.if_decoration_store,
        s.annual_rent,
        s.scene_suit,
        s.market_capacity,
        s.population,
        s.store_level,
        s.is_unmanned,
        s.sign_shape,
        s.sign_size,
        s.sign_cost,
        s.is_uniform,
        s.uniform_pic,
        s.top_kitchens,

        o.parent_id parentId,
        deptorg.full_path_name parentName,
        <!--(select p.full_path_name from orgcenter.t_org p where p.id=o.parent_id) as parentName,-->
        deptorg.code as departmentCode,
        deptorg.name as departmentName,
        (select ifnull(tmp.status,'0') from orgcenter.t_department tmp where tmp.org_id=deptorg.id limit 1) as departmentStatus,

        o.full_path_id fullPathId,
        o.full_path_name fullPathName,

        s.address2,
        s.company_id companyId,
        s.is_to_drp isToDrp,
        s.is_manager_clues_into_sea isManagerCluesIntoSea,
        s.to_drp_stage toDrpStage,
        s.store_type storeType,
        s.intro,
        s.consultation_pc,
        s.consultation_m,
        cc.`name` storeTypeName,
        cc.code storeTypeCode,
        IF(s.abbreviation IS NULL OR TRIM(s.abbreviation) = '', o.name, s.abbreviation) abbreviation,
        s.app_pre_audit preAudit,
        c.cut_time cutTime,
        s.provicen_name provicenName,
        s.city_name cityName,
        s.county_name countyName,
        s.is_transfer isTransfer,
        s.is_to_check isToCheck,
        s.decorate_status decorateStatus,
        s.open_date openDate,
        s.close_date closeDate,
        s.close_start_date closeStartDate,
        s.terminal_check_date                    terminalCheckDate,
        s.terminal_check_date_again      terminalCheckDateAgain,
        s.store_market_grade storeMarketGrade,
        cc.id storeChannelId,
        cc.code storeChannelCode,
        cc.name as storeChannelName,
        cc.order_check_channel as orderCheckChannel,
        s.distributor_channel distributorChannel,
        s.related_store_id relatedStoreId,
        s.is_virtual isVirtual,
        s.need_terminal_build needTerminalBuild,
        s.manager_name managerName,
        s.manager_phone managerPhone,
        s.store_biz_type storeBizType,
        s.staffs staffs,
        c.area,
        s.terminal_image_score,
        c.area_code areaCode,
        c.area_name areaName,
        s.property_right propertyRight,
        s.lease_term leaseTerm,
        s.bpm_type,
        s.usable_area,
        s.whole_usable_area,
        td.id as distributorId,
        td.code as distributorCode,
        td.name as distributorName,
        td.receipt_type as receiptType,
        cc1.code as distributorChannelCode,
        s.open_type,
        (select `name` from orgcenter.t_org cpy where cpy.is_deleted=0 and cpy.type=1 and cpy.id=s.company_id) as
        companyName,
        (select `finance_audit_flag` from orgcenter.t_company cpy1 where cpy1.is_deleted=0 and cpy1.org_id=s.company_id) as
        financeAuditFlag,
        s.estimated_annual_sales,
        s.clues_msg_user cluesMsgUser,
        s.wechat,
        s.shopping_remark,
        s.tel_remark<!--czw加密已处理4-->,
        s.email,
        s.email_remark,
        s.wechat_remark<!--czw加密已处理4-->,
        s.alipay_remark,
        s.channel,
        s.develop_salesman_id developSalesmanId,
        s.develop_salesman_code developSalesmanCode,
        s.develop_salesman_name developSalesmanName,
        s.develop_salesman_phone developSalesmanPhone,
        c.id as companyPkId,
        s.store_sub_channel_code,
        s.outside_store_name,
        s.status,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,
        cc1.id as channelCategoryId,
        cc1.code as channelCategoryCode,
        cc1.name as channelCategoryName,
        cc2.id as channelSubdivideId,
        cc2.code as channelSubdivideCode,
        cc2.name as channelSubdivideName,
        s.eps_usable_area,
        s.is_open_kpoint,
        s.is_open_rent,
        s.stadiums_introduce,
        s.travel_tips,
        s.clues_follow cluesFollow,
        s.clues_lose cluesLose,
      <!--  (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )as `character`,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END

        ) as `character`,
        s.clues_overdue cluesOverdue,
        s.deposit_clues_overdue depositCluesOverdue,
        s.use_pos usePos,s.is_scene_flag isSceneFlag,
        s.`version`,
        s.is_cashier_enabled as isCashierEnabled,
        s.merchant_code as merchantCode,
        s.merchant_name as merchantName,
        s.merchant_code_online as merchantCodeOnline,
        s.cashier_charge_mapping as cashierChargeMapping,
        s.open_wy_channel,
        s.tiktok_name,
        s.tiktok_code,
        s.open_zq_channel,
        s.open_rq_channel,
        s.is_smart_badge as isSmartBadge,
        s.iz_im_pay_activity_enabled as izImPayActivityEnabled,
        s.old_compensate_merchant_code as oldCompensateMerchantCode,
        s.clues_check_phone cluesCheckPhone,
        s.clues_check_period cluesCheckPeriod,
        s.clues_check_scope cluesCheckScope,
        s.clues_follow_status cluesFollowStatus,
        s.clues_process_result cluesProcessResult

        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        LEFT JOIN orgcenter.t_org deptorg ON deptorg.id=o.parent_id and deptorg.is_deleted=0
        LEFT JOIN orgcenter.channel_category cc ON s.store_type = cc.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type=3 and tdm.is_deleted=0 and tdm.org_id=s.org_id
        LEFT JOIN orgcenter.t_distributor td on td.id=tdm.distributor_id and td.is_deleted=0
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=td.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=td.channel_subdivide
        WHERE o.is_deleted=0 AND s.is_deleted=0
        <choose>
            <when test="storeId != null">
                and s.id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and s.org_id=#{storeOrgId}
            </when>
            <when test="storeCode != null and storeCode != ''">
                and o.code=#{storeCode}
            </when>
            <otherwise>
                and s.id=0
            </otherwise>
        </choose>
    </select>




    <!-- 根据抖音门店编码查询门店信息 -->
    <select id="findStoreByTiktokCode" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,
        s.org_id orgId,
        s.plan_code,
        o.code,
        o.name,
        o.created_date,
        (
        case when s.open_type=1 and s.is_virtual!=1
        then (SELECT tsfn.door_size FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
        when s.open_type=2 and s.is_virtual!=1
        then (SELECT tsfc.door_size FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
        end
        ) as doorSize,
        s.key_word as keyWord,
        s.coverurl,
        s.address,
        s.provicen_id provicenId,
        s.city_id cityId,
        s.county_id countyId,
        s.longitude,
        s.latitude,
        s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,
        s.tel<!--czw加密已处理-->,
        s.leader_id leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        s.live_demo                             liveDemo,
        s.layout_code                           layoutCode,
        s.if_decoration_store,
        s.annual_rent,
        s.scene_suit,
        s.market_capacity,
        s.population,
        s.store_level,
        s.is_unmanned,
        s.sign_shape,
        s.sign_size,
        s.sign_cost,
        s.is_uniform,
        s.uniform_pic,
        s.top_kitchens,

        o.parent_id parentId,
        deptorg.full_path_name parentName,
        <!--(select p.full_path_name from orgcenter.t_org p where p.id=o.parent_id) as parentName,-->
        deptorg.code as departmentCode,
        deptorg.name as departmentName,
        (select ifnull(tmp.status,'0') from orgcenter.t_department tmp where tmp.org_id=deptorg.id limit 1) as departmentStatus,

        o.full_path_id fullPathId,
        o.full_path_name fullPathName,

        s.address2,
        s.company_id companyId,
        s.is_to_drp isToDrp,
        s.is_manager_clues_into_sea isManagerCluesIntoSea,
        s.to_drp_stage toDrpStage,
        s.store_type storeType,
        s.intro,
        s.consultation_pc,
        s.consultation_m,
        cc.`name` storeTypeName,
        cc.code storeTypeCode,
        IF(s.abbreviation IS NULL OR TRIM(s.abbreviation) = '', o.name, s.abbreviation) abbreviation,
        s.app_pre_audit preAudit,
        c.cut_time cutTime,
        s.provicen_name provicenName,
        s.city_name cityName,
        s.county_name countyName,
        s.is_transfer isTransfer,
        s.is_to_check isToCheck,
        s.decorate_status decorateStatus,
        s.open_date openDate,
        s.close_date closeDate,
        s.close_start_date closeStartDate,
        s.terminal_check_date                    terminalCheckDate,
        s.terminal_check_date_again      terminalCheckDateAgain,
        s.store_market_grade storeMarketGrade,
        cc.id storeChannelId,
        cc.code storeChannelCode,
        cc.name as storeChannelName,
        cc.order_check_channel as orderCheckChannel,
        s.distributor_channel distributorChannel,
        s.related_store_id relatedStoreId,
        s.is_virtual isVirtual,
        s.need_terminal_build needTerminalBuild,
        s.manager_name managerName,
        s.manager_phone managerPhone,
        s.store_biz_type storeBizType,
        s.staffs staffs,
        c.area,
        s.terminal_image_score,
        c.area_code areaCode,
        c.area_name areaName,
        s.property_right propertyRight,
        s.lease_term leaseTerm,
        s.bpm_type,
        s.usable_area,
        s.whole_usable_area,
        td.id as distributorId,
        td.code as distributorCode,
        td.name as distributorName,
        td.receipt_type as receiptType,
        cc1.code as distributorChannelCode,
        s.open_type,
        (select `name` from orgcenter.t_org cpy where cpy.is_deleted=0 and cpy.type=1 and cpy.id=s.company_id) as
        companyName,
        (select `finance_audit_flag` from orgcenter.t_company cpy1 where cpy1.is_deleted=0 and cpy1.org_id=s.company_id) as
        financeAuditFlag,
        s.estimated_annual_sales,
        s.clues_msg_user cluesMsgUser,
        s.wechat,
        s.shopping_remark,
        s.tel_remark<!--czw加密已处理4-->,
        s.email,
        s.email_remark,
        s.wechat_remark<!--czw加密已处理4-->,
        s.alipay_remark,
        s.channel,
        s.develop_salesman_id developSalesmanId,
        s.develop_salesman_code developSalesmanCode,
        s.develop_salesman_name developSalesmanName,
        s.develop_salesman_phone developSalesmanPhone,
        c.id as companyPkId,
        s.store_sub_channel_code,
        s.outside_store_name,
        s.status,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,
        cc1.id as channelCategoryId,
        cc1.code as channelCategoryCode,
        cc1.name as channelCategoryName,
        cc2.id as channelSubdivideId,
        cc2.code as channelSubdivideCode,
        cc2.name as channelSubdivideName,
        s.eps_usable_area,
        s.is_open_kpoint,
        s.is_open_rent,
        s.stadiums_introduce,
        s.travel_tips,
        s.clues_follow cluesFollow,
        s.clues_lose cluesLose,
       <!-- (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )as `character`,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END

        ) as `character`,
        s.clues_overdue cluesOverdue,
        s.deposit_clues_overdue depositCluesOverdue,
        s.use_pos usePos,s.is_scene_flag isSceneFlag,
        s.`version`,
        s.is_cashier_enabled as isCashierEnabled,
        s.merchant_code as merchantCode,
        s.merchant_name as merchantName,
        s.merchant_code_online as merchantCodeOnline,
        s.cashier_charge_mapping as cashierChargeMapping,
        s.open_wy_channel,
        s.tiktok_name,
        s.tiktok_code,
        s.open_zq_channel,
        s.open_rq_channel,
        s.iz_im_pay_activity_enabled as izImPayActivityEnabled,
        s.old_compensate_merchant_code as oldCompensateMerchantCode,
        s.clues_check_phone cluesCheckPhone,
        s.clues_check_period cluesCheckPeriod,
        s.clues_check_scope cluesCheckScope,
        s.clues_follow_status cluesFollowStatus,
        s.clues_process_result cluesProcessResult

        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        LEFT JOIN orgcenter.t_org deptorg ON deptorg.id=o.parent_id and deptorg.is_deleted=0
        LEFT JOIN orgcenter.channel_category cc ON s.store_type = cc.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type=3 and tdm.is_deleted=0 and tdm.org_id=s.org_id
        LEFT JOIN orgcenter.t_distributor td on td.id=tdm.distributor_id and td.is_deleted=0
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=td.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=td.channel_subdivide
        WHERE o.is_deleted=0 AND s.is_deleted=0
        <choose>
            <when test="tiktokCode != null and tiktokCode != ''">
                and s.tiktok_code=#{tiktokCode}
            </when>
            <when test="storeCode != null and storeCode != ''">
                and o.code=#{storeCode}
            </when>
            <otherwise>
                and s.id=0
            </otherwise>
        </choose>
        order by s.created_date desc
        limit 1
    </select>



    <!-- 根据门店orgid查询门店信息 -->
    <select id="findCodeByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.org_id orgId,
        o.code,
        o.name,
        s.store_type storeType,
        s.key_word keyWord
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        WHERE o.is_deleted=0 AND s.is_deleted=0
        <choose>
            <when test="storeId != null">
                and s.id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and s.org_id=#{storeOrgId}
            </when>
            <when test="storeCode != null and storeCode != ''">
                and o.code=#{storeCode}
            </when>
            <otherwise>
                and s.id=0
            </otherwise>
        </choose>
    </select>

    <!-- 根据orgid查询门店基础信息 -->
    <select id="findBasicStoreInfoByOrgId" resultType="com.fotile.orgcenter.store.pojo.dto.FindBasicStoreInfoByOrgIdOutDto">
        SELECT
                st.id,
                st.org_id as storeOrgId,
                o.code as storeCode,
                o.name as storeName,
                st.abbreviation as storeAbbre,
                st.company_id as companyId,
                oc.name as companyName,
                c.area_code as areaCode,
                c.area_name as areaName,
                d.id as distributorId,
                d.code as distributorCode,
                d.name as distributorName
        FROM
                orgcenter.t_org o
                INNER JOIN orgcenter.t_store<!--czw加密已处理--> st ON o.id = st.org_id AND st.is_deleted = 0
                LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND st.company_id = c.org_id
                LEFT JOIN orgcenter.t_org oc ON oc.is_deleted = 0 AND st.company_id = oc.id
                LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND st.org_id=dm.org_id AND dm.type=3
                LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        WHERE
                o.is_deleted = 0
          AND o.id = #{dto.orgId}
    </select>

    <!-- 根据门店orgids查询门店信息 -->
    <select id="findByOrgIds" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByOrgIdOutDto">
        SELECT s.id,s.org_id orgId,o.code,o.name,s.key_word keyWord,s.coverurl,s.address,s.provicen_id
            provicenId,s.city_id cityId,s.county_id countyId,o.longitude,o.latitude,
            s.shopping_start shoppingStart,s.status,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,s.leader_id
            leaderId,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,s.note,o.parent_id parentId,s.tel<!--czw加密已处理-->
            ,o.full_path_id fullPathId,o.full_path_name fullPathName,s.address2,company_id companyId,s.is_to_drp
            isToDrp,s.to_drp_stage toDrpStage,s.store_type storeType,
            s.intro,s.consultation_pc,s.consultation_m,s.store_channel_code storeChannelCode,
            s.abbreviation abbreviation,
            (SELECT cc.`name` FROM orgcenter.channel_category cc WHERE cc.is_deleted=0 AND s.store_type=cc.id )
            storeTypeName,
            (SELECT cc.code FROM orgcenter.channel_category cc WHERE cc.is_deleted=0 AND s.store_type=cc.id ) storeTypeCode,
            (SELECT name FROM orgcenter.t_org co WHERE co.is_deleted=0 AND co.id=s.company_id) companyName,
            td.id as distributorId,
            td.code as distributorCode,
            td.name as distributorName,
            s.is_virtual,s.store_biz_type storeBizType,
            s.merchant_code_online merchantCodeOnline,
            s.is_cashier_enabled isCashierEnabled,
            s.merchant_name merchantName,
        s.tiktok_code,
        s.tiktok_name,
        s.develop_salesman_id developSalesmanId,
        s.develop_salesman_code developSalesmanCode,
        s.develop_salesman_name developSalesmanName,
        s.develop_salesman_phone developSalesmanPhone,
        s.merchant_code merchantCode,
        s.iz_im_pay_activity_enabled as izImPayActivityEnabled,
        s.old_compensate_merchant_code as oldCompensateMerchantCode,
        s.store_sub_channel_code storeSubChannelCode
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type=3 and tdm.is_deleted=0 and tdm.org_id=o.id
        LEFT JOIN orgcenter.t_distributor td on td.id=tdm.distributor_id and td.is_deleted=0
        WHERE
        o.is_deleted=0 AND s.is_deleted=0
        <if test="orgIds != null and orgIds.size() != 0">
            and s.org_id in
            <foreach collection="orgIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="isVirtual != null">
            and s.is_virtual=#{isVirtual}
        </if>
        <if test="name != null and name != ''">
            and  o.name like concat('%',#{name},'%')
        </if>
        ORDER BY o.code ASC
    </select>

    <select id="findStoreNameListByOrgIds" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreNameListByOrgIdsOutDto">
        SELECT
            o.id as orgId,
            o.code,
            o.name,
            st.id as storeId
        FROM orgcenter.t_org o
        inner join orgcenter.t_store st on st.is_deleted = 0 and o.id = st.org_id
        WHERE
            o.is_deleted=0 AND o.type=3
        <if test="dto.orgIds != null and dto.orgIds.size() != 0">
            and o.id in
            <foreach collection="dto.orgIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 启用/禁用 -->
    <update id="enable">
        UPDATE orgcenter.t_store<!--czw加密已处理版本-->
        SET status=#{status},modified_by=#{modifiedBy},modified_date=now(),`version`=`version`+1
        WHERE id IN
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <!-- 查找附近门店 -->
    <sql id="findNearestStoreWHERE">
        SELECT s.id,s.org_id orgId,IF(IFNULL(s.address2,'')='',s.address,s.address2)
        address,s.tel<!--czw加密已处理-->,o.longitude,o.latitude,(6371*ACOS(COS(RADIANS(#{latitude}))
        *COS(RADIANS(o.latitude))
        *COS(RADIANS(o.longitude)-RADIANS(#{longitude}))
        +SIN(RADIANS(#{latitude}))
        *SIN(RADIANS(o.latitude))
        )) AS distance
        ,o.name
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        <where>
            s.is_deleted=0 AND o.is_deleted=0 AND s.status=1
            <if test="cityId !=null">
                AND s.city_id=#{cityId}
            </if>
        </where>
    </sql>
    <select id="findNearestStoreTotal" resultType="int">
        SELECT COUNT(st.id)
        FROM (
        <include refid="findNearestStoreWHERE"/>
        ) st
        WHERE st.distance IS NOT NULL
    </select>
    <select id="findNearestStore" resultType="com.fotile.orgcenter.store.pojo.dto.FindNearestStoreOutDto">
        SELECT st.distance,st.id,st.name,st.longitude,st.latitude,st.orgId,st.address
        FROM (
        <include refid="findNearestStoreWHERE"/>
        ) st
        WHERE st.distance IS NOT NULL
        ORDER BY distance
        limit #{pg.offset},#{pg.size}
    </select>


    <!-- 根据名称查询门店 -->
    <sql id="findStoreByNameWHERE">
        SELECT s.id,
               s.org_id                                               orgId,
               o.code,
               o.name,
               s.key_word                                             keyWord,
               s.coverurl,
               IF(IFNULL(s.address2, '') = '', s.address, s.address2) address,
               s.provicen_id                                          provicenId,
               s.city_id                                              cityId,
               s.county_id                                            countyId,
               s.shopping_start                                       shoppingStart,
               s.shopping_end                                         shoppingEnd,
               s.tel<!--czw加密已处理-->,
               s.leader_id                                            leaderId,
               s.alipayno,
               s.alipaynourl,
               s.wechatno,
               s.wechatnourl,
               s.abbreviation,
               s.intro,
               s.consultation_pc,
               s.consultation_m,
               s.note,
               o.parent_id                                            parentId,
               o.full_path_id                                         fullPathId,
               o.full_path_name                                       fullPathName,
               o.longitude,
               o.latitude,
               (6371 * ACOS(COS(RADIANS(#{latitude}))
                                * COS(RADIANS(o.latitude))
                                * COS(RADIANS(o.longitude) - RADIANS(#{longitude}))
                   + SIN(RADIANS(#{latitude}))
                                * SIN(RADIANS(o.latitude))
                   )) AS                                              distance
        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        WHERE o.name LIKE CONCAT('%',#{name},'%')
          AND o.is_deleted = 0
          AND s.is_deleted = 0
          AND s.status = 1
    </sql>
    <select id="findStoreByNameTotal" resultType="int">
        SELECT COUNT(st.id)
        FROM(
        <include refid="findStoreByNameWHERE"/>
        ) st
    </select>
    <select id="findStoreByName" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByNameOutDto">
        SELECT *
        FROM(
        <include refid="findStoreByNameWHERE"/>
        ) st
        ORDER BY distance
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 分页查询 -->
    <sql id="findPageAll2WHERE">
        <where>
            s.is_deleted =0
            <if test="store.isCashierEnabled != null">
                and s.is_cashier_enabled = #{store.isCashierEnabled}
            </if>
            <if test="store.keyWord !=null and store.keyWord != ''">
                AND
                <foreach collection="store.keyWord.split(',')" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="store.code !=null and store.code !=''"><!-- 部门编码 -->
                AND o.code LIKE CONCAT('%',#{store.code},'%')
            </if>
            <if test="store.name !=null and store.name !=''"><!-- 门店名称，简称的模糊查找 -->
                AND (o.name LIKE CONCAT('%',#{store.name},'%') OR s.abbreviation LIKE CONCAT('%',#{store.name},'%'))
            </if>
            <if test="store.nameOrCode !=null and store.nameOrCode !=''"><!-- 部门名称 -->
                AND (o.name LIKE CONCAT('%',#{store.nameOrCode},'%') OR o.code LIKE CONCAT('%',#{store.nameOrCode},'%') OR s.abbreviation LIKE CONCAT('%',#{store.nameOrCode},'%'))
            </if>
            <if test="store.codes !=null and store.codes.size() > 0 "><!-- 门店编码精确查找 -->
                AND o.code in
                <foreach collection="store.codes" item="codeItem" open="(" close=")" separator=",">
                    #{codeItem}
                </foreach>
            </if>
            <if test="store.realKeyWordStoreIdList !=null and store.realKeyWordStoreIdList.size>0">
                AND s.id IN
                <foreach collection="store.realKeyWordStoreIdList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="subDepList !=null and subDepList.size>0"><!-- 所属部门 -->
                AND o.parent_id IN
                <foreach collection="subDepList" item="depOrgId" open="(" separator="," close=")">
                    #{depOrgId}
                </foreach>
            </if>
            <if test="store.companyId !=null "><!-- 所属公司 -->
                AND s.company_id=#{store.companyId}
            </if>

            <if test="store.companyIds !=null and store.companyIds !=''">
                AND s.company_id IN
                <foreach collection="store.companyIds.split(',')" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="store.notInCompanyIds !=null and store.notInCompanyIds.size()>0">
                AND s.company_id not IN
                <foreach collection="store.notInCompanyIds" separator="," open="(" close=")" item="cid">
                    #{cid}
                </foreach>
            </if>

            <if test="store.leaderId !=null "><!-- 店长 -->
                AND s.leader_id=#{store.leaderId}
            </if>
            <choose>
                <when test="store.statusSet!=null and store.statusSet.size()>0">
                    AND s.status in
                    <foreach collection="store.statusSet" separator="," open="(" close=")" item="status">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    <if test="store.status !=null and store.status !=-1"><!-- 状态 -->
                        <choose>
                            <when test="store.status == 1">
                                AND s.status in (1,2)
                            </when>
                            <otherwise>
                                AND s.status=#{store.status}
                            </otherwise>
                        </choose>

                    </if>
                </otherwise>
            </choose>

            <if test="store.storeType !=null "><!-- 门店类型，1：专卖店；2：KA店；3：社区店 -->
                AND s.store_type=#{store.storeType}
            </if>
            <if test="store.channelCode !=null and store.channelCode !=''"><!-- 展示渠道 -->
                AND
                <foreach collection="store.channelCode.split(',')" item="channelCode" separator="or" open="(" close=")">
                    FIND_IN_SET(#{channelCode},s.channel)
                </foreach>
            </if>

            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>

            <if test="store !=null and store.channelCategory !=null">
                AND d.channel_category=#{store.channelCategory}
            </if>

            <if test="store !=null and store.channelSubdivide !=null">
                AND d.channel_subdivide=#{store.channelSubdivide}
            </if>
            <if test="store !=null and store.area !=null">
                AND c.area=#{store.area}
            </if>

            <if test="store.areas !=null and store.areas !=''">
                AND c.area IN
                <foreach collection="store.areas.split(',')" item="regionId" open="(" separator="," close=")">
                    #{regionId}
                </foreach>
            </if>

            <if test="store.distributorId !=null">
                AND d.id=#{store.distributorId}
            </if>

            <if test="store.distributorIds !=null and store.distributorIds !=''">
                and d.id in
                <foreach collection="store.distributorIds.split(',')" item="distributorId" open="(" separator="," close=")">
                    #{distributorId}
                </foreach>
            </if>

            <if test="store.developSalesmanId !=null">
            <choose>
                <when test="store.developSalesmanId==-1">
                    AND s.develop_salesman_id IS NULL
                </when>
                <otherwise>
                    AND s.develop_salesman_id=#{store.developSalesmanId}
                </otherwise>
            </choose>

            </if>
            <if test="store.createdDateStart !=null and store.createdDateStart !=''">
                AND s.created_date &gt;= DATE_FORMAT(#{store.createdDateStart},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="store.createdDateEnd !=null and store.createdDateEnd !=''">
                AND s.created_date &lt;= DATE_FORMAT(#{store.createdDateEnd},'%Y-%m-%d %H:%i:%s')
            </if>

            <if test="store.terminalCheckDateStart !=null">
                AND s.terminal_check_date &gt;= #{store.terminalCheckDateStart}
            </if>
            <if test="store.terminalCheckDateEnd !=null">
                AND s.terminal_check_date &lt;= #{store.terminalCheckDateEnd}
            </if>
            <if test="store.terminalCheckDateAgainStart !=null">
                AND s.terminal_check_date_again &gt;= #{store.terminalCheckDateAgainStart}
            </if>
            <if test="store.terminalCheckDateAgainEnd !=null">
                AND s.terminal_check_date_again &lt;= #{store.terminalCheckDateAgainEnd}
            </if>

            <!--added by zd, 2021/02/26 新增查询条件-->
            <if test="store.storeKeyword != null and store.storeKeyword != ''">
                AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%') OR o.code
                LIKE CONCAT('%',#{store.storeKeyword},'%')
                <!--  OR cast(s.id as char)=#{store.storeKeyword}  -->
                )
            </if>
            <if test="store.decorateStatus != null">
                AND s.decorate_status=#{store.decorateStatus}
            </if>
            <if test="store.developSalesman != null and store.developSalesman != ''">
                AND (s.develop_salesman_name = #{store.developSalesman, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                or s.develop_salesman_phone = #{store.developSalesman, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                or s.develop_salesman_code like CONCAT('%',#{store.developSalesman},'%'))
            </if>
            <if test="store.isToDrp != null and store.isToDrp != ''">
                AND s.is_to_drp=#{store.isToDrp}
            </if>
            <if test="store.toDrpStage != null and store.toDrpStage != ''">
                AND s.to_drp_stage=#{store.toDrpStage}
            </if>
            <if test="store.preAudit != null">
                AND s.app_pre_audit=#{store.preAudit}
            </if>
            <if test="store.openStartTime != null">
                AND s.open_date >= #{store.openStartTime}
            </if>
            <if test="store.openEndTime != null">
                AND s.open_date &lt;= #{store.openEndTime}
            </if>
            <if test="store.closeStartTime != null">
                AND s.close_date >= #{store.closeStartTime}
            </if>
            <if test="store.closeEndTime != null">
                AND s.close_date &lt;= #{store.closeEndTime}
            </if>
            <if test="store.createdStartTime != null">
                AND s.created_date >= #{store.createdStartTime}
            </if>
            <if test="store.createdEndTime != null">
                AND s.created_date &lt;= #{store.createdEndTime}
            </if>
            <if test="store.wholeUsableAreaBegin != null">
                AND s.whole_usable_area >= #{store.wholeUsableAreaBegin}
            </if>
            <if test="store.wholeUsableAreaEnd != null">
                AND s.whole_usable_area &lt;= #{store.wholeUsableAreaEnd}
            </if>


            <if test="store.statusMulti != null and store.statusMulti != ''">
                AND s.status in
                <foreach collection="store.statusMulti.split(',')" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="store.storeId != null">
                and s.id=#{store.storeId}
            </if>

            <!--added by zd,2021/04/19 support ids search-->
            <if test="store.orgIds != null and store.orgIds.size() != 0">
                and o.id in
                <foreach collection="store.orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>

            <if test="store.ids != null and store.ids.size() != 0">
                and s.id in
                <foreach collection="store.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!--added by zd,2021/05/14 迭代0526 issue:8788-->
            <!--门店渠道支持多选-->
            <if test="store.storeChannelCode != null and store.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="store.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="store.channelCategoryIds != null and store.channelCategoryIds != '' and store.channelSubdivideIds != null and store.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="store.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="store.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="store.channelCategoryIds != null and store.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="store.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="store.channelSubdivideIds != null and store.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="store.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <choose>
                <when test="store.channelCategoryCode != null and store.channelCategoryCode != '' and store.channelSubdivideCode != null and store.channelSubdivideCode != ''">
                    and (
                    cc1.code in
                    <foreach collection="store.channelCategoryCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                    or cc2.code in
                    <foreach collection="store.channelSubdivideCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                    )
                </when>
                <when test="store.channelCategoryCode != null and store.channelCategoryCode != ''">
                    AND cc1.code in
                    <foreach collection="store.channelCategoryCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                </when>
                <when test="store.channelSubdivideCode != null and store.channelSubdivideCode != ''">
                    AND cc2.code in
                    <foreach collection="store.channelSubdivideCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                </when>
            </choose>
            <if test="store.openModelList != null and store.openModelList.size() != 0">
                <if test="store.openModelList.size() == 1 and store.openModelList.contains(1)">
                    and s.is_cashier_enabled = 1
                </if>
                <if test="store.openModelList.size() == 1 and store.openModelList.contains(2)">
                    and s.iz_im_pay_activity_enabled = 1
                </if>
                <if test="store.openModelList.size() == 2 and store.openModelList.contains(1) and store.openModelList.contains(2)">
                    and (s.is_cashier_enabled = 1 or s.iz_im_pay_activity_enabled = 1)
                </if>
            </if>

            <if test="store.isVirtual != null">
                and s.is_virtual=#{store.isVirtual}
            </if>
            <if test="store.emptyPlanCode!=null and store.emptyPlanCode ">
                and s.plan_code is null
            </if>
            <if test="store.needTerminalBuild != null">
                and s.need_terminal_build=#{store.needTerminalBuild}
            </if>
            <if test="store.provicenId != null">
                and s.provicen_id=#{store.provicenId}
            </if>
            <if test="store.cityId != null">
                and s.city_id=#{store.cityId}
            </if>
            <if test="store.countyId != null">
                and s.county_id=#{store.countyId}
            </if>
            <if test="store.address != null and store.address != ''">
                and s.address like concat('%',#{store.address},'%')
            </if>
            <if test="store.parentId != null">
                and (o.full_path_id LIKE CONCAT('',#{store.parentId},'-%') OR o.full_path_id LIKE CONCAT('%-',#{store.parentId},'-%'))
                <!--                and (o.parent_id=#{store.parentId} OR o.full_path_id LIKE CONCAT('',#{store.parentId},'-%'))-->
            </if>
            <if test="store.depOrgIds != null and store.depOrgIds!=''">
                AND
                <foreach collection="store.depOrgIds.split(',')" item="item" open="(" separator="OR" close=")">
                    o.full_path_id LIKE CONCAT('',#{item},'-%') OR o.full_path_id LIKE CONCAT('%-',#{item},'-%')
                </foreach>
            </if>


            <if test="store.usableAreaLower != null">
                and s.usable_area &gt;= #{store.usableAreaLower}
            </if>
            <if test="store.usableAreaUpper != null">
                and s.usable_area &lt;= #{store.usableAreaUpper}
            </if>

            <if test="store.storeLevels != null and store.storeLevels.size()>0 ">
            <choose>
                <when test='store.storeLevels.contains("-1")'>
                    and (
                    s.store_level in
                    <foreach collection="store.storeLevels" item="level" open="(" close=")" separator=",">
                        #{level}
                    </foreach>
                    or (s.store_level is null or s.store_level='')
                    )
                </when>
                <otherwise>
                    and s.store_level in
                    <foreach collection="store.storeLevels" item="level" open="(" close=")" separator=",">
                        #{level}
                    </foreach>
                </otherwise>
            </choose>
            </if>

            <if test="store.floorSet != null and store.floorSet.size()>0">
                AND (
                CASE   cc3.`code`
                WHEN 'L028' THEN
                EXISTS( SELECT tsfctemp.id FROM t_store_facilities_community tsfctemp WHERE tsfctemp.is_deleted=0 AND tsfctemp.store_id=s.id AND tsfctemp.floors IN
                <foreach collection="store.floorSet" open="(" close=")" separator="," item="floor">
                    #{floor}
                </foreach>
                )
                ELSE
                EXISTS( SELECT tsfntemp.id FROM t_store_facilities_noncommunity tsfntemp WHERE tsfntemp.is_deleted=0 AND tsfntemp.store_id=s.id AND tsfntemp.floors IN
                <foreach collection="store.floorSet" open="(" close=")" separator="," item="floor">
                    #{floor}
                </foreach>
                )
                END )

            </if>
            <if test="store.isUnmanned != null">
                and s.is_unmanned = #{store.isUnmanned}
            </if>
            <if test="store.isUniform != null">
                and s.is_uniform = #{store.isUniform}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <sql id="findPageAll3WHERE">
        <where>
             s.is_deleted =0
            <if test="store.keyWord !=null and store.keyWord != ''">
                AND
                <foreach collection="store.keyWord.split(',')" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="store.code !=null and store.code !=''"><!-- 部门编码 -->
                AND o.code LIKE CONCAT('%',#{store.code},'%')
            </if>
            <if test="store.name !=null and store.name !=''"><!-- 部门名称 -->
                AND o.name LIKE CONCAT('%',#{store.name},'%')
            </if>
            <if test="store.nameOrCode !=null and store.nameOrCode !=''"><!-- 部门名称 -->
                AND (o.name LIKE CONCAT('%',#{store.nameOrCode},'%')
                OR o.code LIKE CONCAT('%',#{store.nameOrCode},'%')
                OR s.abbreviation LIKE CONCAT('%',#{store.nameOrCode},'%')
                OR d.`name` like CONCAT('%',#{store.nameOrCode},'%')
                OR d.`code` like CONCAT('%',#{store.nameOrCode},'%') )
            </if>
            <if test="subDepList !=null and subDepList.size>0"><!-- 所属部门 -->
                AND o.parent_id IN
                <foreach collection="subDepList" item="depOrgId" open="(" separator="," close=")">
                    #{depOrgId}
                </foreach>
            </if>
            <if test="store.companyId !=null "><!-- 所属公司 -->
                AND s.company_id=#{store.companyId}
            </if>

            <if test="store.companyIds !=null and store.companyIds !=''">
                AND s.company_id IN
                <foreach collection="store.companyIds.split(',')" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>

            <if test="store.leaderId !=null "><!-- 店长 -->
                AND s.leader_id=#{store.leaderId}
            </if>
            <choose>
                <when test="store.statusSet!=null and store.statusSet.size()>0">
                    AND s.status in
                    <foreach collection="store.statusSet" separator="," open="(" close=")" item="status">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    <if test="store.status !=null and store.status !=-1"><!-- 状态 -->
                        <choose>
                            <when test="store.status == 1">
                                AND s.status in (1,2)
                            </when>
                            <otherwise>
                                AND s.status=#{store.status}
                            </otherwise>
                        </choose>

                    </if>
                </otherwise>
            </choose>

            <if test="store.storeType !=null "><!-- 门店类型，1：专卖店；2：KA店；3：社区店 -->
                AND s.store_type=#{store.storeType}
            </if>
            <if test="store.channelCode !=null and store.channelCode !=''"><!-- 展示渠道 -->
                AND
                <foreach collection="store.channelCode.split(',')" item="channelCode" separator="or" open="(" close=")">
                    FIND_IN_SET(#{channelCode},s.channel)
                </foreach>
            </if>

            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>

            <if test="store !=null and store.channelCategory !=null">
                AND d.channel_category=#{store.channelCategory}
            </if>

            <if test="store !=null and store.channelSubdivide !=null">
                AND d.channel_subdivide=#{store.channelSubdivide}
            </if>
            <if test="store !=null and store.area !=null">
                AND c.area=#{store.area}
            </if>

            <if test="store.areas !=null and store.areas !=''">
                AND c.area IN
                <foreach collection="store.areas.split(',')" item="regionId" open="(" separator="," close=")">
                    #{regionId}
                </foreach>
            </if>

            <if test="store.distributorId !=null">
                AND d.id=#{store.distributorId}
            </if>
            <if test="store.developSalesmanId !=null">
                AND s.develop_salesman_id=#{store.developSalesmanId}
            </if>
            <if test="store.createdDateStart !=null and store.createdDateStart !=''">
                AND s.created_date &gt;= DATE_FORMAT(#{store.createdDateStart},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="store.createdDateEnd !=null and store.createdDateEnd !=''">
                AND s.created_date &lt;= DATE_FORMAT(#{store.createdDateEnd},'%Y-%m-%d %H:%i:%s')
            </if>

            <!--added by zd, 2021/02/26 新增查询条件-->
            <if test="store.storeKeyword != null and store.storeKeyword != ''">
                AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%') OR o.code
                LIKE CONCAT('%',#{store.storeKeyword},'%') OR cast(s.id as char)=#{store.storeKeyword})
            </if>
            <if test="store.decorateStatus != null">
                AND s.decorate_status=#{store.decorateStatus}
            </if>
            <if test="store.developSalesman != null and store.developSalesman != ''">
                AND (s.develop_salesman_name = #{store.developSalesman, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                or s.develop_salesman_phone = #{store.developSalesman, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                or s.develop_salesman_code like CONCAT('%',#{store.developSalesman},'%'))
            </if>
            <if test="store.isToDrp != null and store.isToDrp != ''">
                AND s.is_to_drp=#{store.isToDrp}
            </if>
            <if test="store.toDrpStage != null and store.toDrpStage != ''">
                AND s.to_drp_stage=#{store.toDrpStage}
            </if>
            <if test="store.preAudit != null">
                AND s.app_pre_audit=#{store.preAudit}
            </if>
            <if test="store.openStartTime != null">
                AND s.open_date >= #{store.openStartTime}
            </if>
            <if test="store.openEndTime != null">
                AND s.open_date &lt;= #{store.openEndTime}
            </if>
            <if test="store.closeStartTime != null">
                AND s.close_date >= #{store.closeStartTime}
            </if>
            <if test="store.closeEndTime != null">
                AND s.close_date &lt;= #{store.closeEndTime}
            </if>
            <if test="store.createdStartTime != null">
                AND s.created_date >= #{store.createdStartTime}
            </if>
            <if test="store.createdEndTime != null">
                AND s.created_date &lt;= #{store.createdEndTime}
            </if>
            <if test="store.statusMulti != null and store.statusMulti != ''">
                AND s.status in
                <foreach collection="store.statusMulti.split(',')" item="status" separator="," open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="store.storeId != null">
                and s.id=#{store.storeId}
            </if>

            <!--added by zd,2021/04/19 support ids search-->
            <if test="store.orgIds != null and store.orgIds.size() != 0">
                and o.id in
                <foreach collection="store.orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>

            <if test="store.ids != null and store.ids.size() != 0">
                and s.id in
                <foreach collection="store.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!--added by zd,2021/05/14 迭代0526 issue:8788-->
            <!--门店渠道支持多选-->
            <if test="store.storeChannelCode != null and store.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="store.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="store.channelCategoryIds != null and store.channelCategoryIds != '' and store.channelSubdivideIds != null and store.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="store.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="store.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="store.channelCategoryIds != null and store.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="store.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="store.channelSubdivideIds != null and store.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="store.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <choose>
                <when test="store.channelCategoryCode != null and store.channelCategoryCode != '' and store.channelSubdivideCode != null and store.channelSubdivideCode != ''">
                    and (
                    cc1.code in
                    <foreach collection="store.channelCategoryCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                    or cc2.code in
                    <foreach collection="store.channelSubdivideCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                    )
                </when>
                <when test="store.channelCategoryCode != null and store.channelCategoryCode != ''">
                    AND cc1.code in
                    <foreach collection="store.channelCategoryCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                </when>
                <when test="store.channelSubdivideCode != null and store.channelSubdivideCode != ''">
                    AND cc2.code in
                    <foreach collection="store.channelSubdivideCode.split(',')" item="code" open="(" separator=","
                             close=")">
                        #{code}
                    </foreach>
                </when>
            </choose>


            <if test="store.isVirtual != null">
                and s.is_virtual=#{store.isVirtual}
            </if>
            <if test="store.emptyPlanCode!=null and store.emptyPlanCode ">
                and s.plan_code is null
            </if>
            <if test="store.needTerminalBuild != null">
                and s.need_terminal_build=#{store.needTerminalBuild}
            </if>
            <if test="store.provicenId != null">
                and s.provicen_id=#{store.provicenId}
            </if>
            <if test="store.cityId != null">
                and s.city_id=#{store.cityId}
            </if>
            <if test="store.countyId != null">
                and s.county_id=#{store.countyId}
            </if>
            <if test="store.address != null and store.address != ''">
                and s.address like concat('%',#{store.address},'%')
            </if>
            <if test="store.parentId != null">
                and (o.parent_id=#{store.parentId} OR o.full_path_id LIKE CONCAT('',#{store.parentId},'-%'))
            </if>


            <if test="store.usableAreaLower != null">
                and s.usable_area &gt;= #{store.usableAreaLower}
            </if>
            <if test="store.usableAreaUpper != null">
                and s.usable_area &lt;= #{store.usableAreaUpper}
            </if>

            <if test="store.storeLevels != null and store.storeLevels.size()>0 ">
                <choose>
                    <when test='store.storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="store.storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="store.storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>

            <if test="store.floorSet != null and store.floorSet.size()>0">
                and tsfn.floors in
                <foreach collection="store.floorSet" open="(" close=")" separator="," item="floor">
                    #{floor}
                </foreach>
            </if>
            <if test="store.isUnmanned != null">
                and s.is_unmanned = #{store.isUnmanned}
            </if>
            <if test="store.isUniform != null">
                and s.is_uniform = #{store.isUniform}
            </if>
        </where>
    </sql>

    <select id="findPageAll2Total" resultType="int">
        SELECT COUNT(s.id)
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id

        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND s.org_id=dm.org_id AND dm.type=3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=d.channel_subdivide
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id=s.store_type
        <if test="store.floorSet != null and store.floorSet.size()>0">
            LEFT JOIN orgcenter.t_store_facilities_noncommunity tsfn on s.id=tsfn.store_id
        </if>

        <include refid="findPageAll2WHERE"/>
    </select>

    <select id="findPageAll2" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,s.org_id orgId,s.key_word keyWord,o.code,o.name,p.name parentName,p.id parentId,p.code as parentCode,
        '' AS leaderName,s.provicen_id provicenId,s.provicen_name provicenName,s.city_id cityId,s.city_name cityName,
        s.county_id countyId,s.county_name countyName,s.address address,s.leader_id leaderId,
               s.terminal_check_date,s.terminal_check_date_again,
               s.note,s.status,s.coverurl,s.shopping_start shoppingStart,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,
        s.company_id companyId, s.terminal_check_date  terminalCheckDate, s.terminal_check_date_again terminalCheckDateAgain,
        s.usable_area usableArea,s.eps_usable_area epsUsableArea,s.whole_usable_area wholeUsableArea, s.store_level storeLevel,
        s.is_unmanned isUnmanned,s.is_uniform isUniform,s.is_virtual as isVirtual,
        <if test="store.floorSet != null and store.floorSet.size()>0">
            tsfn.floors,
        </if>
        (SELECT name from t_org c WHERE s.company_id=c.id) as companyName,
        (SELECT code from t_org c WHERE s.company_id=c.id) as companyCode,
        o.longitude,o.latitude,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,
        o.full_path_id fullPathId,o.full_path_name fullPathName,s.address2,s.is_open_kpoint isOpenKpoint,
        s.store_type storeType,s.channel channelId,
        (SELECT GROUP_CONCAT(name) FROM orgcenter.t_channel WHERE FIND_IN_SET(code,s.channel)) channelName,
        (select cc.name from orgcenter.channel_category cc WHERE s.store_type=cc.id) storeTypeName,s.abbreviation,
        (select tsfnn2.character from orgcenter.t_store_facilities_noncommunity tsfnn2 where  s.id=tsfnn2.store_id and tsfnn2.is_deleted=0) `character`,
        d.name as distributorName,
        d.code as distributorCode,
        case s.is_to_drp
        when '1' then '不同步'
        when '2' then '同步至DMS/总部DRP/DCS'
        WHEN '3' then '同步至华东DRP/DCS'
        when '4' then '同步至广东DRP/DCS'
        when '5' then '同步至DMS/总部DRP/华东DRP/DCS'
        when '6' then '同步至DMS/广东DRP/DCS'
        end `isToDrp`,
        c.area,
        s.develop_salesman_name developSalesmanName,
               s.open_date openDate,
        cc2.name as channelSubdivides,s.decorate_status,s.created_date,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,
        d.id distributorId,
        cc1.code channelCategoryCode,
        cc1.name channelCategoryName,
        cc3.code storeTypeCode,
        cc3.name storeTypeName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id

        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND s.org_id=dm.org_id AND dm.type=3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=d.channel_subdivide
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id=s.store_type
        <if test="store.floorSet != null and store.floorSet.size()>0">
            LEFT JOIN orgcenter.t_store_facilities_noncommunity tsfn on s.id=tsfn.store_id
        </if>
        <include refid="findPageAll2WHERE"/>
        ORDER BY
        <if test="store !=null and store.orderByCode !=null and store.orderByCode !=0">
            o.code ASC,
        </if>
        s.created_date DESC
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <select id="findPageAll22" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,s.org_id orgId,o.code,o.name,s.status
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        <include refid="findPageAll2WHERE"/>
        ORDER BY
        <if test="store !=null and store.orderByCode !=null and store.orderByCode !=0">
            o.code ASC,
        </if>
        s.created_date DESC
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!-- 小程序根据门店id查询门店信息 -->
    <select id="findStoreInfoById" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id, s.org_id orgId,o.code,o.name,s.key_word
        keyWord,s.coverurl,s.intro,s.consultation_pc,s.consultation_m, <!-- CONCAT(s.provicen_name,s.city_name,s.county_name, IFNULL(s.address2,s.address)) address -->
        IF(IFNULL(s.address2,'')='',s.address,s.address2) address,s.provicen_id provicenId,s.city_id cityId,s.county_id
        countyId,o.longitude,o.latitude,
        s.shopping_start shoppingStart,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,s.leader_id
        leaderId,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,s.note,o.parent_id parentId,s.tel<!--czw加密已处理-->
        ,o.full_path_id fullPathId,o.full_path_name fullPathName,s.address2,s.company_id,s.abbreviation,
        s.status status
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        WHERE s.id=#{id} AND o.is_deleted=0 AND s.is_deleted=0
    </select>


    <!-- 根据门店名称、省、市、区、地址联合查询门店 -->
    <sql id="findStoreByParamWHERE">
        SELECT s.id,
               s.org_id                                               orgId,
               o.code,
               o.name,
               s.key_word                                             keyWord,
               s.coverurl,
               IF(IFNULL(s.address2, '') = '', s.address, s.address2) address,
               s.provicen_id                                          provicenId,
               s.city_id                                              cityId,
               s.county_id                                            countyId,
               s.shopping_start                                       shoppingStart,
               s.shopping_end                                         shoppingEnd,
               s.tel<!--czw加密已处理-->,
               s.leader_id                                            leaderId,
               s.alipayno,
               s.alipaynourl,
               s.wechatno,
               s.wechatnourl,
               s.intro,
               s.consultation_pc,
               s.consultation_m,
               s.note,
               o.parent_id                                            parentId,
               o.full_path_id                                         fullPathId,
               o.full_path_name                                       fullPathName,
               o.longitude,
               o.latitude,
               (6371 * ACOS(COS(RADIANS(#{latitude}))
                                * COS(RADIANS(o.latitude))
                                * COS(RADIANS(o.longitude) - RADIANS(#{longitude}))
                   + SIN(RADIANS(#{latitude}))
                                * SIN(RADIANS(o.latitude))
                   )) AS                                              distance
        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        WHERE (o.name LIKE CONCAT('%',#{name},'%') OR s.address LIKE CONCAT('%',#{name},'%')  OR s.provicen_name LIKE CONCAT('%',#{name},'%')  OR
               s.city_name LIKE CONCAT('%',#{name},'%')  OR s.county_name LIKE CONCAT('%',#{name},'%') )
          AND o.is_deleted = 0
          AND s.is_deleted = 0
          AND s.status = 1
    </sql>
    <select id="findStoreByParamTotal" resultType="int">
        SELECT COUNT(st.id)
        FROM(
        <include refid="findStoreByParamWHERE"/>
        ) st
    </select>
    <select id="findStoreByParam" resultType="com.fotile.orgcenter.store.pojo.dto.FindNearestStoreOutDto">
        SELECT *
        FROM(
        <include refid="findStoreByParamWHERE"/>
        ) st
        ORDER BY distance
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 查找附近门店(只查询'方太厨房管家'渠道) -->
    <sql id="findNearestStore2WHERE">
        SELECT s.id,s.org_id orgId, IF(IFNULL(s.address2,'')='',s.address,s.address2)
        address,s.tel<!--czw加密已处理-->,o.longitude,o.latitude,(6371*ACOS(COS(RADIANS(#{latitude}))
        *COS(RADIANS(o.latitude))
        *COS(RADIANS(o.longitude)-RADIANS(#{longitude}))
        +SIN(RADIANS(#{latitude}))
        *SIN(RADIANS(o.latitude))
        )) AS distance
        ,o.name,s.channel
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        <where>
            s.is_deleted=0 AND o.is_deleted=0 AND s.status=1
            <if test="cityId !=null">
                AND s.city_id=#{cityId}
            </if>
        </where>
    </sql>
    <select id="findNearestStore2Total" resultType="int">
        SELECT COUNT(st.id)
        FROM (
        <include refid="findNearestStore2WHERE"/>
        ) st
        INNER JOIN orgcenter.t_channel c ON find_in_set(c.code,st.channel) AND c.code='s0001' <!-- c.`name`='方太厨房管家' -->
        WHERE st.distance IS NOT NULL
    </select>
    <select id="findNearestStore2" resultType="com.fotile.orgcenter.store.pojo.dto.FindNearestStoreOutDto">
        SELECT st.distance,st.id,st.name,st.longitude,st.latitude,st.orgId,st.address
        FROM (
        <include refid="findNearestStore2WHERE"/>
        ) st
        INNER JOIN orgcenter.t_channel c ON find_in_set(c.code,st.channel) AND c.code='s0001' <!-- c.`name`='方太厨房管家' -->
        WHERE st.distance IS NOT NULL
        ORDER BY distance
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 根据门店名称、省、市、区、地址联合查询门店(只查询'方太厨房管家'渠道) -->
    <sql id="findStoreByParam2WHERE">
        SELECT s.id,s.org_id orgId,o.code,o.name,s.key_word
        keyWord,s.coverurl,s.intro,s.consultation_pc,s.consultation_m, <!-- CONCAT(s.provicen_name,s.city_name,s.county_name, IFNULL(s.address2,s.address)) address -->
        IF(IFNULL(s.address2,'')='',s.address,s.address2) address,s.provicen_id provicenId,s.city_id cityId,s.county_id
        countyId,
        s.shopping_start shoppingStart,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,s.leader_id
        leaderId,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,
        s.note,o.parent_id parentId,o.full_path_id fullPathId,o.full_path_name fullPathName,
        o.longitude,o.latitude,
        (6371*ACOS(COS(RADIANS(#{latitude}))
        *COS(RADIANS(o.latitude))
        *COS(RADIANS(o.longitude)-RADIANS(#{longitude}))
        +SIN(RADIANS(#{latitude}))
        *SIN(RADIANS(o.latitude))
        )) AS distance,s.channel
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        WHERE (o.name LIKE CONCAT('%',#{name},'%') OR s.address LIKE CONCAT('%',#{name},'%') OR s.provicen_name LIKE CONCAT('%',#{name},'%')
        OR s.city_name LIKE CONCAT('%',#{name},'%') OR s.county_name LIKE CONCAT('%',#{name},'%'))
        AND o.is_deleted=0 AND s.is_deleted=0 AND s.status=1
    </sql>
    <select id="findStoreByParam2Total" resultType="int">
        SELECT COUNT(st.id)
        FROM(
        <include refid="findStoreByParam2WHERE"/>
        ) st
        INNER JOIN orgcenter.t_channel c ON find_in_set(c.code,st.channel) AND
        c.code='s0001' <!-- c.`name`='方太厨房管家'  -->
    </select>
    <select id="findStoreByParam2" resultType="com.fotile.orgcenter.store.pojo.dto.FindNearestStoreOutDto">
        SELECT *
        FROM(
        <include refid="findStoreByParam2WHERE"/>
        ) st
        INNER JOIN orgcenter.t_channel c ON find_in_set(c.code,st.channel) AND c.code='s0001' <!-- c.`name`='方太厨房管家' -->
        ORDER BY distance
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 查询门店数据权限内的门店分页查询 -->
    <sql id="findPageAllWithStoreAuthorWHERE">

            o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
            <if test="store !=null">
                <if test="store.code !=null and store.code !=''"><!-- 部门编码 -->
                    AND o.code LIKE CONCAT('%',#{store.code},'%')
                </if>
                <if test="store.name !=null and store.name !=''"><!-- 部门名称 -->
                    AND o.name LIKE CONCAT('%',#{store.name},'%')
                </if>
                <if test="store.parentId !=null "><!-- 所属部门 -->
                    AND o.parent_id=#{store.parentId}
                </if>
                <if test="store.companyId !=null "><!-- 所属公司 -->
                    AND s.company_id=#{store.companyId}
                </if>
                <if test="store.leaderId !=null "><!-- 店长 -->
                    AND s.leader_id=#{store.leaderId}
                </if>
                <if test="store.status !=null and store.status == 1"><!-- 2021/10/14，13796:1查询状态1启用门店 -->
                    AND s.status=#{store.status}
                </if>
                <if test="store.status !=null and store.status == 2"><!-- 2021/10/14，13796:2查询状态1,2启用筹备门店 -->
                    AND s.status in (1,2)
                </if>
                <if test="store.statusSet !=null and store.statusSet.size() > 0"><!-- 2021/10/14，13796:2查询状态1,2启用筹备门店 -->
                    AND s.status in
                    <foreach collection="store.statusSet" item="status" separator="," open="(" close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="store.companyIds !=null and store.companyIds !=''">
                    AND s.company_id IN
                    <foreach collection="store.companyIds.split(',')" item="orgId" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
                <if test="store.statusMulti != null and store.statusMulti != ''">
                    AND s.status in
                    <foreach collection="store.statusMulti.split(',')" item="status" separator="," open="(" close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="store.isVirtual != null">
                    and s.is_virtual=#{store.isVirtual}
                </if>
                <if test="store.storeOrgIdList !=null and store.storeOrgIdList.size() > 0">
                    AND s.org_id in
                    <foreach collection="store.storeOrgIdList" item="storeOrgId" separator="," open="(" close=")">
                        #{storeOrgId}
                    </foreach>
                </if>
            </if>




            <if test="storeAuthorList != null and storeAuthorList.size > 0">
                and s.id in (
                <foreach item="item" collection="storeAuthorList" separator=",">
                    #{item.storeId}
                </foreach>
                )
            </if>
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
    </sql>
    <select id="findPageAllWithStoreAuthorTotal" resultType="int">
        SELECT COUNT(s.id)
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id
        LEFT JOIN orgcenter.channel_category cc ON cc.is_deleted=0 AND s.store_type=cc.id
        <where>
            <include refid="findPageAllWithStoreAuthorWHERE"/>
            <if test="store != null ">
                <if test="store.storeKeyword != null and store.storeKeyword != ''">
                    <choose>
                        <when test="store.storeKeywordType == 2">
                            AND cc.name LIKE CONCAT('%',#{store.storeKeyword},'%')
                        </when>
                        <otherwise>
                            AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%') OR o.code
                            LIKE CONCAT('%',#{store.storeKeyword},'%') OR cast(s.id as char)=CONCAT('%',#{store.storeKeyword},'%'))
                        </otherwise>
                    </choose>
                </if>
            </if>
        </where>
    </select>
    <select id="findPageAllWithStoreAuthor" resultType="com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto">
        SELECT s.id,s.org_id orgId,s.close_date,s.key_word keyWord,o.code,o.name,p.name parentName,p.id parentId,
        '' AS leaderName,s.provicen_id provicenId,s.provicen_name provicenName,s.city_id cityId,
        s.city_name cityName,s.county_id countyId,s.county_name countyName,s.address address,
        s.leader_id leaderId,s.note,s.status,s.coverurl,s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,o.parent_id parentId,s.tel<!--czw加密已处理-->,s.company_id companyId,
        s.intro,s.consultation_pc,s.consultation_m,s.abbreviation,
        c.area_code areaCode,
               s.whole_usable_area,s.store_biz_type,
        (SELECT name from t_org c WHERE s.company_id=c.id) companyName,o.longitude,o.latitude,s.alipayno,
        s.alipaynourl,s.wechatno,s.wechatnourl,o.full_path_id fullPathId,o.full_path_name fullPathName,
        td.id as distributorId,
        td.code as distributorCode,
        td.name as distributorName,
       <!-- (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )as `character`,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END
        ) as `character`,
        s.address2,s.is_open_kpoint isOpenKpoint,s.store_type storeType,cc.`name` storeTypeName,cc.code storeTypeCode,
        s.develop_salesman_id developSalesmanId,s.develop_salesman_code developSalesmanCode,s.develop_salesman_name
        developSalesmanName,s.develop_salesman_phone developSalesmanPhone,s.app_pre_audit preAudit,c.cut_time cutTime,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type=3 and tdm.is_deleted=0 and tdm.org_id=s.org_id
        LEFT JOIN orgcenter.t_distributor td on td.id=tdm.distributor_id and td.is_deleted=0
        <where>
            <include refid="findPageAllWithStoreAuthorWHERE"/>
            <if test="store != null ">
                <if test="store.storeKeyword != null and store.storeKeyword != ''">
                    <choose>
                        <when test="store.storeKeywordType == 2">
                            AND cc.name LIKE CONCAT('%',#{store.storeKeyword},'%')
                        </when>
                        <otherwise>
                            AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%') OR o.code
                            LIKE CONCAT('%',#{store.storeKeyword},'%') OR cast(s.id as char)=CONCAT('%',#{store.storeKeyword},'%'))
                        </otherwise>
                    </choose>
                </if>
            </if>
        </where>
        <choose>
            <when test="store.orgId != null">
                ORDER BY s.org_id = #{store.orgId} desc, s.created_date DESC
            </when>
            <otherwise>
                ORDER BY s.created_date DESC
            </otherwise>
        </choose>
        <if test="pg !=null ">
        limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <select id="findPageAllWithStoreAuthor22" resultType="com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto">
        SELECT s.id,s.org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        where o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
        <if test="storeAuthorList != null and storeAuthorList.size > 0">
            and s.id in (
            <foreach item="item" collection="storeAuthorList" separator=",">
                #{item.storeId}
            </foreach>
            )
        </if>
        <if test="companyAuthorList != null and companyAuthorList.size > 0">
            and s.company_id in (
            <foreach item="item" collection="companyAuthorList" separator=",">
                #{item.orgId}
            </foreach>
            )
        </if>

    </select>

    <select id="findListAllWithStoreAuthor" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreListAllOutDto">
        SELECT s.id,s.org_id orgId,o.code,o.name,
        s.status,s.company_id companyId,s.abbreviation,
        <!--(SELECT name from t_org c WHERE s.company_id=c.id) companyName,-->
        s.store_type storeType,cc.`name` storeTypeName,cc.code storeTypeCode,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        <!--LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id-->
        where
            o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
            <if test="store !=null">
                <if test="store.status !=null and store.status == 1"><!-- 2021/10/14，13796:1查询状态1启用门店 -->
                    AND s.status=#{store.status}
                </if>
                <if test="store.status !=null and store.status == 2"><!-- 2021/10/14，13796:2查询状态1,2启用筹备门店 -->
                    AND s.status in (1,2)
                </if>
                <if test="store.companyOrgIds != null and store.companyOrgIds.size > 0">
                    AND s.company_id IN
                    <foreach collection="store.companyOrgIds" item="orgId" open="(" separator="," close=")">
                        #{orgId}
                    </foreach>
                </if>
            </if>
            <if test="storeAuthorList != null and storeAuthorList.size > 0">
                and s.id in (
                <foreach item="item" collection="storeAuthorList" separator=",">
                    #{item.storeId}
                </foreach>
                )
            </if>
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
            <if test="store != null ">
                <if test="store.storeKeyword != null and store.storeKeyword != ''">
                    <choose>
                        <when test="store.storeKeywordType == 2">
                            AND cc.name LIKE CONCAT('%',#{store.storeKeyword},'%')
                        </when>
                        <otherwise>
                            AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%') OR o.code
                            LIKE CONCAT('%',#{store.storeKeyword},'%') OR cast(s.id as char)=CONCAT('%',#{store.storeKeyword},'%'))
                        </otherwise>
                    </choose>
                </if>
            </if>
            ORDER BY s.created_date DESC
    </select>

    <select id="findPageAllWithStoreAuthor2" resultType="com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto">
        SELECT s.id,s.org_id orgId,o.code,o.name,c.cut_time cutTime,s.abbreviation abbreviation
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        <where>
            <include refid="findPageAllWithStoreAuthorWHERE"/>
            <if test="store != null ">
                <if test="store.storeKeyword != null and store.storeKeyword != ''">
                    AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%')  OR
                    o.code
                    LIKE CONCAT('%',#{store.storeKeyword},'%')  OR cast(s.id as char)=#{store.storeKeyword})
                </if>
            </if>
        </where>
        ORDER BY s.created_date DESC
        limit #{pg.offset},#{pg.size}
    </select>

    <select id="findListAllWithStoreAuthor2" resultType="com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto">
        SELECT s.id,s.org_id orgId,o.code,o.name,c.cut_time cutTime,s.abbreviation abbreviation
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        <where>
            <include refid="findPageAllWithStoreAuthorWHERE"/>
            <if test="store != null ">
                <if test="store.storeKeyword != null and store.storeKeyword != ''">
                    AND (o.name LIKE CONCAT('%',#{store.storeKeyword},'%') OR s.abbreviation LIKE CONCAT('%',#{store.storeKeyword},'%')  OR
                    o.code
                    LIKE CONCAT('%',#{store.storeKeyword},'%')  OR cast(s.id as char)=#{store.storeKeyword})
                </if>
            </if>
        </where>
        ORDER BY s.created_date DESC
    </select>

    <select id="findPageAllWithStoreAuthor3" resultType="com.fotile.orgcenter.store.pojo.dto.FindStorePageAllOutDto">
        SELECT
        s.develop_salesman_id developSalesmanId,s.develop_salesman_code developSalesmanCode,s.develop_salesman_name
        developSalesmanName,s.develop_salesman_phone developSalesmanPhone

        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        <where>
            o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
            AND s.develop_salesman_id is not null
            <if test="storeAuthorList != null and storeAuthorList.size > 0">
                and s.id in (
                <foreach item="item" collection="storeAuthorList" separator=",">
                    #{item.storeId}
                </foreach>
                )
            </if>
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
        </where>
        group by s.develop_salesman_id
        ORDER BY s.created_date DESC
        <if test="pg !=null ">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!-- 分页查询 -->
    <select id="findPageAll3" resultType="com.fotile.orgcenter.org.pojo.dto.FindOrgPageAllOutDto2">
        SELECT s.id,s.org_id orgId,s.key_word keyWord,o.code,o.name,p.name parentName,p.id parentId,'' AS
        leaderName,s.provicen_id provicenId,s.provicen_name provicenName,s.city_id cityId,s.city_name cityName,
        s.county_id countyId,s.county_name countyName,s.address address,s.leader_id
        leaderId,s.note,s.status,s.coverurl,s.shopping_start shoppingStart,s.shopping_end shoppingEnd,o.parent_id
        parentId,s.tel<!--czw加密已处理-->,s.intro,s.consultation_pc,s.consultation_m,
        s.company_id companyId,(SELECT name from t_org c WHERE s.company_id=c.id)
        companyName,o.longitude,o.latitude,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,o.full_path_id
        fullPathId,o.full_path_name fullPathName,s.address2,s.is_open_kpoint isOpenKpoint
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id
        <where>
            o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
            <if test="store !=null">
                <if test="store.code !=null and store.code !=''"><!-- 部门编码 -->
                    AND o.code LIKE CONCAT('%',#{store.code},'%')
                </if>
                <if test="store.name !=null and store.name !=''"><!-- 部门名称 -->
                    AND o.name LIKE CONCAT('%',#{store.name},'%')
                </if>
                <if test="store.parentId !=null "><!-- 所属部门 -->
                    AND o.parent_id=#{store.parentId}
                </if>
                <if test="store.companyId !=null "><!-- 所属公司 -->
                    AND s.company_id=#{store.companyId}
                </if>
                <if test="store.leaderId !=null "><!-- 店长 -->
                    AND s.leader_id=#{store.leaderId}
                </if>
                <if test="store.status !=null "><!-- 状态 -->
                    AND s.status=#{store.status}
                </if>
            </if>
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
        </where>
        ORDER BY s.created_date DESC
    </select>


    <!-- 根据公司id集合查询门店信息 -->
    <select id="findStoreByCompanyIds" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.is_open_kpoint,
        s.is_open_rent,
        s.`status`,
        s.wechat,
        s.shopping_remark,
        s.tel_remark<!--czw加密已处理4-->,
        s.email,
        s.email_remark,
        s.wechat_remark<!--czw加密已处理4-->,
        s.alipay_remark,
        s.channel,
        s.org_id orgId,
        o. CODE,
        o. NAME,
        s.key_word keyWord,
        s.coverurl,
        s.address,
        s.provicen_id provicenId,
        s.city_id cityId,
        s.county_id countyId,
        o.longitude,
        o.latitude,
        s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,
        s.tel<!--czw加密已处理-->,
        s.leader_id leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        o.parent_id parentId,
        o.full_path_id fullPathId,
        o.full_path_name fullPathName,
        s.company_id,
        s.address2,
        s.intro,s.consultation_pc,s.consultation_m,
        s.is_to_drp isToDrp,s.to_drp_stage toDrpStage
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        WHERE
        o.is_deleted=0 AND s.is_deleted=0
        <if test="idList !=null and idList.size>0">
            AND s.company_id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="findStoreTypeIdsByCompanyIds" resultType="java.lang.Integer">
        SELECT distinct
        s.store_type
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        WHERE
        o.is_deleted=0 AND s.is_deleted=0 and s.store_type is not null
        <if test="idList !=null and idList.size>0">
            AND s.company_id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 根据公司id集合查询门店信息 -->
    <select id="findStoreByCompanyIds2" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.is_open_kpoint,
        s.is_open_rent,
        s.`status`,
        s.wechat,
        s.shopping_remark,
        s.tel_remark<!--czw加密已处理4-->,
        s.email,
        s.email_remark,
        s.wechat_remark<!--czw加密已处理4-->,
        s.alipay_remark,
        s.channel,
        s.org_id orgId,
        o. CODE,
        o. NAME,
        s.key_word keyWord,
        s.coverurl,
        s.address,
        s.provicen_id provicenId,
        s.city_id cityId,
        s.county_id countyId,
        o.longitude,
        o.latitude,
        s.shopping_start shoppingStart,
        s.shopping_end shoppingEnd,
        s.tel<!--czw加密已处理-->,
        s.leader_id leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        o.parent_id parentId,
        o.full_path_id fullPathId,
        o.full_path_name fullPathName,
        s.company_id,
        s.address2,
        s.intro,s.consultation_pc,s.consultation_m,
        s.is_to_drp isToDrp,s.to_drp_stage toDrpStage
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        WHERE
        o.is_deleted=0 AND s.is_deleted=0
        <if test="idList !=null and idList.size>0">
            AND s.company_id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="storeIdList !=null and storeIdList.size>0">
            AND s.id IN
            <foreach collection="storeIdList" item="storeId" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
    </select>


    <!-- 小程序根据门店id查询门店信息 -->
    <select id="findStoreInfoByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,s.org_id orgId,o.code,o.name,s.key_word
        keyWord,s.coverurl,s.intro,s.consultation_pc,s.consultation_m, <!-- CONCAT(s.provicen_name,s.city_name,s.county_name, IFNULL(s.address2,s.address)) address -->
        IF(IFNULL(s.address2,'')='',s.address,s.address2) address,s.provicen_id provicenId,s.city_id cityId,s.county_id
        countyId,o.longitude,o.latitude,
        s.shopping_start shoppingStart,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,s.leader_id
        leaderId,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,s.note,o.parent_id parentId,s.tel<!--czw加密已处理-->
        ,o.full_path_id fullPathId,o.full_path_name fullPathName,s.address2,s.company_id companyId,s.abbreviation
        abbreviation
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        WHERE s.org_id = #{orgId} AND o.is_deleted=0 AND s.is_deleted=0
    </select>


    <select id="queryStore" resultType="com.fotile.orgcenter.store.pojo.dto.QueryStoreOutDto">
        SELECT
        s.id,
        s.address,
        o. NAME,
        s.tel<!--czw加密已处理-->
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        where
        o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
        and s.status='1'
        <if test="key!=null and key!=''">
            and (o.`name` like concat('%',#{key},'%')
            or (s.address like concat('%',#{key},'%')
            ))
        </if>
        <if test="channelCode!=null and channelCode!=''">
            and s.channel like concat('%',#{channelCode},'%')
        </if>
        limit #{offset},#{size}
    </select>

    <select id="queryStoreCount" resultType="long">
        SELECT
        count(1)
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        where
        o.is_deleted=0 AND s.is_deleted =0 AND o.type=3
        and s.status='1'
        <if test="key!=null and key!=''">
            and (o.`name` like concat('%',#{key},'%')
            or (s.address like concat('%',#{key},'%')
            ))
        </if>
        <if test="channelCode!=null and channelCode!=''">
            and s.channel like concat('%',#{channelCode},'%')
        </if>
    </select>

    <!-- 根据门店类型查询门店 -->
    <select id="findStoreByStoreType" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id, s.is_to_drp isToDrp, s.to_drp_stage toDrpStage, s.store_type storeType, s.org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        WHERE s.is_deleted = 0
          AND s.store_type IN
        <foreach collection="storeType" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>
    <!--根据orgid查询所属客户-->
    <select id="selectByOrgId"
            parameterType="com.fotile.orgcenter.store.pojo.dto.SelectDistributorInDto"
            resultType="com.fotile.orgcenter.store.pojo.dto.DistributorDto">
        select distinct d.id distributorId,
        d.code distributorCode,
        d.name distributorName,
        c1.id as channelCategoryId,
        c1.name as channelCategoryName,
        c2.id as channelSubdivideId,
        c2.name as channelSubdivideName
        from orgcenter.t_distributor d
        left join orgcenter.t_distributor_mapping m1 on d.id = m1.distributor_id and m1.is_deleted = 0 AND m1.type = 1
        left join orgcenter.t_distributor_mapping m2 on d.id = m2.distributor_id and m2.is_deleted = 0 AND m2.type = 3
        left join orgcenter.channel_category c1 on c1.id = d.channel_category and c1.is_deleted = 0
        left join orgcenter.channel_category c2 on c2.id = d.channel_subdivide and c2.is_deleted = 0
        where d.is_deleted = 0
        <if test="query.channelCategoryId != null">
            and c1.id=#{query.channelCategoryId}
        </if>
        <if test="query.channelCategoryCode != null and query.channelCategoryCode != ''">
            and c1.code=#{query.channelCategoryCode}
        </if>
        <if test="query.channelSubdivideId != null">
            and c2.id=#{query.channelSubdivideId}
        </if>
        <if test="query.channelSubdivideCode != null and query.channelSubdivideCode != ''">
            and c2.code=#{query.channelSubdivideCode}
        </if>
        <if test="query.status != null">
            and d.status=#{query.status}
        </if>
        <!--数据权限-->
        <if test="query.dataScopeCompany != null and query.dataScopeCompany.size() != 0">
            and m1.org_id in
            <foreach collection="query.dataScopeCompany" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="query.orgId != null">
            and m1.org_id = #{query.orgId}
        </if>
        <if test="query.storeChannelCode != null and query.storeChannelCode != ''">
            and c2.id in (
            select cc.parent_id
            from channel_category cc
            where cc.is_deleted=0 and cc.`level`=3 and cc.`code`=#{query.storeChannelCode}
            )
        </if>
        <!-- OR m1.org_id IS NULL OR TRIM(m1.org_id) = '')-->
    </select>
    <select id="selectDis" resultType="com.fotile.orgcenter.store.pojo.dto.DistributorDto">
        select d.id   distributorId,
               d.name distributorName,
               d.code distributorCode,
               c.name channelSubdivideName,
               c.id   channelSubdivideId
        from orgcenter.t_distributor d
                 left join orgcenter.channel_category c on c.id = d.channel_subdivide and c.is_deleted = 0
                 left join orgcenter.t_store<!--czw加密已处理--> s on s.distributor_id = d.id and s.is_deleted = 0
        where d.is_deleted = 0
          and s.id = #{id}
    </select>
    <!--	根据渠道细分id查询门店类型-->
    <select id="findStoerType" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelCategoryEntity">
        SELECT name,id
        FROM orgcenter.channel_category
        WHERE is_deleted = 0 AND type_code='store_type'
        <if test="parentId !=null">
            AND parent_id=#{parentId}
        </if>
    </select>
    <insert id="insertDis">
        insert orgcenter.t_distributor_mapping(org_id, distributor_id)
        values (#{orgId},
                #{disId})
    </insert>
    <insert id="insertStoreAndDis">

    </insert>
    <update id="updateDis">
        update
            orgcenter.t_distributor_mapping
        set org_id = #{orgId}
        where distributor_id = #{disId}
    </update>
    <select id="selectByDisId" resultType="java.lang.Long">
        select distributor_id
        from t_distributor
        where id = #{disId}
    </select>
    <select id="findChannelCategoryByOrgId" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelCategory">
        SELECT (SELECT code FROM orgcenter.channel_category cc WHERE d.channel_category = cc.id)  channelCategoryCode,
               (SELECT name FROM orgcenter.channel_category cc WHERE d.channel_category = cc.id)  channelCategoryName,
               (SELECT code FROM orgcenter.channel_category cc WHERE d.channel_subdivide = cc.id) channelSubdivideCode,
               (SELECT name FROM orgcenter.channel_category cc WHERE d.channel_subdivide = cc.id) channelSubdivideName
        FROM orgcenter.t_distributor d
        WHERE d.is_deleted = 0
          AND d.id = #{distributorId}
    </select>

    <select id="findChannelCategoryById" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelCategory">
        SELECT id, code storeTypeCode, name storeTypeName
        FROM orgcenter.channel_category d
        WHERE d.is_deleted = 0
          AND d.id = #{id}
    </select>


    <!-- 根据门店orgids查询门店信息 -->
    <select id="getReportStoreInfo" resultType="com.fotile.orgcenter.salesman.pojo.dto.ReportStoreInfo">
        SELECT s.org_id storeOrgId,o.code storeCode,
        case when s.abbreviation is not null
        then s.abbreviation
        else
        o.name
        end storeName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        WHERE
        o.is_deleted=0 AND s.is_deleted=0 and s.status = '1'
        <if test="companyIdList != null and companyIdList.size > 0">
            and s.company_id in
            <foreach collection="companyIdList" item="orgId" index="index" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="orgIds != null and orgIds.size > 0">
            and s.org_id in
            <foreach collection="orgIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by o.code
        limit 0,20
    </select>

    <select id="getReportStoreInfoByManagerId" resultType="com.fotile.orgcenter.salesman.pojo.dto.ReportStoreInfo">
        SELECT
        s.company_id companyId,
        oc.NAME companyName,
        tc.area_code regionCode,
        tc.area_name regionName,
        s.develop_salesman_id chargeUesrId,
        s.develop_salesman_code chargeUesrCode,
        s.develop_salesman_name chargeUesrName,
        s.org_id storeOrgId,
        os.CODE storeCode,
        CASE

        WHEN s.abbreviation IS NOT NULL THEN
        s.abbreviation ELSE os.NAME
        END storeName
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org os ON os.is_deleted = 0
        AND os.type = 3
        AND s.org_id = os.id
        INNER JOIN orgcenter.t_org oc ON oc.is_deleted = 0
        AND oc.type = 1
        AND s.company_id = oc.id
        LEFT JOIN orgcenter.t_company tc ON s.company_id = tc.org_id
        WHERE
        oc.is_deleted = 0
        AND s.is_deleted = 0
        AND s.STATUS = '1'
        and develop_salesman_id = #{managerId}
        <if test="companyIdList != null and companyIdList.size > 0">
            and s.company_id in
            <foreach collection="companyIdList" item="orgId" index="index" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="orgIds != null and orgIds.size > 0">
            and s.org_id in
            <foreach collection="orgIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by os.code
    </select>
    <select id="getStoreTypeByOrgId" resultType="long" parameterType="long">
        SELECT store_type
        from orgcenter.t_store<!--czw加密已处理-->
        where org_id = #{orgId}
          and is_deleted = 0
    </select>

    <select id="findManagerInfoByIds" resultType="com.fotile.orgcenter.store.pojo.dto.ManagerInfoDto">
        SELECT
        s.develop_salesman_id managerId,
        s.develop_salesman_code managerCode,
        s.develop_salesman_name managerName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        WHERE
        develop_salesman_id is not null
        <if test="orgIds !=null and orgIds.size>0">
            AND s.org_id IN
            <foreach collection="orgIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and s.status = 1
        and s.is_deleted=0
        GROUP BY
        s.develop_salesman_id
    </select>

    <select id="findManagerInfoByIdsWithoutStatus" resultType="com.fotile.orgcenter.store.pojo.dto.ManagerInfoDto">
        SELECT
        s.develop_salesman_id managerId,
        s.develop_salesman_code managerCode,
        s.develop_salesman_name managerName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        WHERE
        develop_salesman_id is not null
        <if test="orgIds !=null and orgIds.size>0">
            AND s.org_id IN
            <foreach collection="orgIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and s.is_deleted=0
        GROUP BY
        s.develop_salesman_id
    </select>

    <sql id="findManagerInfoByStoreAuthorListWhere">
        <where>
            is_deleted=0 AND develop_salesman_id IS NOT NULL AND status=1
            <if test="name !=null and name !=''">
                AND develop_salesman_name = #{name, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
            </if>
            <if test="companyIdList != null and companyIdList.size() != 0">
                and company_id in
                <foreach collection="companyIdList" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="storeAuthorList !=null and storeAuthorList.size>0">
                AND id IN
                <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                    #{store.storeId}
                </foreach>
            </if>
        </where>

    </sql>

    <select id="findManagerInfoByStoreAuthorListTotal" resultType="int">
        SELECT COUNT(DISTINCT develop_salesman_id)
        FROM orgcenter.t_store<!--czw加密已处理-->
        <include refid="findManagerInfoByStoreAuthorListWhere"/>
    </select>

    <select id="findManagerInfoByStoreAuthorList" resultType="com.fotile.orgcenter.store.pojo.dto.ManagerInfoDto">
        SELECT DISTINCT develop_salesman_id managerId,develop_salesman_code managerCode,develop_salesman_name
        managerName
        FROM orgcenter.t_store<!--czw加密已处理-->
        <include refid="findManagerInfoByStoreAuthorListWhere"/>
        <if test="pg !=null">
            LIMIT #{pg.offset},#{pg.size}
        </if>
    </select>


    <select id="findCutTimeByOrgIds" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,s.org_id orgId,o.code,o.name,c.cut_time cutTime
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        <where>
            s.is_deleted =0
            AND s.org_id IN
            <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </where>
    </select>

    <!-- 根据公司id集合查询门店信息 -->
    <select id="findByCodes" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.status,
        s.org_id orgId,
        o.code,
        o.name,
        c.area,
        c.area_code areaCode,
        c.area_name areaName,
        c.id companyPkId,
        s.abbreviation,
        s.store_type storeType,
        s.provicen_name,
        s.city_name,
        s.county_name,
        s.address,
        s.address2,
        s.plan_code,
        s.usable_area,
        s.eps_usable_area,
        s.whole_usable_area,
        s.decorate_status,
        s.created_date,
        s.close_date,
        s.terminal_check_date                    terminalCheckDate,
        s.terminal_check_date_again      terminalCheckDateAgain,
        s.store_sub_channel_code,
        s.outside_store_name,
        s.is_virtual,
        s.need_terminal_build,
        s.store_market_grade,
        s.manager_name,
        s.manager_phone,
        s.market_capacity,
        s.population,
        s.top_kitchens,
        s.store_biz_type,
        s.property_right,
        s.lease_term,
        s.annual_rent,
        s.estimated_annual_sales,
        s.store_level,
        s.is_unmanned,
        s.if_decoration_store,
        (
        case when s.open_type=1 and s.is_virtual!=1
        then (SELECT tsfn.decoration_type FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
        when s.open_type=2 and s.is_virtual!=1
        then (SELECT tsfc.decoration_type FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
        end
        ) as decorationType,
        (SELECT tsfn.is_frontage FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)isFrontage,
        dm.distributor_id distributorId,
        d.`code` distributorCode,
        d.`name` distributorName,
        d.channel_category channelCategory,
        d.channel_subdivide channelSubdivide,
        cc.`name` storeTypeName,
       <!-- (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )as `character`,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END

        ) as `character`,

        cc1.`code` channelCategoryCode ,
        cc1.`name`  channelCategoryName,

        cc2.`code` channelSubdivideCode,

        cc2.`name`  channelSubdivideName,

        cc.`code`  storeTypeCode,
        s.is_to_drp isToDrp,
        (SELECT name FROM orgcenter.t_org co WHERE co.is_deleted=0 AND co.id=s.company_id) companyName,
        s.company_id companyId
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type = cc.id AND cc.is_deleted=0
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND dm.type=3 AND s.org_id=dm.org_id
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 ON cc1.id=d.channel_category AND cc1.is_deleted=0
        LEFT JOIN orgcenter.channel_category cc2 ON cc2.id=d.channel_subdivide AND cc2.is_deleted=0
        WHERE s.is_deleted=0
        <if test="codeList !=null and codeList.size>0">
            AND o.code IN
            <foreach collection="codeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </select>


    <!-- 查找附近门店 -->
   <!-- AND s.status=#{inDto.status}-->
    <sql id="findStoreByDistributorWHERE">
        <if test="inDto.distributorId !=null">AND d.id=#{inDto.distributorId}</if>
        <if test="inDto.status !=null and inDto.status !=1">AND d.status=#{inDto.status}
        </if>

        <if test="inDto.statusSet!=null and inDto.statusSet.size()>0">
            and s.status in
            <foreach collection="inDto.statusSet" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>


    </sql>
   <!-- <if test="inDto.status !=null and inDto.status !=1">AND s.status=#{inDto.status}</if>-->
    <sql id="findStoreByDistributorWHERE2">
        WHERE s.is_deleted=0
        <if test="inDto.companyId !=null">
            AND (s.company_id=#{inDto.companyId} OR dm.org_id=#{inDto.companyId} )
        </if>
        <if test="storeAuthorList != null and storeAuthorList.size > 0">
            AND s.id IN (
            <foreach item="item" collection="storeAuthorList" separator=",">
                #{item.storeId}
            </foreach>
            )
        </if>

        <if test="inDto.statusSet!=null and inDto.statusSet.size()>0">
            and s.status in
            <foreach collection="inDto.statusSet" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="companyAuthorList !=null and companyAuthorList.size>0">
            AND s.company_id IN
            <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                #{company.orgId}
            </foreach>
        </if>
    </sql>
    <select id="findStoreByDistributorTotal" resultType="int">
        SELECT COUNT(s.id)
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND o.id=s.org_id
        <if test="inDto.keyWord !=null and inDto.keyWord !=''">
            AND (o.name LIKE CONCAT('%',#{inDto.keyWord},'%') OR o.code LIKE CONCAT('%',#{inDto.keyWord},'%'))
        </if>
        <choose>
            <when test="inDto.distributorId !=null">
                INNER JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND o.id=dm.org_id
                INNER JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
                <include refid="findStoreByDistributorWHERE"/>
            </when>
            <otherwise>
                LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND o.id=dm.org_id
                LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
                <include refid="findStoreByDistributorWHERE"/>
            </otherwise>
        </choose>
        <include refid="findStoreByDistributorWHERE2"/>
    </select>
    <select id="findStoreByDistributor" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByDistributorOutDto">
        SELECT s.id,s.org_id orgId,o.`code`,o.`name`,s.store_type storeType,s.abbreviation,s.status,
        (SELECT cc.`name` FROM orgcenter.channel_category cc WHERE cc.is_deleted=0 AND cc.`level`=3 AND
        cc.id=s.store_type) storeTypeName
        ,s.app_pre_audit preAudit,d.id distributorId,d.name distributorName,d.code distributorCode
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND o.id=s.org_id
        <if test="inDto.keyWord !=null and inDto.keyWord !=''">
            AND (o.name LIKE CONCAT('%',#{inDto.keyWord},'%') OR o.code LIKE CONCAT('%',#{inDto.keyWord},'%'))
        </if>
        <choose>
            <when test="inDto.distributorId !=null">
                INNER JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND dm.type=3 AND o.id=dm.org_id
                INNER JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
                <include refid="findStoreByDistributorWHERE"/>
            </when>
            <otherwise>
                LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND dm.type=3 AND o.id=dm.org_id
                LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
                <include refid="findStoreByDistributorWHERE"/>
            </otherwise>
        </choose>
        <include refid="findStoreByDistributorWHERE2"/>
        ORDER BY o.code ASC
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 根据公司id集合查询门店信息 -->
    <select id="findNameByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,
               s.org_id                            orgId,
               s.company_id                        companyId,
               o.code,
               o.name,
               s.store_type                        storeType,
               dm.distributor_id                   distributorId,
               d.`code`                            distributorCode,
               d.`name`                            distributorName,
               d.channel_category                  channelCategory,
               d.channel_subdivide                 channelSubdivide,
               s.status                             status,
               (SELECT code
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = d.channel_category)  channelCategoryCode,
               (SELECT name
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = d.channel_category)  channelCategoryName,
               (SELECT code
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = d.channel_subdivide) channelSubdivideCode,
               (SELECT name
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = d.channel_subdivide) channelSubdivideName,
               (SELECT code
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = s.store_type)        storeTypeCode,
               (SELECT name
                FROM orgcenter.channel_category cc
                WHERE cc.is_deleted = 0
                  AND cc.id = s.store_type)        storeTypeName
        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
                 LEFT JOIN orgcenter.t_distributor_mapping dm
                           ON dm.is_deleted = 0 AND dm.type = 3 AND s.org_id = dm.org_id
                 LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0 AND dm.distributor_id = d.id
        WHERE s.is_deleted = 0
          AND s.org_id = #{orgId}
    </select>
    <select id="findAllStore" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByIdOutDto">
        SELECT s.id,
               s.org_id            orgId,
               o.code,
               o.name,
               o.parent_id         parentId,
               s.store_type        storeType,
               s.company_id        companyId,
               s.`status`          `status` ,
               dm.distributor_id   distributorId,
               d.`code`            distributorCode,
               d.`name`            distributorName,
               d.channel_category  channelCategory,
               d.channel_subdivide channelSubdivide,
               s.is_to_drp         isToDrp,
               s.is_transfer       isTransfer
        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
                 LEFT JOIN orgcenter.t_distributor_mapping dm
                           ON dm.is_deleted = 0 AND dm.type = 3 AND s.org_id = dm.org_id
                 LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0 AND dm.distributor_id = d.id
        WHERE s.is_deleted = 0
          AND (o.`code` != '' and o.`code` is NOT NULL)
    </select>

    <select id="findAllStoreByIds" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByIdOutDto">
        SELECT s.id,
               s.org_id            orgId,
               o.code,
               o.name,
               o.parent_id         parentId,
               s.store_type        storeType,
               s.company_id        companyId,
               s.`status`          `status` ,
               dm.distributor_id   distributorId,
               d.`code`            distributorCode,
               d.`name`            distributorName,
               d.channel_category  channelCategory,
               d.channel_subdivide channelSubdivide,
               s.is_to_drp         isToDrp,
               s.is_transfer       isTransfer
        FROM orgcenter.t_store<!--czw加密已处理--> s
                 INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
                 LEFT JOIN orgcenter.t_distributor_mapping dm
                           ON dm.is_deleted = 0 AND dm.type = 3 AND s.org_id = dm.org_id
                 LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0 AND dm.distributor_id = d.id
        WHERE s.is_deleted = 0
          AND (o.`code` != '' and o.`code` is NOT NULL)
          <if test="ids != null and ids.size() > 0">
              and s.id in
              <foreach collection="ids" item="id" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
    </select>

    <select id="findAllStoreByCodes" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByIdOutDto">
        SELECT s.id,
        s.org_id            orgId,
        o.code,
        o.name,
        o.parent_id         parentId,
        s.store_type        storeType,
        s.company_id        companyId,
        s.`status`          `status` ,
        dm.distributor_id   distributorId,
        d.`code`            distributorCode,
        d.`name`            distributorName,
        d.channel_category  channelCategory,
        d.channel_subdivide channelSubdivide,
        s.is_cashier_enabled isCashierEnabled,
        s.cashier_charge_mapping cashierChargeMapping,
        s.is_to_drp         isToDrp,
        s.store_biz_type storeBizType,
        s.is_transfer       isTransfer,
        s.store_sub_channel_code       storeSubChannelCode,
        s.iz_im_pay_activity_enabled izImPayActivityEnabled
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        LEFT JOIN orgcenter.t_distributor_mapping dm
        ON dm.is_deleted = 0 AND dm.type = 3 AND s.org_id = dm.org_id
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0 AND dm.distributor_id = d.id
        WHERE s.is_deleted = 0
        AND (o.`code` != '' and o.`code` is NOT NULL)
        <if test="codes != null and codes.size() > 0">
            and o.code in
            <foreach collection="codes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </select>


    <select id="selectCompanyInfoById" parameterType="long"
            resultType="com.fotile.orgcenter.store.pojo.dto.CompanyRegionVto">
        SELECT org_id orgId, area areaId, area_name areaName
        from t_company
        where is_deleted = 0
          and org_id = #{id}
        limit 1
    </select>
    <select id="getStoreFacilities" resultType="java.util.Map">
        SELECT *
        from orgcenter.t_store_facilities
        where is_deleted = 0
          and store_id = #{orgId}
    </select>

    <select id="findStoreByStoreOrgId"
            resultType="com.fotile.orgcenter.pstore.pojo.dto.FindSalesmanANdStoreBysalesmanIdOutDTO">
        select o.code storeCode,o.name storeName,cc.code storeTypeCode,cc.name storeTypeName,s.store_type storeType,s.status status,
        s.store_sub_channel_code
        from orgcenter.t_store<!--czw加密已处理--> s
        left join orgcenter.t_org o on s.org_id = o.id and o.is_deleted = 0 and o.type = 3
        left join orgcenter.channel_category cc on cc.is_deleted=0 and s.store_type=cc.id
        where s.org_id = #{storeOrgId,jdbcType=BIGINT} and s.is_deleted = 0
    </select>

    <select id="getStoreChannel" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelDetail">
        SELECT id, name, code
        from orgcenter.channel_category
        where is_deleted = 0
          and id = #{storeType}
    </select>

    <select id="getDistributorChannel" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelDetail">
        SELECT c.CODE,
               c.NAME
        FROM t_distributor d
                 INNER JOIN channel_category c ON (
                d.channel_category = c.id
                AND d.is_deleted = 0
                AND c.is_deleted = 0
            )
        WHERE d.id = #{distributorId}
    </select>

    <select id="getChannelSubdivide" resultType="com.fotile.orgcenter.store.pojo.dto.ChannelDetail">
        SELECT c.CODE,
               c.NAME
        FROM t_distributor d
                 INNER JOIN channel_category c ON (
                d.channel_subdivide = c.id
                AND d.is_deleted = 0
                AND c.is_deleted = 0
            )
        WHERE d.id = #{distributorId}
    </select>

    <select id="queryStoreIdByOrgId" resultType="java.lang.Long">
        SELECT ts.id
        FROM orgcenter.t_store<!--czw加密已处理--> ts
                 INNER JOIN t_org tg ON ts.org_id = tg.id
            AND ts.is_deleted = 0
            AND tg.is_deleted = 0
            AND tg.type = 3
        WHERE tg.id = #{orgId}
    </select>

    <!-- 根据关键字查询门店orgId -->
    <select id="findByKeyWord" resultType="java.lang.Long">
        SELECT org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理-->
        WHERE is_deleted=0 AND
        <foreach collection="keyWords" item="keyWord" open="(" separator="OR" close=")">
            FIND_IN_SET(#{keyWord},key_word)
        </foreach>
        <if test="storeList != null and storeList.size() > 0">
            AND id IN (
            <foreach item="store" collection="storeList" separator=",">
                #{store.storeId}
            </foreach>
            )
        </if>
        <if test="companyAuthorList != null and companyAuthorList.size() > 0">
            AND company_id IN (
            <foreach item="company" collection="companyAuthorList" separator=",">
                #{company.orgId}
            </foreach>
            )
        </if>
    </select>

    <!--门店批量分配-->
    <update id="batchUpdate">
        UPDATE orgcenter.t_store
        <set>
            modified_by=#{modifiedBy},modified_date=NOW(),`version`=`version`+1
            <if test="inDto.isLeaderChange !=null and inDto.isLeaderChange==1">
                ,leader_id=#{inDto.leaderId}
            </if>
            <if test="inDto.developChange !=null and inDto.developChange==1">
                ,develop_salesman_id=#{inDto.developSalesmanId}
                ,develop_salesman_code=#{inDto.developSalesmanCode}
                ,develop_salesman_name=#{inDto.developSalesmanName,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
                ,develop_salesman_phone=#{inDto.developSalesmanPhone,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="inDto.cluesChange !=null and inDto.cluesChange==1">
                ,clues_msg_user=#{inDto.cluesMsgUser}
            </if>
            <if test="inDto.channelChange !=null and inDto.channelChange==1">
                ,channel=#{inDto.channel}
            </if>
            <if test="inDto.chargePersonChange != null and inDto.chargePersonChange==1">
                ,manager_name=#{inDto.chargePerson,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="inDto.isToDrpChange !=null and inDto.isToDrpChange==1">
                ,is_to_drp=#{inDto.isToDrp}
            </if>
            <if test="inDto.isToCheckChange !=null and inDto.isToCheckChange==1">
                ,is_to_check=#{inDto.isToCheck}
            </if>
            <if test="inDto.appPreAuditChange !=null and inDto.appPreAuditChange==1">
                ,app_pre_audit=#{inDto.appPreAudit}
            </if>
            <if test="inDto.cluesFollowChange !=null and inDto.cluesFollowChange==1">
                ,clues_follow=#{inDto.cluesFollow}
            </if>
            <if test="inDto.cluesLoseChange !=null and inDto.cluesLoseChange==1">
                ,clues_lose=#{inDto.cluesLose}
            </if>
            <if test="inDto.isManagerCluesIntoSeaChange !=null and inDto.isManagerCluesIntoSeaChange==1">
                ,is_manager_clues_into_sea=#{inDto.isManagerCluesIntoSea}
            </if>
            <!--<if test="inDto.isSceneFlagChange !=null and inDto.isSceneFlagChange==1">
                ,is_scene_flag=#{inDto.isSceneFlag}
            </if>-->
            <if test="inDto.openRqChannelChange !=null and inDto.openRqChannelChange==1">
                ,open_rq_channel=#{inDto.openRqChannel}
            </if>
            <if test="inDto.openZqChannelChange !=null and inDto.openZqChannelChange==1">
                ,open_zq_channel=#{inDto.openZqChannel}
            </if>
            <if test="inDto.openWyChannelChange !=null and inDto.openWyChannelChange==1">
                ,open_wy_channel=#{inDto.openWyChannel}
            </if>
            <if test="inDto.storeStatus !=null">
                ,status=#{inDto.storeStatus}
            </if>
        </set>
        WHERE is_deleted=0 AND org_id IN
        <foreach collection="inDto.idList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        ;

        <if test="inDto.idListForHuaDongArea != null and inDto.idListForHuaDongArea.size() > 0">
            UPDATE orgcenter.t_store
            <set>
                modified_by=#{modifiedBy},modified_date=NOW(),`version`=`version`+1
                <if test="inDto.toDrpStageChange !=null and inDto.toDrpStageChange==1">
                    ,to_drp_stage=#{inDto.toDrpStage}
                </if>
            </set>
            WHERE is_deleted=0 AND org_id IN
            <foreach collection="inDto.idListForHuaDongArea" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            ;
        </if>


        <if test="inDto.depChange !=null and inDto.depChange == 1">
            UPDATE orgcenter.t_org o
            <if test="inDto.parentId !=null"><!--门店所属部门不为空-->
                ,orgcenter.t_org p
            </if>
            <set>
                o.modified_by=#{modifiedBy},o.modified_date=NOW()
                <choose>
                    <when test="inDto.parentId ==null">
                        ,o.parent_id=null
                        ,o.full_path_id=id
                        ,o.full_path_name=name
                    </when>
                    <otherwise><!--门店所属部门不为空-->
                        ,o.parent_id=#{inDto.parentId}
                        ,o.full_path_id=CONCAT(p.full_path_id,'-',o.id)
                        ,o.full_path_name=CONCAT(p.full_path_name,'-',o.`name`)
                    </otherwise>
                </choose>
            </set>
            WHERE o.is_deleted=0 AND o.id IN
            <foreach collection="inDto.idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="inDto.parentId !=null">
                AND p.id=#{inDto.parentId}
            </if>;
        </if>
    </update>

    <!--门店及人员统计报表-->
    <insert id="storeSalesmanJob">
        <!--所有门店信息-->
        <!--插入之前删除当天的记录-->
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1-->
        SET is_deleted=1,modified_by='batch deleted',modified_date=now()
        WHERE is_deleted=0 AND date_format(created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        INSERT INTO
        orgcenter.t_storeSalesman_report<!--czw加密已处理1-->(store_id,company_id,develop_salesman_id,develop_salesman_name,created_by,created_date,report_date,abbreviation,status,is_virtual,store_num)
        SELECT
        s.org_id,s.company_id,s.develop_salesman_id,s.develop_salesman_name,'job',NOW(),NOW(),s.abbreviation,s.status,s.is_virtual,1
        FROM orgcenter.t_store<!--czw加密已处理1--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND s.org_id=o.id
        WHERE s.is_deleted=0 ;

        /**门店名称*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,orgcenter.t_org o
        SET r.store_name=o.name
        WHERE r.store_id=o.id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**公司名称*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,orgcenter.t_org o
        SET r.company_name=o.name
        WHERE r.company_id=o.id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**大区id、大区名称*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,orgcenter.t_company c
        SET r.area_id=c.area,r.area_name=c.area_name
        WHERE r.company_id=c.org_id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**店长数*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman<!--czw加密已处理--> s
        WHERE s.is_deleted=0 AND s.station=22
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.shopowner_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**厨电顾问数*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman<!--czw加密已处理--> s
        WHERE s.is_deleted=0 AND s.station=24
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.adviser_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**客户经理数*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman<!--czw加密已处理--> s
        WHERE s.is_deleted=0 AND s.station=23
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.customer_manager_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');

        /**门店渠道*/
        UPDATE orgcenter.t_storeSalesman_report<!--czw加密已处理1--> r,
        (SELECT s.org_id,cc.code,cc.id store_type FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.channel_category<!--czw加密已处理--> cc ON cc.is_deleted=0 AND cc.level=3 AND cc.id=s.store_type
        WHERE s.is_deleted=0) s
        SET r.store_type=s.store_type,r.store_type_code=s.code
        WHERE r.store_id=s.org_id AND date_format(r.created_date,'%Y_%m_%d')=date_format(NOW(),'%Y_%m_%d');
    </insert>

    <!--门店及人员统计报表（按年分区）-->
    <insert id="storeSalesmanJobByPartition">
        <!--所有门店信息-->
        <!--插入之前删除当天的记录-->
        UPDATE ${schemaTableName}
        SET is_deleted=1,modified_by='batch deleted',modified_date=now()
        WHERE is_deleted=0 AND date_format(report_date,'%Y-%m-%d')=#{calcDate};

        INSERT INTO
        ${schemaTableName}(store_id,company_id,develop_salesman_id,develop_salesman_name,created_by,created_date,report_date,abbreviation,status,is_virtual,store_num)
        SELECT
        s.org_id,s.company_id,s.develop_salesman_id,s.develop_salesman_name,'job',NOW(),#{calcDate},s.abbreviation,s.status,s.is_virtual,1
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND s.org_id=o.id
        WHERE s.is_deleted=0 ;

        /**门店名称*/
        UPDATE ${schemaTableName} r,orgcenter.t_org o
        SET r.store_name=o.name
        WHERE r.store_id=o.id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**公司名称*/
        UPDATE ${schemaTableName} r,orgcenter.t_org o
        SET r.company_name=o.name
        WHERE r.company_id=o.id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**大区id、大区名称*/
        UPDATE ${schemaTableName} r,orgcenter.t_company c
        SET r.area_id=c.area,r.area_name=c.area_name
        WHERE r.company_id=c.org_id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**店长数*/
        UPDATE ${schemaTableName} r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman s
        WHERE s.is_deleted=0 AND s.station=22
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.shopowner_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**厨电顾问数*/
        UPDATE ${schemaTableName} r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman<!--czw加密已处理--> s
        WHERE s.is_deleted=0 AND s.station=24
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.adviser_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**客户经理数*/
        UPDATE ${schemaTableName} r,
        (SELECT s.store_id,IFNULL(COUNT(s.id),0) num
        FROM orgcenter.t_salesman<!--czw加密已处理--> s
        WHERE s.is_deleted=0 AND s.station=23
        AND s.status=1 and s.store_id is not null
        GROUP BY s.store_id
        ) ss
        SET r.customer_manager_num=ss.num
        WHERE r.store_id=ss.store_id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};

        /**门店渠道*/
        UPDATE ${schemaTableName} r,
        (SELECT s.org_id,cc.code,cc.id store_type FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.channel_category<!--czw加密已处理--> cc ON cc.is_deleted=0 AND cc.level=3 AND cc.id=s.store_type
        WHERE s.is_deleted=0) s
        SET r.store_type=s.store_type,r.store_type_code=s.code
        WHERE r.store_id=s.org_id AND date_format(r.report_date,'%Y-%m-%d')=#{calcDate};
    </insert>


    <!-- 门店及人员统计报表 -->
    <sql id="storeSalesmanReportWHERE">
        <where>
            sr.is_deleted=0
            <if test="inDto.type !=null and inDto.type == 1">
                AND sr.area_id is not null and sr.area_id != ''
            </if>
            <if test="inDto.type !=null and inDto.type == 2">
                AND sr.company_id is not null and sr.company_id != ''
            </if>
            <if test="inDto.type !=null and inDto.type == 3">
                AND sr.develop_salesman_id is not null and sr.develop_salesman_id != ''
            </if>
            <if test="inDto.type !=null and inDto.type == 4">
                AND sr.store_id is not null and sr.store_id != ''
            </if>
            <if test="inDto.parentId !=null and inDto.type !=null "><!--parentId:上级id   type:1：大区；2：公司；3：门店业务主管；4：门店-->
                <choose>
                    <when test="inDto.type ==2"><!--查询大区下所有分公司的数据-->
                        AND sr.area_id=#{inDto.parentId}
                    </when>
                    <when test="inDto.type ==3"><!--查询分公司下所有门店业务主管的数据-->
                        AND sr.company_id=#{inDto.parentId}
                    </when>
                    <when test="inDto.type ==4"><!--查询门店业务主管下所有门店的数据-->
                        AND sr.develop_salesman_id=#{inDto.parentId}
                    </when>
                </choose>
            </if>
            <if test="companyAuthorList != null and companyAuthorList.size > 0"><!--大区、分公司根据公司数据权限查询-->
                and sr.company_id in (
                <foreach item="item" collection="companyAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
            <if test="storeAuthorList != null and storeAuthorList.size > 0 and (inDto.type==3 or inDto.type==4)"><!--门店业务主管、门店根据门店数据权限查询-->
                and sr.store_id in (
                <foreach item="item" collection="storeAuthorList" separator=",">
                    #{item.orgId}
                </foreach>
                )
            </if>
            <if test="inDto.storeTypes != null and inDto.storeTypes.size>0"><!--门店渠道-->
                AND sr.store_type IN
                <foreach collection="inDto.storeTypes" item="storeType" open="(" separator="," close=")">
                    #{storeType}
                </foreach>
            </if>
            <if test="inDto.keyWords !=null and inDto.keyWords.size>0"><!--门店标签-->
                AND
                <foreach collection="inDto.keyWords" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="inDto.storeTypeCodes != null and inDto.storeTypeCodes.size>0"><!--门店渠道编码-->
                AND sr.store_type_code IN
                <foreach collection="inDto.storeTypeCodes" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="storeSalesmanReportTotal" resultType="int">
        SELECT COUNT(r.id)
        FROM (
        SELECT sr.id
        FROM ${inDto.reportDateSchemaTableName}<!--czw加密已处理1--> sr
        <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
            INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
        </if>
        <include refid="storeSalesmanReportWHERE"/>
        AND sr.is_reference=0 and sr.status !=0
        <if test="inDto.reportDate !=null and inDto.reportDate !=''"><!--统计时间-->
            AND sr.report_date=DATE_FORMAT(#{inDto.reportDate},'%Y-%m-%d')
        </if>
        <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
            AND sr.is_virtual=#{inDto.isVirtual}
        </if>
        <if test="inDto.areaId !=null"><!--大区id-->
            AND sr.area_id=#{inDto.areaId}
        </if>
        <if test="inDto.companyId !=null"><!--分公司id-->
            AND sr.company_id=#{inDto.companyId}
        </if>
        <if test="inDto.developSalesmanId !=null"><!--业务id-->
            AND sr.develop_salesman_id=#{inDto.developSalesmanId}
        </if>
        <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
            <when test="inDto.type ==1">
                GROUP BY sr.area_id
            </when>
            <when test="inDto.type ==2">
                GROUP BY sr.company_id
            </when>
            <when test="inDto.type ==3">
                GROUP BY sr.develop_salesman_id
            </when>
            <when test="inDto.type ==4">
                GROUP BY sr.store_id
            </when>
        </choose>
        ) r
    </select>
    <select id="storeSalesmanReport" resultType="com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportOutDto">
        <choose>
            <when test="inDto.isCumulative ==null or inDto.isCumulative !=0 "><!--累计-->
                SELECT sr.store_id storeId,sr.store_name storeName,sr.company_id companyId,ifnull(sr.company_name,'空') companyName,
                sr.area_id areaId,ifnull(sr.area_name,'空') areaName,sr.develop_salesman_id
                developSalesmanId,ifnull(sr.develop_salesman_name,'空') developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum,
                sr.abbreviation
                FROM ${inDto.reportDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0 and sr.status !=0
                <if test="inDto.reportDate !=null and inDto.reportDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.reportDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id order by sr.area_id desc
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id order by sr.company_id desc
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
            </when>
            <otherwise><!--变动-->
                SELECT
                r1.storeId,r1.storeName,r1.companyId,ifnull(r1.companyName,'空') companyName,r1.areaId,ifnull(r1.areaName,'空') areaName,
                       r1.developSalesmanId,ifnull(r1.developSalesmanName,'空') developSalesmanName,
                r1.reportDate,(r1.storeNum-IFNULL(r2.storeNum ,0)) storeNum,(r1.shopownerNum -IFNULL(r2.shopownerNum ,0)) shopownerNum,
                (r1.adviserNum-IFNULL(r2.adviserNum ,0)) adviserNum,(r1.customerManagerNum-IFNULL(r2.customerManagerNum ,0)) customerManagerNum,
                r1.abbreviation
                FROM
                (SELECT sr.store_id storeId,sr.store_name storeName,ifnull(sr.company_id,-1) companyId,sr.company_name companyName,
                ifnull(sr.area_id,-1) areaId,sr.area_name areaName,ifnull(sr.develop_salesman_id,-1)
                developSalesmanId,sr.develop_salesman_name developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum,sr.abbreviation
                FROM ${inDto.endDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0 and sr.status !=0
                <if test="inDto.endDate !=null and inDto.endDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.endDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id order by sr.area_id desc
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id order by sr.company_id desc
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
                ) r1 left join
                (SELECT sr.store_id storeId,sr.store_name storeName,ifnull(sr.company_id,-1) companyId,sr.company_name companyName,
                ifnull(sr.area_id,-1) areaId,sr.area_name areaName,ifnull(sr.develop_salesman_id,-1)
                developSalesmanId,sr.develop_salesman_name developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum,sr.abbreviation
                FROM ${inDto.startDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0 and sr.status !=0
                <if test="inDto.startDate !=null and inDto.startDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.startDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id order by sr.area_id desc
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id order by sr.company_id desc
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
                ) r2
                on
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        r1.areaId = r2.areaId
                    </when>
                    <when test="inDto.type ==2">
                        r1.companyId = r2.companyId
                    </when>
                    <when test="inDto.type ==3">
                        r1.developSalesmanId = r2.developSalesmanId
                    </when>
                    <when test="inDto.type ==4">
                        r1.storeId = r2.storeId
                    </when>
                </choose>
            </otherwise>
        </choose>

        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!--合计-->
    <select id="storeSalesmanSum" resultType="com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportOutDto">
        SELECT '合计' areaName,SUM(storeNum) storeNum,SUM(shopownerNum) shopownerNum,SUM(adviserNum)
        adviserNum,SUM(customerManagerNum) customerManagerNum
        FROM (
        <choose>
            <when test="inDto.isCumulative ==null or inDto.isCumulative !=0 "><!--累计-->
                SELECT sr.store_id storeId,sr.store_name storeName,sr.company_id companyId,sr.company_name companyName,
                sr.area_id areaId,sr.area_name areaName,sr.develop_salesman_id
                developSalesmanId,sr.develop_salesman_name developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum
                FROM ${inDto.reportDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0  and sr.status !=0
                <if test="inDto.reportDate !=null and inDto.reportDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.reportDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
            </when>
            <otherwise><!--变动-->
                SELECT
                r1.storeId,r1.storeName,r1.companyId,r1.companyName,r1.areaId,r1.areaName,r1.developSalesmanId,r1.developSalesmanName,
                r1.reportDate,
                (r1.storeNum-IFNULL(r2.storeNum ,0)) storeNum,(r1.shopownerNum -IFNULL(r2.shopownerNum ,0)) shopownerNum,
                (r1.adviserNum-IFNULL(r2.adviserNum ,0)) adviserNum,(r1.customerManagerNum-IFNULL(r2.customerManagerNum ,0)) customerManagerNum
                FROM
                (SELECT sr.store_id storeId,sr.store_name storeName,sr.company_id companyId,sr.company_name companyName,
                sr.area_id areaId,sr.area_name areaName,sr.develop_salesman_id
                developSalesmanId,sr.develop_salesman_name developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum
                FROM ${inDto.endDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0 and sr.status !=0
                <if test="inDto.endDate !=null and inDto.endDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.endDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
                ) r1 LEFT JOIN
                (SELECT sr.store_id storeId,sr.store_name storeName,sr.company_id companyId,sr.company_name companyName,
                sr.area_id areaId,sr.area_name areaName,sr.develop_salesman_id
                developSalesmanId,sr.develop_salesman_name developSalesmanName,
                sr.report_date reportDate,SUM(sr.store_num) storeNum,SUM(sr.shopowner_num)
                shopownerNum,SUM(sr.adviser_num) adviserNum,
                SUM(sr.customer_manager_num) customerManagerNum
                FROM ${inDto.startDateSchemaTableName}<!--czw加密已处理1--> sr
                <if test="inDto.keyWords !=null and inDto.keyWords.size>0">
                    INNER JOIN orgcenter.t_store<!--czw加密已处理--> s ON s.is_deleted=0 AND sr.store_id=s.org_id
                </if>
                <include refid="storeSalesmanReportWHERE"/>
                AND sr.is_reference=0 and sr.status !=0
                <if test="inDto.startDate !=null and inDto.startDate !=''"><!--统计时间-->
                    AND sr.report_date=DATE_FORMAT(#{inDto.startDate},'%Y-%m-%d')
                </if>
                <if test="inDto.isVirtual !=null"><!--是否虚拟门店-->
                    AND sr.is_virtual=#{inDto.isVirtual}
                </if>
                <if test="inDto.areaId !=null"><!--大区id-->
                    AND sr.area_id=#{inDto.areaId}
                </if>
                <if test="inDto.companyId !=null"><!--分公司id-->
                    AND sr.company_id=#{inDto.companyId}
                </if>
                <if test="inDto.developSalesmanId !=null"><!--业务id-->
                    AND sr.develop_salesman_id=#{inDto.developSalesmanId}
                </if>
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        GROUP BY sr.area_id
                    </when>
                    <when test="inDto.type ==2">
                        GROUP BY sr.company_id
                    </when>
                    <when test="inDto.type ==3">
                        GROUP BY sr.develop_salesman_id order by sr.develop_salesman_id desc
                    </when>
                    <when test="inDto.type ==4">
                        GROUP BY sr.store_id
                    </when>
                </choose>
                ) r2
                on
                <choose> <!--type:1：大区；2：公司；3：门店业务主管；4：门店-->
                    <when test="inDto.type ==1">
                        r1.areaId = r2.areaId
                    </when>
                    <when test="inDto.type ==2">
                        r1.companyId = r2.companyId
                    </when>
                    <when test="inDto.type ==3">
                        r1.developSalesmanId = r2.developSalesmanId
                    </when>
                    <when test="inDto.type ==4">
                        r1.storeId = r2.storeId
                    </when>
                </choose>
            </otherwise>
        </choose>
        ) t
    </select>

    <!--门店及人员统计报表-切换-->
    <select id="storeSalesmanReportChange"
            resultType="com.fotile.orgcenter.store.pojo.dto.StoreSalesmanReportChangeOutDto">
        <choose>
            <when test="inDto.type ==1"><!--大区-->
                SELECT DISTINCT c.area id,c.area_name name
                FROM orgcenter.t_company c
                WHERE c.is_deleted=0 AND c.area IS NOT NULL
                <if test="inDto.name !=null and inDto.name !='' "><!--名称-->
                    AND c.area_name LIKE CONCAT('%',#{inDto.name},'%')
                </if>
                <if test="companyAuthorList != null and companyAuthorList.size > 0"><!--大区、分公司根据公司数据权限查询-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </if>
            </when>
            <when test="inDto.type==2"><!--公司-->
                SELECT DISTINCT o.id id,o.name name
                FROM orgcenter.t_company c
                INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND c.org_id=o.id
                WHERE c.is_deleted=0
                <if test="inDto.name !=null and inDto.name !='' "><!--名称-->
                    AND o.name LIKE CONCAT('%',#{inDto.name},'%')
                </if>
                <if test="companyAuthorList != null and companyAuthorList.size > 0"><!--大区、分公司根据公司数据权限查询-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </if>
            </when>
            <when test="inDto.type==3"><!--门店业务主管-->
                SELECT DISTINCT s.develop_salesman_id id,AES_DECRYPT(UNHEX(s.develop_salesman_name),#{encryptPassword}) name
                FROM orgcenter.t_store<!--czw加密已处理--> s
                WHERE s.is_deleted=0 AND s.develop_salesman_id IS NOT NULL
                <if test="inDto.name !=null and inDto.name !='' "><!--名称-->
                    AND s.develop_salesman_name LIKE CONCAT('%',#{inDto.name},'%')
                </if>
                <if test="storeAuthorList != null and storeAuthorList.size > 0 and (inDto.type==3 or inDto.type==4)"><!--门店业务主管、门店根据门店数据权限查询-->
                    AND s.org_id IN
                    <foreach collection="storeAuthorList" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </if>
                <if test="companyAuthorList != null and companyAuthorList.size > 0"><!--大区、分公司根据公司数据权限查询-->
                    AND s.company_id IN
                    <foreach collection="companyAuthorList" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </if>
            </when>
        </choose>
    </select>

    <select id="storeListByOrgIds" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select distinct store_type from t_store<!--czw加密已处理--> where is_deleted = 0
        <if test="orgIds != null and orgIds.size() > 0">
            AND org_id IN
            <foreach collection="orgIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and store_type is not null
    </select>

    <select id="storeTypeIdsByOrgIds" resultType="java.lang.Integer">
        select distinct store_type from t_store<!--czw加密已处理--> where is_deleted = 0
        <if test="orgIds != null and orgIds.size() > 0">
            AND org_id IN
            <foreach collection="orgIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and store_type is not null
    </select>

    <!--- ********************* 门店系统互通接口 added by zd ********************** -->
    <update id="updateStoreStatus"
            parameterType="com.fotile.orgcenter.store.pojo.dto.UpdateStoreStatusDto">
        update orgcenter.t_store<!--czw加密已处理版本-->
       set
         modified_date=now(),
        modified_by=#{edit.operatorId},`version`=`version`+1,
        <if test='edit.status != null and edit.status == "1" '>
            open_date=(IF(open_date is null, now(), open_date)),
            close_start_date= NULL,
        </if>
        <if test='edit.status != null and edit.status == "0" and edit.isCleanCloseDate'>
            close_date=now(),
        </if>
        <if test='edit.status != null and edit.status == "3" '>
            close_start_date=now(),
        </if>
        `status`=#{edit.status}
        where id=#{edit.storeId}
    </update>

    <select id="getStoreCompanyOrgId"
            parameterType="java.lang.Long"
            resultType="java.lang.Long">
        select org_id
        from orgcenter.t_company
        where id = #{companyId}
    </select>

    <select id="getToMdmStoreInfoByCode" resultType="com.fotile.orgcenter.store.pojo.dto.MdmParamsDto"
            parameterType="string">
        SELECT o.`code`                                zzmdbm,
               s.org_id                                orgId,
               o.name                                  zzmdmc,
               cc.code                                 zzmdqd,
               s.is_virtual                            zzsfxnmd,
               s.`status`                              zzmdzt,
               ifnull(s.decorate_status, '')        as zzmdzxjd,
             <!--  ifnull(sfn.`character`, '')          as zzmdxz,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END
        ) as zzmdxz,
               ifnull(left(s.provicen_id, 2), '')   as zzsf,
               ifnull(left(s.city_id, 4), '')       as zzcs,
               ifnull(left(s.county_id, 6), '')     as zzqx,
               ifnull(s.address, '')                as zzxxdz,
               ifnull(s.manager_name, '')           as zzfzr,
               ifnull(s.manager_phone, '')          as zzlxfs,
               ifnull(s.email, '')                  as zzyx,
               ifnull(s.tel, '')                    as zzmdzjdh,
               ifnull(s.store_biz_type, '')         as zzmdywlx,
               ifnull(s.property_right, '')         as zzmdcq,
               ifnull(s.lease_term, '')             as zzzynx,
               ifnull(s.longitude, '')              as jd,
               ifnull(s.latitude, '')               as wd,
               ifnull(s.store_sub_channel_code, '') as zzmdqdxf,
               ifnull(s.abbreviation,'')            as zmdjc,
               ifnull(s.shopping_start,'')          as zyyqssj,
               ifnull(s.shopping_end,'')            as zyyjzsj,
               ifnull(s.address2,'')                as zxsdz,
               ifnull(s.channel,'')                 as zzsqd,
               ifnull(s.whole_usable_area,'')         as zsjzzmj,
<!--        //                zzmdssbsc		门店所属办事处-->
<!--        //        zzqysj		启用时间-->
<!--        //        zztysj		停用时间-->
<!--        //        zznzj			年租金-->
<!--        //        zzmdlx		门店类型-->
                ifnull(tc.code,'')  as zzmdssbsc,
                ifnull(s.open_date,'')  as zzqysj,
                ifnull(s.close_date,'')  as zztysj,
                ifnull(s.annual_rent,'')  as zznzj,
                ifnull(s.store_level,'')  as zzmdlx
               <!--,ifnull((
                    case when s.open_type=1 and s.is_virtual!=1
                            then (SELECT tsfn.store_size FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
                        when s.open_type=2 and s.is_virtual!=1
                            then (SELECT tsfc.store_size FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
                    end
                ),'') as zmdcc-->
        FROM orgcenter.t_org o
             LEFT JOIN orgcenter.t_store<!--czw加密已处理--> s on (o.id = s.org_id AND s.is_deleted = 0)
        LEFT JOIN orgcenter.t_org<!--czw加密已处理--> tc on (tc.id = s.company_id AND tc.is_deleted = 0)

        LEFT JOIN orgcenter.t_store_facilities_noncommunity sfn on (s.id = sfn.store_id and sfn.is_deleted = 0)
             left JOIN orgcenter.channel_category cc on (s.store_type = cc.id and cc.is_deleted = 0)
        WHERE o.is_deleted = 0
        <choose>
            <when test="storeOrgId != null">
                and o.id=#{storeOrgId}
            </when>
            <when test="storeId != null">
                and s.id=#{storeId}
            </when>
            <when test="code != null and code != ''">
                AND o.`code` = #{code}
            </when>
            <otherwise>
                AND o.id=0
            </otherwise>
        </choose>

          AND o.type = 3
        group by s.id
    </select>


    <select id="getToMdmStoreInfoList"
            resultType="com.fotile.orgcenter.store.pojo.dto.MdmParamsDto">
        SELECT o.`code`                                zzmdbm,
        s.org_id                                orgId,
        o.name                                  zzmdmc,
        cc.code                                 zzmdqd,
        s.is_virtual                            zzsfxnmd,
        s.`status`                              zzmdzt,
        ifnull(s.decorate_status, '')        as zzmdzxjd,
      <!--  ifnull(sfn.`character`, '')          as zzmdxz,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END
        ) as zzmdxz,
        ifnull(left(s.provicen_id, 2), '')   as zzsf,
        ifnull(left(s.city_id, 4), '')       as zzcs,
        ifnull(left(s.county_id, 6), '')     as zzqx,
        ifnull(s.address, '')                as zzxxdz,
        ifnull(s.manager_name, '')           as zzfzr,
        ifnull(s.manager_phone, '')          as zzlxfs,
        ifnull(s.email, '')                  as zzyx,
        ifnull(s.tel, '')                    as zzmdzjdh,
        ifnull(s.store_biz_type, '')         as zzmdywlx,
        ifnull(s.property_right, '')         as zzmdcq,
        ifnull(s.lease_term, '')             as zzzynx,
        ifnull(s.longitude, '')              as jd,
        ifnull(s.latitude, '')               as wd,
        ifnull(s.store_sub_channel_code, '') as zzmdqdxf,
        ifnull(s.abbreviation,'')            as zmdjc,
        ifnull(s.shopping_start,'')          as zyyqssj,
        ifnull(s.shopping_end,'')            as zyyjzsj,
        ifnull(s.address2,'')                as zxsdz,
        ifnull(s.channel,'')                 as zzsqd,
        ifnull(s.eps_usable_area,'')         as zsjzzmj,
        ifnull((
            SELECT d.`code`
            from orgcenter.t_distributor d
            inner join orgcenter.t_distributor_mapping dm
                on d.id = dm.distributor_id
                    and d.is_deleted = 0
                    and dm.is_deleted = 0
                    and dm.type = 3
            where dm.org_id = s.org_id
            limit 1
        ),'') as zzsskh,
        ifnull(tc.code,'')  as zzmdssbsc,
        ifnull(s.open_date,'')  as zzqysj,
        ifnull(s.close_date,'')  as zztysj,
        ifnull(s.annual_rent,'')  as zznzj,
        ifnull(s.store_level,'')  as zzmdlx
        FROM orgcenter.t_org o
        LEFT JOIN orgcenter.t_store<!--czw加密已处理--> s on (o.id = s.org_id AND s.is_deleted = 0)
        LEFT JOIN orgcenter.t_org<!--czw加密已处理--> tc on (tc.id = s.company_id AND tc.is_deleted = 0)

        LEFT JOIN orgcenter.t_store_facilities_noncommunity sfn on (s.id = sfn.store_id and sfn.is_deleted = 0)
        left JOIN orgcenter.channel_category cc on (s.store_type = cc.id and cc.is_deleted = 0)
        WHERE o.is_deleted = 0 AND o.type = 3
        <choose>
            <when test="storeOrgIds != null and storeOrgIds.size() != 0">
                and o.id in
                <foreach collection="storeOrgIds" item="storeOrgId" open="(" separator="," close=")">
                    #{storeOrgId}
                </foreach>
            </when>
            <when test="storeIds != null and storeIds.size() != 0 != null">
                and s.id in
                <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <when test="codes != null and codes.size() != 0">
                AND o.`code` in
                <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </when>
            <otherwise>
                AND o.id=0
            </otherwise>
        </choose>
        group by s.id
    </select>

    <select id="getDistributorCodeByOrgId" parameterType="long" resultType="string">
        SELECT d.`code`
        from orgcenter.t_distributor d,
             orgcenter.t_distributor_mapping dm
        where d.id = dm.distributor_id
          and d.is_deleted = 0
          and dm.is_deleted = 0
          and dm.type = 3
          and dm.org_id = #{orgId}
    </select>

    <select id="getStoreStatus" resultType="java.lang.String">
        select status
        from orgcenter.t_store<!--czw加密已处理-->
        where 1=1
        <choose>
            <when test="storeId != null">
                and id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and org_id=#{storeOrgId}
            </when>
            <otherwise>
                and id=0
            </otherwise>
        </choose>
    </select>

    <select id="getStoreName" resultType="java.lang.String">
        select ifnull(org.`name`,ts.abbreviation) as storeName
        from orgcenter.t_store<!--czw加密已处理--> ts
        inner join orgcenter.t_org org on ts.org_id=org.id
        where 1=1
        <choose>
            <when test="storeId != null">
                and ts.id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and ts.org_id=#{storeOrgId}
            </when>
            <otherwise>
                and ts.id=0
            </otherwise>
        </choose>
    </select>

    <select id="getStoreDecorateStatus" resultType="java.lang.Integer">
        select decorate_status
        from orgcenter.t_store<!--czw加密已处理-->
        where 1=1
        <choose>
            <when test="storeId != null">
                and id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and org_id=#{storeOrgId}
            </when>
            <otherwise>
                and id=0
            </otherwise>
        </choose>
    </select>
    <select id="getDistributorCodeByStoreId" resultType="java.lang.String">
        SELECT
        td.`code`
        FROM
        orgcenter.t_store<!--czw加密已处理--> ts
        LEFT JOIN orgcenter.t_distributor_mapping tdm ON tdm.org_id = ts.org_id
        AND tdm.type = 3
        LEFT JOIN orgcenter.t_distributor td ON td.id = tdm.distributor_id
        WHERE
        ts.id = #{storeId}
        AND tdm.is_deleted = 0;
    </select>

    <!--更新门店装修状态(1:装修,2:验收)-->
    <update id="updateStoreDecorateStatus"
            parameterType="com.fotile.orgcenter.store.pojo.dto.UpdateStoreDecorateStatusDto">
        update orgcenter.t_store<!--czw加密已处理版本-->
        set decorate_status=#{edit.decorateStatus},
        <if test="edit.status != null and edit.status !=''">
            `status`=#{edit.status},
        </if>
        <if test='edit.status != null and edit.status == "1" '>
            open_date=(IF(open_date is null, now(), open_date)),
        </if>
        modified_by=#{edit.operatorId},
            modified_date=now(),`version`=`version`+1
        where id = #{edit.storeId};
    </update>


    <select id="getToMdmStoreInfoByCodes" resultType="com.fotile.orgcenter.store.pojo.dto.MdmParamsDto"
            parameterType="string">
        SELECT o.`code`                                zzmdbm,
        s.org_id                                orgId,
        o.name                                  zzmdmc,
        cc.code                                 zzmdqd,
        s.is_virtual                            zzsfxnmd,
        s.`status`                              zzmdzt,
        ifnull(s.decorate_status, '')        as zzmdzxjd,
       <!-- ifnull(sfn.`character`, '')          as zzmdxz,-->
        (
        CASE cc.`code`
        WHEN 'L028' THEN
        (select `character` from orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0 )
        ELSE
        (select `character` from orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0 )
        END
        ) as zzmdxz,
        ifnull(left(s.provicen_id, 2), '')   as zzsf,
        ifnull(left(s.city_id, 4), '')       as zzcs,
        ifnull(left(s.county_id, 6), '')     as zzqx,
        ifnull(s.address, '')                as zzxxdz,
        ifnull(s.manager_name, '')           as zzfzr,
        ifnull(s.manager_phone, '')          as zzlxfs,
        ifnull(s.email, '')                  as zzyx,
        ifnull(s.tel, '')                    as zzmdzjdh,
        ifnull(s.store_biz_type, '')         as zzmdywlx,
        ifnull(s.property_right, '')         as zzmdcq,
        ifnull(s.lease_term, '')             as zzzynx,
        ifnull(s.longitude, '')              as jd,
        ifnull(s.latitude, '')               as wd,
        ifnull(s.store_sub_channel_code, '') as zzmdqdxf,
        ifnull(s.abbreviation,'')            as zmdjc,
        ifnull(s.shopping_start,'')          as zyyqssj,
        ifnull(s.shopping_end,'')            as zyyjzsj,
        ifnull(s.address2,'')                as zxsdz,
        ifnull(s.channel,'')                 as zzsqd,
        ifnull((
        case when s.open_type=1 and s.is_virtual!=1
        then (SELECT tsfn.store_size FROM orgcenter.t_store_facilities_noncommunity tsfn WHERE tsfn.store_id=s.id AND tsfn.is_deleted=0)
        when s.open_type=2 and s.is_virtual!=1
        then (SELECT tsfc.store_size FROM orgcenter.t_store_facilities_community tsfc WHERE tsfc.store_id=s.id AND tsfc.is_deleted=0)
        end
        ),'') as zmdcc,
        ifnull(s.eps_usable_area,'')         as zsjzzmj,
        ifnull(tc.code,'')  as zzmdssbsc,
        ifnull(s.open_date,'')  as zzqysj,
        ifnull(s.close_date,'')  as zztysj,
        ifnull(s.annual_rent,'')  as zznzj,
        ifnull(s.store_level,'')  as zzmdlx,
        FROM orgcenter.t_org o
        LEFT JOIN orgcenter.t_store<!--czw加密已处理--> s on (o.id = s.org_id AND s.is_deleted = 0)
        LEFT JOIN orgcenter.t_org<!--czw加密已处理--> tc on (tc.id = s.company_id AND tc.is_deleted = 0)

        LEFT JOIN orgcenter.t_store_facilities_noncommunity sfn on (s.id = sfn.store_id and sfn.is_deleted = 0)
        left JOIN orgcenter.channel_category cc on (s.store_type = cc.id and cc.is_deleted = 0)
        WHERE o.is_deleted = 0
        <choose>
            <when test="endCode != null and endCode != '' and startCode != null and  startCode != ''">
                AND o.`code` between #{startCode} and #{endCode}
            </when>
            <when test="(endCode == null or endCode == '' ) and startCode != null and  startCode != ''">
                AND o.`code` = #{startCode}
            </when>
        </choose>
        AND o.type =3
        group by s.id
        limit #{offset,jdbcType=INTEGER},#{size,jdbcType=INTEGER}
    </select>

    <select id="getToMdmStoreInfoByCodesCount" resultType="int">
        select count(1)
        FROM
        orgcenter.t_org o
        WHERE
        o.is_deleted = 0
        <choose>
            <when test="endCode != null and endCode != '' and startCode != null and  startCode != ''">
                AND o.`code` between #{startCode} and #{endCode}
            </when>
            <when test="(endCode == null or endCode == '' ) and startCode != null and  startCode != ''">
                AND o.`code` = #{startCode}
            </when>
        </choose>

        AND o.type =3
    </select>

    <!--根据公司orgId查询公司/大区账号对应关系-->
    <select id="findByCompanyOrgId" resultType="java.lang.String">
        SELECT u.username<!--账号和公司映射关系-->
        FROM orgcenter.t_company_user_mapping u
        WHERE u.is_deleted=0 AND u.type=1 AND u.org_id=#{orgId}

        UNION

        SELECT u.username   <!--账号和大区映射关系-->
        FROM orgcenter.t_company_user_mapping u
        INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND u.org_id=c.area
        WHERE u.is_deleted=0 AND u.type=2 AND c.org_id=#{orgId}
    </select>


    <!--变更门店所属客户 added by zd,2021/05/18 0526迭代-->
    <update id="updateStoreName"
        parameterType="com.fotile.orgcenter.store.pojo.dto.UpdateStoreNameDto">
        update orgcenter.t_org
        <set>
            <choose>
                <when test="edit.storeName != null and edit.storeName != ''">
                    name=#{edit.storeName},
                </when>
                <otherwise>
                    name=concat(ifnull(name,''),'(清退)'),
                </otherwise>
            </choose>
        </set>
        <where>
            <choose>
                <when test="edit.storeOrgId != null">
                    id=#{edit.storeOrgId}
                </when>
                <when test="edit.storeCode != null and edit.storeCode != ''">
                    code=#{edit.storeCode} and type=3
                </when>
                <otherwise>
                    id=0
                </otherwise>
            </choose>
        </where>
    </update>

    <update id="updateStoreDistributor"
            parameterType="com.fotile.orgcenter.store.pojo.dto.UpdateStoreDistributorDto">
        update orgcenter.t_org
        set name=concat(ifnull(name, ''), '(清退)')
        where id = #{edit.storeOrgId};


    </update>


    <!-- 门店组织树图 -->
    <select id="orgTreeTotal" resultType="int">
        SELECT COUNT(r.id)
        FROM (
        <choose>
            <when test="inDto.type==1"><!--大区-->
                SELECT c.area id
                FROM orgcenter.t_salesman<!--czw加密已处理-->  s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限下的大区-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '大区门店业务发展经理','办事处门店业务发展经理','办事处门店业务发展主管'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND c.area_name LIKE CONCAT('%',#{inDto.keyWord},'%')
                </if>
                AND c.area IS NOT NULL
                GROUP BY c.area,c.area_name
            </when>
            <when test="inDto.type==2"><!--公司-->
                SELECT c.org_id id
                FROM orgcenter.t_salesman<!--czw加密已处理-->  s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=1 AND c.org_id=o.id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.area=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '办事处门店业务发展经理','办事处门店业务发展主管'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND c.area_name LIKE CONCAT('%',#{inDto.keyWord},'%')
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND o.name LIKE CONCAT('%',#{inDto.keyWord},'%')
                </if>
                GROUP BY c.org_id
            </when>
            <when test="inDto.type==3"><!--门店业务主管-->
                SELECT c.develop_salesman_id id
                FROM orgcenter.t_salesman<!--czw加密已处理-->  s
                INNER JOIN orgcenter.t_store<!--czw加密已处理-->  c ON c.is_deleted=0 AND s.company_id=c.company_id
                AND IFNULL(c.develop_salesman_id,'')!='' AND s.id=c.develop_salesman_id
                WHERE s.is_deleted=0 AND s.status=1 AND c.status IN(1,2,3 ) AND c.is_virtual=0
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND c.org_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.company_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND c.develop_salesman_name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
                GROUP BY c.develop_salesman_id
            </when>
            <when test="inDto.type==4"><!--门店-->
                SELECT s.org_id id
                FROM orgcenter.t_store<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND s.org_id=o.id AND o.type=3
                WHERE s.is_deleted=0 AND s.status IN(1,2,3) AND s.is_virtual=0
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND s.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND s.org_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND s.develop_salesman_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND (o.name LIKE CONCAT('%',#{inDto.keyWord},'%') OR s.abbreviation LIKE CONCAT('%',#{inDto.keyWord},'%'))
                </if>
            </when>
            <when test="inDto.type==5"><!--业务员-->
                SELECT s.id id
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND s.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND s.store_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND s.store_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
            <when test="inDto.type==6"><!--大区门店业务发展经理-->
                SELECT s.id id
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.area=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '大区门店业务发展经理'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
            <when test="inDto.type==7"><!--办事处门店业务发展经理-->
                SELECT s.id id
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1

                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.org_id=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '办事处门店业务发展经理'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
        </choose>
        ) r
    </select>
    <select id="orgTree" resultType="com.fotile.orgcenter.store.pojo.dto.OrgTreeOutDto">
        <choose>
            <when test="inDto.type==1"><!--大区-->
                SELECT c.area id,c.area_name name,COUNT(s.id) num
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限下的大区-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '大区门店业务发展经理','办事处门店业务发展经理','办事处门店业务发展主管'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND c.area_name LIKE CONCAT('%',#{inDto.keyWord},'%')
                </if>
                AND c.area IS NOT NULL
                GROUP BY c.area,c.area_name
            </when>
            <when test="inDto.type==2"><!--公司-->
                SELECT c.org_id id,o.name name,COUNT(s.id) num
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=1 AND c.org_id=o.id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.area=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '办事处门店业务发展经理','办事处门店业务发展主管'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND o.name LIKE CONCAT('%',#{inDto.keyWord},'%')
                </if>
                GROUP BY c.org_id
            </when>
            <when test="inDto.type==3"><!--门店业务主管-->
                SELECT c.develop_salesman_id id,
                AES_DECRYPT(UNHEX(c.develop_salesman_name), #{encryptPassword}) name,
                ( SELECT d.value_name
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.id=s.station) stationName,
                AES_DECRYPT(UNHEX(s.phone), #{encryptPassword}) phone,
                COUNT(c.id) num
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_store<!--czw加密已处理--> c ON c.is_deleted=0 AND s.company_id=c.company_id
                AND IFNULL(c.develop_salesman_id,'')!='' AND s.id=c.develop_salesman_id
                WHERE s.is_deleted=0 AND s.status=1 AND c.status IN(1,2,3 ) AND c.is_virtual=0
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND c.org_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.company_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND c.develop_salesman_name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
                GROUP BY c.develop_salesman_id
            </when>
            <when test="inDto.type==4"><!--门店-->
                SELECT s.org_id id,
                s.abbreviation name,
                (SELECT COUNT(sl.id)
                FROM orgcenter.t_salesman<!--czw加密已处理--> sl
                    WHERE sl.is_deleted=0 AND sl.store_id=s.org_id AND sl.status=1) num
                FROM orgcenter.t_store<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND s.org_id=o.id AND o.type=3
                WHERE s.is_deleted=0 AND s.status IN(1,2,3) AND s.is_virtual=0
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND s.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND s.org_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND s.develop_salesman_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND (o.name LIKE CONCAT('%',#{inDto.keyWord},'%') OR s.abbreviation LIKE CONCAT('%',#{inDto.keyWord},'%'))
                </if>
            </when>
            <when test="inDto.type==5"><!--业务员-->
                SELECT s.id id,
                AES_DECRYPT(UNHEX(s.name), #{encryptPassword}) name,
                (SELECT d.value_name
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.id=s.station) stationName,
                AES_DECRYPT(UNHEX(s.phone), #{encryptPassword}) phone
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND s.company_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="storeAuthorList !=null and storeAuthorList.size>0"><!--门店数据权限-->
                    AND s.store_id IN
                    <foreach collection="storeAuthorList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND s.store_id=#{inDto.parentId}
                </if>
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
            <when test="inDto.type==6"><!--大区门店业务发展经理-->
                SELECT s.id id,
                AES_DECRYPT(UNHEX(s.name), #{encryptPassword}) name,
                (SELECT d.value_name
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.id=s.station) stationName,
                AES_DECRYPT(UNHEX(s.phone), #{encryptPassword}) phone
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.area=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '大区门店业务发展经理'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
            <when test="inDto.type==7"><!--办事处门店业务发展经理-->
                SELECT s.id id,
                AES_DECRYPT(UNHEX(s.name), #{encryptPassword}) name,
                (SELECT d.value_name
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.id=s.station) stationName,
                AES_DECRYPT(UNHEX(s.phone), #{encryptPassword}) phone
                FROM orgcenter.t_salesman<!--czw加密已处理--> s
                INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
                WHERE s.is_deleted=0 AND s.status=1
                <if test="companyAuthorList !=null and companyAuthorList.size>0"><!--公司数据权限-->
                    AND c.org_id IN
                    <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                        #{company.orgId}
                    </foreach>
                </if>
                <if test="inDto.parentId !=null">
                    AND c.org_id=#{inDto.parentId}
                </if>
                AND s.station IN (
                SELECT d.id
                FROM systemcenter.dic d
                WHERE d.is_deleted=0 AND d.type_code='gw' AND d.value_name IN (
                '办事处门店业务发展经理'
                )
                )
                <if test="inDto.keyWord !=null and inDto.keyWord !=''">
                    AND s.name = #{inDto.keyWord, typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}<!--czw加密处理模糊-->
                </if>
            </when>
        </choose>
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>
    <select id="queryStoreByIds" resultType="com.fotile.orgcenter.store.pojo.dto.QueryStoreByIdsVO">
        <!--        private String storeTypeCode ;-->
        <!--        private String storeTypeName ;-->
        <!--        private String storeKeyWord ;-->
        <!--        private String storeTypeId ;-->
        select ts.key_word storeKeyWord,cc.name storeTypeName,cc.code storeTypeCode,cc.id storeTypeId,ts.org_id orgId
        from orgcenter.t_store<!--czw加密已处理--> ts
        left join orgcenter.channel_category cc on ts.store_type = cc.id
        where ts.org_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="queryStoreByModifiedDate" resultType="com.fotile.orgcenter.store.pojo.dto.QueryStoreByModifiedDateVO">
        select ts.key_word storeKeyWord,
               cc.name     storeTypeName,
               cc.code     storeTypeCode,
               cc.id       storeTypeId,
               ts.org_id   orgId
        from orgcenter.t_store<!--czw加密已处理--> ts
                 left join orgcenter.channel_category cc on ts.store_type = cc.id
        where ts.modified_date >= #{startDate,jdbcType=TIMESTAMP}
    </select>

    <!--门店批量添加/删除标签-->
    <update id="batchAddOrDelKeyWords">
        UPDATE orgcenter.t_store<!--czw加密已处理版本-->
        <set>
            modified_by=#{modifiedBy},modified_date=NOW(),`version`=`version`+1
            <choose>
                <when test="inDto.flag !=null and inDto.flag==1"><!--批量新增门店标签-->
                    ,key_word=CASE WHEN IFNULL(key_word,'')!='' THEN CONCAT(key_word,',',#{inDto.keyWords}) ELSE
                    #{inDto.keyWords} END
                </when>
                <when test="inDto.flag !=null and inDto.flag==2 and inDto.keyWords !=null and inDto.keyWords !=''"><!--批量删除门店标签-->
                    <foreach collection="inDto.keyWords.split(',')" item="keyWord">
                        ,key_word=REPLACE(key_word,CONCAT(#{keyWord},','),''),key_word=REPLACE(key_word,CONCAT(',',#{keyWord}),''),key_word=REPLACE(key_word,#{keyWord},'')
                    </foreach>
                </when>
            </choose>
        </set>
        WHERE is_deleted=0 AND org_id IN
        <foreach collection="inDto.orgIdList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </update>
    <update id="updateDoorSize">
        update orgcenter.t_store_facilities_community
        set door_size=#{dto.doorSize}
        where store_id=#{dto.id}
    </update>
    <update id="updateStoreDate">
        update orgcenter.t_store<!--czw加密已处理版本-->
        set modified_date=now(),`version`=`version`+1,
            <if test='dto.status != null and dto.status == "1" '>
                open_date=now(),
            </if>
            <if test='dto.status != null and dto.status == "0" '>
                close_date=now(),
            </if>
        modified_date=now()
        where id=#{dto.id}
    </update>
    <update id="updateStorePlanCode">
        update orgcenter.t_store<!--czw加密已处理版本--> set plan_code=#{planCode},`version`=`version`+1
        where is_deleted=0
        and id in
        <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
            #{storeId}
        </foreach>
    </update>
    <update id="updateStoreOpenInfo">
        UPDATE orgcenter.t_store<!--czw加密已处理版本-->
        SET
         <if test="edit.propertyRight !=null">
             property_right=#{edit.propertyRight},
         </if>
         <if test="edit.leaseTerm !=null">
             lease_term=#{edit.leaseTerm},
         </if>
        <if test="edit.annualRent !=null">
            annual_rent=#{edit.annualRent},
        </if>
        <if test="edit.decorateStatus != null">
            decorate_status=#{edit.decorateStatus},
        </if>
        <if test='edit.status != null and edit.status !=""'>
            `status`=#{edit.status},
        </if>
        modified_date=now(),`version`=`version`+1
          WHERE id=#{edit.storeId};
    </update>
    <update id="updateByWithLogDto">
        UPDATE orgcenter.t_store<!--czw加密已处理版本-->
            <set>
               <if test="usableArea !=null">
                   usable_area=#{usableArea},
               </if>
                <if test="coverurl !=null and coverurl!=''">
                    coverurl=#{coverurl},
                </if>
                <if test="sceneSuit != null">
                    scene_suit=#{sceneSuit},
                </if>
                <!--   <if test="wholeUsableArea !=null">
                       whole_usable_area=#{wholeUsableArea},
                   </if>-->
               <choose>
                   <when test="wholeUsableArea !=null">
                       whole_usable_area=#{wholeUsableArea},
                   </when>
                   <otherwise>
                   <if test="isUpdateWholeUsableArea !=null and isUpdateWholeUsableArea">
                       whole_usable_area=#{wholeUsableArea},
                   </if>
                   </otherwise>
               </choose>

                   store_level=#{storeLevel},`version`=`version`+1
               </set>
               where id=#{storeId}
       </update>

       <select id="getStoreChannelId"
               resultType="java.lang.Long">
           select id
           from orgcenter.channel_category
           where is_deleted = 0
             and parent_id = #{channelSubdivideId}
             and `level` = 3
             and `code` = #{storeChannelCode}
           limit 1
       </select>

       <select id="getStoreAbbreviation" resultType="java.lang.String">
           select ifnull(ts.abbreviation, org.`name`) as storeName
           from orgcenter.t_store<!--czw加密已处理--> ts
        inner join orgcenter.t_org org on ts.is_deleted=0 and org.is_deleted=0 and ts.org_id=org.id
        where 1=1
        <choose>
            <when test="storeId != null">
                and ts.id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and ts.org_id=#{storeOrgId}
            </when>
            <when test="storeCode != null and storeCode != ''">
                and org.code=#{storeCode}
            </when>
            <otherwise>
                and ts.id=0
            </otherwise>
        </choose>
        limit 1;
    </select>

    <select id="queryStoreByDevelopSalesmanId"
            resultType="com.fotile.orgcenter.store.pojo.vo.QueryStoreByDevelopSalesmanIdVO">
        select org_id from orgcenter.t_store<!--czw加密已处理-->
        where develop_salesman_id = #{developSalesmanId,jdbcType=BIGINT}
    </select>

    <select id="getDevelopSalesmanStoreList"
            parameterType="com.fotile.orgcenter.store.pojo.dto.DevelopSalesmanStoreInDto"
            resultType="com.fotile.orgcenter.store.pojo.dto.DevelopSalesmanStoreOutDto">
        select
            ts.id as storeId,
            ts.abbreviation,
            org.code as storeCode,
            org.name as storeName,
            ts.org_id as storeOrgId,
            ts.develop_salesman_id,
            ts.develop_salesman_code,
            ts.develop_salesman_name,
            ts.develop_salesman_phone
        from orgcenter.t_store<!--czw加密已处理--> ts
            inner join orgcenter.t_org org on ts.org_id=org.id and org.is_deleted=0 and org.type=3
        where ts.is_deleted=0
        <if test="query.developSalesmanId != null">
            and ts.develop_salesman_id=#{query.developSalesmanId}
        </if>
        <if test="query.developSalesmanIds != null and query.developSalesmanIds.size() != 0">
            and ts.develop_salesman_id in
            <foreach collection="query.developSalesmanIds" item="sid" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>
        <if test="query.developSalesmanCode != null and query.developSalesmanCode != ''">
            and ts.develop_salesman_code=#{query.developSalesmanCode}
        </if>
        <if test="query.developSalesmanCodes != null and query.developSalesmanCodes.size() != 0">
            and ts.develop_salesman_code in
            <foreach collection="query.developSalesmanCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="query.storeOrgId != null">
            and org.id=#{query.storeOrgId}
        </if>
        <if test="query.storeOrgIds != null and query.storeOrgIds.size() != 0">
            and org.id in
            <foreach collection="query.storeOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
    </select>
    <select id="getCompanyIdByStoreId" resultType="java.lang.Long">
         SELECT ts.company_id FROM orgcenter.t_store<!--czw加密已处理--> ts WHERE ts.id=#{storeId}
    </select>

    <select id="queryStoreByCodes" resultType="com.fotile.orgcenter.store.pojo.vo.QueryStoreCodesVO">
        select ts.id id,t.id orgId,t.code code,t.name name,ts.status status from orgcenter.t_org t
        left join orgcenter.t_store<!--czw加密已处理--> ts on ts.org_id = t.id and t.type = 3
        <where>
            t.is_deleted = 0 and ts.is_deleted = 0
            <foreach collection="list" item="item" open=" and code in (" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            <if test="companyIds != null and companyIds.size > 0">
                and ts.company_id in
                <foreach collection="companyIds" item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            <if test="storeIds != null and storeIds.size > 0">
                and t.id in
                <foreach collection="storeIds" item="item" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findStoreConfigurationByOrgId"
            resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreConfigurationByOrgIdVO">
        select org_id,clues_follow,clues_lose,clues_overdue,deposit_clues_overdue
        from orgcenter.t_store<!--czw加密已处理-->
                where org_id = #{orgId,jdbcType=BIGINT} and is_deleted = 0  limit 1;
    </select>


    <select id="findStoreOrgIdPageAllTotal" resultType="int">
        SELECT COUNT(s.id)
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id

        <include refid="findPageAll2WHERE"/>
    </select>

    <!--只查询门店的orgId-->
    <select id="findStoreOrgIdPageAll" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT DISTINCT s.id,s.org_id orgId,o.code,o.name,s.abbreviation,s.company_id companyId,s.`status`,
        (SELECT name from t_org c WHERE c.is_deleted=0 AND c.type=1 AND s.company_id=c.id) as companyName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        <include refid="findPageAll2WHERE"/>
        ORDER BY
        <if test="store !=null and store.orderByCode !=null and store.orderByCode !=0">
            o.code ASC,
        </if>
        s.created_date DESC
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <select id="selectByOrgIds" resultType="com.fotile.orgcenter.store.pojo.dto.DistributorDto">
        select distinct d.id distributorId,
        d.code distributorCode,
        d.name distributorName,
        c1.id as channelCategoryId,
        c1.name as channelCategoryName,
        c2.id as channelSubdivideId,
        c2.name as channelSubdivideName
        from orgcenter.t_distributor d
        left join orgcenter.t_distributor_mapping m1 on d.id = m1.distributor_id and m1.is_deleted = 0 AND m1.type = 1
        left join orgcenter.channel_category c1 on c1.id = d.channel_category and c1.is_deleted = 0
        left join orgcenter.channel_category c2 on c2.id = d.channel_subdivide and c2.is_deleted = 0
        where d.is_deleted = 0
        <if test="query.channelCategoryId != null">
            and c1.id=#{query.channelCategoryId}
        </if>
        <if test="query.channelCategoryCode != null and query.channelCategoryCode != ''">
            and c1.code=#{query.channelCategoryCode}
        </if>
        <if test="query.channelSubdivideId != null">
            and c2.id=#{query.channelSubdivideId}
        </if>
        <if test="query.channelSubdivideCode != null and query.channelSubdivideCode != ''">
            and c2.code=#{query.channelSubdivideCode}
        </if>
        <if test="query.status != null">
            and d.status=#{query.status}
        </if>
        <!--数据权限-->
        <if test="query.dataScopeCompany != null and query.dataScopeCompany.size() != 0">
            and m1.org_id in
            <foreach collection="query.dataScopeCompany" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="query.orgId != null">
            and m1.org_id = #{query.orgId}
        </if>
        <if test="query.companyOrgIds != null and query.companyOrgIds.size() > 0">
            and m1.org_id in
            <foreach collection="query.companyOrgIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="query.storeChannelCode != null and query.storeChannelCode != ''">
            and c2.id in (
            select cc.parent_id
            from channel_category cc
            where cc.is_deleted=0 and cc.`level`=3 and cc.`code`=#{query.storeChannelCode}
            )
        </if>
    </select>
    <select id="getStoreOrgIdByCompanyId" resultType="java.lang.Long">
        SELECT o.id FROM t_store<!--czw加密已处理--> ts
LEFT JOIN t_org o ON ts.org_id=o.id
 WHERE ts.is_deleted=0
 AND o.is_deleted=0
 AND ts.company_id=#{companyId}
    </select>
    <!-- 查询分公司权限下启用状态的实体门店 -->
    <select id="getStoreByCompanyAuthor" resultType="com.fotile.orgcenter.store.pojo.vo.QueryStoreVo">
        SELECT s.id storeNo,
        s.org_id storeId,
        o.`name` storeName,
        o.code storeCode,
        (SELECT cc.name FROM orgcenter.channel_category cc WHERE cc.is_deleted=0 AND cc.`level`=3 AND cc.id=s.store_type) storeTypeName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND o.id=s.org_id
        <if test="!isAllCompanyAuthor">
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted=0 AND rc.org_id=s.company_id AND rc.user_id=#{userId,jdbcType=VARCHAR}
        </if>
        WHERE s.is_deleted=0 AND s.`status` =1 and s.is_virtual=0
    </select>

    <select id="getStoreByCompanyAuthorByCondition" resultType="com.fotile.orgcenter.store.pojo.vo.QueryStoreVo">
        SELECT st.id storeNo,
        st.org_id storeId,
        o.`name` storeName,
        o.code storeCode,
        (SELECT cc1.name FROM orgcenter.channel_category cc1 WHERE cc1.is_deleted=0 AND cc1.`level`=3 AND cc1.id=st.store_type) storeTypeName
        FROM orgcenter.t_store<!--czw加密已处理--> st
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND o.id=st.org_id
        <if test="req.storeChannelSubCodeList != null and req.storeChannelSubCodeList.size() != 0">
            inner join orgcenter.channel_category cc on cc.is_deleted = 0 and st.store_type = cc.id
            AND cc.code IN
            <foreach collection="req.storeChannelSubCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted=0 AND rc.org_id=st.company_id AND rc.user_id=#{userId,jdbcType=VARCHAR}
        </if>
        WHERE st.is_deleted=0 AND st.`status` =1 and st.is_virtual=0
        <!--<if test="req.storeChannelSubCodeList != null and req.storeChannelSubCodeList.size() != 0">
            AND st.store_sub_channel_code IN
            <foreach collection="req.storeChannelSubCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>-->
    </select>


    <!--根据门店类别和门店标签查询-->
    <select id="selectStoreByStoreLevels" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT DISTINCT s.id,s.org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        <where>
            s.is_deleted =0
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in
                <foreach item="item" collection="companyAuthorList" open="(" separator="," close=")">
                    #{item.orgId}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="storeType != null and storeType.size() > 0">
                AND s.store_type IN
                <foreach collection="storeType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="storeSubChannelCodeList != null and storeSubChannelCodeList.size() > 0">
                <if test="!storeSubChannelCodeList.contains(null)">
                    AND s.store_sub_channel_code IN
                    <foreach collection="storeSubChannelCodeList" item="storeSubChannelCode" open="(" separator="," close=")">
                        #{storeSubChannelCode}
                    </foreach>
                </if>
                <if test="storeSubChannelCodeList.contains(null)">
                    <if test="storeSubChannelCodeList.size()==1">
                        AND (s.store_sub_channel_code is null or s.store_sub_channel_code = '')
                    </if>
                    <if test="storeSubChannelCodeList.size()>1">
                        AND (s.store_sub_channel_code is null or s.store_sub_channel_code = '' or s.store_sub_channel_code IN
                        <foreach collection="storeSubChannelCodeList" item="storeSubChannelCode" open="(" separator="," close=")">
                            <if test="storeSubChannelCode!=null">
                            #{storeSubChannelCode}
                            </if>
                        </foreach>)
                    </if>
                </if>
            </if>


        </where>
        ORDER BY
        s.created_date DESC
    </select>

    <!--根据门店类别和门店标签查询-->
    <select id="selectStoreByStoreLevels3" resultType="com.fotile.orgcenter.store.pojo.dto.StoreByStoreLevelsOutDto">
        SELECT DISTINCT s.id,s.org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        <where>
            s.is_deleted =0
            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in
                <foreach item="item" collection="companyAuthorList" open="(" separator="," close=")">
                    #{item.orgId}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>
            <if test="storeType != null and storeType.size() > 0">
                AND s.store_type IN
                <foreach collection="storeType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="storeSubChannelCodeList != null and storeSubChannelCodeList.size() > 0">
                <if test="!storeSubChannelCodeList.contains(null)">
                    AND s.store_sub_channel_code IN
                    <foreach collection="storeSubChannelCodeList" item="storeSubChannelCode" open="(" separator="," close=")">
                        #{storeSubChannelCode}
                    </foreach>
                </if>
                <if test="storeSubChannelCodeList.contains(null)">
                    <if test="storeSubChannelCodeList.size()==1">
                        AND (s.store_sub_channel_code is null or s.store_sub_channel_code = '')
                    </if>
                    <if test="storeSubChannelCodeList.size()>1">
                        AND (s.store_sub_channel_code is null or s.store_sub_channel_code = '' or s.store_sub_channel_code IN
                        <foreach collection="storeSubChannelCodeList" item="storeSubChannelCode" open="(" separator="," close=")">
                            <if test="storeSubChannelCode!=null">
                                #{storeSubChannelCode}
                            </if>
                        </foreach>)
                    </if>
                </if>
            </if>


        </where>
        ORDER BY
        s.created_date DESC
    </select>

    <select id="getStoreByCluesOverdueNotNull" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select company_id ,id,org_id,clues_overdue,deposit_clues_overdue
        from orgcenter.t_store<!--czw加密已处理-->
        <where>
            <choose>
                <when test="list != null and list.size() != 0">
                    and org_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and   clues_overdue is not null
                </otherwise>
            </choose>

        </where>
    </select>
    <select id="findRelatedStoresId" resultType="com.fotile.orgcenter.store.pojo.dto.RelatedStoreDto">
    SELECT
	ts.id storeId,
	o.`code` storeCode,
	ts.related_store_id relatedStoreId
FROM
	orgcenter.t_store<!--czw加密已处理--> ts
	LEFT JOIN orgcenter.t_org o ON ts.org_id = o.id
WHERE
	ts.is_deleted = 0
	AND o.is_deleted = 0
        AND ts.related_store_id is NOT NULL
        AND ts.related_store_id !=''
        AND LOCATE(',',ts.related_store_id )=0
	AND o.`code` IN
	<foreach collection="codeList" open="(" close=")" item="storeCode" separator=",">
        #{storeCode}
    </foreach>
    </select>
    <select id="findCodesByIds" resultType="com.fotile.orgcenter.store.pojo.dto.RelatedStoreDto">
        SELECT
	ts.id relatedStoreId,
	ts.org_id relatedStoreOrgId,
	o.`code` relatedStoreCode,
	o.`name` relatedStoreName
FROM
	orgcenter.t_store<!--czw加密已处理--> ts
	LEFT JOIN orgcenter.t_org o ON ts.org_id = o.id
WHERE
	ts.is_deleted = 0
	AND o.is_deleted = 0
	AND ts.id IN
	<foreach collection="relatedStoreIds" open="(" close=")" separator="," item="id">
        #{id}
    </foreach>
    </select>
    <select id="queryCommonlyStores" resultType="com.fotile.orgcenter.store.pojo.dto.FindStoreByIdOutDto">
        SELECT
            o.`code`,
            o.`name`,
            o.id orgId,
            ts.id,
            ts.abbreviation abbreviation,
            ts.store_channel_code channelCategoryCode,
            ts.company_id companyId,
            (select o2.name from orgcenter.t_org o2 where o2.id=ts.company_id and o2.is_deleted=0) companyName,
            td.id                                as distributorId,
            td.code                              as distributorCode,
            td.name                              as distributorName,
            ts.`status`
            ,  (6371 * ACOS(COS(RADIANS(#{dto.latitude}))
            * COS(RADIANS(o.latitude))
            * COS(RADIANS(o.longitude) - RADIANS(#{dto.longitude}))
            + SIN(RADIANS(#{dto.latitude}))
            * SIN(RADIANS(o.latitude))
            )) AS  distance
        FROM
            orgcenter.t_store<!--czw加密已处理--> ts
                LEFT JOIN orgcenter.t_org o
                          ON ts.org_id=o.id
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type = 3 and tdm.is_deleted = 0 and tdm.org_id = ts.org_id
        LEFT JOIN orgcenter.t_distributor td on td.id = tdm.distributor_id and td.is_deleted = 0
        <if test="dto.storeOrderCounts !=null and dto.storeOrderCounts.size()>0">
            INNER JOIN
            <foreach collection="dto.storeOrderCounts" open="(" close=")" separator="union" item="store">
                select #{store.storeId} as tempId,
                 #{store.orderCreateDate} as tempDate,
                  #{store.orderCount} as tempCount
            </foreach>
            temp on temp.tempId=ts.org_id
        </if>
        WHERE
          <include refid="queryCommonlyStoreWhere"></include>
        order by
         <choose>
             <when test="dto.storeOrderCounts !=null and dto.storeOrderCounts.size()>0">
                 temp.tempDate DESC
             </when>
                 <otherwise>
                     distance asc
                 </otherwise>
         </choose>
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>
    <select id="queryCommonlyStoreCount" resultType="java.lang.Integer">
        SELECT
          count(ts.id)
        FROM
            orgcenter.t_store<!--czw加密已处理--> ts
                LEFT JOIN orgcenter.t_org o
                          ON ts.org_id=o.id
        WHERE
    <include refid="queryCommonlyStoreWhere"></include>
    </select>
<sql id="queryCommonlyStoreWhere">
    ts.is_deleted=0
    AND o.is_deleted=0
    and ts.status in (1,2)

    <if test="dto.storeIds!=null and dto.storeIds.size()>0">
        AND ts.org_id IN
        <foreach collection="dto.storeIds" separator="," open="(" close=")" item="storeId">
            #{storeId}
        </foreach>
    </if>
    <if test="dto.authorStoreIds!=null and dto.authorStoreIds.size()>0">
        and ts.org_id in
        <foreach collection="dto.authorStoreIds" separator="," open="(" close=")" item="storeId">
            #{storeId}
        </foreach>
    </if>
    <if test="dto.notInStoreIds!=null and dto.notInStoreIds.size()>0">
        AND ts.org_id not IN
        <foreach collection="dto.notInStoreIds" separator="," open="(" close=")" item="storeId">
            #{storeId}
        </foreach>
    </if>
    <if test="dto.nameOrCode!=null and dto.nameOrCode!=''">
        AND   (
        o.`name` LIKE  CONCAT('%',#{dto.nameOrCode},'%')
          OR
           o.`code` LIKE CONCAT('%',#{dto.nameOrCode},'%')
        OR
        ts.abbreviation LIKE  CONCAT('%',#{dto.nameOrCode},'%')
           )
    </if>
</sql>



    <select id="getStoreBaseInfoById" resultType="com.fotile.orgcenter.store.pojo.dto.StoreBaseInfoDto" parameterType="long">
        SELECT
        s.id storeRealId,
        o.id storeId,
        o.code storeCode,
        o.name storeName,
        o.parent_id depId,
        s.status status,
        s.abbreviation abbreviation,
        s.company_id companyId,
        s.is_to_drp isToDrp,
        s.to_drp_stage toDrpStage,
        tdm.distributor_id distributorId,
        s.app_pre_audit preAudit,
        s.is_transfer isTransfer,
        s.is_to_check isToCheck,
        s.store_type storeType,
        cc.code storeTypeCode,
        cc.name storeTypeName,
        cc.order_check_channel orderCheckChannel,
        s.is_cashier_enabled isCashierEnabled,
        s.cashier_charge_mapping cashierChargeMapping,
        s.iz_im_pay_activity_enabled izImPayActivityEnabled
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON s.org_id=o.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type = cc.id and cc.is_deleted = 0
        LEFT JOIN orgcenter.t_distributor_mapping tdm on tdm.type=3 and tdm.is_deleted=0 and tdm.org_id=s.org_id
        WHERE s.is_deleted = 0 and o.is_deleted = 0 and o.type = 3
        <if test="storeId != null">
            and s.org_id = #{storeId}
        </if>
    </select>
    <select id="getStoreWithLogDto" resultType="com.fotile.orgcenter.store.pojo.dto.UpdateStoreWithLogDto">
        SELECT
               s.id storeId,
        s.usable_area,
               s.store_channel_code storeChannelCode,
        s.store_level storeLevel,
               s.coverurl coverurl,
               s.scene_suit sceneSuit,
        s.whole_usable_area
        FROM orgcenter.t_store<!--czw加密已处理--> s
        WHERE s.is_deleted = 0
          and s.id = #{storeId}
    </select>
 <select id="findPageAll3Total" resultType="int">
        SELECT COUNT(s.id)
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        INNER JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id

        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND s.org_id=dm.org_id AND dm.type=3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=d.channel_subdivide
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id=s.store_type
        <if test="store.floorSet != null and store.floorSet.size()>0">
            LEFT JOIN orgcenter.t_store_facilities_noncommunity tsfn on s.id=tsfn.store_id
        </if>

        <include refid="findPageAll3WHERE"/>
    </select>

    <select id="findAll3ByParam" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,s.org_id orgId,s.key_word keyWord,o.code,o.name,p.name parentName,p.id parentId,
        '' AS leaderName,s.provicen_id provicenId,s.provicen_name provicenName,s.city_id cityId,s.city_name cityName,
        s.county_id countyId,s.county_name countyName,s.address address,s.leader_id
        leaderId,s.note,s.status,s.coverurl,s.shopping_start shoppingStart,s.shopping_end shoppingEnd,s.tel<!--czw加密已处理-->,
        s.company_id companyId,
        s.usable_area usableArea,s.eps_usable_area epsUsableArea, s.store_level storeLevel,s.is_unmanned isUnmanned,s.is_uniform isUniform,
        <if test="store.floorSet != null and store.floorSet.size()>0">
            tsfn.floors,
        </if>
        (SELECT name from t_org c WHERE s.company_id=c.id) as companyName,
        o.longitude,o.latitude,s.alipayno,s.alipaynourl,s.wechatno,s.wechatnourl,
        o.full_path_id fullPathId,o.full_path_name fullPathName,s.address2,s.is_open_kpoint isOpenKpoint,
        s.store_type storeType,s.channel channelId,
        (SELECT GROUP_CONCAT(name) FROM orgcenter.t_channel WHERE FIND_IN_SET(code,s.channel)) channelName,
        (select cc.name from orgcenter.channel_category cc WHERE s.store_type=cc.id) storeTypeName,s.abbreviation,
        (select tsfnn2.character from orgcenter.t_store_facilities_noncommunity tsfnn2 where  s.id=tsfnn2.store_id and tsfnn2.is_deleted=0) `character`,
        d.name as distributorName,
        d.code as distributorCode,
        case s.is_to_drp
        when '1' then '不同步'
        when '2' then '同步至DMS/总部DRP/DCS'
        WHEN '3' then '同步至华东DRP/DCS'
        when '4' then '同步至广东DRP/DCS'
        when '5' then '同步至DMS/总部DRP/华东DRP/DCS'
        when '6' then '同步至DMS/广东DRP/DCS'
        end `isToDrp`,
        c.area,
        s.develop_salesman_name developSalesmanName,
        cc2.name as channelSubdivides,s.decorate_status,s.created_date,
        CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,
        d.id distributorId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        LEFT JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted=0 AND s.company_id=c.org_id
        LEFT JOIN orgcenter.t_org p ON p.is_deleted=0 AND o.parent_id=p.id

        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND s.org_id=dm.org_id AND dm.type=3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=d.channel_subdivide
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id=s.store_type
        <if test="store.floorSet != null and store.floorSet.size()>0">
            LEFT JOIN orgcenter.t_store_facilities_noncommunity tsfn on s.id=tsfn.store_id
        </if>
        <include refid="findPageAll3WHERE"/>
        ORDER BY
        <if test="store !=null and store.orderByCode !=null and store.orderByCode !=0">
            o.code ASC,
        </if>
        s.created_date DESC
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>
    <select id="getStoreCount"
            resultType="com.fotile.orgcenter.store.pojo.dto.FindBasicStoreInfoByOrgIdOutDto">
        SELECT
        c.id companyId,
        c.org_id companyOrgId,
        oo.`name` companyName,
        c.area_code areaCode,
        c.area_name areaName,
        ts.id,
        ts.org_id storeOrgId,
        o.`code` storeCode,
        o.`name` storeName,
        cc1.`code` channelCategoryCode,
        cc1.`name` channelCategoryName,
        cc2.`code` channelSubdivideCode,
        cc.code storeChannelCode,
        cc.name as storeChannelName,
        ts.eps_usable_area,
        d.id distributorId,
        d.`code` distributorCode,
        d.`name` distributorName,
        COUNT(ts.id) storeCount
        FROM
        orgcenter.t_store<!--czw加密已处理--> ts
        INNER JOIN orgcenter.t_org o ON ts.org_id = o.id
        AND o.is_deleted = 0
        LEFT JOIN orgcenter.channel_category cc ON ts.store_type = cc.id
        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted = 0
        AND ts.org_id = dm.org_id
        AND dm.type = 3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0
        AND dm.distributor_id = d.id
        LEFT JOIN orgcenter.channel_category cc1 ON cc1.id = d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 ON cc2.id = d.channel_subdivide
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0
        AND ts.company_id = c.org_id
        LEFT JOIN orgcenter.t_org oo ON c.org_id=oo.id AND oo.is_deleted=0
WHERE
	ts.is_deleted = 0
        AND ts.is_virtual=0
        AND ts.`status` =1
        <if test="companyOrgIds!=null and companyOrgIds.size()>0">
        and c.org_id IN
            <foreach collection="companyOrgIds" separator="," item="companyId" open="(" close=")">
              #{companyId}
            </foreach>
        </if>
	<choose>
        <when test="channelCategoryCodes!=null and channelCategoryCodes.size()>0 and channelSubdivideCodes!=null and channelSubdivideCodes.size()>0">
            AND cc1.`code` in
            <foreach collection="channelCategoryCodes" separator="," item="channelCategoryCode" open="(" close=")">
                #{channelCategoryCode}
            </foreach>
            or cc2.`code` in
            <foreach collection="channelSubdivideCodes" item="channelSubdivideCode" open="(" close=")" separator=",">
                #{channelSubdivideCode}
            </foreach>
        </when>
        <otherwise>
            <if test="channelCategoryCodes!=null and channelCategoryCodes.size()>0">
                AND cc1.`code` in
                <foreach collection="channelCategoryCodes" separator="," item="channelCategoryCode" open="(" close=")">
                    #{channelCategoryCode}
                </foreach>
            </if>
            <if test="channelSubdivideCodes!=null and channelSubdivideCodes.size()>0">
                AND cc2.`code` in
                <foreach collection="channelSubdivideCodes" item="channelSubdivideCode" open="(" close=")" separator=",">
                    #{channelSubdivideCode}
                </foreach>
            </if>
        </otherwise>
    </choose>
	<if test="groupBy!=null">
        GROUP BY
        <if test="groupBy==1">
            c.area_code
        </if>
        <if test="groupBy==2">
            c.id
        </if>
        <if test="groupBy==3">
            ts.id
        </if>
    </if>
    </select>

    <select id="getOrgIdListByCompanyCrossStoreAuthor" resultType="java.lang.Long">
        SELECT
            s.org_id
        FROM orgcenter.t_store<!--czw加密已处理--> s
            INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}<!--25036权限优化标记-已修改-->
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <if test="statusSet != null and statusSet.size() > 0">
            AND s.status IN
            <foreach collection="statusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--获取公司交门店权限下的门店orgId集合，可指定companyOrgId、storeOrgId范围-->
    <select id="getOrgIdListByCompanyCrossStoreAuthorAndIdList" resultType="java.lang.Long">
        SELECT
        s.org_id
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}<!--25036权限优化标记-已修改-->
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <if test="inDTO.stringStatusSet != null and inDTO.stringStatusSet.size() > 0">
            AND s.status IN
            <foreach collection="inDTO.stringStatusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inDTO.companyOrgIdList != null and inDTO.companyOrgIdList.size() > 0">
            AND s.company_id IN
            <foreach collection="inDTO.companyOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inDTO.storeOrgIdList != null and inDTO.storeOrgIdList.size() > 0">
            AND s.org_id IN
            <foreach collection="inDTO.storeOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getStoreAuthorListByCompanyCrossStoreAuthorAndIdList" resultType="com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor">
        SELECT
        s.id AS storeId,
        s.org_id AS orgId,
        o.`name`,
        s.`abbreviation`,
        s.`status`,
        o.code
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}<!--25036权限优化标记-已修改-->
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <if test="inDTO.stringStatusSet != null and inDTO.stringStatusSet.size() > 0">
            AND s.status IN
            <foreach collection="inDTO.stringStatusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inDTO.companyOrgIdList != null and inDTO.companyOrgIdList.size() > 0">
            AND s.company_id IN
            <foreach collection="inDTO.companyOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inDTO.storeOrgIdList != null and inDTO.storeOrgIdList.size() > 0">
            AND s.org_id IN
            <foreach collection="inDTO.storeOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getStoreAuthorListByCompanyCrossStoreAuthor" resultType="com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor">
        SELECT
        s.id AS storeId,
        s.org_id AS orgId,
        o.`name`,
        s.`abbreviation`,
        s.`status`,
        o.code
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}<!--25036权限优化标记-已修改-->
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <if test="statusSet != null and statusSet.size() > 0">
            AND s.status IN
            <foreach collection="statusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countPageStoreAuthorListByCompanyCrossStoreAuthor" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        left join orgcenter.t_org ocom ON ocom.is_deleted=0 AND ocom.type=1 AND s.company_id=ocom.id
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <include refid="pageStoreAuthorListByCompanyCrossStoreAuthorWhere"/>
    </select>

    <select id="pageStoreAuthorListByCompanyCrossStoreAuthor" resultType="com.fotile.orgcenter.store.pojo.dto.QueryCompanyCrossStoreAuthorOutDto">
        SELECT
        s.id as id,
        s.id AS storeId,
        s.org_id AS orgId,
        o.`name`,
        o.`name` as storeName,
        s.`abbreviation`,
        ifnull(s.`abbreviation`,o.`name`) as storeCombName,
        s.`status`,
        o.code,
        s.company_id as companyId,
        ocom.name as companyName
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        left join orgcenter.t_org ocom ON ocom.is_deleted=0 AND ocom.type=1 AND s.company_id=ocom.id
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <include refid="pageStoreAuthorListByCompanyCrossStoreAuthorWhere"/>
        <choose>
            <when test="dto.sortType != null and dto.sortType == 1">
                ORDER BY
                CASE s.`status`
                WHEN '1' THEN 1
                WHEN '2' THEN 2
                WHEN '0' THEN 3
                WHEN '3' THEN 4
                END, o.code DESC
            </when>
            <otherwise>
                order by o.code ASC
            </otherwise>
        </choose>
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <select id="storeAuthorListByCompanyCrossStoreAuthor" resultType="com.fotile.orgcenter.store.pojo.dto.QueryCompanyCrossStoreAuthorOutDto">
        SELECT
        s.id as id,
        s.id AS storeId,
        s.org_id AS orgId,
        o.`name`,
        o.`name` as storeName,
        s.`abbreviation`,
        s.`status`,
        o.code,
        s.company_id as companyId,
        ocom.name as companyName
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        left join orgcenter.t_org ocom ON ocom.is_deleted=0 AND ocom.type=1 AND s.company_id=ocom.id
        <if test="!isAllStoreAuthor">
            INNER JOIN usercenter.user_entity_rule_store rs ON rs.org_id = o.id AND rs.is_deleted = 0 AND rs.user_id = #{userId}
        </if>
        <if test="!isAllCompanyAuthor">
            INNER JOIN orgcenter.t_company c ON c.org_id = s.company_id AND c.is_deleted = 0
            INNER JOIN orgcenter.t_org oc ON c.org_id = oc.id AND oc.is_deleted = 0 AND oc.`type` = 1
            INNER JOIN usercenter.user_entity_rule_company rc ON rc.is_deleted = 0 AND rc.user_id = #{userId} AND rc.org_id = oc.id
        </if>
        WHERE s.is_deleted = 0
        <include refid="pageStoreAuthorListByCompanyCrossStoreAuthorWhere"/>
        order by o.code ASC
    </select>

    <sql id="pageStoreAuthorListByCompanyCrossStoreAuthorWhere">
        <if test="dto.stringStatusSet != null and dto.stringStatusSet.size() > 0">
            AND s.status IN
            <foreach collection="dto.stringStatusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.name !=null and dto.name !=''">
            AND o.name LIKE CONCAT('%',#{dto.name},'%')
        </if>
        <if test="dto.nameOrCode !=null and dto.nameOrCode !=''">
            AND (o.name LIKE CONCAT('%',#{dto.nameOrCode},'%') OR o.code LIKE CONCAT('%',#{dto.nameOrCode},'%') OR s.abbreviation LIKE CONCAT('%',#{dto.nameOrCode},'%'))
        </if>
        <if test="dto.isVirtual != null">
            and s.is_virtual = #{dto.isVirtual}
        </if>
        <if test="dto.companyOrgId !=null ">
            AND s.company_id=#{dto.companyOrgId}
        </if>
        <if test="dto.companyIdList != null and dto.companyIdList.size() > 0">
            AND s.company_id IN
            <foreach collection="dto.companyIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.isSmartBadge != null ">
            AND s.is_smart_badge = #{dto.isSmartBadge}
        </if>
    </sql>

    <!--门店批量分配-->
    <select id="findBatchUpdate" resultType="com.fotile.orgcenter.store.pojo.dto.BatchUpdateStoreInDto">
        SELECT s.id,s.org_id orgId,s.leader_id leaderId,s.develop_salesman_id developSalesmanId,
         s.develop_salesman_code developSalesmanCode,
        AES_DECRYPT(UNHEX(s.develop_salesman_name),#{encryptPassword}) developSalesmanName,
        AES_DECRYPT(UNHEX(s.develop_salesman_phone),#{encryptPassword}) developSalesmanPhone,
        s.clues_msg_user cluesMsgUser,s.channel channel,
        AES_DECRYPT(UNHEX(s.manager_name),#{encryptPassword}) chargePerson,o.parent_id parentId,
        c.area as areaId,
        s.is_to_drp as isToDrp,
        s.to_drp_stage as toDrpStage,
        s.is_to_check as isToCheck,
        s.app_pre_audit as appPreAudit,
        s.clues_follow as cluesFollow,
        s.clues_lose as cluesLose,
        s.is_manager_clues_into_sea as isManagerCluesIntoSea,
        s.is_scene_flag as isSceneFlag,
        s.open_rq_channel as openRqChannel,
        s.open_zq_channel as openZqChannel,
        s.open_wy_channel as openWyChannel,
        s.status as storeStatus,
        s.close_date as closeDate,
        s.close_start_date as closeStartDate
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND o.id=s.org_id
        left join orgcenter.t_company c on c.is_deleted = 0 and s.company_id = c.org_id
        WHERE s.is_deleted=0 AND s.org_id IN
        <foreach collection="idList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </select>

    <!-- 根据门店id查询门店信息 -->
    <select id="findByOrgIdList" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.org_id orgId,
        s.key_word keyWord,
        s.STATUS STATUS,
        CASE
        s.STATUS
        WHEN 0 THEN
        '禁用'
        WHEN 1 THEN
        '启用'
        WHEN 2 THEN
        '筹备'
        WHEN 3 THEN
        '清退'
        END statusName,
        o.CODE CODE,
        o.name name,
        s.abbreviation abbreviation
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        LEFT JOIN t_org o ON s.org_id = o.id
        WHERE
        s.is_deleted = 0
        AND o.is_deleted = 0
        AND org_id IN
        <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </select>
    <select id="findStoreEntity" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
SELECT
too.`code`,
too.`name`,
	ts.*
FROM
	orgcenter.t_store<!--czw加密已处理--> ts
	INNER JOIN orgcenter.t_org too ON ts.org_id = too.id
	AND too.type = 3
	AND too.is_deleted = 0
WHERE
	ts.is_deleted = 0
	<if test="codes!=null and codes.size()>0">
        AND too.`code` in
        <foreach collection="codes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </if>
    <if test="dataScopeCompany!=null and dataScopeCompany.size()>0">
        and ts.company_id in
        <foreach collection="dataScopeCompany" item="companyId" separator="," open="(" close=")">
            #{companyId}
        </foreach>
    </if>

    </select>

    <!--门店批量添加/删除标签-->
    <update id="batchAddOrDelKeyWords2">
        <foreach collection="storeList" item="store">
            UPDATE orgcenter.t_store<!--czw加密已处理版本-->
            SET modified_by=#{modifiedBy},modified_date=NOW(),key_word=#{store.keyWord},`version`=`version`+1
            WHERE org_id=#{store.orgId};
        </foreach>
    </update>

    <select id="findOrgIdListByStoreChannelCodes" resultType="java.lang.Long">
        SELECT
        s.org_id
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON s.org_id = o.id AND o.is_deleted = 0 AND o.`type` = 3
        <if test="inDTO.storeChannelCategoryCodeList != null and inDTO.storeChannelCategoryCodeList.size() > 0">
            INNER JOIN orgcenter.channel_category cc on cc.is_deleted=0 AND cc.id=s.store_type
        </if>
        WHERE s.is_deleted = 0
        <if test="inDTO.statusSet != null and inDTO.statusSet.size() > 0">
            AND s.status IN
            <foreach collection="inDTO.statusSet" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="inDTO.storeChannelCategoryCodeList != null and inDTO.storeChannelCategoryCodeList.size() > 0">
            AND cc.`code` IN
            <foreach collection="inDTO.storeChannelCategoryCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="basisStoreByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select s.id,s.org_id orgId,s.leader_id leaderId,s.develop_salesman_id developSalesmanId,
        s.develop_salesman_code developSalesmanCode,s.develop_salesman_name developSalesmanName,
        s.develop_salesman_phone developSalesmanPhone,s.clues_msg_user cluesMsgUser,s.channel channel
        ,s.manager_name chargePerson,s.status status from orgcenter.t_store<!--czw加密已处理--> s
        where s.org_id = #{orgId}
    </select>

    <select id="getStoreNameByStoreIds" resultType="com.fotile.orgcenter.store.pojo.vo.QueryStoreVo">
        select
            s.id storeNo,
            o.id storeId,
            o.name storeName,
            o.code storeCode
        from
            orgcenter.t_store<!--czw加密已处理--> s
            inner join orgcenter.t_org o
            on s.org_id = o.id and o.type = 3 and s.is_deleted=0 and o.is_deleted=0
        where
            s.id in
            <foreach collection="storeIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="getStoreNames" resultType="com.fotile.orgcenter.store.pojo.dto.StoreCodeInfo">
        SELECT
        too.`code`,
        too.`name`,
        ts.abbreviation
        FROM
        t_org too
        INNER JOIN t_store ts ON too.id=ts.org_id
        WHERE
        too.is_deleted=0
        AND  too.`code`  IN
        <foreach collection="storeCodes" open="(" close=")" separator="," item="storeCode">
            #{storeCode}
        </foreach>
    </select>

    <!-- 根据大区codes查询门店 -->
    <select id="findStoreByAreaCodes" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.status,
        s.org_id orgId,
        o.code,
        o.name,
        c.area,
        c.area_code areaCode,
        c.area_name areaName,
        s.store_type storeType,
        s.provicen_name,
        s.city_name,
        s.county_name,
        s.address,
        s.address2,
        (SELECT name FROM orgcenter.t_org co WHERE co.is_deleted=0 AND co.id=s.company_id) companyName,
        s.company_id companyId
        FROM
        orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id = o.id
        LEFT JOIN orgcenter.t_company c ON c.is_deleted = 0 AND s.company_id = c.org_id
        WHERE s.is_deleted=0 AND s.status = 1
        <if test="areaCodes !=null and areaCodes.size>0">
            AND c.area_code IN
            <foreach collection="areaCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="storeAuthorList != null and storeAuthorList.size > 0">
            and s.id in
            <foreach item="item" collection="storeAuthorList" open="(" separator="," close=")">
                #{item.storeId}
            </foreach>
        </if>
        <if test="companyAuthorList != null and companyAuthorList.size > 0">
            and s.company_id in
            <foreach item="item" collection="companyAuthorList" open="(" separator="," close=")">
                #{item.orgId}
            </foreach>
        </if>
    </select>


    <select id="findByRelatedStoreId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
            ts.id,
            ts.org_id,
            ts.related_store_id,
        ts.whole_usable_area,
            ts.eps_usable_area
        FROM
            orgcenter.t_store<!--czw加密已处理--> ts
        WHERE
            related_store_id = #{relatedStoreId}
    </select>

    <select id="findParam" resultType="com.fotile.orgcenter.store.domain.StoreDomain">
        SELECT
            o.CODE code,
            s.abbreviation abbreviation,
            sa.station station
        FROM
            orgcenter.t_org o
            JOIN orgcenter.t_store<!--czw加密已处理--> s ON o.id = s.org_id
            JOIN orgcenter.t_salesman<!--czw加密已处理--> sa ON sa.store_id = s.org_id
        <where>
            <if test = " codes != null and codes.size > 0 ">
                o.code in
                <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test = " salesmanIds != null and salesmanIds.size > 0 ">
                AND sa.id in
                <foreach collection="salesmanIds" item="salesmanId" index="index" open="(" separator="," close=")">
                    #{salesmanId}
                </foreach>
            </if>
            AND sa.STATUS = 1
            and s.is_deleted = 0
            and o.is_deleted = 0
            and sa.is_deleted = 0
        </where>

    </select>

    <update id="batchUpdateStoreLeader">
        <!--启用：批量插入门店店长变更日志-->
        insert into orgcenter.operator_org_log
        (
            `source_id`,
            `source_table_name`,
            `operator_name`,
            `event_description_code`,
            `event_description`,
            `description`,
            `old_data`,
            `new_data`,
            `is_deleted`,
            `created_by`,
            `created_date`,
            `modified_by`,
            `modified_date`
        )
        select
            store.id as source_id,
            't_store' as source_table_name,
            LOWER(HEX(AES_ENCRYPT('system',#{encryptPassword}))) as operator_name,
            '403' as event_description_code,
            '修改门店信息' as event_description,
            (
                case
                    when ifnull(AES_DECRYPT(UNHEX(tsb.`name`), #{encryptPassword}),'')='' or ifnull(tsb.`code`,'')=''
                        then concat('店长从【空】修改成【',ifnull(AES_DECRYPT(UNHEX(tsa.`name`), #{encryptPassword}),''),'/',ifnull(tsa.`code`,''),'】')
                    else
                        concat('店长从【',ifnull(AES_DECRYPT(UNHEX(tsb.`name`), #{encryptPassword}),'空'),'/',ifnull(tsb.`code`,'空'),'】修改成【',ifnull(AES_DECRYPT(UNHEX(tsa.`name`), #{encryptPassword}),''),'/',ifnull(tsa.`code`,''),'】')
                    end
            ) as description,
            ifnull(tsb.id,'空') as old_data,
            ifnull(tsa.id,'空') as new_data,
            0 as is_deleted,
           'system-job' as created_by,
            now() as created_date,
           'system-job' as modified_by,
            now() as modified_date
        from orgcenter.t_store  store
        inner join (
            select ts.store_id, max(ts.created_date) as latestCreatedDate, max(ts.id) as maxId
            from orgcenter.t_salesman ts
                 inner join systemcenter.dic post
                    on post.is_deleted=0
                        and post.type_code='gw'
                        and post.value_code='5'
                        and post.id=ts.station
                        and ts.`status`=1
            group by ts.store_id
        ) sm on store.org_id=sm.store_id and (store.leader_id is null or store.leader_id!=sm.maxId)

        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and store.org_id in
            <foreach collection="storeOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
         </if>

        <if test="salesmanIds != null and salesmanIds.size() != 0">
            and sm.maxId in
            <foreach collection="salesmanIds" item="sid" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>

        left join orgcenter.t_salesman<!--czw加密已处理--> tsb on tsb.id=store.leader_id
        left join orgcenter.t_salesman<!--czw加密已处理--> tsa on tsa.id=sm.maxId;


        <!--启用：批量更新门店店长-->
        update orgcenter.t_store<!--czw加密已处理版本--> store,
        (
            select ts.store_id, max(ts.created_date) as latestCreatedDate, max(ts.id) as maxId
            from orgcenter.t_salesman<!--czw加密已处理--> ts
            inner join systemcenter.dic post
            on post.is_deleted=0
            and post.type_code='gw'
            and post.value_code='5'
            and post.id=ts.station
            and ts.`status`=1
            group by ts.store_id
        ) sm
        set store.leader_id=sm.maxId,
            store.modified_by='批量更新门店店长信息_修正',
            store.modified_date=now(),
            store.`version`=store.`version`+1
        where store.org_id=sm.store_id
        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and store.org_id in
            <foreach collection="storeOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>

        <if test="salesmanIds != null and salesmanIds.size() != 0">
            and sm.maxId in
            <foreach collection="salesmanIds" item="sid" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>
        and (store.leader_id is null or store.leader_id!=sm.maxId);

    </update>

    <update id="batchClearStoreLeader">
        <!--禁用或错误店长：批量插入门店店长变更日志-->
        insert into orgcenter.operator_org_log
        (
            `source_id`,
            `source_table_name`,
            `operator_name`,
            `event_description_code`,
            `event_description`,
            `description`,
            `old_data`,
            `new_data`,
            `is_deleted`,
            `created_by`,
            `created_date`,
            `modified_by`,
            `modified_date`
        )
        select
            store.id as source_id,
            't_store' as source_table_name,
            LOWER(HEX(AES_ENCRYPT('system',#{encryptPassword}))) as operator_name,
            '403' as event_description_code,
            '修改门店信息' as event_description,
            concat('店长从【',ifnull(AES_DECRYPT(UNHEX(tsb.`name`), #{encryptPassword}),''),'/',ifnull(tsb.`code`,''),'】修改成【空】') as description,
            ifnull(tsb.id,'空') as old_data,
            '空' as new_data,
            0 as is_deleted,
            'system-job' as created_by,
            now() as created_date,
            'system-job' as modified_by,
            now() as modified_date
        from orgcenter.t_store store
        left join
        (
            select ts.store_id
            from orgcenter.t_salesman ts
            inner join systemcenter.dic post
                on post.is_deleted=0
                    and post.type_code='gw'
                    and post.value_code='5'
                    and post.id=ts.station
                    and ts.`status`=1
        ) sm on store.org_id=sm.store_id
        left join orgcenter.t_salesman tsb on tsb.id=store.leader_id
        where sm.store_id is null
        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and store.org_id in
            <foreach collection="storeOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        and store.leader_id>0;


        <!--禁用或错误店长：批量更新门店店长-->
        update orgcenter.t_store<!--czw加密已处理版本--> store,
        (
            select store.org_id
            from orgcenter.t_store<!--czw加密已处理--> store
            left join
            (
                select ts.store_id
                from orgcenter.t_salesman<!--czw加密已处理--> ts
                inner join systemcenter.dic post
                    on post.is_deleted=0
                        and post.type_code='gw'
                        and post.value_code='5'
                        and post.id=ts.station
                        and ts.`status`=1
            ) sm on store.org_id=sm.store_id
            where sm.store_id is null and store.leader_id>0
        ) sm
        set store.leader_id=null,
            store.modified_by='批量更新门店店长信息_清空',
            store.modified_date=now(),
            store.`version`=store.`version`+1
        where 1=1
        <if test="storeOrgIds != null and storeOrgIds.size() != 0">
            and store.org_id in
            <foreach collection="storeOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        and store.org_id=sm.org_id;

    </update>
    <update id="updateStoreSelective">
        UPDATE orgcenter.t_store
        <set>
            <if test="wholeUsableArea != null">
                whole_usable_area = #{wholeUsableArea} ,
            </if>
            <if test="storeLevel != null">
                store_level = #{storeLevel} ,
            </if>
            <if test="modifiedBy !=null and modifiedBy !=''">
                modified_by=#{modifiedBy},
            </if>
            modified_date=now(),`version`=`version`+1
        </set>
        <where>
            id=#{id}
        </where>
    </update>
    <update id="updateByChangeProcess">
        UPDATE orgcenter.t_store ts

                <set>
                    ts.modified_date=now(),
                    ts.`version`=ts.`version`+1,
                <if test="isVirtual !=null">
                    ts.is_virtual=#{isVirtual},
                </if>
                    <if test="needTerminalBuild !=null">
                        ts.need_terminal_build=#{needTerminalBuild},
                    </if>
                    <if test="provicenId !=null">
                        ts.provicen_id=#{provicenId},
                    </if>
                    <if test="provicenName !=null and provicenName != ''">
                        ts.provicen_name=#{provicenName},
                    </if>
                    <if test="cityId !=null">
                        ts.city_id=#{cityId},
                    </if>
                    <if test="cityName !=null and cityName != ''">
                        ts.city_name=#{cityName},
                    </if>
                    <if test="countyId !=null">
                        ts.county_id=#{countyId},
                    </if>
                    <if test="countyName !=null and countyName != ''">
                        ts.county_name=#{countyName},
                    </if>
                    <if test="isUpdateAddress !=null  and isUpdateAddress">
                        ts.address=#{address},
                    </if>
                    <if test="longitude != null and longitude !=''">
                        ts.longitude=#{longitude},
                    </if>
                    <if test="latitude != null and latitude !=''">
                        ts.latitude=#{latitude},
                    </if>
                    <if test="storeBizType !=null and storeBizType != ''">
                        ts.store_biz_type=#{storeBizType},
                    </if>
                    <if test="storeMarketGrade !=null and storeMarketGrade != ''">
                        ts.store_market_grade=#{storeMarketGrade},
                    </if>
                    <if test="storeType !=null">
                        ts.store_type = #{storeType},
                    </if>
                    <if test="storeChannelCode !=null and storeChannelCode != ''">
                        ts.store_channel_code=#{storeChannelCode},
                    </if>
                    <if test="needSetSubChannel !=null and needSetSubChannel">
                        ts.store_sub_channel_code=#{storeSubChannelCode},
                    </if>
                    <if test="needSetWholeUsableArea !=null and needSetWholeUsableArea">
                        ts.whole_usable_area=#{wholeUsableArea},
                    </if>
                    <if test="needSetStoreLevel !=null and needSetStoreLevel">
                        ts.store_level=#{storeLevel},
                    </if>
                    <if test="status !=null">
                        ts.status = #{status},
                    </if>
                </set>
        WHERE ts.id=#{storeId};
    </update>
    <update id="updateStoreTerminalDate">
        UPDATE orgcenter.t_store
        <set>
            modified_date=NOW(),
            `version`=`version`+1,
            <if test="isUpdateAgain">
                terminal_check_date_again = #{terminalCheckDateAgain},
            </if>
            <if test="isUpdate">
                <choose>
                    <when test="terminalCheckDate !=null">
                        terminal_check_date =IF(terminal_check_date IS NULL,#{terminalCheckDate},terminal_check_date)
                    </when>
                    <otherwise>
                        terminal_check_date =null
                    </otherwise>
                </choose>

            </if>
        </set>
        WHERE
            id = #{storeId}
    </update>
    <update id="updateStoreTerminalDateBatch">
        <foreach collection="list" item="store"  separator=";">
            UPDATE orgcenter.t_store
            <set>
                    `version`=`version`+1,
                    terminal_check_date_again = #{store.terminalCheckDateAgain},
                    terminal_check_date = #{store.terminalCheckDate},
            </set>
            WHERE
            id = #{store.id}
        </foreach>
    </update>

    <select id="getStoreByDepositOrderCluesOverdueNotNull"
            resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select company_id ,id,org_id,deposit_clues_overdue from orgcenter.t_store<!--czw加密已处理--> where deposit_clues_overdue is not null
    </select>
    <select id="find5kmWithInStores" resultType="com.fotile.orgcenter.store.pojo.dto.StoreBaseInfoDto">
        SELECT
        ts.*
        FROM orgcenter.t_store ts
        LEFT JOIN orgcenter.t_org o ON ts.org_id=o.id
        LEFT JOIN (SELECT ts2.longitude,ts2.latitude,ts2.id FROM t_store ts2 WHERE ts2.is_deleted=0 AND ts2.id=#{storeId} )ts3
        ON (6371*ACOS(COS(RADIANS(ts3.latitude))
        *COS(RADIANS(ts.latitude))
        *COS(RADIANS(ts.longitude)-RADIANS(ts3.longitude))
        +SIN(RADIANS(ts3.latitude))
        *SIN(RADIANS(ts.latitude))
        ))&lt; 5
        WHERE ts.is_deleted=0
        AND ts.`status` IN (1,2)
        AND o.is_deleted=0
        AND ts.id !=ts3.id
        AND ts.store_channel_code='L025'
    </select>


    <select id="findStoreByCategory" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.org_id orgId,
        o.code,
        o.name,
        s.store_type storeType,
        s.store_channel_code storeChannelCode,
        s.store_sub_channel_code storeSubChannelCode
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        WHERE s.is_deleted=0
            <if test="categoryCodeList != null and categoryCodeList.size > 0">
                and s.store_channel_code in
                <foreach collection="categoryCodeList" item="categoryCode" open="(" separator="," close=")">
                    #{categoryCode}
                </foreach>
            </if>
            <if test="subdivideCodeList != null and subdivideCodeList.size > 0">
                or s.store_sub_channel_code in
                <foreach collection="subdivideCodeList" item="subdivideCode" open="(" separator="," close=")">
                    #{subdivideCode}
                </foreach>
            </if>
    </select>

    <select id="findStoreByCategoryCodes" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.org_id orgId,
        o.code,
        o.name,
        s.store_type storeType,
        cc1.code storeChannelCode,
        cc2.code storeSubChannelCode
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND s.org_id=dm.org_id AND dm.type=3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND dm.distributor_id=d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id=d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id=d.channel_subdivide
        WHERE s.is_deleted=0
        <if test="categoryCodeList != null and categoryCodeList.size > 0">
            and cc1.code in
            <foreach collection="categoryCodeList" item="categoryCode" open="(" separator="," close=")">
                #{categoryCode}
            </foreach>
        </if>
        <if test="subdivideCodeList != null and subdivideCodeList.size > 0">
            and (1=1 or cc2.code in
            <foreach collection="subdivideCodeList" item="subdivideCode" open="(" separator="," close=")">
                #{subdivideCode}
            </foreach>
            )
        </if>
    </select>
    <select id="getOriginalStoreByChangeLog" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
            ts.id,
            ts.related_store_id,
            ts.terminal_check_date,
            ts.terminal_check_date_again
        FROM
            orgcenter.operator_org_log ool
                LEFT JOIN orgcenter.t_store ts ON ts.is_deleted = 0
                AND ts.id = ool.source_id
        WHERE
            ool.is_deleted = 0
          AND ool.event_description_code = '407';
    </select>
    <select id="getByRelatedStoreIds" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
            ts.id,
            ts.related_store_id,
            ts.terminal_check_date,
            ts.terminal_check_date_again
        FROM orgcenter.t_store ts LEFT JOIN orgcenter.t_org o ON ts.org_id=o.id
        WHERE ts.is_deleted=0 AND o.is_deleted=0
          AND ts.related_store_id IN
    <foreach collection="pids" item="pid" open="(" separator="," close=")">
        #{pid}
    </foreach>
    </select>

    <select id="getStoreRealKeyWordMappingByStoreId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreRealKeyWordMapping">
        select
            m.store_id as storeId,
            m.word as word
        from orgcenter.t_store_real_keyword_mapping m
        where m.is_deleted = 0
          and m.store_id = #{storeId}
        order by id
    </select>

    <update id="logicDeleteRealKeyWordByStoreId">
        update orgcenter.t_store_real_keyword_mapping
        set is_deleted = 1
        where store_id = #{storeId}
    </update>

    <insert id="batchInsertRealKeyWordList">
        insert into orgcenter.t_store_real_keyword_mapping
        (is_deleted, created_by, created_date, modified_by, modified_date,store_id, word)
        values
        <if test="realKeyWordList != null and realKeyWordList.size() != 0">
            <foreach collection="realKeyWordList" item="item" separator=",">
                (0,
                #{userId},
                now(),
                #{userId},
                now(),
                #{storeId},
                #{item})
            </foreach>
        </if>
    </insert>

    <select id="getStoreRealKeyWordList" resultType="java.lang.String">
        select distinct
            m.word as word
        from orgcenter.t_store_real_keyword_mapping m
        where m.is_deleted = 0
        <if test="dto.matchContent != null and dto.matchContent != ''">
            and m.word LIKE CONCAT('%',#{dto.matchContent},'%')
        </if>
        order by id desc
    </select>

    <select id="listStoreIdByRealKeyWordList" resultType="java.lang.Long">
        select distinct
        m.store_id
        from orgcenter.t_store_real_keyword_mapping m
        where m.is_deleted = 0
        <if test="realKeyWordsList != null and realKeyWordsList.size() != 0">
            and
            <foreach collection="realKeyWordsList" item="item" open="(" separator="or" close=")">
                (m.word = #{item})
            </foreach>
        </if>
    </select>

    <update id="logicDeleteRealKeyWordByStoreIdsAndWords">
        update orgcenter.t_store_real_keyword_mapping set is_deleted = 1, modified_by = #{userId}, modified_date = now()
        where
            is_deleted = 0
            and store_id in
            <foreach collection="storeIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and word in
            <foreach collection="realKeyWordList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <select id="listRealKeyWordByStoreIdList" resultType="com.fotile.orgcenter.common.KvLs">
        select
        m.store_id as `key`,
        m.`word` as `value`
        from orgcenter.t_store_real_keyword_mapping m
        where m.is_deleted = 0
        <if test="storeIdList != null and storeIdList.size() != 0">
            and store_id in
            <foreach collection="storeIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by m.store_id
    </select>

    <insert id="batchInsertRealKeyWordListByEntity">
        insert into orgcenter.t_store_real_keyword_mapping
        (is_deleted, created_by, created_date, modified_by, modified_date,store_id, word)
        values
        <if test="realKeyWordList != null and realKeyWordList.size() != 0">
            <foreach collection="realKeyWordList" item="item" separator=",">
                (0,
                #{userId},
                now(),
                #{userId},
                now(),
                #{item.storeId},
                #{item.word})
            </foreach>
        </if>
    </insert>

    <select id="listRealKeyWordByStoreId" resultType="java.lang.String">
        select distinct
        m.word as word
        from orgcenter.t_store_real_keyword_mapping m
        where m.is_deleted = 0
        and m.store_id = #{storeId}
        order by id desc
    </select>
    <select id="queryStoreByConditions" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT DISTINCT s.id,s.org_id orgId
        FROM orgcenter.t_store<!--czw加密已处理--> s
        left join orgcenter.t_distributor_mapping dm on  dm.type=3  AND s.org_id=dm.org_id AND dm.is_deleted=0
        left join orgcenter.t_distributor d on d.is_deleted=0 AND d.id=dm.distributor_id
        left join orgcenter.t_store_real_keyword_mapping rkm on rkm.is_deleted = 0 and s.id = rkm.store_id
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id = s.store_type

        <where>
            s.is_deleted=0

            <if test="companyAuthorList != null and companyAuthorList.size > 0">
                and s.company_id in
                <foreach item="item" collection="companyAuthorList" open="(" separator="," close=")">
                    #{item.orgId}
                </foreach>
            </if>
            <if test="storeLevels != null and storeLevels.size()>0 ">
                <choose>
                    <when test='storeLevels.contains("-1")'>
                        and (
                        s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                        or s.store_level is null or s.store_level = ''
                        )
                    </when>
                    <otherwise>
                        and s.store_level in
                        <foreach collection="storeLevels" item="level" open="(" close=")" separator=",">
                            #{level}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="keyWordSet != null and keyWordSet.size() > 0">
                AND
                <foreach collection="keyWordSet" item="keyWord" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{keyWord},s.key_word)
                </foreach>
            </if>

            <if test="realKeyWordsList != null and realKeyWordsList.size() > 0">
                and rkm.word in
                <foreach collection="realKeyWordsList" item="realKeyWord" open="(" close=")" separator=",">
                    #{realKeyWord}
                </foreach>
            </if>

            <if test="storeType != null and storeType.size() > 0">
                AND s.store_type IN
                <foreach collection="storeType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="storeChannelCodeList != null and storeChannelCodeList.size() > 0">
                AND cc3.code in
                <foreach collection="storeChannelCodeList" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>
            <if test="developSalesmanId != null">
                and s.develop_salesman_id = #{developSalesmanId}
            </if>
            <if test="storeStatus != null and storeStatus.size() != 0">
                and s.status in
                <foreach collection="storeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="distributorId != null">
                AND d.id = #{distributorId}
            </if>
            <if test="storeChannelSubdivisionCodes != null and storeChannelSubdivisionCodes.size != 0">
                <choose>
                    <when test="storeChannelSubdivisionCodes.contains(null)">
                        and ( s.store_sub_channel_code is null or  s.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <otherwise>
                        and s.store_sub_channel_code in
                        <foreach collection="storeChannelSubdivisionCodes" separator="," item="item" close=")" open="(">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>

        </where>
        ORDER BY
        s.created_date DESC
    </select>

    <select id="listNearByStoreByLocation" resultType="com.fotile.orgcenter.store.pojo.dto.ListNearByStoreByLocationOutDTO">
        select
            *
        from
        (
            SELECT DISTINCT
                st.id as storeId,
                st.org_id as storeOrgId,
                case when st.abbreviation is null then o.name else st.abbreviation end as storeWebName,
                st.abbreviation as storeAbbreviation,
                o.name as storeName,
                st.address,
                st.`status`,
                st.provicen_id as provicenId,
                st.provicen_name as provicenName,
                st.city_id as cityId,
                st.city_name as cityName,
                st.county_id as countyId,
                st.county_name as countyName,
                <if test="dto.latitude != null and dto.longitude != null">
                    (case when st.latitude is null or st.longitude is null then #{dto.distanceMaxMin} else
                    ifnull(ROUND(6378.138*2*ASIN(SQRT(POW(SIN((#{dto.latitude}*PI()/180-st.latitude*PI()/180)/2),2)+COS(#{dto.latitude}*PI()/180)
                    * COS(st.latitude*PI()/180)*POW(SIN((#{dto.longitude}*PI()/180-st.longitude*PI()/180)/2),2))),1),#{dto.distanceMaxMin}) end) AS distance,
                </if>
                <if test="dto.latitude == null or dto.longitude == null">
                    #{dto.distanceMaxMin} AS distance,
                </if>
                st.longitude,
                st.latitude,
                cam.charge_user_id as chargeUserId
            FROM
                orgcenter.t_company_area ca
                INNER JOIN orgcenter.`t_company_area_mapping` cam ON ca.id = cam.company_area_id AND cam.is_deleted = 0 and cam.type = 1
                INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 and o.type = 3 and o.id = cam.store_org_id
                inner join orgcenter.t_store st ON st.is_deleted = 0 and st.`status` = '1' and o.id = st.org_id
                INNER JOIN (
									SELECT
										m1.company_area_id,
										m1.store_org_id,
										max( m1.id ) as id
									FROM
										orgcenter.`t_company_area_mapping` m1
										INNER JOIN (
										SELECT
											m.company_area_id,
											m.store_org_id,
											max( m.weight ) AS weight
										FROM
                                            orgcenter.`t_company_area_mapping` m
										WHERE
											m.is_deleted = 0
											AND m.type = 1
										GROUP BY
											m.company_area_id,
											m.store_org_id
										) m2 ON m2.company_area_id = m1.company_area_id
										AND m2.store_org_id = m1.store_org_id
										AND m2.weight = m1.weight
									WHERE
										m1.is_deleted = 0
										AND m1.type = 1
									GROUP BY
										m1.company_area_id,
										m1.store_org_id
								) mm on cam.id = mm.id
            WHERE
            ca.is_deleted = 0 AND ca.`enable` = 1
            and ca.county_id = #{dto.countyId}
        ) tt
        <if test="dto.latitude != null and dto.longitude != null">
            ORDER BY distance
        </if>
        <!--<if test="dto.latitude == null or dto.longitude == null">
            ORDER BY cam.id
        </if>-->
    </select>

    <select id="findStoreSingleSimpleInfoDTOByOrgId" resultType="com.fotile.orgcenter.store.pojo.dto.StoreSingleSimpleInfoDTO">
        select
            st.id,
            st.org_id as orgId,
            o.code,
            o.name,
            cc.id as storeTypeId,
            cc.name as storeTypeName
        from orgcenter.t_store st
        inner join orgcenter.t_org o on o.is_deleted = 0 and o.type = 3 and o.id = st.org_id
        left join orgcenter.channel_category cc on cc.is_deleted = 0 and st.store_type = cc.id
        where
            st.is_deleted = 0
            and st.org_id = #{orgId}
    </select>
    <select id="findStoreVersion" resultType="java.lang.Integer">
        SELECT
            ts.version
        FROM
            orgcenter.t_store ts
        WHERE
            ts.is_deleted = 0
          <if test="storeId != null">
            AND ts.id = #{storeId}
            </if>
          <if test="storeOrgId!=null">
            AND ts.org_id = #{storeOrgId}
          </if>
    </select>

    <select id="getOrgIdListByCondition" resultType="java.lang.Long">
        SELECT s.org_id
        FROM orgcenter.t_store s
        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted = 0 AND s.org_id = dm.org_id AND dm.type = 3
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0 AND dm.distributor_id = d.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id = d.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id = d.channel_subdivide
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id = s.store_type
        where s.is_deleted = 0
        <if test="param.storeChannelCodeList != null and param.storeChannelCodeList.size() > 0">
            AND cc3.code in
            <foreach collection="param.storeChannelCodeList" item="scc" open="(" separator="," close=")">
                #{scc}
            </foreach>
        </if>
        <if test="param.channelCategoryIds != null and param.channelCategoryIds.size() > 0">
            AND cc1.id in
            <foreach collection="param.channelCategoryIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="param.channelSubdivideIds != null and param.channelSubdivideIds.size() > 0">
            AND cc2.id in
            <foreach collection="param.channelSubdivideIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
    </select>

    <select id="findStoreSimpleInfoByOrgIds" resultType="com.fotile.orgcenter.store.pojo.dto.StoreSimpleInfoOutDTO">
        SELECT
            s.id as id,
            s.org_id as orgId,
            o.code as `code`,
            o.name as name,
            s.abbreviation,
            IFNULL(s.abbreviation,o.name) as storeNameComb,
            s.status,
            CASE s.status
            WHEN 0 THEN '禁用'
            WHEN 1 THEN '启用'
            WHEN 2 THEN '筹备'
            WHEN 3 THEN '清退'
            END as statusName,
            s.company_id as companyId,
            oc.name as companyName,
            cc.code as storeTypeCode,
            cc.name as storeTypeName
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org oc ON oc.is_deleted=0 AND s.company_id=oc.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        where
            s.is_deleted = 0
            <if test="dto.idList != null and dto.idList.size() > 0">
                AND s.org_id IN
                <foreach collection="dto.idList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="checkCountPageStoreWithAuthForImApplet" resultType="int">
        SELECT
            count(*)
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org oc ON oc.is_deleted=0 AND s.company_id=oc.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        where
            s.is_deleted = 0
        AND s.status in (1,2)
        <if test="dto.companyOrgId != null">
            AND s.company_id = #{dto.companyOrgId}
        </if>
        <if test="dto.storeOrgIdList != null and dto.storeOrgIdList.size() > 0">
            AND s.org_id IN
            <foreach collection="dto.storeOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countPageStoreWithAuthForImApplet" resultType="int">
        SELECT
            count(*)
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org oc ON oc.is_deleted=0 AND s.company_id=oc.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        where
            <include refid="pageStoreWithAuthForImAppletWhere"/>
    </select>

    <select id="pageStoreWithAuthForImApplet" resultType="com.fotile.orgcenter.store.pojo.dto.StoreForImAppletOutDTO">
        SELECT
            s.id as id,
            s.org_id as orgId,
            o.code as `code`,
            o.name as name,
            s.abbreviation,
            IFNULL(s.abbreviation,o.name) as storeNameComb,
            s.status,
            CASE s.status
            WHEN 0 THEN '禁用'
            WHEN 1 THEN '启用'
            WHEN 2 THEN '筹备'
            WHEN 3 THEN '清退'
            END as statusName,
            s.company_id as companyId,
            oc.name as companyName,
            cc.code as storeTypeCode,
            cc.name as storeTypeName
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_org o ON o.is_deleted=0 AND o.type=3 AND s.org_id=o.id
        LEFT JOIN orgcenter.t_org oc ON oc.is_deleted=0 AND s.company_id=oc.id
        LEFT JOIN orgcenter.channel_category cc ON s.store_type=cc.id
        where
            <include refid="pageStoreWithAuthForImAppletWhere"/>
        order by s.org_id desc
        LIMIT #{pg.offset},#{pg.size}
    </select>

    <sql id="pageStoreWithAuthForImAppletWhere">
        s.is_deleted = 0
        AND s.status in (1,2)
        <if test="dto.matchContent !=null and dto.matchContent != ''">
            AND (
            UPPER(o.name) like concat('%',UPPER(#{dto.matchContent}),'%')
            or UPPER(o.code) like concat('%',UPPER(#{dto.matchContent}),'%')
            )
        </if>
        <if test="dto.companyOrgId != null">
            AND s.company_id = #{dto.companyOrgId}
        </if>
        <if test="dto.storeOrgIdList != null and dto.storeOrgIdList.size() > 0">
            AND s.org_id IN
            <foreach collection="dto.storeOrgIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.storeOrgIds != null and dto.storeOrgIds.size() > 0">
            AND s.org_id IN
            <foreach collection="dto.storeOrgIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getStoreNewBizConfig"
            resultType="com.fotile.orgcenter.store.pojo.dto.StoreNewBizConfigDTO">
        select ts.id as storeId,
            ts.org_id as storeOrgId,
            ts.open_wy_channel as openWyChannel,
            ts.open_zq_channel as openZqChannel,
            ts.open_rq_channel as openRqChannel
        from orgcenter.t_store ts
        where ts.is_deleted=0
        <choose>
            <when test="storeId != null">
                and ts.id=#{storeId}
            </when>
            <when test="storeOrgId != null">
                and ts.org_id=#{storeOrgId}
            </when>
            <otherwise>
                and ts.id=0
            </otherwise>
        </choose>
    </select>

    <select id="queryDesignatedStoreCount" resultType="int">
        select count(1) from orgcenter.t_store s
            inner join orgcenter.t_org o on o.id = s.org_id
        LEFT JOIN orgcenter.channel_category cc ON cc.is_deleted = 0 AND s.store_type = cc.id
        where s.is_deleted = 0
        <if test="search != null and search !=''">
            <choose>
                <when test="storeKeywordType == 2">
                    AND cc.name LIKE CONCAT('%',#{search},'%')
                </when>
                <otherwise>
                    AND (o.name LIKE CONCAT('%',#{search},'%') OR s.abbreviation LIKE CONCAT('%',#{search},'%') OR o.code
                    LIKE CONCAT('%',#{search},'%') OR cast(s.id as char)=CONCAT('%',#{search},'%'))
                </otherwise>
            </choose>
        </if>
        <if test="storeIds!= null  and storeIds.size > 0 ">
          and   o.id in (
            <foreach collection="storeIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="queryDesignatedStore" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select s.org_id,o.code,o.name,CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,s.status from orgcenter.t_store s
        inner join orgcenter.t_org o on o.id = s.org_id
        LEFT JOIN orgcenter.channel_category cc ON cc.is_deleted = 0 AND s.store_type = cc.id
        where s.is_deleted = 0
        <if test="search != null and search !=''">
        <choose>
            <when test="storeKeywordType == 2">
                AND cc.name LIKE CONCAT('%',#{search},'%')
            </when>
            <otherwise>
                AND (o.name LIKE CONCAT('%',#{search},'%') OR s.abbreviation LIKE CONCAT('%',#{search},'%') OR o.code
                LIKE CONCAT('%',#{search},'%') OR cast(s.id as char)=CONCAT('%',#{search},'%'))
            </otherwise>
        </choose>
        </if>
        <if test="storeIds!= null  and storeIds.size > 0 ">
           and  o.id in (
            <foreach collection="storeIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        order by field(s.status,1,2,3,0) asc
        limit #{offset},#{pageSize}
    </select>

    <select id="queryFotileStoreCount" resultType="int">
        select count(1) from orgcenter.t_store s
        inner join orgcenter.t_org o on o.id = s.org_id
        where s.is_deleted = 0 and s.status = 1
        <if test="search != null and search !=''">
            AND (o.name LIKE CONCAT('%',#{search},'%') OR s.abbreviation LIKE CONCAT('%',#{search},'%') OR o.code
            LIKE CONCAT('%',#{search},'%') OR cast(s.id as char)=CONCAT('%',#{search},'%'))
        </if>
        <if test="companyIds != null  and companyIds.size > 0 ">
            and s.company_id in (
            <foreach collection="companyIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="queryFotileStore" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select s.org_id,o.code,o.name,CASE s.status
        WHEN 0 THEN '禁用'
        WHEN 1 THEN '启用'
        WHEN 2 THEN '筹备'
        WHEN 3 THEN '清退'
        END statusName,
        s.status,
        s.provicen_id,
        s.provicen_name,
        s.city_id,
        s.city_name,
        s.county_id,
        s.county_name,
        s.address,
        s.address2
        from orgcenter.t_store s
        inner join orgcenter.t_org o on o.id = s.org_id
        where s.is_deleted = 0
        and s.status = 1
        <if test="search != null and search !=''">

        AND (o.name LIKE CONCAT('%',#{search},'%') OR s.abbreviation LIKE CONCAT('%',#{search},'%') OR o.code
        LIKE CONCAT('%',#{search},'%') OR cast(s.id as char)=CONCAT('%',#{search},'%'))
        </if>
        <if test="companyIds != null  and companyIds.size > 0 ">
            and s.company_id in (
            <foreach collection="companyIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>

        limit #{offset},#{pageSize}

    </select>

    <select id="getStoreLeaderByStoreId"
    resultType="com.fotile.orgcenter.salesman.pojo.dto.FindSalesmanByIdOutDto">
        SELECT
            man.*
        FROM
            t_store ts
                INNER JOIN t_salesman man ON ts.leader_id = man.id
                AND man.is_deleted = 0
                AND man.`status` =1
                INNER JOIN usercenter.user_entity_extend uee ON man.id=uee.salesman_id AND uee.is_deleted=0 AND uee.stage=1
        WHERE ts.org_id=#{storeId}
          AND ts.is_deleted=0
  </select>

    <select id="findStoreByAddress" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select s.org_id,o.code,o.name,CASE s.status
                                          WHEN 0 THEN '禁用'
                                          WHEN 1 THEN '启用'
                                          WHEN 2 THEN '筹备'
                                          WHEN 3 THEN '清退'
            END statusName,
               s.status,
               s.provicen_id,
               s.provicen_name,
               s.city_id,
               s.city_name,
               s.county_id,
               s.county_name,
               s.address,
               s.address2
        from orgcenter.t_store s
                 inner join orgcenter.t_org o on o.id = s.org_id
        where s.is_deleted = 0
          and s.status = 1
        <if test="provinceId != null">
            and s.provicen_id = #{provinceId}
        </if>
        <if test="cityId != null">
            and s.city_id = #{cityId}
        </if>
        <if test="countyId != null">
            and s.county_id = #{countyId}
        </if>
    </select>

    <select id="getStoreSimpleInfo"
            resultType="com.fotile.orgcenter.store.pojo.dto.StoreSingleSimpleInfoDTO">
        select ts.id, ts.org_id, org.code, org.name, ts.abbreviation
        from orgcenter.t_store ts
            inner join orgcenter.t_org org
                on ts.is_deleted=0
                    and org.is_deleted=0
                    and ts.org_id=org.id
                <if test="storeId != null">
                    and ts.id=#{storeId}
                </if>
                <if test="storeCode != null and storeCode!=''">
                    and org.code=#{storeCode}
                </if>
                <if test="storeOrgId != null">
                    and org.id=#{storeOrgId}
                </if>
        limit 1;
    </select>

    <select id="queryDesignerStoreInfo" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        ts.org_id,
        ts.`status`,
            ts.address,
            o.`name`,
            ts.abbreviation
        FROM
        orgcenter.t_store ts
                INNER JOIN orgcenter.t_org o ON o.id = ts.org_id
        WHERE
            ts.is_deleted = 0
          AND o.is_deleted = 0
          AND ts.org_id IN
        <foreach collection="storeIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <sql id="queryStoreSelectBoxWhere">
        <if test="query.id!=null">
            and ts.id=#{query.id}
        </if>
        <if test="query.storeOrgId!=null">
            and ts.org_id=#{query.storeOrgId}
        </if>
        <if test="query.companyOrgId!=null">
            and ts.company_id=#{query.companyOrgId}
        </if>
        <if test="query.code!=null and query.code!=''">
            and ts.code=#{query.code}
        </if>
        <if test="query.name!=null and query.name!=''">
            and storeorg.name like concat('%', #{query.name},'%')
        </if>
        <if test="query.provinceId!=null">
            and ts.provicen_id=#{query.provinceId}
        </if>
        <if test="query.cityId!=null">
            and ts.city_id=#{query.cityId}
        </if>
        <if test="query.countyId!=null">
            and ts.county_id=#{query.countyId}
        </if>
        <if test="query.status!=null and query.status.size()>0">
            and ts.status in
            <foreach collection="query.status" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.keywords!=null and query.keywords!=''">
            and (
                storeorg.code like concat('%', #{query.keywords},'%') or
                storeorg.name like concat('%', #{query.keywords},'%') or
                ts.abbreviation like concat('%', #{query.keywords},'%')
            )
        </if>

        <if test="query.dataScopeCompany!=null and query.dataScopeCompany.size()>0">
            and ts.company_id in
            <foreach collection="query.dataScopeCompany" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="query.dataScopeStore!=null and query.dataScopeStore.size()>0">
            and ts.org_id in
            <foreach collection="query.dataScopeStore" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="countStoreSelectBox"
            parameterType="com.fotile.orgcenter.store.pojo.dto.QueryStoreSelectBoxDTO"
            resultType="java.lang.Integer">
        select count(ts.id) as totalRows
        from orgcenter.t_store ts
            inner join orgcenter.t_org storeorg
               on ts.is_deleted=0
                   and storeorg.type=3
                   and storeorg.is_deleted=0
                   and storeorg.id=ts.org_id
            inner join orgcenter.t_org cpyorg
               on cpyorg.is_deleted=0
                   and cpyorg.type=1
                   and cpyorg.id=ts.company_id
                  <include refid="queryStoreSelectBoxWhere"/>

    </select>

    <select id="queryStoreSelectBox"
            parameterType="com.fotile.orgcenter.store.pojo.dto.QueryStoreSelectBoxDTO"
            resultType="com.fotile.orgcenter.store.pojo.vo.StoreSelectBoxVO">
        select ts.id as storeId, ts.org_id as storeOrgId, ts.company_id as companyOrgId,
            cpyorg.code as companyCode,  cpyorg.name as companyName, storeorg.code, storeorg.name, ts.abbreviation, ts.status
        from orgcenter.t_store ts
            inner join orgcenter.t_org storeorg
                on ts.is_deleted=0
                    and storeorg.type=3
                    and storeorg.is_deleted=0
                    and storeorg.id=ts.org_id
                    inner join orgcenter.t_org cpyorg
                    on cpyorg.is_deleted=0
                    and cpyorg.type=1
                    and cpyorg.id=ts.company_id
                    <include refid="queryStoreSelectBoxWhere"/>
    </select>

    <update id="updateCloseDate">
        update orgcenter.t_store
        <set>
            close_date=#{closeDate}, close_start_date=#{closeStartDate}
        </set>
        where id=#{storeId}
    </update>

    <select id="queryByOrgId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select s.clues_check_period,s.clues_follow_status,s.clues_check_phone,s.clues_check_scope,s.clues_process_result from orgcenter.t_store s
        where s.org_id =#{orgId}
    </select>

    <select id="findCodeByCodes" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        s.id,
        s.org_id orgId,
        o.code,
        o.name,
        s.store_type storeType,
        s.key_word keyWord
        FROM orgcenter.t_store<!--czw加密已处理--> s
        INNER JOIN orgcenter.t_org o ON o.is_deleted = 0 AND o.type = 3 AND s.org_id = o.id
        WHERE o.is_deleted=0 AND s.is_deleted=0
        <choose>
            <when test="storeOrgIds != null and storeOrgIds.size>0">
                <foreach collection="storeOrgIds" open="and o.code in (" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and s.id=0
            </otherwise>
        </choose>
    </select>

    <select id="findBySalesmanId"
            resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT s.id,
        s.plan_code,
        s.is_open_kpoint,
        s.annual_rent,
        s.is_open_rent,
        s.stadiums_introduce,
        s.travel_tips,
        s.`status`,
        s.wechat,
        s.shopping_remark,
        s.tel_remark,
        s.email,
        s.email_remark,
        s.wechat_remark,
        s.alipay_remark,
        s.channel,
        s.org_id                                orgId,
        s.key_word                              keyWord,
        s.coverurl,
        s.address,
        s.provicen_id                           provicenId,
        s.provicen_name,
        s.city_id                               cityId,
        s.city_name,
        s.county_id                             countyId,
        s.county_name,
        s.longitude,
        s.latitude,
        s.shopping_start                        shoppingStart,
        s.shopping_end                          shoppingEnd,
        s.tel,
        s.leader_id                             leaderId,
        s.alipayno,
        s.alipaynourl,
        s.wechatno,
        s.wechatnourl,
        s.note,
        s.company_id,
        s.address2,
        s.is_to_drp                             isToDrp,
        s.is_manager_clues_into_sea             isManagerCluesIntoSea,
        s.to_drp_stage                          toDrpStage,
        s.store_type                            storeType,
        s.clues_msg_user                        cluesMsgUser,
        s.abbreviation,
        s.intro,
        s.consultation_pc,
        s.consultation_m,
        s.develop_salesman_id                   developSalesmanId,
        s.develop_salesman_code                 developSalesmanCode,
        s.develop_salesman_name                 developSalesmanName,
        s.develop_salesman_phone                developSalesmanPhone,
        s.app_pre_audit                         preAudit,
        s.is_transfer                           isTransfer,
        s.is_to_check                           isToCheck,
        s.decorate_status                       decorateStatus,
        s.open_date                             openDate,
        s.close_date                            closeDate,
        s.terminal_check_date                    terminalCheckDate,
        s.terminal_check_date_again      terminalCheckDateAgain,
        s.is_scene_flag,
        s.created_date,
        s.store_market_grade                    storeMarketGrade,
        s.distributor_channel                   distributorChannel,
        s.related_store_id                      relatedStoreId,
        s.is_virtual                            isVirtual,
        s.need_terminal_build                   needTerminalBuild,
        s.manager_name                          managerName,
        s.manager_phone                         managerPhone,
        s.store_biz_type                        storeBizType,
        s.staffs                                staffs,
        s.property_right                        propertyRight,
        s.lease_term                            leaseTerm,
        s.bpm_type,
        s.usable_area,
        s.open_type,
        s.live_demo                             liveDemo,
        s.layout_code                           layoutCode,
        s.if_decoration_store,
        s.open_type,
        s.store_sub_channel_code,
        s.eps_usable_area,
        s.whole_usable_area,
        s.market_capacity,
        s.population,
        s.store_level,
        s.is_unmanned,
        s.sign_shape,
        s.use_pos,
        s.clues_lose,
        s.clues_follow,
        s.store_channel_code,
        s.close_start_date,
        s.modified_date,
        s.modified_by,
        s.created_by,
        s.is_deleted,
        s.`version`,
        s.sign_size,
        s.sign_cost,
        S.terminal_image_score,
        s.is_uniform,
        s.outside_store_name,
        s.uniform_pic,
        s.scene_suit,
        s.top_kitchens,
        s.clues_overdue cluesOverdue,
        s.deposit_clues_overdue depositCluesOverdue,
        s.iz_im_pay_activity_enabled as izImPayActivityEnabled,
        s.is_cashier_enabled,
        s.merchant_code,
        s.merchant_name,
        s.merchant_code_online,
        s.cashier_charge_mapping,
        s.open_wy_channel,
        s.open_zq_channel,
        s.open_rq_channel
        FROM orgcenter.t_store s
        INNER JOIN orgcenter.t_salesman ts ON ts.is_deleted = 0 AND s.is_deleted = 0 AND s.org_id = ts.store_id
        AND ts.id = #{salesmanId}
    </select>

    <select id="querystroreBydouyinStoreId" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        select * from orgcenter.t_store
        where status in (1,2)
            and tiktok_code =#{douyinStoreId}
        order by created_date desc
        limit 1;
    </select>

    <!-- 根据id查询 -->
    <select id="findDataByOrgId" resultType="com.fotile.orgcenter.store.pojo.dto.StoreDataSimpleInfo">
        SELECT
        s.company_id companyId,
        (SELECT o.name FROM orgcenter.t_org o WHERE s.company_id = o.id) companyName,
        (SELECT o.code FROM orgcenter.t_org o WHERE s.company_id = o.id) companyCode,
        (SELECT o.area FROM orgcenter.t_company o WHERE s.company_id = o.org_id) areaId,
        (SELECT o.area_code FROM orgcenter.t_company o WHERE s.company_id = o.org_id) areaCode,
        (SELECT o.area_name FROM orgcenter.t_company o WHERE s.company_id = o.org_id) areaName,
        s.org_id storeId,
        (SELECT o.name FROM orgcenter.t_org o WHERE s.org_id = o.id) storeName,
        (SELECT o.code FROM orgcenter.t_org o WHERE s.org_id = o.id) storeCode,
        d.id distributorId,
        d.code distributorCode,
        d.name distributorName
        FROM orgcenter.t_store<!--czw加密已处理--> s
        LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted=0 AND dm.type=3 AND dm.org_id=s.org_id
        LEFT JOIN orgcenter.t_distributor d ON d.is_deleted=0 AND d.id=dm.distributor_id
        WHERE s.is_deleted = 0
        <if test="orgId !=null">
            AND s.org_id = #{orgId}
        </if>
    </select>

    <select id="queryStore4DesignerClubExport" resultType="com.fotile.orgcenter.store.pojo.entity.StoreEntity">
        SELECT
        o.id orgId,
            o.`code` code,
            o.`name` name,
            ts.develop_salesman_name,
            cc1.CODE channelCategoryCode,
            cc1.NAME channelCategoryName,
            cc2.CODE channelSubdivideCode,
            cc2.NAME as channelSubdivideName
        FROM
            orgcenter.t_store ts
                INNER JOIN orgcenter.t_org o ON ts.org_id = o.id
                AND o.is_deleted = 0
                LEFT JOIN orgcenter.t_distributor_mapping dm ON dm.is_deleted = 0
                AND ts.org_id = dm.org_id
                AND dm.type = 3
                LEFT JOIN orgcenter.t_distributor d ON d.is_deleted = 0
                AND dm.distributor_id = d.id
                LEFT JOIN orgcenter.channel_category cc1 ON cc1.id = d.channel_category
                LEFT JOIN orgcenter.channel_category cc2 ON cc2.id = d.channel_subdivide
        WHERE
            ts.is_deleted = 0
          AND ts.org_id IN
            <foreach collection="storeIds" open="(" close=")" item="storeId" separator=",">
                #{storeId}
            </foreach>
    </select>

    <select id="toktikStoreStatusException"
            resultType="com.fotile.orgcenter.store.pojo.vo.ToktikStoreStatusExceptionVO">
        select
            s.provicen_name provinceName,
            s.city_name,
            s.county_name,
            s.tiktok_code as tokTikStoreCode,
            s.`status` as storeStatus,
            o.name as storeName,
            s.tiktok_name as tokTikStoreName,
            o.code storeCode,
            company.name as companyName,
            tc.area_name regionName
        from orgcenter.t_store s
                 left join orgcenter.t_org o on s.org_id = o.id
                 left join orgcenter.t_org company on s.company_id = company.id
                 left join orgcenter.t_company tc on s.company_id = tc.org_id
        where s.is_deleted = 0
          and s.`status` in ('0','3')
          and s.tiktok_code is not null
    </select>
</mapper>
