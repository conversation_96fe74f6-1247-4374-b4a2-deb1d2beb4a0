<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.customer.dao.UserInstallAddressFromMallDao">


    <select id="queryUserAddressByCustomerId2" resultType="com.fotile.customercenter.customer.pojo.entity.UserAddressFromMall">
        select a.*
        from customercenter.user_install_address a
        <where>
            a.is_deleted = 0
            <if test="customerId!=null">
                and a.customer_info_id = #{customerId}
            </if>

        </where>
        order by is_default desc, created_date desc,id desc
        limit #{offset},#{pageSize}
    </select>

    <!-- count查询 -->
    <select id="selectPropertyCount" resultType="java.lang.Long">
        select count(a.id)
        from customercenter.user_install_address a
        <where>
            a.is_deleted = 0
            <if test="customerId!=null">
                and a.customer_info_id = #{customerId}
            </if>

        </where>
    </select>

    <update id="deleteAddressById" >
        UPDATE user_install_address
        set is_deleted = id,
             modified_by=#{userId},
             modified_date=now()
        where id = #{id}
    </update>

    <delete id="deleteAddressByIds">
        UPDATE user_install_address
        set is_deleted = id,
        modified_by=#{userId},
        modified_date=now()
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>



