<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.devops.module.dao.ModuleDAO">
    <resultMap id="BaseResultMap" type="com.fotile.devops.module.pojo.entity.Module">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="devops_user_ID" jdbcType="BIGINT" property="devopsUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, is_deleted, created_by, created_date, modified_by, modified_date, `name`, devops_user_ID
    </sql>

    <select id="moduleSelectDTO"
            resultType="com.fotile.devops.module.pojo.entity.Module">
        select m.*
        from devops.module m
        <where>
            m.is_deleted = 0
            <if test="module.createdBy != null and module.createdBy != ''">
                and m.createdBy = #{created_by}
            </if>
            <if test="module.name != null and module.name != ''">
                and m.name like '%${module.name}%'
            </if>

            <if test="module.devopsUserId != null and module.devopsUserId != ''">
                and m.devops_user_ID = #{module.devopsUserId}
            </if>
            <if test="module.startTime != null and module.startTime != ''">
                and m.create_time &gt;= #{module.startTime}
            </if>

            <if test="module.endTime != null and module.endTime != ''">
                and m.create_time &lt; #{module.endTime}
            </if>
            <if test="module.like != null and module.like != ''">
                and( id like CONCAT('%',#{module.like},'%')
                or
                name like CONCAT('%',#{module.like},'%')
                )
            </if>

        </where>
        order by m.id desc limit #{page.offset},#{page.size}
    </select>
    <select id="moduleSelectDTOCount"
            resultType="java.lang.Long">
        select count(*)
        from devops.module m
        <where>
            m.is_deleted = 0
            <if test="module.createdBy != null and module.createdBy != ''">
                and m.createdBy = #{created_by}
            </if>
            <if test="module.name != null and module.name != ''">
                and m.name like '%${module.name}%'
            </if>

            <if test="module.devopsUserId != null and module.devopsUserId != ''">
                and m.devops_user_ID = #{module.devopsUserId}
            </if>
            <if test="module.startTime != null and module.startTime != ''">
                and m.create_time &gt;= #{module.startTime}
            </if>

            <if test="module.endTime != null and module.endTime != ''">
                and m.create_time &lt; #{module.endTime}
            </if>
            <if test="module.like != null and module.like != ''">
                and( id like CONCAT('%',#{module.like},'%')
                or
                name like CONCAT('%',#{module.like},'%')
                )
            </if>

        </where>
    </select>

    <select id="selectNameById" resultType="java.lang.String">
        SELECT name from devops.module
        <where>
            id=#{id}
        </where>

    </select>
    <select id="selectModuleByName" resultType="com.fotile.devops.module.pojo.entity.Module">

        SELECT * from devops.module
        <where>
            name=#{name}
        </where>
        limit 1

    </select>

</mapper>