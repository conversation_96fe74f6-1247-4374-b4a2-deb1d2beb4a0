<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.activiti.workflow.role.dao.RoleDao">
    <resultMap id="BaseResultMap" type="com.fotile.activiti.workflow.role.pojo.entity.Role">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="process_code" jdbcType="VARCHAR" property="processCode"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="role_type" jdbcType="TINYINT" property="roleType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_user_name" jdbcType="VARCHAR" property="createdUserName"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_user_name" jdbcType="VARCHAR" property="modifiedUserName"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <result column="is_show" jdbcType="TINYINT" property="isShow"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
    wf_role.id, wf_role.process_code, wf_role.`name`,role_type, wf_role.remark, wf_role.created_by,wf_role.created_user_name, wf_role.created_date, wf_role.modified_by,
    wf_role.modified_user_name,wf_role.modified_date,wf_role.is_show, wf_role.is_deleted
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,dict.value_name as processName
        from wf_role left join dict on dict.type_code='process_type' and dict.value_code=wf_role.process_code
        where wf_role.id = #{id,jdbcType=BIGINT}
        and wf_role.is_deleted=0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wf_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.fotile.activiti.workflow.role.pojo.entity.Role" useGeneratedKeys="true">
    insert into wf_role (process_code, `name`,role_type, remark,
      created_by,created_user_name, created_date, modified_by, modified_user_name,
      modified_date,is_show, is_deleted)
    values (#{processCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},#{roleType,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
      #{createdBy,jdbcType=VARCHAR}, #{createdUserName,jdbcType=VARCHAR},#{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedUserName,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},#{isShow,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT})
  </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.fotile.activiti.workflow.role.pojo.entity.Role" useGeneratedKeys="true">
        insert into wf_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processCode != null">
                process_code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="roleType != null">
                role_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdUserName != null">
                created_user_name,
            </if>
            <if test="createdDate != null">
                created_date,
            </if>
            <if test="modifiedBy != null">
                modified_by,
            </if>
            <if test="modifiedUserName != null">
                modified_user_name,
            </if>
            <if test="modifiedDate != null">
                modified_date,
            </if>
            <if test="isShow != null">
                is_show,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processCode != null">
                #{processCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                #{roleType,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdUserName != null">
                #{createdUserName,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedUserName != null">
                #{modifiedUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isShow != null">
                #{isShow,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.fotile.activiti.workflow.role.pojo.entity.Role">
        update wf_role
        <set>
            <if test="processCode != null">
                process_code = #{processCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                role_type = #{roleType,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdUserName != null">
                created_user_name = #{createdUserName,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedUserName != null">
                modified_user_name = #{modifiedUserName,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="isShow != null">
                is_show = #{isShow,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fotile.activiti.workflow.role.pojo.entity.Role">
    update wf_role
    set process_code = #{processCode,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      role_type = #{roleType,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_user_name = #{createdUserName,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_user_name = #{modifiedUserName,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      is_show = #{isShow,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="queryPageCount" parameterType="com.fotile.activiti.workflow.role.pojo.RoleDTO"
            resultType="java.lang.Integer">
        select count(*) from wf_role
        where wf_role.is_deleted=0 and wf_role.is_show=true
        <if test="processCode != null and processCode!=''">
            and process_code = #{processCode,jdbcType=VARCHAR}
        </if>
        <if test="createdBy != null and createdBy !=''">
            and created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name !=''">
            and `name` like CONCAT('%',#{name,jdbcType=VARCHAR},'%')
        </if>
        <if test="createdDateStart != null ">
            and created_date &gt;= #{createdDateStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createdDateEnd != null ">
            and created_date &lt;= #{createdDateEnd,jdbcType=TIMESTAMP}
        </if>
    </select>
    <select id="queryPage" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        select
        <include refid="Base_Column_List"></include>,dict.value_name as processName
        from wf_role left join dict on dict.type_code='process_type' and dict.value_code=wf_role.process_code
        where wf_role.is_deleted=0 and wf_role.is_show=true
        <if test="dto.processCode != null and dto.processCode!=''">
            and wf_role.process_code = #{dto.processCode,jdbcType=VARCHAR}
        </if>
        <if test="dto.createdBy != null and dto.createdBy !=''">
            and wf_role.created_by = #{dto.createdBy,jdbcType=VARCHAR}
        </if>
        <if test="dto.name != null and dto.name !=''">
            and wf_role.`name` like CONCAT('%',#{dto.name,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.createdDateStart != null ">
            and wf_role.created_date &gt;= #{dto.createdDateStart,jdbcType=TIMESTAMP}
        </if>
        <if test="dto.createdDateEnd != null ">
            and wf_role.created_date &lt;= #{dto.createdDateEnd,jdbcType=TIMESTAMP}
        </if>
        ORDER BY wf_role.created_date DESC
        limit #{pg.offset},#{pg.size}
    </select>
    <select id="getByIds" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        select
        <include refid="Base_Column_List"></include>,dict.value_name as processName
        from wf_role left join dict on dict.type_code='process_type' and dict.value_code=wf_role.process_code
        where wf_role.is_deleted=0
        <if test="list !=null and list.size()>0">
            <foreach collection="list" item="id" separator="," open="and wf_role.id in(" close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="list" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        select
        <include refid="Base_Column_List"></include>
        from wf_role where wf_role.is_deleted=0
        <if test="roleDTO.processCode != null and roleDTO.processCode != '' ">
            and wf_role.process_code=#{roleDTO.processCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getByParams" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        select
        <include refid="Base_Column_List"></include>
        from wf_role where wf_role.is_deleted=0
        <if test="dto.processCode != null and dto.processCode != '' ">
            and wf_role.process_code=#{dto.processCode,jdbcType=VARCHAR}
        </if>
        <if test="dto.name != null and dto.name != '' ">
            and wf_role.`name` = #{dto.name,jdbcType=VARCHAR}
        </if>
        <if test="dto.roleType != null and dto.roleType != '' ">
            and wf_role.role_type = #{dto.roleType,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="listByUserNumber" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        user_role_mapping urm
        LEFT JOIN wf_role wf_role ON urm.role_id = wf_role.id
        WHERE
        wf_role.is_show=true
        and urm.is_deleted=FALSE
        and urm.number = #{userNumber,jdbcType=VARCHAR}
        AND urm.process_code = #{processType,jdbcType=VARCHAR}
    </select>
    <select id="listCreateUser" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
        SELECT DISTINCT wr.created_by,wr.created_user_name FROM wf_role wr where wr.is_deleted=FALSE and wr.is_show=true
        <if test="userId != null and userId != ''">
            and wr.created_by=#{userId,jdbcType=VARCHAR}
        </if>
        <if test="userName != null and userName != ''">
            and wr.created_user_name = #{userName,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
        </if>
    </select>
    <select id="listByUserIdAndProcType" resultType="com.fotile.activiti.workflow.role.pojo.RoleDTO">
      SELECT
      <include refid="Base_Column_List"/>
FROM
	wf_role
	LEFT JOIN   user_role_mapping  urm  ON urm.role_id = wf_role.id
WHERE
        wf_role.is_show=true and wf_role.is_deleted=FALSE
        AND urm.user_entity_id = #{userId,jdbcType=VARCHAR}
        AND urm.is_deleted = FALSE
        <foreach collection="processTypes" item="processType" separator="," open=" AND urm.process_code in ( " close=")">
            #{processType,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getHistoryTaskRoleName" resultType="java.lang.String">
        SELECT
	        ahi.GROUP_ID_
        FROM
	        act_hi_taskinst aht
	    INNER JOIN act_hi_identitylink ahi ON ahi.TASK_ID_ = aht.ID_ AND ahi.TYPE_ = 'candidate'
        WHERE
	        aht.ID_ = #{taskId};
    </select>
</mapper>