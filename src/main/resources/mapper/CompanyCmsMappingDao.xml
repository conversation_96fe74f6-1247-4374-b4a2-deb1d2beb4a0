<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.content.dao.CompanyCmsMappingDao">
    <update id="updateBySourceTableAndSourceId">
        UPDATE cmscenter.company_cms_mapping
        SET is_deleted=1,
        modified_by = #{modifiedBy},
        modified_date = now()
        where source_table_name=#{sourceTable} and source_id=#{sourceId}
    </update>
    <select id="findBySSCId" resultType="long">
        SELECT company_id from cmscenter.company_cms_mapping
        where source_table_name= #{sourceTable}
        and source_id= #{sourceId}
        <if test="companyId !=null">
        and company_id=#{companyId}
        </if>
        and is_deleted=0
    </select>
    <select id="findAllCompany" resultType="com.fotile.cmscenter.content.pojo.vo.AllCompanyVo">
        SELECT company_id companyId, source_table_name sourceTableName, source_id sourceId
          FROM cmscenter.company_cms_mapping
         WHERE source_table_name IN ( 'content_menu', 'content' )
	       AND is_deleted =0
        and source_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into cmscenter.company_cms_mapping (`source_table_name`, `source_id`, `company_id`, `sort`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.sourceTableName}, #{entity.sourceId}, #{entity.companyId}, #{entity.sort}, 0, #{entity.createdBy}, now(), #{entity.modifiedBy}, now())
        </foreach>
    </insert>

</mapper>