<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.tag.dao.TagPictureDao">
    <insert id="insert" keyProperty="id" parameterType="com.fotile.cmscenter.tag.pojo.TagPicture"
            useGeneratedKeys="true">
    insert into tag_picture (created_by, created_date,
      modified_by, modified_date, `name`,
      wide, high, img_url,type)
    values (#{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR},
      #{wide,jdbcType=INTEGER}, #{high,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR},#{type,jdbcType=TINYINT})
  </insert>

    <insert id="insertTags">
        insert into tag_info ( created_by, created_date,
        modified_by, modified_date, `name`,
        x_axis, y_axis, goods_id,
        sort, picture_id,no_jump_dic,is_jump)
        values
        <foreach collection="tags" item="tag" separator=",">
            ( #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
            #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{tag.name,jdbcType=VARCHAR},
            #{tag.x_axis,jdbcType=INTEGER}, #{tag.y_axis,jdbcType=INTEGER}, #{tag.goodsId,jdbcType=BIGINT},
            #{tag.sort,jdbcType=INTEGER}, #{id,jdbcType=BIGINT},#{tag.noJumpDic,jdbcType=VARCHAR},#{tag.isJump,jdbcType=INTEGER})
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.fotile.cmscenter.tag.pojo.TagPicture">
    update tag_picture
    set
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      `name` = #{name,jdbcType=VARCHAR},
      wide = #{wide,jdbcType=INTEGER},
      high = #{high,jdbcType=INTEGER},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="delTags">
        update
        cmscenter.tag_info
        set is_deleted=id,modified_date=now(),modified_by=#{userId}
        where is_deleted=0
        and picture_id=#{id}
    </update>

    <update id="del">
        update
        cmscenter.tag_picture
        set is_deleted=id,modified_date=now(),modified_by=#{userId}
        where is_deleted=0
        and id=#{id}
    </update>


    <select id="counts" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM cmscenter.tag_picture
        where
        is_deleted=0
        <if test="ids != null and ids.size > 0">
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="id!=null">
            and id=#{id}
        </if>
        <if test="name!=null and name!=''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="type !=null">
            and type=#{type}
        </if>
    </select>
    <select id="list" resultType="com.fotile.cmscenter.tag.pojo.dto.TagPicturesOutDto">
        SELECT
        tp.id,tp.`name`,tp.img_url,tp.wide,tp.high,ti.x_axis,ti.y_axis,tp.created_date createdDate
        from cmscenter.tag_picture tp
        left join cmscenter.tag_info ti on tp.id=ti.picture_id and ti.is_deleted=0
        where
        tp.is_deleted=0
        <if test="ids != null and ids.size > 0">
            and tp.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="id!=null">
            and tp.id=#{id}
        </if>
        <if test="name!=null and name!=''">
            and tp.name like concat('%',#{name},'%')
        </if>
        <if test="type !=null">
            and type=#{type}
        </if>
        group by tp.id
        order by tp.created_date desc
        limit #{offset},#{size}
    </select>

    <select id="detail" resultMap="detailMap">
        select
            tp.id pid, tp.`name`, tp.wide,tp.type,
            tp.high, tp.img_url,ti.name tagName,ti.x_axis,ti.y_axis,ti.goods_id,ti.id tid,ti.no_jump_dic,ti.is_jump
            from cmscenter.tag_picture tp
            left join cmscenter.tag_info ti on tp.id=ti.picture_id and tp.is_deleted=0 and ti.is_deleted=0
            where tp.id=#{id}
    </select>
    <resultMap id="detailMap" type="com.fotile.cmscenter.tag.pojo.dto.TagDetail">
        <id column="pid" property="id"></id>
        <result column="name" property="name"></result>
        <result column="wide" property="wide"></result>
        <result column="high" property="high"></result>
        <result column="img_url" property="imgUrl"></result>
        <result column="type" property="type"></result>
        <collection property="tags" ofType="com.fotile.cmscenter.tag.pojo.TagInfo">
            <id column="tid" property="id"></id>
            <result column="tagName" property="name"></result>
            <result column="x_axis" property="x_axis"></result>
            <result column="y_axis" property="y_axis"></result>
            <result column="goods_id" property="goodsId"></result>
            <result column="no_jump_dic" property="noJumpDic"></result>
            <result column="is_jump" property="isJump"></result>
        </collection>
    </resultMap>

    <select id="getByTagIds" resultMap="detailMap">
        select
            tp.id pid, tp.`name`, tp.wide,tp.type,
            tp.high, tp.img_url,ti.name tagName,ti.x_axis,ti.y_axis,ti.goods_id,ti.id tid,ti.no_jump_dic,ti.is_jump
            from cmscenter.tag_picture tp
            left join cmscenter.tag_info ti on tp.id=ti.picture_id and tp.is_deleted=0 and ti.is_deleted=0
            where tp.id in
            <foreach collection="tagIdsList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>


</mapper>
