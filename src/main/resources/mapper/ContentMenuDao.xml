<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.content.dao.ContentMenuDao">

    <select id="findTitleByCategoryId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentCategoryVo" parameterType="long">
           SELECT id, title,sort,content_category_id categoryId
             from cmscenter.content_menu
            where content_category_id=#{categoryId}
              and is_deleted=0
    </select>
    <select id="findContentMenuByMenuId" parameterType="long" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoVo">
        SELECT cm.created_person,cm.id,5 as type,cc.name,cm.title,cm.cover_url coverUrl,cm.created_date createdDate,cm.auditing_status auditingStatus,
	           cm.online_status onlineStatus,scm.browse_count browseCount,scm.collection_count collectionCount,qu.questions,pr.products
        FROM   cmscenter.content_menu cm
        LEFT JOIN ( SELECT browse_count,collection_count,source_id
	                FROM   cmscenter.statistics_cms_mapping
	                WHERE  source_table_name = 'content_menu'
	                AND is_deleted = 0 ) scm ON (cm.id = scm.source_id)
        LEFT JOIN content_category cc ON cc.id = cm.content_category_id
        LEFT JOIN (SELECT count(*) questions,source_id
                   FROM cmscenter.question_info
                   WHERE source_table_name ='content_menu'
                   and is_deleted=0
                   GROUP BY source_table_name,source_id) qu on(qu.source_id=cm.id)
        LEFT JOIN (SELECT COUNT(*) products,content_menu_id
                   from cmscenter.show_product
                   where is_deleted=0 GROUP BY content_menu_id) pr on(cm.id=pr.content_menu_id)
        WHERE cm.is_deleted = 0
        and cm.id=#{menuId}
    </select>

    <select id="findSpecialContentMenuByMenuId" parameterType="long" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoVo">
       SELECT cm.created_person,cm.id,5 as type,cc.name,cm.title,cm.cover_url coverUrl,cm.created_date createdDate,cm.auditing_status auditingStatus,
	           cm.online_status onlineStatus,scm.browse_count browseCount,scm.collection_count collectionCount,cm.is_paid isPaid
        FROM   cmscenter.content_menu cm
        LEFT JOIN statistics_cms_mapping  scm ON (cm.id = scm.source_id and scm.source_table_name = 'content_menu' and scm.is_deleted = 0)
        LEFT JOIN content_category cc ON cc.id = cm.content_category_id
        WHERE cm.is_deleted = 0
        and cm.id=#{menuId}
    </select>
    <select id="queryPartContentMenuById" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoClassVo" parameterType="long">
        SELECT cm.id contentId,'5' as type,cc.name className,cm.start_time startTime,cm.end_time endTime,cm.title,
	           cm.auditing_status auditingStatus,cm.online_status onlineStatus,'content_menu' as contentTable,
	           cm.sort contentSort
        FROM   cmscenter.content_menu cm,cmscenter.content_category cc
        WHERE  cm.content_category_id = cc.id
        AND    cm.is_deleted = 0
        AND    cc.is_deleted = 0
        AND    cm.id=#{id}
    </select>

    <select id="quetyContentMenuByUserId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentMenuForTalentVo">
       SELECT id, auditing_status,title, inside_title,created_date,online_status
       from content_menu
       WHERE `upload_user_type` = '02' and is_deleted=0
       <if test="userId !=null">
         and  user_id= #{userId}
       </if>
    </select>

    <select id="findValidData" resultType="com.fotile.cmscenter.content.pojo.ContentMenu">
        SELECT id,recomend_goods,model_num
        FROM `cmscenter`.`content_menu`
        WHERE `recomend_goods` IS NOT NULL
        AND `recomend_goods` != ''
        AND (`model_num` IS NULL
        OR `model_num` = '')
    </select>

    <select id="findClassIdByContentMenuId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentToClassVo">
        select cm.id contentId ,cc.id contentClassId,cc.name contentClassName,"content_menu" as sourceTable
        from cmscenter.content_menu cm
        LEFT JOIN cmscenter.content_category cc on (cc.id=cm.content_category_id and cc.is_deleted =0)
        where  cm.is_deleted=0
        and cm.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getCompoundInfoTitle" resultType="com.fotile.cmscenter.compound.pojo.dto.CompoundInfoTitleOutDto">
        SELECT
            id,
            title
        FROM
            content_menu
        WHERE
            is_deleted = 0
          and id  = #{id}
    </select>

    <select id="findContentMenuByGoodsIdPage" resultType="com.fotile.cmscenter.content.pojo.vo.ContentMenuDetaiVo">
        SELECT
            menu.id,
            menu.title,
            IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) as systemBrowseCount,
            menu.cover_url coverUrl
        FROM
            content_menu menu
        left join statistics_cms_mapping scm on scm.source_id = menu.id and scm.source_table_name = 'content_menu' and scm.is_deleted = 0
        WHERE
            menu.is_deleted = 0
          and menu.online_status = 1
          and now() between IFNULL(menu.start_time,'2010-01-01 00:00:00') and IFNULL(menu.end_time,'2200-01-01 00:00:00')
          and menu.vedio_menu = 0
          and menu.recomend_goods LIKE CONCAT('%', #{goodsId}, '%')
        order by systemBrowseCount desc
            LIMIT #{pg.offset},#{pg.size}
    </select>

    <select id="findContentMenuByGoodsIdCount" resultType="java.lang.Long">
        SELECT
            count(id)
        FROM
            content_menu
        WHERE
            is_deleted = 0
          and vedio_menu = 0
          and recomend_goods LIKE CONCAT('%', #{goodsId}, '%')
    </select>
</mapper>