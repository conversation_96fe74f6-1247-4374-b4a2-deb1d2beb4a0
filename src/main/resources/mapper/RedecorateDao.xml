<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.activiti.workflow.phase.dao.RedecorateDao">
  <resultMap id="BaseResultMap" type="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.Redecorate">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="apply_id" jdbcType="INTEGER" property="applyId" />
    <result column="decorate_type" jdbcType="VARCHAR" property="decorateType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_id, decorate_type, remark, created_by, created_date, modified_by, modified_date, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.RedecorateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from s_apply_redecorate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="mySelectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from s_apply_redecorate
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from s_apply_redecorate
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.RedecorateExample">
    delete from s_apply_redecorate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="myInsert" keyColumn="id" keyProperty="id" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.Redecorate" useGeneratedKeys="true">
    insert into s_apply_redecorate (apply_id, decorate_type, remark, 
      created_by, created_date, modified_by, 
      modified_date, is_deleted)
    values (#{applyId,jdbcType=INTEGER}, #{decorateType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.Redecorate" useGeneratedKeys="true">
    insert into s_apply_redecorate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        apply_id,
      </if>
      <if test="decorateType != null">
        decorate_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        #{applyId,jdbcType=INTEGER},
      </if>
      <if test="decorateType != null">
        #{decorateType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.RedecorateExample" resultType="java.lang.Long">
    select count(*) from s_apply_redecorate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="queryRedecorateStores" resultType="java.lang.Integer">
      SELECT
        sa.store_id
      FROM
        s_apply sa
          INNER JOIN s_apply_redecorate sar ON sa.id=sar.apply_id

          LEFT JOIN act_ru_execution are ON sa.wf_instance_code = are.PROC_INST_ID_
      WHERE
        sa.apply_type = 9
        AND are.ACT_ID_ = 'revice-060'
        AND sa.is_deleted = 0
        AND sar.t_apply_id IS NULL
        <if test="nameOrCode !=null and nameOrCode !=''">
          and (sa.store_name like #{nameOrCode} or sa.store_code like #{nameOrCode})
        </if>
    </select>
  <select id="queryReviceRedecorates"
          resultType="com.fotile.activiti.workflow.business.pojo.entity.ApplyStore">
    SELECT sa.* FROM s_apply sa
                       LEFT JOIN act_ru_execution arex ON sa.wf_instance_code=arex.PROC_INST_ID_
    WHERE sa.store_id =#{storeId}
      AND sa.apply_type = 9
      AND arex.ACT_ID_ = 'revice-060'
      AND sa.is_deleted = 0
  </select>
    <select id="getNotCompleteWrongProccess" resultType="java.lang.String">
      SELECT sa.wf_instance_code FROM s_apply sa INNER JOIN s_apply_redecorate sar ON sa.id=sar.apply_id
      WHERE sa.is_deleted=0 AND sa.apply_status=1
        AND EXISTS(SELECT are.ID_ FROM act_ru_execution are WHERE are.PROC_INST_ID_=sa.wf_instance_code AND are.ACT_ID_='revice-060')
        AND NOT EXISTS( SELECT art.ID_ FROM act_ru_task art WHERE art.PROC_INST_ID_=sa.wf_instance_code AND art.TASK_DEF_KEY_='revice-060')
        AND  EXISTS (
              SELECT ta.id FROM t_apply ta
                                  LEFT JOIN t_apply_normal tan ON ta.id=tan.apply_id
                                  LEFT JOIN t_apply_large tal ON ta.id=tal.apply_id
              WHERE ta.store_code=sa.store_code AND ta.type IN(3,4) AND ta.is_deleted=0 AND
                (tan.decorate_type=2 OR tal.decorate_type=2)
                AND ta.`status`=2

        )
    </select>
    <update id="updateByExampleSelective" parameterType="map">
    update s_apply_redecorate
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.applyId != null">
        apply_id = #{record.applyId,jdbcType=INTEGER},
      </if>
      <if test="record.decorateType != null">
        decorate_type = #{record.decorateType,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null">
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null">
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null">
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update s_apply_redecorate
    set id = #{record.id,jdbcType=INTEGER},
      apply_id = #{record.applyId,jdbcType=INTEGER},
      decorate_type = #{record.decorateType,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.Redecorate">
    update s_apply_redecorate
    <set>
      <if test="applyId != null">
        apply_id = #{applyId,jdbcType=INTEGER},
      </if>
      <if test="decorateType != null">
        decorate_type = #{decorateType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="myUpdateByPrimaryKey" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.redecorate.Redecorate">
    update s_apply_redecorate
    set apply_id = #{applyId,jdbcType=INTEGER},
      decorate_type = #{decorateType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <update id="setTApplyId">
      UPDATE s_apply_redecorate
      SET t_apply_id = #{tApplyId}
      WHERE
        apply_id IN
    <foreach collection="applyIds" item="applyId" index="index" open="(" separator="," close=")">
      #{applyId}
    </foreach>
    </update>
</mapper>