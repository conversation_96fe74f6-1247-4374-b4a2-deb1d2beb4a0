<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.fission.dao.CustomerShareInfoMapper">
  <resultMap id="BaseResultMap" type="com.fotile.customercenter.fission.pojo.entity.CustomerShareInfo">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="trace_code" jdbcType="VARCHAR" property="traceCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="share_count" jdbcType="VARCHAR" property="shareCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
<sql id="Base_Column_List">
	  	  	      	id,
       	  	      	customer_id,
       	  	      	activity_id,
       	  	      	trace_code,
       	  	      	is_deleted,
       	  	      	created_by,
       	  	      	created_date,
       	  	      	modified_by,
       	  	      	modified_date,
       	  	    	  	remark,
       	  	    	  	share_count
       	</sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from customer_share_info
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" parameterType="com.fotile.customercenter.fission.pojo.entity.CustomerShareInfo">
    insert into customer_share_info (
      	  	       id,
       	  	       customer_id,
       	  	       activity_id,
       	  	       trace_code,
       	  	       is_deleted,
       	  	       created_by,
       	  	       created_date,
       	  	       modified_by,
       	  	       modified_date,
       	  	    	  remark,share_count
       	    )
    values (
	  	  	      	#{id,jdbcType=BIGINT},
       	  	      	#{customerId,jdbcType=BIGINT},
       	  	      	#{activityId,jdbcType=BIGINT},
       	  	      	#{traceCode,jdbcType=VARCHAR},
       	  	      	#{isDeleted,jdbcType=TINYINT},
       	  	      	#{createdBy,jdbcType=VARCHAR},
       	  	      	#{createdDate,jdbcType=TIMESTAMP},
       	  	      	#{modifiedBy,jdbcType=VARCHAR},
       	  	      	#{modifiedDate,jdbcType=TIMESTAMP},
       	  	    	  #{remark,jdbcType=VARCHAR},
       	  	    	   #{shareCount}
       	    )
  </insert>
  
  <insert id="insertSelective" parameterType="com.fotile.customercenter.fission.pojo.entity.CustomerShareInfo">
    insert into customer_share_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
          
      <if test="id != null">
        id,
      </if>
      
         
      <if test="customerId != null">
        customer_id,
      </if>
      
         
      <if test="activityId != null">
        activity_id,
      </if>
      
         
      <if test="traceCode != null">
        trace_code,
      </if>

      
         
      <if test="isDeleted != null">
        is_deleted,
      </if>
      
         
      <if test="createdBy != null">
        created_by,
      </if>
      
         
      <if test="createdDate != null">
        created_date,
      </if>
      
         
      <if test="modifiedBy != null">
        modified_by,
      </if>
      
         
      <if test="modifiedDate != null">
        modified_date,
      </if>
      
         
      <if test="remark != null">
        remark,
      </if>
      <if test="shareCount != null">
        share_count,
      </if>


         </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
          <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
          <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
          <if test="traceCode != null">
        #{traceCode,jdbcType=VARCHAR},
      </if>
          <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
          <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
          <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
          <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
          <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
          <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>

      <if test="shareCount != null">
        #{shareCount},
      </if>


        </trim>
  </insert>
  


  

  
      <update id="updateByPrimaryKeySelective" parameterType="com.fotile.customercenter.fission.pojo.entity.CustomerShareInfo">
    update customer_share_info
    <set>
                          <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
                      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
                      <if test="traceCode != null">
        trace_code = #{traceCode,jdbcType=VARCHAR},
      </if>

                      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
                      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
                      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
                      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
                      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
                      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>

      <if test="shareCount != null">
        share_count = #{shareCount},
      </if>

              </set>
    where 
            id = #{id,jdbcType=BIGINT}
                                                                                          
  </update>

      	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	

  <update id="updateByPrimaryKey" parameterType="com.fotile.customercenter.fission.pojo.entity.CustomerShareInfo">
    update customer_share_info
    set 
             	        	    	 			customer_id = #{customerId,jdbcType=BIGINT},
		     	        	    	 			activity_id = #{activityId,jdbcType=BIGINT},
		     	        	    	 			trace_code = #{traceCode,jdbcType=VARCHAR},
		     	        	    	 			is_deleted = #{isDeleted,jdbcType=TINYINT},
		     	        	    	 			created_by = #{createdBy,jdbcType=VARCHAR},
		     	        	    	 			created_date = #{createdDate,jdbcType=TIMESTAMP},
		     	        	    	 			modified_by = #{modifiedBy,jdbcType=VARCHAR},
		     	        	    	 			modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
		     	        	    	 			remark = #{remark,jdbcType=VARCHAR},
		     	        	    	 			share_count = #{shareCount}
		     	        where 
            id = #{id,jdbcType=BIGINT}
                                                                                            
  </update>


  <select id="selectByCustomerIdAndActivityId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from customer_share_info
    where is_deleted = 0
    and customer_id = #{customerId}
    and activity_id = #{activityId}
  </select>

  <select id="getByTraceCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from
      customer_share_info
    where
      trace_code = #{traceCode}
    limit 1
  </select>

  <select id="getCustomerShareRecordByCustomerIdAndActivityId" resultType="java.lang.Integer">
    select count(1) from customercenter.customer_share_info
    where customer_id = #{customerId} and activity_id = #{activityId}
    <if test="startTime != null and startTime != ''">
        and created_date &gt;= #{startTime}
    </if>
    <if test="endTime != null and endTime != ''">
        and created_date &lt;= #{endTime}
    </if>
    and is_deleted = 0 ;
  </select>

  <select id="migrateUpdate" resultType="java.lang.Integer">
    update
    customercenter.customer_share_info
    set customer_id= #{customerId}
    where
    customer_id = #{oldCustomerId}
    <if test="activityIds != null and activityIds.size() != 0">
      <foreach collection="activityIds" item = "item" separator="," open="and activity_id in (" close=")">
        #{item,jdbcType=BIGINT}
      </foreach>
    </if>
  </select>

</mapper>