<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.devops.serviceRequest.dao.TServiceRequestIssueMappingMapper">
  <resultMap id="BaseResultMap" type="com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestIssueMapping">
    <!--@mbg.generated-->
    <!--@Table devops.t_service_request_issue_mapping-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="service_request_id" jdbcType="BIGINT" property="serviceRequestId" />
    <result column="issue_id" jdbcType="BIGINT" property="issueId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_deleted, created_by, created_date, modified_by, modified_date, service_request_id, 
    issue_id
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update devops.t_service_request_issue_mapping
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="created_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="modified_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="modified_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="service_request_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.serviceRequestId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="issue_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.issueId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update devops.t_service_request_issue_mapping
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="modified_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="modified_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="service_request_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceRequestId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.serviceRequestId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="issue_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.issueId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.issueId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into devops.t_service_request_issue_mapping
    (is_deleted, created_by, created_date, modified_by, modified_date, service_request_id, 
      issue_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.isDeleted,jdbcType=BIGINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP}, 
        #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, #{item.serviceRequestId,jdbcType=BIGINT}, 
        #{item.issueId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestIssueMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into devops.t_service_request_issue_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      is_deleted,
      created_by,
      created_date,
      modified_by,
      modified_date,
      service_request_id,
      issue_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{isDeleted,jdbcType=BIGINT},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{serviceRequestId,jdbcType=BIGINT},
      #{issueId,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      is_deleted = #{isDeleted,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      service_request_id = #{serviceRequestId,jdbcType=BIGINT},
      issue_id = #{issueId,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestIssueMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into devops.t_service_request_issue_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
      <if test="serviceRequestId != null">
        service_request_id,
      </if>
      <if test="issueId != null">
        issue_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceRequestId != null">
        #{serviceRequestId,jdbcType=BIGINT},
      </if>
      <if test="issueId != null">
        #{issueId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceRequestId != null">
        service_request_id = #{serviceRequestId,jdbcType=BIGINT},
      </if>
      <if test="issueId != null">
        issue_id = #{issueId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>