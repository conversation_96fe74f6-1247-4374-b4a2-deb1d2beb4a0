<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.macenter.rule.dao.TRuleTemplateMappingDao">

    <resultMap type="com.fotile.macenter.rule.pojo.entity.TRuleTemplateMapping" id="TRuleTemplateMappingMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="reviseName" column="revise_name" jdbcType="VARCHAR"/>
        <result property="reviseTime" column="revise_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="templateId" column="template_id" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TRuleTemplateMappingMap">
        select
          id, is_deleted, created_by, created_date, modified_by, modified_date, revise_name, revise_time, remark, rule_id, template_id, sort
        from msgcenter.t_rule_template_mapping
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TRuleTemplateMappingMap">
        select
          id, is_deleted, created_by, created_date, modified_by, modified_date, revise_name, revise_time, remark, rule_id, template_id, sort
        from msgcenter.t_rule_template_mapping
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="TRuleTemplateMappingMap">
        select
        id, is_deleted, created_by, created_date, modified_by, modified_date, revise_name, revise_time, remark, rule_id,
        template_id, sort
        from msgcenter.t_rule_template_mapping
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                and modified_by = #{modifiedBy}
            </if>
            <if test="modifiedDate != null">
                and modified_date = #{modifiedDate}
            </if>
            <if test="reviseName != null and reviseName != ''">
                and revise_name = #{reviseName}
            </if>
            <if test="reviseTime != null">
                and revise_time = #{reviseTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="ruleId != null">
                and rule_id = #{ruleId}
            </if>
            <if test="templateId != null">
                and template_id = #{templateId}
            </if>
            <if test="sort != null">
                and sort = #{sort}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insertOne" keyProperty="id" useGeneratedKeys="true">
        insert into msgcenter.t_rule_template_mapping(is_deleted, created_by, created_date, modified_by, modified_date, revise_name, revise_time, remark, rule_id, template_id, sort)
        values (#{isDeleted}, #{createdBy}, now(), #{modifiedBy}, now(), #{reviseName}, #{reviseTime}, #{remark}, #{ruleId}, #{templateId}, #{sort})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into msgcenter.t_rule_template_mapping(is_deleted, created_by, created_date, modified_by, modified_date,
        revise_name, revise_time, remark, rule_id, template_id, sort)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDeleted}, #{entity.createdBy}, #{entity.createdDate}, #{entity.modifiedBy},
            #{entity.modifiedDate}, #{entity.reviseName}, #{entity.reviseTime}, #{entity.remark}, #{entity.ruleId},
            #{entity.templateId}, #{entity.sort})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into msgcenter.t_rule_template_mapping(is_deleted, created_by, created_date, modified_by, modified_date,
        revise_name, revise_time, remark, rule_id, template_id, sort)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDeleted}, #{entity.createdBy}, #{entity.createdDate}, #{entity.modifiedBy},
            #{entity.modifiedDate}, #{entity.reviseName}, #{entity.reviseTime}, #{entity.remark}, #{entity.ruleId},
            #{entity.templateId}, #{entity.sort})
        </foreach>
        on duplicate key update
        is_deleted = values(is_deleted) , created_by = values(created_by) , created_date = values(created_date) ,
        modified_by = values(modified_by) , modified_date = values(modified_date) , revise_name = values(revise_name) ,
        revise_time = values(revise_time) , remark = values(remark) , rule_id = values(rule_id) , template_id =
        values(template_id) , sort = values(sort)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update msgcenter.t_rule_template_mapping
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate},
            </if>
            <if test="reviseName != null and reviseName != ''">
                revise_name = #{reviseName},
            </if>
            <if test="reviseTime != null">
                revise_time = #{reviseTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from msgcenter.t_rule_template_mapping where id = #{id}
    </delete>

    <update id="updateByTemplate">
        update  msgcenter.t_rule_template_mapping set is_deleted=1 where template_id=#{templateId}
    </update>

</mapper>