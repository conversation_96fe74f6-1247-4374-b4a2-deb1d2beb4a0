<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.cmscenter.adviertisement.dao.TAdverMapper">
    <resultMap id="TAdverMap" type="com.fotile.cmscenter.adviertisement.pojo.entity.TAdver">
        <result column="id" property="id"/>
        <result column="adverposition_id" property="adverpositionId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="name" property="name"/>
        <result column="adver_type" property="adverType"/>
        <result column="content" property="content"/>
        <result column="url" property="url"/>
        <result column="term_start" property="termStart"/>
        <result column="term_end" property="termEnd"/>
        <result column="sort" property="sort"/>
        <result column="jump_type" property="jumpType"/>
        <result column="choose_from_id" property="chooseFromId"/>
        <result column="choose_from_name" property="chooseFromName"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="abstract_note" property="abstractNote"/>
        <result column="video_url" property="videoUrl"/>
        <result column="video_cover_url" property="videoCoverUrl"/>
        <result column="stage" property="stage"/>
        <result column="video_id" property="videoId"/>
        <result column="is_leave" property="isLeave"/>
        <result column="corner" property="corner"/>
        <result column="content_title" property="contentTitle"/>
        <result column="is_content_img" property="isContentImg"/>
        <result column="is_content_note" property="isContentNote"/>
        <result column="app_id" property="appId"/>
    </resultMap>


    <sql id="Base_Column_List">

    </sql>

    <sql id="Base_Column_Field">
            `id`,
            `adverposition_id`,
            `is_deleted`,
            `created_by`,
            `created_date`,
            `modified_by`,
            `modified_date`,
            `name`,
            `adver_type`,
            `content`,
            `url`,
            `term_start`,
            `term_end`,
            `sort`,
            `jump_type`,
            `choose_from_id`,
            `choose_from_name`,
            `picture_url`,
            `abstract_note`,
            `video_url`,
            `video_cover_url`,
            `stage`,
            `video_id`,
            `is_leave`,
            `corner`,
            `content_title`,
            `is_content_img`,
            `is_content_note`,
            `app_id`
    </sql>

</mapper>