<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.fss.dao.live.LiveRoomDAO">

    <!-- 查询在线的直播列表 -->
    <select id="listOnlineRoom" resultType="com.fotile.fss.pojo.dto.live.ListLiveOnlineRoomDTO">
        SELECT
            a.logo,
            b.rtmp,
            b.flv,
            b.hls,
            a.room_id 'roomId',
            b.master_user_id 'masterUserId',
            a.title,
            a.location_address 'locationAddress',
            a.id 'streamId',
            a.purchase_url as purchaseUrl
        FROM fss_live_stream a
            LEFT JOIN fss_live_room b ON a.room_id = b.id
        WHERE
            a.state = 1 AND a.is_deleted = 0 AND b.is_deleted = 0
            <if test="null != streamId">
                and a.id = #{streamId}
            </if>
    </select>
</mapper>