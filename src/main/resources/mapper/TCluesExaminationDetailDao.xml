<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.exportcenter.cluesExaminationDetail.dao.TCluesExaminationDetailDao">

    <select id="findttributeByCompanyIds"
            resultType="com.fotile.exportcenter.marketing.pojo.dto.FindAttributeValueOutDto">
        SELECT *
        FROM marketingcenter.attribute
        WHERE  is_deleted=0 and crux_flag = 0
        <if test="companyIds != null and companyIds.size >0">

            and org_id in (
            <foreach collection="companyIds" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ORDER BY created_date desc
    </select>

    <!--查询服务报告列表-->
    <select id="queryExaminationExportListUseSalesman" resultType="com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto">
        SELECT
            d.id,
            d.type typeId,
            CASE d.type when 1 then '厨房健康报告' when 2 then '清洗服务报告' when 3 then '厨房健康报告'   else '厨电选购指南' end as typeName,
            d.created_by createdBy,
            c.charge_code chargeCode,
            c.charge_user_name chargeUserName,
            IF(cs.id is null, '(空)', CONCAT(cs.`code`, '/', cast(AES_DECRYPT_MY(UNHEX(cs.`name`), #{inDto.secretKey}) as char))) as createdPerson,
            d.salesman_id belongSalesmanId,
            IF(bs.id is null, '(空)', CONCAT(bs.`code`, '/', cast(AES_DECRYPT_MY(UNHEX(bs.`name`), #{inDto.secretKey}) as char))) as belongPerson,
            d.clues_id cluesId,
            c.customer_phone as cluesCustomerPhone,
            dic.value_code as districtCode,
            dic.value_name as districtValue,
            gw.value_name as createdPersonStationName,
            oc.id companyId,
            oc.name companyName,
            os.id stroeId,
            os.`code` stroeCode,
            os.name stroeName,
            ts.abbreviation as storeAbbreName,
            IFNULL(ts.abbreviation,os.name) as storeCombName,
            CONCAT(os.`code`,'/',IFNULL(ts.abbreviation,os.name)) stroeFullName,
            IF(YEAR(ts.terminal_check_date) = YEAR(CURDATE()),'是','否') as isNewStoreName,
<!--            c.customer_phone customerPhone,-->
<!--            c.customer_name customerName,-->
            CASE d.type when 1 then r.phone when 2 then c.customer_phone when 3 then r.phone  else c.customer_phone end as customerPhone,
            CASE d.type when 1 then r.nick_name when 2 then c.customer_name when 3 then r.nick_name  else c.customer_name end as customerName,
            c.gender gender,
            CASE c.gender WHEN 1 THEN '男' WHEN 2 THEN '女' ELSE '未知' END AS genderName,
            c.house_type houseType,
            c.address_id addressId,
            c.clues_source cluesSource,
            c.clues_source_value cluesSourceValue,
            c.audit_status cluesAuditStatus,
            CASE c.audit_status when '40' then '未提交' when '1' then '待审核' when '2' then '审核通过' when '3' then '审核不通过' else '空' end as cluesAuditStatusName,
            CASE d.check_count WHEN 0 THEN '/' ELSE d.check_count END AS checkCount,
            d.recheck_flag recheckFlag,
            CASE d.recheck_flag WHEN 1 THEN '是' ELSE '否' END AS recheckFlagName,
            d.created_date createdDate,
            d.view_count viewCount,
            d.share_count shareCount,
            CASE d.type when 1 then r.province_name when 3 then r.province_name  else c.province end as province,
            CASE d.type when 1 then r.city_name when 3 then r.city_name  else c.city end as city,
            CASE d.type when 1 then r.area_name when 3 then r.area_name  else c.area end as area,
            CASE d.type when 1 then r.community_id when 3 then r.community_id  else c.village_id end as villageId,
            CASE d.type when 1 then r.community_name when 3 then r.community_name  else c.village_name end as villageName,
            tsoi.id as serviceOrderId,
            tsoi.qywx_external_username as customerNickname,
            khr.id  AS kitchenHealthReportId,
            case when d.type = 1 then d.range_hood_wind_speed when d.type = 3 then khr.yyj_fs else null end as hoodBlowingRate,
            case when d.type = 1 then d.tds_value when d.type = 3 then khr.tds else null end as tds,

            case when d.type = 1 and d.cookers_blow_flag = 1 then '是'
            when d.type = 1 and d.cookers_blow_flag = 0 then '否'
            when d.type = 3 then khr.zj_lq else null end as gasBlow,

            case when d.type = 1 and d.hood_out_period_flag = 1 then '是'
            when d.type = 1 and d.hood_out_period_flag = 0 then '否'
            when d.type = 3 then khr.zj_cl else null end as gasOutPeriod,

            case when d.type = 1 and d.heater_blow_flag = 1 then '是'
            when d.type = 1 and d.heater_blow_flag = 0 then '否'
            when d.type = 3 then khr.rsq_lq else null end as heaterBlow,

            case when d.type = 3 then khr.rsq_cl else null end as heaterOutPeriod,

            case when cmi.customer_id is null then '否' else '是' end as isVip,
            case when dii.clues_id is null then '否' else '是' end as isDeal,
            dii.audit_date as auditDate,
            case when cdbl.id is null then '否' else '是' end as isEquity,
            cc3.name as storeChannelName,
            IF(cc2.id is null,'', concat(cc1.name,'/',cc2.name)) as distributorChannelCombName,
            td.name as distributorName,
            cc1.name as distributorChannelCategoryName
        FROM marketingcenter.t_clues_examination_detail d

                <!--inner join (
                select d1.id,
                       s.id                                                                                               as createdSalesmanId,
                       IF(s.id is null, '(空)', CONCAT(s.`code`, '/',
                                                      cast(AES_DECRYPT_MY(UNHEX(s.`name`), #{inDto.secretKey}) as char))) as createdPerson
                FROM marketingcenter.t_clues_examination_detail d1
                         left join orgcenter.t_salesman s
                                   on s.is_deleted = 0 and s.id = d1.created_by
                WHERE d1.is_deleted = 0
                  AND d1.type = 4

                UNION ALL

                select d2.id,
                       s.id               as createdSalesmanId,
                       CASE
                           WHEN s.id is not null THEN CONCAT(s.`code`, '/',
                                                             cast(AES_DECRYPT_MY(UNHEX(s.`name`), #{inDto.secretKey}) as char))
                           WHEN uee.id is not null THEN cast(AES_DECRYPT_MY(UNHEX(uee.first_name),
                                                                            #{inDto.secretKey}) as char)
                           else '(空)' end as createdPerson
                FROM marketingcenter.t_clues_examination_detail d2
                         left join usercenter.user_entity_extend uee
                                   on uee.is_deleted = 0 and uee.user_entity_id = d2.created_by
                         left join orgcenter.t_salesman s
                                   on s.is_deleted = 0 and s.id = uee.salesman_id
                WHERE d2.is_deleted = 0
                  AND d2.type in (1, 2, 3)
                <if test="inDto.createdByAccountIds != null and inDto.createdByAccountIds.size() > 0">
                    and uee.user_entity_id in
                    <foreach collection="inDto.createdByAccountIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) dd on dd.id = d.id-->

        <if test=" inDto.calcStartDate != null or inDto.calcEndDate != null">
            inner join (
                select distinct
                    bc.examination_detail_id
                from marketingcenter.t_clues_examination_detail_bjt_count bc
                where
                    bc.is_deleted = 0
                    <if test=" inDto.calcStartDate != null">
                        and bc.calc_date &gt;= #{inDto.calcStartDate}
                    </if>
                    <if test=" inDto.calcEndDate != null">
                        and bc.calc_date &lt;= #{inDto.calcEndDate}
                    </if>
            ) tt on tt.examination_detail_id = d.id
        </if>
        left JOIN marketingcenter.user_clues c on c.id = d.clues_id <!--and c.is_deleted = 0-->
        <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
            and c.id in
            <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join orgcenter.t_salesman bs on bs.is_deleted = 0 and bs.id = d.salesman_id
        left join orgcenter.t_salesman cs on cs.is_deleted = 0 and cs.id = d.created_salesman_id
        left join orgcenter.t_org os on os.is_deleted = 0 and os.type = 3 and os.id = cs.store_id
        left join orgcenter.t_org oc on oc.is_deleted = 0 and oc.type = 1 and oc.id = cs.company_id
        left join orgcenter.t_store ts on ts.is_deleted = 0 and ts.org_id = os.id
        left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = oc.id
        left join systemcenter.dic dic on dic.is_deleted = 0 and dic.type_code = '所属大区' and dic.id = tc.area
        left join systemcenter.dic gw on gw.is_deleted = 0 and gw.type_code = 'gw' and gw.id = cs.station
        LEFT JOIN orgcenter.channel_category cc3 on cc3.id = ts.store_type
        LEFT JOIN orgcenter.t_distributor_mapping tdm
                  ON tdm.is_deleted = 0 AND ts.org_id = tdm.org_id AND tdm.type = 3
        LEFT JOIN orgcenter.t_distributor td ON td.is_deleted = 0 AND tdm.distributor_id = td.id
        LEFT JOIN orgcenter.channel_category cc1 on cc1.id = td.channel_category
        LEFT JOIN orgcenter.channel_category cc2 on cc2.id = td.channel_subdivide
        LEFT JOIN marketingcenter.kitchen_health_report r on r.is_deleted = 0 and r.medical_report_id = d.id
        LEFT JOIN omscenter.t_service_order_info tsoi on tsoi.is_deleted = 0 and tsoi.examination_id = d.id

        left join	(
						SELECT
							r.id,
							r.medical_report_id,
							GROUP_CONCAT( IF ( qm.question_id = 13, qm.user_input, NULL ) SEPARATOR '' ) as yyj_fs,
							GROUP_CONCAT( IF ( qm.question_id = 18, qm.user_input, NULL ) SEPARATOR '' ) as tds,
                            if(GROUP_CONCAT( IF ( qm.question_id = 2, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as zj_lq,-- 灶具是否存在燃气泄漏隐患
                            if(GROUP_CONCAT( IF ( qm.question_id = 3, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as zj_cl,-- 灶具是否超龄使用
                            if(GROUP_CONCAT( IF ( qm.question_id = 5, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as rsq_lq,-- 热水器是否存在燃气泄漏隐患
                            if(GROUP_CONCAT( IF ( qm.question_id = 6, qm.user_choose, NULL ) SEPARATOR '' )='1','是','否') as rsq_cl -- 热水器是否超龄使用
						FROM
							marketingcenter.kitchen_health_report r
							INNER JOIN marketingcenter.kitchen_health_report_question_mapping qm ON qm.is_deleted = 0
							AND qm.report_id = r.id
						WHERE
							r.is_deleted = 0
							AND qm.question_id IN ( 13, 18, 2, 3, 5, 6 )
						GROUP BY
							r.id,
						r.medical_report_id
			) khr ON khr.medical_report_id = d.id
        left join (
			SELECT
				di.clues_id,
				max(di.audit_date ) as audit_date
			FROM
				marketingcenter.t_clues_examination_detail dd
				INNER JOIN omscenter.declaration_info di ON  di.clues_id = dd.clues_id and di.is_deleted = 0
				AND di.stage = 2
				AND di.order_stage !=4
                <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
                    and di.clues_id in
                    <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

			WHERE
				dd.is_deleted = 0 and
                TIMESTAMPDIFF(SECOND,dd.created_date,di.order_create_time)>=-48*3600
				GROUP BY
				di.clues_id
			) dii on dii.clues_id = d.clues_id
			left join customercenter.customer_info ci on ci.is_deleted = 0 and ci.type =1 and ci.phone = c.customer_phone
			left join (select DISTINCT cmiTemp.customer_id from customercenter.t_company_member_info cmiTemp where cmiTemp.is_deleted = 0) cmi on  cmi.customer_id = ci.id
            LEFT JOIN (select distinct cdbl_temp.source_id as  id from promotioncenter.exchange_card_detail_bjt_log cdbl_temp where cdbl_temp.is_deleted = 0
            AND cdbl_temp.biz_type IN ( 3, 4 ) ) cdbl on cdbl.id = d.id
        <where>
            d.is_deleted = 0
            <if test="inDto.ids != null and inDto.ids.size > 0">
                and d.id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.districtCode != null and inDto.districtCode != ''">
                and dic.value_code = #{inDto.districtCode}
            </if>
            <if test="inDto.companyId != null">
                and cs.company_id = #{inDto.companyId}
            </if>
            <if test="inDto.storeOrgId != null">
                and cs.store_id = #{inDto.storeOrgId}
            </if>
            <if test="inDto.chargeId != null and inDto.chargeId != ''">
                and cs.id = #{inDto.chargeId}
            </if>
            <if test="inDto.belongSalesmanId != null">
                and d.salesman_id = #{inDto.belongSalesmanId}
            </if>
            <if test="inDto.type != null">
                <choose>
                    <when test="inDto.type == 1">
                        and d.type in (1,3)
                    </when>
                    <otherwise>
                        and d.type = #{inDto.type}
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.phone != null and inDto.phone != ''">
                and
                    (
                        (d.type in (1, 3)
                            and r.phone =
                                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                        (d.type in (2, 4)
                            and c.customer_phone =
                                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                        )
            </if>
            <if test="inDto.name != null and inDto.name != ''">
                and
                    (
                        (d.type in (1, 3)
                            and r.nick_name =
                                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                            or
                        (d.type in (2, 4)
                            and c.customer_name =
                                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                        )
            </if>
            <if test="inDto.startTime != null and inDto.endTime != null">
                and d.created_date between #{inDto.startTime} and #{inDto.endTime}
            </if>
            <if test="inDto.authorCompanyIds != null and inDto.authorCompanyIds.size > 0">
                and cs.company_id in
                <foreach collection="inDto.authorCompanyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="inDto.cluesId != null">
                <if test="inDto.cluesId == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesId != '-1'">
                    and d.clues_id = #{inDto.cluesId}
                </if>
            </if>
            <if test="inDto.cluesAuditStatus != null and inDto.cluesAuditStatus != ''">
                <if test="inDto.cluesAuditStatus == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesAuditStatus != '-1'">
                    and c.audit_status = #{inDto.cluesAuditStatus}
                </if>
            </if>
            <if test="inDto.isNewStore != null">
                <if test="inDto.isNewStore == 1">
                    and YEAR(ts.terminal_check_date) = YEAR(CURDATE())
                </if>
                <if test="inDto.isNewStore == 2">
                    and (ts.terminal_check_date is null or YEAR(ts.terminal_check_date) != YEAR(CURDATE()))
                </if>
            </if>
            <!--门店渠道支持多选-->
            <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="inDto.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != '' and inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <if test="inDto.isAssociateServiceOrder != null">
                <if test="inDto.isAssociateServiceOrder == 0">
                    and tsoi.id is null
                </if>
                <if test="inDto.isAssociateServiceOrder == 1">
                    and tsoi.id is not null
                </if>
            </if>

            <if test="inDto.isAssociateCustomer != null">
                <if test="inDto.isAssociateCustomer == 0">
                    and tsoi.qywx_external_username is null
                </if>
                <if test="inDto.isAssociateCustomer == 1">
                    and tsoi.qywx_external_username is not null
                </if>
            </if>

        </where>
        ORDER BY d.created_date desc
        limit #{pageInfo.offset}, #{pageInfo.size}
    </select>

    <select id="queryExaminationListCountUseSalesman" resultType="java.lang.Long">
        SELECT
        count(d.id)
        FROM marketingcenter.t_clues_examination_detail d
            <!--inner join (
            select d1.id,
                   s.id as createdSalesmanId
            FROM marketingcenter.t_clues_examination_detail d1
                     left join orgcenter.t_salesman s on s.is_deleted = 0 and s.id = d1.created_by
            WHERE d1.is_deleted = 0
              AND d1.type = 4

            UNION ALL

            select d2.id,
                   s.id as createdSalesmanId
            FROM marketingcenter.t_clues_examination_detail d2
                     left join usercenter.user_entity_extend uee
                               on uee.is_deleted = 0 and uee.user_entity_id = d2.created_by
                     left join orgcenter.t_salesman s on s.is_deleted = 0 and s.id = uee.salesman_id
            WHERE d2.is_deleted = 0
              AND d2.type in (1, 2, 3)
                <if test="inDto.createdByAccountIds != null and inDto.createdByAccountIds.size() > 0">
                    and uee.user_entity_id in
                    <foreach collection="inDto.createdByAccountIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
        ) dd on dd.id = d.id-->
        <if test=" inDto.calcStartDate != null or inDto.calcEndDate != null">
            inner join (
            select distinct
            bc.examination_detail_id
            from marketingcenter.t_clues_examination_detail_bjt_count bc
            where
            bc.is_deleted = 0
            <if test=" inDto.calcStartDate != null">
                and bc.calc_date &gt;= #{inDto.calcStartDate}
            </if>
            <if test=" inDto.calcEndDate != null">
                and bc.calc_date &lt;= #{inDto.calcEndDate}
            </if>
            ) tt on tt.examination_detail_id = d.id
        </if>
        left JOIN marketingcenter.user_clues c on c.id = d.clues_id <!--and c.is_deleted = 0-->
        <if test="inDto.cluesIds != null and inDto.cluesIds.size() > 0">
            and c.id in
            <foreach collection="inDto.cluesIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        left join orgcenter.t_salesman cs on cs.is_deleted = 0 and cs.id = d.created_salesman_id
        left join orgcenter.t_salesman bs on bs.is_deleted = 0 and bs.id = d.salesman_id
        left join orgcenter.t_store ts on ts.is_deleted = 0 and ts.org_id = cs.store_id
        <if test="inDto.districtCode != null and inDto.districtCode != ''">
            left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = cs.company_id
            left join systemcenter.dic dic on dic.is_deleted = 0 and dic.type_code = '所属大区' and dic.id = tc.area
        </if>

        <if test="(inDto.phone != null and inDto.phone != '') or (inDto.name != null and inDto.name != '')">
            LEFT JOIN marketingcenter.kitchen_health_report r on r.is_deleted = 0 and r.medical_report_id = d.id
        </if>

        <if test="inDto.isAssociateServiceOrder != null or inDto.isAssociateCustomer != null">
            LEFT JOIN omscenter.t_service_order_info tsoi on tsoi.is_deleted = 0 and tsoi.examination_id = d.id
        </if>

        <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
            LEFT JOIN orgcenter.channel_category cc3 on cc3.id = ts.store_type
        </if>

        <if test="(inDto.channelCategoryIds != null and inDto.channelCategoryIds != '')
        or (inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != '')">
            LEFT JOIN orgcenter.t_distributor_mapping tdm
                      ON tdm.is_deleted = 0 AND ts.org_id = tdm.org_id AND tdm.type = 3
            LEFT JOIN orgcenter.t_distributor td ON td.is_deleted = 0 AND tdm.distributor_id = td.id
            LEFT JOIN orgcenter.channel_category cc1 on cc1.id = td.channel_category
            LEFT JOIN orgcenter.channel_category cc2 on cc2.id = td.channel_subdivide
        </if>
        <where>
            d.is_deleted = 0
            <if test="inDto.ids != null and inDto.ids.size > 0">
                and d.id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.districtCode != null and inDto.districtCode != ''">
                and dic.value_code = #{inDto.districtCode}
            </if>
            <if test="inDto.companyId != null">
                and cs.company_id = #{inDto.companyId}
            </if>
            <if test="inDto.storeOrgId != null">
                and cs.store_id = #{inDto.storeOrgId}
            </if>
            <if test="inDto.chargeId != null and inDto.chargeId != ''">
                and cs.id = #{inDto.chargeId}
            </if>
            <if test="inDto.belongSalesmanId != null">
                and d.salesman_id = #{inDto.belongSalesmanId}
            </if>
            <if test="inDto.type != null">
                <choose>
                    <when test="inDto.type == 1">
                        and d.type in (1,3)
                    </when>
                    <otherwise>
                        and d.type = #{inDto.type}
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.phone != null and inDto.phone != ''">
                and
                (
                (d.type in (1, 3)
                and r.phone =
                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                or
                (d.type in (2, 4)
                and c.customer_phone =
                #{inDto.phone,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                )
            </if>
            <if test="inDto.name != null and inDto.name != ''">
                and
                (
                (d.type in (1, 3)
                and r.nick_name =
                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                or
                (d.type in (2, 4)
                and c.customer_name =
                #{inDto.name,jdbcType=VARCHAR,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler})
                )
            </if>
            <if test="inDto.startTime != null and inDto.endTime != null">
                and d.created_date between #{inDto.startTime} and #{inDto.endTime}
            </if>
            <if test="inDto.authorCompanyIds != null and inDto.authorCompanyIds.size > 0">
                and cs.company_id in
                <foreach collection="inDto.authorCompanyIds" item="companyId" open="(" separator="," close=")">
                    #{companyId}
                </foreach>
            </if>
            <if test="inDto.cluesId != null">
                <if test="inDto.cluesId == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesId != '-1'">
                    and d.clues_id = #{inDto.cluesId}
                </if>
            </if>
            <if test="inDto.cluesAuditStatus != null and inDto.cluesAuditStatus != ''">
                <if test="inDto.cluesAuditStatus == '-1'">
                    and d.clues_id is null
                </if>
                <if test="inDto.cluesAuditStatus != '-1'">
                    and c.audit_status = #{inDto.cluesAuditStatus}
                </if>
            </if>
            <if test="inDto.isNewStore != null">
                <if test="inDto.isNewStore == 1">
                    and YEAR(ts.terminal_check_date) = YEAR(CURDATE())
                </if>
                <if test="inDto.isNewStore == 2">
                    and (ts.terminal_check_date is null or YEAR(ts.terminal_check_date) != YEAR(CURDATE()))
                </if>
            </if>
            <!--门店渠道支持多选-->
            <if test="inDto.storeChannelCode != null and inDto.storeChannelCode != ''">
                AND cc3.code in
                <foreach collection="inDto.storeChannelCode.split(',')" item="scc" open="(" separator="," close=")">
                    #{scc}
                </foreach>
            </if>

            <!--客户渠道支持多选-->
            <choose>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != '' and inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    and (
                    cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    or cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                    )
                </when>
                <when test="inDto.channelCategoryIds != null and inDto.channelCategoryIds != ''">
                    AND cc1.id in
                    <foreach collection="inDto.channelCategoryIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
                <when test="inDto.channelSubdivideIds != null and inDto.channelSubdivideIds != ''">
                    AND cc2.id in
                    <foreach collection="inDto.channelSubdivideIds.split(',')" item="cid" open="(" separator=","
                             close=")">
                        #{cid}
                    </foreach>
                </when>
            </choose>

            <if test="inDto.isAssociateServiceOrder != null">
                <if test="inDto.isAssociateServiceOrder == 0">
                    and tsoi.id is null
                </if>
                <if test="inDto.isAssociateServiceOrder == 1">
                    and tsoi.id is not null
                </if>
            </if>

            <if test="inDto.isAssociateCustomer != null">
                <if test="inDto.isAssociateCustomer == 0">
                    and tsoi.qywx_external_username is null
                </if>
                <if test="inDto.isAssociateCustomer == 1">
                    and tsoi.qywx_external_username is not null
                </if>
            </if>

        </where>

    </select>

    <select id="querySimpleExaminationListUseSalesman"
            resultType="com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QuerySimpleExaminationListOutDto">
        SELECT  d.id,
                d.created_by AS createdByAccountId,
                d.salesman_id AS createdBySalesmanId,
                d.clues_id as cluesId
        FROM marketingcenter.t_clues_examination_detail d
        where d.is_deleted = 0
        <if test="inDto.ids != null and inDto.ids.size() > 0">
            and d.id in
            <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inDto.type != null">
            <choose>
                <when test="inDto.type == 1">
                    and d.type in (1, 3)
                </when>
                <otherwise>
                    and d.type = #{inDto.type}
                </otherwise>
            </choose>
        </if>
        <if test="inDto.startTime != null and inDto.endTime != null">
            and d.created_date between #{inDto.startTime} and #{inDto.endTime}
        </if>
        <if test="inDto.cluesId != null">
            <if test="inDto.cluesId == '-1'">
                and d.clues_id is null
            </if>
            <if test="inDto.cluesId != '-1'">
                and d.clues_id = #{inDto.cluesId}
            </if>
        </if>
    </select>
</mapper>