<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.happinessTest.dao.THappinessTestDao">

    <resultMap type="com.fotile.cmscenter.happinessTest.pojo.entity.THappinessTest" id="THappinessTestMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
        <result property="screenUrl" column="screen_url" jdbcType="VARCHAR"/>
        <result property="buttonColor" column="button_color" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="contentText" column="content_text" jdbcType="VARCHAR"/>
        <result property="multiple" column="multiple" jdbcType="NUMERIC"/>
        <result property="addend" column="addend" jdbcType="NUMERIC"/>
        <result property="isRound" column="is_round" jdbcType="INTEGER"/>
        <result property="onlineStatus" column="online_status" jdbcType="INTEGER"/>
        <result property="onlineTime" column="online_time" jdbcType="TIMESTAMP"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="completersNumber" column="completers_number" jdbcType="INTEGER"/>
        <result property="finishesNumber" column="finishes_number" jdbcType="INTEGER"/>
        <result property="isDraft" column="is_draft" jdbcType="INTEGER"/>
        <result property="isShowAnswer" column="is_show_answer" jdbcType="INTEGER"/>
        <result property="isShowScore" column="is_show_score" jdbcType="INTEGER"/>
        <result property="lockCopy" column="lock_copy" jdbcType="VARCHAR"/>
        <result property="sharedContent" column="shared_content" jdbcType="VARCHAR"/>
        <result property="sharedUrl" column="shared_url" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.fotile.cmscenter.happinessTest.pojo.dto.HappinessTestInDto" id="THappinessTestDtoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
        <result property="screenUrl" column="screen_url" jdbcType="VARCHAR"/>
        <result property="buttonColor" column="button_color" jdbcType="VARCHAR"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="contentText" column="content_text" jdbcType="VARCHAR"/>
        <result property="multiple" column="multiple" jdbcType="NUMERIC"/>
        <result property="addend" column="addend" jdbcType="NUMERIC"/>
        <result property="isRound" column="is_round" jdbcType="INTEGER"/>
        <result property="onlineStatus" column="online_status" jdbcType="INTEGER"/>
        <result property="onlineTime" column="online_time" jdbcType="TIMESTAMP"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="completersNumber" column="completers_number" jdbcType="INTEGER"/>
        <result property="finishesNumber" column="finishes_number" jdbcType="INTEGER"/>
        <result property="isDraft" column="is_draft" jdbcType="INTEGER"/>
        <result property="isShowAnswer" column="is_show_answer" jdbcType="INTEGER"/>
        <result property="isShowScore" column="is_show_score" jdbcType="INTEGER"/>
        <result property="lockCopy" column="lock_copy" jdbcType="VARCHAR"/>
        <result property="sharedContent" column="shared_content" jdbcType="VARCHAR"/>
        <result property="sharedUrl" column="shared_url" jdbcType="VARCHAR"/>
        <result property="testCount" column="test_count" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `is_deleted`,
        `created_by`,
        `modified_by`,
        `created_date`,
        `modified_date`,
        `code`,
        `title`,
        `category`,
        `type`,
        `cover_url`,
        `screen_url`,
        `button_color`,
        `summary`,
        `content_text`,
        `multiple`,
        `addend`,
        `is_round`,
        `online_status`,
        `online_time`,
        `sort`,
        `completers_number`,
        `finishes_number`,
        is_draft,
        is_show_answer,
        is_show_score,
        lock_copy,
        shared_content,
        shared_url,
        test_count
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="THappinessTestDtoMap">
        select
         <include refid="Base_Column_List"/>
        from cmscenter.t_happiness_test
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultType="com.fotile.cmscenter.happinessTest.pojo.dto.GetHappinessTestOutDto">
        select
        `id`,
        `created_date` createdDate,
        `code`,
        `title`,
        `category`,
        `type`,
        `online_status` onlineStatus,
        `online_time` onlineTime,
        `sort`,
        `completers_number` completersNumber,
        `finishes_number` finishesNumber
        from cmscenter.t_happiness_test
        <where>
            is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="createdDateStart != null and createdDateEnd != null">
                and created_date between #{createdDateStart} and #{createdDateEnd}
            </if>
            <if test="code != null and code != ''">
                and code like concat('%', #{code}, '%')
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        <choose>
            <when test="orderBy != null and orderBy != ''">
                order by ${orderBy},id
            </when>
            <otherwise>
                order by created_date desc
            </otherwise>
        </choose>
        limit #{offset}, #{size}
    </select>

    <!--统计总行数-->
    <select id="queryCount" resultType="java.lang.Long">
        select count(1)
        from cmscenter.t_happiness_test
        <where>
            is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="createdDateStart != null and createdDateEnd != null">
                and created_date between #{createdDateStart} and #{createdDateEnd}
            </if>
            <if test="code != null and code != ''">
                and code like concat('%', #{code}, '%')
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cmscenter.t_happiness_test(is_deleted, created_by, modified_by, created_date, modified_date, code, title, category, type, cover_url, screen_url, button_color, summary, content_text, multiple, addend, is_round, online_status, online_time,is_draft, sort, completers_number, finishes_number, is_show_answer, is_show_score, lock_copy, shared_content, shared_url,test_count)
        values (0, #{createdBy}, #{modifiedBy}, now(), now(), #{code}, #{title}, #{category}, #{type}, #{coverUrl}, #{screenUrl}, #{buttonColor}, #{summary}, #{contentText}, #{multiple}, #{addend}, #{isRound}, #{onlineStatus}, #{onlineTime}, #{isDraft} , #{sort}, 0, 0, #{isShowAnswer}, #{isShowScore}, #{lockCopy}, #{sharedContent}, #{sharedUrl} ,#{testCount})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into cmscenter.t_happiness_test(is_deleted, created_by, modified_by, created_date, modified_date, code, title, category, type, cover_url, screen_url, button_color, summary, content_text, multiple, addend, is_round, online_status, online_time,is_draft, sort, completers_number, finishes_number, is_show_answer,is_show_score, lock_copy, shared_content, shared_url)
        values
        <foreach collection="entities" item="entity" separator=",">
        (0, #{entity.createdBy}, #{entity.modifiedBy}, #{entity.createdDate}, #{entity.modifiedDate}, #{entity.code}, #{entity.title}, #{entity.category}, #{entity.type}, #{entity.coverUrl}, #{entity.screenUrl}, #{entity.buttonColor}, #{entity.summary}, #{entity.contentText}, #{entity.multiple}, #{entity.addend}, #{entity.isRound}, #{entity.onlineStatus}, #{entity.onlineTime},1 #{entity.sort}, #{entity.completersNumber}, #{entity.finishesNumber}, #{entity.isShowAnswer}, #{entity.isShowScore}, #{entity.lockCopy}, #{entity.sharedContent}, #{entity.sharedUrl})
        </foreach>
    </insert>
    
    <!--通过主键修改数据-->
    <update id="update">
        update cmscenter.t_happiness_test
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
                modified_date = now(),
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="coverUrl != null and coverUrl != ''">
                cover_url = #{coverUrl},
            </if>
                screen_url = #{screenUrl},
                button_color = #{buttonColor},
                summary = #{summary},
                content_text = #{contentText},
            <if test="onlineStatus != null">
                online_status = #{onlineStatus},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
                is_show_answer = #{isShowAnswer},
            <if test="isDraft != null">
                is_draft = #{isDraft},
            </if>
                multiple = #{multiple},
                addend = #{addend},
                is_round = #{isRound},
            <if test="isShowScore != null">
                is_show_score = #{isShowScore},
            </if>
                test_count = #{testCount},
                lock_copy = #{lockCopy},
                shared_content = #{sharedContent},
                shared_url = #{sharedUrl}
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cmscenter.t_happiness_test where id = #{id}
    </delete>

    <!--通过主键修改数据-->
    <update id="updateCode">
        update cmscenter.t_happiness_test
        set code = #{code}
        where id = #{id}
    </update>

    <!--通过主键修改数据-->
    <update id="updateSort">
        update cmscenter.t_happiness_test
        <set>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            modified_date = now(),
            <if test="onlineStatus != null">
                online_status = #{onlineStatus},
                <if test="onlineStatus == 1">
                    online_time = now(),
                </if>
            </if>
            <if test="isDraft != null">
                is_draft = #{isDraft},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--查询指定行数据-->
    <select id="queryHappinessTestByIds" resultType="com.fotile.cmscenter.channel.pojo.dto.ChannelHappinessTestDto">
        select
        `id`,
        `created_date` createdDate,
        `code`,
        `title`,
        `category`,
        `type`,
        `online_status` onlineStatus,
        `online_time` onlineTime,
        `sort`,
        `completers_number` completersNumber,
        `finishes_number` finishesNumber
        from cmscenter.t_happiness_test
        <where>
            is_deleted = 0
            and id in
            <foreach collection="sourceIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!--查询推荐测试-->
    <select id="queryTestByIds" resultMap="THappinessTestMap">
        select
        <include refid="Base_Column_List"/>
        from cmscenter.t_happiness_test
        <where>
            is_deleted = 0
            and id in
            <foreach collection="sourceIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!--查询推荐测试-->
    <select id="queryTestById" resultMap="THappinessTestMap">
        select
        <include refid="Base_Column_List"/>
        from cmscenter.t_happiness_test
        <where>
            is_deleted = 0
            and (id = #{keyword} or code like concat('%', #{keyword}, '%'))
        </where>
        limit 1
    </select>
</mapper>

