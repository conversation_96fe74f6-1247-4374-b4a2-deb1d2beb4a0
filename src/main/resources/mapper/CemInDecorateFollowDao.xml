<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.dep.provider.cem.dao.CemInDecorateFollowDao">
    <sql id="Base_Column_List">
        batch_no,
        distribute_no,
        follow_id,
        SRId,
        FollowContent,
        `Type`,
        Updated,
        created_by,
        modified_by,
        created_date,
        modified_date,
        id,
        is_deleted
    </sql>

    <resultMap id="BaseResultMap"
               type="com.fotile.dep.provider.cem.pojo.entity.CemInDecorateFollow">
        <result column="batch_no" property="batchNo"/>
        <result column="distribute_no" property="distributeNo"/>
        <result column="follow_id" property="follow_id"/>
        <result column="SRId" property="SRId"/>
        <result column="FollowContent" property="FollowContent"/>
        <result column="Type" property="Type"/>
        <result column="Updated" property="Updated"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="findByBatchNoAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from depcenter.cem_in_decorate_follow
        where batch_no = #{batchNo}
          and is_deleted = #{isDeleted}
    </select>
</mapper>