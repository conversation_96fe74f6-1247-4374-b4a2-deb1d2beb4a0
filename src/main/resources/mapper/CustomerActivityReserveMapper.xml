<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.customercenter.fission.dao.CustomerActivityReserveMapper">
    <resultMap id="CustomerLotteryCodeMap" type="com.fotile.customercenter.fission.pojo.entity.CustomerActivityReserve">
        <result column="id" property="id"/>
        <result column="customer_id" property="customerId"/>
        <result column="activity_id" property="activityId"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="last_modified_time" property="lastModifiedTime"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
    </resultMap>


    <sql id="Base_Column_List">

    </sql>

    <sql id="Base_Column_Field">
        `id`,
        `customer_id`,
        `activity_id`,
        `created_time`,
        `created_by`,
        `last_modified_time`,
        `last_modified_by`
    </sql>


</mapper>