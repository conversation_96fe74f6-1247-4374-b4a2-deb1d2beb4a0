<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.macenter.rule.dao.RuleValueAttributeMapper">
  <resultMap id="BaseResultMap" type="com.fotile.macenter.rule.pojo.dto.RuleValueAttribute">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="field_id" jdbcType="VARCHAR" property="fieldId" />
    <result column="attribute_id" jdbcType="VARCHAR" property="attributeId" />
    <result column="attribute_value" jdbcType="VARCHAR" property="attributeValue" />
    <result column="attribute_sort" jdbcType="BIGINT" property="attributeSort" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  
	  	  	      	id,
       	  	      	field_id,
       	  	      	attribute_id,
       	  	      	attribute_value,
       	  	      	attribute_sort,
       	  	      	is_deleted,
       	  	      	created_by,
       	  	      	created_date,
       	  	      	modified_by,
       	  	    	  	modified_date
       	  	
  </sql>
  

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_rule_value_attribute
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="getAttributeByFieldId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_rule_value_attribute
    where is_deleted = 0 and field_id = #{fieldId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_rule_value_attribute
    where id = #{id,jdbcType=BIGINT}
  </delete>

  
  <insert id="insert" parameterType="com.fotile.macenter.rule.pojo.dto.RuleValueAttribute">
    insert into t_rule_value_attribute (
      	  	       id,
       	  	       field_id,
       	  	       attribute_id,
       	  	       attribute_value,
       	  	       attribute_sort,
       	  	       is_deleted,
       	  	       created_by,
       	  	       created_date,
       	  	       modified_by,
       	  	    	  modified_date
       	    )
    values (
	  	  	      	#{id,jdbcType=BIGINT},
       	  	      	#{fieldId,jdbcType=VARCHAR},
       	  	      	#{attributeId,jdbcType=VARCHAR},
       	  	      	#{attributeValue,jdbcType=VARCHAR},
       	  	      	#{attributeSort,jdbcType=BIGINT},
       	  	      	#{isDeleted,jdbcType=BIGINT},
       	  	      	#{createdBy,jdbcType=VARCHAR},
       	  	      	#{createdDate,jdbcType=TIMESTAMP},
       	  	      	#{modifiedBy,jdbcType=VARCHAR},
       	  	    	  #{modifiedDate,jdbcType=TIMESTAMP}
       	    )
  </insert>
  
  <insert id="insertSelective" parameterType="com.fotile.macenter.rule.pojo.dto.RuleValueAttribute">
    insert into t_rule_value_attribute
    <trim prefix="(" suffix=")" suffixOverrides=",">
          
      <if test="id != null">
        id,
      </if>
         
      <if test="fieldId != null">
        field_id,
      </if>
         
      <if test="attributeId != null">
        attribute_id,
      </if>
         
      <if test="attributeValue != null">
        attribute_value,
      </if>
         
      <if test="attributeSort != null">
        attribute_sort,
      </if>
         
      <if test="isDeleted != null">
        is_deleted,
      </if>
         
      <if test="createdBy != null">
        created_by,
      </if>
         
      <if test="createdDate != null">
        created_date,
      </if>
         
      <if test="modifiedBy != null">
        modified_by,
      </if>
         
      <if test="modifiedDate != null">
        modified_date,
      </if>
         </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
          <if test="fieldId != null">
        #{fieldId,jdbcType=VARCHAR},
      </if>
          <if test="attributeId != null">
        #{attributeId,jdbcType=VARCHAR},
      </if>
          <if test="attributeValue != null">
        #{attributeValue,jdbcType=VARCHAR},
      </if>
          <if test="attributeSort != null">
        #{attributeSort,jdbcType=BIGINT},
      </if>
          <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIGINT},
      </if>
          <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
          <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
          <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
          <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
        </trim>
  </insert>
  


  
  
      <update id="updateByPrimaryKeySelective" parameterType="com.fotile.macenter.rule.pojo.dto.RuleValueAttribute">
    update t_rule_value_attribute
    <set>
                          
      <if test="fieldId != null">
        field_id = #{fieldId,jdbcType=VARCHAR},
      </if>
                      
      <if test="attributeId != null">
        attribute_id = #{attributeId,jdbcType=VARCHAR},
      </if>
                      
      <if test="attributeValue != null">
        attribute_value = #{attributeValue,jdbcType=VARCHAR},
      </if>
                      
      <if test="attributeSort != null">
        attribute_sort = #{attributeSort,jdbcType=BIGINT},
      </if>
                      
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
                      
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
                      
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
                      
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
                      
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
              </set>
    
    where 
            
    id = #{id,jdbcType=BIGINT}
                                                                                  
  </update>

      	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	   	      	

  <update id="updateByPrimaryKey" parameterType="com.fotile.macenter.rule.pojo.dto.RuleValueAttribute">
    update t_rule_value_attribute
    set 
    		 		 	  	  
		field_id = #{fieldId,jdbcType=VARCHAR},
	  	 		 	  	  
		attribute_id = #{attributeId,jdbcType=VARCHAR},
	  	 		 	  	  
		attribute_value = #{attributeValue,jdbcType=VARCHAR},
	  	 		 	  	  
		attribute_sort = #{attributeSort,jdbcType=BIGINT},
	  	 		 	  	  
		is_deleted = #{isDeleted,jdbcType=BIGINT},
	  	 		 	  	  
		created_by = #{createdBy,jdbcType=VARCHAR},
	  	 		 	  	  
		created_date = #{createdDate,jdbcType=TIMESTAMP},
	  	 		 	  	  
		modified_by = #{modifiedBy,jdbcType=VARCHAR},
	  	 		 	  		
		modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
	  	 		
    where 
            
    id = #{id,jdbcType=BIGINT}
                                                                                    
  </update>

  <select id="getAttributeByFieldIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_rule_value_attribute
    where is_deleted = 0 and field_id in
    <foreach collection="fieldIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from msgcenter.t_rule_value_attribute
    where is_deleted = 0
  </select>
</mapper>