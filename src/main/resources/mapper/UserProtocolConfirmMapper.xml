<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.usercenter.user.dao.UserProtocolConfirmMapper">
    <resultMap id="BaseResultMap" type="com.fotile.usercenter.user.pojo.entity.UserProtocolConfirm">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="protocol_id" property="protocolId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR"/>
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, protocol_id, status, is_deleted, created_by, created_date, modified_by,
    modified_date
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from user_protocol_confirm
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from user_protocol_confirm
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.fotile.usercenter.user.pojo.entity.UserProtocolConfirm">
        insert into user_protocol_confirm (id, user_id, protocol_id,
                                           status, is_deleted, created_by,
                                           created_date, modified_by, modified_date)
        values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR}, #{protocolId,jdbcType=BIGINT},
                #{status,jdbcType=TINYINT}, 0, #{userId,jdbcType=VARCHAR},
                now(), #{userId,jdbcType=VARCHAR}, now())
    </insert>
    <insert id="insertSelective" parameterType="com.fotile.usercenter.user.pojo.entity.UserProtocolConfirm">
        insert into user_protocol_confirm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="protocolId != null">
                protocol_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDate != null">
                created_date,
            </if>
            <if test="modifiedBy != null">
                modified_by,
            </if>
            <if test="modifiedDate != null">
                modified_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="protocolId != null">
                #{protocolId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.fotile.usercenter.user.pojo.entity.UserProtocolConfirm">
        update user_protocol_confirm
        <set>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="modifiedDate != null">
                modified_date = now(),
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fotile.usercenter.user.pojo.entity.UserProtocolConfirm">
        update user_protocol_confirm
        set user_id       = #{userId,jdbcType=VARCHAR},
            protocol_id   = #{protocolId,jdbcType=BIGINT},
            status        = #{status,jdbcType=TINYINT},
            is_deleted    = 0,
            modified_by   = #{userId,jdbcType=VARCHAR},
            modified_date = now()
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByUserIdAndProtocolId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_protocol_confirm
        where is_deleted = 0 and user_id = #{userId} and protocol_id = #{protocolId}
    </select>

    <select id="getConfirmProtocol" resultType="java.lang.Long">
        select protocol_id
        from user_protocol_confirm
        where is_deleted = 0
          and status = 1
          and user_id = #{userId}
    </select>

    <select id="getNotConfirmProtocol" resultMap="BaseResultMap">
        select *
        from user_protocol_confirm
        where is_deleted = 0
          and status = 0
          and protocol_id = #{protocolId}
          and user_id = #{userId}
        order by id desc limit 1
    </select>


    <update id="updateConfirmStatus" parameterType="java.lang.Long">
        update user_protocol_confirm
        set status        = 0,
            user_protocol_status = null,
            modified_date = now()
        where is_deleted = 0
          and protocol_id = #{protocolId}
    </update>

    <update id="updateConfirmProtocolStatus" parameterType="java.lang.Long">
        update user_protocol_confirm
        set user_protocol_status = #{userProtocolStatus},
            modified_date = now()
        where is_deleted = 0
        <if test="protocolIdList != null and protocolIdList.size() > 0">
            and protocol_id in
            <foreach collection="protocolIdList" item="protocolId" open="(" separator="," close=")">
                #{protocolId}
            </foreach>
        </if>
    </update>

    <select id="queryConfirmProtocolStatus" resultType="java.lang.Integer">
        select user_protocol_status
        from user_protocol_confirm
        where is_deleted = 0
          and user_id = #{userId}
        <if test="protocolIdList != null and protocolIdList.size() > 0">
            and protocol_id in
            <foreach collection="protocolIdList" item="protocolId" open="(" separator="," close=")">
                #{protocolId}
            </foreach>
        </if>
        order by created_date desc limit 1
    </select>
</mapper>