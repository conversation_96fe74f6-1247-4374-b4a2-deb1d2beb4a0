<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.dep.provider.msp.dao.MspInSApplyCloseDao">
    <sql id="Base_Column_List">
        batch_no,
        distribute_no,
        apply_close_id,
        apply_id,
        total_area,
        down_time,
        close_reason,
        created_by,
        modified_by,
        created_date,
        modified_date,
        id,
        is_deleted
    </sql>
    <resultMap id="BaseResultMap" type="com.fotile.dep.provider.msp.pojo.entity.MspInSApplyClose">
        <result column="batch_no" property="batchNo"/>
        <result column="distribute_no" property="distributeNo"/>
        <result column="apply_close_id" property="apply_close_id"/>
        <result column="apply_id" property="apply_id"/>
        <result column="total_area" property="total_area"/>
        <result column="down_time" property="down_time"/>
        <result column="close_reason" property="close_reason"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="findByBatchNoAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from depcenter.msp_in_s_apply_close
        where batch_no=#{batchNo} and is_deleted=#{isDeleted}
    </select>
</mapper>