<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.activiti.workflow.phase.dao.AbnormalStoreDao">
  <resultMap id="BaseResultMap" type="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_org_id" jdbcType="BIGINT" property="storeOrgId" />
    <result column="store_channel_code" jdbcType="VARCHAR" property="storeChannelCode" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="abnormal_reason" jdbcType="VARCHAR" property="abnormalReason" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="solver_remark" jdbcType="VARCHAR" property="solverRemark" />
    <result column="original_value" jdbcType="VARCHAR" property="originalValue" />
    <result column="modified_value" jdbcType="VARCHAR" property="modifiedValue" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="executer_id" jdbcType="VARCHAR" property="executerId" />
    <result column="executer_remark" jdbcType="VARCHAR" property="executerRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="modified_user_name" jdbcType="VARCHAR" property="modifiedUserName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_code, store_name,store_id, store_org_id, store_channel_code, distributor_id, distributor_code,
    company_id, company_code,company_name, area_code, abnormal_reason, `state`, solver_remark, original_value,
    modified_value, apply_reason, executer_id, executer_remark,remark, is_deleted, created_by,
    created_date, modified_by, modified_date
  </sql>
  <select id="selectByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStoreExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from se_abnormal_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from se_abnormal_store
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from se_abnormal_store
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStoreExample">
    delete from se_abnormal_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="myInsert" keyColumn="id" keyProperty="id" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStore" useGeneratedKeys="true">
    insert into se_abnormal_store (store_code, store_id, store_name,store_org_id,
      store_channel_code, distributor_id, distributor_code, 
      company_id, company_code, company_name,area_code,
      abnormal_reason, `state`, solver_remark, 
      original_value, modified_value, apply_reason, 
      executer_id, executer_remark,remark, is_deleted,
      created_by, created_date, modified_by,
       modified_user_name,    modified_date)
    values (#{storeCode,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR},#{storeOrgId,jdbcType=BIGINT},
      #{storeChannelCode,jdbcType=VARCHAR}, #{distributorId,jdbcType=BIGINT}, #{distributorCode,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR},#{companyName,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR},
      #{abnormalReason,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, #{solverRemark,jdbcType=VARCHAR}, 
      #{originalValue,jdbcType=VARCHAR}, #{modifiedValue,jdbcType=VARCHAR}, #{applyReason,jdbcType=VARCHAR}, 
      #{executerId,jdbcType=VARCHAR}, #{executerRemark,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT},
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR},
            #{modifiedUserName,jdbcType=VARCHAR},  #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="myInsertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStore" useGeneratedKeys="true">
    insert into se_abnormal_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeOrgId != null">
        store_org_id,
      </if>
      <if test="storeChannelCode != null">
        store_channel_code,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="abnormalReason != null">
        abnormal_reason,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="solverRemark != null">
        solver_remark,
      </if>
      <if test="originalValue != null">
        original_value,
      </if>
      <if test="modifiedValue != null">
        modified_value,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="executerId != null">
        executer_id,
      </if>
      <if test="executerRemark != null">
        executer_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDate != null">
        created_date,
      </if>
      <if test="modifiedBy != null">
        modified_by,
      </if>
      <if test="modifiedUserName != null">
        modified_user_name,
      </if>
      <if test="modifiedDate != null">
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName},
      </if>
      <if test="storeOrgId != null">
        #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeChannelCode != null">
        #{storeChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="abnormalReason != null">
        #{abnormalReason,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="solverRemark != null">
        #{solverRemark,jdbcType=VARCHAR},
      </if>
      <if test="originalValue != null">
        #{originalValue,jdbcType=VARCHAR},
      </if>
      <if test="modifiedValue != null">
        #{modifiedValue,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="executerId != null">
        #{executerId,jdbcType=VARCHAR},
      </if>
      <if test="executerRemark != null">
        #{executerRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedUserName != null">
        #{modifiedUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStoreExample" resultType="java.lang.Long">
    select count(*) from se_abnormal_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="count" resultType="java.lang.Integer">
      SELECT
      COUNT(sas.id)
      FROM
      se_abnormal_store sas
    <include refid="queryPageWhere"></include>
    </select>
  <select id="queryPage"
          resultType="com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreVo">
    SELECT
      sas.*,
    <if test="dto.sortColumn !=null and dto.sortColumn=='lastNodeCreated'">
      (
      CASE sas.state
      WHEN -1 THEN
      NULL
      WHEN 0 THEN
      DATEDIFF( NOW(), sas.created_date )
      ELSE
      DATEDIFF( NOW(), t2.lastDate )
      END
      )taskCreatedDays
    </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeCreated'">
      DATEDIFF(NOW(),MAX(lo.created_date))    taskCreatedDays
    </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeUnhandled'">
      (
      CASE sas.state
      WHEN 1 THEN
      DATEDIFF(NOW(),MAX(lo.created_date))
      ELSE
      NULL
      END)taskCreatedDays
    </if>
    FROM
      se_abnormal_store sas
          <if test="dto.sortColumn !=null and dto.sortColumn=='lastNodeCreated'">
            LEFT JOIN (
            SELECT
            lo.ref_id,
            MAX(lo.created_date) lastDate
            FROM
            log_op lo
            WHERE
            lo.is_deleted = 0
            AND lo.ref_table = 'se_abnormal_store'

            GROUP BY lo.ref_id ) t2 ON t2.ref_id=sas.id
          </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeCreated'">
      LEFT JOIN  log_op lo ON sas.id=lo.ref_id AND lo.ref_table='se_abnormal_store' AND lo.ref_type='400' AND lo.type='401'
    </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeUnhandled'">
      LEFT JOIN  log_op lo ON sas.id=lo.ref_id AND lo.ref_table='se_abnormal_store' AND lo.ref_type='400' AND lo.type='401'
    </if>
    <include refid="queryPageWhere"></include>
    GROUP BY sas.id
    ORDER BY
    <if test="dto.sortColumn !=null and dto.sortColumn=='lastNodeCreated'">
      taskCreatedDays ${dto.sort},
    </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeCreated'">
      taskCreatedDays  ${dto.sort},
    </if>
    <if test="dto.sortColumn !=null and dto.sortColumn=='executerNodeUnhandled'">
      taskCreatedDays ${dto.sort},
    </if>
    sas.id ${dto.sort}
    limit #{pg.offset},#{pg.size}
  </select>
    <select id="getAbnormalLastAuditDate"
            resultType="com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreVo">
      SELECT
        sas2.id,
        sas2.store_id ,
        sas2.store_org_id,
        MAX(lo.created_date) lastAuditDate,
        sas2.state
      FROM
        se_abnormal_store sas2
          LEFT JOIN log_op lo ON sas2.id  = lo.ref_id
          AND lo.is_deleted = 0
          AND lo.ref_type='400'
      WHERE
          sas2.id
          IN (
            SELECT
              MAX( sas.id )
            FROM
              se_abnormal_store sas
            WHERE
              sas.is_deleted = 0
              AND sas.abnormal_reason = #{abnormalReason}
            GROUP BY
              sas.store_org_id
          )
      GROUP BY sas2.id;
    </select>
    <sql id="queryPageWhere">
    <where>
      sas.is_deleted = 0
      <if test="dto.executerId !=null and dto.executerId !=''">
        AND sas.executer_id=#{dto.executerId}
      </if>
      <if test="dto.ids!=null and dto.ids.size()>0">
        AND sas.id IN
         <foreach collection="dto.ids" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="dto.storeOrgIds!=null and dto.storeOrgIds.size()>0">
        AND sas.store_org_id IN
        <foreach collection="dto.storeOrgIds" separator="," open="(" close=")" item="storeOrgId">
          #{storeOrgId}
        </foreach>
      </if>
      <if test="dto.storeChannelCodes !=null and dto.storeChannelCodes.size()>0">
        AND sas.store_channel_code IN
        <foreach collection="dto.storeChannelCodes" item="storeChannelCode" open="(" close=")" separator=",">
          #{storeChannelCode}
        </foreach>
      </if>
      <if test="dto.areaCodes !=null and dto.areaCodes.size()>0">
        AND sas.area_code IN
        <foreach collection="dto.areaCodes" separator="," open="(" close=")" item="areaCode">
          #{areaCode}
        </foreach>
      </if>
      <if test="dto.companyIds !=null and dto.companyIds.size()>0">
        AND sas.company_id IN
        <foreach collection="dto.companyIds" item="companyId" open="(" close=")" separator=",">
          #{companyId}
        </foreach>
      </if>
      <if test="dto.dataScopeCompany !=null and dto.dataScopeCompany.size()>0">
        AND sas.company_id IN
        <foreach collection="dto.dataScopeCompany" separator="," open="(" close=")" item="companyId">
          #{companyId}
        </foreach>
      </if>
      <if test="dto.distributorIds !=null and dto.distributorIds.size()>0">
        AND sas.distributor_id IN
        <foreach collection="dto.distributorIds" separator="," open="(" close=")" item="distributorId">
          #{distributorId}
        </foreach>
      </if>
      <if test="dto.abnormalReasons !=null and dto.abnormalReasons.size()>0">
        AND sas.abnormal_reason IN
        <foreach collection="dto.abnormalReasons" item="abnormalReason" open="(" close=")" separator=",">
          #{abnormalReason}
        </foreach>
      </if>
      <if test="dto.authAbnormalReasons != null and dto.authAbnormalReasons.size()>0">
        AND sas.abnormal_reason IN
        <foreach collection="dto.authAbnormalReasons" item="abnormalReason" open="(" close=")" separator=",">
          #{abnormalReason}
        </foreach>
      </if>
      <if test="dto.states !=null and dto.states.size()>0">
        AND sas.state IN
        <foreach collection="dto.states" separator="," open="(" close=")" item="state">
          #{state}
        </foreach>
      </if>
      <if test="dto.createdDateBegin !=null">
        AND sas.created_date &gt;= #{dto.createdDateBegin}
      </if>
      <if test="dto.createdDateEnd !=null">
        AND sas.created_date &lt;= #{dto.createdDateEnd}
      </if>
      <if test="dto.modifiedDateBegin !=null">
        AND sas.modified_date &gt;= #{dto.modifiedDateBegin}
      </if>
      <if test="dto.modifiedDateEnd != null">
        AND sas.modified_date  &lt;= #{dto.modifiedDateEnd}
      </if>
    </where>
  </sql>
    <update id="updateByExampleSelective" parameterType="map">
    update se_abnormal_store
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeOrgId != null">
        store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.storeChannelCode != null">
        store_channel_code = #{record.storeChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorId != null">
        distributor_id = #{record.distributorId,jdbcType=BIGINT},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.abnormalReason != null">
        abnormal_reason = #{record.abnormalReason,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        `state` = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.solverRemark != null">
        solver_remark = #{record.solverRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.originalValue != null">
        original_value = #{record.originalValue,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedValue != null">
        modified_value = #{record.modifiedValue,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReason != null">
        apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.executerId != null">
        executer_id = #{record.executerId,jdbcType=VARCHAR},
      </if>
      <if test="record.executerRemark != null">
        executer_remark = #{record.executerRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null">
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null">
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null">
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update se_abnormal_store
    set id = #{record.id,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
    store_name = #{record.storeName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_org_id = #{record.storeOrgId,jdbcType=BIGINT},
      store_channel_code = #{record.storeChannelCode,jdbcType=VARCHAR},
      distributor_id = #{record.distributorId,jdbcType=BIGINT},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      area_code = #{record.areaCode,jdbcType=VARCHAR},
      abnormal_reason = #{record.abnormalReason,jdbcType=VARCHAR},
      `state` = #{record.state,jdbcType=TINYINT},
      solver_remark = #{record.solverRemark,jdbcType=VARCHAR},
      original_value = #{record.originalValue,jdbcType=VARCHAR},
      modified_value = #{record.modifiedValue,jdbcType=VARCHAR},
      apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      executer_id = #{record.executerId,jdbcType=VARCHAR},
      executer_remark = #{record.executerRemark,jdbcType=VARCHAR},
        remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStore">
    update se_abnormal_store
    <set>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeOrgId != null">
        store_org_id = #{storeOrgId,jdbcType=BIGINT},
      </if>
      <if test="storeChannelCode != null">
        store_channel_code = #{storeChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
      company_code = #{companyCode,jdbcType=VARCHAR},
    </if>
      <if test="companyName != null">
        company_name = #{companyName},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="abnormalReason != null">
        abnormal_reason = #{abnormalReason,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=TINYINT},
      </if>
      <if test="solverRemark != null">
        solver_remark = #{solverRemark,jdbcType=VARCHAR},
      </if>
      <if test="originalValue != null">
        original_value = #{originalValue,jdbcType=VARCHAR},
      </if>
      <if test="modifiedValue != null">
        modified_value = #{modifiedValue,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="executerId != null">
        executer_id = #{executerId,jdbcType=VARCHAR},
      </if>
      <if test="executerRemark != null">
        executer_remark = #{executerRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedUserName != null">
        modified_user_name = #{modifiedUserName,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="myUpdateByPrimaryKey" parameterType="com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore.AbnormalStore">
    update se_abnormal_store
    set store_code = #{storeCode,jdbcType=VARCHAR},
        store_name = #{storeName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_org_id = #{storeOrgId,jdbcType=BIGINT},
      store_channel_code = #{storeChannelCode,jdbcType=VARCHAR},
      distributor_id = #{distributorId,jdbcType=BIGINT},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
        company_name = #{companyName},
      area_code = #{areaCode,jdbcType=VARCHAR},
      abnormal_reason = #{abnormalReason,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=TINYINT},
      solver_remark = #{solverRemark,jdbcType=VARCHAR},
      original_value = #{originalValue,jdbcType=VARCHAR},
      modified_value = #{modifiedValue,jdbcType=VARCHAR},
      apply_reason = #{applyReason,jdbcType=VARCHAR},
      executer_id = #{executerId,jdbcType=VARCHAR},
      executer_remark = #{executerRemark,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_user_name = #{modifiedUserName,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>