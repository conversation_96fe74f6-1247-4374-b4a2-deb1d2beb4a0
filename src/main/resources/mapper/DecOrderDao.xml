<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.omscenter.decOrder.mapper.DecOrderDao">

    <sql id="authorStoreList">
        (
        <foreach collection="authorStoreList" item="store" separator="union">
            select @storeOrgId:=#{store.orgId} as storeOrgId
        </foreach>
        ) as authorStoreList
    </sql>

    <sql id="findPageAllWhere">
        <where>
            o.is_deleted=0
            <if test="findDecOrderPageAllInDto.id !=null"><!-- 报单id -->
                AND o.id=#{findDecOrderPageAllInDto.id}
            </if>
            <if test="findDecOrderPageAllInDto.cluesSalesmanId !=null"><!-- 线索负责人关联的业务员id -->
                AND o.clues_salesman_id=#{findDecOrderPageAllInDto.cluesSalesmanId}
            </if>
            <if test="findDecOrderPageAllInDto.adviserId !=null"><!-- 录入人 -->
                AND o.adviser_id =#{findDecOrderPageAllInDto.adviserId}
            </if>
            <if test="findDecOrderPageAllInDto.orderCreatetimeStart !=null and findDecOrderPageAllInDto.orderCreatetimeStart !=''"><!-- 报单时间-起始 -->
                AND DATE_FORMAT(o.order_create_time,'%Y-%m-%d %T') &gt;=DATE_FORMAT(#{findDecOrderPageAllInDto.orderCreatetimeStart},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.orderCreatetimeEnd !=null and findDecOrderPageAllInDto.orderCreatetimeEnd !=''"><!-- 报单时间-结束 -->
                AND DATE_FORMAT(o.order_create_time,'%Y-%m-%d %T') &lt;=DATE_FORMAT(#{findDecOrderPageAllInDto.orderCreatetimeEnd},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.goodsAmountStart !=null"><!-- 订单总金额-起始 -->
                AND o.goods_amount &gt;=#{findDecOrderPageAllInDto.goodsAmountStart}
            </if>
            <if test="findDecOrderPageAllInDto.goodsAmountEnd !=null"><!-- 订单总金额-结束 -->
                AND o.goods_amount &lt;=#{findDecOrderPageAllInDto.goodsAmountEnd}
            </if>
            <if test="findDecOrderPageAllInDto.contact !=null and findDecOrderPageAllInDto.contact !=''"><!-- 收件人(支持名称和手机号模糊匹配) -->
                AND (
                    o.contact_name = #{findDecOrderPageAllInDto.contact,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    OR o.contact_mobile = #{findDecOrderPageAllInDto.contact,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                )
            </if>
            <if test="findDecOrderPageAllInDto.proviceId !=null"><!-- 收货地址-省 -->
                AND o.provice_id =#{findDecOrderPageAllInDto.proviceId}
            </if>
            <if test="findDecOrderPageAllInDto.cityId !=null"><!-- 收货地址-市 -->
                AND o.city_id =#{findDecOrderPageAllInDto.cityId}
            </if>
            <if test="findDecOrderPageAllInDto.countyId !=null"><!-- 收货地址-区 -->
                AND o.county_id =#{findDecOrderPageAllInDto.countyId}
            </if>
            <if test="findDecOrderPageAllInDto.address !=null and findDecOrderPageAllInDto.address !=''"><!-- 详细地址 -->
                AND o.delivery_address = #{findDecOrderPageAllInDto.address,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="findDecOrderPageAllInDto.companyId !=null"><!-- 所属分公司 -->
                AND o.company_id =#{findDecOrderPageAllInDto.companyId}
            </if>
            <if test="findDecOrderPageAllInDto.storeId !=null"><!-- 所属门店 -->
                AND o.store_id =#{findDecOrderPageAllInDto.storeId}
            </if>
            <if test="findDecOrderPageAllInDto.stage !=null">
                <choose>
                    <when test="findDecOrderPageAllInDto.stage ==1 or findDecOrderPageAllInDto.stage ==2 or findDecOrderPageAllInDto.stage ==3"><!--1：待审核；2：审核通过；3：审核拒绝-->
                        AND o.stage =#{findDecOrderPageAllInDto.stage}<!-- 审核状态 -->
                    </when>
                    <when test="findDecOrderPageAllInDto.stage ==4 or findDecOrderPageAllInDto.stage ==5 or findDecOrderPageAllInDto.stage ==6"><!--4：未转单；5：已转单；6：已作废-->
                        AND o.order_type IN(501,303)
                        <choose>
                            <when test="findDecOrderPageAllInDto.stage ==4">AND o.stransfer_stage =1</when><!--未转单-->
                            <when test="findDecOrderPageAllInDto.stage ==5">AND o.stransfer_stage =2</when><!--已转单-->
                            <when test="findDecOrderPageAllInDto.stage ==6">AND o.stransfer_stage =3</when><!--已作废-->
                        </choose>
                    </when>
                    <when test="findDecOrderPageAllInDto.stage ==7 or findDecOrderPageAllInDto.stage ==8 or findDecOrderPageAllInDto.stage ==9"><!--7：不同步；8：同步成功；9：同步失败-->
                        <choose>
                            <when test="findDecOrderPageAllInDto.stage ==7">AND ( is_to_drp=1 )
                            </when><!--不同步-->
                            <when test="findDecOrderPageAllInDto.stage ==8">AND ( o.drp_stage =2 AND is_to_drp !=1 )
                            </when><!--同步成功-->
                            <when test="findDecOrderPageAllInDto.stage ==9">AND ( o.drp_stage =3 AND is_to_drp !=1)
                            </when><!--同步失败-->
                        </choose>
                    </when>
                    <when test="findDecOrderPageAllInDto.stage ==10 or findDecOrderPageAllInDto.stage ==11 or findDecOrderPageAllInDto.stage ==12"><!--drp订单状态 10：打开；11：审核；12：作废-->
                        <choose>
                            <when test="findDecOrderPageAllInDto.stage ==10">AND o.order_stage=1</when><!--打开-->
                            <when test="findDecOrderPageAllInDto.stage ==11">AND o.order_stage=2</when><!--审核-->
                            <when test="findDecOrderPageAllInDto.stage ==12">AND o.order_stage=4</when><!--作废-->
                        </choose>
                    </when>
                </choose>
            </if>

            <if test="findDecOrderPageAllInDto.decorateCompanyName !=null and findDecOrderPageAllInDto.decorateCompanyName !=''"><!-- 家装公司名称 -->
                AND o.decorate_company_name = #{findDecOrderPageAllInDto.decorateCompanyName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="findDecOrderPageAllInDto.designerName !=null and findDecOrderPageAllInDto.designerName !=''"><!-- 设计师名称 -->
                AND o.designer_name = #{findDecOrderPageAllInDto.designerName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="findDecOrderPageAllInDto.orderType !=null"><!-- 订单类型 1：经销/零售；2：定金 -->
                AND order_type=#{findDecOrderPageAllInDto.orderType}
            </if>
            <if test="findDecOrderPageAllInDto.drpOrderId !=null and findDecOrderPageAllInDto.drpOrderId !=''"><!-- DRP订单号 -->
                AND drp_orderId LIKE '%${findDecOrderPageAllInDto.drpOrderId}%'
            </if>

            <!-- 门店类型查询到的门店id -->
            <if test="findDecOrderPageAllInDto.storeIdList != null and findDecOrderPageAllInDto.storeIdList.size > 0">
                AND o.store_id IN
                <foreach collection="findDecOrderPageAllInDto.storeIdList" item="storeId" open="(" separator=","
                         close=")">
                    #{storeId}
                </foreach>
            </if>

            <!-- 审核时间 -->
            <if test="findDecOrderPageAllInDto.auditDateStart != null and findDecOrderPageAllInDto.auditDateStart != ''">
                AND o.audit_date &gt;#{findDecOrderPageAllInDto.auditDateStart}
            </if>
            <if test="findDecOrderPageAllInDto.auditDateEnd != null and findDecOrderPageAllInDto.auditDateEnd != ''">AND
                o.audit_date &lt;#{findDecOrderPageAllInDto.auditDateEnd}
            </if>
            <!-- 财务审核时间 -->
            <if test="findDecOrderPageAllInDto.examineDateStart != null and findDecOrderPageAllInDto.examineDateStart != ''">
                AND o.examine_date &gt;#{findDecOrderPageAllInDto.examineDateStart}
            </if>
            <if test="findDecOrderPageAllInDto.examineDateEnd != null and findDecOrderPageAllInDto.examineDateEnd != ''">
                AND o.examine_date &lt;#{findDecOrderPageAllInDto.examineDateEnd}
            </if>
            <!-- 大区 -->
            <if test="findDecOrderPageAllInDto.area != null and findDecOrderPageAllInDto.area != ''">
                AND o.area_code =#{findDecOrderPageAllInDto.area}
            </if>
            <!-- 11.6补充-->


            <!-- 引流渠道 -->
            <if test="findDecOrderPageAllInDto.decorateCompanyCode != null and findDecOrderPageAllInDto.decorateCompanyCode != '' ">
                AND o.decorate_company_code = #{findDecOrderPageAllInDto.decorateCompanyCode}
            </if>
            <!-- 引流渠道类型 -->
            <if test="findDecOrderPageAllInDto.decorateCompanyType != null ">
                AND o.decorate_company_type = #{findDecOrderPageAllInDto.decorateCompanyType}
            </if>
            <if test="findDecOrderPageAllInDto.designerCode != null and findDecOrderPageAllInDto.designerCode != '' ">
                AND o.designer_code = #{findDecOrderPageAllInDto.designerCode}
            </if>
            <if test="companyAuthorList !=null and companyAuthorList.size>0"><!-- 所属分公司 -->
                AND o.company_id IN
                <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                    #{company.orgId}
                </foreach>
            </if>
            <if test="authorStoreListNest != null and authorStoreListNest.size() != 0">
                and (
                <foreach collection="authorStoreListNest" item="list" separator="or">
                    o.store_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </foreach>
                )
            </if>
            <!-- 安装单号 -->
            <if test="findDecOrderPageAllInDto.installId != null and findDecOrderPageAllInDto.installId != '' ">
                AND o.install_id = #{findDecOrderPageAllInDto.installId}
            </if>
        </where>
    </sql>
    <!-- 报单列表 -->
    <select id="findPageAll" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderPageAllOutDto">
        SELECT o.id,o.clues_salesman_id cluesSalesmanId,o.adviser_id adviserId,o.adviser_name
        adviserName,o.order_create_time orderCreatetime,
        o.goods_amount goodsAmount,o.contact_name contactName,o.contact_mobile contactMobile,o.delivery_address address,
        CONCAT(o.provice_name,o.city_name,o.county_name) pccName,o.company_id companyId,o.store_id storeId,
        o.decorate_company_code decorateCompanyCode,o.decorate_company_name decorateCompanyName,o.designer_code
        designerCode,
        o.designer_name designerName,o.designer_phone designerPhone,
        CASE stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,
        CASE WHEN is_to_drp=1 THEN '不同步' WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理' WHEN is_to_drp
        !=1 AND drp_stage=2 THEN '同步成功' WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败' END drpStageName,
        (SELECT dc.category_name FROM omscenter.decorder_category dc WHERE dc.is_deleted=0 AND
        o.order_type=dc.category_code) orderType,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStage,
        CASE WHEN o.order_type=501 AND o.stransfer_stage=1 THEN '未转单' WHEN o.order_type=501 AND o.stransfer_stage=2 THEN
        '已转单' WHEN o.order_type=501 AND o.stransfer_stage=3 THEN '已作废' END stransferStage,
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,
        <!--补充订单渠道-->
        o.channel_category_name orderChannel,
        <!--补充引流渠道分类-->
        o.decorate_category decorateCategory,
        <!--补充引流渠道-->
        o.decorate_category_name decorateCategoryName,
        CASE o.decorate_company_type
        WHEN 1 THEN '家装公司'
        WHEN 2 THEN '异业三工'
        WHEN 3 THEN '设计师'
        END decorateCompanyType,o.company_name companyName,o.store_name storeName,
        o.earnest_amount earnestAmount
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
<!--        <if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->

        <include refid="findPageAllWhere"/>
        ORDER BY o.created_date DESC
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 报单列表count查询 -->
    <select id="selectPropertyCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
<!--        <if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="findPageAllWhere"/>
    </select>

    <sql id="findDecOrderByIdSql">
        SELECT
        o.id,
        o.print_num printNum,
        o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.contact_mobile_bak contactMobileBak,
        CONCAT(o.provice_name,o.city_name,o.county_name) pccName,
        o.delivery_address address,
        o.clues_id cluesId,
        o.clues_salesman_id cluesSalesmanId,
        o.adviser_id adviserId,
        o.adviser_name adviserName,
        o.company_id companyId,
        o.company_name companyName,
        o.store_id storeId,
        o.store_name storeName,
        o.drp_stage drpStage,
        o.retry_drp_num retryDrpNum,
        CASE stage
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '审核拒绝'
        WHEN 4 THEN '已作废'
        END
        stageName,
        CASE
        WHEN is_to_drp=1 THEN '不同步'
        WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理'
        WHEN is_to_drp !=1 AND drp_stage=2 THEN '同步成功'
        WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.order_create_time orderCreatetime,
        o.order_create_time orderCreateTimeDate,
        o.delivery_date deliveryDate,
        o.order_note orderNote,
        o.goods_amount goodsAmount,
        o.goods_num goodsNum,
        o.buy_reason buyReason,
        o.business_type businessType,
        o.settlement_ticket settlementTicket,
        o.install_id installId,
        o.stage,
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.decorate_company_phone decorateCompanyPhone,
        o.designer_code designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.is_to_drp isToDrp,
        o.provice_id proviceId,
        o.provice_name proviceName,
        o.city_id cityId,
        o.city_name cityName,
        o.county_id countyId,
        o.county_name countyName,
        o.cash_amount cashAmount,
        o.transfer_amount transferAmount,
        o.card_amount cardAmount,
        o.pos_amount posAmount,
        o.earnest_amount earnestAmount,
        o.order_type orderType,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        o.order_stage orderStage,
        o.modified_date stageTime,o.parent_id parentId,
        o.stransfer_stage stransferStage,
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,
        o.village_id villageId,
        o.village_name villageName,o.to_drp_msg toDrpMsg,
        o.ware_house wareHouse,o.examine_date examineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine_date ELSE o.app_pre_examine_date END preExamineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine ELSE o.app_pre_examine END preExamine,o.settlement_stage
        settlementStage,o.store_type storeType,o.distributor_name dealerName,
        o.card_no cardNo,o.card_id cardId,
        o.order_date orderDate,o.uuid,o.delivery_address deliveryAddress,
        o.decorate_company_type decorateCompanyType,o.activity_remark activityRemark,o.audit_date auditDate,
        <!-- 权限+审核通过+经销订单+需要预审+需要同步 -->
        CASE WHEN o.stage=2 AND o.order_type !=501 AND app_pre_audit=1 AND is_to_drp!=0 THEN 1 ELSE 0 END needPreAudit,
        o.order_channel orderChannel,o.clues_source_code cluesSourceCode,o.is_inStore isInStore,
        o.goods_num_canEnter goodsNumCanEnter,
        o.decorate_category decorateCategory,
        o.decorate_category_name decorateCategoryName,
        o.is_check_price isCheckPrice,
        o.central_purchase centralPurchase,
        o.clues_source_name cluesSource,
        o.clues_created_date cluesCreatedDate,
        o.dep_id depId,
        o.install_check installCheck,
        o.receipt_url receiptUrl,
        o.receipt_type receiptType,
        o.limit_type limitType,
        o.limit_number limitNumber,
        o.is_auto_audit isAutoAudit,
        o.app_pre_audit appPreAudit,
        o.distributor_code distributorCode,
        o.distributor_id distributorId,
        o.store_type_code storeTypeCode,
        o.deal_receiver_id dealReceiverId,
        o.created_by createdBy,
        o.created_date createdDate,
        o.modified_by modifiedBy,
        o.modified_date modifiedDate,
        o.is_draft isDraft,
        o.is_scene_deal isSceneDeal,
        o.building building,
        o.unit unit,
        o.house_number houseNumber,
        o.storage,
        o.dcs_out_status dcsStatus,
        o.distributor_name distributorName,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode,
        extend.oaid oaid,
        o.is_come_devise isComeDevise,
        o.original_orderId originalOrderId,
        o.manager_id managerId,
        o.manager_name managerName,
        o.drp_audit_reject_reason drpAuditRejectReason,
        o.is_from_app isFromApp,
        extend.is_cashier_enabled isCashierEnabled,
        o.remain_pay_amount remainPayAmount,
        o.is_monthly_settlement isMonthlySettlement,
        o.cashier_charge_mapping cashierChargeMapping,
        o.trade_in_flag tradeInFlag,
        o.real_store_id realStoreId,
        o.real_distributor_id realDistributorId,
        o.channel_category_code channelCategoryCode,
        o.channel_category_name channelCategoryName,
        o.channel_subdivide_code channelSubdivideCode,
        o.channel_subdivide_name channelSubdivideName,
        extend.designer_club_member_id designerClubMemberId,
        extend.designer_club_member_grade designerClubMemberGrade,
        extend.old_compensate_flag oldCompensateFlag,
        extend.old_compensate_merchant_code oldCompensateMerchantCode,
        o.pay_status payStatus,extend.shop_order_no shopOrderNo,extend.shop_card_no shopCardNo,
        o.dms_audit_reject_reason dmsAuditRejectReason,o.dms_examine_date dmsExamineDate,
        o.dms_status dmsStatus,o.sync_to_dms syncToDms,o.dms_order_no dmsOrderNo,
        extend.sync_dms_flag syncDmsFlag,o.area_code areaCode,
        extend.house_feature houseFeature,o.version
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
    </sql>

    <!-- 报单详情 -->
    <select id="findDecOrderById" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByIdOutDto">
        <include refid="findDecOrderByIdSql" />
        WHERE o.id=#{id} AND o.is_deleted=0
    </select>

    <!-- 报单详情 -->
    <select id="findDecOrderByIds" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByIdOutDto">
        SELECT
        o.id,
        o.print_num printNum,
        o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.contact_mobile_bak contactMobileBak,
        CONCAT(o.provice_name,o.city_name,o.county_name) pccName,
        o.delivery_address address,
        o.clues_id cluesId,
        o.clues_salesman_id cluesSalesmanId,
        o.adviser_id adviserId,
        o.adviser_name adviserName,
        o.company_id companyId,
        o.company_name companyName,
        o.store_id storeId,
        o.store_name storeName,
        o.drp_stage drpStage,
        o.retry_drp_num retryDrpNum,
        CASE stage
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '审核拒绝'
        WHEN 4 THEN '已作废'
        END
        stageName,
        CASE
        WHEN is_to_drp=1 THEN '不同步'
        WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理'
        WHEN is_to_drp !=1 AND drp_stage=2 THEN '同步成功'
        WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.order_create_time orderCreatetime,
        o.order_create_time orderCreateTimeDate,
        o.delivery_date deliveryDate,
        o.order_note orderNote,
        o.goods_amount goodsAmount,
        o.goods_num goodsNum,
        o.buy_reason buyReason,
        o.business_type businessType,
        o.settlement_ticket settlementTicket,
        o.install_id installId,
        o.stage,
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.decorate_company_phone decorateCompanyPhone,
        o.designer_code designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.is_to_drp isToDrp,
        o.provice_id proviceId,
        o.provice_name proviceName,
        o.city_id cityId,
        o.city_name cityName,
        o.county_id countyId,
        o.county_name countyName,
        o.cash_amount cashAmount,
        o.transfer_amount transferAmount,
        o.card_amount cardAmount,
        o.earnest_amount earnestAmount,
        o.order_type orderType,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        o.order_stage orderStage,
        o.modified_date stageTime,o.parent_id parentId,
        o.stransfer_stage stransferStage,
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,
        o.village_id villageId,
        o.village_name villageName,o.to_drp_msg toDrpMsg,
        o.ware_house wareHouse,o.examine_date examineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine_date ELSE o.app_pre_examine_date END preExamineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine ELSE o.app_pre_examine END preExamine,o.settlement_stage
        settlementStage,o.store_type storeType,o.distributor_name dealerName,
        o.card_no cardNo,o.card_id cardId,
        o.order_date orderDate,o.uuid,o.delivery_address deliveryAddress,
        o.decorate_company_type decorateCompanyType,o.activity_remark activityRemark,o.audit_date auditDate,
        <!-- 权限+审核通过+经销订单+需要预审+需要同步 -->
        CASE WHEN o.stage=2 AND o.order_type !=501 AND app_pre_audit=1 AND is_to_drp!=0 THEN 1 ELSE 0 END needPreAudit,
        o.order_channel orderChannel,o.clues_source_code cluesSourceCode,o.is_inStore isInStore,
        o.goods_num_canEnter goodsNumCanEnter,
        o.decorate_category decorateCategory,
        o.decorate_category_name decorateCategoryName,
        o.is_check_price isCheckPrice,
        o.central_purchase centralPurchase,
        o.clues_source_name cluesSource,
        o.clues_created_date cluesCreatedDate,
        o.dep_id depId,
        o.install_check installCheck,
        o.receipt_url receiptUrl,
        o.receipt_type receiptType,
        o.limit_type limitType,
        o.limit_number limitNumber,
        o.is_auto_audit isAutoAudit,
        o.app_pre_audit appPreAudit,
        o.distributor_code distributorCode,
        o.distributor_id distributorId,
        o.store_type_code storeTypeCode,
        o.deal_receiver_id dealReceiverId
        FROM omscenter.declaration_info o
        WHERE o.is_deleted=0
        and o.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        <if test="companyIdList != null and companyIdList.size() > 0">
             and o.company_id in
             <foreach collection="companyIdList" item="companyId" separator="," open="(" close=")">
                 #{companyId}
             </foreach>
        </if>
        <if test="storeIdList != null and storeIdList.size() > 0">
            and o.store_id in
            <foreach collection="storeIdList" item="storeId" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
    </select>



    <!-- 根据外部订单号查询报单详情 -->
    <select id="findDecOrderByOutOrderId" parameterType="java.lang.String" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByIdOutDto">
        SELECT
        o.id,
        o.print_num printNum,
        o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.contact_mobile_bak contactMobileBak,
        CONCAT(o.provice_name,o.city_name,o.county_name) pccName,
        o.delivery_address address,
        o.clues_id cluesId,
        o.clues_salesman_id cluesSalesmanId,
        o.adviser_id adviserId,
        o.adviser_name adviserName,
        o.company_id companyId,
        o.company_name companyName,
        o.store_id storeId,
        o.store_name storeName,
        o.drp_stage drpStage,
        o.retry_drp_num retryDrpNum,
        CASE stage
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '审核拒绝'
        WHEN 4 THEN '已作废'
        END
        stageName,
        CASE
        WHEN is_to_drp=1 THEN '不同步'
        WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理'
        WHEN is_to_drp !=1 AND drp_stage=2 THEN '同步成功'
        WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.order_create_time orderCreatetime,
        o.order_create_time orderCreateTimeDate,
        o.delivery_date deliveryDate,
        o.order_note orderNote,
        o.goods_amount goodsAmount,
        o.goods_num goodsNum,
        o.buy_reason buyReason,
        o.business_type businessType,
        o.settlement_ticket settlementTicket,
        o.install_id installId,
        o.stage,
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.decorate_company_phone decorateCompanyPhone,
        o.designer_code designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.is_to_drp isToDrp,
        o.provice_id proviceId,
        o.provice_name proviceName,
        o.city_id cityId,
        o.city_name cityName,
        o.county_id countyId,
        o.county_name countyName,
        o.cash_amount cashAmount,
        o.transfer_amount transferAmount,
        o.card_amount cardAmount,
        o.earnest_amount earnestAmount,
        o.order_type orderType,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        o.order_stage orderStage,
        o.modified_date stageTime,o.parent_id parentId,
        o.stransfer_stage stransferStage,
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,
        o.village_id villageId,
        o.village_name villageName,o.to_drp_msg toDrpMsg,
        o.ware_house wareHouse,o.examine_date examineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine_date ELSE o.app_pre_examine_date END preExamineDate,
        CASE WHEN app_pre_audit=0 THEN o.pre_examine ELSE o.app_pre_examine END preExamine,o.settlement_stage
        settlementStage,o.store_type storeType,o.distributor_name dealerName,
        o.card_no cardNo,o.card_id cardId,
        o.order_date orderDate,o.uuid,o.delivery_address deliveryAddress,
        o.decorate_company_type decorateCompanyType,o.activity_remark activityRemark,o.audit_date auditDate,
        <!-- 权限+审核通过+经销订单+需要预审+需要同步 -->
        CASE WHEN o.stage=2 AND o.order_type !=501 AND app_pre_audit=1 AND is_to_drp!=0 THEN 1 ELSE 0 END needPreAudit,
        o.order_channel orderChannel,o.clues_source_code cluesSourceCode,o.is_inStore isInStore,
        o.goods_num_canEnter goodsNumCanEnter,
        o.decorate_category decorateCategory,
        o.decorate_category_name decorateCategoryName,
        o.is_check_price isCheckPrice,
        o.central_purchase centralPurchase,
        o.clues_source_name cluesSource,
        o.clues_created_date cluesCreatedDate,
        o.dep_id depId,
        o.install_check installCheck,
        o.receipt_url receiptUrl,
        o.receipt_type receiptType,
        o.limit_type limitType,
        o.limit_number limitNumber,
        o.is_auto_audit isAutoAudit,
        o.app_pre_audit appPreAudit,
        o.distributor_code distributorCode,
        o.distributor_id distributorId,
        o.store_type_code storeTypeCode,
        o.deal_receiver_id dealReceiverId,
        o.created_by createdBy,
        o.created_date createdDate,
        o.modified_by modifiedBy,
        o.modified_date modifiedDate,
        o.is_draft isDraft,
        o.is_scene_deal isSceneDeal,
        o.building building,
        o.unit unit,
        o.house_number houseNumber,
        o.storage,
        o.dcs_out_status dcsStatus,
        o.distributor_name distributorName,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        WHERE o.out_orderId = #{outOrderId} AND o.is_deleted = 0
        order by o.id desc limit 1
    </select>

    <!-- 中台报单详情 -->
    <select id="platFindDecOrderById" resultType="com.fotile.omscenter.decOrder.pojo.dto.PlatFindDecOrderByIdOutDto">
        SELECT
        o.order_type,
        o.company_id companyId,
        o.company_name companyName,
        o.distributor_id distributorId,
        o.distributor_code distributorCode,
        o.distributor_name distributorName,
        o.store_id storeId,
        o.store_name storeName,
        o.store_type storeType,
        <!--订单信息-->
        o.id,<!--订单号-->
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,<!--DRP订单状态名称-->
        o.order_stage orderStage,<!--DRP订单状态-->
        o.created_by createdBy,
        o.adviser_id adviserId,<!--厨电顾问id-->
        o.adviser_name adviserName,<!--厨电顾问名称-->
        o.input_id inputId,<!--厨电顾问(录入人)ID-->
        o.input_name inputName,<!--厨电顾问(录入人)名称-->
        o.original_orderId originalOrderId,<!--原销售订单号-->
        o.out_orderId outOrderId,
        o.out_order_id2 outOrderId2,
        o.stage,<!--云管理审核状态-->
        CASE stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,<!--云管理审核状态名称-->
        o.order_create_time orderCreatetime,<!--创建时间-->
        o.first_return_reason firstReturnReason,<!--一级退货原因id-->
        o.first_return_reason_name firstReturnReasonName,<!--一级退货原因名称-->
        o.sec_return_reason secReturnReason,<!--二级退货原因id-->
        o.sec_return_reason_name secReturnReasonName,<!--二级退货原因名称-->
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,<!--DRP订单号-->
        CASE WHEN app_pre_audit=0 THEN o.pre_examine ELSE o.app_pre_examine END preExamine,<!--预审核-->
        <!--是否需要显示预审核按钮:权限+审核通过+经销订单+需要预审+需要同步 -->
        CASE WHEN o.stage=2 AND o.order_type !=501 AND app_pre_audit=1 AND is_to_drp!=0 THEN 1 ELSE 0 END needPreAudit,
        o.examine_date examineDate,<!--DRP财务审核时间-->
        o.oa_no OANo,<!--OA编号-->
        o.parent_id parentId,<!--关联定金单号-->
        o.drp_stage drpStage,<!--同步drp状态 1：未处理；2：同步成功；3：同步失败-->
        CASE WHEN is_to_drp=1 THEN '不同步' WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理' WHEN is_to_drp
        !=1 AND drp_stage=2 THEN '同步成功' WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败' END drpStageName,<!--同步drp状态名称-->
        o.retry_drp_num retryDrpNum,<!--同步drp错误次数-->
        o.to_drp_stage toDrpStage,<!--订单传送至DRP状态：0:根据drp设置 1：不需要预审核；2：需要审核 默认值0-->
        CASE WHEN app_pre_audit=0 THEN o.pre_examine_date ELSE o.app_pre_examine_date END preExamineDate,<!--预审核时间-->
        o.reason,<!--原因描述-->
        o.settlement_stage settlementStage,<!--结算状态-->
        o.audit_date auditDate,<!--云管理审核时间-->
        o.stransfer_stage stransferStage,<!--定金单状态-->
        o.order_date orderDate,<!--订购日期-->
        <!--业务信息-->
        o.decorate_company_type decorateCompanyType,<!--引流渠道类型 1：家装；2：异业三工 3：设计师-->
        o.clues_id cluesId,<!--线索id-->
        o.clues_salesman_id cluesSalesmanId,<!--线索负责人id-->
        o.clues_source_code cluesSourceCode,<!--线索来源-->
        o.order_note orderNote,<!--备注-->
        o.user_note userNote,
        o.order_channel orderChannel,<!--订单渠道-->
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.decorate_company_phone decorateCompanyPhone,
        o.designer_code designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.activity_remark activityRemark,<!--活动说明-->
        o.buy_reason buyReason,<!--购买原因-->
        <!--配送信息-->
        o.contact_name contactName,<!--收货人-->
        o.village_id villageId,<!--小区id-->
        o.village_name villageName,<!--小区名称-->
        o.storage,<!--默认储位-->
        o.install_id installId,<!--安装单号-->
        o.contact_mobile contactMobile,<!--手机号-->
        o.provice_id proviceId,
        o.provice_name proviceName,
        o.city_id cityId,
        o.city_name cityName,
        o.county_id countyId,
        o.county_name countyName,
        o.delivery_date deliveryDate,<!--送货时间-->
        o.contact_mobile_bak contactMobileBak,<!--联系电话-->
        o.delivery_address deliveryAddress,<!--详细地址-->
        <!--结算信息-->
        o.earnest_amount earnestAmount,<!--定金金额-->
        o.cash_amount cashAmount,<!--现金金额-->
        o.transfer_amount transferAmount,<!--转账金额-->
        o.card_amount cardAmount,<!--刷卡金额-->
        o.pos_amount posAmount,
        o.goods_amount goodsAmount,<!--订单总金额-->
        o.goods_num goodsNum,<!--商品数量-->
        <!--********************其他冗余字段*****-->
        o.uuid,<!--订单uuid-->
        o.is_to_drp isToDrp,<!--是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP,4：同步至事业上DRP-->
        o.business_type businessType,<!--业务类型 默认值：BBC,根据订单类型传值-->
        o.to_drp_msg toDrpMsg,<!--同步至DRP返回的信息-->
        o.to_dms_msg toDmsMsg,
        o.is_inStore isInStore,<!--是否店内成交 0：否；1：是-->
        o.manager_id managerId,
        o.manager_name managerName,
        o.clues_source_name cluesSourceName,
        o.goods_num_canEnter goodsNumCanEnter,
        o.decorate_category decorateCategory,
        o.decorate_category_name decorateCategoryName,
        o.app_pre_audit appPreAudit,
        o.is_check_price isCheckPrice,
        o.stransfer_stage stransferStage,
        o.clues_created_date cluesCreatedDate,
        o.dep_id depId,
        o.receipt_url receiptUrl,
        o.receipt_type receiptType,
        o.limit_type limitType,
        o.limit_number limitNumber,
        o.is_auto_audit isAutoAudit,
        o.deal_receiver_id dealReceiverId,
        o.is_scene_deal isSceneDeal,
        o.channel_category_code channelCategoryCode,
        o.channel_category_name channelCategoryName,
        o.channel_subdivide_name channelSubdivideName,
        o.store_type_code storeTypeCode,
        o.store_type_name storeTypeName,
        o.area_code areaCode,
        o.sample_type sampleType,
        o.building building,
        o.unit unit,
        o.house_number houseNumber,
        o.dcs_out_status dcsStatus,
        o.is_from_app isFromApp,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode,
        o.is_come_devise isComeDevise,
        o.drp_audit_reject_reason drpAuditRejectReason,
        o.ware_house wareHouse,
        extend.oaid,extend.virtual_order_flag virtualOrderFlag,extend.virtual_number_time_out virtualNumberTimeOut,
        extend.carry_mode carryMode,extend.carrier_code carrierCode,o.taobao_trade_no taobaoTradeNo,
        o.region_proxy_flag regionProxyFlag,extend.is_cashier_enabled isCashierEnabled,
        o.pos_pay_amount payAmount, o.remain_pay_amount remainPayAmount,
        o.trade_in_flag  tradeInFlag,o.real_store_id realStoreId,o.real_distributor_id realDistributorId,
        extend.designer_club_member_id designerClubMemberId, extend.designer_club_member_grade designerClubMemberGrade,
        extend.old_compensate_flag oldCompensateFlag,
        o.pay_status,extend.need_feedback_flag needFeedbackFlag,
        extend.supplier_id supplierId,extend.supplier_name supplierName,
        o.dms_audit_reject_reason dmsAuditRejectReason,o.dms_examine_date dmsExamineDate,
        o.dms_status dmsStatus,o.sync_to_dms syncToDms,o.dms_order_no dmsOrderNo,
        extend.sync_dms_flag syncDmsFlag,
        o.cost_short_fall costShortFall,extend.allow_refund_flag allowRefundFlag,
        extend.house_feature houseFeature,o.store_sub_channel_code storeSubChannelCode,o.store_sub_channel_name storeSubChannelName,
        o.version,
        extend.designer_industry_level designerIndustryLevel
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        WHERE o.id=#{id} AND o.is_deleted=0
    </select>

    <select id="getParentId" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        o.parent_id parentId,o.order_type orderType
        FROM omscenter.declaration_info o
        WHERE o.is_deleted = 0 AND o.id = #{id}
    </select>

    <!-- 根据报单id查询商品列表 -->
    <select id="findGoodsByOrderId" resultType="findGoodsByOrderIdOutDto">
        SELECT go.id,
        go.goods_id goodsId,
        go.goods_category_id goodsCategoryId,
        go.created_date createdDate,
        go.goods_name goodsName,
        go.goods_code goodsCode,
        go.goods_num goodsNum,
        go.goods_price goodsPrice,
        go.goods_total_price goodsTotalPrice,
        goods_image goodsImage,
        goods_note goodsNote,
        go.goods_type goodsType,
        is_gift isGift,
        go.order_id orderId,
        go.`sort`,
        go.location_type locationType,
        s.id supplyId,
        s.card_no cardNo,
        s.card_id cardId,
        s.is_consume isConsume,
        go.logistics_no logisticsNo,
        go.logistics_status logisticsStatus,
        go.order_status orderStatus,
        go.goods_note lineNote,
        go.delivery_date deliveryDate,
        go.logistics_status logisticsStatus,
        go.uuid,
        go.source_guid sourceGuid,
        go.guid,
        go.lowest_price lowestPrice,
        go.company_lowest_price companyLowestPrice,
        go.store_lowest_price storeLowestPrice,
        go.cost_estimate costEstimate,
        s.card_name cardName,
        go.code code,
        go.online,
        dcs.logistics_trace_link logisticsTraceLink,
        dcs.express_code expressCode,
        dcs.out_status dcsStatus,
        dcs.province_name provinceName,
        dcs.city_name cityName,
        dcs.county_name countyName,
        dcs.address,
        dcs.transport_type transportType,
        dcs.driver_name driverName,
        dcs.driver_contact driverContact,
        go.is_return isReturn,
        go.sub_order_code subOrderCode,
        go.sc_item_id scItemId,
        go.out_line_id outLineId,
        go.trade_in_flag tradeInFlag,
        cem.province_name installProvinceName,
        cem.city_name installCityName,
        cem.county_name installCountyName,
        cem.address installAddress,
        go.discount_type discountType,
        go.goods_national_code goodsNationalCode,
        go.energy_class energyClass,
        go.discount_code discountCode,
        go.cost_short_fall costShortFall,
        go.diff_total_price diffTotalPrice
        FROM omscenter.goods_declaration go
        inner JOIN omscenter.goods_declaration_supple s ON s.is_deleted=0 AND s.goods_declaration_id = go.id
        left join omscenter.goods_declaration_dcs dcs
        on go.order_id = dcs.order_id and go.id = dcs.order_line_id and dcs.is_deleted = 0
        left join omscenter.goods_declaration_cem cem
        on go.order_id = cem.order_id and go.guid = cem.guid and cem.is_deleted = 0
        WHERE go.order_id = #{orderId}
        AND go.is_deleted = 0
        ORDER BY go.`sort` ASC
    </select>

    <!-- 根据订单id查询商品是否已退货列表 -->
    <select id="findGoodsIsReturnByOrderId" resultType="java.lang.Long">
        SELECT DISTINCT gd.original_goodId
        FROM omscenter.goods_declaration gd,
             omscenter.declaration_info di
        WHERE gd.is_deleted = 0
          AND di.is_deleted = 0
          AND gd.order_id = di.id
          AND di.original_orderId = #{orderId}
          AND (di.order_stage != 4
            AND di.stage !=3
            AND SUBSTRING(di.order_type,-1)=2 <!--逆向订单-->
            )
    </select>

    <sql id="findGoodsByOrderIdsList">
        (
        <foreach collection="orderIdList" item="id" separator="union">
            select @orderIdList:=#{id} as orderId
        </foreach>
        ) as orderIdList
    </sql>
    <!-- 根据报单id查询商品列表 -->
    <select id="findGoodsByOrderIds" resultType="findGoodsByOrderIdOutDto">
        SELECT go.id,go.goods_id goodsId,go.goods_category_id goodsCategoryId,go.created_date createdDate,
        go.goods_name goodsName,go.goods_code goodsCode,go.goods_num goodsNum,go.goods_price goodsPrice,
        go.goods_total_price goodsTotalPrice,goods_image goodsImage,goods_note goodsNote,go.goods_type goodsType,
        is_gift isGift,go.order_id orderId,go.`sort`,go.location_type locationType,go.code
        FROM omscenter.goods_declaration go
        <if test="orderIdList !=null and orderIdList.size>0">
            inner join
            <include refid="findGoodsByOrderIdsList"/>
            on orderIdList.orderId=go.order_id
        </if>
        WHERE go.is_deleted=0
        ORDER BY go.`sort` ASC
    </select>

    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsById" resultType="findGoodsByIdOutDto">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_num         goodsNum,
               go.goods_price       goodsPrice,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               go.goods_category_id goodsCategoryId,
               goods_note           goodsNote,
               go.goods_type        goodsType,
               is_gift              isGift
        FROM omscenter.goods_declaration go
        WHERE go.id = #{id}
    </select>

    <!-- 修改商品 -->
    <update id="updateGoods">
        UPDATE omscenter.goods_declaration
        <set>
            modified_date=now(),modified_by=#{modifiedBy}
            <if test="goodsCategoryId !=null">,goods_category_id=#{goodsCategoryId}</if>
            <if test="goodsId !=null">,goods_id=#{goodsId}</if>
            <if test="goodsName !=null ">,goods_name=#{goodsName}</if>
            <if test="goodsPrice !=null">,goods_price=#{goodsPrice}</if>
            <if test="goodsNum !=null">,goods_num=#{goodsNum}</if>
            <if test="goodsTotalPrice !=null">,goods_total_price=#{goodsTotalPrice}</if>
            <if test="goodsImage !=null">,goods_image=#{goodsImage}</if>
            <if test="goodsNote !=null">,goods_note=#{goodsNote}</if>
            <if test="goodsType !=null">,goods_type=#{goodsType}</if>
            <if test="isGift !=null">,is_gift=#{isGift}</if>
            <if test="posPayAmount != null ">,pos_pay_amount = #{posPayAmount}</if>
            <choose>
                <when test="locationType !=null">
                    #{locationType},
                </when>
                <otherwise>
                    1,
                </otherwise>
            </choose>
        </set>
        WHERE id=#{id}
    </update>

    <!-- 删除商品 -->
    <update id="delGoods">
        UPDATE omscenter.goods_declaration
        SET is_deleted=1,
            modified_date=now(),
            modified_by=#{modifiedBy}
        WHERE id = #{id}
    </update>

    <!-- 导出订单 -->
    <select id="exportOrder" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderPageAllOutDto">
        SELECT o.id,o.clues_id cluesId,o.created_by createdBy,o.order_create_time orderCreatetime,o.goods_amount goodsAmount,
        o.contact_name contactName,o.contact_mobile contactMobile,o.delivery_address address,
        o.company_id companyId,o.company_name companyName,o.store_id storeId,o.clues_salesman_id cluesSalesmanId,o.decorate_company_type decorateCompanyType,
        o.decorate_company_code decorateCompanyCode,o.decorate_company_name decorateCompanyName,o.designer_code designerCode,
        o.designer_name designerName,o.designer_phone designerPhone,o.order_type orderType,o.install_id
        installId,o.input_id inputId
        ,FIND_IN_SET(o.order_type,o.order_type_check_install) orderTypeCheckInstall,o.is_from_app
        isFromApp,central_purchase centralPurchase,o.install_check installCheck
        ,o.stage stage,o.drp_stage drpStage,o.store_type_code storeTypeCode,parent_id parentId,
        o.cut_time cutTime,app_pre_audit appPreAudit,o.is_scene_deal isSceneDeal,extend.is_cashier_enabled isCashierEnabled,
        o.is_monthly_settlement isMonthlySettlement,o.cashier_charge_mapping cashierChargeMapping,
        o.remain_pay_amount remainPayAmount,o.is_from_app isFromApp,extend.old_compensate_flag oldCompensateFlag,
        o.pay_status payStatus
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on o.id = extend.order_id and extend.is_deleted = 0
        WHERE o.id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 导出订单扩展表 -->
    <select id="exportOrderExtend" resultType="com.fotile.omscenter.decOrder.pojo.dto.PlatFindDecOrderByIdOutDto">
        SELECT o.id,o.goods_amount goodsAmount,o.order_create_time orderCreatetime,o.contact_name
        contactName,o.contact_mobile contactMobile,CONCAT(o.provice_name,o.city_name,o.county_name) pccName,
        o.delivery_address address,o.user_note userNote,o.order_note orderNote,o.created_by createdBy,o.clues_id
        cluesId,
        o.company_id companyId,o.store_id storeId
        FROM omscenter.declaration_info o
        WHERE o.id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据订单id查询商品数量、总金额 -->
    <select id="findGoodsNumAndAmountByOrderId" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindGoodsNumAndAmountByOrderIdOutDto">
        SELECT order_id orderId, sum(goods_num) goodsNum, sum(goods_total_price) goodsTotalPrice
        FROM omscenter.goods_declaration
        WHERE is_deleted = 0
          AND order_id = #{orderId}
        GROUP BY order_id
    </select>

    <!-- 修改订单 -->
    <update id="updateDecOrder">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now(),
            modified_by=#{modifiedBy},
            version =  version + 1,
            <if test="orderCreatetime != null and orderCreatetime != ''">
                order_create_time=#{orderCreatetime},<!-- 订单创建时间 -->
            </if>
            <if test="orderDate != null and orderDate != ''">
                order_date=#{orderDate},<!-- 订购时间 -->
            </if>
            <if test="orderChannel != null and orderChannel != ''">
                order_channel=#{orderChannel},<!-- 订单渠道 -->
            </if>
            <if test="cardNo !=null and cardNo !='' and cardId !=null">card_no=#{cardNo},card_id=#{cardId},</if>
            <if test="userName != null"><!-- 买家姓名 -->

            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                goods_amount=abs(#{goodsAmount}),
            </if>
            <if test="contactName != null"><!-- 收件人 -->
                contact_name=#{contactName},
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                contact_mobile=#{contactMobile},
            </if>
            <if test="userNote != null"><!-- 买家留言 -->
                user_note=#{userNote},
            </if>
            <if test="orderNote != null"><!-- 其他备注 -->
                order_note=#{orderNote},
            </if>
            <choose>
                <when test="deliveryDate != null and deliveryDate != ''">
                    delivery_date = #{deliveryDate},
                </when>
                <otherwise>
                    delivery_date = null,
                </otherwise>
            </choose>
            <if test="companyId != null"><!-- 分公司id -->
                company_id=#{companyId},
            </if>
            <if test="storeId != null"><!-- 门店id -->
                store_id=#{storeId},
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                buy_reason=#{buyReason},
            </if>
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                adviser_id=#{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                adviser_name=#{adviserName},
            </if>
            <if test="adviserId != null"><!-- 厨电顾问(录入人)ID -->
                input_id=#{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问(录入人)名称 -->
                input_name=#{adviserName},
            </if>
            clues_id=#{cluesId},
            user_id=#{userId},
            user_name=#{userName},
            clues_salesman_id=#{cluesSalesmanId},
            clues_created_date = #{cluesCreatedDate},
            manager_id=#{managerId},
            manager_name=#{managerName},
            clues_source_code=#{cluesSourceCode},
            clues_source_name=#{cluesSourceName},
            integrate_id = #{integrateId},
            content_template_id = #{contentTemplateId},
            is_come_devise = #{isComeDevise},
            <if test="settlementTicket != null"><!-- 结算小票号 -->
                settlement_ticket=#{settlementTicket},
            </if>
            <if test="approveStatus != null"><!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                approve_status=#{approveStatus},
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                provice_id=#{proviceId},
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                provice_name=#{proviceName},
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                city_id=#{cityId},
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                city_name=#{cityName},
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                county_id=#{countyId},
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                county_name=#{countyName},
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                delivery_address=#{deliveryAddress},
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                goods_num=abs(#{goodsNum}),
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                non_gift_num=abs(#{nonGiftNum}),
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                non_gift_amount=abs(#{nonGiftAmount}),
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                install_id=#{installId},
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                contact_mobile_bak=#{contactMobileBak},
            </if>
            decorate_company_type=#{decorateCompanyType},
            decorate_company_code=#{decorateCompanyCode},
            decorate_company_name=#{decorateCompanyName},
            decorate_company_phone=#{decorateCompanyPhone},<!-- 家装公司手机号 -->
            designer_code=#{designerCode},
            designer_name=#{designerName},
            designer_phone=#{designerPhone},
            <if test="stage != null"><!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 -->
                stage=#{stage},
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                cash_amount=#{cashAmount},
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                transfer_amount=#{transferAmount},
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                card_amount=#{cardAmount},
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                pos_amount=#{posAmount},
            </if>
            <if test="earnestAmount != null"><!-- 定金金额 -->
                earnest_amount=#{earnestAmount},
            </if>
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金 -->
                order_type=#{orderType},
            </if>
            village_id = #{villageId},
            village_name = #{villageName},
            <if test="areaCode != null"><!-- 大区编码 -->
                area_code=#{areaCode},
            </if>
            <if test="areaName != null"><!-- 大区名称 -->
                area_name=#{areaName},
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                distributor_id=#{distributorId},
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                distributor_name=#{distributorName},
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                distributor_code=#{distributorCode},
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                channel_category_code=#{channelCategoryCode},
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                channel_category_name=#{channelCategoryName},
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                channel_subdivide_code=#{channelSubdivideCode},
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                channel_subdivide_name=#{channelSubdivideName},
            </if>

            <if test="storeType != null"><!-- 门店类型 -->
                store_type=#{storeType},
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                company_name=#{companyName},
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                store_name=#{storeName},
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!--活动说明-->
                activity_remark=#{activityRemark},
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                goods_num_canEnter=#{goodsNumCanEnter},
            </if>
            <if test="storage !=null and storage !=''"><!--默认储位-->
                storage=#{storage},
            </if>
            <if test="decorateCategory != null and decorateCategory != ''">
                decorate_category=#{decorateCategory},
            </if>
            <if test="decorateCategoryName != null and decorateCategoryName != ''">
                decorate_category_name=#{decorateCategoryName},
            </if>
            <if test="businessType !=null and businessType !='' ">
                business_type=#{businessType},
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                is_transfer=#{isTransfer},
            </if>
            cut_time=#{cutTime},
            <!--安装单号不检查订单类型,多个值逗号分隔-->
            order_type_check_install=#{orderTypeCheckInstall}
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,store_type_code=#{storeTypeCode}
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,store_type_name=#{storeTypeName}
            </if>
            <if test="installCheck != null"><!-- 安装单号检查接口 0:drp；1：云管理 -->
                ,install_check=#{installCheck}
            </if>
          <!--  <if test="receiptUrl != null and receiptUrl != ''">&lt;!&ndash; 小票地址 &ndash;&gt;
                ,receipt_url = #{receiptUrl}
            </if>-->
            <!-- 小票地址 -->
            <choose>
                <when test="receiptUrl != null and receiptUrl != ''">
                    ,receipt_url = #{receiptUrl}
                </when>
                <otherwise>
                    ,receipt_url = null
                </otherwise>
            </choose>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,receipt_type = #{receiptType}
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,limit_type = #{limitType}
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,limit_number = #{limitNumber}
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,is_auto_audit = #{isAutoAudit}
            </if>
            <if test="appPreAudit != null"><!-- 是否需要预审核 -->
                ,app_pre_audit = #{appPreAudit}
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,deal_receiver_id = #{dealReceiverId}
            </if>
            <if test="isDraft != null"><!-- 草稿1是0否 -->
                ,is_draft = #{isDraft}
            </if>
            <if test="isCheckPrice != null"><!-- 是否检查价格 0：否；1：是 -->
                ,is_check_price = #{isCheckPrice}
            </if>
            <if test="isInStore !=null "><!-- 是否店内成交 0：否；1：是 -->
                ,is_inStore = #{isInStore}
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,contact_mobile_enc = #{contactMobileEnc}
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,has_ambry = #{hasAmbry}
            </if>
            ,is_scene_deal = #{isSceneDeal}
            ,building = #{building}
            ,unit = #{unit}
            ,house_number = #{houseNumber}
            <if test="regionProxyFlag != null">
                ,region_proxy_flag = #{regionProxyFlag}
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,affect_company_quality_flag = #{affectCompanyQualityFlag}
            </if>
            <if test="costShortFall != null">
                ,cost_short_fall = #{costShortFall}
            </if>
            <if test="isMonthlySettlement != null">
                ,is_monthly_settlement = #{isMonthlySettlement}
            </if>
            <if test="cashierChargeMapping != null">
                ,cashier_charge_mapping = #{cashierChargeMapping}
            </if>
            ,has_attachment = #{hasAttachment}
            ,trade_in_flag = #{tradeInFlag}
            ,real_store_id = #{realStoreId}
            ,real_distributor_id = #{realDistributorId}
            ,store_sub_channel_code = #{storeSubChannelCode}
            ,store_sub_channel_name = #{storeSubChannelName}
        </set>
        WHERE id=#{id}
    </update>

    <!-- 新增商品 -->
    <insert id="addGoods" parameterType="addGoodsInDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        omscenter.goods_declaration(order_id,goods_category_id,goods_id,goods_name,goods_price,goods_num,goods_total_price,goods_image,goods_code,goods_note,goods_type,is_gift,created_by,created_date,modified_by,modified_date,`sort`,guid,location_type)
        VALUES(#{orderId},#{goodsCategoryId},#{goodsId},#{goodsName},#{goodsPrice},#{goodsNum},#{goodsTotalPrice},#{goodsImage},#{goodsCode},#{goodsNote},#{goodsType},#{isGift},#{createdBy},now(),#{modifiedBy},now(),#{sort},CONCAT('HLB-',UUID()),
        <choose>
            <when test="locationType !=null">
                #{locationType}
            </when>
            <otherwise>
                1
            </otherwise>
        </choose>
        )
    </insert>

    <!-- 新增报单 -->
    <insert id="addDecOrder" parameterType="addDecOrderInDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        omscenter.declaration_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null"><!-- 买家id -->
                user_id,
            </if>
            <if test="userName != null"><!-- 买家姓名 -->
                user_name,
            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                goods_amount,
            </if>
            <if test="contactName != null"><!-- 收件人 -->
                contact_name,
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                contact_mobile,
            </if>
            <if test="userNote != null"><!-- 买家留言 -->
                user_note,
            </if>
            <if test="orderNote != null"><!-- 其他备注 -->
                order_note,
            </if>
            <if test="deliveryDate != null and deliveryDate !=''"><!-- 送货时间 -->
                delivery_date,
            </if>
            <if test="companyId != null"><!-- 分公司id -->
                company_id,
            </if>
            <if test="storeId != null"><!-- 门店id -->
                store_id,
            </if>
            <if test="cluesId != null"><!-- 线索id -->
                clues_id,
            </if>
            <if test="cluesSalesmanId != null"><!-- 线索负责人关联的业务员id -->
                clues_salesman_id,
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                buy_reason,
            </if>
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                adviser_id,
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                adviser_name,
            </if>
            <if test="adviserId != null"><!-- 厨电顾问(录入人)ID -->
                input_id,
            </if>
            <if test="adviserName != null"><!-- 厨电顾问(录入人)名称 -->
                input_name,
            </if>
            <if test="managerId != null"><!-- 客户经理(线索负责人)ID -->
                manager_id,
            </if>
            <if test="managerName != null"><!-- 客户经理(线索负责人)名称 -->
                manager_name,
            </if>
            <if test="settlementTicket != null"><!-- 结算小票号 -->
                settlement_ticket,
            </if>
            <if test="approveStatus != null"><!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                approve_status,
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                provice_id,
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                provice_name,
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                city_id,
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                city_name,
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                county_id,
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                county_name,
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                delivery_address,
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                goods_num,
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                non_gift_num,
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                non_gift_amount,
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                install_id,
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                contact_mobile_bak,
            </if>
            <if test="decorateCompanyCode != null"><!-- 家装公司编码 -->
                decorate_company_code,
            </if>
            <if test="decorateCompanyName != null"><!-- 家装公司名称 -->
                decorate_company_name,
            </if>
            <if test="decorateCompanyPhone != null"><!-- 家装公司名称 -->
                decorate_company_phone,
            </if>
            <if test="designerCode != null"><!-- 设计师编码 -->
                designer_code,
            </if>
            <if test="designerName != null"><!-- 设计师名称 -->
                designer_name,
            </if>
            <if test="designerPhone != null"><!-- 设计师手机号 -->
                designer_phone,
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                cash_amount,
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                transfer_amount,
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                card_amount,
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                pos_amount,
            </if>
            <if test="earnestAmount != null"><!-- 定金金额 -->
                earnest_amount,
            </if>
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金 -->
                order_type,
            </if>
            <if test="villageId != null"><!-- 小区id -->
                village_id,
            </if>
            <if test="villageName != null"><!-- 小区名称 -->
                village_name,
            </if>
            <if test="parentId != null"><!-- 关联定金订单号 -->
                parent_id,
            </if>
            <if test="isToDrp != null"><!-- 是否同步至drp -->
                is_to_drp,
            </if>
            <if test="areaCode != null"><!-- 大区编码 -->
                area_code,
            </if>
            <if test="areaName != null"><!-- 大区名称 -->
                area_name,
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                distributor_id,
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                distributor_name,
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                distributor_code,
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                channel_category_code,
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                channel_category_name,
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                channel_subdivide_code,
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                channel_subdivide_name,
            </if>
            <if test="cluesSourceCode != null"><!-- 线索来源编码 -->
                clues_source_code,
            </if>
            <if test="cluesSourceName != null"><!-- 线索来源名称 -->
                clues_source_name,
            </if>
            <if test="storeType != null"><!-- 门店类型 -->
                store_type,
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                company_name,
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                store_name,
            </if>
            <if test="cardNo != null and cardNo !='' and cardId != null "><!-- 优惠券 -->
                card_no,card_id,
            </if>
            <if test="stage != null "><!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 -->
                stage,
            </if>
            <if test="orderDate != null"><!-- 订购时间 -->
                order_date,
            </if>
            <if test="uuid != null "><!-- 订购时间 -->
                uuid,
            </if>

            <if test="decorateCompanyType != null"><!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
                decorate_company_type,
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!--活动说明-->
                activity_remark,
            </if>
            <if test="appPreAudit != null "><!--是否在云管理预审核 0：否；1：是 2:预审通过-->
                app_pre_audit,app_pre_examine,
            </if>
            <if test="orderChannel !=null and orderChannel !=''"><!-- 订单渠道 -->
                order_channel,
            </if>
            <if test="isInStore !=null "><!-- 是否店内成交 0：否；1：是 -->
                is_inStore,
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                goods_num_canEnter,
            </if>
            <if test="storage !=null and storage !=''"><!--默认储位-->
                storage,
            </if>
            <if test="decorateCategory != null and decorateCategory != ''">
                decorate_category,
            </if>
            <if test="decorateCategoryName != null and decorateCategoryName != ''">
                decorate_category_name,
            </if>
            <if test="businessType !=null and businessType !='' ">
                business_type,
            </if>
            <if test="cutTime !=null and cutTime !='' ">
                cut_time,
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                is_transfer,
            </if>
            order_create_time,created_by,created_date,modified_date,is_from_app,sample_type
            <if test="orderTypeCheckInstall !=null and orderTypeCheckInstall !=''"><!--安装单号不检查订单类型,多个值逗号分隔-->
                ,order_type_check_install
            </if>
            <if test="centralPurchase !=null and centralPurchase !=''"><!--集采单号，多个值用逗号分隔-->
                ,central_purchase
            </if>
            <if test="isCheckPrice !=null"><!--是否价格检查 0：否；1：是-->
                ,is_check_price
            </if>
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,store_type_code
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,store_type_name
            </if>
            <if test="depId != null"><!-- 门店所属部门 -->
                ,dep_id
            </if>
            <if test="cluesCreatedDate != null and cluesCreatedDate !=''"><!-- 线索创建时间 -->
                ,clues_created_date
            </if>
            <if test="installCheck != null"><!-- 安装单号检查接口 0:drp；1：云管理 -->
                ,install_check
            </if>
            <if test="receiptUrl != null and receiptUrl != ''"><!-- 小票地址 -->
                ,receipt_url
            </if>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,receipt_type
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,limit_type
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,limit_number
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,is_auto_audit
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,deal_receiver_id
            </if>
            <if test="isDraft != null"><!-- 是否是草稿 -->
                ,is_draft
            </if>
            <if test="isQuick != null"><!-- 是否快捷录单 -->
                ,is_quick
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,contact_mobile_enc
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,has_ambry
            </if>
            ,is_scene_deal, building, unit, house_number,integrate_id,content_template_id,store_sub_channel_code,store_sub_channel_name
            <if test="isComeDevise != null"><!-- 是否有橱柜 -->
                ,is_come_devise
            </if>
            <if test="isOldCustomer != null">
                ,is_old_customer
            </if>
            <if test="regionProxyFlag != null">
                ,region_proxy_flag
            </if>
            <if test="hasAttachment != null">
                ,has_attachment
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,affect_company_quality_flag
            </if>
            <if test="costShortFall != null">
                ,cost_short_fall
            </if>
            <if test="isMonthlySettlement != null">
                ,is_monthly_settlement
            </if>
            <if test="cashierChargeMapping != null">
                ,cashier_charge_mapping
            </if>
            <if test="tradeInFlag != null">
                ,trade_in_flag
            </if>
            <if test="realStoreId != null">
                ,real_store_id
            </if>
            <if test="realDistributorId != null">
                ,real_distributor_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null"><!-- 买家id -->
                #{userId},
            </if>
            <if test="userName != null"><!-- 买家姓名 -->
                #{userName},
            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                abs(#{goodsAmount}),
            </if>
            <if test="contactName != null"><!-- 收件人 -->
                #{contactName},
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                #{contactMobile},
            </if>
            <if test="userNote != null"><!-- 买家留言 -->
                #{userNote},
            </if>
            <if test="orderNote != null"><!-- 其他备注 -->
                #{orderNote},
            </if>
            <if test="deliveryDate != null and deliveryDate !=''"><!-- 送货时间 -->
                #{deliveryDate},
            </if>
            <if test="companyId != null"><!-- 分公司id -->
                #{companyId},
            </if>
            <if test="storeId != null"><!-- 门店id -->
                #{storeId},
            </if>
            <if test="cluesId != null"><!-- 线索id -->
                #{cluesId},
            </if>
            <if test="cluesSalesmanId != null"><!-- 线索负责人关联的业务员id -->
                #{cluesSalesmanId},
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                #{buyReason},
            </if>
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                #{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                #{adviserName},
            </if>
            <if test="adviserId != null"><!-- 厨电顾问(录入人)ID -->
                #{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问(录入人)名称 -->
                #{adviserName},
            </if>
            <if test="managerId != null"><!-- 客户经理(线索负责人)ID -->
                #{managerId},
            </if>
            <if test="managerName != null"><!-- 客户经理(线索负责人)名称 -->
                #{managerName},
            </if>
            <if test="settlementTicket != null"><!-- 结算小票号 -->
                #{settlementTicket},
            </if>
            <if test="approveStatus != null"><!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                #{approveStatus},
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                #{proviceId},
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                #{proviceName},
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                #{cityId},
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                #{cityName},
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                #{countyId},
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                #{countyName},
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                #{deliveryAddress},
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                abs(#{goodsNum}),
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                abs(#{nonGiftNum}),
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                abs(#{nonGiftAmount}),
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                #{installId},
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                #{contactMobileBak},
            </if>
            <if test="decorateCompanyCode != null"><!-- 家装公司编码 -->
                #{decorateCompanyCode},
            </if>
            <if test="decorateCompanyName != null"><!-- 家装公司名称 -->
                #{decorateCompanyName},
            </if>
            <if test="decorateCompanyPhone != null"><!-- 家装公司手机号 -->
                #{decorateCompanyPhone},
            </if>
            <if test="designerCode != null"><!-- 设计师编码 -->
                #{designerCode},
            </if>
            <if test="designerName != null"><!-- 设计师名称 -->
                #{designerName},
            </if>
            <if test="designerPhone != null"><!-- 设计师手机号 -->
                #{designerPhone},
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                #{cashAmount},
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                #{transferAmount},
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                #{cardAmount},
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                #{posAmount},
            </if>
            <if test="earnestAmount != null"><!-- 定金金额 -->
                #{earnestAmount},
            </if>
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金 -->
                #{orderType},
            </if>
            <if test="villageId != null"><!-- 小区id -->
                #{villageId},
            </if>
            <if test="villageName != null"><!-- 小区名称 -->
                #{villageName},
            </if>
            <if test="parentId != null"><!-- 关联定金订单号 -->
                #{parentId},
            </if>
            <if test="isToDrp != null"><!-- 是否同步至drp -->
                #{isToDrp},
            </if>
            <if test="areaCode != null"><!-- 大区编码 -->
                #{areaCode},
            </if>
            <if test="areaName != null"><!-- 大区名称 -->
                #{areaName},
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                #{distributorId},
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                #{distributorName},
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                #{distributorCode},
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                #{channelCategoryCode},
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                #{channelCategoryName},
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                #{channelSubdivideCode},
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                #{channelSubdivideName},
            </if>
            <if test="cluesSourceCode != null"><!-- 线索来源编码 -->
                #{cluesSourceCode},
            </if>
            <if test="cluesSourceName != null"><!-- 线索来源名称 -->
                #{cluesSourceName},
            </if>
            <if test="storeType != null"><!-- 门店类型 -->
                #{storeType},
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                #{companyName},
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                #{storeName},
            </if>
            <if test="cardNo != null and cardNo !='' and cardId != null "><!-- 优惠券 -->
                #{cardNo},#{cardId},
            </if>

            <if test="stage != null "><!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 -->
                #{stage},
            </if>
            <if test="orderDate != null "><!-- 订购时间 -->
                #{orderDate},
            </if>
            <if test="uuid != null "><!-- 订购时间 -->
                #{uuid},
            </if>

            <if test="decorateCompanyType != null"><!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
                #{decorateCompanyType},
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!-- 活动说明 -->
                #{activityRemark},
            </if>
            <if test="appPreAudit != null "><!--是否在云管理预审核 0：否；1：是 2:预审通过-->
                #{appPreAudit},1,
            </if>
            <if test="orderChannel !=null and orderChannel !=''"><!-- 订单渠道 -->
                #{orderChannel},
            </if>
            <if test="isInStore !=null "><!-- 是否店内成交 0：否；1：是 -->
                #{isInStore},
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                #{goodsNumCanEnter},
            </if>
            <if test="storage !=null and storage !=''"><!--默认储位-->
                #{storage},
            </if>
            <if test="decorateCategory != null and decorateCategory != ''">
                #{decorateCategory},
            </if>
            <if test="decorateCategoryName != null and decorateCategoryName != ''">
                #{decorateCategoryName},
            </if>
            <if test="businessType !=null and businessType !='' ">
                #{businessType},
            </if>
            <if test="cutTime !=null and cutTime !='' ">
                #{cutTime},
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                #{isTransfer},
            </if>
            <if test="orderCreatetime != null and orderCreatetime != '' ">
                #{orderCreatetime},
            </if>
            <if test="orderCreatetime == null or orderCreatetime == '' ">
                now(),
            </if>
            #{createdBy},now(),now(),#{isFromApp},
            <choose><!--出样类型
→
sampleType
0 非上样订单
5 上样订单
3 特价订单

0和5的式样
 * 101 经销零售订单→0 非上样订单
 * 102 经销零售销退订单→0 非上样订单
 * 201 经销上样订单→0 非上样订单
 * 202 经销退样订单→0 非上样订单
 * 301 经销样机销售订单→0 非上样订单(修改)
 * 401 换货销售订单→0 非上样订单
 * 402 换货退货订单→0 非上样订单
 * 501 定金订单→不同步drp
 * 601 KA上样订单→5 上样订单 （修改）
 * 602 KA退样订单→5 上样订单  （修改）
 * 701 KA样机销售订单→0 非上样订单  （修改）
 * 702 KA样机销退订单→0 非上样订单  （修改）
 * 801 B2B销售订单→0 非上样订单
 * 802 B2B退货订单→0 非上样订单-->
                <when test="isCheckPrice !=null and isCheckPrice==0">3</when>
                <otherwise>
                    <choose>
                        <when test="orderType!=null and (orderType==101 or orderType==102)">0</when>
                        <when test="orderType!=null and (orderType==201 or orderType==202)">0</when>
                        <when test="orderType!=null and orderType==301">0</when>
                        <when test="orderType!=null and (orderType==401 or orderType==402)">0</when>
                        <when test="orderType!=null and (orderType==601 or orderType==602)">5</when>
                        <when test="orderType!=null and (orderType==701 or orderType==702)">0</when>
                        <when test="orderType!=null and (orderType==801 or orderType==802)">0</when>
                        <otherwise>null</otherwise><!--像501给个默认值空，否则报异常-->
                    </choose>
                </otherwise>
            </choose>
            <if test="orderTypeCheckInstall !=null and orderTypeCheckInstall !=''"><!--安装单号不检查订单类型,多个值逗号分隔-->
                ,#{orderTypeCheckInstall}
            </if>
            <if test="centralPurchase !=null and centralPurchase !=''"><!--集采单号，多个值用逗号分隔-->
                ,#{centralPurchase}
            </if>
            <if test="isCheckPrice !=null"><!--是否价格检查 0：否；1：是-->
                ,#{isCheckPrice}
            </if>
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,#{storeTypeCode}
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,#{storeTypeName}
            </if>
            <if test="depId != null"><!-- 门店所属部门 -->
                ,#{depId}
            </if>
            <if test="cluesCreatedDate != null and cluesCreatedDate !=''"><!-- 线索创建时间 -->
                ,#{cluesCreatedDate}
            </if>
            <if test="installCheck != null"><!-- 安装单号检查接口 0:drp；1：云管理 -->
                ,#{installCheck}
            </if>
            <if test="receiptUrl != null and receiptUrl != ''"><!-- 小票地址 -->
                ,#{receiptUrl}
            </if>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,#{receiptType}
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,#{limitType}
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,#{limitNumber}
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,#{isAutoAudit}
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,#{dealReceiverId}
            </if>
            <if test="isDraft != null"><!-- 是否是草稿 -->
                ,#{isDraft}
            </if>
            <if test="isQuick != null"><!-- 是否快捷录单 -->
                ,#{isQuick}
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,#{contactMobileEnc}
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,#{hasAmbry}
            </if>
            ,#{isSceneDeal}, #{building}, #{unit}, #{houseNumber}
            ,#{integrateId},#{contentTemplateId},#{storeSubChannelCode},#{storeSubChannelName}
            <if test="isComeDevise != null">
                ,#{isComeDevise}
            </if>
            <if test="isOldCustomer != null">
                ,#{isOldCustomer}
            </if>
            <if test="regionProxyFlag != null">
                ,#{regionProxyFlag}
            </if>
            <if test="hasAttachment != null">
                ,#{hasAttachment}
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,#{affectCompanyQualityFlag}
            </if>
            <if test="costShortFall != null">
                ,#{costShortFall}
            </if>
            <if test="isMonthlySettlement != null">
                ,#{isMonthlySettlement}
            </if>
            <if test="cashierChargeMapping != null">
                ,#{cashierChargeMapping}
            </if>
            <if test="tradeInFlag != null">
                ,#{tradeInFlag}
            </if>
            <if test="realStoreId != null">
                ,#{realStoreId}
            </if>
            <if test="realDistributorId != null">
                ,#{realDistributorId}
            </if>
        </trim>
    </insert>

    <update id="updateStransferStage">
        update omscenter.declaration_info o set modified_date=NOW(),o.stransfer_stage =#{transferStage}
        <if test="uuid !=null and uuid !=''">
            ,uuid=#{uuid}
        </if>
        where o.id=#{id}
    </update>
    <!-- 修改定金订单商品uuid -->
    <update id="updateGoodsUUID">
        update omscenter.goods_declaration o
        set modified_date=NOW(),o.uuid=#{uuid}
        where o.order_id = #{id}
    </update>

    <!-- 修改定金订单商品临时表uuid -->
    <update id="updateGoodsTempUUID">
        update omscenter.goods_declaration_temp o
        set modified_date=NOW(),o.uuid=#{uuid}
        where o.order_id = #{id}
    </update>

    <!-- 修改定金订单商品明细uuid -->
    <update id="updateGoodsSupplyUUID">
        update omscenter.goods_declaration_supple o
        set modified_date=NOW(),o.uuid=#{uuid}
        where o.order_id = #{id}
    </update>

    <update id="updateGoodsSupplyTempUUID">
        update omscenter.goods_declaration_supple_temp o
        set modified_date=NOW(),o.uuid=#{uuid}
        where o.order_id = #{id}
    </update>

    <sql id="findDecOrderByCluesIdWHERE">
        <where>
            o.is_deleted=0 and o.is_draft = 0
            <if test="inDto.orderCreatetimeStart !=null and inDto.orderCreatetimeStart !=''"><!-- 报单时间-起始 -->
                AND o.order_create_time &gt;=DATE_FORMAT(#{inDto.orderCreatetimeStart},'%Y-%m-%d')
            </if>
            <if test="inDto.orderCreatetimeEnd !=null and inDto.orderCreatetimeEnd !=''"><!-- 报单时间-结束 -->
                AND o.order_create_time &lt; date_add(DATE_FORMAT(#{inDto.orderCreatetimeEnd},'%Y-%m-%d'),INTERVAL 1 DAY)
            </if>
            <if test="inDto.pageEndTime !=null and inDto.pageEndTime !=''"><!-- 报单时间-结束 -->
                AND o.order_create_time &lt;= DATE_FORMAT(#{inDto.pageEndTime},'%Y-%m-%d %H:%i:%S')
            </if>
            <!-- 审核时间 -->
            <if test="inDto.auditDateStart != null and inDto.auditDateStart != ''">
                AND o.audit_date &gt;= #{inDto.auditDateStart}
            </if>
            <if test="inDto.auditDateEnd != null and inDto.auditDateEnd != ''">
                AND o.audit_date &lt;= #{inDto.auditDateEnd}
            </if>
            <if test="inDto.stage !=null">
                <choose>
                    <when test="inDto.stage ==1 or inDto.stage ==2 or inDto.stage ==3"><!--1：待审核；2：审核通过；3：审核拒绝-->
                        AND o.stage =#{inDto.stage}<!-- 审核状态 -->
                    </when>
                    <when test="inDto.stage == 6"><!--6：已作废-->
                        AND o.stage = 4<!-- 审核状态 -->
                    </when>
                    <when test="inDto.stage ==4 or inDto.stage ==5 "><!--4：未转单；5：已转单；-->
                        AND o.order_type IN(501,303)
                        <choose>
                            <when test="inDto.stage ==4">AND o.stransfer_stage =1</when><!--未转单-->
                            <when test="inDto.stage ==5">AND o.stransfer_stage =2</when><!--已转单-->
                        </choose>
                    </when>
                    <when test="inDto.stage ==7 or inDto.stage ==8 or inDto.stage ==9"><!--7：不同步；8：同步成功；9：同步失败-->
                        <choose>
                            <when test="inDto.stage ==7">AND ( is_to_drp=1 )</when><!--不同步-->
                            <when test="inDto.stage ==8">AND ( o.drp_stage =2 AND is_to_drp !=1 )
                            </when><!--同步成功-->
                            <when test="inDto.stage ==9">AND ( o.drp_stage =3 AND is_to_drp !=1 )
                            </when><!--同步失败-->
                        </choose>
                    </when>
                    <when test="inDto.stage ==10 or inDto.stage ==11 or inDto.stage ==12"><!--drp订单状态 10：打开；11：审核；12：作废-->
                        <choose>
                            <when test="inDto.stage ==10">AND o.order_stage=1</when><!--打开-->
                            <when test="inDto.stage ==11">AND o.order_stage=2</when><!--审核-->
                            <when test="inDto.stage ==12">AND o.order_stage=4</when><!--作废-->
                        </choose>
                    </when>
                </choose>
            </if>
            <!-- 线索id-->
            <if test="inDto.cluesIdList !=null and inDto.cluesIdList.size>0">
                AND o.clues_id IN
                <foreach collection="inDto.cluesIdList" item="cluesId" open="(" separator="," close=")">
                    #{cluesId}
                </foreach>
            </if>
            <if test="inDto.companyIdList !=null and inDto.companyIdList.size() > 0">
                AND o.company_id IN
                <foreach collection="inDto.companyIdList" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
            </if>
            <if test="inDto.storeIdNestList != null and inDto.storeIdNestList.size() > 0">
                and (
                <foreach collection="inDto.storeIdNestList" item="list" separator="or">
                    o.store_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </foreach>
                )
            </if>
            <if test="inDto.salesmanId != null">
                AND o.input_id = #{inDto.salesmanId}
            </if>
            <if test="inDto.orderType !=null"><!-- 订单类型 1：经销/零售；2：定金 -->
                AND o.order_type=#{inDto.orderType}
            </if>
            <if test="inDto.orderTypeList !=null and inDto.orderTypeList.size>0">
                AND o.order_type IN
                <foreach collection="inDto.orderTypeList" item="orderType2" open="(" separator="," close=")">
                    #{orderType2}
                </foreach>
            </if>
            <if test="inDto.includeOrderTypeList != null and inDto.includeOrderTypeList.size>0">
                AND o.order_type IN
                <foreach collection="inDto.includeOrderTypeList" item="includeOrderType" open="(" separator="," close=")">
                    #{includeOrderType}
                </foreach>
            </if>
            <if test="inDto.excludeOrderStageList != null and inDto.excludeOrderStageList.size>0">
                AND o.order_stage not in
                <foreach collection="inDto.excludeOrderStageList" item="excludeOrderStage" open="(" separator="," close=")">
                    #{excludeOrderStage}
                </foreach>
            </if>
            <if test="inDto.inputId !=null"><!-- 录单人 -->
                AND o.input_id = #{inDto.inputId}
            </if>

            <if test="inDto.orderId !=null"><!--订单号-->
                AND o.id LIKE '${inDto.orderId}%'
            </if>
            <if test="inDto.idList !=null and inDto.idList.size() > 0"><!--订单号-->
                AND o.id in
                <foreach collection="inDto.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.drpOrderId !=null and inDto.drpOrderId !=''"><!--drp订单号-->
                AND o.drp_orderId LIKE '${inDto.drpOrderId}%'
            </if>
            <if test="inDto.contactName !=null and inDto.contactName !=''"><!--收货人-->
                AND o.id  in (
                    select id from omscenter.declaration_info where is_deleted = 0
                    and AES_DECRYPT( UNHEX(o.contact_name), #{inDto.secretKey} ) like CONCAT('%',#{inDto.contactName},'%')
                )
            </if>
            <if test="inDto.contactMobile !=null and inDto.contactMobile !=''"><!--手机号-->
                AND o.id  in (
                    select id from omscenter.declaration_info where is_deleted = 0
                    and AES_DECRYPT( UNHEX(o.contact_mobile), #{inDto.secretKey} ) like CONCAT('%',#{inDto.contactMobile},'%')
                )
            </if>
            <if test="inDto.contactMobileBak !=null and inDto.contactMobileBak !=''"><!--联系方式-->
                AND o.id  in (
                    select id from omscenter.declaration_info where is_deleted = 0
                    and AES_DECRYPT( UNHEX(o.contact_mobile_bak), #{inDto.secretKey} ) like CONCAT('%',#{inDto.contactMobileBak},'%')
                )
            </if>
            <if test="inDto.installId !=null and inDto.installId !=''"><!--安装单号-->
                AND o.install_id LIKE '${inDto.installId}%'
            </if>
            <if test="inDto.orderPkId !=null">
                AND o.id=#{inDto.orderPkId}
            </if>
            <if test="(inDto.goodsName!=null and inDto.goodsName!='') or inDto.isGift != null">
                AND exists (
                    select 1 from omscenter.goods_declaration gd where gd.is_deleted = 0 and gd.order_id = o.id
                    <if test="inDto.goodsName!=null and inDto.goodsName!=''">
                        AND gd.goods_name LIKE '%${inDto.goodsName}%'
                    </if>
                    <if test="inDto.isGift != null">
                        AND gd.is_gift = #{inDto.isGift}
                    </if>
                )
            </if>
            <if test="inDto.stageList != null and inDto.stageList.size() > 0">
                AND o.stage in
                <foreach collection="inDto.stageList" item="stage" separator="," open="(" close=")">
                    #{stage}
                </foreach>
            </if>
            <if test="inDto.appPreAuditList != null and inDto.appPreAuditList.size() > 0">
                <choose>
                    <when test="inDto.appPreAuditList.contains(3)"><!-- 预审核拒绝 -->
                        AND (
                            o.app_pre_audit in
                            <foreach collection="inDto.appPreAuditList" item="appPreAudit" separator="," open="(" close=")">
                                #{appPreAudit}
                            </foreach>
                            or o.stage = 3
                        )
                    </when>
                    <otherwise>
                        AND  o.app_pre_audit in
                        <foreach collection="inDto.appPreAuditList" item="appPreAudit" separator="," open="(" close=")">
                            #{appPreAudit}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.drpStageList != null and inDto.drpStageList.size() > 0"><!-- 同步drp状态 -->
                <choose>
                    <when test="inDto.drpStageList.contains(4)"><!-- 不同步 -->
                        AND (
                            o.is_to_drp = 1 or o.drp_stage in
                            <foreach collection="inDto.drpStageList" item="drpStage" separator="," open="(" close=")">
                                #{drpStage}
                            </foreach>
                        )
                    </when>
                    <otherwise>
                        AND o.is_to_drp != 1
                        AND o.drp_stage in
                        <foreach collection="inDto.drpStageList" item="drpStage" separator="," open="(" close=")">
                            #{drpStage}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="inDto.orderStageList != null and inDto.orderStageList.size() > 0">
                AND o.order_stage in
                <foreach collection="inDto.orderStageList" item="orderStage" separator="," open="(" close=")">
                    #{orderStage}
                </foreach>
            </if>
            <if test="inDto.dcsOutStatusList != null and inDto.dcsOutStatusList.size() > 0">
                AND o.dcs_out_status in
                <foreach collection="inDto.dcsOutStatusList" item="dcsOutStatus" separator="," open="(" close=")">
                    #{dcsOutStatus}
                </foreach>
            </if>
            <if test="inDto.stransferStageList != null and inDto.stransferStageList.size() > 0">
                AND order_type = 501
                AND o.stransfer_stage in
                <foreach collection="inDto.stransferStageList" item="stransferStage" separator="," open="(" close=")">
                    #{stransferStage}
                </foreach>
            </if>
            <if test="inDto.deliveryDateStart != null and inDto.deliveryDateStart != ''">
                AND o.delivery_date &gt;= #{inDto.deliveryDateStart}
            </if>
            <if test="inDto.deliveryDateEnd != null and inDto.deliveryDateEnd != ''">
                AND o.delivery_date &lt;= #{inDto.deliveryDateEnd}
            </if>
            <if test="inDto.storeOrgIdList != null and inDto.storeOrgIdList.size() > 0">
                AND o.store_id in
                <foreach collection="inDto.storeOrgIdList" item="storeOrgId" separator="," open="(" close=")">
                    #{storeOrgId}
                </foreach>
            </if>
            <if test="inDto.storeTypeCodeList != null and inDto.storeTypeCodeList.size() > 0">
                AND o.store_type_code in
                <foreach collection="inDto.storeTypeCodeList" item="storeTypeCode" separator="," open="(" close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="inDto.orderChannelList != null and inDto.orderChannelList.size() > 0">
                AND o.order_channel in
                <foreach collection="inDto.orderChannelList" item="orderChannel" separator="," open="(" close=")">
                    #{orderChannel}
                </foreach>
            </if>
            <if test="(inDto.goodsIdList != null and inDto.goodsIdList.size() > 0)
                or (inDto.goodsCategoryIdList != null and inDto.goodsCategoryIdList.size() > 0)">
                AND o.id in (
                    select order_id from omscenter.goods_declaration gd
                    where gd.is_deleted = 0
                    <if test="inDto.goodsIdList != null and inDto.goodsIdList.size() > 0">
                        and gd.goods_id in
                        <foreach collection="inDto.goodsIdList" item="goodsId" separator="," open="(" close=")">
                            #{goodsId}
                        </foreach>
                    </if>
                    <if test="inDto.goodsCategoryIdList != null and inDto.goodsCategoryIdList.size() > 0">
                        and gd.goods_category_id in
                        <foreach collection="inDto.goodsCategoryIdList" item="goodsCategoryId" separator="," open="(" close=")">
                            #{goodsCategoryId}
                        </foreach>
                    </if>
                )
            </if>
            <if test="inDto.goodsAmountStart != null">
                AND o.goods_amount &gt;= #{inDto.goodsAmountStart}
            </if>
            <if test="inDto.goodsAmountEnd != null">
                AND o.goods_amount &lt;= #{inDto.goodsAmountEnd}
            </if>
            <if test="inDto.goodsNumStart != null">
                AND o.non_gift_num &gt;= #{inDto.goodsNumStart}
            </if>
            <if test="inDto.goodsNumEnd != null">
                AND o.non_gift_num &lt;= #{inDto.goodsNumEnd}
            </if>
            <if test="inDto.dealReceiverIdList != null and inDto.dealReceiverIdList.size() > 0">
                AND o.deal_receiver_id in
                <foreach collection="inDto.dealReceiverIdList" item="dealReceiverId" separator="," open="(" close=")">
                    #{dealReceiverId}
                </foreach>
            </if>
            <if test="inDto.cluesSalesmanIdList != null and inDto.cluesSalesmanIdList.size() > 0">
                AND o.clues_salesman_id in
                <foreach collection="inDto.cluesSalesmanIdList" item="cluesSalesmanId" separator="," open="(" close=")">
                    #{cluesSalesmanId}
                </foreach>
            </if>
            <if test="inDto.contentTemplateIdList !=null and inDto.contentTemplateIdList.size() > 0">
                AND o.content_template_id in
                <foreach collection="inDto.contentTemplateIdList" item="contentTemplateId" separator="," open="(" close=")">
                    #{contentTemplateId}
                </foreach>
            </if>
            <if test="inDto.stageList !=null and inDto.stageList.size() > 0">
                AND o.stage in
                <foreach collection="inDto.stageList" item="stage" separator="," open="(" close=")">
                    #{stage}
                </foreach>
            </if>
            <if test="inDto.fullChargebackFlagList !=null and inDto.fullChargebackFlagList.size() > 0">
                AND o.full_chargeback_flag in
                <foreach collection="inDto.fullChargebackFlagList" item="fullChargebackFlag" separator="," open="(" close=")">
                    #{fullChargebackFlag}
                </foreach>
            </if>
            <if test="inDto.cluesSalesmanId != null">
                AND o.clues_salesman_id = #{inDto.cluesSalesmanId}
            </if>
        </where>
    </sql>

    <!--

    <if test="inDto.orderId == null and (inDto.idList == null or inDto.idList.size() == 0)">
            <choose>
                <when test="inDto.companyIdList !=null and inDto.companyIdList.size() > 0">
                    FORCE index(idx_company_id,index_clues_id)
                </when>
                <when test="inDto.cluesSalesmanId !=null ">

                </when>
                <otherwise>
                    FORCE index(idx_storeId_orderType,idx_input_id,index_clues_id)
                </otherwise>
            </choose>
        </if>

    -->

    <!-- 根据线索id查询订单列表 -->
    <select id="findDecOrderByCluesId" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">
        SELECT o.id,o.order_create_time orderCreatetime,o.clues_id cluesId,o.goods_num goodsNum,
        CASE o.stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,
        o.stage,
        o.buy_reason buyReason,o.business_type businessType,o.settlement_ticket settlementTicket,
        o.install_id installId,o.drp_orderId drpOrderId,o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.designer_code designerCode,o.designer_name designerName,o.designer_phone designerPhone,
        o.company_id companyId,o.store_id storeId,
        o.adviser_name adviserName,o.order_type orderType,o.stransfer_stage stransferStage,
        o.to_drp_stage toDrpStage,o.retry_drp_num retryDrpNum,
        o.parent_id parentId,o.order_stage orderStage,o.is_to_drp isToDrp,o.drp_stage drpStage,
        CASE o.order_stage
        WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        CASE
        WHEN o.is_to_drp=1 OR o.order_type=501 THEN '不同步'
        WHEN o.is_to_drp !=1 AND o.drp_stage=1 THEN '未处理'
        WHEN o.is_to_drp !=1 AND o.drp_stage=2 THEN '同步成功'
        WHEN o.is_to_drp !=1 AND o.drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.to_drp_msg toDrpMsg,
        o.goods_amount goodsAmount,
        o.contact_name contactName,o.contact_mobile contactMobile ,o.earnest_amount earnestAmount,
        o.is_auto_audit isAutoAudit,o.app_pre_audit appPreAudit,
        o.dcs_out_status dcsOutStatus
        FROM omscenter.declaration_info o

        <!--<if test="inDto.keyWord !=null and inDto.keyWord !=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
            AND (o.id LIKE '%${inDto.keyWord}%'
            <if test="!isNumeric">
                OR g.goods_name LIKE '%${inDto.keyWord}%'
            </if>
            OR o.contact_name LIKE '%${inDto.keyWord}%'
            OR o.contact_mobile LIKE '%${inDto.keyWord}%'
            OR o.contact_mobile_bak LIKE '%${inDto.keyWord}%'
            OR o.install_id LIKE '%${inDto.keyWord}%'
            OR o.drp_orderId LIKE '%${inDto.keyWord}%')
        </if>

        <if test="inDto.goodsName!=null and inDto.goodsName!=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id AND g.goods_name LIKE '%${inDto.goodsName}%'
        </if>-->
        <include refid="findDecOrderByCluesIdWHERE"/>
        ORDER BY
        <choose>
            <when test="inDto.orderByField != null and inDto.orderByField == 1">
                o.order_create_time
            </when>
            <when test="inDto.orderByField != null and inDto.orderByField == 2">
                o.modified_date
            </when>
            <when test="inDto.orderByField != null and inDto.orderByField == 3">
                o.input_id
            </when>
            <when test="inDto.orderByField != null and inDto.orderByField == 4">
                o.order_type
            </when>
            <otherwise>
                o.order_create_time
            </otherwise>
        </choose>
        <choose>
            <when test="inDto.orderSort != null and inDto.orderSort == 1">
                 ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
        ,o.order_create_time desc
        <if test="pg != null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>


    <!--
     <if test="inDto.keyWord !=null and inDto.keyWord !=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
            AND (o.id LIKE '%${inDto.keyWord}%'
            <if test="!isNumeric">
                OR g.goods_name LIKE '%${inDto.keyWord}%'
            </if>
            OR o.contact_name = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile_bak = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.install_id LIKE '%${inDto.keyWord}%'
            OR o.drp_orderId LIKE '%${inDto.keyWord}%')
        </if>


        <if test="inDto.goodsName!=null and inDto.goodsName!=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id AND g.goods_name LIKE '%${inDto.goodsName}%'
        </if>

            <if test="inDto.orderId == null and (inDto.idList == null or inDto.idList.size() == 0)">
                <choose>
                    <when test="inDto.companyIdList !=null and inDto.companyIdList.size() > 0">
                        FORCE index(idx_company_id,index_clues_id)
                    </when>
                    <when test="inDto.cluesSalesmanId != null ">

                    </when>
                    <otherwise>
                        FORCE index(idx_storeId_orderType,idx_input_id,index_clues_id)
                    </otherwise>
                </choose>
            </if>


    -->

    <!-- 根据线索id查询订单列表count查询 -->
    <select id="selectDecOrderCount" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">
        SELECT count(oo.id) orderNum,
        SUM(CASE WHEN oo.stage=1 THEN 1 ELSE 0 END ) stageNum,
        SUM(CASE oo.stransfer_stage WHEN 2 THEN 1 ELSE 0 END ) transferNum
        FROM (
            SELECT o.id,stage,o.stransfer_stage
            FROM omscenter.declaration_info o

            <include refid="findDecOrderByCluesIdWHERE"/>
        ) oo
    </select>
    <!-- 根据线索集合查询订单,模糊匹配>支持收货人，手机号，总金额，产品型号的搜索-->
    <select id="selectDecOrderByCluesIdsCount" resultType="java.lang.Integer">

        SELECT count(DISTINCT o.id)
        FROM omscenter.declaration_info o
        <if test="inDto.keyWord !=null and inDto.keyWord !=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
            and ( o.contact_name = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile_bak = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            or o.goods_amount LIKE '%${inDto.keyWord}%'
            or g.goods_code LIKE '%${inDto.keyWord}%')
        </if>
        <where>
            and o.is_deleted = 0 and o.order_stage != 4 and stage != 4
            <!-- 线索id 和 手机号-->
            <if test="( inDto.cluesIdList !=null and inDto.cluesIdList.size>0 ) or (inDto.userPhone != null and inDto.userPhone != '')">
                AND
                (
                <if test="inDto.cluesIdList !=null and inDto.cluesIdList.size>0">
                    o.clues_id IN
                    <foreach collection="inDto.cluesIdList" item="cluesId" open="(" separator="," close=")">
                        #{cluesId}
                    </foreach>
                </if>
                <if test="inDto.cluesIdList !=null and inDto.cluesIdList.size>0 and inDto.userPhone != null and inDto.userPhone != ''">
                    or
                </if>
                <if test="inDto.userPhone != null and inDto.userPhone != ''">
                    o.designer_phone = #{inDto.userPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    or o.decorate_company_phone = #{inDto.userPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                </if>
                )
            </if>

            <if test="inDto.orderCreatetimeStart !=null and inDto.orderCreatetimeStart !=''"><!-- 报单时间-起始 -->
                AND o.order_create_time &gt;= concat(DATE_FORMAT(#{inDto.orderCreatetimeStart},'%Y-%m-%d'),' 00:00:00')
            </if>
            <if test="inDto.orderCreatetimeEnd !=null and inDto.orderCreatetimeEnd !=''"><!-- 报单时间-结束 -->
                AND o.order_create_time &lt;=concat(DATE_FORMAT(#{inDto.orderCreatetimeEnd},'%Y-%m-%d'),' 23:59:59')
            </if>
            <if test="inDto.stage !=null">
                <choose>
                    <when test="inDto.stage ==1 or inDto.stage ==2 or inDto.stage ==3"><!--1：待审核；2：审核通过；3：审核拒绝-->
                        AND o.stage =#{inDto.stage}<!-- 审核状态 -->
                    </when>
                    <when test="inDto.stage ==4 or inDto.stage ==5 or inDto.stage ==6"><!--4：未转单；5：已转单；6：已作废-->
                        AND o.order_type IN(501,303)
                        <choose>
                            <when test="inDto.stage ==4">AND o.stransfer_stage =1</when><!--未转单-->
                            <when test="inDto.stage ==5">AND o.stransfer_stage =2</when><!--已转单-->
                            <when test="inDto.stage ==6">AND o.stransfer_stage =3</when><!--已作废-->
                        </choose>
                    </when>
                    <when test="inDto.stage ==7 or inDto.stage ==8 or inDto.stage ==9"><!--7：不同步；8：同步成功；9：同步失败-->
                        <choose>
                            <when test="inDto.stage ==7">AND ( is_to_drp=1 )</when><!--不同步-->
                            <when test="inDto.stage ==8">AND ( o.drp_stage =2 AND is_to_drp !=1 )
                            </when><!--同步成功-->
                            <when test="inDto.stage ==9">AND ( o.drp_stage =3 AND is_to_drp !=1 )
                            </when><!--同步失败-->
                        </choose>
                    </when>
                </choose>
            </if>

        </where>
    </select>
    <!-- 根据线索集合查询订单,模糊匹配>支持收货人，手机号，总金额，产品型号的搜索-->
    <select id="selectDecOrderByCluesIdsList"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">

        SELECT DISTINCT o.id,o.order_create_time orderCreatetime,o.clues_id cluesId,o.goods_num goodsNum,
        CASE o.stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,o.stage,
        o.buy_reason buyReason,o.business_type businessType,o.settlement_ticket settlementTicket,o.install_id
        installId,o.drp_orderId drpOrderId,o.decorate_company_code decorateCompanyCode,o.decorate_company_name
        decorateCompanyName,
        o.designer_code designerCode,o.designer_name designerName,o.designer_phone designerPhone,o.company_id
        companyId,o.store_id storeId,
        o.adviser_name adviserName,o.order_type,o.stransfer_stage stransferStage,o.to_drp_stage
        toDrpStage,o.retry_drp_num retryDrpNum,
        o.parent_id parentId,o.order_stage orderStage,o.is_to_drp isToDrp,o.drp_stage drpStage,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        CASE
        WHEN o.is_to_drp=1 OR o.order_type=501 THEN '不同步'
        WHEN o.is_to_drp !=1 AND o.drp_stage=1 THEN '未处理'
        WHEN o.is_to_drp !=1 AND o.drp_stage=2 THEN '同步成功'
        WHEN o.is_to_drp !=1 AND o.drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.to_drp_msg toDrpMsg,
        o.goods_amount goodsAmount,
        o.contact_name contactName,o.contact_mobile contactMobile ,o.earnest_amount earnestAmount,
        o.distributor_code distributorCode,o.distributor_name distributorName
        FROM omscenter.declaration_info o
        <if test="inDto.keyWord !=null and inDto.keyWord !=''">
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
            and ( o.contact_name = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            OR o.contact_mobile_bak = #{inDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            or o.goods_amount LIKE '%${inDto.keyWord}%'
            or g.goods_code LIKE '%${inDto.keyWord}%')
        </if>
        <where>
            and o.is_deleted = 0 and o.order_stage != 4 and stage != 4
            <!-- 线索id 和 手机号-->
            <if test="( inDto.cluesIdList !=null and inDto.cluesIdList.size>0 ) or (inDto.userPhone != null and inDto.userPhone != '')">
                AND
                (
                    <if test="inDto.cluesIdList !=null and inDto.cluesIdList.size>0">
                        o.clues_id IN
                        <foreach collection="inDto.cluesIdList" item="cluesId" open="(" separator="," close=")">
                            #{cluesId}
                        </foreach>
                    </if>
                    <if test="inDto.cluesIdList !=null and inDto.cluesIdList.size>0 and inDto.userPhone != null and inDto.userPhone != ''">
                        or
                    </if>
                    <if test="inDto.userPhone != null and inDto.userPhone != ''">
                        o.designer_phone = #{inDto.userPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                        or o.decorate_company_phone = #{inDto.userPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    </if>
                )
            </if>
            <if test="inDto.orderCreatetimeStart !=null and inDto.orderCreatetimeStart !=''"><!-- 报单时间-起始 -->
                AND o.order_create_time &gt;= concat(DATE_FORMAT(#{inDto.orderCreatetimeStart},'%Y-%m-%d'),' 00:00:00')
            </if>
            <if test="inDto.orderCreatetimeEnd !=null and inDto.orderCreatetimeEnd !=''"><!-- 报单时间-结束 -->
                AND o.order_create_time &lt;=concat(DATE_FORMAT(#{inDto.orderCreatetimeEnd},'%Y-%m-%d'),' 23:59:59')
            </if>
            <if test="inDto.stage !=null">
                <choose>
                    <when test="inDto.stage ==1 or inDto.stage ==2 or inDto.stage ==3"><!--1：待审核；2：审核通过；3：审核拒绝-->
                        AND o.stage =#{inDto.stage}<!-- 审核状态 -->
                    </when>
                    <when test="inDto.stage ==4 or inDto.stage ==5 or inDto.stage ==6"><!--4：未转单；5：已转单；6：已作废-->
                        AND o.order_type IN(501,303)
                        <choose>
                            <when test="inDto.stage ==4">AND o.stransfer_stage =1</when><!--未转单-->
                            <when test="inDto.stage ==5">AND o.stransfer_stage =2</when><!--已转单-->
                            <when test="inDto.stage ==6">AND o.stransfer_stage =3</when><!--已作废-->
                        </choose>
                    </when>
                    <when test="inDto.stage ==7 or inDto.stage ==8 or inDto.stage ==9"><!--7：不同步；8：同步成功；9：同步失败-->
                        <choose>
                            <when test="inDto.stage ==7">AND ( is_to_drp=1 )</when><!--不同步-->
                            <when test="inDto.stage ==8">AND ( o.drp_stage =2 AND is_to_drp !=1 )
                            </when><!--同步成功-->
                            <when test="inDto.stage ==9">AND ( o.drp_stage =3 AND is_to_drp !=1 )
                            </when><!--同步失败-->
                        </choose>
                    </when>
                </choose>
            </if>
        </where>
        order by o.order_create_time desc
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>
    <!-- 保存图片 -->
    <insert id="addPic">
        INSERT INTO omscenter.goods_declaration(order_id, goods_category_id, goods_id, goods_name, goods_price,
                                                goods_num, goods_total_price, goods_image, goods_code)
        VALUES (#{orderId}, #{goodsCategoryId}, #{goodsId}, #{goodsName}, #{goodsPrice}, #{goodsNum},
                #{goodsTotalPrice}, #{goodsImage}, #{goodsCode})
    </insert>

    <!-- 业务员排行榜-成交金额 -->
    <select id="salesmanRankingAmount" resultType="java.util.Map">
        SELECT manager_id        chargeUserId,
               manager_name      chargeUserName,
               SUM(goods_amount) goodsNum,
               store_id          storeId,
               store_name        storeName
        FROM omscenter.declaration_info
        WHERE DATE_FORMAT(order_create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(order_create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY store_id, manager_id
        ORDER BY goodsNum DESC
    </select>

    <!-- 业务员排行榜-商品销售 -->
    <select id="salesmanRankingGoods" resultType="java.util.Map">
        SELECT a.goods_id          goodId
             , a.goods_name        goodName
             , manager_id          chargeUserId
             , manager_name        chargeUserName
             , b.store_id          storeId
             , SUM(a.goods_num)    goodsNum
             , a.goods_category_id goodsCategoryId
             , store_name          storeName
        FROM omscenter.goods_declaration a
                 left join omscenter.declaration_info b on a.order_id = b.id
        WHERE DATE_FORMAT(a.created_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(a.created_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY b.store_id, b.manager_id, a.goods_id
        ORDER BY goodsNum DESC
    </select>
    <!-- 门店排行榜 -品销售  -->
    <select id="storeRankingGoods" resultType="java.util.Map">
        SELECT a.goods_id          goodId
             , a.goods_name        goodName
             , manager_id          chargeUserId
             , manager_name        chargeUserName
             , b.store_id          storeId
             , SUM(a.goods_num)    goodsNum
             , a.goods_category_id goodsCategoryId
             , store_name          storeName
        FROM omscenter.goods_declaration a
                 left join omscenter.declaration_info b on a.order_id = b.id
        WHERE DATE_FORMAT(a.created_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(a.created_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY b.store_id, a.goods_id
        ORDER BY goodsNum DESC
    </select>

    <select id="storeRankingAmount" resultType="java.util.Map">
        SELECT store_id storeId, SUM(goods_amount) goodsNum, store_name storeName
        FROM omscenter.declaration_info
        WHERE DATE_FORMAT(order_create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(order_create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY store_id
        ORDER BY goodsNum DESC
    </select>

    <!-- 订单审核 -->
    <update id="updateStage">
        <if test="drpList !=null and drpList.size>0">
            <foreach collection="drpList" item="order" separator=";" close=";">
                UPDATE omscenter.declaration_info

                <set>
                    modified_date=now(),
                    version =  version + 1,
                    modified_by=#{order.modifiedBy},
                    audit_date=#{order.auditDate}
                    <if test="order.stage !=null">,stage=#{order.stage}</if>
                    <if test="order.areaCode != null and order.areaCode != ''">
                        ,area_code=#{order.areaCode}
                    </if>
                    <if test="order.areaName != null and order.areaName != ''">
                        ,area_name=#{order.areaName}
                    </if>
                    <if test="order.isToDrp !=null ">,is_to_drp=#{order.isToDrp}</if>
                    <if test="order.toDrpStage !=null ">,to_drp_stage=#{order.toDrpStage}</if>
                    ,report_date = #{order.reportDate}
                    <if test="order.isTransfer !=null">
                        ,is_transfer=#{order.isTransfer}
                    </if>
                </set>
                WHERE stage=1 AND id =#{order.orderId}
            </foreach>
        </if>
    </update>

    <!-- 新增操作日志 -->
    <insert id="addLog" parameterType="orderLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO omscenter.declaration_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            created_by,created_date,modified_by,modified_date,
            <if test="orderId != null"><!-- 订单id -->
                order_id,
            </if>
            <if test="operatorId != null"><!-- 操作人id -->
                operator_id,
            </if>
            <if test="operatorName != null"><!-- 操作人名称 -->
                operator_name,
            </if>
            <if test="operatorType != null"><!-- 操作类型 -->
                operator_type,
            </if>
            <if test="msg != null"><!-- 描述内容 -->
                msg
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{createdBy},now(),#{modifiedBy},now(),
            <if test="orderId != null"><!-- 订单id -->
                #{orderId},
            </if>
            <if test="operatorId != null"><!-- 操作人id -->
                #{operatorId},
            </if>
            <if test="operatorName != null"><!-- 操作人名称 -->
                #{operatorName},
            </if>
            <if test="operatorType != null"><!-- 操作类型 -->
                #{operatorType},
            </if>
            <if test="msg != null"><!-- 描述内容 -->
                #{msg}
            </if>
        </trim>


    </insert>


    <!-- 多个订单批量新增操作日志 -->
    <insert id="batchAddLog" parameterType="com.fotile.omscenter.decOrder.pojo.dto.OrderLog">
        INSERT INTO omscenter.declaration_log (created_by,created_date,modified_by,modified_date,order_id,operator_id,operator_name,operator_type,msg)
        values
        <foreach collection="logList" item="log" separator=",">
            (#{log.createdBy},now(),#{log.modifiedBy},now(),#{log.orderId},#{log.operatorId},#{log.operatorName},#{log.operatorType},#{log.msg})
        </foreach>
    </insert>

    <!-- 订单操作日志列表 -->
    <select id="orderLogList" resultType="com.fotile.omscenter.decOrder.pojo.dto.OrderLog">
        SELECT id,
               operator_id   operatorId,
               operator_name operatorName,
               operator_type operatorType,
               created_date  createdDate,
               msg
        FROM omscenter.declaration_log
        WHERE is_deleted = 0 and order_id = #{orderId}
        ORDER BY created_date DESC, id DESC
        limit #{pageInfo.offset},#{pageInfo.size}
    </select>

    <!-- 订单操作日志列表count查询 -->
    <select id="selectOrderLogCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM omscenter.declaration_log
        WHERE is_deleted = 0 and order_id = #{orderId}
        ORDER BY created_date DESC
    </select>

    <!-- 同步至drp -->
    <!--
            CONCAT(
            IFNULL(AES_DECRYPT(UNHEX(IFNULL(o.contact_name,'')), #{secretKey}),''),'~',
            IFNULL(AES_DECRYPT(UNHEX(IFNULL(o.contact_mobile,'')), #{secretKey}),''),'~',
            IFNULL(AES_DECRYPT(UNHEX(IFNULL(o.contact_mobile_bak,'')), #{secretKey}),''),'~',
            IFNULL(o.village_name,''),'',
            IFNULL(AES_DECRYPT(UNHEX(IFNULL(o.delivery_address,'')), #{secretKey}),'')
        ) sendinfo,
    -->
    <select id="syncToDRP" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        SELECT o.id,o.store_id storeId,o.install_id installId,
               o.order_date orderCreatetime,o.order_create_time orderTime,o.order_note orderNote,
               o.provice_id proviceId,o.provice_name proviceName,o.city_id cityId,
        o.city_name cityName,o.county_id countyId,o.county_name countyName,
        o.area_code areaCode,
        o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.contact_mobile_bak contactMobileBak,
        o.delivery_address deliveryAddress,
        o.village_name villageName,
        o.settlement_ticket settlementTicket,
        CASE o.buy_reason WHEN '1' THEN '毛坯新装' WHEN '01' THEN '毛坯新装'
        WHEN '2' THEN '厨电换新' WHEN '02' THEN '厨电换新'
        WHEN '3' THEN '全屋重装' WHEN '03' THEN '全屋重装'
        WHEN '4' THEN '厨房局装' WHEN '04' THEN '厨房局装'
        WHEN '5' THEN '厨电添置' WHEN '05' THEN '厨电添置'
        WHEN '6' THEN '原因未知' WHEN '06' THEN '原因未知' END buyReason,
        o.delivery_date deliveryDate,o.to_drp_stage toDrpStage,o.input_name inputName,
        o.cash_amount cashAmount,o.transfer_amount transferAmount,o.card_amount cardAmount,o.company_id
        companyId,o.clues_id cluesId,
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,o.is_to_drp isToDrp,DATE_FORMAT(o.created_date,'%Y-%m-%d %H:%i:%s') createdDate,
        o.clues_source_name cluesSourceName,o.activity_remark activityRemark,o.order_channel orderChannel,
        o.order_type orderType,o.oa_no OANo,o.sec_return_reason secReturnReason,o.sec_return_reason_name
        secReturnReasonName,o.original_orderId originalOrderId,
        o.is_check_price isCheckPrice,o.decorate_company_type decorateCompanyType,o.decorate_category
        decorateCompanyCategory,o.decorate_category_name decorateCompanyCategoryName,
        o.sample_type sampleType,o.is_transfer isTransfer,FIND_IN_SET(o.order_type,o.order_type_check_install)
        allowNoRepeat,o.is_from_app isFromApp,o.install_check installCheck,o.is_auto_audit isAutoAudit,
        parent_id parentId,earnest_amount earnestAmount,receipt_url receiptUrl,o.has_ambry hasAmbry,
        drp_orderId drpOrderId,building, unit, house_number houseNumber,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode,
        o.created_by createdBy,o.region_proxy_flag regionProxyFlag,
        o.real_store_id realStoreId,o.trade_in_flag tradeInFlag,
        extend.old_compensate_flag oldCompensateFlag
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        <choose>
            <when test="orderIdList !=null and orderIdList.size>0"><!-- 审核通过，同步至drp -->
                WHERE is_draft = 0 and (app_pre_audit in(0,2) AND o.stage=2 AND o.is_to_drp !=1)
                AND o.id IN
                <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </when>
            <otherwise><!-- 定时同步至drp -->
                WHERE is_draft = 0 and app_pre_audit in (0,2) AND o.stage=2 AND o.is_to_drp !=1 AND (o.drp_stage=1 OR (o.drp_stage=3 AND
                o.retry_drp_num &lt;8))
            </otherwise>
        </choose>
        <if test="systemSwitch">
            and o.is_to_drp != 4
        </if>
        and o.order_create_time &lt;= DATE_ADD(NOW(), INTERVAL -10 SECOND)
        and not EXISTS (
            SELECT 1 from order_audit oa where oa.is_deleted = 0 and o.id = oa.order_id and oa.audit_status in (0,1,2)
        )
        and (
            not (
                o.order_type in (101,401,501) and extend.is_cashier_enabled = 1
                and (o.is_monthly_settlement = 0 or o.is_monthly_settlement is null)
                and o.cashier_charge_mapping = 1 and o.remain_pay_amount > 0
            )
            or (extend.old_compensate_flag = 1 and o.pay_status in (1,2))
        )
        and not (
            extend.old_compensate_flag = 1 and extend.old_compensate_flag is not null
            and (o.pay_status = 0 or o.pay_status is null)
        )
    </select>

    <!-- 同步至drp 根据订单id查询商品列表 -->
    <select id="syncToDRPFindGoodsByOrderId" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsEntity">
        SELECT go.goods_id      id,
               go.goods_num     goodsNum,
               go.goods_price   goodsPrice,
               goods_note       goodsNote,
               go.id            goodsDeclarationId,
               go.order_id      orderId,
               go.goods_id      goodsId,
               go.sort,
               go.guid,
               go.location_type locationType,
               go.delivery_date deliveryDate,
               gs.card_id       cardId,
               go.source_guid   sourceGuid,
               gs.id            supplyId,
               go.goods_code   goodsCode,
               go.goods_category_id goodsCategoryId,
               go.pos_pay_amount  posPayAmount,
               go.trade_in_flag  tradeInFlag
        FROM omscenter.goods_declaration go
                 LEFT JOIN omscenter.goods_declaration_supple gs
                           ON gs.is_deleted = 0 AND go.id = gs.goods_declaration_id
        WHERE go.order_id = #{orderId}
          AND go.is_deleted = 0
        ORDER BY go.sort ASC
    </select>

    <!-- 更新同步drp状态 -->
    <update id="updateDrpStage">
        UPDATE omscenter.declaration_info
        <set>
            version =  version + 1,
            modified_date=now()
            <if test="modifiedBy !=null">,modified_by=#{modifiedBy}</if>
            <if test="drpStage !=null">,drp_stage=#{drpStage}</if>
            <choose>
                <when test="flage==0">
                    <if test="drpStage !=null and drpStage==3">,retry_drp_num=retry_drp_num+1</if>
                </when>
                <when test="flage==1">
                    <if test="drpStage !=null and drpStage==3">,retry_drp_num=0,stage=3</if>
                </when>
            </choose>
            <if test="drpOrderId !=null ">,drp_orderId=#{drpOrderId}</if>
            <if test="toDrpMsg !=null ">,to_drp_msg=#{toDrpMsg}</if>
            <if test="orderStage !=null">
                ,order_stage = CASE
                                WHEN order_stage is null or order_stage &lt; #{orderStage} THEN #{orderStage}
                                ELSE order_stage
                                END
            </if>
        </set>
        WHERE id =#{orderId} and drp_stage!=2;
        <include refid="updateSyncFlagSql" />
    </update>

    <!-- 更新同步drp状态 -->
    <update id="updateDmsStage">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now()
            <if test="modifiedBy != null">,modified_by=#{modifiedBy}</if>
            <if test="drpStage != null">,drp_stage=#{drpStage}</if>
            <if test="dmsOrderNo != null ">,dms_order_no=#{dmsOrderNo},sync_to_dms = 1</if>
            <if test="toDmsMsg != null ">,to_dms_msg=#{toDmsMsg}</if>
            <if test="dmsStatus != null ">
                ,dms_status=#{dmsStatus}
                <if test="drpStage != null and drpStage == 2">
                    ,order_stage = 1
                </if>
            </if>
        </set>
        WHERE id = #{orderId};
        <include refid="updateSyncFlagSql" />
    </update>

    <sql id="updateSyncFlagSql">
        <if test="drpStage != null and drpStage == 2">
            UPDATE omscenter.declaration_info_extend
            set sync_cem_flag = 1,
            sync_dcs_flag = 1
            where order_id = #{orderId};
        </if>
    </sql>


    <update id="batchUpdateDmsOrderNo">
        <foreach collection="callbackDtoList" item="item">
            UPDATE omscenter.declaration_info
            <set>
                modified_date = now(),
                dmsOrderNo = #{item.dmsOrderNo}
            </set>
            WHERE id =#{item.orderId}
            and sync_to_dms = 1
            and (drp_orderId is null or drp_orderId not like '${item.dmsOrderNo}%');
        </foreach>
    </update>


    <update id="forceUpdateDrpStage">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now()
            <if test="modifiedBy !=null">,modified_by=#{modifiedBy}</if>
            <if test="drpStage !=null">,drp_stage=#{drpStage}</if>
        </set>
        WHERE id = #{orderId}
    </update>

    <!-- 自动审核更新同步drp状态 -->
    <update id="autoAuditUpdateDrpStage">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now()
            <if test="modifiedBy !=null">,modified_by=#{modifiedBy}</if>
            <if test="drpStage !=null">,drp_stage=#{drpStage}</if>
            <choose>
                <when test="flage==0">
                    <if test="drpStage !=null and drpStage==3">,retry_drp_num=retry_drp_num+1</if>
                </when>
                <when test="flage==1">
                    <if test="drpStage !=null and drpStage==3">,retry_drp_num=0,stage=3</if>
                </when>
            </choose>

            <if test="drpOrderId !=null ">,drp_orderId=#{drpOrderId}</if>
            <if test="toDrpMsg !=null ">,to_drp_msg=#{toDrpMsg}</if>
            <if test="orderStage !=null">,order_stage=#{orderStage}</if>
        </set>
        WHERE id =#{orderId}
    </update>

    <sql id="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE">
        <where>
            o.is_deleted=0 AND o.order_type =101   <!--2020-11-23 带单数只统计经销零售订单-->
            <if test="inDto.designerCode !=null and inDto.designerCode !=''"><!-- 设计师编码 -->
                AND o.designer_code = #{inDto.designerCode}
            </if>
            <if test="inDto.decorateCompanyCode !=null and inDto.decorateCompanyCode !=''"><!-- 家装公司编码 -->
                AND o.decorate_company_code = #{inDto.decorateCompanyCode}
            </if>
            <if test="inDto.type != null and inDto.type ==2">
                AND o.stage != 3
            </if>
            <if test="inDto.type !=null and inDto.type==1 and (inDto.stage==null or inDto.stage==0)"><!-- 【云管理APP】3468-Android_家装管理,设计师详情页带单信息不需要显示审核拒绝的订单 -->
                <!--2020-08-031 【云管理APP】4093- APP_异业三工列表，列表和详情页带单数默认需要记录审核拒绝的订单 -->
                <!-- AND o.stage !=3 -->
            </if>
            <if test="inDto.stage !=null and inDto.stage !=0"><!-- 审核状态 -->
                AND o.stage =#{inDto.stage}
            </if>
        </where>
    </sql>

    <sql id="designerCodeList">
        (
        <foreach collection="inDto.designerCodeList" item="code" separator="union">
            select @designerCode:=#{code} as designerCode
        </foreach>
        ) as designer
    </sql>

    <sql id="decorateCodeList">
        (
        <foreach collection="inDto.decorateCompanyCodeList" item="code" separator="union">
            select @decorateCode:=#{code} as decorateCode
        </foreach>
        ) as decorate
    </sql>

    <sql id="decOrDesiList">
        (
        <foreach collection="inDto.decOrDesiList" item="code" separator="union">
            select @decorateCompanyCode:=#{code.decorateCompanyCode} as decorateCompanyCode,
            @designerCode:=#{code.designerCode} as designerCode
        </foreach>
        ) as decOrDes
    </sql>
    <!-- 根据设计师编码或家装公司编码查询订单列表 -->
    <select id="findDecOrderByDesignerCodeOrDecorateCompanyCode"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">
        SELECT DISTINCT
        oo.id,oo.orderCreatetime,oo.cluesId,oo.goodsAmount,oo.goodsNum,oo.stageName,oo.stage,oo.buyReason,
        oo.businessType,oo.settlementTicket,oo.installId,oo.drpOrderId,oo.decorateCompanyCode,oo.decorateCompanyName,oo.designerCode,
        oo.designerName,oo.designerPhone,oo.createdDate
        FROM(
        SELECT o.id,o.order_create_time orderCreatetime,o.clues_id cluesId,o.goods_amount goodsAmount,o.goods_num
        goodsNum,
        CASE stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,stage,
        o.buy_reason buyReason,o.business_type businessType,o.settlement_ticket settlementTicket,o.install_id
        installId,o.drp_orderId drpOrderId,
        o.decorate_company_code decorateCompanyCode,o.decorate_company_name decorateCompanyName,o.designer_code
        designerCode,
        o.designer_name designerName,o.designer_phone designerPhone,o.created_date createdDate
        FROM omscenter.declaration_info o
        <if test="inDto.designerCodeList !=null and inDto.designerCodeList.size>0">
            inner join
            <include refid="designerCodeList"/>
            on designer.designerCode=o.designer_code
        </if>
        <if test="inDto.decorateCompanyCodeList !=null and inDto.decorateCompanyCodeList.size>0">
            inner join
            <include refid="decorateCodeList"/>
            on decorate.decorateCode=o.decorate_company_code
        </if>
        <if test="inDto.decorateCompanyType==1 and inDto.decOrDesiList !=null and inDto.decOrDesiList.size>0">
            inner join
            <include refid="decOrDesiList"/>
            on
            (decOrDes.decorateCompanyCode=o.decorate_company_code
            OR decOrDes.designerCode=o.designer_code)
        </if>
        <include refid="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE"/>
        ) oo
        <choose>
            <when test="inDto.orderSort==1">
                ORDER BY oo.createdDate ASC
            </when>
            <otherwise>
                ORDER BY oo.createdDate DESC
            </otherwise>
        </choose>
        <if test="pg !=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!-- 根据设计师编码或家装公司编码查询订单列表count查询 -->
    <select id="selectDecOrderByDesignerCodeOrDecorateCompanyCodeCount" resultType="totalOutDto">
        <choose>
            <when test="inDto.decorateCompanyType==1 and inDto.decOrDesiList !=null and inDto.decOrDesiList.size>0"><!-- 家装 -->
                SELECT COUNT(DISTINCT oo.id) total,SUM(CASE oo.stage WHEN 1 THEN 1 ELSE 0 END ) noAudited,SUM(CASE
                oo.stage WHEN 2 THEN 1 ELSE 0 END ) audited
                FROM (
                SELECT o.id,o.stage
                FROM omscenter.declaration_info o
                <if test="inDto.designerCodeList !=null and inDto.designerCodeList.size>0">
                    inner join
                    <include refid="designerCodeList"/>
                    on designer.designerCode=o.designer_code
                </if>
                <if test="inDto.decorateCompanyCodeList !=null and inDto.decorateCompanyCodeList.size>0">
                    inner join
                    <include refid="decorateCodeList"/>
                    on decorate.decorateCode=o.decorate_company_code
                </if>
                <if test="inDto.decorateCompanyType==1 and inDto.decOrDesiList !=null and inDto.decOrDesiList.size>0">
                    inner join
                    <include refid="decOrDesiList"/>
                    on
                    (decOrDes.decorateCompanyCode=o.decorate_company_code
                    OR decOrDes.designerCode=o.designer_code)
                </if>
                <include refid="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE"/>
                ) oo
            </when>
            <otherwise>
                SELECT count(o.id) total,SUM(CASE o.stage WHEN 1 THEN 1 ELSE 0 END ) noAudited,SUM(CASE o.stage WHEN 2
                THEN 1 ELSE 0 END ) audited
                FROM omscenter.declaration_info o
                <if test="inDto.designerCodeList !=null and inDto.designerCodeList.size>0">
                    inner join
                    <include refid="designerCodeList"/>
                    on designer.designerCode=o.designer_code
                </if>
                <if test="inDto.decorateCompanyCodeList !=null and inDto.decorateCompanyCodeList.size>0">
                    inner join
                    <include refid="decorateCodeList"/>
                    on decorate.decorateCode=o.decorate_company_code
                </if>
                <include refid="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE"/>
            </otherwise>
        </choose>

    </select>

    <!-- 新增商品补充信息 -->
    <insert id="addGoodsSupple" parameterType="goodsSuppleEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        omscenter.goods_declaration_supple
        <trim prefix="(" suffix=")" suffixOverrides=",">
            created_by,created_date,modified_by,modified_date,
            <if test="orderId != null"><!-- 订单id -->
                order_id,
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                goods_id,
            </if>
            <if test="posCode != null"><!-- POS编码 -->
                pos_code,
            </if>
            <if test="ticketCode != null"><!-- 小票编码 -->
                ticket_code,
            </if>
            <if test="checkCode != null"><!-- 校验码 -->
                check_code,
            </if>
            <if test="omsCode != null"><!-- OMS号码 -->
                oms_code,
            </if>
            <if test="isShow != null"><!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                is_show,
            </if>
            <if test="handselOff != null"><!-- 定金优惠 -->
                handsel_off,
            </if>
            <if test="plummetOff != null"><!-- 直降优惠 -->
                plummet_off,
            </if>
            <if test="specialOff != null"><!-- 特批优惠 -->
                special_off,
            </if>
            <if test="special != null"><!-- 特批 -->
                special,
            </if>
            <if test="meal != null"><!-- 套餐 -->
                meal,
            </if>
            <if test="redCard != null"><!-- 红卡 -->
                red_card,
            </if>
            <if test="marketRed != null"><!-- 市调红 -->
                market_red,
            </if>
            <if test="marketBlue != null"><!-- 市调蓝 -->
                market_blue,
            </if>
            <if test="nonStandard2 != null"><!-- 非标2 -->
                non_standard2,
            </if>
            <if test="plummet != null"><!-- 直降 -->
                plummet,
            </if>
            <if test="useTicket != null"><!-- 用券 -->
                use_ticket,
            </if>
            <if test="discount != null"><!-- 优惠 -->
                discount,
            </if>
            <if test="totalBlue != null"><!-- 总额蓝 -->
                total_blue,
            </if>
            <if test="managerCard != null"><!-- 经理卡 -->
                manager_card,
            </if>
            <if test="mealModel != null"><!-- 套餐机型 -->
                meal_model,
            </if>
            <if test="points != null"><!-- 积分 -->
                points,
            </if>
            <if test="returnTicket != null"><!-- 返券 -->
                return_ticket,
            </if>
            <if test="colReturnTicket != null"><!-- 收返券/返利卡 -->
                col_return_ticket,
            </if>
            <if test="eTicket != null"><!-- 电子券/积分 -->
                e_ticket,
            </if>
            <if test="deliveryCard != null"><!-- 提货卡/联心卡/福卡 -->
                delivery_card,
            </if>
            <if test="returnTicket2 != null"><!-- 返券2 -->
                return_ticket2,
            </if>
            <if test="sigCard != null"><!-- 签售卡 -->
                sig_card,
            </if>
            <if test="wealthCard != null"><!-- 财富增值卡 -->
                wealth_card,
            </if>
            <if test="towVoucher != null"><!-- 二次券 -->
                tow_voucher,
            </if>
            <if test="gift != null"><!-- 赠品 -->
                gift,
            </if>
            <if test="delayGuarantee != null"><!-- 延保 -->
                delay_guarantee,
            </if>
            <if test="otherPromotion != null"><!-- 其他促销费 -->
                other_promotion,
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                `sort`,
            </if>
            <if test="cardId != null and cardNo != null and cardNo != ''">
                card_id,card_no,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{createdBy},now(),#{modifiedBy},now(),
            <if test="orderId != null"><!-- 订单id -->
                #{orderId},
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                #{goodsId},
            </if>
            <if test="posCode != null"><!-- POS编码 -->
                #{posCode},
            </if>
            <if test="ticketCode != null"><!-- 小票编码 -->
                #{ticketCode},
            </if>
            <if test="checkCode != null"><!-- 校验码 -->
                #{checkCode},
            </if>
            <if test="omsCode != null"><!-- OMS号码 -->
                #{omsCode},
            </if>
            <if test="isShow != null"><!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                #{isShow},
            </if>
            <if test="handselOff != null"><!-- 定金优惠 -->
                #{handselOff},
            </if>
            <if test="plummetOff != null"><!-- 直降优惠 -->
                #{plummetOff},
            </if>
            <if test="specialOff != null"><!-- 特批优惠 -->
                #{specialOff},
            </if>
            <if test="special != null"><!-- 特批 -->
                #{special},
            </if>
            <if test="meal != null"><!-- 套餐 -->
                #{meal},
            </if>
            <if test="redCard != null"><!-- 红卡 -->
                #{redCard},
            </if>
            <if test="marketRed != null"><!-- 市调红 -->
                #{marketRed},
            </if>
            <if test="marketBlue != null"><!-- 市调蓝 -->
                #{marketBlue},
            </if>
            <if test="nonStandard2 != null"><!-- 非标2 -->
                #{nonStandard2},
            </if>
            <if test="plummet != null"><!-- 直降 -->
                #{plummet},
            </if>
            <if test="useTicket != null"><!-- 用券 -->
                #{useTicket},
            </if>
            <if test="discount != null"><!-- 优惠 -->
                #{discount},
            </if>
            <if test="totalBlue != null"><!-- 总额蓝 -->
                #{totalBlue},
            </if>
            <if test="managerCard != null"><!-- 经理卡 -->
                #{managerCard},
            </if>
            <if test="mealModel != null"><!-- 套餐机型 -->
                #{mealModel},
            </if>
            <if test="points != null"><!-- 积分 -->
                #{points},
            </if>
            <if test="returnTicket != null"><!-- 返券 -->
                #{returnTicket},
            </if>
            <if test="colReturnTicket != null"><!-- 收返券/返利卡 -->
                #{colReturnTicket},
            </if>
            <if test="eTicket != null"><!-- 电子券/积分 -->
                #{eTicket},
            </if>
            <if test="deliveryCard != null"><!-- 提货卡/联心卡/福卡 -->
                #{deliveryCard},
            </if>
            <if test="returnTicket2 != null"><!-- 返券2 -->
                #{returnTicket2},
            </if>
            <if test="sigCard != null"><!-- 签售卡 -->
                #{sigCard},
            </if>
            <if test="wealthCard != null"><!-- 财富增值卡 -->
                #{wealthCard},
            </if>
            <if test="towVoucher != null"><!-- 二次券 -->
                #{towVoucher},
            </if>
            <if test="gift != null"><!-- 赠品 -->
                #{gift},
            </if>
            <if test="delayGuarantee != null"><!-- 延保 -->
                #{delayGuarantee},
            </if>
            <if test="otherPromotion != null"><!-- 其他促销费 -->
                #{otherPromotion},
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                #{sort},
            </if>
            <if test="cardId != null and cardNo != null and cardNo != ''">
                #{cardId},#{cardNo},
            </if>

        </trim>
    </insert>

    <!-- 修改商品补充信息 -->
    <update id="updateGoodsSupple" parameterType="goodsSuppleEntity">
        update goods_declaration_supple
        <set>
            modified_date=now(),modified_by=#{modifiedBy},
            <if test="orderId != null"><!-- 订单id -->
                order_id=#{orderId},
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                goods_id=#{goodsId},
            </if>
            <if test="posCode != null">
                pos_code = #{posCode,jdbcType=VARCHAR},
            </if>
            <if test="ticketCode != null">
                ticket_code = #{ticketCode,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                check_code = #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="omsCode != null">
                oms_code = #{omsCode,jdbcType=VARCHAR},
            </if>
            <if test="isShow != null"><!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                is_show = #{isShow,jdbcType=CHAR},
            </if>
            <if test="handselOff != null">
                handsel_off = #{handselOff,jdbcType=DECIMAL},
            </if>
            <if test="plummetOff != null">
                plummet_off = #{plummetOff,jdbcType=DECIMAL},
            </if>
            <if test="specialOff != null">
                special_off = #{specialOff,jdbcType=DECIMAL},
            </if>
            <if test="special != null">
                special = #{special,jdbcType=DECIMAL},
            </if>
            <if test="meal != null">
                meal = #{meal,jdbcType=DECIMAL},
            </if>
            <if test="redCard != null">
                red_card = #{redCard,jdbcType=DECIMAL},
            </if>
            <if test="marketRed != null">
                market_red = #{marketRed,jdbcType=DECIMAL},
            </if>
            <if test="marketBlue != null">
                market_blue = #{marketBlue,jdbcType=DECIMAL},
            </if>
            <if test="nonStandard2 != null">
                non_standard2 = #{nonStandard2,jdbcType=DECIMAL},
            </if>
            <if test="plummet != null">
                plummet = #{plummet,jdbcType=DECIMAL},
            </if>
            <if test="useTicket != null">
                use_ticket = #{useTicket,jdbcType=DECIMAL},
            </if>
            <if test="discount != null">
                discount = #{discount,jdbcType=DECIMAL},
            </if>
            <if test="totalBlue != null">
                total_blue = #{totalBlue,jdbcType=DECIMAL},
            </if>
            <if test="managerCard != null">
                manager_card = #{managerCard,jdbcType=DECIMAL},
            </if>
            <if test="mealModel != null">
                meal_model = #{mealModel,jdbcType=DECIMAL},
            </if>
            <if test="points != null">
                points = #{points,jdbcType=DECIMAL},
            </if>
            <if test="returnTicket != null">
                return_ticket = #{returnTicket,jdbcType=DECIMAL},
            </if>
            <if test="colReturnTicket != null">
                col_return_ticket = #{colReturnTicket,jdbcType=DECIMAL},
            </if>
            <if test="eTicket != null">
                e_ticket = #{eTicket,jdbcType=DECIMAL},
            </if>
            <if test="deliveryCard != null">
                delivery_card = #{deliveryCard,jdbcType=DECIMAL},
            </if>
            <if test="returnTicket2 != null">
                return_ticket2 = #{returnTicket2,jdbcType=DECIMAL},
            </if>
            <if test="sigCard != null">
                sig_card = #{sigCard,jdbcType=DECIMAL},
            </if>
            <if test="wealthCard != null">
                wealth_card = #{wealthCard,jdbcType=DECIMAL},
            </if>
            <if test="towVoucher != null">
                tow_voucher = #{towVoucher,jdbcType=DECIMAL},
            </if>
            <if test="gift != null">
                gift = #{gift,jdbcType=DECIMAL},
            </if>
            <if test="delayGuarantee != null">
                delay_guarantee = #{delayGuarantee,jdbcType=DECIMAL},
            </if>
            <if test="otherPromotion != null">
                other_promotion = #{otherPromotion,jdbcType=DECIMAL},
            </if>
            <if test="sort != null">
                `sort` = #{sort},
            </if>
            <if test="cardId != null and cardNo != null and cardNo != ''">
                card_id = #{cardId},card_no = #{cardNo},
            </if>

        </set>
        WHERE order_id=#{orderId} AND goods_id=#{goodsId} AND `sort`=#{sort}
    </update>

    <!-- 根据商品表id查询商品补充信息 -->
    <select id="findGoodsSuppleByGoodsInfoId" resultType="goodsSuppleEntity">
        SELECT id, goods_declaration_id goodsDeclarationId,pos_code posCode,
        <!--<choose>
            <when test="toDrpUse !=null and toDrpUse==1">
                CASE WHEN card_no IS NOT NULL AND TRIM(card_no) !='' THEN card_no ELSE ticket_code END ticketCode,
            </when>
            <otherwise>
                ticket_code ticketCode,
            </otherwise>
        </choose>-->
        card_no cardNo,ticket_code ticketCode,
        check_code checkCode,oms_code omsCode,is_show isShow
        ,handsel_off handselOff,plummet_off plummetOff,special_off specialOff,special special,meal meal,red_card
        redCard,market_red marketRed
        ,market_blue marketBlue,non_standard2 nonStandard2,plummet plummet,use_ticket useTicket,discount
        discount,total_blue totalBlue,manager_card managerCard
        ,meal_model mealModel,points points,return_ticket returnTicket,col_return_ticket colReturnTicket,e_ticket
        eTicket,delivery_card deliveryCard
        ,return_ticket2 returnTicket2,sig_card sigCard,wealth_card wealthCard,tow_voucher towVoucher,gift
        gift,delay_guarantee delayGuarantee,other_promotion otherPromotion,
        order_id orderId,goods_id goodsId,sort,card_id cardId,is_consume isConsume,card_name cardName,
        modified_date modifiedDate
        FROM omscenter.goods_declaration_supple
        WHERE goods_declaration_id=#{goodsDeclarationId} AND is_deleted=0
    </select>
    <!-- 根据商品表ids批量查询商品补充信息 -->
    <select id="findGoodsSuppleByGoodsInfoIds" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsSuppleEntity">
        SELECT id, goods_declaration_id goodsDeclarationId,pos_code posCode,ticket_code ticketCode,check_code
        checkCode,oms_code omsCode,
        CASE is_show WHEN 0 THEN '否' WHEN 1 THEN '库房样机销售' WHEN 2 THEN '门店样机销售' WHEN 3 THEN '上样/出样' END
        isShowName,is_show isShow
        ,handsel_off handselOff,plummet_off plummetOff,special_off specialOff,special special,meal meal,red_card
        redCard,market_red marketRed
        ,market_blue marketBlue,non_standard2 nonStandard2,plummet plummet,use_ticket useTicket,discount
        discount,total_blue totalBlue,manager_card managerCard
        ,meal_model mealModel,points points,return_ticket returnTicket,col_return_ticket colReturnTicket,e_ticket
        eTicket,delivery_card deliveryCard
        ,return_ticket2 returnTicket2,sig_card sigCard,wealth_card wealthCard,tow_voucher towVoucher,gift
        gift,delay_guarantee delayGuarantee,other_promotion otherPromotion,
        order_id orderId,goods_id goodsId,sort,card_no cardNo,card_id cardId,is_consume isConsume,uuid,card_name
        cardName,bar_code barCode
        FROM omscenter.goods_declaration_supple
        WHERE
        is_deleted = 0
        <if test="orderIds !=null and orderIds.size>0">
            AND order_id in
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        </if>
        <if test="goodsIds !=null and goodsIds.size>0">
            AND goods_id in
            <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
                #{goodsId}
            </foreach>
        </if>
        group by id
    </select>

    <select id="selectGoodsSuppleCountByGoodsInfoId" resultType="java.lang.Integer">
        select count(1)
        from omscenter.goods_declaration_supple
        WHERE is_deleted = 0
          AND order_id = #{orderId}
          AND goods_id = #{goodsId}
          AND sort = #{sort}
    </select>

    <!-- 根据订单id、商品表id、sort删除商品补充信息 -->
    <update id="delGoodsSuppleCountByGoodsInfoId">
        UPDATE omscenter.goods_declaration_supple
        SET modified_date=NOW(),is_deleted=1
        WHERE is_deleted=0 AND order_id=#{orderId} AND goods_id=#{goodsId}
        <if test="sortList !=null and sortList.size>0">
            AND sort NOT IN
            <foreach collection="sortList" item="sort" open="(" separator="," close=")">
                #{sort}
            </foreach>
        </if>
    </update>

    <!-- 根据订单id删除商品 -->
    <update id="delGoodsByOrderId">
        UPDATE omscenter.goods_declaration
        SET is_deleted=1,
            modified_date=now(),
            modified_by=#{modifiedBy}
        WHERE order_id = #{id}
    </update>

    <!-- 删除订单 -->
    <update id="delDecOrder">
        UPDATE omscenter.declaration_info
        SET is_deleted=1,
            modified_date=now(),
            del_time=now(),
            modified_by=#{modifiedBy}
        WHERE id = #{orderId}
    </update>


    <update id="updateOrderStage">
        update omscenter.declaration_info o
        set modified_date=NOW(),stage=4,invalid_time=NOW(),plat_invalid_time = NOW()
        where o.id = #{id}
    </update>


    <!-- 录单人列表 -->
    <select id="findAdverList" resultType="findAdverListOutDto">
        SELECT  o.input_id inputId,o.input_name inputName
        FROM omscenter.declaration_info o
        /*LEFT JOIN omscenter.goods_declaration g ON o.id=g.order_id*/
        <include refid="findDecOrderByCluesIdWHERE"/>
        AND (o.input_id IS NOT NULL OR trim(o.input_id)!='')
        group by o.input_id
    </select>


    <select id="selectGoodsSuppleTempCountByGoodsInfoId" resultType="java.lang.Integer">
        SELECT count(1)
        <choose>
            <when test="flag !=null and flag==2"><!-- 查正式表 -->
                FROM omscenter.goods_declaration_supple
            </when>
            <otherwise>
                FROM omscenter.goods_declaration_supple_temp<!-- 查临时表 -->
            </otherwise>
        </choose>
        WHERE is_deleted=0 AND uuid=#{uuid} AND card_no=#{cardNo} AND card_id=#{cardId}
        <if test="id !=null">AND id !=#{id}</if>
    </select>


    <!-- 根据商品表id查询商品补充信息(临时表) -->
    <select id="findGoodsSuppleTempByUUID" resultType="goodsSuppleTempEntity">
        SELECT id
             , goods_declaration_id goodsDeclarationId
             , pos_code             posCode
             , ticket_code          ticketCode
             , check_code           checkCode
             , oms_code             omsCode
             , is_show              isShow
             , handsel_off          handselOff
             , plummet_off          plummetOff
             , special_off          specialOff
             , special              special
             , meal                 meal
             , red_card             redCard
             , market_red           marketRed
             , market_blue          marketBlue
             , non_standard2        nonStandard2
             , plummet              plummet
             , use_ticket           useTicket
             , discount             discount
             , total_blue           totalBlue
             , manager_card         managerCard
             , meal_model           mealModel
             , points               points
             , return_ticket        returnTicket
             , col_return_ticket    colReturnTicket
             , e_ticket             eTicket
             , delivery_card        deliveryCard
             , return_ticket2       returnTicket2
             , sig_card             sigCard
             , wealth_card          wealthCard
             , tow_voucher          towVoucher
             , gift                 gift
             , delay_guarantee      delayGuarantee
             , other_promotion      otherPromotion
             , order_id             orderId
             , goods_id             goodsId
             , sort
             , card_id              cardId
             , card_no              cardNo
             , uuid
        FROM omscenter.goods_declaration_supple_temp
        WHERE uuid = #{uuid}
          AND is_deleted = 0
    </select>

    <!-- 根据uuid批量更新产品明细表订单id(临时表) -->
    <update id="updateGoodsSuppleTempByUUID">
        UPDATE omscenter.goods_declaration_supple_temp
        SET modified_date=NOW(),order_id=#{orderId},
            is_consume=1
        WHERE uuid = #{uuid}
          AND is_deleted = 0
    </update>

    <!-- 根据uuid批量更新产品明细表订单id(临时表) -->
    <update id="updateGoodsSuppleTempByUUID2">
        UPDATE omscenter.goods_declaration_supple_temp
        SET modified_date=NOW(),is_consume=1
        WHERE uuid = #{uuid}
          AND is_deleted = 0
          AND card_no IS NOT NULL
    </update>

    <!-- 根据uuid批量更新产品明细表订单id(临时表) -->
    <update id="updateGoodsSuppleByUUID2">
        UPDATE omscenter.goods_declaration_supple
        SET modified_date=NOW(),is_consume=1
        WHERE order_id = #{orderId}
          AND is_deleted = 0
          AND card_no IS NOT NULL and card_no != ''
    </update>
    <update id="updateGoodsSuppleByGoodsDeclarationId">
        UPDATE omscenter.goods_declaration_supple
        SET modified_date=NOW(),is_consume=1
        WHERE goods_declaration_id = #{goodsDeclarationId}
          AND is_deleted = 0
          AND card_no IS NOT NULL
    </update>
    <!-- 根据uuid批量更新产品表订单id(临时表) -->
    <update id="updateGoodsTempByUUID">
        UPDATE omscenter.goods_declaration_temp
        SET modified_date=NOW(),order_id=#{orderId},
            is_sync=1
        WHERE uuid = #{uuid}
          AND is_deleted = 0
    </update>
    <!-- 将临时表中的产品明细移至正式表 -->
    <insert id="moveGoodsSuppleTemp" parameterType="goodsSuppleTempEntity">
        INSERT INTO
        omscenter.goods_declaration_supple(created_by,created_date,modified_by,modified_date,order_id,goods_id,pos_code,
        ticket_code,check_code,oms_code,is_show,handsel_off,plummet_off,special_off,special,meal,red_card,market_red,
        market_blue,non_standard2,plummet,use_ticket,discount,total_blue,manager_card,meal_model,points,return_ticket,
        col_return_ticket,e_ticket,delivery_card,return_ticket2,sig_card,wealth_card,tow_voucher,gift,delay_guarantee,
        other_promotion,`sort`,card_id,card_no,uuid,is_consume)
        VALUES
        <foreach collection="suppleList" item="supple" separator=",">
            (#{createdBy},now(),#{createdBy},now(),#{orderId},#{supple.goodsId},#{supple.posCode},#{supple.ticketCode},
            #{supple.checkCode},#{supple.omsCode},#{supple.isShow},#{supple.handselOff},#{supple.plummetOff},#{supple.specialOff},
            #{supple.special},#{supple.meal},#{supple.redCard},#{supple.marketRed},#{supple.marketBlue},#{supple.nonStandard2},
            #{supple.plummet},#{supple.useTicket},#{supple.discount},#{supple.totalBlue},#{supple.managerCard},#{supple.mealModel},
            #{supple.points},#{supple.returnTicket},#{supple.colReturnTicket},#{supple.eTicket},#{supple.deliveryCard},
            #{supple.returnTicket2},#{supple.sigCard},#{supple.wealthCard},#{supple.towVoucher},#{supple.gift},#{supple.delayGuarantee},
            #{supple.otherPromotion},#{supple.sort},#{supple.cardId},#{supple.cardNo},#{supple.uuid},1)
        </foreach>
    </insert>


    <!-- 批量核销后更新产品明细表核销状态 作废-->
    <update id="batchUpdateGoodsSupple">
        <if test="inDto.needConsumeList !=null and inDto.needConsumeList.size>0">
            UPDATE omscenter.goods_declaration_supple
            SET is_consume=1,modified_by=#{modifiedBy},modified_date=now()
            WHERE is_deleted=0
            AND id IN
            <foreach collection="inDto.needConsumeList" item="con" open="(" separator="," close=")">
                #{con.id}
            </foreach>
        </if>
    </update>
    <!-- check优惠券是否已使用 -->
    <select id="checkCardNo" resultType="java.lang.Integer">
        select count(1) from omscenter.goods_declaration_supple
        WHERE is_deleted=0 AND order_id=#{orderId}
        <if test="cardId !=null and cardNo !=null and cardNo !=''">
            AND card_id=#{cardId}
            AND card_no=#{cardNo}
        </if>

    </select>
    <!-- 2020-07-08迭代版本 -->
    <!-- 新增商品(进临时表) -->
    <insert id="addGoodsTemp" parameterType="goodsTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <choose>
            <when test="flag !=null and flag==2"><!-- 新增产品至正式表中 -->
                omscenter.goods_declaration
            </when>
            <otherwise><!-- 新增产品至临时表中-->
                omscenter.goods_declaration_temp
            </otherwise>
        </choose>

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null"><!-- 订单id -->
                order_id,
            </if>
            <if test="goodsCategoryId != null"><!-- 商品分类 -->
                goods_category_id,
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                goods_id,
            </if>
            <if test="goodsName != null"><!-- 商品名称 -->
                goods_name,
            </if>
            <if test="goodsPrice != null"><!-- 单价 -->
                goods_price,
            </if>
            <if test="goodsNum != null"><!-- 数量 -->
                goods_num,
            </if>
            <if test="goodsTotalPrice != null"><!-- 商品金额 -->
                goods_total_price,
            </if>
            <if test="goodsImage != null "><!-- 商品图片 -->
                goods_image,
            </if>
            <if test="goodsCode != null"><!-- 商品编号 -->
                goods_code,
            </if>
            <if test="goodsNote != null"><!-- 商品备注 -->
                goods_note,
            </if>
            <if test="goodsType != null"><!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
                goods_type,
            </if>
            <if test="isGift != null"><!-- 是否赠品 1：赠品；0：非赠品 -->
                is_gift,
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                `sort`,
            </if>
            <if test="uuid != null"><!-- uuid -->
                `uuid`,
            </if>
            <if test="flag !=null and flag==2">
                `guid`,
            </if>
            <if test="code != null ">
                code,
            </if>
            location_type,
            created_by,created_date,modified_by,modified_date
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null"><!-- 订单id -->
                #{orderId},
            </if>
            <if test="goodsCategoryId != null"><!-- 商品分类 -->
                #{goodsCategoryId},
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                #{goodsId},
            </if>
            <if test="goodsName != null"><!-- 商品名称 -->
                #{goodsName},
            </if>
            <if test="goodsPrice != null"><!-- 单价 -->
                #{goodsPrice},
            </if>
            <if test="goodsNum != null"><!-- 数量 -->
                #{goodsNum},
            </if>
            <if test="goodsTotalPrice != null"><!-- 商品金额 -->
                #{goodsTotalPrice},
            </if>
            <if test="goodsImage != null "><!-- 商品图片 -->
                #{goodsImage},
            </if>
            <if test="goodsCode != null"><!-- 商品编号 -->
                #{goodsCode},
            </if>
            <if test="goodsNote != null"><!-- 商品备注 -->
                #{goodsNote},
            </if>
            <if test="goodsType != null"><!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
                #{goodsType},
            </if>
            <if test="isGift != null"><!-- 是否赠品 1：赠品；0：非赠品 -->
                #{isGift},
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                #{sort},
            </if>
            <if test="uuid != null"><!-- uuid -->
                #{uuid},
            </if>
            <if test="flag !=null and flag==2">
                CONCAT('HLB-',UUID()),
            </if>
            <if test="code != null ">
                #{code},
            </if>
            <choose>
                <when test="locationType !=null">
                    #{locationType},
                </when>
                <otherwise>
                    1,
                </otherwise>
            </choose>
            #{createdBy},now(),#{modifiedBy},now()
        </trim>
    </insert>

    <!-- 新增商品补充信息(进临时表) -->
    <insert id="addTempGoodsSupple" parameterType="goodsSuppleTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <choose>
            <when test="flag !=null and flag==2"><!-- 新增产品补充信息至正式表中 -->
                omscenter.goods_declaration_supple
            </when>
            <otherwise><!-- 新增产品补充信息至临时表中-->
                omscenter.goods_declaration_supple_temp
            </otherwise>
        </choose>

        <trim prefix="(" suffix=")" suffixOverrides=",">
            created_by,created_date,modified_by,modified_date,
            <if test="orderId != null"><!-- 订单id -->
                order_id,
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                goods_id,
            </if>
            <if test="posCode != null"><!-- POS编码 -->
                pos_code,
            </if>
            <if test="ticketCode != null"><!-- 小票编码 -->
                ticket_code,
            </if>
            <if test="checkCode != null"><!-- 校验码 -->
                check_code,
            </if>
            <if test="omsCode != null"><!-- OMS号码 -->
                oms_code,
            </if>
            <if test="isShow != null"><!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                is_show,
            </if>
            <if test="handselOff != null"><!-- 定金优惠 -->
                handsel_off,
            </if>
            <if test="plummetOff != null"><!-- 直降优惠 -->
                plummet_off,
            </if>
            <if test="specialOff != null"><!-- 特批优惠 -->
                special_off,
            </if>
            <if test="special != null"><!-- 特批 -->
                special,
            </if>
            <if test="meal != null"><!-- 套餐 -->
                meal,
            </if>
            <if test="redCard != null"><!-- 红卡 -->
                red_card,
            </if>
            <if test="marketRed != null"><!-- 市调红 -->
                market_red,
            </if>
            <if test="marketBlue != null"><!-- 市调蓝 -->
                market_blue,
            </if>
            <if test="nonStandard2 != null"><!-- 非标2 -->
                non_standard2,
            </if>
            <if test="plummet != null"><!-- 直降 -->
                plummet,
            </if>
            <if test="useTicket != null"><!-- 用券 -->
                use_ticket,
            </if>
            <if test="discount != null"><!-- 优惠 -->
                discount,
            </if>
            <if test="totalBlue != null"><!-- 总额蓝 -->
                total_blue,
            </if>
            <if test="managerCard != null"><!-- 经理卡 -->
                manager_card,
            </if>
            <if test="mealModel != null"><!-- 套餐机型 -->
                meal_model,
            </if>
            <if test="points != null"><!-- 积分 -->
                points,
            </if>
            <if test="returnTicket != null"><!-- 返券 -->
                return_ticket,
            </if>
            <if test="colReturnTicket != null"><!-- 收返券/返利卡 -->
                col_return_ticket,
            </if>
            <if test="eTicket != null"><!-- 电子券/积分 -->
                e_ticket,
            </if>
            <if test="deliveryCard != null"><!-- 提货卡/联心卡/福卡 -->
                delivery_card,
            </if>
            <if test="returnTicket2 != null"><!-- 返券2 -->
                return_ticket2,
            </if>
            <if test="sigCard != null"><!-- 签售卡 -->
                sig_card,
            </if>
            <if test="wealthCard != null"><!-- 财富增值卡 -->
                wealth_card,
            </if>
            <if test="towVoucher != null"><!-- 二次券 -->
                tow_voucher,
            </if>
            <if test="gift != null"><!-- 赠品 -->
                gift,
            </if>
            <if test="delayGuarantee != null"><!-- 延保 -->
                delay_guarantee,
            </if>
            <if test="otherPromotion != null"><!-- 其他促销费 -->
                other_promotion,
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                `sort`,
            </if>
            <if test="uuid != null"><!-- uuid -->
                `uuid`,
            </if>

            <if test="cardId != null and cardNo != null and cardNo != ''"><!-- 优惠券id -->
                `card_id`,`card_no`,
            </if>
            <if test="goodsDeclarationId != null"><!-- goods_declaration表的id -->
                goods_declaration_id,
            </if>
            <if test="isConsume !=null"><!-- 是否核销 0：否；1：是 -->
                is_consume,
            </if>
            <if test="cardName !=null and cardName !=''"><!--优惠券名称-->
                card_name,
            </if>
            <if test="barCode !=null and barCode !=''"><!--样机条形码-->
                bar_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{createdBy},now(),#{modifiedBy},now(),
            <if test="orderId != null"><!-- 订单id -->
                #{orderId},
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                #{goodsId},
            </if>
            <if test="posCode != null"><!-- POS编码 -->
                #{posCode},
            </if>
            <if test="ticketCode != null"><!-- 小票编码 -->
                #{ticketCode},
            </if>
            <if test="checkCode != null"><!-- 校验码 -->
                #{checkCode},
            </if>
            <if test="omsCode != null"><!-- OMS号码 -->
                #{omsCode},
            </if>
            <if test="isShow != null"><!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                #{isShow},
            </if>
            <if test="handselOff != null"><!-- 定金优惠 -->
                #{handselOff},
            </if>
            <if test="plummetOff != null"><!-- 直降优惠 -->
                #{plummetOff},
            </if>
            <if test="specialOff != null"><!-- 特批优惠 -->
                #{specialOff},
            </if>
            <if test="special != null"><!-- 特批 -->
                #{special},
            </if>
            <if test="meal != null"><!-- 套餐 -->
                #{meal},
            </if>
            <if test="redCard != null"><!-- 红卡 -->
                #{redCard},
            </if>
            <if test="marketRed != null"><!-- 市调红 -->
                #{marketRed},
            </if>
            <if test="marketBlue != null"><!-- 市调蓝 -->
                #{marketBlue},
            </if>
            <if test="nonStandard2 != null"><!-- 非标2 -->
                #{nonStandard2},
            </if>
            <if test="plummet != null"><!-- 直降 -->
                #{plummet},
            </if>
            <if test="useTicket != null"><!-- 用券 -->
                #{useTicket},
            </if>
            <if test="discount != null"><!-- 优惠 -->
                #{discount},
            </if>
            <if test="totalBlue != null"><!-- 总额蓝 -->
                #{totalBlue},
            </if>
            <if test="managerCard != null"><!-- 经理卡 -->
                #{managerCard},
            </if>
            <if test="mealModel != null"><!-- 套餐机型 -->
                #{mealModel},
            </if>
            <if test="points != null"><!-- 积分 -->
                #{points},
            </if>
            <if test="returnTicket != null"><!-- 返券 -->
                #{returnTicket},
            </if>
            <if test="colReturnTicket != null"><!-- 收返券/返利卡 -->
                #{colReturnTicket},
            </if>
            <if test="eTicket != null"><!-- 电子券/积分 -->
                #{eTicket},
            </if>
            <if test="deliveryCard != null"><!-- 提货卡/联心卡/福卡 -->
                #{deliveryCard},
            </if>
            <if test="returnTicket2 != null"><!-- 返券2 -->
                #{returnTicket2},
            </if>
            <if test="sigCard != null"><!-- 签售卡 -->
                #{sigCard},
            </if>
            <if test="wealthCard != null"><!-- 财富增值卡 -->
                #{wealthCard},
            </if>
            <if test="towVoucher != null"><!-- 二次券 -->
                #{towVoucher},
            </if>
            <if test="gift != null"><!-- 赠品 -->
                #{gift},
            </if>
            <if test="delayGuarantee != null"><!-- 延保 -->
                #{delayGuarantee},
            </if>
            <if test="otherPromotion != null"><!-- 其他促销费 -->
                #{otherPromotion},
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                #{sort},
            </if>
            <if test="uuid != null"><!-- uuid -->
                #{uuid},
            </if>

            <if test="cardId != null and cardNo != null and cardNo != ''"><!-- 优惠券id -->
                #{cardId},#{cardNo},
            </if>
            <if test="goodsDeclarationId != null"><!-- goods_declaration表的id -->
                #{goodsDeclarationId},
            </if>
            <if test="isConsume !=null"><!-- 是否核销 0：否；1：是 -->
                #{isConsume},
            </if>
            <if test="cardName !=null and cardName !=''"><!--优惠券名称-->
                #{cardName},
            </if>
            <if test="barCode !=null and barCode !=''"><!--样机条形码-->
                #{barCode},
            </if>
        </trim>
    </insert>

    <!-- 根据id查询产品明细(临时表) -->
    <select id="findGoodsSuppleTempById" resultType="goodsSuppleTempEntity">
        SELECT s.id, s.goods_declaration_id goodsDeclarationId,s.pos_code posCode,s.ticket_code ticketCode,
        s.check_code checkCode,s.oms_code omsCode,s.is_show isShow,s.handsel_off handselOff,s.plummet_off plummetOff,
        s.special_off specialOff,s.special special,s.meal meal,s.red_card redCard,s.market_red marketRed,
        s.market_blue marketBlue,s.non_standard2 nonStandard2,s.plummet plummet,s.use_ticket useTicket,
        s.discount discount,s.total_blue totalBlue,s.manager_card managerCard,s.meal_model mealModel,
        s.points points,s.return_ticket returnTicket,s.col_return_ticket colReturnTicket,s.e_ticket eTicket,
        s.delivery_card deliveryCard,s.return_ticket2 returnTicket2,s.sig_card sigCard,s.wealth_card wealthCard,
        s.tow_voucher towVoucher,s.gift gift,s.delay_guarantee delayGuarantee,s.other_promotion otherPromotion,
        s.order_id orderId,s.goods_id goodsId,s.sort,s.card_id cardId,s.card_no cardNo,s.uuid,s.is_consume isConsume
        ,g.location_type locationType,g.logistics_status logisticsStatus,g.goods_id goodsId,g.goods_code goodsCode,
        g.lowest_price lowestPrice,g.company_lowest_price companyLowestPrice,g.store_lowest_price storeLowestPrice,
        g.cost_estimate costEstimate,g.online online,s.bar_code barCode
        <if test="flag !=null and flag==2">
            ,dcs.out_status dcsStatus
            ,dcs.express_code expressCode
            ,dcs.logistics_trace_link logisticsTraceLink
            ,g.guid
        </if>
        <choose>
            <when test="flag !=null and flag==2"><!-- 查正式表中的产品补充信息 -->
                FROM omscenter.goods_declaration_supple s
                LEFT JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND s.goods_declaration_id=g.id
                LEFT JOIN omscenter.goods_declaration_dcs dcs ON dcs.is_deleted=0
                   AND concat(s.goods_declaration_id, '') = dcs.order_line_id and g.order_id = dcs.order_id
            </when>
            <otherwise><!-- 查临时表中的产品补充信息 -->
                FROM omscenter.goods_declaration_supple_temp s
                LEFT JOIN omscenter.goods_declaration_temp g ON g.is_deleted=0 AND s.goods_declaration_id=g.id
            </otherwise>

        </choose>
        WHERE s.is_deleted=0 AND s.goods_declaration_id=#{goodsDeclarationId}
    </select>


    <!-- 根据id查询产品明细(临时表) -->
    <select id="findGoodsSuppleTempByIdList" resultType="goodsSuppleTempEntity">
        SELECT s.id, s.goods_declaration_id goodsDeclarationId,s.pos_code posCode,s.ticket_code ticketCode,
        s.check_code checkCode,s.oms_code omsCode,s.is_show isShow,s.handsel_off handselOff,s.plummet_off plummetOff,
        s.special_off specialOff,s.special special,s.meal meal,s.red_card redCard,s.market_red marketRed,
        s.market_blue marketBlue,s.non_standard2 nonStandard2,s.plummet plummet,s.use_ticket useTicket,
        s.discount discount,s.total_blue totalBlue,s.manager_card managerCard,s.meal_model mealModel,
        s.points points,s.return_ticket returnTicket,s.col_return_ticket colReturnTicket,s.e_ticket eTicket,
        s.delivery_card deliveryCard,s.return_ticket2 returnTicket2,s.sig_card sigCard,s.wealth_card wealthCard,
        s.tow_voucher towVoucher,s.gift gift,s.delay_guarantee delayGuarantee,s.other_promotion otherPromotion,
        s.order_id orderId,s.goods_id goodsId,s.sort,s.card_id cardId,s.card_no cardNo,s.uuid,s.is_consume isConsume
        ,g.location_type locationType,g.logistics_status logisticsStatus,g.goods_id goodsId,g.goods_code goodsCode,
        g.lowest_price lowestPrice,g.company_lowest_price companyLowestPrice,g.store_lowest_price storeLowestPrice,
        g.cost_estimate costEstimate,g.online online,s.bar_code barCode
        <if test="flag != null and flag == 2">
            ,dcs.out_status dcsStatus
            ,dcs.express_code expressCode
            ,dcs.logistics_trace_link logisticsTraceLink
        </if>
        <choose>
            <when test="flag != null and flag == 2"><!-- 查正式表中的产品补充信息 -->
                FROM omscenter.goods_declaration_supple s
                LEFT JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND s.goods_declaration_id=g.id
                LEFT JOIN omscenter.goods_declaration_dcs dcs ON dcs.is_deleted=0
                AND concat(s.goods_declaration_id, '') = dcs.order_line_id and g.order_id = dcs.order_id
            </when>
            <otherwise><!-- 查临时表中的产品补充信息 -->
                FROM omscenter.goods_declaration_supple_temp s
                LEFT JOIN omscenter.goods_declaration_temp g ON g.is_deleted=0 AND s.goods_declaration_id=g.id
            </otherwise>
        </choose>
        WHERE s.is_deleted=0
        AND s.goods_declaration_id in
        <foreach collection="goodsDeclarationIdList" item="goodsDeclarationId" open="(" close=")" separator=",">
            #{goodsDeclarationId}
        </foreach>
    </select>


    <!-- 根据id查询产品明细(临时表) -->
    <select id="findGoodsSuppleByGoodsId" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsSuppleTempEntity">
        SELECT s.id, s.goods_declaration_id goodsDeclarationId,s.pos_code posCode,s.ticket_code ticketCode,
        s.check_code checkCode,s.oms_code omsCode,s.is_show isShow,s.handsel_off handselOff,s.plummet_off plummetOff,
        s.special_off specialOff,s.special special,s.meal meal,s.red_card redCard,s.market_red marketRed,
        s.market_blue marketBlue,s.non_standard2 nonStandard2,s.plummet plummet,s.use_ticket useTicket,
        s.discount discount,s.total_blue totalBlue,s.manager_card managerCard,s.meal_model mealModel,
        s.points points,s.return_ticket returnTicket,s.col_return_ticket colReturnTicket,s.e_ticket eTicket,
        s.delivery_card deliveryCard,s.return_ticket2 returnTicket2,s.sig_card sigCard,s.wealth_card wealthCard,
        s.tow_voucher towVoucher,s.gift gift,s.delay_guarantee delayGuarantee,s.other_promotion otherPromotion,
        s.order_id orderId,s.goods_id goodsId,s.sort,s.card_id cardId,s.card_no cardNo,s.uuid,s.is_consume isConsume,bar_code barCode
        FROM omscenter.goods_declaration_supple s
        WHERE s.is_deleted=0 AND s.goods_declaration_id=#{goodsDeclarationId}
    </select>

    <!-- 根据id查询产品明细 -->
    <select id="findGoodsSuppleByGoodsIdList" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsSuppleTempEntity">
        SELECT s.id, s.goods_declaration_id goodsDeclarationId,s.pos_code posCode,s.ticket_code ticketCode,
        s.check_code checkCode,s.oms_code omsCode,s.is_show isShow,s.handsel_off handselOff,s.plummet_off plummetOff,
        s.special_off specialOff,s.special special,s.meal meal,s.red_card redCard,s.market_red marketRed,
        s.market_blue marketBlue,s.non_standard2 nonStandard2,s.plummet plummet,s.use_ticket useTicket,
        s.discount discount,s.total_blue totalBlue,s.manager_card managerCard,s.meal_model mealModel,
        s.points points,s.return_ticket returnTicket,s.col_return_ticket colReturnTicket,s.e_ticket eTicket,
        s.delivery_card deliveryCard,s.return_ticket2 returnTicket2,s.sig_card sigCard,s.wealth_card wealthCard,
        s.tow_voucher towVoucher,s.gift gift,s.delay_guarantee delayGuarantee,s.other_promotion otherPromotion,
        s.order_id orderId,s.goods_id goodsId,s.sort,s.card_id cardId,s.card_no cardNo,s.uuid,s.is_consume isConsume,bar_code barCode
        FROM omscenter.goods_declaration_supple s
        WHERE s.is_deleted=0
        AND s.goods_declaration_id in
        <foreach collection="goodsDeclarationIdList" item="goodsDeclarationId" separator="," open="(" close=")">
            #{goodsDeclarationId}
        </foreach>
    </select>

    <!-- 删除商品明细(临时表) -->
    <update id="delTempGoodsSupple">
        UPDATE
        <choose>
            <when test="inDto.flag !=null and inDto.flag==2"><!-- 删除正式表中的产品补充信息 -->
                omscenter.goods_declaration_supple
            </when>
            <otherwise><!-- 删除临时表中的产品补充信息 -->
                omscenter.goods_declaration_supple_temp
            </otherwise>
        </choose>
        SET is_deleted=1,modified_date=now(),modified_by=#{modifiedBy}
        WHERE is_deleted=0 AND goods_declaration_id=#{inDto.goodsDeclarationId}
    </update>

    <!-- 修改产品明细(临时表) -->
    <update id="updateTempGoodsSupple">
        <if test="inDto !=null">
            UPDATE
            <choose>
                <when test="inDto.flag !=null and inDto.flag==2"><!-- 删除正式表中的产品补充信息 -->
                    omscenter.goods_declaration_supple
                </when>
                <otherwise><!-- 删除临时表中的产品补充信息 -->
                    omscenter.goods_declaration_supple_temp
                </otherwise>
            </choose>
            <set>
                modified_by=#{modifiedBy},modified_date=now()
                <!-- 订单id -->
                ,order_id=#{inDto.orderId}
                <!-- 商品id -->
                ,goods_id=#{inDto.goodsId}
                <!-- POS编码 -->
                ,pos_code=#{inDto.posCode}
                <!-- 小票编码 -->
                ,ticket_code=#{inDto.ticketCode}
                <!-- 校验码 -->
                ,check_code=#{inDto.checkCode}
                <!-- OMS号码 -->
                ,oms_code=#{inDto.omsCode}
                <!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
                ,is_show=#{inDto.isShow}
                <!-- 定金优惠 -->
                ,handsel_off=#{inDto.handselOff}
                <!-- 直降优惠 -->
                ,plummet_off=#{inDto.plummetOff}
                <!-- 特批优惠 -->
                ,special_off=#{inDto.specialOff}
                <!-- 特批 -->
                ,special=#{inDto.special}
                <!-- 套餐 -->
                ,meal=#{inDto.meal}
                <!-- 红卡 -->
                ,red_card=#{inDto.redCard}
                <!-- 市调红 -->
                ,market_red=#{inDto.marketRed}
                <!-- 市调蓝 -->
                ,market_blue=#{inDto.marketBlue}
                <!-- 非标2 -->
                ,non_standard2=#{inDto.nonStandard2}
                <!-- 直降 -->
                ,plummet=#{inDto.plummet}
                <!-- 用券 -->
                ,use_ticket=#{inDto.useTicket}
                <!-- 优惠 -->
                ,discount=#{inDto.discount}
                <!-- 总额蓝 -->
                ,total_blue=#{inDto.totalBlue}
                <!-- 经理卡 -->
                ,manager_card=#{inDto.managerCard}
                <!-- 套餐机型 -->
                ,meal_model=#{inDto.mealModel}
                <!-- 积分 -->
                ,points=#{inDto.points}
                <!-- 返券 -->
                ,return_ticket=#{inDto.returnTicket}
                <!-- 收返券/返利卡 -->
                ,col_return_ticket=#{inDto.colReturnTicket}
                <!-- 电子券/积分 -->
                ,e_ticket=#{inDto.eTicket}
                <!-- 提货卡/联心卡/福卡 -->
                ,delivery_card=#{inDto.deliveryCard}
                <!-- 返券2 -->
                ,return_ticket2=#{inDto.returnTicket2}
                <!-- 签售卡 -->
                ,sig_card=#{inDto.sigCard}
                <!-- 财富增值卡 -->
                ,wealth_card=#{inDto.wealthCard}
                <!-- 二次券 -->
                ,tow_voucher=#{inDto.towVoucher}
                <!-- 赠品 -->
                ,gift=#{inDto.gift}
                <!-- 延保 -->
                ,delay_guarantee=#{inDto.delayGuarantee}
                <!-- 其他促销费 -->
                ,other_promotion=#{inDto.otherPromotion}
                <!-- 商品序号 -->
                ,`sort`=#{inDto.sort}
                <!-- uuid -->
                ,uuid=#{inDto.uuid}
                <choose>
                    <when test="inDto.flag !=null and inDto.flag==2 and inDto.needConsume==1 and inDto.cardId !=null and inDto.cardNo !=null">
                        <!-- 优惠券 -->
                        ,card_no=#{inDto.cardNo}
                        <!-- 优惠券id -->
                        ,card_id=#{inDto.cardId}
                    </when>
                    <when test="inDto.flag !=null and (inDto.flag==1 or (inDto.flag==2 and inDto.needConsume==0))">
                        <!-- 优惠券 -->
                        ,card_no=#{inDto.cardNo}
                        <!-- 优惠券id -->
                        ,card_id=#{inDto.cardId}
                    </when>
                </choose>
                ,card_name=#{inDto.cardName}
                ,bar_code=#{inDto.barCode}
            </set>
            WHERE goods_declaration_id=#{inDto.goodsDeclarationId} AND is_deleted=0
        </if>
    </update>
    <!-- 修改商品表(临时表)订单id -->
    <update id="updateGoodsTemp" parameterType="goodsTempEntity">
        UPDATE
        <choose>
            <when test="flag !=null and flag==2">
                omscenter.goods_declaration
            </when>
            <otherwise>
                omscenter.goods_declaration_temp
            </otherwise>
        </choose>
        <set>
            modified_date=now(),modified_by=#{modifiedBy}
            <if test="orderId != null"><!-- 订单id -->
                ,order_id=#{orderId}
            </if>
            <if test="goodsCategoryId != null"><!-- 商品分类 -->
                ,goods_category_id=#{goodsCategoryId}
            </if>
            <if test="goodsId != null"><!-- 商品id -->
                ,goods_id=#{goodsId}
            </if>
            <if test="goodsName != null"><!-- 商品名称 -->
                ,goods_name=#{goodsName}
            </if>
            <if test="goodsPrice != null"><!-- 单价 -->
                ,goods_price=abs(#{goodsPrice})
            </if>
            <if test="goodsNum != null"><!-- 数量 -->
                ,goods_num=abs(#{goodsNum})
            </if>
            <if test="goodsTotalPrice != null"><!-- 商品金额 -->
                ,goods_total_price=abs(#{goodsTotalPrice})
            </if>
            <if test="goodsImage != null "><!-- 商品图片 -->
                ,goods_image=#{goodsImage}
            </if>
            <if test="goodsCode != null"><!-- 商品编号 -->
                ,goods_code=#{goodsCode}
            </if>
            <if test="goodsNote != null"><!-- 商品备注 -->
                ,goods_note=#{goodsNote}
            </if>
            <if test="goodsType != null"><!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
                ,goods_type=#{goodsType}
            </if>
            <if test="isGift != null"><!-- 是否赠品 1：赠品；0：非赠品 -->
                ,is_gift=#{isGift}
            </if>
            <if test="sort != null"><!-- 商品序号 -->
                ,`sort`=#{sort}
            </if>
            <if test="uuid != null"><!-- uuid -->
                ,`uuid`=#{uuid}
            </if>
            <choose>
                <when test="locationType !=null">
                    ,location_type=#{locationType}
                </when>
                <otherwise>
                    ,location_type=1
                </otherwise>
            </choose>
            <if test="originalGoodId !=null"><!--原商品行id-->
                ,original_goodId=#{originalGoodId}
            </if>
            <if test="code != null">
                ,code = #{code}
            </if>
            <if test="deliveryDate != null and deliveryDate != ''">
                ,delivery_date = #{deliveryDate}
            </if>
            <if test="lowestPrice != null">
                ,lowest_price = #{lowestPrice}
            </if>
            <if test="companyLowestPrice != null">
                ,company_lowest_price = #{companyLowestPrice}
            </if>
            <if test="storeLowestPrice != null">
                ,store_lowest_price = #{storeLowestPrice}
            </if>
            <if test="costEstimate != null">
                ,cost_estimate = #{costEstimate}
            </if>
            <if test="online != null">
                ,online = #{online}
            </if>
            ,trade_in_flag = #{tradeInFlag}
            ,goods_national_code = #{goodsNationalCode}
            ,discount_type = #{discountType}
            ,energy_class = #{energyClass}
            ,discount_code = #{discountCode}
            ,cost_short_fall = #{costShortFall}
        </set>
        WHERE id=#{id} AND is_deleted=0
    </update>

    <!-- 修改产品明细表订单id(临时表) -->
    <update id="updateTempGoodsSuppleOrderId">
        <if test="inDto !=null">
            UPDATE omscenter.goods_declaration_supple_temp
            <set>
                modified_by=#{modifiedBy},modified_date=now()
                <!-- 订单id -->
                ,order_id=#{inDto.orderId}
                <if test="inDto.uuid !=null and inDto.uuid !=''">,uuid=#{inDto.uuid}</if>
            </set>
            WHERE goods_declaration_id=#{inDto.id} AND is_deleted=0
        </if>
    </update>

    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsTempByUUID" resultType="goodsTempEntity">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_category_id goodsCategoryId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_code        goodsCode,
               go.goods_price       goodsPrice,
               go.goods_num         goodsNum,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               sort,
               go.goods_type        goodsType,
               goods_note           goodsNote,
               is_gift              isGift,
               uuid,
               go.location_type     locationType
                ,
               go.delivery_date     deliveryDate,
               go.created_by        createdBy,
               go.created_date      createdDate,
               go.modified_by       modifiedBy,
               go.modified_date     modifiedDate,
               go.source_guid       sourceGuid
                ,go.original_goodId originalGoodId,
                go.code             code,
                go.lowest_price     lowestPrice,
                go.company_lowest_price     companyLowestPrice,
                go.store_lowest_price     storeLowestPrice,
                go.cost_estimate    costEstimate,
                go.online             online,
                go.sub_order_code     subOrderCode,
                go.sc_item_id       scItemId,
                go.sc_item_name     scItemName,
                go.refund_id     refundId,
                go.refund_reason     refundReason,
                go.item_amount     itemAmount,
                go.group_code     groupCode,
                go.out_line_id     outLineId,
                go.express     express,
                go.waybill_no     waybillNo,
                go.trade_in_flag     tradeInFlag,
                go.goods_national_code     goodsNationalCode,
                go.discount_type     discountType,
                go.energy_class     energyClass,
                go.discount_code     discountCode,
                go.cost_short_fall     costShortFall
        FROM omscenter.goods_declaration_temp go
        WHERE is_deleted = 0
          AND is_sync = 0
          AND go.uuid = #{uuid}
        ORDER BY `sort` ASC
    </select>


    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsByUUID" resultType="goodsTempEntity">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_category_id goodsCategoryId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_code        goodsCode,
               go.goods_price       goodsPrice,
               go.goods_num         goodsNum,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               sort,
               go.goods_type        goodsType,
               goods_note           goodsNote,
               is_gift              isGift,
               uuid,
               go.location_type     locationType
                ,
               go.delivery_date     deliveryDate,
               go.created_by        createdBy,
               go.created_date      createdDate,
               go.modified_by       modifiedBy,
               go.modified_date     modifiedDate,
               go.source_guid       sourceGuid
                ,go.original_goodId originalGoodId,
                go.code             code,
                go.lowest_price     lowestPrice,
                go.cost_estimate             costEstimate,
                go.online             online,
               go.trade_in_flag     tradeInFlag
        FROM omscenter.goods_declaration go
        WHERE is_deleted = 0
          AND go.uuid = #{uuid}
        ORDER BY `sort` ASC
    </select>

    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsTempByOrderId" resultType="goodsTempEntity">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_category_id goodsCategoryId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_code        goodsCode,
               go.goods_price       goodsPrice,
               go.goods_num         goodsNum,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               sort,
               go.goods_type        goodsType,
               goods_note           goodsNote,
               is_gift              isGift,
               uuid,
               go.location_type     locationType
                ,
               go.delivery_date     deliveryDate,
               go.created_by        createdBy,
               go.created_date      createdDate,
               go.modified_by       modifiedBy,
               go.modified_date     modifiedDate,
               go.source_guid       sourceGuid
                ,go.original_goodId originalGoodId,
               go.code             code
        FROM omscenter.goods_declaration_temp go
        WHERE is_deleted = 0
          AND go.order_id = #{orderId}
        ORDER BY `sort` ASC
    </select>

    <sql id="findGoodsSql">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_category_id goodsCategoryId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_code        goodsCode,
               go.goods_price       goodsPrice,
               go.goods_num         goodsNum,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               sort,
               go.goods_type        goodsType,
               goods_note           goodsNote,
               is_gift              isGift,
               uuid,
               go.location_type     locationType,
               go.delivery_date     deliveryDate,
               go.created_by        createdBy,
               go.created_date      createdDate,
               go.modified_by       modifiedBy,
               go.modified_date     modifiedDate,
               go.guid       guid,
               go.source_guid       sourceGuid,
               go.original_goodId originalGoodId,
               go.code             code,
               go.code             goodsModel,
               go.sc_item_id scItemId,
               go.sc_item_name scItemName,
               go.item_amount itemAmount,
               go.group_code groupCode
        FROM omscenter.goods_declaration go
    </sql>

    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsByOrderId2" resultType="goodsTempEntity">
        <include refid="findGoodsSql" />
        WHERE is_deleted = 0
          AND go.order_id = #{orderId}
        ORDER BY `sort` ASC
    </select>

    <select id="findGoodsByLineIdList" resultType="goodsTempEntity">
        <include refid="findGoodsSql" />
        WHERE is_deleted = 0
        AND go.id in
        <foreach collection="lineIdList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        ORDER BY `sort` ASC
    </select>

    <!-- 根据商品表id查询商品补充信息(临时表) -->
    <select id="findGoodsSuppleTempByGDIdList" resultType="goodsSuppleTempEntity">
        SELECT id, goods_declaration_id goodsDeclarationId,pos_code posCode,ticket_code ticketCode,check_code
        checkCode,oms_code omsCode,is_show isShow
        ,handsel_off handselOff,plummet_off plummetOff,special_off specialOff,special special,meal meal,red_card
        redCard,market_red marketRed
        ,market_blue marketBlue,non_standard2 nonStandard2,plummet plummet,use_ticket useTicket,discount
        discount,total_blue totalBlue,manager_card managerCard
        ,meal_model mealModel,points points,return_ticket returnTicket,col_return_ticket colReturnTicket,e_ticket
        eTicket,delivery_card deliveryCard
        ,return_ticket2 returnTicket2,sig_card sigCard,wealth_card wealthCard,tow_voucher towVoucher,gift
        gift,delay_guarantee delayGuarantee,other_promotion otherPromotion,
        order_id orderId,goods_id goodsId,sort,card_id cardId,card_no cardNo,uuid,is_consume isConsume,
        created_by createdBy,created_date createdDate,modified_by modifiedBy,modified_date modifiedDate,card_name
        cardName,bar_code barCode
        FROM omscenter.goods_declaration_supple_temp
        WHERE is_deleted=0
        AND goods_declaration_id IN
        <foreach collection="goodsDeclarationIdList" item="gdId" open="(" separator="," close=")">
            #{gdId}
        </foreach>
    </select>

    <!-- 根据订单id删除正式表中的商品 -->
    <update id="delGoodsByOrderId2">

        <choose>
            <when test="flag !=null and flag==2">
                UPDATE omscenter.goods_declaration
            </when>
            <otherwise>
                UPDATE omscenter.goods_declaration_temp
            </otherwise>
        </choose>
        SET is_deleted=1,modified_date=now(),modified_by=#{modifiedBy}
        WHERE order_id=#{orderId}
        <if test="goodsDeclarationIdList !=null and goodsDeclarationIdList.size>0">
            AND id NOT IN
            <foreach collection="goodsDeclarationIdList" item="gdId" open="(" separator="," close=")">
                #{gdId}
            </foreach>
        </if>
    </update>

    <!-- 根据订单id删除正式表中的商品补充信息 -->
    <update id="delGoodsSuppleByOrderId">
        <choose>
            <when test="flag !=null and flag==2">
                UPDATE omscenter.goods_declaration_supple
            </when>
            <otherwise>
                UPDATE omscenter.goods_declaration_supple_temp
            </otherwise>
        </choose>
        SET is_deleted=1,modified_date=now(),modified_by=#{modifiedBy}
        WHERE order_id=#{orderId}
        <if test="goodsDeclarationIdList !=null and goodsDeclarationIdList.size>0">
            AND goods_declaration_id NOT IN
            <foreach collection="goodsDeclarationIdList" item="gdId" open="(" separator="," close=")">
                #{gdId}
            </foreach>
        </if>
    </update>

    <!-- 修改商品表(临时表)订单id -->
    <select id="findGoodsTemp" resultType="goodsTempEntity">
        SELECT id,goods_note goodsNote,location_type locationType,
        goods_national_code goodsNationalCode,
        discount_type discountType,
        energy_class energyClass,
        discount_code discountCode
        <choose>
            <when test="flag !=null and flag==2">
                ,2 'flag'
                FROM omscenter.goods_declaration
            </when>
            <otherwise>
                ,1 'flag'
                FROM omscenter.goods_declaration_temp
            </otherwise>
        </choose>
        WHERE id=#{goodsDeclarationId} AND is_deleted=0
    </select>


    <!-- 修改订单是否需要同步字段 -->
    <update id="updateOrderIsToDrp">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now(),modified_by=#{modifiedBy}
            <if test="isToDrp !=null ">,is_to_drp=#{isToDrp}</if>
        </set>
        WHERE id =#{orderId}
    </update>
    <!-- 根据设计师编码或家装公司编码查询订单列表count查询 -->
    <select id="selectDecOrderByDesignerCodeOrDecorateCompanyCodeCount2" resultType="java.util.Map">
        <choose>
            <when test="inDto.decorateCompanyType==1 and inDto.decOrDesiList !=null and inDto.decOrDesiList.size>0"><!-- 家装 -->
                SELECT SUM(CASE stage WHEN 1 THEN 1 ELSE 0 END ) noAudited,SUM(CASE stage WHEN 2 THEN 1 ELSE 0 END )
                audited
                FROM (
                SELECT o.id,o.stage
                FROM omscenter.declaration_info o
                <if test="inDto.designerCodeList !=null and inDto.designerCodeList.size>0">
                    inner join
                    <include refid="designerCodeList"/>
                    on designer.designerCode=o.designer_code
                </if>
                <if test="inDto.decorateCompanyCodeList !=null and inDto.decorateCompanyCodeList.size>0">
                    inner join
                    <include refid="decorateCodeList"/>
                    on decorate.decorateCode=o.decorate_company_code
                </if>
                <if test="inDto.decorateCompanyType==1 and inDto.decOrDesiList !=null and inDto.decOrDesiList.size>0">
                    inner join
                    <include refid="decOrDesiList"/>
                    on (
                    decOrDes.decorateCompanyCode=o.decorate_company_code
                    OR decOrDes.designerCode=o.designer_code)
                </if>
                <include refid="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE"/>
                ) oo
            </when>
            <otherwise>
                SELECT SUM(CASE o.stage WHEN 1 THEN 1 ELSE 0 END ) noAudited,SUM(CASE o.stage WHEN 2 THEN 1 ELSE 0 END )
                audited
                FROM omscenter.declaration_info o
                <if test="inDto.designerCodeList !=null and inDto.designerCodeList.size>0">
                    inner join
                    <include refid="designerCodeList"/>
                    on designer.designerCode=o.designer_code
                </if>
                <if test="inDto.decorateCompanyCodeList !=null and inDto.decorateCompanyCodeList.size>0">
                    inner join
                    <include refid="decorateCodeList"/>
                    on decorate.decorateCode=o.decorate_company_code
                </if>
                <include refid="findDecOrderByDesignerCodeOrDecorateCompanyCodeWHERE"/>
            </otherwise>
        </choose>

    </select>

    <!-- 预审核 -->
    <update id="preAudit">
        UPDATE omscenter.declaration_info
        SET
            version =  version + 1,
            modified_date=now(),modified_by=#{modifiedBy}
        <if test="inDto.areaCode != null and inDto.areaCode != ''">
            ,area_code=#{inDto.areaCode}
        </if>
        <if test="inDto.areaName != null and inDto.areaName != ''">
            ,area_name=#{inDto.areaName}
        </if>
        <choose>
            <when test="inDto.status !=null and inDto.status==0"><!-- 预审核拒绝 -->
                ,stage=3
            </when>
            <otherwise><!-- 预审核通过 -->
                ,app_pre_audit=2,app_pre_examine_date=now(),app_pre_examine=0
            </otherwise>
        </choose>
        WHERE is_deleted=0 AND id =#{inDto.orderId}
    </update>

    <!-- 修改关联家装异业三工 -->
    <update id="updateDecorate">
        UPDATE omscenter.declaration_info
        SET modified_date=now(),modified_by=#{modifiedBy}
        <!-- 家装公司编码 -->
        ,decorate_company_code=#{inDto.decorateCompanyCode}
        <!-- 家装公司名称 -->
        ,decorate_company_name=#{inDto.decorateCompanyName}
        <!-- 设计师编码 -->
        ,designer_code=#{inDto.designerCode}
        <!-- 设计师名称 -->
        ,designer_name=#{inDto.designerName}
        <!-- 设计师手机号 -->
        ,designer_phone=#{inDto.designerPhone}
        WHERE is_deleted=0 AND id =#{inDto.orderId}
    </update>

    <sql id="getCodes">
        (
        <foreach collection="stats.codes" item="c" separator="union">
            select @code:=#{c} as code
        </foreach>
        ) as dcCodes
    </sql>

    <select id="getDecOrderStats"
            parameterType="com.fotile.omscenter.decOrder.pojo.dto.DecOrderStatsInDto"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.DecOrderStatsDto">
        select
        <choose>
            <when test="stats.decorateCompanyType==3">
                di.decorate_company_code as decorateCompanyCode,
                di.designer_code as designerCode,
            </when>
            <otherwise>
                di.decorate_company_code as decorateCompanyCode,
            </otherwise>
        </choose>
        count(di.id) as orderCount,
        sum(ifnull(di.goods_num,0)) as totalNum,
        sum(ifnull(di.goods_amount,0)) as totalAmount
        from declaration_info di
        <choose>
            <when test="stats.decorateCompanyType==1"><!--家装公司-->
                inner join
                <include refid="getCodes"/>
                on di.decorate_company_code=dcCodes.code
            </when>
            <when test="stats.decorateCompanyType==2"><!--异业三工-->
                inner join
                <include refid="getCodes"/>
                on di.decorate_company_code=dcCodes.code
            </when>
            <when test="stats.decorateCompanyType==3"><!--设计师-->
                inner join
                <include refid="getCodes"/>
                on di.designer_code=dcCodes.code
            </when>
        </choose>
        and di.is_deleted=0
        <if test="stats.getAllOrders == null or stats.getAllOrders==0">
            and di.stage=2
            and di.order_type=101
        </if>
        <if test="stats.createdStartDate != null">
            and di.created_date >= #{stats.createdStartDate}
        </if>
        <if test="stats.createdEndDate != null">
            and di.created_date &lt;= #{stats.createdEndDate}
        </if>

        <choose>
            <when test="stats.decorateCompanyType==3">
                group by di.decorate_company_code,di.designer_code
            </when>
            <otherwise>
                group by di.decorate_company_code
            </otherwise>
        </choose>
    </select>

    <select id="getOrderGoodsLinesByPhone" parameterType="com.fotile.omscenter.decOrder.pojo.FssGetDrpGoodsInfoDto"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.DrpOrderListInfo">
        SELECT d.order_stage       status,
               g.goods_category_id cateId,
               d.drp_orderId       orderno,
               g.goods_code        itemnamefull,
               d.store_name        shopname,
               g.goods_price       pricesettle,
               g.logistics_status  transstatus,
               d.created_date      orderdate,
               d.delivery_date     needdate,
               d.input_name        createdname,
               d.goods_num         num,
               g.goods_name        goodsName,
               g.goods_id          goodsId
        FROM declaration_info d,
             goods_declaration g
        WHERE d.id = g.order_id
          AND d.stage = 2
          AND d.drp_stage = 2
          AND d.contact_mobile = #{phoneinfo,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
        order by g.created_date ${sort}
        limit #{offset},#{size}
    </select>

    <select id="getOrderGoodsLinesByPhoneCount" parameterType="com.fotile.omscenter.decOrder.pojo.FssGetDrpGoodsInfoDto"
            resultType="long">
        SELECT count(1)
        FROM declaration_info d,
             goods_declaration g
        WHERE d.id = g.order_id
          AND d.stage = 2
          AND d.drp_stage = 2
          AND d.contact_mobile = #{phoneinfo,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
    </select>

    <!-- 批量插入商品临时表 -->
    <insert id="addBatchGoodsTemp" parameterType="goodsTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <!-- 新增产品至临时表中-->
        omscenter.goods_declaration_temp
        (   <!-- 订单id -->
        order_id,
        <!-- 商品分类 -->
        goods_category_id,
        <!-- 商品id -->
        goods_id,
        <!-- 商品名称 -->
        goods_name,
        <!-- 单价 -->
        goods_price,
        <!-- 数量 -->
        goods_num,
        <!-- 商品金额 -->
        goods_total_price,
        <!-- 商品图片 -->
        goods_image,
        <!-- 商品编号 -->
        goods_code,
        <!-- 商品备注 -->
        goods_note,
        <!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
        goods_type,
        <!-- 是否赠品 1：赠品；0：非赠品 -->
        is_gift,
        <!-- 商品序号 -->
        `sort`,
        <!-- uuid -->
        `uuid`,
        location_type,
        created_by,created_date,modified_by,modified_date,
        delivery_date,
        source_guid
        <!--原商品行id-->
            ,original_goodId,code,
        lowest_price,company_lowest_price,store_lowest_price,cost_estimate,online,sub_order_code,sc_item_id,sc_item_name,
        refund_id,refund_reason,item_amount,group_code,out_line_id,express,waybill_no,trade_in_flag,goods_national_code,
        discount_type,energy_class,discount_code,cost_short_fall
        )
        VALUES
        <foreach collection="list" item="g" separator=",">
            (<!-- 订单id -->
            #{g.orderId},
            <!-- 商品分类 -->
            #{g.goodsCategoryId},
            <!-- 商品id -->
            #{g.goodsId},
            <!-- 商品名称 -->
            #{g.goodsName},
            <!-- 单价 -->
            abs(#{g.goodsPrice}),
            <!-- 数量 -->
            abs(#{g.goodsNum}),
            <!-- 商品金额 -->
            abs(#{g.goodsTotalPrice}),
            <!-- 商品图片 -->
            #{g.goodsImage},
            <!-- 商品编号 -->
            #{g.goodsCode},
            <!-- 商品备注 -->
            #{g.goodsNote},
            <!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
            #{g.goodsType},
            <!-- 是否赠品 1：赠品；0：非赠品 -->
            #{g.isGift},
            <!-- 商品序号 -->
            #{g.sort},
            <!-- uuid -->
            #{g.uuid},
            #{g.locationType},
            #{g.modifiedBy},now(),#{g.modifiedBy},now(),
            <choose>
                <when test="g.deliveryDate != null and g.deliveryDate != ''">
                    #{g.deliveryDate},
                </when>
                <otherwise>null,</otherwise>
            </choose>
            #{g.sourceGuid}
            <!--原商品行id-->
            ,#{g.originalGoodId},#{g.code},
            #{g.lowestPrice},#{g.companyLowestPrice},#{g.storeLowestPrice},#{g.costEstimate},#{g.online},#{g.subOrderCode},
             #{g.scItemId},#{g.scItemName},#{g.refundId},#{g.refundReason},
             #{g.itemAmount},#{g.groupCode},#{g.outLineId},#{g.express},#{g.waybillNo},#{g.tradeInFlag},#{g.goodsNationalCode},
             #{g.discountType},#{g.energyClass},#{g.discountCode},#{g.costShortFall}
            )
        </foreach>
    </insert>

    <!-- 进正式表 -->
    <insert id="addBatchGoods" parameterType="goodsTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <!-- 新增产品至正式表中 -->
        omscenter.goods_declaration
        (   <!-- 订单id -->
        order_id,
        <!-- 商品分类 -->
        goods_category_id,
        <!-- 商品id -->
        goods_id,
        <!-- 商品名称 -->
        goods_name,
        <!-- 单价 -->
        goods_price,
        <!-- 数量 -->
        goods_num,
        <!-- 商品金额 -->
        goods_total_price,
        <!-- 商品图片 -->
        goods_image,
        <!-- 商品编号 -->
        goods_code,
        <!-- 商品备注 -->
        goods_note,
        <!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
        goods_type,
        <!-- 是否赠品 1：赠品；0：非赠品 -->
        is_gift,
        <!-- 商品序号 -->
        `sort`,
        <!-- uuid -->
        `uuid`,
        `guid`,
        location_type,
        created_by,created_date,modified_by,modified_date,delivery_date,source_guid,original_goodId,code,
        lowest_price,company_lowest_price,store_lowest_price,cost_estimate,online,temp_id,sub_order_code,sc_item_id,sc_item_name,
        refund_id,refund_reason,item_amount,group_code,out_line_id,express,waybill_no,trade_in_flag,goods_national_code,
        discount_type,energy_class,discount_code,cost_short_fall
        )
        VALUES
        <foreach collection="list" item="g" separator=",">
            (<!-- 订单id -->
            #{g.orderId},
            <!-- 商品分类 -->
            #{g.goodsCategoryId},
            <!-- 商品id -->
            #{g.goodsId},
            <!-- 商品名称 -->
            #{g.goodsName},
            <!-- 单价 -->
            abs(#{g.goodsPrice}),
            <!-- 数量 -->
            abs(#{g.goodsNum}),
            <!-- 商品金额 -->
            abs(#{g.goodsTotalPrice}),
            <!-- 商品图片 -->
            #{g.goodsImage},
            <!-- 商品编号 -->
            #{g.goodsCode},
            <!-- 商品备注 -->
            #{g.goodsNote},
            <!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
            #{g.goodsType},
            <!-- 是否赠品 1：赠品；0：非赠品 -->
            #{g.isGift},
            <!-- 商品序号 -->
            #{g.sort},
            <!-- uuid -->
            #{g.uuid},
            CONCAT('HLB-',UUID()),
            <choose>
                <when test="g.locationType !=null">
                    #{g.locationType},
                </when>
                <otherwise>
                    1,
                </otherwise>
            </choose>
            #{g.modifiedBy},now(),#{g.modifiedBy},now(),
            <choose>
                <when test="g.deliveryDate != null and g.deliveryDate != ''">
                    #{g.deliveryDate},
                </when>
                <otherwise>
                    null,
                </otherwise>
            </choose>
            #{g.sourceGuid},
            #{g.originalGoodId},
            #{g.code,jdbcType=VARCHAR},
            #{g.lowestPrice},
            #{g.companyLowestPrice},
            #{g.storeLowestPrice},
            #{g.costEstimate},
            #{g.online},
            #{g.id},
            #{g.subOrderCode},
            #{g.scItemId},
            #{g.scItemName},
            #{g.refundId},
            #{g.refundReason},
            #{g.itemAmount},
            #{g.groupCode},
            #{g.outLineId},
            #{g.express},
            #{g.waybillNo},
            #{g.tradeInFlag},
            #{g.goodsNationalCode},
            #{g.discountType},
            #{g.energyClass},
            #{g.discountCode},
            #{g.costShortFall}
            )
        </foreach>
    </insert>
    <!-- 新增商品补充信息(进临时表) -->
    <insert id="addBatchGoodsSuppleTemp" parameterType="goodsTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <!-- 新增产品补充信息至临时表中-->
        omscenter.goods_declaration_supple_temp
        (
        created_by,created_date,modified_by,modified_date,
        <!-- 订单id -->
        order_id,
        <!-- 商品id -->
        goods_id,
        <!-- POS编码 -->
        pos_code,
        <!-- 小票编码 -->
        ticket_code,
        <!-- 校验码 -->
        check_code,
        <!-- OMS号码 -->
        oms_code,
        <!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
        is_show,
        <!-- 定金优惠 -->
        handsel_off,
        <!-- 直降优惠 -->
        plummet_off,
        <!-- 特批优惠 -->
        special_off,
        <!-- 特批 -->
        special,
        <!-- 套餐 -->
        meal,
        <!-- 红卡 -->
        red_card,
        <!-- 市调红 -->
        market_red,
        <!-- 市调蓝 -->
        market_blue,
        <!-- 非标2 -->
        non_standard2,
        <!-- 直降 -->
        plummet,
        <!-- 用券 -->
        use_ticket,
        <!-- 优惠 -->
        discount,
        <!-- 总额蓝 -->
        total_blue,
        <!-- 经理卡 -->
        manager_card,
        <!-- 套餐机型 -->
        meal_model,
        <!-- 积分 -->
        points,
        <!-- 返券 -->
        return_ticket,
        <!-- 收返券/返利卡 -->
        col_return_ticket,
        <!-- 电子券/积分 -->
        e_ticket,
        <!-- 提货卡/联心卡/福卡 -->
        delivery_card,
        <!-- 返券2 -->
        return_ticket2,
        <!-- 签售卡 -->
        sig_card,
        <!-- 财富增值卡 -->
        wealth_card,
        <!-- 二次券 -->
        tow_voucher,
        <!-- 赠品 -->
        gift,
        <!-- 延保 -->
        delay_guarantee,
        <!-- 其他促销费 -->
        other_promotion,
        <!-- 商品序号 -->
        `sort`,
        <!-- uuid -->
        `uuid`,
        <!-- 优惠券id -->
        `card_id`,`card_no`,
        <!-- goods_declaration表的id -->
        goods_declaration_id,
        <!-- 是否核销 0：否；1：是 -->
        is_consume,
        card_name,
        bar_code)
        VALUES
        <foreach collection="list" item="g" separator=",">
            (#{g.modifiedBy},now(),#{g.modifiedBy},now(),
            <!-- 订单id -->
            #{g.orderId},
            <!-- 商品id -->
            #{g.goodsId},
            <!-- POS编码 -->
            #{g.goodsSuppleTempEntity.posCode},
            <!-- 小票编码 -->
            #{g.goodsSuppleTempEntity.ticketCode},
            <!-- 校验码 -->
            #{g.goodsSuppleTempEntity.checkCode},
            <!-- OMS号码 -->
            #{g.goodsSuppleTempEntity.omsCode},
            <!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
            #{g.goodsSuppleTempEntity.isShow},
            <!-- 定金优惠 -->
            null,
            <!-- 直降优惠 -->
            null,
            <!-- 特批优惠 -->
            null,
            <!-- 特批 -->
            null,
            <!-- 套餐 -->
            null,
            <!-- 红卡 -->
            null,
            <!-- 市调红 -->
            null,
            <!-- 市调蓝 -->
            #{g.goodsSuppleTempEntity.marketBlue},
            <!-- 非标2 -->
            null,
            <!-- 直降 -->
            #{g.goodsSuppleTempEntity.plummet},
            <!-- 用券 -->
            null,
            <!-- 优惠 -->
            #{g.goodsSuppleTempEntity.discount},
            <!-- 总额蓝 -->
            null,
            <!-- 经理卡 -->
            null,
            <!-- 套餐机型 -->
            null,
            <!-- 积分 -->
            null,
            <!-- 返券 -->
            #{g.goodsSuppleTempEntity.returnTicket},
            <!-- 收返券/返利卡 -->
            null,
            <!-- 电子券/积分 -->
            null,
            <!-- 提货卡/联心卡/福卡 -->
            null,
            <!-- 返券2 -->
            null,
            <!-- 签售卡 -->
            null,
            <!-- 财富增值卡 -->
            null,
            <!-- 二次券 -->
            #{g.goodsSuppleTempEntity.towVoucher},
            <!-- 赠品 -->
            #{g.goodsSuppleTempEntity.gift},
            <!-- 延保 -->
            #{g.goodsSuppleTempEntity.delayGuarantee},
            <!-- 其他促销费 -->
            #{g.goodsSuppleTempEntity.otherPromotion},
            <!-- 商品序号 -->
            #{g.goodsSuppleTempEntity.sort},
            <!-- uuid -->
            #{g.goodsSuppleTempEntity.uuid},
            <!-- 优惠券id -->
            #{g.goodsSuppleTempEntity.cardId},#{g.goodsSuppleTempEntity.cardNo},
            <!-- goods_declaration表的id -->
            #{g.id},
            <!-- 是否核销 0：否；1：是 -->
            <choose>
                <when test="g.goodsSuppleTempEntity.isConsume != null">
                    #{g.goodsSuppleTempEntity.isConsume},
                </when>
                <otherwise>0,</otherwise>
            </choose>
            #{g.goodsSuppleTempEntity.cardName},
            #{g.goodsSuppleTempEntity.barCode}
            )
        </foreach>
    </insert>

    <insert id="addBatchGoodsSupple" parameterType="goodsSuppleTempEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <!-- 新增产品补充信息至正式表中 -->
        omscenter.goods_declaration_supple
        (
        created_by,created_date,modified_by,modified_date,
        <!-- 订单id -->
        order_id,
        <!-- 商品id -->
        goods_id,
        <!-- POS编码 -->
        pos_code,
        <!-- 小票编码 -->
        ticket_code,
        <!-- 校验码 -->
        check_code,
        <!-- OMS号码 -->
        oms_code,
        <!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
        is_show,
        <!-- 定金优惠 -->
        handsel_off,
        <!-- 直降优惠 -->
        plummet_off,
        <!-- 特批优惠 -->
        special_off,
        <!-- 特批 -->
        special,
        <!-- 套餐 -->
        meal,
        <!-- 红卡 -->
        red_card,
        <!-- 市调红 -->
        market_red,
        <!-- 市调蓝 -->
        market_blue,
        <!-- 非标2 -->
        non_standard2,
        <!-- 直降 -->
        plummet,
        <!-- 用券 -->
        use_ticket,
        <!-- 优惠 -->
        discount,
        <!-- 总额蓝 -->
        total_blue,
        <!-- 经理卡 -->
        manager_card,
        <!-- 套餐机型 -->
        meal_model,
        <!-- 积分 -->
        points,
        <!-- 返券 -->
        return_ticket,
        <!-- 收返券/返利卡 -->
        col_return_ticket,
        <!-- 电子券/积分 -->
        e_ticket,
        <!-- 提货卡/联心卡/福卡 -->
        delivery_card,
        <!-- 返券2 -->
        return_ticket2,
        <!-- 签售卡 -->
        sig_card,
        <!-- 财富增值卡 -->
        wealth_card,
        <!-- 二次券 -->
        tow_voucher,
        <!-- 赠品 -->
        gift,
        <!-- 延保 -->
        delay_guarantee,
        <!-- 其他促销费 -->
        other_promotion,
        <!-- 商品序号 -->
        `sort`,
        <!-- uuid -->
        `uuid`,
        <!-- 优惠券id -->
        `card_id`,`card_no`,
        <!-- goods_declaration表的id -->
        goods_declaration_id,
        <!-- 是否核销 0：否；1：是 -->
        is_consume,
        card_name,bar_code)
        VALUES
        <foreach collection="list" item="g" separator=",">
            (#{g.modifiedBy},now(),#{g.modifiedBy},now(),
            <!-- 订单id -->
            #{g.orderId},
            <!-- 商品id -->
            #{g.goodsId},
            <!-- POS编码 -->
            #{g.goodsSuppleTempEntity.posCode},
            <!-- 小票编码 -->
            #{g.goodsSuppleTempEntity.ticketCode},
            <!-- 校验码 -->
            #{g.goodsSuppleTempEntity.checkCode},
            <!-- OMS号码 -->
            #{g.goodsSuppleTempEntity.omsCode},
            <!-- 是否样机 0:否；1：库房样机；2：门店样机;3:上样/出样 -->
            #{g.goodsSuppleTempEntity.isShow},
            <!-- 定金优惠 -->
            null,
            <!-- 直降优惠 -->
            null,
            <!-- 特批优惠 -->
            null,
            <!-- 特批 -->
            null,
            <!-- 套餐 -->
            null,
            <!-- 红卡 -->
            null,
            <!-- 市调红 -->
            null,
            <!-- 市调蓝 -->
            #{g.goodsSuppleTempEntity.marketBlue},
            <!-- 非标2 -->
            null,
            <!-- 直降 -->
            #{g.goodsSuppleTempEntity.plummet},
            <!-- 用券 -->
            null,
            <!-- 优惠 -->
            #{g.goodsSuppleTempEntity.discount},
            <!-- 总额蓝 -->
            null,
            <!-- 经理卡 -->
            null,
            <!-- 套餐机型 -->
            null,
            <!-- 积分 -->
            null,
            <!-- 返券 -->
            #{g.goodsSuppleTempEntity.returnTicket},
            <!-- 收返券/返利卡 -->
            null,
            <!-- 电子券/积分 -->
            null,
            <!-- 提货卡/联心卡/福卡 -->
            null,
            <!-- 返券2 -->
            null,
            <!-- 签售卡 -->
            null,
            <!-- 财富增值卡 -->
            null,
            <!-- 二次券 -->
            #{g.goodsSuppleTempEntity.towVoucher},
            <!-- 赠品 -->
            #{g.goodsSuppleTempEntity.gift},
            <!-- 延保 -->
            #{g.goodsSuppleTempEntity.delayGuarantee},
            <!-- 其他促销费 -->
            #{g.goodsSuppleTempEntity.otherPromotion},
            <!-- 商品序号 -->
            #{g.goodsSuppleTempEntity.sort},
            <!-- uuid -->
            #{g.uuid},
            <!-- 优惠券id -->
            #{g.goodsSuppleTempEntity.cardId},#{g.goodsSuppleTempEntity.cardNo},
            <!-- goods_declaration表的id -->
            #{g.id},
            <!-- 是否核销 0：否；1：是 -->
            #{g.goodsSuppleTempEntity.isConsume},
            #{g.goodsSuppleTempEntity.cardName},
            #{g.goodsSuppleTempEntity.barCode}
            )
        </foreach>
    </insert>

    <update id="updateBatchGoodsTemp"  >
        <foreach collection="list" item="g"  >
            UPDATE
            <choose>
                <when test="flag !=null and flag==2">
                    omscenter.goods_declaration
                </when>
                <otherwise>
                    omscenter.goods_declaration_temp
                </otherwise>
            </choose>
            <set>
                modified_date=now(),modified_by=#{modifiedBy}
                <if test="orderId != null"><!-- 订单id -->
                    ,order_id=#{orderId}
                </if>
                <if test="g.goodsCategoryId != null"><!-- 商品分类 -->
                    ,goods_category_id=#{g.goodsCategoryId}
                </if>
                <if test="g.goodsId != null"><!-- 商品id -->
                    ,goods_id=#{g.goodsId}
                </if>
                <if test="g.goodsName != null"><!-- 商品名称 -->
                    ,goods_name=#{g.goodsName}
                </if>
                <if test="g.goodsPrice != null"><!-- 单价 -->
                    ,goods_price=abs(#{g.goodsPrice})
                </if>
                <if test="g.goodsNum != null"><!-- 数量 -->
                    ,goods_num=abs(#{g.goodsNum})
                </if>
                <if test="g.goodsTotalPrice != null"><!-- 商品金额 -->
                    ,goods_total_price=abs(#{g.goodsTotalPrice})
                </if>
                <if test="g.goodsImage != null "><!-- 商品图片 -->
                    ,goods_image=#{g.goodsImage}
                </if>
                <if test="g.goodsCode != null"><!-- 商品编号 -->
                    ,goods_code=#{g.goodsCode}
                </if>
                <if test="g.goodsNote != null"><!-- 商品备注 -->
                    ,goods_note=#{g.goodsNote}
                </if>
                <if test="g.goodsType != null"><!-- 商品类型 1-实物单品 2-实物组合 3-虚拟商品 -->
                    ,goods_type=#{g.goodsType}
                </if>
                <if test="g.isGift != null"><!-- 是否赠品 1：赠品；0：非赠品 -->
                    ,is_gift=#{g.isGift}
                </if>
                <if test="g.sort != null"><!-- 商品序号 -->
                    ,`sort`=#{g.sort}
                </if>
                <if test="g.uuid != null"><!-- uuid -->
                    ,`uuid`=#{g.uuid}
                </if>
                <choose>
                    <when test="g.locationType !=null">
                        ,location_type=#{g.locationType}
                    </when>
                    <otherwise>
                        ,location_type=1
                    </otherwise>
                </choose>
                <if test="g.originalGoodId !=null"><!--原商品行id-->
                    ,original_goodId=#{g.originalGoodId}
                </if>
                <if test="g.code != null">
                    ,code = #{g.code}
                </if>
                <if test="g.deliveryDate != null and g.deliveryDate != ''">
                    ,delivery_date = #{g.deliveryDate}
                </if>
                <if test="g.lowestPrice != null">
                    ,lowest_price = #{g.lowestPrice}
                </if>
                <if test="g.companyLowestPrice != null">
                    ,company_lowest_price = #{g.companyLowestPrice}
                </if>
                <if test="g.storeLowestPrice != null">
                    ,store_lowest_price = #{g.storeLowestPrice}
                </if>
                <if test="g.costEstimate != null">
                    ,cost_estimate = #{g.costEstimate}
                </if>
                <if test="g.online != null">
                    ,online = #{g.online}
                </if>
                ,trade_in_flag = #{g.tradeInFlag}
                ,goods_national_code = #{g.goodsNationalCode}
                ,discount_type = #{g.discountType}
                ,energy_class = #{g.energyClass}
                ,discount_code = #{g.discountCode}
                ,cost_short_fall = #{g.costShortFall}
            </set>
            WHERE id=#{g.id} AND is_deleted=0;
        </foreach>
    </update>

    <update id="updateBatchTempGoodsSuppleOrderId">
        <if test="list !=null">
            UPDATE omscenter.goods_declaration_supple_temp
            <set>
                modified_by=#{modifiedBy},modified_date=now()
                <!-- 订单id -->
                ,order_id=#{orderId}
                <if test="uuid !=null and uuid !=''">,uuid=#{uuid}</if>
            </set>
            WHERE is_deleted=0
            AND goods_declaration_id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <!-- 中台创建订单 -->
    <insert id="platAddDecOrder" parameterType="com.fotile.omscenter.decOrder.pojo.dto.AddOrderInDto"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        omscenter.declaration_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金;102:经销/零售销退订单；201：经销上样订单；202：经销退样订单；301：经销样机销售订单；
	   			                              401：换货销售订单；402：换货退货订单；601：KA上样订单；602：KA退样订单；701：KA样机销售订单；702：KA样机销退订单；
	                     			          801：B2B销售订单；802：B2B退货订单 -->
                order_type,
            </if>
            <if test="companyId != null"><!-- 分公司id -->
                company_id,
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                company_name,
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                distributor_id,
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                distributor_name,
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                distributor_code,
            </if>
            <!--***********订单信息******-->
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                adviser_id,
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                adviser_name,
            </if>
            <if test="inputId != null"><!-- 厨电顾问(录入人)ID -->
                input_id,
            </if>
            <if test="inputName != null"><!-- 厨电顾问(录入人)名称 -->
                input_name,
            </if>
            <if test="originalOrderId !=null"><!--原销售订单-->
                original_orderId,
            </if>
            <if test="outOrderId !=null and outOrderId !=''"><!--外部订单号-->
                out_orderId,
            </if>
            <!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 默认审核通过-->
            <if test="stage !=null">
                stage,
            </if>
            <if test="firstReturnReason !=null"><!--一级退货原因id-->
                first_return_reason,
            </if>
            <if test="firstReturnReasonName !=null and firstReturnReasonName !=''"><!--一级退货原因名称-->
                first_return_reason_name,
            </if>
            <if test="secReturnReason !=null"><!--二级退货原因id-->
                sec_return_reason,
            </if>
            <if test="secReturnReasonName !=null and secReturnReasonName !=''"><!--二级退货原因名称-->
                sec_return_reason_name,
            </if>
            <if test="appPreAudit != null "><!--是否在云管理预审核 0：否；1：是 2:预审通过-->
                app_pre_audit,
            </if>
            <if test="OANo!=null and OANo !=''"><!--OA编号-->
                oa_no,
            </if>
            <if test="parentId != null"><!-- 关联定金订单号 -->
                parent_id,
            </if>
            <if test="reason !=null and reason !=''"><!--原因描述-->
                reason,
            </if>
            <if test="examineDate !=null and examineDate !=''"><!--审核时间-->
                examine_date,
            </if>
            <if test="approveStatus !=null and approveStatus !=''"> <!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                approve_status,
            </if>
            <if test="orderDate != null "><!-- 订购时间 -->
                order_date,
            </if>
            <!--********************业务信息*****-->
            <if test="storeId != null"><!-- 门店id -->
                store_id,
            </if>
            <if test="storeType != null"><!-- 门店类型 -->
                store_type,
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                store_name,
            </if>
            <if test="decorateCompanyType != null"><!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
                decorate_company_type,
            </if>
            <if test="cluesId != null"><!-- 线索id -->
                clues_id,
            </if>
            <if test="cluesSalesmanId != null"><!-- 线索负责人关联的业务员id -->
                clues_salesman_id,
            </if>
            <if test="managerId != null"><!-- 客户经理(线索负责人)ID -->
                manager_id,
            </if>
            <if test="managerName != null"><!-- 客户经理(线索负责人)名称 -->
                manager_name,
            </if>
            <if test="orderNote != null"><!-- 其他备注 -->
                order_note,
            </if>
            <if test="decorateCompanyCode != null"><!-- 家装公司编码 -->
                decorate_company_code,
            </if>
            <if test="decorateCompanyName != null"><!-- 家装公司名称 -->
                decorate_company_name,
            </if>
            <if test="designerCode != null"><!-- 设计师编码 -->
                designer_code,
            </if>
            <if test="designerName != null"><!-- 设计师名称 -->
                designer_name,
            </if>
            <if test="designerPhone != null"><!-- 设计师手机号 -->
                designer_phone,
            </if>
            <if test="orderChannel !=null and orderChannel !=''"><!-- 订单渠道 -->
                order_channel,
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!--活动说明-->
                activity_remark,
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                buy_reason,
            </if>
            <if test="cluesSourceCode != null"><!-- 线索来源编码 -->
                clues_source_code,
            </if>
            <if test="cluesSourceName != null"><!-- 线索来源名称 -->
                clues_source_name,
            </if>
            <!--********************配送信息*****-->
            <if test="contactName != null"><!-- 收件人 -->
                contact_name,
            </if>
            <if test="villageId != null"><!-- 小区id -->
                village_id,
            </if>
            <if test="villageName != null"><!-- 小区名称 -->
                village_name,
            </if>
            <if test="storage !=null and storage !=''"><!--默认储位-->
                storage,
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                install_id,
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                contact_mobile,
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                provice_id,
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                provice_name,
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                city_id,
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                city_name,
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                county_id,
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                county_name,
            </if>
            <if test="deliveryDate != null and deliveryDate !=''"><!-- 送货时间 -->
                delivery_date,
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                contact_mobile_bak,
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                delivery_address,
            </if>
            <!--********************结算信息*****-->
            <if test="earnestAmount != null"><!-- 定金金额 -->
                earnest_amount,
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                cash_amount,
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                transfer_amount,
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                card_amount,
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                pos_amount,
            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                goods_amount,
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                goods_num,
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                non_gift_num,
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                non_gift_amount,
            </if>
            <!--********************其他冗余字段*****-->
            <if test="isToDrp != null"><!-- 是否同步至drp -->
                is_to_drp,
            </if>
            <if test="areaCode != null"><!-- 大区编码 -->
                area_code,
            </if>
            <if test="areaName != null"><!-- 大区名称 -->
                area_name,
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                channel_category_code,
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                channel_category_name,
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                channel_subdivide_code,
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                channel_subdivide_name,
            </if>
            <if test="uuid != null "><!-- 订购时间 -->
                uuid,
            </if>
            <if test="isInStore !=null"> <!-- 是否店内成交 0：否；1：是 -->
                is_inStore,
            </if>
            <if test="auditDate !=null and auditDate !=''"><!--云管理审核时间-->
                audit_date,
            </if>
            <if test="businessType !=null and businessType !=''"><!--业务类型 默认值：BBC,根据订单类型传值-->
                business_type,
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                goods_num_canEnter,
            </if>
            <if test="userName != null and userName !=''">
                user_name,
            </if>
            <if test="decorateCategory != null and decorateCategory != ''">
                decorate_category,
            </if>
            <if test="decorateCategoryName != null and decorateCategoryName != ''">
                decorate_category_name,
            </if>
            <if test="toDrpStage != null ">
                to_drp_stage,
            </if>
            <if test="appPreAudit != null and appPreAudit ==2"><!--是否在云管理预审核 0：否；1：是 2:预审通过  预审核通过，给预审核时间-->
                app_pre_examine_date,app_pre_examine,
            </if>
            <if test="isCheckPrice != null "><!--是否价格检查 0：否；1：是-->
                is_check_price,
            </if>
            <if test="cutTime !=null and cutTime !='' ">
                cut_time,
            </if>
            order_create_time,created_by,created_date,modified_by,modified_date,is_from_app,drp_stage,retry_drp_num,sample_type
            <if test="reportDate != null and reportDate != ''">
                ,report_date
            </if>
            <if test="stransferStage !=null">
                ,stransfer_stage
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                ,is_transfer
            </if>
            <if test="orderTypeCheckInstall !=null and orderTypeCheckInstall !=''"><!--安装单号不检查订单类型,多个值逗号分隔-->
                ,order_type_check_install
            </if>
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,store_type_code
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,store_type_name
            </if>
            <if test="depId != null"><!-- 门店所属部门 -->
                ,dep_id
            </if>
            <if test="cluesCreatedDate != null and cluesCreatedDate !='' "><!-- 线索创建时间 -->
                ,clues_created_date
            </if>
            <if test="receiptUrl != null and receiptUrl != ''"><!-- 小票地址 -->
                ,receipt_url
            </if>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,receipt_type
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,limit_type
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,limit_number
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,is_auto_audit
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,deal_receiver_id
            </if>
            <if test="isDraft != null"><!-- 是否是草稿 -->
                ,is_draft
            </if>
            <if test="isQuick != null"><!-- 是否快捷录单 -->
                ,is_quick
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,contact_mobile_enc
            </if>
            <if test="decorateCompanyPhone != null and decorateCompanyPhone != ''"><!-- 家装公司手机号 -->
                ,decorate_company_phone
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,has_ambry
            </if>
            <if test="installCheck != null"><!-- 安装单号检查 -->
                ,install_check
            </if>
            ,is_scene_deal, building, unit, house_number
            , integrate_id, content_template_id,store_sub_channel_code,store_sub_channel_name
            <if test="isComeDevise != null">
                ,is_come_devise
            </if>
            ,out_order_id2
            <if test="wareHouse != null and wareHouse != ''">
                ,ware_house
            </if>
            <if test="isOldCustomer != null">
                ,is_old_customer
            </if>
            <if test="taobaoTradeNo != null and taobaoTradeNo != ''">
                ,taobao_trade_no
            </if>
            <if test="regionProxyFlag != null">
                ,region_proxy_flag
            </if>
            <if test="hasAttachment != null">
                ,has_attachment
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,affect_company_quality_flag
            </if>
            <if test="costShortFall != null">
                ,cost_short_fall
            </if>
            <if test="isMonthlySettlement != null">
                ,is_monthly_settlement
            </if>
            <if test="cashierChargeMapping != null">
                ,cashier_charge_mapping
            </if>
            <if test="tradeInFlag != null">
                ,trade_in_flag
            </if>
            <if test="realStoreId != null">
                ,real_store_id
            </if>
            <if test="realDistributorId != null">
                ,real_distributor_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金;102:经销/零售销退订单；201：经销上样订单；202：经销退样订单；301：经销样机销售订单；
	   			                              401：换货销售订单；402：换货退货订单；601：KA上样订单；602：KA退样订单；701：KA样机销售订单；702：KA样机销退订单；
	                     			          801：B2B销售订单；802：B2B退货订单 -->
                #{orderType},
            </if>
            <if test="companyId != null"><!-- 分公司id -->
                #{companyId},
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                #{companyName},
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                #{distributorId},
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                #{distributorName},
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                #{distributorCode},
            </if>
            <!--***********订单信息******-->
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                #{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                #{adviserName},
            </if>
            <if test="inputId != null"><!-- 厨电顾问(录入人)ID -->
                #{inputId},
            </if>
            <if test="inputName != null"><!-- 厨电顾问(录入人)名称 -->
                #{inputName},
            </if>
            <if test="originalOrderId !=null"><!--原销售订单-->
                #{originalOrderId},
            </if>
            <if test="outOrderId !=null and outOrderId !=''"><!--外部订单号-->
                #{outOrderId},
            </if>
            <!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 默认审核通过-->
            <if test="stage !=null">
                #{stage},
            </if>
            <if test="firstReturnReason !=null"><!--一级退货原因id-->
                #{firstReturnReason},
            </if>
            <if test="firstReturnReasonName !=null and firstReturnReasonName !=''"><!--一级退货原因名称-->
                #{firstReturnReasonName},
            </if>
            <if test="secReturnReason !=null"><!--二级退货原因id-->
                #{secReturnReason},
            </if>
            <if test="secReturnReasonName !=null and secReturnReasonName !=''"><!--二级退货原因名称-->
                #{secReturnReasonName},
            </if>
            <if test="appPreAudit != null "><!--是否在云管理预审核 0：否；1：是 2:预审通过-->
                #{appPreAudit},
            </if>
            <if test="OANo!=null and OANo !=''"><!--OA编号-->
                #{OANo},
            </if>
            <if test="parentId != null"><!-- 关联定金订单号 -->
                #{parentId},
            </if>
            <if test="reason !=null and reason !=''"><!--原因描述-->
                #{reason},
            </if>
            <if test="examineDate !=null and examineDate !=''"><!--审核时间-->
                #{examineDate},
            </if>
            <if test="approveStatus !=null and approveStatus !=''"> <!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                #{approveStatus},
            </if>
            <if test="orderDate != null "><!-- 订购时间 -->
                #{orderDate},
            </if>
            <!--********************业务信息*****-->
            <if test="storeId != null"><!-- 门店id -->
                #{storeId},
            </if>
            <if test="storeType != null"><!-- 门店类型 -->
                #{storeType},
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                #{storeName},
            </if>
            <if test="decorateCompanyType != null"><!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
                #{decorateCompanyType},
            </if>
            <if test="cluesId != null"><!-- 线索id -->
                #{cluesId},
            </if>
            <if test="cluesSalesmanId != null"><!-- 线索负责人关联的业务员id -->
                #{cluesSalesmanId},
            </if>
            <if test="managerId != null"><!-- 客户经理(线索负责人)ID -->
                #{managerId},
            </if>
            <if test="managerName != null"><!-- 客户经理(线索负责人)名称 -->
                #{managerName},
            </if>
            <if test="orderNote != null"><!-- 其他备注 -->
                #{orderNote},
            </if>
            <if test="decorateCompanyCode != null"><!-- 家装公司编码 -->
                #{decorateCompanyCode},
            </if>
            <if test="decorateCompanyName != null"><!-- 家装公司名称 -->
                #{decorateCompanyName},
            </if>
            <if test="designerCode != null"><!-- 设计师编码 -->
                #{designerCode},
            </if>
            <if test="designerName != null"><!-- 设计师名称 -->
                #{designerName},
            </if>
            <if test="designerPhone != null"><!-- 设计师手机号 -->
                #{designerPhone},
            </if>
            <if test="orderChannel !=null and orderChannel !=''"><!-- 订单渠道 -->
                #{orderChannel},
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!--活动说明-->
                #{activityRemark},
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                #{buyReason},
            </if>
            <if test="cluesSourceCode != null"><!-- 线索来源编码 -->
                #{cluesSourceCode},
            </if>
            <if test="cluesSourceName != null"><!-- 线索来源名称 -->
                #{cluesSourceName},
            </if>
            <!--********************配送信息*****-->
            <if test="contactName != null"><!-- 收件人 -->
                #{contactName},
            </if>
            <if test="villageId != null"><!-- 小区id -->
                #{villageId},
            </if>
            <if test="villageName != null"><!-- 小区名称 -->
                #{villageName},
            </if>
            <if test="storage !=null and storage !=''"><!--默认储位-->
                #{storage},
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                #{installId},
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                #{contactMobile},
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                #{proviceId},
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                #{proviceName},
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                #{cityId},
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                #{cityName},
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                #{countyId},
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                #{countyName},
            </if>
            <if test="deliveryDate != null and deliveryDate !=''"><!-- 送货时间 -->
                #{deliveryDate},
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                #{contactMobileBak},
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                #{deliveryAddress},
            </if>
            <!--********************结算信息*****-->
            <if test="earnestAmount != null"><!-- 定金金额 -->
                #{earnestAmount},
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                #{cashAmount},
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                #{transferAmount},
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                #{cardAmount},
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                abs(#{posAmount}),
            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                abs(#{goodsAmount}),
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                abs(#{goodsNum}),
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                abs(#{nonGiftNum}),
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                abs(#{nonGiftAmount}),
            </if>
            <!--********************其他冗余字段*****-->
            <if test="isToDrp != null"><!-- 是否同步至drp -->
                #{isToDrp},
            </if>
            <if test="areaCode != null"><!-- 大区编码 -->
                #{areaCode},
            </if>
            <if test="areaName != null"><!-- 大区名称 -->
                #{areaName},
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                #{channelCategoryCode},
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                #{channelCategoryName},
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                #{channelSubdivideCode},
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                #{channelSubdivideName},
            </if>
            <if test="uuid != null "><!-- 订购时间 -->
                #{uuid},
            </if>
            <if test="isInStore !=null"> <!-- 是否店内成交 0：否；1：是 -->
                #{isInStore},
            </if>
            <if test="auditDate !=null and auditDate !=''"><!--云管理审核时间-->
                #{auditDate},
            </if>
            <if test="businessType !=null and businessType !=''"><!--业务类型 默认值：BBC,根据订单类型传值-->
                #{businessType},
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                #{goodsNumCanEnter},
            </if>
            <if test="userName != null and userName !=''">
                #{userName},
            </if>
            <if test="decorateCategory != null and decorateCategory != ''">
                #{decorateCategory},
            </if>
            <if test="decorateCategoryName != null and decorateCategoryName != ''">
                #{decorateCategoryName},
            </if>
            <if test="toDrpStage != null ">
                #{toDrpStage},
            </if>
            <if test="appPreAudit != null and appPreAudit ==2"><!--是否在云管理预审核 0：否；1：是 2:预审通过  预审核通过，给预审核时间-->
                now(),0,
            </if>
            <if test="isCheckPrice != null "><!--是否价格检查 0：否；1：是-->
                #{isCheckPrice},
            </if>
            <if test="cutTime !=null and cutTime !='' ">
                #{cutTime},
            </if>
            now(),#{createdBy},now(),#{modifiedBy},now(),#{isFromApp},1,0,
            <choose><!--出样类型
→
sampleType
0 非上样订单
5 上样订单
3 特价订单

0和5的式样
 * 101 经销零售订单→0 非上样订单
 * 102 经销零售销退订单→0 非上样订单
 * 201 经销上样订单→0 非上样订单
 * 202 经销退样订单→0 非上样订单
 * 301 经销样机销售订单→0 非上样订单(修改)
 * 401 换货销售订单→0 非上样订单
 * 402 换货退货订单→0 非上样订单
 * 501 定金订单→不同步drp
 * 601 KA上样订单→5 上样订单 （修改）
 * 602 KA退样订单→5 上样订单  （修改）
 * 701 KA样机销售订单→0 非上样订单  （修改）
 * 702 KA样机销退订单→0 非上样订单  （修改）
 * 801 B2B销售订单→0 非上样订单
 * 802 B2B退货订单→0 非上样订单-->
                <when test="isCheckPrice !=null and isCheckPrice==0">3</when>
                <otherwise>
                    <choose>
                        <when test="orderType!=null and (orderType==101 or orderType==102) ">
                            <choose>
                                <when test="isCheckPrice !=null and isCheckPrice==0">3</when>
                                <otherwise>0</otherwise>
                            </choose>
                        </when>
                        <when test="orderType!=null and (orderType==201 or orderType==202)">0</when>
                        <when test="orderType!=null and orderType==301">0</when>
                        <when test="orderType!=null and (orderType==401 or orderType==402)">0</when>
                        <when test="orderType!=null and (orderType==601 or orderType==602)">5</when>
                        <when test="orderType!=null and (orderType==701 or orderType==702)">0</when>
                        <when test="orderType!=null and (orderType==801 or orderType==802)">0</when>
                        <when test="orderType!=null and (orderType==901 or orderType==902)">3</when>
                        <otherwise>null</otherwise>
                    </choose>
                </otherwise>
            </choose>
            <if test="reportDate != null and reportDate != ''">
                ,#{reportDate}
            </if>
            <if test="stransferStage !=null">
                ,#{stransferStage}
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                ,#{isTransfer}
            </if>
            <if test="orderTypeCheckInstall !=null and orderTypeCheckInstall !=''"><!--安装单号不检查订单类型,多个值逗号分隔-->
                ,#{orderTypeCheckInstall}
            </if>
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,#{storeTypeCode}
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,#{storeTypeName}
            </if>
            <if test="depId != null"><!-- 门店所属部门 -->
                ,#{depId}
            </if>
            <if test="cluesCreatedDate != null and cluesCreatedDate !='' "><!-- 线索创建时间 -->
                ,#{cluesCreatedDate}
            </if>
            <if test="receiptUrl != null and receiptUrl != ''"><!-- 小票地址 -->
                ,#{receiptUrl}
            </if>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,#{receiptType}
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,#{limitType}
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,#{limitNumber}
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,#{isAutoAudit}
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,#{dealReceiverId}
            </if>
            <if test="isDraft != null"><!-- 是否是草稿 -->
                ,#{isDraft}
            </if>
            <if test="isQuick != null"><!-- 是否快捷录单 -->
                ,#{isQuick}
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,#{contactMobileEnc}
            </if>
            <if test="decorateCompanyPhone != null and decorateCompanyPhone != ''"><!-- 家装公司手机号 -->
                ,#{decorateCompanyPhone}
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,#{hasAmbry}
            </if>
            <if test="installCheck != null"><!-- 安装单号检查 -->
                ,#{installCheck}
            </if>
            ,#{isSceneDeal}, #{building}, #{unit}, #{houseNumber}
            , #{integrateId}, #{contentTemplateId},#{storeSubChannelCode},#{storeSubChannelName}
            <if test="isComeDevise != null">
                ,#{isComeDevise}
            </if>
            ,#{outOrderId2}
            <if test="wareHouse != null and wareHouse != ''">
                ,#{wareHouse}
            </if>
            <if test="isOldCustomer != null">
                ,#{isOldCustomer}
            </if>
            <if test="taobaoTradeNo != null and taobaoTradeNo != ''">
                ,#{taobaoTradeNo}
            </if>
            <if test="regionProxyFlag != null">
                ,#{regionProxyFlag}
            </if>
            <if test="hasAttachment != null">
                ,#{hasAttachment}
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,#{affectCompanyQualityFlag}
            </if>
            <if test="costShortFall != null">
                ,#{costShortFall}
            </if>
            <if test="isMonthlySettlement != null">
                ,#{isMonthlySettlement}
            </if>
            <if test="cashierChargeMapping != null">
                ,#{cashierChargeMapping}
            </if>
            <if test="tradeInFlag != null">
                ,#{tradeInFlag}
            </if>
            <if test="realStoreId != null">
                ,#{realStoreId}
            </if>
            <if test="realDistributorId != null">
                ,#{realDistributorId}
            </if>
        </trim>
    </insert>

<!--
    <if test="areaCode != null">
        area_code=#{areaCode},
    </if>
    <if test="areaName != null">
        area_name=#{areaName},
    </if>
-->
    
    <!-- 中台修改订单 -->
    <update id="platUpdateDecOrder" parameterType="com.fotile.omscenter.decOrder.pojo.dto.AddOrderInDto">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now(),
            modified_by=#{modifiedBy},
            version =  version + 1,
            to_drp_stage=#{toDrpStage},
            order_date=#{orderDate},<!-- 订购时间 -->
            order_channel=#{orderChannel},<!-- 订单渠道 -->
            cut_time=#{cutTime},<!--截单时间-->
            <if test="orderType != null"><!-- 订单类型 1：经销/零售；2：定金;102:经销/零售销退订单；201：经销上样订单；202：经销退样订单；301：经销样机销售订单；
	   			                              401：换货销售订单；402：换货退货订单；601：KA上样订单；602：KA退样订单；701：KA样机销售订单；702：KA样机销退订单；
	                     			          801：B2B销售订单；802：B2B退货订单 -->
                order_type=#{orderType},
            </if>
            <if test="companyId != null"><!-- 分公司id -->
                company_id=#{companyId},
            </if>
            <if test="companyName != null"><!-- 公司名称 -->
                company_name=#{companyName},
            </if>
            <if test="distributorId != null"><!-- 经销商id -->
                distributor_id=#{distributorId},
            </if>
            <if test="distributorName != null"><!-- 经销商名称 -->
                distributor_name=#{distributorName},
            </if>
            <if test="distributorCode != null"><!-- 经销商编码 -->
                distributor_code=#{distributorCode},
            </if>
            <!--***********订单信息******-->
            <if test="adviserId != null"><!-- 厨电顾问ID -->
                adviser_id=#{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问姓名 -->
                adviser_name=#{adviserName},
            </if>
            <if test="adviserId != null"><!-- 厨电顾问(录入人)ID -->
                input_id=#{adviserId},
            </if>
            <if test="adviserName != null"><!-- 厨电顾问(录入人)名称 -->
                input_name=#{adviserName},
            </if>
            <if test="originalOrderId !=null"><!--原销售订单-->
                original_orderId=#{originalOrderId},
            </if>
            <if test="outOrderId !=null and outOrderId !=''"><!--外部订单号-->
                out_orderId=#{outOrderId},
            </if>
            <!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 默认审核通过-->
            <if test="stage !=null">
                stage=#{stage},
            </if>
            <if test="areaCode != null and areaCode != ''">
                area_code=#{areaCode},
            </if>
            <if test="areaName != null and areaName != ''">
                area_name=#{areaName},
            </if>
            <if test="firstReturnReason !=null"><!--一级退货原因id-->
                first_return_reason=#{firstReturnReason},
            </if>
            <if test="firstReturnReasonName !=null and firstReturnReasonName !=''"><!--一级退货原因名称-->
                first_return_reason_name=#{firstReturnReasonName},
            </if>
            <if test="secReturnReason !=null"><!--二级退货原因id-->
                sec_return_reason=#{secReturnReason},
            </if>
            <if test="secReturnReasonName !=null and secReturnReasonName !=''"><!--二级退货原因名称-->
                sec_return_reason_name=#{secReturnReasonName},
            </if>
            <if test="appPreAudit != null "><!--是否在云管理预审核 0：否；1：是 2:预审通过-->
                app_pre_audit=#{appPreAudit},
            </if>
            <if test="OANo!=null and OANo !=''"><!--OA编号-->
                oa_no=#{OANo},
            </if>
            <if test="parentId != null"><!-- 关联定金订单号 -->
                parent_id=#{parentId},
            </if>
            <if test="reason !=null and reason !=''"><!--原因描述-->
                reason=#{reason},
            </if>
            <if test="examineDate !=null and examineDate !=''"><!--审核时间-->
                examine_date=#{examineDate},
            </if>
            <if test="approveStatus !=null and approveStatus !=''"> <!-- 审批状态 1:未审批；2：审批通过；3：审批不通过 -->
                approve_status=#{approveStatus},
            </if>
            <if test="orderDate != null "><!-- 订购时间 -->
                order_date=#{orderDate},
            </if>
            <!--********************业务信息*****-->
            <if test="storeId != null"><!-- 门店id -->
                store_id=#{storeId},
            </if>
            <if test="storeType != null"><!-- 门店类型 -->
                store_type=#{storeType},
            </if>
            <if test="storeName != null"><!-- 门店名称 -->
                store_name=#{storeName},
            </if>
            <!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
            decorate_company_type=#{decorateCompanyType},
            clues_id=#{cluesId},
            clues_created_date = #{cluesCreatedDate},
            clues_salesman_id=#{cluesSalesmanId},
            manager_id=#{managerId},
            manager_name=#{managerName},
            clues_source_code=#{cluesSourceCode},
            clues_source_name=#{cluesSourceName},
            integrate_id = #{integrateId},
            content_template_id = #{contentTemplateId},
            is_come_devise = #{isComeDevise},
            <if test="orderNote != null"><!-- 其他备注 -->
                order_note=#{orderNote},
            </if>
            <!-- 家装公司编码 -->
            decorate_company_code=#{decorateCompanyCode},

            <!-- 家装公司名称 -->
            decorate_company_name=#{decorateCompanyName},
            <!-- 设计师编码 -->
            designer_code=#{designerCode},
            <!-- 设计师名称 -->
            designer_name=#{designerName},
            <!-- 设计师手机号 -->
            designer_phone=#{designerPhone},
            <choose>
                <when test="decorateCategory ==null or decorateCategory ==''">
                    decorate_category=null,
                </when>
                <otherwise>
                    decorate_category=#{decorateCategory},
                </otherwise>
            </choose>

            decorate_category_name=#{decorateCategoryName},
            <if test="orderChannel !=null and orderChannel !=''"><!-- 订单渠道 -->
                order_channel=#{orderChannel},
            </if>
            <if test="activityRemark != null and activityRemark != ''"><!--活动说明-->
                activity_remark=#{activityRemark},
            </if>
            <if test="buyReason != null"><!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
                buy_reason=#{buyReason},
            </if>
            <!--********************配送信息*****-->
            <if test="contactName != null"><!-- 收件人 -->
                contact_name=#{contactName},
            </if>
            village_id=#{villageId},
            village_name=#{villageName},
            <if test="storage !=null and storage !=''"><!--默认储位-->
                storage=#{storage},
            </if>
            <if test="installId != null"><!-- 安装单号 -->
                install_id=#{installId},
            </if>
            <if test="contactMobile != null"><!-- 收件人手机号 -->
                contact_mobile=#{contactMobile},
            </if>
            <if test="proviceId != null"><!-- 地址-省id -->
                provice_id=#{proviceId},
            </if>
            <if test="proviceName != null"><!-- 地址-省名称 -->
                provice_name=#{proviceName},
            </if>
            <if test="cityId != null"><!-- 地址-市id -->
                city_id=#{cityId},
            </if>
            <if test="cityName != null"><!-- 地址-市名称 -->
                city_name=#{cityName},
            </if>
            <if test="countyId != null"><!-- 地址-区id -->
                county_id=#{countyId},
            </if>
            <if test="countyName != null"><!-- 地址-区名称 -->
                county_name=#{countyName},
            </if>
            <if test="deliveryDate != null and deliveryDate !=''"><!-- 送货时间 -->
                delivery_date=#{deliveryDate},
            </if>
            <if test="contactMobileBak != null"><!-- 联系方式 -->
                contact_mobile_bak=#{contactMobileBak},
            </if>
            <if test="deliveryAddress != null"><!-- 收人人地址 -->
                delivery_address=#{deliveryAddress},
            </if>
            <!--********************结算信息*****-->
            <if test="earnestAmount != null"><!-- 定金金额 -->
                earnest_amount=#{earnestAmount},
            </if>
            <if test="cashAmount != null"><!-- 现金金额 -->
                cash_amount=#{cashAmount},
            </if>
            <if test="transferAmount != null"><!-- 转账金额 -->
                transfer_amount=#{transferAmount},
            </if>
            <if test="cardAmount != null"><!-- 刷卡金额 -->
                card_amount=#{cardAmount},
            </if>
            <if test="posAmount != null"><!-- pos机金额 -->
                pos_amount=#{posAmount},
            </if>
            <if test="goodsAmount != null"><!-- 订单总金额 -->
                goods_amount=abs(#{goodsAmount}),
            </if>
            <if test="goodsNum != null"><!-- 商品总数量 -->
                goods_num=abs(#{goodsNum}),
            </if>
            <if test="nonGiftNum != null"><!-- 非赠品数量 -->
                non_gift_num=abs(#{nonGiftNum}),
            </if>
            <if test="nonGiftAmount != null"><!-- 非赠品金额 -->
                non_gift_amount=abs(#{nonGiftAmount}),
            </if>
            <!--********************其他冗余字段*****-->
            <if test="isToDrp != null"><!-- 是否同步至drp -->
                is_to_drp=#{isToDrp},
            </if>
            <if test="channelCategoryCode != null"><!-- 渠道大类编码 -->
                channel_category_code=#{channelCategoryCode},
            </if>
            <if test="channelCategoryName != null"><!-- 渠道大类名称 -->
                channel_category_name=#{channelCategoryName},
            </if>
            <if test="channelSubdivideCode != null"><!-- 渠道细分编码 -->
                channel_subdivide_code=#{channelSubdivideCode},
            </if>
            <if test="channelSubdivideName != null"><!-- 渠道细分名称 -->
                channel_subdivide_name=#{channelSubdivideName},
            </if>
            <if test="uuid != null "><!-- 订购时间 -->
                uuid=#{uuid},
            </if>
            <if test="isInStore !=null"> <!-- 是否店内成交 0：否；1：是 -->
                is_inStore=#{isInStore},
            </if>
            <if test="auditDate !=null and auditDate !=''"><!--云管理审核时间-->
                audit_date=#{auditDate},
            </if>
            <if test="businessType !=null and businessType !=''"><!--业务类型 默认值：BBC,根据订单类型传值-->
                business_type=#{businessType},
            </if>
            <if test="goodsNumCanEnter !=null "><!-- 产品数量是否可输入 0：否；1：是 -->
                goods_num_canEnter=#{goodsNumCanEnter},
            </if>
            <if test="appPreAudit != null and appPreAudit ==2"><!--是否在云管理预审核 0：否；1：是 2:预审通过  预审核通过，给预审核时间-->
                app_pre_examine_date = case when app_pre_examine_date is null or app_pre_examine_date = '' then now()
                else app_pre_examine_date end,
                app_pre_examine=0,
            </if>
            <if test="isCheckPrice != null "><!--是否价格检查 0：否；1：是-->
                is_check_price=#{isCheckPrice},
            </if>
            <if test="stransferStage !=null">
                stransfer_stage=#{stransferStage},
            </if>
            <if test="isTransfer !=null"><!--是否通过drp转单 0：否；1：是-->
                is_transfer=#{isTransfer},
            </if>
            <!--安装单号不检查订单类型,多个值逗号分隔-->
            order_type_check_install=#{orderTypeCheckInstall}
            <if test="storeTypeCode != null"><!-- 门店类型编码 -->
                ,store_type_code=#{storeTypeCode}
            </if>
            <if test="storeTypeName != null"><!-- 门店类型名称 -->
                ,store_type_name=#{storeTypeName}
            </if>
            <if test="receiptUrl != null"><!-- 小票地址 -->
                ,receipt_url=#{receiptUrl}
            </if>
            <if test="receiptType != null"><!-- 小票类型 -->
                ,receipt_type=#{receiptType}
            </if>
            <if test="limitType != null"><!-- 赠品限额类型 -->
                ,limit_type=#{limitType}
            </if>
            <if test="limitNumber != null"><!-- 赠品限额数值 -->
                ,limit_number=#{limitNumber}
            </if>
            <if test="isAutoAudit != null"><!-- 是否自动审核 -->
                ,is_auto_audit=#{isAutoAudit}
            </if>
            <if test="dealReceiverId != null"><!-- 成交接待人 -->
                ,deal_receiver_id=#{dealReceiverId}
            </if>
            <if test="contactMobileEnc != null"><!-- 加密手机号 -->
                ,contact_mobile_enc = #{contactMobileEnc}
            </if>
            <if test="decorateCompanyPhone != null and decorateCompanyPhone != ''"><!-- 家装公司手机号 -->
                ,decorate_company_phone = #{decorateCompanyPhone}
            </if>
            <if test="hasAmbry != null"><!-- 是否有橱柜 -->
                ,has_ambry = #{hasAmbry}
            </if>
            <if test="oldStage != null and oldStage != 2">
                ,audit_date = #{auditDate}
                ,report_date = #{reportDate}
            </if>
            ,is_scene_deal = #{isSceneDeal}
            ,building = #{building}
            ,unit = #{unit}
            ,house_number = #{houseNumber}
            <if test="isOldCustomer != null">
                ,is_old_customer = #{isOldCustomer}
            </if>
            <if test="regionProxyFlag != null">
                ,region_proxy_flag = #{regionProxyFlag}
            </if>
            <if test="hasAttachment != null">
                ,has_attachment = #{hasAttachment}
            </if>
            <if test="affectCompanyQualityFlag != null">
                ,affect_company_quality_flag = #{affectCompanyQualityFlag}
            </if>
            <if test="costShortFall != null">
                ,cost_short_fall = #{costShortFall}
            </if>
            <if test="isMonthlySettlement != null">
                ,is_monthly_settlement = #{isMonthlySettlement}
            </if>
            <if test="cashierChargeMapping != null">
                ,cashier_charge_mapping = #{cashierChargeMapping}
            </if>
            ,trade_in_flag = #{tradeInFlag}
            ,real_store_id = #{realStoreId}
            ,real_distributor_id = #{realDistributorId}
            ,store_sub_channel_code = #{storeSubChannelCode}
            ,store_sub_channel_name = #{storeSubChannelName}
            <choose>
                <when test="isCheckPrice !=null and isCheckPrice==0">,sample_type = 3</when>
                <otherwise>
                    <choose>
                        <when test="orderType!=null and (orderType==101 or orderType==102) ">
                            <choose>
                                <when test="isCheckPrice !=null and isCheckPrice==0">,sample_type = 3</when>
                                <otherwise>,sample_type = 0</otherwise>
                            </choose>
                        </when>
                        <when test="orderType!=null and (orderType==201 or orderType==202)">,sample_type = 0</when>
                        <when test="orderType!=null and orderType==301">,sample_type = 0</when>
                        <when test="orderType!=null and (orderType==401 or orderType==402)">,sample_type = 0</when>
                        <when test="orderType!=null and (orderType==601 or orderType==602)">,sample_type = 5</when>
                        <when test="orderType!=null and (orderType==701 or orderType==702)">,sample_type = 0</when>
                        <when test="orderType!=null and (orderType==801 or orderType==802)">,sample_type = 0</when>
                        <when test="orderType!=null and (orderType==901 or orderType==902)">,sample_type = 3</when>
                        <otherwise>,sample_type = null</otherwise>
                    </choose>
                </otherwise>
            </choose>
        </set>
        WHERE id=#{id}
    </update>


    <!-- 中台订单导入 -->
    <insert id="platImportDecOrder" parameterType="com.fotile.omscenter.decOrder.pojo.dto.ImportOrderInDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO omscenter.declaration_info(
        <!-- 订购时间 -->
        uuid,
        <!-- 订单类型 1：经销/零售；2：定金;102:经销/零售销退订单；201：经销上样订单；202：经销退样订单；301：经销样机销售订单；
                                             401：换货销售订单；402：换货退货订单；601：KA上样订单；602：KA退样订单；701：KA样机销售订单；702：KA样机销退订单；
                                           801：B2B销售订单；802：B2B退货订单 -->
        order_type,
        <!--外部订单号-->
        out_orderId,
        <!--是否在云管理预审核 0：否；1：是 2:预审通过-->
        app_pre_audit,
        <!-- 订购时间 -->
        order_date,
        <!--原销售订单-->
        original_orderId,
        <!--一级退货原因id-->
        first_return_reason,
        <!--一级退货原因名称-->
        first_return_reason_name,
        <!--二级退货原因id-->
        sec_return_reason,
        <!--二级退货原因名称-->
        sec_return_reason_name,
        <!--OA编号-->
        oa_no,
        <!--原因描述-->
        reason,
        <!-- 门店名称 -->
        store_name,
        <!-- 订单渠道 -->
        order_channel,
        <!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
        decorate_company_type,
        <!-- 家装公司编码 -->
        decorate_company_code,
        <!-- 家装公司名称 -->
        decorate_company_name,
        <!-- 设计师编码 -->
        designer_code,
        <!-- 设计师名称 -->
        designer_name,
        <!-- 设计师手机号 -->
        designer_phone,
        <!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
        buy_reason,
        <!-- 线索id -->
        clues_id,
        <!-- 其他备注 -->
        order_note,
        <!--活动说明-->
        activity_remark,
        <!-- 收件人 -->
        contact_name,
        <!-- 收件人手机号 -->
        contact_mobile,
        <!-- 联系方式 -->
        contact_mobile_bak,
        <!-- 小区名称 -->
        village_name,
        <!-- 地址-省名称 -->
        provice_name,
        <!-- 地址-市名称 -->
        city_name,
        <!-- 地址-区名称 -->
        county_name,
        <!-- 收人人地址 -->
        delivery_address,
        <!-- 安装单号 -->
        install_id,
        <!-- 现金金额 -->
        cash_amount,
        <!-- 转账金额 -->
        transfer_amount,
        <!-- 刷卡金额 -->
        card_amount,
        pos_amount,
        <!-- 送货时间 -->
        delivery_date,
        <!-- 门店id -->
        store_id,
        <!-- 门店类型 -->
        store_type,
        <!-- 经销商id -->
        distributor_id,
        <!-- 经销商名称 -->
        distributor_name,
        <!-- 经销商编码 -->
        distributor_code,
        <!-- 是否同步至drp -->
        is_to_drp,
        <!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 默认审核通过-->
        stage,
        <!--云管理审核时间-->
        audit_date,
        <!-- 厨电顾问ID -->
        adviser_id,
        <!-- 厨电顾问姓名 -->
        adviser_name,
        <!-- 厨电顾问(录入人)ID -->
        input_id,
        <!-- 厨电顾问(录入人)名称 -->
        input_name,
        <!-- 分公司id -->
        company_id,
        <!-- 公司名称 -->
        company_name,
        <!-- 大区编码 -->
        area_code,
        <!-- 大区名称 -->
        area_name,
        <!-- 线索来源编码 -->
        clues_source_code,
        <!-- 线索来源名称 -->
        clues_source_name,
        <!-- 地址-省id -->
        provice_id,
        <!-- 地址-市id -->
        city_id,
        <!-- 地址-区id -->
        county_id,
        <!-- 渠道大类编码 -->
        channel_category_code,
        <!-- 渠道大类名称 -->
        channel_category_name,
        <!-- 渠道细分编码 -->
        channel_subdivide_code,
        <!-- 渠道细分名称 -->
        channel_subdivide_name,
        <!-- 订单总金额 -->
        goods_amount,
        <!-- 商品总数量 -->
        goods_num,
        non_gift_num,
        non_gift_amount,
        <!--业务类型 默认值：BBC,根据订单类型传值-->
        business_type,
        order_create_time,created_by,created_date,modified_by,modified_date,is_from_app,user_name,manager_id,manager_name,clues_salesman_id,
        goods_num_canEnter,decorate_category,decorate_category_name,storage,to_drp_stage,is_check_price,app_pre_examine_date,cut_time,sample_type,report_date,is_transfer,
        store_type_code, store_type_name,clues_created_date,dep_id,is_draft,is_quick,contact_mobile_enc,decorate_company_phone,
        building, unit, house_number,install_check,is_come_devise,integrate_id,content_template_id,
        is_old_customer,region_proxy_flag,affect_company_quality_flag,cost_short_fall,is_monthly_settlement,cashier_charge_mapping,
        store_sub_channel_code,store_sub_channel_name
        )
        VALUES
        <foreach collection="importOrderList" item="order" separator=",">
            (
            <!-- 订购时间 -->
            #{order.uuid},
            <!-- 订单类型 1：经销/零售；2：定金;102:经销/零售销退订单；201：经销上样订单；202：经销退样订单；301：经销样机销售订单；
                                                 401：换货销售订单；402：换货退货订单；601：KA上样订单；602：KA退样订单；701：KA样机销售订单；702：KA样机销退订单；
                                               801：B2B销售订单；802：B2B退货订单 -->
            #{order.orderType},
            <!--外部订单号-->
            #{order.outOrderId},
            <!--是否在云管理预审核 0：否；1：是 2:预审通过-->
            #{order.appPreAudit},
            <!-- 订购时间 -->
            #{order.orderDate},
            <!--原销售订单-->
            #{order.originalOrderId},
            <!--一级退货原因id-->
            #{order.firstReturnReason},
            <!--一级退货原因名称-->
            #{order.firstReturnReasonName},
            <!--二级退货原因id-->
            #{order.secReturnReason},
            <!--二级退货原因名称-->
            #{order.secReturnReasonName},
            <!--OA编号-->
            #{order.OANo},
            <!--原因描述-->
            #{order.reason},
            <!-- 门店名称 -->
            #{order.storeName},
            <!-- 订单渠道 -->
            #{order.orderChannel},
            <!-- 装修公司类型 1：家装；2：异业三工 3：设计师 -->
            #{order.decorateCompanyType},
            <!-- 家装公司编码 -->
            #{order.decorateCompanyCode},
            <!-- 家装公司名称 -->
            #{order.decorateCompanyName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 设计师编码 -->
            #{order.designerCode},
            <!-- 设计师名称 -->
            #{order.designerName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 设计师手机号 -->
            #{order.designerPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装 -->
            #{order.buyReason},
            <!-- 线索id -->
            #{order.cluesId},
            <!-- 其他备注 -->
            #{order.orderNote},
            <!--活动说明-->
            #{order.activityRemark},
            <!-- 收件人 -->
            #{order.contactName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 收件人手机号 -->
            #{order.contactMobile,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 联系方式 -->
            #{order.contactMobileBak,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 小区名称 -->
            #{order.villageName},
            <!-- 地址-省名称 -->
            #{order.proviceName},
            <!-- 地址-市名称 -->
            #{order.cityName},
            <!-- 地址-区名称 -->
            #{order.countyName},
            <!-- 收人人地址 -->
            #{order.deliveryAddress,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 安装单号 -->
            #{order.installId},
            <!-- 现金金额 -->
            #{order.cashAmount},
            <!-- 转账金额 -->
            #{order.transferAmount},
            <!-- 刷卡金额 -->
            #{order.cardAmount},
            #{order.posAmount},
            <!-- 送货时间 -->
            #{order.deliveryDate},
            <!-- 门店id -->
            #{order.storeOrgId},
            <!-- 门店类型 -->
            #{order.storeType},
            <!-- 经销商id -->
            #{order.distributorId},
            <!-- 经销商名称 -->
            #{order.distributorName},
            <!-- 经销商编码 -->
            #{order.distributorCode},
            <!-- 是否同步至drp -->
            #{order.isToDrp},
            <!-- 审核状态 1：待审核；2：审核通过；3：审核拒绝 默认审核通过-->
            #{order.stage},
            <!--云管理审核时间-->
            #{order.auditDate},
            <!-- 厨电顾问ID -->
            #{order.adviserId},
            <!-- 厨电顾问姓名 -->
            #{order.adviserName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 厨电顾问(录入人)ID -->
            #{order.inputId},
            <!-- 厨电顾问(录入人)名称 -->
            #{order.inputName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            <!-- 分公司id -->
            #{order.companyId},
            <!-- 公司名称 -->
            #{order.companyName},
            <!-- 大区编码 -->
            #{order.areaCode},
            <!-- 大区名称 -->
            #{order.areaName},
            <!-- 线索来源编码 -->
            #{order.cluesSourceCode},
            <!-- 线索来源名称 -->
            #{order.cluesSourceName},
            <!-- 地址-省id -->
            #{order.proviceId},
            <!-- 地址-市id -->
            #{order.cityId},
            <!-- 地址-区id -->
            #{order.countyId},
            <!-- 渠道大类编码 -->
            #{order.channelCategoryCode},
            <!-- 渠道大类名称 -->
            #{order.channelCategoryName},
            <!-- 渠道细分编码 -->
            #{order.channelSubdivideCode},
            <!-- 渠道细分名称 -->
            #{order.channelSubdivideName},
            <!-- 订单总金额 -->
            #{order.goodsAmount},
            <!-- 商品总数量 -->
            #{order.goodsNum},
            #{order.nonGiftNum},
            #{order.nonGiftAmount},
            <!--业务类型 默认值：BBC,根据订单类型传值-->
            #{order.businessType},
            now(),#{order.createdBy},now(),#{order.modifiedBy},now(),0,
            #{order.userName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            #{order.managerId},
            #{order.managerName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            #{order.cluesSalesmanId},
            #{order.goodsNumCanEnter},#{order.decorateCategory},#{order.decorateCategoryName},#{order.storage},#{order.toDrpStage},
            #{order.isCheckPrice},#{order.appPreExamineDate},#{order.cutTime},#{order.sampleType}
            ,#{order.reportDate}
            ,#{order.isTransfer}
            ,#{order.storeTypeCode}
            ,#{order.storeTypeName}
            <choose>
                <when test="order.cluesCreatedDate !=null and order.cluesCreatedDate !='' ">
                    ,#{order.cluesCreatedDate}
                </when>
                <otherwise>
                    ,null
                </otherwise>
            </choose>
            ,#{order.depId},#{order.isDraft},#{order.isQuick},#{order.contactMobileEnc}
            ,#{order.decorateCompanyPhone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            ,#{order.building}, #{order.unit}, #{order.houseNumber},#{order.installCheck},#{order.isComeDevise}
            ,#{order.integrateId},#{order.contentTemplateId},#{order.isOldCustomer},#{order.regionProxyFlag}
            ,#{order.affectCompanyQualityFlag},#{order.costShortFall},#{order.isMonthlySettlement},#{order.cashierChargeMapping},
            #{order.storeSubChannelCode},
            #{order.storeSubChannelName}
            )
        </foreach>
    </insert>

    <!-- 根据uuid集合查询订单 -->
    <select id="findDecorderByUUIDSet" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        SELECT id,uuid,store_id storeId,store_name storeName
        FROM omscenter.declaration_info
        WHERE is_deleted=0 AND uuid IN
        <foreach collection="uuidSet" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </select>
    <!--根据云订单id/drp订单查询-->
    <select id="queryOrderByDrpIdOrId"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.QueryOrderByDrpIdOrIdOutDto">
        SELECT
        d.id,
        d.clues_id cluesId,
        d.user_id userId,
        d.user_name userName,
        d.company_id companyId,
        d.company_name companyName,
        d.store_id storeId,
        d.store_name stroeName,
        d.approve_status approveStatus,
        d.order_create_time orderCreateTime,
        d.contact_name contactName,
        d.contact_mobile contactMobile,
        d.provice_id proviceId,
        d.city_id cityId,
        d.county_id countyId,
        d.provice_name proviceName,
        d.city_name cityName,
        d.county_name countyName,
        d.delivery_address deliveryAddress,
        d.delivery_date deliveryDate,
        d.user_note userNote,
        d.order_note orderNote,
        d.goods_num goodsNum,
        d.goods_amount goodsAmount,
        d.buy_reason buyReason,
        d.business_type businessType,
        d.adviser_id adviserId,
        d.adviser_name adviserName,
        d.input_id inputId,
        d.input_name inputName,
        d.manager_id managerId,
        d.manager_name managerName,
        d.settlement_ticket settlementTicket,
        d.clues_salesman_id cluesSalesmanId,
        d.install_id installId,
        d.contact_mobile_bak contactMobileBak,
        d.stage stage,
        d.drp_stage drpStage,
        d.retry_drp_num retryDrpNum,
        d.drp_orderId drpOrderId,
        d.is_to_drp isToDrp,
        d.to_drp_stage toDrpStage,
        d.decorate_company_code decorateCompanyCode,
        d.decorate_company_name decorateCompanyName,
        d.decorate_company_phone decorateCompanyPhone,
        d.designer_code designerCode,
        d.designer_name designerName,
        d.designer_phone designerPhone,
        d.cash_amount cashAmount,
        d.transfer_amount transferAmount,
        d.card_amount cardAmount,
        d.pos_amount posAmount,
        d.order_type orderType,
        d.order_stage orderStage,
        d.stransfer_stage stransferStage,
        d.parent_id parentId,
        d.earnest_amount earnestAmount,
        d.village_id villageId,
        d.village_name villageName,
        d.to_drp_msg toDrpMsg,
        d.store_name storeName,
        d.area_code areaCode,
        d.area_name areaName,
        d.distributor_id distributorId,
        d.distributor_name distributorName,
        d.distributor_code distributorCode,
        d.channel_category_code channelCategoryCode,
        d.channel_category_name channelCategoryName,
        d.channel_subdivide_code channelSubdivideCode,
        d.channel_subdivide_name channelSubdivideName,
        d.clues_source_code cluesSourceCode,
        d.clues_source_name cluesSourceName,
        d.store_type storeType,
        d.examine_date examineDate,
        d.pre_examine_date preExamineDate,
        d.pre_examine preExamine,
        d.settlement_stage settlementStage,
        d.ware_house wareHouse,
        d.audit_date auditDate,
        d.card_no cardNo,
        d.card_id cardId,
        d.order_date orderDate,
        d.`storage` `storage`,
        d.decorate_company_type decorateCompanyType,
        d.activity_remark activityRemark,
        d.app_pre_audit appPreAudit,
        d.app_pre_examine_date appPreExamineDate,
        d.app_pre_examine appPreExamine,
        d.order_channel orderChannel,
        d.is_inStore isInStore,
        d.original_orderId originalOrderId,
        d.out_orderId outOrderId,
        d.first_return_reason firstReturnReason,
        d.first_return_reason_name firstReturnReasonName,
        d.sec_return_reason secReturnReason,
        d.sec_return_reason_name secReturnReasonName,
        d.oa_no oaNo,
        d.reason,
        d.decorate_category decorateCategory,
        d.decorate_category_name decorateCategoryName,
        d.clues_created_date cluesCreatedDate,
        d.dep_id depId,
        d.deal_receiver_id dealReceiverId,
        d.is_scene_deal isSceneDeal,
        d.building, d.unit, d.house_number houseNumber,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode,
        d.is_come_devise isComeDevise,
        d.trade_in_flag tradeInFlag,
        d.real_store_id realStoreId,
        d.real_distributor_id realDistributorId,
        extend.old_compensate_flag oldCompensateFlag,
        extend.is_cashier_enabled isCashierEnabled,
        d.pay_status payStaus,
        extend.house_feature houseFeature,
        extend.designer_industry_level designerIndustryLevel
        FROM declaration_info d
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = d.id
        <where>
            d.is_deleted = 0 and (d.is_from_app != 5 or d.is_from_app is null)
            <if test="queryOrderByDrpIdOrIdDto.drpOrderId!=null and queryOrderByDrpIdOrIdDto.drpOrderId!=''">
                AND (
                    d.drp_orderId = #{queryOrderByDrpIdOrIdDto.drpOrderId}
                    OR
                    d.id = #{queryOrderByDrpIdOrIdDto.drpOrderId}
                    or
                    d.dms_order_no = #{queryOrderByDrpIdOrIdDto.drpOrderId}
                )
            </if>
            <if test="queryOrderByDrpIdOrIdDto.storeAuthorList !=null and queryOrderByDrpIdOrIdDto.storeAuthorList.size>0">
                AND d.store_id IN
                <foreach collection="queryOrderByDrpIdOrIdDto.storeAuthorList" item="store" open="(" separator=","
                         close=")">
                    #{store.orgId}
                </foreach>
            </if>
            <if test="companyAuthorList !=null and companyAuthorList.size>0">
                AND d.company_id IN
                <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                    #{company.orgId}
                </foreach>
            </if>
        </where>

    </select>


    <sql id="platFindPageAllWhere">
        <where>
            o.is_deleted=0
            <if test="findDecOrderPageAllInDto.fullChargeBackFlagList !=null and findDecOrderPageAllInDto.fullChargeBackFlagList.size() > 0"><!-- 是否完全退单 -->
                AND o.full_chargeback_flag in
                <foreach collection="findDecOrderPageAllInDto.fullChargeBackFlagList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.isDraft !=null"><!-- 是否是草稿 -->
                AND o.is_draft=#{findDecOrderPageAllInDto.isDraft}
            </if>
            <if test="findDecOrderPageAllInDto.id !=null"><!-- 报单id -->
                AND o.id=#{findDecOrderPageAllInDto.id}
            </if>
            <if test="findDecOrderPageAllInDto.idList !=null and findDecOrderPageAllInDto.idList.size() > 0"><!-- 报单id -->
                AND o.id in
                <foreach collection="findDecOrderPageAllInDto.idList" open="(" close=")" separator="," item="orderId">
                    #{orderId}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.cluesId !=null"><!-- 线索id -->
                <choose>
                    <when test="findDecOrderPageAllInDto.cluesId == -1">
                        AND o.clues_id is null
                    </when>
                    <otherwise>
                        AND o.clues_id=#{findDecOrderPageAllInDto.cluesId}
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.cluesSalesmanId !=null"><!-- 线索负责人关联的业务员id -->
                <choose>
                    <when test="findDecOrderPageAllInDto.cluesSalesmanId == -1">
                        AND o.clues_salesman_id is null
                    </when>
                    <otherwise>
                        AND o.clues_salesman_id=#{findDecOrderPageAllInDto.cluesSalesmanId}
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.dealReceiverId !=null"><!-- 成交接待人 -->
                <choose>
                    <when test="findDecOrderPageAllInDto.dealReceiverId == -1">
                        AND o.deal_receiver_id is null
                    </when>
                    <otherwise>
                        AND o.deal_receiver_id=#{findDecOrderPageAllInDto.dealReceiverId}
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.adviserId !=null"><!-- 录入人 -->
                AND o.adviser_id =#{findDecOrderPageAllInDto.adviserId}
            </if>
            <if test="findDecOrderPageAllInDto.orderCreatetimeStart !=null and findDecOrderPageAllInDto.orderCreatetimeStart !=''"><!-- 报单时间-起始 -->
                AND o.order_create_time &gt;=DATE_FORMAT(#{findDecOrderPageAllInDto.orderCreatetimeStart},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.orderCreatetimeEnd !=null and findDecOrderPageAllInDto.orderCreatetimeEnd !=''"><!-- 报单时间-结束 -->
                AND o.order_create_time &lt;=DATE_FORMAT(#{findDecOrderPageAllInDto.orderCreatetimeEnd},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.goodsAmountStart !=null"><!-- 订单总金额-起始 -->
                AND o.goods_amount &gt;=#{findDecOrderPageAllInDto.goodsAmountStart}
            </if>
            <if test="findDecOrderPageAllInDto.goodsAmountEnd !=null"><!-- 订单总金额-结束 -->
                AND o.goods_amount &lt;=#{findDecOrderPageAllInDto.goodsAmountEnd}
            </if>
            <if test="findDecOrderPageAllInDto.contact !=null and findDecOrderPageAllInDto.contact !=''"><!-- 收件人(支持名称和手机号模糊匹配) -->
                <choose>
                    <when test="findDecOrderPageAllInDto.isMobile">
                        AND o.contact_mobile = #{findDecOrderPageAllInDto.contact,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    </when>
                    <otherwise>
                        AND o.id in (
                            SELECT id from omscenter.declaration_info where is_deleted = 0 and (
                                AES_DECRYPT( UNHEX(contact_name), #{findDecOrderPageAllInDto.secretKey} ) like CONCAT('%',#{findDecOrderPageAllInDto.contact},'%')
                                or
                                AES_DECRYPT( UNHEX(contact_mobile), #{findDecOrderPageAllInDto.secretKey} ) like CONCAT('%',#{findDecOrderPageAllInDto.contact},'%')
                            )
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.proviceId !=null"><!-- 收货地址-省 -->
                AND o.provice_id =#{findDecOrderPageAllInDto.proviceId}
            </if>
            <if test="findDecOrderPageAllInDto.cityId !=null"><!-- 收货地址-市 -->
                AND o.city_id =#{findDecOrderPageAllInDto.cityId}
            </if>
            <if test="findDecOrderPageAllInDto.countyId !=null"><!-- 收货地址-区 -->
                AND o.county_id =#{findDecOrderPageAllInDto.countyId}
            </if>
            <if test="findDecOrderPageAllInDto.address !=null and findDecOrderPageAllInDto.address !=''"><!-- 详细地址 -->
                AND AES_DECRYPT( UNHEX(o.delivery_address), #{findDecOrderPageAllInDto.secretKey} ) like CONCAT('%',#{findDecOrderPageAllInDto.address},'%')
            </if>
            <if test="findDecOrderPageAllInDto.companyId !=null"><!-- 所属分公司 -->
                AND o.company_id =#{findDecOrderPageAllInDto.companyId}
            </if>
            <if test="findDecOrderPageAllInDto.storeId !=null"><!-- 所属门店 -->
                AND o.store_id =#{findDecOrderPageAllInDto.storeId}
            </if>
            <if test="findDecOrderPageAllInDto.stage !=null"><!-- 审核状态 -->
                AND o.stage =#{findDecOrderPageAllInDto.stage}
            </if>
            <if test="findDecOrderPageAllInDto.stageList != null and findDecOrderPageAllInDto.stageList.size() > 0 "><!-- 审核状态 -->
                AND o.stage in
                <foreach collection="findDecOrderPageAllInDto.stageList" separator="," item="stage" open="(" close=")">
                    #{stage}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.drpStage !=null"><!-- 同步drp状态 -->
                <choose>
                    <when test="findDecOrderPageAllInDto.drpStage==4"><!-- 不同步 -->
                        AND ( is_to_drp=1)
                    </when>
                    <otherwise>
                        AND o.drp_stage =#{findDecOrderPageAllInDto.drpStage} AND is_to_drp !=1
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.drpStageList !=null and findDecOrderPageAllInDto.drpStageList.size() > 0"><!-- 同步drp状态 -->
                <choose>
                    <when test="findDecOrderPageAllInDto.drpStageList.contains(4)"><!-- 不同步 -->
                        AND (
                            o.is_to_drp = 1 or o.drp_stage in
                            <foreach collection="findDecOrderPageAllInDto.drpStageList" item="drpStage" separator="," open="(" close=")">
                                #{drpStage}
                            </foreach>
                        )
                    </when>
                    <otherwise>
                        AND o.is_to_drp != 1
                        AND o.drp_stage in
                        <foreach collection="findDecOrderPageAllInDto.drpStageList" item="drpStage" separator="," open="(" close=")">
                            #{drpStage}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.decorateCompanyName !=null and findDecOrderPageAllInDto.decorateCompanyName !=''"><!-- 家装公司名称 -->
                AND o.decorate_company_name = #{findDecOrderPageAllInDto.decorateCompanyName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="findDecOrderPageAllInDto.designerName !=null and findDecOrderPageAllInDto.designerName !=''"><!-- 设计师名称 -->
                AND o.designer_name = #{findDecOrderPageAllInDto.designerName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
            </if>
            <if test="findDecOrderPageAllInDto.orderType !=null"><!-- 订单类型 1：经销/零售；2：定金 -->
                AND order_type=#{findDecOrderPageAllInDto.orderType}
            </if>
            <if test="findDecOrderPageAllInDto.drpOrderId !=null and findDecOrderPageAllInDto.drpOrderId !=''"><!-- DRP订单号 -->
                <choose>
                    <when test="(companyAuthorList != null and companyAuthorList.size() > 0) or (authorStoreListNest != null and authorStoreListNest.size() > 0)">
                        and o.drp_orderId like CONCAT('%',#{findDecOrderPageAllInDto.drpOrderId},'%')
                    </when>
                    <otherwise>
                        and o.id in
                        (
                            SELECT id from omscenter.declaration_info
                            where is_deleted = 0 and drp_orderId like CONCAT('%',#{findDecOrderPageAllInDto.drpOrderId},'%')
                        )
                    </otherwise>
                </choose>

            </if>
            <if test="findDecOrderPageAllInDto.orderStage !=null"><!-- DRP订单状态 0:无  1:打开 2:审核 3:关闭 4:作废 5:中止 -->
                AND order_stage=#{findDecOrderPageAllInDto.orderStage}
            </if>
            <if test="findDecOrderPageAllInDto.stransferStage !=null"><!-- 定金转单状态 1：未转单；2：已转单' 3：已作废' -->
                AND stransfer_stage=#{findDecOrderPageAllInDto.stransferStage} AND order_type = 501
            </if>
            <!-- 门店类型查询到的门店id -->
            <if test="findDecOrderPageAllInDto.storeIdList != null and findDecOrderPageAllInDto.storeIdList.size > 0">
                AND o.store_id IN
                <foreach collection="findDecOrderPageAllInDto.storeIdList" item="storeId" open="(" separator=","
                         close=")">
                    #{storeId}
                </foreach>
            </if>
            <if test="authorStoreListNest != null and authorStoreListNest.size() != 0">
                and (
                <foreach collection="authorStoreListNest" item="list" separator="or">
                    o.store_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </foreach>
                )
            </if>
            <!-- 审核时间 -->
            <if test="findDecOrderPageAllInDto.auditDateStart != null and findDecOrderPageAllInDto.auditDateStart != ''">
                AND o.audit_date &gt;= #{findDecOrderPageAllInDto.auditDateStart}
            </if>
            <if test="findDecOrderPageAllInDto.auditDateEnd != null and findDecOrderPageAllInDto.auditDateEnd != ''">AND
                o.audit_date &lt;= #{findDecOrderPageAllInDto.auditDateEnd}
            </if>
            <!-- 财务审核时间 -->
            <if test="findDecOrderPageAllInDto.examineDateStart != null and findDecOrderPageAllInDto.examineDateStart != ''">
                AND o.examine_date &gt;= #{findDecOrderPageAllInDto.examineDateStart}
            </if>
            <if test="findDecOrderPageAllInDto.examineDateEnd != null and findDecOrderPageAllInDto.examineDateEnd != ''">
                AND o.examine_date &lt;= #{findDecOrderPageAllInDto.examineDateEnd}
            </if>
            <!-- 大区 -->
            <if test="findDecOrderPageAllInDto.area != null and findDecOrderPageAllInDto.area != ''">
                AND o.area_code = #{findDecOrderPageAllInDto.area}
            </if>
            <!--引流渠道类型-->
            <if test="findDecOrderPageAllInDto.decorateCompanyType !=null">
                <choose><!--2020-11-19 中台只有家装公司和异业三工，按需求传1将3的返回数据也返回-->
                    <when test="findDecOrderPageAllInDto.decorateCompanyType==1 or findDecOrderPageAllInDto.decorateCompanyType==3">
                        AND (o.decorate_company_type=1 OR o.decorate_company_type=3)
                    </when>
                    <when test="findDecOrderPageAllInDto.decorateCompanyType==2"><!--设计师-->
                        AND o.decorate_company_type=2
                    </when>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.decorateCompanyCode !=null and findDecOrderPageAllInDto.decorateCompanyCode !=''">
                AND o.decorate_company_code=#{findDecOrderPageAllInDto.decorateCompanyCode}
            </if>
            <!-- 订单备注 -->
            <if test="findDecOrderPageAllInDto.orderNote != null and findDecOrderPageAllInDto.orderNote != ''">
                AND o.order_note like CONCAT('%',CONCAT(#{findDecOrderPageAllInDto.orderNote},'%'))
            </if>
            <!-- 门店渠道编码 -->
            <if test="findDecOrderPageAllInDto.storeTypeCodeList != null and findDecOrderPageAllInDto.storeTypeCodeList.size() > 0">
                AND o.store_type_code IN
                <foreach collection="findDecOrderPageAllInDto.storeTypeCodeList" item="typeCode" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
            </if>
            <!-- 门店渠道名称 -->
            <if test="findDecOrderPageAllInDto.storeTypeName != null and findDecOrderPageAllInDto.storeTypeName != ''">
                AND o.store_type_name = #{findDecOrderPageAllInDto.storeTypeName}
            </if>
            <if test="companyAuthorList !=null and companyAuthorList.size >0">
                AND o.company_id IN
                <foreach collection="companyAuthorList" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>

            <if test="findDecOrderPageAllInDto.depId !=null"><!-- 所属部门 -->
                AND o.dep_id =#{findDecOrderPageAllInDto.depId}
            </if>
            <if test="findDecOrderPageAllInDto.depIdList !=null and findDecOrderPageAllInDto.depIdList.size() > 0"><!-- 所属部门 -->
                AND o.dep_id in
                <foreach collection="findDecOrderPageAllInDto.depIdList" separator="," open="(" close=")" item="depId">
                    #{depId}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.cluesCreatedStart !=null and findDecOrderPageAllInDto.cluesCreatedStart !=''"><!-- 线索时间-起始 -->
                AND DATE_FORMAT(o.clues_created_date,'%Y-%m-%d %T') &gt;=DATE_FORMAT(#{findDecOrderPageAllInDto.cluesCreatedStart},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.cluesCreatedEnd !=null and findDecOrderPageAllInDto.cluesCreatedEnd !=''"><!-- 线索时间-结束 -->
                AND DATE_FORMAT(o.clues_created_date,'%Y-%m-%d %T') &lt;=DATE_FORMAT(#{findDecOrderPageAllInDto.cluesCreatedEnd},'%Y-%m-%d %T')
            </if>
            <if test="findDecOrderPageAllInDto.distributorId !=null"><!-- 所属客户 -->
                AND o.distributor_id =#{findDecOrderPageAllInDto.distributorId}
            </if>
            <if test="findDecOrderPageAllInDto.channelCategoryCodeList !=null and findDecOrderPageAllInDto.channelCategoryCodeList.size() > 0"><!-- 客户渠道 -->
                AND o.channel_category_code IN
                <foreach collection="findDecOrderPageAllInDto.channelCategoryCodeList"
                         item="channelCategoryCode" open="(" separator="," close=")">
                    #{channelCategoryCode}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.channelSubdivideCodeList !=null and findDecOrderPageAllInDto.channelSubdivideCodeList.size() > 0"><!-- 渠道细分 -->
                AND o.channel_subdivide_code IN
                <foreach collection="findDecOrderPageAllInDto.channelSubdivideCodeList"
                         item="channelSubdivideCode" open="(" separator="," close=")">
                    #{channelSubdivideCode}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.installId !=null and findDecOrderPageAllInDto.installId !=''"><!-- 安装单号 -->
                AND o.install_id=#{findDecOrderPageAllInDto.installId}
            </if>
            <!--9136-云管理订单-筛选项调整-->
            <if test="findDecOrderPageAllInDto.orderTypeList != null and findDecOrderPageAllInDto.orderTypeList.size() > 0"><!-- 订单类型 1：经销/零售；2：定金 ,多个值用逗号分隔-->
                AND order_type IN
                <foreach collection="findDecOrderPageAllInDto.orderTypeList" item="orderType" open="("
                         separator="," close=")">
                    #{orderType}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.orderStageList != null and findDecOrderPageAllInDto.orderStageList.size() > 0"><!-- DRP订单状态 0:无  1:打开 2:审核 3:关闭 4:作废 5:中止 ,多个值用逗号分隔-->
                AND order_stage IN
                <foreach collection="findDecOrderPageAllInDto.orderStageList" item="orderStage" open="("
                         separator="," close=")">
                    #{orderStage}
                </foreach>
            </if>
            <!-- 大区 ,多个值用逗号分隔-->
            <if test="findDecOrderPageAllInDto.areaList != null and findDecOrderPageAllInDto.areaList.size() > 0">
                AND o.area_code IN
                <foreach collection="findDecOrderPageAllInDto.areaList" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
            </if>
            <!--9486-云管理订单-引流渠道类型筛选打开-->
            <if test="findDecOrderPageAllInDto.decorateCompanyTypeList != null and findDecOrderPageAllInDto.decorateCompanyTypeList !=''">
                <choose>
                    <when test="findDecOrderPageAllInDto.decorateCompanyTypeList =='1'.toString()"><!--家装-->
                        AND o.decorate_category IN (1,2,3) AND IFNULL(decorate_company_code,'')!=''
                    </when>
                    <when test="findDecOrderPageAllInDto.decorateCompanyTypeList =='2'.toString()"><!--设计师-->
                        AND o.decorate_category IN (1,2,3) AND IFNULL(designer_code,'')!=''
                    </when>
                    <otherwise><!--异业三工-->
                        AND o.decorate_category IN
                        <foreach collection="findDecOrderPageAllInDto.decorateCompanyTypeList.split(',')"
                                 item="decorateType" open="(" separator="," close=")">
                            #{decorateType}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <!--引流渠道-->
            <if test="findDecOrderPageAllInDto.drainageChannel !=null and findDecOrderPageAllInDto.drainageChannel !=''">
                AND (o.decorate_company_code=#{findDecOrderPageAllInDto.drainageChannel} OR
                o.designer_code=#{findDecOrderPageAllInDto.drainageChannel})
            </if>
            <if test="findDecOrderPageAllInDto.createdBy !=null and findDecOrderPageAllInDto.createdBy !=''"><!-- 录入人 -->
                AND o.created_by = #{findDecOrderPageAllInDto.createdBy}
            </if>
            <if test="(findDecOrderPageAllInDto.goodsIdList != null and findDecOrderPageAllInDto.goodsIdList.size() > 0)
                or (findDecOrderPageAllInDto.goodsCategoryIdList != null and findDecOrderPageAllInDto.goodsCategoryIdList.size() > 0)"><!-- 商品id 商品分类 -->
                AND o.id in (
                    select order_id from omscenter.goods_declaration gd
                    where gd.is_deleted = 0
                    <if test="findDecOrderPageAllInDto.goodsIdList != null and findDecOrderPageAllInDto.goodsIdList.size() > 0">
                        and gd.goods_id in
                        <foreach collection="findDecOrderPageAllInDto.goodsIdList" item="goodsId" separator="," open="(" close=")">
                            #{goodsId}
                        </foreach>
                    </if>
                    <if test="findDecOrderPageAllInDto.goodsCategoryIdList != null and findDecOrderPageAllInDto.goodsCategoryIdList.size() > 0">
                        and gd.goods_category_id in
                        <foreach collection="findDecOrderPageAllInDto.goodsCategoryIdList" item="goodsCategoryId" separator="," open="(" close=")">
                            #{goodsCategoryId}
                        </foreach>
                    </if>
                )
            </if>
            <if test="findDecOrderPageAllInDto.hasPrint !=null and findDecOrderPageAllInDto.hasPrint == 1"><!-- 是否已打印 -->
                AND o.print_num > 0
            </if>
            <if test="findDecOrderPageAllInDto.orderChannelSet !=null and findDecOrderPageAllInDto.orderChannelSet.size() > 0 "><!-- 订单渠道 -->
                AND o.order_channel in
                <foreach collection="findDecOrderPageAllInDto.orderChannelSet" item="orderChannel" separator="," open="(" close=")">
                    #{orderChannel}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.hasAttachment !=null "><!-- 是否有附件 -->
                <if test="findDecOrderPageAllInDto.hasAttachment == 1">
                    and (
                        (o.receipt_url is not null and LENGTH(trim(o.receipt_url)) > 0)
                        or exists (
                            select 1 from omscenter.picture_oms_mapping pom
                            where pom.is_deleted = 0 and pom.source_id = o.id
                            and source_table_name = 'declaration_info' and pom.type = 1
                        )
                    )
                </if>
                <if test="findDecOrderPageAllInDto.hasAttachment == 0">
                    and (o.receipt_url is null or LENGTH(trim(o.receipt_url)) = 0)
                    and not exists (
                        select 1 from omscenter.picture_oms_mapping pom
                        where pom.is_deleted = 0 and pom.source_id = o.id
                        and source_table_name = 'declaration_info' and pom.type = 1
                    )
                </if>
            </if>
            <if test="findDecOrderPageAllInDto.cluesSourceCodeList != null and findDecOrderPageAllInDto.cluesSourceCodeList.size() > 0"><!-- 线索来源 -->
                and
                <foreach collection="findDecOrderPageAllInDto.cluesSourceCodeList" item="sourceCode" open="(" close=")" separator="or">
                    <choose>
                        <when test="sourceCode == -1">
                            ISNULL(o.clues_source_name) = 1 or LENGTH(trim(o.clues_source_name)) = 0
                         </when>
                         <otherwise>
                             o.clues_source_code = #{sourceCode}
                         </otherwise>
                    </choose>
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.isSceneDeal !=null"><!-- 是否是场景化讲解成交，1是，0否 -->
                AND o.is_scene_deal = #{findDecOrderPageAllInDto.isSceneDeal}
            </if>
            <if test="findDecOrderPageAllInDto.outOrderId != null and findDecOrderPageAllInDto.outOrderId != ''"><!-- 外部订单号 -->
                AND o.out_orderId = #{findDecOrderPageAllInDto.outOrderId}
            </if>
            <if test="findDecOrderPageAllInDto.appPreAudit != null"><!-- 是否云管理预审核 -->
                AND o.app_pre_audit = #{findDecOrderPageAllInDto.appPreAudit}
            </if>
            <if test="findDecOrderPageAllInDto.appPreAuditList != null and findDecOrderPageAllInDto.appPreAuditList.size() > 0"><!-- 是否云管理预审核 -->
                AND o.app_pre_audit in
                <foreach collection="findDecOrderPageAllInDto.appPreAuditList" separator="," item="appPreAudit" open="(" close=")">
                    #{appPreAudit}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.villageIdSet != null and findDecOrderPageAllInDto.villageIdSet.size() > 0"><!-- 小区集合 -->
                AND o.village_id in
                <foreach collection="findDecOrderPageAllInDto.villageIdSet" item="villageId" separator="," open="(" close=")">
                    #{villageId}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.isComeDevise != null "><!-- 是否上门设计1是0否 -->
                AND o.is_come_devise = #{findDecOrderPageAllInDto.isComeDevise}
            </if>
            <if test="findDecOrderPageAllInDto.dcsOutStatusList != null and findDecOrderPageAllInDto.dcsOutStatusList.size() > 0"><!-- dcs出库状态 -->
                AND o.dcs_out_status in
                <foreach collection="findDecOrderPageAllInDto.dcsOutStatusList" separator="," item="dcsOutStatus" open="(" close=")">
                    #{dcsOutStatus}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.drpAuditRejectReason != null and findDecOrderPageAllInDto.drpAuditRejectReason != ''">
                AND o.drp_audit_reject_reason like CONCAT('%',CONCAT(#{findDecOrderPageAllInDto.drpAuditRejectReason},'%'))
            </if>
            <if test="findDecOrderPageAllInDto.inputId != null">
                <choose>
                    <when test="findDecOrderPageAllInDto.inputId != -1L">
                        AND o.input_id = #{findDecOrderPageAllInDto.inputId}
                    </when>
                    <otherwise>
                        AND o.input_id is null
                    </otherwise>
                </choose>
            </if>
            <if test="findDecOrderPageAllInDto.integrateId != null">
                and o.integrate_id = #{findDecOrderPageAllInDto.integrateId}
            </if>
            <if test="findDecOrderPageAllInDto.integrateIdList !=null and findDecOrderPageAllInDto.integrateIdList.size() >0">
                AND o.integrate_id in
                <foreach collection="findDecOrderPageAllInDto.integrateIdList" item="integrateId" open="(" close=")" separator=",">
                    #{integrateId}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.contactMobileBak != null and findDecOrderPageAllInDto.contactMobileBak != ''">
                AND o.contact_mobile_bak like concat('%',#{findDecOrderPageAllInDto.contactMobileBak},'%')
            </if>
            <if test="findDecOrderPageAllInDto.isFromAppList != null and findDecOrderPageAllInDto.isFromAppList.size() > 0">
                AND o.is_from_app in
                <foreach collection="findDecOrderPageAllInDto.isFromAppList" separator="," item="isFromApp" open="(" close=")">
                    #{isFromApp}
                </foreach>
            </if>
            <if test="findDecOrderPageAllInDto.taobaoTradeNo != null and findDecOrderPageAllInDto.taobaoTradeNo != ''">
                AND o.taobao_trade_no like concat('%',#{findDecOrderPageAllInDto.taobaoTradeNo},'%')
            </if>
        </where>
    </sql>
    <!-- 报单列表
     CASE WHEN LENGTH(o.contact_mobile) &lt;5 THEN o.contact_mobile WHEN LENGTH(o.contact_mobile) &gt;4 AND
        LENGTH(o.contact_mobile) &lt;9 THEN CONCAT('****',RIGHT(o.contact_mobile,4)) ELSE
        CONCAT(LEFT(o.contact_mobile,3),'****',RIGHT(o.contact_mobile,4)) END contactMobile,

        CASE WHEN LENGTH(o.designer_phone) &lt;5 THEN o.designer_phone WHEN LENGTH(o.designer_phone) &gt;4 AND
        LENGTH(o.designer_phone) &lt;9 THEN CONCAT('****',RIGHT(o.designer_phone,4)) ELSE
        CONCAT(LEFT(o.designer_phone,3),'****',RIGHT(o.designer_phone,4)) END designerPhone,
     -->
    <select id="platFindPageAll" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderPageAllOutDto">
        SELECT o.id,o.clues_salesman_id cluesSalesmanId,o.clues_id cluesId,o.adviser_id adviserId,o.adviser_name
        adviserName,o.order_create_time orderCreatetime,o.goods_num goodsNum,
        o.print_num printNum,o.receipt_url receiptUrl,
        o.goods_amount goodsAmount,o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.delivery_address address,
        CONCAT(o.provice_name,o.city_name,o.county_name) pccName,o.company_id companyId,o.store_id storeId,
        o.decorate_company_code decorateCompanyCode,o.decorate_company_name decorateCompanyName,o.designer_code
        designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.stage stage,
        CASE stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,
        CASE WHEN is_to_drp=1 THEN '不同步' WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理' WHEN is_to_drp
        !=1 AND drp_stage=2 THEN '同步成功' WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败' END drpStageName,
        (SELECT dc.category_name FROM omscenter.decorder_category dc WHERE dc.is_deleted=0 AND
        o.order_type=dc.category_code) orderType,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStage,
        CASE WHEN o.order_type=501 AND o.stransfer_stage=1 THEN '未转单' WHEN o.order_type=501 AND o.stransfer_stage=2 THEN
        '已转单' WHEN o.order_type=501 AND o.stransfer_stage=3 THEN '已作废' END stransferStage,
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,
        <!--补充订单渠道-->
        o.order_channel orderChannel,
        <!--补充引流渠道ID-->
        o.decorate_category decorateCategory,
        <!--补充引流渠道名称-->
        o.decorate_category_name decorateCategoryName,
        o.created_by createdBy,
        o.created_date createdDate,
        o.modified_by modifiedBy,
        o.modified_date modifiedDate,
        <!--引流渠道类型-->
        o.decorate_company_type decorateCompanyType,
        o.pos_pay_amount amountPaid,
        CASE o.decorate_company_type
        WHEN 1 THEN '家装公司'
        WHEN 2 THEN '异业三工'
        <!-- WHEN 3 THEN '设计师'-->
        WHEN 3 THEN '家装公司-设计师' <!--2020-11-19 中台只有家装公司和异业三工，按需求将3的返回“家装公司"-->
        END decorateCompanyTypeName,o.company_name companyName,o.store_name storeName,
        o.app_pre_audit appPreAudit,o.dcs_out_status dcsOutStatus
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->

        <include refid="platFindPageAllWhere"/>
        ORDER BY o.order_create_time DESC
        <if test="pg != null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!-- 报单列表count查询 -->
    <select id="platFindPageAllTotal" resultType="long">
        SELECT count(1)
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="platFindPageAllWhere"/>
    </select>

    <select id="findStoreIdListByInput" resultType="java.lang.Long">
        SELECT DISTINCT store_id storeId
        FROM omscenter.declaration_info
        WHERE is_deleted = 0
          AND input_id = #{inputId}
    </select>

    <!-- 优惠券撤销核销，更新is_consume值 -->
    <update id="updateIsConsume">
        UPDATE omscenter.goods_declaration_supple
        SET modified_date=NOW(),is_consume=#{isConsume}
        WHERE is_deleted=0
        AND id IN
        <foreach collection="supplyIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--7576 关联原单支持营销中台订单的安装单号查询-->
    <select id="queryOrderByInfo"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.QueryOrderByDrpIdOrIdOutDto">
        SELECT d.id,d.drp_orderId drpOrderId,d.install_id installId,d.contact_name contactName,d.contact_mobile
        contactMobile,
        d.company_id companyId,d.company_name companyName,d.store_id storeId,d.store_name stroeName, d.order_type
        orderType,
        d.stage stage,d.goods_num goodsNum,d.goods_amount goodsAmount,d.order_stage orderStage,
        CASE d.order_stage WHEN 0 THEN '无' WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5
        THEN '中止' END orderStageName,d.dms_status dmsStatus,d.dms_order_no dmsOrderNo
        FROM declaration_info d
        left join declaration_info_extend extend ON d.id = extend.order_id and extend.is_deleted = 0
        <where>
            d.is_deleted = 0 and (d.drp_stage = 2 or extend.sync_dms_flag = 2) and (d.is_from_app != 5 or d.is_from_app is null)
            <if test="queryOrderByDrpIdOrIdDto.drpOrderId!=null and queryOrderByDrpIdOrIdDto.drpOrderId!=''">
                AND (
                    d.drp_orderId = #{queryOrderByDrpIdOrIdDto.drpOrderId}
                    <if test="queryOrderByDrpIdOrIdDto.isNum == 1">
                        OR
                        d.id = cast(#{queryOrderByDrpIdOrIdDto.drpOrderId} as SIGNED)
                    </if>
                    OR d.install_id=#{queryOrderByDrpIdOrIdDto.drpOrderId}
                    OR d.dms_order_no=#{queryOrderByDrpIdOrIdDto.drpOrderId}
                )
            </if>
            <if test="queryOrderByDrpIdOrIdDto.storeAuthorList !=null and queryOrderByDrpIdOrIdDto.storeAuthorList.size>0">
                AND d.store_id IN
                <foreach collection="queryOrderByDrpIdOrIdDto.storeAuthorList" item="store" open="(" separator=","
                         close=")">
                    #{store.orgId}
                </foreach>
            </if>

        </where>

    </select>

    <!-- 修改订单打印小票次数 -->
    <update id="updateOrderPrint">
        UPDATE omscenter.declaration_info
        <set>
            modified_date=now(),
            print_num = print_num + 1
        </set>
        WHERE id=#{orderId}
    </update>


    <sql id="centralPurchaseWHERE">
        <where><!--7373	集采转B2B订单录入功能	app端B2B关联集采订单（集采，审核通过，未转单）-->
            is_deleted=0 AND order_type=303 AND stage=2 AND stransfer_stage=1
            <if test="inDto.salesmanId !=null and inDto.storeIdList !=null and inDto.storeIdList.size>0">
                AND
                (
                input_id=#{inDto.salesmanId}
                <if test="inDto.storeIdList !=null and inDto.storeIdList.size>0">
                    OR store_id IN
                    <foreach collection="inDto.storeIdList" item="store" open="(" separator="," close=")">
                        #{store.orgId}
                    </foreach>
                </if>
                )
            </if>
            <if test="inDto.inputId !=null"><!-- 录单人 -->
                AND input_id = #{inDto.inputId}
            </if>
            <if test="companyAuthorList !=null and companyAuthorList.size>0">
                AND company_id IN
                <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                    #{company.orgId}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 集采订单列表count查询 -->
    <select id="centralPurchaseTotal" resultType="java.lang.Integer">
        SELECT count(id) orderNum
        FROM omscenter.declaration_info
        <include refid="centralPurchaseWHERE"/>
    </select>
    <!-- 集采订单列表 -->
    <select id="centralPurchase" resultType="com.fotile.omscenter.decOrder.pojo.dto.CentralPurchaseOutDto">
        SELECT DISTINCT o.id,o.order_create_time orderCreatetime,o.clues_id cluesId,o.goods_num goodsNum,
        CASE o.stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,o.stage,
        o.buy_reason buyReason,o.business_type businessType,o.settlement_ticket settlementTicket,o.install_id
        installId,o.drp_orderId drpOrderId,o.decorate_company_code decorateCompanyCode,o.decorate_company_name
        decorateCompanyName,
        o.designer_code designerCode,o.designer_name designerName,o.designer_phone designerPhone,o.company_id
        companyId,o.store_id storeId,
        o.adviser_name adviserName,o.order_type,o.stransfer_stage stransferStage,o.to_drp_stage
        toDrpStage,o.retry_drp_num retryDrpNum,
        o.parent_id parentId,o.order_stage orderStage,o.is_to_drp isToDrp,o.drp_stage drpStage,
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        CASE
        WHEN o.is_to_drp=1 OR o.order_type=501 THEN '不同步'
        WHEN o.is_to_drp !=1 AND o.drp_stage=1 THEN '未处理'
        WHEN o.is_to_drp !=1 AND o.drp_stage=2 THEN '同步成功'
        WHEN o.is_to_drp !=1 AND o.drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.to_drp_msg toDrpMsg,
        o.goods_amount goodsAmount,
        o.contact_name contactName,o.contact_mobile contactMobile ,o.earnest_amount earnestAmount
        FROM omscenter.declaration_info o
        <include refid="centralPurchaseWHERE"/>
        <choose>
            <when test="inDto.orderSort==1">
                ORDER BY created_date ASC
            </when>
            <otherwise>
                ORDER BY created_date DESC
            </otherwise>
        </choose>
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>


    <sql id="queryDrainageChannelWhere">
        <where>
            o.is_deleted=0
            <if test="queryDrainageChannelInDto.keyWords !=null and queryDrainageChannelInDto.keyWords !=''"><!-- 关键字 -->
                AND (o.decorate_company_code LIKE '${queryDrainageChannelInDto.keyWords}%'
                OR o.decorate_company_name = = #{queryDrainageChannelInDto.keyWords,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                OR o.designer_code LIKE '${queryDrainageChannelInDto.keyWords}%'
                OR o.designer_name = #{queryDrainageChannelInDto.keyWords,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                )
            </if>
            <if test="companyAuthorList !=null and companyAuthorList.size >0">
                AND o.company_id IN
                <foreach collection="companyAuthorList" item="companyAuthor" open="(" separator="," close=")">
                    #{companyAuthor.orgId}
                </foreach>
            </if>
            <if test="authorStoreListNest != null and authorStoreListNest.size() != 0">
                and (
                <foreach collection="authorStoreListNest" item="list" separator="or">
                    o.store_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </foreach>
                )
            </if>

        </where>
    </sql>
    <!-- 查询订单所有的家装、设计师 -->
    <select id="queryDrainageChannel" resultType="com.fotile.omscenter.decOrder.pojo.dto.QueryDrainageChannelOutDto">
        SELECT DISTINCT t.code,t.name
            FROM
        (
        SELECT o.decorate_company_code code,o.decorate_company_name name
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="queryDrainageChannelWhere"/>
        AND IFNULL(o.decorate_company_code,'') !=''

        UNION

        SELECT o.designer_code code,o.designer_name name
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="queryDrainageChannelWhere"/>
        AND IFNULL(o.designer_code,'') !=''
        ) t
        limit #{pg.offset},#{pg.size}
    </select>

    <!-- 报单列表count查询 -->
    <select id="queryDrainageChannelTotal" resultType="int">
        SELECT COUNT(DISTINCT t.code)
        FROM
        (
        SELECT o.decorate_company_code code,o.decorate_company_name name
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="queryDrainageChannelWhere"/>
        AND IFNULL(o.decorate_company_code,'') !=''

        UNION

        SELECT o.designer_code code,o.designer_name name
        FROM omscenter.declaration_info o
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">
            inner join
            <include refid="authorStoreList"/>
            on authorStoreList.storeOrgId=o.store_id
        </if>-->
        <include refid="queryDrainageChannelWhere"/>
        AND IFNULL(o.designer_code,'') !=''
        ) t
    </select>

    <select id="getAmountByCluesIdList" resultType="double" >
        SELECT ifnull(sum(o.goods_amount),0.0)
        FROM omscenter.declaration_info o
        where o.stage = 2 and o.order_stage != 4
        <if test="( cluesIdList !=null and cluesIdList.size() > 0 ) or ( phone != null and phone != '' )">
            and
            (
                <if test="cluesIdList !=null and cluesIdList.size() > 0">
                    o.clues_id in
                    <foreach collection="cluesIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cluesIdList !=null and cluesIdList.size() > 0 and phone != null and phone != '' ">
                    or
                </if>
                <if test="phone != null and phone != ''">
                    o.designer_phone = #{phone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    or decorate_company_phone =  #{phone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                </if>
            )
        </if>
    </select>

    <select id="getCountByCluesIdList" resultType="int" >
        SELECT ifnull(count(1),0)
        FROM omscenter.declaration_info o
        where o.stage = 2 and o.order_stage != 4
        <if test="( cluesIdList !=null and cluesIdList.size() > 0 ) or ( phone != null and phone != '' )">
            and
            (
                <if test="cluesIdList !=null and cluesIdList.size() > 0">
                    o.clues_id in
                    <foreach collection="cluesIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cluesIdList !=null and cluesIdList.size() > 0 and phone != null and phone != '' ">
                    or
                </if>
                <if test="phone != null and phone != ''">
                    o.designer_phone = #{phone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    or decorate_company_phone =  #{phone,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                </if>
            )
        </if>
    </select>

    <select id="selectCountMspDecOrderByModifiyDate" resultType="int">
        select count(1) from omscenter.declaration_info o
        where is_deleted = 0
        <if test ="startTime != null and startTime != ''">
            AND o.stage=2
            AND o.is_to_drp = 4
            and modified_date between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="orderId != null">
            and id = #{orderId,jdbcType=BIGINT}
            and (app_pre_audit in(0,2)
            AND o.stage=2 AND o.is_to_drp = 4)
        </if>
        <if test="orderIds != null and orderIds.size() != 0">
            <foreach collection="orderIds" item="item" open="and id in(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            and (app_pre_audit in(0,2)
            AND o.stage=2 AND o.is_to_drp = 4)
        </if>
        and order_type != '501'
        and stage = 2
    </select>

    <select id="selectMspDecOrderListByModifiyDate" resultType="com.fotile.omscenter.decOrder.pojo.dto.InfoDTOS">
        select o.*,o.contact_name customerName
        from omscenter.declaration_info o
        where is_deleted = 0
        <if test ="startTime != null and startTime != ''">
            AND o.stage=2
            AND o.is_to_drp = 4
            and modified_date between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="orderId != null">
            and id = #{orderId,jdbcType=BIGINT}
            and (app_pre_audit in(0,2)
            AND o.stage=2 AND o.is_to_drp  = 4)
        </if>
        <if test="orderIds != null and orderIds.size() != 0">
            <foreach collection="orderIds" item="item" open="and id in(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            and (app_pre_audit in(0,2)
            AND o.stage=2 AND o.is_to_drp = 4)
        </if>
        and order_type != '501'
        and stage = 2
        <if test="size != null">
        limit #{num,jdbcType=INTEGER},#{size,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectDeclarationListByOrderIds" resultType="com.fotile.omscenter.decOrder.pojo.dto.DeclarationDTOS">
        select *
        from omscenter.goods_declaration
        where is_deleted = 0
        and order_id
        <foreach collection="orderIds" item="item" open="in (" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSuppleListByGoodsIds" resultType="com.fotile.omscenter.decOrder.pojo.dto.SuppleDTOS">
        select *
        from omscenter.goods_declaration_supple
        where
        is_deleted = 0
        and goods_declaration_id
        <foreach collection="goodIds" item="item" open="in (" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateDecOrderAuditByMsp">
<!--        data  List<FrInOrderAuditDto>  否  body数据-->
<!--        └─ orderId  Integer  否  订单头NO→`declaration_info`.id-->
<!--        └─ checkStatus  Integer  否  订单状态→declaration_info.order_stage-->
<!--        └─ remark  String  否  备注→declaration_info.order_note-->
<!--        └─ checkTime  DateTime  否  审核时间→declaration_info.examine_date-->
<!--        └─ advanceCheckStatus  Integer  否  预审核状态→0 否，1是    根据app_pre_audit 0：否；1：是 2:预审通过'，0传0, 1和2 传1-->
<!--        └─ goodsDtoList  List<FrInOrderAuditGoodsDto>  否  商品行-->
<!--            └─ orderId  Integer  否  订单id-->
<!--            └─ guid  String  否  guid→ goods_declaration.guid-->
<!--            └─ code  String  否  商品NO→ goods_declaration.goods_code-->
<!--            └─ name  String  否  商品名称→goods_declaration.goods_name-->
<!--            └─ productSettlementPrice  BigDecimal  否  结算价  添加字段 goods_declaration.productSettlementPrice-->
<!--            └─ price  BigDecimal  否  信用价                  添加字段 goods_declaration.price-->
<!--            └─ creditPrice  BigDecimal  否  合同价          添加字段 goods_declaration.creditPrice-->
<!--            └─ outNum  Integer  否  已出库数量→                     添加字段 goods_declaration.quantity-->
<!--            └─ clearingStatus  Integer  否  结算状态                添加字段 goods_declaration.settlement_stage-->
<!--            └─ status  Integer  否  物流状态→ goods_declaration.logistics_status-->
<!--            └─ remark  String  否  备注→ goods_declaration.goods_note-->
        update omscenter.declaration_info
        <trim prefix="set" suffixOverrides=",">
            modified_date=now(),
            <trim prefix="order_stage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkStatus != null">
                    when id = #{item.orderId} then #{item.checkStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_note = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.orderId} then #{item.remark}
                    </if>
                </foreach>
            </trim>
            <trim prefix="examine_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkTime != null">
                        when id = #{item.orderId} then #{item.checkTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="drp_orderId = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.frOrderId != null">
                        when id = #{item.orderId} then #{item.frOrderId}
                    </if>
                </foreach>
            </trim>

        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchGoodsMsp">
        <!--        └─ goodsDtoList  List<FrInOrderAuditGoodsDto>  否  商品行-->
        <!--            └─ orderId  Integer  否  订单id-->
        <!--            └─ guid  String  否  guid→ goods_declaration.guid-->
        <!--            └─ code  String  否  商品NO→ goods_declaration.goods_code-->
        <!--            └─ name  String  否  商品名称→goods_declaration.goods_name-->
        <!--            └─ productSettlementPrice  BigDecimal  否  结算价  添加字段 goods_declaration.productSettlementPrice-->
        <!--            └─ price  BigDecimal  否  信用价                  添加字段 goods_declaration.price-->
        <!--            └─ creditPrice  BigDecimal  否  合同价          添加字段 goods_declaration.creditPrice-->
        <!--            └─ outNum  Integer  否  已出库数量→                     添加字段 goods_declaration.quantity-->
        <!--            └─ clearingStatus  Integer  否  结算状态                添加字段 goods_declaration.settlement_stage-->
        <!--            └─ status  Integer  否  物流状态→ goods_declaration.logistics_status-->
        <!--            └─ remark  String  否  备注→ goods_declaration.goods_note-->
        update omscenter.goods_declaration
        <trim prefix="set" suffixOverrides=",">
            modified_date=now(),
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.code != null">
                        when id = #{item.guid} then #{item.code}
                    </if>
                </foreach>
            </trim>
            <trim prefix="goods_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.guid} then #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_settlement_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSettlementPrice != null">
                        when id = #{item.guid} then #{item.productSettlementPrice}
                    </if>
                </foreach>
            </trim>
            <trim prefix="price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.price != null">
                        when id = #{item.guid} then #{item.price}
                    </if>
                </foreach>
            </trim>
            <trim prefix="credit_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creditPrice != null">
                        when id = #{item.guid} then #{item.creditPrice}
                    </if>
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.outNum != null">
                        when id = #{item.guid} then #{item.outNum}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settlement_stage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.clearingStatus != null">
                        when id = #{item.guid} then #{item.clearingStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="logistics_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.guid} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="goods_note = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.guid} then #{item.remark}
                    </if>
                </foreach>
            </trim>

        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.guid}
        </foreach>
    </update>

    <update id="updateBatchOrderStageMspByOrderIds">
        update declaration_info
        <set>
            modified_date=NOW(),
            <if test="orderStage != null">
                order_stage = #{orderStage,jdbcType=INTEGER},
            </if>
            <if test="drpStage != null">
                drp_stage = #{drpStage,jdbcType=INTEGER},
            </if>
        </set>
        <where>
            <foreach collection="orderIds" item="item" open="id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="updateBatchDecOrderByClearingStatusMsp">
        update declaration_info
        <set>
            modified_date=NOW(),
            <trim prefix="settlement_stage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.clearingStatus != null">
                        when id = #{item.orderId} then #{item.clearingStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </set>
        <where>
            <foreach collection="list" item="item" open="id in (" close=")" separator=",">
                #{item.orderId}
            </foreach>
        </where>
    </update>
    <select id="queryDecOrderById" resultType="com.fotile.omscenter.decOrder.pojo.vo.QueryDecOrderByIdVO">
        select t.contact_mobile customerPhone
        ,t.contact_name customerName
        ,t.store_name storeName
        ,t.store_id storeId
        ,"" chargeUserPhone
        ,clues_id cluesId
        ,stage stage
        ,app_pre_audit appPreAudit
        ,is_auto_audit isAutoAudit
        ,company_id companyId
        ,distributor_id distributorId
        ,order_type orderType
        ,is_to_drp isToDrp,
        stransfer_stage stransferStage
        from omscenter.declaration_info t
        where t.id = #{id}
    </select>

    <select id="getAreaIsNullDecOrder" resultType="com.fotile.omscenter.decOrder.pojo.dto.AddDecOrderInDto">
        SELECT t.id,t.company_id from omscenter.declaration_info t where t.area_code is null
    </select>

    <update id="updateOrderArea">
        update omscenter.declaration_info o set modified_date=NOW(),o.area_code =#{areaCode}
        <if test="areaName !=null and areaName !=''">
            ,area_name=#{areaName}
        </if>
        where o.id=#{id}
    </update>

    <update id="updateDecOrderStage" parameterType="com.fotile.omscenter.decOrder.pojo.dto.UpdateDecOrderStageDto">
        update omscenter.declaration_info o set modified_date=NOW(),o.stage = 3
        where o.id in
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>
    <insert id="addLogs" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO omscenter.declaration_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            created_by,created_date,modified_by,modified_date,
            order_id,
            operator_id,
            operator_name,
            operator_type,
            msg
        </trim>
        values
        <foreach collection="list" item ="item"  separator=",">
            (
            #{item.createdBy},now(),#{item.modifiedBy},now(),
            #{item.orderId},
            #{item.operatorId},
            #{item.operatorName,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler},
            #{item.operatorType},
            #{item.msg}
            )
        </foreach>
        ;
    </insert>
    <update id="updateOrderExamineDate" parameterType="com.fotile.omscenter.decOrder.pojo.dto.UpdateDecOrderStageDto">
        update omscenter.declaration_info o set modified_date=NOW(),o.examine_date = #{currentDate}
        where o.id in
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <select id="selectOrderOfDryProcess" resultType="com.fotile.omscenter.dryProcess.pojo.dto.DecOrderMatchDTO">
        SELECT
            t.id AS orderId,
            t.clues_id AS cluesId,
            t.goods_num AS productTotalNum,
            t.goods_amount AS orderTotalPrice,
            t.delivery_date AS deliveryTime,
            CASE
                    WHEN IFNULL( t.decorate_company_code, '' ) = ''
                            AND IFNULL( t.designer_code, '' ) = '' THEN
                            0 ELSE 1
                    END AS drainageChannel,
            t.buy_reason AS purchaseReason,
            t.is_inStore AS isStoreDeal,
            t.created_date AS createdDate,
            t.clues_salesman_id AS cluesSalesmanId,
            t.input_id AS inputId,
            t.decorate_company_type AS decorateCompanyType,
            t.decorate_company_code AS decorateCompanyCode,
            t.designer_code AS designerCode,
            t.store_id AS storeId
        FROM
            omscenter.declaration_info t
        WHERE
            t.id = #{orderId}
    </select>

    <select id="selectGoodsOfDryProcess" resultType="com.fotile.omscenter.dryProcess.pojo.bo.DecOrderGoodsMatchBO">
        SELECT
            d.goods_id AS goodsId,
            d.`CODE` AS goodsCode,
            d.goods_code AS goodsModel,
            d.goods_name as goodsName,
            d.goods_num AS goodsNum,
            d.is_gift AS isGift
        FROM
            omscenter.goods_declaration d
        WHERE
            d.is_deleted = 0
            AND d.order_id = #{orderId}
    </select>

    <!-- 查询订单实体 -->
    <select id="selectByOrderId" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        SELECT o.id,o.store_id storeId,o.install_id installId,o.order_date orderCreatetime,o.order_note
                               orderNote,o.provice_id proviceId,o.provice_name proviceName,o.city_id cityId,
               o.city_name cityName,o.county_id countyId,o.county_name
                               countyName,CONCAT(IFNULL(o.contact_name,''),'~',IFNULL(o.contact_mobile,''),'~',IFNULL(o.contact_mobile_bak,''),'~',IFNULL(o.village_name,''),'',IFNULL(o.delivery_address,'')) sendinfo,
               o.settlement_ticket settlementTicket,
               CASE o.buy_reason WHEN '1' THEN '毛坯新装' WHEN '01' THEN '毛坯新装'
                                 WHEN '2' THEN '厨电换新' WHEN '02' THEN '厨电换新'
                                 WHEN '3' THEN '全屋重装' WHEN '03' THEN '全屋重装'
                                 WHEN '4' THEN '厨房局装' WHEN '04' THEN '厨房局装'
                                 WHEN '5' THEN '厨电添置' WHEN '05' THEN '厨电添置'
                                 WHEN '6' THEN '原因未知' WHEN '06' THEN '原因未知' END buyReason,
               o.delivery_date deliveryDate,o.to_drp_stage toDrpStage,o.input_name inputName,
               o.cash_amount cashAmount,o.transfer_amount transferAmount,o.card_amount cardAmount,o.company_id
                   companyId,o.clues_id cluesId,
               o.decorate_company_code decorateCompanyCode,
               o.decorate_company_name decorateCompanyName,o.is_to_drp isToDrp,o.created_date createdDate,
               o.clues_source_name cluesSourceName,o.activity_remark activityRemark,o.order_channel orderChannel,
               o.order_type orderType,o.oa_no OANo,o.sec_return_reason secReturnReason,o.sec_return_reason_name
                   secReturnReasonName,o.original_orderId originalOrderId,
               o.is_check_price isCheckPrice,o.decorate_company_type decorateCompanyType,o.decorate_category
                   decorateCompanyCategory,o.decorate_category_name decorateCompanyCategoryName,
               o.sample_type sampleType,o.is_transfer isTransfer,FIND_IN_SET(o.order_type,o.order_type_check_install)
                   allowNoRepeat,o.is_from_app isFromApp,o.install_check installCheck,o.is_auto_audit isAutoAudit,
               o.stage stage,o.app_pre_audit appPreAudit,o.store_name storeName,o.has_ambry hasAmbry ,o.drp_stage drpStage
               , o.goods_amount goodsAmount,o.pos_pay_amount posPayAmount,o.parent_id parentId,o.distributor_id distributorId,o.clues_salesman_id,o.created_by createdBy,o.earnest_amount earnestAmount
        ,o.pay_total_amount payTotalAmount, o.trade_in_flag tradeInFlag, o.area_code areaCode,
        o.sync_to_dms syncToDms
        FROM omscenter.declaration_info o
        where o.id = #{orderId}
    </select>


    <sql id="appFindServicePageAllWhere">
        <where>
            o.is_deleted=0
            <if test="findDecOrderPageAllInDto.id !=null"><!-- 报单id -->
                AND o.id=#{findDecOrderPageAllInDto.id}
            </if>

            <if test="companyAuthorList !=null and companyAuthorList.size>0"><!-- 所属分公司 -->
                AND o.company_id IN
                <foreach collection="companyAuthorList" item="company" open="(" separator="," close=")">
                    #{company.orgId}
                </foreach>
            </if>
            <if test="authorStoreListNest != null and authorStoreListNest.size() != 0">
                and (
                <foreach collection="authorStoreListNest" item="list" separator="or">
                    o.store_id in
                    <foreach collection="list" item="item" open="(" separator="," close=")">
                        #{item.orgId}
                    </foreach>
                </foreach>
                )
            </if>
            <!-- 搜索关键字 -->
            <if test="findDecOrderPageAllInDto.keyWord != null and findDecOrderPageAllInDto.keyWord != '' ">
                AND (  o.id LIKE '%${findDecOrderPageAllInDto.keyWord}%'
                    OR o.contact_name = #{findDecOrderPageAllInDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    OR o.contact_mobile = #{findDecOrderPageAllInDto.keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                    OR g.goods_code LIKE '%${findDecOrderPageAllInDto.keyWord}%'
                )
            </if>
        </where>
    </sql>
    <!-- 服务卡列表 -->
    <select id="appFindServicePageAll" resultType="com.fotile.omscenter.decOrder.pojo.dto.AppFindServicePageAllOutDto">
        SELECT o.id,o.contact_name contactName,o.contact_mobile contactMobile,
        GROUP_CONCAT(g.goods_name) model,
        CASE stage WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '审核拒绝'
        WHEN 4 THEN '已作废'
        END stageName,
        <!--CASE WHEN is_to_drp=1 THEN '不同步'
             WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理'
             WHEN is_to_drp !=1 AND drp_stage=2 THEN '同步成功'
             WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败'
        END drpStageName,-->
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,o.install_id installId
        ,CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        drpStageName
        FROM omscenter.declaration_info o
        INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
        <!-- 数据权限 -->
        <!--<if test="authorStoreList != null and authorStoreList.size > 0">
            INNER JOIN
            <include refid="authorStoreList"/>
            ON authorStoreList.storeOrgId=o.store_id
        </if>-->

        <include refid="appFindServicePageAllWhere"/>
        GROUP BY o.id
        ORDER BY g.sort
        <if test="pg!=null">
            limit #{pg.offset},#{pg.size}
        </if>
    </select>

    <!-- 服务卡列表条数 -->
    <select id="appFindServicePageAllTotal" resultType="java.lang.Integer">
        SELECT count(t.id)
        FROM(
            SELECT o.id
            FROM omscenter.declaration_info o
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id
            <!-- 数据权限 -->
            <!--<if test="authorStoreList != null and authorStoreList.size > 0">
                INNER JOIN
                <include refid="authorStoreList"/>
                ON authorStoreList.storeOrgId=o.store_id
            </if>-->
            <include refid="appFindServicePageAllWhere"/>
            GROUP BY o.id
        )t
    </select>

    <!-- 下载服务卡 -->
    <select id="downloadFindService" resultType="com.fotile.omscenter.decOrder.pojo.dto.DownloadServiceOutDto">
        SELECT DISTINCT g.goods_id goodsId,o.id orderId,g.goods_image goodsImg,o.clues_salesman_id cluesSalesmanId,
               o.store_id storeId,o.order_date orderDate,o.company_id companyId,g.goods_name goodsName,clues_id cluesId
        FROM omscenter.declaration_info o
                 LEFT JOIN omscenter.goods_declaration g ON g.is_deleted=0 AND o.id=g.order_id AND g.is_gift=0
        WHERE o.is_deleted=0  AND o.id=#{orderId}
    </select>
    <select id="getStoresId" resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.StoreOrderCount">
        SELECT   di.store_id storeId,
                 MAX(di.created_date)  orderCreateDate,
                 COUNT(di.store_id) orderCount
        FROM
            declaration_info di
        WHERE
            di.is_deleted = 0
          AND di.created_by = #{currentUserId}
          AND di.created_date &gt;= #{aheadDate}
        GROUP BY   di.store_id
        ORDER BY orderCreateDate DESC;
    </select>
    <select id="getCommonlyUsedGoodsByUser"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.GoodsInfo">
        SELECT
               gd.goods_id goodsId,
               MAX(di.created_date)  orderCreateDate,
               COUNT(gd.id) as goodsCount
        FROM declaration_info di
            LEFT JOIN goods_declaration gd ON di.id=gd.order_id
        WHERE di.is_deleted=0 AND gd.is_deleted=0
          AND di.created_by=#{currentUserId}
          AND di.created_date &gt;=#{aheadDate}
        GROUP BY gd.goods_id
        ORDER BY orderCreateDate DESC;

    </select>
    <select id="getCommonlyUsedGoodsByStoreId"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.GoodsInfo">
        SELECT
            gd.goods_id goodsId,
            COUNT(gd.id) as goodsCount
        FROM declaration_info di
                 LEFT JOIN goods_declaration gd ON di.id=gd.order_id
        WHERE di.is_deleted=0 AND gd.is_deleted=0
          AND di.store_id=#{storeOrgId}
          AND di.created_date &gt;=#{aheadDate}
        GROUP BY gd.goods_id
        ORDER BY goodsCount DESC;
    </select>
    <select id="getRecentReceiver" resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.SalesmanInfo">
        SELECT
            di.deal_receiver_id id,
               max(di.created_date) orderCreateDate,
            COUNT(*) AS orderCount
        FROM
            declaration_info di
        WHERE
            di.is_deleted = 0
          and di.deal_receiver_id is not null
          AND di.created_date &gt;=#{aheadDate}
          AND di.created_by=#{currentUserId}
        GROUP BY di.deal_receiver_id
    </select>
    <select id="getRecentDecorate" resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.DecorateInfo">
        SELECT
            di.decorate_company_type type,
        (CASE di.decorate_company_type
        WHEN 3 THEN
        di.designer_code
        ELSE
        di.decorate_company_code
        END
        ) code,
            di.designer_phone phone
        FROM
            declaration_info di
        WHERE
            di.is_deleted=0
            AND di.created_by=#{currentUserId}
        AND (CASE di.decorate_company_type WHEN 3 THEN di.designer_code ELSE di.decorate_company_code  END ) is not NULL
        AND (CASE di.decorate_company_type WHEN 3 THEN di.designer_code ELSE di.decorate_company_code  END ) !=''
          AND di.decorate_company_type IS NOT NULL
        <if test="type !=null">
            AND di.decorate_company_type =#{type}
        </if>
          <if test="keyWord !=null and keyWord !=''">
              AND
              (
              di.decorate_company_name = #{keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                  or
              di.designer_name = #{keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
                  or
              di.designer_phone = #{keyWord,typeHandler = com.fotile.framework.data.secure.util.AESTypeHandler}
              )
          </if>
          AND di.created_date &gt;=#{aheadDate}
        GROUP BY (CASE di.decorate_company_type WHEN 3 THEN di.designer_code ELSE di.decorate_company_code  END )
        order By di.id DESC
    </select>
    <select id="getRecentVillageNames" resultType="java.lang.String">
       select t.village_name FROM (
        SELECT
        di.village_name,
        MAX(di.id) mId
        FROM
        declaration_info di
        WHERE
        di.is_deleted=0
        AND di.created_by=#{currentUserId}
        AND di.village_name IS NOT NULL
        AND di.village_name !=''
        <if test="keyWord!=null and keyWord!=''">
            AND di.village_name  LIKE CONCAT('%',#{keyWord},'%')
        </if>
        AND di.created_date &gt;=#{aheadDate}
        AND di.city_name LIKE CONCAT('%',#{cityName},'%')
        GROUP BY di.village_name
        ORDER BY mId DESC
        LIMIT 2
        )t
    </select>

    <select id="getRecentVillageIdList" resultType="java.lang.Long">
        select t.village_id FROM (
        SELECT
        di.village_id,
        MAX(di.id) mId
        FROM
        declaration_info di
        WHERE
        di.is_deleted=0
        AND di.created_by=#{currentUserId}
        AND di.village_id IS NOT NULL
        <if test="keyWord!=null and keyWord!=''">
            AND di.village_name  LIKE CONCAT('%',#{keyWord},'%')
        </if>
        AND di.created_date &gt;=#{aheadDate}
        AND di.city_name LIKE CONCAT('%',#{cityName},'%')
        GROUP BY di.village_id
        ORDER BY mId DESC
        LIMIT 2
        )t
    </select>

    <select id="getRecentCitys" resultType="com.fotile.omscenter.decOrder.pojo.dto.quickOrder.CityInfo">
        SELECT
            t.cityId,
            t.cityName,
            SUM( t.num ) AS total
        FROM
            ((SELECT
                     di.city_id cityId,di.city_name cityName,COUNT(di.city_id) as num
            FROM omscenter.declaration_info di
            WHERE di.is_deleted=0
              AND di.created_date >=#{aheadDate}
              AND di.created_by=#{currentUserId}
              AND di.city_id IS NOT NULL
              AND di.city_id !=''
              <if test="keyWord!=null and keyWord !=''">
                  AND di.city_name LIKE CONCAT('%',#{keyWord},'%')
              </if>
            GROUP BY di.city_id ORDER BY num DESC)
             UNION
             (SELECT uc.city_code cityId,uc.city cityName,COUNT(uc.city_code) as num
             FROM marketingcenter.user_clues uc
             WHERE uc.is_deleted=0
               AND uc.created_date>=#{aheadDate}
               AND uc.created_by=#{currentUserId}
               AND uc.city_code IS NOT NULL
               AND uc.city_code !=''
               AND uc.city_code != -1
        <if test="keyWord!=null and keyWord !=''">
            AND uc.city LIKE CONCAT('%',#{keyWord},'%')
        </if>
             GROUP BY uc.city_code ORDER BY num DESC))t
        GROUP BY t.cityId ORDER BY total DESC
        LIMIT 6
    </select>

    <update id="batchBackTransferStage">
        update omscenter.declaration_info set modified_date=NOW(),stransfer_stage = 1
        where is_deleted = 0 and stransfer_stage = 2
          and id in
        <foreach collection="transferIdSet" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <!-- 进正式表 -->
    <update id="updateBatchGoods">
        <foreach collection="list" item="g" >
            update omscenter.goods_declaration set
            goods_category_id = #{g.goodsCategoryId}, goods_id = #{g.goodsId}, goods_name = #{g.goodsName},
            goods_price = #{g.goodsPrice}, goods_num = #{g.goodsNum}, goods_total_price = #{g.goodsTotalPrice},
            goods_image = #{g.goodsImage}, goods_code = #{g.goodsCode}, goods_note = #{g.goodsNote}, goods_type = #{g.goodsType},
            is_gift = #{g.isGift}, sort = #{g.sort}, uuid = #{g.uuid}, guid = CONCAT('HLB-',UUID()),
            location_type = #{g.locationType}, modified_by = #{g.modifiedBy}, modified_date = now(),
            <choose>
                <when test="g.deliveryDate != null and g.deliveryDate != ''">
                    delivery_date = #{g.deliveryDate},
                </when>
                <otherwise>delivery_date = null,</otherwise>
            </choose>
            source_guid = #{g.sourceGuid}, original_goodId = #{g.originalGoodId}, code = #{g.code},
            lowest_price = #{g.lowestPrice}, company_lowest_price = #{g.companyLowestPrice},
            store_lowest_price = #{g.storeLowestPrice},
            cost_estimate = #{g.costEstimate}, online = #{g.online} where id = #{g.id};
        </foreach>
    </update>

    <update id="updateBatchGoodsSupple">
        <foreach collection="list" item="g" >
            update goods_declaration_supple
            set goods_declaration_id = #{g.goodsSuppleTempEntity.goodsDeclarationId,jdbcType=BIGINT},
            pos_code = #{g.goodsSuppleTempEntity.posCode,jdbcType=VARCHAR},
            ticket_code = #{g.goodsSuppleTempEntity.ticketCode,jdbcType=VARCHAR},
            check_code = #{g.goodsSuppleTempEntity.checkCode,jdbcType=VARCHAR},
            oms_code = #{g.goodsSuppleTempEntity.omsCode,jdbcType=VARCHAR},
            is_show = #{g.goodsSuppleTempEntity.isShow,jdbcType=CHAR},
            handsel_off = #{g.goodsSuppleTempEntity.handselOff,jdbcType=DECIMAL},
            plummet_off = #{g.goodsSuppleTempEntity.plummetOff,jdbcType=DECIMAL},
            special_off = #{g.goodsSuppleTempEntity.specialOff,jdbcType=DECIMAL},
            special = #{g.goodsSuppleTempEntity.special,jdbcType=DECIMAL},
            meal = #{g.goodsSuppleTempEntity.meal,jdbcType=DECIMAL},
            red_card = #{g.goodsSuppleTempEntity.redCard,jdbcType=DECIMAL},
            market_red = #{g.goodsSuppleTempEntity.marketRed,jdbcType=DECIMAL},
            market_blue = #{g.goodsSuppleTempEntity.marketBlue,jdbcType=DECIMAL},
            non_standard2 = #{g.goodsSuppleTempEntity.nonStandard2,jdbcType=DECIMAL},
            plummet = #{g.goodsSuppleTempEntity.plummet,jdbcType=DECIMAL},
            use_ticket = #{g.goodsSuppleTempEntity.useTicket,jdbcType=DECIMAL},
            discount = #{g.goodsSuppleTempEntity.discount,jdbcType=DECIMAL},
            total_blue = #{g.goodsSuppleTempEntity.totalBlue,jdbcType=DECIMAL},
            manager_card = #{g.goodsSuppleTempEntity.managerCard,jdbcType=DECIMAL},
            meal_model = #{g.goodsSuppleTempEntity.mealModel,jdbcType=DECIMAL},
            points = #{g.goodsSuppleTempEntity.points,jdbcType=DECIMAL},
            return_ticket = #{g.goodsSuppleTempEntity.returnTicket,jdbcType=DECIMAL},
            col_return_ticket = #{g.goodsSuppleTempEntity.colReturnTicket,jdbcType=DECIMAL},
            e_ticket = #{g.goodsSuppleTempEntity.eTicket,jdbcType=DECIMAL},
            delivery_card = #{g.goodsSuppleTempEntity.deliveryCard,jdbcType=DECIMAL},
            return_ticket2 = #{g.goodsSuppleTempEntity.returnTicket2,jdbcType=DECIMAL},
            sig_card = #{g.goodsSuppleTempEntity.sigCard,jdbcType=DECIMAL},
            wealth_card = #{g.goodsSuppleTempEntity.wealthCard,jdbcType=DECIMAL},
            tow_voucher = #{g.goodsSuppleTempEntity.towVoucher,jdbcType=DECIMAL},
            gift = #{g.goodsSuppleTempEntity.gift,jdbcType=DECIMAL},
            delay_guarantee = #{g.goodsSuppleTempEntity.delayGuarantee,jdbcType=DECIMAL},
            other_promotion = #{g.goodsSuppleTempEntity.otherPromotion,jdbcType=DECIMAL},
            modified_by = #{g.goodsSuppleTempEntity.modifiedBy,jdbcType=VARCHAR},
            modified_date = now(),
            order_id = #{g.goodsSuppleTempEntity.orderId,jdbcType=BIGINT},
            goods_id = #{g.goodsSuppleTempEntity.goodsId,jdbcType=BIGINT},
            sort = #{g.goodsSuppleTempEntity.sort,jdbcType=SMALLINT},
            uuid = #{g.goodsSuppleTempEntity.uuid,jdbcType=VARCHAR},
            card_no = #{g.goodsSuppleTempEntity.cardNo,jdbcType=VARCHAR},
            card_id = #{g.goodsSuppleTempEntity.cardId,jdbcType=BIGINT},
            is_consume = #{g.goodsSuppleTempEntity.isConsume,jdbcType=TINYINT},
            card_name = #{g.goodsSuppleTempEntity.cardName,jdbcType=VARCHAR}
            where id = #{g.id,jdbcType=BIGINT};
        </foreach>
    </update>

    <!-- 删除订单商品 -->
    <update id="deleteGoodsByIdSet">
        UPDATE omscenter.goods_declaration
        SET is_deleted=1,
            modified_date=now(),
            modified_by=#{modifiedBy}
        WHERE id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteGoodsSuppleByGoodsIdSet">
        UPDATE omscenter.goods_declaration_supple
        SET is_deleted=1,
            modified_date=now(),
            modified_by=#{modifiedBy}
        WHERE goods_declaration_id in
        <foreach collection="goodsIdSet" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </update>

    <update id="deleteGoodsTempByIdSet">
        UPDATE omscenter.goods_declaration
        SET is_deleted=1,
        modified_date=now(),
        modified_by=#{modifiedBy}
        WHERE id in
        <foreach collection="tempIdSet" item="tempId" open="(" separator="," close=")">
            #{tempId}
        </foreach>
    </update>

    <update id="deleteGoodsSuppleTempByGoodsIdSet">
        UPDATE omscenter.goods_declaration_supple
        SET is_deleted=1,
        modified_date=now(),
        modified_by=#{modifiedBy}
        WHERE goods_declaration_id in
        <foreach collection="goodsTempIdSet" item="goodsTempId" open="(" separator="," close=")">
            #{goodsTempId}
        </foreach>
    </update>

    <!-- 根据记录id查询商品详情 -->
    <select id="findGoodsTempById" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsTempEntity">
        SELECT go.id,
               go.order_id          orderId,
               go.goods_category_id goodsCategoryId,
               go.goods_id          goodsId,
               go.goods_name        goodsName,
               go.goods_code        goodsCode,
               go.goods_price       goodsPrice,
               go.goods_num         goodsNum,
               go.goods_total_price goodsTotalPrice,
               goods_image          goodsImage,
               sort,
               go.goods_type        goodsType,
               goods_note           goodsNote,
               is_gift              isGift,
               uuid,
               go.location_type     locationType
                ,
               go.delivery_date     deliveryDate,
               go.created_by        createdBy,
               go.created_date      createdDate,
               go.modified_by       modifiedBy,
               go.modified_date     modifiedDate,
               go.source_guid       sourceGuid
                ,go.original_goodId originalGoodId,
                go.code             code,
                go.lowest_price     lowestPrice,
                go.cost_estimate             costEstimate,
                go.online             online
        FROM omscenter.goods_declaration_temp go
        WHERE is_deleted = 0
          AND go.id = #{id}

    </select>


    <!-- 根据设计师编码或家装公司编码查询订单列表 -->
    <select id="getOrderCountByDesignerCodeOrDecorateCompanyCode"
            resultType="com.fotile.omscenter.decOrder.pojo.DecorateCompanyOrDesignerCountDto">
        SELECT count(o.id) orderCount,
        <if test="inDto.decorateCompanyType == 3">
            o.designer_code code
        </if>
        <if test="inDto.decorateCompanyType == 1 or inDto.decorateCompanyType == 2">
            o.decorate_company_code code
        </if>
        FROM omscenter.declaration_info o where o.is_deleted = 0 AND o.order_type =101
        <if test="inDto.designerCodeList != null and inDto.designerCodeList.size() > 0">
            and o.designer_code in
            <foreach collection="inDto.designerCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="inDto.decorateCompanyCodeList != null and inDto.decorateCompanyCodeList.size() > 0">
            and o.decorate_company_code in
            <foreach collection="inDto.decorateCompanyCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        group by
        <if test="inDto.decorateCompanyType == 3">
            o.designer_code
        </if>
        <if test="inDto.decorateCompanyType == 1 or inDto.decorateCompanyType == 2">
            o.decorate_company_code
        </if>
    </select>

    <select id="getNotRejectOrderCount" resultType="java.lang.Integer">
        select count(*) from omscenter.declaration_info o where o.is_deleted = 0
        and o.store_id = #{storeId} and o.install_id = #{installId} and id != #{currentId} and o.stage in (1,2)
    </select>

    <select id="getNotDrpRejectOrderCount" resultType="java.lang.Integer">
        select count(*) from omscenter.declaration_info o where o.is_deleted = 0
        and o.store_id = #{storeId} and o.install_id = #{installId} and id != #{currentId} and o.order_stage != 4 and o.install_check = 1
    </select>

    <select id="getSellCount" resultType="com.fotile.omscenter.decOrder.pojo.QueryGoodsSellCountDto">
        SELECT
di.store_id storeId,
di.company_id companyId,
di.area_code regionCode,
SUM(gd.goods_num) sellCount
FROM declaration_info di
INNER JOIN goods_declaration gd ON di.id=gd.order_id AND gd.is_deleted=0
WHERE di.is_deleted=0
        AND di.order_stage=2
AND di.created_date BETWEEN #{dateStart} AND #{dateEnd}
AND gd.`code` IN
        <foreach collection="goodsCodes" separator="," open="(" close=")" item="goodsCode">
            #{goodsCode}
        </foreach>
        <if test="storeIds!=null and storeIds.size()>0">
            AND di.store_id IN
            <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
        </if>
GROUP BY
        <if test="groupBy==1">
            di.area_code
        </if>
        <if test="groupBy==2">
            di.company_id
        </if>
        <if test="groupBy==3">
            di.store_id
        </if>
    </select>

    <select id="findGoodsModelByCluesId" resultType="com.fotile.omscenter.decOrder.pojo.vo.FindGoodsModelByCluesIdVO">
        select d.id orderId,d.clues_id cluesId,group_concat(g.goods_code,',') goodsModel from goods_declaration g
        inner join declaration_info d on d.id = g.order_id
        <where>
            d.stage = 2
            <if test="cluesId != null">
              and  d.clues_id = #{cluesId}
            </if>
            <if test="orderId != null">
                and  d.id = #{orderId}
            </if>
        </where>
    </select>

    <select id="findPosOrderCountByKey" parameterType="map" resultType="int">
        SELECT
            count(1)
        FROM
       declaration_info info
        <where>
            info.is_draft = 0 AND info.order_type IN ( 101, 401, 501 )
            <if test="orderNo != null and orderNo != ''">
                and info.id = CAST(#{orderNo} AS SIGNED)
            </if>
            <if test="phone != null and phone != ''">
                and info.contact_mobile = #{phone}
            </if>
            <if test="storeIdList != null and storeIdList.size() > 0">
                and info.store_id in
                <foreach collection="storeIdList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findPosOrderListByKey" parameterType="map" resultType="com.fotile.omscenter.pos.pojo.dto.FindPosOrderVO">
        SELECT
            info.id orderNo,
            info.goods_amount totalAmount,
            info.pos_pay_amount amountPaid,
            info.order_create_time createDate,
            info.contact_name  contactName,
            info.contact_mobile mobile,
            info.goods_num goodsNum
        FROM
            declaration_info info
        <where>
            info.is_draft = 0 and info.order_type in (101,401,501)
            <if test="orderNo != null and orderNo != ''">
                and info.id = CAST(#{orderNo} AS SIGNED)
            </if>
            <if test="phone != null and phone != ''">
                and info.contact_mobile = #{phone}
            </if>
            <if test="storeIdList != null and storeIdList.size() > 0">
                and info.store_id in
                <foreach collection="storeIdList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </if>
        </where>
        ORDER BY
        info.order_create_time DESC
        limit #{offset},#{size}
    </select>
    <select id="queryDeclarationInfoById" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        SELECT
            id,
            clues_id,
            user_id,
            user_name,
            company_id,
            store_id,
            approve_status,
            order_create_time,
            contact_name,
            contact_mobile,
            provice_id,
            city_id,
            county_id,
            provice_name,
            city_name,
            county_name,
            delivery_address,
            delivery_date,
            user_note,
            order_note,
            goods_num,
            goods_amount,
            is_deleted,
            created_by,
            created_date,
            modified_by,
            modified_date,
            buy_reason,
            business_type,
            adviser_id,
            adviser_name,
            input_id,
            input_name,
            manager_id,
            manager_name,
            settlement_ticket,
            clues_salesman_id,
            install_id,
            contact_mobile_bak,
            stage,
            drp_stage,
            retry_drp_num,
            drp_orderId,
            is_to_drp,
            to_drp_stage,
            decorate_company_code,
            decorate_company_name,
            decorate_company_phone,
            designer_code,
            designer_name,
            designer_phone,
            cash_amount,
            transfer_amount,
            card_amount,
            order_type,
            order_stage,
            stransfer_stage,
            parent_id,
            earnest_amount,
            village_id,
            village_name,
            to_drp_msg,
            area_code,
            area_name,
            distributor_id,
            distributor_name,
            distributor_code,
            channel_category_code,
            channel_category_name,
            channel_subdivide_code,
            channel_subdivide_name,
            clues_source_code,
            clues_source_name,
            store_type,
            company_name,
            store_name,
            ware_house,
            examine_date,
            pre_examine_date,
            pre_examine,
            settlement_stage,
            audit_date,
            card_no,
            card_id,
            order_date,
            uuid,
            decorate_company_type,
            activity_remark,
            app_pre_audit,
            app_pre_examine_date,
            app_pre_examine,
            order_channel,
            is_inStore,
            original_orderId,
            goods_num_canEnter,
            is_from_app,
            out_orderId,
            first_return_reason,
            first_return_reason_name,
            sec_return_reason,
            sec_return_reason_name,
            oa_no,
            reason,
            STORAGE,
            decorate_category,
            decorate_category_name,
            is_check_price,
            cut_time,
            report_date,
            sample_type,
            is_transfer,
            order_type_check_install,
            print_num,
            central_purchase,
            store_type_code,
            store_type_name,
            clues_created_date,
            dep_id,
            install_check,
            receipt_url,
            receipt_type,
            limit_type,
            limit_number,
            is_auto_audit,
            deal_receiver_id,
            contact_mobile_enc,
            is_draft,
            is_quick,
            pos_pay_amount
        FROM
            declaration_info
        WHERE
            id = #{id} and is_deleted = 0
    </select>

    <!-- 修改订单pos支付金额 -->
    <update id="updateDecOrderPosPayAmount">
        UPDATE omscenter.declaration_info
        <set>
            <if test="posPayAmount != null">
                pos_pay_amount = #{posPayAmount},
            </if>
            pay_total_amount = #{payTotalAmount},
            remain_pay_amount = #{remainPayAmount},
            need_pay_amount = #{needPayAmount},
            pay_status = #{payStatus}
        </set>
        WHERE id=#{id} and order_type in (101,102,401,402,501)
        and exists(
            select 1 from omscenter.declaration_info_extend
            where order_id = #{id} and is_deleted = 0
            and (is_cashier_enabled = 1 or old_compensate_flag = 1)
        )
    </update>

    <select id="queryDecOrderPayInfoList" resultType="com.fotile.omscenter.decOrder.pojo.dto.DecOrderPayInfoDto">
        select id,order_id,amount_paid,pay_time,pay_type,payment_serial_number,txn_status
            from declaration_pay_info
            WHERE order_id = #{id} and is_deleted = 0
    </select>

    <select id="queryOrderPayInfoVo" resultType="com.fotile.omscenter.decOrder.pojo.dto.QueryOrderPayInfoVo">
         SELECT
             ( SELECT sum(goods_price * goods_num) from goods_declaration
              where order_id = info.id and is_deleted=0 ) totalAmount,
            info.order_type orderType,
            info.earnest_amount earnestAmount,
            info.original_orderId originalOrderId,
            info.store_id storeId,
            info.drp_stage drpStage,
            info.order_stage orderStage,
            extend.is_cashier_enabled isCashierEnabled,
            extend.old_compensate_flag oldCompensateFlag
        FROM
            declaration_info info
        left join declaration_info_extend extend on info.id = extend.order_id and extend.is_deleted = 0
        where info.id = #{id}
    </select>

    <select id="queryInvoiceList" resultType="com.fotile.omscenter.decOrder.pojo.dto.DeclarationInvoiceExportDto">
        select CASE invoice_type WHEN 2 THEN '数电普票'
        WHEN 4 THEN '数电普票'
        WHEN 1 THEN '数电专票'
        WHEN 3 THEN '不开票'
        END invoiceType,
        CASE header_type WHEN 1 THEN '个人'
        WHEN 2 THEN '公司'
        END headerType,name,tax_id taxId,address,phone,depositary_bank depositaryBank,
        bank_account bankAccount, order_id orderId
        from declaration_invoice where order_id in
        <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="findGoodsPosPriceById" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindGoodsByOrderIdOutDto">
        SELECT go.id,
        go.goods_id goodsId,
        go.goods_category_id goodsCategoryId,
        go.created_date createdDate,
        go.goods_name goodsName,
        go.goods_code goodsCode,
        go.goods_num goodsNum,
        go.goods_price goodsPrice,
        go.goods_total_price goodsTotalPrice,
        goods_image goodsImage,
        goods_note goodsNote,
        go.goods_type goodsType,
        is_gift isGift,
        go.order_id orderId,
        go.`sort`,
        go.location_type locationType,
        s.id supplyId,
        s.card_no cardNo,
        s.card_id cardId,
        go.logistics_no logisticsNo,
        go.logistics_status logisticsStatus,
        go.order_status orderStatus,
        go.goods_note lineNote,
        go.delivery_date deliveryDate,
        go.logistics_status logisticsStatus,
        go.uuid,
        go.source_guid sourceGuid,
        go.guid,
        go.lowest_price lowestPrice,
        go.cost_estimate costEstimate,
        s.card_name cardName,
        go.code code,
        go.online,
        go.pos_pay_amount posPayAmount
        FROM omscenter.goods_declaration go
        LEFT JOIN omscenter.goods_declaration_supple s ON s.is_deleted=0 AND s.goods_declaration_id = go.id
        WHERE go.order_id = #{orderId}
        AND go.is_deleted = 0
        ORDER BY go.`code` ASC
    </select>

    <update id="updatePostPayAmountByGoodsId">
            UPDATE omscenter.goods_declaration
            <set>
                modified_date=now(),modified_by=#{modifiedBy}
             <if test="posPayAmount != null ">,pos_pay_amount = #{posPayAmount}</if>
            </set>
            WHERE id=#{id}
    </update>
    <select id="querySyncFallOrderIds" resultType="java.lang.Long">
        select order_id from declaration_pay_info
        where is_deleted = 0 and need_sync = 1
        and sync_status = 2 GROUP BY order_id
    </select>


    <select id="getOrder" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        SELECT
            id,
            store_id,
            decorate_company_code,
            decorate_company_type
        FROM
            declaration_info o
        WHERE o.is_deleted = 0 and o.order_create_time &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        and o.order_create_time &lt; DATE_FORMAT(#{endTime},'%Y-%m-%d')
        order by id asc
        limit #{pg.offset},#{pg.size}
    </select>

    <update id="updateOrderChannel" >
        <foreach collection="list" item="order" separator=";">
            update declaration_info set order_channel = #{order.orderChannel},modified_date=now(),modified_by=#{order.modifiedBy}
            where id = #{order.id}
        </foreach>
    </update>


    <update id="updateDeliveryDate" >
        <if test="dtoList != null and dtoList.size() > 0">
            <foreach collection="dtoList" item="goods" separator=";">
                update goods_declaration set delivery_date = #{goods.deliveryDate},modified_date=now(),modified_by=#{userId}
                where id = #{goods.id} and order_id = #{orderId}
            </foreach>
        </if>
    </update>

    <update id="updateOrderDeliveryDate" >
        update declaration_info
        set
        version = version + 1,
        delivery_date = #{deliveryDate},
        modified_date=now(),
        modified_by=#{userId}
        where is_deleted = 0 and id = #{orderId}
    </update>

    <!-- 根据报单id查询商品列表 -->
    <select id="getDcsOrderLineInfo" resultType="com.fotile.omscenter.decOrder.pojo.dto.DcsOrderGoodsDto">
        SELECT go.id orderLineNum,
        go.guid orderLineId,
        go.code itemCode,
        go.goods_id goodsId,
        go.goods_num quantity,
        s.is_show isSample,
        s.bar_code snList,
        go.is_gift isGift,
        go.delivery_date requireDate,
        go.goods_note remark,
        go.location_type businessType,
        go.logistics_status logisticsStatus
        FROM omscenter.goods_declaration go
        LEFT JOIN omscenter.goods_declaration_supple s ON s.is_deleted=0 AND s.goods_declaration_id = go.id
        WHERE go.order_id = #{orderId}
        AND go.is_deleted = 0
        ORDER BY go.`sort` ASC
    </select>

    <!-- 根据报单id查询商品列表 -->
    <select id="getDcsOrderLineInfoForMibo" resultType="com.fotile.omscenter.decOrder.pojo.dto.DcsOrderGoodsDtoForMibo">
        SELECT go.id orderLineNum,
               go.guid orderLineId,
               go.source_guid sourceOrderLineNum,
               go.code itemCode,
               go.goods_id goodsId,
               go.goods_num quantity,
               go.goods_total_price realpayMoney,
               go.is_gift isGift,
               go.delivery_date requireDate,
               go.goods_note remark,
               go.location_type locationType,
               go.logistics_status logisticsStatus,
               go.express express,
               go.waybill_no waybillNo
        FROM omscenter.goods_declaration go
        LEFT JOIN omscenter.goods_declaration_supple s ON s.is_deleted=0 AND s.goods_declaration_id = go.id
        WHERE go.order_id = #{orderId}
          AND go.is_deleted = 0
        ORDER BY go.`sort` ASC
    </select>
    
    <!-- 根据报单id查询商品列表 -->
    <select id="getCemOrderLineInfo" resultType="com.fotile.omscenter.decOrder.pojo.SyncOrderGoodsToCemDto">
        SELECT
        go.guid guid,
        s.ticket_code ticketCode,
        go.code,
        go.goods_id goodsId,
        s.bar_code barCode,
        go.order_id orderId,
        go.goods_note goodsNote,
        go.delivery_date deliveryDate,
        go.source_guid sourceGuid,
        go.id,
        go.goods_num goodsNum,
        go.goods_price goodsPrice,
        case go.is_gift
            when 1 then 'Y'
            when 0 then 'N'
            end isGift,
        case go.is_deleted
            when -1 then 'Y'
            when 0 then 'N'
            end deleteFlag,
        s.card_no cardNo,
        go.location_type locationType
        FROM omscenter.goods_declaration go
        LEFT JOIN omscenter.goods_declaration_supple s ON s.is_deleted=0 AND s.goods_declaration_id = go.id
        WHERE go.order_id = #{orderId}
        AND go.is_deleted in (0,-1)
        ORDER BY go.`sort` ASC
    </select>

    <select id="findDepositOrderAmountByCluesId" resultType="java.lang.String">
        select sum(earnest_amount) from omscenter.declaration_info d
        <where>
            order_type = 501 and stage = 2
            <if test="cluesId != null">
                and  d.clues_id = #{cluesId}
            </if>
            <if test="orderId != null">
                and  d.id = #{orderId}
            </if>
        </where>
    </select>

    <select id="getUpdateDrpOrderDtoList" resultType="com.fotile.omscenter.decOrder.pojo.dto.UpdateDrpOrderDto">
        SELECT
            o.drp_orderId orderNo,
            o.id tradeId,
            g.guid lineId,
            o.provice_name province,
            o.city_name city,
            o.county_name town,
            o.contact_name contactName,
            o.contact_mobile contactMobile,
            o.contact_mobile_bak contactMobileBak,
            o.delivery_address deliveryAddress,
            o.delivery_date deliveryDate,
            o.village_name villageName,
            g.delivery_date needDate,
            g.location_type locationType,
            o.order_note orderNote,
            o.building, o.unit, o.house_number houseNumber
        FROM
            omscenter.declaration_info o
            INNER JOIN omscenter.goods_declaration g ON g.is_deleted = 0
            AND o.id = g.order_id
        WHERE
            o.is_deleted = 0
            AND o.id = #{orderId}
    </select>

    <update id="updateLog" >
        update declaration_log set is_deleted = id
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="testTxc" >
        update declaration_log set msg = now()
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    
    
    <select id="getGoodsByOrderIdAndGuid" resultType="com.fotile.omscenter.decOrder.pojo.entity.GoodsEntity">
        SELECT
        goods_name goodsName,code goodsCode
        from omscenter.declaration_info o
        inner join omscenter.goods_declaration g ON g.is_deleted = 0
        and o.id = g.order_id
        WHERE g.is_deleted = 0 and o.order_type in (901,902)
        and o.stage != 4 and o.order_stage != 4
        and o.original_orderId = #{orderId}
        and g.source_guid in
        <foreach collection="guidList" item="guid" open="(" close=")" separator=",">
            #{guid}
        </foreach>
    </select>


    <select id="getOrderAmountByCluesIds" resultType="com.fotile.omscenter.decOrder.pojo.dto.OrderAmountForSchemeDTO">
        select clues_id cluesId,
        sum(ord.goods_amount) as orderAmount
        from omscenter.declaration_info ord
        where ord.is_deleted = 0
        and ord.order_stage!=4
        and ord.stage=2
        and clues_id in
        <foreach collection="cluesIds" item="cluesId" open="(" close=")" separator=",">
            #{cluesId}
        </foreach>
        GROUP BY clues_id
    </select>

    <select id="getGoodsLineByIds" resultType="com.fotile.omscenter.decOrder.pojo.dto.UpdateDeliveryDateGoodsDto">
        SELECT
        g.id,g.guid,g.order_id orderId,'${deliveryDate}' deliveryDate
        from omscenter.goods_declaration g
        left join omscenter.goods_declaration_dcs dcs on dcs.is_deleted = 0 and g.order_id = dcs.order_id and dcs.order_line_id = g.id
        WHERE g.is_deleted = 0 and (dcs.out_status not in ('OUTBOUND','SIGNED','CANCELED') or dcs.out_status is null or dcs.out_status = '')
        and g.order_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateIsComeDevise" parameterType="java.lang.Long">
        update omscenter.declaration_info set is_come_devise = 1
        where is_deleted = 0 and is_come_devise = 0
        and clues_id = #{cluesId}
    </update>

    <update id="updateDecOrderSync2Es"  >
        update omscenter.declaration_info
        set modified_date = now(),
            has_attachment = #{hasAttachment}
        where is_deleted = 0
        and id = #{orderId}
    </update>


    <!--根据手机号,时间段查询订单成交金额-->
    <select id="findOrderAmountByTime" resultType="com.fotile.omscenter.decOrder.pojo.dto.FindOrderAmountByTimeOutDto">
            select contact_mobile as phone , sum(goods_amount) as orderAmount from omscenter.declaration_info
            <where>
                is_deleted = 0
                and order_stage!=4
                and stage=2
                and contact_mobile = #{phone,typeHandler=com.fotile.framework.data.secure.util.AESTypeHandler}
                <if test="companyId != null">
                    and company_id = #{companyId}
                </if>
                <if test="startTime != null and endTime != null">
                    and order_create_time between #{startTime} and #{endTime}
                </if>
            </where>
            group by contact_mobile;
    </select>

    <select id="getOrderListByExplainRecord" resultType="com.fotile.omscenter.decOrder.pojo.dto.OrderInfoForSchemeAmountDTO">
        select id as orderId,
        goods_amount as goodsAmount,
        order_type as  orderType,
        created_date as createdDate
        from omscenter.declaration_info ord
        where ord.is_deleted = 0
        and ord.order_stage!=4
        and ord.stage=2
        and ord.clues_id = #{cluesId}
        and ord.created_date <![CDATA[>=]]> #{startTime}
        and ord.goods_amount <![CDATA[>]]> 0
    </select>


    <select id="getOrderListByExplainRecordForScheme" resultType="com.fotile.omscenter.decOrder.pojo.dto.OrderInfoForSchemeAmountDTO">
        select id as orderId,
        goods_amount as goodsAmount,
        order_type as  orderType,
        created_date as createdDate,
        clues_id as cluesId
        from omscenter.declaration_info ord
        where ord.is_deleted = 0
        and ord.order_stage!=4
        and ord.stage=2
        and ord.clues_id in
        <foreach collection="cluesIds" item="cluesId" open="(" close=")" separator=",">
            #{cluesId}
        </foreach>
        and ord.goods_amount <![CDATA[>]]> 0
    </select>


    <select id="getEarliestOrderByCluesId" resultType="com.fotile.omscenter.decOrder.pojo.dto.ChangeSceneActionDTO">
        SELECT clues_id AS cluesId,
               min(created_date)  earliestTime
        FROM omscenter.declaration_info ord
        WHERE ord.is_deleted = 0
          AND ord.order_stage != 4
            AND ord.stage = 2
            and ord.is_scene_deal = 1
            AND ord.clues_id  = #{cluesId}
            <if test="orderId != null">
                and ord.id != #{orderId}
            </if>
        GROUP BY ord.clues_id;
    </select>





    <select id="queryOrderCountByCluesIds" resultType="java.lang.Integer">
        select count(distinct ord.id) from omscenter.declaration_info ord
        inner join omscenter.goods_declaration pro
        on  ord.id=pro.order_id
        and pro.is_deleted=0
        and pro.is_gift=0
        and pro.is_deleted=0
        and pro.is_return=0
        where ord.is_deleted = 0
        and ord.content_template_id is not null
        and ord.order_stage!=4
        and ord.stage not in (3,4)
        and ord.order_type in (101,301,303,401,701,901)
        and ord.manager_id  = #{chargeUserId}
        and ord.full_chargeback_flag in (0,1)
        and ord.content_template_id in
        <foreach collection="contentTemplateIds" item="cluesId" open="(" close=")" separator=",">
            #{cluesId}
        </foreach>
        <if test="startTime != null">
            and ord.order_create_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and ord.order_create_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>

    <select id="queryOrderByCluesIds"
            resultType="com.fotile.omscenter.decOrder.pojo.vo.QueryTodayOrderListByIntegrateIdVO">
        select earnest_amount,stage,id,clues_id,stransfer_stage,goods_num,created_date,input_name from omscenter.declaration_info ord
        where ord.is_deleted = 0
        and ord.clues_id in
        <foreach collection="cluesIdList" item="cluesId" open="(" close=")" separator=",">
            #{cluesId}
        </foreach>
        <if test="startTime != null">
            and ord.created_date <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and ord.created_date <![CDATA[<=]]> #{endTime}
        </if>
        limit #{offset},#{pageSize}
    </select>

    <select id="queryOrderGoodsByOrderIds" resultType="java.util.Map">
        select order_id orderId,group_concat(goods_name) goodsName from goods_declaration
        group by order_id;


    </select>

    <select id="findDecOrderByIdAndOutOrderId" resultType="com.fotile.omscenter.decOrder.pojo.dto.UpdateOrderAddressDto">
        SELECT
        o.id,
        o.out_orderId outOrderId,
        o.contact_name contactName,
        o.contact_mobile contactMobile,
        o.contact_mobile_bak contactMobileBak,
        o.provice_name provinceName,
        o.city_name cityName,
        o.county_name countyName,
        o.delivery_address address,
        o.delivery_date deliveryDate,
        extend.oaid oaid,
        o.drp_stage drpStage,
        o.order_stage orderStage,
        o.is_to_drp isToDrp,
        o.drp_orderId drpOrderId
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        WHERE o.is_deleted=0 and o.id=#{id} AND o.out_orderId = #{outOrderId}
    </select>

    <update id="updateOrderAddress" >
        update omscenter.declaration_info
        set modified_date = now(),
            modified_by = #{user.userId},
            contact_name = #{param.contactName},
            contact_mobile = #{param.contactMobile},
            contact_mobile_bak = #{param.contactMobileBak},
            provice_name = #{param.provinceName},
            city_name = #{param.cityName},
            county_name = #{param.countyName},
            delivery_address = #{param.address},
            provice_id = #{param.provinceId},
            city_id = #{param.cityId},
            county_id = #{param.countyId}
        where is_deleted = 0
          and id = #{param.id}
    </update>
    
    <select id="getIdByCluesId" resultType="java.lang.Long" parameterType="java.lang.Long">
        select id
        from declaration_info
        where is_deleted = 0 and stage = 2 and order_stage != 4 
        and clues_id = #{cluesId}
        <if test="orderId != null">
            and id != #{orderId}
        </if>
        limit 1
    </select>

    <select id="getCluesIdByCluesIds" resultType="java.lang.Long" >
        select clues_id
        from declaration_info
        where is_deleted = 0 and stage = 2 and order_stage != 4 
        and clues_id in 
        <foreach collection="cluesIds" item="cluesId" open="(" close=")" separator=",">
           #{cluesId} 
        </foreach>
        group by clues_id
    </select>


    <select id="querySceneOrderOne" resultType="com.fotile.omscenter.decOrder.pojo.dto.GetSceneOrderOneDto">
        select id,non_gift_num as goodsNum,non_gift_amount as goodsAmount,
               clues_salesman_id as cluesSalesmanId,
               adviser_id as adviserId,adviser_name as adviserName,
               store_id as storeId,store_name as storeName,
               company_id as companyId,company_name as companyName
        from omscenter.declaration_info
        where
            is_deleted = 0
          and stage = 2
          and is_scene_deal = 1
          <if test="orderTypes != null and orderTypes.size > 0">
            and order_type in
            <foreach collection="orderTypes" item="orderType" open="(" close=")" separator=",">
                #{orderType}
            </foreach>
          </if>
        order by audit_date desc
            limit 5;
    </select>

    <select id="queryOrderByIds" resultType="com.fotile.omscenter.decOrder.pojo.entity.DecOrderEntity">
        select id,contact_name,contact_name,contact_mobile,clues_id  from
        omscenter.declaration_info
        where
        is_deleted = 0
        <foreach collection="list" item="item" open=" and id in (" close=")" separator=",">
         #{item}
        </foreach>
        <if test="companyOrgIds != null and companyOrgIds.size > 0">
        <foreach collection="companyOrgIds" item="item" open=" and company_id in (" close=")" separator=",">
            #{item}
        </foreach>
        </if>
    </select>
    <select id="findDecOrderNotInvoiced"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderNoInvoicedDto">
        SELECT
            a.created_by createdBy,
            a.id orderId
        FROM
            omscenter.declaration_info a
                LEFT JOIN
            omscenter.declaration_invoice b
            ON
                b.order_id = a.id and b.is_deleted = 0
        WHERE
            a.is_deleted = 0 and
            a.order_type IN (101, 201, 301, 401, 601, 701, 801)
          AND a.dcs_out_status = 'SIGNED'
          AND (b.invoice_type = 3 or b.invoice_type is null)

          AND (a.signed_date IS NOT NULL AND a.signed_date >= CURDATE() - INTERVAL 7 DAY)
            LIMIT #{startIndex} , #{batchSize};

    </select>
    <select id="findDecOrderNotInvoicedCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            omscenter.declaration_info a
                LEFT JOIN
            omscenter.declaration_invoice b
            ON
                b.order_id = a.id and b.is_deleted = 0
        WHERE
            a.is_deleted = 0 and
            a.order_type IN (101, 201, 301, 401, 601, 701, 801)
          AND a.dcs_out_status = 'SIGNED'
          AND (b.invoice_type = 3 or b.invoice_type is null)
          AND (a.signed_date IS NOT NULL AND a.signed_date >= CURDATE() - INTERVAL 7 DAY)
    </select>


    <!-- 根据原单id,查询订单 -->
    <select id="findByOriginalOrderId" resultType="com.fotile.omscenter.decOrder.pojo.dto.ReverseOrderDto">
        SELECT
            o.id,o.goods_amount goodsAmount,o.drp_stage drpStage,extend.allow_refund_flag allowRefundFlag
        FROM
        omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        WHERE o.is_deleted = 0 and o.stage != 4 and o.order_stage != 4
        AND o.original_orderId = #{orginalOrderId}
        AND SUBSTRING(o.order_type,-1) = 2 <!--逆向订单-->
    </select>

    <!-- 根据原单id,查询订单 -->
    <select id="findIdByOriginalOrderId" resultType="java.lang.Long">
        SELECT
        o.id
        FROM
        omscenter.declaration_info o
        WHERE o.is_deleted = 0
        AND (o.original_orderId = #{orginalOrderId} or o.parent_id = #{orginalOrderId2})
    </select>

    <!-- 根据原单id,查询订单 -->
    <select id="findIdByOriginalOrderIdList" resultType="java.lang.Long">
        SELECT
            o.id
        FROM
            omscenter.declaration_info o
        WHERE o.is_deleted = 0
        AND (
            o.original_orderId in
            <foreach collection="idStrList" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
                or
            o.parent_id in
            <foreach collection="idList" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        )
    </select>

    <!-- 查询逆向订单退款金额 -->
    <select id="getRefundAmount" resultType="java.math.BigDecimal">
        SELECT
        sum(g.goods_total_price)
        FROM
        omscenter.declaration_info o
        inner join omscenter.goods_declaration g on g.is_deleted = 0 and o.id = g.order_id
        WHERE o.is_deleted = 0 and o.stage = 2
        and order_stage = 2
        AND o.original_orderId = #{orginalOrderId}
        AND SUBSTRING(o.order_type,-1) = 2
        and o.order_type not in (901,902)
    </select>

    <select id="getDiffTotalAmountSum" resultType="java.math.BigDecimal">
        SELECT
            sum(diff_total_price)
        FROM
            omscenter.goods_declaration
        WHERE is_deleted = 0
          AND order_id = #{orderId}
    </select>


    <!-- 根据原单id,查询订单 -->
    <select id="findDecOrderPreAuditTime" resultType="java.util.Date" parameterType="java.lang.Long">
        SELECT
            o.app_pre_examine_date
        FROM
        omscenter.declaration_info o
        WHERE o.is_deleted = 0
        AND o.id = #{orderId}
    </select>

    <update id="updateOrderById">
        update omscenter.declaration_info
        set
            cashier_charge_mapping = #{cashierChargeMapping},
            modified_date = now()
        where id = #{orderId}
    </update>

    <select id="queryDesignClubMemberOrderCount" resultType="java.lang.Integer">
        SELECT
          count(di.id)
        FROM
            omscenter.declaration_info di
       <include refid="queryDesignClubMemberOrderWhere">
       </include>

    </select>

    <select id="queryDesignClubMemberOrderList"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderPageAllOutDto">
        SELECT
            di.id,
            di.distributor_id,
            di.distributor_code,
            di.distributor_name,
            di.order_create_time,
            di.goods_amount,
            CASE di.stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,
            di.stage,
            di.goods_num
        FROM
            omscenter.declaration_info di
        <include refid="queryDesignClubMemberOrderWhere">
        </include>
        order by di.order_create_time desc
        limit #{pg.offset},#{pg.size}
    </select>

    <select id="querySceneOrderOneByDate" resultType="com.fotile.omscenter.decOrder.pojo.dto.GetSceneOrderOneDto">
        select id,non_gift_num as goodsNum,non_gift_amount as goodsAmount,
        clues_salesman_id as cluesSalesmanId,
        adviser_id as adviserId,adviser_name as adviserName,
        store_id as storeId,store_name as storeName,
        company_id as companyId,company_name as companyName
        from omscenter.declaration_info
        where
        is_deleted = 0
        and stage = 2
        and is_scene_deal = 1
        and non_gift_amount > 0
        <if test="endDate != null">
            and audit_date <![CDATA[<=]]> #{endDate}
        </if>
        <if test="startDate != null">
            and audit_date <![CDATA[>=]]> #{startDate}
        </if>
        <if test="orderTypes != null and orderTypes.size > 0">
            and order_type in
            <foreach collection="orderTypes" item="orderType" open="(" close=")" separator=",">
                #{orderType}
            </foreach>
        </if>
        order by non_gift_amount desc
        limit 5;
    </select>

    <sql id="queryDesignClubMemberOrderWhere">
        WHERE
        di.is_deleted = 0
        AND di.stage != '4'
        AND di.order_type in(101,301,303,401,501,701,901)
        AND di.id IN ( SELECT die.order_id FROM omscenter.declaration_info_extend die WHERE die.is_deleted = 0 AND die.designer_club_member_id = #{query.clubMemberId} )
        <!--
         AND di.decorate_category IN(1,2,3,6)
         AND (di.decorate_company_code in
         <foreach collection="query.drainageChannelList" separator="," open="(" close=")" item="item">
             #{item}
         </foreach>
         OR di.designer_code in
         <foreach collection="query.drainageChannelList" separator="," open="(" close=")" item="item">
             #{item}
         </foreach>
         ) -->
        <if test="query.search !=null and query.search !=''">
            AND(AES_DECRYPT( UNHEX(di.contact_name), #{query.secretKey} ) LIKE   CONCAT('%',#{query.search},'%')
            OR  AES_DECRYPT( UNHEX(di.contact_mobile), #{query.secretKey} )  LIKE CONCAT('%',#{query.search},'%')
            OR di.goods_amount LIKE CONCAT('%',#{query.search},'%')
            OR EXISTS (SELECT gd.id FROM goods_declaration gd WHERE gd.is_deleted=0 AND gd.order_id=di.id AND gd.goods_code LIKE CONCAT('%',#{query.search},'%'))
            )
        </if>
        <if test="query.cluesId != null">
            AND di.clues_id=#{query.cluesId}
        </if>
        <if test="query.stageList !=null and query.stageList.size()>0">
            AND di.stage IN
            <foreach collection="query.stageList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.drpStageList != null and query.drpStageList.size() > 0"><!-- 同步drp状态 -->
            <choose>
                <when test="query.drpStageList.contains(4)"><!-- 不同步 -->
                    AND (
                    di.is_to_drp = 1 or di.drp_stage in
                    <foreach collection="query.drpStageList" item="drpStage" separator="," open="(" close=")">
                        #{drpStage}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND di.is_to_drp != 1
                    AND di.drp_stage in
                    <foreach collection="query.drpStageList" item="drpStage" separator="," open="(" close=")">
                        #{drpStage}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="query.stransferStageList != null and query.stransferStageList.size() > 0">
            AND di.order_type = 501
            AND di.stransfer_stage in
            <foreach collection="query.stransferStageList" item="stransferStage" separator="," open="(" close=")">
                #{stransferStage}
            </foreach>
        </if>
        <if test="query.orderCreatetimeStart !=null and query.orderCreatetimeStart !=''">
            AND di.order_create_time >= #{query.orderCreatetimeStart}
        </if>
        <if test="query.orderCreatetimeEnd !=null and query.orderCreatetimeEnd !=''">
            AND di.order_create_time &lt;= #{query.orderCreatetimeEnd}
        </if>

    </sql>

    <select id="getSyncSuccessOrNotSyncOrder" resultType="java.lang.Long">
        SELECT o.id
        FROM omscenter.declaration_info o
        WHERE is_draft = 0 and (o.is_to_drp = 1 or o.drp_stage = 2)
        AND o.id IN
        <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <!-- 小程序端根据id查询报单详情 23591 上海以旧换新 -->
    <select id="oldSubsidyDecOrderById" resultType="com.fotile.omscenter.decOrder.pojo.dto.OldSubsidyDecOrderByIdOutDto">
        SELECT
        o.order_type,
        o.company_id companyId,
        o.company_name companyName,
        o.distributor_id distributorId,
        o.distributor_code distributorCode,
        o.distributor_name distributorName,
        o.store_id storeId,
        o.store_name storeName,
        o.store_type storeType,
        <!--订单信息-->
        o.id,<!--订单号-->
        CASE o.order_stage WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,<!--DRP订单状态名称-->
        o.order_stage orderStage,<!--DRP订单状态-->
        o.created_by createdBy,
        o.adviser_id adviserId,<!--厨电顾问id-->
        o.adviser_name adviserName,<!--厨电顾问名称-->
        o.input_id inputId,<!--厨电顾问(录入人)ID-->
        o.input_name inputName,<!--厨电顾问(录入人)名称-->
        o.original_orderId originalOrderId,<!--原销售订单号-->
        o.out_orderId outOrderId,
        o.out_order_id2 outOrderId2,
        o.stage,<!--云管理审核状态-->
        CASE stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,<!--云管理审核状态名称-->
        o.order_create_time orderCreatetime,<!--创建时间-->
        o.first_return_reason firstReturnReason,<!--一级退货原因id-->
        o.first_return_reason_name firstReturnReasonName,<!--一级退货原因名称-->
        o.sec_return_reason secReturnReason,<!--二级退货原因id-->
        o.sec_return_reason_name secReturnReasonName,<!--二级退货原因名称-->
        SUBSTRING_INDEX(o.drp_orderId,'!',-1) drpOrderId,<!--DRP订单号-->
        CASE WHEN app_pre_audit=0 THEN o.pre_examine ELSE o.app_pre_examine END preExamine,<!--预审核-->
        <!--是否需要显示预审核按钮:权限+审核通过+经销订单+需要预审+需要同步 -->
        CASE WHEN o.stage=2 AND o.order_type !=501 AND app_pre_audit=1 AND is_to_drp!=0 THEN 1 ELSE 0 END needPreAudit,
        o.examine_date examineDate,<!--DRP财务审核时间-->
        o.oa_no OANo,<!--OA编号-->
        o.parent_id parentId,<!--关联定金单号-->
        o.drp_stage drpStage,<!--同步drp状态 1：未处理；2：同步成功；3：同步失败-->
        CASE WHEN is_to_drp=1 THEN '不同步' WHEN is_to_drp !=1 AND drp_stage=1 THEN '未处理' WHEN is_to_drp
        !=1 AND drp_stage=2 THEN '同步成功' WHEN is_to_drp !=1 AND drp_stage=3 THEN '同步失败' END drpStageName,<!--同步drp状态名称-->
        o.retry_drp_num retryDrpNum,<!--同步drp错误次数-->
        o.to_drp_stage toDrpStage,<!--订单传送至DRP状态：0:根据drp设置 1：不需要预审核；2：需要审核 默认值0-->
        CASE WHEN app_pre_audit=0 THEN o.pre_examine_date ELSE o.app_pre_examine_date END preExamineDate,<!--预审核时间-->
        o.reason,<!--原因描述-->
        o.settlement_stage settlementStage,<!--结算状态-->
        o.audit_date auditDate,<!--云管理审核时间-->
        o.stransfer_stage stransferStage,<!--定金单状态-->
        o.order_date orderDate,<!--订购日期-->
        <!--业务信息-->
        o.decorate_company_type decorateCompanyType,<!--引流渠道类型 1：家装；2：异业三工 3：设计师-->
        o.clues_id cluesId,<!--线索id-->
        o.clues_salesman_id cluesSalesmanId,<!--线索负责人id-->
        o.clues_source_code cluesSourceCode,<!--线索来源-->
        o.order_note orderNote,<!--备注-->
        o.user_note userNote,
        o.order_channel orderChannel,<!--订单渠道-->
        o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.decorate_company_phone decorateCompanyPhone,
        o.designer_code designerCode,
        o.designer_name designerName,
        o.designer_phone designerPhone,
        o.activity_remark activityRemark,<!--活动说明-->
        o.buy_reason buyReason,<!--购买原因-->
        <!--配送信息-->
        o.contact_name contactName,<!--收货人-->
        o.village_id villageId,<!--小区id-->
        o.village_name villageName,<!--小区名称-->
        o.storage,<!--默认储位-->
        o.install_id installId,<!--安装单号-->
        o.contact_mobile contactMobile,<!--手机号-->
        o.provice_id proviceId,
        o.provice_name proviceName,
        o.city_id cityId,
        o.city_name cityName,
        o.county_id countyId,
        o.county_name countyName,
        o.delivery_date deliveryDate,<!--送货时间-->
        o.contact_mobile_bak contactMobileBak,<!--联系电话-->
        o.delivery_address deliveryAddress,<!--详细地址-->
        <!--结算信息-->
        o.earnest_amount earnestAmount,<!--定金金额-->
        o.cash_amount cashAmount,<!--现金金额-->
        o.transfer_amount transferAmount,<!--转账金额-->
        o.card_amount cardAmount,<!--刷卡金额-->
        o.goods_amount goodsAmount,<!--订单总金额-->
        o.goods_num goodsNum,<!--商品数量-->
        <!--********************其他冗余字段*****-->
        o.uuid,<!--订单uuid-->
        o.is_to_drp isToDrp,<!--是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP,4：同步至事业上DRP-->
        o.business_type businessType,<!--业务类型 默认值：BBC,根据订单类型传值-->
        o.to_drp_msg toDrpMsg,<!--同步至DRP返回的信息-->
        o.is_inStore isInStore,<!--是否店内成交 0：否；1：是-->
        o.manager_id managerId,
        o.manager_name managerName,
        o.clues_source_name cluesSourceName,
        o.goods_num_canEnter goodsNumCanEnter,
        o.decorate_category decorateCategory,
        o.decorate_category_name decorateCategoryName,
        o.app_pre_audit appPreAudit,
        o.is_check_price isCheckPrice,
        o.stransfer_stage stransferStage,
        o.clues_created_date cluesCreatedDate,
        o.dep_id depId,
        o.receipt_url receiptUrl,
        o.receipt_type receiptType,
        o.limit_type limitType,
        o.limit_number limitNumber,
        o.is_auto_audit isAutoAudit,
        o.deal_receiver_id dealReceiverId,
        o.is_scene_deal isSceneDeal,
        o.channel_category_code channelCategoryCode,
        o.channel_category_name channelCategoryName,
        o.channel_subdivide_name channelSubdivideName,
        o.store_type_name storeTypeName,
        o.area_code areaCode,
        o.sample_type sampleType,
        o.building building,
        o.unit unit,
        o.house_number houseNumber,
        o.dcs_out_status dcsStatus,
        o.is_from_app isFromApp,
        extend.engineering_flag engineeringFlag,
        extend.process_no processNo,
        extend.engineering_city_name engineeringCityName,
        extend.engineering_city_code engineeringCityCode,
        o.is_come_devise isComeDevise,
        o.drp_audit_reject_reason drpAuditRejectReason,
        o.ware_house wareHouse,
        extend.oaid,extend.virtual_order_flag virtualOrderFlag,extend.virtual_number_time_out virtualNumberTimeOut,
        extend.carry_mode carryMode,extend.carrier_code carrierCode,o.taobao_trade_no taobaoTradeNo,
        o.region_proxy_flag regionProxyFlag,extend.is_cashier_enabled isCashierEnabled,
        o.pos_pay_amount payAmount, o.remain_pay_amount remainPayAmount,
        o.trade_in_flag  tradeInFlag,o.real_store_id realStoreId,o.real_distributor_id realDistributorId,
        extend.designer_club_member_id designerClubMemberId, extend.designer_club_member_grade designerClubMemberGrade,
        extend.old_compensate_merchant_code oldCompensateMerchantCode,
        o.pay_status payStatus
        FROM omscenter.declaration_info o
        left join omscenter.declaration_info_extend extend on extend.is_deleted = 0 and extend.order_id = o.id
        WHERE o.id=#{id} AND o.is_deleted=0
    </select>

    <!-- 根据原单id,订单类型查询订单 -->
    <select id="findInfoByOriginalOrderIdOrderTypeList" resultType="com.fotile.omscenter.decOrder.pojo.entity.DeclarationInfo">
        SELECT
            o.id,o.stage
        FROM
            omscenter.declaration_info o
        WHERE o.is_deleted = 0
          AND o.original_orderId = #{orginalOrderId}
          AND o.order_type = #{orderType}
    </select>

    <select id="getDesignClubOrderListByCluesId"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">
        SELECT
        di.id,
        di.order_create_time orderCreatetime,
        di.clues_id cluesId,
        di.goods_num goodsNum,
        CASE
        di.stage
        WHEN 1 THEN
        '待审核'
        WHEN 2 THEN
        '审核通过'
        WHEN 3 THEN
        '审核拒绝'
        WHEN 4 THEN
        '已作废'
        END stageName,
        di.stage,
        di.buy_reason buyReason,
        di.business_type businessType,
        di.settlement_ticket settlementTicket,
        di.install_id installId,
        di.drp_orderId drpOrderId,
        di.decorate_company_code decorateCompanyCode,
        di.decorate_company_name decorateCompanyName,
        di.designer_code designerCode,
        di.designer_name designerName,
        di.designer_phone designerPhone,
        di.company_id companyId,
        di.store_id storeId,
        di.adviser_name adviserName,
        di.order_type orderType,
        di.stransfer_stage stransferStage,
        di.to_drp_stage toDrpStage,
        di.retry_drp_num retryDrpNum,
        di.parent_id parentId,
        di.order_stage orderStage,
        di.is_to_drp isToDrp,
        di.drp_stage drpStage,
        CASE
        di.order_stage
        WHEN 1 THEN
        '打开'
        WHEN 2 THEN
        '审核'
        WHEN 3 THEN
        '关闭'
        WHEN 4 THEN
        '作废'
        WHEN 5 THEN
        '中止'
        END orderStageName,
        CASE

        WHEN di.is_to_drp = 1
        OR di.order_type = 501 THEN
        '不同步'
        WHEN di.is_to_drp != 1
        AND di.drp_stage = 1 THEN
        '未处理'
        WHEN di.is_to_drp != 1
        AND di.drp_stage = 2 THEN
        '同步成功'
        WHEN di.is_to_drp != 1
        AND di.drp_stage = 3 THEN
        '同步失败'
        END drpStageName,
        di.to_drp_msg toDrpMsg,
        di.goods_amount goodsAmount,
        di.contact_name contactName,
        di.contact_mobile contactMobile,
        di.earnest_amount earnestAmount,
        di.is_auto_audit isAutoAudit,
        di.app_pre_audit appPreAudit,
        di.dcs_out_status dcsOutStatus
        FROM omscenter.declaration_info di
        <include refid="queryDesignClubMemberOrderWhere">
        </include>
        order by di.order_create_time desc
        limit #{pg.offset},#{pg.size}
    </select>

    <select id="queryCount4APPLitigation" resultType="java.lang.Integer">
        SELECT count(o.id)
        FROM omscenter.declaration_info o

        <include refid="queryCount4APPLitigationWhere"/>

    </select>

    <sql id="queryCount4APPLitigationWhere">
        <where>
            o.is_deleted = 0
            <if test="param.orderTypeList!=null and param.orderTypeList.size()!=0">
            and o.order_type in
            <foreach collection="param.orderTypeList" item="orderType" separator="," open="(" close=")">
                #{orderType}
            </foreach>
            </if>
          <if test="param.orderType != null">
              and o.order_type = #{param.orderType}
          </if>
            <if test="param.companyId !=null">
                and o.company_id = #{param.companyId}
            </if>
            <if test="param.stageList !=null and param.stageList.size()!=0">
                and o.stage in
                <foreach collection="param.stageList" item="stage" separator="," open="(" close=")">
                    #{stage}
                </foreach>
            </if>
            <if test="param.stage!=null">
                and o.stage = #{param.stage}
            </if>
        </where>
    </sql>

    <select id="queryList4APPLitigation"
            resultType="com.fotile.omscenter.decOrder.pojo.dto.FindDecOrderByCluesIdOutDto">
        SELECT
        o.id,o.order_create_time orderCreatetime,o.clues_id cluesId,o.goods_num goodsNum,
        CASE o.stage WHEN 1 THEN '待审核' WHEN 2 THEN '审核通过' WHEN 3 THEN '审核拒绝' WHEN 4 THEN '已作废' END stageName,
        o.stage,
        o.buy_reason buyReason,o.business_type businessType,o.settlement_ticket settlementTicket,
        o.install_id installId,o.drp_orderId drpOrderId,o.decorate_company_code decorateCompanyCode,
        o.decorate_company_name decorateCompanyName,
        o.designer_code designerCode,o.designer_name designerName,o.designer_phone designerPhone,
        o.company_id companyId,o.store_id storeId,
        o.adviser_name adviserName,o.order_type orderType,o.stransfer_stage stransferStage,
        o.to_drp_stage toDrpStage,o.retry_drp_num retryDrpNum,
        o.parent_id parentId,o.order_stage orderStage,o.is_to_drp isToDrp,o.drp_stage drpStage,
        CASE o.order_stage
        WHEN 1 THEN '打开' WHEN 2 THEN '审核' WHEN 3 THEN '关闭' WHEN 4 THEN '作废' WHEN 5 THEN '中止' END
        orderStageName,
        CASE
        WHEN o.is_to_drp=1 OR o.order_type=501 THEN '不同步'
        WHEN o.is_to_drp !=1 AND o.drp_stage=1 THEN '未处理'
        WHEN o.is_to_drp !=1 AND o.drp_stage=2 THEN '同步成功'
        WHEN o.is_to_drp !=1 AND o.drp_stage=3 THEN '同步失败'
        END drpStageName,
        o.to_drp_msg toDrpMsg,
        o.goods_amount goodsAmount,
        o.contact_name contactName,o.contact_mobile contactMobile ,o.earnest_amount earnestAmount,
        o.is_auto_audit isAutoAudit,o.app_pre_audit appPreAudit,
        o.dcs_out_status dcsOutStatus
        FROM omscenter.declaration_info o

        <include refid="queryCount4APPLitigationWhere"/>
        order by id desc
        limit #{pg.offset},#{pg.size}
    </select>
</mapper>
