<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.content.dao.ContentDao">

    <!--  根据分类Id查询内容条数-->
    <select id="findCountByclassificationId" resultType="java.lang.Integer">
        select count(1)
        from cmscenter.content c
        <where>
            <if test="classificationId !=null">and content_classification_id = #{classificationId}</if>
            and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
            and c.is_deleted = 0
        </where>
    </select>


    <select id="findContents" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO"
            parameterType="com.fotile.cmscenter.content.pojo.vo.ContentQueryVo">
        SELECT tt.* from (
        SELECT * from (
        SELECT c.id,c.title,c.start_time startTime,c.end_time endTime,c.type,
        cc.name as className,c.sort,
        c.auditing_status as auditStatus,c.online_status as onlineStatus,c.created_date as createTime,
        IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) as browseCount,
        IFNULL(scm.collection_count,0)+IFNULL(ccuc.collectNum,0) AS collectionCount,
        scm.comment_count as commentCount,scm.like_count as likeCount, scm.share_count as shareCount,
        scm.system_collection_count as systemCollectionCount,scm.system_browse_count as systemBrowseCount,
        c.cover_url coverUrl,qu.questions,0 as products,c.content_classification_id classId,
        c.upload_user_type as uploadUserType,c.created_person createdPerson,'' originId,'' originalAuthor,100 as intelligent,c.online_time onlineTime,100 as
        vedioMenu, if(c.is_paid = 0 , 0 , 1) as isPaid,
        ( SELECT GROUP_CONCAT( si.id)  from special_content_mapping scm
        INNER JOIN special_info si on scm.special_info_id=si.id
        WHERE scm.is_deleted=0 and si.type in ('02','03','10')
        and c.id=scm.source_id and scm.source_table_name ='content' ) as attributionCourse,
        c.second_category_id secondCategoryId,
        c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl,c.official_account_url officialAccountUrl
        ,c.kit_area kitArea,c.kit_layout kitLayout,c.kit_style kitStyle,c.kit_product kitProduct,c.kit_installation kitInstallation,c.kit_type kitType
        ,c.summary,c.show_type as showType, c.created_by as createdBy,'' menuDifficulty
        from cmscenter.content c

        <if test=" contentQueryVo.calcStartDate != null or contentQueryVo.calcEndDate != null">
            inner join (
                select distinct
                    cbm.content_id
                from cmscenter.statistics_content_bjt_mapping cbm
                where
                    cbm.is_deleted = 0 and cbm.content_type = 1
                    <if test=" contentQueryVo.calcStartDate != null">
                        and cbm.calc_date &gt;= #{contentQueryVo.calcStartDate}
                    </if>
                    <if test=" contentQueryVo.calcEndDate != null">
                        and cbm.calc_date &lt;= #{contentQueryVo.calcEndDate}
                    </if>
            ) tt on tt.content_id = c.id
        </if>
        <if test=" contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0">
            inner join (
                SELECT DISTINCT
                    cgm.source_id as contentId
                FROM
                    cmscenter.content_goods_mapping cgm
                    WHERE
                    cgm.is_deleted = 0
                    AND cgm.source_table = 'content'
                    AND cgm.goods_id IN
                    <foreach collection="contentQueryVo.goodsIdList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            ) cgm2 on cgm2.contentId = c.id
        </if>
        <if test=" contentQueryVo.goodsRelationFlag != null and contentQueryVo.goodsRelationFlag == 1 ">
            inner join (
            SELECT DISTINCT
            cgmrelation.source_id as contentId
            FROM
            cmscenter.content_goods_mapping cgmrelation
            WHERE
            cgmrelation.is_deleted = 0
            AND cgmrelation.source_table = 'content'
            ) cgmrelation2 on cgmrelation2.contentId = c.id
        </if>
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content' and scm.source_id=c.id and
        scm.is_deleted=0 )
        LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
        'content'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        LEFT JOIN ( SELECT count( 1 ) AS collectNum, content_id AS caseIds FROM cmscenter.content_case_user_collect
        WHERE is_collect = 1 AND is_deleted = 0 and content_type = 1 GROUP BY content_id ) ccuc ON ccuc.caseIds = c.id
        where c.is_deleted=0
        <if test="contentQueryVo.showType != null">
            and c.show_type = #{contentQueryVo.showType}
        </if>
        <if test="contentQueryVo.secondCategoryIdList != null and contentQueryVo.secondCategoryIdList.size()>0">
            and c.second_category_id in
            <foreach collection="contentQueryVo.secondCategoryIdList" item="secondCategoryId" open="(" close=")"  separator=",">
                #{secondCategoryId}
            </foreach>
        </if>
        <if test="contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0">
            and c.community_id in
            <foreach collection="contentQueryVo.communityIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
            and cc.id in
            <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.activityId !=null">
            and c.activity_id =#{contentQueryVo.activityId}
        </if>
        <if test="contentQueryVo.isAuthorization != null">
            and c.is_authorization=#{contentQueryVo.isAuthorization}
        </if>
        <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            cmscenter.label_cms_mapping lcm
            WHERE
            lcm.source_table_name = 'content'
            AND lcm.is_deleted = 0
            and lcm.label_id in
            <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.channel_radio_cms_mapping crcm
            WHERE
            crcm.source_table_name = 'content'
            AND crcm.is_deleted =0
            <trim prefix="and " suffixOverrides="or">
                <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="item.channelId !=null and item.channelId !=''">
                            crcm.channel_id=#{item.channelId}
                        </if>
                        <if test="item.ratioId !=null and item.ratioId !=''">
                            and crcm.radio_id=#{item.ratioId}
                        </if>
                    </trim>
                </foreach>
            </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            select DISTINCT
            c.id
            from cmscenter.content c
            left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
            where
            c.is_deleted = 0
                and (ccm.source_id is null
                or ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            )
            <!--SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )-->
        </if>
        <if test="contentQueryVo.audioVideoType!=null and contentQueryVo.audioVideoType !=''">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            audio_video
            WHERE
            is_deleted = 0
            AND source_table_name = 'content'
            and type=#{contentQueryVo.audioVideoType}
            )
        </if>
        <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
            and c.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
        </if>
        <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
            and c.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
        </if>
        <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
            and c.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
        </if>
        <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
            and c.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
        </if>
        <if test="contentQueryVo.id !=null">
            and c.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
            and concat('',c.id,'') in
            <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
            and (
            concat('N',c.id) like concat('%',#{contentQueryVo.keywords},'%')
            or
            c.title like CONCAT('%',#{contentQueryVo.keywords},'%')
            )
        </if>

        <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
        </if>
        <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and c.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and c.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
            and c.created_person =#{contentQueryVo.createdPerson}
        </if>
        <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
            and c.type!= 5
        </if>
        <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
            and '' like CONCAT('%',#{contentQueryVo.foodName},'%')
        </if>
        <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
            and c.upload_user_type = #{contentQueryVo.uploadUserType}
        </if>
        <if test="contentQueryVo.intelligent!=null">
            and 100 = #{contentQueryVo.intelligent}
        </if>
        <if test="contentQueryVo.menuDifficulty !=null and contentQueryVo.menuDifficulty != ''">
            and '' = #{contentQueryVo.menuDifficulty}
        </if>
        <if test="contentQueryVo.vedioMenu!=null">
            and 100 = #{contentQueryVo.vedioMenu}
        </if>
        <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
            and c.is_paid= #{contentQueryVo.isPaid}
        </if>
<!--        厨房案例权限校验-->
        <if test="contentQueryVo.purview != 1">
            and c.type != 15
        </if>
        <if test="contentQueryVo.type !=null and contentQueryVo.type !=''">
            and c.type=#{contentQueryVo.type}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and c.type in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
 <!--        过滤厨房案例草稿-->
        and c.is_draft = 0
<!--        厨房案例推荐内容筛选-->
        <if test="caseRecommendList != null and caseRecommendList.size() != 0">
            and case_recommend in
            <foreach collection="caseRecommendList" item="caseRecommend" open="(" separator="," close=")">
                #{caseRecommend}
            </foreach>
        </if>
        <if test="isRecommendHome != null">
            <choose>
                <when test="caseRecommendList != null and caseRecommendList.size() != 0">
                    or
                </when>
                <otherwise>
                    and
                </otherwise>
            </choose>
            is_recommend_home = #{isRecommendHome}
        </if>
        <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
            and EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
            and not EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        group by c.id
        <choose>
            <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                <if test="contentQueryVo.orderBy=='createTime asc'.toString()">
                    order by c.created_date asc
                </if>
                <if test="contentQueryVo.orderBy=='createTime desc'.toString()">
                    order by c.created_date desc
                </if>
                <if test="contentQueryVo.orderBy=='browseCount asc'.toString()">
                    order by scm.browse_count asc
                </if>
                <if test="contentQueryVo.orderBy=='browseCount desc'.toString()">
                    order by scm.browse_count desc
                </if>
                <if test="contentQueryVo.orderBy=='commentCount asc'.toString()">
                    order by scm.comment_count asc
                </if>
                <if test="contentQueryVo.orderBy=='commentCount desc'.toString()">
                    order by scm.comment_count desc
                </if>
                <if test="contentQueryVo.orderBy=='shareCount asc'.toString()">
                    order by scm.share_count asc
                </if>
                <if test="contentQueryVo.orderBy=='shareCount desc'.toString()">
                    order by scm.share_count desc
                </if>
                <if test="contentQueryVo.orderBy=='collectionCount asc'.toString()">
                    order by collectionCount asc
                </if>
                <if test="contentQueryVo.orderBy=='collectionCount desc'.toString()">
                    order by collectionCount desc
                </if>
                <if test="contentQueryVo.orderBy=='likeCount asc'.toString()">
                    order by scm.like_count asc
                </if>
                <if test="contentQueryVo.orderBy=='likeCount desc'.toString()">
                    order by scm.like_count desc
                </if>
                <if test="contentQueryVo.orderBy=='questions asc'.toString()">
                    order by qu.questions asc
                </if>
                <if test="contentQueryVo.orderBy=='questions desc'.toString()">
                    order by qu.questions desc
                </if>
                <if test="contentQueryVo.orderBy=='onlineTime asc'.toString()">
                    order by c.online_time asc
                </if>
                <if test="contentQueryVo.orderBy=='onlineTime desc'.toString()">
                    order by c.online_time desc
                </if>
                <if test="contentQueryVo.orderBy=='sort asc'.toString()">
                    order by c.sort asc
                </if>
                <if test="contentQueryVo.orderBy=='sort desc'.toString()">
                    order by c.sort desc
                </if>

            </when>
            <otherwise>
                order by c.created_date DESC
            </otherwise>
        </choose>
        ,c.id DESC
        limit #{contentQueryVo.offsetAndSize}
        ) a
        <if test=" contentQueryVo.calcStartDate == null and contentQueryVo.calcEndDate == null
        and !(contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0)
        and !(contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0)
        and contentQueryVo.showType == null">
            UNION ALL
            select * from (
            SELECT cm.id,cm.title,cm.start_time as startTime,cm.end_time as endTime,5 as type,cc.name as className,cm.sort,
            cm.auditing_status as auditStatus,cm.online_status as onlineStatus,cm.created_date as createTime,
            scm.browse_count as browseCount,scm.collection_count as collectionCount,
            scm.comment_count as commentCount,scm.like_count as likeCount, scm.share_count as shareCount,
            scm.system_collection_count as systemCollectionCount,scm.system_browse_count as systemBrowseCount,
            cm.cover_url coverUrl,qu.questions,pr.products,cm.content_category_id classId,
            cm.upload_user_type uploadUserType,cm.created_person createdPerson,cm.origin_id originId,cm.original_author originalAuthor,
            if(cm.origin_id is null,0,1) intelligent,cm.online_time onlineTime,cm.vedio_menu vedioMenu, if(cm.is_paid = 0 , 0 , 1) as isPaid,
            ( SELECT GROUP_CONCAT( si.id)  from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and cm.id=scm.source_id and scm.source_table_name ='content_menu' ) as attributionCourse,
            null as secondCategoryId,
            '' as inspireVideoUrl,'' as  inspireVideoCoverUrl,'' as  officialAccountUrl
            ,'' kitArea,'' kitLayout,'' kitStyle,'' kitProduct,'' kitInstallation,'' kitType
            ,cm.menu_abstract as summary, '' showType, cm.created_by as createdBy,cm.menu_difficulty menuDifficulty
            from cmscenter.content_menu cm
            LEFT JOIN cmscenter.content_category cc on cc.id=cm.content_category_id and cc.is_deleted=0
            LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content_menu' and scm.source_id=cm.id
            and scm.is_deleted = 0)
            LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
            'content_menu'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=cm.id)
            LEFT JOIN (SELECT COUNT(1) products,content_menu_id from cmscenter.show_product where content_menu_id >0 and
            is_deleted=0 GROUP BY
            content_menu_id) pr on(cm.id=pr.content_menu_id)
            where cm.is_deleted=0
            <if test="contentQueryVo.secondCategoryIdList != null and contentQueryVo.secondCategoryIdList.size()>0">
                and false
            </if>
            <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
                and cc.id in
                <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
                and cm.id in (
                SELECT DISTINCT
                source_id
                FROM
                cmscenter.label_cms_mapping lcm
                WHERE
                lcm.source_table_name = 'content_menu'
                AND lcm.is_deleted =0
                and lcm.label_id in
                <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
                and cm.id in (
                SELECT
                DISTINCT source_id
                FROM
                cmscenter.channel_radio_cms_mapping crcm
                WHERE
                crcm.source_table_name = 'content_menu'
                AND crcm.is_deleted =0
                <trim prefix="and " suffixOverrides="or">
                    <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                        <trim prefix="(" suffix=")" prefixOverrides="and">
                            <if test="item.channelId !=null and item.channelId !=''">
                                crcm.channel_id=#{item.channelId}
                            </if>
                            <if test="item.ratioId !=null and item.ratioId !=''">
                                and crcm.radio_id=#{item.ratioId}
                            </if>
                        </trim>
                    </foreach>
                </trim>
                )
            </if>
            <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
                and cm.id in(
                SELECT DISTINCT
                source_id
                FROM
                cmscenter.company_cms_mapping ccm
                WHERE
                ccm.source_table_name = 'content_menu'
                AND ccm.is_deleted =0
                and ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
                and cm.id in(
                select DISTINCT
                c.id
                from cmscenter.content_menu c
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu' and c.id = ccm.source_id
                where
                c.is_deleted = 0
                    and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                )
                <!--SELECT DISTINCT
                source_id
                FROM
                cmscenter.company_cms_mapping ccm
                WHERE
                ccm.source_table_name = 'content_menu'
                AND ccm.is_deleted =0
                and ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )-->
            </if>
            <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
                and cm.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
            </if>
            <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
                and cm.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
            </if>
            <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
                and cm.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
            </if>
            <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
                and cm.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
            </if>
            <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
                and cm.id IN (
                SELECT DISTINCT
                fg.content_menu_id
                FROM
                food_group fg,
                food_info_mapping fim,
                food_info fi
                WHERE
                fg.id = fim.food_group_id
                AND fim.food_id = fi.id
                AND fim.is_deleted = 0
                AND fim.is_deleted = 0
                AND fi.is_deleted = 0
                AND fi.food_name LIKE CONCAT('%',#{contentQueryVo.foodName},'%')
                )
            </if>
            <if test="contentQueryVo.id !=null">
                and cm.id=#{contentQueryVo.id}
            </if>
            <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
                and cm.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
            </if>
            <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
                and (IFNULL(cm.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
                or IFNULL(cm.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
            </if>
            <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
                and (IFNULL(cm.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
                or IFNULL(cm.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
            </if>
            <if test="contentQueryVo.auditStatus !=null ">
                and cm.auditing_status=#{contentQueryVo.auditStatus}
            </if>
            <if test="contentQueryVo.onlineStatus !=null">
                and cm.online_status=#{contentQueryVo.onlineStatus}
            </if>
            <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
                and cm.created_person =#{contentQueryVo.createdPerson}
            </if>
            <if test="(contentQueryVo.type !=null and contentQueryVo.type !=''
            ) or (contentQueryVo.types != null and contentQueryVo.types !='')">
                and 5=#{contentQueryVo.type}
            </if>
            <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
                and 5 != 5
            </if>
            <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
                and cm.upload_user_type = #{contentQueryVo.uploadUserType}
            </if>
            <if test="contentQueryVo.intelligent!=null">
                and if(cm.origin_id is null,0,1) = #{contentQueryVo.intelligent}
            </if>
            <if test="contentQueryVo.menuDifficulty !=null and contentQueryVo.menuDifficulty != ''">
                and cm.menu_difficulty = #{contentQueryVo.menuDifficulty}
            </if>
            <if test="contentQueryVo.vedioMenu!=null">
                and cm.vedio_menu = #{contentQueryVo.vedioMenu}
            </if>
            <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
                and cm.is_paid= #{contentQueryVo.isPaid}
            </if>
            <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
                and EXISTS  ( SELECT 1 from special_content_mapping scm
                INNER JOIN special_info si on scm.special_info_id=si.id
                WHERE scm.is_deleted=0 and si.type in ('02','03','10')
                and cm.id=scm.source_id and scm.source_table_name ='content_menu' )
            </if>
            <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
                and not EXISTS  ( SELECT 1 from special_content_mapping scm
                INNER JOIN special_info si on scm.special_info_id=si.id
                WHERE scm.is_deleted=0 and si.type in ('02','03','10')
                and cm.id=scm.source_id and scm.source_table_name ='content_menu' )
            </if>
            <if test="contentQueryVo.activityId !=null">
                and 0 =#{contentQueryVo.activityId}
            </if>
            group by cm.id
            <choose>
                <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                    <if test="contentQueryVo.orderBy=='createTime asc'.toString()">
                        order by cm.created_date asc
                    </if>
                    <if test="contentQueryVo.orderBy=='createTime desc'.toString()">
                        order by cm.created_date desc
                    </if>
                    <if test="contentQueryVo.orderBy=='browseCount asc'.toString()">
                        order by scm.browse_count asc
                    </if>
                    <if test="contentQueryVo.orderBy=='browseCount desc'.toString()">
                        order by scm.browse_count desc
                    </if>
                    <if test="contentQueryVo.orderBy=='commentCount asc'.toString()">
                        order by scm.comment_count asc
                    </if>
                    <if test="contentQueryVo.orderBy=='commentCount desc'.toString()">
                        order by scm.comment_count desc
                    </if>
                    <if test="contentQueryVo.orderBy=='shareCount asc'.toString()">
                        order by scm.share_count asc
                    </if>
                    <if test="contentQueryVo.orderBy=='shareCount desc'.toString()">
                        order by scm.share_count desc
                    </if>
                    <if test="contentQueryVo.orderBy=='collectionCount asc'.toString()">
                        order by scm.collection_count asc
                    </if>
                    <if test="contentQueryVo.orderBy=='collectionCount desc'.toString()">
                        order by scm.collection_count desc
                    </if>
                    <if test="contentQueryVo.orderBy=='likeCount asc'.toString()">
                        order by scm.like_count asc
                    </if>
                    <if test="contentQueryVo.orderBy=='likeCount desc'.toString()">
                        order by scm.like_count desc
                    </if>
                    <if test="contentQueryVo.orderBy=='questions asc'.toString()">
                        order by qu.questions asc
                    </if>
                    <if test="contentQueryVo.orderBy=='questions desc'.toString()">
                        order by qu.questions desc
                    </if>
                    <if test="contentQueryVo.orderBy=='products asc'.toString()">
                        order by pr.products asc
                    </if>
                    <if test="contentQueryVo.orderBy=='products desc'.toString()">
                        order by pr.products desc
                    </if>
                    <if test="contentQueryVo.orderBy=='onlineTime asc'.toString()">
                        order by cm.online_time asc
                    </if>
                    <if test="contentQueryVo.orderBy=='onlineTime desc'.toString()">
                        order by cm.online_time desc
                    </if>
                    <if test="contentQueryVo.orderBy=='sort asc'.toString()">
                        order by cm.sort asc
                    </if>
                    <if test="contentQueryVo.orderBy=='sort desc'.toString()">
                        order by cm.sort desc
                    </if>

                </when>
                <otherwise>
                    order by cm.created_date DESC
                </otherwise>
            </choose>
            ,cm.id DESC
            limit #{contentQueryVo.offsetAndSize}
            ) b
        </if>
        ) tt
        <choose>
            <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                order by ${contentQueryVo.orderBy}
            </when>
            <otherwise>
                order by createTime DESC
            </otherwise>
        </choose>
        ,id DESC
        limit #{contentQueryVo.offset},#{contentQueryVo.size}
    </select>


    <select id="findContentsAI" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryAIBaseDTO">
        SELECT
            c.id,
            c.title,
            c.start_time startTime,
            c.end_time endTime,
            c.type,
            cc.NAME AS className,
            c.sort,
            c.auditing_status AS auditStatus,
            c.online_status AS onlineStatus,
            c.created_date AS createTime,
            c.cover_url coverUrl,
            qu.questions,
            0 AS products,
            c.content_classification_id classId,
            c.upload_user_type AS uploadUserType,
            c.created_person createdPerson,
            '' originId,
            '' originalAuthor,
            100 AS intelligent,
            c.online_time onlineTime,
            100 AS vedioMenu,
            IF
                ( c.is_paid = 0, 0, 1 ) AS isPaid,

            c.second_category_id secondCategoryId,
            c.inspire_video_url inspireVideoUrl,
            c.inspire_video_cover_url inspireVideoCoverUrl,
            c.official_account_url officialAccountUrl,
            c.kit_area kitArea,
            c.kit_layout kitLayout,
            c.kit_style kitStyle,
            c.kit_product kitProduct,
            c.kit_installation kitInstallation,
            c.kit_type kitType,
            c.kitchen_case_type kitchenCaseType,
            c.community_name communityName,
            c.is_deleted isDeleted,
            (SELECT
            count(1)
            FROM
            cmscenter.statistics_content_bjt_mapping_detail md
            WHERE
            md.is_deleted = 0
            and md.type = 1
                AND md.content_id = c.id
                and md.content_type = 1
            ) sendNum
        FROM
            cmscenter.content c
                LEFT JOIN cmscenter.content_category cc ON ( cc.id = c.content_classification_id AND cc.is_deleted = 0 )
                LEFT JOIN ( SELECT count( 1 ) questions, source_id FROM cmscenter.question_info WHERE source_table_name = 'content' AND is_deleted = 0 GROUP BY source_table_name, source_id ) qu ON ( qu.source_id = c.id )
                LEFT JOIN (
                SELECT
                    count( 1 ) AS collectNum,
                    content_id AS caseIds
                FROM
                    cmscenter.content_case_user_collect
                WHERE
                    is_collect = 1
                  AND is_deleted = 0
                  AND content_type = 1
                GROUP BY
                    content_id
            ) ccuc ON ccuc.caseIds = c.id
        WHERE
            c.type =${type}
          AND c.is_draft = 0
        <if test="modifiedDateStart != null">
            AND (c.modified_date &gt;= #{modifiedDateStart} or
                EXISTS (SELECT
                1
                FROM
                cmscenter.statistics_content_bjt_mapping_detail md
                WHERE
                md.content_id = c.id
                AND md.is_deleted = 0
                AND md.type = 1
                AND md.content_type = 1 and md.modified_date &gt;= #{modifiedDateStart})
            )
        </if>
        GROUP BY
            c.id
    </select>





    <select id="findContentsByIdsForSpecial" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO"
            parameterType="com.fotile.cmscenter.content.pojo.vo.ContentQueryVo">
        SELECT c.id,c.title,c.start_time startTime,c.end_time endTime,c.type,cc.name as className,c.sort,
        c.auditing_status as auditStatus,c.online_status as onlineStatus,c.created_date as createTime,
        IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) as browseCount,
        IFNULL(scm.collection_count,0)+IFNULL(ccuc.collectNum,0) AS collectionCount,
        scm.comment_count as commentCount,scm.like_count as likeCount, scm.share_count as shareCount,
        scm.system_collection_count as systemCollectionCount,scm.system_browse_count as systemBrowseCount,
        c.cover_url coverUrl,qu.questions,0 as products,c.content_classification_id classId,
        c.upload_user_type as uploadUserType,c.created_person createdPerson,'' originId,'' originalAuthor,100 as intelligent,c.online_time onlineTime,100 as
        vedioMenu, if(c.is_paid = 0 , 0 , 1) as isPaid,
        ( SELECT GROUP_CONCAT( si.id)  from special_content_mapping scm
        INNER JOIN special_info si on scm.special_info_id=si.id
        WHERE scm.is_deleted=0 and si.type in ('02','03','10')
        and c.id=scm.source_id and scm.source_table_name ='content' ) as attributionCourse,
        c.second_category_id secondCategoryId
        from cmscenter.content c
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content' and scm.source_id=c.id and
        scm.is_deleted=0 )
        LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
        'content'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        LEFT JOIN ( SELECT count( 1 ) AS collectNum, content_id AS caseIds FROM cmscenter.content_case_user_collect
        WHERE is_collect = 1 AND is_deleted = 0 and content_type = 1 GROUP BY content_id ) ccuc ON ccuc.caseIds = c.id
        where c.is_deleted=0
        <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
            and concat('',c.id,'') in
            <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        group by c.id
    </select>






    <select id="findTitleByCategoryId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentCategoryVo"
            parameterType="long">
        SELECT id, title,sort,content_classification_id categoryId
        FROM cmscenter.content
        WHERE content_classification_id = #{classId}
        AND is_deleted = 0
    </select>
    <select id="findContentBycontentId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoVo"
            parameterType="long">
        SELECT c.id,c.type,c.created_person,cc.name,c.title,c.cover_url coverUrl,c.created_date createdDate,c.auditing_status
        auditingStatus,
        c.online_status onlineStatus,scm.browse_count browseCount,scm.collection_count collectionCount,qu.questions
        FROM cmscenter.content c
        LEFT JOIN ( SELECT browse_count,collection_count,source_id
        FROM cmscenter.statistics_cms_mapping
        WHERE source_table_name = 'content'
        AND is_deleted = 0
        ) scm ON (c.id = scm.source_id)
        LEFT JOIN (SELECT count(*) questions,source_id
        FROM cmscenter.question_info
        WHERE source_table_name = 'content'
        and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        LEFT JOIN content_category cc ON cc.id = c.content_classification_id
        WHERE c.is_deleted = 0
        and c.id=#{contentId}
    </select>

    <select id="findSpecialContentBycontentId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoVo"
            parameterType="long">
         SELECT c.id,c.type,c.created_person,cc.name,c.title,c.cover_url coverUrl,c.created_date createdDate,c.auditing_status
        auditingStatus,
        c.online_status onlineStatus,scm.browse_count browseCount,scm.collection_count collectionCount,c.is_paid isPaid
        FROM cmscenter.content c
        LEFT JOIN cmscenter.statistics_cms_mapping scm ON (c.id = scm.source_id and scm.source_table_name = 'content' AND scm.is_deleted = 0)
        LEFT JOIN content_category cc ON cc.id = c.content_classification_id
        WHERE c.is_deleted = 0
        and c.id=#{contentId}
    </select>


    <select id="findContentsByIds" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO">
        SELECT c.id,
               c.type,
               cc.name,
               c.title,
               c.cover_url     coverUrl,
               c.created_date  createdDate,
               c.auditing_status
                               auditingStatus,
               c.online_status onlineStatus,
        c.second_category_id secondCategoryId
        FROM cmscenter.content c
                 LEFT JOIN content_category cc ON cc.id = c.content_classification_id
        WHERE c.is_deleted = 0
          and c.id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>



    <select id="findMiniContentById" parameterType="long"
            resultType="com.fotile.cmscenter.content.pojo.vo.ContentMiniVo">
        SELECT id, cover_url coverUrl,title,summary,content_text contentText,
        area_desc,layout_desc,style_desc,type_desc,
        panorama_url panoramaUrl,panorama_cover_url panoramaCoverUrl,
        tag_picture tagPicture,
        shared_url sharedUrl,shared_words sharedWords,original_author originalAuthor,
        inspire_video_url inspireVideoUrl,inspire_video_cover_url inspireVideoCoverUrl,
        official_account_url officialAccountUrl,
        first_category_id firstCategoryId,second_category_id secondCategoryId,
        custom_jump_pic_url customJumpPicUrl,
        custom_jump_url customJumpUrl,
        custom_jump_channel customJumpChannel
        FROM cmscenter.content
        WHERE is_deleted = 0
        AND id =#{contentId}
    </select>
    <select id="findMiniContentAllByRadioId" parameterType="long"
            resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT c.type,c.cover_url coverUrl,c.id,c.activity_id activityId,c.skip_url skipUrl,c.title,c.summary
        FROM cmscenter.content c,cmscenter.channel_radio_cms_mapping crcm
        WHERE c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        AND c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        AND crcm.radio_id=#{radioId}
        ORDER by c.sort DESC
        limit #{offset},#{size}
    </select>
    <select id="queryPartContentById" resultType="com.fotile.cmscenter.content.pojo.vo.ContentInfoClassVo"
            parameterType="long">
        SELECT c.id contentId,c.type,cc.name className,c.start_time startTime,c.end_time endTime,c.title,
        c.auditing_status auditingStatus,c.online_status onlineStatus,'content' as contentTable,
        c.sort contentSort
        FROM cmscenter.content c
        LEFT JOIN cmscenter.content_category cc ON c.content_classification_id = cc.id AND cc.is_deleted = 0
        WHERE
        c.is_deleted = 0
        AND c.id = #{id}
    </select>
    <select id="findH5ContentAll" parameterType="com.fotile.cmscenter.content.pojo.dto.H5QueryContentDTO"
            resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT c.type,c.cover_url coverUrl,c.id,c.activity_id activityId,c.skip_url skipUrl,c.title,c.summary,
        c.panorama_url panoramaUrl,c.panorama_cover_url panoramaCoverUrl
        FROM cmscenter.content c
        LEFT JOIN (SELECT * from cmscenter.channel_radio_cms_mapping where is_deleted=0 and source_table_name =
        'content')
        crcm ON(c.id = crcm.source_id)
        LEFT JOIN (SELECT source_id,company_id from cmscenter.company_cms_mapping
        where source_table_name='content' and is_deleted=0)ccm ON(ccm.source_id=c.id)
        WHERE
        c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        <if test="contentIds !=null and contentIds.size()>0">
            and c.id in
            <foreach collection="contentIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="radioId != null">
            AND crcm.radio_id=#{radioId}
        </if>
        <if test="companyId != null">
            AND ccm.company_id = #{companyId}
        </if>
        GROUP BY c.id
        ORDER by c.created_date DESC
        limit #{offset},#{size}
    </select>
    <select id="findMiniContentAllByRadioIdCount" resultType="java.lang.Integer">
        SELECT count(1) FROM cmscenter.content c,cmscenter.channel_radio_cms_mapping crcm
        WHERE c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        AND c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        AND crcm.radio_id=#{radioId}
    </select>
    <select id="findH5ContentAllCount" resultType="java.lang.Integer">
        SELECT count(1)
        from (
        select c.id
        FROM cmscenter.content c
        LEFT JOIN (SELECT * from cmscenter.channel_radio_cms_mapping where is_deleted=0 and source_table_name =
        'content')
        crcm ON(c.id = crcm.source_id)
        LEFT JOIN (SELECT source_id,company_id from cmscenter.company_cms_mapping
        where source_table_name='content' and is_deleted=0)ccm ON(ccm.source_id=c.id)
        WHERE
        c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        <if test="contentIds !=null and contentIds.size()>0">
            and c.id in
            <foreach collection="contentIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="radioId != null">
            AND crcm.radio_id=#{radioId}
        </if>
        <if test="companyId != null">
            AND ccm.company_id = #{companyId}
        </if>
        GROUP BY c.id
        )d
    </select>
    <select id="findContentsCount" resultType="java.lang.Integer">
        SELECT sum(tem.total) from (
        SELECT count(DISTINCT c.id) total
        from cmscenter.content c
        <if test=" contentQueryVo.calcStartDate != null or contentQueryVo.calcEndDate != null">
            inner join (
                select distinct
                    cbm.content_id
                from cmscenter.statistics_content_bjt_mapping cbm
                where
                    cbm.is_deleted = 0 and cbm.content_type = 1
                    <if test=" contentQueryVo.calcStartDate != null">
                        and cbm.calc_date &gt;= #{contentQueryVo.calcStartDate}
                    </if>
                    <if test=" contentQueryVo.calcEndDate != null">
                        and cbm.calc_date &lt;= #{contentQueryVo.calcEndDate}
                    </if>
            ) tt on tt.content_id = c.id
        </if>
        <if test=" contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0">
            inner join (
                SELECT DISTINCT
                    cgm.source_id as contentId
                FROM
                    cmscenter.content_goods_mapping cgm
                    WHERE
                    cgm.is_deleted = 0
                    AND cgm.source_table = 'content'
                    AND cgm.goods_id IN
                    <foreach collection="contentQueryVo.goodsIdList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            ) cgm2 on cgm2.contentId = c.id
        </if>
        <if test=" contentQueryVo.goodsRelationFlag != null and contentQueryVo.goodsRelationFlag == 1 ">
            inner join (
            SELECT DISTINCT
            cgmrelation.source_id as contentId
            FROM
            cmscenter.content_goods_mapping cgmrelation
            WHERE
            cgmrelation.is_deleted = 0
            AND cgmrelation.source_table = 'content'
            ) cgmrelation2 on cgmrelation2.contentId = c.id
        </if>
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content' and scm.source_id=c.id and
        scm.is_deleted=0 )
        LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
        'content'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        where c.is_deleted=0
        <if test="contentQueryVo.showType != null">
            and c.show_type = #{contentQueryVo.showType}
        </if>
        <if test="contentQueryVo.secondCategoryIdList != null and contentQueryVo.secondCategoryIdList.size()>0">
            and c.second_category_id in
            <foreach collection="contentQueryVo.secondCategoryIdList" item="secondCategoryId" open="(" close=")" separator=",">
                #{secondCategoryId}
            </foreach>
        </if>
        <if test="contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0">
            and c.community_id in
            <foreach collection="contentQueryVo.communityIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
            and cc.id in
            <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.activityId !=null">
            and c.activity_id =#{contentQueryVo.activityId}
        </if>
        <if test="contentQueryVo.isAuthorization != null">
            and c.is_authorization=#{contentQueryVo.isAuthorization}
        </if>
        <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            cmscenter.label_cms_mapping lcm
            WHERE
            lcm.source_table_name = 'content'
            AND lcm.is_deleted = 0
            and lcm.label_id in
            <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.channel_radio_cms_mapping crcm
            WHERE
            crcm.source_table_name = 'content'
            AND crcm.is_deleted =0
            <trim prefix="and " suffixOverrides="or">
                <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="item.channelId !=null and item.channelId !=''">
                            crcm.channel_id=#{item.channelId}
                        </if>
                        <if test="item.ratioId !=null and item.ratioId !=''">
                            and crcm.radio_id=#{item.ratioId}
                        </if>
                    </trim>
                </foreach>
            </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            select DISTINCT
            c.id
            from cmscenter.content c
            left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
            where
            c.is_deleted = 0
                and (ccm.source_id is null
                or ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            )
            <!--SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )-->
        </if>
        <if test="contentQueryVo.audioVideoType!=null and contentQueryVo.audioVideoType !=''">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            audio_video
            WHERE
            is_deleted = 0
            AND source_table_name = 'content'
            and type=#{contentQueryVo.audioVideoType}
            )
        </if>
        <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
            and c.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
        </if>
        <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
            and c.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
        </if>
        <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
            and c.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
        </if>
        <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
            and c.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
        </if>
        <if test="contentQueryVo.id !=null">
            and c.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
            and concat('',c.id,'') in
            <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
            and (
                concat('N',c.id) like concat('%',#{contentQueryVo.keywords},'%')
                    or
                c.title like CONCAT('%',#{contentQueryVo.keywords},'%')
            )
        </if>


        <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
        </if>
        <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and c.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and c.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
            and c.created_person =#{contentQueryVo.createdPerson}
        </if>
        <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
            and c.type!= 5
        </if>
        <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
            and '' like CONCAT('%',#{contentQueryVo.foodName},'%')
        </if>
        <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
            and c.upload_user_type = #{contentQueryVo.uploadUserType}
        </if>
        <if test="contentQueryVo.intelligent!=null">
            and 100 = #{contentQueryVo.intelligent}
        </if>
        <if test="contentQueryVo.menuDifficulty !=null and contentQueryVo.menuDifficulty != ''">
            and '' = #{contentQueryVo.menuDifficulty}
        </if>
        <if test="contentQueryVo.vedioMenu!=null">
            and 100 = #{contentQueryVo.vedioMenu}
        </if>
        <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
            and c.is_paid= #{contentQueryVo.isPaid}
        </if>
<!--        厨房案例权限校验-->
        <if test="contentQueryVo.purview != 1">
            and c.type != 15 and c.is_draft = 0
        </if>
        <if test="contentQueryVo.type !=null and contentQueryVo.type !=''">
            and c.type=#{contentQueryVo.type}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and c.type in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
<!--        过滤厨房案例草稿-->
        and c.is_draft = 0
<!--        厨房案例推荐内容筛选-->
        <if test="caseRecommendList != null and caseRecommendList.size() != 0">
            and case_recommend in
            <foreach collection="caseRecommendList" item="caseRecommend" open="(" separator="," close=")">
                #{caseRecommend}
            </foreach>
        </if>
        <if test="isRecommendHome != null">
            <choose>
                <when test="caseRecommendList != null and caseRecommendList.size() != 0">
                    or
                </when>
                <otherwise>
                    and
                </otherwise>
            </choose>
             is_recommend_home = #{isRecommendHome}
        </if>
        <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
            and EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
            and not EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>

        <if test=" contentQueryVo.calcStartDate == null and contentQueryVo.calcEndDate == null
        and !(contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0)
        and !(contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0)
        and contentQueryVo.showType == null">
            UNION ALL
            SELECT count(distinct cm.id) total
            from cmscenter.content_menu cm
            LEFT JOIN cmscenter.content_category cc on cc.id=cm.content_category_id and cc.is_deleted=0
            LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content_menu' and scm.source_id=cm.id
            and scm.is_deleted = 0)
            LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
            'content_menu'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=cm.id)
            LEFT JOIN (SELECT COUNT(1) products,content_menu_id from cmscenter.show_product where content_menu_id >0 and
            is_deleted=0 GROUP BY
            content_menu_id) pr on(cm.id=pr.content_menu_id)
            where cm.is_deleted=0
            <if test="contentQueryVo.secondCategoryIdList != null and contentQueryVo.secondCategoryIdList.size()>0">
               and false
            </if>
            <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
                and cc.id in
                <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
                and cm.id in (
                SELECT DISTINCT
                source_id
                FROM
                cmscenter.label_cms_mapping lcm
                WHERE
                lcm.source_table_name = 'content_menu'
                AND lcm.is_deleted =0
                and lcm.label_id in
                <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
                and cm.id in (
                SELECT
                DISTINCT source_id
                FROM
                cmscenter.channel_radio_cms_mapping crcm
                WHERE
                crcm.source_table_name = 'content_menu'
                AND crcm.is_deleted =0
                <trim prefix="and " suffixOverrides="or">
                    <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                        <trim prefix="(" suffix=")" prefixOverrides="and">
                            <if test="item.channelId !=null and item.channelId !=''">
                                crcm.channel_id=#{item.channelId}
                            </if>
                            <if test="item.ratioId !=null and item.ratioId !=''">
                                and crcm.radio_id=#{item.ratioId}
                            </if>
                        </trim>
                    </foreach>
                </trim>
                )
            </if>
            <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
                and cm.id in(
                SELECT DISTINCT
                source_id
                FROM
                cmscenter.company_cms_mapping ccm
                WHERE
                ccm.source_table_name = 'content_menu'
                AND ccm.is_deleted =0
                and ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
                and cm.id in(
                select DISTINCT
                c.id
                from cmscenter.content_menu c
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu' and c.id = ccm.source_id
                where
                c.is_deleted = 0
                    and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                )
                <!--SELECT DISTINCT
                source_id
                FROM
                cmscenter.company_cms_mapping ccm
                WHERE
                ccm.source_table_name = 'content_menu'
                AND ccm.is_deleted =0
                and ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )-->
            </if>
            <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
                and cm.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
            </if>
            <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
                and cm.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
            </if>
            <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
                and cm.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
            </if>
            <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
                and cm.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
            </if>
            <if test="contentQueryVo.id !=null">
                and cm.id=#{contentQueryVo.id}
            </if>
            <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
                and cm.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
            </if>
            <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
                and (IFNULL(cm.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
                or IFNULL(cm.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
            </if>
            <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
                and (IFNULL(cm.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
                or IFNULL(cm.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
            </if>
            <if test="contentQueryVo.auditStatus !=null ">
                and cm.auditing_status=#{contentQueryVo.auditStatus}
            </if>
            <if test="contentQueryVo.onlineStatus !=null">
                and cm.online_status=#{contentQueryVo.onlineStatus}
            </if>
            <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
                and cm.created_person =#{contentQueryVo.createdPerson}
            </if>



            <if test="(contentQueryVo.type !=null and contentQueryVo.type !=''
            ) or (contentQueryVo.types != null and contentQueryVo.types !='')">
                and 5=#{contentQueryVo.type}
            </if>
            <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
                and 5 != 5
            </if>
            <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
                and cm.id IN (
                SELECT DISTINCT
                fg.content_menu_id
                FROM
                food_group fg,
                food_info_mapping fim,
                food_info fi
                WHERE
                fg.id = fim.food_group_id
                AND fim.food_id = fi.id
                AND fim.is_deleted = 0
                AND fim.is_deleted = 0
                AND fi.is_deleted = 0
                AND fi.food_name LIKE CONCAT('%',#{contentQueryVo.foodName},'%')
                )
            </if>
            <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
                and cm.upload_user_type = #{contentQueryVo.uploadUserType}
            </if>
            <if test="contentQueryVo.intelligent!=null">
                and if(cm.origin_id is null,0,1) = #{contentQueryVo.intelligent}
            </if>
            <if test="contentQueryVo.menuDifficulty !=null and contentQueryVo.menuDifficulty != ''">
                and cm.menu_difficulty = #{contentQueryVo.menuDifficulty}
            </if>
            <if test="contentQueryVo.vedioMenu!=null">
                and cm.vedio_menu = #{contentQueryVo.vedioMenu}
            </if>
            <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
                and cm.is_paid= #{contentQueryVo.isPaid}
            </if>
            <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
                and EXISTS  ( SELECT 1 from special_content_mapping scm
                INNER JOIN special_info si on scm.special_info_id=si.id
                WHERE scm.is_deleted=0 and si.type in ('02','03','10')
                and cm.id=scm.source_id and scm.source_table_name ='content_menu' )
            </if>
            <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
                and not EXISTS  ( SELECT 1 from special_content_mapping scm
                INNER JOIN special_info si on scm.special_info_id=si.id
                WHERE scm.is_deleted=0 and si.type in ('02','03','10')
                and cm.id=scm.source_id and scm.source_table_name ='content_menu' )
            </if>
            <if test="contentQueryVo.activityId !=null">
                and 0 =#{contentQueryVo.activityId}
            </if>
        </if>
        ) tem
    </select>

    <select id="queryContentsBjtExportCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT cbm.content_id,cbm.user_id) total
        from cmscenter.content c
        <if test=" contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0">
            inner join (
            SELECT DISTINCT
            cgm.source_id as contentId
            FROM
            cmscenter.content_goods_mapping cgm
            WHERE
            cgm.is_deleted = 0
            AND cgm.source_table = 'content'
            AND cgm.goods_id IN
            <foreach collection="contentQueryVo.goodsIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            ) cgm2 on cgm2.contentId = c.id
        </if>
        inner join cmscenter.statistics_content_bjt_mapping cbm on cbm.is_deleted = 0 and cbm.content_id = c.id and cbm.content_type = 1
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content' and scm.source_id=c.id and
        scm.is_deleted=0 )
        LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
        'content'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        where c.is_deleted=0
        <if test="contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0">
            and c.community_id in
            <foreach collection="contentQueryVo.communityIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.contentIdList != null and contentQueryVo.contentIdList.size() != 0">
            and c.id in
            <foreach collection="contentQueryVo.contentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test=" contentQueryVo.calcStartDate != null">
            and cbm.calc_date &gt;= #{contentQueryVo.calcStartDate}
        </if>
        <if test=" contentQueryVo.calcEndDate != null">
            and cbm.calc_date &lt;= #{contentQueryVo.calcEndDate}
        </if>
        <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
            and cc.id in
            <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            cmscenter.label_cms_mapping lcm
            WHERE
            lcm.source_table_name = 'content'
            AND lcm.is_deleted = 0
            and lcm.label_id in
            <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.channel_radio_cms_mapping crcm
            WHERE
            crcm.source_table_name = 'content'
            AND crcm.is_deleted =0
            <trim prefix="and " suffixOverrides="or">
                <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="item.channelId !=null and item.channelId !=''">
                            crcm.channel_id=#{item.channelId}
                        </if>
                        <if test="item.ratioId !=null and item.ratioId !=''">
                            and crcm.radio_id=#{item.ratioId}
                        </if>
                    </trim>
                </foreach>
            </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
                select DISTINCT
                c.id
                from cmscenter.content c
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
                where
                c.is_deleted = 0
                    and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                )
            <!--SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )-->
        </if>
        <if test="contentQueryVo.audioVideoType!=null and contentQueryVo.audioVideoType !=''">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            audio_video
            WHERE
            is_deleted = 0
            AND source_table_name = 'content'
            and type=#{contentQueryVo.audioVideoType}
            )
        </if>
        <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
            and c.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
        </if>
        <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
            and c.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
        </if>
        <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
            and c.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
        </if>
        <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
            and c.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
        </if>
        <if test="contentQueryVo.id !=null">
            and c.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
        </if>
        <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and c.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and c.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
            and c.created_person =#{contentQueryVo.createdPerson}
        </if>
        <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
            and c.type!= 5
        </if>
        <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
            and '' like CONCAT('%',#{contentQueryVo.foodName},'%')
        </if>
        <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
            and c.upload_user_type = #{contentQueryVo.uploadUserType}
        </if>
        <if test="contentQueryVo.intelligent!=null">
            and 100 = #{contentQueryVo.intelligent}
        </if>
        <if test="contentQueryVo.vedioMenu!=null">
            and 100 = #{contentQueryVo.vedioMenu}
        </if>
        <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
            and c.is_paid= #{contentQueryVo.isPaid}
        </if>
<!--        厨房案例权限校验-->
        <if test="contentQueryVo.purview != 1">
            and c.type != 15 and c.is_draft = 0
        </if>
        <if test="contentQueryVo.type !=null and contentQueryVo.type !=''">
            and c.type=#{contentQueryVo.type}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and c.type in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
<!--        过滤厨房案例草稿-->
        and c.is_draft = 0
<!--        厨房案例推荐内容筛选-->
        <if test="caseRecommendList != null and caseRecommendList.size() != 0">
            and case_recommend in
            <foreach collection="caseRecommendList" item="caseRecommend" open="(" separator="," close=")">
                #{caseRecommend}
            </foreach>
        </if>
        <if test="isRecommendHome != null">
            <choose>
                <when test="caseRecommendList != null and caseRecommendList.size() != 0">
                    or
                </when>
                <otherwise>
                    and
                </otherwise>
            </choose>
             is_recommend_home = #{isRecommendHome}
        </if>
        <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
            and EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
            and not EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>

    </select>

    <select id="queryContentsBjtExport" resultType="com.fotile.cmscenter.content.pojo.dto.ContentBjtCountExportDTO">
        SELECT
            cbm.content_id as contentId,
            cbm.user_id as userId,
            c.title,
            concat('N',c.id) as contentNo,
            c.type,
            sum(cbm.share_count) as shareCount,
            sum(cbm.show_count) as showCount
        from cmscenter.content c
        <if test=" contentQueryVo.goodsIdList != null and contentQueryVo.goodsIdList.size() != 0">
            inner join (
            SELECT DISTINCT
            cgm.source_id as contentId
            FROM
            cmscenter.content_goods_mapping cgm
            WHERE
            cgm.is_deleted = 0
            AND cgm.source_table = 'content'
            AND cgm.goods_id IN
            <foreach collection="contentQueryVo.goodsIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            ) cgm2 on cgm2.contentId = c.id
        </if>
        inner join cmscenter.statistics_content_bjt_mapping cbm on cbm.is_deleted = 0 and cbm.content_id = c.id and cbm.content_type = 1
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        LEFT JOIN cmscenter.statistics_cms_mapping scm on(scm.source_table_name='content' and scm.source_id=c.id and
        scm.is_deleted=0 )
        LEFT JOIN (SELECT count(1) questions,source_id FROM cmscenter.question_info WHERE source_table_name =
        'content'and is_deleted=0 GROUP BY source_table_name,source_id) qu on(qu.source_id=c.id)
        where c.is_deleted=0
        <if test="contentQueryVo.communityIdList !=null and contentQueryVo.communityIdList.size()>0">
            and c.community_id in
            <foreach collection="contentQueryVo.communityIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.contentIdList != null and contentQueryVo.contentIdList.size() != 0">
            and c.id in
            <foreach collection="contentQueryVo.contentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test=" contentQueryVo.calcStartDate != null">
            and cbm.calc_date &gt;= #{contentQueryVo.calcStartDate}
        </if>
        <if test=" contentQueryVo.calcEndDate != null">
            and cbm.calc_date &lt;= #{contentQueryVo.calcEndDate}
        </if>
        <if test="contentQueryVo.classIds !=null and contentQueryVo.classIds.size()>0">
            and cc.id in
            <foreach collection="contentQueryVo.classIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="contentQueryVo.keyIds !=null and contentQueryVo.keyIds.size()>0">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            cmscenter.label_cms_mapping lcm
            WHERE
            lcm.source_table_name = 'content'
            AND lcm.is_deleted = 0
            and lcm.label_id in
            <foreach collection="contentQueryVo.keyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.channel_radio_cms_mapping crcm
            WHERE
            crcm.source_table_name = 'content'
            AND crcm.is_deleted =0
            <trim prefix="and " suffixOverrides="or">
                <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="item.channelId !=null and item.channelId !=''">
                            crcm.channel_id=#{item.channelId}
                        </if>
                        <if test="item.ratioId !=null and item.ratioId !=''">
                            and crcm.radio_id=#{item.ratioId}
                        </if>
                    </trim>
                </foreach>
            </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            select DISTINCT
            c.id
            from cmscenter.content c
            left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
            where
            c.is_deleted = 0
            and (ccm.source_id is null
            or ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
            )
            <!--SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )-->
        </if>
        <if test="contentQueryVo.audioVideoType!=null and contentQueryVo.audioVideoType !=''">
            and c.id in (
            SELECT DISTINCT
            source_id
            FROM
            audio_video
            WHERE
            is_deleted = 0
            AND source_table_name = 'content'
            and type=#{contentQueryVo.audioVideoType}
            )
        </if>
        <if test="contentQueryVo.createStartTime != null and contentQueryVo.createStartTime != ''">
            and c.created_date <![CDATA[>=]]> #{contentQueryVo.createStartTime}
        </if>
        <if test="contentQueryVo.createEndTime != null and contentQueryVo.createEndTime != ''">
            and c.created_date <![CDATA[<=]]> #{contentQueryVo.createEndTime}
        </if>
        <if test="contentQueryVo.onlineStart != null and contentQueryVo.onlineStart != ''">
            and c.online_time <![CDATA[>=]]> #{contentQueryVo.onlineStart}
        </if>
        <if test="contentQueryVo.onlineEnd != null and contentQueryVo.onlineEnd != ''">
            and c.online_time <![CDATA[<=]]> #{contentQueryVo.onlineEnd}
        </if>
        <if test="contentQueryVo.id !=null">
            and c.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.startTime != null and contentQueryVo.startTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[>=]]>#{contentQueryVo.startTime})
        </if>
        <if test="contentQueryVo.endTime != null and contentQueryVo.endTime != ''">
            and (IFNULL(c.start_time,'2010-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime}
            or IFNULL(c.end_time,'2200-01-01 00:00:00') <![CDATA[<=]]>#{contentQueryVo.endTime})
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and c.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and c.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.createdPerson!=null and contentQueryVo.createdPerson !=''">
            and c.created_person =#{contentQueryVo.createdPerson}
        </if>
        <if test="contentQueryVo.otherType !=null and contentQueryVo.otherType !=''">
            and c.type!= 5
        </if>
        <if test="contentQueryVo.foodName !=null and contentQueryVo.foodName !=''">
            and '' like CONCAT('%',#{contentQueryVo.foodName},'%')
        </if>
        <if test="contentQueryVo.uploadUserType !=null and contentQueryVo.uploadUserType !=''">
            and c.upload_user_type = #{contentQueryVo.uploadUserType}
        </if>
        <if test="contentQueryVo.intelligent!=null">
            and 100 = #{contentQueryVo.intelligent}
        </if>
        <if test="contentQueryVo.vedioMenu!=null">
            and 100 = #{contentQueryVo.vedioMenu}
        </if>
        <if test="contentQueryVo.isPaid !=null and contentQueryVo.isPaid !=''">
            and c.is_paid= #{contentQueryVo.isPaid}
        </if>
<!--        厨房案例权限校验-->
        <if test="contentQueryVo.purview != 1">
            and c.type != 15 and c.is_draft = 0
        </if>
        <if test="contentQueryVo.type !=null and contentQueryVo.type !=''">
            and c.type=#{contentQueryVo.type}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and c.type in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
<!--        过滤厨房案例草稿-->
        and c.is_draft = 0
<!--        厨房案例推荐内容筛选-->
        <if test="caseRecommendList != null and caseRecommendList.size() != 0">
            and case_recommend in
            <foreach collection="caseRecommendList" item="caseRecommend" open="(" separator="," close=")">
                #{caseRecommend}
            </foreach>
        </if>
        <if test="isRecommendHome != null">
            <choose>
                <when test="caseRecommendList != null and caseRecommendList.size() != 0">
                    or
                </when>
                <otherwise>
                    and
                </otherwise>
            </choose>
             is_recommend_home = #{isRecommendHome}
        </if>
        <if test="contentQueryVo.isCourse ==1 and contentQueryVo.isCourse!=null">
            and EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        <if test="contentQueryVo.isCourse ==0 and contentQueryVo.isCourse!=null">
            and not EXISTS  ( SELECT 1 from special_content_mapping scm
            INNER JOIN special_info si on scm.special_info_id=si.id
            WHERE scm.is_deleted=0 and si.type in ('02','03','10')
            and c.id=scm.source_id and scm.source_table_name ='content' )
        </if>
        group by cbm.content_id,cbm.user_id
        ORDER BY cbm.content_id,cbm.user_id
        limit #{contentQueryVo.offset},#{contentQueryVo.size}
    </select>

    <select id="queryAllContentWebsite" resultType="com.fotile.cmscenter.content.pojo.dto.QueryWebsiteContentOutDto">
        SELECT DISTINCT
        a.id cId,
        a.*,c.cover_image_url,c.resources_url
        FROM
        cmscenter.content a
        LEFT JOIN cmscenter.audio_video c ON a.id = c.source_id
        AND c.is_deleted = 0
        AND c.source_table_name = 'content'
        AND c.type='02'
        LEFT JOIN cmscenter.channel_radio_cms_mapping b ON a.id = b.source_id
        AND b.source_table_name = 'content'
        AND b.is_deleted = '0'
        LEFT JOIN cmscenter.label_cms_mapping d ON a.id = d.source_id
        AND d.is_deleted = '0'
        AND d.source_table_name = 'content'
        LEFT JOIN cmscenter.label_cms e ON d.label_id = e.id
        AND e.is_deleted = 0
        <where>
            a.is_deleted=0
            <if test=" channelId!= null">
                and b.channel_id=#{channelId}
            </if>
            <if test="key!=null and key!=''">
                and (a. title like concat('%',#{key},'%')
                or (e.name like concat('%',#{key},'%')
                ))
            </if>
        </where>
        limit #{offset},#{size}
    </select>

    <select id="getContentListByIds" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        SELECT
        c.id ,c.title,c.summary,c.type,c.cover_url coverUrl,c.created_date createdDate,
        (select d.name from cmscenter.content_category d where d.id=c.content_classification_id)
        contentClassificationName
        FROM
        cmscenter.content c
        ,cmscenter.channel_radio_cms_mapping cm
        WHERE
        cm.source_table_name = 'content'
        AND cm.channel_id = #{channelId}
        AND c.id = cm.source_id
        AND c.is_deleted = 0
        AND cm.is_deleted = 0
        AND c.online_status = '1'
        <if test="ids!=null and ids.size>0">
            and c.content_classification_id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        limit #{offset},#{size}
    </select>
    <select id="getContentCountByIds" resultType="long">
        SELECT count(c.id)
        FROM
        cmscenter.content c
        ,cmscenter.channel_radio_cms_mapping cm
        WHERE
        cm.source_table_name = 'content'
        AND cm.channel_id = #{channelId}
        AND c.id = cm.source_id
        AND c.is_deleted = 0
        AND cm.is_deleted = 0
        AND c.online_status = '1'
        <if test="ids!=null and ids.size>0">
            and c.content_classification_id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <!--官网内容详情-->
    <select id="getById" resultType="com.fotile.cmscenter.content.pojo.dto.ContentDetailOutDto">
        select  cc.name,ct.id,ct.cover_url, ct.source, ct.title,ct.content_text,ct.recommend_content_id,ct.created_date,scm.collection_count,scm.comment_count,
		scm.like_count from cmscenter.content ct
        	LEFT JOIN
		cmscenter.statistics_cms_mapping scm ON (ct.id
		= scm.source_id
		AND
		scm.source_table_name = 'content')
		left join  cmscenter.content_category cc on (ct.content_classification_id=cc.id and cc.is_deleted=0)
		where  ct.id=#{id} and ct.is_deleted=0
    </select>

    <select id="getByIds" resultType="com.fotile.cmscenter.content.pojo.dto.ContentDetailOutDto">
        select ct.id,ct.type,ct.created_date,ct.title,ct.cover_url,ct.summary,ct.content_classification_id,ct.second_category_id,
        (select d.name from cmscenter.content_category d where d.id=ct.content_classification_id)
        contentClassificationName
        from cmscenter.content ct
        where ct.is_deleted=0
        <if test="ids!=null and ids.size>0">
            and ct.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getRecommendContentListByIds" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        select * from cmscenter.content where is_deleted=0
        <if test="ids!=null and ids!=''">
            and content_classification_id in (${ids})

        </if>
    </select>

    <select id="getCategoryByChannelCode" resultType="com.fotile.cmscenter.content.pojo.dto.GetCategoryByChannelCode">
            SELECT
                cc.id,
                cc.name
            FROM
                cmscenter.channel_info ci,
                cmscenter.channel_category cc
            WHERE
                cc.channel_info_id = ci.id
            AND cc.is_deleted = 0
            AND ci.is_deleted = 0
            and  cc.stage=1
            and  cc.parent_id is null
            AND ci.channel_code = #{channelCode}
    </select>

    <select id="getSecondCategory" resultType="com.fotile.cmscenter.content.pojo.dto.GetCategoryByChannelCode">
        select
        id,name,full_path_id
        from cmscenter.channel_category
        where
        is_deleted=0
        and stage=1
        and parent_id=#{id}
    </select>

    <select id="getChildrenList" resultType="com.fotile.cmscenter.content.pojo.dto.GetCategoryByChannelCode">
        select
        id,name,full_path_id,together_type,content_category_id,key_word
        from cmscenter.channel_category
        where
        full_path_id like  concat(#{id},'%')
        and stage=1
        and is_deleted=0
    </select>

    <select id="getContentList" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        select
        DISTINCT
        ct.*,ctcm.channel_category_id,cc.name contentClassificationName
        from
        cmscenter.content ct,
        cmscenter.channel_together_content_mapping ctcm,
        cmscenter.channel_category cc
        where
        ct.id=ctcm.content_id
        and ctcm.channel_category_id =cc.id
        and ct.is_deleted=0
        and ctcm.is_deleted=0
        and cc.is_deleted=0
        and ctcm.content_table = 'content'
        and cc.together_type='2'
        and ct.online_status=1
        and ctcm.channel_category_id in
        <foreach collection="channelCategoryIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY ct.id
        limit #{offset},#{size}
    </select>

    <select id="getCounts" resultType="java.lang.Long">
        select
        COUNT(DISTINCT ct.id)
        from
        cmscenter.content ct,
        cmscenter.channel_together_content_mapping ctcm
        where
        ct.id=ctcm.content_id
        and ct.is_deleted=0
        and ctcm.is_deleted=0
        and ctcm.content_table='content'
        and ct.id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="kitchenLifeIndex" resultType="com.fotile.cmscenter.content.pojo.dto.GetCategoryByChannelCode">
        select
        *
        from
        cmscenter.channel_category
        where
        parent_id=#{id}
        and is_deleted=0
        and stage=1
    </select>

    <select id="getChildFive" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        SELECT
        ct.*,c.full_path_id
        FROM
        cmscenter.content ct
        inner join
        (
        select ctcm.content_id,cc.full_path_id from  cmscenter.channel_category cc inner join channel_together_content_mapping ctcm
        on (cc.id=ctcm.channel_category_id
        and cc.is_deleted=0
        and cc.stage=1
        and  ctcm.is_deleted=0
        and cc.name=#{categoryName})
        ) c
        on c.content_id=ct.id
        and ct.is_deleted=0
         LIMIT 6
    </select>

    <select id="getAudioVideosByIds" resultType="com.fotile.cmscenter.content.pojo.dto.GetAudioVideos">
        select
        *
        from
        cmscenter.audio_video
        where
        is_deleted=0
        and source_table_name='content'
        <if test="type!=null and type!=''">
            and type=#{type}
        </if>
        and source_id in
        <foreach collection="ids" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getContentIds" resultType="java.lang.Long">
        select DISTINCT
        ctcm.content_id
        from
        cmscenter.channel_category cc,
        cmscenter.channel_together_content_mapping ctcm
        where
        cc.id=ctcm.channel_category_id
        and cc.is_deleted=0
        and ctcm.is_deleted=0
        and ctcm.content_table='content'
        <if test="ids!=null and ids.size>0">
            and cc.id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getCommentList" resultType="com.fotile.cmscenter.content.pojo.dto.CommentsOutDto">
        select
        ci.id,ci.user_name,ci.created_date,ci.comment_content,scm.like_count
        from cmscenter.comment_info ci
        left join cmscenter.statistics_cms_mapping scm
        on (ci.id=scm.source_id and scm.is_deleted=0 and  ci.is_deleted=0 and  scm.source_table_name='comment_info')
        where
       ci. is_deleted=0
        and ci.source_id=#{id}
        and ci.source_table_name='content'
        GROUP BY
	    ci.id
        ORDER BY
	    ci.comment_date DESC
	    limit #{offset},#{size}
    </select>

    <select id="getAnswerList" resultType="com.fotile.cmscenter.content.pojo.dto.CommentsOutDto">
     select
     cd.*,scm.like_count
     from
     cmscenter.comment_detail cd
       left join cmscenter.statistics_cms_mapping scm
        on (cd.id=scm.source_id and scm.is_deleted=0 and  cd.is_deleted=0 and  scm.source_table_name='comment_detail')
        where
       cd. is_deleted=0
        and cd.comment_info_id=#{id}
        GROUP BY
	    cd.id
</select>

    <insert id="answer" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into cmscenter.comment_detail (comment_info_id, comment_content,user_name,modified_by,modified_date,created_date,created_by)
    values (#{commentInfoId},  #{commentContent},#{userName},#{modifiedBy},#{modifiedDate},#{createdDate},#{createdBy})
    </insert>

    <select id="getKeyWord" resultType="java.lang.String">
        select
        lc.name
        from cmscenter.label_cms lc,cmscenter.label_cms_mapping lcm
        where
        lc.is_deleted=0
        and lcm.is_deleted=0
        and lc.id=lcm.label_id
        and lcm.source_table_name='content'
        and lcm.source_id=#{id}
        limit 1
    </select>

    <select id="getCommentPics" resultType="com.fotile.cmscenter.adviertisement.pojo.entity.Pic">
        select
        pc.id,pc.name,pc.cover_url1
        from
        cmscenter.picture_cms pc,cmscenter.picture_cms_mapping pcm
        where
        pc.id=pcm.picture_cms_id
        and pc.is_deleted=0
        and  pcm.is_deleted=0
        and  pcm.source_table_name='comment_info'
        and  pcm.source_id=#{id}
    </select>

    <insert id="collection">
         insert into collection_detail (source_table_name, source_table_alias,
    source_id, source_name, content_category_id,
    `type`, collection_date, user_id,
    nickname, phone, is_deleted,
    created_by, created_date, modified_by,
    modified_date, source_table_type, source_table_alias_dis
    )
    values (#{sourceTableName,jdbcType=VARCHAR}, #{sourceTableAlias,jdbcType=VARCHAR},
    #{sourceId,jdbcType=BIGINT}, #{sourceName,jdbcType=VARCHAR}, #{contentCategoryId,jdbcType=BIGINT},
    #{type,jdbcType=BIGINT}, #{collectionDate,jdbcType=TIMESTAMP}, #{userId,jdbcType=VARCHAR},
    #{nickname,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR},
    #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR},
    #{modifiedDate,jdbcType=TIMESTAMP}, #{sourceTableType,jdbcType=VARCHAR}, #{sourceTableAliasDis,jdbcType=VARCHAR}
    )
    </insert>
    <update id="setCollectionCount">
       update cmscenter.statistics_cms_mapping
       set
        collection_count=collection_count+1,
        modified_by = #{modifiedBy},
        modified_date = now()
       where
      source_id=#{id}
      and source_table_name='content'
   </update>
    <update id="updateSyncMaterial">
        update cmscenter.content c
        set c.sync_basic_material_id = #{syncBasicMaterialId},c.sync_material_id = #{syncMaterialId},c.is_sync_material = #{isSyncMaterial}
        where c.id = #{id} and c.is_deleted=0
    </update>

    <select id="checkIsCollection" resultType="java.lang.Long">
        SELECT COUNT(1)
        from cmscenter.collection_detail
        where phone=#{phone}
        and source_id=#{sourceId}
    </select>

    <select id="queryAllContentWebsiteCount" resultType="long">
        SELECT count(1)
        FROM
        cmscenter.content a
        LEFT JOIN cmscenter.audio_video c ON a.id = c.source_id
        AND c.is_deleted = 0
        AND c.source_table_name = 'content'
        AND c.type='02'
        LEFT JOIN cmscenter.channel_radio_cms_mapping b ON a.id = b.source_id
        AND b.source_table_name = 'content'
        AND b.is_deleted = '0'
        LEFT JOIN cmscenter.label_cms_mapping d ON a.id = d.source_id
        AND d.is_deleted = '0'
        AND d.source_table_name = 'content'
        LEFT JOIN cmscenter.label_cms e ON d.label_id = e.id
        AND e.is_deleted = 0
        <where>
            a.is_deleted=0
            <if test=" channelId!= null">
                and b.channel_id=#{channelId}
            </if>
            <if test="key!=null and key!=''">
                and (a. title like concat('%',#{key},'%')
                or (e.name like concat('%',#{key},'%')
                ))
            </if>
        </where>
        group by a.id
    </select>

    <select id="getContentListByIdsCount" resultType="long">
        SELECT
        count(1)
        FROM
        cmscenter.content c
        ,cmscenter.channel_radio_cms_mapping cm
        WHERE
        cm.source_table_name = 'content'
        AND cm.channel_id = #{channelId}
        AND c.id = cm.source_id
        AND c.is_deleted = 0
        AND cm.is_deleted = 0
        AND c.online_status = '1'
        <if test="ids!=null and ids.size>0">
            and c.content_classification_id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getContentListCount" resultType="long">
        select
        count(DISTINCT (ct.id))
        from
        cmscenter.content ct,
        cmscenter.channel_together_content_mapping ctcm,
        cmscenter.channel_category cc
        where
        ct.id=ctcm.content_id
        and ctcm.channel_category_id =cc.id
        and ct.is_deleted=0
        and ctcm.is_deleted=0
        and cc.is_deleted=0
        and ctcm.content_table = 'content'
        and cc.together_type='2'
        and ct.online_status=1
        and ctcm.channel_category_id in
        <foreach collection="channelCategoryIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getCommentListCount" resultType="long">
        select
       count(DISTINCT(ci.id))
        from cmscenter.comment_info ci
        left join cmscenter.statistics_cms_mapping scm
        on (ci.id=scm.source_id and scm.is_deleted=0 and  ci.is_deleted=0 and  scm.source_table_name='comment_info')
        where
       ci. is_deleted=0
        and ci.source_id=#{id}
        and ci.source_table_name='content'
        ORDER BY
	    ci.comment_date DESC
    </select>

    <select id="getContentIdList" resultType="com.fotile.cmscenter.content.pojo.vo.ChannelContentIdVto">
        select
        ct.id,
        crcm.channel_id channelId
        from
        cmscenter.content ct,
        cmscenter.channel_together_content_mapping ctcm,
        cmscenter.channel_category cc,
        cmscenter.channel_radio_cms_mapping crcm
        where
        ct.id=ctcm.content_id
        and ct.id=crcm.source_id
        and crcm.is_deleted=0
        and ctcm.channel_category_id =cc.id
        and ct.is_deleted=0
        and ctcm.is_deleted=0
        and cc.is_deleted=0
        and ctcm.content_table = 'content'
        and crcm.source_table_name='content'
        and cc.together_type='2'
        and ct.online_status=1
        and ctcm.channel_category_id in
        <foreach collection="channelCategoryIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getContentIdList1" resultType="java.lang.Long">
        select ct.id
        from cmscenter.content ct left join cmscenter.channel_radio_cms_mapping crcm
        on (ct.id=crcm.source_id and ct.is_deleted=0 and crcm.is_deleted=0 and crcm.source_table_name='content')
        where
        ct.is_deleted=0
        and ct.auditing_status=3
        and ct.online_status=1
        and now() BETWEEN IFNULL(ct.start_time,'2010-01-01 00:00:00') and IFNULL(ct.end_time,'2200-01-01 00:00:00')
        and crcm.channel_id=#{channelId}
        <if test="contentCategoryIds!=null and contentCategoryIds.size>0">
            and ct.content_classification_id in
            <foreach collection="contentCategoryIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="keyWordList!=null and keyWordList.size>0">
            AND ct.id IN ( SELECT DISTINCT lcm.source_id
            FROM cmscenter.label_cms_mapping lcm,cmscenter.label_cms lc
            WHERE lc.id=lcm.label_id and lcm.is_deleted = 0 AND lcm.source_table_name = 'content'
            and lc.is_deleted=0 AND lc.name IN
            <foreach collection="keyWordList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getAllContentsByIdList" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        select
        DISTINCT
        ct.*,cc.name contentClassificationName
        from
        cmscenter.content ct,
        cmscenter.content_category cc
        where
        ct.is_deleted=0
        and ct.content_classification_id=cc.id
        and cc.is_deleted=0
        and ct.online_status=1
        and ct.id in
        <if test="ids!=null and ids.size>0">
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY ct.id
        limit #{offset},#{size}
    </select>

    <select id="getAllContentsByIdListCount" resultType="java.lang.Long">
        select
        count(DISTINCT (ct.id))
        from
        cmscenter.content ct,
        cmscenter.content_category cc
        where
        ct.is_deleted=0
        and ct.content_classification_id=cc.id
        and cc.is_deleted=0
        and ct.online_status=1
        and ct.id in
        <if test="ids!=null and ids.size>0">
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getAllContents" resultType="com.fotile.cmscenter.content.pojo.dto.GetContentIdsOut">
        select
        DISTINCT
        ct.*,cc.name contentClassificationName
        from
        cmscenter.content ct,
        cmscenter.content_category cc
        where
        ct.content_classification_id=cc.id
        and ct.is_deleted=0
        and cc.is_deleted=0
        and ct.online_status=1
        and ct.id in
        <if test="ids!=null and ids.size>0">
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY ct.id
        limit 6
    </select>

    <select id="getContentidsByCateIds" resultType="long">
        SELECT DISTINCT c.id
        FROM cmscenter.content c,cmscenter.channel_radio_cms_mapping crcm
        WHERE c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        AND c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        AND crcm.channel_id=#{channelId}
        and c.content_classification_id in
        <foreach collection="cateIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getShoppingMallContents" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT c.type,c.cover_url coverUrl,c.id,c.activity_id activityId,c.skip_url skipUrl,c.title,c.summary
        FROM cmscenter.content c,cmscenter.channel_radio_cms_mapping crcm
        WHERE c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        AND c.is_deleted=0
        AND c.online_status=1
        and now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type !='4'
        AND crcm.channel_id=1
        <if test="key!=null and key!=''">
            and c.title like CONCAT('%',#{key},'%')
        </if>
        and c.id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by c.id
        ORDER by c.sort DESC
    </select>

    <resultMap id="resultKitchenMap" type="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        <result property="id" column="id"></result>
        <result property="coverUrl" column="coverUrl"></result>
        <result property="type" column="type"></result>
        <result property="activityId" column="activityId"></result>
        <result property="skipUrl" column="skipUrl"></result>
        <result property="title" column="title"></result>
        <result property="summary" column="summary"></result>
        <result property="panoramaUrl" column="panoramaUrl"></result>
        <result property="panoramaCoverUrl" column="panoramaCoverUrl"></result>
        <collection property="keywords" ofType="java.lang.String" javaType="java.util.List" column="id">
            <result column="keywordName"/>
        </collection>
    </resultMap>

    <select id="findKitchenContentAllByRadioId"
            resultMap="resultKitchenMap">
        SELECT
        c.*,lc.name keywordName,lc.id
        FROM
        (
        SELECT
        c.type,c.cover_url coverUrl,c.id,c.activity_id activityId,c.skip_url
        skipUrl,c.title,c.summary,c.panorama_url panoramaUrl,c.panorama_cover_url panoramaCoverUrl
        FROM
        cmscenter.content c
        INNER JOIN channel_radio_cms_mapping crcm ON c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        <if test="param.classPropertyId != null">
            INNER JOIN (
            SELECT
            content_id, property_parameter_id
            FROM
            content_property_parameter
            WHERE
            property_id = #{param.classPropertyId}
            AND is_deleted = 0
            <if test="param.propertyIds != null">
                and property_parameter_id
                in
                <foreach collection="param.propertyIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            GROUP BY
            content_id
            ) pp ON pp.content_id = c.id
            INNER JOIN property_parameter ppa on pp.property_parameter_id = ppa.id and ppa.is_deleted = 0
        </if>
        LEFT JOIN content_property_parameter pcode ON pcode.content_id = c.id
        AND pcode.is_deleted = 0
        AND pcode.property_id = 20
        WHERE
        c.is_deleted = 0
        AND c.online_status = 1
        AND now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00')
        AND IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type != '4'
        AND c.content_classification_id = #{param.classId}
        AND (
        (
        pcode. CODE != 'old'
        AND pcode. CODE NOT LIKE 'old,%'
        AND pcode. CODE NOT LIKE '%,old,%'
        AND pcode. CODE NOT LIKE '%,old'
        )
        OR pcode. CODE IS NULL
        )
        <if test="param.keyword != null">
            AND (
            c.title LIKE CONCAT('%', #{param.keyword}, '%')
            or (
            SELECT
            count(1)
            FROM
            label_cms_mapping lcm,
            label_cms lc
            WHERE
            lcm.is_deleted = 0
            AND lcm.source_table_name = 'content'
            AND lcm.label_id = lc.id
            AND lc.is_deleted = 0
            AND lc. NAME LIKE CONCAT('%', #{param.keyword}, '%')
            and lcm.source_id = c.id
            ) > 0
            )
        </if>
        order by c.sort desc,c.created_date desc
        limit #{offset},#{size}
        ) c
        left join cmscenter.label_cms_mapping lcm on c.id = lcm.source_id AND lcm.is_deleted=0 AND
        lcm.source_table_name='content'
        left join cmscenter.label_cms lc on lcm.label_id = lc.id AND lc.is_deleted=0
    </select>
    <select id="findKitchenContentByRadioIdCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        cmscenter.content c
        INNER JOIN channel_radio_cms_mapping crcm ON c.id = crcm.source_id
        AND crcm.source_table_name = 'content'
        AND crcm.is_deleted = 0
        <if test="param.classPropertyId != null">
            INNER JOIN (
            SELECT
            content_id, property_parameter_id
            FROM
            content_property_parameter
            WHERE
            property_id = #{param.classPropertyId}
            AND is_deleted = 0
            <if test="param.propertyIds != null">
                and property_parameter_id
                in
                <foreach collection="param.propertyIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            GROUP BY
            content_id
            ) pp ON pp.content_id = c.id
            INNER JOIN property_parameter ppa on pp.property_parameter_id = ppa.id and ppa.is_deleted = 0
        </if>
        LEFT JOIN content_property_parameter pcode ON pcode.content_id = c.id
        AND pcode.is_deleted = 0
        AND pcode.property_id = 20
        WHERE
        c.is_deleted = 0
        AND c.online_status = 1
        AND now() BETWEEN IFNULL(c.start_time,'2010-01-01 00:00:00')
        AND IFNULL(c.end_time,'2200-01-01 00:00:00')
        AND c.type != '4'
        AND c.content_classification_id = #{param.classId}
        AND (
        (
        pcode. CODE != 'old'
        AND pcode. CODE NOT LIKE 'old,%'
        AND pcode. CODE NOT LIKE '%,old,%'
        AND pcode. CODE NOT LIKE '%,old'
        )
        OR pcode. CODE IS NULL
        )
        <if test="param.keyword != null">
            AND (
            c.title LIKE CONCAT('%', #{param.keyword}, '%')
            or (
            SELECT
            count(1)
            FROM
            label_cms_mapping lcm,
            label_cms lc
            WHERE
            lcm.is_deleted = 0
            AND lcm.source_table_name = 'content'
            AND lcm.label_id = lc.id
            AND lc.is_deleted = 0
            AND lc. NAME LIKE CONCAT('%', #{param.keyword}, '%')
            and lcm.source_id = c.id
            ) > 0
            )
        </if>
    </select>

    <select id="findClassIdByContentId" resultType="com.fotile.cmscenter.content.pojo.vo.ContentToClassVo">
        select c.id contentId ,cc.id contentClassId,cc.name contentClassName,"content" as sourceTable
        from cmscenter.content c
        LEFT JOIN cmscenter.content_category cc on (cc.id=c.content_classification_id and cc.is_deleted =0)
        where  c.is_deleted=0
        and c.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getIDList" resultType="com.fotile.cmscenter.yard.dto.ContentOrSpecialIDS">
        SELECT
        content.id  id,
        content.code code,
        content.resourceTable resourceTable
        FROM
        (
        SELECT
        id,
        concat('N',id) as code,
        'content' resourceTable
        FROM content
        WHERE
        is_deleted = 0

            <if test="code!=null">
                and concat('N',id) like concat('%',#{code},'%')
            </if>


        UNION ALL

        SELECT
        id,
        concat('C',id) as code,
        'content_menu' resourceTable
        FROM content_menu
        WHERE
        is_deleted = 0

            <if test="code!=null">
                and concat('C',id) like concat('%',#{code},'%')
            </if>


        UNION ALL

        SELECT
        id,
        concat('Z',id) as code,
        'special_info' resourceTable
        FROM special_info
        WHERE
        is_deleted = 0

            <if test="code!=null">
                and concat('Z',id) like concat('%',#{code},'%')
            </if>

        ) content
        GROUP BY content.code
        limit #{offset},#{size}
    </select>

    <select id="getIDListCount" resultType="java.lang.Long">
        SELECT count(1) from (
        SELECT
        content.id contentId,
        content.code,
        content.resourceTable
        FROM
        (
        SELECT
        id,
        concat('N',id) as code,
        'content' resourceTable
        FROM content
        WHERE
        is_deleted = 0

            <if test="code!=null">
                and concat('N',id) like concat('%',#{code},'%')
            </if>


        UNION ALL

        SELECT
        id,
        concat('C',id) as code,
        'content_menu' resourceTable
        FROM content_menu
        WHERE
        is_deleted = 0

            <if test="code!=null">
                and concat('C',id) like concat('%',#{code},'%')
            </if>


        UNION ALL

        SELECT
        id,
        concat('Z',id) as code,
        'special_info' resourceTable
        FROM special_info
        WHERE
        is_deleted = 0
        <if test="code!=null">
                and concat('Z',id) like concat('%',#{code},'%')
            </if>

        ) content
        GROUP BY content.code
        ) a
    </select>

    <sql id="contentCase">
        id,title,created_by,is_deleted,created_date,`type`,content_classification_id,sort,`source`,kitchen_case_type,store_code,
        store_name,case_describe,community_id,community_name ,refuse_note,auditing_status,province_id,province_name,city_id,
        city_name,area_id,area_name,store_address,is_draft,online_status,pain_spot,online_status,renovation_project,longitude,latitude,
        case_video_url, case_video_cover_url, case_detail_video_url, case_detail_video_cover_url, is_support_send, vr_url,vr_cover_url,
        before_vr_url,before_vr_cover_url
    </sql>

    <select id="selectKitchenCaseById" resultType="com.fotile.cmscenter.cases.pojo.dto.ContentCaseDto">
        SELECT
           <include refid="contentCase"/>
        FROM
            cmscenter.`content`
        WHERE
            is_deleted = 0
          AND `type` = 15
          AND id = #{caseId}
    </select>

    <select id="selectSimpleKitchenCaseById" resultType="com.fotile.cmscenter.cases.pojo.dto.ContentCaseDto">
        SELECT
           <include refid="contentCase"/>
        FROM
            cmscenter.`content`
        WHERE
            is_deleted = 0
          AND id = #{caseId}
    </select>

    <select id="selectKitchenCasePageCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (
            SELECT
            DISTINCT  a.id
            FROM
            cmscenter.content a
            left join cmscenter.company_cms_mapping ccm on a.id = ccm.source_id and ccm.is_deleted = 0 and ccm.source_table_name = 'content'
            LEFT JOIN cmscenter.content_goods_mapping c ON c.source_id = a.id
            AND c.source_table = 'content'
            AND c.is_deleted = 0
            <where>
                a.type = 15
                AND a.is_deleted = 0
                AND a.auditing_status = 3
                AND a.is_draft = 0
                AND a.online_status = 1
                <if test="channelId != null and channelId != ''">
                    <if test="channelId == '1'.toString() ">
                        and a.is_support_send = 1
                    </if>
                </if>
                <if test="caseIdList !=null  and caseIdList.size()> 0">
                    and a.id in
                    <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
                        #{caseId}
                    </foreach>
                </if>
                <if test="provinceId != null">
                   and a.province_id = #{provinceId}
                </if>
                <if test="cityId != null">
                    and a.city_id = #{cityId}
                </if>
                <if test="areaId != null">
                    and a.area_id = #{areaId}
                </if>
                <if test="keyValue != null and keyValue != ''">
                    AND (upper(a.title) LIKE concat('%' , upper(#{keyValue,jdbcType=VARCHAR})  , '%')
                    or upper(a.community_name) like concat('%' ,upper(#{keyValue,jdbcType=VARCHAR}) , '%')
                    or upper(c.goods_name) like concat('%' , upper(#{keyValue,jdbcType=VARCHAR}) , '%')
                    or concat(a.id,'') = #{keyValue,jdbcType=VARCHAR})
                </if>
                <if test="kitchenCaseType != null">
                    AND a.kitchen_case_type = #{kitchenCaseType}
                </if>
                <if test="contentId != null and contentId.size() != 0">
                    AND a.id in
                    <foreach collection="contentId" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="communityIdList != null and communityIdList.size() != 0">
                    AND a.community_id in
                    <foreach collection="communityIdList" item="communityId" open="(" separator="," close=")">
                        #{communityId}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size() != 0">
                    AND c.goods_id IN
                    <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
                        #{goodsId}
                    </foreach>
                </if>
                <if test="companyId != null">
                    and (
                        ccm.company_id = #{companyId}
                        <!--<if test="companyId != 2405">-->
                            or a.show_type = 2
                        <!--</if>-->
                    )
                </if>
                <if test="renovationProjectList != null and renovationProjectList.size() != 0">
                    AND
                    <foreach collection="renovationProjectList" item="str" open="(" separator=" OR " close=")">
                        a.renovation_project LIKE concat('%' , #{str}  , '%')
                    </foreach>
                </if>
                <if test="izVr != null">
                    <if test="izVr == 1">
                        and (a.vr_url is not null or a.before_vr_url is not null)
                    </if>
                    <if test="izVr == 0">
                        and (a.vr_url is null and a.before_vr_url is null)
                    </if>
                </if>
                <if test="izVideo != null">
                    <if test="izVideo == 1">
                        and (a.case_video_url is not null and a.case_video_url != '')
                    </if>
                    <if test="izVideo == 0">
                        and (a.case_video_url is null or a.case_video_url = '')
                    </if>
                </if>
                <if test="mediaTypeList != null and mediaTypeList.size() != 0">
                    <if test="mediaTypeList.size() == 1">
                        <if test="mediaTypeList.contains(1)">
                            and (a.vr_url is not null or a.before_vr_url is not null)
                        </if>
                        <if test="mediaTypeList.contains(2)">
                            and (a.case_video_url is not null and a.case_video_url != '')
                        </if>
                    </if>
                    <if test="mediaTypeList.size() == 2">
                        <if test="mediaTypeList.contains(1) and mediaTypeList.contains(2)">
                            and ((a.vr_url is not null or a.before_vr_url is not null) or (a.case_video_url is not null and a.case_video_url != ''))
                        </if>
                    </if>
                </if>
                <if test="aiCaseIdList != null and aiCaseIdList.size() > 0">
                    AND a.id in
                    <foreach collection="aiCaseIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </where>
                 ) AS temp_a
    </select>

    <select id="selectKitchenCasePage" resultType="com.fotile.cmscenter.cases.pojo.dto.QueryCaseWithPageOutputDto">
        SELECT
            a.id AS caseId,
            a.title AS caseTitle ,
            a.kitchen_case_type,
            a.community_name AS communityName,
            a.is_recommend_home AS isPopularCase,
            a.is_support_send as isSupportSend,
            a.created_date,
            a.case_recommend,
            a.vr_url as vrUrl,
            a.vr_cover_url as vrCoverUrl,
            a.before_vr_url as beforeVrUrl,
            a.before_vr_cover_url as beforeVrCoverUrl,
            (
            6371393 * ACOS(
            COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * COS(RADIANS(#{longitude} - longitude))
            + SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))
            )
            ) AS distance
        FROM
            cmscenter.content a
            left join cmscenter.company_cms_mapping ccm on a.id = ccm.source_id and ccm.is_deleted = 0 and ccm.source_table_name = 'content'
            LEFT JOIN cmscenter.content_goods_mapping c ON c.source_id = a.id AND c.source_table = 'content' AND c.is_deleted = 0
            <if test="sortType == 3">
                left join (
                    SELECT
                        t.content_id as contentId,
                        count(1) as countNum
                    FROM
                        cmscenter.content_case_user_collect t
                    WHERE
                        t.is_deleted = 0
                        AND t.is_collect = 1 and t.content_type = 1
                        GROUP BY
                        t.content_id
                ) tt on tt.contentId = a.id
            </if>

            <if test="sortType == 4">
                left join (
                    SELECT
                        t.content_id as contentId,
                        count(1) as countNum
                    FROM
                        cmscenter.content_case_user_helpful t
                    WHERE
                        t.is_deleted = 0
                        AND t.is_helpful = 1 and t.content_type = 1
                        GROUP BY
                        t.content_id
                ) tt on tt.contentId = a.id
            </if>

            <if test="sortType == 5">
                left join (
                    SELECT
                        t.content_id as contentId,
                        count(1) as countNum
                    FROM
                        cmscenter.statistics_content_bjt_mapping_detail t
                    WHERE
                        t.is_deleted = 0
                        AND t.type = 1 and t.content_type = 1
                        <!--and t.source_type = 1-->
                        GROUP BY
                        t.content_id
                ) tt on tt.contentId = a.id
            </if>

        <where>
            a.type = 15
            AND a.is_deleted = 0
            AND a.auditing_status = 3
            and a.is_draft = 0
            and a.online_status = 1
            <if test="channelId != null and channelId != ''">
                <if test="channelId == '1'.toString() ">
                    and a.is_support_send = 1
                </if>
            </if>
            <if test="caseIdList !=null  and caseIdList.size()> 0">
                and a.id in
                <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
                    #{caseId}
                </foreach>
            </if>
            <if test="provinceId != null">
                and a.province_id = #{provinceId}
            </if>
            <if test="cityId != null">
                and a.city_id = #{cityId}
            </if>
            <if test="areaId != null">
                and a.area_id = #{areaId}
            </if>
            <if test="keyValue != null and keyValue != ''">
                AND (
                upper(a.title) LIKE concat('%' ,upper(#{keyValue,jdbcType=VARCHAR}) , '%')
                or upper(a.community_name) like concat('%' , upper(#{keyValue,jdbcType=VARCHAR}) , '%')
                or upper(c.goods_name) like concat('%' , upper(#{keyValue,jdbcType=VARCHAR}) , '%' )
                or upper(c.model_num) like concat('%' , upper(#{keyValue,jdbcType=VARCHAR}) , '%')
                or concat(a.id,'') = #{keyValue,jdbcType=VARCHAR})
            </if>
            <if test="kitchenCaseType != null">
                AND a.kitchen_case_type = #{kitchenCaseType}
            </if>
            <if test="contentId != null and contentId.size() != 0">
                AND a.id in
                <foreach collection="contentId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="communityIdList != null and communityIdList.size() != 0">
                AND a.community_id in
                <foreach collection="communityIdList" item="communityId" open="(" separator="," close=")">
                    #{communityId}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size() != 0">
                AND c.goods_id IN
                <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
                    #{goodsId}
                </foreach>
            </if>
            <if test="companyId != null">
                and (
                    ccm.company_id = #{companyId}
                    <!--<if test="companyId != 2405">-->
                        or a.show_type = 2
                    <!--</if>-->

                )
            </if>
            <if test="renovationProjectList != null and renovationProjectList.size() != 0">
                AND
                <foreach collection="renovationProjectList" item="str" open="(" separator=" OR " close=")">
                    a.renovation_project LIKE concat('%' , #{str}  , '%')
                </foreach>
            </if>
            <if test="izVr != null">
                <if test="izVr == 1">
                    and (a.vr_url is not null or a.before_vr_url is not null)
                </if>
                <if test="izVr == 0">
                    and (a.vr_url is null and a.before_vr_url is null)
                </if>
            </if>
            <if test="izVideo != null">
                <if test="izVideo == 1">
                    and (a.case_video_url is not null and a.case_video_url != '')
                </if>
                <if test="izVideo == 0">
                    and (a.case_video_url is null or a.case_video_url = '')
                </if>
            </if>
            <if test="mediaTypeList != null and mediaTypeList.size() != 0">
                <if test="mediaTypeList.size() == 1">
                    <if test="mediaTypeList.contains(1)">
                        and (a.vr_url is not null or a.before_vr_url is not null)
                    </if>
                    <if test="mediaTypeList.contains(2)">
                        and (a.case_video_url is not null and a.case_video_url != '')
                    </if>
                </if>
                <if test="mediaTypeList.size() == 2">
                    <if test="mediaTypeList.contains(1) and mediaTypeList.contains(2)">
                        and ((a.vr_url is not null or a.before_vr_url is not null) or (a.case_video_url is not null and a.case_video_url != ''))
                    </if>
                </if>
            </if>
            <if test="aiCaseIdList != null and aiCaseIdList.size() > 0">
                AND a.id in
                <foreach collection="aiCaseIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY
            a.id
        ORDER BY
        <if test="sortType == 1">
            <choose>
                <when test="goodsIds != null and goodsIds.size() != 0">
                    field(a.case_recommend , 2,3) desc , a.is_recommend_home desc ,distance ASC , a.online_time  desc , a.modified_date desc
                </when>
                <otherwise>
                    <choose>
                        <when test="caseIdList != null and caseIdList.size() != 0">
                            FIELD(a.id,
                            <foreach collection="caseIdList" item="mixGoodsId" separator=",">
                                #{mixGoodsId}
                            </foreach>
                            )
                        </when>
                        <otherwise>
                            a.is_recommend_home desc ,distance ASC , a.online_time  desc , a.modified_date desc
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <if test="sortType == 2">
            a.id desc
        </if>
        <if test="sortType == 3 or sortType == 4 or sortType == 5">
            tt.countNum DESC ,a.id desc
        </if>
        <if test="sortType == 6">
            case when distance is null then 1 else 0 end,distance
        </if>
        <if test="sortType == 7">
            FIELD(a.id,
            <foreach collection="aiCaseIdList" item="aiCaseId" separator=",">
                #{aiCaseId}
            </foreach>
            )
        </if>
        LIMIT #{page} , #{size}
    </select>

    <select id="selectKitchenCaseByIds" resultType="com.fotile.cmscenter.cases.pojo.dto.QueryCaseWithPageOutputDto">
        SELECT DISTINCT
            b.id AS caseId,
            b.title AS caseTitle,
            b.community_name AS communityName,
            b.is_recommend_home AS isPopularCase
        FROM
        cmscenter.content_goods_mapping a
        INNER JOIN cmscenter.content b ON a.source_id = b.id
        left join cmscenter.company_cms_mapping ccm on b.id = ccm.source_id and ccm.source_table_name = 'content' and ccm.is_deleted = 0
        <where>
            a.is_deleted = 0
            AND b.is_deleted = 0
            AND b.type = '15'
            AND b.auditing_status = 3
            AND b.is_draft = 0
            AND a.source_table = 'content'
            and b.online_status = 1
            AND a.goods_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="companyId != null">
                and (
                    ccm.company_id = #{companyId}
                    <!--<if test="companyId != 2405">-->
                        or b.show_type = 2
                    <!--</if>-->

                )
            </if>
        </where>
        GROUP BY b.id
        ORDER BY field(b.case_recommend , 2 , 3 ) desc , b.online_time desc , b.modified_date desc
    </select>

    <select id="selectListByCompanyId" resultType="com.fotile.cmscenter.content.pojo.Content">
        select  c.id
        from content c
                 left join company_cms_mapping ccm on c.id = ccm.source_id and ccm.source_table_name = 'content' and
                                                      ccm.is_deleted = 0
        where c.type = '15'
          and c.is_deleted = 0
          and c.is_recommend_home = 1
            <if test="companyId != null">
                and ccm.company_id = #{companyId}
            </if>
        order by c.recommend_home_time desc
    </select>

    <select id="querySalesManDepartmentCaseWithPage"
            resultType="com.fotile.cmscenter.cases.pojo.dto.QueryCaseByUserIdOutDto">
        SELECT
            a.user_id,
            b.id AS caseId,
            b.created_date ,
            b.type,
            b.title AS caseTitle,
            b.content_classification_id,
            b.source,
            b.start_time,
            b.end_time,
            b.remark,
            b.label,
            b.kitchen_case_type,
            b.store_code,
            b.store_name,
            b.case_describe,
            b.community_id ,
            b.community_name AS caseAddress,
            b.is_support_send as isSupportSend,
            b.vr_url as vrUrl,
            b.vr_cover_url as vrCoverUrl,
            b.before_vr_url as beforeVrUrl,
            b.before_vr_cover_url as beforeVrCoverUrl,
            CASE
            WHEN b.auditing_status = '1' AND b.is_draft = 1  THEN 1
            WHEN b.auditing_status = '2' THEN 2
            WHEN b.auditing_status = '3' THEN 4
            WHEN b.auditing_status = '4' THEN 3
            END AS `status`
            FROM
            `content_case_user_mapping` a
            LEFT JOIN content b ON a.content_id = b.id AND b.is_deleted = 0
            <where>
                a.is_deleted = 0
                AND a.user_id in
                <foreach collection="salesmanIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                and b.`type` = '15' and online_status = 1 and auditing_status = 3
                <if test="keyValue != null and keyValue != ''">
                    AND (upper(b.community_name) LIKE CONCAT('%', upper(#{keyValue}), '%')
                    OR upper(b.title) LIKE CONCAT('%', upper(#{keyValue}), '%')
                    or concat(b.id,'') = #{keyValue})
                </if>
            </where>
            ORDER BY b.created_date desc
            LIMIT #{page},#{size}
    </select>

    <select id="querySalesManDepartmentCaseWithCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        `content_case_user_mapping` a
        LEFT JOIN content b ON a.content_id = b.id AND b.is_deleted = 0
        <where>
            a.is_deleted = 0
            AND a.user_id in
            <foreach collection="salesmanIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and b.`type` = '15' and online_status = 1 and auditing_status = 3
            <if test="keyValue != null and keyValue != ''">
                AND (upper(b.community_name) LIKE CONCAT('%', upper(#{keyValue}), '%')
                OR upper(b.title) LIKE CONCAT('%', upper(#{keyValue}), '%')
                or concat(b.id,'') = #{keyValue})
            </if>
        </where>
        ORDER BY b.created_date desc
    </select>

    <select id="findContentDryById" resultType="com.fotile.cmscenter.content.pojo.vo.FindContentDryByIdVO">
        select title,content_text,cover_url from cmscenter.content
        where id =#{contentId}
    </select>

    <select id="findContentPageByCategoryId"
            resultType="com.fotile.cmscenter.content.pojo.dto.CustomerContentDTO">
        select
            id,
            label,
            cover_url
        from
            content
        where
            content_classification_id = #{id}
        and online_status = 1
        and is_deleted = 0
        and type = '16'
        and
        (
        (start_time is null and end_time is null)
        OR
        (start_time  <![CDATA[<=]]> now() and end_time >= now())
        )
        order by
            sort desc,created_date desc
        limit #{pageNum},#{pageSize}
    </select>

    <select id="findContentPageByCategoryIds"
            resultType="com.fotile.cmscenter.content.pojo.dto.CustomerContentDTO">
        select
            id,
            label,
            cover_url
        from
            content
        where
            id in
        <foreach collection="contentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and online_status = 1
        and is_deleted = 0
        and type = '16'
        and
        (
        (start_time is null and end_time is null)
        OR
        (start_time  <![CDATA[<=]]> now() and end_time >= now())
        )
        order by
            sort desc,created_date desc
        limit #{pageNum},#{pageSize}
    </select>

    <select id="findContentCountByCategoryId" resultType="java.lang.Integer">
        select count(1)
        from
            content
        where
            content_classification_id = #{id}
        and online_status = 1
        and is_deleted = 0
        and type = '16'
        and
        (
        (start_time is null and end_time is null)
        OR
        (start_time  <![CDATA[<=]]> now() and end_time >= now())
        )
    </select>

    <select id="findContentCountByCategoryIds" resultType="java.lang.Integer">
        select
            count(1)
        from
            content
        where
            id in
            <foreach collection="contentIds" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        and online_status = 1
        and is_deleted = 0
        and type = '16'
        and
        (
        (start_time is null and end_time is null)
        OR
        (start_time  <![CDATA[<=]]> now() and end_time >= now())
        )
    </select>

    <!--上门设计查询内容详情-->
    <select id="getContentById" resultType="com.fotile.cmscenter.content.pojo.vo.GetContentVo">
        SELECT
            c.id,
            c.cover_url coverUrl,
            c.title,
            c.summary,
            c.content_text contentText,
            c.panorama_url panoramaUrl,
            c.panorama_cover_url panoramaCoverUrl,
            c.shared_url sharedUrl,
            c.shared_words sharedWords,
            c.original_author originalAuthor
        FROM cmscenter.content c
        LEFT JOIN cmscenter.channel_radio_cms_mapping cr ON c.id = cr.source_id
            AND cr.source_table_name = 'content' AND cr.is_deleted = 0
        WHERE
            c.is_deleted = 0
            AND c.type = '4'
            AND c.content_classification_id = 41
            AND cr.channel_id in (11)
            AND c.auditing_status = '3'
            AND c.online_status = '1'
            AND ( now( ) BETWEEN c.start_time AND c.end_time OR ( c.start_time IS NULL AND c.end_time IS NULL ) )
        order by c.sort desc,c.modified_date desc
    </select>


    <!--方案讲解教学视频查询内容详情-->
    <select id="getContentByCategoryName" resultType="com.fotile.cmscenter.content.pojo.vo.GetContentVo">
        SELECT
            c.id,
            c.cover_url coverUrl,
            c.title,
            c.summary,
            c.content_text contentText,
            c.panorama_url panoramaUrl,
            c.panorama_cover_url panoramaCoverUrl,
            c.shared_url sharedUrl,
            c.shared_words sharedWords,
            c.original_author originalAuthor
        FROM cmscenter.content c
        LEFT JOIN cmscenter.channel_radio_cms_mapping cr ON c.id = cr.source_id
            AND cr.source_table_name = 'content' AND cr.is_deleted = 0
        WHERE
            c.is_deleted = 0
          AND c.type = '4'
          AND c.content_classification_id = #{contentClassificationId}
          AND cr.channel_id in (11)
          AND c.auditing_status = '3'
          AND c.online_status = '1'
          AND ( now( ) BETWEEN c.start_time AND c.end_time OR ( c.start_time IS NULL AND c.end_time IS NULL ) )
        group by c.id
        order by c.sort desc,c.modified_date desc
    </select>

    <select id="getTopShareCasesBySalesmanId" resultType="java.lang.Long">
        SELECT
            t.contentId
        FROM
            (
            SELECT
                cbmd.content_id AS contentId,
                count(*) AS selfSendNum
            FROM
                cmscenter.`statistics_content_bjt_mapping_detail` cbmd
            WHERE
                cbmd.is_deleted = 0
         		AND cbmd.user_id = #{dto.userId}
                AND cbmd.type = 1
                AND cbmd.source_type = 1
              and cbmd.content_type = 1
                GROUP BY
                cbmd.content_id
            ) t
        ORDER BY
            t.selfSendNum DESC
            LIMIT 20
    </select>

    <select id="myCommonUseCases" resultType="com.fotile.cmscenter.cases.pojo.dto.QueryCaseWithPageOutputDto">
        select *
        from (
                 SELECT
                     c.id AS caseId,
                     c.title AS caseTitle,
                     c.kitchen_case_type,
                     c.community_name AS communityName,
                     c.is_recommend_home AS isPopularCase,
                     c.is_support_send as isSupportSend,
                     c.vr_url as vrUrl,
                     c.vr_cover_url as vrCoverUrl,
                     c.before_vr_url as beforeVrUrl,
                     c.before_vr_cover_url as beforeVrCoverUrl,
                     null as caseImg,
                     null projectCode,
                     1 as materialType,
                     null dealOrderAmount,
                     null projectUrl,
                     null threeDimensionalLink,
                     null cluesId,
                     null explainStatus,
                     null excellentFlag,
                     null kitchenType,
                     null kitchenArea,
                     tt.selfSendNum
                 FROM
                     (
                         SELECT
                             cbmd.content_id AS contentId,
                             count(*) AS selfSendNum
                         FROM
                             cmscenter.`statistics_content_bjt_mapping_detail` cbmd
                         WHERE
                             cbmd.is_deleted = 0
                           AND cbmd.user_id = #{dto.userId}
                           AND cbmd.type = 1
                           AND cbmd.source_type = 1
                           and cbmd.content_type = 1
                         GROUP BY
                             cbmd.content_id
                     ) tt
                         INNER JOIN cmscenter.content c ON c.is_deleted = 0
                         AND c.type = 15
                         AND c.auditing_status = 3
                         AND c.is_draft = 0
                         AND c.online_status = 1
                         AND c.id = tt.contentId
                 union all
                 SELECT
                     c.id AS caseId,
                     c.project_name AS caseTitle,
                     null as kitchen_case_type,
                     c.village_name AS communityName,
                     null AS isPopularCase,
                     null as isSupportSend,
                     null as vrUrl,
                     null as vrCoverUrl,
                     null as beforeVrUrl,
                     null as beforeVrCoverUrl,
                     c.cover_picture_url as caseImg,
                     c.project_code projectCode,
                     2 as materialType,
                     C.deal_order_amount dealOrderAmount,
                     c.project_url projectUrl,
                     c.three_dimensional_link threeDimensionalLink,
                     c.clues_id cluesId,
                     c.explain_status explainStatus,
                     c.excellent_flag excellentFlag,
                     c.kitchen_type kitchenType,
                     c.kitchen_area kitchenArea,
                     tt.selfSendNum
                 FROM
                     (
                         SELECT
                             cbmd.content_id AS contentId,
                             count(*) AS selfSendNum
                         FROM
                             cmscenter.`statistics_content_bjt_mapping_detail` cbmd
                         WHERE
                             cbmd.is_deleted = 0
                           AND cbmd.user_id = #{dto.userId}
                           AND cbmd.type = 1
                           AND cbmd.source_type = 1
                           and cbmd.content_type = 5
                         GROUP BY
                             cbmd.content_id
                     ) tt
                         INNER JOIN cmscenter.t_devise_scheme_main_info c ON c.is_deleted = 0 AND c.status = 1
                        and c.three_dimensional_link is not null and c.three_dimensional_link != ''
                        and c.three_dimensional_link like concat('%','kujiale','%')
                         AND c.id = tt.contentId
             )t
        ORDER BY
            t.selfSendNum DESC,t.caseId desc,FIELD(t.materialType,1,2,3)
        LIMIT 20
    </select>

    <select id="countSearchCommunityByCompanyAuthForCenter" resultType="int">
        SELECT
            count(*)
        FROM
            (
                SELECT DISTINCT
                    c.community_id AS communityId,
                    c.community_name AS communityName,
                    c.province_id AS provinceId,
                    c.province_name AS provinceName,
                    c.city_id AS cityId,
                    c.city_name AS cityName,
                    c.area_id AS countyId,
                    c.area_name AS countyName
                FROM
                    cmscenter.content c
                    <if test="dto.companyAuthIdList != null and dto.companyAuthIdList.size() != 0">
                        INNER JOIN orgcenter.t_company_area ca ON ca.is_deleted = 0
                        AND c.province_id = ca.provicen_id
                        AND c.city_id = ca.city_id
                        AND c.area_id = ca.county_id
                        AND ca.company_id IN
                        <foreach collection="dto.companyAuthIdList" item="item" index="index" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                WHERE
                    c.is_deleted = 0
                    AND c.type = 15
                    AND c.community_id IS NOT NULL AND c.community_name IS NOT NULL and c.community_name !=''
                    <if test="dto.matchContent != null and dto.matchContent != ''">
                        AND (upper(c.community_name) LIKE CONCAT('%', upper(#{dto.matchContent}), '%'))
                    </if>
            ) t
    </select>

    <select id="searchCommunityByCompanyAuthForCenter" resultType="com.fotile.cmscenter.cases.pojo.dto.searchCommunityByCompanyAuthForCenterOutDTO">
        SELECT DISTINCT
            c.community_id AS communityId,
            c.community_name AS communityName,
            c.province_id AS provinceId,
            c.province_name AS provinceName,
            c.city_id AS cityId,
            c.city_name AS cityName,
            c.area_id AS countyId,
            c.area_name AS countyName
        FROM
            cmscenter.content c
            <if test="dto.companyAuthIdList != null and dto.companyAuthIdList.size() != 0">
                INNER JOIN orgcenter.t_company_area ca ON ca.is_deleted = 0
                AND c.province_id = ca.provicen_id
                AND c.city_id = ca.city_id
                AND c.area_id = ca.county_id
                AND ca.company_id IN
                <foreach collection="dto.companyAuthIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        WHERE
            c.is_deleted = 0
            AND c.type = 15
            AND c.community_id IS NOT NULL AND c.community_name IS NOT NULL and c.community_name !=''
            <if test="dto.matchContent != null and dto.matchContent != ''">
                AND (upper(c.community_name) LIKE CONCAT('%', upper(#{dto.matchContent}), '%'))
            </if>
            ORDER BY c.community_name
        LIMIT #{pg.offset},#{pg.size}
    </select>

    <select id="queryContentsCount" resultType="java.lang.Integer">
        SELECT sum(tem.total) from (
            SELECT count(DISTINCT c.id) total
            from cmscenter.content c
            <where>
                c.is_deleted = 0 and c.online_status = 1
                <if test="channelIds !=null and channelIds.size()>0">
                    and c.id in (
                    SELECT
                    DISTINCT source_id
                    FROM
                    cmscenter.channel_radio_cms_mapping crcm
                    WHERE
                    crcm.source_table_name = 'content'
                    AND crcm.is_deleted =0
                    and crcm.channel_id in
                    <foreach collection="channelIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="companyIds !=null and companyIds.size()>0">
                    and c.id in (
                    select DISTINCT
                    c.id
                    from cmscenter.content c
                    left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
                    where
                    c.is_deleted = 0
                    and (ccm.source_id is null
                        or ccm.company_id in
                        <foreach collection="companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                    )
                </if>
                <if test="keywords != null and keywords != ''">
                    and (
                    concat('N',c.id) like concat('%',#{keywords},'%')
                    OR concat('',c.id) like concat('%',#{keywords},'%')
                    or c.title like CONCAT('%',#{keywords},'%')
                    )
                </if>
                <if test="type != null ">
                    and c.type = #{type}
                </if>
            </where>
            UNION ALL
            SELECT count(distinct cm.id) total
            from cmscenter.content_menu cm
            <where>
                cm.is_deleted = 0 and cm.online_status = 1
                <if test="channelIds !=null and channelIds.size()>0">
                    and cm.id in (
                    SELECT
                    DISTINCT source_id
                    FROM
                    cmscenter.channel_radio_cms_mapping crcm
                    WHERE
                    crcm.source_table_name = 'content_menu'
                    AND crcm.is_deleted =0
                    and crcm.channel_id in
                    <foreach collection="channelIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="companyIds !=null and companyIds.size()>0">
                    and cm.id in (
                    select DISTINCT
                    cm.id
                    from cmscenter.content_menu cm
                    left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu' and cm.id = ccm.source_id
                    where
                    cm.is_deleted = 0
                    and (ccm.source_id is null
                        or ccm.company_id in
                        <foreach collection="companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                    )
                </if>
                <if test="keywords != null and keywords != ''">
                    and (
                    concat('C',cm.id) like concat('%',#{keywords},'%')
                    OR concat('',cm.id) like concat('%',#{keywords},'%')
                    or cm.title like CONCAT('%',#{keywords},'%')
                    )
                </if>
                <if test="type != null ">
                    and 5 = #{type}
                </if>
            </where>
        ) tem
    </select>

    <select id="queryContents" resultType="com.fotile.cmscenter.happinessTest.pojo.dto.GetContentSpecialsListOutDto">
        SELECT tem.* from (
        SELECT c.id,concat('N',c.id) as code,c.title, c.summary, c.cover_url coverUrl, c.type
        from cmscenter.content c
        <where>
            c.is_deleted = 0 and c.online_status = 1
            <if test="channelIds !=null and channelIds.size()>0">
                and c.id in (
                SELECT
                DISTINCT source_id
                FROM
                cmscenter.channel_radio_cms_mapping crcm
                WHERE
                crcm.source_table_name = 'content'
                AND crcm.is_deleted =0
                and crcm.channel_id in
                <foreach collection="channelIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyIds !=null and companyIds.size()>0">
                and c.id in (
                select DISTINCT
                c.id
                from cmscenter.content c
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
                where
                c.is_deleted = 0
                and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                )
            </if>
            <if test="keywords != null and keywords != ''">
                and (
                concat('N',c.id) like concat('%',#{keywords},'%')
                OR concat('',c.id) like concat('%',#{keywords},'%')
                or c.title like CONCAT('%',#{keywords},'%')
                )
            </if>
            <if test="type != null ">
                and c.type = #{type}
            </if>
        </where>
        UNION ALL
        SELECT cm.id,concat('C',cm.id) as code,cm.title, cm.menu_abstract as summary, cm.cover_url coverUrl,5 as type
        from cmscenter.content_menu cm
        <where>
            cm.is_deleted = 0 and cm.online_status = 1
            <if test="channelIds !=null and channelIds.size()>0">
                and cm.id in (
                SELECT
                DISTINCT source_id
                FROM
                cmscenter.channel_radio_cms_mapping crcm
                WHERE
                crcm.source_table_name = 'content_menu'
                AND crcm.is_deleted =0
                and crcm.channel_id in
                <foreach collection="channelIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="companyIds !=null and companyIds.size()>0">
                and cm.id in (
                select DISTINCT
                cm.id
                from cmscenter.content_menu cm
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu' and cm.id = ccm.source_id
                where
                cm.is_deleted = 0
                and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                )
            </if>
            <if test="keywords != null and keywords != ''">
                and (
                concat('C',cm.id) like concat('%',#{keywords},'%')
                OR concat('',cm.id) like concat('%',#{keywords},'%')
                or cm.title like CONCAT('%',#{keywords},'%')
                )
            </if>
            <if test="type != null ">
                and 5 = #{type}
            </if>
        </where>
        ) tem
        limit #{offset},#{size}
    </select>

    <select id="queryByContentIds" resultType="com.fotile.cmscenter.happinessTest.pojo.dto.GetContentSpecialsListOutDto">
        SELECT tem.* from (
        SELECT c.id,concat('N',c.id) as code,c.title, c.summary, c.cover_url coverUrl,c.type
        from cmscenter.content c
        <where>
            c.is_deleted = 0
            <if test="idcodes != null and idcodes.size > 0">
                and concat('N',c.id) in
                <foreach collection="idcodes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT cm.id,concat('C',cm.id) as code,cm.title, cm.menu_abstract as summary, cm.cover_url coverUrl, 5 as type
        from cmscenter.content_menu cm
        <where>
            cm.is_deleted = 0
            <if test="idcodes != null and idcodes != ''">
                and concat('C',cm.id) in
                <foreach collection="idcodes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ) tem
    </select>

    <select id="miniAppFindContentCount" resultType="java.lang.Long">
        SELECT count(distinct c.id)
        FROM cmscenter.content c
        <if test="keyword != null and keyword != ''">
            inner join cmscenter.content_goods_mapping cgm
            on c.id = cgm.source_id
            and cgm.source_table = 'content'
            and cgm.is_deleted = 0
        </if>
        <include refid="miniAppFindContentWhere"/>
    </select>

    <select id="miniAppFindContentPage" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT
        <if test="keyword != null and keyword != ''">
            distinct
        </if>
            c.id,c.title,c.type,c.cover_url coverUrl,
               c.skip_url skipUrl,c.summary,c.sort,c.original_author originalAuthor,
               c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl,
               c.official_account_url as officialAccountUrl
        FROM cmscenter.content c
        <if test="orderField != null and orderField !=''">
            LEFT JOIN cmscenter.statistics_cms_mapping scm
            on scm.source_table_name = 'content' and scm.source_id = c.id and scm.is_deleted = 0
        </if>
        <if test="keyword != null and keyword != ''">
            inner join cmscenter.content_goods_mapping cgm
            on c.id = cgm.source_id
            and cgm.source_table = 'content'
            and cgm.is_deleted = 0
        </if>
        <include refid="miniAppFindContentWhere"/>
        <choose>
           <when test="sortIdList != null and sortIdList.size() > 0 ">
                order by field(c.id,<foreach collection="sortIdList" item="item" separator=",">#{item}</foreach>),
                c.sort DESC,c.id desc
           </when>
            <when test="orderField != null and orderField !=''">
                ORDER by c.sort DESC, ${orderField} desc,c.id desc
            </when>
           <otherwise>
               ORDER by c.sort DESC,c.id desc
           </otherwise>
        </choose>
        <if test="offset != null and size != null">
            limit #{offset},#{size}
        </if>
    </select>

    <sql id="miniAppFindContentWhere">
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        <if test="channelId !=null">
            and exists (
                SELECT 1 from cmscenter.channel_radio_cms_mapping radio
                where radio.is_deleted = 0
                and radio.source_table_name = 'content'
                and radio.channel_id = #{channelId}
                and c.id = radio.source_id
            )
        </if>
        <if test="type != null and type != ''">
            AND c.type = #{type}
        </if>
        <if test="firstCategoryId != null">
            AND c.first_category_id = #{firstCategoryId}
        </if>
        <if test="secondCategoryId != null">
            AND c.second_category_id = #{secondCategoryId}
        </if>
        <if test="contentIdList != null and contentIdList.size() > 0">
            AND c.id in
            <foreach collection="contentIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and (
                c.title like CONCAT('%',#{keyword},'%')
                or cgm.goods_name like CONCAT('%',#{keyword},'%')
            )
        </if>
    </sql>


    <select id="miniAppSearchContentCount" resultType="java.lang.Long">
        SELECT count(c.id)
        FROM cmscenter.content c
        left join cmscenter.statistics_cms_mapping scm on c.id = scm.source_id
        and scm.source_table_name = 'content' and scm.is_deleted = 0
        <include refid="miniAppSearchContentWhere"/>
    </select>

    <select id="miniAppSearchContentPage" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT
        c.id,c.title,c.type,c.cover_url coverUrl,
        c.skip_url skipUrl,c.summary,c.sort,c.original_author originalAuthor,
        c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl,
        c.official_account_url as officialAccountUrl,
        IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) as browseCount
        FROM cmscenter.content c
        left join cmscenter.statistics_cms_mapping scm on c.id = scm.source_id
        and scm.source_table_name = 'content' and scm.is_deleted = 0
        <include refid="miniAppSearchContentWhere"/>
        ORDER by IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) DESC,c.sort DESC,c.id desc
        <if test="offset != null and size != null">
            limit #{offset},#{size}
        </if>
    </select>

    <sql id="miniAppSearchContentWhere">
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        <if test="channelId !=null">
            and exists (
                SELECT 1 from cmscenter.channel_radio_cms_mapping radio
                where radio.is_deleted = 0
                and radio.source_table_name = 'content'
                and radio.channel_id = #{channelId}
                and c.id = radio.source_id
            )
        </if>
        <if test="type != null and type != ''">
            AND c.type = #{type}
        </if>
        <if test="firstCategoryId != null">
            AND c.first_category_id = #{firstCategoryId}
        </if>
        <if test="secondCategoryId != null">
            AND c.second_category_id = #{secondCategoryId}
        </if>
        <if test="contentIdList != null and contentIdList.size() > 0">
            AND c.id in
            <foreach collection="contentIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and c.title like CONCAT('%',#{keyword},'%')
        </if>
    </sql>




    <select id="getContentListByIdList" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT distinct c.id,c.title,c.type,c.cover_url coverUrl,
        c.skip_url skipUrl,c.summary,c.sort,
        c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl
        FROM cmscenter.content c
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        and id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getContentListByIdSecondCategoryIdList" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT distinct c.id,c.title,c.type,c.cover_url coverUrl,
        c.skip_url skipUrl,c.summary,c.sort,
        c.official_account_url officialAccountUrl,
        c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl
        FROM cmscenter.content c
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        AND c.second_category_id = #{secondCategoryId}
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        and id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getContentListByIdSecondCategoryIdListPage" resultType="com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo">
        SELECT distinct c.id,c.title,c.type,c.cover_url coverUrl,
        c.skip_url skipUrl,c.summary,c.sort,
        c.official_account_url officialAccountUrl,
        c.second_category_id secondCategoryId,
        IFNULL(scm.browse_count,0) + IFNULL(scm.system_browse_count,0) as browseCount,
        c.inspire_video_url inspireVideoUrl,c.inspire_video_cover_url inspireVideoCoverUrl
        FROM cmscenter.content c
        left join cmscenter.statistics_cms_mapping scm on c.id = scm.source_id and scm.source_table_name = 'content' and scm.is_deleted = 0
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        and c.id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="secondCategoryIdList != null and secondCategoryIdList.size()>0">
            and c.second_category_id in
            <foreach collection="secondCategoryIdList" item="secondCategoryId" open="(" close=")"  separator=",">
                #{secondCategoryId}
            </foreach>
        </if>
        order by browseCount desc
        LIMIT #{pg.offset},#{pg.size}
    </select>

    <select id="getContentListByIdSecondCategoryIdCount" resultType="java.lang.Long">
        SELECT distinct count(c.id)
        FROM cmscenter.content c
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        and id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="secondCategoryIdList != null and secondCategoryIdList.size()>0">
            and c.second_category_id in
            <foreach collection="secondCategoryIdList" item="secondCategoryId" open="(" close=")"  separator=",">
                #{secondCategoryId}
            </foreach>
        </if>
    </select>

    <select id="queryCategoryCount" resultType="com.fotile.cmscenter.content.pojo.vo.ContentCategoryCountVo">
        SELECT
            <if test="categoryType != null and categoryType == 1">
                c.first_category_id
            </if>
            <if test="categoryType != null and categoryType == 2">
                c.second_category_id
            </if>
            categoryId,
            count(c.id) count
        FROM cmscenter.content c
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        and c.id in (
            SELECT source_id
            from cmscenter.channel_radio_cms_mapping
            where is_deleted = 0
            and source_table_name = #{sourceTable}
            and channel_id = #{channelId}
        )
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            <if test="categoryType != null and categoryType == 1">
                and c.first_category_id in
            </if>
            <if test="categoryType != null and categoryType == 2">
                and c.second_category_id in
            </if>
            <foreach collection="categoryIdList" open="(" close=")" item="categoryId" separator=",">
                #{categoryId}
            </foreach>
        </if>
        <if test="categoryType != null and categoryType == 1">
            group by c.first_category_id
        </if>
        <if test="categoryType != null and categoryType == 2">
            group by c.second_category_id
        </if>
    </select>

    <select id="queryContentCount" resultType="com.fotile.cmscenter.content.pojo.vo.ContentCategoryCountVo">
        SELECT
        cm.channel_category_id categoryId,
        count(c.id) count
        FROM cmscenter.content c
        inner join cmscenter.channel_together_content_mapping cm
        on c.id = cm.content_id and cm.is_deleted = 0 and cm.content_table = #{sourceTable}
        WHERE c.is_deleted = 0
        AND c.online_status = 1
        AND c.type = #{type}
        and c.id in (
            SELECT source_id
            from cmscenter.channel_radio_cms_mapping
            where is_deleted = 0
            and source_table_name = #{sourceTable}
            and channel_id = #{channelId}
        )
        and now() between IFNULL(c.start_time,'2010-01-01 00:00:00') and IFNULL(c.end_time,'2200-01-01 00:00:00')
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            and cm.channel_category_id in
            <foreach collection="categoryIdList" open="(" close=")" item="categoryId" separator=",">
                #{categoryId}
            </foreach>
        </if>
        group by cm.channel_category_id
    </select>


    <!--菜谱专题根据编码查询菜谱列表-->
    <select id="queryByContentCodes" resultType="com.fotile.cmscenter.customMenu.pojo.dto.GetCustomContentListOutDto">
        SELECT tem.* from (
        SELECT c.id,concat('N',c.id) as code,c.title, c.summary, c.cover_url coverUrl,c.type,
               case when c.is_paid = 1 then '付费' else '免费' end isPaid, '否' as intelligent,
               c.online_status onlineStatus
        from cmscenter.content c
        <where>
            c.is_deleted = 0
            <if test="idcodes != null and idcodes.size > 0">
                and concat('N',c.id) in
                <foreach collection="idcodes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT cm.id,concat('C',cm.id) as code,cm.title, cm.menu_abstract as summary, cm.cover_url coverUrl, 5 as type,
               case when cm.is_paid = 1 then '付费' else '免费' end isPaid,
               case when cm.origin_id is null then '否' else '是' end intelligent,
               cm.online_status onlineStatus
        from cmscenter.content_menu cm
        <where>
            cm.is_deleted = 0
            <if test="idcodes != null and idcodes != ''">
                and concat('C',cm.id) in
                <foreach collection="idcodes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ) tem
    </select>

    <select id="queryByContentIdList" resultType="com.fotile.cmscenter.customMenu.pojo.dto.GetCustomContentListOutDto">
        SELECT cm.id,concat('C',cm.id) as code,cm.title, cm.menu_abstract as summary, cm.cover_url coverUrl, 5 as type,
        case when cm.is_paid = 1 then '付费' else '免费' end isPaid,
        case when cm.origin_id is null then '否' else '是' end intelligent
        from cmscenter.content_menu cm
        <where>
            cm.is_deleted = 0
            <if test="idList != null and idList != ''">
                and cm.id in
                <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryDesignClubContentCount" resultType="java.lang.Integer">
        SELECT
            COUNT(c.id)
        FROM
            cmscenter.content c
        <if test="dto.queryCollected">
            INNER JOIN cmscenter.collection_detail cd ON cd.is_deleted = 0
            AND cd.source_id = c.id
            AND cd.user_id = #{dto.userId}
            <if test="dto.memberId != null">
                AND cd.c_user_id=#{dto.memberId}
            </if>
            AND cd.source_table_name = 'content'
            AND cd.type = #{dto.channelId}
            <if test="dto.type != null">
                AND cd.content_category_id = #{dto.type}
            </if>
            AND (
            ( c.content_classification_id = 17  AND c.type = 6 ) /*厨房图库*/
            OR (
            c.content_classification_id = 17 /*屋主案例*/
            AND c.type = 1
            AND EXISTS (
            SELECT
            m.id
            FROM
            cmscenter.channel_radio_cms_mapping m
            WHERE
            m.source_table_name = 'content'
            AND m.source_id = c.id
            AND m.channel_id = #{dto.channelId}
            AND m.radio_id = #{dto.c0007RadioId}
            AND m.is_deleted = 0
            )
            )
            OR
            (
            c.type =16  /*厨房案例*/
            )
            )
        </if>

        <include refid="queryDesignClubContentWhere">
        </include>
    </select>
    <select id="queryDesignClubInfoContentCount" resultType="java.lang.Integer">
        SELECT
        COUNT(c.id)
        FROM
        cmscenter.content c
        <if test="dto.queryCollected">
            INNER JOIN cmscenter.collection_detail cd ON cd.is_deleted = 0
            AND cd.source_id = c.id
            AND cd.user_id = #{dto.userId}
            <if test="dto.memberId != null">
                AND cd.c_user_id=#{dto.memberId}
            </if>
            AND cd.source_table_name = 'content'
            AND cd.type = #{dto.channelId}
            <if test="dto.type != null">
                AND cd.content_category_id = #{dto.type}
            </if>
        </if>

        <include refid="queryDesignClubContentWhere">
        </include>
    </select>


    <sql id="queryDesignClubContentWhere">
        <where>
            c.is_deleted = 0
            <if test="dto.type != null">  <!--6：厨房图库  1:图文、  16：灵感案例  -->
                AND c.type = #{dto.type}
            </if>

            AND c.auditing_status=3
            AND c.online_status=1
            and (c.end_time is null or (now() between c.start_time and c.end_time))
            <if test="dto.contentClassificationId != null">   <!-- 内容分类 17:居家装修 -->
                and c.content_classification_id= #{dto.contentClassificationId}
            </if>
            <if test="dto.secondCategoryIdList != null and  dto.secondCategoryIdList.size()>0">   <!-- 灵感分类二级id集合，当内容类型是灵感案例时赋值 -->
                AND c.second_category_id IN
                <foreach collection="dto.secondCategoryIdList" item="secondCategoryId" separator="," open="(" close=")">
                    #{secondCategoryId}
                </foreach>
            </if>

            <if test="dto.kitAreaList != null and dto.kitAreaList.size()>0">
                and
                <foreach collection="dto.kitAreaList" item="kitArea" open="(" close=")" separator="or">
                    FIND_IN_SET( #{kitArea},c.kit_area)
                </foreach>
            </if>

            <if test="dto.kitProductList != null and dto.kitProductList.size()>0">
                and
                <foreach collection="dto.kitProductList" separator="or" open="(" close=")" item="kitProduct">
                    FIND_IN_SET( #{kitProduct},c.kit_product)
                </foreach>
            </if>

            <if test="dto.kitLayoutList != null and dto.kitLayoutList.size()>0">
                and
                <foreach collection="dto.kitLayoutList" item="kitLayout" open="(" close=")" separator="or">
                    FIND_IN_SET( #{kitLayout},c.kit_layout)
                </foreach>
            </if>

            <if test="dto.kitStyleList != null and dto.kitStyleList.size()>0">
                and
                <foreach collection="dto.kitStyleList" separator="or" open="(" close=")" item="kitStyle">
                    FIND_IN_SET( #{kitStyle},c.kit_style)
                </foreach>
            </if>




            <if test="dto.channelId != null">
                AND EXISTS (
                SELECT
                m.id
                FROM
                cmscenter.channel_radio_cms_mapping m
                WHERE
                m.source_table_name = 'content'
                AND m.source_id=c.id
                AND m.channel_id = #{dto.channelId}
                <if test="dto.radioId != null">
                    and m.radio_id =#{dto.radioId}
                </if>
                AND m.is_deleted = 0)
            </if>
        </where>
    </sql>

    <select id="queryDesignClubContent"
            resultType="com.fotile.cmscenter.content.pojo.vo.designClub.DesignClubContentVO">
        SELECT
        c.id,
        c.title,
        c.type,
        c.original_author originalAuthor,
        c.summary,
        c.created_date createDate,
        c.inspire_video_url inspireVideoUrl,
        c.inspire_video_cover_url inspireVideoCoverUrl,
        c.cover_url
        FROM
        cmscenter.content c
        <if test="dto.queryCollected">
            INNER JOIN cmscenter.collection_detail cd ON cd.is_deleted = 0
            AND cd.source_id = c.id
            AND cd.user_id = #{dto.userId}
            <if test="dto.memberId != null">
                AND cd.c_user_id=#{dto.memberId}
            </if>
            AND cd.source_table_name = 'content'
            <if test="dto.type != null">
                AND cd.content_category_id = #{dto.type}
            </if>
            AND cd.type = #{dto.channelId}
            AND (
            ( c.content_classification_id = 17  AND c.type = 6 ) /*厨房图库*/
            OR (
            c.content_classification_id = 17 /*屋主案例*/
            AND c.type = 1
            AND EXISTS (
            SELECT
            m.id
            FROM
            cmscenter.channel_radio_cms_mapping m
            WHERE
            m.source_table_name = 'content'
            AND m.source_id = c.id
            AND m.channel_id = #{dto.channelId}
            AND m.radio_id = #{dto.c0007RadioId}
            AND m.is_deleted = 0
            )
            )
            OR
            (
            c.type =16  /*厨房案例*/
            )
            )
        </if>
        <include refid="queryDesignClubContentWhere">
        </include>
        ORDER BY c.id DESC
        LIMIT #{pg.offset},#{pg.size}
    </select>

    <select id="queryDesignClubInfoContent"
            resultType="com.fotile.cmscenter.content.pojo.vo.designClub.DesignClubContentVO">
        SELECT
        c.id,
        c.title,
        c.type,
        c.original_author originalAuthor,
        c.summary,
        c.created_date createDate,
        c.inspire_video_url inspireVideoUrl,
        c.inspire_video_cover_url inspireVideoCoverUrl,
        c.cover_url
        FROM
        cmscenter.content c
        <if test="dto.queryCollected">
            INNER JOIN cmscenter.collection_detail cd ON cd.is_deleted = 0
            AND cd.source_id = c.id
            AND cd.user_id = #{dto.userId}
            <if test="dto.memberId != null">
                AND cd.c_user_id=#{dto.memberId}
            </if>
            AND cd.source_table_name = 'content'
            <if test="dto.type != null">
                AND cd.content_category_id = #{dto.type}
            </if>
            AND cd.type = #{dto.channelId}
        </if>
        <include refid="queryDesignClubContentWhere">
        </include>
        ORDER BY c.id DESC
        LIMIT #{pg.offset},#{pg.size}
    </select>

    <select id="findContentsCount4DesignClub" resultType="java.lang.Integer">
        SELECT
            COUNT( t.id )
        FROM
            cmscenter.content t
        <include refid="findContentsList4DesignClubWhere">
        </include>
    </select>

    <select id="findContentsList4DesignClub" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO">
        SELECT
            COUNT(cd.id) collectionCount,
            t.*
        FROM
            cmscenter.content t
                LEFT JOIN cmscenter.collection_detail cd ON cd.source_id = t.id
                AND cd.is_deleted = 0
                AND cd.source_table_name = #{dto.sourceTableName}
        <if test="dto.channelId != null">
            AND cd.type = #{dto.channelId}
        </if>
      <include refid="findContentsList4DesignClubWhere">
      </include>
        GROUP BY
            t.id
        ORDER BY
        collectionCount desc,
            t.created_date DESC
        limit #{pg.offset},#{pg.size}
    </select>

    <sql id="findContentsList4DesignClubWhere">
        <where>
            t.is_deleted = 0
            AND t.auditing_status = 3
            AND t.online_status = 1
            AND t.is_authorization =1
            <if test="dto.activityId!=null">
                AND t.activity_id = #{dto.activityId}
            </if>
        </where>
    </sql>

    <select id="queryAllContentToWord" resultType="com.fotile.cmscenter.content.pojo.dto.ExportToWordDto">
        SELECT c.id,
               c.title,
               cc.name contentClassificationName,
               c.show_type showType,
               case when c.kitchen_case_type = 1 then '换装' when c.kitchen_case_type = 2 then '新装' else '' end kitchenCaseType,
               concat(c.province_name,city_name,area_name,c.community_name) communityName,
               c.case_video_url caseVideoUrl,
               c.case_describe caseDescribe,
               c.pain_spot painSpot,
               c.renovation_project renovationProject,
               case when c.auditing_status = '4' then refuse_note else '您发布的案例已经审核通过!' end caseDescribe,
               c.vr_url vrUrl,c.vr_cover_url vrCoverUrl,c.before_vr_url beforeVrUrl,c.before_vr_cover_url beforeVrCoverUrl
        FROM cmscenter.content c
        LEFT JOIN cmscenter.content_category cc on cc.id=c.content_classification_id and cc.is_deleted =0
        WHERE c.is_deleted = 0 and c.online_status = '1' and c.type = '15'
              and (c.start_time is null or c.end_time is null or now() between c.start_time and c.end_time)
        <if test="ids != null and ids.size > 0">
            and c.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getCompoundInfoTitle" resultType="com.fotile.cmscenter.compound.pojo.dto.CompoundInfoTitleOutDto">
        SELECT
        id,
        title
        FROM
        content
        WHERE
        is_deleted = 0
        and id  = #{id}
    </select>

    <select id="queryContentListCount" resultType="java.lang.Integer">
        SELECT sum(tem.total) from (
        SELECT count(DISTINCT c.id) total
        from cmscenter.content c
        where c.is_deleted=0
        and c.is_draft = 0
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.channel_radio_cms_mapping crcm
            WHERE
            crcm.source_table_name = 'content'
            AND crcm.is_deleted =0
            <trim prefix="and " suffixOverrides="or">
                <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="item.channelId !=null and item.channelId !=''">
                            crcm.channel_id=#{item.channelId}
                        </if>
                        <if test="item.ratioId !=null and item.ratioId !=''">
                            and crcm.radio_id=#{item.ratioId}
                        </if>
                    </trim>
                </foreach>
            </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and c.id in (
            select DISTINCT
            c.id
            from cmscenter.content c
            left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
            where
            c.is_deleted = 0
            and (ccm.source_id is null
            or ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
            )
            <!--SELECT
            DISTINCT source_id
            FROM
            cmscenter.company_cms_mapping ccm
            WHERE
            ccm.source_table_name = 'content'
            AND ccm.is_deleted =0
            and ccm.company_id in
            <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )-->
        </if>
        <if test="contentQueryVo.id !=null">
            and c.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
            and concat('',c.id,'') in
            <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
            and (
            concat('N',c.id) like concat('%',#{contentQueryVo.keywords},'%')
            or
            c.title like CONCAT('%',#{contentQueryVo.keywords},'%')
            )
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and c.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and c.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and c.type in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
        UNION ALL
        SELECT count(distinct cm.id) total
        from cmscenter.content_menu cm
        where cm.is_deleted=0
        <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
            and cm.id in (
                SELECT
                DISTINCT source_id
                FROM
                cmscenter.channel_radio_cms_mapping crcm
                WHERE
                crcm.source_table_name = 'content_menu'
                AND crcm.is_deleted =0
                <trim prefix="and " suffixOverrides="or">
                    <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                        <trim prefix="(" suffix=")" prefixOverrides="and">
                            <if test="item.channelId !=null and item.channelId !=''">
                                crcm.channel_id=#{item.channelId}
                            </if>
                            <if test="item.ratioId !=null and item.ratioId !=''">
                                and crcm.radio_id=#{item.ratioId}
                            </if>
                        </trim>
                    </foreach>
                </trim>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
            and cm.id in(
                SELECT DISTINCT
                source_id
                FROM
                cmscenter.company_cms_mapping ccm
                WHERE
                ccm.source_table_name = 'content_menu'
                AND ccm.is_deleted =0
                and ccm.company_id in
                <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            )
        </if>
        <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
            and cm.id in(
                select DISTINCT
                c.id
                from cmscenter.content_menu c
                left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu'
                and c.id = ccm.source_id
                where
                    c.is_deleted = 0
                    and (ccm.source_id is null
                    or ccm.company_id in
                    <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
            )
        </if>
        <if test="contentQueryVo.id !=null">
            and cm.id=#{contentQueryVo.id}
        </if>
        <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
            and concat('',cm.id,'') in
            <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
            and cm.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
        </if>
        <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
            and (
            concat('C',cm.id) like concat('%',#{contentQueryVo.keywords},'%')
            or
            cm.title like CONCAT('%',#{contentQueryVo.keywords},'%')
            )
        </if>
        <if test="contentQueryVo.auditStatus !=null ">
            and cm.auditing_status=#{contentQueryVo.auditStatus}
        </if>
        <if test="contentQueryVo.onlineStatus !=null">
            and cm.online_status=#{contentQueryVo.onlineStatus}
        </if>
        <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
            and '5' in (
            <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                #{item}
            </foreach>
            )
        </if>
        ) tem
    </select>
    <select id="queryContentList" resultType="com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO"
            parameterType="com.fotile.cmscenter.content.pojo.vo.ContentQueryVo">
        SELECT tt.* from (
            SELECT * from (
                SELECT c.id,concat('N',c.id) as code,c.title,c.start_time startTime,c.end_time endTime,c.type,
                c.sort,c.auditing_status as auditStatus,c.online_status as onlineStatus,
                c.created_date as createTime, c.cover_url coverUrl,c.content_classification_id classId,
                c.upload_user_type as uploadUserType,c.created_person createdPerson,'' originId,'' originalAuthor,
                100 as intelligent,c.online_time onlineTime,100 as vedioMenu, if(c.is_paid = 0 , 0 , 1) as isPaid,
                c.second_category_id secondCategoryId, c.inspire_video_url inspireVideoUrl,
                c.inspire_video_cover_url inspireVideoCoverUrl,c.official_account_url officialAccountUrl,
                c.kit_area kitArea,c.kit_layout kitLayout,c.kit_style kitStyle,c.kit_product kitProduct,
                c.kit_installation kitInstallation,c.kit_type kitType,
                c.summary,c.show_type as showType, c.created_by as createdBy
                from cmscenter.content c
                <where>
                    c.is_deleted=0
                    and c.is_draft = 0
                    <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
                        and c.id in (
                        SELECT
                        DISTINCT source_id
                        FROM
                        cmscenter.channel_radio_cms_mapping crcm
                        WHERE
                        crcm.source_table_name = 'content'
                        AND crcm.is_deleted =0
                        <trim prefix="and " suffixOverrides="or">
                            <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                                <trim prefix="(" suffix=")" prefixOverrides="and">
                                    <if test="item.channelId !=null and item.channelId !=''">
                                        crcm.channel_id=#{item.channelId}
                                    </if>
                                    <if test="item.ratioId !=null and item.ratioId !=''">
                                        and crcm.radio_id=#{item.ratioId}
                                    </if>
                                </trim>
                            </foreach>
                        </trim>
                        )
                    </if>
                    <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
                        and c.id in (
                        SELECT
                        DISTINCT source_id
                        FROM
                        cmscenter.company_cms_mapping ccm
                        WHERE
                        ccm.source_table_name = 'content'
                        AND ccm.is_deleted =0
                        and ccm.company_id in
                        <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
                        and c.id in (
                        select DISTINCT
                        c.id
                        from cmscenter.content c
                        left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content' and c.id = ccm.source_id
                        where
                        c.is_deleted = 0
                        and (ccm.source_id is null
                        or ccm.company_id in
                        <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                        )
                        <!--SELECT
                        DISTINCT source_id
                        FROM
                        cmscenter.company_cms_mapping ccm
                        WHERE
                        ccm.source_table_name = 'content'
                        AND ccm.is_deleted =0
                        and ccm.company_id in
                        <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )-->
                    </if>
                    <if test="contentQueryVo.id !=null">
                        and c.id=#{contentQueryVo.id}
                    </if>
                    <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
                        and concat('',c.id,'') in
                        <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                            #{cid}
                        </foreach>
                    </if>
                    <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
                        and c.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
                    </if>
                    <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
                        and (
                        concat('N',c.id) like concat('%',#{contentQueryVo.keywords},'%')
                        or
                        c.title like CONCAT('%',#{contentQueryVo.keywords},'%')
                        )
                    </if>
                    <if test="contentQueryVo.auditStatus !=null ">
                        and c.auditing_status=#{contentQueryVo.auditStatus}
                    </if>
                    <if test="contentQueryVo.onlineStatus !=null">
                        and c.online_status=#{contentQueryVo.onlineStatus}
                    </if>
                    <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
                        and c.type in (
                        <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </where>
                group by c.id
                <choose>
                    <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                        <if test="contentQueryVo.orderBy=='createTime asc'.toString()">
                            order by c.created_date asc
                        </if>
                        <if test="contentQueryVo.orderBy=='createTime desc'.toString()">
                            order by c.created_date desc
                        </if>
                        <if test="contentQueryVo.orderBy=='browseCount asc'.toString()">
                            order by scm.browse_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='browseCount desc'.toString()">
                            order by scm.browse_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='commentCount asc'.toString()">
                            order by scm.comment_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='commentCount desc'.toString()">
                            order by scm.comment_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='shareCount asc'.toString()">
                            order by scm.share_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='shareCount desc'.toString()">
                            order by scm.share_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='collectionCount asc'.toString()">
                            order by collectionCount asc
                        </if>
                        <if test="contentQueryVo.orderBy=='collectionCount desc'.toString()">
                            order by collectionCount desc
                        </if>
                        <if test="contentQueryVo.orderBy=='likeCount asc'.toString()">
                            order by scm.like_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='likeCount desc'.toString()">
                            order by scm.like_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='questions asc'.toString()">
                            order by qu.questions asc
                        </if>
                        <if test="contentQueryVo.orderBy=='questions desc'.toString()">
                            order by qu.questions desc
                        </if>
                        <if test="contentQueryVo.orderBy=='onlineTime asc'.toString()">
                            order by c.online_time asc
                        </if>
                        <if test="contentQueryVo.orderBy=='onlineTime desc'.toString()">
                            order by c.online_time desc
                        </if>
                        <if test="contentQueryVo.orderBy=='sort asc'.toString()">
                            order by c.sort asc
                        </if>
                        <if test="contentQueryVo.orderBy=='sort desc'.toString()">
                            order by c.sort desc
                        </if>

                    </when>
                    <otherwise>
                        order by c.created_date DESC
                    </otherwise>
                </choose>
                ,c.id DESC
                limit #{contentQueryVo.offsetAndSize}
            ) a
            UNION ALL
            select * from (
                SELECT cm.id,concat('C',cm.id) as code,cm.title,cm.start_time as startTime,cm.end_time as endTime,5 as type,
                cm.sort, cm.auditing_status as auditStatus,cm.online_status as onlineStatus,
                cm.created_date as createTime, cm.cover_url coverUrl, cm.content_category_id classId,
                cm.upload_user_type uploadUserType,cm.created_person createdPerson,cm.origin_id originId,cm.original_author originalAuthor,
                if(cm.origin_id is null,0,1) intelligent,cm.online_time onlineTime,cm.vedio_menu vedioMenu, if(cm.is_paid = 0 , 0 , 1) as isPaid,
                null as secondCategoryId, '' as inspireVideoUrl,'' as  inspireVideoCoverUrl,'' as  officialAccountUrl,
                '' kitArea,'' kitLayout,'' kitStyle,'' kitProduct,'' kitInstallation,'' kitType,
                cm.menu_abstract as summary, '' showType, cm.created_by as createdBy
                from cmscenter.content_menu cm
                <where>
                    cm.is_deleted=0
                    <if test="contentQueryVo.channelIds !=null and contentQueryVo.channelIds.size()>0">
                        and cm.id in (
                        SELECT
                        DISTINCT source_id
                        FROM
                        cmscenter.channel_radio_cms_mapping crcm
                        WHERE
                        crcm.source_table_name = 'content_menu'
                        AND crcm.is_deleted =0
                        <trim prefix="and " suffixOverrides="or">
                            <foreach collection="contentQueryVo.channelIds" index="index" item="item" open="(" close=")" separator="or">
                                <trim prefix="(" suffix=")" prefixOverrides="and">
                                    <if test="item.channelId !=null and item.channelId !=''">
                                        crcm.channel_id=#{item.channelId}
                                    </if>
                                    <if test="item.ratioId !=null and item.ratioId !=''">
                                        and crcm.radio_id=#{item.ratioId}
                                    </if>
                                </trim>
                            </foreach>
                        </trim>
                        )
                    </if>
                    <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and !contentQueryVo.isCompanyChooseEmpty">
                        and cm.id in(
                        SELECT DISTINCT
                        source_id
                        FROM
                        cmscenter.company_cms_mapping ccm
                        WHERE
                        ccm.source_table_name = 'content_menu'
                        AND ccm.is_deleted =0
                        and ccm.company_id in
                        <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <if test="contentQueryVo.companyIds !=null and contentQueryVo.companyIds.size()>0 and contentQueryVo.isCompanyChooseEmpty">
                        and cm.id in(
                        select DISTINCT
                        c.id
                        from cmscenter.content_menu c
                        left join cmscenter.company_cms_mapping ccm on ccm.is_deleted = 0 and ccm.source_table_name = 'content_menu'
                        and c.id = ccm.source_id
                        where
                        c.is_deleted = 0
                        and (ccm.source_id is null
                        or ccm.company_id in
                        <foreach collection="contentQueryVo.companyIds" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        )
                        )
                    </if>
                    <if test="contentQueryVo.id !=null">
                        and cm.id=#{contentQueryVo.id}
                    </if>
                    <if test="contentQueryVo.contentIds != null and contentQueryVo.contentIds.size() != 0">
                        and concat('',cm.id,'') in
                        <foreach collection="contentQueryVo.contentIds" item="cid" open="(" separator="," close=")">
                            #{cid}
                        </foreach>
                    </if>
                    <if test="contentQueryVo.title != null and contentQueryVo.title != ''">
                        and cm.title like CONCAT(CONCAT('%',#{contentQueryVo.title}),'%')
                    </if>
                    <if test="contentQueryVo.keywords != null and contentQueryVo.keywords != ''">
                        and (
                        concat('C',cm.id) like concat('%',#{contentQueryVo.keywords},'%')
                        or
                        cm.title like CONCAT('%',#{contentQueryVo.keywords},'%')
                        )
                    </if>
                    <if test="contentQueryVo.auditStatus !=null ">
                        and cm.auditing_status=#{contentQueryVo.auditStatus}
                    </if>
                    <if test="contentQueryVo.onlineStatus !=null">
                        and cm.online_status=#{contentQueryVo.onlineStatus}
                    </if>
                    <if test="contentQueryVo.types != null and contentQueryVo.types !=''">
                        and '5' in (
                        <foreach item = "item" collection="contentQueryVo.types.split(',')" separator=",">
                            #{item}
                        </foreach>
                        )
                    </if>
                </where>
                group by cm.id
                <choose>
                    <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                        <if test="contentQueryVo.orderBy=='createTime asc'.toString()">
                            order by cm.created_date asc
                        </if>
                        <if test="contentQueryVo.orderBy=='createTime desc'.toString()">
                            order by cm.created_date desc
                        </if>
                        <if test="contentQueryVo.orderBy=='browseCount asc'.toString()">
                            order by scm.browse_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='browseCount desc'.toString()">
                            order by scm.browse_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='commentCount asc'.toString()">
                            order by scm.comment_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='commentCount desc'.toString()">
                            order by scm.comment_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='shareCount asc'.toString()">
                            order by scm.share_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='shareCount desc'.toString()">
                            order by scm.share_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='collectionCount asc'.toString()">
                            order by scm.collection_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='collectionCount desc'.toString()">
                            order by scm.collection_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='likeCount asc'.toString()">
                            order by scm.like_count asc
                        </if>
                        <if test="contentQueryVo.orderBy=='likeCount desc'.toString()">
                            order by scm.like_count desc
                        </if>
                        <if test="contentQueryVo.orderBy=='questions asc'.toString()">
                            order by qu.questions asc
                        </if>
                        <if test="contentQueryVo.orderBy=='questions desc'.toString()">
                            order by qu.questions desc
                        </if>
                        <if test="contentQueryVo.orderBy=='products asc'.toString()">
                            order by pr.products asc
                        </if>
                        <if test="contentQueryVo.orderBy=='products desc'.toString()">
                            order by pr.products desc
                        </if>
                        <if test="contentQueryVo.orderBy=='onlineTime asc'.toString()">
                            order by cm.online_time asc
                        </if>
                        <if test="contentQueryVo.orderBy=='onlineTime desc'.toString()">
                            order by cm.online_time desc
                        </if>
                        <if test="contentQueryVo.orderBy=='sort asc'.toString()">
                            order by cm.sort asc
                        </if>
                        <if test="contentQueryVo.orderBy=='sort desc'.toString()">
                            order by cm.sort desc
                        </if>

                    </when>
                    <otherwise>
                        order by cm.created_date DESC
                    </otherwise>
                </choose>
                ,cm.id DESC
                limit #{contentQueryVo.offsetAndSize}
            ) b
        ) tt
        <choose>
            <when test="contentQueryVo.orderBy !=null and contentQueryVo.orderBy !=''">
                order by ${contentQueryVo.orderBy}
            </when>
            <otherwise>
                order by createTime DESC
            </otherwise>
        </choose>
        ,id DESC
        limit #{contentQueryVo.offset},#{contentQueryVo.size}
    </select>

</mapper>
