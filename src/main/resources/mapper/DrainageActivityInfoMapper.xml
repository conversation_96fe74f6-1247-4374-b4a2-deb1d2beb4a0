<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.sopcenter.dryProcess.dao.DrainageActivityInfoMapper">
  <resultMap id="BaseResultMap" type="com.fotile.sopcenter.dryProcess.pojo.entity.DrainageActivityInfoEntity">
    <!--@mbg.generated-->
    <!--@Table `customercenter`.`drainage_activity_info`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="page_type" jdbcType="VARCHAR" property="pageType" />
    <result column="drainage_page" jdbcType="VARCHAR" property="drainagePage" />
    <result column="page_id" jdbcType="INTEGER" property="pageId" />
    <result column="qr_type" jdbcType="VARCHAR" property="qrType" />
    <result column="source_code" jdbcType="VARCHAR" property="sourceCode" />
    <result column="source_detail_code" jdbcType="VARCHAR" property="sourceDetailCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="revise_name" jdbcType="VARCHAR" property="reviseName" />
    <result column="revise_time" jdbcType="TIMESTAMP" property="reviseTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
    <result column="store_all_flag" jdbcType="TINYINT" property="storeAllFlag" />
    <result column="user_all_flag" jdbcType="TINYINT" property="userAllFlag" />
    <result column="created_person" jdbcType="VARCHAR" property="createdPerson" />
    <result column="transmit_type" jdbcType="TINYINT" property="transmitType" />
    <result column="company_main_flag" jdbcType="TINYINT" property="companyMainFlag" />
    <result column="create_clues_flag" jdbcType="TINYINT" property="createCluesFlag" />
    <result column="draft_Clues_Flag" jdbcType="TINYINT" property="draftCluesFlag" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="interests_background_url" jdbcType="VARCHAR" property="interestsBackgroundUrl" />
    <result column="member_card_top_url" jdbcType="VARCHAR" property="memberCardTopUrl" />
    <result column="member_card_detail_url" jdbcType="VARCHAR" property="memberCardDetailUrl" />
    <result column="check_address_flag" jdbcType="TINYINT" property="checkAddressFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `name`, `company_id`, `company_name`, `start_time`, `end_time`, `background_url`, 
    `channel_code`, `channel_name`, `page_type`, `drainage_page`, `page_id`, `qr_type`, 
    `source_code`, `source_detail_code`, `status`, `remark`, `revise_name`, `revise_time`, 
    `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `store_all_flag`, 
    `user_all_flag`, `created_person`, `transmit_type`, `company_main_flag`, `create_clues_flag`, 
    `draft_Clues_Flag`, `activity_id`, `interests_background_url`, `member_card_top_url`, 
    `member_card_detail_url`, `check_address_flag`
  </sql>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.dryProcess.pojo.entity.DrainageActivityInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `customercenter`.`drainage_activity_info`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">
        `name`,
      </if>
      <if test="companyId != null">
        `company_id`,
      </if>
      <if test="companyName != null and companyName != ''">
        `company_name`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        `background_url`,
      </if>
      <if test="channelCode != null and channelCode != ''">
        `channel_code`,
      </if>
      <if test="channelName != null and channelName != ''">
        `channel_name`,
      </if>
      <if test="pageType != null and pageType != ''">
        `page_type`,
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        `drainage_page`,
      </if>
      <if test="pageId != null">
        `page_id`,
      </if>
      <if test="qrType != null and qrType != ''">
        `qr_type`,
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        `source_code`,
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        `source_detail_code`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null and remark != ''">
        `remark`,
      </if>
      <if test="reviseName != null and reviseName != ''">
        `revise_name`,
      </if>
      <if test="reviseTime != null">
        `revise_time`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="storeAllFlag != null">
        `store_all_flag`,
      </if>
      <if test="userAllFlag != null">
        `user_all_flag`,
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        `created_person`,
      </if>
      <if test="transmitType != null">
        `transmit_type`,
      </if>
      <if test="companyMainFlag != null">
        `company_main_flag`,
      </if>
      <if test="createCluesFlag != null">
        `create_clues_flag`,
      </if>
      <if test="draftCluesFlag != null">
        `draft_Clues_Flag`,
      </if>
      <if test="activityId != null">
        `activity_id`,
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        `interests_background_url`,
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        `member_card_top_url`,
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        `member_card_detail_url`,
      </if>
      <if test="checkAddressFlag != null">
        `check_address_flag`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null and companyName != ''">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null and channelCode != ''">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="pageType != null and pageType != ''">
        #{pageType,jdbcType=VARCHAR},
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        #{drainagePage,jdbcType=VARCHAR},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=INTEGER},
      </if>
      <if test="qrType != null and qrType != ''">
        #{qrType,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        #{sourceDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reviseName != null and reviseName != ''">
        #{reviseName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTime != null">
        #{reviseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeAllFlag != null">
        #{storeAllFlag,jdbcType=TINYINT},
      </if>
      <if test="userAllFlag != null">
        #{userAllFlag,jdbcType=TINYINT},
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        #{createdPerson,jdbcType=VARCHAR},
      </if>
      <if test="transmitType != null">
        #{transmitType,jdbcType=TINYINT},
      </if>
      <if test="companyMainFlag != null">
        #{companyMainFlag,jdbcType=TINYINT},
      </if>
      <if test="createCluesFlag != null">
        #{createCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="draftCluesFlag != null">
        #{draftCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        #{interestsBackgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        #{memberCardTopUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        #{memberCardDetailUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkAddressFlag != null">
        #{checkAddressFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.sopcenter.dryProcess.pojo.entity.DrainageActivityInfoEntity">
    <!--@mbg.generated-->
    update `customercenter`.`drainage_activity_info`
    <set>
      <if test="name != null and name != ''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        `company_id` = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null and companyName != ''">
        `company_name` = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        `background_url` = #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null and channelCode != ''">
        `channel_code` = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        `channel_name` = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="pageType != null and pageType != ''">
        `page_type` = #{pageType,jdbcType=VARCHAR},
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        `drainage_page` = #{drainagePage,jdbcType=VARCHAR},
      </if>
      <if test="pageId != null">
        `page_id` = #{pageId,jdbcType=INTEGER},
      </if>
      <if test="qrType != null and qrType != ''">
        `qr_type` = #{qrType,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        `source_code` = #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        `source_detail_code` = #{sourceDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null and remark != ''">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reviseName != null and reviseName != ''">
        `revise_name` = #{reviseName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTime != null">
        `revise_time` = #{reviseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeAllFlag != null">
        `store_all_flag` = #{storeAllFlag,jdbcType=TINYINT},
      </if>
      <if test="userAllFlag != null">
        `user_all_flag` = #{userAllFlag,jdbcType=TINYINT},
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        `created_person` = #{createdPerson,jdbcType=VARCHAR},
      </if>
      <if test="transmitType != null">
        `transmit_type` = #{transmitType,jdbcType=TINYINT},
      </if>
      <if test="companyMainFlag != null">
        `company_main_flag` = #{companyMainFlag,jdbcType=TINYINT},
      </if>
      <if test="createCluesFlag != null">
        `create_clues_flag` = #{createCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="draftCluesFlag != null">
        `draft_Clues_Flag` = #{draftCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="activityId != null">
        `activity_id` = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        `interests_background_url` = #{interestsBackgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        `member_card_top_url` = #{memberCardTopUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        `member_card_detail_url` = #{memberCardDetailUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkAddressFlag != null">
        `check_address_flag` = #{checkAddressFlag,jdbcType=TINYINT},
      </if>
    </set>
    where `id` = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `customercenter`.`drainage_activity_info`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`company_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`company_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`start_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.startTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`end_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.endTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`background_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.backgroundUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.channelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`channel_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.channelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`page_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.pageType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`drainage_page` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.drainagePage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`page_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.pageId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`qr_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.qrType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`source_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.sourceCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`source_detail_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.sourceDetailCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`remark` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`revise_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.reviseName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`revise_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.reviseTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.modifiedBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.modifiedDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`store_all_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.storeAllFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`user_all_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.userAllFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`created_person` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdPerson,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`transmit_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.transmitType,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`company_main_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyMainFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`create_clues_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.createCluesFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`draft_Clues_Flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.draftCluesFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`activity_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.activityId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`interests_background_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.interestsBackgroundUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`member_card_top_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.memberCardTopUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`member_card_detail_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.memberCardDetailUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`check_address_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=INTEGER} then #{item.checkAddressFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `customercenter`.`drainage_activity_info`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`company_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`company_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyName != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`start_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.startTime != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.startTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`end_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.endTime != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.endTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`background_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.backgroundUrl != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.backgroundUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelCode != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.channelCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`channel_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelName != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.channelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`page_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pageType != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.pageType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`drainage_page` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.drainagePage != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.drainagePage,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`page_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pageId != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.pageId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`qr_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.qrType != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.qrType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`source_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sourceCode != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.sourceCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`source_detail_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sourceDetailCode != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.sourceDetailCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`remark` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`revise_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reviseName != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.reviseName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`revise_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reviseTime != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.reviseTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`is_deleted` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdDate != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_by` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedBy != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.modifiedBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`modified_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifiedDate != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.modifiedDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`store_all_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeAllFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.storeAllFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`user_all_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userAllFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.userAllFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`created_person` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdPerson != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.createdPerson,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`transmit_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transmitType != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.transmitType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`company_main_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyMainFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.companyMainFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`create_clues_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createCluesFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.createCluesFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`draft_Clues_Flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.draftCluesFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.draftCluesFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`activity_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.activityId != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.activityId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`interests_background_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.interestsBackgroundUrl != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.interestsBackgroundUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`member_card_top_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memberCardTopUrl != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.memberCardTopUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`member_card_detail_url` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memberCardDetailUrl != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.memberCardDetailUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`check_address_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkAddressFlag != null">
            when `id` = #{item.id,jdbcType=INTEGER} then #{item.checkAddressFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `customercenter`.`drainage_activity_info`
    (`name`, `company_id`, `company_name`, `start_time`, `end_time`, `background_url`, 
      `channel_code`, `channel_name`, `page_type`, `drainage_page`, `page_id`, `qr_type`, 
      `source_code`, `source_detail_code`, `status`, `remark`, `revise_name`, `revise_time`, 
      `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `store_all_flag`, 
      `user_all_flag`, `created_person`, `transmit_type`, `company_main_flag`, `create_clues_flag`, 
      `draft_Clues_Flag`, `activity_id`, `interests_background_url`, `member_card_top_url`, 
      `member_card_detail_url`, `check_address_flag`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.companyId,jdbcType=INTEGER}, #{item.companyName,jdbcType=VARCHAR}, 
        #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.backgroundUrl,jdbcType=VARCHAR}, 
        #{item.channelCode,jdbcType=VARCHAR}, #{item.channelName,jdbcType=VARCHAR}, #{item.pageType,jdbcType=VARCHAR}, 
        #{item.drainagePage,jdbcType=VARCHAR}, #{item.pageId,jdbcType=INTEGER}, #{item.qrType,jdbcType=VARCHAR}, 
        #{item.sourceCode,jdbcType=VARCHAR}, #{item.sourceDetailCode,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.reviseName,jdbcType=VARCHAR}, 
        #{item.reviseTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, 
        #{item.createdDate,jdbcType=TIMESTAMP}, #{item.modifiedBy,jdbcType=VARCHAR}, #{item.modifiedDate,jdbcType=TIMESTAMP}, 
        #{item.storeAllFlag,jdbcType=TINYINT}, #{item.userAllFlag,jdbcType=TINYINT}, #{item.createdPerson,jdbcType=VARCHAR}, 
        #{item.transmitType,jdbcType=TINYINT}, #{item.companyMainFlag,jdbcType=TINYINT}, 
        #{item.createCluesFlag,jdbcType=TINYINT}, #{item.draftCluesFlag,jdbcType=TINYINT}, 
        #{item.activityId,jdbcType=BIGINT}, #{item.interestsBackgroundUrl,jdbcType=VARCHAR}, 
        #{item.memberCardTopUrl,jdbcType=VARCHAR}, #{item.memberCardDetailUrl,jdbcType=VARCHAR}, 
        #{item.checkAddressFlag,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.dryProcess.pojo.entity.DrainageActivityInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `customercenter`.`drainage_activity_info`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      `name`,
      `company_id`,
      `company_name`,
      `start_time`,
      `end_time`,
      `background_url`,
      `channel_code`,
      `channel_name`,
      `page_type`,
      `drainage_page`,
      `page_id`,
      `qr_type`,
      `source_code`,
      `source_detail_code`,
      `status`,
      `remark`,
      `revise_name`,
      `revise_time`,
      `is_deleted`,
      `created_by`,
      `created_date`,
      `modified_by`,
      `modified_date`,
      `store_all_flag`,
      `user_all_flag`,
      `created_person`,
      `transmit_type`,
      `company_main_flag`,
      `create_clues_flag`,
      `draft_Clues_Flag`,
      `activity_id`,
      `interests_background_url`,
      `member_card_top_url`,
      `member_card_detail_url`,
      `check_address_flag`,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{name,jdbcType=VARCHAR},
      #{companyId,jdbcType=INTEGER},
      #{companyName,jdbcType=VARCHAR},
      #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP},
      #{backgroundUrl,jdbcType=VARCHAR},
      #{channelCode,jdbcType=VARCHAR},
      #{channelName,jdbcType=VARCHAR},
      #{pageType,jdbcType=VARCHAR},
      #{drainagePage,jdbcType=VARCHAR},
      #{pageId,jdbcType=INTEGER},
      #{qrType,jdbcType=VARCHAR},
      #{sourceCode,jdbcType=VARCHAR},
      #{sourceDetailCode,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT},
      #{remark,jdbcType=VARCHAR},
      #{reviseName,jdbcType=VARCHAR},
      #{reviseTime,jdbcType=TIMESTAMP},
      #{isDeleted,jdbcType=INTEGER},
      #{createdBy,jdbcType=VARCHAR},
      #{createdDate,jdbcType=TIMESTAMP},
      #{modifiedBy,jdbcType=VARCHAR},
      #{modifiedDate,jdbcType=TIMESTAMP},
      #{storeAllFlag,jdbcType=TINYINT},
      #{userAllFlag,jdbcType=TINYINT},
      #{createdPerson,jdbcType=VARCHAR},
      #{transmitType,jdbcType=TINYINT},
      #{companyMainFlag,jdbcType=TINYINT},
      #{createCluesFlag,jdbcType=TINYINT},
      #{draftCluesFlag,jdbcType=TINYINT},
      #{activityId,jdbcType=BIGINT},
      #{interestsBackgroundUrl,jdbcType=VARCHAR},
      #{memberCardTopUrl,jdbcType=VARCHAR},
      #{memberCardDetailUrl,jdbcType=VARCHAR},
      #{checkAddressFlag,jdbcType=TINYINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=INTEGER},
      </if>
      `name` = #{name,jdbcType=VARCHAR},
      `company_id` = #{companyId,jdbcType=INTEGER},
      `company_name` = #{companyName,jdbcType=VARCHAR},
      `start_time` = #{startTime,jdbcType=TIMESTAMP},
      `end_time` = #{endTime,jdbcType=TIMESTAMP},
      `background_url` = #{backgroundUrl,jdbcType=VARCHAR},
      `channel_code` = #{channelCode,jdbcType=VARCHAR},
      `channel_name` = #{channelName,jdbcType=VARCHAR},
      `page_type` = #{pageType,jdbcType=VARCHAR},
      `drainage_page` = #{drainagePage,jdbcType=VARCHAR},
      `page_id` = #{pageId,jdbcType=INTEGER},
      `qr_type` = #{qrType,jdbcType=VARCHAR},
      `source_code` = #{sourceCode,jdbcType=VARCHAR},
      `source_detail_code` = #{sourceDetailCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      `remark` = #{remark,jdbcType=VARCHAR},
      `revise_name` = #{reviseName,jdbcType=VARCHAR},
      `revise_time` = #{reviseTime,jdbcType=TIMESTAMP},
      `is_deleted` = #{isDeleted,jdbcType=INTEGER},
      `created_by` = #{createdBy,jdbcType=VARCHAR},
      `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      `store_all_flag` = #{storeAllFlag,jdbcType=TINYINT},
      `user_all_flag` = #{userAllFlag,jdbcType=TINYINT},
      `created_person` = #{createdPerson,jdbcType=VARCHAR},
      `transmit_type` = #{transmitType,jdbcType=TINYINT},
      `company_main_flag` = #{companyMainFlag,jdbcType=TINYINT},
      `create_clues_flag` = #{createCluesFlag,jdbcType=TINYINT},
      `draft_Clues_Flag` = #{draftCluesFlag,jdbcType=TINYINT},
      `activity_id` = #{activityId,jdbcType=BIGINT},
      `interests_background_url` = #{interestsBackgroundUrl,jdbcType=VARCHAR},
      `member_card_top_url` = #{memberCardTopUrl,jdbcType=VARCHAR},
      `member_card_detail_url` = #{memberCardDetailUrl,jdbcType=VARCHAR},
      `check_address_flag` = #{checkAddressFlag,jdbcType=TINYINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.sopcenter.dryProcess.pojo.entity.DrainageActivityInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `customercenter`.`drainage_activity_info`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null and name != ''">
        `name`,
      </if>
      <if test="companyId != null">
        `company_id`,
      </if>
      <if test="companyName != null and companyName != ''">
        `company_name`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        `background_url`,
      </if>
      <if test="channelCode != null and channelCode != ''">
        `channel_code`,
      </if>
      <if test="channelName != null and channelName != ''">
        `channel_name`,
      </if>
      <if test="pageType != null and pageType != ''">
        `page_type`,
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        `drainage_page`,
      </if>
      <if test="pageId != null">
        `page_id`,
      </if>
      <if test="qrType != null and qrType != ''">
        `qr_type`,
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        `source_code`,
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        `source_detail_code`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null and remark != ''">
        `remark`,
      </if>
      <if test="reviseName != null and reviseName != ''">
        `revise_name`,
      </if>
      <if test="reviseTime != null">
        `revise_time`,
      </if>
      <if test="isDeleted != null">
        `is_deleted`,
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by`,
      </if>
      <if test="createdDate != null">
        `created_date`,
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by`,
      </if>
      <if test="modifiedDate != null">
        `modified_date`,
      </if>
      <if test="storeAllFlag != null">
        `store_all_flag`,
      </if>
      <if test="userAllFlag != null">
        `user_all_flag`,
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        `created_person`,
      </if>
      <if test="transmitType != null">
        `transmit_type`,
      </if>
      <if test="companyMainFlag != null">
        `company_main_flag`,
      </if>
      <if test="createCluesFlag != null">
        `create_clues_flag`,
      </if>
      <if test="draftCluesFlag != null">
        `draft_Clues_Flag`,
      </if>
      <if test="activityId != null">
        `activity_id`,
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        `interests_background_url`,
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        `member_card_top_url`,
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        `member_card_detail_url`,
      </if>
      <if test="checkAddressFlag != null">
        `check_address_flag`,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null and companyName != ''">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null and channelCode != ''">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="pageType != null and pageType != ''">
        #{pageType,jdbcType=VARCHAR},
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        #{drainagePage,jdbcType=VARCHAR},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=INTEGER},
      </if>
      <if test="qrType != null and qrType != ''">
        #{qrType,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        #{sourceDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reviseName != null and reviseName != ''">
        #{reviseName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTime != null">
        #{reviseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null and createdBy != ''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeAllFlag != null">
        #{storeAllFlag,jdbcType=TINYINT},
      </if>
      <if test="userAllFlag != null">
        #{userAllFlag,jdbcType=TINYINT},
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        #{createdPerson,jdbcType=VARCHAR},
      </if>
      <if test="transmitType != null">
        #{transmitType,jdbcType=TINYINT},
      </if>
      <if test="companyMainFlag != null">
        #{companyMainFlag,jdbcType=TINYINT},
      </if>
      <if test="createCluesFlag != null">
        #{createCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="draftCluesFlag != null">
        #{draftCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        #{interestsBackgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        #{memberCardTopUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        #{memberCardDetailUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkAddressFlag != null">
        #{checkAddressFlag,jdbcType=TINYINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        `id` = #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null and name != ''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        `company_id` = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null and companyName != ''">
        `company_name` = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backgroundUrl != null and backgroundUrl != ''">
        `background_url` = #{backgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null and channelCode != ''">
        `channel_code` = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        `channel_name` = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="pageType != null and pageType != ''">
        `page_type` = #{pageType,jdbcType=VARCHAR},
      </if>
      <if test="drainagePage != null and drainagePage != ''">
        `drainage_page` = #{drainagePage,jdbcType=VARCHAR},
      </if>
      <if test="pageId != null">
        `page_id` = #{pageId,jdbcType=INTEGER},
      </if>
      <if test="qrType != null and qrType != ''">
        `qr_type` = #{qrType,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null and sourceCode != ''">
        `source_code` = #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDetailCode != null and sourceDetailCode != ''">
        `source_detail_code` = #{sourceDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null and remark != ''">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reviseName != null and reviseName != ''">
        `revise_name` = #{reviseName,jdbcType=VARCHAR},
      </if>
      <if test="reviseTime != null">
        `revise_time` = #{reviseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        `is_deleted` = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null and createdBy != ''">
        `created_by` = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        `created_date` = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null and modifiedBy != ''">
        `modified_by` = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null">
        `modified_date` = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="storeAllFlag != null">
        `store_all_flag` = #{storeAllFlag,jdbcType=TINYINT},
      </if>
      <if test="userAllFlag != null">
        `user_all_flag` = #{userAllFlag,jdbcType=TINYINT},
      </if>
      <if test="createdPerson != null and createdPerson != ''">
        `created_person` = #{createdPerson,jdbcType=VARCHAR},
      </if>
      <if test="transmitType != null">
        `transmit_type` = #{transmitType,jdbcType=TINYINT},
      </if>
      <if test="companyMainFlag != null">
        `company_main_flag` = #{companyMainFlag,jdbcType=TINYINT},
      </if>
      <if test="createCluesFlag != null">
        `create_clues_flag` = #{createCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="draftCluesFlag != null">
        `draft_Clues_Flag` = #{draftCluesFlag,jdbcType=TINYINT},
      </if>
      <if test="activityId != null">
        `activity_id` = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="interestsBackgroundUrl != null and interestsBackgroundUrl != ''">
        `interests_background_url` = #{interestsBackgroundUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardTopUrl != null and memberCardTopUrl != ''">
        `member_card_top_url` = #{memberCardTopUrl,jdbcType=VARCHAR},
      </if>
      <if test="memberCardDetailUrl != null and memberCardDetailUrl != ''">
        `member_card_detail_url` = #{memberCardDetailUrl,jdbcType=VARCHAR},
      </if>
      <if test="checkAddressFlag != null">
        `check_address_flag` = #{checkAddressFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
</mapper>