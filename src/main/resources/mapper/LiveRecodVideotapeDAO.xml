<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.fss.dao.live.LiveRecodVideotapeDAO">
    <resultMap id="BaseResultMap" type="com.fotile.fss.pojo.entity.live.LiveRecodVideotape">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="stream_id" jdbcType="BIGINT" property="streamId"/>
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, stream_id, video_url, task_id, request_id, `state`, is_deleted, created_by, created_date,
        modified_by, modified_date
    </sql>
</mapper>