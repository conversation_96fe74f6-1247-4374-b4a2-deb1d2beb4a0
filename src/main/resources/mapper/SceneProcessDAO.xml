<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.fss.dao.scene.SceneProcessDAO">

    <!-- 查询流程列表 -->
    <select id="selectSceneProcessList" resultType="com.fotile.fss.pojo.dto.scene.SceneProcessDTO">
        SELECT
            a.id,
            a.channel_type,
            a.send_type,
            c.msg_type,
            a.sorted,
            b.job_name,
            b.job_group,
            b.bean_name,
            b.method_name,
            b.cron_time,
            b.job_start_date,
            b.job_end_date
        FROM fss_scene_process a
            LEFT JOIN fss_scene_job b ON a.id = b.scene_process_id
            LEFT JOIN fss_scene_msg c ON a.id = c.scene_process_id
        WHERE
            a.scene_id = #{sceneId}
         ORDER BY a.sorted ASC
    </select>

    <!-- 查询场景任务状态 -->
    <select id="selectSceneStatus" resultType="com.fotile.fss.pojo.dto.scene.SceneStatusDTO">
        SELECT
            b.id,
            b.app_info_id
        FROM
            fss_scene_process a
            LEFT JOIN fss_scene_list b ON a.scene_id = b.id
        WHERE
            a.is_deleted = 0 AND b.is_deleted = 0 AND b.scene_status = 1 AND a.id = #{sceneProcessId}
    </select>
</mapper>