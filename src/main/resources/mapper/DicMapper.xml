<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.systemcenter.dictionary.dao.DicMapper">
    <select id="queryByType" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where type_code = #{typeCode}
          and is_deleted = 0
          and active = 1
          <if test="keyword != null and keyword != ''">
            and value_name like concat('%',#{keyword},'%')
          </if>
        order by sort desc
        limit #{offset},#{pageSize}
    </select>

    <select id="queryByTypes" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where type_code in
        <foreach collection="typeCodes" item="typeCode" open="(" separator="," close=")">
            #{typeCode}
        </foreach>
        and is_deleted = 0
        and active = 1
        order by sort desc
    </select>

    <select id="queryAllByTypes" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where type_code in
        <foreach collection="typeCodes" item="typeCode" open="(" separator="," close=")">
            #{typeCode}
        </foreach>
        and is_deleted = 0
        order by sort desc
    </select>

    <select id="queryValueNameByType" parameterType="java.lang.String" resultType="java.lang.String">
        select value_name
        from systemcenter.dic
        where type_code = #{typeCode}
          and is_deleted = 0
          and active = 1
        order by sort desc
    </select>


    <select id="queryByTypeAndName" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where is_deleted = 0
        <if test="typeCode != null and typeCode != ''">
            and type_code like '%${typeCode}%'
        </if>
        <if test="valueName != null and valueName != ''">
            and value_name like '%${valueName}%'
        </if>
        <if test="active != null">
            and active = #{active}
        </if>
        order by type_code, sort desc
        limit #{offset},#{pageSize}
    </select>

    <update id="changeStatus">
        update systemcenter.dic
        set active=ABS(active - 1),
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        <!--(CASE  WHEN active=1 THEN 0 WHEN active=0 then 1 else 1 END) -->
        where id = #{id,jdbcType=BIGINT}
          and is_deleted = 0
    </update>

    <!--<update id="enableByType" parameterType="String">-->
    <!--update systemcenter.dic set active=1 where type_code=#{typeCode} and is_deleted =0-->
    <!--</update>-->

    <!--<update id="disableByType" parameterType="String">-->
    <!--update dic set active=0 where type_code=#{typeCode} and is_deleted =0-->
    <!--</update>-->

    <insert id="insertDic" parameterType="com.fotile.systemcenter.dictionary.pojo.dto.InsertInDto"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into systemcenter.dic(is_deleted, value_code, type_code, value_name, active, sort, remark,
                                     modified_by, modified_date, created_date, created_by)
        values (0, #{valueCode}, #{typeCode}, #{valueName}, #{active}, #{sort}, #{remark},
                #{modifiedBy}, #{modifiedDate}, #{createdDate}, #{createdBy})
    </insert>

    <update id="updateValueName" parameterType="com.fotile.systemcenter.dictionary.pojo.Dic">
        update systemcenter.dic
        set value_name=#{valueName},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        where type_code = #{typeCode}
          and value_code = #{valueCode}
    </update>

    <select id="queryType" resultType="java.lang.String">
        select distinct type_code
        from systemcenter.dic
        where is_deleted = 0
    </select>

    <select id="queryListByTypeCode" parameterType="java.util.Map" resultType="java.util.Map">
        select value_code code, value_name name
        from systemcenter.dic
        where type_code = #{typeCode}
          and is_deleted = 0
          and active = 1
        order by sort asc
    </select>

    <select id="queryDoubleCode" parameterType="com.fotile.systemcenter.dictionary.pojo.dto.QueryByDoubleCodeDto"
            resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        SELECT *
        from systemcenter.dic
        WHERE is_deleted = 0
          and value_code = #{valueCode}
          and type_code = #{typeCode}
    </select>

    <select id="queryByidList" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
        order by sort desc
    </select>

    <select id="queryByIdList" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0 and active = 1
        order by sort desc
    </select>

    <select id="queryByTypeCount" resultType="java.lang.Integer">
        select count(1)
        from systemcenter.dic
        where type_code = #{typeCode}
          and is_deleted = 0
          and active = 1
        <if test="keyword != null and keyword != ''">
            and value_name like concat('%',#{keyword},'%')
        </if>
    </select>

    <select id="queryByTypeAndNameCount" resultType="java.lang.Integer">
        select COUNT(1)
        from systemcenter.dic where is_deleted = 0
        <if test="typeCode != null and typeCode != ''">
            and type_code like '%${typeCode}%'
        </if>
        <if test="valueName != null and valueName != ''">
            and value_name like '%${valueName}%'
        </if>
        <if test="active != null">
            and active = #{active}
        </if>
    </select>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, is_deleted, created_by, created_date, modified_by, modified_date, value_code,
        type_code, value_name, active, sort, remark
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from systemcenter.dic
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from systemcenter.dic
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.systemcenter.dictionary.pojo.Dic"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into systemcenter.dic (is_deleted, created_by, created_date,
                         modified_by, modified_date, value_code,
                         type_code, value_name, active, sort, remark)
        values (#{isDeleted,jdbcType=BIGINT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
                #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}, #{valueCode,jdbcType=VARCHAR},
                #{typeCode,jdbcType=VARCHAR}, #{valueName,jdbcType=VARCHAR}, #{active,jdbcType=TINYINT},
                #{sort,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.fotile.systemcenter.dictionary.pojo.Dic" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into systemcenter.dic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDate != null">
                created_date,
            </if>
            <if test="modifiedBy != null">
                modified_by,
            </if>
            <if test="modifiedDate != null">
                modified_date,
            </if>
            <if test="valueCode != null">
                value_code,
            </if>
            <if test="typeCode != null">
                type_code,
            </if>
            <if test="valueName != null">
                value_name,
            </if>
            <if test="active != null">
                active,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="description != null">
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="valueCode != null">
                #{valueCode,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null">
                #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="valueName != null">
                #{valueName,jdbcType=VARCHAR},
            </if>
            <if test="active != null">
                #{active,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.fotile.systemcenter.dictionary.pojo.Dic">
        <!--@mbg.generated-->
        update systemcenter.dic
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="modifiedBy != null">
                modified_by = #{modifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="modifiedDate != null">
                modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="valueCode != null">
                value_code = #{valueCode,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null">
                type_code = #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="valueName != null">
                value_name = #{valueName,jdbcType=VARCHAR},
            </if>
            <if test="active != null">
                active = #{active,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fotile.systemcenter.dictionary.pojo.Dic">
        <!--@mbg.generated-->
        update systemcenter.dic
        set is_deleted    = #{isDeleted,jdbcType=BIGINT},
            created_by    = #{createdBy,jdbcType=VARCHAR},
            created_date  = #{createdDate,jdbcType=TIMESTAMP},
            modified_by   = #{modifiedBy,jdbcType=VARCHAR},
            modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
            value_code    = #{valueCode,jdbcType=VARCHAR},
            type_code     = #{typeCode,jdbcType=VARCHAR},
            value_name    = #{valueName,jdbcType=VARCHAR},
            active        = #{active,jdbcType=TINYINT},
            sort          = #{sort,jdbcType=INTEGER},
            remark        = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryByValueCodes" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where value_code IN
        <foreach collection="valueCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and is_deleted = 0
        order by sort ASC
    </select>

    <select id="getDicByUK" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where  value_code=#{valueCode} and type_code = #{typeCode}
    </select>

    <update id="updateDicAreaVersion">
        update systemcenter.dic
        set modified_by   = 'anonymousUser',
            modified_date = now(),
            value_code=#{valueCode}
        where type_code = #{typeCode}
    </update>

    <select id="queryByTypeAndCode" resultType="com.fotile.systemcenter.dictionary.pojo.Dic" parameterType="com.fotile.systemcenter.dictionary.pojo.dto.TypeAndCodeDto">
        select *
        from systemcenter.dic
        where type_code = #{typeCode,jdbcType=VARCHAR}
        <if test="valueCodeList != null and valueCodeList.size() > 0">
            and value_code IN
            <foreach collection="valueCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        and is_deleted = 0
        order by sort ASC
    </select>

    <select id="queryAllByTypeAndId" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        SELECT value_code,value_name
        FROM `dic`
        WHERE type_code=#{typeCode}
        and id=#{id}
        and is_deleted=0
    </select>

    <select id="searchDict"
            parameterType="com.fotile.systemcenter.dictionary.pojo.dto.QueryDictInDto"
            resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        <where>
            <if test="query.id != null">
                and id=#{query.id}
            </if>
            <if test="query.ids != null and query.ids != ''">
                and id in
                <foreach collection="query.ids.split(',')" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.needDeletedRows == null or query.needDeletedRows != 1">
                and is_deleted=0
            </if>
            <if test="query.valueCode != null and query.valueCode != ''">
                and value_code=#{query.valueCode}
            </if>
            <if test="query.valueCodes != null and query.valueCodes != ''">
                and value_code in
                <foreach collection="query.valueCodes.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.typeCode != null and query.typeCode != ''">
                and type_code=#{query.typeCode}
            </if>
            <if test="query.typeCodes != null and query.typeCodes != ''">
                and type_code in
                <foreach collection="query.typeCodes.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.active != null">
                <if test="query.active == 0">
                    and active=0
                </if>
                <if test="query.active == 1">
                    and active=1
                </if>
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                and (
                    type_code like concat('%', #{query.keyword}, '%') or
                    value_name like concat('%',#{query.keyword}, '%')
                )
            </if>
        </where>
    </select>



    <select id="queryByTypeCodesAndName" resultType="com.fotile.systemcenter.dictionary.pojo.Dic">
        select *
        from systemcenter.dic
        where is_deleted = 0
        <if test="typeCode != null and typeCode != ''">
            and type_code = #{typeCode}
        </if>
        <if test="valueName != null and valueName != ''">
            and value_name like '%${valueName}%'
        </if>
        and active = 1
        order by type_code, sort desc
        limit #{offset},#{pageSize}
    </select>

    <select id="queryByTypeCodesAndNameCount" resultType="java.lang.Integer">
        select COUNT(1)
        from systemcenter.dic where is_deleted = 0
        <if test="typeCode != null and typeCode != ''">
            and type_code = #{typeCode}
        </if>
        <if test="valueName != null and valueName != ''">
            and value_name like '%${valueName}%'
        </if>
        and active = 1
    </select>

    <select id="getDicByTargetCompeteCols" resultType="com.fotile.systemcenter.dictionary.pojo.vo.TargetCompeteColsVO">
        select
            dic.value_name as valueName,
            dic.value_code as valueCode,
            0 as isChoose
        from systemcenter.dic dic
        where dic.type_code = 'column_keyword'
            and dic.is_deleted = 0
            and dic.active = 1
            and dic.value_code not in ('drainage_clues_proportion','stock_clues_proportion')
        order by dic.sort desc
    </select>

    <select id="getDicByTargetCompeteColsLimit" resultType="java.lang.String">
        select
            dic.value_code as valueCode
        from systemcenter.dic dic
        where dic.type_code = 'column_keyword'
            and dic.is_deleted = 0
            and dic.active = 1
            and dic.value_code not in ('drainage_clues_proportion','stock_clues_proportion')
        order by dic.sort desc
        limit 3
    </select>

    <select id="queryByTypeAndFlag" resultType="com.fotile.systemcenter.dictionary.pojo.Dic" parameterType="com.fotile.systemcenter.dictionary.pojo.dto.TypeAndCodeDto">
        select *
        from systemcenter.dic
        where is_deleted = 0
        and type_code = #{typeCode}
        <if test="flag != null">
            and flag = #{flag}
        </if>
        <if test="active != null">
            and active = #{active}
        </if>
        order by sort ASC
    </select>
</mapper>