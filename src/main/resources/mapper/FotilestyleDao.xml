<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.productcenter.fotilestyle.dao.FotilestyleDao">
  
  <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductDto" id="productsMap">
      <id column="id" property="id"/>
      <result column="model" property="model"/>
      <result column="title" property="title"/>
      <result column="summary" property="summary"/>
      <result column="userCollect" property="userCollect"/>
      <collection property="files" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto" select="getFiles" column="id">
      </collection>
  </resultMap>

    <select id="countGoodsWishlist" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM user_goods_wishlist u
             INNER JOIN productcenter.goods a
                 ON u.gooods_id = a.id  and a.is_deleted = 0 and a.stage = 1
             INNER JOIN productcenter.goods_general_description b
                 ON a.id = b.goods_id and b.is_deleted = 0
        WHERE u.user_id = #{userId} and u.is_deleted = 0
    </select>

    <select id="findGoodsWishlist" resultMap="productsWithPropertyMap">
        SELECT
            a.id AS id,
            a.name AS title,
            a.model_num AS model,
            b.introduction AS summary,
            0 as userCollect,
            if(c.user_id is null,1,0) as userCompare
        FROM user_goods_wishlist u
        INNER JOIN productcenter.goods a
        ON u.gooods_id = a.id  and a.is_deleted = 0
        and a.stage = 1
        INNER JOIN productcenter.goods_general_description b
        ON a.id = b.goods_id and b.is_deleted = 0
        LEFT JOIN productcenter.compare_goods c on a.id = c.gooods_id
        and c.user_id = #{userId}
        and c.is_deleted = 0
        WHERE u.user_id = #{userId} and u.is_deleted = 0
        order by u.created_date desc
        limit 30
    </select>

    <select id="searchChannelCategoryGoods" resultMap="productsWithPropertyMap">
        SELECT
            a.id AS id,
            a.name AS title,
            a.model_num AS model,
            b.introduction AS summary,
            0 as userCollect,
            if(c.user_id is null,1,0) as userCompare
        FROM productcenter.goods a
        INNER JOIN productcenter.goods_general_description b
        ON a.id = b.goods_id and b.is_deleted = 0
        LEFT JOIN productcenter.compare_goods c on a.id = c.gooods_id
        and c.user_id = #{userId}
        and c.is_deleted = 0
        WHERE a.is_deleted = 0
        and a.stage = 1
        and LEFT(a.code,2) = '10'
        <if test="key != null">
        and ( upper(a.name) like concat('%',upper(#{key}),'%')
            or ( upper(a.model_num) like concat('%',upper(#{key}),'%'))
            )
        </if>
        and a.goods_category_id in
        <foreach collection="categoryIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by a.created_date desc
        limit #{offset},#{size}
    </select>

    <select id="getNewProductsByIds" resultMap="productsMap">
        SELECT
        a.id AS id,
        a.name AS title,
        a.model_num AS model,
        b.introduction AS summary,
        0 as userCollect
        FROM productcenter.goods a
        INNER JOIN productcenter.goods_general_description b
        ON a.id = b.goods_id and b.is_deleted = 0
        WHERE a.is_deleted = 0
        and a.stage = 1
        and a.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by a.sort desc,a.id desc
    </select>
  
  <select id="getProductsByIds" resultMap="productsMap">
    SELECT
    a.id AS id,
    a.name AS title,
    a.model_num AS model,
    b.introduction AS summary,
    if(c.user_id is null,1,0) as userCollect
    FROM productcenter.goods a
    INNER JOIN productcenter.goods_general_description b 
    ON a.id = b.goods_id and b.is_deleted = 0
    LEFT JOIN productcenter.collect_goods c on a.id = c.gooods_id
    and c.user_id = #{userId}
    and c.is_deleted = 0
    WHERE
    a.is_deleted = 0
    and a.stage = 1
    and a.id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
    #{id}
    </foreach>
    order by a.sort desc,a.id desc
  </select>

    <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductDto" id="productsWithPropertyMap">
        <id column="id" property="id"/>
        <result column="goodsCode" property="goodsCode"/>
        <result column="model" property="model"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="userCollect" property="userCollect"/>
        <result column="userCompare" property="userCompare"/>
        <result column="categoryId" property="categoryId"/>
        <result column="categoryName" property="categoryName"/>
        <result column="newProducts" property="newProducts"/>
        <result column="newProductsData" property="newProductsData"/>
        <collection property="files" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto" select="getFiles" column="id">
        </collection>
        <collection property="techProperty" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductPropertyDto" select="getTechProperty" column="id">
        </collection>
        <collection property="labels" ofType="java.lang.String" select="getLabels" column="id">
        </collection>
    </resultMap>

    <select id="getProductsOrderByIds" resultMap="productsWithPropertyMap">
        SELECT
            a.id AS id,
            a.name AS title,
            a.model_num AS model,
            b.introduction AS summary,
            0 as userCollect,
            a.goods_category_id as categoryId,
            gc.name as categoryName,
            a.new_products as newProducts,
            a.new_products_date as newProductsData
        FROM productcenter.goods a
        INNER JOIN productcenter.goods_general_description b
        ON a.id = b.goods_id and b.is_deleted = 0
        LEFT JOIN goods_category gc ON a.goods_category_id = gc.id
        WHERE
        a.is_deleted = 0
        and a.stage = 1
        and a.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by
        <foreach collection="ids" item="id" open="field(a.id," close=")" separator=",">
           #{id}
        </foreach>
    </select>

    <select id="getChannelGoodsInfoOrderByIds" resultMap="productDtoMap">
        select
            b.id,
            a.id AS sourceId,
            b.name AS title,
            b.model_num AS model,
            gc.name as categoryName
        from channel_goods_mapping a
        inner join goods b ON a.goods_id = b.id and b.is_deleted = 0 and b.stage = 1
        LEFT JOIN goods_category gc ON b.goods_category_id = gc.id
        where a.is_deleted = 0
        and a.id in
        <foreach collection="sourceIds" item="id"
                 separator="," open="(" close=")">
            #{id}
        </foreach>
        order by
        <foreach collection="sourceIds" item="id" open="field(a.id," close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getProductsWithTechPropertyByIds" resultMap="productsWithPropertyMap">
      SELECT
      a.id AS id,
      a.name AS title,
      a.model_num AS model,
      b.introduction AS summary,
      if(c.user_id is null,1,0) as userCompare
      FROM productcenter.goods a
      INNER JOIN productcenter.goods_general_description b
      ON a.id = b.goods_id and b.is_deleted = 0
      LEFT JOIN productcenter.compare_goods c on a.id = c.gooods_id
      and c.user_id = #{userId}
      and c.is_deleted = 0
      WHERE
      a.is_deleted = 0
      and a.stage = 1
      and a.id in
      <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
      </foreach>
      order by
        <foreach collection="ids" item="id" open="field(a.id," close=")" separator=",">
            #{id}
        </foreach>
    </select>
  
  <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductInfoDto" id="productInfoMap">
      <id column="id" property="id"/>
      <result column="model" property="model"/>
      <result column="title" property="title"/>
      <result column="summary" property="summary"/>
      <result column="userCollect" property="userCollect"/>
      <result column="categoryId" property="categoryId"/>
      <result column="questions" property="questions"/>
      <result column="remark" property="remark"/>
      <result column="poster" property="poster"/>
      <result column="chnGoods" property="chnGoods"/>
      <result column="nestGoodsIds" property="nestGoodsIds"/>
      <collection property="techProperty" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductPropertyDto" select="getTechProperty" column="id">
      </collection>
      <collection property="files" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto" select="getFiles" column="id">
      </collection>
      <collection property="labels" ofType="java.lang.String" select="getLabels" column="id">
      </collection>
  </resultMap>
  <select id="getProductsInfoById" resultMap="productInfoMap">
    SELECT
    a.id AS id,
    IFNULL(b.show_model,a.name) AS title,
    a.note AS remark,
    a.model_num AS model,
    b.introduction AS summary,
    if(c.user_id is null,1,0) as userCollect,
    a.goods_category_id as categoryId,
    b.questions  AS questions,
    null as poster,
    (SELECT COUNT(1) FROM productcenter.t_hyy_product_goods_mapping m, productcenter.t_hyy_product n
     WHERE m.hyy_product_id = n.id AND m.is_deleted = 0 AND n.is_deleted = 0 AND m.good_id = a.id) AS chnGoods,
    b.nest_goods as nestGoodsIds
    FROM productcenter.goods a
    INNER JOIN productcenter.goods_general_description b
    ON a.id = b.goods_id and b.is_deleted = 0
    LEFT JOIN productcenter.user_goods_wishlist c on a.id = c.gooods_id
    and c.user_id = #{userId}
    and c.is_deleted = 0
    WHERE
    a.is_deleted = 0
    and a.stage = 1
    and a.id = #{id}
  </select>

    <select id="getChannelProductOutById" resultType="com.fotile.productcenter.fotilestyle.pojo.ChannelProductOut">
     SELECT b.id as id,
            a.goods_model as title,
            a.lable as label,
            a.jump_url as tbUrl
   from channel_goods_description a
  INNER JOIN channel_goods_mapping b
	ON a.channel_goods_id = b.id
	AND b.is_deleted = 0 AND b.online = 1
	AND b.goods_id = #{id} AND b.channel_id = 21
    WHERE a.is_deleted = 0
  </select>

    <select id="getChannelProductFiles" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto">
        SELECT
              a.type,
              a.file_type as fileType,
              a.path
        from productcenter.goods_file a
        where a.is_deleted = 0 and a.source_table = 'channel_goods_description'
        and  a.source_id = #{id} and a.file_type = 1 and a.type in (9,12,13)
        ORDER BY a.sort desc,a.id asc
    </select>
  
  <select id="getFiles" parameterType="java.lang.Long" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto">
    SELECT
      a.type,
      a.file_type as fileType,
      a.path 
    from productcenter.goods_file a
    WHERE a.is_deleted = 0 and a.goods_id = #{id} and a.source_id = #{id} AND a.source_table = 'goods'
    and a.file_type = 1 and a.type in (1,5,6,13,16)
    ORDER BY a.sort,a.id asc
  </select>

    <select id="getVideoFiles" parameterType="java.lang.Long" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductFileDto">
        SELECT
            a.type,
            a.file_type as fileType,
            a.path
        from productcenter.goods_file a
        WHERE a.is_deleted = 0 and a.goods_id = #{id} and a.source_id = #{id} AND a.source_table = 'goods'
          and a.file_type = 2 and a.type = 16
        ORDER BY a.sort,a.id asc
    </select>
  
  <select id="getTechProperty" parameterType="java.lang.Long" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductPropertyDto">
      SELECT
          a.property_name AS NAME,
          b.category_propety_value AS VALUE
      FROM
          productcenter.goods_category_propety_mapping b,
          productcenter.goods_category_property a
      WHERE
          a.property_name = b.category_propety_name
        AND b.is_deleted = 0
        AND a.is_deleted = 0
        AND b.goods_id = #{id}
        AND a.property_category = 1
        AND b.category_propety_value IS NOT NULL
        AND b.category_propety_value != ''
        AND a.category_id = 1
      ORDER BY a.sort DESC,a.id asc
  </select>
  
  <select id="getLabels" parameterType="java.lang.Long" resultType="java.lang.String">
   SELECT
    a.name
   FROM
    productcenter.label_product a,
    productcenter.label_product_mapping b
   WHERE
   a.id = b.label_id
   AND b.is_deleted = 0
   AND a.is_deleted = 0
   AND b.source_id = #{id}
   AND a.source_table_name='goods'
  </select>
  
  <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductDetailDto" id="productDetailMap">
      <result column="title" property="title"/>
      <result column="summary" property="summary"/>
      <collection property="pictures" ofType="java.lang.String">
       <result property="path" column="path"/>
      </collection>
  </resultMap>
  <select id="getDescriptionById" parameterType="java.lang.Long" resultMap="productDetailMap">
   SELECT
    a.title as title,
    a.descp as summary,
    b.path  as path
   FROM productcenter.goods_description a
   LEFT JOIN productcenter.goods_file b
   ON a.id = b.source_id and b.source_table = 'goods_description' and b.is_deleted = 0 and b.file_type = 1
   WHERE a.goods_id = #{id}
   AND a.is_deleted = 0
  </select>
  
  <select id="getAllProducts" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductSearchDto">
    SELECT DISTINCT
    a.id AS oid,
    a.name AS title,
    b.introduction AS summary,
    (SELECT f.path 
    FROM productcenter.goods_file f
    WHERE f.is_deleted = 0 AND f.goods_id = a.id AND f.file_type = 1 AND f.type = 1
    ORDER BY f.sort LIMIT 1) AS picture
    FROM
    productcenter.channel_goods_mapping cgm
    INNER JOIN productcenter.goods a
    ON cgm.goods_id = a.id
    LEFT JOIN productcenter.goods_general_description b
    ON a.id = b.goods_id AND b.is_deleted = 0
    WHERE
    cgm.channel_id IN (21,32)
    AND cgm.online = 1
    AND cgm.is_deleted = 0
    AND a.is_deleted = 0
    AND a.stage = 1
    AND a.goods_category_id IN
    <foreach collection="ids" item="id" separator="," open="(" close=")">
    #{id}
    </foreach>
  </select>
  
  <select id="getFiksProducts" resultType="java.lang.String">
    SELECT b.product_code FROM
    productcenter.t_hyy_product_goods_mapping a, productcenter.t_hyy_product b
    WHERE
    a.hyy_product_id = b.id
    AND a.is_deleted = 0 AND b.is_deleted = 0
    AND a.good_id IN
    <foreach collection="ids" item="id" separator="," open="(" close=")">
    #{id}
    </foreach>
  </select>

    <resultMap type="com.fotile.productcenter.fotilestyle.pojo.MallProductDetailDto" id="mallProductMap">
        <id column="id" property="id"/>
        <result column="channelGoodsId" property="channelGoodsId"/>
        <result column="goodsId" property="goodsId"/>
        <result column="goodsCode" property="goodsCode"/>
        <result column="goodsModel" property="goodsModel"/>
        <result column="goodsName" property="goodsName"/>
        <result column="sales" property="sales"/>
        <result column="newProducts" property="newProducts"/>
        <result column="newProductsData" property="newProductsData"/>
        <result column="summary" property="summary"/>
        <collection property="labels" ofType="java.lang.String" select="getLabels" column="id">
        </collection>
    </resultMap>

    <select id="getMallProductDetail" resultMap="mallProductMap">
        SELECT
            g.id as id,
            cgm.id as channelGoodsId,
            g.id as goodsId,
            g.code as goodsCode,
            IFNULL(cgd.goods_model,g.model_num) as goodsModel,
            g.name as goodsName,
            cgm.sell as sales,
            g.new_products as newProducts,
            g.new_products_date as newProductsData,
            b.introduction AS summary
        FROM productcenter.channel_goods_mapping cgm
             INNER JOIN productcenter.goods g ON g.id = cgm.goods_id
             LEFT JOIN productcenter.channel_goods_description cgd ON cgm.id = cgd.channel_goods_id and cgd.is_deleted = 0
             LEFT JOIN productcenter.goods_general_description b ON g.id = b.goods_id and b.is_deleted = 0
             LEFT JOIN productcenter.goods_category gc ON gc.id = g.goods_category_id and gc.is_deleted = 0
        WHERE
            cgm.is_deleted = 0 AND cgm.channel_id = 21 and g.series_main = 1 AND cgm.online = 1
          AND g.code = #{goodsCode}
          AND cgm.id = #{channelGoodsId}
    </select>

    <select id="getProductSeries" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductSeriesDto">
        SELECT
            gs.id,
            cgm.id as channelGoodsId,
            gs.goods_code,
            gs.series_goods_code,
            gs.series_name,
            gs.series_model,
            gs.series_main,
            gs.series_specification,
            g.goods_category_id as goodsCategoryId,
            gc.`name` as goodsCategoryName,
            cgmm.split_order_group_type as splitOrderGroupType
        FROM goods_series gs
        INNER JOIN goods g ON g.code = gs.series_goods_code and g.is_deleted = 0
        <if test="pureWater == 1">
            and g.series_main = 0
        </if>
        INNER JOIN channel_goods_mapping cgm ON cgm.goods_id = g.id and cgm.is_deleted = 0 and cgm.channel_id = 21
        LEFT JOIN channel_goods_merchandising cgmm ON cgmm.channel_goods_id = cgm.id and cgmm.is_deleted = 0
        LEFT JOIN productcenter.goods_category gc ON gc.id = g.goods_category_id and gc.is_deleted = 0
        WHERE gs.goods_code IN (
        SELECT series_goods_code
        FROM goods_series
        WHERE is_deleted = 0 AND goods_code = #{goodsCode} )
        AND gs.is_deleted = 0  and cgm.`online` = 1
        order by gs.series_main DESC
    </select>

    <select id="getFilterProductsGoodsIds" resultType="java.lang.Long">
        select goods_id from goods_category_propety_mapping gcpm
        left join goods_category_property gcp ON gcp.property_name = gcpm.category_propety_name and
        gcp.is_deleted = 0
        where gcpm.is_deleted = 0
        and gcp.property_category = 2
        and gcpm.category_propety_name = #{property.categoryPropetyName}
        and gcpm.category_propety_value in
        <foreach collection="property.categoryPropetyValue.split(',')" item="propertyValue"
                 separator="," open="(" close=")">
            #{propertyValue}
        </foreach>
    </select>

    <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductDto" id="productsWithAttributeMap">
        <id column="id" property="id"/>
        <result column="model" property="model"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="categoryId" property="categoryId"/>
        <collection property="productAttributeList" ofType="com.fotile.productcenter.fotilestyle.pojo.ProductAttributeDto" select="getProductAttributeMap" column="id">
        </collection>
    </resultMap>

    <select id="getFilterProductsInfo" resultMap="productsWithAttributeMap">
        SELECT
            a.id AS id,
            IFNULL(b.show_model,a.name) AS title,
            a.model_num AS model,
            b.introduction AS summary,
            a.goods_category_id as categoryId
        FROM productcenter.goods a
        INNER JOIN productcenter.goods_general_description b ON a.id = b.goods_id and b.is_deleted = 0
        WHERE
        a.is_deleted = 0
        and a.stage = 1
        AND a.id in
        <foreach collection="queryFilterDto.goodsList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="queryFilterDto.hotProducts == 1">
            and a.hot_products = 1
            and a.hot_products_date > now()
        </if>
        <if test="queryFilterDto.newProducts == 1">
            and a.new_products = 1
            and a.new_products_date > now()
        </if>
        order by
        <foreach collection="queryFilterDto.goodsList" item="id" open="field(a.id," close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getProductAttributeMap" parameterType="java.lang.Long" resultType="com.fotile.productcenter.fotilestyle.pojo.ProductAttributeDto">
        SELECT
            t.path,
            t.propertyName,
            t.propertyValue,
            t.labelName
        from (
                 SELECT
                     a.path as path,
                     '' as propertyName,
                     '' as propertyValue,
                     '' as sort,
                     '' as labelName
                 from productcenter.goods_file a
                 WHERE a.is_deleted = 0 and a.goods_id = #{id}
                   and a.source_id = #{id} AND a.source_table = 'goods'
                   and a.file_type = 1 and a.type = 6
                 UNION ALL
                 SELECT
                     '' as path,
                     a.property_name AS propertyName,
                     b.category_propety_value AS propertyValue,
                     a.sort as sort,
                     '' as labelName
                 FROM
                     productcenter.goods_category_propety_mapping b,
                     productcenter.goods_category_property a
                 WHERE
                     a.property_name = b.category_propety_name
                   AND b.is_deleted = 0
                   AND a.is_deleted = 0
                   AND b.goods_id = #{id}
                   AND a.property_category = 1
                   AND b.category_propety_value IS NOT NULL
                   AND b.category_propety_value != ''
						AND a.category_id = 1
                 UNION ALL
                 SELECT
                     '' as path,
                     '' as propertyName,
                     '' as propertyValue,
                     '' as sort,
                     a.name as labelName
                 FROM
                     productcenter.label_product a,
                     productcenter.label_product_mapping b
                 WHERE
                     a.id = b.label_id
                   AND b.is_deleted = 0
                   AND a.is_deleted = 0
                   AND b.source_id = #{id}
                   AND a.source_table_name='goods'
             ) t ORDER BY t.sort desc
    </select>

    <resultMap type="com.fotile.productcenter.fotilestyle.pojo.ProductDto" id="productDtoMap">
        <id column="id" property="id"/>
        <result column="sourceId" property="sourceId"/>
        <result column="title" property="title"/>
        <result column="model" property="model"/>
        <result column="categoryName" property="categoryName"/>
        <collection property="pictures" ofType="java.lang.String" select="getPictures" column="sourceId">
        </collection>
    </resultMap>

    <select id="getPictures" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT
            c.path picture
        FROM
             channel_goods_mapping a
        inner join goods b ON a.goods_id = b.id and b.is_deleted = 0 and b.stage = 1
        inner join productcenter.goods_file c ON c.goods_id = b.id and c.is_deleted = 0 and c.source_table = 'goods' and c.type = 6
        WHERE
            a.is_deleted = 0
          AND a.id = #{id}
    </select>


    <select id="getChannelGoodsInfoByIds" resultMap="productDtoMap">
        select
               b.id,
               a.id AS sourceId,
               b.name AS title,
               b.model_num AS model
        from channel_goods_mapping a
        inner join goods b ON a.goods_id = b.id and b.is_deleted = 0 and b.stage = 1
        where a.is_deleted = 0
        and a.id in
        <foreach collection="sourceIds" item="id"
                 separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getGoodsInfoByCode" resultMap="productDtoMap">
        select
            id
        from goods
        where is_deleted = 0
          and stage = 1
          and code = #{goodsCode}
    </select>

    <insert id="insertCollectGoodsAsset" parameterType="com.fotile.productcenter.fotilestyle.pojo.AssetsDto">
        insert into collect_goods_asset (user_id,goods_code,order_no,order_create_time,is_deleted,created_by,created_date)
        VALUES (#{userId}, #{goodsCode},#{orderNo},#{orderCreateTime},0,#{userId},now())
    </insert>

    <update id="updateCollectGoodsAsset">
        update productcenter.collect_goods_asset
        set modified_by= #{userId},
        modified_date=now(),
        is_deleted=1
        where id = #{id}
        and user_id = #{userId}
    </update>

    <select id="findCollectGoodsAssetId" parameterType="com.fotile.productcenter.fotilestyle.pojo.AssetsDto" resultType="java.lang.Long">
        SELECT id FROM
            productcenter.collect_goods_asset
        WHERE
              is_deleted = 0
          and user_id = #{userId}
          AND goods_code = #{goodsCode}
        <if test="orderNo != null" >
            AND order_no = #{orderNo}
        </if>
    </select>

    <select id="findCollectGoodsAssetList" resultType="com.fotile.productcenter.fotilestyle.pojo.AssetsDto">
        SELECT a.id,
               a.goods_code,
               a.order_no,
               a.order_create_time,
               b.name AS goodsName
        FROM productcenter.collect_goods_asset a
        INNER JOIN  productcenter.goods b
        ON b.`code` = a.goods_code AND b.is_deleted = 0
        WHERE a.is_deleted = 0
        and a.user_id = #{userId}
        <if test="size == 3" >
            limit 3
        </if>
        order by a.created_date desc
    </select>

    <select id="searchGoodsList" resultType="com.fotile.productcenter.fotilestyle.pojo.AssetsDto">
        SELECT
            a.id AS goodsId,
            a.name AS goodsName,
            a.code AS goodsCode
        FROM productcenter.goods a
        INNER JOIN productcenter.goods_general_description b
        ON a.id = b.goods_id and b.is_deleted = 0
        WHERE a.is_deleted = 0
        and a.stage = 1
        and LEFT(a.code,2) = '10'
        <if test="key != null">
            and ( upper(a.name) like concat('%',upper(#{key}),'%')
            or ( upper(a.model_num) like concat('%',upper(#{key}),'%'))
            )
        </if>
        order by a.created_date desc
        limit #{offset},#{size}
    </select>

    <select id="getProductHeardFilesByCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
            a.path
        from productcenter.goods_file a
            INNER JOIN productcenter.goods b ON b.id = a.goods_id
            AND b.is_deleted = 0 AND b.code = #{goodsCode}
        WHERE a.is_deleted = 0 AND a.source_table = 'goods'
          and a.file_type = 1 and a.type = 1
        ORDER BY a.sort,a.id asc;
    </select>

</mapper>