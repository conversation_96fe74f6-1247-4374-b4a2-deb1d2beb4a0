<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fotile.customercenter.awe.mapper.AweRecordMapper" >
  <resultMap id="BaseResultMap" type="com.fotile.customercenter.awe.pojo.AweRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="customer_id" property="customerId" jdbcType="BIGINT" />
    <result column="get_prize_flag" property="getPrizeFlag" jdbcType="TINYINT" />
    <result column="get_prize_secret" property="getPrizeSecret" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, customer_id, get_prize_flag, get_prize_secret, is_deleted, created_by, created_date, 
    modified_by, modified_date
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from awe_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from awe_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.fotile.customercenter.awe.pojo.AweRecord" >
    insert into awe_record (customer_id, get_prize_flag, 
      created_by, 
      created_date, modified_by, modified_date
      )
    values (#{customerId,jdbcType=BIGINT}, 0, 
      #{createdBy,jdbcType=VARCHAR}, 
      now(), #{modifiedBy,jdbcType=VARCHAR}, now()
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fotile.customercenter.awe.pojo.AweRecord" >
    insert into awe_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="customerId != null" >
        customer_id,
      </if>
      <if test="getPrizeFlag != null" >
        get_prize_flag,
      </if>
      <if test="getPrizeSecret != null" >
        get_prize_secret,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="customerId != null" >
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="getPrizeFlag != null" >
        #{getPrizeFlag,jdbcType=TINYINT},
      </if>
      <if test="getPrizeSecret != null" >
        #{getPrizeSecret,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.customercenter.awe.pojo.AweRecord" >
    update awe_record
    <set >
      <if test="customerId != null" >
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="getPrizeFlag != null" >
        get_prize_flag = #{getPrizeFlag,jdbcType=TINYINT},
      </if>
      <if test="getPrizeSecret != null" >
        get_prize_secret = #{getPrizeSecret,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.customercenter.awe.pojo.AweRecord" >
    update awe_record
    set customer_id = #{customerId,jdbcType=BIGINT},
      get_prize_flag = #{getPrizeFlag,jdbcType=TINYINT},
      get_prize_secret = #{getPrizeSecret,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getAweRecordByCustomerId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
      id, customer_id, get_prize_flag
    from awe_record
    where is_deleted = 0 
    and customer_id = #{customerId}
  </select>
</mapper>
