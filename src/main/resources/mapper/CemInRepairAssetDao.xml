<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.dep.provider.cem.dao.CemInRepairAssetDao">
    <sql id="Base_Column_List">
        batch_no,
        distribute_no,
        asset_id,
        SRId,
        FTXZpp,
        FTAssetType,
        FTAssetModel,
        FTPurchaseChannel,
        AssetNumber,
        PersonalAddressId,
        created_by,
        modified_by,
        created_date,
        modified_date,
        id,
        is_deleted
    </sql>

    <resultMap id="BaseResultMap"
               type="com.fotile.dep.provider.cem.pojo.entity.CemInRepairAsset">
        <result column="batch_no" property="batchNo"/>
        <result column="distribute_no" property="distributeNo"/>
        <result column="asset_id" property="asset_id"/>
        <result column="SRId" property="SRId"/>
        <result column="FTXZpp" property="FTXZpp"/>
        <result column="FTAssetType" property="FTAssetType"/>
        <result column="FTAssetModel" property="FTAssetModel"/>
        <result column="FTPurchaseChannel" property="FTPurchaseChannel"/>
        <result column="AssetNumber" property="AssetNumber"/>
        <result column="PersonalAddressId" property="PersonalAddressId"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="findByBatchNoAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from depcenter.cem_in_repair_asset
        where batch_no = #{batchNo}
          and is_deleted = #{isDeleted}
    </select>
</mapper>