<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.customercenter.identity.dao.TIdentityInfoDao">

    <resultMap type="com.fotile.customercenter.identity.pojo.entity.TIdentityInfo" id="TIdentityInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="modifiedBy" column="modified_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="modifiedDate" column="modified_date" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="describe" column="describe" jdbcType="VARCHAR"/>
        <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.fotile.customercenter.identity.pojo.entity.TIdentityInfo" id="pageMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="describe" column="describe" jdbcType="VARCHAR"/>
        <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `is_deleted`,
        `created_by`,
        `modified_by`,
        `created_date`,
        `modified_date`,
        `type`,
        `title`,
        `describe`,
        `pic_url`,
        `status`
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TIdentityInfoMap">
        select
         <include refid="Base_Column_List"/>
        from t_identity_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="pageMap">
        select
         <include refid="Base_Column_List"/>
        from t_identity_info
        <where>
            is_deleted = 0
            <if test="keyword != null and keyword != ''">
                and (type like CONCAT('%',#{keyword},'%') or and title like CONCAT('%',#{keyword},'%') )
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by created_date desc
        limit #{offset}, #{size}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from t_identity_info
        <where>
            is_deleted = 0
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="describe != null and describe != ''">
                and `describe` = #{describe}
            </if>
            <if test="picUrl != null and picUrl != ''">
                and pic_url = #{picUrl}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into t_identity_info(is_deleted, created_by, modified_by, created_date, modified_date, type, title, `describe`, pic_url, `status`)
        values (0, #{createdBy}, #{modifiedBy}, now(), now(), #{type}, #{title}, #{describe}, #{picUrl}, 1)
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_identity_info(is_deleted, created_by, modified_by, created_date, modified_date, type, title, `describe`, pic_url, status)
        values
        <foreach collection="entities" item="entity" separator=",">
        (0, #{entity.createdBy}, #{entity.modifiedBy}, #{entity.createdDate}, #{entity.modifiedDate}, #{entity.type}, #{entity.title}, #{entity.describe}, #{entity.picUrl}, #{entity.status})
        </foreach>
    </insert>
    
    <!--通过主键修改数据-->
    <update id="update">
        update t_identity_info
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
                modified_date = now(),
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            `describe` = #{describe},
            <if test="picUrl != null and picUrl != ''">
                pic_url = #{picUrl},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键修改数据-->
    <update id="updateStatusById">
        update t_identity_info
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            modified_date = now(),
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update t_identity_info
        <set>
            <if test="modifiedBy != null and modifiedBy != ''">
                modified_by = #{modifiedBy},
            </if>
            modified_date = now(),
            is_deleted = 9
        </set>
        where id = #{id}
    </update>


    <select id="queryAllByIds" resultMap="TIdentityInfoMap">
        select
        <include refid="Base_Column_List"/>
        from t_identity_info
        where is_deleted = 0
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="queryByTitles" resultMap="TIdentityInfoMap">
        select
        <include refid="Base_Column_List"/>
        from t_identity_info
        where is_deleted = 0
        and type in
        <foreach collection="titleList" item="title" separator="," open="(" close=")">
            #{title}
        </foreach>
    </select>


</mapper>

