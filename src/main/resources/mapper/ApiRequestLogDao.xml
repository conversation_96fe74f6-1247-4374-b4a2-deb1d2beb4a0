<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.logcenter.dao.ApiRequestLogDao">

    <insert id="saveRequestLog" parameterType="com.fotile.logcenter.pojo.entity.ApiRequestLog">
        insert into t_api_request_log
        (
         source_id, source_table_name, center_name, req_url,
         req_type, req_param, res_result, success_flag,
         is_deleted, created_by, created_date, modified_by, modified_date
        )
        values
        (
         #{sourceId}, #{sourceTableName}, #{centerName}, #{reqUrl},
         #{reqType}, #{reqParam}, #{resResult}, #{successFlag},
         0,#{createdBy},now(),#{modifiedBy},now()
        )
    </insert>

</mapper>