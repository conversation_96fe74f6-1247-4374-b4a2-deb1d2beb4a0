<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.toolscenter.configurationcategory.dao.DtToolConfigurationCategoryDao">
  <resultMap id="BaseResultMap" type="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategory">
    <id column="dt_tool_configuration_category_id" jdbcType="VARCHAR" property="id" />
    <result column="dt_tool_configuration_category_name" jdbcType="VARCHAR" property="name" />
    <result column="dt_tool_configuration_category_images" jdbcType="VARCHAR" property="images" />
    <result column="dt_tool_configuration_category_sortNo" jdbcType="VARCHAR" property="sortNo" />
    <result column="dt_tool_configuration_category_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="dt_tool_configuration_category_creator" jdbcType="VARCHAR" property="creator" />
    <result column="dt_tool_configuration_category_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="dt_tool_configuration_category_modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="dt_tool_configuration_category_is_del" jdbcType="VARCHAR" property="is_del" />
    <result column="dt_tool_configuration_category_reserve_item1" jdbcType="VARCHAR" property="reserve_item1" />
    <result column="dt_tool_configuration_category_reserve_item2" jdbcType="VARCHAR" property="reserve_item2" />
    <result column="dt_tool_configuration_category_reserve_item3" jdbcType="VARCHAR" property="reserve_item3" />
    <result column="dt_tool_configuration_category_reserve_item4" jdbcType="VARCHAR" property="reserve_item4" />
    <result column="dt_tool_configuration_category_reserve_item5" jdbcType="VARCHAR" property="reserve_item5" />
    <result column="dt_tool_configuration_category_reserve_item6" jdbcType="VARCHAR" property="reserve_item6" />
    <result column="dt_tool_configuration_category_reserve_item7" jdbcType="VARCHAR" property="reserve_item7" />
    <result column="dt_tool_configuration_category_reserve_item8" jdbcType="VARCHAR" property="reserve_item8" />
    <result column="dt_tool_configuration_category_reserve_item9" jdbcType="VARCHAR" property="reserve_item9" />
    <result column="dt_tool_configuration_category_reserve_item10" jdbcType="VARCHAR" property="reserve_item10" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    dt_tool_configuration_category.id as dt_tool_configuration_category_id, dt_tool_configuration_category.`name` as `dt_tool_configuration_category_name`, 
    dt_tool_configuration_category.images as dt_tool_configuration_category_images, dt_tool_configuration_category.sortNo as dt_tool_configuration_category_sortNo, 
    dt_tool_configuration_category.ctime as dt_tool_configuration_category_ctime, dt_tool_configuration_category.creator as dt_tool_configuration_category_creator, 
    dt_tool_configuration_category.mtime as dt_tool_configuration_category_mtime, dt_tool_configuration_category.modifier as dt_tool_configuration_category_modifier, 
    dt_tool_configuration_category.is_del as dt_tool_configuration_category_is_del, dt_tool_configuration_category.reserve_item1 as dt_tool_configuration_category_reserve_item1, 
    dt_tool_configuration_category.reserve_item2 as dt_tool_configuration_category_reserve_item2, 
    dt_tool_configuration_category.reserve_item3 as dt_tool_configuration_category_reserve_item3, 
    dt_tool_configuration_category.reserve_item4 as dt_tool_configuration_category_reserve_item4, 
    dt_tool_configuration_category.reserve_item5 as dt_tool_configuration_category_reserve_item5, 
    dt_tool_configuration_category.reserve_item6 as dt_tool_configuration_category_reserve_item6, 
    dt_tool_configuration_category.reserve_item7 as dt_tool_configuration_category_reserve_item7, 
    dt_tool_configuration_category.reserve_item8 as dt_tool_configuration_category_reserve_item8, 
    dt_tool_configuration_category.reserve_item9 as dt_tool_configuration_category_reserve_item9, 
    dt_tool_configuration_category.reserve_item10 as dt_tool_configuration_category_reserve_item10
  </sql>
  <select id="selectByExample" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dt_tool_configuration_category dt_tool_configuration_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
    <if test="forUpdate != null and forUpdate == true">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dt_tool_configuration_category dt_tool_configuration_category
    where dt_tool_configuration_category.id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from dt_tool_configuration_category
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategoryExample">
    delete from dt_tool_configuration_category dt_tool_configuration_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategory" useGeneratedKeys="true">
    insert into dt_tool_configuration_category (`name`, images, sortNo, 
      ctime, creator, mtime, 
      modifier, is_del, reserve_item1, 
      reserve_item2, reserve_item3, reserve_item4, 
      reserve_item5, reserve_item6, reserve_item7, 
      reserve_item8, reserve_item9, reserve_item10
      )
    values (#{name,jdbcType=VARCHAR}, #{images,jdbcType=VARCHAR}, #{sortNo,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{mtime,jdbcType=TIMESTAMP}, 
      #{modifier,jdbcType=VARCHAR}, #{is_del,jdbcType=VARCHAR}, #{reserve_item1,jdbcType=VARCHAR}, 
      #{reserve_item2,jdbcType=VARCHAR}, #{reserve_item3,jdbcType=VARCHAR}, #{reserve_item4,jdbcType=VARCHAR}, 
      #{reserve_item5,jdbcType=VARCHAR}, #{reserve_item6,jdbcType=VARCHAR}, #{reserve_item7,jdbcType=VARCHAR}, 
      #{reserve_item8,jdbcType=VARCHAR}, #{reserve_item9,jdbcType=VARCHAR}, #{reserve_item10,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategory" useGeneratedKeys="true">
    insert into dt_tool_configuration_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="images != null">
        images,
      </if>
      <if test="sortNo != null">
        sortNo,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="is_del != null">
        is_del,
      </if>
      <if test="reserve_item1 != null">
        reserve_item1,
      </if>
      <if test="reserve_item2 != null">
        reserve_item2,
      </if>
      <if test="reserve_item3 != null">
        reserve_item3,
      </if>
      <if test="reserve_item4 != null">
        reserve_item4,
      </if>
      <if test="reserve_item5 != null">
        reserve_item5,
      </if>
      <if test="reserve_item6 != null">
        reserve_item6,
      </if>
      <if test="reserve_item7 != null">
        reserve_item7,
      </if>
      <if test="reserve_item8 != null">
        reserve_item8,
      </if>
      <if test="reserve_item9 != null">
        reserve_item9,
      </if>
      <if test="reserve_item10 != null">
        reserve_item10,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="images != null">
        #{images,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        #{sortNo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="is_del != null">
        #{is_del,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item1 != null">
        #{reserve_item1,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item2 != null">
        #{reserve_item2,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item3 != null">
        #{reserve_item3,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item4 != null">
        #{reserve_item4,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item5 != null">
        #{reserve_item5,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item6 != null">
        #{reserve_item6,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item7 != null">
        #{reserve_item7,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item8 != null">
        #{reserve_item8,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item9 != null">
        #{reserve_item9,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item10 != null">
        #{reserve_item10,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategoryExample" resultType="java.lang.Long">
    select count(*) from dt_tool_configuration_category dt_tool_configuration_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dt_tool_configuration_category dt_tool_configuration_category
    <set>
      <if test="record.id != null">
        dt_tool_configuration_category.id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        dt_tool_configuration_category.`name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.images != null">
        dt_tool_configuration_category.images = #{record.images,jdbcType=VARCHAR},
      </if>
      <if test="record.sortNo != null">
        dt_tool_configuration_category.sortNo = #{record.sortNo,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        dt_tool_configuration_category.ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        dt_tool_configuration_category.creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.mtime != null">
        dt_tool_configuration_category.mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifier != null">
        dt_tool_configuration_category.modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.is_del != null">
        dt_tool_configuration_category.is_del = #{record.is_del,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item1 != null">
        dt_tool_configuration_category.reserve_item1 = #{record.reserve_item1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item2 != null">
        dt_tool_configuration_category.reserve_item2 = #{record.reserve_item2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item3 != null">
        dt_tool_configuration_category.reserve_item3 = #{record.reserve_item3,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item4 != null">
        dt_tool_configuration_category.reserve_item4 = #{record.reserve_item4,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item5 != null">
        dt_tool_configuration_category.reserve_item5 = #{record.reserve_item5,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item6 != null">
        dt_tool_configuration_category.reserve_item6 = #{record.reserve_item6,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item7 != null">
        dt_tool_configuration_category.reserve_item7 = #{record.reserve_item7,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item8 != null">
        dt_tool_configuration_category.reserve_item8 = #{record.reserve_item8,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item9 != null">
        dt_tool_configuration_category.reserve_item9 = #{record.reserve_item9,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve_item10 != null">
        dt_tool_configuration_category.reserve_item10 = #{record.reserve_item10,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dt_tool_configuration_category dt_tool_configuration_category
    set dt_tool_configuration_category.id = #{record.id,jdbcType=VARCHAR},
      dt_tool_configuration_category.`name` = #{record.name,jdbcType=VARCHAR},
      dt_tool_configuration_category.images = #{record.images,jdbcType=VARCHAR},
      dt_tool_configuration_category.sortNo = #{record.sortNo,jdbcType=VARCHAR},
      dt_tool_configuration_category.ctime = #{record.ctime,jdbcType=TIMESTAMP},
      dt_tool_configuration_category.creator = #{record.creator,jdbcType=VARCHAR},
      dt_tool_configuration_category.mtime = #{record.mtime,jdbcType=TIMESTAMP},
      dt_tool_configuration_category.modifier = #{record.modifier,jdbcType=VARCHAR},
      dt_tool_configuration_category.is_del = #{record.is_del,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item1 = #{record.reserve_item1,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item2 = #{record.reserve_item2,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item3 = #{record.reserve_item3,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item4 = #{record.reserve_item4,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item5 = #{record.reserve_item5,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item6 = #{record.reserve_item6,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item7 = #{record.reserve_item7,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item8 = #{record.reserve_item8,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item9 = #{record.reserve_item9,jdbcType=VARCHAR},
      dt_tool_configuration_category.reserve_item10 = #{record.reserve_item10,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategory">
    update dt_tool_configuration_category
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="images != null">
        images = #{images,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        sortNo = #{sortNo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="is_del != null">
        is_del = #{is_del,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item1 != null">
        reserve_item1 = #{reserve_item1,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item2 != null">
        reserve_item2 = #{reserve_item2,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item3 != null">
        reserve_item3 = #{reserve_item3,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item4 != null">
        reserve_item4 = #{reserve_item4,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item5 != null">
        reserve_item5 = #{reserve_item5,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item6 != null">
        reserve_item6 = #{reserve_item6,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item7 != null">
        reserve_item7 = #{reserve_item7,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item8 != null">
        reserve_item8 = #{reserve_item8,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item9 != null">
        reserve_item9 = #{reserve_item9,jdbcType=VARCHAR},
      </if>
      <if test="reserve_item10 != null">
        reserve_item10 = #{reserve_item10,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.toolscenter.configurationcategory.pojo.entity.DtToolConfigurationCategory">
    update dt_tool_configuration_category
    set `name` = #{name,jdbcType=VARCHAR},
      images = #{images,jdbcType=VARCHAR},
      sortNo = #{sortNo,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_del = #{is_del,jdbcType=VARCHAR},
      reserve_item1 = #{reserve_item1,jdbcType=VARCHAR},
      reserve_item2 = #{reserve_item2,jdbcType=VARCHAR},
      reserve_item3 = #{reserve_item3,jdbcType=VARCHAR},
      reserve_item4 = #{reserve_item4,jdbcType=VARCHAR},
      reserve_item5 = #{reserve_item5,jdbcType=VARCHAR},
      reserve_item6 = #{reserve_item6,jdbcType=VARCHAR},
      reserve_item7 = #{reserve_item7,jdbcType=VARCHAR},
      reserve_item8 = #{reserve_item8,jdbcType=VARCHAR},
      reserve_item9 = #{reserve_item9,jdbcType=VARCHAR},
      reserve_item10 = #{reserve_item10,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>