<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.dep.provider.cem.dao.CemInDecorateVocDao">
    <sql id="Base_Column_List">
        batch_no,
        distribute_no,
        SRId,
        FTVOC1,
        FTVOC2,
        created_by,
        modified_by,
        created_date,
        modified_date,
        id,
        is_deleted
    </sql>

    <resultMap id="BaseResultMap"
               type="com.fotile.dep.provider.cem.pojo.entity.CemInDecorateVoc">
        <result column="batch_no" property="batchNo"/>
        <result column="distribute_no" property="distributeNo"/>
        <result column="SRId" property="SRId"/>
        <result column="FTVOC1" property="FTVOC1"/>
        <result column="FTVOC2" property="FTVOC2"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="findByBatchNoAndIsDeleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from depcenter.cem_in_decorate_voc
        where batch_no = #{batchNo}
          and is_deleted = #{isDeleted}
    </select>
</mapper>