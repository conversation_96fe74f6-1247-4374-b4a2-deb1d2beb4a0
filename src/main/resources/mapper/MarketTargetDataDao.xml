<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.datacenter.tMarketTargetData.dao.MarketTargetDataDao">

    <!-- 批量新增 -->
    <insert id="insertMarketTargetDataByList" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.List">
        <if test="addMarketTargetDataByList != null and addMarketTargetDataByList.size > 0">
            <foreach collection="addMarketTargetDataByList" item="marketTargetData" index="index" separator=";">
                insert into datacenter.t_market_target_data<!--目标完成率标记点-->
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    is_deleted,
                    created_by,
                    created_date,
                    modified_by,
                    modified_date,
                    revise_name,
                    revise_time,
                    remark,
                    data_target_code,
                    region_code,
                    region_name,
                    company_id,
                    company_name,
                    charge_uesr_id,
                    charge_uesr_code,
                    charge_uesr_name,
                    store_org_id,
                    store_code,
                    store_name,
                    market_date_id,
                    date_type,
                    date_value,
                    start_time,
                    end_time,

                    <if test="marketTargetData.cluesNums != null">
                        clues_nums,
                    </if>
                    <if test="marketTargetData.cluesOtherNums != null">
                        clues_other_nums,
                    </if>
                    <if test="marketTargetData.intoStoresNums != null">
                        into_stores_nums,
                    </if>
                    <if test="marketTargetData.designNums != null">
                        design_nums,
                    </if>
                    <if test="marketTargetData.buryPipeNums != null">
                        bury_pipe_nums,
                    </if>
                    <if test="marketTargetData.dealNums != null">
                        deal_nums,
                    </if>
                    <if test="marketTargetData.goodsCount != null">
                        goods_count,
                    </if>
                    <if test="marketTargetData.salesAmount != null">
                        sales_amount,
                    </if>
                    <if test="marketTargetData.unitPrice != null">
                        unit_price,<!--目标完成率标记点-->
                    </if>
                    <if test="marketTargetData.manualCluesFollowNums != null">
                        manual_clues_follow_nums,
                    </if>
                    <if test="marketTargetData.drainageChannelRetain != null">
                        drainage_channel_retain,
                    </if>
                    <if test="marketTargetData.drainageChannelActive != null">
                        drainage_channel_active,
                    </if>
                    <if test="marketTargetData.drainageCluesProportion != null">
                        drainage_clues_proportion,
                    </if>
                    <if test="marketTargetData.stockCluesProportion != null">
                        stock_clues_proportion,
                    </if>
                    weekly_show_name
                </trim>
                <trim prefix="values (" suffix=")" suffixOverrides=",">
                    0,
                    #{marketTargetData.createdBy,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.modifiedBy,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.reviseName,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.remark,jdbcType=VARCHAR},
                    #{marketTargetData.dataTargetCode,jdbcType=VARCHAR},
                    #{marketTargetData.regionCode,jdbcType=VARCHAR},
                    #{marketTargetData.regionName,jdbcType=VARCHAR},
                    #{marketTargetData.companyId,jdbcType=BIGINT},
                    #{marketTargetData.companyName,jdbcType=VARCHAR},
                    #{marketTargetData.chargeUesrId,jdbcType=BIGINT},
                    #{marketTargetData.chargeUesrCode,jdbcType=VARCHAR},
                    #{marketTargetData.chargeUesrName,jdbcType=VARCHAR},
                    #{marketTargetData.storeOrgId,jdbcType=BIGINT},
                    #{marketTargetData.storeCode,jdbcType=VARCHAR},
                    #{marketTargetData.storeName,jdbcType=VARCHAR},
                    #{marketTargetData.marketDateId,jdbcType=BIGINT},
                    #{marketTargetData.dateType,jdbcType=BIGINT},
                    #{marketTargetData.dateValue,jdbcType=VARCHAR},
                    #{marketTargetData.startTime,jdbcType=TIMESTAMP},
                    #{marketTargetData.endTime,jdbcType=TIMESTAMP},

                    <if test="marketTargetData.cluesNums != null">
                        #{marketTargetData.cluesNums},
                    </if>
                    <if test="marketTargetData.cluesOtherNums != null">
                        #{marketTargetData.cluesOtherNums},
                    </if>
                    <if test="marketTargetData.intoStoresNums != null">
                        #{marketTargetData.intoStoresNums},
                    </if>
                    <if test="marketTargetData.designNums != null">
                        #{marketTargetData.designNums},
                    </if>
                    <if test="marketTargetData.buryPipeNums != null">
                        #{marketTargetData.buryPipeNums},
                    </if>
                    <if test="marketTargetData.dealNums != null">
                        #{marketTargetData.dealNums},
                    </if>
                    <if test="marketTargetData.goodsCount != null">
                        #{marketTargetData.goodsCount},
                    </if>
                    <if test="marketTargetData.salesAmount != null">
                        #{marketTargetData.salesAmount},
                    </if>
                    <if test="marketTargetData.unitPrice != null">
                        #{marketTargetData.unitPrice},
                    </if>
                    <if test="marketTargetData.manualCluesFollowNums != null">
                        #{marketTargetData.manualCluesFollowNums},
                    </if>
                    <if test="marketTargetData.drainageChannelRetain != null">
                        #{marketTargetData.drainageChannelRetain},
                    </if>
                    <if test="marketTargetData.drainageChannelActive != null">
                        #{marketTargetData.drainageChannelActive},
                    </if>
                    <if test="marketTargetData.drainageCluesProportion != null">
                        #{marketTargetData.drainageCluesProportion},
                    </if>
                    <if test="marketTargetData.stockCluesProportion != null">
                        #{marketTargetData.stockCluesProportion},
                    </if>
                    #{marketTargetData.weeklyShowName,jdbcType=VARCHAR}
                </trim>
            </foreach>
        </if>
    </insert>

    <!-- 分页查询count -->
    <select id="selectAllByCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        (
        SELECT
        tccd.id,
        50 dataTargetCode,
        tccd.region_code regionCode,
        tccd.region_name regionName,
        tccd.company_id companyId,
        tccd.company_name companyName,
        tccd.manager_user_id chargeUesrId,
        tccd.manager_user_code chargeUesrCode,
        tccd.manager_user_name chargeUesrName,
        tccd.store_org_id storeOrgId,
        tccd.store_code storeCode,
        tccd.store_name storeName,
        tccd.charge_uesr_id salesUserId,
        tccd.charge_uesr_code salesUserCode,
        tccd.charge_uesr_name salesUserName,
        tccd.date_type dateType,
        tccd.date_value dateValue,
        tccd.start_time startTime,
        tccd.end_time endTime,
        tccd.weekly_show_name weeklyShowName,
        tccd.clues_nums cluesNums,
        tccd.clues_other_nums cluesOtherNums,
        tccd.into_stores_nums intoStoresNums,
        tccd.design_nums designNums,
        tccd.bury_pipe_nums buryPipeNums,
        tccd.deal_nums dealNums,
        tccd.goods_count goodsCount,
        tccd.sales_amount salesAmount,
        tccd.unit_price unitPrice,<!--目标完成率标记点-->
        tccd.manual_clues_follow_nums manualCluesFollowNums,
        tccd.revise_name reviseName,
        tccd.revise_time reviseTime,
        tccd.store_type_code storeTypeCode,
        tccd.store_key_word storeLabel
        FROM
        datacenter.t_charge_complete_data<!--目标完成率表标记点--> tccd
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            INNER JOIN orgcenter.t_store sto1 ON tccd.store_org_id = sto1.org_id
            INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
        </if>
        where tccd.is_deleted = 0
        <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
            and tccd.region_code in
            <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            and ifnull(sto1.store_sub_channel_code,'#') in
            <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                #{subChannelCode}
            </foreach>
        </if>
        <if test="inDto.regionCode != null and inDto.regionCode != ''">
            and tccd.region_code = #{inDto.regionCode}
        </if>

        <if test="inDto.dateValue != null and inDto.dateValue != ''">
            and tccd.date_value = #{inDto.dateValue}
        </if>
        <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
            AND tccd.region_code IN
            <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
            and tccd.date_value IN
            <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                #{dateValue}
            </foreach>
        </if>


        UNION ALL
        SELECT
        tm.id,
        tm.data_target_code dataTargetCode,
        tm.region_code regionCode,
        tm.region_name regionName,
        tm.company_id companyId,
        tm.company_name companyName,
        tm.charge_uesr_id chargeUesrId,
        tm.charge_uesr_code chargeUesrCode,
        tm.charge_uesr_name chargeUesrName,
        tm.store_org_id storeOrgId,
        tm.store_code storeCode,
        tm.store_name storeName,
        NULL salesUserId,
        NULL salesUserCode,
        NULL salesUserName,
        tm.date_type dateType,
        tm.date_value dateValue,
        tm.start_time startTime,
        tm.end_time endTime,
        tm.weekly_show_name weeklyShowName,
        tm.clues_nums cluesNums,
        tm.clues_other_nums cluesOtherNums,
        tm.into_stores_nums intoStoresNums,
        tm.design_nums designNums,
        tm.bury_pipe_nums buryPipeNums,
        tm.deal_nums dealNums,
        tm.goods_count goodsCount,
        tm.sales_amount salesAmount,
        tm.unit_price unitPrice,<!--目标完成率标记点-->
        tm.manual_clues_follow_nums manualCluesFollowNums,
        tm.revise_name reviseName,
        tm.revise_time reviseTime,
        tm.store_type_code storeTypeCode,
        tm.store_key_word storeLabel
        FROM
        datacenter.t_market_target_data<!--目标完成率标记点--> tm
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            INNER JOIN orgcenter.t_store sto1 ON tm.store_org_id = sto1.org_id
            INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
        </if>
        where tm.is_deleted = 0
                <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
                    and tm.region_code in
                    <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                        #{regionCode}
                    </foreach>
                </if>
                <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
                    and ifnull(sto1.store_sub_channel_code,'#') in
                    <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                        #{subChannelCode}
                    </foreach>
                </if>
                <if test="inDto.regionCode != null and inDto.regionCode != ''">
                    and tm.region_code = #{inDto.regionCode}
                </if>
                <if test="inDto.dateValue != null and inDto.dateValue != ''">
                    and tm.date_value = #{inDto.dateValue}
                </if>
                <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
                    AND tm.region_code IN
                    <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                        #{regionCode}
                    </foreach>
                </if>
                <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
                    and tm.date_value IN
                    <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                        #{dateValue}
                    </foreach>
                </if>
        ) tt2
        <where>
            <if test="inDto.ids != null and inDto.ids.size() > 0 ">
                and id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.storeTypes != null and inDto.storeTypes.size > 0">
                and storeTypeCode in
                <foreach collection="inDto.storeTypes" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="inDto.storeLabelList != null and inDto.storeLabelList.size() != 0">
                and
                <foreach collection="inDto.storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                    FIND_IN_SET(#{storeLabelCode},storeLabel)
                </foreach>
            </if>
            <if test="inDto.dataTargetCode != null and inDto.dataTargetCode != ''">
                and dataTargetCode = #{inDto.dataTargetCode}
            </if>

            <if test="inDto.companyId != null">
                and companyId =#{inDto.companyId}
            </if>
            <if test="inDto.companyOrgIds != null and inDto.companyOrgIds.size > 0">
                and (companyId in
                <foreach collection="inDto.companyOrgIds" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
                or (dataTargetCode = '10' and companyId is null and regionCode in
                <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                    #{regionCode}
                </foreach>
                    )
                )
            </if>
            <if test="inDto.chargeUesrId != null">
                and chargeUesrId = #{inDto.chargeUesrId}
            </if>
            <if test="inDto.storeOrgId != null">
                and storeOrgId = #{inDto.storeOrgId}
            </if>
            <if test="inDto.storeOrgIds != null and inDto.storeOrgIds.size() != 0">
                <foreach collection="inDto.storeOrgIds" item="item" separator="," open="and storeOrgId in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="inDto.dateType != null">
                and dateType = #{inDto.dateType}
            </if>
            <if test="inDto.companyOrgIdsStr != null and inDto.companyOrgIdsStr !=''">
                AND companyId IN
                <foreach collection="inDto.companyOrgIdsStr.split(',')" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
            </if>
            <if test="inDto.storeOrgIdsStr != null and inDto.storeOrgIdsStr !=''">
                AND storeOrgId IN
                <foreach collection="inDto.storeOrgIdsStr.split(',')" item="storeOrgId" open="(" separator="," close=")">
                    #{storeOrgId}
                </foreach>
            </if>
            <if test="inDto.storeTypesStr != null and inDto.storeTypesStr !=''">
                AND storeTypeCode IN
                <foreach collection="inDto.storeTypesStr.split(',')" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
        </where>
    </select>


    <!-- 分页查询 -->
    <select id="selectAllBy" resultType="com.fotile.datacenter.tMarketTargetData.pojo.dto.SelectAllByOutDto">
        SELECT
        *
        FROM
        (
        SELECT
        tccd.id,
        50 dataTargetCode,
        tccd.region_code regionCode,
        tccd.region_name regionName,
        tccd.company_id companyId,
        tccd.company_name companyName,
        tccd.manager_user_id chargeUesrId,
        tccd.manager_user_code chargeUesrCode,
        tccd.manager_user_name chargeUesrName,
        tccd.store_org_id storeOrgId,
        tccd.store_code storeCode,
        tccd.store_name storeName,
        tccd.charge_uesr_id salesUserId,
        tccd.charge_uesr_code salesUserCode,
        tccd.charge_uesr_name salesUserName,
        tccd.date_type dateType,
        tccd.date_value dateValue,
        tccd.start_time startTime,
        tccd.end_time endTime,
        tccd.weekly_show_name weeklyShowName,
        tccd.clues_nums cluesNums,
        tccd.clues_other_nums cluesOtherNums,
        tccd.into_stores_nums intoStoresNums,
        tccd.design_nums designNums,
        tccd.bury_pipe_nums buryPipeNums,
        tccd.deal_nums dealNums,
        tccd.goods_count goodsCount,
        tccd.sales_amount salesAmount,
        tccd.unit_price unitPrice,<!--目标完成率标记点-->
        tccd.manual_clues_follow_nums manualCluesFollowNums,
        tccd.revise_name reviseName,
        tccd.revise_time reviseTime,
        tccd.store_type_code storeTypeCode,
        store_key_word storeLabel,
        drainage_clues_proportion drainageCluesProportion,
        stock_clues_proportion stockCluesProportion,
        tccd.drainage_channel_retain drainageChannelRetain,
        tccd.drainage_channel_active drainageChannelActive
        FROM
        datacenter.t_charge_complete_data<!--目标完成率表标记点--> tccd
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            INNER JOIN orgcenter.t_store sto1 ON tccd.store_org_id = sto1.org_id
            INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
        </if>
        where tccd.is_deleted = 0

        <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
            and tccd.region_code in
            <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            and ifnull(sto1.store_sub_channel_code,'#') in
            <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                #{subChannelCode}
            </foreach>
        </if>
        <if test="inDto.regionCode != null and inDto.regionCode != ''">
            and tccd.region_code = #{inDto.regionCode}
        </if>
        <if test="inDto.dateValue != null and inDto.dateValue != ''">
            and tccd.date_value = #{inDto.dateValue}
        </if>
        <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
            AND tccd.region_code IN
            <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
            and tccd.date_value IN
            <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                #{dateValue}
            </foreach>
        </if>
        UNION ALL
        SELECT
        tm.id,
        tm.data_target_code dataTargetCode,
        tm.region_code regionCode,
        tm.region_name regionName,
        tm.company_id companyId,
        tm.company_name companyName,
        tm.charge_uesr_id chargeUesrId,
        tm.charge_uesr_code chargeUesrCode,
        tm.charge_uesr_name chargeUesrName,
        tm.store_org_id storeOrgId,
        tm.store_code storeCode,
        tm.store_name storeName,
        NULL salesUserId,
        NULL salesUserCode,
        NULL salesUserName,
        tm.date_type dateType,
        tm.date_value dateValue,
        tm.start_time startTime,
        tm.end_time endTime,
        tm.weekly_show_name weeklyShowName,
        tm.clues_nums cluesNums,
        tm.clues_other_nums cluesOtherNums,
        tm.into_stores_nums intoStoresNums,
        tm.design_nums designNums,
        tm.bury_pipe_nums buryPipeNums,
        tm.deal_nums dealNums,
        tm.goods_count goodsCount,
        tm.sales_amount salesAmount,
        tm.unit_price unitPrice,<!--目标完成率标记点-->
        tm.manual_clues_follow_nums manualCluesFollowNums,
        tm.revise_name reviseName,
        tm.revise_time reviseTime,
        tm.store_type_code storeTypeCode,
        tm.store_key_word storeLabel,
        tm.drainage_clues_proportion drainageCluesProportion,
        tm.stock_clues_proportion stockCluesProportion,
        tm.drainage_channel_retain drainageChannelRetain,
        tm.drainage_channel_active drainageChannelActive
        FROM
        datacenter.t_market_target_data<!--目标完成率标记点--> tm
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            INNER JOIN orgcenter.t_store sto1 ON tm.store_org_id = sto1.org_id
            INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
        </if>
        where tm.is_deleted = 0
        <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
            and tm.region_code in
            <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            and ifnull(sto1.store_sub_channel_code,'#') in
            <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                #{subChannelCode}
            </foreach>
        </if>
        <if test="inDto.regionCode != null and inDto.regionCode != ''">
            and tm.region_code = #{inDto.regionCode}
        </if>
        <if test="inDto.dateValue != null and inDto.dateValue != ''">
            and tm.date_value = #{inDto.dateValue}
        </if>
        <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
            AND tm.region_code IN
            <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
            and tm.date_value IN
            <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                #{dateValue}
            </foreach>
        </if>
        ) tt2
        <where>

            <if test="inDto.ids != null and inDto.ids.size() > 0 ">
                and id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.storeOrgIds != null and inDto.storeOrgIds.size() != 0">
                <foreach collection="inDto.storeOrgIds" item="item" separator="," open="and storeOrgId in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="inDto.storeTypes != null and inDto.storeTypes.size > 0">
                and storeTypeCode in
                <foreach collection="inDto.storeTypes" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="inDto.storeLabelList != null and inDto.storeLabelList.size() != 0">
                and
                <foreach collection="inDto.storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                    FIND_IN_SET(#{storeLabelCode},storeLabel)
                </foreach>
            </if>
            <if test="inDto.dataTargetCode != null and inDto.dataTargetCode != ''">
                and dataTargetCode = #{inDto.dataTargetCode}
            </if>
            <if test="inDto.companyId != null">
                and companyId =#{inDto.companyId}
            </if>
            <if test="inDto.companyOrgIds != null and inDto.companyOrgIds.size > 0">
                and (companyId in
                        <foreach collection="inDto.companyOrgIds" item="companyOrgId" open="(" separator="," close=")">
                            #{companyOrgId}
                        </foreach>
                    or (dataTargetCode = '10' and companyId is null and regionCode in
                        <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                            #{regionCode}
                        </foreach>
                    )
                 )
            </if>
            <if test="inDto.chargeUesrId != null">
                and chargeUesrId = #{inDto.chargeUesrId}
            </if>
            <if test="inDto.storeOrgId != null">
                and storeOrgId = #{inDto.storeOrgId}
            </if>
            <if test="inDto.dateType != null">
                and dateType = #{inDto.dateType}
            </if>
            <if test="inDto.companyOrgIdsStr != null and inDto.companyOrgIdsStr !=''">
                AND companyId IN
                <foreach collection="inDto.companyOrgIdsStr.split(',')" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
            </if>
            <if test="inDto.storeOrgIdsStr != null and inDto.storeOrgIdsStr !=''">
                AND storeOrgId IN
                <foreach collection="inDto.storeOrgIdsStr.split(',')" item="storeOrgId" open="(" separator="," close=")">
                    #{storeOrgId}
                </foreach>
            </if>
            <if test="inDto.storeTypesStr != null and inDto.storeTypesStr !=''">
                AND storeTypeCode IN
                <foreach collection="inDto.storeTypesStr.split(',')" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
        </where>
        order by reviseTime desc
        limit #{pageInfo.offset},#{pageInfo.size}
    </select>


    <!-- 批量删除 -->
    <update id="byIdsDelete">
        update datacenter.t_market_target_data<!--目标完成率标记点-->
        set is_deleted = id,
        revise_time = now(),
        revise_name = #{reviseName},
        modified_by = #{userId},
        modified_date = now()
        <where>
            is_deleted = 0
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>


    <!-- 根据层级，大区/公司/业务员/门店 查询 -->
    <select id="findAllByType" resultType="com.fotile.datacenter.tMarketTargetData.pojo.dto.FindAllByTypeOutDto">
        select *
        from datacenter.t_market_target_data<!--目标完成率标记点-->
        <where>
            <if test="inDto.dataTargetCode != null and inDto.dataTargetCode != ''">
                and data_target_code = #{inDto.dataTargetCode}
            </if>
            <if test="inDto.regionCode != null and inDto.regionCode != ''">
                and region_code = #{inDto.regionCode}
            </if>
            <if test="inDto.companyId != null">
                and company_id =#{inDto.companyId}
            </if>
            <if test="inDto.chargeUesrId != null">
                and charge_uesr_id = #{inDto.chargeUesrId}
            </if>
            <if test="inDto.storeOrgId != null">
                and store_org_id = #{inDto.storeOrgId}
            </if>
            <if test="inDto.marketDateId != null and inDto.marketDateId != ''">
                and market_date_id = #{inDto.marketDateId}
            </if>
            <if test="inDto.ids != null and inDto.ids.size() != 0">
                and id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        order by revise_time desc
    </select>


    <!-- 批量修改 -->
    <update id="updateBatche">
        <foreach collection="list" item="item" index="index">
            update datacenter.t_market_target_data<!--目标完成率标记点-->
            set
            modified_by = #{item.modifiedBy},
            modified_date = now(),
            revise_time = now(),
            <if test="item.cluesNums != null">
                clues_nums = #{item.cluesNums},
            </if>
            <if test="item.cluesOtherNums != null">
                clues_other_nums = #{item.cluesOtherNums},
            </if>
            <if test="item.intoStoresNums != null">
                into_stores_nums = #{item.intoStoresNums},
            </if>
            <if test="item.designNums != null">
                design_nums = #{item.designNums},
            </if>
            <if test="item.buryPipeNums != null">
                bury_pipe_nums = #{item.buryPipeNums},
            </if>
            <if test="item.dealNums != null">
                deal_nums = #{item.dealNums},
            </if>
            <if test="item.goodsCount != null">
                goods_count = #{item.goodsCount},
            </if>
            <if test="item.salesAmount != null">
                sales_amount = #{item.salesAmount},
            </if>
            <if test="item.unitPrice != null">
                unit_price = #{item.unitPrice},<!--目标完成率标记点-->
            </if>
            <if test="item.manualCluesFollowNums != null">
                manual_clues_follow_nums = #{item.manualCluesFollowNums},
            </if>
            <if test="item.drainageChannelRetain != null">
                drainage_channel_retain = #{item.drainageChannelRetain},
            </if>
            <if test="item.drainageChannelActive != null">
                drainage_channel_active = #{item.drainageChannelActive},
            </if>
            <if test="item.drainageCluesProportion != null">
                drainage_clues_proportion = #{item.drainageCluesProportion},
            </if>
            <if test="item.stockCluesProportion != null">
                stock_clues_proportion = #{item.stockCluesProportion},
            </if>
            revise_name = #{item.reviseName}
            where
            is_deleted = 0
            and id =#{item.id};
        </foreach>
    </update>

    <!-- 批量新增2 -->
    <insert id="insertMarketTargetDataByList2" keyProperty="id" useGeneratedKeys="true" parameterType="java.util.List">
        <if test="addMarketTargetDataByList != null and addMarketTargetDataByList.size > 0">
            <foreach collection="addMarketTargetDataByList" item="marketTargetData" index="index" separator=";">
                insert into datacenter.t_market_target_data<!--目标完成率标记点-->
                <trim prefix="(" suffix=")" suffixOverrides=",">
                is_deleted,
                created_by,
                created_date,
                modified_by,
                modified_date,
                revise_name,
                revise_time,
                remark,
                data_target_code,
                region_code,
                region_name,
                company_id,
                company_name,
                charge_uesr_id,
                charge_uesr_code,
                charge_uesr_name,
                store_org_id,
                store_code,
                store_name,
                market_date_id,
                date_type,
                date_value,
                start_time,
                end_time,

                <if test="marketTargetData.cluesNums != null">
                    clues_nums,
                </if>
                <if test="marketTargetData.cluesOtherNums != null">
                    clues_other_nums,
                </if>
                <if test="marketTargetData.intoStoresNums != null">
                    into_stores_nums,
                </if>
                <if test="marketTargetData.designNums != null">
                    design_nums,
                </if>
                <if test="marketTargetData.buryPipeNums != null">
                    bury_pipe_nums,
                </if>
                <if test="marketTargetData.dealNums != null">
                    deal_nums,
                </if>
                <if test="marketTargetData.goodsCount != null">
                    goods_count,
                </if>
                <if test="marketTargetData.salesAmount != null">
                    sales_amount,
                </if>
                <if test="marketTargetData.unitPrice != null">
                    unit_price,<!--目标完成率标记点-->
                </if>
                <if test="marketTargetData.manualCluesFollowNums != null">
                    manual_clues_follow_nums,
                </if>
                <if test="marketTargetData.drainageChannelRetain != null">
                    drainage_channel_retain,
                </if>
                <if test="marketTargetData.drainageChannelActive != null">
                    drainage_channel_active,
                </if>
                <if test="marketTargetData.drainageCluesProportion != null">
                    drainage_clues_proportion,
                </if>
                <if test="marketTargetData.stockCluesProportion != null">
                    stock_clues_proportion,
                </if>
                weekly_show_name
                </trim>
                <trim prefix="values (" suffix=")" suffixOverrides=",">
                    0,
                    #{marketTargetData.createdBy,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.modifiedBy,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.reviseName,jdbcType=VARCHAR},
                    now(),
                    #{marketTargetData.remark,jdbcType=VARCHAR},
                    '40',
                    #{marketTargetData.regionCode,jdbcType=VARCHAR},
                    #{marketTargetData.regionName,jdbcType=VARCHAR},
                    #{marketTargetData.companyId,jdbcType=BIGINT},
                    #{marketTargetData.companyName,jdbcType=VARCHAR},
                    #{marketTargetData.chargeUesrId,jdbcType=BIGINT},
                    #{marketTargetData.chargeUesrCode,jdbcType=VARCHAR},
                    #{marketTargetData.chargeUesrName,jdbcType=VARCHAR},
                    #{marketTargetData.storeOrgId,jdbcType=BIGINT},
                    #{marketTargetData.storeCode,jdbcType=VARCHAR},
                    #{marketTargetData.storeName,jdbcType=VARCHAR},
                    #{marketTargetData.marketDateId,jdbcType=BIGINT},
                    #{marketTargetData.dateType,jdbcType=BIGINT},
                    #{marketTargetData.dateValue,jdbcType=VARCHAR},
                    #{marketTargetData.startTime,jdbcType=TIMESTAMP},
                    #{marketTargetData.endTime,jdbcType=TIMESTAMP},

                    <if test="marketTargetData.cluesNums != null">
                        #{marketTargetData.cluesNums},
                    </if>
                    <if test="marketTargetData.cluesOtherNums != null">
                        #{marketTargetData.cluesOtherNums},
                    </if>
                    <if test="marketTargetData.intoStoresNums != null">
                        #{marketTargetData.intoStoresNums},
                    </if>
                    <if test="marketTargetData.designNums != null">
                        #{marketTargetData.designNums},
                    </if>
                    <if test="marketTargetData.buryPipeNums != null">
                        #{marketTargetData.buryPipeNums},
                    </if>
                    <if test="marketTargetData.dealNums != null">
                        #{marketTargetData.dealNums},
                    </if>
                    <if test="marketTargetData.goodsCount != null">
                        #{marketTargetData.goodsCount},
                    </if>
                    <if test="marketTargetData.salesAmount != null">
                        #{marketTargetData.salesAmount},
                    </if>
                    <if test="marketTargetData.unitPrice != null">
                        #{marketTargetData.unitPrice},
                    </if>
                    <if test="marketTargetData.manualCluesFollowNums != null">
                        #{marketTargetData.manualCluesFollowNums},
                    </if>
                    <if test="marketTargetData.drainageChannelRetain != null">
                        #{marketTargetData.drainageChannelRetain},
                    </if>
                    <if test="marketTargetData.drainageChannelActive != null">
                        #{marketTargetData.drainageChannelActive},
                    </if>
                    <if test="marketTargetData.drainageCluesProportion != null">
                        #{marketTargetData.drainageCluesProportion},
                    </if>
                    <if test="marketTargetData.stockCluesProportion != null">
                        #{marketTargetData.stockCluesProportion},
                    </if>
                    #{marketTargetData.weeklyShowName,jdbcType=VARCHAR}
                </trim>
            </foreach>
        </if>
    </insert>
    <!-- 批量修改2 -->
    <update id="updateBatche2">
        <foreach collection="list" item="item" index="index">
            update datacenter.t_market_target_data<!--目标完成率标记点-->
            set
            modified_by = #{item.modifiedBy},
            modified_date = now(),
            revise_time = now(),
            <if test="item.cluesNums != null">
                clues_nums = #{item.cluesNums},
            </if>
            <if test="item.cluesOtherNums != null">
                clues_other_nums = #{item.cluesOtherNums},
            </if>
            <if test="item.intoStoresNums != null">
                into_stores_nums = #{item.intoStoresNums},
            </if>
            <if test="item.designNums != null">
                design_nums = #{item.designNums},
            </if>
            <if test="item.buryPipeNums != null">
                bury_pipe_nums = #{item.buryPipeNums},
            </if>
            <if test="item.dealNums != null">
                deal_nums = #{item.dealNums},
            </if>
            <if test="item.goodsCount != null">
                goods_count = #{item.goodsCount},
            </if>
            <if test="item.salesAmount != null">
                sales_amount = #{item.salesAmount},
            </if>
            <if test="item.unitPrice != null">
                unit_price = #{item.unitPrice},<!--目标完成率标记点-->
            </if>
            <if test="item.manualCluesFollowNums != null">
                manual_clues_follow_nums = #{item.manualCluesFollowNums},
            </if>
            <if test="item.drainageChannelRetain != null">
                drainage_channel_retain = #{item.drainageChannelRetain},
            </if>
            <if test="item.drainageChannelActive != null">
                drainage_channel_active = #{item.drainageChannelActive},
            </if>
            <if test="item.drainageCluesProportion != null">
                drainage_clues_proportion = #{item.drainageCluesProportion},
            </if>
            <if test="item.stockCluesProportion != null">
                stock_clues_proportion = #{item.stockCluesProportion},
            </if>
            revise_name = #{item.reviseName}
            where
            is_deleted = 0
            and id =#{item.id};
        </foreach>
    </update>

    <!-- 根据type,value查询-->
    <select id="findAllByType2" resultType="com.fotile.datacenter.completeData.pojo.dto.AddStoreCompleteDto">
        select id,data_target_code dataTargetCode,
        region_code regionCode,region_name regionName,
        company_id companyId,company_name companyName,
        charge_uesr_id chargeUesrId,charge_uesr_code chargeUesrCode,charge_uesr_name chargeUesrName,
        store_org_id storeOrgId,store_code storeCode,store_name storeName,
        market_date_id marketDateId,
        clues_nums cluesNums, clues_other_nums cluesOtherNums,into_stores_nums intoStoresNums,design_nums designNums,bury_pipe_nums buryPipeNums,
        deal_nums dealNums,goods_count goodsCount,sales_amount salesAmount,unit_price unitPrice,manual_clues_follow_nums manualCluesFollowNums,<!--目标完成率标记点-->
        drainage_clues_proportion drainageCluesProportion, stock_clues_proportion stockCluesProportion,
        drainage_channel_retain drainageChannelRetain,
        drainage_channel_active drainageChannelActive
        from datacenter.t_market_target_data<!--目标完成率标记点-->
        <where>
            <if test="dateType != null ">
                and date_type = #{dateType}
            </if>
            <if test="dateValue != null and dateValue != ''">
                and date_value = #{dateValue}
            </if>
            <if test="weeklyShowName != null and weeklyShowName != ''">
                and weekly_show_name = #{weeklyShowName}
            </if>
            <if test="managerId != null and managerId != ''">
                and charge_uesr_id = #{managerId}
            </if>
            <if test="storeOrgIds != null and storeOrgIds.size > 0">
                and store_org_id in
                <foreach collection="storeOrgIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
            </if>
            and is_deleted = 0
        </where>
        order by revise_time desc
    </select>

    <!-- 查询门店业务目标 -->
    <select id="selectStoreCompleteAll" resultType="com.fotile.datacenter.completeData.pojo.dto.GetStoreCompleteOutDto">
        select id,date_type dateType,date_value dateValue,start_time startTime,end_time endTime,weekly_show_name weeklyShowName,
        charge_uesr_id chargeUesrId,charge_uesr_code chargeUesrCode,charge_uesr_name chargeUesrName,
        store_org_id storeOrgId,store_code storeCode,store_name storeName,
        clues_nums cluesNums, clues_other_nums cluesOtherNums,into_stores_nums intoStoresNums,design_nums designNums,
        bury_pipe_nums buryPipeNums,deal_nums dealNums,goods_count goodsCount,sales_amount salesAmount,unit_price unitPrice,manual_clues_follow_nums manualCluesFollowNums<!--目标完成率标记点-->
        ,drainage_clues_proportion drainageCluesProportion,
        stock_clues_proportion stockCluesProportion,
        drainage_channel_retain drainageChannelRetain,
        drainage_channel_active drainageChannelActive
        from datacenter.t_market_target_data <!--目标完成率标记点-->
        <where>
            is_deleted = 0
            <if test="dateType != null ">
                and date_type = #{dateType}
            </if>
            <if test="dateValue != null and dateValue != ''">
                and date_value = #{dateValue}
            </if>
            <if test="weeklyShowName != null and weeklyShowName != ''">
                and weekly_show_name = #{weeklyShowName}
            </if>
            <if test="storeOrgIds != null and storeOrgIds.size > 0">
                and store_org_id in
                <foreach collection="storeOrgIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
            </if>
        </where>
        group by id
        order by id desc
    </select>


    <select id="selectListForComplete" parameterType="com.fotile.datacenter.completeData.pojo.dto.TargetCompleteDTO" resultType="com.fotile.datacenter.completeData.pojo.dto.QueryTargetDataDTO">
        select id,date_type dateType,date_value dateValue,start_time startTime,end_time endTime,weekly_show_name weeklyShowName,
        <if test="targetCompleteDTO.dataTargetCode == '10'">
            region_code targetCode,region_name targetName,
        </if>
        <if test="targetCompleteDTO.dataTargetCode == '20'">
            company_id targetCode,company_name targetName,
        </if>
        <if test="targetCompleteDTO.dataTargetCode == '30'">
            charge_uesr_id targetCode,charge_uesr_name targetName,
        </if>
        <if test="targetCompleteDTO.dataTargetCode == '40'">
            store_org_id targetCode,store_name targetName,
        </if>
        <if test="targetCompleteDTO.dataTargetCode == '50'">
            charge_uesr_id targetCode,charge_uesr_name targetName,
        </if>
        clues_nums cluesNums, clues_other_nums cluesOtherNums,into_stores_nums intoStoresNums,design_nums designNums,
        bury_pipe_nums buryPipeNums,deal_nums dealNums,goods_count goodsCount,sales_amount salesAmount,unit_price unitPrice,manual_clues_follow_nums manualCluesFollowNums,<!--目标完成率标记点-->
        drainage_clues_proportion drainageCluesProportion, stock_clues_proportion stockCluesProportion,
        drainage_channel_retain drainageChannelRetain,
        drainage_channel_active drainageChannelActive
        from datacenter.t_market_target_data <!--目标完成率标记点待修改-->
        <where>
            is_deleted = 0
            and data_target_code = #{targetCompleteDTO.dataTargetCode}
            <if test="targetCompleteDTO.storeTypeList != null and targetCompleteDTO.storeTypeList.size > 0">
                and store_type_code in
                <foreach collection="targetCompleteDTO.storeTypeList" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="targetCompleteDTO.storeLabelList != null and targetCompleteDTO.storeLabelList.size() != 0">
                and
                <foreach collection="targetCompleteDTO.storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                    FIND_IN_SET(#{storeLabelCode},store_key_word)
                </foreach>
            </if>
            <if test="targetCompleteDTO.dateType != null ">
                and date_type = #{targetCompleteDTO.dateType}
            </if>
            <if test="targetCompleteDTO.dateValue != null and targetCompleteDTO.dateValue != ''">
                and date_value = #{targetCompleteDTO.dateValue}
            </if>
            <if test="targetCompleteDTO.dataTargetCode == '10'">
                <if test="targetCompleteDTO.regionCodes != null and targetCompleteDTO.regionCodes.size() != 0">
                and region_code in
                <foreach collection="targetCompleteDTO.regionCodes" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
                </if>
            </if>
            <if test="targetCompleteDTO.dataTargetCode == '20'">
                and region_code = #{targetCompleteDTO.regionCode}
                <if test="targetCompleteDTO.companyIds != null and targetCompleteDTO.companyIds.size() != 0">
                and company_id in
                <foreach collection="targetCompleteDTO.companyIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
                </if>
            </if>
            <if test="targetCompleteDTO.dataTargetCode == '30'">
                and company_id =#{targetCompleteDTO.companyId}
            <if test="targetCompleteDTO.managerIds != null and targetCompleteDTO.managerIds.size() != 0">
                and charge_uesr_id in
                <foreach collection="targetCompleteDTO.managerIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
            </if>
            </if>
            <if test="targetCompleteDTO.dataTargetCode == '40'">
                and charge_uesr_id =#{targetCompleteDTO.managerId}
                <if test="targetCompleteDTO.storeIds != null and targetCompleteDTO.storeIds.size() != 0">
                    and store_org_id in
                    <foreach collection="targetCompleteDTO.storeIds" item="item" open="(" close=")" separator="," index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="targetCompleteDTO.dataTargetCode == '50'">
                and store_org_id =#{targetCompleteDTO.storeOrgId,jdbcType=BIGINT}
            </if>
            <if test="targetCompleteDTO.isTargetStore != null">
                <choose>
                    <when test="targetCompleteDTO.isTargetStore == 1">
                        <if test="targetCompleteDTO.queryWordValueList != null and targetCompleteDTO.queryWordValueList.size() != 0">
                            and
                            <foreach collection="targetCompleteDTO.queryWordValueList" item="item" separator="or" open="(" close=")">
                                ${item} > 0
                            </foreach>
                        </if>
                    </when>
                    <when test="targetCompleteDTO.isTargetStore == 2">
                        <if test="targetCompleteDTO.queryWordValueList != null and targetCompleteDTO.queryWordValueList.size() != 0">
                            and
                            <foreach collection="targetCompleteDTO.queryWordValueList" item="item" separator="and">
                                ${item} = 0
                            </foreach>
                        </if>
                    </when>
                </choose>
            </if>
        </where>
        group by id
        order by id desc
        limit #{pageInfo.offset},#{pageInfo.size}
    </select>

    <select id="selectListForCompleteCount" resultType="java.lang.Integer">
        select count(1)
        from datacenter.t_market_target_data <!--目标完成率标记点-->
        <where>
            is_deleted = 0
            and data_target_code = #{dataTargetCode}
            <if test="dateType != null ">
                and date_type = #{dateType}
            </if>
            <if test="storeTypeList != null and storeTypeList.size > 0">
                and store_type_code in
                <foreach collection="storeTypeList" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="storeLabelList != null and storeLabelList.size() != 0">
                and
                <foreach collection="storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                    FIND_IN_SET(#{storeLabelCode},store_key_word)
                </foreach>
            </if>
            <if test="dateValue != null and dateValue != ''">
                and date_value = #{dateValue}
            </if>
            <if test="dataTargetCode == '10'">
                <if test="regionCodes != null and regionCodes.size() != 0">
                    and region_code in
                    <foreach collection="regionCodes" item="item" open="(" close=")" separator="," index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dataTargetCode == '20'">
                and region_code = #{regionCode}
                <if test="companyIds != null and companyIds.size() != 0">
                    and company_id in
                    <foreach collection="companyIds" item="item" open="(" close=")" separator="," index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dataTargetCode == '30'">
                and company_id =#{companyId}
                <if test="managerIds != null and managerIds.size() != 0">
                    and charge_uesr_id in
                    <foreach collection="managerIds" item="item" open="(" close=")" separator="," index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dataTargetCode == '40'">
                and charge_uesr_id =#{managerId}
                <if test="storeIds != null and storeIds.size() != 0">
                    and store_org_id in
                    <foreach collection="storeIds" item="item" open="(" close=")" separator="," index="">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="dataTargetCode == '50'">
                and store_org_id =#{storeOrgId,jdbcType=BIGINT}
            </if>
            <if test="isTargetStore != null">
                <choose>
                    <when test="isTargetStore == 1">
                        <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                            and
                            <foreach collection="queryWordValueList" item="item" separator="or" open="(" close=")">
                                ${item} > 0
                            </foreach>
                        </if>
                    </when>
                    <when test="isTargetStore == 2">
                        <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                            and
                            <foreach collection="queryWordValueList" item="item" separator="and">
                                ${item} = 0
                            </foreach>
                        </if>
                    </when>
                </choose>
            </if>
        </where>

    </select>

    <select id="SynchronizeStoreDataByStoreTypeIsNull"
            resultType="com.fotile.datacenter.tMarketTargetData.pojo.entity.MarketTargetData">
        select id,store_org_id from datacenter.t_market_target_data <!--目标完成率标记点-->
        where store_type_code is null and modified_date >= #{startDate,jdbcType=TIMESTAMP}
    </select>

    <update id="updateMarketCompleByQueryStoreByModifiedDateVO">
        update datacenter.t_market_target_data <!--目标完成率标记点-->
        set store_type_code = #{storeTypeCode,jdbcType=VARCHAR},
        store_type_name = #{storeTypeName,jdbcType=VARCHAR},
        store_key_word = #{storeKeyWord,jdbcType=VARCHAR} ,
        store_type_id = #{storeTypeId,jdbcType=BIGINT}
        where store_org_id = #{orgId,jdbcType=BIGINT}
    </update>

    <select id="selectAllTargetStoreNums" resultType="java.lang.Long">
        select
        count(1)
        from (
            select distinct id
            from datacenter.t_market_target_data
            <where>
                is_deleted = 0
                and data_target_code = #{dataTargetCode}
                <if test="dateType != null ">
                    and date_type = #{dateType}
                </if>
                <if test="storeTypeList != null and storeTypeList.size > 0">
                    and store_type_code in
                    <foreach collection="storeTypeList" item="storeTypeCode" open="(" separator="," close=")">
                        #{storeTypeCode}
                    </foreach>
                </if>
                <if test="storeLabelList != null and storeLabelList.size() != 0">
                    and
                    <foreach collection="storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                        FIND_IN_SET(#{storeLabelCode},store_key_word)
                    </foreach>
                </if>
                <if test="dateValue != null and dateValue != ''">
                    and date_value = #{dateValue}
                </if>
                <if test="dataTargetCode == '10'">
                    <if test="regionCodes != null and regionCodes.size() != 0">
                        and region_code in
                        <foreach collection="regionCodes" item="item" open="(" close=")" separator="," index="">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dataTargetCode == '20'">
                    and region_code = #{regionCode}
                    <if test="companyIds != null and companyIds.size() != 0">
                        and company_id in
                        <foreach collection="companyIds" item="item" open="(" close=")" separator="," index="">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dataTargetCode == '30'">
                    and company_id =#{companyId}
                    <if test="managerIds != null and managerIds.size() != 0">
                        and charge_uesr_id in
                        <foreach collection="managerIds" item="item" open="(" close=")" separator="," index="">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dataTargetCode == '40'">
                    and charge_uesr_id =#{managerId}
                    <if test="storeIds != null and storeIds.size() != 0">
                        and store_org_id in
                        <foreach collection="storeIds" item="item" open="(" close=")" separator="," index="">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dataTargetCode == '50'">
                    and store_org_id =#{storeOrgId,jdbcType=BIGINT}
                </if>
                <if test="isTargetStore != null">
                    <choose>
                        <when test="isTargetStore == 1">
                            <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                                and
                                <foreach collection="queryWordValueList" item="item" separator="or" open="(" close=")">
                                    ${item} > 0
                                </foreach>
                            </if>
                        </when>
                        <when test="isTargetStore == 2">
                            <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                                and
                                <foreach collection="queryWordValueList" item="item" separator="and">
                                    ${item} = 0
                                </foreach>
                            </if>
                        </when>
                    </choose>
                </if>
            </where>
        ) a
    </select>

    <select id="selectHaveTargetStoreNums" resultType="java.lang.Long">
        select
        count(1)
        from (
        select distinct id
        from datacenter.t_market_target_data
        <where>
            is_deleted = 0
            <if test="storeIds != null and storeIds.size() != 0">
                and store_org_id in
                <foreach collection="storeIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
            </if>
            <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                and
                <foreach collection="queryWordValueList" item="item" separator="or" open="(" close=")">
                    ${item} > 0
                </foreach>
            </if>
        </where>
        ) a
    </select>

    <select id="selectHaveTargetStore" resultType="java.lang.Long">
        select distinct id
        from datacenter.t_market_target_data
        <where>
            is_deleted = 0
            <if test="storeIds != null and storeIds.size() != 0">
                and store_org_id in
                <foreach collection="storeIds" item="item" open="(" close=")" separator="," index="">
                    #{item}
                </foreach>
            </if>
            <if test="queryWordValueList != null and queryWordValueList.size() != 0">
                and
                <foreach collection="queryWordValueList" item="item" separator="or" open="(" close=")">
                    ${item} > 0
                </foreach>
            </if>
        </where>
    </select>

    <select id="getExportPercentMarket" resultType="com.fotile.datacenter.tMarketTargetData.pojo.dto.SelectAllByOutDto">
        SELECT
        *
        FROM
        (
        SELECT
        tccd.id,
        50 dataTargetCode,
        tccd.region_code regionCode,
        tccd.region_name regionName,
        tccd.company_id companyId,
        tccd.company_name companyName,
        tccd.manager_user_id chargeUesrId,
        tccd.manager_user_code chargeUesrCode,
        tccd.manager_user_name chargeUesrName,
        tccd.store_org_id storeOrgId,
        tccd.store_code storeCode,
        tccd.store_name storeName,
        tccd.charge_uesr_id salesUserId,
        tccd.charge_uesr_code salesUserCode,
        tccd.charge_uesr_name salesUserName,
        tccd.date_type dateType,
        tccd.date_value dateValue,
        tccd.start_time startTime,
        tccd.end_time endTime,
        tccd.weekly_show_name weeklyShowName,
        tccd.clues_nums cluesNums,
        tccd.clues_other_nums cluesOtherNums,
        tccd.into_stores_nums intoStoresNums,
        tccd.design_nums designNums,
        tccd.bury_pipe_nums buryPipeNums,
        tccd.deal_nums dealNums,
        tccd.goods_count goodsCount,
        tccd.sales_amount salesAmount,
        tccd.unit_price unitPrice,<!--目标完成率标记点-->
        tccd.manual_clues_follow_nums manualCluesFollowNums,
        tccd.revise_name reviseName,
        tccd.revise_time reviseTime,
        tccd.store_type_code storeTypeCode,
        tccd.store_type_name storeTypeName,
        sto1.store_sub_channel_code storeSubChannelCode,
        store_key_word storeLabel,
        drainage_clues_proportion drainageCluesProportion,
        stock_clues_proportion stockCluesProportion,
        tccd.drainage_channel_retain drainageChannelRetain,
        tccd.drainage_channel_active drainageChannelActive
        FROM
        datacenter.t_charge_complete_data tccd
        <choose>
            <when test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
                INNER JOIN orgcenter.t_store sto1 ON tccd.store_org_id = sto1.org_id
                INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
            </when>
            <otherwise>
                LEFT JOIN orgcenter.t_store sto1 ON tccd.store_org_id = sto1.org_id
                LEFT JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
            </otherwise>
        </choose>
        where tccd.is_deleted = 0

        <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
            and tccd.region_code in
            <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            and ifnull(sto1.store_sub_channel_code,'#') in
            <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                #{subChannelCode}
            </foreach>
        </if>
        <if test="inDto.regionCode != null and inDto.regionCode != ''">
            and tccd.region_code = #{inDto.regionCode}
        </if>
        <if test="inDto.dateValue != null and inDto.dateValue != ''">
            and tccd.date_value = #{inDto.dateValue}
        </if>
        <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
            AND tccd.region_code IN
            <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
            and tccd.date_value IN
            <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                #{dateValue}
            </foreach>
        </if>
        UNION ALL
        SELECT
        tm.id,
        tm.data_target_code dataTargetCode,
        tm.region_code regionCode,
        tm.region_name regionName,
        tm.company_id companyId,
        tm.company_name companyName,
        tm.charge_uesr_id chargeUesrId,
        tm.charge_uesr_code chargeUesrCode,
        tm.charge_uesr_name chargeUesrName,
        tm.store_org_id storeOrgId,
        tm.store_code storeCode,
        tm.store_name storeName,
        NULL salesUserId,
        NULL salesUserCode,
        NULL salesUserName,
        tm.date_type dateType,
        tm.date_value dateValue,
        tm.start_time startTime,
        tm.end_time endTime,
        tm.weekly_show_name weeklyShowName,
        tm.clues_nums cluesNums,
        tm.clues_other_nums cluesOtherNums,
        tm.into_stores_nums intoStoresNums,
        tm.design_nums designNums,
        tm.bury_pipe_nums buryPipeNums,
        tm.deal_nums dealNums,
        tm.goods_count goodsCount,
        tm.sales_amount salesAmount,
        tm.unit_price unitPrice,<!--目标完成率标记点-->
        tm.manual_clues_follow_nums manualCluesFollowNums,
        tm.revise_name reviseName,
        tm.revise_time reviseTime,
        tm.store_type_code storeTypeCode,
        tm.store_type_name storeTypeName,
        sto1.store_sub_channel_code storeSubChannelCode,
        tm.store_key_word storeLabel,
        tm.drainage_clues_proportion drainageCluesProportion,
        tm.stock_clues_proportion stockCluesProportion,
        tm.drainage_channel_retain drainageChannelRetain,
        tm.drainage_channel_active drainageChannelActive
        FROM
        datacenter.t_market_target_data tm
        <choose>
            <when test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
                INNER JOIN orgcenter.t_store sto1 ON tm.store_org_id = sto1.org_id
                INNER JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
            </when>
            <otherwise>
                LEFT JOIN orgcenter.t_store sto1 ON tm.store_org_id = sto1.org_id
                LEFT JOIN orgcenter.channel_category cc ON sto1.store_type = cc.id
            </otherwise>
        </choose>
        where tm.is_deleted = 0
        <if test="inDto.regionCodes != null and inDto.regionCodes.size > 0">
            and tm.region_code in
            <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.storeSubChannelCodes != null and inDto.storeSubChannelCodes != ''">
            and ifnull(sto1.store_sub_channel_code,'#') in
            <foreach collection="inDto.storeSubChannelCodes.split(',')" item="subChannelCode" open="(" separator="," close=")">
                #{subChannelCode}
            </foreach>
        </if>
        <if test="inDto.regionCode != null and inDto.regionCode != ''">
            and tm.region_code = #{inDto.regionCode}
        </if>
        <if test="inDto.dateValue != null and inDto.dateValue != ''">
            and tm.date_value = #{inDto.dateValue}
        </if>
        <if test="inDto.regionCodesStr != null and inDto.regionCodesStr !=''">
            AND tm.region_code IN
            <foreach collection="inDto.regionCodesStr.split(',')" item="regionCode" open="(" separator="," close=")">
                #{regionCode}
            </foreach>
        </if>
        <if test="inDto.dateValuesStr != null and inDto.dateValuesStr !=''">
            and tm.date_value IN
            <foreach collection="inDto.dateValuesStr.split(',')" item="dateValue" open="(" separator="," close=")">
                #{dateValue}
            </foreach>
        </if>
        ) tt2
        <where>

            <if test="inDto.ids != null and inDto.ids.size() > 0 ">
                and id in
                <foreach collection="inDto.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inDto.storeOrgIds != null and inDto.storeOrgIds.size() != 0">
                <foreach collection="inDto.storeOrgIds" item="item" separator="," open="and storeOrgId in (" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="inDto.storeTypes != null and inDto.storeTypes.size > 0">
                and storeTypeCode in
                <foreach collection="inDto.storeTypes" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
            <if test="inDto.storeLabelList != null and inDto.storeLabelList.size() != 0">
                and
                <foreach collection="inDto.storeLabelList" item="storeLabelCode" open="(" separator="or" close=")">
                    FIND_IN_SET(#{storeLabelCode},storeLabel)
                </foreach>
            </if>
            <if test="inDto.dataTargetCode != null and inDto.dataTargetCode != ''">
                and dataTargetCode = #{inDto.dataTargetCode}
            </if>
            <if test="inDto.companyId != null">
                and companyId =#{inDto.companyId}
            </if>
            <if test="inDto.companyOrgIds != null and inDto.companyOrgIds.size > 0">
                and (companyId in
                <foreach collection="inDto.companyOrgIds" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
                or (dataTargetCode = '10' and companyId is null and regionCode in
                <foreach collection="inDto.regionCodes" item="regionCode" open="(" separator="," close=")">
                    #{regionCode}
                </foreach>
                )
                )
            </if>
            <if test="inDto.chargeUesrId != null">
                and chargeUesrId = #{inDto.chargeUesrId}
            </if>
            <if test="inDto.storeOrgId != null">
                and storeOrgId = #{inDto.storeOrgId}
            </if>
            <if test="inDto.dateType != null">
                and dateType = #{inDto.dateType}
            </if>
            <if test="inDto.companyOrgIdsStr != null and inDto.companyOrgIdsStr !=''">
                AND companyId IN
                <foreach collection="inDto.companyOrgIdsStr.split(',')" item="companyOrgId" open="(" separator="," close=")">
                    #{companyOrgId}
                </foreach>
            </if>
            <if test="inDto.storeOrgIdsStr != null and inDto.storeOrgIdsStr !=''">
                AND storeOrgId IN
                <foreach collection="inDto.storeOrgIdsStr.split(',')" item="storeOrgId" open="(" separator="," close=")">
                    #{storeOrgId}
                </foreach>
            </if>
            <if test="inDto.storeTypesStr != null and inDto.storeTypesStr !=''">
                AND storeTypeCode IN
                <foreach collection="inDto.storeTypesStr.split(',')" item="storeTypeCode" open="(" separator="," close=")">
                    #{storeTypeCode}
                </foreach>
            </if>
        </where>
        order by reviseTime desc
        limit #{inDto.pageNum},#{inDto.pageSize}
    </select>


</mapper>