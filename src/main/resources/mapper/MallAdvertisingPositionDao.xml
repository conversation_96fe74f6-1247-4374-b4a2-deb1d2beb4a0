<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.cmscenter.mallAdver.dao.MallAdvertisingPositionDao">
    <resultMap id="BaseResultMap" type="com.fotile.cmscenter.mallAdver.pojo.MallAdvertisingPosition">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="jump_type" jdbcType="BIGINT" property="jumpType"/>
        <result column="channel_child" jdbcType="VARCHAR" property="channelChild" />
        <result column="sort" jdbcType="BIGINT" property="sort"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="web_style" jdbcType="VARCHAR" property="webStyle"/>
        <result column="width" jdbcType="BIGINT" property="width"/>
        <result column="height" jdbcType="BIGINT" property="height"/>
        <result column="upper_limit" jdbcType="BIGINT" property="upperLimit"/>
        <result column="lower_limit" jdbcType="BIGINT" property="lowerLimit"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="key_name" jdbcType="VARCHAR" property="keyName"/>
        <result column="their_page" jdbcType="VARCHAR" property="theirPage"/>
        <result column="recommend_show_num" jdbcType="BIGINT" property="recommendShowNum"/>
        <result column="created_by_name" jdbcType="VARCHAR" property="createdByName"/>
        <result column="modified_by_name" jdbcType="VARCHAR" property="modifiedByName"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
    </resultMap>

    <resultMap id="MallAdvertisingMap"
               type="com.fotile.cmscenter.mallAdver.pojo.dto.MallAdvertisingPositionInfoByCodeOutDto">
        <id column="map_id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="sort" jdbcType="BIGINT" property="sort"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="web_style" jdbcType="VARCHAR" property="webStyle"/>
        <result column="width" jdbcType="BIGINT" property="width"/>
        <result column="height" jdbcType="BIGINT" property="height"/>
        <result column="upper_limit" jdbcType="BIGINT" property="upperLimit"/>
        <result column="lower_limit" jdbcType="BIGINT" property="lowerLimit"/>
        <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="jump_type" jdbcType="BIGINT" property="jumpType"/>
        <result column="channel_child" jdbcType="VARCHAR" property="channelChild" />
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="key_name" jdbcType="VARCHAR" property="keyName"/>
        <result column="recommend_show_num" jdbcType="BIGINT" property="recommendShowNum"/>
        <result column="created_by_name" jdbcType="VARCHAR" property="createdByName"/>
        <result column="their_page" jdbcType="VARCHAR" property="theirPage"/>
        <result column="modified_by_name" jdbcType="VARCHAR" property="modifiedByName"/>
        <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
        <collection property="mallAdvertisings" ofType="com.fotile.cmscenter.mallAdver.pojo.MallAdvertising" javaType="list">
            <id column="ma_id" jdbcType="BIGINT" property="id" />
            <result column="ma_advertising_position_id" jdbcType="VARCHAR" property="advertisingPositionId" />
            <result column="ma_name" jdbcType="VARCHAR" property="name" />
            <result column="ma_sort" jdbcType="BIGINT" property="sort" />
            <result column="ma_staus" jdbcType="BIGINT" property="status" />
            <result column="validity_start_time" jdbcType="TIMESTAMP" property="validityStartTime" />
            <result column="validity_end_time" jdbcType="TIMESTAMP" property="validityEndTime" />
            <result column="ma_web_style" jdbcType="BIGINT" property="webStyle" />
            <result column="ma_picture" jdbcType="VARCHAR" property="picture" />
            <result column="ma_video_url" jdbcType="VARCHAR" property="videoUrl" />
            <result column="ma_video_surface" jdbcType="VARCHAR" property="videoSurface" />
            <result column="ma_video_name" jdbcType="VARCHAR" property="videoName" />
            <result column="ma_show_name" jdbcType="BIGINT" property="showName" />
            <result column="ma_content" jdbcType="VARCHAR" property="content" />
            <result column="ma_jump_type" jdbcType="BIGINT" property="jumpType" />
            <result column="ma_jump_url" jdbcType="VARCHAR" property="jumpUrl" />
            <result column="ma_goods_code" jdbcType="VARCHAR" property="goodsCode" />
            <result column="ma_goods_name" jdbcType="VARCHAR" property="goodsName" />
            <result column="ma_goods_model" jdbcType="VARCHAR" property="goodsModel" />
            <result column="ma_pre_sale_price" jdbcType="DECIMAL" property="preSalePrice" />
            <result column="ma_listed_price" jdbcType="DECIMAL" property="listedPrice" />
            <result column="ma_click_num" jdbcType="BIGINT" property="clickNum" />
            <result column="ma_click_count" jdbcType="BIGINT" property="clickCount" />
            <result column="ma_show_site" jdbcType="VARCHAR" property="showSite" />
            <result column="ma_color" jdbcType="VARCHAR" property="color" />
            <result column="ma_title" jdbcType="VARCHAR" property="title" />
            <result column="ma_description" jdbcType="VARCHAR" property="description" />
            <result column="ma_lable" jdbcType="VARCHAR" property="lable" />
            <result column="ma_key_name" jdbcType="VARCHAR" property="keyName" />
            <result column="ma_created_by_name" jdbcType="VARCHAR" property="createdByName" />
            <result column="ma_modified_by_name" jdbcType="VARCHAR" property="modifiedByName" />
            <result column="ma_is_deleted" jdbcType="INTEGER" property="isDeleted" />
            <result column="ma_created_by" jdbcType="VARCHAR" property="createdBy" />
            <result column="ma_created_date" jdbcType="TIMESTAMP" property="createdDate" />
            <result column="ma_modified_by" jdbcType="VARCHAR" property="modifiedBy" />
            <result column="ma_modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
            <result column="ma_channel_goods_id" jdbcType="BIGINT" property="channelGoodsId"/>
            <result column="mini_app_id" jdbcType="BIGINT" property="miniAppId"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
    id, code, `name`, channel, sort, `status`, show_name, web_style, width, height, upper_limit, their_page,
    lower_limit, description, key_name,activity_id,jump_url,jump_type,channel_child, recommend_show_num, created_by_name, modified_by_name,
    is_deleted, created_by, created_date, modified_by, modified_date
  </sql>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        id,code, `name`, channel, sort,`status`,their_page,channel_child
        from mall_advertising_position
        where is_deleted = 0
        <if test="mallAdvertisingPositionInDto.code != null and mallAdvertisingPositionInDto.code != '' ">and code =
            #{mallAdvertisingPositionInDto.code}
        </if>
        <if test="mallAdvertisingPositionInDto.name != null and mallAdvertisingPositionInDto.name != '' ">and `name`
            like CONCAT('%',#{mallAdvertisingPositionInDto.name},'%')
        </if>
        <if test="mallAdvertisingPositionInDto.channel != null and mallAdvertisingPositionInDto.channel != ''">and channel_child = #{mallAdvertisingPositionInDto.channel}
        </if>
        <if test="mallAdvertisingPositionInDto.theirPage != null">and their_page = #{mallAdvertisingPositionInDto.theirPage}
        </if>
        <if test="mallAdvertisingPositionInDto.status != null ">and `status` = #{mallAdvertisingPositionInDto.status}
        </if>
        order by sort
        <if test="mallAdvertisingPositionInDto.sortOrder!=null and mallAdvertisingPositionInDto.sortOrder!=''">
            ${mallAdvertisingPositionInDto.sortOrder}
        </if>
        ,created_date
        limit #{mallAdvertisingPositionInDto.page},#{mallAdvertisingPositionInDto.size}

    </select>
    <select id="selectCountAll" resultType="java.lang.Long">
        select
        count(1)
        from mall_advertising_position
        where is_deleted = 0
        <if test="mallAdvertisingPositionInDto.code != null and mallAdvertisingPositionInDto.code != '' ">and code =
            #{mallAdvertisingPositionInDto.code}
        </if>
        <if test="mallAdvertisingPositionInDto.name != null and mallAdvertisingPositionInDto.name != '' ">and `name`
            like CONCAT('%',#{mallAdvertisingPositionInDto.name},'%')
        </if>
        <if test="mallAdvertisingPositionInDto.channel != null and mallAdvertisingPositionInDto.channel != ''">and channel_child = #{mallAdvertisingPositionInDto.channel}
        </if>
        <if test="mallAdvertisingPositionInDto.theirPage != null">and their_page = #{mallAdvertisingPositionInDto.theirPage}
        </if>
        <if test="mallAdvertisingPositionInDto.status != null ">and `status` = #{mallAdvertisingPositionInDto.status}
        </if>
    </select>

    <select id="selectByChannelId"
            resultType="com.fotile.cmscenter.mallAdver.pojo.dto.MallAdvertisingPositionInfoByCodeOutDto"
            resultMap="MallAdvertisingMap">
        SELECT
        map.id as map_id,map.`code`,map.`name`,map.channel,map.sort,map.`status`,map.show_name,map.web_style,map.width,map.height,
        map.upper_limit,map.lower_limit,map.description,map.activity_id,map.jump_url,map.jump_type,map.channel_child,map.key_name,
        map.recommend_show_num,map.created_by_name,map.modified_by_name,map.is_deleted,map.created_by,map.created_date,map.modified_by,
        map.modified_date,map.their_page,
        ma.id as ma_id,ma.advertising_position_id as ma_advertising_position_id,ma.`name` as ma_name,ma.sort as ma_sort,
        ma.`status` as ma_staus,ma.validity_start_time,ma.validity_end_time,ma.web_style as ma_web_style,ma.picture as ma_picture,
        ma.video_url as ma_video_url,ma.video_surface as ma_video_surface,ma.video_name as ma_video_name,ma.show_name as ma_show_name,
        ma.content as ma_content,ma.jump_type as ma_jump_type,ma.jump_url as ma_jump_url,ma.goods_code as ma_goods_code,
        ma.goods_name as ma_goods_name,ma.goods_model as ma_goods_model,ma.pre_sale_price as ma_pre_sale_price,
        ma.listed_price as ma_listed_price,ma.click_num as ma_click_num,ma.click_count as ma_click_count,ma.show_site as ma_show_site,
        ma.color as ma_color,ma.title as ma_title,ma.description as ma_description,ma.lable as ma_lable,ma.key_name as ma_key_name,
        ma.created_by_name as ma_created_by_name,ma.modified_by_name as ma_modified_by_name,ma.is_deleted as ma_is_deleted,
        ma.created_by as ma_created_by,ma.created_date as ma_created_date,ma.modified_by as ma_modified_by,
        ma.modified_date as ma_modified_date ,ma.channel_goods_id as ma_channel_goods_id,ma.mini_app_id as miniAppId
        FROM
            mall_advertising_position map
        LEFT JOIN mall_advertising ma ON map.`code` = ma.advertising_position_id
        AND ma.is_deleted = 0
        <where>
            map.is_deleted = 0
            AND map.status = 0
            <if test="code != null and code != ''">
                AND map.code = #{code}
            </if>
            <if test="channelCode != null and channelCode != ''">
                AND map.channel_child = #{channelCode}
            </if>
            <if test="theirPage != null and theirPage != ''">
                AND map.their_page = #{theirPage}
            </if>
        </where>
        ORDER BY map.sort ASC
    </select>

    <resultMap id="selectAdvertisingMap" type="com.fotile.cmscenter.mallAdver.pojo.dto.MallAdvertisingPositionInfoByCodeOutDto" extends="BaseResultMap">
        <collection property="mallAdvertisings"
                    ofType="com.fotile.cmscenter.mallAdver.pojo.dto.MallAdvertisingVo"
                    javaType="list" select="com.fotile.cmscenter.mallAdver.dao.MallAdvertisingDao.selectAdvertising" column="code" fetchType="eager">
        </collection>
    </resultMap>

    <select id="selectAdvertising" resultMap="selectAdvertisingMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            mall_advertising_position
        <where>
            is_deleted = 0
            AND status = 0
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="channelCode != null and channelCode != ''">
                AND channel_child = #{channelCode}
            </if>
            <if test="theirPage != null and theirPage != ''">
                AND their_page = #{theirPage}
            </if>
        </where>
        ORDER BY sort ASC
    </select>
</mapper>
