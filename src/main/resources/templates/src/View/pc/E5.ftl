<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>方太水槽洗碗机E5 洗碗更洁净 还能去果蔬农残</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="moudle" content="Index" />
    <meta name="renderer" content="webkit" />
    <meta name="referrer" content="unsafe-url" />
    <meta
            name="Keywords"
            content="方太，方太油烟机，方太燃气灶，方太消毒柜，方太蒸箱，方太烤箱，方太微波炉，方太水槽洗碗机，方太热水器"
    />
    <meta
            name="Description"
            content="26年深耕高端厨电领域，方太持续围绕空气、水、烹饪三大核心科技，推进基础科研纵深发展，首创近吸式吸油烟机、欧式吸油烟机、水槽洗碗机、集成烹饪中心等划时代厨电品类，引领厨电科技化浪潮"
    />
    <link href="/webstatic/Public/Home/css/global.min.css" rel="stylesheet" type="text/css" />
    <link href="/webstatic/Public/Home/css/survey_user.css?20181202" rel="stylesheet" type="text/css"/>
    <script src="https://libs.baidu.com/jquery/2.0.0/jquery.js"></script>

    <script src=""></script>
</head>

<script>

     function  skipMobile(){
        let thisOS = navigator.userAgent;
        let os = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"]
        let check = 0
        for (var i = 0; i < os.length; i++) {
            if (thisOS.match(os[i])) {
                console.log('在所有设备中')
                check = 1
            }
        }
        if (check == 1) {
            //跳转PC
            window.location.href= '/h5/hero/E5.html'
        }
    }
    skipMobile()
</script>
<style>


    .e5{
        width: 100%;

        background: rgba(255,255,255,0.8);
        backdrop-filter: blur(10px);
    }
    .e5-container{
        display: flex;
        width: 100%;
        margin: 0 auto;
        justify-content: space-around;
        height: 51px;
        line-height: 51px;
        align-items: center;
        box-sizing: border-box;
    }
    .left{
        display: flex;
        box-sizing: border-box;
        align-items: center;
    }

    .left>img{
        width: 36px;
        height: 24px;
    }
    .left>div{
        height: 20px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        line-height: 20px;
        letter-spacing: 1px;
        margin-left: 10px;
    }
    .e5-container>.right{
        display: flex;
    }
    .e5-container>.right>div{
        box-sizing: border-box;
        height: 17px;
        font-size: 12px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 17px;
        padding-right: 34px;
        cursor: pointer;
    }
    .e5-container>.right>div:last-child{
        font-size: 12px;
        transform: scale(0.83);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9C1D22;
    }
    .e5-fixed{
        position: fixed;
        top: 0px;
    }
    .footer{
        padding-top: 0px !important;
    }


</style>
<body>
<#include "/src/View/common/js.ftl"/>
<#include "/src/View/components/header_new.ftl"/>
<div class="e5">
    <div class="e5-container">
        <div class="left">
            <img src="https://hsimage.fotile.com/202304241532209786202.png" alt="">
            <div>水槽洗碗机</div>
        </div>
        <div class="right">
            <div class="right-one">首页</div>
            <div class="right-two">场景图库</div>
            <div class="right-three">参数与安装</div>
            <div class="right-four" id="zaixianzixun">如何购买</div>
        </div>
    </div>
</div>

<div id="bodyWapper">
    <img src="https://hsimage.fotile.com/202404080956081915939.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956134686114.jpg" alt=""   class="bodyWapper-one">
    <img src="https://hsimage.fotile.com/202404080956176413903.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956219307149.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956268892863.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956307267984.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956344648393.jpg" alt=""   >
    <img src="https://hsimage.fotile.com/202404080956390896492.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956434434349.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956495356262.jpg" alt="">
    <img src="https://hsimage.fotile.com/202404080956535269883.jpg" alt="" class="bodyWapper-two">
    <img src="https://hsimage.fotile.com/202404080956580592029.jpg" alt="">
    
</div>
<style>
    #bodyWapper img{
        width: 100%;
        height: auto;
        display: block;
    }
    
</style>
<div id="footerWapper"></div>

<!-- 用户留资 S ================================ -->
<div class="overlay-cover-survey"></div>
<div class="survey-user-wrapper">
    <div class="guic-header-control">
        <a href="javascript:void(0);" class="close-survey-user-wrapper"></a>
    </div>
    <div class="survey-user-body">
        <!-- 用户留资 -->
        <div class="survey-user-content">
            <div class="survey-user-container">
                <div class="guic-header">
                    <h3 class="tit">让专业厨电顾问联系我</h3>
                    <p class="summary">方太厨电顾问将尽快与您联系，为您解答疑问</p>
                    <p class="tip">*为必填项目</p>
                </div>

                <div id="surveyUserWrapper">
                    <div class="guic-body">
                        <div class="content-loader" v-if="loadingData"></div>

                        <div class="guic-form">
                            <form id="surveyForm" @submit="checkFormData" method="post">
                                <div class="form-group clearfix">
                                    <div class="form-block product-check-block">
                                        <label class="form-label">您想了解的产品(可多选)<sup>*</sup></label>
                                        <div class="form-control" id="productCheckPopupBtn" @click.stop="toggleProductCheckPopup" :title="checkedProductName">
                                            <template v-if="checkedProductName == '' ">选择产品</template>
                                            <template v-if="checkedProductName != '' ">{{checkedProductName}}</template>
                                        </div>
                                        <input type="hidden" name="product" v-validate="'required'" v-model="product" />

                                        <p class="helper-error" v-show="errors.has('product')" v-cloak>{{ errors.first('product') }}</p>
                                        <!-- 产品多选 -->
                                        <div id="productCheckPopup" :class=" isShowProductCheckPopup?'product-check-popup show':'product-check-popup' ">
                                            <label class="prd-checkbox" :for=" 'prd-check-'+index "
                                                   v-for="(key, value, index) in surveyBaseData.product">
                                                <input type="checkbox"
                                                       :data-name="key"
                                                       :data-index="index"
                                                       :id=" 'prd-check-'+index "
                                                       :key="index"
                                                       :value="value"
                                                       @change="productChange" /> {{key}}
                                            </label>

                                            <!-- <a href="javascript:void(0);" class="cancel-control-btn" @click="toggleProductCheckPopup">&times;</a> -->
                                        </div>
                                    </div>

                                    <div class="form-block mr0">
                                        <label class="form-label">产品型号(选填)</label>
                                        <input name="model" class="form-control" placeholder="输入产品型号" v-model="model"/>
                                    </div>

                                    <div class="form-block">
                                        <label class="form-label">您的姓氏<sup>*</sup></label>
                                        <input name="firstname" class="form-control" placeholder="输入您的姓氏"
                                               v-validate="'required'"
                                               v-model="firstname"/>
                                        <p class="helper-error" v-show="errors.has('firstname')" v-cloak>{{ errors.first('firstname') }}</p>
                                    </div>

                                    <div class="form-block mr0">
                                        <label class="form-label">您的称谓<sup>*</sup></label>
                                        <div>
                                            <label class="radio-control"
                                                   v-for="(key, value, index) in surveyBaseData.sex"
                                                   :for="'sex'+value">
                                                <input type="radio" name="sex" :id="'sex'+value" :key="index" :value="value"
                                                       v-validate="'required'"
                                                       v-model="sex" />{{key}}
                                            </label>
                                        </div>
                                        <p class="helper-error" v-show="errors.has('sex')" v-cloak>{{ errors.first('sex') }}</p>
                                    </div>

                                    <div class="form-block" style="margin-right: 0;width:482px;">
                                        <label class="form-label">您的手机号<sup>*</sup></label>
                                        <input name="phone" class="form-control" placeholder="输入您的手机号"
                                               v-validate="'required|phone'"
                                               v-model="phone"/>
                                        <p class="helper-error" v-show="errors.has('phone')" v-cloak>{{ errors.first('phone') }}</p>
                                    </div>

                                    <!-- 省市区选择 S -->
                                    <div class="form-block full region-block clearfix mr0">
                                        <div class="region-item province-item">
                                            <label class="form-label">省份<sup>*</sup></label>
                                            <select name="sheng_id" class="form-control"
                                                    ref="shengSelect"
                                                    v-validate="'required'"
                                                    v-model="sheng_id">
                                                <option value="" selected>{{shengPlaceholderText}}</option>
                                                <option
                                                        v-for="item in shengData"
                                                        :key="item.id"
                                                        :value="item.id">{{item.name}}</option>
                                            </select>
                                            <p class="helper-error" v-show="errors.has('sheng_id')" v-cloak>{{ errors.first('sheng_id') }}</p>
                                        </div>
                                        <div class="region-item mr0" style="width:235px;">
                                            <label class="form-label">城市<sup>*</sup></label>
                                            <select name="shi_id" class="form-control"
                                                    ref="shiSelect"
                                                    v-validate="'required'"
                                                    v-model="shi_id">
                                                <option value="" selected>{{shiPlaceholderText}}</option>
                                                <option
                                                        v-for="item in shiData"
                                                        :key="item.id"
                                                        :value="item.id">{{item.name}}</option>
                                            </select>
                                            <p class="helper-error" v-show="errors.has('shi_id')" v-cloak>{{ errors.first('shi_id') }}</p>
                                        </div>
                                        <div class="region-item county-item mr0" style="display:none;">
                                            <label class="form-label">区/县</label>
                                            <select name="qu_id" class="form-control"
                                                    ref="quSelect"
                                                    v-model="qu_id">
                                                <option value="" selected>{{quPlaceholderText}}</option>
                                                <option
                                                        v-for="item in quData"
                                                        :key="item.id"
                                                        :value="item.id">{{item.name}}</option>
                                            </select>
                                            <p class="helper-error" v-show="errors.has('qu_id')" v-cloak>{{ errors.first('qu_id') }}</p>
                                        </div>
                                    </div>
                                    <!-- 省市区选择 End -->

                                    <div class="form-block full channel-block mr0" style="display:none;">
                                        <label class="form-label">您更希望通过哪个渠道了解更多产品、服务和活动的最新消息<sup>*</sup></label>
                                        <select name="knowfrom" class="form-control"
                                                v-model="knowfrom">
                                            <option value="" disabled selected>了解渠道</option>
                                            <option v-for="(key, value, index) in surveyBaseData.knowfrom" :key="index" :value="value">{{key}}</option>
                                        </select>
                                        <p class="helper-error" v-show="errors.has('knowfrom')" v-cloak>{{ errors.first('knowfrom') }}</p>
                                    </div>

                                    <div class="form-block full aggre-block mr0">
                                        <input type="checkbox" name="agreement"
                                               v-validate="'required'"
                                               v-model="agreement" />
                                        我已阅读并同意方太
                                        <a href="/account/agreement.html" target="_blank" style="text-decoration: underline;">隐私政策</a>
                                        <p class="helper-error" v-show="errors.has('agreement')" v-cloak>{{ errors.first('agreement') }}</p>
                                    </div>

                                </div>
                                <!-- / form-group -->
                                <button type="submit" class="send-form-btn"
                                        :class=" canSubmitForm == false?'disabled':'' ">{{submitFormBtnText}}</button>
                            </form>

                        </div>
                        <!-- / guic-form -->

                    </div>
                    <!-- / guic-body -->

                    <div :class=" isSaveSuccess? 'alert-dialog show':'alert-dialog'  " v-if="isSaveSuccess">
                        <div class="alert-dialog-wrapper">
                            <span class="close-alert-dialog" @click="closeAlertDialog">&times;</span>
                            <div class="alert-dialog-body">
                                <i class="alert-dialog-icon"></i>
                                <h3 class="tit">您已提交成功</h3>
                                <p class="cont">我们将尽快与您联系，为您解答产品相关疑问。</p>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
            <!-- / get-userinfo-container -->
        </div>

        <div class="guic-footer">
            <h3 class="tit">已经购买，咨询售后相关问题</h3>
            <a href="javascript:void(0);" class="service-control-btn toggle-service-dialog">联系售后客服</a>
            <p class="tel">或者拨打 <span>952315</span></p>
        </div>
    </div>
</div>
<!-- 用户留资 End ========================== -->
<script src="https://lib.baomitu.com/vee-validate/2.1.1/vee-validate.min.js"></script>
<script src="https://lib.baomitu.com/vee-validate/2.1.1/locale/zh_CN.js"></script>
<script src="https://lib.baomitu.com/vue/2.5.17/vue.min.js"></script>
<script src="https://lib.baomitu.com/sweetalert/2.1.2/sweetalert.min.js"></script>
<script src="/webstatic/Public/Home/js/survey_user_20191009.js?20181207" type="text/javascript"></script>
<script>
    $("#footerWapper").load("/common/global_footer")
</script>
<div id="jsWapper"></div>
<script>
    $("#jsWapper").load("/common/js")
</script>


</body>


<script>
    // 请在标签body之后加载
    (function (w, d, n, a, j) {
        w[n] = w[n] || function () {
            (w[n].a = w[n].a || []).push(arguments)
        };
        j = d.createElement('script');
        j.async = true;
        j.src = 'https://qiyukf.com/script/c8f4a5cb93d7b53c07a3467875022e64.js?subdomain=1';
        d.body.appendChild(j);
    })(window, document, 'ysf');

    // 设置用户身份
    ysf('onready', function () {
        ysf('config', {
            wp:1,
            robotShuntSwitch:1,
            robotId:5286982,
            groupid: '481839794'//客服组

        })
    })


    // 直接打开客服
    function ysf_open(type) {
        ysf('open');
    }
    window.addEventListener('scroll',()=>{
        //获取操作元素最顶端到页面顶端的垂直距离
        let scrollTop=document.body.scrollTop || document.documentElement.scrollTop
        if (scrollTop >=80) {
            $('.e5').addClass('e5-fixed')
        } else {
            $('.e5').removeClass('e5-fixed')
        }
    })
    //首页
    $('.right-one').click(()=>{
        let options = {
            top: 0,
            behavior: "smooth",
        };
        window.scrollTo(options);
    })
    //场景图库
    $('.right-two').click(()=>{
        let options = {
            top: $(".bodyWapper-one").offset().top,
            behavior: "smooth",
        };
        window.scrollTo(options);
    })
    // 参数与安装
    $('.right-three').click(()=>{
        let options = {
            top: $(".bodyWapper-two").offset().top+1000,
            behavior: "smooth",
        };
        window.scrollTo(options);
    })

    $('.right-four').click(function(){
        $('html, body').addClass('hidden')
        $('.overlay-cover-survey, .survey-user-wrapper').addClass('open');

    });
</script>
<script type="text/javascript">
    // 关闭留资
    function closeSurveyModal() {
        $('.survey-user-wrapper').removeClass('open')
        setTimeout(function(){
            $('.overlay-cover-survey').removeClass('open')
        }, 800)

        $('html, body').removeClass('hidden')
    }
    $(document).ready(function(){
        $('#zaixianzixun').click(function(){
            console.log('什么')
            $('html, body').addClass('hidden')
            $('.overlay-cover-survey, .survey-user-wrapper').addClass('open');

        });
        $('.close-survey-user-wrapper, .overlay-cover-survey').click(function(){
            closeSurveyModal()
        });
    });

</script>
</html>
