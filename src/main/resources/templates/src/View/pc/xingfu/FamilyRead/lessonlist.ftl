<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="moudle" content="Xinfu" />
    <meta name="fssName" content="harmony" />
    <title>线下课程列表</title>
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/animate.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/reset.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/global.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/list1.css">
    <#include "/src/View/common/js.ftl"/>
</head>
<body>
<#include "/src/View/components/header_new.ftl"/>
<div class="main">
   <#include "/src/View/pc/xingfu/xinfuTab.ftl"/>

    <div class="w1170 box2">
        <div class="pack clearfix" id="content">
            <#if res.data.list??>
                <#list  res.data.list as item>
                <!-- pic-word是上图下文，double-style是两列样式，pic-all是满屏图样式，color-all是背景样色样式 -->
                <!-- pic-word是上图下文 -->
                <a href="/xingfu/${item.contentId!''}.html" class="item pic-word">
                    <div class="list">
                        <div class="tips_bg {$art.tag_color}">${item.contentClassificationName!''}</div>
                        <div class="pic"
                             style="background-image:url(${item.coverUrl!''})"></div>
                        <div class="desc">
                            <div class="ellipsis-2">
                                <h1>${item.title!''}</h1>
                            </div>
                            <div class="ellipsis-3">
                                <p>${item.summary!''}</p>
                            </div>
                        </div>
                    </div>
                </a>
                </#list>
            </#if>
        </div>
       <#if res.data.total gt 15>
            <a target="_blank" href="javascript:void(0)" class="load-btn">加载更多</a>
       </#if>
        <!-- 底部面包屑导航 -->
        <div class="footer-nav">
            <a target="_blank" href="/xingfu/">方太幸福家</a>
            <i>&nbsp;>&nbsp;</i>
            <a target="_blank" href="/xingfu/cooking.html">美食烹饪</a>
            <i>&nbsp;>&nbsp;</i>
            <a target="_blank" href="javascript:void(0)">低卡美味</a>
        </div>

    </div>
</div>
<div id="footerWapper">
    <#include "/src/View/common/global_footer.ftl"/>
</div>

<#--<script type="text/javascript" src="/webstatic/Public/Home/js/jquery-1.8.0.min.js"></script>-->
<#--<script type="text/javascript" src="/webstatic/Public/Home/js/wow.js"></script>-->
<#--<script type="text/javascript" src="/webstatic/Public/Home/js/jquery.placeholder.min.js"></script>-->
<script type="text/javascript" src="/webstatic/Public/Home/js/global.js"></script>
<script type="text/javascript">
    $(function () {
        $('.xinfuHarmony').addClass('active').siblings().removeClass('active');
        var page = 2, limit = 15, _artType = 13, html = '';

        $('.load-btn').click(function () {
            var id= ${id!''};
            $.ajax({
                method: 'GET',
                url: '/content/getContentJson?id=' + id+'&page='+page +'&size=15',
                success: function (res) {
                    if (res ) {
                        getHtml(res.data)
                    } else {
                        console.warn('没有获取到数据')
                    }
                },

            });
        });

        function getHtml(data) {
            let _html='';
            if(data.data.list && data.data.list.length>0){
                data.data.list.forEach( function(item){
                    _html+='<a href="/xingfu/'+item.contentId+'.html" class="item pic-word">'+
                        '<div class="list">'+
                        '<div class="pic" style="background-image: url('+item.coverUrl+')"></div>'+
                        '<div class="desc">'+
                        '<h1 class="ellipsis-2">'+item.title+'</h1>'+
                        '<p class="ellipsis-3">'+item.summary+'</p>'+
                        '</div>'+
                        '</div>'+
                        '</a>'
                });
            }
            html = $('#content').html();
            $('#content').html(html + _html);
            var now =$('#content a').length;
            if (data.data == '' || data.data.list==null || now == data.data.total) {
                $('.load-btn').css('display', 'none');
            }
            page++;

    })
</script>
</body>
</html>

    


