<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣食无忧</title>
    <link rel="stylesheet" type="text/css" href="http://hsdev.efotile.com/webstatic/Public/Home/css/idangerous.swiper2.7.6.css">
    <link rel="stylesheet" type="text/css" href="http://hsdev.efotile.com/webstatic/Public/Home/css/animate.css">
    <link rel="stylesheet" type="text/css" href="http://hsdev.efotile.com/webstatic/Public/Home/css/xingfu/reset.css">
    <link rel="stylesheet" type="text/css" href="http://hsdev.efotile.com/webstatic/Public/Home/css/xingfu/global.css">
    <link rel="stylesheet" type="text/css"
        href="http://hsdev.efotile.com/webstatic/Public/Home/css/xingfu/life_home.css">
    <link rel="stylesheet" type="text/css"
        href="http://hsdev.efotile.com/webstatic/Public/Home/css/xingfu/research.css">
</head>

<body>
    <!-- 导航 -->
    <div class="nav">
        <div class="w1170 clearfix">
            <div class="left">
                <a target="_blank" href="/src/View/index.html?page=pc/xingfu/index/index">
                    <p>方太幸福家首页</p>
                </a>
                <a target="_blank" class="active" href="/src/View/index.html?page=pc/xingfu/KitchenLife/index">
                    <p>衣食无忧</p>
                </a>
                <a target="_blank" href="/src/View/index.html?page=pc/xingfu/Health/index">
                    <p>身心康宁</p>
                </a>
                <a target="_blank" href="/src/View/index.html?page=pc/xingfu/FamilyRead/index">
                    <p>相处和睦</p>
                </a>
                <a target="_blank" href="/src/View/index.html?page=pc/xingfu/Lecture/index">
                    <p>传家有道</p>
                </a>
            </div>
            <div class="right clearfix">
                <div class="line"></div>
                <a target="_blank" href="{:U('Personal/question')}" class="clearfix">
                    <img src="http://hsdev.efotile.com/webstatic/Public/Home/img/icon/<EMAIL>">
                    <!-- <p><?php echo ((session('nickname') !== null && (session('nickname') !== ""))?(session('nickname')):'登录');?></p> -->
                    <p>登录</p>

                </a>
            </div>
        </div>
    </div>
    <!-- 导航结束 -->
    <div class="main">
        <!-- <include file="Public:nav" /> -->

        <div class="health_title w1170"><span>衣食无忧</span>
            <!--幸福，就是一家人共度三餐四季，用健康食谱过幸福生<br>活，用健康食谱过幸福生活。-->
        </div>
        <div class="banner">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <a target="_blank" href="{$part_one.url}" class="swiper-slide"
                        style="display:block;background-image: url('http://fotilepc.oss-cn-hangzhou.aliyuncs.com/2020-02-10/5e40e1abadb86.jpg')">
                        <div class="desc">
                            <div class="text2">防疫指南</div>
                            <div class="text ellipsis-2">让家更安全，9个实用家居消毒方法</div>
                        </div>
                        <div class="banner-mask"></div>
                    </a>
                </div>
                <div class="pagination"></div>
            </div>
        </div>
        <div class="box1">
            <div class="w1170">
                <div class="pack clearfix">
                    <a target="_blank" href="/src/View/index.html?page=pc/xingfu/KitchenLife/Kitchendecoration" class="item">
                        <div class="list">
                            <div class="pic"
                                style="background-image: url(http://fotilepc.oss-cn-hangzhou.aliyuncs.com/2019-04-24/5cbfb9e088cff.jpg)">
                                <div class="desc">
                                    <h1>装修知识</h1>
                                    <p>装修也有讲究</p>
                                </div>
                            </div>
                            <div class="bg-mask"></div>
                        </div>
                    </a>
                    <a target="_blank" href="/src/View/index.html?page=pc/xingfu/KitchenLife/Kitchenbuy" class="item">
                        <div class="list">
                            <div class="pic"
                                style="background-image: url(http://fotilepc.oss-cn-hangzhou.aliyuncs.com/2019-05-08/5cd242114de9b.jpg)">
                                <div class="desc">
                                    <h1>厨电选购</h1>
                                    <p>厨电选购有技巧</p>
                                </div>
                            </div>
                            <div class="bg-mask"></div>
                        </div>
                    </a>
                    <a target="_blank" href="/src/View/index.html?page=pc/xingfu/KitchenLife/lifeskills" class="item">
                        <div class="list">
                            <div class="pic"
                                style="background-image: url(http://fotilepc.oss-cn-hangzhou.aliyuncs.com/2019-03-13/5c88a5daf2460.jpg)">
                                <div class="desc">
                                    <h1>生活技巧</h1>
                                    <p>怎样生活更精致</p>
                                </div>
                            </div>
                            <div class="bg-mask"></div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="box3">
            <div class="w1170">
                <div class="title clearfix">
                    <h1>厨房剧场</h1><a target="_blank" href="/src/View/index.html?page=pc/xingfu/FtVideo/videoList">查看更多</a>
                </div>
                <div class="pack clearfix">
                    <switch name="key">
                        <case value="0">
                            <a target="_blank" href="/src/View/index.html?page=pc/xingfu/FtVideo/detail" class="lt"
                                style="background-image: url(http://fotilepc.oss-cn-hangzhou.aliyuncs.com/2019-11-22/5dd73f57e80f7.jpg)">
                                <img src="http://hsdev.efotile.com/webstatic/Public/Home/img/icon/video_icon.png"
                                    class="video-btn videoplay" data-id="{$fv_one.play_id}">
                                <div class="desc">
                                    <h2>装修案例</h2>
                                    <div class="name clearfix">
                                        <p>建筑设计师陆启水：最好的作品，是我家</p>
                                        <span>299 播放</span>
                                    </div>
                                </div>
                            </a>
                        </case>
                    </switch>

                    <div class="rt">
                        <div class="content">
                            <a target="_blank" href="{:U('FtVideo/detail',array('id'=>$fv_two['id']))}"
                                class="list box-size clearfix">
                                <div class="name">
                                    <h2>装修案例</h2>
                                    <h1 class="ellipsis-2">幸福新厨房 | 爱工作，也爱工作餐</h1>
                                </div>
                                <img src="http://hsdev.efotile.com/webstatic/Public/Home/img/icon/video_icon.png"
                                    class="video-btn videoplay" data-id="{$fv_two.play_id}">
                            </a>
                            <a target="_blank" href="/src/View/index.html?page=pc/xingfu/FtVideo/detail"
                                class="list box-size clearfix">
                                <div class="name">
                                    <h2>装修案例</h2>
                                    <h1 class="ellipsis-2">幸福新厨房 | 爱工作，也爱工作餐</h1>
                                </div>
                                <img src="http://hsdev.efotile.com/webstatic/Public/Home/img/icon/video_icon.png"
                                    class="video-btn videoplay" data-id="{$fv_two.play_id}">
                            </a>
                            <a target="_blank" href="/src/View/index.html?page=pc/xingfu/FtVideo/detail"
                                class="list box-size clearfix">
                                <div class="name">
                                    <h2>装修案例</h2>
                                    <h1 class="ellipsis-2">幸福新厨房 | 爱工作，也爱工作餐</h1>
                                </div>
                                <img src="http://hsdev.efotile.com/webstatic/Public/Home/img/icon/video_icon.png"
                                    class="video-btn videoplay" data-id="{$fv_two.play_id}">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--视频弹窗-->
    <div class="video_player">
        <div class="video_mask"></div>
        <div class="video_close"><img src="__IMG__/icon/video_close.png"></div>
        <div class="video_content" id="video_content"></div>
    </div>

    <script type="text/javascript" src="http://hsdev.efotile.com/webstatic/Public/Home/js/idangerous.swiper2.7.6.min.js"></script>
    <script type="text/javascript" src="http://hsdev.efotile.com/webstatic/Public/Home/js/wow.js"></script>
    <script type="text/javascript" src="http://hsdev.efotile.com/webstatic/Public/Home/js/global.js"></script>
    <script src="http://hsdev.efotile.com/webstatic/Public/static/tencentvideo/tvp.player_v2_jq.js"></script>
    <script type="text/javascript">
        $(function () {

            //轮播图
            var mySwiper = new Swiper('.swiper-container', {
                pagination: '.pagination',
                paginationClickable: true,
                autoplay: 2000,
                loop: true,
                speed: 800,
                autoplayDisableOnInteraction: false,
            })
            $('.swiper-slide').css('height','600px');
            // 初始化腾讯播放器
            var _videoTen = new tvp.VideoInfo();
            var _playerTen = new tvp.Player();

            function videoPlay(id) {
                _videoTen.setVid(id);
                var _img = _videoTen.getVideoSnap();//获取视频截图，返回一个数组，包含了几种规格的视频截图 URL 地址
                _playerTen.create({
                    width: "100%",
                    height: "600",
                    video: _videoTen,
                    modId: "video_content",
                    autoplay: true,
                    pic: _img[2],
                    isHtml5ShowPlayBtnOnPause: true,//暂停的时候是否显示播放按钮
                    isHtml5ShowPosterOnEnd: true//HTML5 播放器播放完毕是否显示 poster
                });
            }

            $(".videoplay").on('click', function (e) {
                e.preventDefault();
                var id = $(this).attr('data-id');
                $(".video_player").fadeIn();
                videoPlay(id);
                setTimeout(function () {
                    _playerTen.play();
                }, 300);
            })
            $(".video_player .video_close,.video_player .video_mask").on('click', function () {
                _playerTen.pause();
                $(".video_player").fadeOut();
            });

        })
    </script>

</body>

</html>