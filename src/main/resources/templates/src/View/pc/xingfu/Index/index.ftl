<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="moudle" content="Xinfu" />
    <title>方太幸福家-FOTILE方太厨电官网</title>
    <meta name="Keywords" content="方太幸福家"/>
    <meta name="fssName" content="happy" />
    <meta name="description" content="方太集团始终专注于高端嵌入式厨房电器的研发和制造，致力于为追求高品质生活的人们提供优质的产品和服务，打造健康环保有品位有文化的生活方式，让千万家庭享受更加幸福安心的生活。"/>
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/animate.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/reset.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/global.css">
    <link rel="stylesheet" type="text/css" href="/webstatic/Public/Home/css/xingfu/homepage.css">
    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.8.2/skins/default/aliplayer-min.css" />
    <script charset="utf-8" type="text/javascript" src="https://g.alicdn.com/de/prismplayer/2.8.2/aliplayer-min.js"></script>
    <#include "/src/View/common/js.ftl"/>
</head>

<body>
<#--<#include "/src/View/common/header.ftl"/>-->
<#include "/src/View/components/header_new.ftl"/>
<!-- 导航 -->
<#include "/src/View/pc/xingfu/xinfuTab.ftl"/>
<!-- 导航结束 -->
<div class="main">
    <#if  adver1??>
    <div class="banner clearfix">
            <#list adver1 as item>
                <#if item_index==0>
                    <a href="<#if item.jumpType=='01'> ${item.url!''}<#elseif item.jumpType=='10'>javascript:void(0);<#else >/xingfu/${item.contentId!''}.html</#if>" class="left">
                        <div class="pic"
                             style="background-image: url(${item.pictureList[0].coverUrl1!''})">
                        </div>
                        <div class="mask">
                            <div class="desc">
                                <h1>${item.name!''}</h1>
                                <p>${item.summary!''}</p>
                            </div>
                        </div>
                        <div class="bg-mask"></div>
                    </a>
                </#if>
            </#list>
            <div class="right clearfix">
                <#list adver1 as li>
                    <#if li_index gt 0  && li_index lt 6>
                        <a href="<#if li.jumpType=='01'> ${li.url!''}<#elseif li.jumpType=='10'>javascript:void(0);<#else >/xingfu/${li.contentId!''}.html</#if>" class="item">
                            <div class="pic"
                                 style="background-image: url(${li.pictureList[0].coverUrl1!''})">
                            </div>
                            <div class="mask">
                                <div class="desc">
                                    <h1>${li.name!''}</h1>
                                    <p>${ li.summary!''}</p>
                                </div>
                            </div>
                            <div class="bg-mask"></div>
                        </a>
                    </#if>
                </#list>
            </div>

    </div>
    </#if>

    <div class="box2">
        <div class="w1170">
            <h1 class="title">热门</h1>
            <div class="pack clearfix">

                <#if adver2??>
                    <#list adver2 as item>
                        <#if item_index==1>
                            <a href="<#if item.jumpType=='01'> ${item.url!''}<#elseif item.jumpType=='10'>javascript:void(0);<#else >/xingfu/${item.contentId!''}.html</#if>"
                               class="item double-style">
                                <div class="list clearfix">
                                    <div class="tips_bg {$art.tag_color}">${item.name!''}</div>
                                    <div class="lt">
                                        <div class="desc">
                                            <h1>${item.title!''}</h1>
                                            <p class="ellipsis-3">${ item.summary!''}</p>
                                        </div>
                                        <span class="btn">阅读详情</span>
                                    </div>
                                    <div class="rt"
                                         style="background-image:url(${item.pictureList[0].coverUrl1!''})">
                                    </div>
                                </div>
                            </a>
                        <#else>
                            <a href="<#if item.jumpType=='01'> ${item.url!''}<#elseif item.jumpType=='10'>javascript:void(0);<#else >/xingfu/${item.contentId!''}.html</#if>"
                               class="item pic-word">
                                <div class="list">
                                    <div class="tips_bg blue">${item.name!''}</div>
                                    <div class="pic"
                                         style="background-image: url(${item.pictureList[0].coverUrl1!''})">
                                    </div>
                                    <div class="desc">
                                        <h1 class="ellipsis-2">${item.title!''}</h1>
                                        <p class="ellipsis-3">${ item.summary!''}</p>
                                    </div>
                                </div>
                            </a>
                        </#if>
                    </#list>
                </#if>
            </div>
        </div>
    </div>
    <div class="box3">
        <div class="w1170">
            <div class="title clearfix">
                <h1>厨房剧场</h1><a target="_blank"
                                href="/xingfu/FtVideo/videoList.html">查看更多</a>
            </div>
            <div class="pack clearfix">
                <#if adver3??>
                    <volist name="re_videos" id="fv_one">
                        <#list adver3 as item>
                            <#if item_index==0>
                                <a target="_blank" href="<#if item.jumpType=='01'> ${item.url!''}<#elseif item.jumpType=='10'>javascript:void(0);<#else >/xingfu/ftvideo/${item.contentId!''}.html</#if>" class="lt"
                                   style="background-image: url(${item.pictureList[0].coverUrl1!''})">
                                    <img src="/webstatic/Public/Home/img/icon/video_icon.png"
                                         class="video-btn videoplay" data-url="${item.resourcesUrl!''}" data-id="{$fv_one.play_id}">
                                    <div class="desc">
                                        <h2>${item.name!''}</h2>
                                        <div class="name clearfix">
                                            <p>${item.summary!''}</p>
                                        </div>
                                    </div>
                                </a>
                            </#if>
                        </#list>
                    </volist>
                    <div class="rt">

                        <div class="content">
                            <#list adver3 as li>
                                <#if li_index gt 0 && li_index lt 6>
                                <a target="_blank" href="<#if li.jumpType=='01'> ${li.url!''}<#elseif li.jumpType=='10'>javascript:void(0);<#else >/xingfu/ftvideo/${li.contentId!''}.html</#if>"
                                   class="list box-size clearfix">
                                    <div class="name">
                                        <h2>${li.name!''}</h2>
                                        <h1 class="ellipsis-2">${li.summary!''}</h1>
                                    </div>
                                    <img src="/webstatic/Public/Home/img/icon/video_icon.png" data-url="${li.resourcesUrl!''}"
                                         class="video-btn videoplay">
                                </a>
                                </#if>
                            </#list>
                        </div>

                    </div>
                </#if>
            </div>
        </div>
    </div>
    <div class="box4">
        <div class="w1170">
            <h1 class="title">所有（${res.totalCounts!''}）</h1>
            <div class="menu" id="menu">
                <div class="sub active" data-id="${map['居家装修']!''}">居家装修 (${res.kitchenlifeCounts!''})</div>
                <div class="sub" data-id="${map['健康养生']!''}">健康养生 (${res.healthCounts!''})</div>
                <div class="sub" data-id="${map['美食烹饪']!''}">美食烹饪 (${res.familyreadCounts!''})</div>
                <div class="sub" data-id="${map['幸福成长']!''}">幸福成长 (${res.lectureCounts!''})</div>
            </div>
            <div class="pack clearfix" id="content">
                <#if res.pageInfo??>
                <#if res.pageInfo.list??>
                <#list res.pageInfo.list as item>
                    <a href="/xingfu/${item.id!''}.html"
                       class="item pic-word">
                        <div class="list">
                            <div class="tips_bg cyan">${item.contentClassificationName!''}</div>
                            <div class="pic"
                                 style="background-image: url(${item.coverUrl!''})">
                            </div>
                            <div class="desc">
                                <h1 class="ellipsis-2">${item.title!''}</h1>
                                <p class="ellipsis-3">${item.summary!''}</p>
                            </div>

                        </div>
                    </a>
                </#list>
                </#if>
                </#if>
            </div>

            <a href="javascript:void(0)" class="load-btn" <#if (res.kitchenlifeCounts gt 10)>style="display:block"<#else>style="display:none"</#if>>加载更多</a>

        </div>
    </div>
</div>
<div id="footerWapper">
    <#include "/src/View/common/global_footer.ftl"/>
</div>
<!--视频弹窗-->
<div class="video_player">
    <div class="video_mask"></div>
    <div class="video_close"><img src="/webstatic/Public/Home/img/icon/video_close.png">
    </div>
    <div class="video_content" id="video_content">
        <div>
            <video id="myVideo"  autoplay preload="auto" style='width: 100%;height: 100%'>
                <source id="source" src="" type="video/mp4">
            </video>
        </div>

    </div>
</div>
<!--右侧浮层-->
<div class="right_button">下载方太幸福家APP
    <div class="popup"><img src="https://hsimage.fotile.com/202401111522452631225.png"></div>
</div>
<script src="/webstatic/Public/Home/new/js/swiper-3.4.2.min.js"></script>
<!-- <script type="text/javascript" src="/webstatic/Public/Home/js/idangerous.swiper2.7.6.min.js"></script> -->
<script src="/webstatic/Public/static/tencentvideo/tvp.player_v2_jq.js"></script>
<script src='/webstatic/Public/static/tencentvideo/h5e_v1.4.18.min.js' id='h5e-node'></script>
<script src="/webstatic/Public/Home/js/json2.js"></script>
<script type="text/javascript">
    $('.renwenBtn').addClass('on').siblings().removeClass('on');

    $(function () {
        var page = 2, limit = 10, _index = 0, html = '';
        $('#menu .sub').eq(0).addClass('active');
        $("#menu .sub").click(function () {
            $('.load-btn').css('display', 'block');
            _index = $(this).index();
            page = 1;
            $(this).addClass('active').siblings().removeClass('active');
            var id = $(this).attr('data-id');
            getData(id);
        });
        $('.load-btn').click(function () {
            var id = $('.menu').find('.active').attr('data-id');
            getData(id);
        });

        function getData(id) {
            $.ajax({
                method: 'GET',
                url: '/content/getContentJson?id=' + id + '&page=' + page,
                success: function (res) {
                    if (res) {
                        getHtml(res.data)
                    } else {
                        console.warn('没有获取到cate为' + cate + '的类目菜单')
                    }

                },
                fail: function () {

                }
            });
        }

        function getHtml(data) {
            let _html = '';
            if (data.list && data.list.length > 0) {
                data.list.forEach(function (item) {
                    _html += '<a href="/xingfu/' + item.id + '.html" class="item pic-word">' +
                        '<div class="list">' +
                        '<div class="tips_bg cyan">' + item.contentClassificationName + '</div>' +
                        '<div class="pic" style="background-image: url(' + item.coverUrl + ')"></div>' +
                        '<div class="desc">' +
                        '<h1 class="ellipsis-2">' + item.title + '</h1>' +
                        '<p class="ellipsis-3">' + item.summary + '</p>' +
                        '</div>' +
                        '</div>' +
                        '</a>'
                });
            }

            if (page == 1) {
                //无数据显示html
                if (!data || data.list == null || data.list.length == 0) {
                    $('#content').html('<div style="text-align: center">暂无该分类文章</div>');
                    $('.load-btn').css('display', 'none');
                    return false;
                }
                //首次切换显示数据
                $('#content').html(_html);
                let now= $('#content a').length;
                if (now < data.total) {
                    $('.load-btn').css('display', 'block');
                }else{
                    $('.load-btn').css('display', 'none');
                }
            } else {
                html = $('#content').html();
                $('#content').html(html + _html);
                let now= $('#content a').length;
                if ( now < data.total) {
                    $('.load-btn').css('display', 'block');
                }else{
                    $('.load-btn').css('display', 'none');
                }
            }
            page++;
        }

        //轮播图
        // var mySwiper = new Swiper('.swiper-container', {
        //     pagination: '.pagination',
        //     paginationClickable: true,
        //     autoplay: 2000,
        //     loop: true,
        //     speed: 800,
        //     autoplayDisableOnInteraction: false,
        // })

        // 初始化腾讯播放器
        var _videoTen = new tvp.VideoInfo();
        var _playerTen = new tvp.Player();

        function videoPlay(id) {
            _videoTen.setVid(id);
            var _img = _videoTen.getVideoSnap();//获取视频截图，返回一个数组，包含了几种规格的视频截图 URL 地址
            _playerTen.create({
                width: "100%",
                height: "600",
                video: _videoTen,
                modId: "video_content",
                autoplay: true,
                pic: _img[2],
                isHtml5ShowPlayBtnOnPause: true,//暂停的时候是否显示播放按钮
                isHtml5ShowPosterOnEnd: true//HTML5 播放器播放完毕是否显示 poster
            });
        }
        //当前页全屏播放
        var player;
        $(".videoplay").on('click', function (e) {
            e.preventDefault();
            var id = $(this).attr('data-id');
            var url = $(this).attr('data-url');
            // document.getElementById("myVideo").src=url ;
            // document.getElementById("myVideo").play();
            $(".video_player").fadeIn();
            if(player){
                player.play()
            }else{
                player = new Aliplayer({
                    id: "myVideo",
                    height:"100%",
                    width: "100%",
                    autoplay: true,
                    rePlay:true,
                    useH5Prism:true,
                    isLive:false,//是不是直播
                    controlBarVisibility:"hover",
                    //支持播放地址播放,此播放优先级最高
                    source: url,
                    function(player){
                        //先静音然后播放
                        player.play()

                    },
                });
            }
        });
        $(".video_player .video_close,.video_player .video_mask").on('click', function () {
            // _playerTen.pause();
            player.pause();
            player.source='';
            player=undefined;
            $(".video_player").fadeOut();
            // document.getElementById("myVideo").pause();
        });

        // 右侧弹窗二维码

        $(".right_button").on("click", function (e) {
            $(".right_button .popup").fadeToggle();
            e.stopPropagation();
        })
        $(document).on("click", function (e) {
            $(".right_button .popup").fadeOut();
            e.stopPropagation();
        })

    })

    $('.huanxinBtn').hover(()=>{
        $('.huanxinBtn>a').css("color",'#000')
        $('.huanxinBtn>a').css("font-weight",'600')
    },()=>{
        $('.huanxinBtn>a').css("color",'#1a1a1a')
        $('.huanxinBtn>a').css("font-weight",'400')
    })



    $('.mallBtn').hover(()=>{
        $('.mallBtn>a').css("color",'#000')
        $('.mallBtn>a').css("font-weight",'600')
    },()=>{
        $('.mallBtn>a').css("color",'#1a1a1a')
        $('.mallBtn>a').css("font-weight",'400')
    })

    $('.cpBtn').hover(()=>{
        $('.cpBtn>a').css("color",'#000')
        $('.cpBtn>a').css("font-weight",'600')
    },()=>{
        $('.cpBtn>a').css("color",'#1a1a1a')
        $('.cpBtn>a').css("font-weight",'400')
    })



    $('.serBtn').hover(()=>{
        $('.serBtn>a').css("color",'#000')
        $('.serBtn>a').css("font-weight",'600')
    },()=>{
        $('.serBtn>a').css("color",'#1a1a1a')
        $('.serBtn>a').css("font-weight",'400')
    })

    $('.renwenBtn').hover(()=>{
        $('.renwenBtn>a').css("color",'#000')
        $('.renwenBtn>a').css("font-weight",'600')
    },()=>{
        $('.renwenBtn>a').css("color",'#1a1a1a')
        $('.renwenBtn>a').css("font-weight",'400')
    })


</script>
</body>

</html>