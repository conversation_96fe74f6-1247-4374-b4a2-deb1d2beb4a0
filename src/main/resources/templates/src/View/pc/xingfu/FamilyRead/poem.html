<extend name="Public:base"/>

<block name="title">{$title}</block>
<block name="css">
    <link rel="stylesheet" type="text/css" href="__CSS__/animate.css">
    <link rel="stylesheet" type="text/css" href="__CSS__/reset.css">
    <link rel="stylesheet" type="text/css" href="__CSS__/global.css">
    <link rel="stylesheet" type="text/css" href="__CSS__/audioDetails.css">
</block>

<block name="main">
    <div class="main">
        <include file="Public:nav"/>
        <div class="bg">
            <div class="box clearfix">
                <div class="left" id="carouselPack"
                     style="background-image: url({$poem.path});filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='{$poem.path}', sizingMethod='scale');">
                    <div class="bg-mask"></div>
                    <div class="status">
                        <div class="top">
                            <div class="wrap">
                                <audio preload="preload">
                                    <source src="{$poem.audio.audio_path}" type="audio/mpeg">
                                </audio>
                                <div class="play playaudio"></div>
                                <div class="desc">
                                    <h1>{$poem.title}</h1>
                                    <h2>{$poem.dynasty} {$poem.author}</h2>
                                    {$poem.content}
                                </div>
                            </div>
                        </div>
                        <div class="bot">
                            <div class="pic"
                                 style="background-image: url({$poem.audio.pic_path});filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='{$poem.audio.pic_path}', sizingMethod='scale');"></div>
                            <p>音频来源：小李白会员@{$poem.audio.author}</p>
                        </div>
                    </div>
                </div>
                <div class="right">
                    <div class="word"><img src="__IMG__/audio/audio_word.png"></div>
                    <h1 class="title">和孩子一起读经典，构建健康亲子关系</h1>
                    <p class="desc">小时候读过的诗，会在心里留下深深的烙印。任时光匆匆过，诗歌依然连接着一代又一代。诵读是最好的文化传承，亦是孩子认识世界的启蒙。</p>
                    <div class="code">
                        <div class="lt"><img src="__IMG__/audio/audio_code.png"></div>
                        <div class="rt">
                            <h1>扫描识别图中二维码</h1>
                            <p>关注<i>“我陪孩子读经典”</i>公众号</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部面包屑导航 -->
        <div class="box-nav">
            <div class="w1170">
                <div class="footer-nav">
                    <a target = "_blank"href="{:U('Index/index')}">方太幸福家</a>
                    <i>&nbsp;>&nbsp;</i>
                    <a target = "_blank"href="{:U('FamilyRead/index')}">相处和睦</a>
                    <i>&nbsp;>&nbsp;</i>
                    <a target = "_blank"href="javascript:void(0)">我是小李白</a>
                </div>
            </div>
        </div>
    </div>
</block>

<block name="js">
    <script type="text/javascript" src="__JS__/jquery-1.8.0.min.js"></script>
    <script type="text/javascript" src="__JS__/wow.js"></script>
    <script type="text/javascript" src="__JS__/jquery.placeholder.min.js"></script>
    <script type="text/javascript" src="__JS__/global.js"></script>
    <script type="text/javascript">
        $(function () {
            $("#carouselPack").on('click', '.playaudio', function () {
                var _this = $(this);
                _this.siblings("audio").on('ended', function () {
                    _this.removeClass('active');
                });
                $("audio").trigger("pause");
                if (_this.hasClass('active')) {
                    _this.siblings("audio").trigger("pause");
                    _this.removeClass('active');
                } else {
                    _this.siblings("audio").trigger("play");
                    $(".playaudio").removeClass('active');
                    _this.addClass('active');
                }
            });
        })
    </script>
</block>