<meta charset="utf-8" />
<title><block name="title">方太官网 - FOTILE方太厨电官方网站</block></title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="renderer" content="webkit">
<meta name="referrer" content="unsafe-url">
<meta name="Keywords" content="<block name='keywords'>方太，方太油烟机，方太燃气灶，方太消毒柜，方太蒸箱，方太烤箱，方太微波炉，方太水槽洗碗机，方太热水器</block>" />
<meta name="Description" content="<block name='description'>方太专注高端厨房电器，拥有吸油烟机、嵌入式灶具、嵌入式消毒柜、蒸微一体机、嵌入式蒸箱、嵌入式烤箱、嵌入式微波炉以及原创发明的水槽洗碗机、燃气热水器等九大产品线。进入方太厨房电器官方网站，了解更多方太家用电器产品详情，并获得方太专业售前、售后服务。</block>" />

<script src="https://libs.baidu.com/jquery/2.0.0/jquery.js"></script>
<script type='text/javascript'>
    // // 监测代码 20180914 添加
    (function (root) {
        root._tt_config = true;
        var ta = document.createElement('script');
        ta.type = 'text/javascript';
        ta.async = true;
        ta.src = document.location.protocol + '//' +
            's1.pstatp.com/bytecom/resource/track_log/src/toutiao-track-log.js';
        ta.onerror = function () {
            var request = new XMLHttpRequest();
            var web_url = window.encodeURIComponent(window.location.href);
            var js_url = ta.src;
            var url = '//ad.toutiao.com/link_monitor/cdn_failed?web_url=' + web_url + '&js_url=' + js_url +
                '&convert_id=1608029767946276';
            request.open('GET', url, true);
            request.send(null);
        }
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ta, s);
    })(window);

    window["_BFD"] = window["_BFD"] || {};

    try {
        var EFOTILE_IS_LOGIN = <?php echo intval(!empty($_SESSION['efotile_user'])); ?>;

        if (EFOTILE_IS_LOGIN) {

            console.log("已登录");
            var aaa1 = window.localStorage.EFOTILE_USER_INFO;
            var aaa2 = JSON.parse(aaa1);

            _BFD.USER_INFO = {
                "client_id": "Cfotile", //客户标识，埋点人员提供
                "appkey": "f3beccd81c033ba756635995b65dbc08", //应用标示，埋点人员提供
                "pv_type": "0", //页面埋点请求方式，0为自动发送，1为手动调用
                "title": "", //当前页面标题
                "pos": true, //是否启用鼠标点击，值为true/false
                "lc": true, //是否启用链接点击，值为true/false
                "open_id": "", //当前站点在微信加载时，回传open_id
                "phone_number": aaa2.Tel, //登录用户手机号，未登录回传空字符串
                "user_id": aaa2.ID //登录用户唯一标识，如果未登录就回传空字符串
            };
        } else {
            _BFD.USER_INFO = {
                "client_id": "Cfotile", //客户标识，埋点人员提供
                "appkey": "f3beccd81c033ba756635995b65dbc08", //应用标示，埋点人员提供
                "pv_type": "0", //页面埋点请求方式，0为自动发送，1为手动调用
                "title": "", //当前页面标题
                "pos": true, //是否启用鼠标点击，值为true/false
                "lc": true, //是否启用链接点击，值为true/false
                "open_id": "", //当前站点在微信加载时，回传open_id
                "phone_number": "", //登录用户手机号，未登录回传空字符串
                "user_id": "" //登录用户唯一标识，如果未登录就回传空字符串
            };
        }
        _BFD.script = document.createElement("script");
        _BFD.script.type = "text/javascript";
        _BFD.script.async = true;
        _BFD.script.charset = "utf-8";
        _BFD.script.src = ('https://caiji.efotile.com/js/web.js');
        // js代码路径，由部署人员提供
        document.getElementsByTagName("head")[0].appendChild(_BFD.script);
        setTimeout(function () {}, 1000);
        window.addEventListener("loadscript", function () {

            bcore.onGidReady(function (gid) {
                console.log("gid:" + gid)
                window.gio('page.set', 'gid', gid);

            }, {
                /***静态资源路径，包括 clientData.html***/
                assetsPath: 'https://caiji.efotile.com/js/web.js',
                /***gid 服务地址***/
                stdIdURL: _BFD.webservice_src + "StdID&appkey=" + _BFD.USER_INFO.appkey
            })
        });

    } catch (e) {
        console.log("京东获取gid出错" + e);
    };

    $(document).ready(function () {
        var wxopenid = getcookie('fts_openid');
        window.gio('page.set', 'open_id', wxopenid);
    })

    function getcookie(name) {
        var strcookie = document.cookie;
        var arrcookie = strcookie.split("; ");
        for (var i = 0; i < arrcookie.length; i++) {
            var arr = arrcookie[i].split("=");
            if (arr[0] == name) return decodeURIComponent(arr[1]); //增加对特殊字符的解析
        }
        return "";
    }
</script>


<!-- End GrowingIO Analytics code version: 2.1 -->

<script type='text/javascript'>
    ! function (e, t, n, g, i) {
        e[i] = e[i] || function () {
            (e[i].q = e[i].q || []).push(arguments)
        }, n = t.createElement("script"), tag = t.getElementsByTagName("script")[0], n.async = 1, n.src = (
            'https:' ==
            document.location.protocol ? 'https://' : 'http://') + g, tag.parentNode.insertBefore(n, tag)
    }(window, document, "script", "assets.giocdn.com/2.1/gio.js", "gio");
    gio('init', '8145b89552224e77', {});
    gio('send');
</script>


<script type="text/javascript">
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o), m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');
    ga('create', 'UA-17555282-1', 'auto', {
        'allowLinker': true
    });
    ga('require', 'linker');
    ga('linker:autoLink', ['efotile.com']);
    ga('send', 'pageview');

    var _hmt = _hmt || [];
    (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?956adefb0eb473d0cd054107659ab6fd";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>

<link rel="shortcut icon" href="/Public/favicon.ico" />
<link href="__CSS__/global.min.css?v=20190924" rel="stylesheet" type="text/css" />
<block name="css"></block>
