<!DOCTYPE HTML>
<html>
<head>
<include file="Public/head_login_reg"/>
</head>
<body>
<!-- Ptengine 监测代码 ************  -->
<script type="text/javascript">
    window._pt_lt = new Date().getTime();
    window._pt_sp_2 = [];
    _pt_sp_2.push('setAccount,13cf26c9');
    var _protocol = (("https:" == document.location.protocol) ? " https://" : " http://");
    (function() {
        var atag = document.createElement('script'); atag.type = 'text/javascript'; atag.async = true;
        atag.src = _protocol + 'js.ptengine.cn/13cf26c9.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(atag, s);
    })();
</script>

<!-- 头部 -->
<include file="Public/header_login_reg"/>
<!-- /头部 -->

<!-- 主体 -->
<include file="Public/body_login_reg"/>
<!-- /主体 -->

<!-- 底部 -->
<include file="Public/footer_login_reg"/>
<!-- /底部 -->
<include file="Public/js_login_reg"/>

<script>
function set_utm_source (link_lists) {
    var Request = {
        QueryString : function(item){
            var svalue = location.search.match(new RegExp("[\?\&]" + item + "=([^\&]*)(\&?)","i"));
            return svalue ? svalue[1] : svalue;
        }
    }

    link_lists.each(function(i,v){
        var href = $(v).attr('href');
        if (!href) {return}
        if (href) { //针对a标签
            if (href.indexOf('fotile-cp-') > -1) { //商城的产品无需重新设置utm_source
                return false;
            }
            if(href.indexOf('efotile') > -1){
                var utm_source = '';
                var localUrl = href.split('?')[0];
                // console.log(href);
                utm_source_cookie = getCookie('onethink_home_utm_source');

                if(utm_source_cookie){
                    if (href.indexOf('utm_source') > -1) {
                        $(v).attr('href', href.replace(/(utm_source=)[^&]+/, '$1'+utm_source_cookie));
                    } else {
                        if (href.indexOf('gw.efotile.com') > -1) { //针对购物车等
                            $(v).attr('href', href + '&utm_source=' + utm_source_cookie);
                        } else {
                            $(v).attr('href', localUrl + '?utm_source=' + utm_source_cookie);
                        }
                    }

                }else{
                    var utm = Request.QueryString('utm_source')||false;

                    if(utm){
                        if (href.indexOf('utm_source') > -1) {
                            $(v).attr('href', href.replace(/(utm_source=)[^&]+/, '$1'+utm));
                        } else {
                            if (href.indexOf('gw.efotile.com') > -1) { //针对购物车等
                                $(v).attr('href', href + '&utm_source=' + utm);
                            } else {
                                $(v).attr('href', localUrl + '?utm_source=' + utm);
                            }
                        }
                        
                        document.cookie = 'onethink_home_utm_source=' + escape(utm);
                    }else{
                        if(href.indexOf('utm_source=fotile-sj') <= -1){
                            if (href.indexOf('utm_source') > -1) {
                                $(v).attr('href', href.replace(/(utm_source=)[^&]+/, '$1'+'fotile-dh'));
                            } else {
                                if (href.indexOf('gw.efotile.com') > -1) {
                                    $(v).attr('href', href + '&utm_source=fotile-dh');
                                } else {
                                    $(v).attr('href', localUrl + '?utm_source=fotile-dh');
                                }
                            }
                        }
                    }
                }
            }
        }

    });
}

$(function () {
    set_utm_source($('body a'));
    // set_utm_source($('.utm-source')); //header与footer
    //    set_utm_source($('#head-hot-link a')); //hot链接
});


</script>

</body>
</html>

