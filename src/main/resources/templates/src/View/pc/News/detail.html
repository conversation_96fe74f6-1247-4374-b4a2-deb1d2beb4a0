<extend name="Public/common"/>
	<!-- 判断title是否设置 -->
	<block name="title"><if condition="$news['page_title'] != '' ">{$news['page_title']}<else />{$news['title']} - FOTILE方太厨电官方网站</if></block>
	<!-- 判断keywords是否设置 -->
	<block name="keywords"><if condition="$news['keywords'] != '' ">{$news['keywords']}<else />方太，方太油烟机，方太燃气灶，方太消毒柜，方太蒸箱，方太烤箱，方太微波炉，方太水槽洗碗机，方太热水器</if></block>
	<!-- 判断description是否设置 -->
	<block name="description"><if condition="$news['description'] != '' ">{:cut_contents($news['description'], 50)}<else />{:cut_contents($news['content'], 50)}</if></block>
<block name="css">
	<link href="__CSS__/news.css?20190123" rel="stylesheet" type="text/css" />
</block>

<block name="script">
	<script src="__JS__/news.js?201905301415" type="text/javascript"></script>
</block>

<block name="body">
	<div class="w1170">
		<div class="path" style="border-top:none;">
			<a href="/news">媒体中心</a> <u>&gt;</u>
			<a href="/news">方太资讯</a>
			<u>&gt;</u>
			<a href="javascript:;">{$news.title}</a>
		</div>
		<div class="helpDetail2">
			<div class="title">
				<h2>{$news.title}</h2>
				<p>
					<span class="icon1">{$news.type|get_news_cate}</span>
					<u>|</u>
					<if condition="$news['id'] == 106 ">
							2018年6月20日
					<else/>
							{$news.create_date|date='Y年m月d日',###}
					</if>
				</p>
			</div>
			<div class="main m2">
				<style>
				.content-wrap {
					padding-right: 285px;
				}
				.content-wrap:after {
					content: "";
					height: 0;
					line-height: 0;
					display: block;
					visibility: hidden;
					clear: both;
				}
				.content-wrap-main {
					float: left;
					width: 100%;
				}
				.content-wrap-main .info {
					padding-right: 45px;
					border-right: 1px solid #DEDEDE;
					padding-bottom: 60px;
				}
				.content-wrap-side {
					float: right;
					position: relative;
    			right: -285px;
					width: 285px;
    			margin-left: -285px;
				}
				.content-wrap-side .pack {
					margin-left: 40px;
				}
				.recommend-panel .tit h4 {
					font-size: 14px;
					color: #838D8F;
					font-weight: 600;
					line-height: 20px;
					margin-bottom: 15px;
				}
				.recommend-panel li {
					margin-bottom: 12px;
					overflow: hidden;
				}
				.recommend-panel li a {
					display: block;
				}
				.recommend-panel li a img {
					float: left;
					width: 80px;
					border-radius: 3px;
					margin-bottom: 0;
					margin-top: 0;
					margin-right: 8px;
				}
				.recommend-panel li a p {
					font-size: 14px;
					color: #242424;
					line-height: 18px;
				}
				</style>
				<div class="content-wrap">
					<div class="content-wrap-main">
						<div class="info">{$news.content}</div>
					</div>
					<div class="content-wrap-side">
						<div class="pack">
							<div class="recommend-panel pinned">
								<div class="tit">
									<h4>相关文章</h4>
								</div>
								<ul>
									<foreach name="s_res" item="v">
										<li>
											<a href="/news/{$v['id']}.html" target="_blank">
												<img src="{$v.cover_img}" alt="{$v.title}">
												<p>{$v.title}</p>
											</a>
										</li>
									</foreach>
								</ul>
							</div>
						</div>
					</div>
				</div>

				<div class="pageBox">
					<notempty name="tags">
						<div class="tag">
							<foreach name="tags" item="v">
								<a href="/tag/{$v['id']}.html">{$v['searchkey']}</a>
							</foreach>
						</div>
					</notempty>
					<div class="link">
						<p>
							上一篇：
							<notempty name="prev">
								<a href="/news/{$prev['id']}.html">{$prev.title}</a>
								<else />
								没有了...
							</notempty>
						</p>
						<p>
							下一篇：
							<notempty name="next">
								<a href="/news/{$next['id']}.html">{$next.title}</a>
								<else/>
								没有了...
							</notempty>
						</p>
					</div>
				</div>

			</div>

		</div>
	</div>

	<div class="footer"  style="margin-bottom: -60px;">
		<div class="w1170">
			<div class="address">
				<a href="/">首页</a>
				<u>&gt;</u>
				<a href="/news">媒体中心</a>
				<u>&gt;</u>
				<a href="/news">方太资讯</a>
				<u>&gt;</u>
				<a href="javascript:;">{$news.title}</a>
			</div>
		</div>
	</div>

</block>
