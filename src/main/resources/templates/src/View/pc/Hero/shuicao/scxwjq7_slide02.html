<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<title>云魔方图库</title>
<link href="http://www.fotile.com/Public/Home/css/global.css" rel="stylesheet" type="text/css" />
<style>
* { margin: 0; padding: 0;}
.flexslider { position: relative; height: 400px; overflow: hidden; background: url(http://www.fotile.com/Public/Home/img/hero/scxwjq7/loading.gif) 50% no-repeat; background:#000;}
.slides { position: relative; z-index: 1; text-align:center;}
.flex-control-nav { position: absolute; bottom: 10px; z-index: 2; width: 100%; text-align: center; height:14px; padding:10px 0 6px 0;}
.flex-control-nav li { display: inline-block; width: 14px; height: 14px; margin: 0 3px; *display: inline; zoom: 1;}
.flex-control-nav a { display: inline-block; width:10px; height:10px; background: url(http://www.fotile.com/Public/Home/img/hero/scxwjq7/06/dot2.png) no-repeat; border:0; cursor: pointer; text-indent:-9999px;}
.flex-control-nav .flex-active { background: url(http://www.fotile.com/Public/Home/img/hero/scxwjq7/06/dot.png) no-repeat;}
.flex-prev,.flex-next{ position:absolute; left:32px; top:50%; margin-top:-30px; width:41px; height:41px; background: url(http://www.fotile.com/Public/Home/img/hero/scxwjq7/06/btn.png) no-repeat; overflow:hidden; z-index:99; text-indent:-9999px; cursor:pointer;}
.flex-next{ background-position:-41px 0; left:auto; right:30px;}

.flex-prev{   
  -webkit-transform: translateX(0);
     -moz-transform: translateX(0);
          transform: translateX(0);
	-webkit-transition: -webkit-transform 0.5s ease;
     -moz-transition:    -moz-transform 0.5s ease;
          transition:         transform 0.5s ease;}
.flex-prev:hover{   
  -webkit-transform: translateX(-8px);
     -moz-transform: translateX(-8px);
          transform: translateX(-8px);
  -webkit-transition: -webkit-transform 0.5s ease;
     -moz-transition:    -moz-transform 0.5s ease;
          transition:         transform 0.5s ease;}
		  
.flex-next{   
  -webkit-transform: translateX(0);
     -moz-transform: translateX(0);
          transform: translateX(0);
	-webkit-transition: -webkit-transform 0.5s ease;
     -moz-transition:    -moz-transform 0.5s ease;
          transition:         transform 0.5s ease;}
.flex-next:hover{   
  -webkit-transform: translateX(8px);
     -moz-transform: translateX(8px);
          transform: translateX(8px);
  -webkit-transition: -webkit-transform 0.5s ease;
     -moz-transition:    -moz-transform 0.5s ease;
          transition:         transform 0.5s ease;}
		  
</style>
<script src="http://hsdev.efotile.com/webstatic/newSrc/js/jquery.js"></script>
<script src="http://hsdev.efotile.com/webstatic/Public/Home/js/jquery.flexslider-min.js"></script>
<script>
$(window).load(function() {
	$('.flexslider').flexslider({
		//directionNav: false,
		//pauseOnAction: false
	});
	
function wrap(){
	var winHeight =$(window).height();
	$(".flexslider,.slides li").height(winHeight);
}
wrap();
$(window).resize(function() {
wrap();
});

});
</script>
</head>
<body>
<div class="flexslider">
	<ul class="slides">
		<li><img src="http://hsdev.efotile.com/webstatic/Public/Home/img/hero/scxwjq7/06/s2_01.jpg" width="100%"></li>
		<li><img src="http://hsdev.efotile.com/webstatic/Public/Home/img/hero/scxwjq7/06/s2_02.jpg" width="100%"></li>
	</ul>
</div>
</body>
</html>