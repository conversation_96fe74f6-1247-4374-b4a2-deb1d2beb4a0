<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- <block name="title">方太嵌入式蒸箱 - FOTILE方太厨房电器官方网站</block>
<block name="description">方太专注高端嵌入式厨房电器，拥有吸油烟机、嵌入式灶具、嵌入式消毒柜、蒸微一体机、嵌入式蒸箱、嵌入式烤箱、嵌入式微波炉以及原创发明的水槽洗碗机、燃气热水器等九大产品线。进入方太嵌入式蒸箱官方网站，了解更多方太智能蒸箱产品详情，并获得方太蒸箱售前、售后服务。</block>
<block name="keywords">蒸箱,方太蒸箱,电蒸箱价格,电蒸箱,方太蒸箱,蒸箱哪个品牌好</block> -->
  <title>嵌入式蒸箱</title>
  <link href="http://hsdev.efotile.com/webstatic/Public/static/swiper3/swiper.min.css" rel="stylesheet" type="text/css" />
  <link href="http://hsdev.efotile.com/webstatic/Public/Home/css/cover.css" rel="stylesheet" type="text/css" />
  <style>
    html,
    body {
      overflow-x: hidden;
    }

    .box {
      margin-top: 0;
    }

    .box li {
      margin-top: 0;
      margin-bottom: 38px;
    }

    .diaoyan-float {
      position: fixed;
      bottom: 120px;
      right: 0;
      z-index: 2;
    }

    .diaoyan-float {
      display: block;
      width: 110px;
      height: 30px;
      overflow: hidden;
      color: #fff !important;
      font-weight: 600;
      text-align: center;
      line-height: 30px;
      background: #aa001a;
      border-radius: 5px 0 0 5px;
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="swiper-container swiper-container-horizontal">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <div class="banner b7 url" alt="/hero/qrszx.html">
            <div class="w1170">
              <div class="txt">
                <h1>方太智能蒸箱</h1>
                <p>蒸汽够大&nbsp;&nbsp;温度够高<br>为家人蒸出健康美味&nbsp;&nbsp;中式蒸箱创新者*</p>
                <a href="/hero/qrszx.html">进一步了解</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pagination"></div>
    </div>



    <h1 class="page-prod-title">嵌入式蒸箱</h1>

    <div class="page-main w1170 toggle" id="page-main">

      <div class="prod-list-wrap default-prod-list" id="defaultProdList">
        <!-- {:W('Hero/hero_cover_products', array(5))} -->
        <div class="w1170">
          <div class="box">
            <div class="section" id="serial{$key}">
              <!-- <h2 class="serial-name">多重强消消毒柜系列</h2> -->
              <ul style="overflow:hidden;">
                <li style="height:auto;" class="shuicaoBox">
                  <a href="/product/{$vv['id']}.html" target="_blank" title="CXW-258-X1">
                    <div class="pic"> <img
                        src="https://fotilepc.oss-cn-hangzhou.aliyuncs.com/2018-09-06/5b90ee314af71.png" width="100%">
                    </div>
                    <p style="padding-top:0;margin-top:10px;height:21px;overflow:hidden;">CXW-258-X1</p>
                    <p style="height:34px;overflow:hidden;color:#999;font-size:12px;">二星消毒，健康耐用，安心净享每一餐</p>
                  </a>
                </li>
                <li style="height:auto;" class="shuicaoBox">
                  <a href="/product/{$vv['id']}.html" target="_blank" title="CXW-258-X1">
                    <div class="pic"> <img
                        src="https://fotilepc.oss-cn-hangzhou.aliyuncs.com/2018-09-06/5b90ee314af71.png" width="100%">
                    </div>
                    <p style="padding-top:0;margin-top:10px;height:21px;overflow:hidden;">CXW-258-X1</p>
                    <p style="height:34px;overflow:hidden;color:#999;font-size:12px;">二星消毒，健康耐用，安心净享每一餐</p>
                  </a>
                </li>
              </ul>
            </div>


          </div>
        </div>
      </div>

      <div class="filter-prod-wrapper" id="filterProdWrapper">

        <!-- 筛选 S -->
        <div class="filter-wrap">
          <div class="filter-wrap-container pinned" id="filter-wrap-container">
            <div class="filter-wrap-header">
              <a href="javascript:void(0);" class="toggle-filter-control-btn">筛选</a>
              <span class="space-line">|</span>
              <a href="javascript:void(0);" class="reset-filter-control-btn" @click="resetFilter">重置</a>
            </div>

            <!-- 已选择 -->
            <div class="checked-labels" v-if="checkedLabel">
              <a href="javascript:void(0);" class="checked-label" v-for="(label, index) in checkedLabel"
                :data-index="index" :data-pname="label.title" :data-name="label.name"
                :data-value="label.value">{{label.name}}
                <span class="clear-label"
                  @click="clearThisLabel(label.name, label.title, label.value)">&times;</span></span>
              </a>
            </div>

            <dl class="filter-panel" v-for="(item, pindex) in filterLabelData" :key="pindex" :data-pidx="pindex">
              <dt class="filter-panel-header" @click="toggleFilterLabel(pindex)">{{item.label}}</dt>
              <dd class="filter-panel-body">
                <a href="javascript:void(0);" v-for="(label, cindex) in item.items"
                  :class=" label.active? 'filter-label active' : 'filter-label' " :data-pname="item.label"
                  :data-cidx="cindex" :data-value="label.value" :data-name="label.name"
                  @click.stop="checkLabel(pindex, item.label, cindex, label.value)">{{label.name}}</a>
              </dd>
            </dl>

          </div>
          <div class=" isShowLoading? 'filter-loading show': 'filter-loading' " v-if="isShowLoading"></div>

        </div>
        <!-- 筛选 End -->

        <!-- 产品列表 S -->
        <div class="prod-list-wrap filter-prod-list" id="filterProdList">
          <div class="box filter-prod-container" id="filter-prod-container" v-if="goodsData">
            <ul>
              <li style="height:auto;" v-for="(item, index) in goodsData" :key="index">
                <a :href=" '/product/' + item.id + '.html' " target="_blank" :title="item.g_model">
                  <div class="pic"><img :src="item.path" width="100%"></div>
                  <p style="padding-top:0;margin-top:10px;height:21px;overflow:hidden;">{{item.g_model}}</p>
                  <p style="height:34px;overflow:hidden;color:#999;font-size:12px;">{{item.highlight_desc}}</p>
                </a>
              </li>
            </ul>
          </div>

        </div>
        <!-- 产品列表 End -->

        <!-- <div :class=" isShowToast? 'toast-wrap show': 'toast-wrap' " v-if="isShowToast">{{toastText}}</div> -->

      </div>

    </div>
  </div>


  <div class="clear_float"></div>
  <div class="footer f2" style="margin-bottom: -100px;">
    <!-- <include file="footer_need" /> -->
    <!-- 此处抽出公共底部 -->
    <div class="w1170">
      <h4>您可能需要</h4>
      <div class="iconList">
        <ul>
          <li class="n3">
            <h5>专卖店查询</h5>
            <a href="/service/fuwuwangluo.html" target="_blank">立即前往&nbsp;&nbsp;&gt;</a>
          </li>
          <li class="n5">
            <h5>立即咨询</h5>
            <a href="/survey.html?from=fotile-chanpinliebiaoye" target="_blank">立即前往&nbsp;&nbsp;&gt;</a>
          </li>
          <li class="n7">
            <h5>方太商城</h5>
            <a class="hero-utm" href="https://www.efotile.com/?utm_source=fotile-dh"
              target="_blank">立即前往&nbsp;&nbsp;&gt;</a>
          </li>
          <li class="n4">
            <h5>了解方太文化</h5>
            <a href="/humanism/index.html" target="_blank">立即前往&nbsp;&nbsp;&gt;</a>
          </li>
        </ul>
      </div>
    </div>
    <!-- 结束 -->
  </div>

  <a class="diaoyan-float"
    href="https://www.fotile.com/huanzhuang?site_id=122&cate=5&utm_source=fotile-zhengkaoliebiao-fuceng" target="_blank"
    style="bottom:80px;">幸福换装服务</a>

  <script src="http://hsdev.efotile.com/webstatic/Public/static/swiper3/swiper.min.js" type="text/javascript"></script>
  <script src="http://hsdev.efotile.com/webstatic/Public/Home/js/cover.js" type="text/javascript"></script>
  <script src="https://fotilepc.oss-cn-hangzhou.aliyuncs.com/assets/js/import.min.js"></script>
  <script src="http://hsdev.efotile.com/webstatic/Public/static/jquery.pin.js"></script>
  <script src="http://hsdev.efotile.com/webstatic/Public/Home/js/product_filter_pc_zhengxiang.js?20190929"></script>
  <script type="text/javascript">

    // var currentProductCate = { $cate_id };
    // var currentProdCateName = '{:get_goods_cate_name($cate_id)}';

    $(document).ready(function () {
      var mySwiper = new Swiper('.swiper-container', {
        pagination: '.pagination',
        autoplay: 5000,
        effect: 'fade',
        loop: false,
        preventClicks: false,
        paginationClickable: true,
      });
      // var prodDomMiniHeight = $('#defaultProdList').outerHeight();
      // $('.page-main').css('min-height', prodDomMiniHeight);

    });
  </script>
</body>

</html>