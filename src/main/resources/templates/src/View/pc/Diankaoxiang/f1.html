<extend name="Public/common"/>
<block name="title">方太同温烤箱KQD60F-F1系列</block>
<block name="description">方太同温烤箱KQD60F-F1系列，60L超大空间，可放5层烤盘；加热管下翻设计，更易清洁；一键智能菜单，私藏烘焙大厨。</block>
<block name="keywords">方太烤箱,方太烤箱F1,kqd60f f1</block>

<block name="css">
  <link href="__CSS__/product_detail.css" rel="stylesheet" type="text/css" />
  <link rel="stylesheet" href="__CSS__/survey_user.css?20181202">
  <style>
  .head {background: #fff;}
  .section img {display: block;width: 100%;max-width: 1920px;margin-left: auto;margin-right: auto;}
  .floatNav .tab li a.askBtn{font-size: 12px;background-size: auto 45%;}
  .floatNav .tab li a.buyNow{width:118px;text-align:center;text-indent:0;font-size: 12px;background-image: none;}
  </style>
</block>
<block name="script">
  <script src="https://lib.baomitu.com/vee-validate/2.1.1/vee-validate.min.js"></script>
  <script src="https://lib.baomitu.com/vee-validate/2.1.1/locale/zh_CN.js"></script>
  <script src="https://lib.baomitu.com/vue/2.5.17/vue.min.js"></script>
  <script src="__JS__/survey_user_20191009.js?20181207" type="text/javascript"></script>
  <script>
    $(function(){
      $(window).scroll(function(event){
        if( $(window).scrollTop() > 80 ) {
          $(".head").stop(true).css({top:"-100px"});
          $(".qNav").stop(true).addClass("fixedtop").css({'top':0});
        } else {
          $(".head").stop(true).css({top:"0px"});
          $(".qNav").stop(true).removeClass("fixedtop").css({'top':80});
        }
      });
    });
  </script>

  <script type="text/javascript">
    // 关闭留资
    function closeSurveyModal() {
        $('.survey-user-wrapper').removeClass('open')
        setTimeout(function(){
            $('.overlay-cover-survey').removeClass('open')
        }, 800)

        $('html, body').removeClass('hidden')
    }

    $(document).ready(function(){
        $('#zaixianzixun').on('click', function() {
            $('html, body').addClass('hidden')
            $('.overlay-cover-survey, .survey-user-wrapper').addClass('open')
        });
        $('.close-survey-user-wrapper, .overlay-cover-survey').on('click', function(){
            closeSurveyModal()
        });
    });

  </script>
</block>

<block name="body">
  <div class="qNav" style="top: 80px;">
    <div class="w1170 floatNav">
      <div class="tab">
        <ul id="menu">
          <li><a href="#section0">产品概述</a></li>
          <li><a href="#section5">安装与参数</a></li>
          <li>
              <a href="javascript:;" id="zaixianzixun" class="askBtn">咨询产品</a>
          </li>
          <li><a class="hero-utm buyNow" href="">立即购买</a></li>
        </ul>
      </div>
      <div class="name"><a href="/diankaoxiang/f1" class="n5"></a>方太同温烤箱KQD60F-F1系列</div>
    </div>
  </div>

  <div id="fullpage">
    <div class="section fp-auto-height" id="section0">
      <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_01.jpg" />
    </div>
    <div class="section fp-auto-height" id="section1">
      <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_02.jpg" />
    </div>
    <div class="section fp-auto-height" id="section2">
      <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_03.jpg" />
    </div>
    <div class="section fp-auto-height" id="section3">
      <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_04.jpg" />
      <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_05.jpg" />
    </div>
    <div class="section fp-auto-height" id="section4">
        <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_06.jpg" />
    </div>
    <div class="section fp-auto-height" id="section5" style="margin-bottom: 20px;">
        <img src="__PUBLIC__/Home/diankaoxiang/images/dkx_pc_07.jpg" />
    </div>
  </div>


  <!-- 用户留资 S ================================ -->
  <div class="overlay-cover-survey"></div>
  <div class="survey-user-wrapper">
      <div class="guic-header-control">
          <a href="javascript:void(0);" class="close-survey-user-wrapper"></a>
      </div>

      <div class="survey-user-body">
          <include file="Public/survey_user" />

          <div class="guic-footer">
              <h3 class="tit">已经购买，咨询售后相关问题</h3>
              <a href="javascript:void(0);" class="service-control-btn toggle-service-dialog">联系售后客服</a>
              <p class="tel">或者拨打 <span>952315</span></p>
          </div>
      </div>
  </div>
  <!-- 用户留资 End ========================== -->

</block>