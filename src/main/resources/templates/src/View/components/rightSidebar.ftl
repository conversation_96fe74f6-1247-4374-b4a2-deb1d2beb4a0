
<style>
    .common_sideBar {
        position: fixed;
        right: 11px;
        bottom: 100px;
        cursor: pointer;
        z-index: 10001;
    }
    .common_sideBar > .common_sideBar_top {
        width: 68px;
        min-height: 61px;
        background: rgba(156, 29, 34, 0.85);
        border-radius: 4px;
        backdrop-filter: blur(5px);
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        overflow: hidden;
        padding-bottom: 5px;
    }
    .common_sideBar > .common_sideBar_top:hover {
        background: #C3242A;
    }
    .common_sideBar > .common_sideBar_top > img {
        width: 24px;
        height: 24px;
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-top: 12px;
    }
    .common_sideBar > .common_sideBar_top > .common_sideBar_top_text {
        text-align: center;
        margin-top: 3px;
    }
    .common_sideBar_bottom {
        width: 68px;
        min-height: 148px;
        background: rgba(255, 255, 255, 0.85);
        border-radius: 4px;
        backdrop-filter: blur(5px);
        /*overflow: hidden;*/
        margin-top: 10px;
        text-align: center;
    }
    .common_sideBar_bottom_box {
        /*overflow: hidden;*/
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        box-sizing: border-box;
        padding-bottom: 10px;
    }
    .common_sideBar_bottom_box  img {
        width: 24px;
        height: 24px;
        margin-top: 12px;
    }
    .common_sideBar_bottom_box
    .common_sideBar_bottom_box_text {
        margin-top: 3px;
    }

    .common_sideBar_bottom_serve{
        position: relative;
    }
    .common_sideBar_bottom_qrCode{
        position: absolute;
        top: 0px;
        right: 70px;
        /*height: 100px;*/
        width: 100px;
        background: #fff;
        display: none;

    }
    .common_sideBar_bottom_qrCode img{
        width: 78px;
        height: 78px;
    }

</style>
<div class="common_sideBar" >
        <div class="common_sideBar_top" onclick="openDialog()">
            <img src="https://hsimage.fotile.com/202308211858553323066.png" alt="" class="defaultGraph">
            <div class="common_sideBar_top_text showName" style="margin-bottom: 5px"></div>
        </div>
        <div class="common_sideBar_bottom">
            <div class="common_sideBar_bottom_box common_sideBar_bottom_serve" onclick="openServe()">

                <img src="https://hsimage.fotile.com/202309132104149396112.png" alt="">
                <div class="common_sideBar_bottom_box_text">
                 至诚服务
                </div>


<#--                <div class="common_sideBar_bottom_qrCode">-->
<#--                    <div class="common_sideBar_bottom_qrCode_img">-->
<#--                        <img src="https://hsimage.fotile.com/202308211309437514465.png" alt="" style="margin-top: 5px">-->
<#--                    </div>-->
<#--                    <div class="common_sideBar_bottom_qrCode_title">至诚服务</div>-->
<#--                </div>-->
            </div>
            <div class="common_sideBar_bottom_box" onclick="kefu()">
                <img src="https://hsimage.fotile.com/202502252015227123966.png" alt="">
                <div class="common_sideBar_bottom_box_text">在线客服</div>
            </div>
            <div class="common_sideBar_bottom_box common_sideBar_bottom_box_store" onclick="goStore()">
                <img src="https://hsimage.fotile.com/202502252015227025129.png" alt="">
                <div class="common_sideBar_bottom_box_text" >附近门店</div>
            </div>
            <div class="common_sideBar_bottom_box common_sideBar_bottom_backTop" onclick="backToTheTop()">
                <img src="https://hsimage.fotile.com/202308211946276086243.png" alt="">
                <div class="common_sideBar_bottom_box_text" >TOP</div>
            </div>
            
        </div>
    </div>
 <#include "/src/View/components/reservePop.ftl"/>

<script>
    var pathname=window.location.pathname;
    var _module="";
    if(pathname.indexOf('chanpin')>-1){
        _module="产品列表页"
    }else if(pathname.indexOf('product/')>-1){
        _module="产品B级页"
    }else if(pathname.indexOf('/service.html')>-1){
        _module="服务首页"
    }else if(pathname.indexOf('/search')>-1){
        _module="搜索结果页"
    }else if(pathname=='/' || pathname=='/homePage.html'){
        _module="首页"
    }else if(pathname.indexOf('/survey.html')>-1){
        _module="咨询留资页"
    }else if(pathname.indexOf('/rehandling.html')>-1){
        _module="换新服务页"
    }else if(pathname=='/news'){
        _module="资讯列表页"
    }else{
        _module=document.title
    }
    let advertArr={}
    function  openDialog(){
        // 埋点
        let name =$('.showName').text()
        gio('track', 'FT_flowClick', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
            moduleName_var: _module+'悬浮条',//模块名称
            positionNumber_var: '1',//坑位
            pitName_var: name,//流量位名称
            frame_var:'-',
            bannerNum_var:'-'
        });
        gio('track', 'FT_consultRetainEntranceClick', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
            moduleName_var:  _module+'悬浮条',//模块名称
            positionNumber_var:'1',//坑位
            pitName_var:name,//流量位名称
            frame_var:'-',
            bannerNum_var:'-',
            formName_var:'1000元添礼券限时领'
        });
        if(advertArr && advertArr.length>0 && advertArr[0].wholeUrl && advertArr[0].wholeUrl !='-1'){
            window.open(advertArr[0].wholeUrl)
        }else{
            // 全局初始化方法
            globalFn()
        }
    }

   const backToTheTop=()=>{
       const options = {
           top: 0,
           behavior: "smooth",
       };
       gio('track', 'FT_flowClick', {
           utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
           pageName_var: _module,//页面名称
           pageUrl_var: window.location.href,//页面地址
           moduleName_var: _module+'悬浮条',//模块名称
           positionNumber_var: '5',//坑位
           pitName_var: 'top',//流量位名称
           frame_var:'-',
           bannerNum_var:'-'
       });
       window.scrollTo(options);
    }
   // 获取回到顶部的按钮
   const backTopBtn=document.getElementsByClassName('common_sideBar_bottom_backTop')[0]
   // 附近门店的按钮
   const storeBtn=document.getElementsByClassName('common_sideBar_bottom_box_store')[0]

   function isShow(){
       // 浏览器高度
       const  outerHeight=  window.outerHeight
       const scrollY = document.documentElement.scrollTop || document.body.scrollTop
       if(scrollY<outerHeight){
           backTopBtn.style.display='none'
           storeBtn.style.marginBottom='20px'
       }else{
           backTopBtn.style.display='block'
           storeBtn.style.marginBottom='0px'
       }

   }
   isShow()
   window.addEventListener('scroll',(e)=>{
       isShow()

   })
    $('.common_sideBar_bottom_qrCode').hide()

    $('.common_sideBar_bottom_serve').on({
        mouseover : function(){
          $('.common_sideBar_bottom_qrCode').show()
        } ,
        mouseout : function(){
            $('.common_sideBar_bottom_qrCode').hide()
        }
    }) ;

//    获取广告位的信息
    function  getAdvertFn(){
        const codes='PC-ow-active-001'
        $.ajax({
            method: 'GET',
            url: "/common/getCustomAdvert?codes="+codes,
            success: (res) =>{
                if(res.success){
                    advertArr=res.data[codes]
                    if(advertArr && advertArr.length>0){
                        if(advertArr[0].showName.length>8){
                            $('.showName').html(advertArr[0].showName.substring(0, 8))
                        }else{
                            $('.showName').html(advertArr[0].showName)
                        }
                        if( advertArr[0].picUrl && advertArr[0].picUrl.length>0){
                            $('.defaultGraph').attr("src", advertArr[0].picUrl[0].coverUrl1);
                        }

                    }else{
                        $('.common_sideBar_top').hide()
                    }


                }
                // console.log('pc-ow-search-001', res)
            },
            error: (error)=> {
            }
        });
    }
    getAdvertFn()
    function  openServe(){
        gio('track', 'FT_flowClick', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
            moduleName_var: _module+'悬浮条',//模块名称
            positionNumber_var: '2',//坑位
            pitName_var: '至诚服务',//流量位名称
            frame_var:'-',
            bannerNum_var:'-'
        });
        window.open('/service.html?utm_source=fotile-guanwang-pcdingbusousuo0430')
    }
    function kefu(){
        gio('track', 'FT_customerServiceEntranceClick ', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
        });
        gio('track', 'FT_flowClick', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
            moduleName_var: _module+'悬浮条',//模块名称
            positionNumber_var: '3',//坑位
            pitName_var: '在线客服',//流量位名称
            frame_var:'-',
            bannerNum_var:'-'
        });
        ysf_open()
    }
    function goStore(){
        gio('track', 'FT_flowClick', {
            utmSource_var:getQueryString('utm_source')?getQueryString('utm_source'):'-',//广告来源
            pageName_var: _module,//页面名称
            pageUrl_var: window.location.href,//页面地址
            moduleName_var: _module+'悬浮条',//模块名称
            positionNumber_var: '4',//坑位
            pitName_var: '附近门店',//流量位名称
            frame_var:'-',
            bannerNum_var:'-'
        });
        window.open('/service/city/queryCity.html')
    }
    $('.common_sideBar_top').hover(function(){
      $('.common_sideBar_bottom').css("opacity","0.7")
    },function(){
        $('.common_sideBar_bottom').css("opacity","1")
    })
</script>