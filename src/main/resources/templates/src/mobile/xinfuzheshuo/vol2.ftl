<!DOCTYPE html>
<html class="no-js">

<head>
  <meta charset="utf-8" />
  <meta name="viewport"
        content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
  <meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
  <meta name="referrer" content="unsafe-url" />
  <title>方太官网 - FOTILE方太厨电官方网站</title>
  <meta name="moudle" content="xinfuzheshuo" />
  <meta name="Keywords" content="方太，方太油烟机，方太燃气灶，方太消毒柜，方太蒸箱，方太烤箱，方太微波炉，方太水槽洗碗机，方太热水器" />
  <meta name="Description"
        content="方太专注高端厨房电器，拥有吸油烟机、嵌入式灶具、嵌入式消毒柜、蒸微一体机、嵌入式蒸箱、嵌入式烤箱、嵌入式微波炉以及原创发明的水槽洗碗机、燃气热水器等九大产品线。进入方太厨房电器官方网站，了解更多方太家用电器产品详情，并获得方太专业售前、售后服务。" />
  <script src="/webstatic/newSrc/js/jquery.js"></script>
  <script src="/webstatic/newSrc/js/anime.min.js"></script>
  <script src="/webstatic/newSrc/js/hammer.js"></script>
  <script src="https://lib.baomitu.com/vue/2.5.17/vue.min.js"></script>
  <script src="/webstatic/newSrc/js/global.js"></script>
  <script src="/webstatic/newSrc/js/jquery.cookie.js"></script>
  <script src="/webstatic/newSrc/js/swiper4.js"></script>
  <link href="/webstatic/Public/Mobile/css/global.css?v=201909181327" rel="stylesheet" type="text/css" />
  <link rel="apple-touch-icon" href="/webstatic/Public/apple-touch-icon.png" />
  <!-- <link rel="icon" type="image/png" href="/Public/apple-touch-icon.png"> -->
  <link rel="shortcut icon" href="/webstatic/Public/favicon.ico" />
</head>

<body>
<!-- 头部 -->
<div id="headerWapper">
  <#include "/src/mobile/common/header.ftl">
</div>
<div id="bodyWapper">
  <script src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/assets/js/flexible.js"></script>
  <link href="https://lib.baomitu.com/Swiper/4.4.2/css/swiper.min.css" rel="stylesheet">
  <link href="/webstatic/Public/Mobile/xingfu/xingfuzheshuo/css/xingfuzheshuo.css?201901151343" rel="stylesheet">
  </link>
  <style>
    .swiper-group-1 .swiper-wrapper .swiper-slide{width: 8.8rem /* 660/75 */;}
    .swiper-group-2 .swiper-wrapper .swiper-slide{width: 7.013333rem /* 526/75 */;}
    .swiper-group-2 .swiper-wrapper .swiper-slide:first-child{margin-left: 0;}
  </style>
  <script src="/webstatic/newSrc/js/swiper.js"></script>
  <script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
  <script src="/webstatic/Public/Mobile/xingfu/xingfuzheshuo/js/imagelightbox.min.js"></script>
  <script src="/webstatic/Public/Mobile/xingfu/xingfuzheshuo/js/xingfuzheshuo-m.js?20190115"></script>
  <script>
    //
    // 获取浏览数/点赞数
    function getViewAndLikeCount() {
      $.get('/index_jr.php?s=/api/advisory/viewaction&code=vol2', function(res){
        if(res.status == 10000) {
          var viewCount = res.data.view
          var likeCount = res.data.zan
          $('.view-count-show').html(viewCount)
          $('.like-count-show').html(likeCount)
        } else {
          console.error('获取数据错误')
        }
      });
    };
    getViewAndLikeCount();

    //
    // 浏览+1
    function viewCountAdd() {
      $.get('/index_jr.php?s=/api/advisory/action&code=vol2&type=view', function(res){
        if(res.status == 10000) {
          getViewAndLikeCount();
        }
      });
    };
    viewCountAdd();

    $(document).ready(function(){
      // if( window.localStorage.getItem('XFZS_LIKE_STATUS') ) {
      //   $('.control-btn-like').addClass('liked');
      // }

      var flagLike = 0
      $('.control-btn-like').on('click', function(){
        if (flagLike == 0) {
          flagLike = 1;
          var currentCount = $('.like-count-show').html()
          $('.like-count-show').html(parseInt(currentCount)+1)
          $('.control-btn-like').addClass('liked')
          $.ajax({
            url:'/h5/xinfuzheshuo/content/contentStaticUpdate?contentId=4671',
            type:'get',
            success:function(res){
              if(res.success){
                console.log("点赞成功")
              }
            }
          })
        }

      });
    });

    //
    //
    var swiperCover = new Swiper('.happiness-cover-swiper', {
      autoplay: true,
      speed: 800,
    });

    //
    var swiperGroup1 = new Swiper('.swiper-group-1', {
      slidesPerView: 'auto',
      spaceBetween: 13,
      freeMode: true,
    });
    //
    var swiperGroup2 = new Swiper('.swiper-group-2', {
      slidesPerView: 'auto',
      spaceBetween: 13,
      freeMode: true,
    });
    //
    var swiperQA = new Swiper('.swiper-qa', {
      slidesPerView: 'auto',
      spaceBetween: 13,
      freeMode: true,
    });

    //
    // 微信分享
    //
    var sharetitle = "幸福者说，标记你的幸福生活";
    var sharelink = location.href.split('?')[0];
    console.log(sharelink)
    var shareimg = "https://fotileh5.oss-cn-hangzhou.aliyuncs.com/yantaohui2019/share_img2.png";
    var sharedesc = "分享你的幸福故事，赢取品牌温暖周边";

    $.ajax({
      type: 'GET',
      url: 'https://api.fotilestyle.com/fotile-api-0.0.2/wx/wxConfig.json?url=' + encodeURIComponent(window.location.href.split('#')[0]),
      success: function (data) {
        console.info(data.appId);
        wx.config({
          debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
          appId: data.appId, // 必填，公众号的唯一标识
          timestamp: data.timestamp, // 必填，生成签名的时间戳
          nonceStr: data.nonceStr, // 必填，生成签名的随机串
          signature: data.signature,
          jsApiList: [
            "onMenuShareTimeline",
            "onMenuShareAppMessage",
            "onMenuShareQQ",
            "onMenuShareWeibo",
            "onMenuShareQZone"
          ]
        });
        wx.ready(function () {
          //分享给朋友圈
          wx.onMenuShareTimeline({
            title: sharetitle, // 分享标题
            desc: sharedesc, // 分享描述
            link: sharelink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: shareimg, // 分享图标
            success: function () {
              // 用户确认分享后执行的回调函数
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
            }
          });
          //分享给朋友
          wx.onMenuShareAppMessage({
            title: sharetitle, // 分享标题
            desc: sharedesc, // 分享描述
            link: sharelink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: shareimg, // 分享图标
            type: '', // 分享类型,music、video或link，不填默认为link
            dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
            success: function () {
              // 用户确认分享后执行的回调函数
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
            }
          });
        });
      },
      error: function (xhr, type) {
        //alert(111);
      }
    });

  </script>


  <div class="happiness-header">
    <div class="inner">
      <a href="/xingfuzheshuo/xingfuzheshuo/index.html">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol1/title2.png" alt="方太 · 幸福者说">
      </a>
      <div class="control-group">
        <span class="control-btn-like">
          <i class="like-icon"></i>
          <span class="like-count-show">${res.data.likeCount!''}</span>
        </span>
        <span class="view-count">
          <i class="view-icon"></i>
          <span class="view-count-show">${res.data.browseCount!''}</span>
        </span>
      </div>
    </div>
  </div>

  <div class="happiness-container">

    <div class="happiness-cover-panel">
      <div class="happiness-cover">
        <div class="swiper-container happiness-cover-swiper">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_cover_1.jpg"/>
            </div>
            <div class="swiper-slide">
              <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_cover_2.jpg"/>
            </div>
            <div class="swiper-slide">
              <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_cover_3.jpg"/>
            </div>
          </div>
        </div>
      </div>
      <div class="happiness-cover-cont">
        <strong class="num">Vol.02</strong>
        <p>幸福是，和自己喜欢的一切在一起</p>
      </div>
    </div>
    <!-- / happiness-cover-panel -->

    <div class="quote-panel">
      <ul>
        <li>
          <span class="avatar">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/<EMAIL>" class="avatar">
          </span>
          <span class="cont">
            <strong>小野</strong><br>资深创意人，neighbormind邻感广告创始人
          </span>
        </li>
        <li>
          <span class="avatar">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/<EMAIL>" class="avatar">
          </span>
          <span class="cont">
            <strong>June</strong><br>一个甘心被爱困在小城市的巨蟹座学霸
          </span>
        </li>
      </ul>

      <div class="quote-cont">
        如果人生是场马拉松，小野就会是那个里最先跑出队伍的人。法学专业出生，在法律界跑了一段之后，他在人潮中停了下来问了问自己，然后拐到了另一个道上。爱潮流、摄影、收藏、设计和篮球、也爱撸猫爱下厨…….这些都是创意人小野的养分，也是他生活的本真。
      </div>

    </div>
    <!-- / quote-panel -->

    <!-- swiper S -->
    <div class="swiper-container swiper-group-1">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_1.jpg" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_1.jpg" alt="">
          </a>
        </div>
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_2.jpg" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_2.jpg" alt="">
          </a>
        </div>
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_3.jpg" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_3.jpg" alt="">
          </a>
        </div>
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_4.jpg" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_1_4.jpg" alt="">
          </a>
        </div>
      </div>
    </div>
    <!-- swiper End -->


    <div class="text-panel">
      <p>
        周一到周五，小野大多是公司和家两点一线。如果问还能在哪碰到他，小野想了想说：“大概是公司门口和小区门口吧～。”他把创业比喻成在肩上驮着一大群人行走，自然不算容易，要懂公司的管理及运营，大多项目也要在他的把控下进行。
      </p>
    </div>

    <!-- 图片组 S -->
    <div class="image-summary-panel space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_1.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_1.jpg" class="img-block" alt="">
      </a>
    </div>
    <div class="image-summary-panel space space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_2.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_2.jpg" class="img-block" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_3.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_3.jpg" class="img-block" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_4.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_1_4.jpg" class="img-block" alt="">
      </a>
    </div>
    <!-- 图片组 End -->

    <div class="text-panel">
      <p>工作再忙，他也会为陪伴家人而抽出时间，一起好好做顿饭再好好吃饭，是他卸下所有压力，投入并享受的生活手段。相较于普通家庭千篇一律的独立式厨房，他执意把厨房和餐厅打通，让食物带来的满满的幸福感能蔓延到家里的每个角落。</p>
    </div>

    <!-- 图片组 S -->
    <div class="image-summary-panel space space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_2_1.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_2_1.jpg" class="img-block" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_2_2.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/image_group_2_2.jpg" class="img-block" alt="">
      </a>
    </div>
    <!-- 图片组 End -->

    <div class="text-panel">
      <p>为爱下厨是幸福的，看着家人从饥肠辘辘到酒足饭饱，是满足的。毕竟幸福，就是和爱的人一起吃好多好多顿饭。</p>
    </div>

    <!-- swiper S -->
    <div class="swiper-container swiper-group-2">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_2_1.png" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_2_1.png" alt="">
          </a>
        </div>
        <div class="swiper-slide">
          <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_2_2.gif" data-imagelightbox="b" class="item-slide">
            <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/swiper_group_2_2.gif" alt="">
          </a>
        </div>
      </div>
    </div>
    <!-- swiper End -->

    <div class="text-panel">
      <p>还有他爱吃的松饼，多半来自June的亲手烤制。甜而不腻，它不仅是松饼的味道，也恰好如他们之间的感情。</p>
    </div>

    <div class="image-summary-panel">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/<EMAIL>" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/<EMAIL>" class="img-block" alt="">
      </a>
    </div>
    <div class="text-panel muted">家里的露台，是深夜食堂。它见证了夏夜的晚风和冰啤酒，也承载过冬天咕噜咕噜冒泡的火锅。食物有慰藉人心的力量，而和家人在一起，能让夜的冷色调变得暖暖。</div>


    <div class="image-summary-panel space space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/1-shoe.gif" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/1-shoe.gif" class="img-block" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/xie2.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/xie2.jpg" class="img-block" alt="">
      </a>
    </div>

    <div class="text-panel muted">从前买一双球鞋的习惯，从此变成了同款你一双我一双。幸福得让人羡慕，小野专门为这个甜蜜的改变创作了一个小动画。</div>

    <div class="image-summary-panel space space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mc_1.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mc_1.jpg" class="img-block" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mc_2.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mc_2.jpg" class="img-block" alt="">
      </a>
    </div>

    <div class="text-panel muted">圣诞节，亲自帮June设计了特别的礼物包装，小野口中的“渣手绘”，在我们看来，用心的东西弥足珍贵。就像他想告诉June的那样“能留下的，都是生活的本真”，他希望让那些节日都留下仪式感的回忆，才能不辜负他们一起经历过的人生。</div>

    <div class="image-summary-panel">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mao.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/mao.jpg" alt="">
      </a>
    </div>

    <div class="text-panel muted">Milk，一个灵活的胖子。虽然已到中年，但是不耽误他扑腾麻雀擒拿老鼠，它是小野的“家居服”，一看到，就有放松的精神疗效。行业里，一般的广告人总是给人苦哈哈的印象，其实真正意义上的广告人是很幸福的，他们的眼里总有兴奋的光芒，脑袋里有闪光的时刻，对生活保持热情和好奇，因为它总有惊喜等着你。</div>

    <div class="image-summary-panel space space2">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_1.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_1.jpg" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_2.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_2.jpg" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_3.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_3.jpg" alt="">
      </a>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_4.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/lx_4.jpg" alt="">
      </a>
    </div>

    <div class="text-panel muted">有时间的时候，小野会带着June一起旅行，在行走中遇见更好的彼此。正如他的签名——“愿我们在分享、给予、接受、流动之中，得到欢喜”。</div>

    <div class="image-summary-panel">
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/last.jpg" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/last.jpg" alt="">
      </a>
    </div>

    <!-- 幸福10问 S -->
    <div class="happiness-qa-panel" style="margin-top:40px;">
      <div class="happiness-qa-panel-hd">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol1/qa_title.png" alt="幸福10问">
      </div>
      <div class="happiness-qa-panel-bd">
        <div class="swiper-container swiper-qa">
          <div class="swiper-wrapper">
            <div class="swiper-slide item">
              <dl>
                <dt>1. 生活中最重要的主题是什么？ </dt>
                <dd>小野: 发现和创造。</dd>
                <dt>2. 最重视的时间？如何度过？ </dt>
                <dd>小野: 最重视的时光一定会加一些仪式感。</dd>
                <dt>3. 和家人一起做过最开心的事情是什么？ </dt>
                <dd>小野: 第一次和June一起请爸妈来家里吃饭。为他们下厨，也是第一次正儿八经像模像样的为他们准备一顿饭。</dd>
                <dt>4. 和June在露台呆一整晚，会做</dt>
              </dl>
            </div>
            <div class="swiper-slide item">
              <dl>
                <dt>什么</dt>
                <dd>小野: 我们有尝试过在夏天躺在露台的大桌子上数掠过的飞机 。 </dd>
                <dt>5. 说一道你最想分享的菜。</dt>
                <dd>小野: 诺曼底炖鱼，本来是稀里糊涂的乱炖，结果有不错的口碑。 </dd>
                <dt>6. 下一个想亲手设计并制作的东西？</dt>
                <dd>小野: 可能是给未来孩子出生时的礼物吧。</dd>
                <dt>7. 分享一个，幸福生活的原则。 </dt>
                <dd>小野: 把自己变得渺小一些。</dd>
              </dl>
            </div>
            <div class="swiper-slide item">
              <dl>
                <dt>8.给心情郁闷的人一句话吧。 </dt>
                <dd>小野: 看看小时候那些你觉得你一定都过不去的坎，现在的你不是还活的好好的吗。</dd>
                <dt>9. 觉得什么样的照片最有幸福感？ </dt>
                <dd>小野: 有那种会让你不自觉的想把自己p到这张照片上的感觉。 </dd>
                <dt>10. 你觉得方太是什么样的品牌？ </dt>
                <dd>小野: 有温度的，充满诚意和幸福感的品牌。</dd>
              </dl>
            </div>
          </div>
          <!-- / swiper-wrapper -->
        </div>
        <!-- / swiper-container -->
      </div>
    </div>
    <!-- 幸福10问 End -->

    <!-- 幸福创作 S -->
    <div class="happiness-work-panel">
      <div class="happiness-work-panel-hd">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol1/work_title.png" alt="幸福创作">
      </div>
      <div class="text-panel">
        作为创意人，他想要把有爱的创意，给爱的那个人。在结婚前夕，小野为June打造了全球仅此一双的鞋，一人包下设计和所有的手工制作。
      </div>
      <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_1.jpg" style="display:block;margin-bottom: 0.533333rem;" data-imagelightbox="b">
        <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_1.jpg" class="img-block" alt="">
      </a>
      <div class="cont">
        <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_2.jpg" data-imagelightbox="b">
          <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_2.jpg" alt="">
        </a>
        <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_3.jpg" data-imagelightbox="b">
          <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_3.jpg" alt="">
        </a>
        <a href="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_4.jpg" data-imagelightbox="b">
          <img src="https://fotileh5.oss-cn-hangzhou.aliyuncs.com/xingfuzheshuo/vol2/cz_4.jpg" alt="">
        </a>
      </div>
    </div>
    <!-- 幸福创作 End -->


    <div class="ft-control-like-btn">
      <span class="control-btn-like"><i class="like-icon"></i></span>
      <p class="num"><span class="like-count-show">${res.data.likeCount!''}</span></p>
    </div>

  </div>
</div>
<div id="footerWapper"></div>
<div id="jsWapper"></div>
<script>
  $("#jsWapper").load("/h5/common/js")
  $("#footerWapper").load("/h5/common/footer")
</script>
</body>

</html>
