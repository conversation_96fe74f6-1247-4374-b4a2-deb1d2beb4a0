<!DOCTYPE html>
<html class="no-js">

<head>
    <meta charset="utf-8" />
    <meta name="viewport"
          content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
    <meta name="referrer" content="unsafe-url" />
    <meta name="moudle" content="Product" />
    <meta name="fssName" content="product" />
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="IE=edge" />
    <meta name="viewport"
          content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no" />
    <meta name="referrer" content="unsafe-url" />
    <title>所有产品</title>
    <meta name="moudle" content="Index" />
    <meta name="fssName" content="rehandling" />
    <meta name="Keywords" content="方太，方太油烟机，方太燃气灶，方太消毒柜，方太蒸箱，方太烤箱，方太微波炉，方太水槽洗碗机，方太热水器" />
    <meta name="Description"
          content="方太专注高端厨房电器，拥有吸油烟机、嵌入式灶具、嵌入式消毒柜、蒸微一体机、嵌入式蒸箱、嵌入式烤箱、嵌入式微波炉以及原创发明的水槽洗碗机、燃气热水器等九大产品线。进入方太厨房电器官方网站，了解更多方太家用电器产品详情，并获得方太专业售前、售后服务。" />
    <script src="/webstatic/newSrc/js/jquery.js"></script>
    <script src="/webstatic/newSrc/js/anime.min.js"></script>
    <script src="/webstatic/newSrc/js/hammer.js"></script>
    <script src="https://lib.baomitu.com/vue/2.5.17/vue.min.js"></script>
    <script src="/webstatic/newSrc/js/global.js"></script>
    <link href="/webstatic/Public/Mobile/css/global.css?v=201909181327" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="/webstatic/jingshui2020/css/animate.min.css">
    <link rel="apple-touch-icon" href="/webstatic/Public/apple-touch-icon.png" />
    <script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>

</head>
<body>

<#include "/src/View/common/kefu.ftl"/>

<!-- 头部 -->

<div id="headerWapper">
    <#include "/src/mobile/common/header.ftl">
</div>

<div id="bodyWapper">
    <script type="text/javascript" src="/webstatic/Public/Mobile/new/js/global.js"></script>
    <script type="text/javascript" src="/webstatic/newSrc/js/swiper.js"></script>
    <script src="https://lib.baomitu.com/vue/2.5.17/vue.min.js"></script>
</div>

<div class="allproduct">
    <div class="menuBox1">
        <div class="preBox">
            <ul>
                <li class="bBor on products btnPro">
                    <div class="categoryBoxs">
                        <div class="categoryBox1">
                            <div class="categoryBox1-title">
                                高端全场景厨电解决方案
                            </div>
                            <div class="categoryBox1-content" >
                                <div class="categoryBox1-box" v-for="(item,index) in h5ArraySeries" :key="index" @click="openCategory(item.url)">
                                    <div class="categoryBox1-content-img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="categoryBox1-content-text">
                                        {{item.text}}
                                    </div>
                                </div>

                            </div>
                        </div>


                        <div class="categoryBox2">
                            <div class="categoryBox2-title">排烟烹饪场景解决方案</div>
                            <div   class="categoryBox2-content">
                                <div  class="categoryBox2-box" v-for="(item,index) in h5ArraySeries1" :key="index" @click="openCategory(item.url)">
                                    <div class="categoryBox2-content-img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="categoryBox2-content-text" v-if="typeof item.text === 'string'">
                                       <div v-html="item.text"></div>
                                    </div>
                                    <div class="categoryBox2-content-text" v-else v-for="text in item.text" :key="text">
                                        {{text}}
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="categoryBox3">
                            <div class="categoryBox3-title">洗消饮用场景解决方案</div>
                            <div   class="categoryBox3-content">
                                <div  class="categoryBox3-box" v-for="(item,index) in h5ArraySeries2" :key="index" @click="openCategory(item.url)">
                                    <div class="categoryBox3-content-img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="categoryBox3-content-text">
                                        {{item.text}}
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="categoryBox4">
                            <div class="categoryBox4-title">保鲜冷储场景解决方案</div>
                            <div   class="categoryBox4-content">
                                <div  class="categoryBox4-box" v-for="(item,index) in h5ArraySeries3" :key="index" @click="openCategory(item.url)">
                                    <div class="categoryBox4-content-img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="categoryBox4-content-text">
                                        {{item.text}}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </li>
<!--                <li class="link newproduct" onclick="gioSet('/h5/chanpin/4259/','新品上市')">-->
<!--                    <a href="/h5/chanpin/4259/" class="bBor">新品上市</a>-->
<!--                </li>-->
                <li class="link newproduct compare-product-miniProgram" onclick="toMiniProgram()">
                    <a href="javascript;" class="bBor">产品对比</a>
                </li>
                <li class="link newproduct" onclick="gioSet('/h5/services/guanwangfuwu.html','服务')">
                    <a href="/h5/services/guanwangfuwu.html" class="bBor">服务</a>
                </li>
                <li class="link newproduct" onclick="gioSet('/rehandling.html?cate=1&utm_source=fotile-guanwang-sj-youyanji','幸福家')">
                    <a href="/h5/xingfu/" class="bBor">幸福家</a>
                </li>
                <li class="link newproduct" onclick="gioSet('/rehandling.html?cate=1&utm_source=fotile-guanwang-sj-youyanji','幸福焕新')">
                    <a href="javascript:;" class="bBor">幸福焕新</a>
                </li>
                <li class="link newproduct" onclick="gioSet('/h5/services/fuwuwangluo.html','附近门店')">
                    <a href="javascript:;" class="bBor">附近门店</a>
                </li>

            </ul>
            <div class="buyway">
                <a href="https://www.efotile.com/?utm_source=fotile-guanwang-shouye-dingbu-shangcheng0303" onclick="gioSetBottom('方太官方商城')">
                    <img src="https://hsimage.fotile.com/202309011721495781496.png" alt=""/>
                    <span>方太官方商城</span>
                </a>
                <a href="https://equity.tmall.com/tm?agentId=976428&_bind=true&bc_fl_src=tmall_market_llb_1_1141052&llbPlatform=_pube&llbOsd=1" onclick="gioSetBottom('天猫官方旗舰店')">
                    <img src="https://hsimage.fotile.com/202309121050516577903.png" alt=""/>
                    <span>天猫官方旗舰店</span>
                </a>
                <a href="https://mall.jd.com/index-1000000907.html" onclick="gioSetBottom('京东官方旗舰店')">
                    <img src="https://hsimage.fotile.com/202309121055066383947.png" alt="">
                    <span>京东官方旗舰店</span>
                </a>
            </div>
            <div class="linkBtn1">
                <a href="tel:952315" class="trblBor utm-source" onclick="gioSetBottom('方太全国服务热线')">952315 方太全国服务热线</a>
            </div>
        </div>

    </div>
    <div class="bottomBox"></div>



    <#--广告位弹窗 --start-->

<#--    <div class="allproduct-adv-space" v-if="isShowAdv">-->
<#--        <div class="box-space">-->
<#--            <div class="allproduct-adv-content">-->
<#--                <img :src="advImg" alt="" @click="goToPage">-->
<#--            </div>-->
<#--            <div class="allproduct-close-btn">-->
<#--                <img src="https://hsimage.fotile.com/202309251721338972645.svg" alt="">-->
<#--            </div>-->
<#--        </div>-->
<#--    </div>-->

    <#--广告位弹窗 ---end-->
</div>
</div>

<script type="text/javascript" src="/webstatic/jingshui2020/js/better-scroll.js"></script>
<script type="text/javascript" src="/webstatic/jingshui2020/js/verification.js"></script>

<div id="jsWapper"></div>
<script>
    $("#jsWapper").load("/h5/common/js.html")
    $(".compare-product-miniProgram").css('display','none')
    wx.miniProgram.getEnv((res)=>{
                    if (res.miniprogram) {
                        $(".compare-product-miniProgram").css('display','block')
                    }
                })
    function toMiniProgram(){
        wx.miniProgram.navigateTo({
                    url: '/pages/compare/cate/index'
                })
    }
</script>

<link rel="stylesheet" href="/webstatic/specialpage/allproduct/h5/style.css">
<script src="/webstatic/specialpage/allproduct/h5/script.js"></script>
</body>
<style>
   .bottomBox{
       height: 100px;
   }
</style>
