<link href="/webstatic/common/h5/css/global_footer.css" rel="stylesheet" type="text/css" />
<footer>
    <div class="footNavNew">
        <div class="lineStyle con1" id="footerContent"></div>
        <div class="lineStyle con2" id="footerAllContent"></div>
        <div id="load-more" class="load-more" onclick="loadmore()">加载更多  ></div>


        <div class="links" style="margin-bottom: 100px"></div>

    </div>

</footer>
<script>
    $(function (){
        getAdver()
    })
    function loadmore(){
        $('.load-more').hide() // 隐藏加载更多按钮
        $('.con2').show()
        $('.con1').hide()

    }
    function getAdver(){
        $.ajax({
            method: 'GET',
            url: "/common/getCustomAdvert?codes=pc-ow-footer-001",
            success: (res)=>{
                if(res.success){
                    $(".lineStyle").html(res.data['pc-ow-footer-001'][0].abstractNote);

                    const richTextContainer = document.getElementById("footerContent");
                    const content = document.getElementById("footerContent");
                    const loadMoreButton = document.getElementById("load-more");
                    const footerAllContent=document.getElementById("footerAllContent");
                    // 检查内容高度是否超过最大高度
                    let time=1000;
                    setTimeout(()=>{
                        if ($(".con1")[0].scrollHeight > $(".con1").height()) {
                            $('.load-more').show()
                        }
                    },time)
                    if(window.location.href.indexOf('/h5/product/')>-1){
                        $('.load-more').show()
                    }


                }
                // console.log(555,res.data)
            },
            error: function(){
            }
        });

    }


</script>
<style>
    #isShows{
        display: none;
    }
    #footerContent{
        font-size: 10px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        /*zoom: 0.83;*/
        line-height: 14px;
        color: rgba(136,136,150,0.5);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 49;
        -webkit-box-orient: vertical;
    }
    #load-more{
        font-size: 10px;
        color: rgba(136,136,150,0.5);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 600;
        display: none;
    }
    #footerAllContent{
        font-size: 10px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        /*zoom: 0.83;*/
        line-height: 14px;
        color: rgba(136,136,150,0.5);
        display: none;
    }

</style>


