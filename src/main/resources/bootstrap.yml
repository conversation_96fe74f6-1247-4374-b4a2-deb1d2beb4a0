
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: oms-center
  cloud:
    nacos:
      config:
        server-addr: ${nacos_url}
        shared-configs[0]:
          data-id: keycloak-init.yaml
          group: KEYCLOAK_GROUP
          refresh: false
        shared-configs[1]:
          data-id: spring-common.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[2]:
          data-id: redis.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[3]:
          data-id: datasource4.yml
          group: OMS_CENTER
          refresh: false
        shared-configs[4]:
          data-id: binder-kafka.yaml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[5]:
          data-id: oms-center.yml
          group: OMS_CENTER
          refresh: false
        shared-configs[6]:
          data-id: xxl.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[7]:
          data-id: seata.yaml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[8]:
          data-id: bingdings-DisposeSendMsgChannel.yml
          group: OMS_CENTER
          refresh: false
        shared-configs[9]:
          data-id: mail.yaml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[10]:
          data-id: bindings-DecOrderExportChannel.yml
          group: OMS_CENTER
          refresh: false
        shared-configs[11]:
          data-id: bindings-PointTimesChannel.yml
          group: CUSTOMER_CENTER
          refresh: false
        shared-configs[12]:
          data-id: msp.yml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[13]:
          data-id: ali.yml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[14]:
          data-id: dcs.yml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[15]:
          data-id: weixin.yaml
          group: DEFAULT_GROUP
          refresh: false
        namespace: ${nacos_namespace}
        context-path: '/nacos'
        username: ${nacos_username}
        password: ${nacos_password}
        enabled: true
      discovery:
        server-addr: ${nacos_url}
        namespace: ${nacos_namespace}
        context-path: '/nacos'
        username: ${nacos_username}
        password: ${nacos_password}
    config:
      discovery:
        enabled: true
    kubernetes:
      client:
        namespace: ${nacos_namespace}
dbname: omscenter
server:
  port: 8083
  max-http-header-size:  1024000
mybatis-plus: 
  typeAliasesPackage: com.fotile.omscenter
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler

logging:
  level:
    com.ke.utopia.scm.open.api.sdk.sign: debug
