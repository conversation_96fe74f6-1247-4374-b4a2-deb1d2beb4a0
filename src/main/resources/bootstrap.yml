spring:
  application:
    name: lottery-center #模块名称,nacos会先自动引入以模块名称命名的配置文件的配置,本版本默认为文件名.properties
  cloud:
    nacos:
      config:
        #nacos 地址
        server-addr: ${nacos_url}
        #引入的nacos的配置文件的列表,data-id和group与Nacos中创建的配置相互对应
        shared-configs[0]:
          #文件的名称,相同的group中只能有一个
          data-id: keycloak-init.yaml
          #文件所属分组名称,建议分组名称为模块名称
          group: KEYCLOAK_GROUP
          #是否自动刷新配置,默认情况下，只有默认加载的配置才会自动刷新，对于这些扩展的配置加载内容需要配置该设置时候才会实现自动刷新
          refresh: false
        shared-configs[1]:
          data-id: spring-common.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[2]:
          data-id: redis.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[3]:
          data-id: datasource2.yml
          group: LOTTERY_CENTER
          refresh: false
        shared-configs[4]:
          data-id: binder-kafka.yaml
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[5]:
          data-id: xxl.properties
          group: DEFAULT_GROUP
          refresh: false
        shared-configs[6]:
          data-id: lottery_channel.yaml
          group: LOTTERY_CENTER
          refresh: false
        shared-configs[7]:
          data-id: seata.yaml
          group: DEFAULT_GROUP
          refresh: false
        #配置所使用的命名空间的id
        namespace: ${nacos_namespace}
        context-path: '/nacos'
        #使用配置中心的帐号和密码
        username: ${nacos_username}
        password: ${nacos_password}
        enabled: true
      #注册中心的相关配置
      discovery:
        server-addr: ${nacos_url}
        namespace: ${nacos_namespace}
        context-path: '/nacos'
        username: ${nacos_username}
        password: ${nacos_password}
    config:
      discovery:
        #启用注册中心
        enabled: true
    #生产环境的k8s配置,此处参数非生产环境无需配置
    kubernetes:
      client:
        namespace: ${KUBERNETES_NAMESPACE}
  main:
    allow-bean-definition-overriding: true

server:
  port: 8067
dbname: lotterycenter


