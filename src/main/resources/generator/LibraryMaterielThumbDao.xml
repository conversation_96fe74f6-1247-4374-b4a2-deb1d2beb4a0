<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fotile.resourcescenter.test.dao.LibraryMaterielThumbDao">
  <resultMap id="BaseResultMap" type="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumb">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="materiel_id" jdbcType="INTEGER" property="materielId" />
    <result column="oss_url" jdbcType="VARCHAR" property="ossUrl" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, materiel_id, oss_url, sort, operator_id, created_at, updated_at, `status`
  </sql>
  <select id="selectByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumbExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from library_materiel_thumb
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from library_materiel_thumb
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from library_materiel_thumb
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumbExample">
    delete from library_materiel_thumb
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumb" useGeneratedKeys="true">
    insert into library_materiel_thumb (materiel_id, oss_url, sort, 
      operator_id, created_at, updated_at, 
      `status`)
    values (#{materielId,jdbcType=INTEGER}, #{ossUrl,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{operatorId,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumb" useGeneratedKeys="true">
    insert into library_materiel_thumb
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materielId != null">
        materiel_id,
      </if>
      <if test="ossUrl != null">
        oss_url,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materielId != null">
        #{materielId,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null">
        #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumbExample" resultType="java.lang.Long">
    select count(*) from library_materiel_thumb
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update library_materiel_thumb
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.materielId != null">
        materiel_id = #{record.materielId,jdbcType=INTEGER},
      </if>
      <if test="record.ossUrl != null">
        oss_url = #{record.ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update library_materiel_thumb
    set id = #{record.id,jdbcType=INTEGER},
      materiel_id = #{record.materielId,jdbcType=INTEGER},
      oss_url = #{record.ossUrl,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumb">
    update library_materiel_thumb
    <set>
      <if test="materielId != null">
        materiel_id = #{materielId,jdbcType=INTEGER},
      </if>
      <if test="ossUrl != null">
        oss_url = #{ossUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fotile.resourcescenter.test.pojo.entity.LibraryMaterielThumb">
    update library_materiel_thumb
    set materiel_id = #{materielId,jdbcType=INTEGER},
      oss_url = #{ossUrl,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>