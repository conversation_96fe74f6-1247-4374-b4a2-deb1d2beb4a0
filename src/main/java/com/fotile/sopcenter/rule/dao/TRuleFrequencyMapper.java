package com.fotile.sopcenter.rule.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.sopcenter.rule.pojo.entity.TRuleFrequency;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TRuleFrequencyMapper extends BaseMapper<TRuleFrequency> {
    int updateBatch(List<TRuleFrequency> list);

    int updateBatchSelective(List<TRuleFrequency> list);

    int batchInsert(@Param("list") List<TRuleFrequency> list);

    int insertOrUpdate(TRuleFrequency record);

    int insertOrUpdateSelective(TRuleFrequency record);
}