package com.fotile.sopcenter.rule.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.sopcenter.rule.pojo.entity.TRuleEngineMappingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TRuleEngineMappingMapper extends BaseMapper<TRuleEngineMappingEntity> {
    int updateBatch(List<TRuleEngineMappingEntity> list);

    int updateBatchSelective(List<TRuleEngineMappingEntity> list);

    int batchInsert(@Param("list") List<TRuleEngineMappingEntity> list);

    int insertOrUpdate(TRuleEngineMappingEntity record);

    int insertOrUpdateSelective(TRuleEngineMappingEntity record);
}