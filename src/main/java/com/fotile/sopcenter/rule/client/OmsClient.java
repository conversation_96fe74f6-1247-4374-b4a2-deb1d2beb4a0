package com.fotile.sopcenter.rule.client;

import com.fotile.framework.web.Result;

import com.fotile.sopcenter.rule.client.pojo.DecOrderMatchBO;
import com.fotile.sopcenter.rule.client.pojo.DryProcessOrderBO;
import com.fotile.sopcenter.rule.client.pojo.GoodsEntityVO;
import com.fotile.sopcenter.rule.client.pojo.QueryDecOrderByIdVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(value = "oms-center", path = "/api")
public interface OmsClient {
    @GetMapping(value = "/decOrder/queryDecOrderById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("根据线索id查询线索")
    public Result<QueryDecOrderByIdVO> queryDecOrderById(@RequestParam("orderId") Long orderId);

    @GetMapping(value = "open/decOrder/getOrderBasicById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("根据线索id查询线索")
    public Result<Map<String, String>> getOrderBasicById(@RequestParam("id") Long id);

    @PostMapping(value = "/open/dryProcess/order", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("根据线索id查询线索")
    public Result<DecOrderMatchBO> dryProcessOrder(DryProcessOrderBO dryProcessOrderBO);

    @ApiOperation("根据订单id查询商品数据")
    @RequestMapping(value = "/open/decOrder/goodsByOrderId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GoodsEntityVO>> goodsByOrderId(@RequestParam("orderId") Long orderId);



    //根据订单查询订单商品
    @RequestMapping(value = "/open/tServiceOrderInfo/middleground/getSerivceOrderInfoById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询服务工单基础详情")
    public Result<Map<String, String>> getSerivceOrderInfoById(@RequestParam("id") Long id) ;

    @ApiOperation("根据线索获取订单商品品类")
    @RequestMapping(value = "/open/decOrder/findGoodsModelByCluesId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Map<String,String>> findGoodsModelByCluesIdOrOrderId(
            @RequestParam(required = false) Long cluesId,
            @RequestParam(required = false) Long orderId
    );

}
