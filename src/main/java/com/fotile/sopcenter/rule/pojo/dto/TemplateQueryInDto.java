package com.fotile.sopcenter.rule.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/16 17:50
 */
@Data
public class TemplateQueryInDto {
    private Integer pageNum;

    private Integer pageSize;

    private Long offset;

    //模板id
    private Long id;

    //模板标题
    private String name;

    private String topTemplate;

    //模板内容
    private String contentTemplate;

    //模板类型
    private Integer type;

    //模板状态
    private Integer status;

    //开始时间
    private String createStartTime;

    //结束时间
    private String createEndTime;

    private String createdBy;

    //所属分公司
    private List<Long> companyIds;

    //应用页面
    private List<Long> pageIds;

    private Integer templateType;

    private Long pageId;
}
