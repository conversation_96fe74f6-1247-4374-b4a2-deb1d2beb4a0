package com.fotile.sopcenter.rule.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResourceRecordMappingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	
	@ApiModelProperty(value = "主键")
	private Long id;
	
	@ApiModelProperty(value = "资源链接")
	private String resourceUrl;
	
	@ApiModelProperty(value = "企业微信账户id")
	private String accountId;
	
	@ApiModelProperty(value = "素材id")
	private String mediaId;


}