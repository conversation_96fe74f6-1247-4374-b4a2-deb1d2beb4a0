package com.fotile.sopcenter.rule.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "com-fotile-msg-rule-pojo-entity-TRuleAssociatedMapping")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "msgcenter.t_rule_associated_mapping")
public class TRuleAssociatedMapping extends AuditingEntity {
    /**
     * 模块code
     */
    @TableField(value = "module_code")
    @ApiModelProperty(value = "模块code")
    private String moduleCode;

    /**
     * 应用页面code
     */
    @TableField(value = "application_page_code")
    @ApiModelProperty(value = "应用页面code")
    private String applicationPageCode;

    /**
     * 提醒用户类型
     */
    @TableField(value = "remind_user_type")
    @ApiModelProperty(value = "提醒用户类型 ")
    private Integer remindUserType;

    /**
     * 提醒方式
     */
    @TableField(value = "remind_way")
    @ApiModelProperty(value = "提醒方式")
    private Integer remindWay;

    /**
     * 规则id
     */
    @TableField(value = "rule_id")
    @ApiModelProperty(value = "规则id")
    private Long ruleId;

    public static final String COL_ID = "id";

    public static final String COL_IS_DELETED = "is_deleted";

    public static final String COL_CREATED_BY = "created_by";

    public static final String COL_CREATED_DATE = "created_date";

    public static final String COL_MODIFIED_BY = "modified_by";

    public static final String COL_MODIFIED_DATE = "modified_date";

    public static final String COL_MODULE_CODE = "module_code";

    public static final String COL_APPLICATION_PAGE_CODE = "application_page_code";

    public static final String COL_REMIND_USER_TYPE = "remind_user_type";

    public static final String COL_REMIND_WAY = "remind_way";

    public static final String COL_RULE_ID = "rule_id";
}