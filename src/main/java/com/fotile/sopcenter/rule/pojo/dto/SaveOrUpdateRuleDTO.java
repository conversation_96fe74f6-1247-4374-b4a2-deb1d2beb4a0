package com.fotile.sopcenter.rule.pojo.dto;


import com.fotile.sopcenter.rule.pojo.entity.TChildTemplateInfoEntity;
import com.fotile.sopcenter.rule.pojo.entity.TRuleConEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

@Data
public class SaveOrUpdateRuleDTO implements Serializable {
    /**
     * 规则id
     */
    private Long id;
    /**
     * 规则code
     */
    private String ruleCode;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则类型类别
     */
    private int ruleCategory;
    /**
     * 规则状态
     */
    private Long ruleStatus;
    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 跳转文本
     */
    private String jumpTitle;
    /**
     * 跳转url
     */
    private String jumpUrl;

    /**
     * 跳转类型
     */
    private Integer jumpType;
    /**
     * 子模板
     */
    private List<TChildTemplateInfoEntity> childTemplateInfoList;
    /**x
     * 条件集合
     */
    private List<TRuleConEntity> ruleConList;
    /**
     * 条件表达式
     */
    private String expression;

    /**
     * 提醒机制类型 1.实时（不需要填写时间） 2.小时(多少小时后) 3.定时（固定时间发送）
     */
    private Integer remindMechanismType;
    /**
     * 年份
     */
    private Integer remindMechanismYear;

    /**
     * 月份
     */
    private Integer remindMechanismMonth;

    /**
     * 日
     */
    private Integer remindMechanismDay;

    /**
     * 小时
     */
    private Integer remindMechanismHours;

    /**
     * 分钟
     */
    private Integer remindMechanismMinutes;
    /**
     * 频率类型:1.每次 2.触发次数
     */
    private Integer frequencyType;

    /**
     * 小时
     */
    private Integer frequencyHour;

    /**
     * 触发条数
     */
    private Integer frequencyNumber;
    /**
     * 排斥组
     */
    private String frequencyGroup;


    private LocalTime remindMechanismDayTime;

    private Integer isJump;

    private String companyName;

    private Long companyId;


    private String editorFieldId;

    private String editorAttributeValue;
    private String editorAttributeId;
    private String editorFieldName;

    private Integer priorityLevel;

    private  String type;

}
