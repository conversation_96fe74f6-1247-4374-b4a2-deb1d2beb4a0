package com.fotile.sopcenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.sopcenter.rule.dao.TTemplateFrequencyMapper;
import com.fotile.sopcenter.rule.pojo.entity.TTemplateFrequencyEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TTemplateFrequencyService extends ServiceImpl<TTemplateFrequencyMapper, TTemplateFrequencyEntity> {

    
    public int updateBatch(List<TTemplateFrequencyEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TTemplateFrequencyEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TTemplateFrequencyEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TTemplateFrequencyEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TTemplateFrequencyEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
