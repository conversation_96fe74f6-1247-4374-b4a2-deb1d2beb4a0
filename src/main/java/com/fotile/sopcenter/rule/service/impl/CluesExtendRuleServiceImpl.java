package com.fotile.sopcenter.rule.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fotile.framework.data.auth.utils.MD5Util;
import com.fotile.framework.web.Result;

import com.fotile.sopcenter.rule.client.CustomerInfoOpenClientService;
import com.fotile.sopcenter.rule.client.MarketingClient;
import com.fotile.sopcenter.rule.client.OmsClient;
import com.fotile.sopcenter.rule.client.OrgClient;
import com.fotile.sopcenter.rule.client.pojo.*;
import com.fotile.sopcenter.rule.enums.QywxCustomerTagRelationDTOGroupEnmus;
import com.fotile.sopcenter.rule.mq.pojo.CanalMapProcessInfo;
import com.fotile.sopcenter.rule.pojo.dto.RuleMatchDTO;
import com.fotile.sopcenter.rule.pojo.entity.TVariableInfo;
import com.fotile.sopcenter.rule.service.MatchCommonService;
import com.fotile.sopcenter.rule.service.RuleMatchService;
import com.fotile.sopcenter.rule.util.RuleConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CluesExtendRuleServiceImpl extends MatchCommonService implements RuleMatchService {


    @Autowired
    MarketingClient marketingClient;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    CustomerInfoOpenClientService customerInfoOpenClientService;
    @Autowired
    OmsClient omsClient;

    @Override
    public void match(RuleMatchDTO<String> ruleMatchDTO) {

    }

    @Override
    public <T> T queryOriginalBySourceId(String applicationCode, Long sourceId) {
        return null;
    }

    @Override
    public void canalMatch(CanalMapProcessInfo canalMapProcessInfo, String code) {

    }

    @Override
    public Map<String, String> getFact(CanalMapProcessInfo canalMapProcessInfo, String code) {
        //获取扩展表需要的字段
        List<TVariableInfo> tVariableInfos = getTvariableInfoByTableName(canalMapProcessInfo.getTableName());
        if (CollectionUtils.isEmpty(tVariableInfos)) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("0.00");

        TVariableInfo add = new TVariableInfo();
        add.setSourceTableField("id");
        add.setFiledName("id");
        tVariableInfos.add(add);
        TVariableInfo cluesId = new TVariableInfo();
        cluesId.setSourceTableField("user_clues_id");
        cluesId.setFiledName("cluesId");
        tVariableInfos.add(cluesId);
        Map<String, String> fact = new HashMap<>();
        extracted(canalMapProcessInfo, tVariableInfos, fact);
        fact.put("currentTime", String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        if (StringUtils.isNotBlank(fact.get("installDate"))) {
            fact.put("installDateFlag", "1");
            fact.put("installDate", df.format((Double.valueOf(fact.get("currentTime")) - Long.valueOf(fact.get("installDate"))) / 1000 / 3600 / 24));
        }
        if (StringUtils.isNotBlank(fact.get("cluesMoveinDate"))){
            fact.put("cluesMoveinDateFlag", "1");
            fact.put("cluesMoveinDate", df.format((Double.valueOf(fact.get("currentTime")) - Long.valueOf(fact.get("cluesMoveinDate"))) / 1000 / 3600 / 24));
        }

        if (StringUtils.isNotBlank(fact.get(RuleConstant.cluesId))) {
            Result<MonitorFactDTO> monitorFactDTOResult = marketingClient.customerPortraitCluesByCluesId(Long.valueOf(fact.get(RuleConstant.cluesId)));
            if (monitorFactDTOResult != null && monitorFactDTOResult.getData() != null) {

                MonitorFactDTO data = monitorFactDTOResult.getData();
                ObjectMapper objectMapper = new ObjectMapper();
                HashMap<String, String> map = objectMapper.convertValue(data, HashMap.class);
                fact.putAll(map);
                fact.put("cluesComeDeviseStatus", data.getCluesComeDeviseStatus());
                fact.put("comeDeviseStatus", data.getComeDeviseStatus());
                fact.put("comeDeviseStatusFlag", "1");
                fact.put("followUpScore", data.getFollowUpScore());
                fact.put("followUpScoreFlag", "1");
                fact.put("isSceneInteraction", data.getIsSceneInteraction());
                fact.put("isSceneInteractionFlag", "1");
                fact.put("cluesType", data.getCluesType());
                fact.put("cluesTypeFlag","1");
                fact.put("companyId", data.getCluesCompanyId());
                fact.put("cluesCompanyId", data.getCluesCompanyId());
                fact.put("serviceActionFlag","1");
                fact.put("serviceAction", data.getServiceAction());
                fact.put("cluesComeDeviseStatusFlag","1");
                fact.put("followUpCount",df.format(Double.valueOf(data.getFollowUpCount())) );
                fact.put("followUpCountFlag", "1");
                fact.put("followUpTypeCount", df.format(Double.valueOf(data.getFollowUpTypeCount())));
                fact.put("followUpTypeCountFlag", "1");
                //获取业务员数据
                if (StringUtils.isNotBlank(data.getCluesChargeUserId())) {
                    Result<FindSalesmanByIdOutDto> chargeUserIdResult = orgClient.apiOpenfindSalesmanById(Long.valueOf(data.getCluesChargeUserId()));
                    if (chargeUserIdResult != null && chargeUserIdResult.getSuccess() && chargeUserIdResult.getData() != null) {
                        FindSalesmanByIdOutDto findSalesmanByIdOutDto = chargeUserIdResult.getData();
                        if (findSalesmanByIdOutDto.getEntryDate() != null) {
                            fact.put("chargeUserEntryDate", String.valueOf((LocalDate.now().toEpochDay()  - LocalDate.ofInstant(findSalesmanByIdOutDto.getEntryDate().toInstant(), ZoneId.systemDefault()).toEpochDay())));
                            fact.put("chargeUserEntryDateFlag", "1");

                        }
                        fact.put("station", findSalesmanByIdOutDto.getStation());
                        fact.put("stationFlag", "1");

                        //判断业务员所属门店是否存在
                        if (ObjectUtils.isNotEmpty(findSalesmanByIdOutDto.getStoreId())) {
                            Result<FindStoreByIdOutDto> codeByOrgId = orgClient.findCodeByOrgId(findSalesmanByIdOutDto.getStoreId());
                            fact.put("chargeUserStoreId", findSalesmanByIdOutDto.getStoreId().toString());
                            fact.put("chargeUserStoreIdFlag", "1");
                            if (codeByOrgId != null && codeByOrgId.getSuccess() && codeByOrgId.getData() != null) {
                                FindStoreByIdOutDto store = codeByOrgId.getData();
                                //获取关键字
                                Result<List<String>> storeRealKeyWordListByStoreId = orgClient.getStoreRealKeyWordListByStoreId(store.getId());
                                if (storeRealKeyWordListByStoreId.getSuccess()){
                                    List<String> data2 = storeRealKeyWordListByStoreId.getData();
                                    if (CollectionUtils.isNotEmpty(data2)) {
                                        fact.put("keyWord", data2.stream().collect(Collectors.joining(",")));
                                        fact.put("keyWordFlag", "1");
                                    }
                                }
                                //获取门店渠道code
                                fact.put("storeTag", ObjectUtils.isNotEmpty(store) ? store.getKeyWord() : "");
                                fact.put("storeTagFlag", "1");

                                if (store.getStoreType() != null) {

                                    Result<Map<String,String>> channelById = orgClient.findChannelById(store.getStoreType());
                                    if (channelById != null && channelById.getSuccess() && channelById.getData() != null) {
                                        Map<String,String> data1 = channelById.getData();
                                        fact.put("storeType", ObjectUtils.isNotEmpty(data1) ? data1.get("code") : "");
                                        fact.put("storeTypeFlag", "1");

                                    }
                                }
                            }
                        }
                    }

                }
            }
            //获取标签
            Result<TQywxCustomerCluesRelationDTO> tQywxCustomerCluesRelationResult = marketingClient.queryQywxCustomerCluesRelationByCluesId(Long.valueOf(fact.get(RuleConstant.cluesId)));
            if (ObjectUtils.isNotEmpty(tQywxCustomerCluesRelationResult) && tQywxCustomerCluesRelationResult.getSuccess()) {
                TQywxCustomerCluesRelationDTO data = tQywxCustomerCluesRelationResult.getData();
                if (ObjectUtils.isNotEmpty(data)) {
                    String externalUserid = data.getExternalUserid();
                    String userId = data.getUserId();
                    Result<List<QywxCustomerTagRelationDTO>> listResult = customerInfoOpenClientService.queryQywxCustomerTagRelationByExternalUserIdAndUserId(externalUserid, userId, data.getAccountId());
                    if (ObjectUtils.isNotEmpty(listResult) && listResult.getSuccess()) {
                        //查询账号绑定的业务员分公司信息
                        Result<QywxUserInfoDTO> userInfo = customerInfoOpenClientService.getUserInfo(data.getAccountId(), userId);
                        if (userInfo.getSuccess() && ObjectUtils.isNotEmpty(userInfo.getData())){
                            QywxUserInfoDTO userinfodata = userInfo.getData();
                            fact.put("examinationChargeUserCompanyId", userinfodata.getCompanyId() == null ? "0" : userinfodata.getCompanyId().toString());
                            fact.put("examinationChargeUserStoreId", userinfodata.getStoreOrgId() == null ? "0" : userinfodata.getStoreOrgId().toString());
                        }
                        List<QywxCustomerTagRelationDTO> qywxCustomerTagRelationDTOList = listResult.getData();
                        if (CollectionUtils.isNotEmpty(qywxCustomerTagRelationDTOList)) {
                            fact.put("tagName", qywxCustomerTagRelationDTOList.stream().map(QywxCustomerTagRelationDTO::getTagName).collect(Collectors.joining(",")));
                            Map<String, List<QywxCustomerTagRelationDTO>> collect = qywxCustomerTagRelationDTOList.stream()
                                    .collect(Collectors.groupingBy(QywxCustomerTagRelationDTO::getGroupName, Collectors.toList()));
                            collect.keySet().forEach(key -> {
                                if (StringUtils.isNotBlank(QywxCustomerTagRelationDTOGroupEnmus.getSexEnumByCode(key))) {
                                    fact.put(QywxCustomerTagRelationDTOGroupEnmus.getSexEnumByCode(key),
                                            CollectionUtils.isNotEmpty(collect.get(key)) ?
                                                    collect.get(key).stream().map(QywxCustomerTagRelationDTO::getTagName).collect(Collectors.joining(",")) : null);

                                }
                            });
                        }
                    }

                }
            }
            //获取订单数据
            Result<Map<String, String>> goodModel = omsClient.findGoodsModelByCluesIdOrOrderId(Long.valueOf(fact.get("id")), null);
            if (goodModel != null && goodModel.getSuccess() && goodModel.getData() != null) {
                Map<String, String> data = goodModel.getData();
                if (StringUtils.isNotBlank(data.get("goodsModel"))) {
                    fact.put("goodsModel", data.get("goodsModel"));
                }
                if (StringUtils.isNotBlank(data.get("depositOrderAmount"))) {
                    fact.put("depositOrderAmount", data.get("depositOrderAmount"));
                    fact.put("depositOrderAmountFlag", "1");
                }
            }
            if (StringUtils.isBlank(fact.get("depositOrderAmount"))) {
                fact.put("depositOrderAmount","0.00");
                fact.put("depositOrderAmountFlag", "1");
            }
        }

        return fact;
    }

    @Override
    public void updateTimeXXlHandler(RuleMatchDTO<String> ruleMatchDTO, String code) {

    }

    @Override
    public Map<String, String> getSender(Map<String, String> params, String code) {
        Map<String, String> map = new HashMap<>();
        if (ObjectUtils.isNotEmpty(params.get(RuleConstant.cluesId))) {
            String cluesId = params.get(RuleConstant.cluesId);
            map.put("cluesId", cluesId);
            Result<QueryOriginalBySourceIdVO> queryOriginalBySourceIdVOResult = marketingClient.queryOriginalBySourceId(Long.valueOf(cluesId), "002");
            if (ObjectUtils.isNotEmpty(queryOriginalBySourceIdVOResult) && queryOriginalBySourceIdVOResult.getSuccess() && ObjectUtils.isNotEmpty(queryOriginalBySourceIdVOResult.getData())) {
                //获取线索信息
                QueryOriginalBySourceIdVO data1 = queryOriginalBySourceIdVOResult.getData();
                if (ObjectUtils.isNotEmpty(data1.getCustomerId())) {
                    map.put(RuleConstant.chargeUserId, data1.getChargeUserId());
                    //获取业务员信息
                    if (StringUtils.isNotBlank(data1.getChargeUserId())) {
                        Result<FindSalesmanByIdOutDto> chargeUserIdResult = orgClient.apiOpenfindSalesmanById(Long.valueOf(data1.getChargeUserId()));
                        if (chargeUserIdResult != null && chargeUserIdResult.getSuccess() && chargeUserIdResult.getData() != null){
                            map.put("storeId", chargeUserIdResult.getData().getStoreId() == null ? "0" : String.valueOf(chargeUserIdResult.getData().getStoreId()));
                            map.put("companyId", chargeUserIdResult.getData().getCompanyId() == null ? "0" : String.valueOf(chargeUserIdResult.getData().getCompanyId()) );
                        }
                    }
                    map.put(RuleConstant.customerId, String.valueOf(data1.getCustomerId()));
                    Result<List<CustomerInfoDTO>> result = customerInfoOpenClientService.queryCustomerInfoByIds(String.valueOf(data1.getCustomerId()));
                    if ((result != null && result.getData() != null) ||
                            (result != null && result.getSuccess() && CollectionUtils.isNotEmpty(result.getData()))) {
                        map.put("phoneEnc", MD5Util.getSaltMD5(result.getData().get(0).getPhone()));

                    }
                }
                //获取线索与企业微信关系
                Result<TQywxCustomerCluesRelationDTO> tQywxCustomerCluesRelationResult = marketingClient.queryQywxCustomerCluesRelationByCluesId(Long.valueOf(cluesId));
                if (ObjectUtils.isNotEmpty(tQywxCustomerCluesRelationResult) && tQywxCustomerCluesRelationResult.getSuccess()) {
                    TQywxCustomerCluesRelationDTO data = tQywxCustomerCluesRelationResult.getData();
                    if (ObjectUtils.isNotEmpty(data)) {
                        map.put("externalUserid", data.getExternalUserid());
                        map.put("userId", data.getUserId());
                        map.put("unionId", data.getUnionId());

                    }
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, String> checkAgainFact(Long sourceId, String code, Long ruleId) {
        return null;
    }
}
