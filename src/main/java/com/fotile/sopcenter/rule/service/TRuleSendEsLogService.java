package com.fotile.sopcenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.sopcenter.rule.dao.TRuleSendEsLogMapper;
import com.fotile.sopcenter.rule.pojo.entity.TRuleSendEsLogEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TRuleSendEsLogService extends ServiceImpl<TRuleSendEsLogMapper, TRuleSendEsLogEntity> {


    public int updateBatch(List<TRuleSendEsLogEntity> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<TRuleSendEsLogEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<TRuleSendEsLogEntity> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(TRuleSendEsLogEntity record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(TRuleSendEsLogEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}







