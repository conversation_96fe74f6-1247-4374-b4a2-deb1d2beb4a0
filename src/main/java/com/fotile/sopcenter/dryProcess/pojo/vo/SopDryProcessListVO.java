package com.fotile.sopcenter.dryProcess.pojo.vo;

import com.fotile.sopcenter.dryProcess.pojo.entity.TSopMsgSendLogEntity;
import com.fotile.sopcenter.pojo.mapper.BaseConvertor;
import lombok.Data;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SopDryProcessListVO implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 主id
     */
    private Long sopMainId;

    /**
     * 发送时间
     */
    private Date sendTime;

    private String qywxUserId;
    private String qywxExternalUserid;



    /**
     *  提醒内容
     */
    private String reminderContent;
    /**
     * 链接类型 1H5，2内容，3活动
     */
    private Integer linkType;
    /**
     * 链接描述
     */
    private String linkDescription;

    private String linkUrl;
    /**
     * 顾客信息集合
     */
    private List<SopQueryCustomerConfigVO> customerConfigs;

    /**
     * 是否已读，0未读，1已读
     */
    private Integer isRead;
    /**
     * 是否有用，1有用
     */
    private Integer isUseful;
    /**
     * 有用数量
     */
    private Integer readNum;
    /**
     * 有用数量
     */
    private Integer usefulNum;

    /**
     * accountId
     */
    private String accountId;

    private Integer isSend;
    @Mapper
    public interface Convertor extends BaseConvertor<SopDryProcessListVO, TSopMsgSendLogEntity> {
        public static final SopDryProcessListVO.Convertor   SOPDRYPROCESSLISTVO_CONVERTOR = Mappers.getMapper(SopDryProcessListVO.Convertor.class);
    }
}
