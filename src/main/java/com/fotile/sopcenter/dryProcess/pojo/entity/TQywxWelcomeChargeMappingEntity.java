package com.fotile.sopcenter.dryProcess.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * 欢迎语-业务员关联表
 */
@Data
@EqualsAndHashCode(callSuper=true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`customercenter`.`t_qywx_welcome_charge_mapping`")
public class TQywxWelcomeChargeMappingEntity extends AuditingEntity {
    /**
     * 欢迎语id
     */
    @TableField(value = "`welcome_main_id`")
    @NotNull(message = "欢迎语id不能为null")
    private Long welcomeMainId;

    /**
     * 业务员id
     */
    @TableField(value = "`charge_user_id`")
    private Long chargeUserId;

    /**
     * 业务员名称
     */
    @TableField(value = "`charge_user_name`")
    @Size(max = 300,message = "业务员名称最大长度要小于 300")
    @FieldEncrypt
    private String chargeUserName;

    /**
     * 业务员编码
     */
    @TableField(value = "`charge_code`")
    @Size(max = 200,message = "业务员编码最大长度要小于 200")

    private String chargeCode;

    /**
     * 业务员手机号
     */
    @TableField(value = "`charge_phone`")
    @Size(max = 200,message = "业务员手机号最大长度要小于 200")
    @FieldEncrypt
    private String chargePhone;

    /**
     * 1欢迎语业务员，3导购名片业务员
     */
    @TableField(value = "`source_type`")
    @NotNull(message = "1欢迎语业务员，3导购名片业务员不能为null")
    private Integer sourceType;


    /**
     * 企微成员id
     */
    @TableField(exist = false)
    private String userId;

    public static final String COL_ID = "id";

    public static final String COL_IS_DELETED = "is_deleted";

    public static final String COL_CREATED_BY = "created_by";

    public static final String COL_CREATED_DATE = "created_date";

    public static final String COL_MODIFIED_BY = "modified_by";

    public static final String COL_MODIFIED_DATE = "modified_date";

    public static final String COL_WELCOME_MAIN_ID = "welcome_main_id";

    public static final String COL_CHARGE_USER_ID = "charge_user_id";

    public static final String COL_CHARGE_USER_NAME = "charge_user_name";

    public static final String COL_CHARGE_CODE = "charge_code";

    public static final String COL_CHARGE_PHONE = "charge_phone";

    public static final String COL_SOURCE_TYPE = "source_type";
}