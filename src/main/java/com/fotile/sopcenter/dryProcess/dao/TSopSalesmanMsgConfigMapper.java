package com.fotile.sopcenter.dryProcess.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.sopcenter.dryProcess.pojo.entity.TSopSalesmanMsgConfigEntity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TSopSalesmanMsgConfigMapper extends BaseMapper<TSopSalesmanMsgConfigEntity> {
    int insertSelective(TSopSalesmanMsgConfigEntity record);

    int updateByPrimaryKeySelective(TSopSalesmanMsgConfigEntity record);

    int updateBatch(List<TSopSalesmanMsgConfigEntity> list);

    int updateBatchSelective(List<TSopSalesmanMsgConfigEntity> list);

    int batchInsert(@Param("list") List<TSopSalesmanMsgConfigEntity> list);

    int insertOrUpdate(TSopSalesmanMsgConfigEntity record);

    int insertOrUpdateSelective(TSopSalesmanMsgConfigEntity record);
}