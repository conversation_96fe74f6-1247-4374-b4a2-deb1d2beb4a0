package com.fotile;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.fotile.pointshopcenter.benefits.mq.BenefitMallMessageChannel;
import com.fotile.pointshopcenter.client.impl.ScheduleMsgChannel;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = {"org.keycloak", "com.fotile"}, exclude = {DruidDataSourceAutoConfigure.class})
@EnableDiscoveryClient
@EnableBinding(value = {ScheduleMsgChannel.class, BenefitMallMessageChannel.class})

@MapperScan(basePackages = {"com.fotile"})
@EnableFeignClients(basePackages = "com.fotile")
//允许使用springcache 在方法
@EnableCaching
public class PointShopCenterApplication {
    public static void main(String[] args) {
        SpringApplication.run(PointShopCenterApplication.class, args);
        System.out.println("The pointshop-center service started.");
    }

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(30000);
        //设置超时
        requestFactory.setReadTimeout(30000);
        return new RestTemplate(requestFactory);
    }

    @Bean
    public Executor executor() {
        return Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);
    }

}
