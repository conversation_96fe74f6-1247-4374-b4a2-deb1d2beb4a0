package com.fotile.omscenter.client;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.web.Result;
import com.fotile.omscenter.client.pojo.*;
import com.fotile.omscenter.client.pojo.org.DecorateAndDesignerDto;
import com.fotile.omscenter.client.pojo.org.InstallCheckOutDto;
import com.fotile.omscenter.client.pojo.org.designer.DesignerClubDesignerVO;
import com.fotile.omscenter.client.pojo.org.saleman.GetSalesmanByStoreOrgIdsInDTO;
import com.fotile.omscenter.client.pojo.org.saleman.SalesmanInDto;
import com.fotile.omscenter.client.pojo.org.saleman.SalesmanOutDto;
import com.fotile.omscenter.client.pojo.org.store.FindStoreNameListByOrgIdsOutDto;
import com.fotile.omscenter.client.pojo.org.store.StoreByStoreLevelsOutDto;
import com.fotile.omscenter.client.pojo.org.store.StoreInDto;
import com.fotile.omscenter.client.pojo.org.store.StoreOutDto;
import com.fotile.omscenter.common.Kv;
import com.fotile.omscenter.decOrder.pojo.InsertOperatorOrgLogInDto;
import com.fotile.omscenter.decOrder.pojo.dto.DesignerOrderUpdateDto;
import com.fotile.omscenter.decOrder.pojo.dto.SelectStoreByStoreLevelsInDto;
import com.fotile.omscenter.decOrder.pojo.dto.StoreBaseInfoDto;
import com.fotile.omscenter.decOrder.pojo.dto.quickOrder.StoreInfo;
import com.fotile.omscenter.tServiceOrderInfo.pojo.dto.FindChannelByCodeOutDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@FeignClient(value = "org-center")
public interface OrgClient {

    /**
     * 根据业务员id获取详细信息
     */
    @GetMapping(value = "/api/salesman/getSalesmanDetailById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<SalesmanDetailVo> getSalesmanDetailById(@RequestParam("id") Long id);

    /**
     * 根据业务员id获取详细信息
     */
    @GetMapping(value = "/api/salesman/api/open/getSalesmanDetailById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<SalesmanDetailVo> getSalesmanDetailByIdOpen(@RequestParam("id") Long id);

    /**
     * 查询所有store,数据量太大
     * 此接口有性能问题，勿用
     *
     * @return
     */
    @GetMapping("/api/store/api/open/findAllStore")
    @Deprecated
    Result<HashMap<String, FindStoreByIdOutDto>> getAllStores();

    @PostMapping(value = "/api/store/api/open/findAllStoreByIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<HashMap<String, FindStoreByIdOutDto>> findAllStoreByIds(@RequestBody Set<Long> ids);

    @PostMapping(value = "/api/store/api/open/findAllStoreByCodes")
    public Result<HashMap<String, FindStoreByIdOutDto>> findAllStoreByCodesOpen(@RequestBody Set<String> codes);

    /**
     * 获取分公司名称
     *
     * @return
     */
    @GetMapping("/api/org/api/open/getAllCompany")
    Result<Map<String, StoreAndOrgCommonDto>> getAllCompany();

    /**
     * 根据名称获取分公司
     *
     * @param names
     * @return
     */
    @GetMapping("/api/org/getCompanyByName")
    Result<Map<String, StoreAndOrgCommonDto>> getCompanyByName(@RequestParam("names") List<String> names);

    /**
     * 获取家装公司
     *
     * @return
     */
    @GetMapping("/api/decorate/decorateNameAndCategory")
    @Deprecated
    public Result<Map<String, DecorateEntity>> decorateNameAndCategory();


    /**
     * 根据编码获取家装公司
     *
     * @param code
     * @return
     */
    @PostMapping("/api/decorate/byCode")
    Result<Map<String, DecorateEntity>> byCode(List<String> code);

    //根据id查询业务员
    @GetMapping("/api/salesman/api/open/findById")
    public Result<FindSalesmanByIdOutDto> findSalesmanById(@RequestParam("id") Long id);

    // 查询当前业务员
    @GetMapping("/api/open/salesman/findSalesmanById")
    public Result<SalesmanEntity> findSalesmanByIdOpen(@RequestParam("id") Long id);

    @GetMapping("/api/m/salesman/findSalesmanById")
    public Result<SalesmanEntity> mFindSalesmanById(@RequestParam("id") Long id);


    //根据id查询业务员信息，门店，分公司
    @GetMapping("/api/salesman/api/open/getSalesmanBaseInfoById")
    public Result<StoreBaseInfoDto> getSalesmanBaseInfoById(@RequestParam("id") Long id);

    //根据id集合查询业务员
    @PostMapping(value = "/api/salesman/findByIds2", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanEntity>> findSalesmanByIds(@RequestBody FindSalesmanByIds2InDto findSalesmanByIds2InDto);

    @RequestMapping(value = "/api/salesman/api/open/findByIds2", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanEntity>> findSalesmanByIds2(@RequestBody @Valid FindSalesmanByIds2InDto findSalesmanByIds2InDto);

    @RequestMapping(value = "/api/m/salesman/findByIds2", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanEntity>> mFindSalesmanByIds2(@RequestBody @Valid FindSalesmanByIds2InDto findSalesmanByIds2InDto);

    @RequestMapping(value = "/api/open/salesman/getDefendantExecutor", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<SalesmanEntity> getDefendantExecutor(@RequestParam("storeId") Long storeId);


    //根据公司数据权限查询门店 status:门店状态，0：禁用；1：启用
    @GetMapping("/api/store/findPageAll")
    public Result<PageInfo<FindStorePageAllOutDto>> findStorePageAll(@RequestParam("page") Integer page, @RequestParam("size") Integer size, @RequestParam("status") Integer status);

    //根据公司数据权限查询门店 status:门店状态，0：禁用；1：启用
    @GetMapping("/api/store/findPageAllWithNoAuthor")
    public Result<PageInfo<FindSalesmanPageAllOutDto>> findPageAllWithNoAuthor(@RequestParam("page") Integer page, @RequestParam("size") Integer size, @RequestParam("status") Integer status);

    //根据门店id查询业务员 findOthers:当前门店没有业务员是否要查询附近门店业务员0:是；1：否
    @GetMapping("/api/open/findSalesmanByStoreId")
    public Result<PageInfo<FindSalesmanByStoreIdOutDto>> findSalesmanByStoreId(@RequestParam("page") Integer page, @RequestParam("size") Integer size, @RequestParam("storeId") Long storeId, @RequestParam("findOthers") Integer findOthers);

    //根据id查询name
    @GetMapping("/api/org/findNameById")
    public Result<FindOrgByIdOutDto> findNameById(@RequestParam("id") Long id);

    //根据公司id查询公司信息
    @GetMapping("/api/company/api/open/findByOrgId")
    public Result<FindCompanyByIdOutDto> findCompanyById(@RequestParam("id") Long id);

    /**
     * 判单使用
     */
    @GetMapping("/api/company/api/open/getLitigationCompany")
    Result<FindCompanyByIdOutDto> getLitigationCompany(@RequestParam Long orgId);
    @GetMapping("/api/company/m/findByOrgId")
    public Result<FindCompanyByIdOutDto> mFindCompanyById(@RequestParam("id") Long id);

    @PostMapping(value = "/api/company/api/open/findByIds2")
    public Result<List<FindCompanyByIdOutDto>> findCompanyByIds(@RequestBody List<Long> ids);

    @GetMapping("/api/company/api/open/findByCodeOpen")
    public Result<FindCompanyByIdOutDto> findCompanyByCodeOpen(@RequestParam("code") String code);


    //根据公司id查询公司信息
    @GetMapping("/api/company/api/open/findByOrgId")
    public Result<FindCompanyByIdOutDto> findCompanyByIdOpen(@RequestParam("id") Long id);


//根据门店id查询公司信息
    //@GetMapping("/api/store/api/open/findByOrgId")
    //public Result<FindStoreByOrgIdOutDto> findStoreById(@RequestParam("orgId")Long orgId);

    //批量更改带单数
    @PostMapping(value = "/api/designer/updateDesignerOrder", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result updateDesignerOrder(List<DesignerOrderUpdateDto> designerOrderUpdateDtos);

    //根据id集合批量查询name
    @PostMapping(value = "/api/org/findNameByIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindOrgByIdOutDto>> findNameByIds(@RequestBody FindNameByIdsInDto findNameByIdsInDto);

    @RequestMapping(value = "/api/org/api/open/findNameByIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindOrgByIdOutDto>> findNameByIdList(@RequestBody FindNameByIdsInDto findNameByIdsInDto);

    //根据门店类型查询门店
    @GetMapping("/api/store/findStoreByStoreType")
    public Result<List<StoreEntity>> findStoreByStoreType(@RequestParam("storeType") List<Integer> storeType);

    //根据门店类型查询门店
    @GetMapping("/api/decorate/api/open/findByCode")
    public Result<DecorateEntity> findDecorateByCode(@RequestParam("code") String code);

    //根据小区id查询小区信息
    @GetMapping("/api/village/findById")
    public Result<FindVillageByIdOutDto> findVillageById(@RequestParam("id") Long id);

    @GetMapping(value = "/api/store/findById")
    public Result<FindStoreByIdOutDto> findById(@RequestParam("id") Long id);

    //根据门店id集合查询门店
    @PostMapping(value = "/api/store/findByIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByIdOutDto>> findByIds(@RequestBody FindStoreByIdsInDto findStoreByIdsInDto);

    @PostMapping(value = "/api/store/getCommonlyUsedStores", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CommonlyUsedStoreDto> getCommonlyUsedStores(@RequestBody StoreInfo dto);

    @PostMapping(value = "/api/decorate/api/open/findByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DecorateEntity>> findByCodes(@RequestBody FindDecorateByCodesInDto findDecorateByCodeInDto);

    @PostMapping(value = "/api/village/findVillageByVillageIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindVillageByIdOutDto>> findVillageByVillageIds(@RequestBody FindVillageByVillageIdsInDto findVillageByVillageIdsInDto);

    //根据门店orgIds查询门店
    @PostMapping(value = "/api/store/api/open/findByOrgIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByOrgIdOutDto>> findByOrgIds(@RequestBody FindStoreByOrgIdsInDto storeByOrgIdsInDto);

    //根据公司ID，取得大区的code 大区的名称
    @GetMapping("/api/company/api/open/findCompanyInfoByOrgId")
    public Result<Map<String, String>> findCompanyInfoById(@RequestParam("id") Long id);

    //根据门店ID取得经销商的code 经销商名字 渠道大分类 渠道细分分类
    @PostMapping(value = "/api/distributor/api/open/findDistributorByStoreId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DistributorEntity>> findDistributorByStoreId(@RequestBody FindDistributorByStoreIdOutDto findDistributorByStoreIdOutDto);

    //根据门店orgId查询门店
    @GetMapping(value = "/api/store/api/open/findByOrgId")
    public Result<FindStoreByOrgIdOutDto> findStoreByOrgId(@RequestParam("orgId") Long orgId);

    @GetMapping(value = "/api/store/api/m/findByOrgId")
    public Result<FindStoreByOrgIdOutDto> mFindStoreByOrgId(@RequestParam("orgId") Long orgId);


    //根据门店orgId查询门店
    @GetMapping(value = "/api/store/api/open/findByOrgId")
    public Result<FindStoreByOrgIdOutDto> findStoreByCode(@RequestParam("storeCode") String storeCode);

    @GetMapping(value = "/api/store/api/open/findByOrgId")
    public Result<FindStoreByOrgIdOutDto> findStoreByOrgIdCTB(@RequestParam("orgId") Long orgId, @RequestHeader("Authorization") String authorization);

    //根据公司id查询订单校验字段
    @GetMapping(value = "/api/order/api/open/queryOrderCheckByCompanyId")
    public Result<List<QueryOrderCheckByCompanyIdOutDto>> queryOrderCheckByCompanyId(@RequestParam("companyId") Long companyId, @RequestParam("type") Integer type, @RequestParam("stage") Integer stage);


    @PostMapping("/api/channel/findByCodes")
    public Result<List<FindChannelByCodeOutDto>> findChannelByCodes(List<String> channelCodeList);

    /**
     * 根据手机号查询技师
     */
    @RequestMapping(value = "/api/serviceStaffInfo/api/open/findServiceStaffByPhone", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhone(@RequestParam("staffPhone") String staffPhone);

    @RequestMapping(value = "/api/serviceStaffInfo/api/m/findServiceStaffByPhone", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhonem(@RequestParam("staffPhone") String staffPhone);

    @RequestMapping(value = "/api/serviceStaffInfo/api/open/findServiceStaffByPhoneList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindServiceStaffOutDto>> findServiceStaffByPhoneList(@RequestBody FindServiceStaffByPhonesInDto staffByPhonesInDto);

    @RequestMapping(value = "/api/serviceStaffInfo/findServiceStaffInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ServiceStaffListVO>> findServiceStaffInfo(@RequestParam("page") Long page, @RequestParam("size") Long size);

    //根据公司id查询订单校验字段
    @GetMapping(value = "/api/designer/findDecorateOrDesignerByCodes")
    public Result<List<FindDecorateOrDesingerByCodesOutDto>> findDecorateOrDesignerByCodes(@RequestParam("decorateCompanyCode") String decorateCompanyCode, @RequestParam("designerCode") String designerCode);


    //根据code查询phone，内部调用-wangzz
    @RequestMapping(value = {
            "/api/diffIndustry/findAllByCodes"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindDiffIndustryAllOutDto>> findAllByCodes(@RequestBody FindDiffIndustryAllInDto allInDto);

    //根据id查询业务员
    @GetMapping("/api/m/salesman/getSalesmanByStoreOrgIdsAndStation")
    public Result<List<FindSalesmanByStoreIdOutDto>> mGetSalesmanByStoreOrgIdsAndStation(@RequestParam("storeId") Long storeId,@RequestParam("stationIdList") List<Integer> stationIdList);

    //根据id查询业务员
    @GetMapping("/api/salesman/api/open/getSalesmanByStoreOrgIdsAndStation")
    public Result<List<FindSalesmanByStoreIdOutDto>> openGetSalesmanByStoreOrgIdsAndStation(@RequestParam("storeId") Long storeId,@RequestParam("stationIdList") List<Integer> stationIdList);


    //根据门店编码集合查询门店
    @PostMapping(value = "/api/store/findByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByIdOutDto>> findStoreByCodes(@RequestBody FindStoreByCodesInDto findStoreByCodesInDto);

    //根据公司编码集合查询公司
    @PostMapping(value = "/api/store/findByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<CompanyEntity>> findCompanyByCodes(@RequestBody FindCompanyByCodesInDto findCompanyByCodesInDto);

    //根据id查询业务员
    @GetMapping("/api/decorate/platOrderUse")
    public Result<PageInfo<PlatOrderUseOutDto>> platOrderUse(@RequestParam("decorateCompanyType") Integer decorateCompanyType, @RequestParam("page") Integer page, @RequestParam("size") Integer size, @RequestParam("decorateCompanyCode") String decorateCompanyCode);

    /**
     * 根据关键字查询门店orgId
     * keyWord 多个值用逗号分隔
     *
     * @return
     */
    @GetMapping("/api/store/findByKeyWord")
    Result<List<Long>> findStoreByKeyWord(@RequestParam("keyWord") String keyWord);

    //根据id查询name
    @GetMapping("/api/org/api/open/findNameByCode")
    public Result<List<FindOrgByIdOutDto>> findNameByCode(@RequestParam("code") String code, @RequestParam("type") Integer type);


    //根据门店id查询一条业务员信息返回
    @RequestMapping(value = "/api/open/findOneSalesmanInfoByStoreId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindSalesmanByIdOutDto> findOneSalesmanInfoByStoreId(@RequestParam("storeId") Long storeId);

    //安装单号检查
    @PostMapping(value = "/api/install/installCheck", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> installCheck(@RequestBody InstallCheckInDto installCheckInDto);

    @PostMapping(value = "/api/install/installCheckStore", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<InstallCheckOutDto> installCheckStore(@RequestBody InstallCheckInDto installCheckInDto);

    //关联订单号
    @PostMapping(value = "/api/install/relationInstall", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> relationInstall(@RequestBody RelationInstallInDto relationInstallInDto);

    //批量关联订单号
    @PostMapping(value = "/api/install/api/open/batchRelationInstall", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> batchRelationInstall(@RequestBody List<RelationInstallInDto> relationInstallInDtoList);


    //更新drp订单号
    @PostMapping(value = "/api/install/api/open/updateDrpOrderId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> updateDrpOrderId(@RequestBody UpdateDrpOrderIdInDto updateDrpOrderIdInDto);

    //删除安装单号关联
    @PostMapping(value = "/api/install/api/open/delRelationInstall", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> delRelationInstall(@RequestBody RelationInstallInDto relationInstallInDto);

    //根据公司id查询公司信息
    @RequestMapping(value = "/api/open/findByOrgId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindCompanyByOrgIdOutDto> getCompanyByOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 根据分公司orgid查询
     */
    @RequestMapping(value = "/api/pstore/api/open/pStoreFindByCompanyOrgId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PStoreFindByCompanyOrgIdOutDto> pStoreFindByCompanyOrgId(@RequestParam("orgId") Long orgId, @RequestParam("provinceCode") Long provinceCode);

    /**
     * 根据orgId集合查询分公司信息 --msp
     */
    @RequestMapping(value = "/api/company/api/open/findCompanyByOrgIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindCompanyByOrgIdsVO>> findCompanyByOrgIds(@RequestBody FindCompanyByOrgIdsDTO findCompanyByOrgIdsDTO);

    /**
     * 根据经销商id查询经销商信息批量
     */
    @RequestMapping(value = "/api/distributor/api/open/findDistributorByIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindDistributorByIdsOutDTO>> findDistributorByIds(@RequestBody FindDistributorByIdDTO findDistributorByIdDTO);

    @PostMapping(value = "/api/distributor/findPageDistributorByOrgId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<DistributorSimpleOutDto>> findPageDistributorByOrgId(@RequestBody @Valid DistributorPageDTO dto);

    @PostMapping(value = "/api/distributor/findDistributorListByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DistributorSimpleOutDto>> findDistributorListByCodes(@RequestBody DistributorSimpleListInDTO dto);

    /**
     * 根据orgId(分公司id)查询客户列表
     */
    @PostMapping(value = "/api/distributor/findListDistributorByOrgId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<DistributorSimpleOutDto>> findListDistributorByOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 根据设计师编码查询设计师
     */
    @PostMapping("/api/designer/findSampleDesignerByDesignerCode")
    public Result<DesignerSampleDTO> findSampleDesignerByDesignerCode(@RequestParam("code") String code);

    /**
     * 根据门店orgid、岗位、员工状态查询业务员
     */
    @PostMapping(value = "/api/salesman/listSalesmanByStoreOrgIdsAndStationAndStatus", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GetSalesmanByStoreOrgIdsOutDTO>> listSalesmanByStoreOrgIdsAndStationAndStatus(@RequestBody @Valid ListSalesmanByStoreOrgIdsInDTO dto);

    /**
     * 根据门店orgid、岗位、员工状态查询业务员
     */
    @PostMapping(value = "/api/m/salesman/listSalesmanByStoreOrgIdsAndStationAndStatus", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GetSalesmanByStoreOrgIdsOutDTO>> mListSalesmanByStoreOrgIdsAndStationAndStatus(@RequestBody @Valid ListSalesmanByStoreOrgIdsInDTO dto);

    @GetMapping(value = "/api/salesman/getSalesmanByUserId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<SalesmanEntity> getSalesmanByUserId(@RequestParam("userId") String userId);

    /**
     * 根据门店id 查询门店信息
     */
    @GetMapping("/api/store/api/open/findByOrgId")
    Result<FindStoreByIdOutDto> findStoreById(@RequestParam("orgId") Long storeId);

    /**
     * 根据门店id 查询门店信息
     */
    @GetMapping("/api/store/api/m/findByOrgId")
    Result<FindStoreByIdOutDto> findStoreById2(@RequestParam("orgId") Long storeId);

    /**
     * 根据门店id 查询门店信息
     */
    @PostMapping("/api/store/api/m/findByOrgIds")
    Result<List<FindStoreByOrgIdOutDto>> mFindStoreById(@RequestBody FindStoreByOrgIdsInDto findStoreByOrgIdInDto);


    /**
     * 查询客户详情
     */
    @GetMapping("/api/distributor/api/open/findById")
    Result<DistributorEntity> findDistributorById(@RequestParam("id") Long id, @RequestParam("orgId") Long orgId);

    /**
     * 查询客户详情
     */
    @GetMapping("/api/distributor/api/open/findDistributorInfoByIds")
    Result<List<DistributorEntity>> findDistributorInfoByIds(DistributorSimpleListInDTO dto);

    /**
     * 新增日志
     *
     * @param operatorOrgLogEntity
     * @return
     */
    @PostMapping(value = "/api/operatorOrgLog/insertOperatorOrgLog", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result insertOperatorOrgLog(@RequestBody InsertOperatorOrgLogInDto operatorOrgLogEntity);


    /**
     * 根据门店类别查询
     */
    @RequestMapping(value = {"/api/store/selectStoreByStoreLevels"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<StoreEntity>> selectStoreByStoreLevels(@RequestParam("keyWordSet") Set<String> keyWordSet, @RequestParam("storeLevels") Set<String> storeLevels, @RequestParam("storeType") List<Integer> storeType, @RequestParam("storeSubChannelCodeList") Set<String> storeSubChannelCodeList);

    /**
     * 根据门店类别查询
     */
    @RequestMapping(value = {"/api/store/selectStoreByStoreLevelsPost"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<StoreByStoreLevelsOutDto>> selectStoreByStoreLevelsPost(SelectStoreByStoreLevelsInDto selectStoreByStoreLevelsInDto);

    /**
     * 根据门店类别查询
     */
    @RequestMapping(value = {"/api/store/api/open/selectStoreByStoreLevels2"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<StoreEntity>> selectStoreByStoreLevels2(@RequestParam("keyWordSet") Set<String> keyWordSet, @RequestParam("storeLevels") Set<String> storeLevels, @RequestParam("storeType") List<Integer> storeType);


    //包括门店、客户、分公司基本信息

    /**
     * 根据门店orgId查询门店
     */
    @RequestMapping(value = "/api/store/api/open/getStoreBaseInfoByOrgId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<StoreBaseInfoDto> getStoreBaseInfoByOrgId(@RequestParam("storeId") Long storeId);


    @PostMapping(value = "/api/order/api/open/getOrderChannelPost", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<OrderChannelVto> getOrderChannelPost(@RequestBody OrderChannelRto orderChannelRto);

    @PostMapping(value = "/api/order/api/open/getOrderChannelName", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Map<Integer, String>> getOrderChannelName(@RequestBody List<OrderChannelRto> orderChannelRtoList);

    @PostMapping(value = "/api/install/api/open/checkInstallExist", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Integer>> checkInstallExist(@RequestBody List<ImportQueryInstallDto> installDtoList);


    /**
     * 根据编码查询家装公司
     */
    @RequestMapping(value = {"api/decorate/api/open/findByCode"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<DecorateEntity> findByCodeOpen(@RequestParam("code") String code);

    /**
     * 根据门店状态获取分公司权限交门店权限的门店orgId列表
     */
    @RequestMapping(value = "/api/store/getOrgIdListByCompanyCrossStoreAuthor", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<Long>> getOrgIdListByCompanyCrossStoreAuthor(@RequestParam(required = false) Set<Integer> dto);

    @RequestMapping(value = {"/api/salesman/api/open/getSalesmanSimpleInfoList"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<FindSalesmanSimpleInfoOutDto>> getSalesmanSimpleInfoList(@Valid @RequestBody FindSalesmanSimpleInfoInDto query);

    //[性能优化-通用权限]根据门店状态及不同权限获取门店列表(authorType:0->不校验权限；1-分公司权限;2-门店权限;3-分公司交门店权限)
    @RequestMapping(value = "/getStoreAuthorListByDiffAuthor", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<StoreAuthor>> getStoreAuthorListByDiffAuthor(@RequestParam(value = "statusSet", required = false) Set<Integer> statusSet,
                                                                    @RequestParam Integer authorType);

    @GetMapping("/api/store/findByKeyWord")
    Result<List<Long>> findStoreByKeyWordWithStoreScope(@RequestParam("keyWord") String keyWord,
                                                        @RequestParam("needStoreDataScope") Boolean needStoreDataScope);

    /**
     * 查询门店
     */
    @GetMapping(value = "/api/store/find")
    Result<List<StoreOutDto>> find(@SpringQueryMap StoreInDto inDto);

    /**
     * 查询业务员
     */
    @GetMapping(value = "/api/salesman/find")
    Result<List<SalesmanOutDto>> find(@SpringQueryMap SalesmanInDto inDto);

    @RequestMapping(value = "api/village/api/open/getRelatedIdListById", method = RequestMethod.GET)
    Result<Set<Long>> getRelatedIdListById(@RequestParam Long villageId);

    /**
     * 根据公司名称，获取对应的门店信息
     */
    @GetMapping(value = "/api/best/company/store/api/open/getStoreByCompanyName")
    public Result<FindStoreByOrgIdOutDto> getStoreByCompanyName(@RequestParam("companyName") String companyName);

    @GetMapping("/api/pstore/api/open/findByAreaId")
    public Result<List<FindPstoreByAreaIdOutDto>> findByAreaId(@RequestParam("provicenId") Long provicenId,
                                                               @RequestParam("cityId") Long cityId,
                                                               @RequestParam("countyId") Long countyId,
                                                               @RequestParam("type") Integer type);

    @PostMapping("/api/org/api/open/getIdListByCodes")
    public Result<List<Long>> getIdListByCodes(@RequestBody @Valid QueryIdByCodeDto dto);

    /**
     * 根据code获取经销商id
     */
    @PostMapping("/api/distributor/api/open/findDistributorIdListByCodes")
    public Result<List<Long>> findDistributorIdListByCodes(@RequestBody List<String> codes);

    @GetMapping(value = "/api/open/org/mapping/getOrgMapping", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<String>> getOrgMapping(@RequestParam("sourceTable")String sourceTable,
                                              @RequestParam("sourceId") Long sourceId,
                                              @RequestParam("type")Integer type,
                                              @RequestParam(name = "cityName",required = false)String cityName);

    @GetMapping(value = "/api/org/api/open/testOrgTxc")
    public Result testOrgTxc(@RequestParam("exception_flag") Integer exception_flag);

    @RequestMapping(value = "/api/store/getOrgIdListByCondition", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Long>> getOrgIdListByCondition(@RequestBody GetOrgIdListByConditionDto param);

    /**
     * 查询非代销的经销商编码
     */
    @RequestMapping(value = "/api/distributor/api/open/findCodesByCodes", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<String>> findCodesByCodes(@RequestBody Collection<String> codes);

    /**
     * 根据门店和岗位查询业务员
     */
    @RequestMapping(value = "/api/salesman/getSalesmanByStoreIdAndStation", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GetSalesmanByStoreOrgIdsOutDTO>> getSalesmanByStoreIdAndStation(@RequestBody GetSalesmanByStoreOrgIdsInDTO getSalesmanByStoreOrgIdsInDTO);

    /**
     * 根据门店和岗位查询业务员
     */
    @RequestMapping(value = "/api/salesman/api/open/getSalesmanByStoreIdAndStation", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GetSalesmanByStoreOrgIdsOutDTO>> getSalesmanByStoreIdAndStationOpen(@RequestBody GetSalesmanByStoreOrgIdsInDTO getSalesmanByStoreOrgIdsInDTO);

    @GetMapping(value = "api/store/pos/api/open/getStoreIdByPosKey",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Long>> getStoreIdByPosKey(@RequestParam(value = "posKey") String posKey);


/**
* 目前仅查了工号和userEntityId
*/
    @GetMapping(value = "api/company/api/open/getCompanyKaUserList",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<UserEntityExtend>> getCompanyKaUserList(@RequestParam("companyOrgId") Long companyOrgId);
    @ApiOperation("根据公司id批量查询公司信息")
    @RequestMapping(value = "/api/company/findByIds2", method = RequestMethod.POST)
    public Result<List<FindCompanyByIdOutDto>> findCompanyByIds2(@RequestBody List<Long> ids);

    @RequestMapping(value = "/api/company/api/open/findByIds", method = RequestMethod.GET)
    Result<List<FindCompanyByIdOutDto>> openApiFindByIds(@RequestParam(value = "ids") List<Long> ids);

    /**  [open]分页查询公司列表 */
    @RequestMapping(value = "/api/company/api/open/findCompanyAllForPost", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<PageInfo<FindCompanyAllOutDto>> findCompanyAllForPost(@RequestBody @Valid FindCompanyAllInDto findCompanyAllInDto);

    @RequestMapping(value = "/api/designerClub/api/open/getDesignerByMemberId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<DesignerClubDesignerVO>> getDesignerByMemberId(@RequestParam("memberId") Long memberId);

    @GetMapping(value = "/api/salesman/findByIdORCode", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<SalesmanSimpleInfo> findByIdORCode(@RequestParam("id")Long id);

    @ApiOperation("查询支付账号基础配置")
    @RequestMapping(value = "/api/pay/account/m/selectByField", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PayAccountBasicConfigOutDto> selectByField(@RequestBody PayAccountBasicConfigInDto payAccountBasicConfigInDto);

    @RequestMapping(value = "/api/pay/account/selectAllList", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<PayAccountBasicConfigOutDto>> selectAllList();

    /**
     * 根据传入大区查询大区内有权限的分公司数据
     */
    @RequestMapping(value = {"/api/company/findAuthCompany"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindCompanyAllOutDto>> findAuthCompany(@RequestBody FindCompanyByAreaInDto dto);

    @RequestMapping(value = "/api/designer/club/api/open/member/phones", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Kv<Long, String>>> getDesignerClubMemberPhones(@RequestBody @Valid Map query);

    /**
     * 根据门店orgIds查询门店名称列表
     */
    @RequestMapping(value = "/api/store/findStoreNameListByOrgIds", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<FindStoreNameListByOrgIdsOutDto>> findStoreNameListByOrgIds(@RequestBody FindStoreByOrgIdsInDto findStoreByOrgIdInDto);

    /**
     * 根据楼盘id查询订单绑定门店
     */
    @RequestMapping(value = "/api/village/getOrderStoreOrgIdByVillageId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Long>> getOrderStoreOrgIdByVillageId(@RequestParam("villageId") Long villageId);

    @RequestMapping(value = "/api/designer/api/open/getNameInfoByCode", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<DecorateAndDesignerDto> getNameInfoByCode(@RequestBody DecorateAndDesignerDto dto);



}
