package com.fotile.omscenter.util;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.omscenter.picture.dao.PictureDao;
import com.fotile.omscenter.picture.dao.PictureMappingDao;
import com.fotile.omscenter.picture.pojo.entity.PictureEntity;
import com.fotile.omscenter.picture.pojo.entity.PictureMappingEntity;
import com.fotile.omscenter.tServiceOrderInfoComment.ServiceOrderConstant;
import com.fotile.omscenter.util.pojo.dto.SelectAllPictureBysourceIdListOutDTO;
import org.apache.poi.ss.usermodel.Picture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ Author     ：黄学后
 * @ Date       ：Created in 10:23 2020/3/12
 * @ Description：
 * @ Modified By：
 * @Version: $
 */
@Component
@DruidTxcMultiDataSourceAnnontation(value = "fourth")
public class PictureUtils {
    @Autowired
    private PictureMappingDao pictureMappingDao;

    @Autowired
    private PictureDao pictureDao;

    /**
     * 删除附件
     */
    public void deletePicBySourceTableAndSourceId(String tableName , Long sourceId){
        deletePicBySourceTableAndSourceId(tableName,sourceId, null);
        /*PictureMappingEntity picMapping = new PictureMappingEntity(tableName, sourceId ,  null, null,null);
        pictureDao.delPicMapping(picMapping);*/
    }
    /**
     * 删除附件
     */
    public void deletePicBySourceTableAndSourceId(String tableName , Long sourceId,Integer type){
        PictureMappingEntity picMapping = new PictureMappingEntity(tableName, sourceId ,  null, null,type);
        pictureDao.delPicMapping(picMapping);
    }


    /**
     * 新增附件及关系
     */
    public void insertDesingerPic(List<PictureEntity> pictureList, String tableName, Long sourceId) {
    /*    List<PictureEntity> pictureOrgs = new ArrayList<>();
        pictureList.stream().forEach(x -> {
            PictureEntity pictureOrg = new PictureEntity();
            pictureOrg.setCoverUrl(x.getCoverUrl());
            pictureDao.addPic(pictureOrg);
            pictureOrgs.add(pictureOrg);
        });
        pictureOrgs.stream().forEach(x -> {
            PictureMappingEntity pictureOrgMapping = new PictureMappingEntity();
            pictureOrgMapping.setPictureOmsId(x.getId());
            pictureOrgMapping.setSourceTableName(tableName);
            pictureOrgMapping.setSourceId(sourceId);
            pictureOrgMapping.setSort(0);
            pictureMappingDao.insert(pictureOrgMapping);
        });*/

        insertDesingerPic(pictureList,tableName,sourceId,null);
    }

    /**
     * 新增附件及关系
     */
    public void insertDesingerPic(List<PictureEntity> pictureList, String tableName, Long sourceId,Integer type) {
        List<PictureEntity> pictureOrgs = new ArrayList<>();
        pictureList.stream().forEach(x -> {
            PictureEntity pictureOrg = new PictureEntity();
            pictureOrg.setCoverUrl(x.getCoverUrl());
            pictureDao.addPic(pictureOrg);
            pictureOrgs.add(pictureOrg);
        });
        pictureOrgs.stream().forEach(x -> {
            PictureMappingEntity pictureOrgMapping = new PictureMappingEntity();
            pictureOrgMapping.setPictureOmsId(x.getId());
            pictureOrgMapping.setSourceTableName(tableName);
            pictureOrgMapping.setSourceId(sourceId);
            pictureOrgMapping.setSort(0);
            pictureOrgMapping.setType(type);
            pictureMappingDao.insert(pictureOrgMapping);
        });
    }



    /**
     * 查询附件
     */
    public List<PictureEntity> getPictureList(String sourceTable, Long sourceId) {
        return pictureDao.queryPicBySourceIdAndSourceTable(sourceId, sourceTable);
    }

    /**
     * 查询附件
     */
    public List<PictureEntity> getPictureList(String sourceTable, Long sourceId,Integer type) {
       return pictureDao.queryPicBySourceIdAndSourceTableAndType(sourceId,sourceTable,type);
    }

    /**
     * 批量查询图片
     *
     * @param sourceTable
     * @param sourceIds
     * @return
     */
    public List<SelectAllPictureBysourceIdListOutDTO> getPictureListBysourceIds(String sourceTable, List<Long> sourceIds) {
        return pictureDao.selectAllPictureBysourceIdList(sourceIds, sourceTable);
    }

    /**
     * 批量查询图片
     *
     * @param sourceTable
     * @param sourceIds
     * @return
     */
    public List<SelectAllPictureBysourceIdListOutDTO> getPictureListBysourceIds(String sourceTable, List<Long> sourceIds,Integer type) {
        return pictureDao.selectAllPictureBysourceIdList2(sourceIds, sourceTable,type);
    }


}
