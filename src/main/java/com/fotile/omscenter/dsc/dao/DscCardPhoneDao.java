package com.fotile.omscenter.dsc.dao;

import com.fotile.omscenter.config.mybatisplus.SpiceBaseMapper;
import com.fotile.omscenter.dsc.pojo.dto.DscCardPhoneSimpleDTO;
import com.fotile.omscenter.dsc.pojo.entity.DscCardPhoneEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成交服务卡主表(omscenter.dsc_card_phone)Dao类
 *
 * <AUTHOR>
 * @since 2025-03-03 15:21:18
 */
@Mapper
public interface DscCardPhoneDao {

  /**
   * 批量插入成交服务卡手机号
   */
  void insertBatch(@Param("list") List<DscCardPhoneEntity> phones);

  Integer delCardPhoneListByCardId(@Param("cardId") Long cardId,
                                   @Param("currentUserId") String currentUserId);

  List<DscCardPhoneEntity> getByCardId(@Param("cardId") Long cardId);

  List<DscCardPhoneSimpleDTO> getSimpleByCardIdList(@Param("cardIdList") List<Long> cardIdList);

  void delCardPhoneListByCardIdAndPhone(@Param("cardId") Long cardId, @Param("list") List<String> historyPhones, @Param("currentUserId") String currentUserId);
}
