package com.fotile.omscenter.decOrder.mapper;


import com.fotile.omscenter.decOrder.pojo.entity.DeclarationInfoShellStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeclarationInfoShellStatisticsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeclarationInfoShellStatistics record);

    int insertSelective(DeclarationInfoShellStatistics record);

    DeclarationInfoShellStatistics selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeclarationInfoShellStatistics record);

    int updateByPrimaryKey(DeclarationInfoShellStatistics record);

    DeclarationInfoShellStatistics selectByDate(@Param("date") String date);

    Long getStatisticsListCount(DeclarationInfoShellStatistics param);

    List<DeclarationInfoShellStatistics> getStatisticsList(DeclarationInfoShellStatistics param);
}