package com.fotile.omscenter.decOrder.pojo.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 脱敏导出dto
 */
@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class DecOrderPayInfoExportOutWithStarDto extends DecOrderDetailExportOutWithStarDto {

    @ColumnWidth(15)
    @ExcelProperty(value = {"支付机构"}, index = 109)
    private String payOrgName;


    @ColumnWidth(15)
    @ExcelProperty(value = {"收款信息订单号"}, index = 110)
    private Long txnOrderId;

    @ColumnWidth(15)
    @ExcelProperty(value = {"收款信息订单类型"}, index = 111)
    private String txnOrderType;

    /**
     * 支付类型
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易方式"}, index = 112)
    private String payTypeName;
    /**
     * 交易动作，1-支付 2-退款
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易动作"}, index = 113)
    private String txnActionName;
    /**
     * 支付金额
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易金额"}, index = 114)
    private BigDecimal txnPaid;
    /**
     * 交易请求号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易请求号"}, index = 115)
    private String txnRequestNo;
    /**
     * 支付流水号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易流水号"}, index = 116)
    private String paymentSerialNumber;
    /**
     * 支付时间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易时间"}, index = 117)
    private Date payTime;
    /**
     * 付款账号ID
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"付款账号ID"}, index = 118)
    private String paymentAccountId;
    /**
     * 交易状态，1-支付中请稍后查询，2-支付成功，3-支付失败，4-退款中请稍后查询，5-退款成功，6-退款失败
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"交易状态"}, index = 119)
    private String txnStatusName;

}
