package com.fotile.omscenter.decOrder.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author：tianzg
 * @className: QueryOrderByDrpIdOrIdOutDto
 * @date: 2020/11/11
 * @version: v1.0
 **/
@Data
public class QueryOrderByDrpIdOrIdOutDto {
    /**
     * 订单id
     */
    private Long id;
    /**
     * 线索id
     */
    private Integer cluesId;
    /**
     * 买家id
     */
    private String userId;
    /**
     * 买家姓名
     */
    @FieldEncrypt
    private String userName;
    /**
     * 分公司id
     */
    private Long companyId;
    /**
     * 分公司名称
     */
    private String companyName;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 门店名称
     */
    private String stroeName;
    /**
     * 订单审批状态 01:未审批 02：审批通过  03：审批不通过
     */
    private String approveStatus;
    /**
     * 订单生成时间
     */
    private String orderCreateTime;
    /**
     * 收货人姓名
     */
    @FieldEncrypt
    private String contactName;
    /**
     * 收货人手机号
     */
    @FieldEncrypt
    private String contactMobile;
    /**
     * 地址-省ID
     */
    private String proviceId;
    /**
     * 地址-市ID
     */
    private String cityId;
    /**
     * 地址-区ID
     */
    private String countyId;
    /**
     * 地址-省名称
     */
    private String proviceName;
    /**
     * 地址-市名称
     */
    private String cityName;
    /**
     * 地址-区名称
     */
    private String countyName;
    /**
     * 收货人地址
     */
    @FieldEncrypt
    private String deliveryAddress;
    /**
     * 送货时间
     */
    private String deliveryDate;
    /**
     * 买家留言
     */
    private String userNote;
    /**
     * 其他备注
     */
    private String orderNote;
    /**
     * 商品总数量
     */
    private Integer goodsNum;
    /**
     * 商品总价格
     */
    private BigDecimal goodsAmount;
    /**
     * 购买原因 01：毛坯新装 02：厨电更新 03：全屋重装
     */
    private String buyReason;
    /**
     * 业务类型 默认值：BBC
     */
    private String businessType;
    /**
     * 厨电顾问ID
     */
    private Integer adviserId;
    /**
     * 厨电顾问姓名
     */
    @FieldEncrypt
    private String adviserName;
    /**
     * 厨电顾问(录入人)id
     */
    private Integer inputId;
    /**
     * 厨电顾问(录入人)名称
     */
    @FieldEncrypt
    private String inputName;
    /**
     * 客户经理ID
     */
    private Integer managerId;
    /**
     * 客户经理名称
     */
    @FieldEncrypt
    private String managerName;
    /**
     * 结算小票号
     */
    private String settlementTicket;
    /**
     * 线索负责人关联的业务员id
     */
    private Integer cluesSalesmanId;
    /**
     * 安装单号
     */
    private String installId;
    /**
     * 联系方式
     */
    @FieldEncrypt
    private String contactMobileBak;
    /**
     * 审核状态 1：待审核；2：审核通过；3：审核拒绝
     */
    private Integer stage;
    /**
     * 同步drp状态 1：未处理；2：同步成功；3：同步失败
     */
    private Integer drpStage;
    /**
     * 同步drp错误次数
     */
    private Integer retryDrpNum;
    /**
     * drp订单号
     */
    private String drpOrderId;
    /**
     * 是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP,4：同步至事业上DRP
     */
    private String isToDrp;
    /**
     * 订单传送至DRP状态：1：不需要预审核；2：需要审核 默认值1
     */
    private String toDrpStage;
    /**
     * 家装公司编码
     */
    private String decorateCompanyCode;
    /**
     * 家装公司名称
     */
    @FieldEncrypt
    private String decorateCompanyName;
    /**
     * 家装公司手机号
     */
    @FieldEncrypt
    private String decorateCompanyPhone;

    /**
     * 0：禁用；1：启用
     */
    private String decorateCompanyStatus;
    /**
     * 设计师编码
     */
    private String designerCode;
    /**
     * 设计师名称
     */
    @FieldEncrypt
    private String designerName;
    /**
     * 设计师手机号
     */
    @FieldEncrypt
    private String designerPhone;
    /**
     * 现金金额
     */
    private BigDecimal cashAmount;
    /**
     * 转账金额
     */
    private BigDecimal transferAmount;
    /**
     * 刷卡金额
     */
    private BigDecimal cardAmount;
    /**
     * 订单类型 1：经销/零售；2：定金
     */
    private Integer orderType;
    /**
     * DRP订单状态 0:无  1:打开 2:审核 3:关闭 4:作废 5:中止
     */
    private Integer orderStage;
    /**
     * DRP订单状态 0:无  1:打开 2:审核 3:关闭 4:作废 5:中止
     */
    private String orderStageName;
    /**
     * 定金转单状态 1：未转单；2：已转单' 3：已作废'
     */
    private Integer stransferStage;
    /**
     * 关联定金订单号
     */
    private Integer parentId;
    /**
     * 定金金额
     */
    private BigDecimal earnestAmount;
    /**
     * 小区名称
     */
    private Long villageId;
    private String villageName;
    /**
     * 同步至drp返回信息
     */
    private String toDrpMsg;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 大区编码
     */
    private String areaCode;
    /**
     * 大区名称
     */
    private String areaName;
    /**
     * 经销商id
     */
    private Long distributorId;
    /**
     * 经销商名称
     */
    private String distributorName;
    /**
     * 经销商编码
     */
    private String distributorCode;
    /**
     * 渠道大类编码
     */
    private String channelCategoryCode;
    /**
     * 渠道大类名称
     */
    private String channelCategoryName;
    /**
     * 渠道细分编码
     */
    private String channelSubdivideCode;
    /**
     * 渠道细分名称
     */
    private String channelSubdivideName;
    /**
     * 线索来源编码
     */
    private String cluesSourceCode;
    /**
     * 线索来源名称
     */
    private String cluesSourceName;
    /**
     * 门店类型
     */
    private String storeType;
    /**
     * 物流状态:打开/计划/关闭/出库
     */
    private String logisticsStage;
    /**
     * 物流单号
     */
    private String logisticsNo;
    /**
     * 审核时间
     */
    private String examineDate;
    /**
     * 预审核时间
     */
    private String preExamineDate;
    /**
     * 预审核
     */
    private String preExamine;
    /**
     * 结算状态
     */
    private String settlementStage;
    /**
     * 默认仓库
     */
    private String wareHouse;
    /**
     * 审核时间
     */
    private String auditDate;
    /**
     * 优惠券
     */
    private String cardNo;
    /**
     * 优惠券id
     */
    private Integer cardId;
    /**
     * 订购时间
     */
    private String orderDate;
    /**
     * 储位
     */
    private String storage;
    /**
     * 类型 1：家装；2：异业三工,3 设计师
     */
    private Integer decorateCompanyType;
    /**
     * 活动说明
     */
    private String activityRemark;
    /**
     * 是否在云管理预审核 0：否；1：是2:预审通过
     */
    private Integer appPreAudit;
    /**
     * 云管理预审核时间
     */
    private String appPreExamineDate;
    /**
     * 云管理预审核 0:是  1:否  订单为单位 0是已经预审过了， 1是还未预审 订单单位
     */
    private String appPreExamine;
    /**
     * 订单渠道
     */
    private String orderChannel;
    /**
     * 是否店内成交 0：否；1：是
     */
    private Integer isInStore;
    /**
     * 原销售订单
     */
    private String originalOrderId;
    /**
     * 外部订单号
     */
    private String outOrderId;
    /**
     * 一级退货原因id
     */
    private Integer firstReturnReason;
    /**
     * 一级退货原因名称
     */
    private String firstReturnReasonName;
    /**
     * 二级退货原因id
     */
    private Integer secReturnReason;
    /**
     * 二级退货原因名称
     */
    private String secReturnReasonName;
    /**
     * OA编号
     */
    private String oaNo;
    /**
     * 原因描述
     */
    private String reason;
    /**
     * 引流渠道类型
     */
    private Integer decorateCategory;
    /**
     * 引流渠道类型名称
     */
    private String decorateCategoryName;

    /**
     * 商品列表
     */
    private List<FindGoodsByOrderIdOutDto> goodsList;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型名称
     */
    private String storeTypeName;

    /**
     * 门店简称
     */
    private String abbreviation;
    /**
     * 配图 多张配图以分号分隔
     */
    private String decOrderPics;

    /**
     * 所属部门
     */
    private Long depId;

    /**
     * 线索创建时间
     */
    private String cluesCreatedDate;

    /**
     * 总数量
     */
    private BigDecimal totalNum;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    //成交接待人id
    private Long dealReceiverId;

    /**
     * 开票信息
     */
    private DecOrderInvoiceDto decOrderInvoiceDto;

    /**
     * 是否是场景化讲解成交，1是，0否
     */
    private Integer isSceneDeal;

    /**
     * 场景化成交附件
     */
    private String sceneDealAttachmentList;

    /**
     * 楼栋
     */
    private String building;
    /**
     * 单元
     */
    private String unit;
    /**
     * 门牌
     */
    private String houseNumber;

    private Integer engineeringFlag;

    private String processNo;

    private String engineeringCityName;

    private String engineeringCityCode;

    /**
     * 是否上门设计1是0否
     */
    private Integer isComeDevise;

    /**
     * 以旧换新标记，0否，1是
     */
    private Integer tradeInFlag;
    /**
     * 真实门店orgId
     */
    private Long realStoreId;
    private String realStoreCode;
    private String realStoreName;
    private String realStoreAbbreviation;
    private String realStoreStatusName;
    /**
     * 真实经销商id
     */
    private Long realDistributorId;
    private String realDistributorCode;
    private String realDistributorName;
    /**
     * 身份证附件信息
     */
    private List<String> idCardPicList;
    /**
     * 是否开启云管理收款-旧补，默认0，1表示开启
     */
    private Integer oldCompensateFlag;
    /**
     * 订单收款状态，0未收款，1部分收款，2全部收款
     */
    private Integer payStatus;

    /**
     * 房屋特征，10新精装  20毛坯房  30已使用的老房（含老精装） /marketing-center/api/baseAttribute/getAttributeByFieldId?fieldId=decorate_type
     */
    private Integer houseFeature;
    /**
     * 是否开启收银管理，默认0，1表示开启
     */
    private Integer isCashierEnabled;
}