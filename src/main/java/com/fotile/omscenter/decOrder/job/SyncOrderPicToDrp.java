package com.fotile.omscenter.decOrder.job;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.omscenter.decOrder.service.DecOrderService;
import com.fotile.omscenter.decOrder.service.DrpOrderInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "fourth")
public class SyncOrderPicToDrp extends IJobHandler {
    @Autowired
    private DrpOrderInfoService drpOrderInfoService;

    /**
     * 暂时先以sql方式修复，如果以后还有分公司有大区，订单表中依然没保存的情况，应找到这个bug修复
     * @description 处理订单大区字段为空的数据
     */
    @Override
    @XxlJob(value = "syncOrderPicToDrp")
    public ReturnT<String> execute(String s) throws InterruptedException {
        XxlJobLogger.log("======SyncOrderPicToDrp.execute");
        if(StringUtils.isBlank(s)){
            return new ReturnT<String>("请传入订单号!");
        }
        for (String orderId : s.split(",")) {
            try {
                drpOrderInfoService.syncOrderPicToDrp(Long.parseLong(orderId));
            }catch (Exception e){
                log.error("同步附件到drp出现错误，错误单号：orderId");
            }
        }
        return new ReturnT<String>("成功!");
    }
}
