package com.fotile.dep.partner.fr.send;

import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessSendService;
import com.fotile.dep.partner.fr.pojo.PartnerFrMapper;
import com.fotile.dep.provider.cem.dao.CemInReturnGoodsAppealDao;
import com.fotile.dep.provider.cem.dao.CemInReturnGoodsUserDao;
import com.fotile.dep.provider.cem.pojo.dto.CemInReturnGoodsAppealDTO;
import com.fotile.dep.provider.cem.pojo.dto.CemInReturnGoodsUserDTO;
import com.fotile.dep.provider.cem.pojo.dto.request.CemInReturnGoodsAppealRequest;
import com.fotile.dep.provider.cem.pojo.entity.CemInReturnGoodsAppeal;
import com.fotile.dep.provider.cem.pojo.entity.CemInReturnGoodsUser;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.dep.partner.fr.send
 * @date 2021/8/30 13:51
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class CemInReturnGoodsSendFrService extends AbstractProcessSendService<CemInReturnGoodsAppealRequest> {
    @Resource
    private CemInReturnGoodsAppealDao appealDao;

    @Resource
    private CemInReturnGoodsUserDao userDao;

    /**
     * 获取需要出站的转换数据
     *
     * @param info 运行过程信息类
     * @return 出站数据
     */
    @Override
    public CemInReturnGoodsAppealRequest getExchangeData(ProcessInfo info) {
        CemInReturnGoodsAppealRequest appealRequest = new CemInReturnGoodsAppealRequest();

        List<CemInReturnGoodsAppeal> appeal = appealDao.findByBatchNoAndIsDeleted(info.getBatchNo(), 0L);
        if (CollectionUtils.isNotEmpty(appeal)) {
            List<CemInReturnGoodsAppealDTO> appealDTOs = appeal.stream()
                    .map(PartnerFrMapper.INSTANCE::cemInReturnGoodsAppealToDTO)
                    .collect(Collectors.toList());
            appealRequest.setAppeal(appealDTOs);
        }

        List<CemInReturnGoodsUser> user = userDao.findByBatchNoAndIsDeleted(info.getBatchNo(), 0L);
        if (CollectionUtils.isNotEmpty(user)) {
            List<CemInReturnGoodsUserDTO> userDTOs = user.stream()
                    .map(PartnerFrMapper.INSTANCE::cemInReturnGoodsUserToDTO)
                    .collect(Collectors.toList());
            appealRequest.setUser(userDTOs);
        }

        return appealRequest;
    }
}
