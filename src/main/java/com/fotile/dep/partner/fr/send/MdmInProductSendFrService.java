package com.fotile.dep.partner.fr.send;

import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessSendService;
import com.fotile.dep.partner.fr.pojo.PartnerFrMapper;
import com.fotile.dep.provider.mdm.dao.MdmInProductDao;
import com.fotile.dep.provider.mdm.pojo.dto.MdmInProductDTO;
import com.fotile.dep.provider.mdm.pojo.entity.MdmInProduct;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出站物料数据给方瑞service
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class MdmInProductSendFrService extends AbstractProcessSendService<List<MdmInProductDTO>> {

    @Resource
    MdmInProductDao mdmInProductDao;

    @Override
    public List<MdmInProductDTO> getExchangeData(ProcessInfo info) {
        List<MdmInProduct> l = mdmInProductDao.findByBatchNoAndIsDeleted(info.getBatchNo(), 0L);
        List<MdmInProductDTO> vos = PartnerFrMapper.INSTANCE.mdmInProductListToDTOList(l);
        return vos;
    }

}
