package com.fotile.dep.provider.mdm.receive;

import com.alibaba.fastjson.JSONObject;
import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessReceiveService;
import com.fotile.dep.provider.mdm.dao.MdmInProductDao;
import com.fotile.dep.provider.mdm.pojo.MdmMapper;
import com.fotile.dep.provider.mdm.pojo.dto.MdmInProductDTO;
import com.fotile.dep.provider.mdm.pojo.entity.MdmInProduct;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * MDM物料数据入站service
 */
@Slf4j
@Service("mdmProductReceiveService")
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class MdmProductReceiveService extends AbstractProcessReceiveService {

    @Resource
    MdmInProductDao mdmInProductDao;

    /**
     * 保存入站数据
     *
     * @param info
     * @return
     */
    @Override
    public void saveData(ProcessInfo info) {
        List<MdmInProductDTO> dtos = JSONObject.parseArray(info.getInInterfaceBody(), MdmInProductDTO.class);
        List<MdmInProduct> list = new ArrayList<>();
        for (MdmInProductDTO dto : dtos) {
            MdmInProduct po = MdmMapper.INSTANCE.mdmProductDTOToProductEntity(dto, info.getBatchNo(), info.getDistributeNo());
            po.setIsDeleted(0L);
            list.add(po);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            mdmInProductDao.insertBatchSomeColumn(list);
        }
    }

}
