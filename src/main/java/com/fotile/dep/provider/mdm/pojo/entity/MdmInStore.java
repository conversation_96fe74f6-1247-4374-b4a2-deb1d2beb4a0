package com.fotile.dep.provider.mdm.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * MDM门店入表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mdm_in_store", schema = "depcenter")
public class MdmInStore extends AuditingEntity {

    /**
     * 分发ID,MDM每次分发主数据产生新的分发ID
     **/
    @TableField(value = "distribute_no")
    private String distributeNo;

    /**
     * 交互平台生成的批次号
     **/
    @TableField(value = "batch_no")
    private String batchNo;

    /**
     * 门店编码
     **/
    @TableField(value = "ZDMDBM")
    private String ZDMDBM;

    /**
     * 城市
     **/
    @TableField(value = "ZDCS")
    private String ZDCS;

    /**
     * 城市 文本
     **/
    @TableField(value = "ZECS_TXT")
    private String ZECS_TXT;

    /**
     * 负责人
     **/
    @FieldEncrypt
    @TableField(value = "ZDFZR")
    private String ZDFZR;

    /**
     * 经度
     **/
    @TableField(value = "ZEJD")
    private String ZEJD;

    /**
     * 联系方式
     **/
    @FieldEncrypt
    @TableField(value = "ZELXFS")
    private String ZELXFS;

    /**
     * 门店产权
     **/
    @TableField(value = "ZDMDCQ")
    private String ZDMDCQ;

    /**
     * 门店名称
     **/
    @TableField(value = "ZDMDMC")
    private String ZDMDMC;

    /**
     * 门店渠道
     **/
    @TableField(value = "ZDMDQD")
    private String ZDMDQD;

    /**
     * 门店渠道描述，V1.0(7)
     **/
    @TableField(value = "MTXT")
    private String MTXT;

    /**
     * 门店渠道细分
     **/
    @TableField(value = "ZDMDQDXF")
    private String ZDMDQDXF;

    /**
     * 门店渠道细分描述，V1.0(7)
     **/
    @TableField(value = "XTXT")
    private String XTXT;

    /**
     * 门店性质
     **/
    @TableField(value = "ZDMDXZ")
    private String ZDMDXZ;

    /**
     * 门店业务类型
     **/
    @TableField(value = "ZDMDYWLX")
    private String ZDMDYWLX;

    /**
     * 门店座机电话
     **/
    @FieldEncrypt
    @TableField(value = "ZDMDZJDH")
    private String ZDMDZJDH;

    /**
     * 门店状态
     **/
    @TableField(value = "ZDMDZT")
    private String ZDMDZT;

    /**
     * 门店装修进度
     **/
    @TableField(value = "ZDMDZXJD")
    private String ZDMDZXJD;

    /**
     * 区县
     **/
    @TableField(value = "ZDQX")
    private String ZDQX;

    /**
     * 区县 文本
     **/
    @TableField(value = "ZEQX_TXT")
    private String ZEQX_TXT;

    /**
     * 省份
     **/
    @TableField(value = "ZDSF")
    private String ZDSF;

    /**
     * 是否虚拟门店
     **/
    @TableField(value = "ZDSFXNMD")
    private String ZDSFXNMD;

    /**
     * 省份 文本
     **/
    @TableField(value = "ZESF_TXT")
    private String ZESF_TXT;

    /**
     * 所属客户
     **/
    @TableField(value = "ZDSSKH")
    private String ZDSSKH;

    /**
     * 纬度
     **/
    @TableField(value = "ZEWD")
    private String ZEWD;

    /**
     * 详细地址
     **/
    @TableField(value = "ZEXXDZ")
    private String ZEXXDZ;

    /**
     * 邮箱
     **/
    @TableField(value = "ZEYX")
    private String ZEYX;

    /**
     * 租用年限
     **/
    @TableField(value = "ZEZYNX")
    private String ZEZYNX;
}
