package com.fotile.dep.provider.fotiledrp.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * drp-发票头
 */
@Data
public class DrpInvoiceDto implements Serializable {

    /**
     * 发票GUID
     */
    private String invoiceguid;

    /**
     * 发票NO
     */
    private String invoiceno;

    /**
     * 虚拟发票NO
     */
    private String virtualinvoiceno;

    /**
     * 税额
     */
    private BigDecimal amounttax;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 折扣
     */
    private BigDecimal amountdiscount;

    /**
     * 备注
     */
    private String demo;

    /**
     * 类别
     */
    private Integer invoicetype;

    /**
     * 归属
     */
    private Integer attribution;

    /**
     * 税率
     */
    private BigDecimal ratetax;

    /**
     * 开票日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date approvedate;

    /**
     * 客户ID
     */
    private String mdmid;

    /**
     * 发票状态发票状态
     */
    private Integer status;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifiedon;

    /**
     * 状态(逻辑删除)
     */
    private Integer state;

    /**
     * 发票明细
     */
    @JSONField(name = "Invoiceline")
    private List<DrpInvoiceLineDto> Invoiceline;

    /**
     * 折扣明细
     */
    private List<DrpInvoiceDiscountDto> discountlist;

}
