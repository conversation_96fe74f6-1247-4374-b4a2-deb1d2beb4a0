package com.fotile.dep.provider.cem.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * CEM报装信息-跟进信息入表
 *
 * <AUTHOR>
 */
@Data
public class CemInDecorateFollowDTO implements Serializable {

    /**
     * 跟进记录Id
     **/
    @JSONField(name = "Id")
    private String Id;

    /**
     * 诉求Id
     **/
    @JSONField(name = "SRId")
    private String SRId;

    /**
     * 跟进内容
     **/
    @JSONField(name = "FollowContent")
    private String FollowContent;

    /**
     * 跟进类型
     **/
    @JSONField(name = "Type")
    private String Type;

    /**
     * 更新时间
     **/
    @JSONField(name = "Updated", format = "yyyy-MM-dd HH:mm:ss")
    private Date Updated;
}
