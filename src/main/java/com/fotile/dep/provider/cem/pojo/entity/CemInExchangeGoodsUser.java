package com.fotile.dep.provider.cem.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Entity;

/**
 * CEM换货信息-用户信息入表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Entity
@Data
@TableName(value = "cem_in_exchange_goods_user", schema = "depcenter")
public class CemInExchangeGoodsUser extends AuditingEntity {

    /**
     * 交互平台生成的批次号
     **/
    @TableField(value = "batch_no")
    private String batchNo;

    /**
     * 分发ID,MDM每次分发主数据产生新的分发ID
     **/
    @TableField(value = "distribute_no")
    private String distributeNo;

    /**
     * 联系人Id
     **/
    @TableField(value = "user_id")
    private String user_id;

    /**
     * 诉求Id
     **/
    @TableField(value = "SRId")
    private String SRId;

    /**
     * 姓名
     **/
    @FieldEncrypt
    @TableField(value = "LastName")
    private String LastName;

    /**
     * 手机号码
     **/
    @FieldEncrypt
    @TableField(value = "CellularPhone")
    private String CellularPhone;
}
