package com.fotile.dep.provider.cem.pojo.dto.request;

import com.fotile.dep.provider.cem.pojo.dto.CemInReturnGoodsAppealDTO;
import com.fotile.dep.provider.cem.pojo.dto.CemInReturnGoodsUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 退货诉求-请求参数
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.dep.provider.cem.pojo.dto.request
 * @date 2021/8/27 14:37
 */
@Data
public class CemInReturnGoodsAppealRequest implements Serializable {
    /**
     * 退货诉求-诉求信息
     */
    private List<CemInReturnGoodsAppealDTO> appeal;

    /**
     * 退货诉求-用户信息
     */
    private List<CemInReturnGoodsUserDTO> user;
}
