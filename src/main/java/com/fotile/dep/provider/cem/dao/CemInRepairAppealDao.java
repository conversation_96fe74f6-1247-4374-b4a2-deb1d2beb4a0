package com.fotile.dep.provider.cem.dao;

import com.fotile.dep.config.mybatisplus.SpiceBaseMapper;
import com.fotile.dep.provider.cem.pojo.entity.CemInRepairAppeal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * CEM报修信息-诉求信息入表数据访问层
 *
 * <AUTHOR>
 */
@Mapper
@Validated
public interface CemInRepairAppealDao extends SpiceBaseMapper<CemInRepairAppeal> {
    List<CemInRepairAppeal> findByBatchNoAndIsDeleted(@Param("batchNo") String batchNo,
                                                      @Param("isDeleted") Long isDeleted);
}
