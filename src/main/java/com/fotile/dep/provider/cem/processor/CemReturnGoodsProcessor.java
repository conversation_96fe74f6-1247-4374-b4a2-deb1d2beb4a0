package com.fotile.dep.provider.cem.processor;

import com.fotile.dep.business.processinfo.ProcessInService;
import com.fotile.dep.business.processinfo.ProcessInfoService;
import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessReceiveService;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.dep.provider.cem.processor
 * @date 2021/8/26 15:37
 */
@Component
@Slf4j
public class CemReturnGoodsProcessor implements Processor {
    @Resource
    ProcessInfoService inProcessInfoService;

    @Resource(name = "cemReturnGoodsReceiveService")
    AbstractProcessReceiveService cemReturnGoodsReceiveService;

    //这里需要多例，名字需要不一样，单例在并发下有问题
    @Autowired
    ProcessInService cemReturnGoodsProcessInService;

    /**
     * Processes the message exchange
     *
     * @param exchange the message exchange
     * @throws Exception if an internal processing error has occurred.
     */
    @Override
    public void process(Exchange exchange) throws Exception {
        //校验body是否为空，及processId是否存在，并记录运行日志
        Result<ProcessInfo> r = inProcessInfoService.check(exchange);

        //封装入站全量日志，并发送kafka进行保存
        inProcessInfoService.saveInlog(r, exchange);

        if (!r.getSuccess()) {
            exchange.getOut().setBody(Result.buildFailure(r.getMsg(), r.getData().getDistributeNo()));
            return;
        }

        //保存数据且发mq
        cemReturnGoodsProcessInService.abstractProcessReceiveService = cemReturnGoodsReceiveService;
        Result rs = cemReturnGoodsProcessInService.saveAndSendMq(r.getData());
        rs.setData(r.getData().getDistributeNo());
        exchange.getOut().setBody(rs);
    }
}
