package com.fotile.dep.provider.cem.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * CEM报装信息-资产信息入表
 *
 * <AUTHOR>
 */
@Data
public class CemInDecorateAssetDTO implements Serializable {
    /**
     * 资产Id
     **/
    @JSONField(name = "Id")
    private String Id;

    /**
     * 诉求Id
     **/
    @JSONField(name = "SRId")
    private String SRId;

    /**
     * 品牌
     **/
    @JSONField(name = "FTXZpp")
    private String FTXZpp;

    /**
     * 产品组
     **/
    @JSONField(name = "FTAssetType")
    private String FTAssetType;

    /**
     * 资产型号
     **/
    @JSONField(name = "FTAssetModel")
    private String FTAssetModel;

    /**
     * 购买渠道
     **/
    @JSONField(name = "FTPurchaseChannel")
    private String FTPurchaseChannel;

    /**
     * 产品编号
     **/
    @JSONField(name = "AssetNumber")
    private String AssetNumber;

    /**
     * 地址ID
     **/
    @JSONField(name = "PersonalAddressId")
    private String PersonalAddressId;
}
