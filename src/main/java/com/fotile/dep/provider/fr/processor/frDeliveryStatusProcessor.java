package com.fotile.dep.provider.fr.processor;

import com.fotile.dep.business.processinfo.ProcessInfoService;
import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.processinfo.ProcessInService;
import com.fotile.dep.business.service.abstracts.AbstractProcessReceiveService;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 方瑞推送送货诉求状态信息至CEM
 */
@Component
@Slf4j
public class frDeliveryStatusProcessor implements Processor {

    @Resource
    ProcessInfoService inProcessInfoService;

    @Resource(name = "frDeliveryStatusReceiveService")
    AbstractProcessReceiveService frDeliveryStatusReceiveService;

    //这里需要多例，名字需要不一样，单例在并发下有问题
    @Autowired
    ProcessInService frDeliveryStatusProcessInService;

    @Override
    public void process(Exchange exchange) throws Exception {

        //校验body是否为空，及processId是否存在，并记录运行日志
        Result<ProcessInfo> r = inProcessInfoService.check(exchange);

        //封装入站全量日志，并发送kafka进行保存
        inProcessInfoService.saveInlog(r, exchange);

        //校验不成功
        if (!r.getSuccess()) {
            exchange.getOut().setBody(r);
            return;
        }

        //保存数据且发mq
        frDeliveryStatusProcessInService.abstractProcessReceiveService = frDeliveryStatusReceiveService;

        Result rs = frDeliveryStatusProcessInService.saveAndSendMq(r.getData());

        rs.setData(r.getData().getDistributeNo());

        exchange.getOut().setBody(rs);

    }
}
