package com.fotile.dep.provider.fr.dao;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.fotile.dep.config.mybatisplus.SpiceBaseMapper;
import com.fotile.dep.provider.fr.pojo.entity.FrInFeeApplyLine;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.validation.annotation.Validated;

/**
 * 方瑞推送费用申请至ECS
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.dep.provider.msp.dao
 * @date 2021/8/5 23:22
 */
@Mapper
@Validated
public interface FrFeeApplyLineDao extends SpiceBaseMapper<FrInFeeApplyLine> {
    List<FrInFeeApplyLine> findByBatchNoAndIsDeleted(@Param("batchNo")String batchNo,@Param("isDeleted")Long isDeleted);


}
