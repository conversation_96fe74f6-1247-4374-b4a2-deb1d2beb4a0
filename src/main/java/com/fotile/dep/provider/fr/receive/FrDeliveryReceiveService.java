package com.fotile.dep.provider.fr.receive;

import com.alibaba.fastjson.JSONObject;
import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessReceiveService;
import com.fotile.dep.provider.fr.dao.FrDeliveryDao;
import com.fotile.dep.provider.fr.dao.FrDeliveryGoodsDao;
import com.fotile.dep.provider.fr.pojo.FrMapper;
import com.fotile.dep.provider.fr.pojo.dto.FrInDeliveryDto;
import com.fotile.dep.provider.fr.pojo.dto.FrInDeliveryGoodsDto;
import com.fotile.dep.provider.fr.pojo.dto.FrInOrderTransGoodsDto;
import com.fotile.dep.provider.fr.pojo.entity.FrInDelivery;
import com.fotile.dep.provider.fr.pojo.entity.FrInDeliveryGoods;
import com.fotile.dep.provider.fr.pojo.entity.FrInOrderTransGoods;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 方瑞物流信息回传(送货诉求)信息至CEM 数据入站service
 */
@Slf4j
@Service("frDeliveryReceiveService")
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class FrDeliveryReceiveService extends AbstractProcessReceiveService {

    @Autowired
    FrDeliveryDao frDeliveryDao;

    @Autowired
    FrDeliveryGoodsDao frDeliveryGoodsDao;

    /**
     * 保存入站数据
     *
     * @param info
     * @return
     */
    @Override
    public void saveData(ProcessInfo info) {

        List<FrInDeliveryDto> deliveryInDtoList = JSONObject.parseArray(info.getInInterfaceBody(), FrInDeliveryDto.class);

        String batchNo = info.getBatchNo();

        String distributorNo = info.getDistributeNo();

        //entity集合
        List<FrInDelivery> frDeliveryEntityList = FrMapper.INSTANCE.frInDeliveryDtoListToFrInDeliveryList(deliveryInDtoList);

        if (CollectionUtils.isNotEmpty(frDeliveryEntityList)) {

            frDeliveryEntityList.stream().forEach(x -> {

                x.setBatchNo(batchNo);

                x.setDistributeNo(distributorNo);

                x.setIsDeleted(0L);

            });

            frDeliveryDao.insertBatchSomeColumn(frDeliveryEntityList);

        }

        //商品行
        deliveryInDtoList.forEach(x -> {

            if (CollectionUtils.isNotEmpty(x.getGoodsDtoList())) {

                //商品行
                List<FrInDeliveryGoodsDto> goodsDtoList =x.getGoodsDtoList();

                List<FrInDeliveryGoods> goodsList =FrMapper.INSTANCE.frInDeliveryGoodsDtoListToFrInDeliveryGoodsList(goodsDtoList);

                if (CollectionUtils.isNotEmpty(goodsList)) {

                    goodsList.stream().forEach(g -> {

                        g.setBatchNo(batchNo);

                        g.setDistributeNo(distributorNo);

                        g.setIsDeleted(0L);

                        g.setGuid(x.getGuid());

                    });

                    frDeliveryGoodsDao.insertBatchSomeColumn(goodsList);

                }

            }

        });

    }

}
