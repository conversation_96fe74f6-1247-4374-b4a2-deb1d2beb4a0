package com.fotile.dep.provider.msp.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * MSP终端建设申请-维修门店entity
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "msp_in_tapply_repair", schema = "depcenter")
public class MspInTApplyRepair extends AuditingEntity {

    /**
     * 批次号，使用uuid
     */
    @TableField(value = "batch_no")
    private String batchNo;

    /**
     * 分发ID（MDM每次分发主数据产生新的分发ID）
     */
    @TableField(value = "distribute_no")
    private String distributeNo;

    /**
     * 终端建设申请-维修id，此处将文档中id修改为apply_repair_id
     */
    @TableField(value = "apply_repair_id")
    private Integer apply_repair_id;

    /**
     * 终端建设申请id
     */
    @TableField(value = "apply_id")
    private Integer apply_id;

    /**
     * 供应商编码，关联供应商表
     */
    @TableField(value = "supplier_code")
    private String supplier_code;

    /**
     * 维修信息备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 实地申请金额
     */
    @TableField(value = "applied_amount")
    private BigDecimal applied_amount;

    /**
     * 实地申请金额(含税)
     */
    @TableField(value = "applied_amount_with_tax")
    private BigDecimal applied_amount_with_tax;

    /**
     * 总部批准金额
     */
    @TableField(value = "approved_amount")
    private BigDecimal approved_amount;

    /**
     * 特殊费用
     */
    @TableField(value = "special_amount")
    private BigDecimal special_amount;

    /**
     * 标准件费用含税
     */
    @TableField(value = "standard_amount")
    private BigDecimal standard_amount;

    /**
     * 差异率(%)
     */
    @TableField(value = "diff_rate")
    private BigDecimal diff_rate;

    /**
     * 税点
     */
    @TableField(value = "tax_rate")
    private BigDecimal tax_rate;
}
