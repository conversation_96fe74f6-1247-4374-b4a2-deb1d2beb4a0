package com.fotile.dep.provider.msp.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * MSP开/闭店申请清单管理-门店申请-闭店申请入表
 */
@Data
public class MspInSApplyCloseDTO implements Serializable {

    /**
     * 门店申请表主键
     **/
    private Integer id;

    /**
     * 申请id
     **/
    private Integer apply_id;

    /**
     * 门店总面积
     **/
    private BigDecimal total_area;

    /**
     * 门店停用时间
     **/
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date down_time;

    /**
     * 闭店原因
     **/
    private String close_reason;
}
