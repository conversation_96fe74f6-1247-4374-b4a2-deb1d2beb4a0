package com.fotile.dep.business.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * 运行日志表VO类，用于列表
 */
@Data
public class DepLogVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日志详情主键ID
     */
    private Long logDetailId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 分发号
     */
    private String distributeNo;

    /**
     * 队列编码
     */
    private String queueCode;

    /**
     * 队列名称
     */
    private String queueName;

    /**
     * 入站平台名称
     */
    private String inPlatformName;

    /**
     * 作业阶段：1-入站，2-转换，3-出站，4-出站回调
     */
    private Integer stage;

    /**
     * 作业阶段：1-入站，2-转换，3-出站，4-出站回调
     */
    private String stageName;

    /**
     * 状态：1-完成，2-错误
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束
     */
    private Date endTime;

    /**
     * 出站回调状态：null-空 1-完成，2-错误
     */
    private Integer callBackStatus;
}
