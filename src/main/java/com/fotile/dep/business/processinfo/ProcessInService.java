package com.fotile.dep.business.processinfo;

import cn.hutool.core.thread.ThreadUtil;
import com.fotile.dep.business.processinfo.pojo.ProcessInfo;
import com.fotile.dep.business.service.abstracts.AbstractProcessReceiveService;
import com.fotile.dep.framework.constant.CommonConstant;
import com.fotile.dep.partner.cem.mq.channel.CemChannel;
import com.fotile.dep.partner.ecs.mq.channel.EcsChannel;
import com.fotile.dep.partner.fotiledrp.mq.channel.DrpChannel;
import com.fotile.dep.partner.fr.mq.channel.FrChannel;
import com.fotile.dep.partner.gddrp.mq.channel.GdDrpChannel;
import com.fotile.dep.partner.mdm.mq.channel.MdmChannel;
import com.fotile.dep.partner.msp.mq.channel.MspChannel;
import com.fotile.dep.util.AopTargetUtils;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 通用数据入站保存及消息发送类
 */
@Slf4j
@Component
//这里一定要多例
@Scope("prototype")
public class ProcessInService {

    public AbstractProcessReceiveService abstractProcessReceiveService;

    @Resource(name = MdmChannel.MDM_ALL_DATA_OUTPUT)
    private MessageChannel mdmChannel;

    @Resource(name = CemChannel.CEM_ALL_DATA_OUTPUT)
    private MessageChannel cemChannel;

    @Resource(name = DrpChannel.DRP_ALL_DATA_OUTPUT)
    private MessageChannel drpChannel;

    @Resource(name = EcsChannel.ECS_ALL_DATA_OUTPUT)
    private MessageChannel ecsChannel;

    @Resource(name = MspChannel.MSP_ALL_DATA_OUTPUT)
    private MessageChannel mspChannel;

    @Resource(name = FrChannel.FR_ALL_DATA_OUTPUT)
    private MessageChannel frChannel;

    @Resource(name = GdDrpChannel.GDDRP_ALL_DATA_OUTPUT)
    private MessageChannel gdDrpChannel;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    /**
     * 保存入站数据并发送kafka
     *
     * @param info 运行过程信息实例
     * @return 保存结果类
     */
    public Result saveAndSendMq(ProcessInfo info) {

        List<ProcessInfo> infoKafkaList = null;
        try {
            //获取调用者的类名
            String className = new Exception().getStackTrace()[0].getClassName();
            //获取调用者的方法名
            String methodName = new Exception().getStackTrace()[0].getMethodName();
            //添加支持入站重试功能的必要信息
            info.setRetryClass(className);
            info.setRetryMethod(methodName);
            info.setRetryInReceiveService(AopTargetUtils.getTarget(abstractProcessReceiveService).getClass().getName());

            infoKafkaList = abstractProcessReceiveService.saveIn(info);

        } catch (Exception e) {

            String eMsg = "接收入站数据,保存入站数据失败";

            try {

                StringWriter sw = new StringWriter();
                e.printStackTrace(new PrintWriter(sw, true));
                log.error("ERROR:method[AbstractProcessReceiveService.saveAndSendMq] msg[{}]",
                        String.format(eMsg + ", processInfo = %s, errormsg = %s", info, sw));
                abstractProcessReceiveService.saveInError(info, e);

            } catch (Exception ex) {

                String exMsg = "接收入站数据,保存错误入站运行日志失败";

                StringWriter sw1 = new StringWriter();
                ex.printStackTrace(new PrintWriter(sw1, true));
                log.error("ERROR:method[AbstractProcessReceiveService.saveAndSendMq] msg[{}]",
                        String.format(exMsg + ", processInfo = %s, errormsg = %s", info, sw1));
                return Result.buildFailure(exMsg);
            }

            return Result.buildFailure(eMsg);
        }

        sendKafka(infoKafkaList);

        return Result.buildSuccess("成功接收入站数据");
    }


    public Boolean retryRedis(ProcessInfo info, Integer retryNum){
        Boolean falg = false;
        for (int i = 0; i <= retryNum ; i++) {

            //重试睡眠时间
            if(i > 0){
                ThreadUtil.safeSleep(1000);
            }

            //入站时将logId放入redis，kafka消费时判断是否有该logId，有则删除的同时进行业务逻辑处理，没有则直接返回，防止kafka重复消费
            falg = stringRedisTemplate.opsForValue()
                    .setIfAbsent(CommonConstant.Redis.RETRY_PREFIX + info.getLogId(), String.valueOf(info.getLogId()), 30, TimeUnit.DAYS);

            Boolean b = stringRedisTemplate.hasKey(CommonConstant.Redis.RETRY_PREFIX + info.getLogId());

            if(falg || b){
                break;
            }
        }

        return falg;
    }

    /**
     * 发送入站消息给MQ
     *
     * @param infos 需要发送的消息列表
     */
    public void sendKafka(List<ProcessInfo> infos) {
        if (CollectionUtils.isNotEmpty(infos)) {
            for (ProcessInfo infoKafka : infos) {
                //入站时将logId放入redis，kafka消费时判断是否有该logId，有则删除的同时进行业务逻辑处理，没有则直接返回，防止kafka重复消费
//                Boolean redisSuccesFlag = stringRedisTemplate.opsForValue()
//                        .setIfAbsent(CommonConstant.Redis.RETRY_PREFIX + infoKafka.getLogId(), String.valueOf(infoKafka.getLogId()), 30, TimeUnit.DAYS);

                //重试redis插入，防止插入失败
                retryRedis(infoKafka, 5);
            }

            for (ProcessInfo infoKafka : infos) {
                String platform = StringUtils.isNoneBlank(infoKafka.getOutPlatformCode()) ? infoKafka.getOutPlatformCode() : "";
                //todo:如何解决kafka异步消息已发送，但事务回滚记录所有队列的运行日志为错误，kafka消费端又会消费已发送数据？？？
                switch (platform) {
                    case CommonConstant.Platform.MDM:
                        mdmChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.CEM:
                        cemChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.FOTILEDRP:
                        drpChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.GDDRP:
                        gdDrpChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.ECS:
                        ecsChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.FANGRUI:
                        frChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    case CommonConstant.Platform.MSP:
                        mspChannel.send(MessageBuilder.withPayload(infoKafka).build());
                        break;
                    default:
                        //作业阶段为入站，若错误，则记录运行日志及其详情，kafka异步消费，如何获取响应结果???
                        log.error("ERROR:method[ProcessInfoService.saveIn] msg[{}]",
                                String.format("入站接收数据保存成功后, 发送kafka消息异常, processInfo = %s, ", infoKafka));
                        break;
                }
            }
        }
    }
}


