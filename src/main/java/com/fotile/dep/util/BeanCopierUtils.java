package com.fotile.dep.util;

import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.core.Converter;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @description cglib的BeanCopier封装类
 * @date 2021/7/30 14:58
 */
public class BeanCopierUtils {
    // 使用WeakHashMap缓存,在内存不足时会自动释放
    private final static Map<String, BeanCopier> BEAN_COPIER_MAP = new WeakHashMap<String, BeanCopier>();
    private final static Map<String, Converter> CONVERTER_MAP = new WeakHashMap<String, Converter>();
    private static Object lock1 = new Object();
    private static Object lock2 = new Object();

    private BeanCopierUtils() {
    }

    /**
     * 创建BeanCopier，并缓存
     *
     * @param src
     * @param target
     * @param useConverter
     * @return
     */
    private static BeanCopier getBeanCopier(Object src, Object target, boolean useConverter) {
        String key = generateKey(src, target, useConverter);
        BeanCopier bc = BEAN_COPIER_MAP.get(key);
        if (null == bc) {
            synchronized (lock1) {
                bc = BEAN_COPIER_MAP.get(key);
                if (null == bc) {
                    bc = BeanCopier.create(src.getClass(), target.getClass(), useConverter);
                    BEAN_COPIER_MAP.put(key, bc);
//          System.out.println("Create BeanCopier with key:" + key);
                }
            }
        }
        return bc;
    }

//  /**
//   * 创建BeanCopier，并缓存
//   *
//   * @param src
//   * @param target
//   * @param useConverter
//   * @return
//   */
//  private static BeanCopier getBeanCopier(Object src, Object target, boolean useConverter) {
//    String key = generateKey(src, target, useConverter);
//    BeanCopier bc = BEAN_COPIER_MAP.get(key);
//
//      synchronized (lock1) {
//
//        if (null == bc) {
//          bc = BeanCopier.create(src.getClass(), target.getClass(), useConverter);
//          BEAN_COPIER_MAP.put(key, bc);
////          System.out.println("Create BeanCopier with key:" + key);
//        }
//      }
//    return bc;
//  }

    /**
     * 复制对象属性
     *
     * @param src
     * @param target
     */
    public static void copy(Object src, Object target) {
        BeanCopier bc = getBeanCopier(src, target, false);
        bc.copy(src, target, null);
    }

    /**
     * 使用自定义的属性转换器复制对象属性
     *
     * @param src
     * @param target
     * @param converter
     */
    public static void copy(Object src, Object target, Converter converter) {
        BeanCopier bc = getBeanCopier(src, target, true);
        bc.copy(src, target, converter);
    }

    /**
     * 对象属性复制，只复制fields中指定的属性，每个属性用逗号分隔
     *
     * @param src
     * @param target
     * @param fields
     */
    public static void copyWithFields(Object src, Object target, final String fields) {
        BeanCopier bc = getBeanCopier(src, target, true);
        bc.copy(src, target, newConverter(src, target, fields, true));
    }

    /**
     * 对象属性复制，排除指定属性
     *
     * @param src
     * @param target
     * @param fields
     */
    public static void copyWithoutFields(Object src, Object target, final String fields) {
        BeanCopier bc = getBeanCopier(src, target, true);
        bc.copy(src, target, newConverter(src, target, fields, false));
    }

    /**
     * new属性转换器，
     *
     * @param fields        需要复制或排除的属性
     * @param fieldCopyFlag 属性复制标识 true:表明fields为需要复制的属性；false:表明fields是需要排除复制的属性
     * @return
     */
    private static Converter newConverter(Object src, Object target, final String fields, final boolean fieldCopyFlag) {
        String key = buildConverterkey(src, target, fields, fieldCopyFlag);
        Converter converter = CONVERTER_MAP.get(key);
        if (null == converter) {
            synchronized (lock2) {
                converter = CONVERTER_MAP.get(key);
                if (null == converter) {
                    converter = new Converter() {
                        @Override
                        public Object convert(Object fieldValue, Class fieldType, Object methodName) {
                            String field = methodName.toString().substring(3); // 得到属性名，如Name
                            field = field.substring(0, 1).toLowerCase() + field.substring(1); // 将首字母小写
                            if ((fieldCopyFlag && fields.contains(field))
                                    || (!fieldCopyFlag && !fields.contains(field))) {
                                return fieldValue;
                            }
                            return null;
                        }
                    };
                    CONVERTER_MAP.put(key, converter);
//          System.out.println("Created Converter with key:" + key);
                }
            }
        }
        return converter;
    }

    private static String generateKey(Object src, Object target, boolean useConverter) {
        return src.getClass().getName() + target.getClass().getName() + String.valueOf(useConverter);
    }

    private static String buildConverterkey(Object src, Object target, String fields, boolean copyFlag) {
        String baseKey = generateKey(src, target, true);
        String key = baseKey + fields + String.valueOf(copyFlag);
        return key;
    }

    /**
     * 复制某个对象为目标对象类型的对象
     *
     * @param source    源对象
     * @param destClazz 目标对象类型
     * @param <T>       目标对象
     * @return
     */
    public static <T> T copyAs(Object source, Class<T> destClazz) {
        if (source == null) {
            return null;
        }

        try {
            T dest = destClazz.newInstance();
            copy(source, dest);
            return dest;
        } catch (InstantiationException e) {
            throw new RuntimeException();
        } catch (IllegalAccessException e) {
            throw new RuntimeException();
        }
    }

    /**
     * 复制某个集合对象为目标对象类型的对象，并返回目标对象类型的集合
     *
     * @param source    源对象
     * @param destClazz 目标对象类型
     * @param <T>       目标对象集合
     * @return
     */
    public static <T> List<T> copyAs(Collection<?> source, Class<T> destClazz) {
        if (source == null) {
            return null;
        }

        List<T> resultList = new ArrayList<T>();
        for (Object obj : source) {
            resultList.add(copyAs(obj, destClazz));
        }

        return resultList;
    }

    //*************************bean属性首尾去空***************************

    /**
     * 去除bean中所有属性为字符串的前后空格
     *
     * @param bean
     */
    public static void beanAttributeValueTrim(Object bean) {
        if (bean != null) {
            try {
                //获取所有的字段包括public,private,protected,private
                Field[] fields = bean.getClass().getDeclaredFields();
                for (int i = 0; i < fields.length; i++) {
                    Field f = fields[i];
                    if (f.getType().isAssignableFrom(String.class)) {
                        String key = f.getName();
                        //获取字段名
                        Object value = getFieldValue(bean, key);
                        if (value == null) {
                            continue;
                        }

                        setFieldValue(bean, key, value.toString().trim());
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException();
            }
        }
    }

    /**
     * 利用反射通过get方法获取bean中字段fieldName的值
     *
     * @param bean
     * @param fieldName
     * @return
     * @throws Exception
     */
    private static Object getFieldValue(Object bean, String fieldName)
            throws Exception {
        StringBuffer result = new StringBuffer();
        String methodName = result.append("get")
                .append(fieldName.substring(0, 1).toUpperCase())
                .append(fieldName.substring(1)).toString();

        Object rObject = null;
        Method method = null;

        @SuppressWarnings("rawtypes")
        Class[] classArr = new Class[0];
        method = bean.getClass().getMethod(methodName, classArr);
        rObject = method.invoke(bean, new Object[0]);

        return rObject;
    }

    /**
     * 利用发射调用bean.set方法将value设置到字段
     *
     * @param bean
     * @param fieldName
     * @param value
     * @throws Exception
     */
    private static void setFieldValue(Object bean, String fieldName, Object value)
            throws Exception {
        StringBuffer result = new StringBuffer();
        String methodName = result.append("set")
                .append(fieldName.substring(0, 1).toUpperCase())
                .append(fieldName.substring(1)).toString();

        //利用反射调用bean.set方法将value设置到字段
        Class[] classArr = new Class[1];
        classArr[0] = "java.lang.String".getClass();
        Method method = bean.getClass().getMethod(methodName, classArr);
        method.invoke(bean, value);
    }

    /**
     * 深拷贝
     *
     * @param srcList
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> depCopy(List<T> srcList) {
        List<T> destList = null;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(srcList);
            out.close();

            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream inStream = new ObjectInputStream(byteIn);
            destList = (List<T>) inStream.readObject();
            inStream.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

        return destList;
    }

    /**
     * 深拷贝
     *
     * @param obj
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> Map depCopy(Map obj) {
        T cloneObj = null;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(cloneObj);
            out.close();

            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream inStream = new ObjectInputStream(byteIn);
            cloneObj = (T) inStream.readObject();
            inStream.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

        return (Map) cloneObj;
    }

}
