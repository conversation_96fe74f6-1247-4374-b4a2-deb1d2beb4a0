package com.fotile.taskcenter.tasklog.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.taskcenter.constant.TaskLogEnum;
import com.fotile.taskcenter.tasklog.pojo.dto.QueryTaskLogDTO;
import com.fotile.taskcenter.tasklog.pojo.entity.TaskOperateLog;
import com.fotile.taskcenter.tasklog.service.TaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 任务日志API
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/7/30 16:57
 */
@RestController
@RequestMapping("/api/task/log")
@Slf4j
public class TaskLogController extends BaseController {

    @Autowired
    private TaskLogService taskLogService;

    /**
     * 写任务日志
     *
     * @param logInfo 日志实体
     * @return 日志ID
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Long> add(@RequestBody @Valid TaskOperateLog logInfo) {
        if (logInfo == null) {
            return failure("参数错误！");
        }

        try {
            TaskLogEnum event = TaskLogEnum.ADD_DESCRIPTION;
            Long logId = taskLogService.add(logInfo, event);
            if (logId == null || logId <= 0) {
                return failure("操作失败！");
            }
            return success("操作成功！", logId);
        } catch (BusinessException ex) {
            log.error("TaskLogController.add 添加操作日志失败！Request -> " + JSON.toJSONString(logInfo), ex);
            if (StringUtils.isNotBlank(ex.getMessage())) {
                return failure(ex.getMessage());
            }
            return failure("操作失败，请与管理员联系！");
        } catch (Exception ex) {
            log.error("DesignerClubLogController.add 添加操作日志发生异常！Request -> " + JSON.toJSONString(logInfo), ex);
            return failure("操作异常：" + ex.getMessage());
        }
    }

    /**
     * 获取任务操作日志总记录数
     *
     * @param query 查询参数
     * @return 总记录数
     */
    @RequestMapping(value = "/count", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> count(@RequestBody @Valid QueryTaskLogDTO query) {
        if (query == null) {
            return failure("参数错误！");
        }

        Integer count = taskLogService.count(query);
        return success("获取任务操作日志总记录数成功！", count == null ? 0 : count);
    }

    /**
     * 获取任务操作日志分页列表
     *
     * @param query 查询参数
     * @return page of TaskOperateLog
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<TaskOperateLog>> query(@RequestBody @Valid QueryTaskLogDTO query) {
        if (query == null) {
            return failure("参数错误！");
        }

        PageInfo<TaskOperateLog> pageInfo = taskLogService.query(query);
        return success("获取任务操作日志分页列表成功！", pageInfo);
    }

}
