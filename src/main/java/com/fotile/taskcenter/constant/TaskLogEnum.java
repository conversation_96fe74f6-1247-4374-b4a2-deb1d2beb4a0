package com.fotile.taskcenter.constant;

/**
 * 日志操作类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/8 11:12
 */
public enum TaskLogEnum {
    /* *-------------------- 日志通用相关 start --------------------* */
    /**
     * 通用-NULL-未知
     */
    NULL(null, null),

    /**
     * 通用-默认
     */
    DEFAULT("", ""),

    /**
     * 通用-添加描述【101】
     */
    ADD_DESCRIPTION("101", "添加描述"),

    /**
     * 通用-创建记录【102】
     */
    CREATE("102", "创建"),

    /**
     * 通用-修改记录【103】
     */
    UPDATE("103", "修改"),

    /**
     * 通用-审核通过【104】
     */
    AUDIT_PASS("104", "审核通过"),

    /**
     * 通用-审核拒绝【105】
     */
    AUDIT_REFUSE("105", "审核拒绝"),

    /**
     * 通用-启用【106】
     */
    ENABLE("106", "启用"),

    /**
     * 通用-禁用【107】
     */
    DISABLE("107", "禁用"),

    /**
     * 通用-删除记录【108】
     */
    DELETE("108", "删除"),

    /**
     * 通用-跟进【109】
     */
    FOLLOW_UP("109", "跟进"),

    /* *-------------------- 日志通用相关 end --------------------* */

    /**
     * 权益金任务-新增任务【201】
     */
    BENEFIT_TASK_ADD("201", "新增任务"),

    /**
     * 权益金任务-编辑任务说明【202】
     */
    BENEFIT_TASK_EDIT("202", "编辑任务说明"),

    /**
     * 权益金任务-编辑奖励规则【203】
     */
    BENEFIT_TASK_EDIT_RULE("203", "编辑奖励规则");

    /* *-------------------- 权益金任务相关 start --------------------* */



    /* *-------------------- 权益金任务相关 end --------------------* */;


    TaskLogEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
