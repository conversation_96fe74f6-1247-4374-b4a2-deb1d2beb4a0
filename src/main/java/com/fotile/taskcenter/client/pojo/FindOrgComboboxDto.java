package com.fotile.taskcenter.client.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class FindOrgComboboxDto implements Serializable {
    public FindOrgComboboxDto(boolean isOpenAPI) {
        if (isOpenAPI) {
            this.requireCompanyDataScope = false;
            this.requireStoreDataScope = false;
            this.requireDeptDataScope = false;
        }
    }

    /**
     * 数据类型(1:分公司,2:部门,3:门店)
     */
    @NotNull(message = "类型不能为空！")
    @Range(min = 1, max = 3, message = "错误的数据类型！")
    private Integer type;

    /**
     * 查询关键字
     */
    private String keyword;

    /**
     * 门店状态(0：禁用；1：启用；2：筹备；3：清退，支持多个状态查询，使用半角逗号分隔，eg:1,2,3)
     */
    private String storeStatusMulti;

    /**
     * 门店所属分公司OrgId
     */
    private Long storeCompanyOrgId;

    /**
     * 是否虚拟门店，0-否，1-是(type=3有效)
     */
    private Integer isVirtual;

    /**
     * 门店所属渠道编码(channel_category.code type_code=store_type,多个逗号半角逗号分隔)(type=3有效)
     */
    private String storeChannelCode;

    /**
     * 客户渠道大类编码集合(多个半角逗号分隔)(type=3有效)
     */
    private String channelCategoryCode;

    /**
     * 客户渠道小类编码集合(多个半角逗号分隔)(type=3有效)
     */
    private String channelSubdivideCode;

    /**
     * 渠道大类ID(多个半角逗号分隔)(type=3有效)
     */
    private String channelCategoryIds;

    /**
     * 渠道细分ID(多个半角逗号分隔)(type=3有效)
     */
    private String channelSubdivideIds;

    /**
     * 是否需要分公司数据隔离(默认是)
     */
    private Boolean requireCompanyDataScope = true;

    /**
     * 是否需要部门数据隔离(默认是)
     */
    private Boolean requireDeptDataScope = true;

    /**
     * 是否需要门店数据隔离(默认是)
     */
    private Boolean requireStoreDataScope = true;

    /**
     * 组织结构id集合
     */
    private List<Long> orgIds;

    /**
     * 目标实体id集合(同type:分公司ID-t_company.id；部门ID-t_department.id；门店ID-t_store.id)
     */
    private List<Long> ids;

    /**
     * 需要排除掉的OrgIds
     */
    private List<Long> filterOrgIds;

    /**
     * 需要排除掉的Ids
     */
    private List<Long> filterIds;

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空！")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @NotNull(message = "每页条数不能为空！")
    private Integer size = 10;
}
