package com.fotile.taskcenter.client;

import com.fotile.framework.web.Result;
import com.fotile.taskcenter.customer.entity.CustomerInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "customer-center")
public interface CustomerClient {
    @GetMapping("/api/customer/queryCustomerPhoneById")
    Result<String> queryCustomerPhoneById(@RequestParam("uid") Long uid);

    @GetMapping("/m/api/customer/queryCustomerPhoneById")
    Result<String> queryCustomerPhoneByIdH5(@RequestParam("uid") Long uid);

    /**
     * 根据账号id查询顾客信息
     *
     * @param userEntity
     * @return
     */
    @RequestMapping(value = "/api/m/customer/queryCustomerInfoByUserEntity", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CustomerInfo> queryCustomerInfoByUserEntity(@RequestParam("userEntity") String userEntity);
    /** * 根据id查询顾客信息 */
    @RequestMapping(value = "/api/m/customer/queryCustomerInfoById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CustomerInfo> queryCustomerInfoById(@RequestParam("id") Long id);


}
