package com.fotile.taskcenter.benefits.mq.task;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.taskcenter.benefits.pojo.dto.QueryBenefitTaskDTO;
import com.fotile.taskcenter.benefits.pojo.vo.QueryBenefitTaskVO;
import com.fotile.taskcenter.benefits.service.BenefitTaskService;
import com.fotile.taskcenter.client.DataClient;
import com.fotile.taskcenter.client.pojo.ChannelInfo;
import com.fotile.taskcenter.client.pojo.Dic;
import com.fotile.taskcenter.client.pojo.ExportTaskRecord;
import com.fotile.taskcenter.task.Util.OssService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 权益金任务列表导出
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/7/29 15:36
 */
@Component("1806")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "fourth")
public class AsyncExportBenefitTaskImpl implements AsyncExportBenefitTask {
    @Autowired
    private BenefitTaskService benefitTaskService;

    @Autowired
    private DataClient dataClient;

    @Autowired
    private OssService ossService;

    @Override
    public void exportBenefitTask(ExportTaskRecord exportTaskRecord) {
        if (exportTaskRecord == null || StringUtils.isBlank(exportTaskRecord.getParamJson())) {
            return;
        }

        QueryBenefitTaskDTO inDto = JSON.parseObject(exportTaskRecord.getParamJson(), QueryBenefitTaskDTO.class);
        if (inDto == null) {
            return;
        }

        //设置为数据导出
        inDto.setExport(1);
        inDto.initQuery();

        //批量查询总条数
        Integer totalCount = exportTaskRecord.getTotalCount();
        //进度
        BigDecimal oldProgress = exportTaskRecord.getProgress();
        Integer count = 50000;
        Integer start1 = inDto.getStart();

        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        //生成Excel，并上传oss
        String sheetName = "权益金任务列表导出";
        String fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        List<QueryBenefitTaskVO> list = new ArrayList<>();

        try {
            for (int j = 0; j <= i; j++) {
                int start = start1 + (j) * count;
                size = count;
                if (j == i) {
                    size = lastCount;
                }
                inDto.setOffset((long) start);
                inDto.setSize(size);

                List<QueryBenefitTaskVO> result = benefitTaskService.getExportBenefitTaskList(inDto);
                if (CollectionUtils.isNotEmpty(result)) {
                    list.addAll(result);
                }

                //更新任务进度
                BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
                oldProgress = newProgress.add(oldProgress);

                ExportTaskRecord exportTask = new ExportTaskRecord();
                exportTask.setId(exportTaskRecord.getId());
                exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
                exportTask.setExt1(String.valueOf(list.size()));
                dataClient.updateTask(exportTask);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                List<Dic> allDictionaries = benefitTaskService.getAllDictionaries();
                List<ChannelInfo> allChannelList = benefitTaskService.getAllChannelList();

                boolean flag = Objects.equals("1", inDto.getFlag());
                list.forEach(item -> {
                    item.formatOutput(flag, allDictionaries, allChannelList, null, null, null, null);
                });
            }

            //写入excel文件流
            EasyExcel.write(os, QueryBenefitTaskVO.class).sheet(sheetName).doWrite(list);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);

            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            dataClient.successTask(exportTask);

            log.info(">>>>>>>>>>>> AsyncExportBenefitTaskImpl.exportBenefitTask >>>>>>>>>>>> benefit task data export successfully.");
        } catch (Exception ex) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(ex.getMessage());
            dataClient.failureTask(exportTask);

            log.error(">>>>>>>>>>>> AsyncExportBenefitTaskImpl.exportBenefitTask >>>>>>>>>>>> ", ex);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException ex) {
                log.error(">>>>>>>>>>>> AsyncExportBenefitTaskImpl.exportBenefitTask >>>>>>>>>>>> ", ex);
            }
        }
    }
}
