package com.fotile.taskcenter.benefits.pojo.vo.rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 权益金任务规则-指定签到日期-【签到】
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/8 16:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BenefitTaskRuleSignDay implements Serializable {
    /**
     * 指定签到日期(周一 ~ 周日)
     */
    private String dayName;

    /**
     * 指定签到日期(取值范围【1~7】)
     */
    private Integer dayValue;
}
