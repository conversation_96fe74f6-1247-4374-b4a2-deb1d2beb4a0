package com.fotile.taskcenter.benefits.pojo.vo.rule;

import com.fotile.taskcenter.constant.BenefitTaskConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 权益金任务规则跳转小程序-【通用】
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/8 17:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BenefitTaskRuleWechatApp implements Serializable {
    /**
     * 小程序AppId
     */
    private String appId;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * 所属渠道编码
     */
    private String channelCode;

    public BenefitTaskRuleWechatApp(BenefitTaskConstant.BenefitTaskWechatAppEnum wechatAppEnum) {
        if (wechatAppEnum != null && wechatAppEnum != BenefitTaskConstant.BenefitTaskWechatAppEnum.NULL) {
            this.appId = wechatAppEnum.getAppId();
            this.appName = wechatAppEnum.getAppName();
            this.channelCode = wechatAppEnum.getChannelCode();
        }
    }
}
