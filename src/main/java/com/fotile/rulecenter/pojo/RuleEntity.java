package com.fotile.rulecenter.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("规则")
@TableName(value = "t_rule", schema = "rulecenter")
public class RuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表主键")
    private Integer id;

    /**
     * 规则名
     */
    @TableField(value = "rule")
    private String rule;

    /**
     * 规则描述
     */
    @TableField(value = "rule_desc")
    private String ruleDesc;

    /**
     * 规则条件
     */
    @TableField(value = "rule_when")
    private String ruleWhen;

    /**
     * 规则结果
     */
    @TableField(value = "rule_then")
    private String ruleThen;

    /**
     * 是否通过校验：0-通过 1-未通过
     */
    @TableField(value = "is_success")
    private Integer isSuccess;
}
