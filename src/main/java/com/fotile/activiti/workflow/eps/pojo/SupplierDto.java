package com.fotile.activiti.workflow.eps.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class SupplierDto implements Serializable {
    /**
     * 供应商编码
     */
    @NotBlank(message = "供应商编码不能空")
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 状态(0禁用1启用)
     */
    private Integer status;

    /**
     * 供应商id，自己用
     */
    private Long id;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SupplierDto dto = (SupplierDto) o;
        return Objects.equals(supplierCode, dto.supplierCode) &&
                Objects.equals(supplierName, dto.supplierName) &&
                Objects.equals(status, dto.status) &&
                Objects.equals(id, dto.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(supplierCode, supplierName, status, id);
    }
}
