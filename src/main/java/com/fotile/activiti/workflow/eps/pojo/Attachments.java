package com.fotile.activiti.workflow.eps.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021-10-22-10:24
 */
@Data
@Accessors(chain = true)
public class Attachments {

    private Long id;

    private String refTable;

    private Integer refType;

    private Integer refId;

    private Integer sort;

    private String title;

    private String attachmentUrl;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;

    private Boolean isDeleted;
    /**
     * 附件的缩略图url
     */
    private String thumbnailUrl;
}
