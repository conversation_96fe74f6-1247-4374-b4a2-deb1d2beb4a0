package com.fotile.activiti.workflow.eps.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.activiti.workflow.business.service.annotation.CompareField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class DesignerDataDto implements Serializable {

    @JsonIgnore
    private Integer applyId;

    /**
     * 非标设计，0-无，1-有
     */
    @NotNull(message = "非标设计不能空")
    private Integer noStandardDesign;
    /**
     * 非标内容
     */
    @CompareField(name = "非标内容",group = "设计资料信息")
    private String noStandardContent;
    /**
     * 施工预算
     */
    @CompareField(name = "施工预算",group = "设计资料信息")
    private BigDecimal budget;
    /**
     * 平面布局图

    private List<String> plane;   */
    /**
     * 平面布局图
     */
    private List<Attachments> plane;

    @JsonIgnore
    @CompareField(name = "平面布局图数量",group = "设计资料信息")
    private Integer planeCount;

    /**
     * 效果图

    private List<String> effect;*/
    private List<Attachments> effect;

    @JsonIgnore
    @CompareField(name = "效果图数量",group = "设计资料信息")
    private Integer effectCount;


    /**
     * 照明布局图

    private List<String> lighting;*/
    private List<Attachments> lighting;

    @JsonIgnore
    @CompareField(name = "照明布局图数量",group = "设计资料信息")
    private Integer lightingCount;

    /**
     * 施工图

    private List<String> productionDrawing;*/
    private List<Attachments> productionDrawing;

    @JsonIgnore
    @CompareField(name = "施工设计图数量",group = "设计资料信息")
    private Integer productionDrawingCount;

    /**
     * 预算清单附件
    private List<String> budgetAttach; */
    private List<Attachments> budgetAttach;

    @JsonIgnore
    @CompareField(name = "预算清单数量",group = "设计资料信息")
    private Integer budgetAttachCount;
    /**
     * 终端建设申请编码
     */
    @NotBlank(message = "终端建设申请编码不能空")
    private String code;

    /**
     * 整改前图纸

    private List<String> beforeChange; */
    private List<Attachments> beforeChange;

    @JsonIgnore
    @CompareField(name = "整改前图纸数量",group = "设计资料信息")
    private Integer beforeChangeCount;
    /**
     * 整改后图纸

    private List<String> afterChange;*/

    private List<Attachments> afterChange;

    @JsonIgnore
    @CompareField(name = "整改后图纸",group = "设计资料信息")
    private Integer afterChangeCount;
    /**
     * 费用清单
     */
    private List<CostOfListingDto> costOfListing;

    private Integer costOfListingCount;

    @CompareField(name = "非标设计",group = "设计资料信息")
    private String nonStandardDesignName;

    public void setNonStandardDesign(Integer nonStandardDesign) {
        this.noStandardDesign = nonStandardDesign;
        if(nonStandardDesign!=null){
            if(nonStandardDesign==0){
                this.nonStandardDesignName="无";
            }else if(nonStandardDesign==1){
                this.nonStandardDesignName="有";
            }
        }
    }

    public void setCostOfListing(List<CostOfListingDto> costOfListing) {
        this.costOfListing = costOfListing;
        this.costOfListingCount=costOfListing==null?0:costOfListing.size();
    }

    public void setPlane(List<Attachments> plane) {
        this.plane = plane;
        this.planeCount=plane==null?0:plane.size();
    }

    public void setEffect(List<Attachments> effect) {
        this.effect = effect;
        this.effectCount=effect==null?0:effect.size();
    }

    public void setLighting(List<Attachments> lighting) {
        this.lighting = lighting;
        this.lightingCount=lighting==null?0:lighting.size();
    }

    public void setProductionDrawing(List<Attachments> productionDrawing) {
        this.productionDrawing = productionDrawing;
        this.productionDrawingCount=productionDrawing==null?0:productionDrawing.size();
    }

    public void setBudgetAttach(List<Attachments> budgetAttach) {
        this.budgetAttach = budgetAttach;
        this.budgetAttachCount=budgetAttach==null?0:budgetAttach.size();
    }

    public void setBeforeChange(List<Attachments> beforeChange) {
        this.beforeChange = beforeChange;
        this.beforeChangeCount=beforeChange==null?0:beforeChange.size();
    }

    public void setAfterChange(List<Attachments> afterChange) {
        this.afterChange = afterChange;
        this.afterChangeCount=afterChange==null?0:afterChange.size();
    }
}
