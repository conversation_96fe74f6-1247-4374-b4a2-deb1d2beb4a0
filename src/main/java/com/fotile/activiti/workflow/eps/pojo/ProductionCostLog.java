package com.fotile.activiti.workflow.eps.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.activiti.workflow.business.service.annotation.CompareField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Create By lubing on 2023/11/22.
 */
@Data
public class ProductionCostLog {
    /**
     * 关联子表id
     */
    @JsonIgnore
    private Integer externalId;
    /**
     * 实际使用面积 0609期修改，常规使用
     */
    private BigDecimal actualUsableArea;
    /**
     * 税点 0609期修改，常规 维修 整改 公用
     */
    private BigDecimal taxPointRate;

    /**
     * 特殊费用
     */
    private BigDecimal specialTotalPrice;
    /**
     * 标准件费用含税
     */
    private BigDecimal standardTotalPrice;
    /**
     * 差异率
     */
    private BigDecimal discrepancyRate;

    private String decorateVersion;

    private String doorSize;

    private BigDecimal signSize;

    /**
     * 实际支付金额
     */
    private BigDecimal realTotalPrice;

    /**
     * 门店建设均价
     */
    private BigDecimal averagePrice;

    /**
     * 一楼制作费用
     */
    @CompareField(name = "一楼制作费用（元）",group = "决算费用合计")
    private BigDecimal firstFloorAmount;
    /**
     * 二楼制作费用
     */
    @CompareField(name = "二楼制作费用（元）",group = "决算费用合计")
    private BigDecimal secondFloorAmount;
    /**
     * 三楼制作费用
     */
    @CompareField(name = "三楼制作费用（元）",group = "决算费用合计")
    private BigDecimal thirdFloorAmount;
    /**
     * 楼梯制作费用
     */
    @CompareField(name = "楼梯制作费用（元）",group = "决算费用合计")
    private BigDecimal stairsAmount;
    /**
     * 外立面制作费用
     */
    @CompareField(name = "外立面制作费用（元）",group = "决算费用合计")
    private BigDecimal facadeAmount;
    /**
     * 软装制作费用
     */
    @CompareField(name = "软装制作费用（元）",group = "决算费用合计")
    private BigDecimal interiorDecorationAmount;
    /**
     * 其他费用
     */
    @CompareField(name = "其他费用（元）",group = "决算费用合计")
    private BigDecimal elseAmount;

}
