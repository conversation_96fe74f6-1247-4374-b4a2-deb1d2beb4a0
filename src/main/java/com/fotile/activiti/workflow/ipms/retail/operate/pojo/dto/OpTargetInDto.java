package com.fotile.activiti.workflow.ipms.retail.operate.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 总部-目标分解
 */
@Data
public class OpTargetInDto extends AuditingEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 操盘计划id
     */
    private Long opId;

    /**
     * 大区code
     */
    private String regionCode;

    /**
     * 大区名称
     */
    private String regionName;

    /**
     * 分公司id
     */
    private Long companyId;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 计划上样台数
     */
    private Integer sampleQuantityPlanned;

    /**
     * 实际上样台数
     */
    private Integer sampleQuantity;

    /**
     * 实际上样完成率
     *
     * =实际上样台数/计划上样台数
     */
    private String sampleQuantityPer;

    /**
     * 销售渠道现有门店数量(启用)
     */
    private Integer storeQuantity;

    /**
     * 计划上样门店数量
     */
    private Integer sampleStoreQuantityPlanned;

    /**
     * 实际上样门店数量
     */
    private Integer sampleStoreQuantity;

    /**
     * 计划覆盖率
     *
     * =计划上样门店数量/现有门店
     */
    private String sampleStoreQuantityPlannedPer;

    /**
     * 实际门店覆盖完成率
     *
     * =实际上样门店数量/计划上样门店数量
     */
    private String sampleStoreQuantityPer;

    /**
     * 上市首月销售目标台数
     */
    private Integer saleTargetFirstMonth;

    /**
     * 实际上市首月销售台数
     */
    private Integer saleQuantityFirstMonth;

    /**
     * 上市次月销售目标台数
     */
    private Integer saleTargetSecondMonth;

    /**
     * 实际上市次月销售台数
     */
    private Integer saleQuantitySecondMonth;

    /**
     * 上市第三个月销售目标台数
     */
    private Integer saleTargetThirdMonth;

    /**
     * 实际上市第三个月销售台数
     */
    private Integer saleQuantityThirdMonth;

    /**
     * 计划月销目标台数
     */
    private Integer saleTargetPerMonth;

    /**
     * 实际月销台数(T+1)
     */
    private Integer saleQuantityPerMonth;

    /**
     * 销售起始日期(第一台样机盘点的保存时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date saleStartTime;

    /**
     * 计划爬坡周期
     */
    private Integer climbingDaysPlanned;

    /**
     * 实际爬坡周期
     */
    private Integer climbingDays;

    /**
     * 实际布展完成率
     */
    private BigDecimal layoutCompletionRate;

    /**
     * 实际布展完成率
     *
     * =实际布展完成门店/实际上样门店数量
     */
    private String layoutCompletionRatePer;

     private Integer layoutStoreQuantity;

     private List<Long> opIds;
}
