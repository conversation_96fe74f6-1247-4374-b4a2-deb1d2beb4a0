package com.fotile.activiti.workflow.business.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.activiti.workflow.business.pojo.entity.ApplyBill;

public interface ApplyBillDao extends BaseMapper<ApplyBill> {
    int deleteByPrimaryKey(Integer id);

    int insert(ApplyBill record);

    int insertSelective(ApplyBill record);

    ApplyBill selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ApplyBill record);

    int updateByPrimaryKey(ApplyBill record);
}