package com.fotile.activiti.workflow.business.job;

import com.fotile.activiti.workflow.business.service.openExplosive.OpenExplosiveJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 根据健康度得分计算规则计算门店健康度得分（注意：每月1日执行！！！）
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.job
 * @date 2023/7/12 10:22
 */
@Component
@Slf4j
public class CalcStoreHealthScoreJob extends IJobHandler {
    @Resource
    private OpenExplosiveJobService jobService;

    /**
     * 根据健康度得分计算规则计算门店健康度得分（每月1日执行）
     *
     * @param params 参数格式：debug:true id:xxx
     * @return ReturnT
     * @throws Exception 异常
     */
    @Override
    @XxlJob(value = "calcStoreHealthScoreJob")
    public ReturnT<String> execute(String params) throws Exception {
        try {
            Boolean isDebug = false;
            Long storeOpenExplosiveId = null;
            if (StringUtils.isNotBlank(params)) {
                List<String> ps = Arrays.asList(params.split(" "));
                if (CollectionUtils.isNotEmpty(ps)) {
                    for (String p : ps) {
                        String[] cp = p.split(":");
                        if (cp.length == 2 && StringUtils.isNotBlank(cp[1])) {
                            if ("debug".equalsIgnoreCase(cp[0])) {
                                isDebug = "true".equalsIgnoreCase(cp[1]);
                            } else if ("id".equalsIgnoreCase(cp[0])) {
                                storeOpenExplosiveId = Long.valueOf(cp[1]);
                            }
                        }
                    }
                }
            }

            jobService.calcStoreHealthScore(isDebug, storeOpenExplosiveId);
        } catch (Exception ex) {
            log.error("calcStoreHealthScoreJob执行出现异常！", ex);
            return new ReturnT<>(ReturnT.FAIL_CODE, "计算门店健康度得分任务执行失败！");
        }
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "计算门店健康度得分任务执行成功！");
    }
}
