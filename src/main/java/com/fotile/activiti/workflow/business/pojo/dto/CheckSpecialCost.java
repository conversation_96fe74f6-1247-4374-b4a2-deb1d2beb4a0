package com.fotile.activiti.workflow.business.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.activiti.workflow.business.pojo.dto.terminal.CostInfo;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.dto
 * @date 2021/6/1 16:16
 */
@Data
public class CheckSpecialCost implements Serializable {
    /**
     * 验收类型(3:常规门店终端建设,4:大型门店终端建设,5:维修申请,6:整改申请)
     */
    /*@NotNull(message = "验收类型不能为空！")
    @Range(min = 3, max = 6, message = "错误的验收类型！")
    private Integer type;*/

    /**
     * 特殊费用清单价格信息
     */
    @NotNull(message = "特殊费用清单价格信息")
    private List<CostInfo> cost;

    /**
     * 申请ID
     */
    @NotNull(message = "申请ID不能为空！")
    private Integer applyId;

    /**
     * 申请标题
     */
    private String title;

    /**
     * 内部参数(操作人Id)
     */
    @JsonIgnore
    private transient String operatorId;

    /**
     * 内部参数(操作人姓名)
     */
    @JsonIgnore
    @FieldEncrypt
    private transient String operatorName;
}
