package com.fotile.activiti.workflow.business.export.dto.openPlan;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fotile.activiti.workflow.business.pojo.interfaces.PlanAttribute;
import com.fotile.activiti.workflow.business.service.annotation.CompareField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Create By lubing on 2022/3/7.
 */
@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
@ExcelIgnoreUnannotated
public class ExportOpenPlanDetailDto implements Serializable, PlanAttribute {

    private Long id;

    @ColumnWidth(10)
    @ExcelProperty(value = "序号", index = 0)
    @ApiModelProperty("序号")
    private Long orderNumber;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否升级")
    @CompareField(name = "是否升级")
    private String ifUpgradeName;

    /**
     * 规划编码
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "规划编码")
    private String code;

    /**
     * 规划类型
     */
    private String type;

    @ColumnWidth(10)
    @ExcelProperty(value = "规划类型")
    @CompareField(name = "规划类型")
    private String typeName;


    /**
     * 大区名称
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "所属大区")
    @CompareField(name = "所属大区")
    private String regionName;


    /**
     * 分公司名称
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "所属分公司")
    @CompareField(name = "所属分公司")
    private String companyName;

    /**
     * 省name
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "省")
    @CompareField(name = "省")
    private String provicenName;

    /**
     * 市name
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "市")
    @CompareField(name = "市")
    private String cityName;

    /**
     * 区name
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "区")
    @CompareField(name = "区")
    private String countyName;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否招商")
    @CompareField(name = "是否招商")
    private String attractedInvestmentName;

    /**
     * 预估承接客户编码
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "预估承接客户编码")
    private String estimateDistributorCode;
    /**
     * 预估承接客户名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "预估承接客户名称")
    @CompareField(name = "预估承接客户")
    private String estimateDistributorName;

    /**
     * 预估选址完成时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "预估选址完成时间")
    @CompareField(name = "预估选址完成时间")
    private Date estimateSiteChooseDate;

    /**
     * 预计完工时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "预计完工时间")
    @CompareField(name = "预计完工时间")
    private Date estimatedFinishDate;

    /**
     * 预估开业活动落地时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "预估开业活动落地时间")
    @CompareField(name = "预估开业活动落地时间")
    private Date estimateOpenCelebrateDate;

    private String storeChannelCode;
    /**
     * 门店渠道名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "门店渠道")
    @CompareField(name = "门店渠道")
    private String storeChannelName;

    private String storeSubChannelCode;

    /**
     * 门店渠道细分
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "门店渠道细分")
    @CompareField(name = "门店渠道细分")
    private String storeSubChannelName;

    /**
     * --初测面积
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店预估面积")
    @CompareField(name = "门店预估面积")
    private BigDecimal usableArea;

    /**
     * 门店级别
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "门店类别")
    @CompareField(name = "门店类别")
    private String storeLevel;
    /**
     * 门店标签
     */
    private String keyWord;

    /**
     * 门店标签名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "门店标签")
    private String keyWordNames;



    @ColumnWidth(20)
    @ExcelProperty(value = "门店编码")
    private String storeCode;

    @ColumnWidth(30)
    @ExcelProperty(value = "门店名称")
    private String storeName;

    /**
     * 门店创建时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "客户编码")
    private String distributorCode;

    @ColumnWidth(30)
    @ExcelProperty(value = "客户名称")
    private String distributorName;

    @ApiModelProperty(value = "门店状态，0：禁用；1：启用；2：筹备；3：清退", example = "门店状态，0：禁用；1：启用；2：筹备；3：清退")
    private Integer storeStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店状态")
    private String storeStatusName;


    private String storeCharacter;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店性质")
    private String characterName;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店整体使用面积")
    private BigDecimal epsUsableArea;

    private Integer decorateStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = "装修进度")
    private String decorationTypeName;

    /**
     * 门店创建时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "门店编码生成时间")
    private Date storeCreatedDate;
    /**
     * 制作商接单时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "制作进场时间")
    private Date orderTakingDate;
    /**
     * 终端验收时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "新装验收时间")
    private Date terminalCheckDate;

    /**
     * 主门店重启时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "重启时间")
    private Date storeReopenDate;

    @ColumnWidth(30)
    @ExcelProperty(value = "重装验收时间")
    @ApiModelProperty("重装验收时间")
    private Date terminalCheckDateAgain;

    /**
     * 主门店渠道变成流程完成时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "渠道变更时间")
    private Date storeChannelChangeDate;

    @ColumnWidth(30)
    @ExcelProperty(value = "闭店申请审核通过时间")
    private Date storeCloseDate;
    /**
     * 关联门店编码
     */
    private List<String> relationStoreCodes;
}
