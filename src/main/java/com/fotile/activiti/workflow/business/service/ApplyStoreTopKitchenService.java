package com.fotile.activiti.workflow.business.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.activiti.workflow.business.dao.SApplyOpenTopKitchenDao;
import com.fotile.activiti.workflow.business.pojo.entity.SApplyOpenTopKitchen;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-01-16:00
 */
@Service
public class ApplyStoreTopKitchenService extends ServiceImpl<SApplyOpenTopKitchenDao, SApplyOpenTopKitchen> {
    @Resource
    private SApplyOpenTopKitchenDao applyOpenTopKitchenDao;

    public  int delete(Wrapper<SApplyOpenTopKitchen> wrapper) {
       return applyOpenTopKitchenDao.delete(wrapper);
    }

    public List<SApplyOpenTopKitchen> queryByParams(SApplyOpenTopKitchen topKitchen){
       return applyOpenTopKitchenDao.queryByParams(topKitchen);
    }
}
