package com.fotile.activiti.workflow.business.service.terminal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.activiti.workflow.business.constant.BusinessConstant;
import com.fotile.activiti.workflow.business.constant.WorkflowEnum;
import com.fotile.activiti.workflow.business.dao.WholeUsableAreaLogDao;
import com.fotile.activiti.workflow.business.pojo.dto.wholeUsableAreaLog.WholeUsableAreaLogDTO;
import com.fotile.activiti.workflow.business.pojo.entity.*;
import com.fotile.activiti.workflow.business.pojo.interfaces.WholeUsableAreaLogAttribute;
import com.fotile.activiti.workflow.business.pojo.vo.WholeUsableAreaLogVO;
import com.fotile.activiti.workflow.business.service.OpenPlanRelationStoreService;
import com.fotile.activiti.workflow.client.OrgClient;
import com.fotile.activiti.workflow.client.UserClient;
import com.fotile.activiti.workflow.client.dto.org.FindStoreByIdOutDto;
import com.fotile.activiti.workflow.client.dto.org.StoreSimpleInfo;
import com.fotile.activiti.workflow.phase.constant.AbnormalStoreState;
import com.fotile.activiti.workflow.user.pojo.UserDTO;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2023/2/27.
 */
@Service
public class WholeUsableAreaLogService extends ServiceImpl<WholeUsableAreaLogDao, WholeUsableAreaLog> {
    @Resource
    private WholeUsableAreaLogDao wholeUsableAreaLogDao;
    @Resource
    @Lazy
    private ApplyTerminalBuildService terminalBuildService;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserClient userClient;
    @Resource
    @Lazy
    private OpenPlanRelationStoreService openPlanRelationStoreService;

    private static final ImmutableSet<Integer> TerminalBuildType = ImmutableSet.of(WorkflowEnum.TerminalBuildNormal.getValue(), WorkflowEnum.TerminalBuildLarge.getValue());


    public void insert(WholeUsableAreaLog wholeUsableAreaLog){
        if(wholeUsableAreaLog != null){
            wholeUsableAreaLog.setLogDate(new Date());
            wholeUsableAreaLogDao.myInsert(wholeUsableAreaLog);
        }
    }


    public void insert(String procInstId){
        if(StringUtils.isNotBlank(procInstId)){
            Long count = this.lambdaQuery().eq(WholeUsableAreaLog::getWfInstanceCode, procInstId)
                    .eq(WholeUsableAreaLog::getIsDeleted, 0)
                    .count();
            if(count == 0){
                ApplyTerminalBuild applyInfo = terminalBuildService.getApplyInfo(null, procInstId, null);
                WholeUsableAreaLog wholeUsableAreaLog = new WholeUsableAreaLog();
                if(WorkflowEnum.TerminalBuildNormal.getValue().equals(applyInfo.getType())  ){
                    ApplyTerminalBuildNormal applyNormalInfo = terminalBuildService.getApplyNormalInfo(applyInfo.getId(),null);
                    wholeUsableAreaLog.setWholeUsableArea(applyNormalInfo.getWholeUsableArea());
                }else if(WorkflowEnum.TerminalBuildLarge.getValue().equals(applyInfo.getType())){
                    ApplyTerminalBuildLarge applyLargeInfo = terminalBuildService.getApplyLargeInfo(applyInfo.getId(), null);
                    wholeUsableAreaLog.setWholeUsableArea(applyLargeInfo.getWholeUsableArea());
                }else{
                    //其他类型不进表
                    return;
                }
                wholeUsableAreaLog.setWfInstanceCode(procInstId);
                wholeUsableAreaLog.setCompanyId(applyInfo.getCompanyId());
                wholeUsableAreaLog.setCompanyName(applyInfo.getCompanyName());
                wholeUsableAreaLog.setStoreCode(applyInfo.getStoreCode());
                wholeUsableAreaLog.setStoreName(applyInfo.getStoreName());
                wholeUsableAreaLog.setCreatedDate(applyInfo.getCreatedDate());
                wholeUsableAreaLog.setCreatedUserName(applyInfo.getCreatedUserName());
                wholeUsableAreaLog.setLogType(applyInfo.getType());
                wholeUsableAreaLogDao.myInsert(wholeUsableAreaLog);
            }
        }
    }

    public void mySaveOrUpdateByProcInstId(String procInstId){
        List<WholeUsableAreaLog> list = this.lambdaQuery().eq(WholeUsableAreaLog::getWfInstanceCode, procInstId)
                .eq(WholeUsableAreaLog::getIsDeleted, 0)
                .list();
        if(list!=null&&list.size()>0) {//更新
            WholeUsableAreaLog wholeUsableAreaLog = list.get(0);
            ApplyTerminalBuild applyInfo = terminalBuildService.getApplyInfo(null, procInstId, null);
            if (WorkflowEnum.TerminalBuildNormal.getValue().equals(applyInfo.getType())) {
                ApplyTerminalBuildNormal applyNormalInfo = terminalBuildService.getApplyNormalInfo(applyInfo.getId(), null);
                wholeUsableAreaLog.setWholeUsableArea(applyNormalInfo.getWholeUsableArea());
            } else if (WorkflowEnum.TerminalBuildLarge.getValue().equals(applyInfo.getType())) {
                ApplyTerminalBuildLarge applyLargeInfo = terminalBuildService.getApplyLargeInfo(applyInfo.getId(), null);
                wholeUsableAreaLog.setWholeUsableArea(applyLargeInfo.getWholeUsableArea());
            }
            this.updateById(wholeUsableAreaLog);
        }else{
            //新增
            insert(procInstId);
        }
    }

    public void insertList(List<StoreSimpleInfo> stores){
        if(stores!=null&&stores.size()>0){
            //这里没有判断是否应该进表 现在规划只有家装和社区店、专卖店，这里也不管了，都进，不然后面维护麻烦
            List<String> userIds = stores.stream().map(StoreSimpleInfo::getCreatedBy).distinct().collect(Collectors.toList());
            Result<List<UserDTO>> userResult = userClient.getUserByUserEntityIds(userIds);
            HashMap<String, String> userMap = Maps.newHashMap();
            LinkedList<WholeUsableAreaLog> addList= Lists.newLinkedList();
            if(userResult.getSuccess() && userResult.getData()!=null){
                userMap = userResult.getData().stream().collect(Collectors.toMap(UserDTO::getUserEntityId, UserDTO::getFirstName, (k1, k2) -> k1, HashMap::new));
            }
            Date now = new Date();
            for(StoreSimpleInfo store:stores){
                FindStoreByIdOutDto storeDto = orgClient.findStoreById(store.getId().intValue()).getData();
                if(storeDto!=null){
                    WholeUsableAreaLog wholeUsableAreaLog = new WholeUsableAreaLog();
                    wholeUsableAreaLog.setLogType(1);
                    wholeUsableAreaLog.setWholeUsableArea(store.getWholeUsableArea());
                    wholeUsableAreaLog.setStoreCode(storeDto.getCode());
                    wholeUsableAreaLog.setStoreName(storeDto.getName());
                    wholeUsableAreaLog.setCompanyId(storeDto.getCompanyPkId().intValue());
                    wholeUsableAreaLog.setCompanyName(storeDto.getCompanyName());
                    wholeUsableAreaLog.setCreatedDate(store.getCreatedDate());
                    wholeUsableAreaLog.setStatus(-1);
                    wholeUsableAreaLog.setLogDate(now);
                    wholeUsableAreaLog.setCreatedUserName(userMap.get(store.getCreatedBy()));
                    addList.add(wholeUsableAreaLog);
                }
            }
            this.saveBatch(addList);
        }
    }

    public PageInfo<WholeUsableAreaLogVO> page(WholeUsableAreaLogDTO dto) {
        PageInfo<WholeUsableAreaLogVO> page = new PageInfo<>(dto.getPage(), dto.getSize());
        if (dto.getPlanId() != null) {
            //查出关联门店编码
            List<String> storeCodes = openPlanRelationStoreService.lambdaQuery()
                    .in(OpenPlanRelationStore::getPlanId, dto.getPlanId())
                    .eq(OpenPlanRelationStore::getIsDeleted, 0)
                    .select(OpenPlanRelationStore::getStoreCode)
                    .list().stream()
                    .map(OpenPlanRelationStore::getStoreCode)
                    .collect(Collectors.toList());
            dto.setStoreCodes(storeCodes);
            Integer count = wholeUsableAreaLogDao.count(dto);
            page.setTotal(count);
            if (count > 0) {
                List<WholeUsableAreaLogVO> list = wholeUsableAreaLogDao.page(dto, page);
                setField(list);
                page.setRecords(list);
            }
        }
        return page;
    }

    public void setField(List<? extends WholeUsableAreaLogAttribute> list){
        if(list!=null && list.size()>0){
            for (WholeUsableAreaLogAttribute wholeUsableAreaLogVO : list) {
                switch (wholeUsableAreaLogVO.getLogType()) {
                    case 1:
                        wholeUsableAreaLogVO.setLogTypeName("门店运营反馈");
                        break;
                    case 2:
                        wholeUsableAreaLogVO.setLogTypeName(wholeUsableAreaLogVO.getCreatedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().getYear() + "年面积备份");
                        break;
                    case 3:
                        wholeUsableAreaLogVO.setLogTypeName("常规门店申请");
                        break;
                    case 4:
                        wholeUsableAreaLogVO.setLogTypeName("大型门店申请");
                        break;
                    default:
                        break;
                }

                if(TerminalBuildType.contains(wholeUsableAreaLogVO.getLogType())){
                    wholeUsableAreaLogVO.setStatusName(BusinessConstant.getApplyStatusName(wholeUsableAreaLogVO.getStatus()));
                    if (Integer.valueOf(1).equals(wholeUsableAreaLogVO.getDecorateType())) {
                        wholeUsableAreaLogVO.setBudgetCodeName("新装");
                    } else if (Integer.valueOf(2).equals(wholeUsableAreaLogVO.getDecorateType())) {
                        wholeUsableAreaLogVO.setBudgetCodeName("重装");
                    }
                }else{
                    if(Integer.valueOf(-1).equals(wholeUsableAreaLogVO.getStatus())){
                        wholeUsableAreaLogVO.setStatusName(AbnormalStoreState.COMPLETED.getName());
                    }
                }
            }
        }
    }

    public void deleteStorePlanMapping(List<String> storeCodes){
        if(storeCodes!=null&&storeCodes.size()>0){
            this.lambdaUpdate()
                    .eq(WholeUsableAreaLog::getStoreCode,storeCodes)
                    .ne(WholeUsableAreaLog::getLogType,2)
                    .set(WholeUsableAreaLog::getPlanId,null)
                    .update();
        }
    }

    public void deleteStorePlanMappingByPlanId(Long planId){
        if(planId!=null){
            this.lambdaUpdate()
                    .eq(WholeUsableAreaLog::getPlanId,planId)
                    .ne(WholeUsableAreaLog::getLogType,2)
                    .set(WholeUsableAreaLog::getPlanId,null)
                    .update();
        }
    }

    public void addStorePlanMapping(List<String> storeCodes,Long planId){
        if(storeCodes!=null && !storeCodes.isEmpty()){
            this.lambdaUpdate()
                    .eq(WholeUsableAreaLog::getStoreCode,storeCodes)
                    .isNull(WholeUsableAreaLog::getPlanId)
                    .ne(WholeUsableAreaLog::getLogType,2)
                    .set(WholeUsableAreaLog::getPlanId,planId)
                    .update();
        }
    }

}
