package com.fotile.activiti.workflow.business.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.entity
 * @date 2021/1/12 16:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "log_op",schema = "workflowcenter")
public class OpLog implements Serializable {
    /**
     * 日志Id,PK,AI
     */
    @TableId
    private Long id;

    /**
     * 关联库表名称
     */
    @TableField("ref_table")
    private String refTable;

    /**
     * 关联类型
     */
    @TableField("ref_type")
    private Integer refType;

    /**
     * 关联Id
     */
    @TableField("ref_id")
    private Integer refId;

    /**
     * 操作类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 操作类型名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 老数据（原数据）
     */
    @TableField("old_data")
    private String oldData;

    /**
     * 新数据
     */
    @TableField("new_data")
    private String newData;

    /**
     * 描述内容
     */
    @TableField("remark")
    private String remark;

    /**
     * 附件地址(attachment_url)
     */
    @TableField("remark")
    private String url;

    /**
     * 操作人名称
     */
    @FieldEncrypt
    @TableField("operator_name")
    private String operatorName;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 逻辑删除标识
     */
    @TableField("is_deleted")
    private Integer isDeleted=0;

}
