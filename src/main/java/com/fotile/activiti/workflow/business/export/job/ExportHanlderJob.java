package com.fotile.activiti.workflow.business.export.job;

import com.fotile.activiti.workflow.business.constant.ExportTypeClassEnum;
import com.fotile.activiti.workflow.business.export.context.ExportContext;
import com.fotile.activiti.workflow.business.export.dto.GetExportTaskRecordInDTO;
import com.fotile.activiti.workflow.client.DataClient;
import com.fotile.activiti.workflow.client.dto.ExportTaskRecord;
import com.fotile.framework.web.Result;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2022/3/7.
 */
@Component
@Slf4j
public class ExportHanlderJob {
    @Autowired
   private DataClient dataClient;
    @Autowired
    private ExportContext exportContext;

    @XxlJob(value = "TaskExport")
    public ReturnT<String> TaskExport(String s){
        //补偿机制
        List<Long> taskIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(s)) {
            taskIds = Arrays.stream(s.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        //查询任务列表
        //先获取是否已经进行中的数据
        GetExportTaskRecordInDTO getExportTaskRecordInDTO = new GetExportTaskRecordInDTO();
        List<String> typeList = new ArrayList<>();
  /*      typeList.add(String.valueOf(ExportTypeClassEnum.OPEN_PLAN_DETAIL_EXPORT.getCode()));
        typeList.add(String.valueOf(ExportTypeClassEnum.OPEN_PLAN_LIST_EXPORT.getCode()));
        typeList.add(String.valueOf(ExportTypeClassEnum.APPLY_STORE_EXPOET.getCode()));
        typeList.add(String.valueOf(ExportTypeClassEnum.TERMINAL_LAYOUT_EXPORT.getCode()));
        //上市操盘列表
        typeList.add(String.valueOf(ExportTypeClassEnum.OP_PLAN_EXPORT.getCode()));
        //总部-目标分解
        typeList.add(String.valueOf(ExportTypeClassEnum.OP_TARGET_EXPORT.getCode()));
        //大区汇总-PCT成员
        typeList.add(String.valueOf(ExportTypeClassEnum.REGIONSUMMARY_PCT_EXPORT.getCode()));
        //大区汇总-里程碑
        typeList.add(String.valueOf(ExportTypeClassEnum.REGIONSUMMARY_MILESTON_EXPORT.getCode()));
        //大区汇总-销售目标分解
        typeList.add(String.valueOf(ExportTypeClassEnum.REGIONSUMMARY_TARGET_EXPORT.getCode()));
        //大区操盘-PCT成员
        typeList.add(String.valueOf(ExportTypeClassEnum.REGION_PCT_EXPORT.getCode()));
        //大区操盘-里程碑
        typeList.add(String.valueOf(ExportTypeClassEnum.REGION_MILESTON_EXPORT.getCode()));
        //大区操盘-销售目标分解
        typeList.add(String.valueOf(ExportTypeClassEnum.REGION_TARGET_EXPORT.getCode()));
        //实际上市结果
        typeList.add(String.valueOf(ExportTypeClassEnum.ABNORMAL_STORE_EXPORT.getCode()));
        //异常门店
        typeList.add(String.valueOf(ExportTypeClassEnum.STORE_RESULT_EXPORT.getCode()));*/
        for (ExportTypeClassEnum value : ExportTypeClassEnum.values()) {
            typeList.add(String.valueOf(value.getCode()));
        }

        getExportTaskRecordInDTO.setTypeList(typeList);
        getExportTaskRecordInDTO.setStatus(1);
        Result<List<ExportTaskRecord>> taskByCode = dataClient.getTaskByCode(getExportTaskRecordInDTO);
        if (taskByCode != null && taskByCode.getData() != null && taskByCode.getData().size() > 0) {
            return new ReturnT<>("暂时有任务正在进行中请稍等");
        }

        // 发现未执行的数据
        GetExportTaskRecordInDTO getExportTaskRecordInDTONo = new GetExportTaskRecordInDTO();
        getExportTaskRecordInDTONo.setTypeList(typeList);
        getExportTaskRecordInDTONo.setStatus(0);
        if (taskIds != null && taskIds.size() > 0) {
            getExportTaskRecordInDTONo.setIds(taskIds);
        }
        Result<List<ExportTaskRecord>> result = dataClient.getTaskByCode(getExportTaskRecordInDTONo);
        if (result == null) {
            return new ReturnT<>("暂无可执行的任务");
        }
        List<ExportTaskRecord> exportTaskRecords = result.getData();
        if (exportTaskRecords == null || exportTaskRecords.size() == 0) {
            return new ReturnT<>("暂无数据");
        }

        exportTaskRecords.forEach(exportTaskRecord -> {
           // StoreOpenPlanDto params = JSONObject.parseObject(exportTaskRecord.getParamJson(), StoreOpenPlanDto.class);
            ExportTaskRecord startExportTaskRecord = new ExportTaskRecord();
           // params.setTaskId(exportTaskRecord.getId());
            startExportTaskRecord.setId(exportTaskRecord.getId());
         //   params.setExportCode(Integer.valueOf(exportTaskRecord.getType()));
            startExportTaskRecord.setStartTime(new Date());
            startExportTaskRecord.setStatus(1);
            dataClient.startTask(startExportTaskRecord);
            exportContext.executeExportTask(exportTaskRecord);

        });

        //分批处理

        return new ReturnT<>("成功");

    }
}
