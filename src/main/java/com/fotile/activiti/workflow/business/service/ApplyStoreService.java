package com.fotile.activiti.workflow.business.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.activiti.workflow.activiti.pojo.DTO.TaskDTO;
import com.fotile.activiti.workflow.activiti.service.FotileActivitiService;
import com.fotile.activiti.workflow.activiti.service.FotileTaskService;
import com.fotile.activiti.workflow.activiti.service.StoreActivitiService;
import com.fotile.activiti.workflow.business.constant.*;
import com.fotile.activiti.workflow.business.dao.ApplyStoreDao;
import com.fotile.activiti.workflow.business.export.dto.applyStore.ApplyStoreQuery;
import com.fotile.activiti.workflow.business.export.dto.applyStore.ExportApplyStoreDto;
import com.fotile.activiti.workflow.business.pojo.dto.*;
import com.fotile.activiti.workflow.business.pojo.dto.openexplosive.StartOpenExplosiveProcess;
import com.fotile.activiti.workflow.business.pojo.dto.out.ApplyStoreDetails;
import com.fotile.activiti.workflow.business.pojo.dto.out.CloseInfoEditResult;
import com.fotile.activiti.workflow.business.pojo.dto.store.*;
import com.fotile.activiti.workflow.business.pojo.entity.*;
import com.fotile.activiti.workflow.business.service.openExplosive.ApplyStoreOpenExplosiveMemberService;
import com.fotile.activiti.workflow.business.service.openExplosive.OpenExplosiveService;
import com.fotile.activiti.workflow.business.service.openExplosive.StoreOpenExplosiveMilestoneService;
import com.fotile.activiti.workflow.business.service.openExplosive.StoreOpenExplosiveStatsService;
import com.fotile.activiti.workflow.business.utils.CommonUtils;
import com.fotile.activiti.workflow.client.OmsClient;
import com.fotile.activiti.workflow.client.OrgClient;
import com.fotile.activiti.workflow.client.ProductClient;
import com.fotile.activiti.workflow.client.SystemClient;
import com.fotile.activiti.workflow.client.dto.*;
import com.fotile.activiti.workflow.client.dto.org.ChannelCategoryEntity;
import com.fotile.activiti.workflow.client.dto.org.FindStoreByIdOutDto;
import com.fotile.activiti.workflow.client.dto.org.FindStoreDto;
import com.fotile.activiti.workflow.client.dto.org.StoreSimpleInfo;
import com.fotile.activiti.workflow.client.dto.org.UpdateStoreStatusDto;
import com.fotile.activiti.workflow.client.dto.system.DicDto;
import com.fotile.activiti.workflow.dict.pojo.Dict;
import com.fotile.activiti.workflow.dict.pojo.DictDto;
import com.fotile.activiti.workflow.dict.pojo.DictMapper;
import com.fotile.activiti.workflow.dict.service.DictService;
import com.fotile.activiti.workflow.peripheral.service.PeripheralServcie;
import com.fotile.activiti.workflow.phase.constant.StoreChangeField;
import com.fotile.activiti.workflow.phase.pojo.dto.change.ChangeVO;
import com.fotile.activiti.workflow.phase.pojo.dto.redecorate.RedecorateVO;
import com.fotile.activiti.workflow.phase.pojo.dto.reopen.ReopenVO;
import com.fotile.activiti.workflow.phase.pojo.entity.change.ApplyChange;
import com.fotile.activiti.workflow.phase.pojo.mapper.ChangeMapper;
import com.fotile.activiti.workflow.phase.service.ChangeService;
import com.fotile.activiti.workflow.phase.service.RedecorateService;
import com.fotile.activiti.workflow.phase.service.ReopenService;
import com.fotile.activiti.workflow.user.pojo.UserDTO;
import com.fotile.activiti.workflow.user.pojo.entity.UserRoleMapping;
import com.fotile.activiti.workflow.user.service.UserService;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.task.Task;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.service
 * @date 2021/1/7 17:55
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class ApplyStoreService extends ServiceImpl<ApplyStoreDao, ApplyStore> {
    @Value("#{'${workflow.reviceSyncMDMFailEmail}'.split(',')}")
    private Set<String> receiveSyncMDMFailEmail;
    @Autowired
    private ApplyStoreDao applyStoreDao;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private LogService logService;
    @Autowired
    private AttachmentService attachmentService;
    @Autowired
    private StoreActivitiService storeActivitiService;
    @Autowired
    private OrgClient orgClient;
    @Resource
    private ApplyStoreTopKitchenService applyStoreTopKitchenService;
    @Resource
    @Lazy
    private StoreOpenPlanService openPlanService;
    @Resource
    private OmsClient omsClient;
    @Resource
    private ProductClient productClient;
    @Resource
    private HistoryService historyService;
    @Resource
    private SystemClient systemClient;
    @Resource
    private DictService dictService;
    @Resource
    @Lazy
    private ReopenService reopenService;
    @Lazy
    @Resource
    private RedecorateService redecorateService;
    @Lazy
    @Resource
    private ChangeService changeService;
    @Resource
    private UserService userService;
    @Resource
    @Lazy
    private ApplyStoreOpenExplosiveMemberService openExplosiveMemberService;
    @Resource
    @Lazy
    private OpenExplosiveService explosiveService;
    @Resource
    @Lazy
    private StoreOpenExplosiveStatsService openExplosiveStatsService;
    @Resource
    @Lazy
    private StoreOpenExplosiveMilestoneService explosiveMilestoneService;
    @Resource
    private ApplyOpenStrategyService applyOpenStrategyService;
    @Resource
    private PeripheralServcie peripheralServcie;
    @Resource
    private MdmSyncLogService mdmSyncLogService;
    @Resource
    private EmailService emailService;
    @Resource
    private FotileActivitiService fotileActivitiService;
    @Resource
    private FotileTaskService fotileTaskService;



    private static final SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final String DISABLE_STORE_NAME = "(禁用)";
    private static final String CLEAR_STORE_NAME = "(清退)";
    private static List<String> acceptTaskKeys = Arrays.asList("normal-check175,large-check175".split(","));

    /************************ 申请流程 ************************/

    /**
     * 获取门店申请总记录数
     */
    public Integer getApplyInfoCount(ApplyInfoQuery query) {
        if (query == null)
            return 0;

        //分公司数据隔离
        query.setDataScopeCompany(CommonUtils.getUserCompanyDataScope(userAuthorConfig, "id"));

        return applyStoreDao.getApplyInfoCount(query);
    }

    /**
     * 获取门店申请分页数据
     */
    public PageInfo<ApplyStore> getApplyInfoList(ApplyInfoQuery query) {
        if (query == null)
            return null;

        //分公司数据隔离
        query.setDataScopeCompany(CommonUtils.getUserCompanyDataScope(userAuthorConfig, "id"));

        int total = applyStoreDao.getApplyInfoCount(query);
        if (total <= 0)
            return null;

        PageInfo<ApplyStore> pageInfo = new PageInfo<>(query.getPage(), query.getSize(), total);
        pageInfo.setRecords(getApplyInfoList(query, pageInfo));

/*        Set<String> noNameExplosiveAssigneeSet = pageInfo.getRecords().stream()
                .filter(a -> WorkflowEnum.StoreOpenExplosive.getValue().equals(a.getApplyType()) && StringUtils.isBlank(a.getWfCurrentAssigneeName()))
                .map(ApplyStore::getWfCurrentAssignee)
                .collect(Collectors.toSet());
        Map<String, String> assigneeNameMap = Maps.newHashMap();
        if (!noNameExplosiveAssigneeSet.isEmpty()) {
            assigneeNameMap = explosiveMilestoneService.lambdaQuery()
                    .in(StoreOpenExplosiveMilestone::getChargerJobNum, noNameExplosiveAssigneeSet)
                    .eq(StoreOpenExplosiveMilestone::getIsDeleted, 0)
                    .select(StoreOpenExplosiveMilestone::getChargerJobNum, StoreOpenExplosiveMilestone::getChargerName)
                    .groupBy(StoreOpenExplosiveMilestone::getChargerJobNum)
                    .list().stream().collect(Collectors.toMap(StoreOpenExplosiveMilestone::getChargerJobNum, StoreOpenExplosiveMilestone::getChargerName));
        }

        //填充渠道名称
        Result<List<ChannelCategoryEntity>> storeChannels = orgClient.findChannelByTypeCode(
                null, "store_type", null, null, null);
        if (storeChannels != null && storeChannels.getData() != null && storeChannels.getData().size() > 0) {
            List<ChannelCategoryEntity> channels = storeChannels.getData();
            Map<String, String> finalAssigneeNameMap = assigneeNameMap;
            pageInfo.getRecords().forEach(x -> {
                if (WorkflowEnum.StoreOpenExplosive.getValue().equals(x.getApplyType()) && StringUtils.isBlank(x.getWfCurrentAssigneeName())) {
                    x.setWfCurrentAssigneeName(finalAssigneeNameMap.get(x.getWfCurrentAssignee()));
                }
                ChannelCategoryEntity channel = channels.stream().filter(s -> (s.getId() + "").equals(x.getStoreChannelId() + "") || (StringUtils.isNotBlank(x.getStoreChannelCode()) && s.getCode().equalsIgnoreCase(x.getStoreChannelCode()))).findFirst().orElse(null);
                if (channel != null) {
                    x.setStoreChannelName(channel.getName());
                }
            });
        }*/

        return pageInfo;
    }

    public List<ApplyStore> getApplyInfoList(ApplyInfoQuery query,PageInfo<ApplyStore> pageInfo){
        if(query==null || pageInfo==null){
            return Lists.newArrayListWithExpectedSize(0);
        }
        List<ApplyStore> applyInfoList = applyStoreDao.getApplyInfoList(query, pageInfo);
        if(CollectionUtils.isNotEmpty(applyInfoList)){
            Set<String> noNameExplosiveAssigneeSet =Sets.newHashSet();
            Set<String> assigneeSet =Sets.newHashSet();
            Set<String> explosiveProcinstIdSet =Sets.newHashSet();
            for (ApplyStore applyStore : applyInfoList) {
                if(WorkflowEnum.StoreOpenExplosive.getValue().equals(applyStore.getApplyType())){
                    explosiveProcinstIdSet.add(applyStore.getWfInstanceCode());
                    noNameExplosiveAssigneeSet.add(applyStore.getWfCurrentAssignee());
                }
                if(WorkflowEnum.ChangeStore.getValue().equals(applyStore.getApplyType())){
                    continue;
                }
                if(StringUtils.isBlank(applyStore.getWfCurrentAssignee())){
                    continue;
                }

                assigneeSet.add(applyStore.getWfCurrentAssignee());

            }
            Map<String, HistoricProcessInstance> procInstIdMap = Optional.of(explosiveProcinstIdSet)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(s -> historyService.createHistoricProcessInstanceQuery().processInstanceIds(s).list())
                    .filter(CollectionUtils::isNotEmpty)
                    .map(l -> l.stream().collect(Collectors.toMap(HistoricProcessInstance::getId, Function.identity())))
                    .orElse(Maps.newHashMapWithExpectedSize(0));


            Map<String, String> userMap =Maps.newHashMap();
            if(!assigneeSet.isEmpty()){
                userMap.putAll(userService.lambdaQuery()
                        .eq(UserRoleMapping::getIsDeleted, 0)
                        .in(UserRoleMapping::getNumber, assigneeSet)
                        .groupBy(UserRoleMapping::getNumber)
                        .list().stream().collect(Collectors.toMap(UserRoleMapping::getNumber, UserRoleMapping::getFirstName)));
            }

            final Map<String, String> openExplosiveMembers =Maps.newHashMap();
            if(!noNameExplosiveAssigneeSet.isEmpty()){
                List<String> onlyInOpenExplosive = noNameExplosiveAssigneeSet.stream().filter(((Predicate<String>) userMap::containsKey).negate()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(onlyInOpenExplosive)){
                    openExplosiveMembers.putAll(explosiveMilestoneService.lambdaQuery()
                            .in(StoreOpenExplosiveMilestone::getChargerJobNum, noNameExplosiveAssigneeSet)
                            .eq(StoreOpenExplosiveMilestone::getIsDeleted, 0)
                            .select(StoreOpenExplosiveMilestone::getChargerJobNum, StoreOpenExplosiveMilestone::getChargerName)
                            .groupBy(StoreOpenExplosiveMilestone::getChargerJobNum)
                            .list().stream().collect(Collectors.toMap(StoreOpenExplosiveMilestone::getChargerJobNum, StoreOpenExplosiveMilestone::getChargerName))) ;
                }
            }
            //填充渠道名称
            Optional<List<ChannelCategoryEntity>> channelOptional = Optional.ofNullable(orgClient.findChannelByTypeCode(
                    null, "store_type", null, null, null).getData());
            applyInfoList.forEach(x -> {
                if(userMap.containsKey(x.getWfCurrentAssignee())){
                    x.setWfCurrentAssigneeName(userMap.get(x.getWfCurrentAssignee()));
                }else {
                    if(WorkflowEnum.StoreOpenExplosive.getValue().equals(x.getApplyType())){
                        if(openExplosiveMembers.containsKey(x.getWfCurrentAssignee())){
                          x.setWfCurrentAssigneeName(openExplosiveMembers.get(x.getWfCurrentAssignee()));
                        }
                    }
                }
                Optional.ofNullable(x.getWfInstanceCode())
                        .filter(procInstIdMap::containsKey)
                        .map(procInstIdMap::get)
                        .map(HistoricProcessInstance::getProcessDefinitionVersion)
                        .map(StoreOpenExplosiveConstant::getHtmlVersion)
                        .ifPresent(x::setHtmlVersion);
                ChannelCategoryEntity channel = channelOptional.orElse(Lists.newArrayList()).stream().filter(s -> (s.getId() + "").equals(x.getStoreChannelId() + "") || (StringUtils.isNotBlank(x.getStoreChannelCode()) && s.getCode().equalsIgnoreCase(x.getStoreChannelCode()))).findFirst().orElse(null);
                if (channel != null) {
                    x.setStoreChannelName(channel.getName());
                }
            });
        }
        return applyInfoList;
    }

    /**
     * 获取门店申请信息(s_apply)
     */
    public ApplyStore getApplyInfo(Integer applyId, String processInstanceId, String processTaskId) {
        return applyStoreDao.getApplyInfo(applyId, processInstanceId, processTaskId);
    }

    /**
     * 添加申请信息(s_apply)
     *
     * @return 新增成功则返回记录Id
     */
    public Integer addApplyInfo(@Valid ApplyInfo applyInfo) {
        if (applyInfo == null)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        applyInfo.setOperatorId(user.get("userid"));
        applyInfo.setOperatorName(user.get("name"));

        //执行新增操作
        applyStoreDao.addApplyInfo(applyInfo);
        return applyInfo.getId();
    }

    /**
     * 编辑申请信息(s_apply)
     */
    public Integer editApplyInfo(@Valid ApplyInfo applyInfo) {
        if (applyInfo == null || applyInfo.getId() == null || applyInfo.getId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        ApplyStore originalApply = this.getById(applyInfo.getId());
        if(Objects.nonNull(originalApply)&& !ApplyStatusEnum.isWrongEditStatus(originalApply.getApplyStatus(),applyInfo.getApplyStatus())){
            throw new BusinessException("流程已流转至最终状态，不可逆转！");
        }


        applyInfo.setOperatorId(user.get("userid"));
        applyInfo.setOperatorName(user.get("name"));
        return applyStoreDao.editApplyInfo(applyInfo);
    }

    /**
     * 修改门店申请状态(0:待提交,1:处理中,2:已结束,3:已取消)
     */
    @GlobalTransactional
    public Integer editApplyInfoStatus(@Valid ApplyInfoStatusEdit edit) {
        if (edit == null || (edit.getApplyId() == null && StringUtils.isBlank(edit.getProcessInstanceId()))) {
            throw new BusinessException("参数错误！");
        }

        ApplyStoreDetails applyInfo = this.getApplyStoreDetails(edit.getApplyId(), edit.getProcessInstanceId(), null, true, false);
        if (applyInfo == null || applyInfo.getApplyInfo() == null) {
            throw new BusinessException("申请信息不存在！");
        }

        ApplyStoreOpen openInfo = applyInfo.getOpenInfo();
        if (applyInfo.getApplyInfo().getApplyType().equals(WorkflowEnum.OpenStore.getValue())) {
            if (openInfo == null) {
                throw new BusinessException("开店申请信息不存在！");
            }

            ApplyStore applyStore = applyInfo.getApplyInfo();
            if (Integer.valueOf(1).equals(edit.getApplyStatus()) && Integer.valueOf(0).equals(applyStore.getApplyStatus())) {
                if (openInfo.getUsableArea() != null && openInfo.getUsableArea().compareTo(new BigDecimal(200)) >= 0
                        && ChannelCategoryEnum.SC_LB_LB002_L025.getCode().equals(applyStore.getStoreChannelCode())
                ) {
                    Date parse = null;
                    try {
                        parse = df.parse("2023-09-20 23:59:59");
                    } catch (Exception e) {
                        log.error("时间转换异常", e);
                    }
                    if (parse != null && applyStore.getCreatedDate() != null && applyStore.getCreatedDate().after(parse)) {
                        List<ApplyStoreOpenExplosiveMember> explosiveMemberList = applyInfo.getExplosiveMemberList();
                        if (CollectionUtils.isEmpty(explosiveMemberList)) {
                            throw new BusinessException("开业爆量人员不可为空");
                        }
                    }
                }
            }
        }

        ApplyStoreClose closeInfo = applyInfo.getCloseInfo();
        if (applyInfo.getApplyInfo().getApplyType().equals(WorkflowEnum.CloseStore.getValue()) && closeInfo == null) {
            throw new BusinessException("闭店申请信息不存在！");
        }

        ReopenVO reopenVO = applyInfo.getReopenVO();
        if (WorkflowEnum.ReopenStore.getValue().equals(applyInfo.getApplyInfo().getApplyType()) && reopenVO == null) {
            throw new BusinessException("重启申请信息不存在");
        }

        RedecorateVO redecorateVO = applyInfo.getRedecorateVO();
        if (WorkflowEnum.RedecorateStore.getValue().equals(applyInfo.getApplyInfo().getApplyType()) && redecorateVO == null) {
            throw new BusinessException("重装信息不存在");
        }

        List<ChangeVO> changeVOList = applyInfo.getChangeVOList();
        if (WorkflowEnum.ChangeStore.getValue().equals(applyInfo.getApplyInfo().getApplyType())
                && (changeVOList == null || changeVOList.isEmpty())
                && (Integer.valueOf(1).equals(edit.getApplyStatus()))) { //如果是申请审批流程的话，审核内容不可为空
            throw new BusinessException("门店变更信息不存在");
        }

        if (edit.getApplyStatus().equals(applyInfo.getApplyInfo().getApplyStatus())) {
            throw new BusinessException("当前状态未发生变化，请勿重复操作！");
        }

        //参数容错
        if (edit.getApplyId() == null || edit.getApplyId() <= 0) {
            edit.setApplyId(applyInfo.getApplyInfo().getId());
        }

        //变更状态
        Integer status = edit.getApplyStatus();
        //当前状态
        switch (applyInfo.getApplyInfo().getApplyStatus()) {
            case 0: //待提交
                if (status != 1 && status != 3) {
                    throw new BusinessException("错误的变更状态！");
                }
            case 1: //处理中
                break; //可以变更成任何状态
            case 2: //已结束
            case 3: //已取消
                throw new BusinessException("无法调整当前状态！");
            default:
                throw new BusinessException("错误的申请状态！");
        }

        OpLogEnum oe = OpLogEnum.NULL;
        WorkflowEnum we = WorkflowEnum.NULL;
        HashMap<String, Object> params = null;

        int distributorType = 3;//客户渠道(1:KA,2:经销商,3:其他,4:米博/电商)
        if (applyInfo.getApplyInfo().getApplyType().equals(WorkflowEnum.OpenStore.getValue())) {//开店
            if (status == 3) {
              /*  if(StringUtils.isNotBlank(applyInfo.getApplyInfo().getStoreCode())){
                    throw new BusinessException("门店已生成，无法取消流程！");
                }*/
                oe = OpLogEnum.StoreOpenCancelApply;
            } else if (status == 2) {
                oe = OpLogEnum.StoreOpenEndApply;
            } else {
                oe = OpLogEnum.StoreOpenUpdateApply;
            }
            we = WorkflowEnum.OpenStore;
            //工作流参数判定
            int bpm =0;
            if(StringUtils.isNotBlank(applyInfo.getApplyInfo().getWfInstanceCode())){
                HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(applyInfo.getApplyInfo().getWfInstanceCode()).singleResult();
                Integer procDefVersion = Optional.ofNullable(processInstance)
                        .map(HistoricProcessInstance::getProcessDefinitionVersion)
                        .orElse(null);
                bpm = BusinessConstant.getOpenRealBpmType(applyInfo.getApplyInfo().getStoreType(), applyInfo.getApplyInfo().getBpmType(), applyInfo.getApplyInfo().getDistributorChannelCode(),procDefVersion);
            }else{
                bpm = BusinessConstant.getOpenRealBpmType(applyInfo.getApplyInfo().getStoreType(), applyInfo.getApplyInfo().getBpmType(), applyInfo.getApplyInfo().getDistributorChannelCode(),null);
            }

            if (bpm <= 0) {
                throw new BusinessException("流程参数错误，操作失败！");
            }

            ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
            //门店类型(0:其他,1:专卖店,2:社区服务店)
            int storeChannelType = 0;
            if (cce.isSpecialStoreChannel(applyInfo.getApplyInfo().getStoreChannelCode())) {
                storeChannelType = 1;
            }
            params = BusinessConstant.getOpenBpmParams(
                    null,
                    applyInfo.getApplyInfo(),
                    applyInfo.getApplyInfo().getId(),
                    applyInfo.getApplyInfo().getStoreType(),
                    bpm,
                    openInfo.getUsableArea(),
                    openInfo.getNeedTerminalBuild(),
                    storeChannelType
            );
            //开店申请状态变更为待提交，清空功能配置
            if (status == 0) {
                applyStoreDao.clearFunctionConfig(openInfo.getId());
            }
            if (status == 1 && Integer.valueOf(0).equals(applyInfo.getApplyInfo().getApplyStatus())) {
                if (applyInfo.getExplosiveMemberList() != null && !applyInfo.getExplosiveMemberList().isEmpty()) {
                    StartOpenExplosiveProcess startOpenExplosiveProcess = new StartOpenExplosiveProcess();
                    startOpenExplosiveProcess.setMembers(applyInfo.getExplosiveMemberList());
                    startOpenExplosiveProcess.setOpenStoreApplyId(applyInfo.getApplyInfo().getId());
                    startOpenExplosiveProcess.setCreateIfNotExists(false);
                    explosiveService.syncOpenStoreInfoToExplosive(startOpenExplosiveProcess);
                }
            }
        } else if (applyInfo.getApplyInfo().getApplyType().equals(WorkflowEnum.CloseStore.getValue())) { //闭店
            if (status == 3) {
                oe = OpLogEnum.StoreCloseCancelApply;
            } else if (status == 2) {
                oe = OpLogEnum.StoreCloseEndApply;
            } else {
                oe = OpLogEnum.StoreCloseUpdateApply;
            }
            we = WorkflowEnum.CloseStore;

            ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
            cce = cce.getChannelCategoryEnum(applyInfo.getApplyInfo().getDistributorChannel(), 1);
            if (cce == ChannelCategoryEnum.KA) {
                distributorType = 1;
            } else if (cce == ChannelCategoryEnum.Distributor) {
                distributorType = 2;
            } else if (cce == ChannelCategoryEnum.DecorationStrategicBizCenter || cce == ChannelCategoryEnum.DecorationStrategic) {
                distributorType = 5;
            } else if (cce == ChannelCategoryEnum.MiBo ||
                    cce.isECommerceChannel(applyInfo.getApplyInfo().getDistributorChannel())) {
                distributorType = 4;
            }else if(cce==ChannelCategoryEnum.LF){
                distributorType = 6;
            }
            Integer storeType = applyInfo.getApplyInfo().getStoreType();
            Integer procDefVersion = Optional.ofNullable(applyInfo.getApplyInfo().getWfInstanceCode())
                    .filter(StringUtils::isNotBlank)
                    .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .orElse(null);
            if(Objects.nonNull(procDefVersion)&& procDefVersion<=62){
                params = BusinessConstant.getCloseBpmParams(
                        applyInfo.getApplyInfo(),
                        null,
                        applyInfo.getApplyInfo().getId(),
                        distributorType,
                        applyInfo.getApplyInfo().getDistributorChanged(),
                        cce.needExcessapprove(applyInfo.getApplyInfo().getStoreChannelCode()),
                        applyInfo.getApplyInfo().getStoreChannelCode(),
                        storeType
                );
            }else{
                params = BusinessConstant.getCloseBpmParamsVersion2(
                        applyInfo.getApplyInfo(),
                        null,
                        applyInfo.getApplyInfo().getId(),
                        distributorType,
                        applyInfo.getApplyInfo().getDistributorChanged(),
                        cce.needExcessapprove(applyInfo.getApplyInfo().getStoreChannelCode()),
                        applyInfo.getApplyInfo().getStoreChannelCode(),
                        storeType
                );
            }
        } else if (WorkflowEnum.ReopenStore.getValue().equals(applyInfo.getApplyInfo().getApplyType())) {// 重启
            if (status == 3) {
                oe = OpLogEnum.ReopenCancelApply;
            } else if (status == 2) {
                oe = OpLogEnum.ReopenApplyFinish;
            } else {
                oe = OpLogEnum.ReopenEditApply;
            }
            we = WorkflowEnum.ReopenStore;
            Optional.ofNullable(applyInfo.getApplyInfo().getWfInstanceCode())
                    .filter(StringUtils::isNotBlank)
                    .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .ifPresent(applyInfo.getApplyInfo()::setProcDefVersion);
            params = BusinessConstant.getReopenBpmParams(applyInfo);
        } else if (WorkflowEnum.RedecorateStore.getValue().equals(applyInfo.getApplyInfo().getApplyType())) {//重装
            if (status == 3) {
                oe = OpLogEnum.RedecorateCancelApply;
            } else if (status == 2) {
                oe = OpLogEnum.RedecorateApplyFinish;
            } else {
                oe = OpLogEnum.RedecorateEditApply;
            }
            we = WorkflowEnum.RedecorateStore;
            Integer procDefVersion = Optional.ofNullable(applyInfo.getApplyInfo().getWfInstanceCode())
                    .filter(StringUtils::isNotBlank)
                    .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .orElse(null);
            if(Objects.nonNull(procDefVersion)&& procDefVersion<=10){
                params = BusinessConstant.getRedecorateBpmParams(applyInfo.getApplyInfo().getId(), applyInfo.getApplyInfo().getStoreChannelCode());
            }else {
                FindStoreByIdOutDto storeFromOrg = Optional.ofNullable(applyInfo.getApplyInfo().getStoreId())
                        .map(orgClient::findStoreById)
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .orElse(null);
                params = BusinessConstant.getRedecorateBpmParamsVersion2(applyInfo.getApplyInfo().getId(), applyInfo.getApplyInfo().getStoreChannelCode(),
                        applyInfo.getApplyInfo().getDistributorChannel(),redecorateVO.getDecorateType(),redecorateVO.getNewArea(),
                        Optional.ofNullable(storeFromOrg).map(FindStoreByIdOutDto::getWholeUsableArea).orElse(BigDecimal.ZERO));
            }
        } else if (WorkflowEnum.ChangeStore.getValue().equals(applyInfo.getApplyInfo().getApplyType())) {//门店信息变更
            if (status == 3) {
                oe = OpLogEnum.CancelChangeApply;
            } else if (status == 2) {
                oe = OpLogEnum.ChangeApplyFinish;
            } else {
                oe = OpLogEnum.EditChangeApply;
            }
            we = WorkflowEnum.ChangeStore;
            Integer procDefVersion = Optional.ofNullable(applyInfo.getApplyInfo().getWfInstanceCode())
                    .filter(StringUtils::isNotBlank)
                    .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .orElse(null);
            if(Objects.nonNull(procDefVersion)&& procDefVersion<=24){
                params = changeService.getChangeParams(ChangeMapper.INSTANCE.VOList2EntityList(changeVOList), applyInfo.getApplyInfo());
            }else{
                params = changeService.getChangeParamsVersion2(ChangeMapper.INSTANCE.VOList2EntityList(changeVOList), applyInfo.getApplyInfo());
            }

        } else if (WorkflowEnum.StoreOpenExplosive.getValue().equals(applyInfo.getApplyInfo().getApplyType())) {//开业爆量活动
            if (status == 3) {
                oe = OpLogEnum.OpenExplosiveApplyCancel;
            } else if (status == 2) {
                oe = OpLogEnum.OpenExplosiveApplyFinish;
            } else {
                oe = OpLogEnum.OpenExplosiveApplyEdit;
            }
            we = WorkflowEnum.StoreOpenExplosive;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        if (StringUtils.isBlank(edit.getOperatorId())) {
            edit.setOperatorId(user.get("userid"));
        }
        if (StringUtils.isBlank(edit.getOperatorName())) {
            edit.setOperatorName(user.get("name"));
        }

        //更新状态
        Integer ret = applyStoreDao.editApplyInfoStatus(edit);
        if (ret == null || ret <= 0) {
            throw new BusinessException("操作失败，错误码[10001]！");
        }

        //TODO:如果状态修改为申请中并且状态更新成功，则开启工作流
        if (status == 1) {
            String bpmTitle = applyInfo.getApplyInfo().getCompanyName() +
                    "-" + we.getDescription() +
                    "-" + applyInfo.getApplyInfo().getStoreName() +
                    "-" + applyInfo.getApplyInfo().getId();

            if (!StringUtils.isBlank(applyInfo.getApplyInfo().getWfInstanceCode())) {
                //更新工作流参数
                //storeActivitiService.setProcInstVariables(applyInfo.getApplyInfo().getWfInstanceCode(), params);
                storeActivitiService.finishTaskById(applyInfo.getApplyInfo().getWfCurrentTaskId(), params);
            } else {
                //闭店申请-提交申请->更新门店信息门店状态为“清退”
                if (applyInfo.getApplyInfo().getApplyType().equals(WorkflowEnum.CloseStore.getValue())) {
                    Boolean succeed = this.updateStoreStatus(applyInfo.getApplyInfo().getStoreId(),
                            applyInfo.getApplyInfo().getId().toString(), "闭店申请提交，门店状态调整为：清退！", StoreStatusEnum.Retreat);
                    if (succeed == null || !succeed) {
                        throw new BusinessException("编辑闭店申请失败，错误码[10004]！");
                    }
                }

                storeActivitiService.startProcess(we.getCode(), applyInfo.getApplyInfo().getCompanyId().toString(), bpmTitle, params);
            }
        }

        //TODO:闭店申请取消 恢复门店状态为 启用
        if (oe == OpLogEnum.StoreCloseCancelApply) {
            Boolean succeed = this.updateStoreStatus(applyInfo.getApplyInfo().getStoreId(),
                    applyInfo.getApplyInfo().getWfInstanceCode(), "闭店申请取消，恢复门店状态为：启用！", StoreStatusEnum.Enable);
            if (succeed == null || !succeed) {
                throw new BusinessException("操作失败，错误码[10001]！");
            }
        }

        //开店申请取消，门店状态变更为禁用，并且同步mdm
        if(oe == OpLogEnum.StoreOpenCancelApply && applyInfo.getApplyInfo().getStoreId()!=null){
            Boolean succeed = this.updateStoreStatus(applyInfo.getApplyInfo().getStoreId(),
                    applyInfo.getApplyInfo().getWfInstanceCode(), "开店申请取消，门店状态变更为：禁用！", StoreStatusEnum.Disable);
            if (succeed == null || !succeed) {
                throw new BusinessException("操作失败，错误码[10001]！");
            }
            peripheralServcie.storeInfoToMdm(applyInfo.getApplyInfo().getWfInstanceCode());
        }

        //如果更新状态已取消，则删除当前的流程实例
        if (status == 3 && !StringUtils.isBlank(applyInfo.getApplyInfo().getWfInstanceCode())) {
            storeActivitiService.deleteProcessInstance(applyInfo.getApplyInfo().getWfInstanceCode(), "申请取消");
        }

        //issue:15034 如果更新状态已结束且为闭店流程，则更新门店名称为禁用 added by zd, 20211126
        if (status == 2 && we == WorkflowEnum.CloseStore) {
           /* UpdateStoreNameDto update = new UpdateStoreNameDto();
            update.setStoreName(applyInfo.getApplyInfo().getStoreName()+"(禁用)");
            update.setStoreCode(applyInfo.getApplyInfo().getStoreCode());
            orgClient.updateStoreName(update);*/
        }

        //写入操作日志
        if (oe != OpLogEnum.NULL && edit.getNeedWriteLog()) {
            try {
                //新增申请操作日志
                OpLogInfo opLog = new OpLogInfo("s_apply", applyInfo.getApplyInfo().getId(), oe, edit.getRemark());
                opLog.setOperatorId(edit.getOperatorId());
                opLog.setOperatorName(edit.getOperatorName());
                logService.addOpLog(opLog);
            } catch (Exception ex) {
                log.error("ApplyStoreService.editApplyInfoStatus 操作日志插入失败，不影响主流程！", ex);
            }
        }
        return ret;
    }

    /**
     * 同步门店状态
     */
    @GlobalTransactional
    public void syncStoreStatus(@Valid SyncStoreStatus sync) {
        if (sync == null) {
            throw new BusinessException("参数错误！");
        }

        if (sync.getStatus() == null ||
                (!sync.getStatus().equals(StoreStatusEnum.Disable.getStatus())
                        && !sync.getStatus().equals(StoreStatusEnum.Enable.getStatus()))
                        && !sync.getStatus().equals(StoreStatusEnum.Retreat.getStatus())) {
            throw new BusinessException("门店状态错误！");
        }

        ApplyStore applyInfo = getApplyInfo(sync.getApplyId(), sync.getProcessInstanceId(), null);
        if (applyInfo == null || applyInfo.getId() == null || applyInfo.getId() <= 0)
            throw new BusinessException("申请信息不存在！");

        //变更门店状态(0:禁用; 1:启用; 2:筹备; 3:清退)
        Integer status = sync.getStatus();
        OpLogEnum oe = null;
        StoreStatusEnum sse = null;
        if (applyInfo.getApplyType() == 1) { //开店
            if (!status.equals(StoreStatusEnum.Enable.getStatus())) {
                throw new BusinessException("开店申请仅支持门店启用操作！");
            }
            oe = OpLogEnum.StoreEnabled;
            sse = StoreStatusEnum.Enable;
        } else if (applyInfo.getApplyType() == 2) { //闭店
           /* if (!status.equals(StoreStatusEnum.Disable.getStatus())
                    &&!status.equals(StoreStatusEnum.Enable.getStatus())) {
                throw new BusinessException("闭店申请仅支持门店禁用操作！");
            }*/
            if (status.equals(StoreStatusEnum.Disable.getStatus())) {
                oe = OpLogEnum.StoreDisabled;
                sse = StoreStatusEnum.Disable;
            } else if (status.equals(StoreStatusEnum.Enable.getStatus())) {
                oe = OpLogEnum.StoreEnabled;
                sse = StoreStatusEnum.Enable;
            } else if (status.equals(StoreStatusEnum.Retreat.getStatus())) {
                oe = OpLogEnum.StoreRetreat;
                sse = StoreStatusEnum.Retreat;
            }
            if (oe == null || sse == null) {
                throw new BusinessException("闭店申请仅支持门店禁用启用清退操作！");
            }

            //TODO:调用DRP接口,判断当前门店是否可以禁用
        } else if (WorkflowEnum.ReopenStore.getValue().equals(applyInfo.getApplyType())) {
            if (status.equals(StoreStatusEnum.Enable.getStatus())) {
                oe = OpLogEnum.StoreEnabled;
                sse = StoreStatusEnum.Enable;
            } else if (status.equals(StoreStatusEnum.Retreat.getStatus())) {
                oe = OpLogEnum.StoreRetreat;
                sse = StoreStatusEnum.Retreat;
            }
        } else {
            throw new BusinessException("业务流程错误！");
        }

        setOperatorIdAndName(sync, applyInfo);

        //日志信息
        OpLogInfo logInfo = new OpLogInfo("s_apply", applyInfo.getId(), oe, sync.getRemark());
        logInfo.setOperatorId(sync.getOperatorId());
        logInfo.setOperatorName(sync.getOperatorName());
        logInfo.setOperatorUserName(sync.getOperatorUserName());
        Integer ret = 0;
        Boolean succeed = false;
        //1:调用ORG接口同步门店状态
        succeed = this.updateStoreStatus(applyInfo.getStoreId(),
                applyInfo.getWfInstanceCode(), logInfo, sse);
        try {

            //流程进入下个节点
            if (!StringUtils.isBlank(applyInfo.getWfCurrentTaskId()) && sync.isNeedFinishTask()) {
                succeed = storeActivitiService.finishTaskById(applyInfo.getWfCurrentTaskId(), null);
                if (!succeed)
                    throw new BusinessException("操作失败，错误码[10002]！");
            }
            // 将门店名称变更这一步移动到同步MDM前
           /*  if(sync.getStatus()!=null && StoreStatusEnum.Disable.getStatus().equals(sync.getStatus())){
                 UpdateStoreNameDto update = new UpdateStoreNameDto();
                 String storeName = applyInfo.getStoreName();
                 storeName=storeName.replace(DISABLE_STORE_NAME,"");
                 storeName=storeName.replace(CLEAR_STORE_NAME,"");
                 update.setStoreName(storeName+DISABLE_STORE_NAME);
                 update.setStoreCode(applyInfo.getStoreCode());
                 orgClient.updateStoreName(update);
             }*/
            if (succeed == null || !succeed) {
                throw new BusinessException("操作失败，错误码[10001]！");
            }

        } catch (Exception ex) {
            log.error("ApplyStoreService.syncStoreStatus 启用/禁用门店状态错误！", ex);
            throw new BusinessException("操作失败，错误码[10003]！");
        }

        try {
            //新增申请操作日志
            logService.addOpLog(logInfo);
        } catch (Exception ex) {
            log.error("ApplyStoreService.syncStoreStatus 操作日志插入失败，不影响主流程！", ex);
        }
    }


    private void setOperatorIdAndName(SyncStoreStatus sync, ApplyStore applyInfo) {
        //先从token中获取操作人
        Map<String, String> user = CommonUtils.getAuthorUser();
        if (StringUtils.isBlank(sync.getOperatorId())) {
            sync.setOperatorId(user.get("userid"));
        }
        if (StringUtils.isBlank(sync.getOperatorName())) {
            sync.setOperatorName(user.get("name"));
        }
        if (StringUtils.isBlank(sync.getOperatorUserName())) {
            sync.setOperatorUserName(user.get("username"));
        }
        if ("anonymousUser".equals(user.get("username"))) {
            sync.setOperatorId(null);
            sync.setOperatorName(null);
            sync.setOperatorUserName(null);
        }
        //如果定时任务或者消费的消息，没有操作人信息，则从流程中获取
        if (StringUtils.isBlank(sync.getOperatorId()) && StringUtils.isNotBlank(applyInfo.getWfInstanceCode())) {
            List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(applyInfo.getWfInstanceCode())
                    .orderByTaskCreateTime()
                    .desc()
                    .listPage(0, 1);
            if (historicTaskInstances != null && historicTaskInstances.size() > 0) {
                HistoricTaskInstance historicTaskInstance = historicTaskInstances.get(0);

                UserDTO userParam = new UserDTO()
                        .setNumber(historicTaskInstance.getAssignee())
                        .setProcessCode(historicTaskInstance.getBusinessKey());

                List<UserDTO> currentTaskUsers = userService.getByParams(userParam);
                if (currentTaskUsers != null && currentTaskUsers.size() > 0) {
                    UserDTO currentTaskUser = currentTaskUsers.get(0);
                    sync.setOperatorId(currentTaskUser.getUserEntityId());
                    sync.setOperatorUserName(currentTaskUser.getUsername());
                    sync.setOperatorName(currentTaskUser.getFirstName());
                }
            }
        }
    }

    /**
     * 删除申请(开店/闭店...)
     */
    @GlobalTransactional
    public Integer delApplyInfo(@Valid ApplyInfoDelete del) {
        if (del == null || del.getApplyId() == null || del.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        ApplyStore applyInfo = getApplyInfo(del.getApplyId(), null, null);
        if (applyInfo == null || applyInfo.getId() == null || applyInfo.getId() <= 0)
            throw new BusinessException("申请信息不存在！");

        if (!applyInfo.getApplyStatus().equals(0))
            throw new BusinessException("申请信息在当前状态下不允许删除！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        del.setOperatorId(user.get("userid"));
        del.setOperatorName(user.get("name"));

        Integer ret = applyStoreDao.delApplyInfo(del);
        if (ret != null && ret > 0) {
            if (applyInfo.getApplyType().equals(2)) {
                //TODO:闭店申请删除 恢复门店状态为 启用
                Boolean succeed = this.updateStoreStatus(applyInfo.getStoreId(),
                        applyInfo.getWfInstanceCode(), "闭店申请删除，恢复门店状态为：启用！", StoreStatusEnum.Enable);
                if (succeed == null || !succeed) {
                    throw new BusinessException("操作失败，错误码[10001]！");
                }
            }
            //删除流程实例
            if (StringUtils.isNotBlank(applyInfo.getWfInstanceCode()))
                storeActivitiService.deleteProcessInstance(applyInfo.getWfInstanceCode(), "取消申请");
        } else {
            throw new BusinessException("操作失败，错误码[10002]！");
        }
        return ret;
    }

    /**
     * 获取门店(开店、闭店)申请记录明细
     *
     * @param applyId           申请Id(s_apply.id)
     * @param processInstanceId 流程实例ID
     * @param processTaskId     流程任务ID
     * @param needDetails       是否需要获取明细(true:是,false:否-仅读取主表信息)
     * @param needAttachment    是否需要获取附件(true:是,false:否)
     */
    public ApplyStoreDetails getApplyStoreDetails(Integer applyId,
                                                  String processInstanceId,
                                                  String processTaskId,
                                                  boolean needDetails,
                                                  boolean needAttachment) {
        if (applyId == null && StringUtils.isBlank(processInstanceId))
            return null;

        ApplyStoreDetails details = new ApplyStoreDetails();
        //获取申请记录(s_apply)
        ApplyStore applyStore = applyStoreDao.getApplyInfo(applyId, processInstanceId, processTaskId);
        dictService.list("sub_channel_code").stream().filter(dict -> {
            if (dict.getValueCode() == null) {
                return dict.getValueCode() == null && applyStore.getStoreSubChannelCode() == null;
            } else {
                return dict.getValueCode().equals(applyStore.getStoreSubChannelCode());
            }
        }).findFirst().ifPresent(dict -> applyStore.setStoreSubChannelName(dict.getValueName()));
        details.setApplyInfo(applyStore);
        if (details.getApplyInfo() == null)
            return null;

        if (applyId == null || applyId <= 0)
            applyId = applyStore.getId();
        Optional<HistoricProcessInstance> processInstanceOptional = Optional.ofNullable(applyStore.getWfInstanceCode())
                .filter(StringUtils::isNotBlank)
                .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult());
        if (needDetails) {
            processInstanceOptional
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .ifPresent(applyStore::setProcDefVersion);
            if (applyStore.getApplyType() == 1) { //开店申请(s_apply_open)
                details.setOpenInfo(getOpenInfo(applyId, null));
                //装修模板
/*                if (applyStore.getOpenType() != null) {
                    if (applyStore.getOpenType().equals(1)) {
                        //专卖店/KA门店
                        details.setOpenNonCommunity(applyStoreDao.getOpenNonCommunityInfo(applyId, null));
                    } else if (applyStore.getOpenType().equals(2)) {
                        //社区店
                        details.setOpenCommunity(applyStoreDao.getOpenCommunityInfo(applyId, null));
                    }
                }*/
                if(Objects.equals(2,applyStore.getOpenType())){
                    //社区店
                    details.setOpenCommunity(applyStoreDao.getOpenCommunityInfo(applyId, null));
                }else{
                    //其他
                    details.setOpenNonCommunity(applyStoreDao.getOpenNonCommunityInfo(applyId, null));
                }

                //附件信息(attachment)
                if (needAttachment && details.getOpenInfo() != null && details.getOpenInfo().getId() != null) {
                    AttachmentQuery query = new AttachmentQuery();
                    List<AttachmentEnum> aes = Arrays.asList(AttachmentEnum.ApplyStoreOpen);

                    Integer applyOpenId = details.getOpenInfo().getId();
                    List<AttachmentQuery> queries = query.getAttachmentQueries(aes, applyOpenId);
                    if (queries != null && queries.size() > 0) {
                        List<AttachmentInfo> attachments = attachmentService.queryAttachmentList(queries);
                        if (attachments != null && attachments.size() > 0) {
                            details.setAttachmentInfo(attachmentService.findAll(attachments, AttachmentEnum.ApplyStoreOpen, applyOpenId));
                        }
                    }

                    //21892 开店申请-附件部分分离明确上传内容 新增功能-附件不统一显示，分类上传，分类显示
                    ArrayList<AttachmentEnum> newAttachmentType = Lists.newArrayList(AttachmentEnum.ApplyStoreOpenTest, AttachmentEnum.ApplyStoreOpenHouse, AttachmentEnum.ApplyStoreOpenAddress,
                            AttachmentEnum.ApplyStoreOpenEnvironment, AttachmentEnum.ApplyStoreOpenEmpower, AttachmentEnum.ApplyStoreOpenPositioning, AttachmentEnum.ApplyStoreOpenChannel);
                    details.setAttachmentInfoMap(attachmentService.getMap(newAttachmentType, applyOpenId));

                    //门头图
                    List<AttachmentEnum> plaqueQuery = Arrays.asList(AttachmentEnum.ApplyStoreOpenPlaque);
                    List<AttachmentQuery> plaqueQueries = query.getAttachmentQueries(plaqueQuery, applyOpenId);
                    if (plaqueQueries != null && plaqueQueries.size() > 0) {
                        List<AttachmentInfo> attachments = attachmentService.queryAttachmentList(plaqueQueries);
                        if (attachments != null && attachments.size() > 0) {
                            details.getOpenInfo().setPlaqueAttachments(attachments);
                        }
                    }
                }
                //开店的新店质量保障项目成员
                List<ApplyStoreOpenExplosiveMember> explosiveMembers = openExplosiveMemberService.lambdaQuery().eq(ApplyStoreOpenExplosiveMember::getOpenId, details.getOpenInfo().getId()).eq(ApplyStoreOpenExplosiveMember::getIsDeleted, 0).list();
                details.setExplosiveMemberList(explosiveMembers);

                //不需要终端建设的开店的出样策略
                details.setApplyOpenStrategy(applyOpenStrategyService.getByApplyId(applyId));
            } else if (applyStore.getApplyType() == 2) {
                //闭店申请(s_apply_close)
                details.setCloseInfo(getCloseInfo(applyId, null));
                if (details.getCloseInfo() != null && StringUtils.isNotBlank(details.getCloseInfo().getCloseReason())) {
                    DicDto dicDto = new DicDto();
                    dicDto.setTypeCodes(Lists.newArrayList("close_reason"));
                    Result<List<DicDto>> closeDictResult = systemClient.getDicByCodes(dicDto);
                    if (closeDictResult != null && closeDictResult.getSuccess() && closeDictResult.getData() != null) {
                        Map<String, String> closeMap = closeDictResult.getData().stream().collect(Collectors.toMap(DicDto::getValueCode, DicDto::getValueName));
                        details.getCloseInfo().setCloseReasonName(Arrays.stream(details.getCloseInfo().getCloseReason().split(","))
                                .map(s->closeMap.get(s)).collect(Collectors.joining(",")) );
                    }
                }

                //附件信息(attachment)
                if (needAttachment && details.getCloseInfo() != null && details.getCloseInfo().getId() != null) {
                    AttachmentQuery query = new AttachmentQuery();
                    List<AttachmentEnum> aes = Arrays.asList(AttachmentEnum.ApplyStoreClose);

                    Integer applyCloseId = details.getCloseInfo().getId();
                    List<AttachmentQuery> queries = query.getAttachmentQueries(aes, applyCloseId);
                    if (queries != null && queries.size() > 0) {
                        List<AttachmentInfo> attachments = attachmentService.queryAttachmentList(queries);
                        if (attachments != null && attachments.size() > 0) {
                            details.setAttachmentInfo(attachmentService.findAll(attachments, AttachmentEnum.ApplyStoreClose, applyCloseId));
                        }
                    }
                }
            } else if (WorkflowEnum.ReopenStore.getValue().equals(applyStore.getApplyType())) {
                //重启申请(s_apply_reopen)
                ReopenVO reopenVO = reopenService.getReopenVO(applyStore.getId(), null);
                details.setReopenVO(reopenVO);

                //附件信息
                if (needAttachment && details.getReopenVO() != null && details.getReopenVO().getId() != null) {
                    AttachmentQuery query = new AttachmentQuery();
                    List<AttachmentEnum> aes = Arrays.asList(AttachmentEnum.ReopenStoreApply);

                    Integer reopenId = details.getReopenVO().getId();
                    List<AttachmentQuery> queries = query.getAttachmentQueries(aes, reopenId);
                    if (queries != null && queries.size() > 0) {
                        List<AttachmentInfo> attachments = attachmentService.queryAttachmentList(queries);
                        if (attachments != null && attachments.size() > 0) {
                            details.setAttachmentInfo(attachmentService.findAll(attachments, AttachmentEnum.ReopenStoreApply, reopenId));
                        }
                    }
                }
            } else if (WorkflowEnum.RedecorateStore.getValue().equals(applyStore.getApplyType())) {//重装
                RedecorateVO redecorateVO = redecorateService.getRedecorateVO(applyStore.getId(), null);
                details.setRedecorateVO(redecorateVO);
                if (needAttachment && details.getRedecorateVO() != null && details.getRedecorateVO().getId() != null) {
                    AttachmentQuery query = new AttachmentQuery();
                    List<AttachmentEnum> aes = Arrays.asList(AttachmentEnum.RedecorateApply);
                    Integer redecorateId = details.getRedecorateVO().getId();
                    List<AttachmentQuery> queries = query.getAttachmentQueries(aes, redecorateId);
                    if (queries != null && queries.size() > 0) {
                        List<AttachmentInfo> attachments = attachmentService.queryAttachmentList(queries);
                        if (attachments != null && attachments.size() > 0) {
                            details.setAttachmentInfo(attachmentService.findAll(attachments, AttachmentEnum.RedecorateApply, redecorateId));
                        }
                    }
                }
            } else if (WorkflowEnum.ChangeStore.getValue().equals(applyStore.getApplyType())) {
                List<ChangeVO> changeVO = changeService.getChangeVO(applyStore.getId());
                details.setChangeVOList(changeVO);
            }
        }

        return details;
    }


    /************************ 开店流程 ************************/

    /**
     * 获取门店开店申请信息(s_apply_open)
     */
    public ApplyStoreOpen getOpenInfo(Integer applyId, Integer id) {
        ApplyStoreOpen openInfo = applyStoreDao.getOpenInfo(applyId, id);
        if (applyId != null) {
            SApplyOpenTopKitchen kitchen = new SApplyOpenTopKitchen();
            kitchen.setApplyId(applyId);
            List<String> topKitchens = applyStoreTopKitchenService.queryByParams(kitchen)
                    .stream().map(SApplyOpenTopKitchen::getBrand).collect(Collectors.toList());
            openInfo.setTopKitchens(topKitchens);
        }
        if (StringUtils.isNotBlank(openInfo.getPlanType())) {
            Result<List<DicDto>> openTypeResult = systemClient.getDicByCodes(new DicDto().setTypeCodes(Arrays.asList("store_open_plan_type")));
            if (openTypeResult.getSuccess() && openTypeResult.getData() != null && !openTypeResult.getData().isEmpty()) {
                for (DicDto dicDto : openTypeResult.getData()) {
                    if (openInfo.getPlanType().equals(dicDto.getValueCode())) {
                        openInfo.setPlanTypeName(dicDto.getValueName());
                    }
                }
            }
        }
        return openInfo;
    }

    /**
     * 添加开店申请(s_apply_open)
     *
     * @return 新增成功则返回记录Id
     */
    public Integer addOpenInfo(@Valid OpenInfo add) {
        if (add == null || add.getApplyId() == null || add.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        setStoreLevel(add);
        //执行新增操作
        applyStoreDao.addOpenInfo(add);
        saveTopKitchens(add);
        return add.getId();
    }

    public String setStoreLevel(OpenInfo add) {
/*        if (add.getUsableArea() != null && ChannelCategoryEnum.SC_LB_LB001_L025.getCode().equals(add.getStoreChannelCode())) {
            if (add.getUsableArea().compareTo(new BigDecimal(400)) >= 0) {
                add.setStoreLevel(StoreLevelEnum.EXPERIENCE.getStoreLevel());
            } else if (add.getUsableArea().compareTo(new BigDecimal(200)) >= 0) {
                add.setStoreLevel(StoreLevelEnum.FLAGSHIP.getStoreLevel());
            } else if (add.getUsableArea().compareTo(new BigDecimal(100)) >= 0) {
                add.setStoreLevel(StoreLevelEnum.COMMON.getStoreLevel());
            } else if (add.getUsableArea().compareTo(new BigDecimal(40)) >= 0) {
                add.setStoreLevel(StoreLevelEnum.FRANCHISE.getStoreLevel());
            }
        }*/
        //现在已经改成了用门店整体使用面积判断门店级别，开店没有这个字段，转移到终端判断
        return add.getStoreLevel();
    }

    public void saveTopKitchens(OpenInfo add) {
        if (add.getTopKitchens() != null && !add.getTopKitchens().isEmpty()) {
            List<SApplyOpenTopKitchen> addBrandList = new LinkedList<>();
            Date createDate = new Date();
            for (String brand : add.getTopKitchens()) {
                SApplyOpenTopKitchen topKitchen = new SApplyOpenTopKitchen().setApplyId(add.getApplyId())
                        .setBrand(brand).setCreatedBy(add.getOperatorId()).setIsDeleted(false)
                        .setCreatedDate(createDate);
                addBrandList.add(topKitchen);
            }
            applyStoreTopKitchenService.saveBatch(addBrandList);
        }
    }

    /**
     * 编辑开店申请(s_apply_open)
     */
    public Integer editOpenInfo(@Valid OpenInfo edit) {
        if (edit == null || edit.getId() == null || edit.getId() <= 0
                || edit.getApplyId() == null || edit.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));


        LambdaQueryWrapper<SApplyOpenTopKitchen> deleteWrapper = new QueryWrapper<SApplyOpenTopKitchen>()
                .lambda().eq(SApplyOpenTopKitchen::getApplyId, edit.getApplyId());
        applyStoreTopKitchenService.delete(deleteWrapper);
        saveTopKitchens(edit);

        setStoreLevel(edit);
        return applyStoreDao.editOpenInfo(edit);
    }

    /**
     * 获取门店开店申请(社区店)信息(s_apply_open_community)
     */
    public ApplyStoreOpenCommunity getOpenCommunityInfo(Integer applyId, Integer id) {
        return applyStoreDao.getOpenCommunityInfo(applyId, id);
    }

    /**
     * 添加开店申请(社区店 s_apply_open_community)
     *
     * @return 新增成功则返回记录Id
     */
    public Integer addOpenCommunityInfo(@Valid OpenCommunityInfo add) {
        if (add == null || add.getApplyId() == null || add.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        //执行新增操作
        applyStoreDao.addOpenCommunityInfo(add);
        return add.getId();
    }

    /**
     * 编辑开店申请(社区店 s_apply_open_community)
     */
    public Integer editOpenCommunityInfo(@Valid OpenCommunityInfo edit) {
        if (edit == null || edit.getId() == null || edit.getId() <= 0
                || edit.getApplyId() == null || edit.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        return applyStoreDao.editOpenCommunityInfo(edit);
    }

    /**
     * 逻辑删除开店申请(社区店 s_apply_open_community)
     */
    public Integer delOpenCommunityInfo(Integer id, Integer applyId, String operatorId) {
        Map<String, String> user = CommonUtils.getAuthorUser();
        return applyStoreDao.delOpenCommunityInfo(id, applyId, user.get("userid"));
    }


    /**
     * 获取门店开店申请(非社区店)信息(s_apply_open_noncommunity)
     */
    public ApplyStoreOpenNonCommunity getOpenNonCommunityInfo(Integer applyId, Integer id) {
        return applyStoreDao.getOpenNonCommunityInfo(applyId, id);
    }

    /**
     * 添加开店申请(KA/专卖店 s_apply_open_noncommunity)
     *
     * @return 新增成功则返回记录Id
     */
    public Integer addOpenNonCommunityInfo(@Valid OpenNonCommunityInfo add) {
        if (add == null || add.getApplyId() == null || add.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        //执行新增操作
        applyStoreDao.addOpenNonCommunityInfo(add);
        return add.getId();
    }

    /**
     * 编辑开店申请(KA/专卖店 s_apply_open_noncommunity)
     */
    public Integer editOpenNonCommunityInfo(@Valid OpenNonCommunityInfo edit) {
        if (edit == null || edit.getId() == null || edit.getId() <= 0
                || edit.getApplyId() == null || edit.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        return applyStoreDao.editOpenNonCommunityInfo(edit);
    }

    /**
     * 逻辑删除开店申请(社区店 s_apply_open_community)
     */
    public Integer delOpenNonCommunityInfo(Integer id, Integer applyId) {
        Map<String, String> user = CommonUtils.getAuthorUser();
        return applyStoreDao.delOpenNonCommunityInfo(id, applyId, user.get("userid"));
    }

    /**
     * 新增开店申请
     *
     * @return 新增成功则返回申请Id(s_apply.id)
     */
    @Transactional
    public Integer addApplyStoreOpen(@Valid OpenInfoEdit add) {
        //参数判断
        if (add == null || add.getApplyInfo() == null || add.getOpenInfo() == null) {
            throw new BusinessException("参数错误！");
        }

        if (add.getOpenInfo().getPlanId() != null && add.getOpenInfo().getPlanId() > 0) {
            Boolean canUse = openPlanService.openStoreCanUse(Long.valueOf(add.getOpenInfo().getPlanId()), add.getApplyInfo().getId());
            if (Boolean.FALSE.equals(canUse)) {
                throw new BusinessException("规划编码已被关联,请更换编码！");
            }
        }

        //操作人信息
        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        //判断当前用户是否具有创建开店申请的权限
        /*HashMap permissions = storeActivitiService.hasButton(user.get("userid"),
                WorkflowEnum.OpenStore.getCode());
        if (MapUtils.isEmpty(permissions)
                || !MapUtils.getBoolean(permissions, WorkflowEnum.OpenStore.getCode())) {
            throw new BusinessException("抱歉，您没有创建开店申请的权限！");
        }*/

        //工作流参数判定
        int bpm = BusinessConstant.getOpenRealBpmType(add.getApplyInfo().getStoreType(),
                add.getApplyInfo().getBpmType(),
                add.getApplyInfo().getDistributorChannelCode(),null);//新增的时候流程定义传空
        if (bpm <= 0)
            throw new BusinessException("业务流程错误！");

        //判断是否有当前的门店渠道
        /*ActivitiBranchDTO openBpm = storeActivitiService.getActivitiBranch(user.get("userid"), WorkflowEnum.OpenStore.getCode());
        if (openBpm == null || openBpm.getParamValues() == null || !openBpm.getParamValues().contains(String.valueOf(bpm)))
            throw new BusinessException("抱歉，您的岗位角色权限不足，请选择其他门店渠道！");*/

        //申请Id
        Integer applyId = 0;
        //执行返回
        Integer result = 0;

        //新增申请信息
        result = addApplyInfo(add.getApplyInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("新增开店申请失败，错误码[10001]！");
        }
        //申请Id
        applyId = result;

        //新增开店申请信息（申请Id）
        add.getOpenInfo().setApplyId(applyId);
        add.getOpenInfo().setStoreChannelCode(add.getApplyInfo().getStoreChannelCode());
        result = addOpenInfo(add.getOpenInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("新增开店申请失败，错误码[10002]！");
        }
        Integer applyOpenId = result;

        //装修类型
        int buildType = BusinessConstant.getOpenRealBuildType(add.getApplyInfo().getStoreType(), add.getApplyInfo().getOpenType());
        if (buildType <= 0) {
            throw new BusinessException("新增开店申请失败，错误码[10003]！");
        }

        if (buildType == 1) {//专卖店,KA
            add.getOpenNonCommunityInfo().setApplyId(applyId);
            result = addOpenNonCommunityInfo(add.getOpenNonCommunityInfo());
            if (result == null || result <= 0) {
                throw new BusinessException("新增开店申请失败，错误码[10004]！");
            }
        } else if (buildType == 2) {//社区店
            add.getOpenCommunityInfo().setApplyId(applyId);
            result = addOpenCommunityInfo(add.getOpenCommunityInfo());
            if (result == null || result <= 0) {
                throw new BusinessException("新增开店申请失败，错误码[10005]！");
            }
        }else{
            //所有渠道都要有NonCommunity或Community表，不然终端后端同步门店详情有问题
            OpenNonCommunityInfo openNonCommunityInfo = new OpenNonCommunityInfo();
            openNonCommunityInfo.setApplyId(applyId);
            addOpenNonCommunityInfo(openNonCommunityInfo);
        }

        //新增附件信息
        if (add.getAttachmentInfo() != null && add.getAttachmentInfo().size() > 0) {
            result = attachmentService.batchInsertAttachmentByRefId(add.getAttachmentInfo(), applyOpenId);
            if (result == null || result <= 0) {
                throw new BusinessException("新增开店申请失败，错误码[10006]！");
            }
        }

        //新增新店质量保障项目成员
        openExplosiveMemberService.saveExplosiveMembers(add.getExplosiveMemberList(), applyOpenId);

        //创建工作流
        if (add.getActionType() == 1) {
            WorkflowEnum we = WorkflowEnum.OpenStore;
            //流程标题(分公司名称-常规门店建设流程-门店名称-流程实例编码)
            String title = add.getApplyInfo().getCompanyName() +
                    "-" + we.getDescription() +
                    "-" + add.getApplyInfo().getStoreName() +
                    "-" + applyId;

            ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
            //门店类型(0:其他,1:专卖店,2:社区服务店)
            int storeChannelType = 0;
            if (cce.isSpecialStoreChannel(add.getApplyInfo().getStoreChannelCode())) {
                storeChannelType = 1;
            }

            HashMap<String, Object> params = BusinessConstant.getOpenBpmParams(
                    add.getApplyInfo(),
                    null,
                    applyId,
                    add.getApplyInfo().getStoreType(),
                    bpm,
                    add.getOpenInfo().getUsableArea(),
                    add.getOpenInfo().getNeedTerminalBuild(),
                    storeChannelType
            );
            storeActivitiService.startProcess(we.getCode(), add.getApplyInfo().getCompanyId().toString(), title, params);
        }

        try {
            //新增申请操作日志
            logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreOpenCreateApply,
                    "创建开店申请，门店名称：" + add.getApplyInfo().getStoreName() + "，门店渠道：" + add.getApplyInfo().getStoreChannelName()));

            if (add.getActionType() == 1) {
                //新增提交申请日志
                logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreOpenSubmitApply, ""));
            }
        } catch (Exception ex) {
            log.error("ApplyStoreService.addApplyStoreOpen 操作日志插入失败，不影响主流程！", ex);
        }

        return applyId;
    }

    /**
     * 编辑开店申请
     *
     * @return 编辑成功则返回申请Id(s_apply.id)
     */
    @Transactional
    public Integer editApplyStoreOpen(@Valid OpenInfoEdit edit) {
        if (edit == null
                || edit.getApplyInfo() == null
                || edit.getApplyInfo().getId() == null || edit.getApplyInfo().getId() <= 0
                || edit.getOpenInfo() == null
                || edit.getOpenInfo().getId() == null || edit.getOpenInfo().getId() <= 0) {
            throw new BusinessException("参数错误！");
        }
        ApplyStoreDetails applyStore = this.getApplyStoreDetails(edit.getApplyInfo().getId(),
                null, null, true, true);
        if (applyStore == null || applyStore.getApplyInfo() == null || applyStore.getApplyInfo().getId() == null)
            throw new BusinessException("开店申请信息不存在！");

        //状态判断
        if (applyStore.getApplyInfo().getApplyStatus() == 0 && edit.getActionType() == 1) {
            edit.getApplyInfo().setApplyStatus(1);
            if (edit.getExplosiveMemberList() == null || edit.getExplosiveMemberList().isEmpty()) {
                // 提交后 爆量人员为空，并且非L025渠道，取消开业爆量流程，L025渠道的在015节点取消
                if(!StringUtils.equals(ChannelCategoryEnum.SC_LB_LB002_L025.getCode(),edit.getApplyInfo().getStoreChannelCode() )){
                    openExplosiveMemberService.canelExplosiveProcessByOpenApply(edit.getApplyInfo().getId(), null, "开店流程信息变更，不再需要开业爆量活动流程");
                }
                //取消开业爆量活动申请,在015取消
                //  openExplosiveMemberService.canelExplosiveProcessByOpenApply(edit.getApplyInfo().getId(), null, "开店流程信息变更，不再需要开业爆量活动流程");
            } else {
                //同步开业爆量活动申请
                StartOpenExplosiveProcess startOpenExplosiveProcess = new StartOpenExplosiveProcess();
                startOpenExplosiveProcess.setCreateIfNotExists(false);
                startOpenExplosiveProcess.setMembers(edit.getExplosiveMemberList());
                startOpenExplosiveProcess.setOpenStoreApplyId(edit.getApplyInfo().getId());
                explosiveService.syncOpenStoreInfoToExplosive(startOpenExplosiveProcess);
            }
        }

        openExplosiveStatsService.setStoreLevelByWholeUsable(edit.getApplyInfo().getStoreId(), edit.getOpenInfo().getWholeUsableArea());

        if (edit.getOpenInfo().getPlanId() != null && edit.getOpenInfo().getPlanId() > 0) {
            Boolean canUse = openPlanService.openStoreCanUse(Long.valueOf(edit.getOpenInfo().getPlanId()), edit.getApplyInfo().getId());
            if (Boolean.FALSE.equals(canUse)) {
                throw new BusinessException("规划编码已被关联,请更换编码！");
            }
        }

        //操作人信息
        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        //执行返回
        Integer result = 0;

        //编辑申请信息
        result = editApplyInfo(edit.getApplyInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("编辑开店申请失败，错误码[10001]！");
        }

        //编辑开店申请信息
        result = editOpenInfo(edit.getOpenInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("编辑开店申请失败，错误码[10002]！");
        }

        //装修类型
        int buildType = BusinessConstant.getOpenRealBuildType(edit.getApplyInfo().getStoreType(), edit.getApplyInfo().getOpenType());
        if (buildType <= 0) {
            throw new BusinessException("编辑开店申请失败，错误码[10003]！");
        }

        if (buildType == 1) {//专卖店,KA
            this.delOpenNonCommunityInfo(null, edit.getApplyInfo().getId());
            result = this.addOpenNonCommunityInfo(edit.getOpenNonCommunityInfo());

            if (result == null || result <= 0) {
                throw new BusinessException("编辑开店申请失败，错误码[10004]！");
            }
        } else if (buildType == 2) {//社区店
            this.delOpenCommunityInfo(null, edit.getApplyInfo().getId(), null);
            result = this.addOpenCommunityInfo(edit.getOpenCommunityInfo());

            if (result == null || result <= 0) {
                throw new BusinessException("编辑开店申请失败，错误码[10005]！");
            }
        } else {
            //删除历史装修信息
           // this.delOpenCommunityInfo(null, edit.getApplyInfo().getId(), null);
          //  this.delOpenNonCommunityInfo(null, edit.getApplyInfo().getId());
        }

        //更新新店质量保障项目成员
        openExplosiveMemberService.lambdaUpdate().eq(ApplyStoreOpenExplosiveMember::getOpenId, edit.getOpenInfo().getId()).remove();
        openExplosiveMemberService.saveExplosiveMembers(edit.getExplosiveMemberList(), edit.getOpenInfo().getId());

        //批量删除附件信息
        List<Integer> refIds = applyStore.getApplyAttachments()
                .stream()
                .map(AttachmentInfo::getId)
                .distinct()
                .collect(Collectors.toList());
        if (applyStore.getAttachmentInfoMap() != null && !applyStore.getAttachmentInfoMap().isEmpty()) {
            applyStore.getAttachmentInfoMap().entrySet().stream().forEach(entry -> {
                refIds.addAll(entry.getValue().stream().map(AttachmentInfo::getId).collect(Collectors.toList()));
            });
        }

        if (refIds.size() > 0) {
            attachmentService.batchDelAttachment(refIds, AttachmentEnum.NULL, edit.getOpenInfo().getId());
        }

        //新增附件信息
        if (edit.getAttachmentInfo() != null && edit.getAttachmentInfo().size() > 0) {
            result = attachmentService.batchInsertAttachmentByRefId(edit.getAttachmentInfo(), edit.getOpenInfo().getId());
            if (result == null || result <= 0) {
                throw new BusinessException("编辑开店申请失败，错误码[10006]！");
            }
        }

        //门头图附件更新
        if (applyStore.getOpenInfo().getPlaqueAttachments() != null) {
            List<Integer> plaqueIds = applyStore.getOpenInfo().getPlaqueAttachments()
                    .stream().map(AttachmentInfo::getId)
                    .distinct()
                    .collect(Collectors.toList());
            if (plaqueIds.size() > 0) {
                attachmentService.batchDelAttachment(plaqueIds, AttachmentEnum.NULL, edit.getOpenInfo().getId());
            }
        }
        if (edit.getOpenInfo().getPlaqueAttachments() != null && edit.getOpenInfo().getPlaqueAttachments().size() > 0) {
            for (AttachmentInfo plaqueAttachment : edit.getOpenInfo().getPlaqueAttachments()) {
                plaqueAttachment.setRefType(AttachmentEnum.ApplyStoreOpenPlaque.getType());
                plaqueAttachment.setRefTable(AttachmentEnum.ApplyStoreOpenPlaque.getRefTable());
            }
            result = attachmentService.batchInsertAttachmentByRefId(edit.getOpenInfo().getPlaqueAttachments(), edit.getOpenInfo().getId());
            if (result == null || result <= 0) {
                throw new BusinessException("编辑开店申请失败，错误码[10006]！");
            }
        }


        //创建工作流
        if (edit.getActionType() == 1) {
            //工作流参数判定
            int bpm =0;
            if(StringUtils.isNotBlank(applyStore.getApplyInfo().getWfInstanceCode())){
                Integer procDefVersion = Optional.ofNullable(applyStore.getApplyInfo().getWfInstanceCode())
                        .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                        .map(HistoricProcessInstance::getProcessDefinitionVersion)
                        .orElse(null);
                log.error("procDefVersion:"+procDefVersion);
                bpm = BusinessConstant.getOpenRealBpmType(edit.getApplyInfo().getStoreType(),
                        edit.getApplyInfo().getBpmType(),
                        edit.getApplyInfo().getDistributorChannelCode(),procDefVersion);
            }else{
                 bpm = BusinessConstant.getOpenRealBpmType(edit.getApplyInfo().getStoreType(),
                        edit.getApplyInfo().getBpmType(),
                        edit.getApplyInfo().getDistributorChannelCode(),null);
            }

            if (bpm <= 0)
                throw new BusinessException("编辑开店申请失败，错误码[10007]！");

            WorkflowEnum we = WorkflowEnum.OpenStore;
            //流程标题(分公司名称-常规门店建设流程-门店名称-流程实例编码)
            String title = edit.getApplyInfo().getCompanyName() +
                    "-" + we.getDescription() +
                    "-" + edit.getApplyInfo().getStoreName() +
                    "-" + edit.getApplyInfo().getId();

            ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
            //门店类型(0:其他,1:专卖店,2:社区服务店)
            int storeChannelType = 0;
            if (cce.isSpecialStoreChannel(edit.getApplyInfo().getStoreChannelCode())) {
                storeChannelType = 1;
            }

            //从数据库中读取当前流程实例ID
            String processInstanceId = applyStore.getApplyInfo().getWfInstanceCode();
            HashMap<String, Object> params = BusinessConstant.getOpenBpmParams(
                    edit.getApplyInfo(),
                    null,
                    edit.getApplyInfo().getId(),
                    edit.getApplyInfo().getStoreType(),
                    bpm,
                    edit.getOpenInfo().getUsableArea(),
                    edit.getOpenInfo().getNeedTerminalBuild(),
                    storeChannelType
            );

            //存在流程实例
            if (StringUtils.isNotBlank(processInstanceId)) {
                storeActivitiService.finishTaskById(applyStore.getApplyInfo().getWfCurrentTaskId(), params);
            } else {
                storeActivitiService.startProcess(we.getCode(), edit.getApplyInfo().getCompanyId().toString(), title, params);
            }
        }

        //申请Id
        Integer applyId = edit.getApplyInfo().getId();
        try {
            //新增操作日志
            StringBuilder logContent = new StringBuilder();
            String storeName = StringUtils.isNotBlank(applyStore.getApplyInfo().getStoreName()) ? applyStore.getApplyInfo().getStoreName() : "空";
            if (!storeName.equals(edit.getApplyInfo().getStoreName())) {
                logContent.append("门店名称从【").append(storeName).append("】修改成【").append(edit.getApplyInfo().getStoreName()).append("】；");
            }
            String storeChannelName = StringUtils.isNotBlank(edit.getApplyInfo().getStoreChannelNameHistory()) ? edit.getApplyInfo().getStoreChannelNameHistory() : "空";
            if (!storeChannelName.equals(edit.getApplyInfo().getStoreChannelName())) {
                logContent.append("门店渠道从【").append(storeChannelName).append("】修改成【").append(edit.getApplyInfo().getStoreChannelName()).append("】；");
            }
            String storeTypeName = applyStore.getApplyInfo().getStoreType() == 2 ? "是" : "否";
            String storeTypeName2 = edit.getApplyInfo().getStoreType() == 2 ? "是" : "否";
            if (!storeTypeName.equals(storeTypeName2)) {
                logContent.append("是否虚拟门店【").append(storeTypeName).append("】修改成【").append(storeTypeName2).append("】；");
            }
            String needTerminalBuild = applyStore.getOpenInfo().getNeedTerminalBuild() == 1 ? "是" : "否";
            String needTerminalBuild2 = edit.getOpenInfo().getNeedTerminalBuild() == 1 ? "是" : "否";
            if (!needTerminalBuild.equals(needTerminalBuild2)) {
                logContent.append("是否需要终端建设【").append(needTerminalBuild).append("】修改成【").append(needTerminalBuild2).append("】；");
            }

            String content = logContent.toString();
            if (StringUtils.isBlank(content)) {
                logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreOpenUpdateApply, "修改开店申请信息"));
            } else {
                logContent = new StringBuilder();
                logContent.append("修改开店申请信息(").append(content).append(")");
                logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreOpenUpdateApply, logContent.toString()));
            }
        } catch (Exception ex) {
            log.error("ApplyStoreService.editApplyStoreOpen 操作日志插入失败，不影响主流程！", ex);
        }

        return applyId;
    }

    /************************ 闭店流程 ************************/

    /**
     * 获取门店闭店申请信息(s_apply_close)
     */
    public ApplyStoreClose getCloseInfo(Integer applyId, Integer id) {
        ApplyStoreClose closeInfo = applyStoreDao.getCloseInfo(applyId, id);
        if(StringUtils.isNotBlank(closeInfo.getNewStoreCode())){
            List<StoreSimpleInfo> storeSimpleInfos = orgClient.findByCodes(new FindStoreDto().setCodeList(Lists.newArrayList(closeInfo.getNewStoreCode()))).getData();
            if(CollectionUtils.isNotEmpty(storeSimpleInfos)){
                StoreSimpleInfo simpleInfo = storeSimpleInfos.get(0);
                closeInfo.setNewStoreName(simpleInfo.getName());
                closeInfo.setNewStoreStatus(simpleInfo.getStatus());
            }
        }
        return closeInfo;
    }

    /**
     * 添加闭店申请(s_apply_close)
     *
     * @return 新增成功则返回记录Id
     */
    public Integer addCloseInfo(@Valid CloseInfo add) {
        if (add == null || add.getApplyId() == null || add.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        //执行新增操作
        applyStoreDao.addCloseInfo(add);
        return add.getId();
    }

    /**
     * 编辑闭店申请(s_apply_close)
     */
    public Integer editCloseInfo(@Valid CloseInfo edit) {
        if (edit == null || edit.getId() == null || edit.getId() <= 0
                || edit.getApplyId() == null || edit.getApplyId() <= 0)
            throw new BusinessException("参数错误！");

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        return applyStoreDao.editCloseInfo(edit);
    }

    /**
     * 新增闭店申请
     *
     * @return 新增成功则返回申请Id(s_apply.id)
     */
    @GlobalTransactional
    public CloseInfoEditResult addApplyStoreClose(@Valid CloseInfoEdit add) {
        //参数判断
        if (add == null || add.getApplyInfo() == null || add.getCloseInfo() == null) {
            throw new BusinessException("参数错误");
        }
        if (StringUtils.isBlank(add.getApplyInfo().getDistributorChannel())) {
            throw new BusinessException("客户渠道编码不能为空");
        }

        //判断是否有未完成的申请
        ApplyInfoQuery applyInfoQuery = new ApplyInfoQuery();
        applyInfoQuery.setStoreId(add.getApplyInfo().getStoreId());
        applyInfoQuery.setApplyType(WorkflowEnum.CloseStore.getValue());
        applyInfoQuery.setApplyStatusList(Lists.newArrayList(0, 1));
        Integer someStoreCount = this.getApplyInfoCount(applyInfoQuery);
        if (someStoreCount > 0) {
            throw new BusinessException("该门店存在待提交或处理中的闭店申请,请先处理");
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        add.setOperatorId(user.get("userid"));
        add.setOperatorName(user.get("name"));

        //判断当前用户是否具有创建闭店申请的权限
        /*HashMap permissions = storeActivitiService.hasButton(user.get("userid"),
                WorkflowEnum.CloseStore.getCode());
        if (MapUtils.isEmpty(permissions)
                || !MapUtils.getBoolean(permissions, WorkflowEnum.CloseStore.getCode())) {
            throw new BusinessException("您没有创建闭店申请的权限");
        }*/

        int distributorType = 3;//客户渠道(1:KA,2:经销商,3:其他,4:米博/电商)
        ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
        cce = cce.getChannelCategoryEnum(add.getApplyInfo().getDistributorChannel(), 1);
        if (cce == ChannelCategoryEnum.KA) {
            distributorType = 1;
        } else if (cce == ChannelCategoryEnum.Distributor) {
            distributorType = 2;
        } else if (cce == ChannelCategoryEnum.DecorationStrategicBizCenter || cce == ChannelCategoryEnum.DecorationStrategic) {
            distributorType = 5;
        } else if (cce == ChannelCategoryEnum.MiBo || cce.isECommerceChannel(add.getApplyInfo().getDistributorChannel())) {
            distributorType = 4;
        }else if(cce==ChannelCategoryEnum.LF){
            distributorType = 6;
        }

        //判断是否有权限
        /*ActivitiBranchDTO closeBpm = storeActivitiService.getActivitiBranch(user.get("userid"), WorkflowEnum.CloseStore.getCode());
        if (closeBpm == null || closeBpm.getParamValues() == null || !closeBpm.getParamValues().contains(String.valueOf(distributorType)))
            throw new BusinessException("您的岗位角色权限不足，请选择其他门店");*/

        //申请Id
        Integer applyId = 0;
        //执行返回
        Integer result = 0;
        //新增申请信息
        result = addApplyInfo(add.getApplyInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("新增闭店申请失败，错误码[10001]");
        }
        //申请Id
        applyId = result;

        //新增闭店申请信息(申请Id)
        add.getCloseInfo().setApplyId(applyId);
        result = addCloseInfo(add.getCloseInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("新增闭店申请失败，错误码[10002]");
        }

        Integer applyCloseId = result;

        //新增附件信息
        if (add.getAttachmentInfo() != null && add.getAttachmentInfo().size() > 0) {
            result = attachmentService.batchInsertAttachmentByRefId(add.getAttachmentInfo(), applyCloseId);
          /*  if (result == null || result <= 0) {
                throw new BusinessException("新增闭店申请失败，错误码[10003]！");
            }*/
        }

        String processInstanceId = "";
        //创建工作流
        if (add.getActionType() == 1) {
            //非经销商变更产生的闭店申请才会调用org接口,反之在orgcenter中直接实现
            if (add.getApplyInfo().getDistributorChanged() == null) {
                add.getApplyInfo().setDistributorChanged(0);
            }

            //工作流申请的直接调用org接口
            if (add.getApplyInfo().getDistributorChanged() != 1) {
                //更新门店信息门店状态为“清退”
                Boolean succeed = this.updateStoreStatus(add.getApplyInfo().getStoreId(),
                        applyId.toString(), "闭店申请提交，门店状态调整为：清退！", StoreStatusEnum.Retreat);
                if (succeed == null || !succeed) {
                    throw new BusinessException("新增闭店申请失败，错误码[10005]");
                }
            }

            WorkflowEnum we = WorkflowEnum.CloseStore;
            //流程标题(分公司名称-常规门店建设流程-门店名称-流程实例编码)
            String title = add.getApplyInfo().getCompanyName() +
                    "-" + we.getDescription() +
                    "-" + add.getApplyInfo().getStoreName() +
                    "-" + applyId;


            //新流程都用新的流程参数获取方式
            HashMap<String, Object> params = BusinessConstant.getCloseBpmParamsVersion2(
                    null,
                    add.getApplyInfo(),
                    applyId,
                    distributorType,
                    add.getApplyInfo().getDistributorChanged(),
                    cce.needExcessapprove(add.getApplyInfo().getStoreChannelCode()),
                    add.getApplyInfo().getStoreChannelCode(),
                    add.getApplyInfo().getStoreType()
            );
            Task task = storeActivitiService.startProcess(we.getCode(), add.getApplyInfo().getCompanyId().toString(), title, params);
            if (task != null) {
                processInstanceId = task.getProcessInstanceId();
            }
        }

        try {
            //新增申请操作日志
            logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreCloseCreateApply,
                    "创建闭店申请，门店名称：" + add.getApplyInfo().getStoreName()));

            if (add.getActionType() == 1) {
                //新增提交申请日志
                logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreCloseSubmitApply, ""));
            }
        } catch (Exception ex) {
            log.error("ApplyStoreService.addApplyStoreClose 操作日志插入失败，不影响主流程！", ex);
        }

        CloseInfoEditResult editResult = new CloseInfoEditResult();
        editResult.setApplyId(applyId);
        editResult.setProcessInstanceId(processInstanceId);
        return editResult;
    }

    /**
     * 编辑闭店申请
     *
     * @return 编辑成功则返回申请Id(s_apply.id)
     */
    @GlobalTransactional
    public Integer editApplyStoreClose(@Valid CloseInfoEdit edit) {
        //参数判断
        if (edit == null || edit.getApplyInfo() == null || edit.getCloseInfo() == null
                || edit.getApplyInfo().getId() == null || edit.getApplyInfo().getId() <= 0
                || edit.getCloseInfo().getId() == null || edit.getCloseInfo().getId() <= 0) {
            throw new BusinessException("参数错误！");
        }
        if (StringUtils.isBlank(edit.getApplyInfo().getDistributorChannel())) {
            throw new BusinessException("参数错误，客户渠道编码不能为空！");
        }

        //获取闭店申请信息
        ApplyStoreDetails applyInfo = getApplyStoreDetails(edit.getApplyInfo().getId(),
                null, null, true, true);
        if (applyInfo == null || applyInfo.getApplyInfo() == null || applyInfo.getApplyInfo().getId() == null) {
            throw new BusinessException("闭店申请信息不存在！");
        }

        //状态判断
        if (applyInfo.getApplyInfo().getApplyStatus() == 0 && edit.getActionType() == 1) {
            edit.getApplyInfo().setApplyStatus(1);
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        //执行返回
        Integer result = 0;

        //编辑申请信息
        result = editApplyInfo(edit.getApplyInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("编辑闭店申请失败，错误码[10001]！");
        }

        //编辑闭店申请信息
        result = editCloseInfo(edit.getCloseInfo());
        if (result == null || result <= 0) {
            throw new BusinessException("编辑闭店申请失败，错误码[10002]！");
        }

        //批量删除附件信息
        List<Integer> refIds = applyInfo.getApplyAttachments()
                .stream()
                .map(AttachmentInfo::getId)
                .distinct()
                .collect(Collectors.toList());
        if (refIds.size() > 0) {
            attachmentService.batchDelAttachment(refIds, AttachmentEnum.NULL, edit.getCloseInfo().getId());
        }

        //新增附件信息
        if (edit.getAttachmentInfo() != null && edit.getAttachmentInfo().size() > 0) {
            result = attachmentService.batchInsertAttachmentByRefId(edit.getAttachmentInfo(), edit.getCloseInfo().getId());
           /* if (result == null || result <= 0) {
                throw new BusinessException("编辑闭店申请失败，错误码[10003]！");
            }*/
        }

        //创建工作流
        if (edit.getActionType() == 1) {
            int distributorType = 3;//客户渠道(1:KA,2:经销商,3:其他,4:米博/电商)
            ChannelCategoryEnum cce = ChannelCategoryEnum.NULL;
            cce = cce.getChannelCategoryEnum(edit.getApplyInfo().getDistributorChannel(), 1);
            if (cce == ChannelCategoryEnum.KA) {
                distributorType = 1;
            } else if (cce == ChannelCategoryEnum.Distributor) {
                distributorType = 2;
            } else if (cce == ChannelCategoryEnum.DecorationStrategicBizCenter || cce == ChannelCategoryEnum.DecorationStrategic) {
                distributorType = 5;
            } else if (cce == ChannelCategoryEnum.MiBo || cce.isECommerceChannel(edit.getApplyInfo().getDistributorChannel())) {
                distributorType = 4;
            }else if(cce==ChannelCategoryEnum.LF){
                distributorType = 6;
            }

            WorkflowEnum we = WorkflowEnum.CloseStore;
            //流程标题(分公司名称-常规门店建设流程-门店名称-流程实例编码)
            String title = edit.getApplyInfo().getCompanyName() +
                    "-" + we.getDescription() +
                    "-" + edit.getApplyInfo().getStoreName() +
                    "-" + edit.getApplyInfo().getId();

            //从数据库中读取当前流程实例ID
            String processInstanceId = applyInfo.getApplyInfo().getWfInstanceCode();
            Integer procDefVersion = Optional.ofNullable(processInstanceId)
                    .filter(StringUtils::isNotBlank)
                    .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                    .map(HistoricProcessInstance::getProcessDefinitionVersion)
                    .orElse(null);
            HashMap<String, Object> params =null;
            if(Objects.nonNull(procDefVersion)&& procDefVersion<=62){
                params = BusinessConstant.getCloseBpmParams(
                        null,
                        edit.getApplyInfo(),
                        edit.getApplyInfo().getId(),
                        distributorType,
                        edit.getApplyInfo().getDistributorChanged(),
                        cce.needExcessapprove(edit.getApplyInfo().getStoreChannelCode()),
                        edit.getApplyInfo().getStoreChannelCode(),
                        edit.getApplyInfo().getStoreType()
                );
            }else{
                params = BusinessConstant.getCloseBpmParamsVersion2(
                        null,
                        edit.getApplyInfo(),
                        edit.getApplyInfo().getId(),
                        distributorType,
                        edit.getApplyInfo().getDistributorChanged(),
                        cce.needExcessapprove(edit.getApplyInfo().getStoreChannelCode()),
                        edit.getApplyInfo().getStoreChannelCode(),
                        edit.getApplyInfo().getStoreType()
                );
            }
            //存在流程实例
            if (StringUtils.isNotBlank(processInstanceId)) {
                storeActivitiService.finishTaskById(applyInfo.getApplyInfo().getWfCurrentTaskId(), params);
            } else {
                //更新门店信息门店状态为“清退”
                Boolean succeed = this.updateStoreStatus(edit.getApplyInfo().getStoreId(),
                        edit.getApplyInfo().getId().toString(), "闭店申请提交，门店状态调整为：清退！", StoreStatusEnum.Retreat);
                if (succeed == null || !succeed) {
                    throw new BusinessException("编辑闭店申请失败，错误码[10005]！");
                }

                storeActivitiService.startProcess(we.getCode(), edit.getApplyInfo().getCompanyId().toString(), title, params);
            }
        }

        //申请Id
        Integer applyId = edit.getApplyInfo().getId();
        try {
            //新增操作日志
            String remark = "修改闭店申请信息";
            String storeName = StringUtils.isNotBlank(applyInfo.getApplyInfo().getStoreName()) ? applyInfo.getApplyInfo().getStoreName() : "空";
            if (!storeName.equals(edit.getApplyInfo().getStoreName())) {
                remark += "(门店名称从【" + storeName + "】修改成【" + edit.getApplyInfo().getStoreName() + "】)";
            }

            logService.addOpLog(new OpLogInfo("s_apply", applyId, OpLogEnum.StoreCloseUpdateApply, remark));
        } catch (Exception ex) {
            log.error("ApplyStoreService.editApplyStoreClose 操作日志插入失败，不影响主流程！", ex);
        }
        return applyId;
    }

    /**
     * 根据不同版本获取流程参数
     */
    private HashMap<String, Object> getCloseParams(ApplyInfo applyInfo,int distributorType,ChannelCategoryEnum cce,String processInstanceId){
        Integer procDefVersion = Optional.ofNullable(processInstanceId)
                .filter(StringUtils::isNotBlank)
                .map(pid -> historyService.createHistoricProcessInstanceQuery().processInstanceId(pid).singleResult())
                .map(HistoricProcessInstance::getProcessDefinitionVersion)
                .orElse(null);
        if(Objects.nonNull(procDefVersion)&& procDefVersion<=62){
            return BusinessConstant.getCloseBpmParams(
                    null,
                    applyInfo,
                    applyInfo.getId(),
                    distributorType,
                    applyInfo.getDistributorChanged(),
                    cce.needExcessapprove(applyInfo.getStoreChannelCode()),
                    applyInfo.getStoreChannelCode(),
                    applyInfo.getStoreType()
            );
        }else{
            return BusinessConstant.getCloseBpmParamsVersion2(
                    null,
                    applyInfo,
                    applyInfo.getId(),
                    distributorType,
                    applyInfo.getDistributorChanged(),
                    cce.needExcessapprove(applyInfo.getStoreChannelCode()),
                    applyInfo.getStoreChannelCode(),
                    applyInfo.getStoreType()
            );
        }
    }

    /**
     * 更新开店申请信息同步状态
     *
     * @param applyId    申请记录Id
     * @param syncStatus 同步状态
     */
    public Integer updateSyncStatus(Integer applyId,
                                    Integer syncStatus,
                                    Integer storeId,
                                    String storeCode) {
        Map<String, String> user = CommonUtils.getAuthorUser();
        applyStoreTopKitchenService.lambdaUpdate().eq(SApplyOpenTopKitchen::getApplyId, applyId)
                .set(SApplyOpenTopKitchen::getStoreId, storeId).update();
        return applyStoreDao.updateSyncStatus(applyId, syncStatus, storeId, storeCode, user.get("userid"), user.get("name"));
    }

    /**
     * 更新门店状态(orgcenter.t_store)
     *
     * @param storeId           门店ID(t_store.id)
     * @param processInstanceId 流程实例ID
     * @param remark            操作备注
     * @param status            门店状态(0:禁用; 1:启用; 2:筹备; 3:清退)
     */
    private Boolean updateStoreStatus(Integer storeId,
                                      String processInstanceId,
                                      String remark,
                                      StoreStatusEnum status) {
        try {
            UpdateStoreStatusDto edit = new UpdateStoreStatusDto();
            edit.setStoreId(Long.valueOf(storeId));
            edit.setProcessInstanceId(processInstanceId);
            edit.setRemark(remark);
            edit.setStatus(status.getStatus().toString());

            Map<String, String> user = CommonUtils.getAuthorUser();
            edit.setOperatorId(user.get("userid"));
            edit.setOperatorName(user.get("name"));

            openPlanService.updatePlanStoreStatus(status, edit.getStoreId());
            Result result = orgClient.updateStoreStatus(edit);
            if (result == null)
                return false;
            return result.getSuccess();
        } catch (Exception ex) {
            throw new BusinessException("更新门店状态失败！", ex);
        }
    }

    /**
     * 更新门店状态(orgcenter.t_store)
     *
     * @param storeId           门店ID(t_store.id)
     * @param processInstanceId 流程实例ID
     * @param logInfo           操作备注
     * @param status            门店状态(0:禁用; 1:启用; 2:筹备; 3:清退)
     */
    private Boolean updateStoreStatus(Integer storeId,
                                      String processInstanceId,
                                      OpLogInfo logInfo,
                                      StoreStatusEnum status) {
        try {
            UpdateStoreStatusDto edit = new UpdateStoreStatusDto();
            edit.setStoreId(Long.valueOf(storeId));
            edit.setProcessInstanceId(processInstanceId);
            edit.setStatus(status.getStatus().toString());
            if (logInfo != null) {
                edit.setRemark(logInfo.getRemark());
                edit.setOperatorId(logInfo.getOperatorId());
                edit.setOperatorName(logInfo.getOperatorUserName());
            }

            Map<String, String> user = CommonUtils.getAuthorUser();
            if (StringUtils.isBlank(edit.getOperatorId())) {
                edit.setOperatorId(user.get("userid"));
            }
            if (StringUtils.isBlank(edit.getOperatorName())) {
                edit.setOperatorName(user.get("name"));
            }

            openPlanService.updatePlanStoreStatus(status, edit.getStoreId());
            Result result = orgClient.updateStoreStatus(edit);
            if (result == null)
                return false;
            if(!result.getSuccess()){
                throw new BusinessException("更新门店状态失败！");
            }
            return result.getSuccess();
        } catch (Exception ex) {
            throw new BusinessException("更新门店状态失败！", ex);
        }
    }

    private Boolean updateStoreStatus(UpdateStoreStatusDto edit) {
        try {
            StoreStatusEnum byStatus = StoreStatusEnum.getByStatus(Integer.valueOf(edit.getStatus()));
            openPlanService.updatePlanStoreStatus(byStatus, edit.getStoreId());
            Result result = orgClient.updateStoreStatus(edit);
            if (result == null)
                return false;
            return result.getSuccess();
        } catch (Exception ex) {
            throw new BusinessException("更新门店状态失败！", ex);
        }
    }

    /**
     * 反更新申请记录流程实例编码
     */
    public Integer updateInstanceCode(UpdateInstanceCode request) {
        if (request == null)
            return 0;

        Map<String, String> user = CommonUtils.getAuthorUser();
        request.setOperatorId(user.get("userid"));
        request.setOperatorName(user.get("name"));

        return applyStoreDao.updateInstanceCode(request);
    }

    public Integer editStoreApply(StoreApplyEdit edit) {
        if (edit == null || edit.getApplyId() == null || edit.getApplyId() <= 0)
            return 0;

        Map<String, String> user = CommonUtils.getAuthorUser();
        edit.setOperatorId(user.get("userid"));
        edit.setOperatorName(user.get("name"));

        return applyStoreDao.editStoreApply(edit);
    }

    public ApplyStore getApplyStoreByStoreIdAndType(Integer storeId, Integer applyType) {
        return applyStoreDao.getApplyStoreByStoreIdAndType(storeId, applyType);
    }

    public void updateModifiedDate(Integer id) {
        applyStoreDao.updateModifiedDate(id);
    }

    public ApplyStoreOpen getOpenInfoByStoreId(Long storeId) {
        return applyStoreDao.getOpenInfoByStoreId(storeId);
    }

    public int setPlanId(Integer id, Long planId) {
        return applyStoreDao.setPlanId(id, planId);
    }

    public int removePlanIdByPlandIds(List<String> idList) {
        int result = 0;
        if (idList != null && !idList.isEmpty()) {
            result = applyStoreDao.removePlanIdByPlandIds(idList);
        }
        return result;
    }

    public List<ApplyStore> getApplyInfoByStoreId(Long storeId, Integer type) {
        return applyStoreDao.getApplyInfoByStoreId(storeId, type);
    }

    public ResultOfinventDto getDrpPrototype(Long storeOrgId, Long storeId) {
        ResultOfinventDto result = null;
        if (storeOrgId == null && storeId == null) {
            throw new BusinessException("storeOrgId和storeId不可同时为空");
        }
        if (storeOrgId == null) {
            Result<FindStoreByIdOutDto> storeResult = orgClient.findStoreById(storeId.intValue());
            if (!storeResult.getSuccess() || storeResult.getData() == null) {
                throw new BusinessException("未找到门店信息，确认storeId是否正确");
            }
            storeOrgId = storeResult.getData().getOrgId();
        }
        ResultOfinventDto ofinventDtoResult = new ResultOfinventDto();
        Result<ResultOfinventDto> resultOfinventDtoResult = omsClient.prototypeQuery(storeOrgId, "");
        Result<Long> protoCount = productClient.getProtoCount(storeOrgId, null);
        if (resultOfinventDtoResult.getSuccess() && resultOfinventDtoResult.getData() != null) {
            ResultOfinventDto resultData = resultOfinventDtoResult.getData();
            ofinventDtoResult.setData(resultData.getData());
            ofinventDtoResult.setStatus(resultData.isStatus());
            ofinventDtoResult.setTotal(resultData.getTotal());
            ofinventDtoResult.setSystem(resultData.getSystem());
            ofinventDtoResult.setMspProtoCount(protoCount.getSuccess() ? protoCount.getData() : Long.valueOf(0));
        }
        return ofinventDtoResult;
    }

    public Boolean clearProto(String procInstId) {
        Boolean result = false;
        ApplyStore applyInfo = getApplyInfo(null, procInstId, null);
        if (applyInfo != null) {
            if (Integer.valueOf(2).equals(applyInfo.getApplyType())) {
                if (StringUtils.isBlank(applyInfo.getStoreCode())) {
                    throw new BusinessException("参数错误，清除门店样机时未找到门店信息");
                }
                Result<Boolean> booleanResult = productClient.clearProtoByStore(null, applyInfo.getStoreCode());
                if (booleanResult == null || !booleanResult.getSuccess() || !booleanResult.getData()) {
                    throw new BusinessException("中台样机清除失败，请联系管理员");
                }
                result = booleanResult.getData();
            }
        } else {
            throw new BusinessException("下样中台样机时未找到闭店申请信息");
        }
        return result;
    }

    public int resetPlanId(Long oldPlanId, Long newPlanId) {
        return applyStoreDao.resetPlanId(oldPlanId, newPlanId);
    }

    public List<ExportApplyStoreDto> getAsynExportList(ApplyStoreQuery queryParam, PageInfo<ExportApplyStoreDto> pageInfo) {
        List<ExportApplyStoreDto> list = applyStoreDao.getAsynExportList(queryParam, pageInfo);
        if (list.size() > 0) {
        /*  Set<String> storeCodeSet = list.stream()
                  .map(ExportApplyStoreDto::getStoreCode)
                  .collect(Collectors.toSet());*/
            Set<String> storeCodeSet = Sets.newHashSet();
            Set<String> procInstSet = Sets.newHashSet();
            HashSet<Long> distributorSet = Sets.newHashSet();
            HashSet<Integer> changeIds = Sets.newHashSet();
            HashSet<Integer> explosiveIds = Sets.newHashSet();
            HashSet<String> explosiveProcInstIds = Sets.newHashSet();
            Integer int0 = Integer.valueOf(0);
            Integer openType = Integer.valueOf(1);
            Integer closeType = Integer.valueOf(2);
            for (ExportApplyStoreDto dto : list) {
                storeCodeSet.add(dto.getStoreCode());
                procInstSet.add(dto.getWfInstanceCode());
                distributorSet.add(dto.getDistributorId());
                changeIds.add(dto.getId());
                if (WorkflowEnum.StoreOpenExplosive.getValue().equals(dto.getApplyType())) {
                    explosiveIds.add(dto.getId());
                    Optional.ofNullable(dto.getWfInstanceCode())
                            .filter(StringUtils::isNotBlank)
                            .ifPresent(explosiveProcInstIds::add);
                }
            }
            Result<List<StoreSimpleInfo>> byCodesResult = orgClient.findByCodes(new FindStoreDto().setCodeList(Lists.newLinkedList(storeCodeSet)));
            if (byCodesResult != null && byCodesResult.getSuccess() && byCodesResult.getData() != null) {
                Map<String, StoreSimpleInfo> storeSimpleInfoMap = byCodesResult.getData()
                        .stream()
                        .collect(Collectors.toMap(StoreSimpleInfo::getCode, dto -> dto));
                Map<String, String> characterMap = dictService.list("store_character").stream()
                        .collect(Collectors.toMap(Dict::getValueCode, Dict::getValueName));
                List<Dict> subChannelCodes = dictService.list("sub_channel_code");
                ImmutableMap<String, Map<String, DictDto>> channelSubMap = ImmutableMap.of(
                        BusinessConstant.ChannelSubInfo.L018.getChannelCode(), dictService.getSubChannelCodeList(BusinessConstant.ChannelSubInfo.L018.getChannelCode()).stream().collect(Collectors.toMap(d-> Optional.ofNullable(d.getValueCode()).orElse(""), Function.identity() )),
                        BusinessConstant.ChannelSubInfo.L025.getChannelCode(), dictService.getSubChannelCodeList(BusinessConstant.ChannelSubInfo.L025.getChannelCode()).stream().collect(Collectors.toMap(DictDto::getValueCode, Function.identity())),
                        BusinessConstant.ChannelSubInfo.L028.getChannelCode(), dictService.getSubChannelCodeList(BusinessConstant.ChannelSubInfo.L028.getChannelCode()).stream().collect(Collectors.toMap(DictDto::getValueCode, Function.identity()))
                        );
                Map<Long, Dict> subChannelIdMap = subChannelCodes.stream().collect(Collectors.toMap(Dict::getId, dto -> dto));
                DictDto defaultDict = subChannelCodes.stream()
                        .filter(d -> StringUtils.isBlank(d.getValueCode()))
                        .map(DictMapper.INSTANCE::entity2Dto)
                        .findAny()
                        .orElse(new DictDto().setValueCode("").setValueName("缺省"));
                Result<List<DicDto>> closeReasonResult = systemClient.getDicByCodes(new DicDto().setTypeCodes(Arrays.asList("close_reason")));
                Map<String, String> closeReasonMap = Maps.newHashMap();
                if (closeReasonResult != null && closeReasonResult.getSuccess() && closeReasonResult.getData() != null) {
                    closeReasonMap = closeReasonResult.getData()
                            .stream()
                            .collect(Collectors.toMap(DicDto::getValueCode, DicDto::getValueName));
                }
                Result<List<DicDto>> regionResult = systemClient.getDicByCodes(new DicDto().setTypeCodes(Arrays.asList("所属大区")));
                Map<Long, String> regionMap = Maps.newHashMap();
                if (regionResult != null && regionResult.getSuccess() && regionResult.getData() != null) {
                    regionMap = regionResult.getData()
                            .stream()
                            .collect(Collectors.toMap(DicDto::getId, DicDto::getValueName));
                }

                //查询门店渠道
                Result<List<ChannelCategoryEntity>> channelResponse = orgClient.findChannelByTypeCode2(null, "store_type", null, null, null);
                Map<String, ChannelCategoryEntity> channelMap = new HashMap<>();
                if (channelResponse.getSuccess() && channelResponse.getData() != null && !channelResponse.getData().isEmpty()) {
                    for (ChannelCategoryEntity channel : channelResponse.getData()) {
                        channelMap.put(channel.getCode(), channel);
                    }
                }
                //查询客户渠道
                Map<Long, DistributorEntity> distributorEntityMap = Maps.newHashMap();
                if (!distributorSet.isEmpty()) {
                    Result<List<DistributorEntity>> distributorResult = orgClient.findDistributorInfoByIds
                            (new DistributorEntity().setIds(Lists.newArrayList(distributorSet)));
                    if (distributorResult != null && distributorResult.getSuccess() && distributorResult.getData() != null) {
                        distributorEntityMap = distributorResult.getData()
                                .stream()
                                .collect(Collectors.toMap(DistributorEntity::getId, dto -> dto));

                    }
                }
                //查询闭店申请到达财务时间
                procInstSet.remove(null);
                Map<String, Date> closeArriveFinanceDateMap = Maps.newHashMap();
                if (!procInstSet.isEmpty()) {
                    closeArriveFinanceDateMap = applyStoreDao.getCloseArriveFinanceDate(procInstSet)
                            .stream()
                            .filter(dto -> dto.getCloseArriveFinanceDate() != null)
                            .collect(Collectors.toMap(ExportApplyStoreDto::getWfInstanceCode, ExportApplyStoreDto::getCloseArriveFinanceDate));
                }

                //查询终端建设验收制作时间(常规115大型145)
                Map<String, Date> terminalAcceptDateMap = Maps.newHashMap();
                if(!storeCodeSet.isEmpty()){
                    terminalAcceptDateMap = applyStoreDao.terminalAcceptDate(acceptTaskKeys, storeCodeSet)
                            .stream().filter(dto -> Objects.nonNull(dto.getTerminalAcceptDate()))
                            .collect(Collectors.toMap(ExportApplyStoreDto::getStoreCode, ExportApplyStoreDto::getTerminalAcceptDate));
                }


                //查询变更信息
                Map<Integer, TaskDTO> idDescriptionMap = Maps.newHashMap();
                if (changeIds.size() > 0) {
                    idDescriptionMap
                            = changeService.getAreaChangeLastAuditRemark(changeIds).stream().collect(Collectors.toMap(t -> t.getBusinessId().intValue(), t -> t));
                }

                Map<Integer, List<ApplyChange>> changeMap = changeService.lambdaQuery()
                        .in(ApplyChange::getApplyId, changeIds)
                        .eq(ApplyChange::getIsDeleted, 0)
                        .list()
                        .stream().collect(Collectors.groupingBy(ApplyChange::getApplyId));

                for (ExportApplyStoreDto dto : list) {
                    Map<String, DictDto> subChannelMap = channelSubMap.getOrDefault(dto.getStoreChannelCode(),Map.of("",defaultDict));
                    dto.setStoreSubChannelName(getFullSubStoreChannelName(subChannelIdMap, subChannelMap, dto.getStoreSubChannelCode()));
                    dto.setApplyTypeName(WorkflowEnum.getWorkflowEnumByValue(dto.getApplyType()).getDescription());
                    dto.setApplyStatusName(BusinessConstant.getApplyStatusName(dto.getApplyStatus()));
                    dto.setCharacter(characterMap.get(dto.getCharacter()));
                    dto.setRegionName(regionMap.get(dto.getRegionId()));
                    dto.setCloseArriveFinanceDate(closeArriveFinanceDateMap.get(dto.getWfInstanceCode()));
                    dto.setTerminalAcceptDate(terminalAcceptDateMap.get(dto.getStoreCode()));
                    DistributorEntity distributorEntity = distributorEntityMap.get(dto.getDistributorId());
                    if (distributorEntity != null) {
                        dto.setDistributorChannelShowName(Joiner.on("/")
                                .useForNull("")
                                .join(distributorEntity.getChannelCategoryName(), distributorEntity.getChannelSubdivideName()));
                    }
                    ChannelCategoryEntity categoryEntity = channelMap.get(dto.getStoreChannelCode());
                    if (categoryEntity != null) {
                        dto.setStoreChannelName(categoryEntity.getName());
                    }


                    if (storeSimpleInfoMap.containsKey(dto.getStoreCode())) {
                        StoreSimpleInfo info = storeSimpleInfoMap.get(dto.getStoreCode());
                        dto.setDistributorChannelShowName(Joiner.on("/")
                                .useForNull("")
                                .join(info.getChannelCategoryName(), info.getChannelSubdivideName()));
                        dto.setStoreChannelName(info.getStoreTypeName());

                        dto.setWholeUsableArea(info.getWholeUsableArea());
                        dto.setEpsUsableArea(info.getWholeUsableArea());
                        dto.setRegionName(info.getAreaName());
                        if (openType.equals(info.getDecorateStatus())) {
                            dto.setDecorateStatusName("装修中");
                        } else if (closeType.equals(info.getDecorateStatus())) {
                            dto.setDecorateStatusName("已验收");
                        }
                        if (closeType.equals(dto.getApplyType())
                                || WorkflowEnum.ChangeStore.getValue().equals(dto.getApplyType())
                                || WorkflowEnum.ReopenStore.getValue().equals(dto.getApplyType())
                                || WorkflowEnum.RedecorateStore.getValue().equals(dto.getApplyType())
                        ) {
                            dto.setStoreSubChannelName(getFullSubStoreChannelName(subChannelIdMap, channelSubMap.getOrDefault(dto.getStoreChannelCode(),Map.of("",defaultDict)), info.getStoreSubChannelCode()));
                            dto.setOutsideStoreName(info.getOutsideStoreName());

                            dto.setPlanCode(info.getPlanCode());
                            if (int0.equals(info.getIsVirtual())) {
                                dto.setStoreTypeName("实体门店");
                            } else if (openType.equals(info.getIsVirtual())) {
                                dto.setStoreTypeName("虚拟门店");
                            }
                            if (int0.equals(info.getNeedTerminalBuild())) {
                                dto.setNeedTerminalBuildName("否");
                            } else if (openType.equals(info.getNeedTerminalBuild())) {
                                dto.setNeedTerminalBuildName("是");
                            }
                            if (closeReasonMap.containsKey(dto.getCloseReason())) {
                                dto.setCloseReason(closeReasonMap.get(dto.getCloseReason()));
                            }
                            dto.setProvinceName(info.getProvicenName());
                            dto.setCityName(info.getCityName());
                            dto.setCountyName(info.getCountyName());
                            dto.setAddress(info.getAddress());
                            dto.setAddress2(info.getAddress2());
                            dto.setStoreMarketGrade(info.getStoreMarketGrade());
                            dto.setManagerName(info.getManagerName());
                            dto.setManagerPhone(info.getManagerPhone());
                            dto.setMarketCapacity(info.getMarketCapacity());
                            dto.setPopulation(info.getPopulation());
                            dto.setTopKitchens(info.getTopKitchens());
                            dto.setStoreBizType(info.getStoreBizType());
                            if (openType.equals(info.getPropertyRight())) {
                                dto.setPropertyRightName("自用");
                            } else if (closeType.equals(info.getPropertyRight())) {
                                dto.setPropertyRightName("租用");
                            }
                            dto.setLeaseTerm(info.getLeaseTerm());
                            dto.setAnnualRent(info.getAnnualRent());
                            dto.setEstimatedAnnualSales(info.getEstimatedAnnualSales());
                            dto.setUsableArea(info.getUsableArea());
                            dto.setStoreLevel(info.getStoreLevel());
                            if (Boolean.TRUE.equals(info.getIsUnmanned())) {
                                dto.setIsUnmanned("无人门店");
                            } else if (Boolean.FALSE.equals(info.getIsUnmanned())) {
                                dto.setIsUnmanned("有人门店");
                            }
                            if (int0.equals(info.getIfDecorationStore())) {
                                dto.setIfDecorationStore("否");
                            } else if (openType.equals(info.getIfDecorationStore())) {
                                dto.setIfDecorationStore("是");
                            }
                            dto.setCharacter(characterMap.get(info.getCharacter()));
                            if (int0.equals(info.getIsFrontage())) {
                                dto.setIsFrontageName("否");
                            } else if (openType.equals(info.getIsFrontage())) {
                                dto.setIsFrontageName("是");
                            }
                        } else if (WorkflowEnum.StoreOpenExplosive.getValue().equals(dto.getApplyType())) {
                            dto.setProvinceName(info.getProvicenName());
                            dto.setCityName(info.getCityName());
                            dto.setCountyName(info.getCountyName());
                            dto.setAddress(info.getAddress());
                            dto.setAddress2(info.getAddress2());
                            if (int0.equals(info.getNeedTerminalBuild())) {
                                dto.setNeedTerminalBuildName("否");
                            } else if (openType.equals(info.getNeedTerminalBuild())) {
                                dto.setNeedTerminalBuildName("是");
                            }
                        }
                    }
                    if (WorkflowEnum.ChangeStore.getValue().equals(dto.getApplyType())) {
                        setChangeFields(dto, changeMap.get(dto.getId()), idDescriptionMap.get(dto.getId()));
                    }
                }
            }
            explosiveIds.remove(null);
            if (!explosiveIds.isEmpty()) {
                List<OpenInfo> openInfos = explosiveService.getOpenInfoByExplosiveIds(Lists.newArrayList(explosiveIds));
                //040节点通过时间
                Map<String, Date> task040Map = fotileTaskService.getTaskByProcInstIdsAndTaskKey("integrate-audit-040", explosiveProcInstIds)
                        .stream()
                        .filter(t -> Objects.nonNull(t.getEndTime()))
                        .filter(t->StringUtils.equals("通过",t.getCategory()))
                        .collect(Collectors.toMap(TaskDTO::getProcInstId, TaskDTO::getEndTime, (v1, v2) -> v1));
                //070节点通过时间
                Map<String, Date> task070Map = fotileTaskService.getTaskByProcInstIdsAndTaskKey("integrate-audit-070", explosiveProcInstIds)
                        .stream()
                        .filter(t -> Objects.nonNull(t.getEndTime()))
                        .filter(t->StringUtils.equals("提交",t.getCategory()))
                        .collect(Collectors.toMap(TaskDTO::getProcInstId, TaskDTO::getEndTime, (v1, v2) -> v1));
                //开业爆量130节点通过时间
                Map<String, Date> task130Map = fotileTaskService.getTaskByProcInstIdsAndTaskKey("integrate-audit-130", explosiveProcInstIds)
                        .stream()
                        .filter(t -> Objects.nonNull(t.getEndTime()))
                        .filter(t->StringUtils.equals("提交",t.getCategory()))
                        .collect(Collectors.toMap(TaskDTO::getProcInstId, TaskDTO::getEndTime,  BinaryOperator.maxBy(Date::compareTo)));
                if (!openInfos.isEmpty()) {
                    Map<Integer, OpenInfo> openExplosiveMap = openInfos.stream().collect(Collectors.toMap(OpenInfo::getExplosiveApplyId, o -> o));
                    for (ExportApplyStoreDto exportApplyStoreDto : list) {
                        if (WorkflowEnum.StoreOpenExplosive.getValue().equals(exportApplyStoreDto.getApplyType())) {
                            OpenInfo info = openExplosiveMap.get(exportApplyStoreDto.getId());
                            if (info != null) {
                                exportApplyStoreDto.setProvinceName(info.getProvinceName());
                                exportApplyStoreDto.setCityName(info.getCityName());
                                exportApplyStoreDto.setCountyName(info.getCountyName());
                                exportApplyStoreDto.setAddress(info.getAddress());
                                exportApplyStoreDto.setAddress2(info.getAddress2());
                                if (int0.equals(info.getNeedTerminalBuild())) {
                                    exportApplyStoreDto.setNeedTerminalBuildName("否");
                                } else if (openType.equals(info.getNeedTerminalBuild())) {
                                    exportApplyStoreDto.setNeedTerminalBuildName("是");
                                }
                            }
                            if(task040Map.containsKey(exportApplyStoreDto.getWfInstanceCode())){
                                exportApplyStoreDto.setTask040EndTime(task040Map.get(exportApplyStoreDto.getWfInstanceCode()));
                            }
                            if(task070Map.containsKey(exportApplyStoreDto.getWfInstanceCode())){
                                exportApplyStoreDto.setTask070EndTime(task070Map.get(exportApplyStoreDto.getWfInstanceCode()));
                            }
                            if(task130Map.containsKey(exportApplyStoreDto.getWfInstanceCode())){
                                exportApplyStoreDto.setIntegrateTask130EndTime(task130Map.get(exportApplyStoreDto.getWfInstanceCode()));
                            }
                        }
                    }
                }
            }
        }
        return list;
    }


    private void setChangeFields(ExportApplyStoreDto dto, List<ApplyChange> changeList, TaskDTO taskDTO) {
        if (dto != null && changeList != null && !changeList.isEmpty()) {
            for (ApplyChange applyChange : changeList) {
                if (StoreChangeField.IS_VIRTUAL.getField().equals(applyChange.getField())) {
                    dto.setChangeStoreTypeName("0".equals(applyChange.getNewValue()) ? "实体门店" : "虚拟门店");
                } else if (StoreChangeField.NEED_TERMINAL_BUILD.getField().equals(applyChange.getField())) {
                    dto.setChangeNeedTerminalBuildName("0".equals(applyChange.getNewValue()) ? "否" : "是");
                } else if (StoreChangeField.PROVICEN_ID.getField().equals(applyChange.getField())) {
                    dto.setChangeProvinceName(applyChange.getNewValueName());
                } else if (StoreChangeField.CITY_ID.getField().equals(applyChange.getField())) {
                    dto.setChangeCityName(applyChange.getNewValueName());
                } else if (StoreChangeField.COUNTY_ID.getField().equals(applyChange.getField())) {
                    dto.setChangeCountyName(applyChange.getNewValueName());
                } else if (StoreChangeField.ADDRESS.getField().equals(applyChange.getField())) {
                    dto.setChangeAddress(applyChange.getNewValue());
                } else if (StoreChangeField.NAME.getField().equals(applyChange.getField())) {
                    dto.setChangeStoreName(applyChange.getNewValue());
                } else if (StoreChangeField.STORE_BIZ_TYPE.getField().equals(applyChange.getField())) {
                    dto.setChangeStoreBizType(applyChange.getNewValue());
                } else if (StoreChangeField.STORE_CHANNEL_CODE.getField().equals(applyChange.getField())) {
                    dto.setChangeStoreChannelName(applyChange.getNewValueName());
                } else if (StoreChangeField.STORE_SUB_CHANNEL_CODE.getField().equals(applyChange.getField())) {
                    dto.setChangeStoreSubChannelName(applyChange.getNewValueName());
                } else if (StoreChangeField.WHOLE_USABLE_AREA.getField().equals(applyChange.getField())) {
                    dto.setWholeUsableAreaRemark(applyChange.getRemark());
                    if (taskDTO != null && StringUtils.isNotBlank(taskDTO.getDescription())) {
                        dto.setWholeUsableAreaAuditRemark(taskDTO.getDescription());
                    }
                    try {
                        if (StringUtils.isNotBlank(applyChange.getNewValue())) {
                            dto.setNewWholeUsableArea(new BigDecimal(applyChange.getNewValue()));
                            dto.setWholeUsableArea(new BigDecimal(applyChange.getOriginalValue()));
                        }
                    } catch (Exception e) {
                        log.error("导出申请门店信息，转换可用面积异常", e);
                    }
                }
            }
        }
    }


    private String getFullSubStoreChannelName(Map<Long, Dict> subChannelIdMap, Map<String, DictDto> subChannelMap, String storeSubChannelCode) {
        StringBuffer subStoreChannelName = new StringBuffer("");
        if (Objects.isNull(storeSubChannelCode)) {
            //兼容null
            storeSubChannelCode = "";
        }
        if(subChannelMap==null){
            return subStoreChannelName.toString();
        }
        if (subChannelMap.containsKey(storeSubChannelCode)) {
            DictDto dict = subChannelMap.get(storeSubChannelCode);
            subStoreChannelName.insert(0, dict.getValueName());
            Dict parentDict = subChannelIdMap.get(dict.getParentId());
            if (parentDict != null) {
                subStoreChannelName.insert(0, "/");
                subStoreChannelName.insert(0, parentDict.getValueName());
            }
        }
        return subStoreChannelName.toString();
    }

    public Integer resetPlanIdByStoreCodes(Long planId, List<String> stores) {
        Integer result = 0;
        if (stores != null && !stores.isEmpty()) {
            result = applyStoreDao.resetPlanIdByStoreCodes(planId, stores);
        }
        return result;
    }

   // @Async
    public void refreshStoreInfo(StoreSimpleInfo simpleInfo) {
        if (simpleInfo != null && (StringUtils.isNotBlank(simpleInfo.getStoreCode()) || simpleInfo.getId() != null)) {
            applyStoreDao.refreshStoreInfo(simpleInfo);
        }
    }

    public void refreshStoreName() {
        List<String> storeCodes = applyStoreDao.getAllStoreCodes();
        Result<List<StoreSimpleInfo>> storeNamesResult = orgClient.getStoreNames(storeCodes);
        if (storeNamesResult != null && storeNamesResult.getSuccess() && storeNamesResult.getData() != null) {
            List<StoreSimpleInfo> storeNames = storeNamesResult.getData();
            applyStoreDao.refreshStoreName(storeNames);
        }
    }

    /**
     * 通过开店申请获取开店计划
     *
     * @param applyInfo
     * @return
     */
    public StoreOpenPlanDto getPlanByOpenApply(ApplyInfo applyInfo) {
        if (applyInfo == null || applyInfo.getId() == null || StringUtils.isBlank(applyInfo.getWfInstanceCode())) {
            return null;
        }
        return applyStoreDao.getPlanByOpenApply(applyInfo);
    }


    public void syncWholeUsableArea2Open(Integer storeId, BigDecimal wholeUsableArea) {
        if (storeId == null || wholeUsableArea == null) {
            return;
        }
        List<ApplyStore> list = this.lambdaQuery()
                .eq(ApplyStore::getApplyType, WorkflowEnum.OpenStore.getValue())
                .eq(ApplyStore::getIsDeleted, 0)
                .eq(ApplyStore::getStoreId, storeId)
                .list();
        if (!list.isEmpty()) {
            applyStoreDao.syncWholeUsableArea2Open(list.get(0).getId(), wholeUsableArea);
        }
    }

    public List<OpenInfo> getWholeUsableAreaByApplyIds(List<Integer> openApplyIds) {
        if (openApplyIds == null || openApplyIds.isEmpty()) {
            return Lists.newArrayListWithCapacity(0);
        }
        return applyStoreDao.getWholeUsableAreaByApplyIds(openApplyIds);
    }

    public List<OpenInfo> getOpenStoreComplateDate(ArrayList<Integer> storeIds) {
        if (storeIds == null || storeIds.isEmpty()) {
            return Lists.newArrayListWithCapacity(0);
        }
        return applyStoreDao.getOpenStoreComplateDate(storeIds);
    }

    @XxlJob(value = "syncMdmStoreInfoJob")
    public ReturnT<String> syncMdmStoreInfoJob(String s) {
        //todo 获取需要同步的开店数据
        // 查出来要发送的数据要避开长期同步失败的
        List<ApplyStore> needSyncMDMStores = mdmSyncLogService.getNeedSyncMDMStores();
        if(CollectionUtils.isEmpty(needSyncMDMStores)){
            return new ReturnT("未找到需要同步mdm的数据，执行结束");
        }
        ArrayList<MdmSyncLog> saveList = Lists.newArrayList();
        for (ApplyStore needSyncMDMStore : needSyncMDMStores) {
            try {
                MdmSyncLog mdmSyncLog = peripheralServcie.addStoreInfo2Mdm(needSyncMDMStore.getWfInstanceCode());
                if("200".equals(mdmSyncLog.getResponseStatus())){//只有200的才触发流程流转
                    fotileActivitiService.triggerReceiveTask("open-syncMDM",needSyncMDMStore.getWfInstanceCode());
                    fotileTaskService.handleTaskCirculate(needSyncMDMStore.getWfInstanceCode());
                }
                saveList.add(mdmSyncLog);
            }catch (Exception e){
                log.error("同步门店信息到mdm失败："+e.getMessage());
            }
        }

        List<MdmSyncLog> errLogs = saveList.stream().filter(log -> !"200".equals(log.getResponseStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(errLogs)){
            mdmSyncLogService.saveBatch(saveList);
            return new ReturnT("同步完成，无失败数据");
        }

        List<ApplyStore> needSendEmailSyncStores = mdmSyncLogService.getNeedSendEmailSyncStores(errLogs);
        mdmSyncLogService.saveBatch(saveList);
        //2 查找出来失败需要发送邮件通知的数据，通知管理员
        if(CollectionUtils.isEmpty(needSendEmailSyncStores)){
            return new ReturnT("同步完成，无需要发送email的数据");
        }
        try {
            emailService.springSendMail(receiveSyncMDMFailEmail, "开店申请同步mdm失败", getSyncMDMEmailContent(needSendEmailSyncStores), null, null);
        }catch (Exception e){
            log.error("发送邮件失败："+e.getMessage());
            return new ReturnT(500,"发送邮件失败："+e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("sendCloseProcNotArrive110Msg")
    public ReturnT<String> sendCloseProcNotArrive110Msg(String s){
        //1、先查出来需要发送的数据
      List<ApplyInfo> list=  applyStoreDao.getNotArrive110CloseProc();
      if(CollectionUtils.isEmpty(list)){
          return new ReturnT<>("暂无数据");
      }
        //2、查出来大区的字典值
        Map<Long, String> regionMap = Optional.of(new DicDto().setTypeCodes(Arrays.asList("所属大区")))
                .map(systemClient::getDicByCodes)
                .filter(Result::getSuccess)
                .map(Result::getData)
                .filter(CollectionUtils::isNotEmpty)
                .map(l -> l.stream().collect(Collectors.toMap(DicDto::getId, DicDto::getValueCode)))
                .orElse(Maps.newHashMapWithExpectedSize(0));

        //3、查出来大区岗位的用户和总部岗位的用户

        for (ApplyInfo applyInfo : list) {
            HashSet<String> userNumbers = Sets.newHashSetWithExpectedSize(2);
            Optional.ofNullable(applyInfo.getRegionId())
                    .map(Integer::longValue)
                    .map(regionMap::get)
                    .filter(StringUtils::isNotBlank)
                    .map(rc->userService.getByRoleName("总部经销商发展模块经理",WorkflowEnum.CloseStore.getCode(),null,rc))
                    .filter(CollectionUtils::isNotEmpty)
                    .map(l->l.get(0))
                    .ifPresent(u->userNumbers.add(u.getNumber()));

                Optional.ofNullable(applyInfo.getRegionId())
                        .map(Integer::longValue)
                        .map(regionMap::get)
                        .filter(StringUtils::isNotBlank)
                        .map(rc->userService.getUserByRoleNameAndRegionCode("大区经销商业务经理",rc,WorkflowEnum.CloseStore.getCode()))
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l->l.get(0))
                        .ifPresent(u->userNumbers.add(u.getNumber()));
            if(CollectionUtils.isEmpty(userNumbers)){
                continue;
            }
            //发送钉钉
            try {
                fotileActivitiService.sendDingDingCopy(applyInfo.getWfInstanceCode(),applyInfo.getWfCurrentTaskId(),userNumbers,",");
            }catch (Exception e){
                log.error("发送钉钉失败："+e.getMessage());
            }
        }
        return ReturnT.SUCCESS;
    }

    private String getSyncMDMEmailContent(List<ApplyStore> needSendEmailSyncStores) {
        if(CollectionUtils.isEmpty(needSendEmailSyncStores)){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder("<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>Title</title>\n" +
                "</head>\n" +
                "<body>\n" +
                "<p>您好，开店申请流程出现mdm同步失败状况，请及时处理<br>\n" +
                "</p>\n" +
                "<table align=\"center\"  border=\"1\" cellspacing=\"0\" style=\"text-align: center;\">\n" +
                "    <thead>\n" +
                "    <tr>\n" +
                "        <th scope=\"col\">流程编码</th>\n" +
                "        <th scope=\"col\">门店编码</th>\n" +
                "        <th scope=\"col\">门店名称</th>\n" +
                "        <th scope=\"col\">所属公司</th>\n" +
                "        <th scope=\"col\">创建人</th>\n" +
                "        <th scope=\"col\">创建时间</th>\n" +
                "    </tr>\n" +
                "    </thead>\n" +
                "    <tbody>\n");
        for (ApplyStore needSendEmailSyncStore : needSendEmailSyncStores) {
            stringBuilder.append("<tr><th scope=\"row\">").append(needSendEmailSyncStore.getWfInstanceCode()).append("</th>")
                    .append("<td>").append(needSendEmailSyncStore.getStoreCode()).append("</td>")
                    .append("<td>").append(needSendEmailSyncStore.getStoreName()).append("</td>")
                    .append("<td>").append(needSendEmailSyncStore.getCompanyName()).append("</td>")
                    .append("<td>").append(needSendEmailSyncStore.getCreatedUserName()).append("</td>")
                    .append("<td>").append(df.format(needSendEmailSyncStore.getCreatedDate()) ).append("</td></tr>");
        }
        stringBuilder.append( "    </tbody>\n" +
                "</table>\n" +
                "</body>\n" +
                "</html>");
      return  stringBuilder.toString();
    }

    @Deprecated
    public void createPlanByOpen() {
     List<TaskEntityImpl> list=   applyStoreDao.getNeedCreatePlanOpenApply();
     if(CollectionUtils.isEmpty(list)){
         return;
     }
        for (TaskEntityImpl taskEntity : list) {
            fotileTaskService.createOpenPlanIfNeed(taskEntity,null);
        }
    }

    /**
     * 23948  闭店流程-DMS项目范围样机接口不调用DRP(5月1日)
     */
    public Boolean ifClearDrpPrototype(Integer regionId) {
        if(Objects.equals(regionId,1653)|| Objects.equals(regionId,290)){
            DateTime now = DateUtil.date();
            if(Objects.equals(regionId,1653)){
                DateTime dateTime = Optional.of("guangdong_clear_drp_date")
                        .map(Lists::newArrayList)
                        .map(new DicDto()::setTypeCodes)
                        .map(systemClient::getDicByCodes)
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l->l.get(0))
                        .map(DicDto::getValueCode)
                        .filter(StringUtils::isNotBlank)
                        .filter(CommonUtils::isValidTime)
                        .map(DateUtil::parseDateTime)
                        .orElse(null);
                if(Objects.nonNull(dateTime)&&now.isAfter(dateTime)){
                    return false;
                }
            }else if(Objects.equals(regionId,290)){
                DateTime dateTime = Optional.of("huadong_clear_drp_date")
                        .map(Lists::newArrayList)
                        .map(new DicDto()::setTypeCodes)
                        .map(systemClient::getDicByCodes)
                        .filter(Result::getSuccess)
                        .map(Result::getData)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l->l.get(0))
                        .map(DicDto::getValueCode)
                        .filter(StringUtils::isNotBlank)
                        .filter(CommonUtils::isValidTime)
                        .map(DateUtil::parseDateTime)
                        .orElse(null);
                if(Objects.nonNull(dateTime)&&now.isAfter(dateTime)){
                    return false;
                }
            }
        }
        return true;
    }
}
