package com.fotile.activiti.workflow.business.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
* <AUTHOR>
* @data 2024/3/26 15:03
*/
@Data
@TableName(value = "workflowcenter.mdm_sync_log")
public class MdmSyncLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 流程编码
     */
    @TableField(value = "proc_inst_id")
    private String procInstId;

    /**
     * 门店id
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * 门店编码
     */
    @TableField(value = "store_code")
    private String storeCode;

    /**
     * 流程类型，现在只有开店申请
     */
    @TableField(value = "apply_type")
    private Integer applyType;

    /**
     * 请求URL
     */
    @TableField(value = "request_url")
    private String requestUrl;

    /**
     * 请求参数
     */
    @TableField(value = "request_param")
    private String requestParam;

    /**
     * 接口返回状态
     */
    @TableField(value = "response_status")
    private String responseStatus;

    /**
     * 接口返回参数
     */
    @TableField(value = "response_result")
    private String responseResult;

    /**
     * 调用时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}