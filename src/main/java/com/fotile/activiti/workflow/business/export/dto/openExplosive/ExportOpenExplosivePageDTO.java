package com.fotile.activiti.workflow.business.export.dto.openExplosive;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Create By lubing on 2023/8/12.
 */
@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
@ExcelIgnoreUnannotated
public class ExportOpenExplosivePageDTO {

    private Long id;


    private Integer openStoreApplyId;
    /**
     * 门店名称
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店名称", index = 0)
    private String storeName;

    /**
     * 门店编码
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店编码")
    private String storeCode;

    /**
     * 整体使用面积
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店整体面积/m2")
    private BigDecimal wholeUsableArea;

    /**
     * 门店类别
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店类别")
    private String storeLevel;
    /**
     * 所属分公司
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "所属分公司")
    private String companyName;

    /**
     * 新店建设总负责人
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "新店建设总负责人")
    @FieldEncrypt
    private String newStoreBuildLeaderName;

    /**
     * 所属大区
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "所属大区")
    private String regionName;
    /**
     * 里程碑节点
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "里程碑节点")
    private String milestoneName;

    /**
     * 里程碑节点状态
     */
    private String nodeStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = "里程碑节点状态")
    private String nodeStatusName;

    /**
     * 里程碑节点审核状态（1-待审核 2-审核通过 3-审核拒绝）
     */
    private Integer auditStatus;

    @ColumnWidth(10)
    @ExcelProperty(value = "里程碑节点审核状态")
    private String auditStatusName;

    /**
     * 销售额
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "销售额（万元）")
    private BigDecimal storeSales;

    /**
     * 录入信用价
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "录入信用价（万元）")
    private BigDecimal creditSales;

    /**
     * 重点产品台数
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "重点产品台数")
    private Integer productLineNumber;

    /**
     * 线索数
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "线索数")
    private Integer userCluesNumber;

    /**
     * 门店总人数
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "门店总人数")
    private Integer storeHeadcount;

    /**
     * 【开业活动落地当天】开业落地完成时间
     */
    private Date oaGroundCompleteDate;

    /**
     * 【里程碑】选址确认时间（日期格式yyyy-MM-dd）
     */
    private Date msSiteSelectionDate;

    /**
     * 建店时长（天）
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "建店时长（天）")
    private Integer consumeDays;

    /**
     * 健康度得分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "健康度得分")
    private BigDecimal healthScore;

    /**
     * 是否有店长
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "是否有店长")
    private String storeHasManager;

    @ColumnWidth(10)
    @ExcelProperty(value = "一店一策是否完成")
    private String dcv1Complated;

    @ColumnWidth(10)
    @ExcelProperty(value = "一店一策2.0是否完成")
    private String dcv2Complated;

    /**
     * 一店一策1.0评价
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "一店一策评价")
    private String dcScoreV1;

    /**
     * 一店一策2.0评价
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "一店一策2.0评价")
    private String dcScoreV2AreaManager;

    /**
     * 新店任务创建时间
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "新店任务创建时间")
    private Date createdDate;

}
