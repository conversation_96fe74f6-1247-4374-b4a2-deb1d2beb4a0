package com.fotile.activiti.workflow.business.pojo.dto.openexplosive;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.dto.openexplosive
 * @date 2023/7/17 17:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SaveStoreHealthScore implements Serializable {
    public SaveStoreHealthScore(StoreHealthScoreInfo healthInfo) {
        if (healthInfo != null) {
            this.storeOpenExplosiveId = healthInfo.getStoreOpenExplosiveId();
            this.healthScore = healthInfo.getCalcScore();
            this.scoreTargetCompleteRate = healthInfo.getOpActivityTargetCompleteRateScore();
            this.scoreAfter3monthTargetCompleteRate = healthInfo.getOpActivityAfter3MonthTargetCompleteRateScore();
            this.scoreStoreManagerStaffing = healthInfo.getStoreManagerStaffingScore();
            this.scoreTeamOrganizationPerson = healthInfo.getTeamOrganizationPersonScore();
            this.operatorId = "calc_store_health_score_job";
        }
    }

    /**
     * 开业爆量id(t_store_open_explosive.id)
     */
    private Long storeOpenExplosiveId;

    /**
     * 开业爆量申请id(s_apply.id)
     */
    private Integer applyId;

    /**
     * 【健康度】总得分(计算合计得分)
     */
    private Double healthScore;

    /**
     * 【健康度】开业活动目标完成率得分
     */
    private Double scoreTargetCompleteRate;

    /**
     * 【健康度】开业后三个月目标完成率得分
     */
    private Double scoreAfter3monthTargetCompleteRate;

    /**
     * 【健康度】专职店长配置得分
     */
    private Double scoreStoreManagerStaffing;

    /**
     * 【健康度】团队组织人数得分
     */
    private Double scoreTeamOrganizationPerson;


    /**
     * 操作人
     */
    private String operatorId;
}
