package com.fotile.activiti.workflow.business.pojo.mapper;

import com.fotile.activiti.workflow.business.pojo.vo.OpPlanExportVO;
import com.fotile.activiti.workflow.business.pojo.vo.OpTargetExportVO;
import com.fotile.activiti.workflow.ipms.retail.operate.pojo.dto.OpPlanChannelInDto;
import com.fotile.activiti.workflow.ipms.retail.operate.pojo.dto.OpPlanFindPageAllOutDto;
import com.fotile.activiti.workflow.ipms.retail.operate.pojo.dto.OpTargetInDto;
import com.fotile.activiti.workflow.ipms.retail.operate.pojo.entity.OperatePlanChannel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-05-09
 */
@Mapper(componentModel="spring")
public interface OpPlanMapper {
    OpPlanMapper INSTANCE = Mappers.getMapper( OpPlanMapper.class );

    //上市操盘列表
    public List<OpPlanExportVO> opPlanOutDtoListToExportList(List<OpPlanFindPageAllOutDto> outDtoList);

    public OpPlanExportVO opPlanOutDtoToExport(OpPlanFindPageAllOutDto outDto);


    //总部-目标分解
    public List<OpTargetExportVO> opTargetOutDtoListToExportList(List<OpTargetInDto> outDtoList);

    public OpTargetExportVO opTargetOutDtoToExport(OpTargetInDto outDto);


    public List<OperatePlanChannel> opPlanListToOperatePlanList(List<OpPlanChannelInDto> opPlanChannelInDtoList);

    public OperatePlanChannel opPlanToOperatePlan(OpPlanChannelInDto opPlanChannelInDto);

}
