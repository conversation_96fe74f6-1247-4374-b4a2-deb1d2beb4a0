package com.fotile.activiti.workflow.business.pojo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ColumnWidth(20)
@ExcelIgnoreUnannotated
public class OpTargetExportVO implements Serializable {


    /**
     * 大区名称
     */
    @ExcelProperty(value = "大区", index = 0)
    private String regionName;

    /**
     * 计划上样台数
     */
    @ExcelProperty(value = "计划上样台数", index = 1)
    private Integer sampleQuantityPlanned;

    /**
     * 实际上样台数
     */
    @ExcelProperty(value = "实际上样台数", index = 2)
    private Integer sampleQuantity;

    /**
     * 实际上样完成率
     *
     * =实际上样台数/计划上样台数
     */
    @ExcelProperty(value = "实际上样完成率=实际上样台数/计划上样台数", index = 3)
    private String sampleQuantityPer;

    /**
     * 销售渠道现有门店数量(启用)
     */
    @ExcelProperty(value = "销售渠道现有门店数量(启用)", index = 4)
    private Integer storeQuantity;

    /**
     * 计划上样门店数量
     */
    @ExcelProperty(value = "计划上样门店数量", index = 5)
    private Integer sampleStoreQuantityPlanned;

    /**
     * 实际上样门店数量
     */
    @ExcelProperty(value = "实际上样门店数量", index = 6)
    private Integer sampleStoreQuantity;

    /**
     * 计划覆盖率
     *
     * =计划上样门店数量/现有门店
     */
    @ExcelProperty(value = "计划覆盖率 =计划上样门店数量/现有门店", index = 7)
    private String sampleStoreQuantityPlannedPer;

    /**
     * 实际门店覆盖完成率
     *
     * =实际上样门店数量/计划上样门店数量
     */
    @ExcelProperty(value = "实际门店覆盖完成率=实际上样门店数量/计划上样门店数量", index = 8)
    private String sampleStoreQuantityPer;

    /**
     * 上市首月销售目标台数
     */
    @ExcelProperty(value = "上市首月销售目标台数", index = 9)
    private Integer saleTargetFirstMonth;

    /**
     * 实际上市首月销售台数
     */
    @ExcelProperty(value = "实际上市首月销售台数", index = 10)
    private Integer saleQuantityFirstMonth;

    /**
     * 上市次月销售目标台数
     */
    @ExcelProperty(value = "上市次月销售目标台数", index = 11)
    private Integer saleTargetSecondMonth;

    /**
     * 实际上市次月销售台数
     */
    @ExcelProperty(value = "实际上市次月销售台数", index = 12)
    private Integer saleQuantitySecondMonth;

    /**
     * 上市第三个月销售目标台数
     */
    @ExcelProperty(value = "上市第三个月销售目标台数", index = 13)
    private Integer saleTargetThirdMonth;

    /**
     * 实际上市第三个月销售台数
     */
    @ExcelProperty(value = "实际上市第三个月销售台数", index = 14)
    private Integer saleQuantityThirdMonth;

    /**
     * 计划月销目标台数
     */
    @ExcelProperty(value = "计划月销目标台数", index = 15)
    private Integer saleTargetPerMonth;

    /**
     * 实际月销台数(T+1)
     */
    @ExcelProperty(value = "实际月销台数(T+1)", index = 16)
    private Integer saleQuantityPerMonth;

    /**
     * 销售起始日期(第一台样机盘点的保存时间)
     */
    @ExcelProperty(value = "销售起始日期(第一台样机盘点的保存时间)", index = 17)
    private String saleStartTime;

    /**
     * 计划爬坡周期
     */
    @ExcelProperty(value = "计划爬坡周期", index = 18)
    private Integer climbingDaysPlanned;

    /**
     * 实际爬坡周期
     */
    @ExcelProperty(value = "实际爬坡周期", index = 19)
    private Integer climbingDays;

    /**
     * 实际布展完成率
     */
    //private BigDecimal layoutCompletionRate;

    /**
     * 实际布展完成率
     *
     * =实际布展完成门店/计划上样门店数量
     */
    @ExcelProperty(value = "实际布展完成率=实际布展完成门店/计划上样门店数量", index = 20)
    private String layoutCompletionRatePer;

    private Long id;
}

