package com.fotile.activiti.workflow.business.constant;


/**
 * Create By lubing on 2022/3/7.
 */
public enum ExportTypeClassEnum {

    OPEN_PLAN_DETAIL_EXPORT(408, "openPlanDetailExport"),
    OPEN_PLAN_LIST_EXPORT(409, "openPlanListExport"),
    APPLY_STORE_EXPOET(410,"applyStoreExport"),
    //上市操盘列表
    OP_PLAN_EXPORT(412,"opPlanExport"),
    //总部-目标分解
    OP_TARGET_EXPORT(413,"opTargetExport"),
    //大区汇总-PCT成员
    REGIONSUMMARY_PCT_EXPORT(414,"regionSummaryPctExport"),
    //大区汇总-里程碑
    REGIONSUMMARY_MILESTON_EXPORT(415,"regionSummaryMilestoneExport"),
    //大区汇总-销售目标分解
    REGIONSUMMARY_TARGET_EXPORT(416,"regionSummaryTargetExport"),
    //大区操盘-PCT成员
    REGION_PCT_EXPORT(417,"regionPctExport"),
    //大区操盘-里程碑
    REGION_MILESTON_EXPORT(418,"regionMilestoneExport"),
    //大区操盘-销售目标分解
    REGION_TARGET_EXPORT(419,"regionTargetExport"),
    //实际上市结果
    STORE_RESULT_EXPORT(420,"storeResultExport"),
    //终端布展列表导出
    TERMINAL_LAYOUT_EXPORT(421,"terminalLayoutExport"),
    //异常门店导出
    ABNORMAL_STORE_EXPORT(423,"abnormalStoreExport"),
    //门店面积变更记录导出
    WHOLE_USABLE_AREA_LOG_EXPORT(424,"wholeUsableAreaLogExport"),

    OPEN_EXPLOSIVE_OVER_ALL_EXPORT(426,"openExplosiveOverAllExport"),

    OPEN_EXPLOSIVE_OVER_PAGE_EXPORT(427,"openExplosivePageExport"),

    //终端申请导出
    TERMINAL_APPLY_EXPORT(431,"terminalApplyExport"),

    //终端费用导出
    TERMINAL_COST_EXPORT(430,"terminalCostExport");

    private Integer code;
    private String value;

    ExportTypeClassEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getExportTypeClass(Integer code) {
        String desc = null;
        for (ExportTypeClassEnum enumVal : ExportTypeClassEnum.values()) {
            if (enumVal.getCode().equals(code)) {
                desc = enumVal.getValue();
                break;
            }
        }
        return desc;
    }
}
