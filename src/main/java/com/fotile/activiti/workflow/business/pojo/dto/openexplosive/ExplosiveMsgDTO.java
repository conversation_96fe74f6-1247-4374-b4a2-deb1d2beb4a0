package com.fotile.activiti.workflow.business.pojo.dto.openexplosive;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * Create By lubing on 2023/7/10.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExplosiveMsgDTO {

    private String companyName;

    private Long companyId;

    private String processType;

    private String assignee;

    private Set<String> assignees;

    private String storeCode;

    private String storeName;

    private String wfInstanceCode;

    private String taskId;

    private String taskDefKey;

    /**
     * 1：普通钉钉、2：人员招聘相关通知、3：040节点通知，4：抄送类型的通知
     */
    private Integer msgType=1;

    private Date createDate;

    private String urlSuffix;

    private Integer htmlVersion;

    private Map<String,String> params;

    /**
     * 终端验收了几个月
     */
    private Integer diffMonth;
}
