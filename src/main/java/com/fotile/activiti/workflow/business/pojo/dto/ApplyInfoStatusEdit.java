package com.fotile.activiti.workflow.business.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 更新申请状态参数实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.dto
 * @date 2021/1/11 14:30
 */
@Data
public class ApplyInfoStatusEdit implements Serializable {
    /**
     * 申请Id
     */
    private Integer applyId;

    /**
     * 申请id集合
     */
    private List<Integer> applyIdList;

    /**
     * 流程实例Id
     */
    private String processInstanceId;

    /**
     * 流程实例Id集合
     */
    private List<String> processInstanceIdList;

    /**
     * 申请状态(0:待提交,1:处理中,2:已结束,3:已取消，4:待审核)
     */
    @NotNull(message = "申请状态不能为空！")
    @Min(message = "错误的申请状态！", value = 0)
    @Max(message = "错误的申请状态！", value = 4)
    private Integer applyStatus;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 门店状态(0:禁用; 1:启用; 2:筹备; 3:清退)-内部参数
     */
    private Integer storeStatus;

    /**
     * 是否需要检查供应商信息(默认true)
     */
    private Boolean needCheckSupplier = true;

    /**
     * 内部参数(操作人Id)
     */
    @JsonIgnore
    private transient String operatorId;

    /**
     * 内部参数(操作人姓名)
     */
    @JsonIgnore
    @FieldEncrypt
    private transient String operatorName;

    /**
     * 是否需要写日志(默认是)
     */
    private Boolean needWriteLog = true;
}
