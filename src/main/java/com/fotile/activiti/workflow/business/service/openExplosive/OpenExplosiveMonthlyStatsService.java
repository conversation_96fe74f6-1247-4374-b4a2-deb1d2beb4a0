package com.fotile.activiti.workflow.business.service.openExplosive;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.activiti.workflow.business.dao.OpenExplosiveMonthlyStatsDao;
import com.fotile.activiti.workflow.business.pojo.entity.OpenExplosiveMonthlyStats;
import com.fotile.activiti.workflow.business.utils.CommonUtils;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/**
 * Create By lubing on 2023/8/10.
 */
@Service
public class OpenExplosiveMonthlyStatsService extends ServiceImpl<OpenExplosiveMonthlyStatsDao, OpenExplosiveMonthlyStats> {
    @Resource
    private OpenExplosiveMonthlyStatsDao openExplosiveMonthlyStatsDao;

    private static ImmutableSet<Integer> fourthSeasonMonthSet=ImmutableSet.of(10,11,12);


    public void initStoreOpenExplosiveStats(Long openExplosiveId){
        LocalDateTime now = LocalDateTime.now();
        ArrayList<OpenExplosiveMonthlyStats> saveList = Lists.newArrayList();
        Map<String, String> user = CommonUtils.getAuthorUser();
        Date createDate = new Date();
        int month=13;
        for (int i = 1; i < month; i++) {
            OpenExplosiveMonthlyStats monthlyStats = new OpenExplosiveMonthlyStats();
            monthlyStats.setStatMonth(i);
            monthlyStats.setStoreOpenExplosiveId(openExplosiveId);
            monthlyStats.setStatYear(now.getYear());
            monthlyStats.setStatType(3);
            monthlyStats.setCreatedBy(user.get("userid"));
            monthlyStats.setCreatedDate(createDate);
            saveList.add(monthlyStats);
        }
        this.saveBatch(saveList);
    }



}
