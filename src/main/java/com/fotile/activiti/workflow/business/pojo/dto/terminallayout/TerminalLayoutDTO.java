package com.fotile.activiti.workflow.business.pojo.dto.terminallayout;


import lombok.Data;
import java.io.Serializable;

@Data
public class TerminalLayoutDTO implements Serializable {

    /**
     * 布展id
     */
    private Long id;

    /**
     * 操盘id
     */
    private Long opId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店编码
     */
     private String storeCode;

    /**
     * 公司id
     */
     private Long companyId;

    /**
     * 大区编码
     */
    private String regionCode;

    private Integer storeCount;

    private Integer auditStatus;

    private Integer groupBy;
}


