package com.fotile.activiti.workflow.business.pojo.dto.terminallayout;

import com.fotile.activiti.workflow.business.pojo.dto.terminallayout.TerminalLayoutPageInDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 异步导出终端布展条件DTO类
 */
@Data
public class TerminalLayoutExportInDTO extends TerminalLayoutPageInDTO {
    /**
     * 导出数据文件名称
     */
    @NotNull(message = "文件名称不能为空")
    private String fileName;

    /**
     * [后端自用]当前登录人账号id
     */
    private String currentUserId;
}
