package com.fotile.activiti.workflow.business.pojo.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 终端建设申请-费用清单-特殊费用
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.entity
 * @date 2021/1/7 18:18
 */
@Data
public class ApplyTerminalBuildBillSpecial implements Serializable {
    /**
     * 终端建设申请-费用清单-特殊费用Id,PK,AI
     */
    private Integer id;

    /**
     * 终端建设申请id(t_apply.id)
     */
    private Integer applyId;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 编号
     */
    private String code;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private BigDecimal amount;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 税点
     */
    private BigDecimal taxRate;

    /**
     * 分公司经理确认金额
     */
    private BigDecimal confirmedAmount;

    /**
     * 备注
     */
    private String remarks;

    //added by zd, 2021/06/01 迭代需求 新增字段
    /**
     * 特殊费用行id编号
     * */
    private String epsFinSepId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 逻辑删除标识
     */
    private Integer isDeleted = 0;
}
