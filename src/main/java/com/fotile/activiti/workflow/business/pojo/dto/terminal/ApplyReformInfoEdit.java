package com.fotile.activiti.workflow.business.pojo.dto.terminal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 终端建设-门店整改
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.business.pojo.dto.terminal
 * @date 2021/1/21 11:13
 */
@Data
public class ApplyReformInfoEdit implements Serializable {
    /**
     * 申请操作类型(0:保存; 1:保存并提交审核)
     */
    @NotNull(message = "操作动作不能为空！")
    @Min(message = "错误的操作动作！", value = 0)
    @Max(message = "错误的操作动作！", value = 1)
    private Integer actionType;

    /**
     * 申请信息
     */
    @NotNull(message = "申请信息不能为空！")
    private ApplyBuildInfo applyInfo;

    /**
     * 终端建设-整改申请信息
     */
    @NotNull(message = "整改申请信息不能为空！")
    private ApplyReformInfo reformInfo;

    /**
     * 内部参数(操作人Id)
     */
    @JsonIgnore
    private transient String operatorId;

    /**
     * 内部参数(操作人姓名)
     */
    @JsonIgnore
    private transient String operatorName;
}
