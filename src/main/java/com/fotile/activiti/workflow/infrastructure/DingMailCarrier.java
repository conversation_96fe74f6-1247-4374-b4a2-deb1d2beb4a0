package com.fotile.activiti.workflow.infrastructure;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.net.url.UrlQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fotile.activiti.workflow.activiti.pojo.DTO.dingding.OAMessage;
import com.fotile.activiti.workflow.activiti.pojo.DTO.dingding.TextMessage;
import com.fotile.activiti.workflow.business.pojo.entity.DingSendLog;
import com.fotile.activiti.workflow.business.service.DingSendLogService;
import com.fotile.activiti.workflow.client.MsgClient;
import com.fotile.activiti.workflow.client.dto.msg.MsgWhiteList;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2023/1/10.
 */
@Data
@Slf4j
@Component
public class DingMailCarrier {
    @Resource
    private RestTemplate restTemplate;

    @Value("${workflow.dingding.source}")
    private String source;

    @Value("${workflow.dingding.messageUrl}")
    private String OAUrl;

    @Value("${workflow.dingding.textUrl}")
    private String TextUrl;
    @Value("${whiteListSwitch}")
    Boolean whiteListSwitch;

    @Resource
    private DingSendLogService dingSendLogService;

    @Autowired
    private MsgClient msgClient;

    /** 默认方式，除非处理成功，否则报错
     * @param oaMessage
     * @return
     */
    public String sendOAMessage(OAMessage oaMessage) throws InterruptedException {
        String returnCode=null;
        if(oaMessage==null || CollectionUtils.isEmpty(oaMessage.getEmployeeCodes())){
            return returnCode;
        }

        //判断是否在白名单内
        if(Boolean.TRUE.equals(whiteListSwitch)){
            Set<String> whiteSets = Optional.ofNullable(msgClient)
                    .map(c -> c.getListByType(5))
                    .filter(Result::getSuccess)
                    .map(Result::getData)
                    .map(l -> l.stream().map(MsgWhiteList::getReceiverId).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))
                    .orElse(Sets.newHashSet());

            if(!whiteSets.isEmpty()){
                List<String> collect = oaMessage.getEmployeeCodes().stream().filter(whiteSets::contains).collect(Collectors.toList());
                oaMessage.setEmployeeCodes(collect);
            }else{
                oaMessage.setEmployeeCodes(Lists.newArrayListWithExpectedSize(0));
            }
        }

        if(oaMessage.getEmployeeCodes()!=null && !oaMessage.getEmployeeCodes().isEmpty()){
            oaMessage.setSource(source);
            DingSendLog logEntity = getLogEntity(oaMessage);
            ResponseEntity<JSONObject> response =null;
            try {
                response = restTemplate.postForEntity(OAUrl, oaMessage, JSONObject.class);

                log.info("发送钉钉通知:"+JSONObject.toJSONString(oaMessage)+"返回消息体："+JSONObject.toJSONString(response));
                //补偿机制，钉钉每秒有上限,若当前秒内发送条数封顶，延迟一两秒补偿
                if(HttpStatus.OK.equals(response.getStatusCode())){
                    JSONObject body = response.getBody();
                    JSONObject data = body.getJSONObject("data");
                    if(data!=null){
                        returnCode = data.getString("errcode");
                    }
                }else{
                    log.error("发送OA钉钉失败，发送消息体:"+JSONObject.toJSONString(oaMessage)+",返回消息："+JSONObject.toJSONString(response));
                }
            }catch (Exception e){
                logEntity.setResponseResult(e.getMessage());
                dingSendLogService.save(logEntity);
                log.error("发送OA钉钉失败，发送消息体:"+JSONObject.toJSONString(oaMessage)+"报错信息："+JSONObject.toJSONString(e));
            }
            if(response!=null){
                try {
                    logEntity.setResponseCode(String.valueOf(response.getStatusCode().value()));
                    logEntity.setResponseResult(JSONObject.toJSONString(response.getBody()));
                    dingSendLogService.save(logEntity);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }

            //returnCode 0 处理成功
            if(!"0".equals(returnCode)){
                Thread.sleep(3000);
                throw new BusinessException("发送失败");
            }
        }
        return returnCode;
    }

    private DingSendLog getLogEntity(OAMessage oaMessage){
        DingSendLog dingSendLog = new DingSendLog();
        try {
            UrlBuilder of = UrlBuilder.of(oaMessage.getUrl().replace("/#",""));
            UrlQuery query = of.getQuery();
            CharSequence taskId = query.get("taskId");
            dingSendLog.setTaskId(Long.valueOf(taskId.toString()));
        }catch (Exception e){
            log.error(e.getMessage());
        }
        dingSendLog.setAssignee(Joiner.on(",").join(oaMessage.getEmployeeCodes()));
        dingSendLog.setRequestUrl(OAUrl);
        dingSendLog.setRequestParam(JSONObject.toJSONString(oaMessage));
        dingSendLog.setCreateTime(new Date());
        return dingSendLog;
    }

    private DingSendLog getLogEntity(TextMessage textMessage){
        DingSendLog dingSendLog = new DingSendLog();
        dingSendLog.setAssignee(Joiner.on(",").join(textMessage.getEmployeeCodes()));
        dingSendLog.setRequestUrl(TextUrl);
        dingSendLog.setRequestParam(JSONObject.toJSONString(textMessage));
        dingSendLog.setCreateTime(new Date());
        return dingSendLog;
    }


/*    public DingReturnDTO sendOAMessage(OAMessage oaMessage){

    }*/

    public String sendTextMessage(TextMessage textMessage) throws InterruptedException {
        String returnCode=null;
        if(textMessage==null || CollectionUtils.isEmpty(textMessage.getEmployeeCodes())){
            return returnCode;
        }

        //判断是否在白名单内
        if(Boolean.TRUE.equals(whiteListSwitch)){
            Set<String> whiteSets = Optional.ofNullable(msgClient)
                    .map(c -> c.getListByType(5))
                    .filter(Result::getSuccess)
                    .map(Result::getData)
                    .map(l -> l.stream().map(MsgWhiteList::getReceiverId).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))
                    .orElse(Sets.newHashSet());

            if(!whiteSets.isEmpty()){
                List<String> collect = textMessage.getEmployeeCodes().stream().filter(whiteSets::contains).collect(Collectors.toList());
                textMessage.setEmployeeCodes(collect);
            }else{
                textMessage.setEmployeeCodes(Lists.newArrayListWithExpectedSize(0));
            }
        }


        if( textMessage.getEmployeeCodes()!=null && !textMessage.getEmployeeCodes().isEmpty()){
            textMessage.setSource(source);
            DingSendLog logEntity = getLogEntity(textMessage);
            ResponseEntity<JSONObject> response =null;
            try {
                response = restTemplate.postForEntity(TextUrl, textMessage, JSONObject.class);
                if(HttpStatus.OK.equals(response.getStatusCode())){
                    JSONObject body = response.getBody();
                    JSONObject data = body.getJSONObject("data");
                    if(data!=null){
                        returnCode = data.getString("errcode");
                    }
                }else{
                    log.error("发送文本钉钉失败，发送消息体:"+JSONObject.toJSONString(textMessage)+",返回消息："+JSONObject.toJSONString(response));
                }
            }catch (Exception e){
                logEntity.setResponseResult(e.getMessage());
                dingSendLogService.save(logEntity);
                log.error("发送文本钉钉失败，发送消息体:"+JSONObject.toJSONString(textMessage)+"报错信息："+JSONObject.toJSONString(e));
            }

            if(response!=null){
                try {
                    logEntity.setResponseCode(String.valueOf(response.getStatusCode().value()));
                    logEntity.setResponseResult(JSONObject.toJSONString(response.getBody()));
                    dingSendLogService.save(logEntity);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
            //returnCode 0 处理成功
            if(!"0".equals(returnCode)){
                Thread.sleep(3000);
                throw new BusinessException("发送失败");
            }
        }
        return returnCode;
    }

    public String sendMsg(JSONObject jsonObject) throws InterruptedException {
        String type = jsonObject.getString("type");
        String result ="";
        if(OAMessage.type.equals(type)){
            OAMessage oaMessage = JSON.toJavaObject(jsonObject, OAMessage.class);
             result = sendOAMessage(oaMessage);
        }else if(TextMessage.type.equals(type)){
            TextMessage textMessage = JSON.toJavaObject(jsonObject, TextMessage.class);
            sendTextMessage(textMessage);
        }
        return result;
    }


}

