package com.fotile.activiti.workflow.activiti;

import org.activiti.api.runtime.shared.identity.UserGroupManager;

import java.util.ArrayList;
import java.util.List;

public class CustomerUserGroupManager implements UserGroupManager {



    public CustomerUserGroupManager() {
    }

    @Override
    public List<String> getUserGroups(String userId) {
        ArrayList<String> strings = new ArrayList<>();
        strings.add("hr");
        strings.add("shenpi1");
        strings.add("shenpi2");
        return strings;
    }

    @Override
    public List<String> getUserRoles(String userId) {
        ArrayList<String> strings = new ArrayList<>();
        strings.add("ACTIVITI_ADMIN");
        strings.add("ACTIVITI_USER");
        strings.add("ACTIVITI_MODELER");
        return strings;
    }

    @Override
    public List<String> getGroups() {
        ArrayList<String> strings = new ArrayList<>();
        strings.add("hr");
        strings.add("shenpi1");
        strings.add("shenpi2");
        return strings;
    }

    @Override
    public List<String> getUsers() {
        ArrayList<String> strings = new ArrayList<>();
        return strings;
    }
}
