package com.fotile.activiti.workflow.activiti.pojo.DTO.dingding;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.framework.web.BusinessException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Data
@Deprecated
@Accessors(chain = true)
public class CardnTextTransmitter {

    @JsonIgnore
    private RestTemplate restTemplate;


    private String textUrl="http://qyding.fotile.com:8382/ding/pushMessageText";//todo 放到配置文件里

    /**
     * 来源应用 默认方太营销中台
     */
    private String source;

    /**
     * 发送的内容
     */
    private String content;

    /**
     * 接收者工号
     */
    private List<String> employeeCodes;


    private String title;

    private String singleTitle;

    private String singleUrl;


    public String sendTextMessage(){
        String returnCode="";
        if(StringUtils.isBlank(source)|| StringUtils.isBlank(content) ||StringUtils.isBlank(title) || employeeCodes==null ||employeeCodes.isEmpty()){
            throw new BusinessException("发送钉钉文本通知参数不足");
        }
        if(restTemplate==null){
            restTemplate = new RestTemplate();
        }
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(textUrl, this, JSONObject.class);
        //补偿机制，钉钉每秒有上限,若当前秒内发送条数封顶，延迟一两秒补偿
        if(HttpStatus.OK.equals(response.getStatusCode())){
            JSONObject body = response.getBody();
            JSONObject data = body.getJSONObject("data");
            if(data!=null){
                 returnCode = data.getString("errcode");
                /*if("90018".equals(errcode)){
                    return
                }else if("90006".equals(errcode)){
                }*/
            }
        }
        return returnCode;
    }

}
