package com.fotile.activiti.workflow.activiti.service.listener;

import com.fotile.activiti.workflow.exception.ListenerException;
import com.fotile.activiti.workflow.peripheral.service.PeripheralServcie;
import org.activiti.engine.delegate.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2021-03-25-19:28
 */
@Component("auditDesignMaterialsListener")
public class AuditDesignMaterialsListener implements Serializable, TaskListener, JavaDelegate {
    @Resource
    private PeripheralServcie peripheralServcie;

    private Expression auditResult;

    private Expression taskDefKey;


    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        auditDesignMaterials(processInstanceId);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String processInstanceId = delegateTask.getProcessInstanceId();
        auditDesignMaterials(processInstanceId);
    }

   public void auditDesignMaterials(String procInstId){
       String auditResultText = auditResult.getExpressionText();
       Boolean auditResultBoolean=null;
       try{
            auditResultBoolean = Boolean.valueOf(auditResultText);
       }catch (Exception e){
           throw new ListenerException("工作流审核意见配置错误，请联系管理员");
       }
       String node = taskDefKey.getExpressionText();
       boolean designMaterials = peripheralServcie.auditDesignMaterials(procInstId, node, auditResultBoolean, "");
       if(!designMaterials){
           throw new ListenerException("同步eps设计资料审核信息失败");
       }
   }
}
