package com.fotile.activiti.workflow.activiti.controller;

import com.fotile.activiti.workflow.activiti.service.WfJobService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @create 2021-03-19-11:37
 */
@RestController
@RequestMapping("api/job")
public class WfJob<PERSON>ontroller extends BaseController {

    @Autowired
    private WfJobService wfJobService;

    @DeleteMapping("byTaskId")
    public Result deleteJobByTaskId(@RequestParam("taskId") String taskId){
       boolean result = wfJobService.deleteByTaskId(taskId);
        return result?success("删除成功"):failure("删除失败");
    }
}
