package com.fotile.activiti.workflow.activiti.service.listener.explosive;

import com.fotile.activiti.workflow.business.constant.StoreOpenExplosiveConstant;
import com.fotile.activiti.workflow.business.pojo.dto.ApplyInfoQuery;
import com.fotile.activiti.workflow.business.pojo.dto.openexplosive.ExplosiveMsgDTO;
import com.fotile.activiti.workflow.business.pojo.entity.ApplyStore;
import com.fotile.activiti.workflow.business.pojo.entity.ApplyStoreOpen;
import com.fotile.activiti.workflow.business.service.ApplyStoreService;
import com.fotile.activiti.workflow.business.service.openExplosive.ApplyStoreOpenExplosiveMemberService;
import com.fotile.activiti.workflow.business.service.openExplosive.OpenExplosiveService;
import com.fotile.activiti.workflow.user.pojo.UserDTO;
import com.fotile.activiti.workflow.user.service.UserService;
import com.fotile.activiti.workflow.utils.DingChannel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.*;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.apache.commons.lang3.StringUtils;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10 16:28
 */
@Component("sendExplosiveDingGradeListenerVersion2")
public class SendDingGradeListenerVersion2 implements Serializable, TaskListener, JavaDelegate {

    @Resource
    private ApplyStoreService applyStoreService;
    @Resource(name = DingChannel.GENERAL_DING_MESSAGE_OUT)
    private MessageChannel dingMsgChannel;
    @Resource
    private UserService userService;
    @Resource
    private ApplyStoreOpenExplosiveMemberService explosiveMemberService;
    @Resource
    private OpenExplosiveService openExplosiveService;
    @Resource
    private HistoryService historyService;

    private Expression taskKeyExpression;

    private static final Map<String, List<String>> TASK_ROLE_MAP=new ImmutableMap.Builder<String, List<String>>()
            .put("体验店",List.of("总部经销商区域发展模块高级经理"))
            .put( "旗舰店",List.of("总部经销商区域发展经理"))
            .put( "integrate-audit-060",List.of("大区门店业务发展经理"))
            .build();


    @Override
    public void notify(DelegateTask delegateTask) {
        sendDingGradeMsg(delegateTask.getProcessInstanceId());
    }

    @Override
    public void execute(DelegateExecution execution) {
        sendDingGradeMsg(execution.getProcessInstanceId());
    }

    public void sendDingGradeMsg(String procInstId){
        String taskDefKey = taskKeyExpression.getExpressionText();
        if(StringUtils.isBlank(taskDefKey)){
            return;
        }

        ArrayList<String> roles = Lists.newArrayList();
        String title="";
        HashMap<String,String> params=new HashMap<>();
        if("integrate-audit-040".equals(taskDefKey)){
            ApplyInfoQuery applyInfoQuery = new ApplyInfoQuery();
            applyInfoQuery.setWfInstanceCode(procInstId);
            ApplyStore applyStore=  openExplosiveService.getOpenApplyByExplosiveProcessId(applyInfoQuery);
            //获取门店类别，通过流程id查出
            if(applyStore==null){
                return;
            }
            ApplyStoreOpen openInfo = applyStoreService.getOpenInfo(applyStore.getId(), null);
            if(openInfo==null){
                return;
            }
            if(openInfo.getUsableArea()!=null &&openInfo.getUsableArea().compareTo(new BigDecimal(400)) >= 0){
                roles.addAll(TASK_ROLE_MAP.get("体验店"));
            }else if(openInfo.getUsableArea()!=null &&openInfo.getUsableArea().compareTo(new BigDecimal(200)) >= 0){
                roles.addAll(TASK_ROLE_MAP.get("旗舰店"));
            }

            title="一店一策需要你评分";
            params.put("gradeField","dcScoreV1HeadManager");
        }else if("integrate-audit-060".equals(taskDefKey)){
            roles.addAll(TASK_ROLE_MAP.get("integrate-audit-060"));
            title="一店一策2.0需要你评分";
            params.put("gradeField","dcScoreV2AreaManager");
        }

        //根据角色查出人员，发送钉钉消息
        if(!roles.isEmpty()){
            //1、通过当前任务查出需要发送钉钉的工号
            ApplyStore applyInfo = applyStoreService.getApplyInfo(null, procInstId, null);
            List<UserDTO> users = userService.getByRoleNames(roles, "integrate_store_build", applyInfo.getCompanyId().toString());
            if(users==null||users.isEmpty()){
                return;
            }
            Set<String> numberSet = users.stream().map(UserDTO::getNumber).collect(Collectors.toSet());

            List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(procInstId)
                    .taskDefinitionKey(taskDefKey)
                    .orderByTaskCreateTime()
                    .desc()
                    .listPage(0, 1);
            if(historicTaskInstances==null||historicTaskInstances.isEmpty()){
                return;
            }

            //2、拼接钉钉发送对象
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
            ExplosiveMsgDTO msgDTO = ExplosiveMsgDTO.builder()
                    .assignees(numberSet)
                    .companyName(applyInfo.getCompanyName())
                    .createDate(applyInfo.getCreatedDate())
                    .processType("开业爆量活动")
                    .storeCode(applyInfo.getStoreCode())
                    .storeName(applyInfo.getStoreName())
                    .wfInstanceCode(applyInfo.getWfInstanceCode())
                    .htmlVersion(Optional.ofNullable(processInstance).map(HistoricProcessInstance::getProcessDefinitionVersion).map(StoreOpenExplosiveConstant::getHtmlVersion).orElse(1))
                    .taskId(historicTaskInstances.get(0).getId())
                    .taskDefKey(taskDefKey)
                    .msgType(3)
                    .params(params)
                    .build();
            explosiveMemberService.sendDingMsg(title,msgDTO);
        }
    }
}
