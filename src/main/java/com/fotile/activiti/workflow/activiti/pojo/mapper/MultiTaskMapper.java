package com.fotile.activiti.workflow.activiti.pojo.mapper;

import com.fotile.activiti.workflow.activiti.pojo.DTO.MultiTaskDto;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.task.Task;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel="spring")
public interface MultiTaskMapper {
    MultiTaskMapper INSTANCE = Mappers.getMapper( MultiTaskMapper.class );

    MultiTaskDto task2Multi(Task task);

    TaskEntityImpl multi2Impl(MultiTaskDto dto);
}
