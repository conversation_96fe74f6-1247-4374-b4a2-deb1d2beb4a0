package com.fotile.activiti.workflow.phase.pojo.entity.abnormalStore;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * se_abnormal_store
 * <AUTHOR>
@Data
@TableName("se_abnormal_store")
@Accessors(chain = true)
public class AbnormalStore implements Serializable {
    /**
     * 主键
     */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 门店编码
     */
    @TableField(value = "store_code")
    private String storeCode;

    /**
     * t_store表的id
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 门店的orgid
     */
    @TableField(value = "store_org_id")
    private Long storeOrgId;

    /**
     * 门店的名称
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 门店渠道
     */
    @TableField(value = "store_channel_code")
    private String storeChannelCode;

    /**
     * 经销商id
     */
    @TableField(value = "distributor_id")
    private Long distributorId;

    /**
     * 经销商编码
     */
    @TableField(value = "distributor_code")
    private String distributorCode;

    /**
     * 分公司id
     */
    @TableField(value = "company_id")
    private Long companyId;

    /**
     * 分公司编码
     */
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 分公司名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 大区编码
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 异常原因(字典值)
     */
    @TableField(value = "abnormal_reason")
    private String abnormalReason;

    /**
     * 状态
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 处理意见
     */
    @TableField(value = "solver_remark")
    private String solverRemark;

    /**
     * 原始值
     */
    @TableField(value = "original_value")
    private String originalValue;

    /**
     * 修改后的值
     */
    @TableField(value = "modified_value")
    private String modifiedValue;

    /**
     * 申请原因
     */
    @TableField(value = "apply_reason")
    private String applyReason;

    /**
     * 执行人
     */
    @TableField(value = "executer_id")
    private String executerId;

    /**
     * 执行人工号
     */
    @TableField(value = "executer_number")
    private String executerNumber;

    /**
     * 执行人姓名
     */
    @FieldEncrypt
    @TableField(value = "executer_name")
    private String executerName;

    /**
     * 整改备注
     */
    @TableField(value = "executer_remark")
    private String executerRemark;

    /**
     * 审核备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 是否删除0否，1删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted=0;

    /**
     * 创建人keycloak系统里的账户ID
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_date")
    private Date createdDate;

    /**
     * 修改人keycloak系统里的账户ID
     */
    @TableField(value = "modified_by")
    private String modifiedBy;

    /**
     * 修改时间
     */
    @TableField(value = "modified_date")
    private Date modifiedDate;

    /**
     * 修改人名称
     */
    @FieldEncrypt
    @TableField(value = "modified_user_name")
    private String modifiedUserName;

    private static final long serialVersionUID = 1L;

}