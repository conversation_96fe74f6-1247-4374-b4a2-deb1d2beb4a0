package com.fotile.activiti.workflow.phase.controller;

import com.alibaba.fastjson.JSONObject;
import com.fotile.activiti.workflow.client.UserClient;
import com.fotile.activiti.workflow.client.dto.org.AddStoreInDto;
import com.fotile.activiti.workflow.dict.pojo.Dict;
import com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreAudit;
import com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreQuery;
import com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreSaveDTO;
import com.fotile.activiti.workflow.phase.pojo.dto.abnormalStore.AbnormalStoreVo;
import com.fotile.activiti.workflow.phase.service.AbnormalStoreService;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常门店
 * Create By lubing on 2022/11/29.
 */
@RestController
@Api(value = "异常门店", tags = {"异常门店"})
@RequestMapping("/api/se/abnormalStore")
@Slf4j
public class AbnormalStoreController extends BaseController {
    @Resource
    private AbnormalStoreService abnormalStoreService;
    @Resource
    private UserClient userClient;

    /** app提报异常门店信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/appAdd", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result appAdd(@RequestBody AbnormalStoreSaveDTO dto){
            abnormalStoreService.appAdd(dto);
            return success("申请成功");
    }

    /** 未分配节点 分配调用
     * @param dto
     * @param result
     * @return
     */
    @RequestMapping(value = {"/batchSetExecutor","api/open/batchSetExecutor"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Boolean> batchSetExecutor(@RequestBody @Validated(AbnormalStoreAudit.BatchUpdate.class) AbnormalStoreAudit dto, BindingResult result){
        if(result.hasErrors()){
            String errMsg = result.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining("\n"));
            return failure(errMsg);
        }
        abnormalStoreService.batchSetExecutor(dto);
        return success("分配执行人成功");
    }


    /** 已处理待审核节点
     * @param dto
     * @return
     */
    @RequestMapping(value = "/batchAudit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result batchAudit(@RequestBody @Validated(AbnormalStoreAudit.BatchUpdate.class) AbnormalStoreAudit dto, BindingResult result){
        if(result.hasErrors()){
            String errMsg = result.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining("\n"));
            return failure(errMsg);
        }
        abnormalStoreService.batchAudit(dto);
        try {
            return success("提交成功");
        }catch (Exception e){
            return failure("提交失败，原因："+e.getMessage());
        }
    }

    /** 门店运营反馈提交节点
     * @param dto
     * @param result
     * @return
     */
    @RequestMapping(value ={"/submitReform","api/open/submitReform"} , method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result submitReform(@RequestBody @Validated(AbnormalStoreAudit.Update.class) AbnormalStoreAudit dto, BindingResult result){
        if(result.hasErrors()){
            String errMsg = result.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining("\n"));
            return failure(errMsg);
        }
        try {
            abnormalStoreService.submitReform(dto);
            return success("提交成功");
        }catch (Exception e){
            return failure("提交失败，原因："+e.getMessage());
        }
    }

    /**
     * 分页查询
     * @param query
     * @return
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<AbnormalStoreVo>> page(@RequestBody AbnormalStoreQuery query){
      PageInfo<AbnormalStoreVo> page = abnormalStoreService.queryPage(query);
      return success(page);
    }

    /** app列表查询
     * @param query
     * @return
     */
    @RequestMapping(value = "/appPage", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<AbnormalStoreVo>> appPage(@RequestBody AbnormalStoreQuery query){
        PageInfo<AbnormalStoreVo> page = abnormalStoreService.appPage(query);
        return success(page);
    }

    /** 详情
     * @param id
     * @return
     */
    @RequestMapping(value = {"/detail","api/open/detail"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<AbnormalStoreVo> detail(@RequestParam("id") Long id){
       AbnormalStoreVo abnormalStoreVo= abnormalStoreService.detail(id);
       return success(abnormalStoreVo);
    }


    /** 中台列表页面获取当前登陆人拥有的异常原因菜单
     * @return
     */
    @RequestMapping(value = "/getAuthAbnormalReasons", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Dict>> getAuthAbnormalReasons(){
        List<Dict> authAbnormalReasons = abnormalStoreService.getAuthAbnormalReasons();
        return success(authAbnormalReasons);
    }

    /** 查询出app上 属于当前登陆人的异常门店信息
     * @return
     */
    @RequestMapping(value = "/getAuthAbnormalStore", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<AbnormalStoreVo>> getAuthAbnormalStore(AddStoreInDto dto){
       List<AbnormalStoreVo> list= abnormalStoreService.getAuthAbnormalStore(dto);
       return success(list);
    }

    /**
     * 钉钉获取执行人下拉框
     *
     */
    @RequestMapping(value = "api/open/getDingExecuters", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getDingExecuters(@RequestBody JSONObject jsonObject){
       return success(userClient.queryEmployeePageAll(jsonObject));
    }

}
