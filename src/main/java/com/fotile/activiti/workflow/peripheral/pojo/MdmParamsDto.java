package com.fotile.activiti.workflow.peripheral.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MdmParamsDto implements Serializable {
    /**
     * 门店编码
     */
    private String zzmdbm;
    /**
     * 所属客户
     */
    private String zzsskh;
    /**
     * 门店名称
     */
    private String zzmdmc;
    /**
     * 门店渠道
     */
    private String zzmdqd;
    /**
     * 是否虚拟门店
     */
    private String zzsfxnmd;
    /**
     * 门店状态
     */
    private String zzmdzt;
    /**
     * 门店装修进度
     */
    private String zzmdzxjd;

    /**
     * 门店性质
     */

    private String zzmdxz;
    /**
     * 省份
     */
    private String zzsf;
    /**
     * 城市
     */
    private String zzcs;
    /**
     * 区县
     */
    private String zzqx;
    /**
     * 详细地址
     */
    private String zzxxdz;
    /**
     * 负责人
     */
    private String zzfzr;
    /**
     * 联系方式  暂时和门店座机电话使用同一个
     */
    private String zzlxfs;
    /**
     * 邮箱
     */
    private String zzyx;
    /**
     * 门店座机电话
     */
    private String zzmdzjdh;
    /**
     * 门店产权
     */
    private String zzmdcq;
    /**
     * 租用年限
     */
    private String zzzynx;
    /**
     * 门店业务类型
     */
    private String zzmdywlx;

    /**
     * 经度
     */
    private String jd;
    /**
     * 维度
     */
    private String wd;

    private String zzmdqdxf;

    /**
     * 门店尺寸
     */
    private String zmdcc;

    /**
     * 营业起始时间
     */
    private String zyyqssj;

    /**
     * 营业截止时间
     */
    private String zyyjzsj;

    /**
     * 显示地址
     */
    private String zxsdz;

    /**
     * 门店简称
     */
    private String zmdjc;

    /**
     * 展示渠道
     */
    private String zzsqd;

    private String zsjzzmj;

    /**
     * 门店所属办事处
     */
    private String zzmdssbsc	;
    /**
     * 启用时间
     */
    private String zzqysj	;
    /**
     * 停用时间
     */
    private String zztysj	;
    /**
     * 年租金
     */
    private String zznzj	;
    /**
     * 门店类型
     */
    private String  zzmdlx;

}
