package com.fotile.activiti.workflow.peripheral.pojo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021-04-15-19:14
 */
@Data
public class LargerOaApplyDto implements Serializable {

    private Integer storeId;

    /**
     * 标题
     * */
    private String title;

    /**
     * 装修预算
     * */
    private BigDecimal decorateBudget;

    /**
     * 预算项
     * */
    private String budgetCode;
    /**
     * eps预算单号
     * */
    private String epsBudgetCode;

}
