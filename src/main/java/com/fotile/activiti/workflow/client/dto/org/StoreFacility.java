package com.fotile.activiti.workflow.client.dto.org;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 新增门店基础设施实体对象
 *
 * <AUTHOR>
 */
@Data
public class StoreFacility extends AuditingEntity {

    /**
     * 组织机构表信息
     */
    @NotNull(message = "组织机构信息不能为空!")
    private AddOrgInDto addOrgInDto;

    /**
     * 门店信息
     */
    @NotNull(message = "门店信息不能为空!")
    private AddStoreInDto addStoreInDto;

    /**
     * 门店设施基础信息
     */
    private StoreFacilityEntity storeFacilityEntity;

    /**
     * 门店设施-社区店信息
     */
    private StoreFacilityCommunityEntity storeFacilityCommunityEntity;

    /**
     * 门店设施-非社区店信息
     */
    private StoreFacilityNonCommunityEntity storeFacilityNonCommunityEntity;

    /**
     * 操作人Id(内部参数)
     */
    private String operatorId;

    /**
     * 操作人姓名(内部参数)
     */
    private String operatorName;
}