package com.fotile.activiti.workflow.client.dto.user;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("查询所有用户账号信息(只返回账号id、名称)")
public class UserEntityExtendSimple implements Serializable{
	
	private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @NotNull(message="页码不能为空!")
    private Integer page;

    /**
     * 每页显示大小
     */
    @NotNull(message="每页显示条数不能为空!")
    private Integer size;

    /**
     *账号名称
     */
    private String userName;

    /**
     * 域
     */
    private String realmId;

    private String keyword;
}
