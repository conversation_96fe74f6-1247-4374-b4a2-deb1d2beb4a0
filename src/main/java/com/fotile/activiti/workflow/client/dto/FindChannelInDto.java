package com.fotile.activiti.workflow.client.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.orgcenter.distributor.pojo.dto
 * @date 2021/2/2 13:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindChannelInDto implements Serializable {
    private Long id;
    private String typeCode;
    private Long parentId;
    private Integer level;
    private String code;

    public FindChannelInDto(String typeCode) {
        this.typeCode = typeCode;
    }
}
