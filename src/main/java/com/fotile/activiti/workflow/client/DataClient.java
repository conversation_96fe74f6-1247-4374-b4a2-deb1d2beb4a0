package com.fotile.activiti.workflow.client;

import com.fotile.activiti.workflow.business.export.dto.GetExportTaskRecordInDTO;
import com.fotile.activiti.workflow.client.dto.ExportTaskRecord;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Create By lubing on 2022/3/7.
 */
@Component
@FeignClient(value = "data-center", path = "/api/exportTask")
public interface DataClient {
    /**
     *  创建任务
     * @param exportTaskRecord
     * @return
     */
    @RequestMapping(value = "/insertTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> insertTask(@RequestBody ExportTaskRecord exportTaskRecord);

    /**
     * 成功
     * @param exportTaskRecord
     * @return
     */
    @RequestMapping(value = "/api/open/successTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> successTask(@RequestBody ExportTaskRecord exportTaskRecord);

    /**
     * 更新失败并返回错误信息
     * @param exportTaskRecord
     * @return
     */
    @RequestMapping(value = "/api/open/failureTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> failureTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @ApiOperation(value = "根据type，状态查询任务")
    @RequestMapping(value = {"/api/open/getTaskByCode"}, method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ExportTaskRecord>> getTaskByCode(@SpringQueryMap GetExportTaskRecordInDTO exportTaskRecord);

    /**
     * 更新开始时间与状态
     * @param exportTaskRecord
     * @return
     */
    @RequestMapping(value = {"/api/open/startTask"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> startTask(@RequestBody ExportTaskRecord exportTaskRecord);
}
