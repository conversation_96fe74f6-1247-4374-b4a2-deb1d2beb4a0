package com.fotile.activiti.workflow.client.dto.org;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ChannelCategoryOutDto {
    private Long id;

    @ApiModelProperty("渠道大类名称")
    private String name;

    private String code;

    /**
     * 开店模式(1:专卖店/KA门店且是实体门店,2:社区服务店且是实体门店,3:其他渠道[默认])
     */
    private Integer openType;

    /**
     * 流程类型(1:电商/米博/办事处;2:经销商且KA/家装;3:经销商且实体)
     */
    private Integer bpmType;

    /**
     * 消息通知的接收人(工号)
     */
    private String msgRecipients;

    private Integer page=0;

    private Integer size=500;
}
