package com.fotile.activiti.workflow.client.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class QueryIpmsStoreDto {

    /**
     * 渠道大类CODE(多个半角逗号分隔)
     */
    private List<String> channelCategoryCodes;
    /**
     * 渠道细分Codes(多个半角逗号分隔)
     */
    private List<String> channelSubdivideCodes;

    private List<Long>  companyOrgIds;


    private Long companyId;

    private Long companyOrgId;

    private String companyName;

    private String regionCode;

    private String regionName;

    private Long storeId;

    private Long storeOrgId;

    private String storeCode;

    private String storeName;

    private String channelCategoryCode;

    /**
     * 客户渠道名称
     */
    private String channelCategoryName;

    private String channelSubdivideCode;

    private String distributorChannelCode;

    private String distributorChannelSubCode;

    private String storeChannelCode;
    /**
     * 门店渠道名称
     */
    private String storeChannelName;

    private BigDecimal epsUsableArea;

    private Integer sampleQuantity;

    private Integer saleQuantity;

    /**
     * 经销商id/所属客户id
     */
    private Integer distributorId;

    /**
     * 经销商编码/所属客户编码
     */
    private String distributorCode;

    /**
     * 经销商名称/所属客户
     */
    private String distributorName;

    /**
     * 1大区 2分公司 3门店
     */
    private Integer groupBy=1;

    /**
     * 大区code(字典：所属大区)
     */
    private String areaCode;

    /**
     * 大区名称
     */
    private String areaName;

    private Integer storeCount;

}
