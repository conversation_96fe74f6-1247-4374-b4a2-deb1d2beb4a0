package com.fotile.activiti.workflow.dict.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 门店渠道细分信息实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.activiti.workflow.dict.pojo
 * @date 2025/4/11 12:57
 */
@Data
public class StoreSubChannelInfo implements Serializable {
    /**
     * 门店渠道细分大类ID
     */
    private Long storeSubChannelFirstId;

    /**
     * 门店渠道细分大类编码
     */
    private String storeSubChannelFirstCode;

    /**
     * 门店渠道细分大类名称
     */
    private String storeSubChannelFirstName;

    /**
     * 门店渠道细分小类ID
     */
    private Long storeSubChannelSecondId;

    /**
     * 门店渠道细分小类编码
     */
    private String storeSubChannelSecondCode;

    /**
     * 门店渠道细分小类名称
     */
    private String storeSubChannelSecondName;

    /**
     * 门店渠道编码(填充值)
     */
    private String storeChannelCode;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 是否启用(1:是; 大小类同值)
     */
    private Integer active;

    /**
     * 是否删除(0:否; 大小类同值)
     */
    private Integer isDeleted;
}
