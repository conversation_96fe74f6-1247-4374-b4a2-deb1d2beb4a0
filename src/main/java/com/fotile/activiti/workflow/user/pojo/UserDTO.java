package com.fotile.activiti.workflow.user.pojo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fotile.activiti.workflow.client.dto.AreaSimpleOutDTO;
import com.fotile.activiti.workflow.client.dto.org.CompanyDto;
import com.fotile.activiti.workflow.utils.Query;
import com.fotile.activiti.workflow.utils.Save;
import com.fotile.activiti.workflow.utils.Update;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021-01-04-16:48
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Accessors(chain = true)
public class UserDTO {
    @NotNull(message = "id不能为空", groups = {Update.class})
    private Long id;

    /**
     * 用户id
     */
    @NotBlank(message = "userEntityId不可为空", groups = {Save.class, Update.class,BatchUpdateNewUser.class})
    private String userEntityId;

    @NotBlank(message = "流程Code不可为空", groups = {Save.class, Update.class})
    private String processCode;

    private String processName;

    /**
     * 工号
     */
    @NotBlank(message = "number不可为空", groups = {Save.class, Update.class,BatchUpdateNewUser.class})
    private String number;



    private String nameOrNumber;

    @FieldEncrypt
    private String nickname;

    @FieldEncrypt
    @NotBlank(message = "姓名不可为空",groups = {BatchUpdateNewUser.class})
    private String firstName;

    @FieldEncrypt
    @NotBlank(message = "username不可为空",groups = {BatchUpdateNewUser.class})
    private String username;

    @FieldEncrypt
    @NotBlank(message = "手机号不可为空",groups = {BatchUpdateNewUser.class})
    private String phoneNumber;

    // @NotNull(message = "companyId不可为空",groups = {Save.class,Update.class})
    private Long companyId;

    private Set<Long> companyIds;

    private List<CompanyDto> companys;

    private String companyName;

    //  @NotBlank(message = "regionCode不可为空",groups = {Save.class})
    private String regionCode;

    private Long regionId;

    private List<String> regionCodes;

    private List<AreaSimpleOutDTO> regions;

    private String regionName;

    @NotNull(message = "roleId不可为空", groups = {Save.class, Update.class})
    private Long roleId;
    @NotNull(message = "roleType不可为空", groups = {Save.class, Update.class})
    private Integer roleType;

    private String roleName;

    /**
     * 员工当前状态 1 在职 0离职
     */
    @NotNull(message = "员工当前状态不能为空", groups = {Save.class, Update.class,BatchUpdateNewUser.class})
    private Integer employeeStatus;

    /**
     * 广东下的分区
     */
    private String zone;

    private String zoneName;

    private String createdBy;

    @FieldEncrypt
    private String createdUserName;

    private Date createdDate;

    private String modifiedBy;

    @FieldEncrypt
    private String modifiedUserName;

    private Date modifiedDate;

    private Byte isDeleted;

    private Boolean isShow;

    @ApiModelProperty("当前页数")
    @NotNull(message = "当前页数不能为空", groups = Query.class)
    private Integer pageNum;

    @ApiModelProperty("每页多少条数据")
    @NotNull(message = "每页多少条数据不能为空", groups = Query.class)
    private Integer pageSize;

    @JsonAlias({"employeeId","number"})
    public UserDTO setNumber(String number) {
        this.number = number;
        return this;
    }


    public interface BatchUpdateNewUser{}
}
