package com.fotile.shopordercenter.test.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.shopordercenter.test.pojo.entity.TempOrderDeallogpic;

/**
* <AUTHOR>
* @description 针对表【temp_order_deallogpic】的数据库操作Mapper
* @createDate 2022-07-05 11:07:20
* @Entity com.fotile.shopordercenter.test.pojo.entity.TempOrderDeallogpic
*/
public interface TempOrderDeallogpicMapper extends BaseMapper<TempOrderDeallogpic> {

    int deleteByPrimaryKey(Long id);

    int insert(TempOrderDeallogpic record);

    int insertSelective(TempOrderDeallogpic record);

    TempOrderDeallogpic selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TempOrderDeallogpic record);

    int updateByPrimaryKey(TempOrderDeallogpic record);

}
