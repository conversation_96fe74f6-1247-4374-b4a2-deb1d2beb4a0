package com.fotile.shopordercenter.test.pojo.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品表
 * @TableName goods
 */
public class Goods implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 是否删除 0未删除 1删除
     */
    private Long isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改日期
     */
    private Date modifiedDate;

    /**
     * 商品分类id
     */
    private Long goodsCategoryId;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 型号
     */
    private String modelNum;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 备注
     */
    private String note;

    /**
     * 商品类型 字典类型: goods_type 1-实物单品 2-实物组合 3-虚拟商品
     */
    private Integer type;

    /**
     * 启用状态 0：禁用；1：启用
     */
    private String stage;

    /**
     * 商品排序
     */
    private Long sort;

    /**
     * guid
     */
    private String guid;

    /**
     * 是否是系列主品，1是0否
     */
    private Integer seriesMain;

    /**
     * 是否新品，1是0否
     */
    private Integer newProducts;

    /**
     * 设置为新品时间
     */
    private Date newProductsDate;

    /**
     * 最新编辑人姓名
     */
    private String modifiedName;

    /**
     * 1.mdm2.人工
     */
    private Integer source;

    /**
     * 简化型号
     */
    private String zjhxh;

    /**
     * 产品生命周期状态
     */
    private String zcpsmzqzt;

    /**
     * 渠道销售状态，只接受零售渠道 
     */
    private String zqdzt;

    /**
     * 旧商品编码
     */
    private String oldCode;

    /**
     * 销售渠道 1.经销商渠道 2.KA渠道
     */
    private Integer salesChannel;

    /**
     * 产品小类描述
     */
    private String zzcpxlt;

    /**
     * 产品线描述
     */
    private String zzcpxtNe;

    /**
     * 产品组
     */
    private String vtext;

    /**
     * 产品品类
     */
    private String zcpxt;

    /**
     * 产品线编码
     */
    private String zzcpxNew;

    /**
     * 产品小类编码
     */
    private String zzcpxl;

    /**
     * 产品组code
     */
    private String spart;

    /**
     * 品类code
     */
    private String zzcpx;

    /**
     * 积分商城商品code
     */
    private String pointsProductCode;

    /**
     * 是否系列品
     */
    private Long isSeriesProducts;

    /**
     *  是否热销，1是0否
     */
    private String hotProducts;

    /**
     * 热销截至时间
     */
    private Date hotProductsDate;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Long getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public Long getIsDeleted() {
        return isDeleted;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建日期
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * 创建日期
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    /**
     * 修改日期
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * 修改日期
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * 商品分类id
     */
    public Long getGoodsCategoryId() {
        return goodsCategoryId;
    }

    /**
     * 商品分类id
     */
    public void setGoodsCategoryId(Long goodsCategoryId) {
        this.goodsCategoryId = goodsCategoryId;
    }

    /**
     * 商品编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 商品编码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 型号
     */
    public String getModelNum() {
        return modelNum;
    }

    /**
     * 型号
     */
    public void setModelNum(String modelNum) {
        this.modelNum = modelNum;
    }

    /**
     * 商品名称
     */
    public String getName() {
        return name;
    }

    /**
     * 商品名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 备注
     */
    public String getNote() {
        return note;
    }

    /**
     * 备注
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     * 商品类型 字典类型: goods_type 1-实物单品 2-实物组合 3-虚拟商品
     */
    public Integer getType() {
        return type;
    }

    /**
     * 商品类型 字典类型: goods_type 1-实物单品 2-实物组合 3-虚拟商品
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 启用状态 0：禁用；1：启用
     */
    public String getStage() {
        return stage;
    }

    /**
     * 启用状态 0：禁用；1：启用
     */
    public void setStage(String stage) {
        this.stage = stage;
    }

    /**
     * 商品排序
     */
    public Long getSort() {
        return sort;
    }

    /**
     * 商品排序
     */
    public void setSort(Long sort) {
        this.sort = sort;
    }

    /**
     * guid
     */
    public String getGuid() {
        return guid;
    }

    /**
     * guid
     */
    public void setGuid(String guid) {
        this.guid = guid;
    }

    /**
     * 是否是系列主品，1是0否
     */
    public Integer getSeriesMain() {
        return seriesMain;
    }

    /**
     * 是否是系列主品，1是0否
     */
    public void setSeriesMain(Integer seriesMain) {
        this.seriesMain = seriesMain;
    }

    /**
     * 是否新品，1是0否
     */
    public Integer getNewProducts() {
        return newProducts;
    }

    /**
     * 是否新品，1是0否
     */
    public void setNewProducts(Integer newProducts) {
        this.newProducts = newProducts;
    }

    /**
     * 设置为新品时间
     */
    public Date getNewProductsDate() {
        return newProductsDate;
    }

    /**
     * 设置为新品时间
     */
    public void setNewProductsDate(Date newProductsDate) {
        this.newProductsDate = newProductsDate;
    }

    /**
     * 最新编辑人姓名
     */
    public String getModifiedName() {
        return modifiedName;
    }

    /**
     * 最新编辑人姓名
     */
    public void setModifiedName(String modifiedName) {
        this.modifiedName = modifiedName;
    }

    /**
     * 1.mdm2.人工
     */
    public Integer getSource() {
        return source;
    }

    /**
     * 1.mdm2.人工
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 简化型号
     */
    public String getZjhxh() {
        return zjhxh;
    }

    /**
     * 简化型号
     */
    public void setZjhxh(String zjhxh) {
        this.zjhxh = zjhxh;
    }

    /**
     * 产品生命周期状态
     */
    public String getZcpsmzqzt() {
        return zcpsmzqzt;
    }

    /**
     * 产品生命周期状态
     */
    public void setZcpsmzqzt(String zcpsmzqzt) {
        this.zcpsmzqzt = zcpsmzqzt;
    }

    /**
     * 渠道销售状态，只接受零售渠道 
     */
    public String getZqdzt() {
        return zqdzt;
    }

    /**
     * 渠道销售状态，只接受零售渠道 
     */
    public void setZqdzt(String zqdzt) {
        this.zqdzt = zqdzt;
    }

    /**
     * 旧商品编码
     */
    public String getOldCode() {
        return oldCode;
    }

    /**
     * 旧商品编码
     */
    public void setOldCode(String oldCode) {
        this.oldCode = oldCode;
    }

    /**
     * 销售渠道 1.经销商渠道 2.KA渠道
     */
    public Integer getSalesChannel() {
        return salesChannel;
    }

    /**
     * 销售渠道 1.经销商渠道 2.KA渠道
     */
    public void setSalesChannel(Integer salesChannel) {
        this.salesChannel = salesChannel;
    }

    /**
     * 产品小类描述
     */
    public String getZzcpxlt() {
        return zzcpxlt;
    }

    /**
     * 产品小类描述
     */
    public void setZzcpxlt(String zzcpxlt) {
        this.zzcpxlt = zzcpxlt;
    }

    /**
     * 产品线描述
     */
    public String getZzcpxtNe() {
        return zzcpxtNe;
    }

    /**
     * 产品线描述
     */
    public void setZzcpxtNe(String zzcpxtNe) {
        this.zzcpxtNe = zzcpxtNe;
    }

    /**
     * 产品组
     */
    public String getVtext() {
        return vtext;
    }

    /**
     * 产品组
     */
    public void setVtext(String vtext) {
        this.vtext = vtext;
    }

    /**
     * 产品品类
     */
    public String getZcpxt() {
        return zcpxt;
    }

    /**
     * 产品品类
     */
    public void setZcpxt(String zcpxt) {
        this.zcpxt = zcpxt;
    }

    /**
     * 产品线编码
     */
    public String getZzcpxNew() {
        return zzcpxNew;
    }

    /**
     * 产品线编码
     */
    public void setZzcpxNew(String zzcpxNew) {
        this.zzcpxNew = zzcpxNew;
    }

    /**
     * 产品小类编码
     */
    public String getZzcpxl() {
        return zzcpxl;
    }

    /**
     * 产品小类编码
     */
    public void setZzcpxl(String zzcpxl) {
        this.zzcpxl = zzcpxl;
    }

    /**
     * 产品组code
     */
    public String getSpart() {
        return spart;
    }

    /**
     * 产品组code
     */
    public void setSpart(String spart) {
        this.spart = spart;
    }

    /**
     * 品类code
     */
    public String getZzcpx() {
        return zzcpx;
    }

    /**
     * 品类code
     */
    public void setZzcpx(String zzcpx) {
        this.zzcpx = zzcpx;
    }

    /**
     * 积分商城商品code
     */
    public String getPointsProductCode() {
        return pointsProductCode;
    }

    /**
     * 积分商城商品code
     */
    public void setPointsProductCode(String pointsProductCode) {
        this.pointsProductCode = pointsProductCode;
    }

    /**
     * 是否系列品
     */
    public Long getIsSeriesProducts() {
        return isSeriesProducts;
    }

    /**
     * 是否系列品
     */
    public void setIsSeriesProducts(Long isSeriesProducts) {
        this.isSeriesProducts = isSeriesProducts;
    }

    /**
     *  是否热销，1是0否
     */
    public String getHotProducts() {
        return hotProducts;
    }

    /**
     *  是否热销，1是0否
     */
    public void setHotProducts(String hotProducts) {
        this.hotProducts = hotProducts;
    }

    /**
     * 热销截至时间
     */
    public Date getHotProductsDate() {
        return hotProductsDate;
    }

    /**
     * 热销截至时间
     */
    public void setHotProductsDate(Date hotProductsDate) {
        this.hotProductsDate = hotProductsDate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Goods other = (Goods) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDate() == null ? other.getCreatedDate() == null : this.getCreatedDate().equals(other.getCreatedDate()))
            && (this.getModifiedBy() == null ? other.getModifiedBy() == null : this.getModifiedBy().equals(other.getModifiedBy()))
            && (this.getModifiedDate() == null ? other.getModifiedDate() == null : this.getModifiedDate().equals(other.getModifiedDate()))
            && (this.getGoodsCategoryId() == null ? other.getGoodsCategoryId() == null : this.getGoodsCategoryId().equals(other.getGoodsCategoryId()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getModelNum() == null ? other.getModelNum() == null : this.getModelNum().equals(other.getModelNum()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getStage() == null ? other.getStage() == null : this.getStage().equals(other.getStage()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getGuid() == null ? other.getGuid() == null : this.getGuid().equals(other.getGuid()))
            && (this.getSeriesMain() == null ? other.getSeriesMain() == null : this.getSeriesMain().equals(other.getSeriesMain()))
            && (this.getNewProducts() == null ? other.getNewProducts() == null : this.getNewProducts().equals(other.getNewProducts()))
            && (this.getNewProductsDate() == null ? other.getNewProductsDate() == null : this.getNewProductsDate().equals(other.getNewProductsDate()))
            && (this.getModifiedName() == null ? other.getModifiedName() == null : this.getModifiedName().equals(other.getModifiedName()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getZjhxh() == null ? other.getZjhxh() == null : this.getZjhxh().equals(other.getZjhxh()))
            && (this.getZcpsmzqzt() == null ? other.getZcpsmzqzt() == null : this.getZcpsmzqzt().equals(other.getZcpsmzqzt()))
            && (this.getZqdzt() == null ? other.getZqdzt() == null : this.getZqdzt().equals(other.getZqdzt()))
            && (this.getOldCode() == null ? other.getOldCode() == null : this.getOldCode().equals(other.getOldCode()))
            && (this.getSalesChannel() == null ? other.getSalesChannel() == null : this.getSalesChannel().equals(other.getSalesChannel()))
            && (this.getZzcpxlt() == null ? other.getZzcpxlt() == null : this.getZzcpxlt().equals(other.getZzcpxlt()))
            && (this.getZzcpxtNe() == null ? other.getZzcpxtNe() == null : this.getZzcpxtNe().equals(other.getZzcpxtNe()))
            && (this.getVtext() == null ? other.getVtext() == null : this.getVtext().equals(other.getVtext()))
            && (this.getZcpxt() == null ? other.getZcpxt() == null : this.getZcpxt().equals(other.getZcpxt()))
            && (this.getZzcpxNew() == null ? other.getZzcpxNew() == null : this.getZzcpxNew().equals(other.getZzcpxNew()))
            && (this.getZzcpxl() == null ? other.getZzcpxl() == null : this.getZzcpxl().equals(other.getZzcpxl()))
            && (this.getSpart() == null ? other.getSpart() == null : this.getSpart().equals(other.getSpart()))
            && (this.getZzcpx() == null ? other.getZzcpx() == null : this.getZzcpx().equals(other.getZzcpx()))
            && (this.getPointsProductCode() == null ? other.getPointsProductCode() == null : this.getPointsProductCode().equals(other.getPointsProductCode()))
            && (this.getIsSeriesProducts() == null ? other.getIsSeriesProducts() == null : this.getIsSeriesProducts().equals(other.getIsSeriesProducts()))
            && (this.getHotProducts() == null ? other.getHotProducts() == null : this.getHotProducts().equals(other.getHotProducts()))
            && (this.getHotProductsDate() == null ? other.getHotProductsDate() == null : this.getHotProductsDate().equals(other.getHotProductsDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDate() == null) ? 0 : getCreatedDate().hashCode());
        result = prime * result + ((getModifiedBy() == null) ? 0 : getModifiedBy().hashCode());
        result = prime * result + ((getModifiedDate() == null) ? 0 : getModifiedDate().hashCode());
        result = prime * result + ((getGoodsCategoryId() == null) ? 0 : getGoodsCategoryId().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getModelNum() == null) ? 0 : getModelNum().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getStage() == null) ? 0 : getStage().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getGuid() == null) ? 0 : getGuid().hashCode());
        result = prime * result + ((getSeriesMain() == null) ? 0 : getSeriesMain().hashCode());
        result = prime * result + ((getNewProducts() == null) ? 0 : getNewProducts().hashCode());
        result = prime * result + ((getNewProductsDate() == null) ? 0 : getNewProductsDate().hashCode());
        result = prime * result + ((getModifiedName() == null) ? 0 : getModifiedName().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getZjhxh() == null) ? 0 : getZjhxh().hashCode());
        result = prime * result + ((getZcpsmzqzt() == null) ? 0 : getZcpsmzqzt().hashCode());
        result = prime * result + ((getZqdzt() == null) ? 0 : getZqdzt().hashCode());
        result = prime * result + ((getOldCode() == null) ? 0 : getOldCode().hashCode());
        result = prime * result + ((getSalesChannel() == null) ? 0 : getSalesChannel().hashCode());
        result = prime * result + ((getZzcpxlt() == null) ? 0 : getZzcpxlt().hashCode());
        result = prime * result + ((getZzcpxtNe() == null) ? 0 : getZzcpxtNe().hashCode());
        result = prime * result + ((getVtext() == null) ? 0 : getVtext().hashCode());
        result = prime * result + ((getZcpxt() == null) ? 0 : getZcpxt().hashCode());
        result = prime * result + ((getZzcpxNew() == null) ? 0 : getZzcpxNew().hashCode());
        result = prime * result + ((getZzcpxl() == null) ? 0 : getZzcpxl().hashCode());
        result = prime * result + ((getSpart() == null) ? 0 : getSpart().hashCode());
        result = prime * result + ((getZzcpx() == null) ? 0 : getZzcpx().hashCode());
        result = prime * result + ((getPointsProductCode() == null) ? 0 : getPointsProductCode().hashCode());
        result = prime * result + ((getIsSeriesProducts() == null) ? 0 : getIsSeriesProducts().hashCode());
        result = prime * result + ((getHotProducts() == null) ? 0 : getHotProducts().hashCode());
        result = prime * result + ((getHotProductsDate() == null) ? 0 : getHotProductsDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", goodsCategoryId=").append(goodsCategoryId);
        sb.append(", code=").append(code);
        sb.append(", modelNum=").append(modelNum);
        sb.append(", name=").append(name);
        sb.append(", note=").append(note);
        sb.append(", type=").append(type);
        sb.append(", stage=").append(stage);
        sb.append(", sort=").append(sort);
        sb.append(", guid=").append(guid);
        sb.append(", seriesMain=").append(seriesMain);
        sb.append(", newProducts=").append(newProducts);
        sb.append(", newProductsDate=").append(newProductsDate);
        sb.append(", modifiedName=").append(modifiedName);
        sb.append(", source=").append(source);
        sb.append(", zjhxh=").append(zjhxh);
        sb.append(", zcpsmzqzt=").append(zcpsmzqzt);
        sb.append(", zqdzt=").append(zqdzt);
        sb.append(", oldCode=").append(oldCode);
        sb.append(", salesChannel=").append(salesChannel);
        sb.append(", zzcpxlt=").append(zzcpxlt);
        sb.append(", zzcpxtNe=").append(zzcpxtNe);
        sb.append(", vtext=").append(vtext);
        sb.append(", zcpxt=").append(zcpxt);
        sb.append(", zzcpxNew=").append(zzcpxNew);
        sb.append(", zzcpxl=").append(zzcpxl);
        sb.append(", spart=").append(spart);
        sb.append(", zzcpx=").append(zzcpx);
        sb.append(", pointsProductCode=").append(pointsProductCode);
        sb.append(", isSeriesProducts=").append(isSeriesProducts);
        sb.append(", hotProducts=").append(hotProducts);
        sb.append(", hotProductsDate=").append(hotProductsDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}