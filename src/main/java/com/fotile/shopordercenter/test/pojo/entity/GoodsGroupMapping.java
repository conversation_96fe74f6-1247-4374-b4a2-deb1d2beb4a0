package com.fotile.shopordercenter.test.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 组合商品关联表
 * @TableName goods_group_mapping
 */
public class GoodsGroupMapping implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 组合商品编码
     */
    private String groupProductCode;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品价格
     */
    private BigDecimal goodsPrice;

    /**
     * 组合内数量
     */
    private Long num;

    /**
     * 商品型号
     */
    private String goodsModel;

    /**
     * 是否系列主品
     */
    private String seriesMain;

    /**
     * 是否系列品
     */
    private String isSeries;

    /**
     * 商品分类
     */
    private String goodsCategory;

    /**
     * 主数据分类
     */
    private String dataType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 编辑人名称
     */
    private String modifiedByName;

    /**
     * 是否删除 0未删除 1删除
     */
    private Long isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改日期
     */
    private Date modifiedDate;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 组合商品编码
     */
    public String getGroupProductCode() {
        return groupProductCode;
    }

    /**
     * 组合商品编码
     */
    public void setGroupProductCode(String groupProductCode) {
        this.groupProductCode = groupProductCode;
    }

    /**
     * 商品编码
     */
    public String getGoodsCode() {
        return goodsCode;
    }

    /**
     * 商品编码
     */
    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    /**
     * 商品价格
     */
    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    /**
     * 商品价格
     */
    public void setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    /**
     * 组合内数量
     */
    public Long getNum() {
        return num;
    }

    /**
     * 组合内数量
     */
    public void setNum(Long num) {
        this.num = num;
    }

    /**
     * 商品型号
     */
    public String getGoodsModel() {
        return goodsModel;
    }

    /**
     * 商品型号
     */
    public void setGoodsModel(String goodsModel) {
        this.goodsModel = goodsModel;
    }

    /**
     * 是否系列主品
     */
    public String getSeriesMain() {
        return seriesMain;
    }

    /**
     * 是否系列主品
     */
    public void setSeriesMain(String seriesMain) {
        this.seriesMain = seriesMain;
    }

    /**
     * 是否系列品
     */
    public String getIsSeries() {
        return isSeries;
    }

    /**
     * 是否系列品
     */
    public void setIsSeries(String isSeries) {
        this.isSeries = isSeries;
    }

    /**
     * 商品分类
     */
    public String getGoodsCategory() {
        return goodsCategory;
    }

    /**
     * 商品分类
     */
    public void setGoodsCategory(String goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    /**
     * 主数据分类
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * 主数据分类
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * 创建人名称
     */
    public String getCreatedByName() {
        return createdByName;
    }

    /**
     * 创建人名称
     */
    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    /**
     * 编辑人名称
     */
    public String getModifiedByName() {
        return modifiedByName;
    }

    /**
     * 编辑人名称
     */
    public void setModifiedByName(String modifiedByName) {
        this.modifiedByName = modifiedByName;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public Long getIsDeleted() {
        return isDeleted;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建日期
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * 创建日期
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    /**
     * 修改日期
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * 修改日期
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GoodsGroupMapping other = (GoodsGroupMapping) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGroupProductCode() == null ? other.getGroupProductCode() == null : this.getGroupProductCode().equals(other.getGroupProductCode()))
            && (this.getGoodsCode() == null ? other.getGoodsCode() == null : this.getGoodsCode().equals(other.getGoodsCode()))
            && (this.getGoodsPrice() == null ? other.getGoodsPrice() == null : this.getGoodsPrice().equals(other.getGoodsPrice()))
            && (this.getNum() == null ? other.getNum() == null : this.getNum().equals(other.getNum()))
            && (this.getGoodsModel() == null ? other.getGoodsModel() == null : this.getGoodsModel().equals(other.getGoodsModel()))
            && (this.getSeriesMain() == null ? other.getSeriesMain() == null : this.getSeriesMain().equals(other.getSeriesMain()))
            && (this.getIsSeries() == null ? other.getIsSeries() == null : this.getIsSeries().equals(other.getIsSeries()))
            && (this.getGoodsCategory() == null ? other.getGoodsCategory() == null : this.getGoodsCategory().equals(other.getGoodsCategory()))
            && (this.getDataType() == null ? other.getDataType() == null : this.getDataType().equals(other.getDataType()))
            && (this.getGoodsName() == null ? other.getGoodsName() == null : this.getGoodsName().equals(other.getGoodsName()))
            && (this.getCreatedByName() == null ? other.getCreatedByName() == null : this.getCreatedByName().equals(other.getCreatedByName()))
            && (this.getModifiedByName() == null ? other.getModifiedByName() == null : this.getModifiedByName().equals(other.getModifiedByName()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDate() == null ? other.getCreatedDate() == null : this.getCreatedDate().equals(other.getCreatedDate()))
            && (this.getModifiedBy() == null ? other.getModifiedBy() == null : this.getModifiedBy().equals(other.getModifiedBy()))
            && (this.getModifiedDate() == null ? other.getModifiedDate() == null : this.getModifiedDate().equals(other.getModifiedDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGroupProductCode() == null) ? 0 : getGroupProductCode().hashCode());
        result = prime * result + ((getGoodsCode() == null) ? 0 : getGoodsCode().hashCode());
        result = prime * result + ((getGoodsPrice() == null) ? 0 : getGoodsPrice().hashCode());
        result = prime * result + ((getNum() == null) ? 0 : getNum().hashCode());
        result = prime * result + ((getGoodsModel() == null) ? 0 : getGoodsModel().hashCode());
        result = prime * result + ((getSeriesMain() == null) ? 0 : getSeriesMain().hashCode());
        result = prime * result + ((getIsSeries() == null) ? 0 : getIsSeries().hashCode());
        result = prime * result + ((getGoodsCategory() == null) ? 0 : getGoodsCategory().hashCode());
        result = prime * result + ((getDataType() == null) ? 0 : getDataType().hashCode());
        result = prime * result + ((getGoodsName() == null) ? 0 : getGoodsName().hashCode());
        result = prime * result + ((getCreatedByName() == null) ? 0 : getCreatedByName().hashCode());
        result = prime * result + ((getModifiedByName() == null) ? 0 : getModifiedByName().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDate() == null) ? 0 : getCreatedDate().hashCode());
        result = prime * result + ((getModifiedBy() == null) ? 0 : getModifiedBy().hashCode());
        result = prime * result + ((getModifiedDate() == null) ? 0 : getModifiedDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupProductCode=").append(groupProductCode);
        sb.append(", goodsCode=").append(goodsCode);
        sb.append(", goodsPrice=").append(goodsPrice);
        sb.append(", num=").append(num);
        sb.append(", goodsModel=").append(goodsModel);
        sb.append(", seriesMain=").append(seriesMain);
        sb.append(", isSeries=").append(isSeries);
        sb.append(", goodsCategory=").append(goodsCategory);
        sb.append(", dataType=").append(dataType);
        sb.append(", goodsName=").append(goodsName);
        sb.append(", createdByName=").append(createdByName);
        sb.append(", modifiedByName=").append(modifiedByName);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}