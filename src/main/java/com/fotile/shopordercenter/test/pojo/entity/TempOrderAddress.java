package com.fotile.shopordercenter.test.pojo.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName temp_order_address
 */
public class TempOrderAddress implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * 关联订单号
     */
    private Integer orderid;

    /**
     * 
     */
    private Integer countryid;

    /**
     * 省
     */
    private Integer provinceid;

    /**
     * 市
     */
    private Integer cityid;

    /**
     * 区县
     */
    private Integer regionid;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 收件人
     */
    private String receiver;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 联系手机
     */
    private String mobilephone;

    /**
     * 邮编
     */
    private String zip;

    /**
     * 
     */
    private String email;

    /**
     * 
     */
    private Integer isvalid;

    /**
     * 关联品牌商ID
     */
    private Integer sysbrandid;

    /**
     * 
     */
    private Date updatedate;

    /**
     * 创建时间
     */
    private Date createdate;

    /**
     * 
     */
    private String updateuserid;

    /**
     * 
     */
    private String createloginid;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 关联订单号
     */
    public Integer getOrderid() {
        return orderid;
    }

    /**
     * 关联订单号
     */
    public void setOrderid(Integer orderid) {
        this.orderid = orderid;
    }

    /**
     * 
     */
    public Integer getCountryid() {
        return countryid;
    }

    /**
     * 
     */
    public void setCountryid(Integer countryid) {
        this.countryid = countryid;
    }

    /**
     * 省
     */
    public Integer getProvinceid() {
        return provinceid;
    }

    /**
     * 省
     */
    public void setProvinceid(Integer provinceid) {
        this.provinceid = provinceid;
    }

    /**
     * 市
     */
    public Integer getCityid() {
        return cityid;
    }

    /**
     * 市
     */
    public void setCityid(Integer cityid) {
        this.cityid = cityid;
    }

    /**
     * 区县
     */
    public Integer getRegionid() {
        return regionid;
    }

    /**
     * 区县
     */
    public void setRegionid(Integer regionid) {
        this.regionid = regionid;
    }

    /**
     * 详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 详细地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 收件人
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 收件人
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    /**
     * 联系电话
     */
    public String getTel() {
        return tel;
    }

    /**
     * 联系电话
     */
    public void setTel(String tel) {
        this.tel = tel;
    }

    /**
     * 联系手机
     */
    public String getMobilephone() {
        return mobilephone;
    }

    /**
     * 联系手机
     */
    public void setMobilephone(String mobilephone) {
        this.mobilephone = mobilephone;
    }

    /**
     * 邮编
     */
    public String getZip() {
        return zip;
    }

    /**
     * 邮编
     */
    public void setZip(String zip) {
        this.zip = zip;
    }

    /**
     * 
     */
    public String getEmail() {
        return email;
    }

    /**
     * 
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 
     */
    public Integer getIsvalid() {
        return isvalid;
    }

    /**
     * 
     */
    public void setIsvalid(Integer isvalid) {
        this.isvalid = isvalid;
    }

    /**
     * 关联品牌商ID
     */
    public Integer getSysbrandid() {
        return sysbrandid;
    }

    /**
     * 关联品牌商ID
     */
    public void setSysbrandid(Integer sysbrandid) {
        this.sysbrandid = sysbrandid;
    }

    /**
     * 
     */
    public Date getUpdatedate() {
        return updatedate;
    }

    /**
     * 
     */
    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    /**
     * 创建时间
     */
    public Date getCreatedate() {
        return createdate;
    }

    /**
     * 创建时间
     */
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    /**
     * 
     */
    public String getUpdateuserid() {
        return updateuserid;
    }

    /**
     * 
     */
    public void setUpdateuserid(String updateuserid) {
        this.updateuserid = updateuserid;
    }

    /**
     * 
     */
    public String getCreateloginid() {
        return createloginid;
    }

    /**
     * 
     */
    public void setCreateloginid(String createloginid) {
        this.createloginid = createloginid;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TempOrderAddress other = (TempOrderAddress) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderid() == null ? other.getOrderid() == null : this.getOrderid().equals(other.getOrderid()))
            && (this.getCountryid() == null ? other.getCountryid() == null : this.getCountryid().equals(other.getCountryid()))
            && (this.getProvinceid() == null ? other.getProvinceid() == null : this.getProvinceid().equals(other.getProvinceid()))
            && (this.getCityid() == null ? other.getCityid() == null : this.getCityid().equals(other.getCityid()))
            && (this.getRegionid() == null ? other.getRegionid() == null : this.getRegionid().equals(other.getRegionid()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getReceiver() == null ? other.getReceiver() == null : this.getReceiver().equals(other.getReceiver()))
            && (this.getTel() == null ? other.getTel() == null : this.getTel().equals(other.getTel()))
            && (this.getMobilephone() == null ? other.getMobilephone() == null : this.getMobilephone().equals(other.getMobilephone()))
            && (this.getZip() == null ? other.getZip() == null : this.getZip().equals(other.getZip()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getIsvalid() == null ? other.getIsvalid() == null : this.getIsvalid().equals(other.getIsvalid()))
            && (this.getSysbrandid() == null ? other.getSysbrandid() == null : this.getSysbrandid().equals(other.getSysbrandid()))
            && (this.getUpdatedate() == null ? other.getUpdatedate() == null : this.getUpdatedate().equals(other.getUpdatedate()))
            && (this.getCreatedate() == null ? other.getCreatedate() == null : this.getCreatedate().equals(other.getCreatedate()))
            && (this.getUpdateuserid() == null ? other.getUpdateuserid() == null : this.getUpdateuserid().equals(other.getUpdateuserid()))
            && (this.getCreateloginid() == null ? other.getCreateloginid() == null : this.getCreateloginid().equals(other.getCreateloginid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderid() == null) ? 0 : getOrderid().hashCode());
        result = prime * result + ((getCountryid() == null) ? 0 : getCountryid().hashCode());
        result = prime * result + ((getProvinceid() == null) ? 0 : getProvinceid().hashCode());
        result = prime * result + ((getCityid() == null) ? 0 : getCityid().hashCode());
        result = prime * result + ((getRegionid() == null) ? 0 : getRegionid().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getReceiver() == null) ? 0 : getReceiver().hashCode());
        result = prime * result + ((getTel() == null) ? 0 : getTel().hashCode());
        result = prime * result + ((getMobilephone() == null) ? 0 : getMobilephone().hashCode());
        result = prime * result + ((getZip() == null) ? 0 : getZip().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getIsvalid() == null) ? 0 : getIsvalid().hashCode());
        result = prime * result + ((getSysbrandid() == null) ? 0 : getSysbrandid().hashCode());
        result = prime * result + ((getUpdatedate() == null) ? 0 : getUpdatedate().hashCode());
        result = prime * result + ((getCreatedate() == null) ? 0 : getCreatedate().hashCode());
        result = prime * result + ((getUpdateuserid() == null) ? 0 : getUpdateuserid().hashCode());
        result = prime * result + ((getCreateloginid() == null) ? 0 : getCreateloginid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderid=").append(orderid);
        sb.append(", countryid=").append(countryid);
        sb.append(", provinceid=").append(provinceid);
        sb.append(", cityid=").append(cityid);
        sb.append(", regionid=").append(regionid);
        sb.append(", address=").append(address);
        sb.append(", receiver=").append(receiver);
        sb.append(", tel=").append(tel);
        sb.append(", mobilephone=").append(mobilephone);
        sb.append(", zip=").append(zip);
        sb.append(", email=").append(email);
        sb.append(", isvalid=").append(isvalid);
        sb.append(", sysbrandid=").append(sysbrandid);
        sb.append(", updatedate=").append(updatedate);
        sb.append(", createdate=").append(createdate);
        sb.append(", updateuserid=").append(updateuserid);
        sb.append(", createloginid=").append(createloginid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}