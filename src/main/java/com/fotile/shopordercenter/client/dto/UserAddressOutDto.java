package com.fotile.shopordercenter.client.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserAddressOutDto implements Serializable {
    private Long id;

    /**
     * 顾客id
     */
    @TableField(value = "customer_info_id")
    private Long customerInfoId;

    /**
     * 是否为默认地址, 0-不是 1-是
     */
    @TableField(value = "is_default")
    private Integer isDefault;

    /**
     * 收货人姓名
     */
    @TableField(value = "name")
    private String name;

    /**
     * 联系方式联系方式
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 省id
     */
    @TableField(value = "province_id")
    private Long provinceId;

    /**
     * 市id
     */
    @TableField(value = "city_id")
    private Long cityId;

    /**
     * 县/区id
     */
    @TableField(value = "county_id")
    private Long countyId;

    /**
     * 省
     */
    @TableField(value = "province_name")
    private String provinceName;

    /**
     * 市
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 县
     */
    @TableField(value = "county_name")
    private String countyName;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 房屋类型
     */
    @TableField(value = "house_type")
    private Integer houseType;

    /**
     * 小区id
     */
    @TableField(value = "village_id")
    private Long villageId;

    /**
     * 小区名称
     */
    private String villageName;

    /**
     * 街道
     */
    @TableField(value = "street")
    private String street;

    /**
     * 楼栋
     */
    @TableField(value = "building")
    private String building;

    /**
     * 单元
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 门牌号
     */
    @TableField(value = "house_number")
    private String houseNumber;

    /**
     * 入住时间
     */
    @TableField(value = "check_in_date")
    private Date checkInDate;

    /**
     * 安装时间
     */
    @TableField(value = "install_date")
    private Date installDate;

    /**
     * 录入来源：0未知  1B端录入  2C端录入
     */
    private Integer source = 0;

    private String createdBy;
    private Date createdDate;
    private String modifiedBy;
    private Date modifiedDate;

    @TableField(exist = false)
    private String provicenName;

    @TableField(exist = false)
    private Long provicenId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
