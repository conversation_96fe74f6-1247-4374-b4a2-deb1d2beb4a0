package com.fotile.shopordercenter.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetActivityByActivityTypeVO implements Serializable {
    //活动尾款开始时间
    @JsonFormat(pattern = "MM月dd日 HH:mm:ss",timezone="GMT+8")
    private Date depositPayStartTime;

    private Long activityId;


    private Date activityStartTime;
    private Date activityEndTime;

}
