package com.fotile.shopordercenter.client.dto;



import lombok.Data;

import java.math.BigDecimal;

@Data
public class AlterPointDto {

    private Long id;
    //方向(-1:减少,1:增加)
    private Integer direction;
    //调整积分来源id
    private String sourceId;
    //调整积分来源编码
    private String sourceCode;
    //调整积分来源名称
    private String sourceName;
    //调整积分来源电话
    //调整积分来源名称
    private String sourcePhone;
    //调整积分来源事务标识
    private String sourceTid;
    //被调整积分来源id
    private Long sourceIdBy;
    //被调整积分来源Code
    private String sourceCodeBy;
    private String sourceNameBy;
    //被调整积分来源事务标识
    private String sourceTidBy;
    //变动类型
    private String typeCode;
    //变动类型名称
    private String typeName;
    //订单id
    private String orderId;
    //流水备注
    private String remark;
    private String modifiedName;
    //变动积分
    private Long available;
    //单据id
    private String billId;
    //交易时间
    private String tradingTime;
    //更改后的事务标识
    private String alterTid;
    private String channelCode;
    private String channelName;

    private BigDecimal afterValue;
    private BigDecimal totalPay;
    private BigDecimal totalIncome;

    //变动成长值
    private BigDecimal growAvailable;
    //成长值方向
    private Integer growDirection;

    private String operatorId;
    private String operatorName;

    private String orderNo;

}
