package com.fotile.shopordercenter.client;

import com.fotile.framework.web.Result;
import com.fotile.shopordercenter.client.dto.CustomerDetailOutDto;
import com.fotile.shopordercenter.client.dto.GetCustomerStewardDto;
import com.fotile.shopordercenter.client.entity.CustomerInvoice;
import com.fotile.shopordercenter.client.entity.CustomerMallActivityInfoDto;
import com.fotile.shopordercenter.client.entity.UserAddressFromMall;
import com.fotile.shopordercenter.client.entity.UserInstallAddress;
import com.fotile.shopordercenter.customer.entity.CustomerInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "customer-center")
public interface CustomerClient {


    /**
     * 根据顾客ID查询手机号
     *
     * @param uid
     * @return
     */
    @GetMapping(value = "/api/customer/queryCustomerPhoneById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<String> queryCustomerPhoneById(@RequestParam("uid") Long uid);


    /**
     * 根据顾客ID查询收货地址
     *
     * @param customerId 客户ID
     * @return
     */
    @GetMapping(value = "/api/customerAddress/m/queryDefaultAddressByCustomerId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserAddressFromMall> queryDefaultAddressByCustomerId(@RequestParam("customerId") Long customerId);

    /**
     * 根据顾客ID查询安装地址
     *
     * @param customerId 客户ID
     * @return
     */
    @GetMapping(value = "/api/customerInstallAddress/m/queryDefaultAddressByCustomerId", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserAddressFromMall> queryDefaultInstallAddressByCustomerId(@RequestParam("customerId") Long customerId);

    /**
     * 根据收货地址ID查询收货地址
     *
     * @param id 地址ID
     * @return
     */
    @GetMapping(value = "/api/customerAddress/m/queryById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserAddressFromMall> queryUserAddressById(@RequestParam("id") Long id);

    /**
     * 根据安装地址ID查询安装地址
     *
     * @param id 地址ID
     * @return
     */
    @GetMapping(value = "/api/customerInstallAddress/m/queryById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserInstallAddress> queryUserInstallAddressById(@RequestParam("id") Long id);

    /**
     * 根据手机号查询详细客户信息
     *
     * @param phone
     * @return
     */
    @RequestMapping(value = "/api/mallCustomer/m/queryCustomerDetailByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<CustomerDetailOutDto> queryCustomerDetailByPhone(@RequestParam("phone") String phone);

    /**
     * 根据手机号查询详细客户信息免token
     *
     * @param phone
     * @return
     */
    @RequestMapping(value = "/api/mallCustomer/api/open/queryCustomerDetailByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<CustomerDetailOutDto> queryCustomerDetailByPhoneAvoidLogin(@RequestParam("phone") String phone);

    /**
     * 根据手机号查询详细客户信息
     *
     * @param phone
     * @return
     */
    @RequestMapping(value = "/api/mallCustomer/queryCustomerDetailByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<CustomerDetailOutDto> queryCustomerDetailByPhone1(@RequestParam("phone") String phone);


    /**
     * 根据id查询发票信息
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/customerInvoice/m/queryInvoiceById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<CustomerInvoice> queryInvoiceById(@RequestParam("id") Long id);

    /**
     * 更新抽奖次数
     */
    @RequestMapping(value = "/api/mallFission/api/open/edit", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> edit(CustomerMallActivityInfoDto customerMallActivityInfoDto);

    /**
     * 更新抽奖次数
     */
    @RequestMapping(value = "/api/mallFission/m/edit", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> editLotteryNum(CustomerMallActivityInfoDto customerMallActivityInfoDto);

    /**
     * 获取客户管家
     */
    @RequestMapping(value = "/api/mallCustomer/m/getCustomerSteward", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<GetCustomerStewardDto> getCustomerSteward(@RequestParam("customerId") Long customerId);


    /**
     * 根据账号id查询顾客信息
     *
     * @param userEntity
     * @return
     */
    @RequestMapping(value = "/api/m/customer/queryCustomerInfoByUserEntity", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CustomerInfo> queryCustomerInfoByUserEntity(@RequestParam("userEntity") String userEntity);

    /**
     * 获取管家
     * @param customerIdList
     * @return
     */
//    @RequestMapping(value = "/api/mallCustomer/getCustomerInfoByIdList", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    Result<List<AllocationInDto>> getCustomerInfoByIdList(@RequestParam("customerIdList") List<Long> customerIdList) ;


    /**
     * 根据幸福家ID查询收货地址
     * @param id
     * @return
     */
    @GetMapping(value = "/api/m/fotilestyle/address", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserAddressFromMall> queryXfjAddressById(@RequestParam("id") Long id);
}
