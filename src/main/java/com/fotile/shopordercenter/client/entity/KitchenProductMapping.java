package com.fotile.shopordercenter.client.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡券与厨电商品映射关系实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.promotioncenter.card.pojo
 * @date 2021/2/21 17:10
 */
@Data
public class KitchenProductMapping implements Serializable {
    /**
     * 优惠卡券厨电商品绑定id,PK,AI
     */
    private Long id;

    /**
     * 优惠卡券id(exchange_card.id)
     */
    private Long exchangeCardId;

    /**
     * 厨电商品id
     */
    private Long productId;

    /**
     * 厨电商品编号
     */
    private String productCode;

    /**
     * 厨电商品名称
     */
    private String productName;

    /**
     * 商品原价
     */
    private BigDecimal price;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 记录状态(0未删除,id已删除)
     */
    private Long isDeleted;

    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 修改人id
     */
    private String modifiedBy;

    /**
     * 修改人
     */
    private String modifiedName;

    /**
     * 修改日期
     */
    private Date modifiedDate;
}
