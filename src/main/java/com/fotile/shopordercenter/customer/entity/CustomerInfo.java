package com.fotile.shopordercenter.customer.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import java.util.Date;

/**
 * 顾客基本信息
 */
@Data
@TableName(value = "customer_info")
public class CustomerInfo extends AuditingEntity {

    /**
     * 第三方顾客id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * keycloak用户Id
     */
    @TableField(value = "user_entity")
    private String userEntity;

    /**
     * 创建业务员id
     */
    @TableField(value = "create_salesman_id")
    private Long createSalesmanId;

    /**
     * 创建业务员名称
     */
    @TableField(value = "create_salesman_name")
    private String createSalesmanName;

    /**
     * 最新业务员名称
     */
    @TableField(value = "latest_salesman_name")
    private String latestSalesmanName;

    /**
     * 账号id
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 顾客－用户名
     */
    @TableField(value = "name")
    private String name;

    /**
     * 顾客－邮箱
     */
    @TableField(value = "email", updateStrategy = FieldStrategy.IGNORED)
    private String email;

    /**
     * 顾客－手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 顾客－手机号
     */
    @TableField(value = "phone_enc")
    private String phoneEnc;

    /**
     * 顾客－腾讯qq账号
     */
    @TableField(value = "customer_qq")
    private Long customerQq;

    /**
     * 会员等级id
     */
    @TableField(value = "grade_id")
    private Long gradeId;

    /*
    //推广业务员姓名
    @TableField(value = "salesman_name")
    private String salesmanName;*/

    /**
     * 最新业务员ID 后期跟进负责该会员的业务员ID
     */
    @TableField(value = "latest_salesman_id")
    private Long latestSalesmanId;

    /**
     * 顾客－头像
     */
    @TableField(value = "head_portrait")
    private String headPortrait;

    /**
     * 顾客－性别 0未知  1 男  2女
     */
    @TableField(value = "sex")
    private Integer sex;

    /**
     * 顾客－类型 1-线上会员 2-线上粉丝 3-门店顾客
     */
    @TableField(value = "type")
    private Integer type;

    /*
    //顾客－真实姓名
    @TableField(value = "realname")
    private String realname;*/

    /**
     * 顾客－昵称
     */
    @TableField(value = "nickname")
    private String nickname;

    /**
     * 注册来源说明
     */
    @TableField(value = "regist_channel_note")
    private String registChannelNote;

    /**
     * 顾客－备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 注册时间"
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 顾客－生日
     */
    @TableField(value = "birthday")
    private Date birthday;

    /**
     * 注册来源
     */
    @TableField(value = "regist_channel")
    private Integer registChannel;

    /**
     * 小区id
     */
    @TableField(value = "village_id")
    private Long villageId;

    /**
     * 所属分公司id
     */
    @TableField(value = "company_id")
    private Long companyId;

    /**
     * 所属门店id
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 上级顾客id
     */
    @TableField(value = "parent_customer_id")
    private Long parentCustomerId;

    /**
     * 客户来源:3-门店顾客（云管理-线索）用,1：扫楼录入 2：后台录入  3：扫码留资 4：换装留资
     */
    @TableField(value = "customer_channel")
    private String customerChannel;

    /**
     * 幸福家注册来源，取数据字典happy_channel
     */
    @TableField(value = "happy_channel")
    private Integer happyChannel;

    /**
     * 注册频道id
     */
    @TableField(value = "regist_radio")
    private Integer registRadio;

    /**
     * 活动id
     */
    @TableField(value = "activity_id")
    private Integer activityId;

    /**
     * 职业code
     */
    private String professionalCode;

    /**
     * 职业名称
     */
    private String professionalName;

    /**
     * 是否发放积分 1是 0否
     */
    private Integer isIssueIntegral;

    /**
     * 是否编辑过头像 0否 1是
     */
    private Integer isEditImg;

    /**
     * 勾选协议时间
     */
    @TableField(value = "regist_time")
    private Date registTime;

    private Date updateGradeTime;
}
