package com.fotile.shopordercenter.order.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceLookup;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.shopordercenter.alipay.pojo.dto.AddOrderLineInfoInDto;
import com.fotile.shopordercenter.alipay.pojo.dto.EditRemarkDto;
import com.fotile.shopordercenter.alipay.pojo.dto.OrderItemCodeAndNumDto;
import com.fotile.shopordercenter.alipay.utils.AlipayUtil;
import com.fotile.shopordercenter.chinaums.service.ChinaumsPayService;
import com.fotile.shopordercenter.client.*;
import com.fotile.shopordercenter.client.dto.*;
import com.fotile.shopordercenter.client.entity.*;
import com.fotile.shopordercenter.customer.CustomerUtil;
import com.fotile.shopordercenter.customer.entity.CustomerInfo;
import com.fotile.shopordercenter.kdApi.dto.OrderSearchListVo;
import com.fotile.shopordercenter.order.constant.GradeEnum;
import com.fotile.shopordercenter.order.constant.OrderConstant;
import com.fotile.shopordercenter.order.constant.StoreInfoEnum;
import com.fotile.shopordercenter.order.constant.TaskConstants;
import com.fotile.shopordercenter.order.dao.*;
import com.fotile.shopordercenter.order.mq.OmsOrderCreateNoticeShopOrderChannel;
import com.fotile.shopordercenter.order.pojo.dto.*;
import com.fotile.shopordercenter.order.pojo.entity.*;
import com.fotile.shopordercenter.order.pojo.vo.ImportExcelResultVo;
import com.fotile.shopordercenter.order.pojo.vo.ImportMiBoOrderVo;
import com.fotile.shopordercenter.order.pojo.vo.JobOrderRecordListVO;
import com.fotile.shopordercenter.order.pojo.vo.OrderAfterSaleOperationLog;
import com.fotile.shopordercenter.utils.IpUtils;
import com.fotile.shopordercenter.utils.JudgeDateUtil;
import com.fotile.shopordercenter.utils.LogUtils;
import com.fotile.shopordercenter.utils.RedisUtil;
import com.fotile.shopordercenter.wxpay.service.PaymentService;
import groovy.lang.Lazy;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fotile.shopordercenter.utils.IpUtils.getIpAddress;
import static java.util.Comparator.comparing;


@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "seventh")
public class OrderService {

    @Autowired(required = false)
    OrderDao orderDao;

    @Autowired(required = false)
    GoodsOrderItemDao goodsOrderItemDao;

    @Autowired(required = false)
    GoodsOrderPromotionDao goodsOrderPromotionDao;

    @Autowired(required = false)
    OrderPayRecordDao orderPayRecordDao;

    @Autowired(required = false)
    OrderDeliveryDao orderDeliveryDao;

    @Autowired(required = false)
    OrderAfterSaleDao orderAfterSaleDao;

    @Autowired(required = false)
    OrderInvoiceDao orderInvoiceDao;

    @Autowired
    OrderOperationLogDao orderOperationLogDao;

    @Autowired
    OrderAfterSaleOperationLogDao orderAfterSaleOperationLogDao;

    @Autowired(required = false)
    OrderDeliveryMappingDao orderDeliveryMappingDao;

    @Autowired
    private ProductClient productClient;

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private CustomerInfoDao customerInfoDao;

    @Autowired
    private PointService pointService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    @Lazy
    private OrderUtil orderUtil;

    @Autowired
    private MarketingClient marketingClient;
    @Autowired
    private OrderPayRecordService orderPayRecordService;

    @Autowired
    private OrderAfterSaleMappingDao orderAfterSaleMappingDao;

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    OrgClient orgClient;
    @Autowired
    private CmsClient cmsClient;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private OrderNoGenerateDao orderNoGenerateDao;

    @Autowired
    private PointClient pointClient;

    @Autowired
    private CustomerUtil customerUtil;

    @Autowired
    private PromotionClient promotionClient;

    @Resource(name = OmsOrderCreateNoticeShopOrderChannel.SHOP_ORDER_CANCEL_NOTICE_OMS_OUTPUT)
    private MessageChannel orderCancelChannel;


    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private ChinaumsPayService chinaumsPayService;

    public PageInfo<OrderListInfoDto> getList(OrderConditionInDto orderConditionInDto) {
        PageInfo<OrderListInfoDto> page = new PageInfo(orderConditionInDto.getPage(), orderConditionInDto.getSize());
        orderConditionInDto.setPage(page.getOffset());
        orderConditionInDto.setSize(page.getSize());
        orderConditionInDto.setSecretKey(MybatisMateConfig.getPassword());
        //动态切换数据库，查询六库
        DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("sixth");
        // 校验订单来源
        verifyOrderSource(orderConditionInDto);
        Long total = orderDao.selectOrderListInfoCount(orderConditionInDto);
        page.setTotal(total == null ? 0 : total);
        List<OrderListInfoDto> listInfoDtos = new ArrayList<>();
        List<OrderListInfoDto> orderListInfoDtos = orderDao.selectOrderListInfoDto(orderConditionInDto);
        //动态切换数据库，查询7库
        DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("seventh");
        if (!CollectionUtils.isEmpty(orderListInfoDtos)) {
            listInfoDtos = fillOrderData(orderListInfoDtos, orderConditionInDto);
            // 匹配列表数据
            matchOrderListData(listInfoDtos);
        }
        page.setRecords(listInfoDtos);
        return page;
    }

    /**
     * 匹配活动信息 and 会员等级信息
     *
     * @param listInfoDtos
     */
    private void matchOrderListData(List<OrderListInfoDto> listInfoDtos) {
        List<String> orderNoList = listInfoDtos.stream().map(OrderListInfoDto::getOrderNo).collect(Collectors.toList());
        List<Long> orderIdList = listInfoDtos.stream().map(OrderListInfoDto::getId).distinct().collect(Collectors.toList());
        List<JobOrderRecordEntity> jobOrderRecordByOrderIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(orderIdList)){
            jobOrderRecordByOrderIds = jobOrderRecordService.findJobOrderRecordByOrderIds(orderIdList);
        }
        List<String> jobIds = new ArrayList<>();
        Map<Long, List<JobOrderRecordEntity>> jobOrderRecordMap = new HashMap<>();
        FindMdmStaffByJobNumListInDto dto = new FindMdmStaffByJobNumListInDto();
        if(!CollectionUtils.isEmpty(jobOrderRecordByOrderIds)){
            jobOrderRecordMap = jobOrderRecordByOrderIds.stream().collect(Collectors.groupingBy(JobOrderRecordEntity::getOrderId));
            jobIds = jobOrderRecordByOrderIds.stream().map(JobOrderRecordEntity::getJobId).collect(Collectors.toList());
            dto.setJobNumList(jobIds);
        }
        Map<String, FindMdmStaffByJobNumsListOutDto> jobInfoMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(jobIds)){
            Result<List<FindMdmStaffByJobNumsListOutDto>> mdmStaffByJobNumsList = orgClient.findMdmStaffByJobNumsList(dto);
            if(!mdmStaffByJobNumsList.getSuccess()){
                throw new BusinessException(mdmStaffByJobNumsList.getMsg());
            }
            if(!CollectionUtils.isEmpty(mdmStaffByJobNumsList.getData())){
                jobInfoMap = mdmStaffByJobNumsList.getData().stream().collect(Collectors.toMap(FindMdmStaffByJobNumsListOutDto::getJobNum, Function.identity()));
            }
        }
        // 获取活动活动信息
        List<OrderPromotionOutDto> orderPromotionList = goodsOrderPromotionDao.selectActivitInfoByOrderNoList(orderNoList);
        boolean promotionFlag = true;
        if (CollectionUtils.isEmpty(orderPromotionList)) {
            promotionFlag = false;
        }
        // 获取分公司地址
        List<FindCompanyByAreaInBatchOutDto> branchAddress = getBranchAddress(listInfoDtos);
        boolean branchAddFlag = true;
        if (CollectionUtils.isEmpty(branchAddress)) {
            branchAddFlag = false;
        }

        // 获取会员等级
/*
        List<MallCustomerIntegralOutDto> mallCustomerIntegralOutDtos = queryCustomerGrow(listInfoDtos);
        boolean growFlag = true;
        if (CollectionUtils.isEmpty(mallCustomerIntegralOutDtos)) {
            growFlag = false;
        }
*/

/*

        //匹配管家
        Set<Long> customerIdSet = listInfoDtos.stream().filter( t -> t.getBuyUserId() != null).map(OrderListInfoDto::getBuyUserId).collect(Collectors.toSet());
        Map<Long, String> stewardMap = null;
        if(CollectionUtils.isNotEmpty(customerIdSet)){
            //动态切换数据库，查询六库
            DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("sixth");
            List<Long> customerIdList = new ArrayList<>(customerIdSet);
            List<AllocationInDto> stewardList = new ArrayList<>();
            int size = 1000;
            int flag = (customerIdList.size() % size == 0) ? (customerIdList.size() / size) : (customerIdList.size() / size + 1);
            for (int i = 0; i < flag; i++) {
                int start = i * size;
                int end = i * size + size;
                if (end > customerIdList.size()) {
                    end = customerIdList.size();
                }
                int finalEnd = end;
                List<Long> subIdList = customerIdList.subList(start, finalEnd);
                if(CollectionUtils.isNotEmpty(subIdList)){
                    List<AllocationInDto> data = customerInfoDao.getCustomerInfoByIdList(subIdList);
                    if(CollectionUtils.isNotEmpty(data)){
                        stewardList.addAll(data);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(stewardList)){
                stewardMap = stewardList.stream().collect(Collectors.toMap(AllocationInDto::getUserId, AllocationInDto::getStewardName));
            }
            //动态切换数据库，查询7库
            DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("seventh");
        }
*/


        for (OrderListInfoDto listInfoDto : listInfoDtos) {
            //匹配员工信息
            if(jobOrderRecordMap.containsKey(listInfoDto.getId())){
                listInfoDto.setJobId(jobOrderRecordMap.get(listInfoDto.getId()).get(0).getJobId());
            }

            if(jobInfoMap.containsKey(listInfoDto.getJobId())){
                FindMdmStaffByJobNumsListOutDto findMdmStaffByJobNumsListOutDto = jobInfoMap.get(listInfoDto.getJobId());
                listInfoDto.setJobName(findMdmStaffByJobNumsListOutDto.getName());
                listInfoDto.setDeptName(findMdmStaffByJobNumsListOutDto.getDepartName());
            }

            // 匹配活动id
            if (promotionFlag) {
                for (OrderPromotionOutDto orderPromotionOutDto : orderPromotionList) {
                    if (null != listInfoDto.getBuyUserId() && listInfoDto.getOrderNo().equals(orderPromotionOutDto.getOrderNo())) {
                        listInfoDto.setActivity(orderPromotionOutDto.getActivityInfo());
                    }
                }
            }

            // 匹配会员等级
            listInfoDto.setGrade(GradeEnum.getNameByCode(listInfoDto.getGradeId()));
/*
            if (growFlag) {
                for (MallCustomerIntegralOutDto mallCustomerIntegralOutDto : mallCustomerIntegralOutDtos) {
                    if (null != listInfoDto.getBuyUserId() && listInfoDto.getBuyUserId().equals(mallCustomerIntegralOutDto.getCustomerId())) {
                        listInfoDto.setGrade(mallCustomerIntegralOutDto.getGradeName());
                    }
                    if (null == listInfoDto.getGrade()) {
                        listInfoDto.setGrade("普通会员");
                    }
                }
            }
*/

            // 匹配分公司地址
            if (branchAddFlag) {
                for (FindCompanyByAreaInBatchOutDto areaInBatchOutDto : branchAddress) {
                    if (null != listInfoDto.getAreaId() && listInfoDto.getAreaId().equals(areaInBatchOutDto.getCountyId())) {
                        listInfoDto.setBranchName(areaInBatchOutDto.getName());
                    }
                }
            }
/*

            if(stewardMap != null && StringUtils.isNotBlank(stewardMap.get(listInfoDto.getBuyUserId()))){
                listInfoDto.setHousekeeperName(stewardMap.get(listInfoDto.getBuyUserId()));
            }
*/

        }
    }

    /**
     * 订单导出（详情）
     *
     * @param orderConditionInDto
     * @return
     */
    public PageInfo<ExportOrderItemVo> getExportOrderItemVoList(OrderConditionInDto orderConditionInDto) {
        PageInfo<ExportOrderItemVo> page = new PageInfo(orderConditionInDto.getPage(), orderConditionInDto.getSize());
        orderConditionInDto.setPage(page.getOffset());
        orderConditionInDto.setSize(page.getSize());
        // 校验订单来源
        verifyOrderSource(orderConditionInDto);

        //动态切换数据库，查询六库
        DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("sixth");
        Long total = orderDao.selectOrderListInfoCount(orderConditionInDto);

        List<OrderListInfoDto> listInfoDtos = new ArrayList<>();
        List<OrderListInfoDto> orderListInfoDtos = orderDao.selectOrderListInfoDto(orderConditionInDto);

        //动态切换数据库，查询7库
        DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("seventh");

        if (!CollectionUtils.isEmpty(orderListInfoDtos)) {
            listInfoDtos = fillOrderData(orderListInfoDtos, orderConditionInDto);
            // 匹配数据
            matchOrderListData(listInfoDtos);
            // 获取订单行
            List<String> orderNoList = orderListInfoDtos.stream().map(OrderListInfoDto::getOrderNo).collect(Collectors.toList());
            List<OrderItemInfo> orderItemInfos = orderDao.selectOrderItemInfoList(orderNoList);
            // 查询所有组合商品
            List<GoodsOrderItem> goodsOrderItemList = goodsOrderItemDao.selectOrderGoodsItemByGroupGoods(orderNoList);
            // 匹配订单行
            listInfoDtos.forEach(order -> {
                if (!CollectionUtils.isEmpty(orderItemInfos)) {
                    List<OrderItemInfo> itemInfoList = orderItemInfos.stream()
                            .filter(item -> order.getOrderNo().equals(item.getOrderNo()))
                            .peek(item -> matchPOLineStatus(item, goodsOrderItemList))
                            .collect(Collectors.toList());
                    order.setOrderItemInfos(itemInfoList);
                }
            });
        }

        List<ExportOrderItemVo> list = new ArrayList<>();

        for (int i = 0; i < listInfoDtos.size(); i++) {
            OrderListInfoDto orderListInfoDto = listInfoDtos.get(i);
            int index = 1;
            for (int j = 0; j < orderListInfoDto.getOrderItemInfos().size(); j++) {
                OrderItemInfo orderItemInfo = orderListInfoDto.getOrderItemInfos().get(j);
                // 订单行信息
                ExportOrderItemVo exportOrderItemVo = new ExportOrderItemVo();
                BeanUtils.copyProperties(orderListInfoDto, exportOrderItemVo);
                BeanUtils.copyProperties(orderItemInfo, exportOrderItemVo);
                exportOrderItemVo.setActivity(orderItemInfo.getActivityStr());
                exportOrderItemVo.setNo(Integer.toString(index));
                list.add(exportOrderItemVo);
                index++;
            }
        }
        page.setTotal(total == null ? 0 : total);
        page.setRecords(list);
        return page;
    }

    /**
     * 校验订单来源
     *
     * @param orderConditionInDto
     */
    private void verifyOrderSource(OrderConditionInDto orderConditionInDto) {
        if (null != orderConditionInDto.getSource() && !orderConditionInDto.getSource().equals("")) {
            switch (orderConditionInDto.getSource()) {
                case "1":
                    orderConditionInDto.setSource("pc");
                    break;
                case "2":
                    orderConditionInDto.setSource("h5");
                    break;
                case "3":
                    orderConditionInDto.setSource("小程序");
                    break;
                case "4":
                    orderConditionInDto.setSource("钉钉");
                    break;
                default:
                    throw new BusinessException("未知来源");
            }
        }
    }

    /**
     * 匹配订单行状态
     *
     * @param item
     * @param goodsOrderItemList
     */
    private void matchPOLineStatus(OrderItemInfo item, List<GoodsOrderItem> goodsOrderItemList) {
        StringBuffer activityStr = new StringBuffer();
        if (null != item.getActivityName() && !item.getActivityName().isEmpty() && null != item.getActivityId() && !item.getActivityId().isEmpty()) {
            activityStr.append(item.getActivityId() + "/" + item.getActivityName() + ";");
            item.setActivityStr(activityStr.toString());
        }

        if (item.getParentItemId() != null && item.getSubGoodsType() == 1 && item.getProductTypes() == 1) {
            for (GoodsOrderItem goodsOrderItem : goodsOrderItemList) {
                if (item.getParentItemId().equals(goodsOrderItem.getId())) {
                    item.setDescription("来自组合商品：" + goodsOrderItem.getSeriesGoodsCode() + "/" + goodsOrderItem.getGoodsName());
                    if (null != goodsOrderItem.getActivityName() && !goodsOrderItem.getActivityName().isEmpty() && null != goodsOrderItem.getActivityId()) {
                        activityStr.append(goodsOrderItem.getActivityId() + "/" + goodsOrderItem.getActivityName() + ";");
                        item.setActivityStr(activityStr.toString());
                    }
                }
            }
        }

        //是否线上机0否，1是
        if (null == item.getOnline() || item.getOnline().equals("")) {
            item.setOnline("0");
        }
        switch (Integer.parseInt(item.getOnline())) {
            case 0:
                item.setOnline("其他");
                break;
            case 1:
                item.setOnline("是");
                break;
            case 2:
                item.setOnline("否");
                break;

        }
        //行状态0已取消，1未确认，2已确认，3已发货，4已转单，5已收货，6已完成
        switch (Integer.parseInt(item.getOrderItemStatus())) {
            case 0:
                item.setOrderItemStatus("已取消");
                break;
            case 1:
                item.setOrderItemStatus("未确认");
                break;
            case 2:
                item.setOrderItemStatus("已确认");
                break;
            case 3:
                item.setOrderItemStatus("已发货");
                break;
            case 4:
                item.setOrderItemStatus("已转单");
                break;
            case 5:
                item.setOrderItemStatus("已收货");
                break;
            case 6:
                item.setOrderItemStatus("已完成");
                break;
            default:
                item.setOrderItemStatus("未知");
                break;
        }
    }

    /**
     * 获取分公司地址
     *
     * @param orderListInfoDtos
     */
    private List<FindCompanyByAreaInBatchOutDto> getBranchAddress(List<OrderListInfoDto> orderListInfoDtos) {
        List<String> areaIds = orderListInfoDtos.stream().filter(orderListInfoDto -> null != orderListInfoDto.getAreaId()).map(orderListInfoDto -> orderListInfoDto.getAreaId().toString()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(areaIds)){
            return null;
        }
        FindCompanyByCodesInDto findCompanyByCodesInDto = new FindCompanyByCodesInDto();
        findCompanyByCodesInDto.setCodeList(areaIds);
        Result<List<FindCompanyByAreaInBatchOutDto>> companyByAreaBatch = orgClient.findCompanyByAreaBatchs(findCompanyByCodesInDto);

        if (!companyByAreaBatch.getSuccess()) {
            throw new BusinessException("获取分公司地址异常");
        }
        if (null == companyByAreaBatch.getData()) {
            return null;
        }
        return companyByAreaBatch.getData();
    }

    /**
     * 获取会员等级
     *
     * @return
     */
    public List<MallCustomerIntegralOutDto> queryCustomerGrow(List<OrderListInfoDto> infoDtoList) {
        List<Long> customerIdList = infoDtoList.stream().map(OrderListInfoDto::getBuyUserId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(customerIdList)){
            return null;
        }
        MallCustomerIntegralInDto mallCustomerIntegralInDto = new MallCustomerIntegralInDto();
        mallCustomerIntegralInDto.setCustomerIdList(customerIdList);
        Result<List<MallCustomerIntegralOutDto>> customerIntegralList = pointClient.queryGrowCustomerByCustomerIdBatchs(mallCustomerIntegralInDto);

        if (!customerIntegralList.getSuccess()) {
            throw new BusinessException("获取会员等级异常");
        }
        if (null == customerIntegralList.getData()) {
            return null;
        }
        return customerIntegralList.getData();
    }

    private List<OrderListInfoDto> fillOrderData(List<OrderListInfoDto> orderRecords, OrderConditionInDto orderConditionInDto) {

        if(CollectionUtils.isEmpty(orderRecords)){
            return orderRecords;
        }

        List<String> orderNoList = orderRecords.stream().map(t -> t.getOrderNo()).collect(Collectors.toList());

        //查询优惠金额
        List<OrderPayRecord> payRecordList = orderPayRecordDao.selectByOrderNoList(orderNoList);
        Map<String,BigDecimal> payRecordMap = null;
        if(CollectionUtils.isNotEmpty(payRecordList)){
            payRecordMap = payRecordList.stream().filter(t -> t.getDiscountAmount() != null).collect(Collectors.toMap(OrderPayRecord::getOrderNo,OrderPayRecord :: getDiscountAmount,(key1, key2) -> key1));
        }
        //查询优惠券码
        List<GoodsOrderPromotion> promotionList = goodsOrderPromotionDao.selectAllByOrderNoList(orderNoList);
        Map<String,List<String>> promotionMap = null;
        if(CollectionUtils.isNotEmpty(promotionList)){
            promotionMap = promotionList.stream().filter(t -> StringUtils.isNotBlank(t.getCouponCodeId())).collect(Collectors.groupingBy(GoodsOrderPromotion::getOrderNo, Collectors.mapping(po -> po.getCouponCodeId(), Collectors.toList())));
        }


        Map<String, BigDecimal> finalPayRecordMap = payRecordMap;
        Map<String, List<String>> finalPromotionMap = promotionMap;
        Stream.iterate(0, i -> i + 1).limit(orderRecords.size()).forEach(index -> {
            if(finalPayRecordMap != null && finalPayRecordMap.get(orderRecords.get(index).getOrderNo()) != null){
                orderRecords.get(index).setPayDiscountAmount(finalPayRecordMap.get(orderRecords.get(index).getOrderNo()));
            }
            if(finalPromotionMap != null && CollectionUtils.isNotEmpty(finalPromotionMap.get(orderRecords.get(index).getOrderNo()))){
                orderRecords.get(index).setCouponCodeId(StringUtils.join(finalPromotionMap.get(orderRecords.get(index).getOrderNo()),","));
            }

            //订单头状态0已取消，1未确认，2已确认，3部分发货，4已发货，5已收货，6已完成
            switch (Integer.valueOf(orderRecords.get(index).getOrderStatus())) {
                case 0:
                    orderRecords.get(index).setOrderStatus("已取消");
                    break;
                case 1:
                    orderRecords.get(index).setOrderStatus("未确认");
                    break;
                case 2:
                    orderRecords.get(index).setOrderStatus("已确认");
                    break;
                case 3:
                    orderRecords.get(index).setOrderStatus("部分发货");
                    break;
                case 4:
                    orderRecords.get(index).setOrderStatus("已发货");
                    break;
                case 5:
                    orderRecords.get(index).setOrderStatus("已收货");
                    break;
                case 6:
                    orderRecords.get(index).setOrderStatus("已完成");
                    break;
                default:
                    orderRecords.get(index).setOrderStatus("/");
                    break;
            }
            //顾客订单状态 0已取消，1待付款，2待确认，3待发货，4部分发货，5已发货，6待评价，7已评价
            switch (Integer.valueOf(orderRecords.get(index).getCustomerOrderStatus())) {
                case 0:
                    orderRecords.get(index).setCustomerOrderStatus("已取消");
                    break;
                case 1:
                    orderRecords.get(index).setCustomerOrderStatus("待付款");
                    break;
                case 2:
                    orderRecords.get(index).setCustomerOrderStatus("待确认");
                    break;
                case 3:
                    orderRecords.get(index).setCustomerOrderStatus("待发货");
                    break;
                case 4:
                    orderRecords.get(index).setCustomerOrderStatus("部分发货");
                    break;
                case 5:
                    orderRecords.get(index).setCustomerOrderStatus("已发货");
                    break;
                case 6:
                    orderRecords.get(index).setCustomerOrderStatus("待评价");
                    break;
                case 7:
                    orderRecords.get(index).setCustomerOrderStatus("已评价");
                    break;
                case 8:
                    orderRecords.get(index).setCustomerOrderStatus("已删除");
                    break;
                default:
                    orderRecords.get(index).setCustomerOrderStatus("/");
                    break;
            }
            //同步电商状态0未同步，1同步成功，2同步失败
            switch (Integer.valueOf(orderRecords.get(index).getSyncStatus())) {
                case 0:
                    orderRecords.get(index).setSyncStatus("未同步");
                    break;
                case 1:
                    orderRecords.get(index).setSyncStatus("同步失败");
                    break;
                case 2:
                    orderRecords.get(index).setSyncStatus("同步成功");
                    break;
                default:
                    orderRecords.get(index).setSyncStatus("/");
                    break;
            }
            //支付类型 1:在线支付、2:货到付款、3:银行转账
            switch (Integer.valueOf(orderRecords.get(index).getPayType())) {
                case 1:
                    orderRecords.get(index).setPayType("在线支付");
                    break;
                case 2:
                    orderRecords.get(index).setPayType("货到付款");
                    break;
                case 3:
                    orderRecords.get(index).setPayType("银行转账");
                    break;
                default:
                    orderRecords.get(index).setPayType("/");
                    break;
            }
            //支付方式 0:无，1:支付宝，2:微信
            switch (Integer.valueOf(orderRecords.get(index).getPayMethod())) {
                case 0:
                    orderRecords.get(index).setPayMethod("无");
                    break;
                case 1:
                    orderRecords.get(index).setPayMethod("支付宝");
                    break;
                case 2:
                    orderRecords.get(index).setPayMethod("微信");
                    break;
                case 3:
                    orderRecords.get(index).setPayMethod("云闪付");
                    break;
                default:
                    orderRecords.get(index).setPayMethod("/");
                    break;
            }
            //支付状态0待支付，1部分支付，2已支付
            switch (Integer.valueOf(orderRecords.get(index).getPayStatus())) {
                case 0:
                    orderRecords.get(index).setPayStatus("待支付");
                    break;
                case 1:
                    orderRecords.get(index).setPayStatus("部分支付");
                    break;
                case 2:
                    orderRecords.get(index).setPayStatus("已支付");
                    break;
                case 3:
                    orderRecords.get(index).setPayStatus("退款中");
                    break;
                case 4:
                    orderRecords.get(index).setPayStatus("已退款");
                    break;
                default:
                    orderRecords.get(index).setPayStatus("/");
                    break;
            }
/*            if (null != orderRecords.get(index).getBuyUserPhone()) {
                //获取会员等级
                try {
                    Result<GrowCustomer> growCustomerResult = pointClient.queryGrowCustomerByCustomerIdWMS(orderRecords.get(index).getBuyUserId());
                    if (null != growCustomerResult || null != growCustomerResult.getData()) {
                        //会员等级
                        String gradeName = growCustomerResult.getData().getGradeName();
                        orderRecords.get(index).setGrade(gradeName);
                    }
                } catch (Exception e) {
                    orderRecords.get(index).setGrade("获取会员等级异常");
                }
            }*/
            //脱敏
            if (orderConditionInDto.getIsDesensitization() == 1) {
                String buyUserPhone = orderRecords.get(index).getBuyUserPhone();
                String receiverPhone = orderRecords.get(index).getReceiverPhone();
                String installPhone = orderRecords.get(index).getInstallPhone();
                if (null != buyUserPhone) {
                    orderRecords.get(index).setBuyUserPhone(buyUserPhone.replaceAll("(\\d{3})\\d*(\\d{4})", "$1****$2"));
                }

                if (null != receiverPhone) {
                    orderRecords.get(index).setReceiverPhone(receiverPhone.replaceAll("(\\d{3})\\d*(\\d{4})", "$1****$2"));
                }

                if (null != installPhone) {
                    orderRecords.get(index).setInstallPhone(installPhone.replaceAll("(\\d{3})\\d*(\\d{4})", "$1****$2"));
                }
            }

//            List<OrderItemInfo> orderItemInfos = orderDao.selectOrderItemInfo(orderRecords.get(index).getOrderNo());
            // 获取除组合商品，保留组合内商品
//            List<GoodsOfJointDto> subGoodsItemList = orderItemInfos.stream().filter(item -> item.getProductTypes() != 2)
//                    .map(item -> {
//                        GoodsOfJointDto goodsOfJointDto = new GoodsOfJointDto();
//                        goodsOfJointDto.setId(item.getProductNo());
//                        BeanUtils.copyProperties(item , goodsOfJointDto);
//                        return goodsOfJointDto;
//                    }).collect(Collectors.toList());


            // 获取组合商品商品明细
            /*List<String> productNos = orderItemInfos.stream().filter(item -> item.getProductTypes() != 2).map(OrderItemInfo::getProductNo).collect(Collectors.toList());
            List<GoodsOfJointDto> goodsOfJointDtoList = new ArrayList<>();
            try {
                if (!CollectionUtils.isEmpty(productNos)) {
                    goodsOfJointDtoList = orderDao.getGoodsOfJointDtoList(productNos);
                }
            } catch (Exception e) {
                log.error("获取组合商品异常", e);
            }*/

//            StringBuffer activityStr = new StringBuffer();
//            orderItemInfos.forEach(orderItemInfo -> {
            // 判断是否是组合商品
//                if (!CollectionUtils.isEmpty(subGoodsItemList)) {
//                    if (orderItemInfo.getProductTypes() == 2) {
//                        String productNo = orderItemInfo.getProductNo();
//                        List<GoodsOfJointDto> goodsOfJointDtos = new ArrayList<>();
//                        for (GoodsOfJointDto goodsOfJointDto : subGoodsItemList) {
//                            if (goodsOfJointDto.getId().equals(productNo)) {
//                                goodsOfJointDtos.add(goodsOfJointDto);
//                            }
//                        }
//                        orderItemInfo.setGoodsOfJointDtos(goodsOfJointDtos);
//                    }
//                }

            //活动
                /*if (null != orderItemInfo.getActivityName() && !orderItemInfo.getActivityName().isEmpty() && null != orderItemInfo.getActivityId() && !orderItemInfo.getActivityId().isEmpty()) {
                    activityStr.append(orderItemInfo.getActivityId() + "/" + orderItemInfo.getActivityName() + ";");
                }
                //是否线上机0否，1是
                if (null == orderItemInfo.getOnline() || orderItemInfo.getOnline().equals("")) {
                    orderItemInfo.setOnline("0");
                }
                switch (Integer.parseInt(orderItemInfo.getOnline())) {
                    case 0:
                        orderItemInfo.setOnline("其他");
                        break;
                    case 1:
                        orderItemInfo.setOnline("是");
                        break;
                    case 2:
                        orderItemInfo.setOnline("否");
                        break;

                }
                //行状态0已取消，1未确认，2已确认，3已发货，4已转单，5已收货，6已完成
                switch (Integer.parseInt(orderItemInfo.getOrderItemStatus())) {
                    case 0:
                        orderItemInfo.setOrderItemStatus("已取消");
                        break;
                    case 1:
                        orderItemInfo.setOrderItemStatus("未确认");
                        break;
                    case 2:
                        orderItemInfo.setOrderItemStatus("已确认");
                        break;
                    case 3:
                        orderItemInfo.setOrderItemStatus("已发货");
                        break;
                    case 4:
                        orderItemInfo.setOrderItemStatus("已转单");
                        break;
                    case 5:
                        orderItemInfo.setOrderItemStatus("已收货");
                        break;
                    case 6:
                        orderItemInfo.setOrderItemStatus("已完成");
                        break;
                    default:
                        orderItemInfo.setOrderItemStatus("未知");
                        break;

                }


            });*/
//            orderRecords.get(index).setActivity(activityStr.toString());

            //商品
//            orderRecords.get(index).setOrderItemInfos(orderItemInfos);

        });
        return orderRecords;
    }

    /**
     * 查询订单列表
     *
     * @param orderConditionInDto
     * @return
     */
    public PageInfo<OrderConditionOutDto> getOrderItemList(MallOrderListReqDto orderConditionInDto) {
        // 判断是否有员工ID，如果有则查询员工的信息
        if(StringUtils.isBlank(orderConditionInDto.getJobId())){
            // 获取用户ID
            Long customerId = customerUtil.getCustomerInfoByUserEntity().getId();
            orderConditionInDto.setBuyUserId(customerId);
        }
//        if (null == orderConditionInDto.getBuyUserId() && null == orderConditionInDto.getJobId()) {
//            throw new BusinessException("用户ID/员工ID不可为空!");
//        }
//        if (null != orderConditionInDto.getBuyUserId() && null != orderConditionInDto.getJobId()) {
//            throw new BusinessException("用户ID和员工ID只可传一个!");
//        }
        PageInfo pageInfo = new PageInfo(orderConditionInDto.getPage(), orderConditionInDto.getSize());
        orderConditionInDto.setPage(pageInfo.getOffset());
        orderConditionInDto.setSize(pageInfo.getSize());
        Long total = orderDao.selectOrderInfoCount(orderConditionInDto);
        pageInfo.setTotal(total == null ? 0 : total);

        List<OrderConditionOutDto> orderConditionOutDtoList = new ArrayList<>();

        if (pageInfo.getTotal() > 0) {
            List<OrderRecord> orderRecordList = orderDao.selectAll(orderConditionInDto);
            // 判断订单业务模式
            orderRecordList.forEach(this::dealWithBusinessModels);
            orderConditionOutDtoList = orderRecordList.stream().map(item -> {
                OrderConditionOutDto orderConditionOutDto = new OrderConditionOutDto();
                BeanUtils.copyProperties(item, orderConditionOutDto);
                return orderConditionOutDto;
            }).collect(Collectors.toList());

            List<String> orderNos = orderRecordList.stream().map(OrderRecord::getOrderNo).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(orderNos)) {
                List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectOrderItemByOrderNo(orderNos);
                if (!CollectionUtils.isEmpty(goodsOrderItems)) {
                    orderConditionOutDtoList.forEach(item -> {
                        List<GoodsOrderItemVo> goodsOrderItemVos = goodsOrderItems.stream().filter(goodsOrderItem -> {
                            if (item.getOrderNo().equals(goodsOrderItem.getOrderNo()) && goodsOrderItem.getParentItemId() == null) {
                                return goodsOrderItem.getSubGoodsType() == 1;
                            }
                            return false;
                        }).map(goodsOrderItem -> {
                            GoodsOrderItemVo goodsOrderItemVo = new GoodsOrderItemVo();
                            BeanUtils.copyProperties(goodsOrderItem, goodsOrderItemVo);
                            goodsOrderItemVo.setGiveIntegral(goodsOrderItem.getActivityGiveIntegral());
                            return goodsOrderItemVo;
                        }).collect(Collectors.toList());

                        goodsOrderItemVos.forEach(childItem -> {
                            childItem.setChildrenVoList(goodsOrderItems.stream().filter(goodsOrderItem -> {
                                if (childItem.getOrderNo().equals(goodsOrderItem.getOrderNo()) && childItem.getId().equals(goodsOrderItem.getParentItemId())) {
                                    if (goodsOrderItem.getSubGoodsType() == 1 && goodsOrderItem.getParentItemId() != null) {
                                        return false;
                                    }
                                    return goodsOrderItem.getSubGoodsType() != 2 && goodsOrderItem.getParentItemId() != null;
                                }
                                return false;
                            }).map(goodsOrderItem -> {
                                GoodsOrderItem orderItem = new GoodsOrderItem();
                                BeanUtils.copyProperties(goodsOrderItem, orderItem);
                                return orderItem;
                            }).collect(Collectors.toList()));
                        });

                        goodsOrderItemVos.forEach(giftOrderItem -> {
                            giftOrderItem.setGiftOrderItemList(goodsOrderItems.stream().filter(goodsOrderItem -> {
                                if (giftOrderItem.getOrderNo().equals(goodsOrderItem.getOrderNo()) && giftOrderItem.getId().equals(goodsOrderItem.getParentItemId())) {
                                    return goodsOrderItem.getSubGoodsType() == 2;
                                }
                                return false;
                            }).map(goodsOrderItem -> {
                                GoodsOrderItem orderItem = new GoodsOrderItem();
                                BeanUtils.copyProperties(goodsOrderItem, orderItem);
                                return orderItem;
                            }).collect(Collectors.toList()));
                        });
                        item.setOrderItemList(goodsOrderItemVos);
//                        getGoodsGiveIntegral(goodsOrderItemVos);
                    });
                }
            }
        }
        pageInfo.setRecords(orderConditionOutDtoList);
        return pageInfo;
    }

    /*private void handleOrderQueryStatus(MallOrderListReqDto orderConditionInDto) {
        if (orderConditionInDto.getStatus() != null) {
            if (orderConditionInDto.getStatus() == 0) {
                orderConditionInDto.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode());
            } else if (orderConditionInDto.getStatus() == 1) {
                orderConditionInDto.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
            } else if (orderConditionInDto.getStatus() == 2) {
                orderConditionInDto.setOrderStatus(OrderConstant.OrderStatusEnum.SHIPPED.getCode());
            } else if (orderConditionInDto.getStatus() == 3) {
                orderConditionInDto.setOrderStatus(OrderConstant.OrderStatusEnum.FINISHED.getCode());
            } else if (orderConditionInDto.getStatus() == 4) {
                orderConditionInDto.setOrderStatus(OrderConstant.OrderStatusEnum.RECEIVED.getCode());
                orderConditionInDto.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.COMMENT.getCode());
            }
        }
    }*/

    /* *//**
     * 商品赠送积分
     *
     * @param goodsOrderItemList
     *//*
    private void getGoodsGiveIntegral(List<GoodsOrderItemVo> goodsOrderItemList) {
        // 查询商品赠送积分
        ComputedActivityPriceDto computedActivityPriceDto = new ComputedActivityPriceDto();
        computedActivityPriceDto.setGoodsList(goodsOrderItemList.stream()
                .filter(index -> {
                    if (index.getSubGoodsType() != null) {
                        return index.getSubGoodsType() == 1;
                    }
                    return false;
                })
                .map(index -> {
                    ComputedActivityPriceDto.Goods goods = new ComputedActivityPriceDto.Goods();
                    goods.setGoodsCode(index.getGoodsCode());
                    goods.setNum(index.getBuyNum());
                    return goods;
                }).collect(Collectors.toList()));
        if (computedActivityPriceDto != null) {
            List<ActivityGoodsPriceVo> goodsPriceVos = null;
            try {
                goodsPriceVos = marketingClient.computedActivityPrice(computedActivityPriceDto).getData();
            } catch (Exception e) {
                log.error("查询赠送积分异常", e);
            }
            if (!CollectionUtils.isEmpty(goodsPriceVos)) {
                List<ActivityGoodsPriceVo> finalGoodsPriceVos = goodsPriceVos;
                goodsOrderItemList.forEach(item -> {
                    finalGoodsPriceVos.stream().filter(priceVo -> priceVo.getGoodsCode().equals(item.getGoodsCode())).forEach(priceVo -> {
                        item.setGiveIntegral(priceVo.getGiveIntegral());
                    });
                });
            }
        }
    }*/

    /**
     * 业务模式
     *
     * @param item
     */
    private void dealWithBusinessModels(OrderRecord item) {
        //判断是否是退换货
        if (item.getAfterSaleStatus() != null && item.getAfterSaleStatus() != OrderConstant.SalesReturnEnum.CANCELLATION_OF_ORDER.getCode()) {
            if (item.getAfterSaleStatus() == OrderConstant.SalesReturnEnum.APPLYING_FOR_RETURN.getCode() ||
                    item.getAfterSaleStatus() == OrderConstant.SalesReturnEnum.THE_RETURN_OF.getCode() ||
                    item.getAfterSaleStatus() == OrderConstant.SalesReturnEnum.HAVE_TO_RETURN.getCode()) {
                item.setBusinessModel(OrderConstant.BusinessModeEnum.SALES_RETURN.getCode());
            } else {
                item.setBusinessModel(OrderConstant.BusinessModeEnum.EXCHANGE_GOODS.getCode());
            }
        } else {
            if (item.getPayType() == OrderConstant.PaymentTypeEnum.ONLINE_PAYMENT.getCode()) {
                if (judgmentIsPaymentOfDeposit(item)) {
                    item.setBusinessModel(OrderConstant.BusinessModeEnum.ONLINE_PAYMENT.getCode());
                }
            } else if (item.getPayType() == OrderConstant.PaymentTypeEnum.PAY_ON_DELIVERY.getCode()) {
                if (judgmentIsPaymentOfDeposit(item)) {
                    item.setBusinessModel(OrderConstant.BusinessModeEnum.PAY_ON_DELIVERY.getCode());
                }
            } else if (item.getPayType() == OrderConstant.PaymentTypeEnum.BANK_TRANSFER.getCode()) {
                if (judgmentIsPaymentOfDeposit(item)) {
                    item.setBusinessModel(OrderConstant.BusinessModeEnum.BANK_TRANSFER.getCode());
                }
            }
        }
    }

    /**
     * 判断是否是定金支付
     *
     * @param item
     * @return
     */
    private boolean judgmentIsPaymentOfDeposit(OrderRecord item) {
        boolean flag = true;
        if (Optional.ofNullable(item.getIsPreSale()).orElse(0L) == 1) {
            // 定金支付时间 && 尾款开始时间
            if (item.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.DEPOSIT_NOT_PAID.getCode()) {
                //未支付定金
                item.setBusinessModel(OrderConstant.BusinessModeEnum.UNPAID_DEPOSIT.getCode());
                flag = false;
            } else if (item.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.PAY_DEPOSIT_NO_BALANCE_PAYMENT.getCode()) {
                // 4:已支付定金-未开始尾款
                item.setBusinessModel(OrderConstant.BusinessModeEnum.DEPOSIT_PAID_BALANCE_NOT_STARTED.getCode());
                flag = false;
            } else if (item.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.PAY_DEPOSIT_START_BALANCE.getCode()) {
                // 5:已支付定金-已开始尾款
                item.setBusinessModel(OrderConstant.BusinessModeEnum.DEPOSIT_PAID_STARTED_BALANCE_NOT_PAID.getCode());
                flag = false;
            } else if (item.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.THE_BALANCE_HAS_BEEN_PAID.getCode()) {
                // 6:已支付定金-已支付尾款
                item.setBusinessModel(OrderConstant.BusinessModeEnum.DEPOSIT_PAID_BALANCE_PAID.getCode());
                flag = false;
            }
        }
        return flag;
    }

    public OrderDetailsVo getOrderDetails(OrderDetailsInDto orderDetailsInDto) {
        // 获取顾客ID
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) {
            throw new BusinessException("用户信息不存在,请核实！");
        }
        Long customerId = customerInfo.getId();
        orderDetailsInDto.setUserId(customerId);

        OrderDetailsVo orderDetailsVo = new OrderDetailsVo();
        // 查询订单信息
        OrderRecordVo orderRecordVo = orderDao.getClientOrderRecordVo(orderDetailsInDto.getOrderNo());
        if (!orderDetailsInDto.getUnCheckFlag() &&
                (null == orderRecordVo || !Objects.equals(customerId,orderRecordVo.getBuyUserId()))) {
            throw new BusinessException("订单信息不存在,请核实订单号！");
        }
        OrderRecord orderRecord = new OrderRecord();
        BeanUtils.copyProperties(orderRecordVo, orderRecord);
        // 校验业务模式
        dealWithBusinessModels(orderRecord);

        BeanUtils.copyProperties(orderRecord, orderRecordVo);
        orderDetailsVo.setOrderRecordVo(orderRecordVo);

        // 填充订单发票信息
        orderDetailsVo.setOrderInvoiceVo(orderInvoiceDao.getClientOrderInvoiceVo(orderDetailsInDto.getOrderNo()));

        // 填充订单行信息 start
        List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.getGoodsOrderItemVo(orderDetailsInDto.getOrderNo());

        if (null == goodsOrderItems && goodsOrderItems.size() == 0) {
            throw new BusinessException("订单行信息不存在,请联系客服！");
        }

        // 过滤主商品
        List<GoodsOrderItemVo> goodsOrderItemVos = goodsOrderItems.stream()
                .filter(goodsOrderItem -> goodsOrderItem.getSubGoodsType() == 1 && goodsOrderItem.getParentItemId() == null)
                .map(goodsOrderItem -> {
                    GoodsOrderItemVo goodsOrderItemVo = new GoodsOrderItemVo();
                    BeanUtils.copyProperties(goodsOrderItem, goodsOrderItemVo);
                    goodsOrderItemVo.setGiveIntegral(goodsOrderItem.getActivityGiveIntegral());
                    return goodsOrderItemVo;
                }).collect(Collectors.toList());

        goodsOrderItemVos.forEach(goodsOrderItemVo -> {
            // 获取组合内子品
            goodsOrderItemVo.setGroupInGoodsItemList(
                    goodsOrderItems.stream()
                            .filter(item -> item.getSubGoodsType() == 1 && item.getProductTypes() == 1 && item.getParentItemId() != null)
                            .collect(Collectors.toList())
            );

            // 获取搭配
            goodsOrderItemVo.setChildrenVoList(goodsOrderItems.stream().filter(goodsOrderItem -> {
                if (goodsOrderItemVo.getId().equals(goodsOrderItem.getParentItemId())) {
                    if (goodsOrderItem.getSubGoodsType() == 1 && goodsOrderItem.getParentItemId() != null) {
                        return false;
                    }
                    return goodsOrderItem.getSubGoodsType() != 2;
                }
                return false;
            }).collect(Collectors.toList()));
            // 获取赠品
            goodsOrderItemVo.setGiftOrderItemList(goodsOrderItems.stream().filter(goodsOrderItem -> {
                if (goodsOrderItemVo.getId().equals(goodsOrderItem.getParentItemId())) {
                    return goodsOrderItem.getSubGoodsType() == 2;
                }
                return false;
            }).collect(Collectors.toList()));
        });

        orderDetailsVo.setGoodsOrderItemVo(goodsOrderItemVos);
        // 查询商品赠送积分
//        getGoodsGiveIntegral(goodsOrderItemVos);
        // 主商品件数
        int total = goodsOrderItemVos.stream().mapToInt(GoodsOrderItemVo::getBuyNum).sum();
        for (GoodsOrderItemVo goodsOrderItemVo : goodsOrderItemVos) {
            if (!CollectionUtils.isEmpty(goodsOrderItemVo.getChildrenVoList())) {
                total += goodsOrderItemVo.getChildrenVoList().stream().mapToInt(GoodsOrderItem::getBuyNum).sum();
            }
        }
        OrderRecordVo orderRecordVo1 = orderDetailsVo.getOrderRecordVo();
        orderRecordVo1.setCouponDiscountAmountTotal(orderRecordVo.getCouponDiscountAmount());
        // 商品总件数
        orderDetailsVo.setTotalOfGoods(total);
        return orderDetailsVo;
    }

    public OrderInfoInDto getInfo(String orderNo) {
        //订单行信息
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        if(orderInfoInDto == null){
            throw new BusinessException("订单不存在");
        }
/*
        if (null != orderInfoInDto && null != orderInfoInDto.getJobId()) {
            //判断员工类型
            Result<MdmStaffInfoVto> byJobNum = orgClient.getByJobNum2(orderInfoInDto.getJobId());
            if (byJobNum != null && byJobNum.getSuccess() && byJobNum.getData() != null) {
                orderInfoInDto.setJobName(byJobNum.getData().getName());
                orderInfoInDto.setJobDepartment(byJobNum.getData().getFirstName());
            }
        }
*/
        // 根据订单号查询订单明细
        List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectOrderItemInfoList(orderNo);

        for (GoodsOrderItem goodsOrderItem : goodsOrderItems) {
            boolean present = Optional.ofNullable(goodsOrderItem.getSeriesGoodsCode()).isPresent();
            if (present) {
                goodsOrderItem.setGoodsCode(goodsOrderItem.getSeriesGoodsCode());
            }
        }

        //获取支付优惠金额
        BigDecimal payDiscountAmount = null;
        if(orderInfoInDto.getTradeInFlag() != null && orderInfoInDto.getTradeInFlag() == 1){
            List<OrderPayRecord> payRecordList = orderPayRecordDao.selectByOrderNo(orderNo);
            if(CollectionUtils.isNotEmpty(payRecordList)){
                payRecordList = payRecordList.stream().filter( t -> Objects.equals(t.getPayStatus(),1L)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(payRecordList)){
                    payDiscountAmount = payRecordList.get(0).getDiscountAmount();
                }
            }
        }



        // 获取主品
        List<GoodsOrderItemVo> goodsOrderItemVos = goodsOrderItems.stream()
                .filter(item -> item.getSubGoodsType() == 2 || item.getSubGoodsType() == 3 || item.getSubGoodsType() == 4
                        || (item.getSubGoodsType() == 1 && null == item.getParentItemId()))
                .map(item -> {
                    GoodsOrderItemVo goodsOrderItemVo = new GoodsOrderItemVo();
                    BeanUtils.copyProperties(item, goodsOrderItemVo);
                    return goodsOrderItemVo;
                }).collect(Collectors.toList());

        BigDecimal finalPayDiscountAmount = payDiscountAmount;
        goodsOrderItemVos.forEach(itemVo -> {
            itemVo.setPayDiscountAmount(finalPayDiscountAmount);
            List<GoodsOrderItem> orderItemList = goodsOrderItems.stream()
                    .filter(item -> {
                        try {
                            if (item.getSubGoodsType() == 1 && item.getParentItemId().equals(itemVo.getId())) {
                                return true;
                            }
                        } catch (Exception e) {
                            return false;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
            itemVo.setChildrenVoList(orderItemList);
        });


        orderInfoInDto.setGoodsOrderItemList(goodsOrderItemVos);
        //促销信息
        List<GoodsOrderPromotion> goodsOrderPromotions = goodsOrderPromotionDao.selectAllByOrderNo(orderNo);
        if(CollectionUtils.isNotEmpty(goodsOrderPromotions)){
            goodsOrderPromotions = goodsOrderPromotions.stream().filter( t -> Objects.equals(t.getPromotionType(),2L)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(goodsOrderPromotions)){
                goodsOrderPromotions.stream().forEach( t -> {
                    if(StringUtils.isNotBlank(t.getSeriesGoodsCode())){
                        t.setGoodsCode(t.getSeriesGoodsCode());
                    }
                });
            }
        }
        orderInfoInDto.setGoodsOrderPromotionList(goodsOrderPromotions);
        //支付信息
        orderInfoInDto.setOrderPayRecordList(orderPayRecordDao.selectByOrderNo(orderNo));
        //配送信息
        List<OrderSearchListVo> orderSearchListVos = orderDeliveryDao.queryOrderInfoDelivery(orderNo);
        if (CollectionUtils.isNotEmpty(orderSearchListVos)) {
            orderSearchListVos.stream().forEach(o -> {
                if (StringUtils.isNotBlank(o.getSeriesGoodsCode())){
                    o.setGoodsCode(o.getSeriesGoodsCode());
                }
            });
            orderInfoInDto.setOrderDeliveryList(orderSearchListVos);
        }
        //售后信息
        orderInfoInDto.setOrderAfterSaleList(orderAfterSaleDao.selectByOrderNo(orderNo));
        //发票信息
        orderInfoInDto.setOrderInvoice(orderInvoiceDao.selectByOrderNo(orderNo));
        //日志
        orderInfoInDto.setOrderOperationLogList(orderOperationLogDao.selectAll(new Long("1"), new Long("5"), orderNo));
        return orderInfoInDto;
    }

    @Transactional
    public void editAddress(OrderAddressInDto orderAddressInDto) {
        String orderNo = orderAddressInDto.getOrderNo();

        // 查询修改前数据
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        OrderRecord orderRecord = new OrderRecord();
//        BeanUtils.copyProperties(orderAddressInDto , orderRecord);
        BeanUtil.copyProperties(orderAddressInDto,orderRecord);
        // 修改订单收货地址
        UpdateWrapper wrapper = new UpdateWrapper();
        /*
        wrapper.set("receiver_name", orderAddressInDto.getReceiverName());
        wrapper.set("receiver_phone", orderAddressInDto.getReceiverPhone());
        wrapper.set("province_id", orderAddressInDto.getProvinceId());
        wrapper.set("province_name", orderAddressInDto.getProvinceName());
        wrapper.set("city_id", orderAddressInDto.getCityId());
        wrapper.set("city_name", orderAddressInDto.getCityName());
        wrapper.set("area_id", orderAddressInDto.getAreaId());
        wrapper.set("area_name", orderAddressInDto.getAreaName());
        wrapper.set("address", orderAddressInDto.getAddress());
        */
        wrapper.eq("order_no", orderAddressInDto.getOrderNo());
        orderDao.update(orderRecord,wrapper);

        String text = "";
        try {
            OrderAddressInDto orderAddressInDto1 = new OrderAddressInDto();
            orderAddressInDto1.setOrderNo(orderInfoInDto.getOrderNo());
            orderAddressInDto1.setReceiverName(orderInfoInDto.getReceiverName());
            orderAddressInDto1.setProvinceId(orderInfoInDto.getProvinceId());
            orderAddressInDto1.setProvinceName(orderInfoInDto.getProvinceName());
            orderAddressInDto1.setCityId(orderInfoInDto.getCityId());
            orderAddressInDto1.setCityName(orderInfoInDto.getCityName());
            orderAddressInDto1.setAreaId(orderInfoInDto.getAreaId());
            orderAddressInDto1.setAreaName(orderInfoInDto.getAreaName());
            orderAddressInDto1.setAddress(orderInfoInDto.getAddress());
            orderAddressInDto1.setReceiverPhone(Long.valueOf(orderInfoInDto.getReceiverPhone().trim()));
            // 对比修改了那些属性值
            List<String> list = compareFields(orderAddressInDto1, orderAddressInDto, OrderAddressInDto.class);
            text = text + String.join("，", list);
        } catch (Exception e) {
            text = "添加编辑收货信息日志说明异常";
        }
        // 添加日志记录
        if (!text.isBlank()) {
            addLog(orderNo, "编辑收货信息", text);
        }
    }

    public void editInvoice(OrderInvoice orderInvoice) {
        if (orderInvoice.getId() == null) {
            orderInvoiceDao.insert(orderInvoice);
        } else {
            orderInvoiceDao.updateByPrimaryKeySelective(orderInvoice);
        }
    }

    @Transactional
    public void editAfterSale(OrderAfterSale orderAfterSale) {
        OrderAfterSale afterSale = orderAfterSaleDao.selectById(orderAfterSale.getId());
        switch (afterSale.getProcessingStatus().intValue()) {
            case 0:
                break;
            case 1:
                break;
            case 2:
                break;
            case 3:
                break;
            case 4:
                break;
            default:
                throw new BusinessException("状态不存在!");
        }


        // 编辑订单售后信息
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        orderAfterSale.setModifiedBy(userAuthor.getUserId());
        orderAfterSale.setModifiedByName(userAuthor.getFirstName());
        orderAfterSale.setModifiedDate(new Date());
        orderAfterSaleDao.updateById(orderAfterSale);
        // 记录订单售后操作日志
        recordOrderOperationLog(orderAfterSale,afterSale);

        LambdaUpdateWrapper<OrderRecord> orderUpdate = new LambdaUpdateWrapper<>();
        // 同步订单退货状态 processingStatus处理状态 0:申请中 1:受理中 2:处理完成 3:拒绝处理 4:已取消
        Long processingStatus = orderAfterSale.getProcessingStatus();
        // 售后类型 1退货 2换货
        Long afterSaleType = orderAfterSale.getAfterSaleType();
        if (afterSaleType == 1) { // 退货
            switch (processingStatus.intValue()) {
                case 0:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 0);
                    break;
                case 1:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 1);
                    break;
                case 2:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 2);
                    break;
                case 3:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 1);
                    break;
                case 4:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 6);
                    break;
                default:
                    throw new BusinessException("状态不存在");
            }
        }
        if (afterSaleType == 2) { // 换货
            switch (processingStatus.intValue()) {
                case 0:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 3);
                    break;
                case 1:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 4);
                    break;
                case 2:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 5);
                    break;
                case 3:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 4);
                    break;
                case 4:
                    orderUpdate.set(OrderRecord::getAfterSaleStatus, 6);
                    break;
                default:
                    throw new BusinessException("状态不存在");
            }
        }

        orderUpdate.eq(OrderRecord::getOrderNo, orderAfterSale.getOrderNo());
        orderDao.update(null, orderUpdate);
    }

    private void recordOrderOperationLog(OrderAfterSale newAfterSale, OrderAfterSale oldAfterSale) {
        // 修改标识
        boolean modifiedFlag = false;
        String description = "";
        // 售后类型 1退货 2换货
        if (!Objects.equals(newAfterSale.getAfterSaleType(), oldAfterSale.getAfterSaleType())){
            modifiedFlag = true;
            String newType = newAfterSale.getAfterSaleType().intValue() == 1 ? "退货" : "换货";
            String oldType = oldAfterSale.getAfterSaleType().intValue() == 1 ? "退货" : "换货";
            description = description + "售后类型：从\"" + oldType + "\"变更为\"" + newType + "\"";
        }
        // 申请原因
        if (!Objects.equals(newAfterSale.getApplyReason(),oldAfterSale.getApplyReason())) {
            modifiedFlag = true;
            description = description + "申请原因：从\"" + oldAfterSale.getApplyReason() + "\"变更为\"" + newAfterSale.getApplyReason() + "\"";
        }
        // 买家原因
        if (!Objects.equals(newAfterSale.getBuyerReason(), oldAfterSale.getBuyerReason())) {
            modifiedFlag = true;
            description = description + "买家原因：从\"" + oldAfterSale.getBuyerReason() + "\"变更为\"" + newAfterSale.getBuyerReason() + "\"";
        }
        // 凭证
        if (!Objects.equals(newAfterSale.getCertificate(), oldAfterSale.getCertificate())) {
            modifiedFlag = true;
            description = description + "凭证：从\"" + oldAfterSale.getCertificate() + "\"变更为\"" + newAfterSale.getCertificate() + "\"";
        }
        // 处理状态 0:申请中 1:受理中 2:处理完成 3:拒绝处理 4:已取消
        if (!Objects.equals(newAfterSale.getProcessingStatus(),oldAfterSale.getProcessingStatus())){
            modifiedFlag = true;
            String newStatus = getProcessingStatusByNo(newAfterSale.getProcessingStatus());
            String oldStatus = getProcessingStatusByNo(oldAfterSale.getProcessingStatus());
            description = description + "处理状态：从\"" + oldStatus + "\"变更为\"" + newStatus + "\"";
        }
        // 客服备注
        if (!Objects.equals(newAfterSale.getServiceRemark(),oldAfterSale.getServiceRemark())) {
            modifiedFlag = true;
            description = description + "客服备注：从\"" + oldAfterSale.getServiceRemark() + "\"变更为\"" + newAfterSale.getServiceRemark() + "\"";
        }
        // 未修改则直接返回，不记录日志
        if (!modifiedFlag){
            return;
        }
        OrderAfterSaleOperationLog saleOperationLog = new OrderAfterSaleOperationLog();

        saleOperationLog.setApplyId(newAfterSale.getId());
        saleOperationLog.setDescription(description);
        saleOperationLog.setOperationType("编辑退换货信息");
        saleOperationLog.setOperationTime(new Date());
        saleOperationLog.setIsDeleted(0);

        saleOperationLog.setOperationById(newAfterSale.getModifiedBy());
        saleOperationLog.setOperationByName(newAfterSale.getModifiedByName());

        orderAfterSaleOperationLogDao.insert(saleOperationLog);
    }

    private String getProcessingStatusByNo(Long processingStatus) {
        // 处理状态 0:申请中 1:受理中 2:处理完成 3:拒绝处理 4:已取消
        switch (processingStatus.intValue()) {
            case 0:
                return "申请中";
            case 1:
                return "受理中";
            case 2:
                return "处理完成";
            case 3:
                return "拒绝处理";
            case 4:
                return "已取消";
            default:
                return null;
        }
    }


    public void editGoodsOrderItem(GoodsOrderItem goodsOrderItem) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("goods_price", goodsOrderItem.getGoodsPrice());
        if (null != goodsOrderItem.getDiscountAmount()) {
            wrapper.set("discount_amount", goodsOrderItem.getDiscountAmount());
        }
        if (null != goodsOrderItem.getDescription()) {
            wrapper.set("description", goodsOrderItem.getDescription());
        }
        wrapper.eq("id", goodsOrderItem.getId());
        goodsOrderItemDao.update(null, wrapper);
    }

    public void deleteGoodsOrderItem(Long id) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("is_deleted", 1);
        wrapper.eq("id", id);
        goodsOrderItemDao.update(null, wrapper);
    }


    public void addGoodsOrderItem(GoodsOrderItem goodsOrderItem) {
        goodsOrderItemDao.insert(goodsOrderItem);
    }


    /**
     * 生成预下单信息
     *
     * @param request
     * @return
     */
    public Map preOrder(PreOrderDto request) {

        // 预下单信息
        PreOrderOutDto preOrderOutDto = new PreOrderOutDto();

        // 获取用户信息
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();

//        CustomerDetailOutDto customerDetailOutDto = orderUtil.getCustomerInfo(request.getPhone());
        if (null == customerInfo) {
            throw new BusinessException("用户信息不存在!");
        }

        // 校验黑名单
        orderUtil.checkBlackList(request.getPhone());

        preOrderOutDto.setCustomerId(customerInfo.getId());
        preOrderOutDto.setCustomerPhone(customerInfo.getPhone());
        preOrderOutDto.setCustomerName(customerInfo.getName());
        preOrderOutDto.setType(request.getType());

        // 校验购买商品信息
        List<OrderGoodsInfoVO> orderGoodsInfoVOList = this.validatePreOrderRequest(request.getType(), request.getGoodsItems(), true, preOrderOutDto);

        preOrderOutDto.setGoodsInfoList(orderGoodsInfoVOList);

        // 根据商品ID查询商品活动信息
        orderUtil.validateActivity(preOrderOutDto);

        // 优惠券优惠金额
        BigDecimal couponDeductionAmount = BigDecimal.ZERO;
        // 查询优惠券
        List<AvailableCardCodeVO> availableCardCodeVOList = orderUtil.getAvailableCardCodeByPhone(request.getPhone());


        for (AvailableCardCodeVO availableCardCodeVO : availableCardCodeVOList) {
            log.error("预下单失败日志1：{}",JSON.toJSONString(availableCardCodeVO));
            log.error("预下单失败日志2：{}",JSON.toJSONString(preOrderOutDto.getGoodsInfoList()));

            // 计算订单可优惠金额
            BigDecimal orderAmount = orderUtil.computedOrderAmountPriceByCard(availableCardCodeVO, preOrderOutDto.getGoodsInfoList());
//            log.error("优惠券计算时的优惠金额" + orderAmount);
            BigDecimal couponsDiscountedPrice = BigDecimal.ZERO;
            log.error("预下单失败日志3：{}",JSON.toJSONString(orderAmount));
            // 计算订单优惠金额
            couponsDiscountedPrice = orderUtil.computedDiscountedPrice(orderAmount, availableCardCodeVO);

            couponDeductionAmount = couponDeductionAmount.add(couponsDiscountedPrice);
        }

//        log.error("优惠券信息" + JSON.toJSONString(availableCardCodeVOList));

        // 对优惠券进行排序
//        availableCardCodeVOList.sort(Comparator.comparing(AvailableCardCodeVO::getUserStatus).thenComparing(AvailableCardCodeVO::getCouponsDiscountedPrice).reversed());

        // 过滤不可优惠的优惠券
        availableCardCodeVOList = availableCardCodeVOList.stream().filter(e -> e.getCouponsDiscountedPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        //排序
        List<AvailableCardCodeVO> unuserCard = availableCardCodeVOList.stream().filter(c -> c.getUserStatus() == 0).collect(Collectors.toList());
        List<AvailableCardCodeVO> userCard = availableCardCodeVOList.stream().filter(c -> c.getUserStatus() != 0).collect(Collectors.toList());
        List<AvailableCardCodeVO> cardCodeVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unuserCard)) {
            unuserCard.sort(comparing(AvailableCardCodeVO::getCouponsDiscountedPrice).reversed());
            cardCodeVOList.addAll(unuserCard);
        }
        if (CollectionUtils.isNotEmpty(userCard)) {
            userCard.sort(comparing(AvailableCardCodeVO::getCouponsDiscountedPrice).reversed());
            cardCodeVOList.addAll(userCard);
        }
        // 优惠券信息
        preOrderOutDto.setCouponList(cardCodeVOList);
//        preOrderOutDto.setCouponDeductionAmount(couponDeductionAmount);

        // 计算商品总金额
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (OrderGoodsInfoVO orderGoodsInfoVO : preOrderOutDto.getGoodsInfoList()) {
            // 计算商品价格 + 搭配商品价格
            BigDecimal matchingPrice = orderGoodsInfoVO.getMatchingProductsList().stream().map(e -> e.getListedPrice().multiply(new BigDecimal(e.getPayNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal goodsPrice = orderGoodsInfoVO.getListedPrice().multiply(new BigDecimal(orderGoodsInfoVO.getPayNum()));
            totalPrice = totalPrice.add(matchingPrice).add(goodsPrice);

        }
        preOrderOutDto.setGoodsTotalAmount(totalPrice);

        // 计算实付金额
        BigDecimal actualPaymentAmount = preOrderOutDto.getGoodsInfoList().stream().map(e -> e.getOrderPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 计算去除配件商品后的实付金额
        BigDecimal actualPaymentWithoutAccessoryAmount = preOrderOutDto.getGoodsInfoList().stream().filter(s->!Objects.equals(s.getFirstCategoryId(),OrderConstant.channelCategoryAccessoryID)).map(e -> e.getOrderPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);


        // 订单总金额
        preOrderOutDto.setOrderTotalAmount(actualPaymentAmount);
        // 实际支付金额
        if (null != preOrderOutDto.getDeposit()) {
            preOrderOutDto.setActualPaymentAmount(preOrderOutDto.getDeposit());
        } else {
            preOrderOutDto.setActualPaymentAmount(actualPaymentAmount);
        }

        // 计算会员赠送积分
//        Long memberGivePoints = orderUtil.computedMemberGivePoints(request.getCustomerId(), actualPaymentAmount);
        Long memberGivePoints = orderUtil.computedMemberGivePoints(request.getCustomerId(), actualPaymentWithoutAccessoryAmount);
        preOrderOutDto.setMemberGivePoints(memberGivePoints);

        // 赠送成长值
//        preOrderOutDto.setGiveGrowthValue(actualPaymentAmount.setScale(0, BigDecimal.ROUND_DOWN).longValue());
        preOrderOutDto.setGiveGrowthValue(actualPaymentWithoutAccessoryAmount.setScale(0, BigDecimal.ROUND_DOWN).longValue());

        // 购买商品总数量
        int orderProductNum = 0;
        for (OrderGoodsInfoVO orderGoodsInfoVO : preOrderOutDto.getGoodsInfoList()) {
            int matchingNum = orderGoodsInfoVO.getMatchingProductsList().stream().mapToInt(OrderGoodsInfoVO.MatchingProducts::getPayNum).sum();
            orderProductNum = orderProductNum + orderGoodsInfoVO.getPayNum() + matchingNum;
        }
        preOrderOutDto.setOrderProNum(orderProductNum);


        // 查询默认收货地址
        UserAddressFromMall userAddress = customerClient.queryDefaultAddressByCustomerId(request.getCustomerId()).getData();
        if (null != userAddress) {
            preOrderOutDto.setUserAddress(userAddress);
        }

        // 获取客户管家
        GetCustomerStewardDto customerStewardDto = orderUtil.getCustomerSteward(request.getCustomerId());
        preOrderOutDto.setStewardId(customerStewardDto.getStewardId());
        preOrderOutDto.setStewardName(customerStewardDto.getStewardName());

        // 缓存订单
        String key = orderUtil.savePreOrder(request.getCustomerId(), preOrderOutDto);

        // 返回下单信息
        Map<String, String> resultData = new HashMap<>();
        resultData.put("preOrderNo", key);
        return resultData;
    }

    @Autowired
    private JobOrderRecordService jobOrderRecordService;

    public Map preOrderInternal(PreOrderDto request) {
        PreOrderOutDto preOrderOutDto = new PreOrderOutDto();


        //查看有效活动
        Result<MallActivity> mallActivity = marketingClient.getMallActivityById2(request.getActivityId());
        MallActivity data = null;
        if (mallActivity != null && mallActivity.getData() != null) {
            data = mallActivity.getData();

            Date startTime = StringUtils.isNotBlank(data.getActivityStartTime()) ? DateUtil.parseDateTime(data.getActivityStartTime()) : null;
            Date endTime = StringUtils.isNotBlank(data.getActivityEndTime()) ? DateUtil.parseDateTime(data.getActivityEndTime()) : null;

            long nowTime = System.currentTimeMillis();

            if (data.getActivityStatus() == 2L) {
                if ((startTime != null &&
                        startTime.getTime() > nowTime)
                ) {
                    // 根据UAT296要求，预下单时不校验活动是否已开始，
//                    throw new BusinessException("活动未开始");
                } else if (endTime != null && endTime.getTime() < nowTime) {
                    throw new BusinessException("活动已结束");

                }
            } else {
                throw new BusinessException("活动未上架");
            }
            // 返回活动开始时间
            preOrderOutDto.setActivityStartTime(startTime);


            // 设置活动的待支付取消时间（小时 * 60 + 分钟）
            preOrderOutDto.setUnpaidOrderCancellationMinutes(data.getUnpaidOrderCancellationTime() * 60 + data.getUnpaidOrderCancellationMinutes());
            // 同一品类限购台数
            preOrderOutDto.setUserLimitNum(data.getCategoryLimitNum());
            // 活动类型
            preOrderOutDto.setActivityType(data.getActivityType().intValue());
        } else {
            throw new BusinessException("无法获取活动信息");
        }

        //判断商品是否存在
        if (CollectionUtils.isEmpty(request.getGoodsItems())) {
            throw new BusinessException("至少选择一件商品");
        }
        // 预下单信息
        CustomerDetailOutDto customerDetailOutDto = orderUtil.getCustomerInfo(request.getPhone());
        if (null == customerDetailOutDto) {
            throw new BusinessException("用户信息不存在!");
        }
        preOrderOutDto.setCustomerId(customerDetailOutDto.getId());
        preOrderOutDto.setActivityId(request.getActivityId());
        preOrderOutDto.setCustomerPhone(customerDetailOutDto.getPhone());
        preOrderOutDto.setCustomerName(customerDetailOutDto.getNickname());
        preOrderOutDto.setGradeId(customerDetailOutDto.getGradeId());
        preOrderOutDto.setType(request.getType());
        request.setActivityType(preOrderOutDto.getActivityType());
        preOrderOutDto.setJobId(request.getJobId());

        List<PreOrderDto.GoodsItem> goodsInfoList = request.getGoodsItems();
        if (CollectionUtils.isEmpty(goodsInfoList)) {
            throw new BusinessException("商品不能为空");
        }

        // 是否校验库存
        Boolean isCheckStock = true;
        // 如果是家人购，则不校验
        if (mallActivity.getData().getActivityType().intValue() == 7) {
            // 校验黑名单
            GetUserBlackListByMobileAndTypesDto dto = new GetUserBlackListByMobileAndTypesDto();
            dto.setMobile(request.getPhone());
            List<Integer> blackTypeList = new ArrayList<>();
            // 3 -> 官方商城
            blackTypeList.add(3);
            // 7 -> 官方商城 - 家人购
            blackTypeList.add(7);
            dto.setBlackTypeList(blackTypeList);
            orderUtil.checkBlackListByJRG(dto);
            isCheckStock = false;
        }
        // 获取商品信息
        List<OrderGoodsInfoVO> orderGoodsInfoVOList = this.validatePreOrderRequest(request.getType(), request.getGoodsItems(), isCheckStock, preOrderOutDto);
        // 去除商品赠品
        for (OrderGoodsInfoVO orderGoodsInfoVO : orderGoodsInfoVOList) {
            orderGoodsInfoVO.setGiveGoodsList(new ArrayList<>());
        }

        // 商品编码
        List<String> goodsCodes = orderGoodsInfoVOList.stream().map(OrderGoodsInfoVO::getGoodsCode).distinct().collect(Collectors.toList());
        Result<List<GetOrderGoodsInfoByCodesDTO>> orderGoodsInfoByCodesR = productClient.getOrderGoodsInfoByCodes(goodsCodes);
        if (orderGoodsInfoByCodesR == null || !orderGoodsInfoByCodesR.getSuccess() || orderGoodsInfoByCodesR.getData() == null) {
            throw new BusinessException("商品不存在");

        }
        List<GetOrderGoodsInfoByCodesDTO> orderGoodsInfoByCodes = orderGoodsInfoByCodesR.getData();


        if (request.getActivityType() != null) {
            JobOrderRecordListDTO jobOrderRecordListDTO = new JobOrderRecordListDTO();
            jobOrderRecordListDTO.setJobId(request.getJobId());
            jobOrderRecordListDTO.setActivityType(request.getActivityType());
            // 判断是否家人购
            if (null != request.getActivityType() && request.getActivityType().intValue() == 7) {
                // 跳转追加活动ID
                jobOrderRecordListDTO.setActivityId(request.getActivityId());
            }

            if (request.getActivityType().equals(8)) {
//                List<String> buyProdct = new ArrayList<>();
//                JobOrderRecordListVO list = jobOrderRecordService.listInternal(jobOrderRecordListDTO);

                jobOrderRecordService.judgeGoodsRejectRelation(jobOrderRecordListDTO,orderGoodsInfoByCodes,data.getCategoryLimitNum());
/*
                if(list != null && CollectionUtils.isNotEmpty(list.getHaveBuyProductGroupBO())){
                    List<String> collect = list.getHaveBuyProductGroupBO().stream().map(t -> t.getProductRejectionGroupCode()).collect(Collectors.toList());
                    buyProdct.addAll(collect);
                }

                if (!CollectionUtils.isEmpty(list.getNotBuyProductGroupBO())) {
                    Map<String, List<GetOrderGoodsInfoByCodesDTO>> collect = orderGoodsInfoByCodes.stream().collect(Collectors.groupingBy(GetOrderGoodsInfoByCodesDTO::getGoodsGroupCode));
                    orderGoodsInfoByCodes.stream().forEach(goodsItem -> {

                        if (buyProdct.contains(goodsItem.getGoodsGroupCode())) {
                            throw new BusinessException("产品型号" + goodsItem.getGoodsName() + "您已经购买过该产品组同类商品，不能再次购买");
                        }
                        List<JobOrderRecordBuyProductGroupBO> jobOrderRecordBuyProductGroupBOs =
                                list.getNotBuyProductGroupBO().stream().filter(product ->
                                StringUtils.isNotEmpty(goodsItem.getGoodsGroupCode()) && goodsItem.getGoodsGroupCode().equals(product.getCode()))
                                        .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(jobOrderRecordBuyProductGroupBOs)) {
                            throw new BusinessException("产品组"+goodsItem.getGoodsGroupCode()+"暂不可购买，请联系客服处理");
                        }
                        //判断是否超出购买数量
                        if (null != collect && null != collect.get(goodsItem.getGoodsGroupCode()) && collect.get(goodsItem.getGoodsGroupCode()).size() > preOrderOutDto.getUserLimitNum()) {
                            throw new BusinessException("产品型号" + goodsItem.getGoodsName() + "已超出本次活动购买上限，请换件产品或减少同品类数量");
                        }
//                        buyProdct.add(goodsItem.getGoodsGroupCode());
//                        jobOrderRecordBuyProductGroupBOs.stream().forEach(jobOrderRecordBuyProductGroupBO -> {
//                            buyProdct.add(jobOrderRecordBuyProductGroupBO.getProductRejectionGroupCode());
//                        });
                    });
                } else {
                    throw new BusinessException("暂无可购买商品");
                }
*/
            } else if (request.getActivityType().equals(7)) {

                List<JobOrderRecordEntity> list = jobOrderRecordService.haveBuyProductGroup(jobOrderRecordListDTO);
                Long unpaidOrderCancellationTime = data.getCategoryLimitNum() == null ? 1 : data.getCategoryLimitNum();
                Map<String, Integer> map = new ConcurrentHashMap<>();
                if (!CollectionUtils.isEmpty(list)) {
                    List<Long> jobOrderGoodIds = list.stream().map(JobOrderRecordEntity::getGoodsId).distinct().collect(Collectors.toList());
                    // 查询商品品类
                    Result<List<GetOrderGoodsInfoByCodesDTO>> orderGoodsInfoByIdsR = productClient.getOrderGoodsInfoByIds(jobOrderGoodIds);
                    if (orderGoodsInfoByIdsR == null || !orderGoodsInfoByIdsR.getSuccess() || orderGoodsInfoByIdsR.getData() == null) {
                        throw new BusinessException("商品不存在");

                    }

                    List<GetOrderGoodsInfoByCodesDTO> orderGoodsInfoByIdsData = orderGoodsInfoByIdsR.getData();
                    Map<Long, String> goodsPlMap = orderGoodsInfoByIdsData.stream()
                            .collect(Collectors.toMap(GetOrderGoodsInfoByCodesDTO::getId, GetOrderGoodsInfoByCodesDTO::getGoodsPlCode));


                    //计算已买品类数量
                    for (JobOrderRecordEntity jobOrderRecordEntity : list) {
                        if (StringUtils.isNotBlank(jobOrderRecordEntity.getGoodsLineCode())) {
                            if(goodsPlMap.containsKey(jobOrderRecordEntity.getGoodsId())){
                                //产品品类
                                String goodsPl = goodsPlMap.get(jobOrderRecordEntity.getGoodsId());
                                map.put(goodsPl,
                                        map.get(goodsPl) == null ? jobOrderRecordEntity.getPayNum() : map.get(goodsPl) + jobOrderRecordEntity.getPayNum());
                            }

                        }
                    }
                }

                StringBuffer stringBuffer = new StringBuffer();
                //判断是否超出购买数量
                for (GetOrderGoodsInfoByCodesDTO orderGoodsInfoVO : orderGoodsInfoByCodes) {
                    OrderGoodsInfoVO orderGoodsInfoVOStream = orderGoodsInfoVOList.stream().filter(goodInfo -> goodInfo.getGoodsId().equals(orderGoodsInfoVO.getId())).findFirst().orElse(null);
                    if (!StringUtils.isEmpty(orderGoodsInfoVO.getGoodsPlCode())) {
                        Integer existingcount = map.get(orderGoodsInfoVO.getGoodsPlCode()) == null ? 0 : map.get(orderGoodsInfoVO.getGoodsPlCode());
                        Integer count = existingcount;
                        if (orderGoodsInfoVOStream != null) {
                            count = count + orderGoodsInfoVOStream.getPayNum();
                        }
                        if (count > unpaidOrderCancellationTime) {
                            stringBuffer.append("&" + orderGoodsInfoVO.getGoodsName() + "&");
//                            throw new BusinessException("产品线:" + orderGoodsInfoVO.getGoodsLineName() + "购买已达上限");
                        }
                        map.put(orderGoodsInfoVO.getGoodsPlCode(), map.get(orderGoodsInfoVO.getGoodsPlCode()) == null ? Objects.requireNonNull(orderGoodsInfoVOStream).getPayNum() : map.get(orderGoodsInfoVO.getGoodsPlCode()) + Objects.requireNonNull(orderGoodsInfoVOStream).getPayNum());
                    }
                }
                if(stringBuffer.length() > 0){
                    StringBuffer sb = new StringBuffer();
                    sb.append("以下商品已达本场活动购买上限，请选购其他产品线！\n");
                    sb.append(stringBuffer);
                    throw new BusinessException(sb.toString());
                }

            } else {

            }
        } else {
            throw new BusinessException("活动类型不能为空");
        }
        preOrderOutDto.setGoodsInfoList(orderGoodsInfoVOList);

        // 根据商品ID查询商品活动信息
        List<OrderGoodsInfoVO> orderGoodsInfoVOS = orderUtil.validateActivityInternal(preOrderOutDto);
        if (!CollectionUtils.isEmpty(orderGoodsInfoVOS)) {
            Map map = new HashMap();
            map.put("failureOrderGoodsInfoVOList", orderGoodsInfoVOS);
            return map;
        }


        // 计算商品总金额
        BigDecimal totalPrice = preOrderOutDto.getGoodsInfoList().stream().map(e -> e.getOrderPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal goodsTotalAmount = preOrderOutDto.getGoodsInfoList().stream().map(e -> e.getListedPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        preOrderOutDto.setGoodsTotalAmount(goodsTotalAmount);

        // 订单总金额
        preOrderOutDto.setOrderTotalAmount(totalPrice);

        // 实际支付金额
        if (null != preOrderOutDto.getDeposit()) {
            preOrderOutDto.setActualPaymentAmount(preOrderOutDto.getDeposit());
        } else {
            preOrderOutDto.setActualPaymentAmount(totalPrice);
        }

        // 如果是展厅，则赠送积分
        if (mallActivity.getData().getActivityType().longValue() == 9) {
            Long memberGivePoints = orderUtil.computedMemberGivePoints(request.getCustomerId(), totalPrice);
            preOrderOutDto.setMemberGivePoints(memberGivePoints);
        }


        // 赠送成长值
        preOrderOutDto.setGiveGrowthValue(totalPrice.setScale(0, RoundingMode.DOWN).longValue());

        // 购买商品总数量
        int orderProductNum = 0;
        for (OrderGoodsInfoVO orderGoodsInfoVO : preOrderOutDto.getGoodsInfoList()) {
            int matchingNum = orderGoodsInfoVO.getMatchingProductsList().stream().mapToInt(OrderGoodsInfoVO.MatchingProducts::getPayNum).sum();
            orderProductNum = orderProductNum + orderGoodsInfoVO.getPayNum() + matchingNum;
        }
        preOrderOutDto.setOrderProNum(orderProductNum);


        // 查询默认收货地址
        if (request.getActivityType() != null && request.getActivityType() == 8 && StringUtils.isNotEmpty(request.getJobId())) {
            Result<MallPurchaseQualificationsAudit> mallOurchQualificationsByJobId = marketingClient.getMallOurchQualificationsByJobId(request.getJobId(), 2);
            if (mallOurchQualificationsByJobId != null && mallOurchQualificationsByJobId.getSuccess() && mallOurchQualificationsByJobId.getData() != null) {
                MallPurchaseQualificationsAudit mallPurchaseQualificationsAudit = mallOurchQualificationsByJobId.getData();
                UserAddressFromMall userAddress = new UserAddressFromMall();
                userAddress.setAddress(mallPurchaseQualificationsAudit.getAddress());
                userAddress.setProvinceId(mallPurchaseQualificationsAudit.getProvinceId());
                userAddress.setProvinceName(mallPurchaseQualificationsAudit.getProvinceName());
                userAddress.setCityId(mallPurchaseQualificationsAudit.getCityId());
                userAddress.setCityName(mallPurchaseQualificationsAudit.getCityName());
                userAddress.setCountyId(mallPurchaseQualificationsAudit.getAreaId());
                userAddress.setCountyName(mallPurchaseQualificationsAudit.getAreaName());
                userAddress.setName(mallPurchaseQualificationsAudit.getConsigneeName());
                userAddress.setPhone(String.valueOf(mallPurchaseQualificationsAudit.getConsigneePhone()));
                preOrderOutDto.setUserAddress(userAddress);
                //                preOrderOutDto.setUserAddress(mallOurchQualificationsByJobId.getData().getAddress());

            }
        } else {
            UserAddressFromMall userAddress = customerClient.queryDefaultAddressByCustomerId(request.getCustomerId()).getData();
            if (null != userAddress) {
                preOrderOutDto.setUserAddress(userAddress);
            }
        }
        UserAddressFromMall userAddressFromMall = customerClient.queryDefaultInstallAddressByCustomerId(request.getCustomerId()).getData();
        if (null != userAddressFromMall) {
            UserInstallAddress userInstallAddress = new UserInstallAddress();
            BeanUtils.copyProperties(userAddressFromMall,userInstallAddress);
            preOrderOutDto.setUserInstallAddress(userInstallAddress);
        }

        // 缓存订单
        String key = orderUtil.savePreOrder(request.getCustomerId(), preOrderOutDto);

        // 返回下单信息
        Map<String, String> resultData = new HashMap<>();
        resultData.put("preOrderNo", key);
        return resultData;
    }

    //校验员工今年参加活动次数是否超限
    public Result checkActivityLimitNum(String jobId){
        if(StringUtils.isNotEmpty(jobId) && (jobId.startsWith("S") || jobId.startsWith("s"))){
            return Result.buildFailure("内购仅开放签订了方太正式合同的员工");
        }
        Result<GetActivityByActivityTypeVO> mallActivityByType = marketingClient.getMallActivityByType(7);
        if(!mallActivityByType.getSuccess() || mallActivityByType.getData() == null){
            return Result.buildFailure("活动正在筹划中");
        }
        JobOrderRecordListDTO jobOrderRecordListDTO = new JobOrderRecordListDTO();
        jobOrderRecordListDTO.setJobId(jobId);
        jobOrderRecordListDTO.setActivityType(7);
        //今年参加活动次数
        List<JobOrderRecordEntity> list = jobOrderRecordService.haveBuyProductGroupByJrg(jobOrderRecordListDTO);
        if(CollectionUtils.isEmpty(list)){
            return Result.buildSuccess("操作成功");
        }
        Result<MallActivity> mallActivityById = marketingClient.getMallActivityByIdOpen(mallActivityByType.getData().getActivityId());
        MallActivity mallActivityByIdData = mallActivityById.getData();
        if(mallActivityByIdData.getActivityLimitNum() == null || mallActivityByIdData.getActivityLimitNum() == 0L){
            return Result.buildFailure("本年度参与方太家人内购活动次数已达上限，请明年参与，感谢！");
        }
        Map<Integer, List<JobOrderRecordEntity>> collect = list.stream().collect(Collectors.groupingBy(JobOrderRecordEntity::getActivityId));
        if(collect.keySet().size() >= mallActivityByIdData.getActivityLimitNum().intValue()
                && !collect.containsKey(mallActivityByIdData.getId().intValue())){
            return Result.buildFailure("本年度参与方太家人内购活动次数已达上限，请明年参与，感谢！");
        }
        return Result.buildSuccess("操作成功");
    }


    /**
     * 校验商品状态
     *
     * @param orderInfo
     * @return
     */
    public List<OrderGoodsInfoVO> validatePreOrderRequest(PreOrderOutDto orderInfo) {
        List<PreOrderDto.GoodsItem> goodsItems = new ArrayList<>();
        for (OrderGoodsInfoVO orderGoodsInfoVO : orderInfo.getGoodsInfoList()) {
            PreOrderDto.GoodsItem goodsItem = new PreOrderDto.GoodsItem();
            goodsItem.setCartId(orderGoodsInfoVO.getCartId());
            goodsItem.setGoodsCode(orderGoodsInfoVO.getGoodsCode());
            goodsItem.setSeriesId(orderGoodsInfoVO.getSeriesId());
            goodsItem.setProductNum(orderGoodsInfoVO.getPayNum());
            // 创建搭配商品
            List<PreOrderDto.GoodsItem> matchingItems = new ArrayList<>();
            for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                PreOrderDto.GoodsItem matchingItem = new PreOrderDto.GoodsItem();
                matchingItem.setSeriesId(matchingProducts.getSpecificationId().toString());
                matchingItems.add(matchingItem);
            }
            goodsItem.setMatchingProducts(matchingItems);
            goodsItems.add(goodsItem);
        }
        return validatePreOrderRequest(orderInfo.getType(), goodsItems, true, orderInfo);
    }


    /**
     * 校验商品信息
     *
     * @param type
     * @param goodsItems
     * @return
     */
    public List<OrderGoodsInfoVO> validatePreOrderRequest(String type, List<PreOrderDto.GoodsItem> goodsItems,
                                                          Boolean isCheckStock, PreOrderOutDto preOrderOutDto) {
        if (null == goodsItems || goodsItems.size() == 0) {
            throw new BusinessException("参数错误！");
        }

        List<OrderGoodsInfoVO> orderGoodsInfoVOList = new ArrayList<>();
        if (type.equals("cart")) {
            // 购物车结算，查询购物车订单
            List<Long> cartIds = new ArrayList<>();
            for (PreOrderDto.GoodsItem goodsItem : goodsItems) {
                if (null == goodsItem.getCartId()) {
                    throw new BusinessException("购物车ID参数错误！");
                }
                cartIds.add(goodsItem.getCartId());
            }
            // 查询购物车商品信息
            orderGoodsInfoVOList = orderUtil.queryOrderGoodsInfoByCartIds(cartIds);
        } else if (type.equals("buy")) {
            // 直接购买
            PreOrderDto.GoodsItem goodsItem = goodsItems.get(0);
            if (StringUtils.isBlank(goodsItem.getGoodsCode())) {
                throw new BusinessException("参数错误，缺失商品编码");
            }
            if (null == goodsItem.getProductNum()) {
                throw new BusinessException("参数错误，缺失商品数量");
            }
            OrderGoodsInfoVO orderGoodsInfoVO = orderUtil.queryOrderGoodsInfoByCode(goodsItem);
            // 设置搭配商品数量为购买数量
            for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                // 立即购买的搭配商品数量为1
                matchingProducts.setPayNum(1);
            }
            // 主商品购买数量
            orderGoodsInfoVO.setPayNum(goodsItem.getProductNum());

            orderGoodsInfoVOList.add(orderGoodsInfoVO);
        }

        // 商品ID
        List<Long> goodsIds = new ArrayList<>();

        // 计算订单金额
        for (OrderGoodsInfoVO orderGoodsInfoVO : orderGoodsInfoVOList) {
            // 设置赠品数量为购买数量
            for (OrderGoodsInfoVO.GiveGoods giveGoods : orderGoodsInfoVO.getGiveGoodsList()) {
                giveGoods.setPayNum(orderGoodsInfoVO.getPayNum());
            }

            // 商品ID
            goodsIds.add(orderGoodsInfoVO.getGoodsId());

            // 配件优惠
            BigDecimal matchingProductDiscounted = BigDecimal.ZERO;

            // 计算商品金额 挂牌价 * 购买数量
            BigDecimal orderPrice = orderGoodsInfoVO.getListedPrice().multiply(new BigDecimal(orderGoodsInfoVO.getPayNum()));
            orderGoodsInfoVO.setGoodsPrice(orderGoodsInfoVO.getListedPrice());

            // 如果是组合商品，则设置组合内商品的orderPrice
            if (orderGoodsInfoVO.getType() == 2 && !orderGoodsInfoVO.getSubOrderGoodsInfoList().isEmpty()) {
                for (OrderGoodsInfoVO goodsInfoVO : orderGoodsInfoVO.getSubOrderGoodsInfoList()) {
                    // 设置组合内商品数量
                    goodsInfoVO.setPayNum(orderGoodsInfoVO.getPayNum());
                    // 设置订单价格
                    goodsInfoVO.setOrderPrice(goodsInfoVO.getListedPrice().multiply(new BigDecimal(orderGoodsInfoVO.getPayNum())));
                    goodsInfoVO.setGoodsPrice(goodsInfoVO.getListedPrice());

                    // 商品ID
                    goodsIds.add(goodsInfoVO.getGoodsId());
                }
            }


            // 计算搭配商品金额
            for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                // 计算搭配金额
                BigDecimal matchingProductPrice = matchingProducts.getActivityPrice().multiply(new BigDecimal(matchingProducts.getPayNum()));
//                log.error("搭配商品金额：" + matchingProductPrice);
                orderPrice = orderPrice.add(matchingProductPrice);
                // 搭配商品的优惠金额 = 挂牌价 - 活动价 * 数量
                matchingProducts.setDiscountedAmoubt(matchingProducts.getListedPrice().subtract(matchingProducts.getActivityPrice()).multiply(new BigDecimal(matchingProducts.getPayNum())));
                // 累加配件优惠金额
                matchingProductDiscounted = matchingProductDiscounted.add(matchingProducts.getDiscountedAmoubt());
                // 商品ID
                goodsIds.add(matchingProducts.getGoodsId());
            }
            // 设置配件优惠金额
            orderGoodsInfoVO.setMatchingProductDiscounted(matchingProductDiscounted);
//            log.error("设置订单金额为" + orderPrice);
            orderGoodsInfoVO.setOrderPrice(orderPrice);
        }

        // 根据商品ID，查询绑定的渠道商品分类
        QueryChannelCategoryGoodsDto qccgDto = new QueryChannelCategoryGoodsDto();
        // 配件分类ID
//        qccgDto.setCategoryId(2578L);
        qccgDto.setGoodsIds(goodsIds);
//        Result<List<Long>> queryChannelCategoryGoodsResponse = cmsClient.queryChannelCategoryGoodsById(qccgDto);
//        if (!queryChannelCategoryGoodsResponse.getSuccess() || null == queryChannelCategoryGoodsResponse.getData()) {
//            throw new BusinessException("获取渠道分类商品信息时失败！");
//        }
//        // 如果数量大于0 ，则包含配件，不允许货到付款
//        if (queryChannelCategoryGoodsResponse.getData().size() > 0) {
//            preOrderOutDto.setIsAvailableCashOnDelivery(false);
//        }
        List<QueryChannelCategoryByGoodsIdsDto> goodsChannelCategoryList = cmsClient.queryChannelCategoryByGoodsByIds(qccgDto).getData();
        if(CollectionUtils.isNotEmpty(goodsChannelCategoryList)){
            Map<Long,QueryChannelCategoryByGoodsIdsDto> channelCategoryMap = goodsChannelCategoryList.stream().collect(Collectors.toMap(QueryChannelCategoryByGoodsIdsDto::getGoodsId, Function.identity(),(key1, key2) -> key2));
            if(channelCategoryMap != null && !channelCategoryMap.isEmpty()){
                for (OrderGoodsInfoVO orderGoodsInfoVO : orderGoodsInfoVOList) {
                    if(channelCategoryMap.get(orderGoodsInfoVO.getGoodsId()) != null){
                        orderGoodsInfoVO.setFirstCategoryId(channelCategoryMap.get(orderGoodsInfoVO.getGoodsId()).getFirstCategoryId());
                        orderGoodsInfoVO.setSecondCategoryId(channelCategoryMap.get(orderGoodsInfoVO.getGoodsId()).getSecondCategoryId());
                    }
                    // 如果是组合商品，则设置组合内商品的orderPrice
                    if (orderGoodsInfoVO.getType() == 2 && !orderGoodsInfoVO.getSubOrderGoodsInfoList().isEmpty()) {
                        for (OrderGoodsInfoVO goodsInfoVO : orderGoodsInfoVO.getSubOrderGoodsInfoList()) {
                            if(channelCategoryMap.get(goodsInfoVO.getGoodsId()) != null){
                                goodsInfoVO.setFirstCategoryId(channelCategoryMap.get(goodsInfoVO.getGoodsId()).getFirstCategoryId());
                                goodsInfoVO.setSecondCategoryId(channelCategoryMap.get(goodsInfoVO.getGoodsId()).getSecondCategoryId());
                            }
                        }
                    }
                    // 计算搭配商品金额
                    for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                        if(channelCategoryMap.get(matchingProducts.getGoodsId()) != null){
                            matchingProducts.setFirstCategoryId(channelCategoryMap.get(matchingProducts.getGoodsId()).getFirstCategoryId());
                            matchingProducts.setSecondCategoryId(channelCategoryMap.get(matchingProducts.getGoodsId()).getSecondCategoryId());
                        }
                    }
                }
            }

//         如果数量大于0 ，则包含配件，不允许货到付款
            boolean flag = goodsChannelCategoryList.parallelStream().anyMatch( t -> t.getSecondCategoryId() != null && Objects.equals(t.getSecondCategoryId(),2578L));
            if (flag) {
                preOrderOutDto.setIsAvailableCashOnDelivery(false);
            }
        }
        

        // 判断是否需要校验库存
        if (isCheckStock) {
            // 校验库存和状态
            for (OrderGoodsInfoVO orderGoodsInfoVO : orderGoodsInfoVOList) {
                if (0 == orderGoodsInfoVO.getOnline()) {
                    throw new BusinessException("商品已下架，请刷新后重新选择");
                }

                if (null == orderGoodsInfoVO.getStock() ||
                        orderGoodsInfoVO.getStock() < orderGoodsInfoVO.getPayNum()) {
                    throw new BusinessException("商品库存不足，请刷新后重新选择");
                }

                // 根据商品类型判断库存
//                if (orderGoodsInfoVO.getType() == 1) {
//                    if (null == orderGoodsInfoVO.getStock() ||
//                            orderGoodsInfoVO.getStock() < orderGoodsInfoVO.getPayNum()) {
//                        throw new BusinessException("商品库存不足，请刷新后重新选择");
//                    }
//                } else if (orderGoodsInfoVO.getType() == 2) {
//                    if (orderGoodsInfoVO.getSubOrderGoodsInfoList().isEmpty()) {
//                        throw new BusinessException("商品库存不足，请刷新后重新选择");
//                    }
//                    for (OrderGoodsInfoVO goodsInfoVO : orderGoodsInfoVO.getSubOrderGoodsInfoList()) {
//                        if (null == goodsInfoVO.getStock() ||
//                                goodsInfoVO.getStock() < orderGoodsInfoVO.getPayNum()) {
//                            throw new BusinessException(goodsInfoVO.getGoodsName() + " " + goodsInfoVO.getSpecifications() + "库存不足");
//                        }
//                    }
//                }

                // 判断搭配商品是否有库存
                for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                    if (new BigDecimal(matchingProducts.getPayNum()).compareTo(new BigDecimal(matchingProducts.getStock())) > 0) {
                        throw new BusinessException(matchingProducts.getGoodsName() + " " + matchingProducts.getSpecifications() + "库存不足");
                    }
                }

            }
        }


        if (orderGoodsInfoVOList.isEmpty()) {
            throw new BusinessException("商品库存不足，请刷新后重新选择");
        }

        return orderGoodsInfoVOList;
    }


    /**
     * 加载预下单信息
     *
     * @param preOrderNo
     * @return
     */
    public PreOrderOutDto loadPreOrder(String preOrderNo) {
        String key = "user_order:" + preOrderNo;
        boolean exists = redisUtil.hasKey(key);
        if (!exists) {
            throw new BusinessException("订单信息已失效，请重新选择商品下单");
        }

        String orderVoString = redisUtil.get(key).toString();
        PreOrderOutDto orderInfoVo = JSONObject.parseObject(orderVoString, PreOrderOutDto.class);
        return orderInfoVo;
    }


    /**
     * 计算优惠金额
     *
     * @param preOrderOutDto
     * @param cardId
     * @return
     */
    public PreOrderOutDto computedOrderPrice(PreOrderOutDto preOrderOutDto, String cardId,BigDecimal cardAmount) {
        // 查询活动信息
        orderUtil.validateActivity(preOrderOutDto);

        // 组合内的商品销售金额按比例分配
        orderUtil.computedGroupSubPrice(preOrderOutDto.getGoodsInfoList());
        // 去除配件商品后的商品列表
        List<OrderGoodsInfoVO> goodsInfoWithoutAccessoryList = preOrderOutDto.getGoodsInfoList().stream().filter(s -> !Objects.equals(s.getFirstCategoryId(), OrderConstant.channelCategoryAccessoryID)).collect(Collectors.toList());
        // 计算去除配件商品后的实付金额
        BigDecimal actualPaymentWithoutAccessoryAmount = goodsInfoWithoutAccessoryList.stream().map(e -> e.getOrderPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (StringUtils.isNotBlank(cardId)) {
            if (!preOrderOutDto.getIsAvailableCoupon()) {
                throw new BusinessException("该订单不支持使用优惠券");
            }

            // 获取优惠券信息
            AvailableCardCodeVO availableCardCodeVO = orderUtil.getAvailableCardCodeByCardId(cardId);
            AvailableCardCodeVO withoutAccessoryAvailableCardCodeVO = new AvailableCardCodeVO();
            BeanUtils.copyProperties(availableCardCodeVO, withoutAccessoryAvailableCardCodeVO);

            //如果优惠券是以旧换新的，替换金额
            if(Objects.equals(OrderConstant.trade_in_coupon,availableCardCodeVO.getExchangeCardId())){
                if(cardAmount == null){
                    throw new BusinessException("参与以旧换新需要输入优惠金额！");
                }
                //todo 需要计算优惠比例是否满足条件
                availableCardCodeVO.setDenomination(cardAmount);
            }

            // 计算订单可优惠金额
            BigDecimal orderAmount = orderUtil.computedOrderAmountPriceByCard(availableCardCodeVO, preOrderOutDto.getGoodsInfoList());

            // 计算订单可优惠金额（去除配件商品）
            BigDecimal withoutAccessoryOrderAmount = orderUtil.computedOrderAmountPriceByCard(availableCardCodeVO, goodsInfoWithoutAccessoryList);

            // 计算优惠金额
            orderUtil.computedDiscountedPrice(orderAmount, availableCardCodeVO);

            // 计算优惠金额（去除配件商品）
            orderUtil.computedDiscountedPrice(withoutAccessoryOrderAmount, withoutAccessoryAvailableCardCodeVO);

            // 设置优惠金额
            preOrderOutDto.setCardNo(availableCardCodeVO.getCardId());
//            log.error("可优惠金额" + JSON.toJSONString(availableCardCodeVO));
            preOrderOutDto.setCouponDeductionAmount(availableCardCodeVO.getCouponsDiscountedPrice());

            // 计算优惠券分摊金额
            if (orderAmount.compareTo(BigDecimal.ZERO) > 0) {
                orderUtil.computedCouponApportionAmount(preOrderOutDto, availableCardCodeVO, orderAmount);
            }


            // 减少优惠金额
            preOrderOutDto.setOrderTotalAmount(preOrderOutDto.getOrderTotalAmount().subtract(availableCardCodeVO.getCouponsDiscountedPrice()));
            preOrderOutDto.setActualPaymentAmount(preOrderOutDto.getActualPaymentAmount().subtract(availableCardCodeVO.getCouponsDiscountedPrice()));
            actualPaymentWithoutAccessoryAmount = actualPaymentWithoutAccessoryAmount.subtract(withoutAccessoryAvailableCardCodeVO.getCouponsDiscountedPrice());

        }

        // 计算成长值
//        preOrderOutDto.setGiveGrowthValue(preOrderOutDto.getActualPaymentAmount().setScale(0, BigDecimal.ROUND_DOWN).longValue());
        preOrderOutDto.setGiveGrowthValue(actualPaymentWithoutAccessoryAmount.setScale(0, BigDecimal.ROUND_DOWN).longValue());


        // 计算会员赠送积分
//        Long memberGivePoints = orderUtil.computedMemberGivePoints(preOrderOutDto.getCustomerId(), preOrderOutDto.getActualPaymentAmount());
        Long memberGivePoints = orderUtil.computedMemberGivePoints(preOrderOutDto.getCustomerId(), actualPaymentWithoutAccessoryAmount);
        preOrderOutDto.setMemberGivePoints(memberGivePoints);


        return preOrderOutDto;
    }


    /**
     * 计算优惠金额
     *
     * @param request
     * @return
     */
    public PreOrderOutDto computedOrderPrice(OrderComputedPriceRequest request) {
        // 获取预下单信息
        PreOrderOutDto orderInfoVo = loadPreOrder(request.getPreOrderNo());
        // 计算优惠金额
        computedOrderPrice(orderInfoVo, request.getCouponId(),request.getCouponAmount());

        // 修改缓存信息
//        orderUtil.savePreOrder(request.getCustomerId(), orderInfoVo, request.getPreOrderNo());
        return orderInfoVo;
    }


    /**
     * 创建订单
     *
     * @param request
     * @return
     */
//    @TxcTransaction
    @GlobalTransactional
    public CreateOrderResponseTO createOrder(CreateOrderDto request, HttpServletRequest httpServletRequest) {
        // 获取预下单信息
        PreOrderOutDto orderInfoVo = loadPreOrder(request.getProOrderNo());

        // 校验支付方式
        if (!orderUtil.checkPayType(request.getPayType(), request.getPayMethod())) {
            throw new BusinessException("暂不支持该支付方式，请刷新页面或者联系管理员");
        }

        // 判断是否支持货到付款
        if (!orderInfoVo.getIsAvailableCashOnDelivery() && 2 == request.getPayType()) {
            throw new BusinessException("该订单不支持货到付款支付！");
        }
        // 判断是否支持银行转账
        if (!orderInfoVo.getIsAvailableBankTransfer() && 3 == request.getPayType()) {
            throw new BusinessException("该订单不支持银行转账支付！");
        }

        // 校验地址信息
        orderUtil.checkAddress(request);

        // 校验发票
        CustomerInvoice customerInvoice = orderUtil.checkInvoice(request);

        // 商品编码
        Set<String> goodsCodes = new HashSet<>();
        List<Long> goodsIds = new ArrayList<>();
        List<String> matchingGoodsCodes = new ArrayList<>();
        for (OrderGoodsInfoVO orderGoodsInfoVO : orderInfoVo.getGoodsInfoList()) {
            CheckGoodsStatusRequestDto dto = new CheckGoodsStatusRequestDto();
            goodsCodes.add(orderGoodsInfoVO.getSeriesGoodsCode());
            // 如果是组合商品
            if (orderGoodsInfoVO.getType() == 1) {
                // 系列规格
                goodsCodes.add(orderGoodsInfoVO.getSeriesGoodsCode());
                goodsIds.add(orderGoodsInfoVO.getGoodsId());
                dto.setGoodsId(orderGoodsInfoVO.getGoodsId());
            } else if (orderGoodsInfoVO.getType() == 2) {
                goodsIds.add(orderGoodsInfoVO.getGoodsId());
                // 如果是组合商品
                if (orderGoodsInfoVO.getType() == 2) {
                    for (OrderGoodsInfoVO goodsInfoVO : orderGoodsInfoVO.getSubOrderGoodsInfoList()) {
                        goodsCodes.add(goodsInfoVO.getSeriesGoodsCode());
                        goodsIds.add(goodsInfoVO.getGoodsId());
                    }
                }
            }
            // 搭配商品
            for (OrderGoodsInfoVO.MatchingProducts matchingProducts : orderGoodsInfoVO.getMatchingProductsList()) {
                matchingGoodsCodes.add(matchingProducts.getSeriesGoodsCode());
                goodsIds.add(matchingProducts.getGoodsId());
            }
            //判断率净水滤芯的配送方式是否正确
            if(orderGoodsInfoVO.getSplitOrderGroupType()!=null && orderGoodsInfoVO.getSplitOrderGroupType().equals(1)
            && !Integer.valueOf(2).equals(request.getDeliveryMethod())){
                throw new BusinessException("选择的商品是净水滤芯，收货方式请选择技师带货");
            }
        }
        // 商品code去重
//        orderInfoVo.getGoodsInfoList().stream().map(OrderGoodsInfoVO::getSeriesGoodsCode).distinct().collect(Collectors.toList());


        // 校验商品上下架状态
//        orderUtil.checkChannelGoodsStatus(goodsIds, 21l);

        //校验配件商品是否已购买超过10件，超过10件提示错误
        List<BuyNumDto> buyNumList = goodsOrderItemDao.getAccessoryBuyNum(request.getCustomerId(),goodsCodes,2566L);
        Map<String,Integer> buyNumMap = null;
        if(CollectionUtils.isNotEmpty(buyNumList)){
            buyNumMap = buyNumList.stream().collect(Collectors.toMap(BuyNumDto::getGoodsCode,BuyNumDto::getBuyNum));
        }
        if(buyNumMap != null && !buyNumMap.isEmpty()){
            for (OrderGoodsInfoVO orderGoodsInfoVO : orderInfoVo.getGoodsInfoList()) {
                if (orderGoodsInfoVO.getType() == 1 || orderGoodsInfoVO.getType() == 3) {
                    Integer buyNum = buyNumMap.get(orderGoodsInfoVO.getSeriesGoodsCode());
                    if(buyNum != null){
                        int limitNum = buyNum + orderGoodsInfoVO.getPayNum();
                        if(limitNum > 10){
                            throw new BusinessException("同一配件当天购买不能超过10个。");
                        }
                    }
                } else if (orderGoodsInfoVO.getType() == 2) {
                    for (OrderGoodsInfoVO goodsInfoVO : orderGoodsInfoVO.getSubOrderGoodsInfoList()) {
                        Integer buyNum = buyNumMap.get(goodsInfoVO.getSeriesGoodsCode());
                        if(buyNum != null){
                            int limitNum = buyNum + goodsInfoVO.getPayNum();
                            if(limitNum > 10){
                                throw new BusinessException("同一配件当天购买不能超过10个。");
                            }
                        }
                    }
                }
            }
        }
        
        // 校验商品库存
        orderUtil.checkGoodsStock(orderInfoVo.getGoodsInfoList());
//        List<ChannelGoodsMerchandising> channelGoodsMerchandisings = orderUtil.getGoodsMerchandisings(goodsCodes);


        // 校验购买商品信息
        List<OrderGoodsInfoVO> orderGoodsInfoVOList = validatePreOrderRequest(orderInfoVo);

//        preOrderOutDto.setGoodsInfoList(orderGoodsInfoVOList);


        // 校验收货地址
        UserAddressFromMall userAddress = null;
        if (null != request.getAddressId()) {
            userAddress = orderUtil.checkUserAddressById(request.getAddressId(), request.getCustomerId());
        } else {
            userAddress = new UserAddressFromMall();
            userAddress.setProvinceId(request.getProvinceId());
            userAddress.setProvinceName(request.getProvinceName());
            userAddress.setCityId(request.getCityId());
            userAddress.setCityName(request.getCityName());
            userAddress.setCountyId(request.getCountyId());
            userAddress.setCountyName(request.getCountyName());
            userAddress.setAddress(request.getAddress());
            userAddress.setName(request.getAddressName());
            userAddress.setPhone(request.getAddressPhone());
        }

        // 禁售区域校验
        orderUtil.checkAddressSalesArea(userAddress, orderInfoVo);


        // 活动商品校验
//        orderUtil.validateActivity(orderInfoVo);

        // 计算订单价格
        computedOrderPrice(orderInfoVo, request.getCouponId(), request.getCouponAmount());

        // 计算商品行分摊金额
//        orderUtil.computedGroupSubPrice(orderInfoVo.getGoodsInfoList());

        //以旧换新校验限价
        if(Objects.equals(request.getPayMethod(),OrderConstant.PayMethod.CLOUD_QUICK_PAY.getCode())){
            if(orderInfoVo.getGoodsInfoList().size() == 1 && orderInfoVo.getGoodsInfoList().get(0).getMinPrice() != null){
                if(orderInfoVo.getActualPaymentAmount().compareTo(orderInfoVo.getGoodsInfoList().get(0).getMinPrice()) < 0){
                    throw new BusinessException("超过抵扣码使用额度，请重新输入！");
                }
            }
        }


        // 生成订单号
        String orderNo = orderUtil.generateOrderNo();

        // 入库操作开始
        try {
            // 优惠券核销
            if (StringUtils.isNotBlank(request.getCouponId())) {
                orderUtil.consumeCardCode(request.getCouponId(),orderNo);
            }

            // 订单渠道库存删减
            orderUtil.channelDeductionStock(orderInfoVo.getGoodsInfoList());

            // 订单活动库存删减
            orderUtil.activityDeductionStock(orderInfoVo.getGoodsInfoList(),true);

            // 清空购物车数据
            orderUtil.deleteCart(orderInfoVo);


            // 添加发票信息
            if (null != customerInvoice) {
                orderUtil.createInvoice(customerInvoice, orderNo);
            }
            // set门店信息
            this.setStoreInfo(request.getPayType(),orderInfoVo);

            // 获取IP
            if(StringUtils.isBlank(request.getIpAddress())){
                String ip = IpUtils.getIpAddress(httpServletRequest);
                orderInfoVo.setIp(ip);
            } else {
                orderInfoVo.setIp(request.getIpAddress());
            }

            // 创建订单
            orderUtil.createOrder(orderInfoVo, request, userAddress, orderNo);

            // 添加促销信息
            orderUtil.createOrderPromotion(orderInfoVo, orderNo);

            // 创建操作日志
            addMobileLog(orderNo, orderInfoVo.getCustomerName(), orderInfoVo.getCustomerId(), "订单提交", "订单提交", null);

            // 判断是否货到付款
            if (request.getPayType().intValue() == 2) {
                //是否累加抽奖次数
                Result<MallActivity> mallActivityResult = marketingClient.getLotteryActivityById();
                if (!mallActivityResult.getSuccess()) {
                    throw new BusinessException(mallActivityResult.getMsg());
                }
                if (null != mallActivityResult.getData()) {
                    MallActivity lotteryActivity = mallActivityResult.getData();
                    CustomerMallActivityInfoDto customerMallActivityInfoDto = new CustomerMallActivityInfoDto();
                    customerMallActivityInfoDto.setActivityId(lotteryActivity.getId());
                    customerMallActivityInfoDto.setActivityName(lotteryActivity.getActivityTitle());
                    customerMallActivityInfoDto.setCustomerId(orderInfoVo.getCustomerId());
                    customerMallActivityInfoDto.setEachLotteryDeductIntegral(lotteryActivity.getEachLotteryDeductIntegral());
                    customerMallActivityInfoDto.setLotteryCount(lotteryActivity.getGiveLotteryNum());
                    customerMallActivityInfoDto.setIntegralFreeLotteryNum(lotteryActivity.getIntegralFreeLotteryNum());
                    Result result = customerClient.editLotteryNum(customerMallActivityInfoDto);
                    if(!result.getSuccess()){
                        throw new BusinessException(result.getMsg());
                    }
                }
            }


            // 删除缓存订单
            orderUtil.delPreOrder(request.getProOrderNo());

            CreateOrderResponseTO response = new CreateOrderResponseTO();
            response.setOrderNo(orderNo);
            response.setPayAmount(orderInfoVo.getActualPaymentAmount());
            return response;
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("系统异常");
        }
    }

    /**
     * set门店信息
     * @param payType
     * @param orderInfoVo
     */
    public void setStoreInfo(Long payType,PreOrderOutDto orderInfoVo){
        if (null != payType) {
            Long storeId = 0L;
            String storeName = "";
            if (payType == 1 || payType == 3) {
                // 设置门店
                //判断订单是否包含净水滤芯
                boolean containsOne = orderInfoVo.getGoodsInfoList().stream().anyMatch(o->o.getSplitOrderGroupType() == 1);
                if (containsOne && payType == 1){
                    storeId = StoreInfoEnum.E_JCLX_MALL_STORE.getStoreId();
//                    storeName = StoreInfoEnum.E_JCLX_MALL_STORE.getStoreName();
                }else {
                    storeId = StoreInfoEnum.E_ONLINE_PAYMENT_STORE.getStoreId();
//                    storeName = StoreInfoEnum.E_ONLINE_PAYMENT_STORE.getStoreName();
                }
            } else if (payType == 2) {
                // 货到付款
                storeId = StoreInfoEnum.E_CASH_ON_DELIVERY_STORE.getStoreId();
//                storeName = StoreInfoEnum.E_CASH_ON_DELIVERY_STORE.getStoreName();
            }
            if(storeId != null){
                Result<StoreSingleSimpleInfoDTO> result = orgClient.findByStoreCode(storeId + "");
                if(result != null && result.getSuccess() && result.getData() != null){
                    storeName = result.getData().getName();
                }
            }
            orderInfoVo.setStoreId(storeId);
            orderInfoVo.setStoreName(storeName);
        }
    }

    @GlobalTransactional
    public CreateOrderResponseTO createOrderInternal(CreateOrderDto request) {

        // 获取预下单信息
        PreOrderOutDto orderInfoVo = loadPreOrder(request.getProOrderNo());
        orderInfoVo.setActivityId(request.getActivityId());
        //查看活动
        Result<MallActivity> mallActivityById = marketingClient.getMallActivityById2(request.getActivityId());

        if (mallActivityById == null || !mallActivityById.getSuccess() || mallActivityById == null) {
            throw new BusinessException("活动不存在");
        }
        MallActivity mallActivity = mallActivityById.getData();

//        Long customerId = request.getCustomerId();

        Date startTime = StringUtils.isNotBlank(mallActivity.getActivityStartTime()) ? DateUtil.parseDateTime(mallActivity.getActivityStartTime()) : null;
        Date endTime = StringUtils.isNotBlank(mallActivity.getActivityEndTime()) ? DateUtil.parseDateTime(mallActivity.getActivityEndTime()) : null;

        long nowTime = System.currentTimeMillis();

        if (mallActivity.getActivityStatus() == 2L) {
            if ((startTime != null && startTime.getTime() > nowTime)

            ) {

                throw new BusinessException("活动未开始");
               
            } else if (endTime != null && endTime.getTime() < nowTime) {
                throw new BusinessException("活动已结束");

            }
        } else {
            throw new BusinessException("活动未上架");
        }

        log.error("下单活动开始时间：{}",startTime);
        log.error("下单活动当前时间：{}",nowTime);
        log.error("下单活动信息：{}",JSON.toJSONString(mallActivity));


        // todo 设置待支付取消时间 分钟（小时 * 60 + 分钟）
        Long unpaidOrderCancellationMinutes = mallActivity.getUnpaidOrderCancellationTime() * 60 + mallActivity.getUnpaidOrderCancellationMinutes();
//        Long unpaidOrderCancellationMinutes = 2L;
        orderInfoVo.setUnpaidOrderCancellationMinutes(unpaidOrderCancellationMinutes);
        // 活动类型
        request.setActivityType(mallActivity.getActivityType());
        request.setActivityName(mallActivity.getActivityTitle());
        // 校验购买商品信息
        //校验商品是否能够购买
        List<OrderGoodsInfoVO> goodsInfoList = orderInfoVo.getGoodsInfoList();
        if (CollectionUtils.isEmpty(goodsInfoList)) {
            throw new BusinessException("商品不能为空");
        }
        // 商品编码
        List<String> goodsCodes = goodsInfoList.stream().map(OrderGoodsInfoVO::getGoodsCode).distinct().collect(Collectors.toList());
        Result<List<GetOrderGoodsInfoByCodesDTO>> orderGoodsInfoByCodesR = productClient.getOrderGoodsInfoByCodes(goodsCodes);
        if (orderGoodsInfoByCodesR == null || !orderGoodsInfoByCodesR.getSuccess() || orderGoodsInfoByCodesR.getData() == null) {
            throw new BusinessException("商品不存在");

        }
        List<GetOrderGoodsInfoByCodesDTO> orderGoodsInfoByCodes = orderGoodsInfoByCodesR.getData();
        MallPurchaseQualificationsAudit data = null;
        Result<MallPurchaseQualificationsAudit> mallOurchQualificationsByJobId = null;
        if (request.getActivityType() != null) {
            if (request.getActivityType().equals(8L)) {
                data = orderUtil.getMallOurchQualificationsByJobId(request.getJobId());

                //下面这一段是判断商品互斥关系的

                JobOrderRecordListDTO jobOrderRecordListDTO = new JobOrderRecordListDTO();
                jobOrderRecordListDTO.setJobId(request.getJobId());
                jobOrderRecordListDTO.setActivityType(orderInfoVo.getActivityType());
                jobOrderRecordListDTO.setActivityId(orderInfoVo.getActivityId());
                JobOrderRecordListVO list = jobOrderRecordService.listInternal(jobOrderRecordListDTO);

                jobOrderRecordService.judgeGoodsRejectRelation(jobOrderRecordListDTO,orderGoodsInfoByCodes,mallActivity.getCategoryLimitNum());

                for (GetOrderGoodsInfoByCodesDTO orderGoodsInfoVO : orderGoodsInfoByCodes) {
                    OrderGoodsInfoVO orderGoodsInfoVOStream = goodsInfoList.stream().filter(goodInfo -> goodInfo.getGoodsCode().equals(orderGoodsInfoVO.getGoodsCode())).findFirst().orElse(null);
                    if (orderGoodsInfoVOStream != null) {
                        orderGoodsInfoVOStream.setGoodsLineCode(orderGoodsInfoVO.getGoodsLineCode());
                        orderGoodsInfoVOStream.setGoodsLineName(orderGoodsInfoVO.getGoodsLineName());
                        orderGoodsInfoVOStream.setGoodsGroupName(orderGoodsInfoVO.getGoodsGroupName());
                        orderGoodsInfoVOStream.setGoodsGroupCode(orderGoodsInfoVO.getGoodsGroupCode());
                        orderGoodsInfoVOStream.setProductSmallCategoryCode(orderGoodsInfoVO.getProductSmallCategoryCode());
                        orderGoodsInfoVOStream.setProductSmallCategoryName(orderGoodsInfoVO.getProductSmallCategoryName());
                    }
                }


/*
                //记录当前不可买上票
                List<String> buyProdct = new ArrayList<>();

                if(list != null && CollectionUtils.isNotEmpty(list.getHaveBuyProductGroupBO())){
                    List<String> collect = list.getHaveBuyProductGroupBO().stream().map(t -> t.getProductRejectionGroupCode()).collect(Collectors.toList());
                    buyProdct.addAll(collect);
                }

                if (!CollectionUtils.isEmpty(list.getNotBuyProductGroupBO())) {
                    Map<String, List<GetOrderGoodsInfoByCodesDTO>> collect = orderGoodsInfoByCodes.stream().collect(Collectors.groupingBy(GetOrderGoodsInfoByCodesDTO::getGoodsGroupCode));

                    orderGoodsInfoByCodes.stream().forEach(goodsItem -> {
                        if (buyProdct.contains(goodsItem.getGoodsGroupCode())) {
                            throw new BusinessException("产品型号" + goodsItem.getGoodsName() + "您已经购买过该产品组同类商品，不能再次购买");
                        }
                        List<JobOrderRecordBuyProductGroupBO> jobOrderRecordBuyProductGroupBOs = list.getNotBuyProductGroupBO().stream().filter(product -> StringUtils.isNotEmpty(goodsItem.getGoodsGroupCode()) && goodsItem.getGoodsGroupCode().equals(product.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(jobOrderRecordBuyProductGroupBOs)) {
                            throw new BusinessException("产品型号" + goodsItem.getGoodsName() + "您已经购买过该产品组同类商品，不能再次购买");
                        }
                        //判断是否超出购买数量
                        if (null != collect &&  null != collect.get(goodsItem.getGoodsGroupCode()) && collect.get(goodsItem.getGoodsGroupCode()).size() > mallActivity.getCategoryLimitNum()) {
                            throw new BusinessException("产品型号" + goodsItem.getGoodsName() + "已超出本次活动购买上限，请换件产品或减少同品类数量");
                        }
                        //
//                        buyProdct.add(goodsItem.getGoodsGroupCode());
//                        jobOrderRecordBuyProductGroupBOs.stream().forEach(jobOrderRecordBuyProductGroupBO -> {
//                            buyProdct.add(jobOrderRecordBuyProductGroupBO.getProductRejectionGroupCode());
//                        });
                    });


                } else {
                    throw new BusinessException("暂无可购买商品");

                }
*/
            } else if (request.getActivityType().equals(7L)) {
                JobOrderRecordListDTO jobOrderRecordListDTO = new JobOrderRecordListDTO();
                jobOrderRecordListDTO.setJobId(request.getJobId());
                jobOrderRecordListDTO.setActivityType(mallActivity.getActivityType().intValue());
                jobOrderRecordListDTO.setActivityId(mallActivity.getId());
                List<JobOrderRecordEntity> list = jobOrderRecordService.haveBuyProductGroup(jobOrderRecordListDTO);

                List<Long> jobOrderGoodIds = list.stream().map(JobOrderRecordEntity::getGoodsId).distinct().collect(Collectors.toList());
                List<GetOrderGoodsInfoByCodesDTO> orderGoodsInfoByIdsData = new ArrayList<>();
                Map<Long, String> goodsPlMap = new HashMap<>();
                if(!CollectionUtils.isEmpty(jobOrderGoodIds)){
                    // 查询商品品类
                    Result<List<GetOrderGoodsInfoByCodesDTO>> orderGoodsInfoByIdsR = productClient.getOrderGoodsInfoByIds(jobOrderGoodIds);
                    if (orderGoodsInfoByIdsR == null || !orderGoodsInfoByIdsR.getSuccess() || orderGoodsInfoByIdsR.getData() == null) {
                        throw new BusinessException("商品不存在");
                    }
                    orderGoodsInfoByIdsData = orderGoodsInfoByIdsR.getData();
                    goodsPlMap = orderGoodsInfoByIdsData.stream()
                            .collect(Collectors.toMap(GetOrderGoodsInfoByCodesDTO::getId, GetOrderGoodsInfoByCodesDTO::getGoodsPlCode));
                }
                Long unpaidOrderCancellationTime = mallActivity.getCategoryLimitNum() == null ? 1 : mallActivity.getCategoryLimitNum();
                Map<String, Integer> map = new ConcurrentHashMap<>();
                if (!CollectionUtils.isEmpty(list)) {
                    //计算已买品类数量
                    for (JobOrderRecordEntity jobOrderRecordEntity : list) {
                        if (StringUtils.isNotBlank(jobOrderRecordEntity.getGoodsLineCode())) {
                            if(goodsPlMap.containsKey(jobOrderRecordEntity.getGoodsId())){
                                //产品品类
                                String goodsPl = goodsPlMap.get(jobOrderRecordEntity.getGoodsId());
                                map.put(goodsPl,
                                        map.get(goodsPl) == null ? jobOrderRecordEntity.getPayNum() : map.get(goodsPl) + jobOrderRecordEntity.getPayNum());
                            }

                        }
                    }

                }
                StringBuffer stringBuffer = new StringBuffer();
                //判断是否超出购买数量
                for (GetOrderGoodsInfoByCodesDTO orderGoodsInfoVO : orderGoodsInfoByCodes) {
                    OrderGoodsInfoVO orderGoodsInfoVOStream = goodsInfoList.stream().filter(goodInfo -> goodInfo.getGoodsCode().equals(orderGoodsInfoVO.getGoodsCode())).findAny().orElse(null);
                    if (!StringUtils.isEmpty(orderGoodsInfoVO.getGoodsPlCode())) {
                        Integer existingcount = map.get(orderGoodsInfoVO.getGoodsPlCode()) == null ? 0 : map.get(orderGoodsInfoVO.getGoodsPlCode());
                        Integer count = existingcount;
                        if (orderGoodsInfoVOStream != null) {
                            count = count + orderGoodsInfoVOStream.getPayNum();
                        }
                        orderGoodsInfoVOStream.setGoodsLineCode(orderGoodsInfoVO.getGoodsLineCode());
                        orderGoodsInfoVOStream.setGoodsLineName(orderGoodsInfoVO.getGoodsLineName());
                        orderGoodsInfoVOStream.setGoodsGroupName(orderGoodsInfoVO.getGoodsGroupName());
                        orderGoodsInfoVOStream.setGoodsGroupCode(orderGoodsInfoVO.getGoodsGroupCode());
                        orderGoodsInfoVOStream.setProductSmallCategoryCode(orderGoodsInfoVO.getProductSmallCategoryCode());
                        orderGoodsInfoVOStream.setProductSmallCategoryName(orderGoodsInfoVO.getProductSmallCategoryName());

                        if (count > unpaidOrderCancellationTime) {
                            stringBuffer.append("&" + orderGoodsInfoVO.getGoodsName() + "&");
//                            throw new BusinessException("产品线:" + orderGoodsInfoVO.getGoodsLineName() + "购买已达上限");
                        }
                        map.put(orderGoodsInfoVO.getGoodsLineCode(), map.get(orderGoodsInfoVO.getGoodsLineCode()) == null ? orderGoodsInfoVOStream.getPayNum() : map.get(orderGoodsInfoVO.getGoodsLineCode()) + orderGoodsInfoVOStream.getPayNum());
                    } else {
                        throw new BusinessException("暂无可购买商品");
                    }
                }
                if(stringBuffer.length() > 0){
                    StringBuffer sb = new StringBuffer();
                    sb.append("以下商品已达本场活动购买上限，请选购其他产品线！\n");
                    sb.append(stringBuffer);
                    throw new BusinessException(sb.toString());
                }
                //家人购活动校验收获地址
                orderUtil.checkAddress(request);
                //家人购活动校验安装地址
                orderUtil.checkInstallAddress(request);

            } else {

            }
        } else {
            throw new BusinessException("活动类型不能为空");
        }
        // 校验支付方式
        if (!orderUtil.checkPayType(request.getPayType(), request.getPayMethod())) {
            throw new BusinessException("暂不支持该支付方式，请刷新页面或者联系管理员");
        }

        // 判断是否支持货到付款
        if (!orderInfoVo.getIsAvailableCashOnDelivery() && 2 == request.getPayType()) {
            throw new BusinessException("该订单不支持货到付款支付！");
        }
        // 判断是否支持银行转账
        if (!orderInfoVo.getIsAvailableBankTransfer() && 3 == request.getPayType()) {
            throw new BusinessException("该订单不支持银行转账支付！");
        }

        // 校验地址信息
        orderUtil.checkAddress(request);

        // 校验发票
        CustomerInvoice customerInvoice = orderUtil.checkInvoice(request);


        // 校验商品库存
//        List<ChannelGoodsMerchandising> channelGoodsMerchandisings = orderUtil.getGoodsMerchandisingsInternal(goodsCodes);

        // 家人购库存校验
        orderUtil.againValidateActivityInternal(orderInfoVo);


        // 校验收货地址
        UserAddressFromMall userAddress = new UserAddressFromMall();
        if (request.getActivityType() == 8L && StringUtils.isNotEmpty(request.getJobId())) {
            //如果无数据则表示没有审核通过

            userAddress.setAddress(data.getAddress());
            userAddress.setProvinceId(data.getProvinceId());
            userAddress.setProvinceName(data.getProvinceName());
            userAddress.setCityId(data.getCityId());
            userAddress.setCityName(data.getCityName());
            userAddress.setCountyId(data.getAreaId());
            userAddress.setCountyName(data.getAreaName());
            userAddress.setName(data.getConsigneeName());
            userAddress.setPhone(String.valueOf(data.getConsigneePhone()));

        } else {
            if (null != request.getAddressId()) {
                userAddress = orderUtil.checkUserAddressById(request.getAddressId(), request.getCustomerId());
            } else {
                userAddress = new UserAddressFromMall();
                userAddress.setProvinceId(request.getProvinceId());
                userAddress.setProvinceName(request.getProvinceName());
                userAddress.setCityId(request.getCityId());
                userAddress.setCityName(request.getCityName());
                userAddress.setCountyId(request.getCountyId());
                userAddress.setCountyName(request.getCountyName());
                userAddress.setAddress(request.getAddress());
                userAddress.setName(request.getAddressName());
                userAddress.setPhone(request.getAddressPhone());
            }
        }
        UserInstallAddress userInstallAddress = null;
        if (null != request.getInstallAddressId()) {
            userInstallAddress = orderUtil.checkUserInstallAddressById(request.getInstallAddressId(), request.getCustomerId());
        } else {
            userInstallAddress = new UserInstallAddress();
            userInstallAddress.setProvinceId(request.getInstallProvinceId());
            userInstallAddress.setProvinceName(request.getInstallProvinceName());
            userInstallAddress.setCityId(request.getInstallCityId());
            userInstallAddress.setCityName(request.getInstallCityName());
            userInstallAddress.setCountyId(request.getInstallCountyId());
            userInstallAddress.setCountyName(request.getInstallCountyName());
            userInstallAddress.setAddress(request.getInstallAddress());
            userInstallAddress.setName(request.getInstallAddressName());
            userInstallAddress.setPhone(request.getInstallAddressPhone());
        }

        // 禁售区域校验
        orderUtil.checkAddressSalesArea(userAddress, orderInfoVo);
        UserAddressFromMall userInstallAddressCheck = new UserAddressFromMall();
        BeanUtils.copyProperties(userInstallAddress,userInstallAddressCheck);
//        orderUtil.checkAddressSalesArea(userInstallAddressCheck, orderInfoVo);

//        // 活动商品校验
//        orderUtil.validateActivity(orderInfoVo);

        // 根据商品ID查询商品活动信息
        List<OrderGoodsInfoVO> orderGoodsInfoVOS = orderUtil.validateActivityInternal(orderInfoVo);
        if (!CollectionUtils.isEmpty(orderGoodsInfoVOS)) {
            CreateOrderResponseTO cancelOrderResponseTO = new CreateOrderResponseTO();
            cancelOrderResponseTO.setFailureOrderGoodsInfoVOList(orderGoodsInfoVOS);
            return cancelOrderResponseTO;
        }


        // 计算商品总金额
        BigDecimal totalPrice = orderInfoVo.getGoodsInfoList().stream().map(e -> e.getOrderPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderInfoVo.setGoodsTotalAmount(totalPrice);

        // 订单总金额
        orderInfoVo.setActualPaymentAmount(totalPrice);
//
        // 生成订单号
        String orderNo = orderUtil.generateOrderNo();
//        String orderNo = orderUtil.getOrderNum(orderInfoVo.getCustomerId());

        // 入库操作开始
        try {
            // 优惠券核销
            if (StringUtils.isNotBlank(orderInfoVo.getCardId()) && StringUtils.isNotBlank(orderInfoVo.getCardNo())) {
                orderUtil.consumeCardCode(orderInfoVo.getCardNo(),orderNo);
            }

            System.out.println(JSON.toJSONString(orderInfoVo.getGoodsInfoList()));

            // 只有员工内购、展厅才扣除渠道库存
            if (mallActivity.getActivityType().longValue() == 8 || mallActivity.getActivityType().longValue() == 9) {
                // 订单库存删减
                orderUtil.channelDeductionStock(orderInfoVo.getGoodsInfoList());
            }


            // 活动库存删减
            orderUtil.activityDeductionStock(orderInfoVo.getGoodsInfoList(), mallActivity.getId(), mallActivity.getActivityTitle());

            // 清空购物车数据
            orderUtil.deleteCart(orderInfoVo);


            // 添加发票信息
            if (null != customerInvoice) {
                orderUtil.createInvoice(customerInvoice, orderNo);
            }

            // 根据类型设置门店
            if (mallActivity.getActivityType() == 7) {
                // 家人购
                orderInfoVo.setStoreId(152957L);
                orderInfoVo.setStoreName("方太等级机内购");

                // 强制扣除积分
                orderInfoVo.setGiveAwayPoints(0L);
                orderInfoVo.setMemberGivePoints(0L);
            } else if (mallActivity.getActivityType() == 8) {
                orderInfoVo.setStoreId(152956L);
                orderInfoVo.setStoreName("八年员工内购");

                // 强制扣除积分
                orderInfoVo.setGiveAwayPoints(0L);
                orderInfoVo.setMemberGivePoints(0L);
            } else if (mallActivity.getActivityType() == 9) {
                orderInfoVo.setStoreId(145878L);
                orderInfoVo.setStoreName("方太E商城");
            }

            // 创建订单
            orderUtil.createFamilyOrder(orderInfoVo, request, userAddress, userInstallAddress, orderNo);

            // 添加促销信息
            orderUtil.createOrderPromotion(orderInfoVo, orderNo);

            // 创建操作日志
            addMobileLog(orderNo, orderInfoVo.getCustomerName(), orderInfoVo.getCustomerId(), "订单提交", "订单提交", null);

            // 删除缓存订单
            orderUtil.delPreOrder(request.getProOrderNo());

            CreateOrderResponseTO response = new CreateOrderResponseTO();
            response.setOrderNo(orderNo);
            response.setPayAmount(orderInfoVo.getActualPaymentAmount());
            return response;
        } catch (BusinessException e) {
            log.error(String.format("原因:%s,错误信息:%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("系统异常");
        }
    }

    @Autowired
    JobOrderRecordMapper jobOrderRecordMapper;

    @GlobalTransactional
    public String operationOrder(OperationOrderInDto operationOrderInDto) {
        // 查询订单
        OrderRecord order = orderDao.findOrderRecordByOrderNo(operationOrderInDto.getOrderNo());
        UpdateWrapper<OrderRecord> wrapper = new UpdateWrapper<>();
        if (operationOrderInDto.getStatus() == 0) {

            if (order.getPayType() == 2 && order.getSyncStatus() == 2){
                throw new BusinessException("该订单不可取消,请联系客服");
            }
            if (Objects.equals(order.getPayType(),OrderConstant.PaymentTypeEnum.ONLINE_PAYMENT.getCode())
                    && !Objects.equals(order.getPayStatus(),OrderConstant.PayStatusEnum.NOT_PAY.getCode())){
                throw new BusinessException("该订单不可取消,请联系客服");
            }
            if(Objects.equals(order.getOrderStatus(),OrderConstant.OrderStatusEnum.CANCELED.getCode())){
                throw new BusinessException("该订单已取消,请勿重复操作！");
            }
            wrapper.set("order_status", operationOrderInDto.getStatus());
            wrapper.set("customer_order_status", operationOrderInDto.getStatus());
            wrapper.set("customer_name", "");
            wrapper.set("id_card", "");
            wrapper.set("id_card_portrait_url", "");
            wrapper.set("id_card_emblem_url", "");
            String orderNo = operationOrderInDto.getOrderNo();

            //检查支付流水是否已经存在支付成功的记录，如果有，不允许取消订单
            boolean flag = this.checkPaySuccessTrade(orderNo);
            if(flag){
                throw new BusinessException("该订单已存在支付成功的交易流水，请联系客服取消订单！");
            }
            // 取消订单方法
            orderUtil.cancelOrder(orderNo, 1L, true);

        } else if (operationOrderInDto.getStatus() == 9) {
//            wrapper.set("is_deleted", operationOrderInDto.getUserId());
            wrapper.set("customer_order_status", 8);
        } else if (operationOrderInDto.getStatus() == 1) {    //退货状态 (0:申请退货中,1:退货中,2:已退货,3:申请换货中,4:换货中,5:已换货)'
            wrapper.set("after_sale_status", 0);
            insertSaleInfo(operationOrderInDto);
        } else if (operationOrderInDto.getStatus() == 2) {
            wrapper.set("after_sale_status", 3);
            insertSaleInfo(operationOrderInDto);
        } else if (operationOrderInDto.getStatus() == 3) {  //取消申请
            wrapper.set("after_sale_status", 6);
            cancelAfterSale(operationOrderInDto.getOrderNo());
        } else if (operationOrderInDto.getStatus() == 4) {  //确认收货
            // 校验订单状态是否是已发货状态
            if (order.getOrderStatus() != OrderConstant.OrderStatusEnum.SHIPPED.getCode()) {
                throw new BusinessException("不合法操作!");
            }
            wrapper.set("order_status", 5);
            wrapper.set("customer_order_status", 6);
            wrapper.set("confirm_receipt_time", new Date());
            wrapper.set("is_refundable_exchangeable", OrderConstant.APPLY_REFUND_EXCHANGE);

            this.editGoodsOrderItem(operationOrderInDto);
            // 获取订单信息
            OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(operationOrderInDto.getOrderNo());
            // 添加日志
            OrderOperationLog orderOperationLog = new OrderOperationLog();
            orderOperationLog.setOrderNo(operationOrderInDto.getOrderNo());
            orderOperationLog.setOperationByName(orderInfoInDto.getBuyUserName());
            orderOperationLog.setCreatedByName(orderInfoInDto.getBuyUserName());
            orderOperationLog.setOperationType("订单收货");
            orderOperationLog.setOperationTime(new Date());
            orderOperationLog.setDescription("订单确认收货");
            if (operationOrderInDto.getIsXXLJob() == 1L){
                orderOperationLog.setDescription("系统自动确认收货");
                orderOperationLog.setOperationByName("系统账号");
                orderOperationLog.setCreatedByName("系统账号");
            }

            orderOperationLog.setIsDeleted(0L);
            orderOperationLog.setCreatedBy(orderInfoInDto.getBuyUserId().toString());
            orderOperationLog.setCreatedDate(new Date());
            orderOperationLogDao.insert(orderOperationLog);
            // 添加到缓存
            redisUtil.lPush(TaskConstants.ORDER_TASK_AUTOMATIC_REVIEW_ORDER, operationOrderInDto.getOrderNo());
        } else if (operationOrderInDto.getStatus() == 5) { //员工内购家人内购确认收货
            // 校验订单状态是否是已发货状态
            if (order.getOrderStatus() != OrderConstant.OrderStatusEnum.SHIPPED.getCode()) {
                throw new BusinessException("不合法操作!");
            }
            wrapper.set("order_status", 6);
            wrapper.set("customer_order_status", 7);
            Date now = new Date();
            wrapper.set("completion_time" , now);
            wrapper.set("confirm_receipt_time" , now);
            // 更新订单明细状态
            UpdateWrapper<GoodsOrderItem> itemUpdate = new UpdateWrapper<>();
            itemUpdate.set("order_item_status", 6);
            itemUpdate.eq("order_no", operationOrderInDto.getOrderNo());
            goodsOrderItemDao.update(null, itemUpdate);
        } else {
            return "状态有误,请核实！";
        }
//        wrapper.eq("buy_user_id", operationOrderInDto.getUserId());
        wrapper.eq("order_no", operationOrderInDto.getOrderNo());
        orderDao.update(null, wrapper);
//            //根据订单号获取订单信息
//            QueryWrapper<OrderRecord> queryWrapper = new QueryWrapper<OrderRecord>().eq("is_deleted", 0).eq("after_sale_status", 0).last("limit 1");
//            OrderRecord orderRecord = orderDao.selectOne(queryWrapper);
//            QueryWrapper<JobOrderRecordEntity> qw =
//                    new QueryWrapper<JobOrderRecordEntity>()
//                            .eq("is_deleted", 0)
//                            .eq(JobOrderRecordEntity.COL_ORDER_ID, orderRecord.getId());
//            List<JobOrderRecordEntity> jobOrderRecordEntities = jobOrderRecordMapper.selectList(qw);
//            if (!CollectionUtils.isEmpty(jobOrderRecordEntities)) {
//                jobOrderRecordEntities.stream().forEach(jobOrderRecord -> {
//                    jobOrderRecord.setIsDeleted(1L);
//                });
//                orderUtil.UpdateJobOrderList(jobOrderRecordEntities);
//
//            }
        return "操作成功！";
    }

    public boolean checkPaySuccessTrade(String orderNo){
        boolean flag = false;
        //查询支付流水
        List<OrderPayRecord> payRecordList = orderPayRecordDao.selectByOrderNo(orderNo);
        if(CollectionUtils.isNotEmpty(payRecordList)){
            boolean isWaterOrder = orderUtil.isWaterOrder(orderNo);
            for (OrderPayRecord payRecord : payRecordList) {
                if(Objects.equals(payRecord.getPayStatus(),1L)){
                    flag = true;
                    break;
                }else{
                    if(payRecord.getPayMethod() != null){
                        try {
                            switch (payRecord.getPayMethod().intValue()){
                                case 1:
                                    boolean aliFlag = alipayUtil.queryOrderTradeState(payRecord.getPayRequestNo(), isWaterOrder);
                                    if(aliFlag){
                                        flag = true;
                                    }
                                    break;
                                case 2:
                                    boolean wxFlag = paymentService.queryOrderTradeState(payRecord.getPayRequestNo(), isWaterOrder);
                                    if(wxFlag){
                                        flag = true;
                                    }
                                    break;
                                case 3:
                                    boolean chinaumsFlag = chinaumsPayService.queryTradeNo(payRecord.getPayRequestNo());
                                    if(chinaumsFlag){
                                        flag = true;
                                    }
                                    break;
                                default:
                                    System.out.println(4);
                            }
                        }catch (Exception e){
                            log.error("订单号:{} 查询支付状态异常:{}",payRecord.getPayRequestNo(),e);
                        }
                    }
                }
            }
        }
        return flag;
    }




    //新增售后记录
    private void insertSaleInfo(OperationOrderInDto operationOrderInDto) {
        OrderRecordVo orderRecordVo = orderDao.getClientOrderRecordVo(operationOrderInDto.getOrderNo());
        if (null == orderRecordVo) {
            throw new BusinessException("订单信息不存在,请核实订单号！");
        }
        boolean flag = false;
        OrderAfterSale orderAfterSale = orderAfterSaleDao.queryByOrderNo(operationOrderInDto.getOrderNo());
        if (null == orderAfterSale) {
            orderAfterSale = new OrderAfterSale();
            flag = true;
        }
        orderAfterSale.setOrderNo(operationOrderInDto.getOrderNo());
        String code = orderUtil.getOrderNum(orderRecordVo.getBuyUserId());
        orderAfterSale.setCode(code);
        orderAfterSale.setProcessingStatus(0L);
        orderAfterSale.setAfterSaleType(operationOrderInDto.getStatus());
        orderAfterSale.setBuyPhone(orderRecordVo.getBuyUserPhone());
        orderAfterSale.setApplyReason(operationOrderInDto.getReason());
        orderAfterSale.setBuyerReason(operationOrderInDto.getReplenish());
        orderAfterSale.setOrderStatus(orderRecordVo.getOrderStatus());
        orderAfterSale.setOrderAmount(orderRecordVo.getTotalOrderAmount());
        orderAfterSale.setConsigneeMobile(orderRecordVo.getReceiverPhone());
        orderAfterSale.setConsigneeName(orderRecordVo.getReceiverName());
        orderAfterSale.setCityId(orderRecordVo.getCityId());
        orderAfterSale.setCityName(orderRecordVo.getCityName());
        orderAfterSale.setProvinceId(orderRecordVo.getProvinceId());
        orderAfterSale.setProvinceName(orderRecordVo.getProvinceName());
        orderAfterSale.setAreaId(orderRecordVo.getAreaId());
        orderAfterSale.setAreaName(orderRecordVo.getAreaName());
        orderAfterSale.setAddress(orderRecordVo.getAddress());
        orderAfterSale.setCertificate(operationOrderInDto.getImage());
        orderAfterSale.setApplyTime(new Date());
        if (flag) {
            orderAfterSaleDao.insert(orderAfterSale);
            Long afterSaleId = orderAfterSale.getId();
            String orderNo = operationOrderInDto.getOrderNo();
            // 新增退换货商品关联
            List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectAllByOrderNo(orderNo);
            List<OrderAfterSaleMapping> orderAfterSaleMappings = goodsOrderItems.stream().map(res -> {
                OrderAfterSaleMapping orderAfterSaleMapping = new OrderAfterSaleMapping();
                orderAfterSaleMapping.setOrderNo(orderNo);
                orderAfterSaleMapping.setOrderItemId(res.getId());
                orderAfterSaleMapping.setAfterSaleId(afterSaleId);
                orderAfterSaleMapping.setGoodsCode(res.getGoodsCode());
                orderAfterSaleMapping.setGoodsName(res.getGoodsName());
                orderAfterSaleMapping.setCreatedByName(orderRecordVo.getReceiverName());
                orderAfterSaleMapping.setIsDeleted(0L);
                orderAfterSaleMapping.setCreatedDate(new Date());
                orderAfterSaleMapping.setModifiedDate(new Date());
                return orderAfterSaleMapping;
            }).collect(Collectors.toList());

            orderAfterSaleMappingDao.insertBatchAll(orderAfterSaleMappings);
        } else {
            orderAfterSaleDao.updateById(orderAfterSale);
        }
    }

    //取消申请
    private void cancelAfterSale(String orderNo) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("processing_status", 4);
        wrapper.eq("order_no", orderNo);
        orderAfterSaleDao.update(null, wrapper);
        //取消之后更新订单状态为可选择退货
        orderDao.update(null , new UpdateWrapper<OrderRecord>().set("is_refundable_exchangeable" ,OrderConstant.APPLY_REFUND_EXCHANGE ).eq("order_no" , orderNo));
    }

    //更新整笔订单所有明细状态
    @Transactional
    public void editGoodsOrderItem(OperationOrderInDto operationOrderInDto) {
        UpdateWrapper wrapper = new UpdateWrapper();
        wrapper.set("order_item_status", 5);
        wrapper.eq("order_no", operationOrderInDto.getOrderNo());
        wrapper.ne("sub_goods_type", 2);
        goodsOrderItemDao.update(null, wrapper);
        // 添加用户成长值
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(operationOrderInDto.getOrderNo());
        if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1)
                && null != orderInfoInDto.getPaidAmount()
                && orderInfoInDto.getPaidAmount().compareTo(new BigDecimal("0")) != -1
                && orderInfoInDto.getGiveGrowthValue()!=null
                && orderInfoInDto.getGiveGrowthValue()>0) {
            AlterGrowCustomerDto alterGrowCustomerDto = new AlterGrowCustomerDto();
            alterGrowCustomerDto.setDirection(1);
            alterGrowCustomerDto.setAvailable(new BigDecimal(orderInfoInDto.getGiveGrowthValue()));
            alterGrowCustomerDto.setCustomerId(operationOrderInDto.getUserId().toString());
            alterGrowCustomerDto.setRemark("商城下单获得成长值");
            alterGrowCustomerDto.setBillId(operationOrderInDto.getOrderNo());
            alterGrowCustomerDto.setTypeCode("20");
            alterGrowCustomerDto.setTypeName("商城下单送成长值");
            alterGrowCustomerDto.setOperatorId(operationOrderInDto.getOperatorId());
            alterGrowCustomerDto.setOperatorName(operationOrderInDto.getOperatorName());
            alterGrowCustomerDto.setOrderNo(operationOrderInDto.getOrderNo());
            pointService.sendGrow(alterGrowCustomerDto);
        }

        if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1) && null != orderInfoInDto.getActivityGiftPoints() && orderInfoInDto.getActivityGiftPoints() > 0) {
            AlterPointDto alterPointDto = new AlterPointDto();
            alterPointDto.setDirection(1);
            alterPointDto.setAvailable(orderInfoInDto.getActivityGiftPoints());
            alterPointDto.setTypeCode("20");
            alterPointDto.setTypeName("下单赠送积分活动");
            alterPointDto.setBillId(operationOrderInDto.getOrderNo());
            alterPointDto.setSourceId(orderInfoInDto.getBuyUserId() + "");
            alterPointDto.setOperatorId(operationOrderInDto.getOperatorId());
            alterPointDto.setOperatorName(operationOrderInDto.getOperatorName());
            alterPointDto.setOrderNo(operationOrderInDto.getOrderNo());
            pointService.sendPoint(alterPointDto);
        }

        if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1) && null != orderInfoInDto.getMemberGiftPoints() && orderInfoInDto.getMemberGiftPoints() > 0) {
            AlterPointDto alterPointDto = new AlterPointDto();
            alterPointDto.setDirection(1);
            alterPointDto.setAvailable(orderInfoInDto.getMemberGiftPoints());
            alterPointDto.setTypeCode("20");
            alterPointDto.setTypeName("下单送积分");
            alterPointDto.setBillId(operationOrderInDto.getOrderNo());
            alterPointDto.setSourceId(orderInfoInDto.getBuyUserId() + "");
            alterPointDto.setOperatorId(operationOrderInDto.getOperatorId());
            alterPointDto.setOperatorName(operationOrderInDto.getOperatorName());
            alterPointDto.setOrderNo(operationOrderInDto.getOrderNo());
            pointService.sendPoint(alterPointDto);
        }
    }

    public RecentOrdersVo queryRecentOrders(RecentOrdersVo recentOrdersVo) {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        // 设置顾客ID
        Long customerId = customerUtil.getCustomerInfoByUserEntity().getId();
        recentOrdersVo.setUserId(customerId);


        RecentOrdersVo ordersVo = new RecentOrdersVo();
        // 查询所有订单
        List<OrderRecord> orderRecordList = orderDao.queryRecentOrders(recentOrdersVo.getUserId(), recentOrdersVo.getOrderCategory());
        if (!CollectionUtils.isEmpty(orderRecordList)) {
            // 查询待付款数量
            ordersVo.setOrderPendingQuantity(orderRecordList.stream().filter(item -> item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode()).count());
            // 查询订单待发货数量
            ordersVo.setOrderToBeShipped(orderRecordList.stream().filter(item -> item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode() || item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.PARTIALLY_SHIPPED.getCode()).count());
            // 查询待收货数量
            ordersVo.setOrderPendingReceiptNum(orderRecordList.stream().filter(item -> item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.SHIPPED.getCode()).count());
            // 查询待评价数量
            ordersVo.setOrderPending(orderRecordList.stream().filter(item -> item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.COMMENT.getCode()).count());
            // 查询已完成数量
            ordersVo.setOrderOffTheStocksNum(orderRecordList.stream().filter(item -> item.getOrderStatus() == 6L).count());
        }
        // 取前四条数据
        List<RecentOrderDto> orderDtoList = orderRecordList.stream().filter(item -> {
            // 取订单状态为 待付款、待发货、待收货、待评价
            Long status = item.getCustomerOrderStatus();
            if (status == OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode() ||
                    status == OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode() ||
                    status == OrderConstant.CustomerOrderStatusEnum.SHIPPED.getCode() ||
                    status == OrderConstant.CustomerOrderStatusEnum.COMMENT.getCode()) {
                return true;
            }
            return false;
        }).limit(4).map(item -> {
            RecentOrderDto recentOrderDto = new RecentOrderDto();
            BeanUtils.copyProperties(item, recentOrderDto);

            // 查询订单图片
            String goodsImg = goodsOrderItemDao.queryOrderPictures(item.getOrderNo());
            recentOrderDto.setGoodsImage(goodsImg);
            try {
                // 判断订单时间状态
                if (item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode()) {
                    recentOrderDto.setMoTime(JudgeDateUtil.getTime(item.getCreateOrderTime()));
                    recentOrderDto.setTime(item.getCreateOrderTime());
                }
                if (item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode()) {
                    recentOrderDto.setMoTime(JudgeDateUtil.getTime(Optional.ofNullable(item.getPayTime()).orElse(item.getPaymentDeadline())));
                    recentOrderDto.setTime(item.getPayTime());
                }
                if (item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.SHIPPED.getCode()) {
                    recentOrderDto.setMoTime(JudgeDateUtil.getTime(item.getEeliveryTime()));
                    recentOrderDto.setTime(item.getEeliveryTime());
                }
                if (item.getCustomerOrderStatus() == OrderConstant.CustomerOrderStatusEnum.COMMENT.getCode()) {
                    recentOrderDto.setMoTime(JudgeDateUtil.getTime(item.getSignTime()));
                    recentOrderDto.setTime(item.getSignTime());
                }
            } catch (Exception e) {
                StringWriter sw = new StringWriter();
                e.printStackTrace(new PrintWriter(sw, true));
                log.error("日期转换错误："+ LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", sw);
                log.error("日期转换错误!");
            }
            return recentOrderDto;
        }).collect(Collectors.toList());

        ordersVo.setRecentOrderDtoList(orderDtoList);
        ordersVo.setUserId(recentOrdersVo.getUserId());

        return ordersVo;
    }


    @Transactional
    public void editPayItemInfo(EditPayInfoRequestVo vo) {
        String orderNo = vo.getOrderNo();
        Long payStatus = vo.getPayStatus();

        // 修改订单支付方式、支付状态
        UpdateWrapper<OrderRecord> wrapper = new UpdateWrapper<>();
        wrapper.eq("order_no", vo.getOrderNo());

        if (vo.getPayMethod() == 1 || vo.getPayMethod() == 2) {
            // 修改订单顾客状态为待付款
            wrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode());
        }
        wrapper.set("pay_method", vo.getPayMethod());
        wrapper.set("pay_type", vo.getPayType());

        // 查询修改前数据 0待支付，1部分支付，2已支付
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);

        if (vo.getPayMethod().equals(orderInfoInDto.getPayMethod()) && vo.getPayStatus().equals(orderInfoInDto.getPayStatus())) {
            if (null != vo.getDescription() || null != vo.getVouchers()) {
                OrderOperationLog orderOperationLog = new OrderOperationLog();
                UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
                orderOperationLog.setOperationByName(userAuthor.getFirstName());
                orderOperationLog.setCreatedByName(userAuthor.getFirstName());
                orderOperationLog.setCreatedBy(userAuthor.getUserId());
                orderOperationLog.setCreatedDate(new Date());
                orderOperationLog.setOrderNo(orderNo);
                orderOperationLog.setOperationType("修改支付信息");
                orderOperationLog.setOperationTime(new Date());
                orderOperationLog.setIsDeleted(0L);
                if (null != vo.getDescription() && !vo.getDescription().isBlank()) {
                    orderOperationLog.setDescription("新增备注：\"" + vo.getDescription() + "\"");
                } else {
                    orderOperationLog.setDescription("新增凭证");
                }

                if (null != vo.getVouchers() && !vo.getVouchers().isEmpty()) {
                    String join = String.join(",", vo.getVouchers());
                    orderOperationLog.setFile(join);
                }
                orderOperationLogDao.insert(orderOperationLog);
            }
            return;
        }

        if (!payStatus.equals(orderInfoInDto.getPayStatus())) {
            if (orderInfoInDto.getPayStatus() == 0) {
                switch (payStatus.intValue()) {
                    case 1:
                        // 修改支付状态为部分支付
                        wrapper.set("pay_status", 1);
                        // 设置订单状态
                        wrapper.set("order_status", OrderConstant.OrderStatusEnum.UNCONFIRMED.getCode());
                        wrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode());
                        break;
                    case 2:
                        // 修改支付状态为已支付
                        wrapper.set("pay_status", 2);
                        // 设置订单状态
                        wrapper.set("order_status", OrderConstant.OrderStatusEnum.ACKNOWLEDGED.getCode());
                        wrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
                        break;
                    default:
                        throw new BusinessException("不可以修改此支付状态");
                }
            } else if (orderInfoInDto.getPayStatus() == 1) {
                if (payStatus == 2) {
                    // 修改支付状态为已完成
                    wrapper.set("pay_status", 2);
                    // 修改订单状态
                    wrapper.set("pay_time", new Date());
                    wrapper.set("order_status", OrderConstant.OrderStatusEnum.ACKNOWLEDGED.getCode());
                    wrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
                } else {
                    throw new BusinessException("不可以修改此支付状态");
                }
            } else {
                throw new BusinessException("不可修改此支付状态");
            }
        }

        orderDao.update(null, wrapper);

        // 保存修改说明、凭证
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderNo);
        orderOperationLog.setOperationType("修改支付信息");
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(vo.getDescription());
        orderOperationLog.setIsDeleted(0L);
        if (null != vo.getVouchers() && !vo.getVouchers().isEmpty()) {
            String join = String.join(",", vo.getVouchers());
            orderOperationLog.setFile(join);
        }
        List<String> descrList = new ArrayList<>();

        // 判断支付方式
        if (!vo.getPayMethod().equals(orderInfoInDto.getPayMethod())) {
            String newPayMethodDescr = "";
            switch (vo.getPayMethod().intValue()) {// 支付方式 0无，1支付宝，2微信
                case 0:
                    newPayMethodDescr = "无";
                    break;
                case 1:
                    newPayMethodDescr = "支付宝";
                    break;
                case 2:
                    newPayMethodDescr = "微信";
                    break;
                default:
                    throw new BusinessException("该支付方式不存在");
            }
            String oldPayMethodDescr = "";
            switch (orderInfoInDto.getPayMethod().intValue()) { // 支付方式 0无，1支付宝，2微信
                case 0:
                    oldPayMethodDescr = "无";
                    break;
                case 1:
                    oldPayMethodDescr = "支付宝";
                    break;
                case 2:
                    oldPayMethodDescr = "微信";
                    break;
                default:
                    throw new BusinessException("该支付方式不存在");
            }
            descrList.add("把支付方式从：\"" + oldPayMethodDescr + "\"改为：\"" + newPayMethodDescr + "\"");
        }
        // 判断支付状态
        if (!vo.getPayStatus().equals(orderInfoInDto.getPayStatus())) {
            String newPayStatusDescr = "";
            switch (vo.getPayStatus().intValue()) {
                case 0:
                    newPayStatusDescr = "待支付";
                    break;
                case 1:
                    newPayStatusDescr = "部分支付";
                    break;
                case 2:
                    newPayStatusDescr = "已支付";
                    break;
                default:
                    throw new BusinessException("该支付状态不存在");
            }
            String oldPayStatusDescr = "";
            switch (orderInfoInDto.getPayStatus().intValue()) {
                case 0:
                    oldPayStatusDescr = "待支付";
                    break;
                case 1:
                    oldPayStatusDescr = "部分支付";
                    break;
                case 2:
                    oldPayStatusDescr = "已支付";
                    break;
                default:
                    throw new BusinessException("该支付状态不存在");
            }
            descrList.add("把支付状态从：\"" + oldPayStatusDescr + "\"改为：\"" + newPayStatusDescr + "\"");

        }
        if (null != vo.getDescription() && !vo.getDescription().isBlank()) {
            descrList.add("新增备注：\"" + vo.getDescription() + "\"");
        }

        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        orderOperationLog.setDescription(String.join("，", descrList));
        orderOperationLog.setOperationByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedBy(userAuthor.getUserId());
        orderOperationLog.setCreatedDate(new Date());
        // 添加日志记录
        orderOperationLogDao.insert(orderOperationLog);
    }

    @Transactional
    public void editOrderGoodsDiscountAmount(EditPayInfoRequestVo vo) {
        String orderNo = vo.getOrderNo();
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        if (orderInfoInDto.getPayStatus() == 0) {
            //删除支付缓存
            paymentService.delPayInfo(orderNo);
        }
        // 校验订单是否已经支付
        if (orderInfoInDto.getPayStatus() == 2) {
            throw new BusinessException("支付订单不可修改商品价格");
        }
        GoodsOrderItem orderItem = goodsOrderItemDao.selectOne(new QueryWrapper<GoodsOrderItem>().eq("order_no", vo.getOrderNo()).eq("is_deleted", 0L).eq("id", vo.getItemId()));

        // 判断是否时搭配
        if (orderItem.getSubGoodsType() == 3 || orderItem.getSubGoodsType() == 4) {
            throw new BusinessException("\"" + orderItem.getGoodsName() + "\"为搭配商品不可修改价格");
        }

        BigDecimal discountAmount1 = orderItem.getDiscountAmount();

        // 获取商品核算价格
        List<ChannelGoodsMerchandising> channelGoodsMerchandisings = productClient.getChannelGoodsAccountingPrice(new ArrayList<>(Collections.singleton(vo.getGoodsCode()))).getData();

        // 判断是否是组合商品
        if (orderItem.getProductTypes() == 2) {
            // 组合内商品分摊优惠金额
            List<GoodsOrderItem> goodsOrderItemList = goodsOrderItemDao.selectList(new QueryWrapper<GoodsOrderItem>().eq("order_no", vo.getOrderNo()).eq("is_deleted", 0).eq("parent_item_id", orderItem.getId()));

            // 使用优惠总金额
            BigDecimal discountAmount = vo.getDiscountAmount();

            // 组合内商品明细总金额
            BigDecimal itemTotalSalesAmount = goodsOrderItemList.stream().map(GoodsOrderItem::getSalesAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal totalFareAmount = BigDecimal.ZERO;

            for (int i = 0; i < goodsOrderItemList.size(); i++) {
                GoodsOrderItem item = goodsOrderItemList.get(i);

                //定义每次分摊后的总优惠
                BigDecimal fareAmount = BigDecimal.ZERO;

                // 计算优惠应该分摊的百分比：当前sku售价*购买数量 /商品总金额
                BigDecimal scale = item.getGoodsPrice().multiply(new BigDecimal(item.getBuyNum() + ""))
                        .divide(itemTotalSalesAmount, 6, BigDecimal.ROUND_HALF_UP);

                //当前商品分摊到的优惠
                fareAmount = discountAmount.multiply(scale);

                // 乘以数量再除以数量，保留两位小数，减少误差
                BigDecimal finalDiscount = fareAmount.divide(new BigDecimal(item.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(item.getBuyNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);

                // 记录总分摊到的优惠
                totalFareAmount = totalFareAmount.add(finalDiscount);

                // 最后一次分摊到的优惠 = 总优惠-前(n-1)个商品分摊到的优惠
                if (i == goodsOrderItemList.size() - 1) {
                    finalDiscount = finalDiscount.add(discountAmount.subtract(totalFareAmount).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                }

                // 当前商品分摊到的最终优惠
                // 更新手动改价金额
                item.setManualDiscountAmount(Optional.ofNullable(item.getManualDiscountAmount()).orElse(BigDecimal.ZERO).add(finalDiscount));
                BigDecimal afterSalesAmount = item.getSalesAmount().subtract(finalDiscount);

                // 判断是否小于核算金额
                if (!CollectionUtils.isEmpty(channelGoodsMerchandisings)) {
                    for (ChannelGoodsMerchandising mer : channelGoodsMerchandisings) {
                        if (mer.getGoodsCode().equals(item.getGoodsCode())) {
                            if (afterSalesAmount.compareTo(mer.getAccountingPrice()) == -1) {
                                throw new BusinessException("\"" + item.getGoodsName() + "\"优惠后金额小于核算价格");
                            }
                        }
                    }
                }

                // 更新总优惠金额
                BigDecimal sumDiscountAmount = item.getDiscountAmount().add(finalDiscount);
                item.setDiscountAmount(sumDiscountAmount);
                // 更新销售单价
                item.setGoodsPrice(afterSalesAmount.divide(new BigDecimal(item.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN));
                // 更新销售金额
                item.setSalesAmount(afterSalesAmount);
                item.setPayAmount(afterSalesAmount);
                item.setPaidAmount(afterSalesAmount);
                // 执行更新订单行
                goodsOrderItemDao.updateById(item);
            }
            BigDecimal groupSalesAmount = orderItem.getSalesAmount().subtract(vo.getDiscountAmount());
            orderItem.setSalesAmount(groupSalesAmount);
            orderItem.setPaidAmount(groupSalesAmount);
            orderItem.setPayAmount(groupSalesAmount);
            // 销售单价
            orderItem.setGoodsPrice(groupSalesAmount.divide(new BigDecimal(orderItem.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN));
            // 优惠金额
            orderItem.setDiscountAmount(orderItem.getDiscountAmount().add(vo.getDiscountAmount()));
            // 手动改价
            orderItem.setManualDiscountAmount(orderItem.getManualDiscountAmount().add(vo.getDiscountAmount()));
            // 更新组合商品价格
            goodsOrderItemDao.updateById(orderItem);

            // 修改订单应付金额 and 总金额
            orderInfoInDto.setDiscountTotalAmount(orderInfoInDto.getDiscountTotalAmount().add(vo.getDiscountAmount()));
            BigDecimal payableAmount = orderInfoInDto.getPayableAmount().subtract(vo.getDiscountAmount());
            orderInfoInDto.setPayableAmount(payableAmount);
            orderInfoInDto.setPaidAmount(payableAmount);
            // 订单总金额
           // orderInfoInDto.setTotalOrderAmount(orderInfoInDto.getTotalOrderAmount().subtract(vo.getDiscountAmount()));
            // 设置订单手动优惠金额
            orderInfoInDto.setManualDiscountAmount(orderInfoInDto.getManualDiscountAmount().add(vo.getDiscountAmount()));
            // 计算会员赠送积分
            Long memberGivePoints = computedMemberGivePointsWMS(orderInfoInDto.getBuyUserId(), orderInfoInDto.getPayableAmount());
            // 计算总赠送积分
            orderInfoInDto.setGiftPoints(memberGivePoints + Optional.ofNullable(orderInfoInDto.getActivityGiftPoints()).orElse(0L));
            orderInfoInDto.setMemberGiftPoints(memberGivePoints);
            // 修改订单
            orderDao.updateById(orderInfoInDto);
        } else {
            // 原销售金额
            BigDecimal salesAmount = orderItem.getSalesAmount();

            // 要进行优惠的金额
            BigDecimal newDiscountAmount = vo.getDiscountAmount();

            // 计算优惠后商品销售金额
            BigDecimal afterDiscountAmount = salesAmount.subtract(newDiscountAmount);

            // 商品核算金额
            BigDecimal accountingPrice = Optional.ofNullable(channelGoodsMerchandisings.get(0).getAccountingPrice()).orElse(BigDecimal.ZERO);

            if (afterDiscountAmount.compareTo(accountingPrice) == -1) {
                throw new BusinessException("\"" + vo.getGoodsName() + "\"优惠后金额低于核算金额");
            }
            orderItem.setGoodsPrice(afterDiscountAmount.divide(new BigDecimal(orderItem.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN));
            orderItem.setSalesAmount(afterDiscountAmount);
            orderItem.setPayAmount(afterDiscountAmount);
            orderItem.setPaidAmount(afterDiscountAmount);
            orderItem.setManualDiscountAmount(newDiscountAmount);
            orderItem.setDiscountAmount(orderItem.getDiscountAmount().add(newDiscountAmount));
            // 执行更新操作
            goodsOrderItemDao.updateById(orderItem);

            BigDecimal orderPayableAmount = orderInfoInDto.getPayableAmount();

            // 修改订单
            orderInfoInDto.setDiscountTotalAmount(orderInfoInDto.getDiscountTotalAmount().add(vo.getDiscountAmount()));
            orderInfoInDto.setPayableAmount(orderPayableAmount.subtract(vo.getDiscountAmount()));
            orderInfoInDto.setPaidAmount(orderPayableAmount.subtract(vo.getDiscountAmount()));
            orderInfoInDto.setManualDiscountAmount(orderInfoInDto.getManualDiscountAmount().add(vo.getDiscountAmount()));
//            // 订单总金额
           // orderInfoInDto.setTotalOrderAmount(orderInfoInDto.getTotalOrderAmount().subtract(vo.getDiscountAmount()));
            // 计算会员赠送积分
            Long memberGivePoints = computedMemberGivePointsWMS(orderInfoInDto.getBuyUserId(), orderInfoInDto.getPayableAmount());
            orderInfoInDto.setMemberGiftPoints(memberGivePoints);
            // 计算总赠送积分
            orderInfoInDto.setGiftPoints(memberGivePoints + Optional.ofNullable(orderInfoInDto.getActivityGiftPoints()).orElse(0L));
            // 修改订单
            orderDao.updateById(orderInfoInDto);
        }

        // 新增促销信息
        GoodsOrderPromotion goodsOrderPromotion = new GoodsOrderPromotion();
        goodsOrderPromotion.setOrderNo(vo.getOrderNo());
        goodsOrderPromotion.setPromotionType(0L);
        goodsOrderPromotion.setGoodsCode(orderItem.getGoodsCode());
        goodsOrderPromotion.setGoodsName(orderItem.getGoodsName());
        goodsOrderPromotion.setGoodsModel(orderItem.getGoodsModel());
        goodsOrderPromotion.setSeriesGoodsCode(orderItem.getSeriesGoodsCode());
        goodsOrderPromotion.setLineId(orderItem.getId());
        goodsOrderPromotion.setDiscountAmount(vo.getDiscountAmount());
        String descr = "";
        if (null != vo.getDescription() && !vo.getDescription().isBlank()) {
            descr = vo.getDescription();
        }
        if (!CollectionUtils.isEmpty(vo.getVouchers())) {
            descr = "|" + String.join(",", vo.getVouchers());
        }
        goodsOrderPromotion.setDescription(descr);
        goodsOrderPromotion.setIsDeleted(0L);
        goodsOrderPromotion.setCreatedDate(new Date());
        goodsOrderPromotion.setModifiedDate(new Date());
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        goodsOrderPromotion.setCreatedByName(userAuthor.getFirstName());
        goodsOrderPromotion.setModifiedByName(userAuthor.getFirstName());
        goodsOrderPromotion.setCreatedBy(userAuthor.getUserId());
        goodsOrderPromotion.setModifiedBy(userAuthor.getUserId());
        goodsOrderPromotionDao.insert(goodsOrderPromotion);

        //取消支付流水
        orderPayRecordDao.updateCancelStatusByOrderNo(orderNo,1);

        // 添加订单操作日志
        addLog1(orderNo, "修改价格", "修改商品信息\n" + "商品\"" + vo.getGoodsCode() + "\"优惠金额从：\"" + discountAmount1 + "\"改成：\"" + discountAmount1.add(vo.getDiscountAmount()) + "\"", String.join(",", vo.getVouchers()));

    }

    public void insertOrderOperationLog(OrderOperationLog operationLog) {
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        operationLog.setCreatedByName(userAuthor.getFirstName());
        operationLog.setOperationByName(userAuthor.getFirstName());
        operationLog.setCreatedDate(new Date());
        operationLog.setCreatedBy(userAuthor.getUserId());
        operationLog.setOperationType("添加日志");
        operationLog.setOperationByName(userAuthor.getFirstName());
        orderOperationLogDao.insert(operationLog);
    }

    public PageInfo<OrderOperationLog> selectOrderOperationLogPageInfo(OrderOperationLogPageRequestVo requestVo) {
        PageInfo<OrderOperationLog> page = new PageInfo<>(requestVo.getPage(), requestVo.getSize());
        requestVo.setPage(page.getOffset());
        requestVo.setSize(page.getSize());
        Long total = orderOperationLogDao.selectOrderOperationLogCount(requestVo);
        page.setTotal(total == null ? 0 : total);
        List<OrderOperationLog> orderOperationLogs = new ArrayList<>();
        if (total > 0) {
            orderOperationLogs = orderOperationLogDao.selectOrderOperationLogPage(requestVo);
        }
        page.setRecords(orderOperationLogs);
        return page;
    }

    /**
     * 支付回调更新订单状态
     *
     * @param out_trade_no 商户订单号
     * @param total_amount 支付金额
     * @param gmt_payment  交易付款时间
     * @param gmt_create   交易创建时间
     * @param payMethod
     */
    public void updateOrderPaymentRecord(String out_trade_no, String total_amount, Date gmt_payment, Date gmt_create, Long payMethod, String chinaumsTotalAmout, Integer tradeInFlag) {
        // 更新订单状态
        OrderRecord orderRecord = orderDao.selectOne(new QueryWrapper<OrderRecord>().eq("order_no", out_trade_no));
        String desc = "";
        // 判断改笔订单是否时预售订单
        if (orderRecord.getIsPreSale() == 1) {
            if (orderRecord.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.DEPOSIT_NOT_PAID.getCode()) {
                // 设置支付状态为部分支付
                orderRecord.setPayStatus(1L);
                orderRecord.setDepositPaymentTime(gmt_payment);
                // 设置顾客支付状态为待付款
                orderRecord.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.PENDING_PAYMENT.getCode());
                // 判断是否已经可以支付尾款金额
                if ((new Date().after(orderRecord.getBalancePaymentStartTime()) && new Date().before(orderRecord.getBalancePaymentEndTime())) ||
                        (new Date().getTime() == orderRecord.getBalancePaymentStartTime().getTime() || new Date().getTime() == orderRecord.getBalancePaymentEndTime().getTime())) {
                    orderRecord.setPreSalePaymentStatus(OrderConstant.preSalePaymentStatusEnum.PAY_DEPOSIT_START_BALANCE.getCode());
                } else {
                    // 设置预售支付状态为：已支付定金未开始尾款
                    orderRecord.setPreSalePaymentStatus(OrderConstant.preSalePaymentStatusEnum.PAY_DEPOSIT_NO_BALANCE_PAYMENT.getCode());
                    // 定金支付完成把未开始支付尾款订单放入缓存
                    Map<String, String> map = new HashMap<>();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    map.put("orderNo", orderRecord.getOrderNo());
                    map.put("balancePaymentStartTime", simpleDateFormat.format(orderRecord.getBalancePaymentStartTime()));
                    map.put("balancePaymentEndTime", simpleDateFormat.format(orderRecord.getBalancePaymentEndTime()));
                    map.put("createOrderTime", simpleDateFormat.format(orderRecord.getCreateOrderTime()));
                    map.put("buyUserPhone", orderRecord.getBuyUserPhone());
                    redisUtil.lPush(TaskConstants.UPDATE_PRE_ORDER_STATUS, JSON.toJSONString(map));
                }
                // 支付截止时间设置为尾款结束时间
                orderRecord.setPaymentDeadline(orderRecord.getBalancePaymentEndTime());
                if (StringUtils.isNotBlank(total_amount)) {
                    orderRecord.setPaidAmount(new BigDecimal(total_amount));
                }
                if (StringUtils.isNotBlank(chinaumsTotalAmout)) {
                    orderRecord.setRealPaidAmount(new BigDecimal(chinaumsTotalAmout));
                }
                desc = "定金支付成功";
            } else if (orderRecord.getPreSalePaymentStatus() == OrderConstant.preSalePaymentStatusEnum.PAY_DEPOSIT_START_BALANCE.getCode()) {
                // 尾款已支付
                orderRecord.setPayStatus(2L);
                orderRecord.setFinalPaymentTime(gmt_payment);
                orderRecord.setPreSalePaymentStatus(OrderConstant.preSalePaymentStatusEnum.THE_BALANCE_HAS_BEEN_PAID.getCode());
                orderRecord.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
                if (StringUtils.isNotBlank(total_amount)) {
                    orderRecord.setPaidAmount(new BigDecimal(total_amount).add(orderRecord.getPaidAmount()));
                }
                if (StringUtils.isNotBlank(chinaumsTotalAmout)) {
                    orderRecord.setRealPaidAmount(new BigDecimal(chinaumsTotalAmout).add(orderRecord.getPaidAmount()));
                }
                desc = "尾款支付成功";
            }
            // 预售订单添加到缓存
            redisUtil.lPush(TaskConstants.SEND_SMS_TEMPLATE, orderRecord.getOrderNo());
        } else {
            orderRecord.setPayStatus(2L);
            orderRecord.setCustomerOrderStatus(OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
            orderRecord.setOrderStatus(2L);
            if (StringUtils.isNotBlank(total_amount)) {
                orderRecord.setPaidAmount(new BigDecimal(total_amount));
            }
            if (StringUtils.isNotBlank(chinaumsTotalAmout)) {
                orderRecord.setRealPaidAmount(new BigDecimal(chinaumsTotalAmout));
            }
            orderRecord.setPayTime(new Date());
            desc = "支付成功";
        }
        orderRecord.setPayMethod(payMethod);
//        orderRecord.setPayType(payMethod);
        orderRecord.setTradeInFlag(tradeInFlag);

        //如果是以旧换新，清空赠送积分
        if(tradeInFlag != null && tradeInFlag == 1){
            orderRecord.setGiftPoints(0L);
            orderRecord.setMemberGiftPoints(0L);
            orderRecord.setActivityGiftPoints(0L);
        }

        orderDao.updateById(orderRecord);
        MybatisMateConfig.decryptByReflect(orderRecord);
        // 同步订单明细状态
        goodsOrderItemDao.update(null, new UpdateWrapper<GoodsOrderItem>().eq("is_deleted", 0).set("order_item_status", 2).eq("order_no", out_trade_no).ne("sub_goods_type",2));

        // 添加订单支付成功日志
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderRecord.getOrderNo());
        orderOperationLog.setOperationByName(orderRecord.getBuyUserName());
        orderOperationLog.setOperationType("支付成功");
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(desc);
        orderOperationLog.setCreatedByName(orderRecord.getBuyUserName());
        orderOperationLog.setIsDeleted(0L);
        orderOperationLog.setCreatedBy(orderRecord.getBuyUserId() + "");
        orderOperationLog.setCreatedDate(new Date());
        orderOperationLogDao.insert(orderOperationLog);
        // 发送消息 更新库存
        // 赠送积分
//        if (null != orderRecord.getGiftPoints() && orderRecord.getGiftPoints() > 0) {
//            AlterPointDto alterPointDto = new AlterPointDto();
//            alterPointDto.setDirection(1);
//            alterPointDto.setAvailable(orderRecord.getGiftPoints());
//            alterPointDto.setBillId(orderRecord.getBuyUserId() + "");
//            alterPointDto.setTypeCode("20");
//            alterPointDto.setTypeName("下单送积分");
//            alterPointDto.setSourceId(orderRecord.getBuyUserId() + "");
//            pointService.sendPoint(alterPointDto);
//        }
        //成长值
//                AlterGrowCustomerDto alterGrowCustomerDto = new AlterGrowCustomerDto();
//                alterGrowCustomerDto.setDirection(1);
//                alterGrowCustomerDto.setAfterValue(new BigDecimal(total_amount));
//                alterGrowCustomerDto.setCustomerId(orderRecord.getBuyUserId().toString());
//                alterGrowCustomerDto.setRemark("商城下单获得成长值");
//                alterGrowCustomerDto.setBillId(out_trade_no);
//                pointService.sendGrow(alterGrowCustomerDto);

        if (null == orderRecord.getPreSalePaymentStatus()) {
            //是否累加抽奖次数
            Result<MallActivity> mallActivityResult = marketingClient.getActivityById();
            if (!mallActivityResult.getSuccess()) {
                throw new BusinessException(mallActivityResult.getMsg());
            }
            if (null != mallActivityResult.getData()) {
                MallActivity mallActivity = mallActivityResult.getData();
                CustomerMallActivityInfoDto customerMallActivityInfoDto = new CustomerMallActivityInfoDto();
                customerMallActivityInfoDto.setActivityId(mallActivity.getId());
                customerMallActivityInfoDto.setActivityName(mallActivity.getActivityTitle());
                customerMallActivityInfoDto.setCustomerId(orderRecord.getBuyUserId());
                customerMallActivityInfoDto.setEachLotteryDeductIntegral(mallActivity.getEachLotteryDeductIntegral());
                customerMallActivityInfoDto.setLotteryCount(mallActivity.getGiveLotteryNum());
                customerMallActivityInfoDto.setIntegralFreeLotteryNum(mallActivity.getIntegralFreeLotteryNum());
                customerClient.edit(customerMallActivityInfoDto);
            }
        }
    }

    /**
     * 查询支付状态
     *
     * @param orderNo
     * @return
     */
    public PayStatusResDto findPayStatusByOrderNo(String orderNo) {
        PayStatusResDto payStatusResDto = new PayStatusResDto();
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        OrderPayRecord orderPayRecord = orderPayRecordDao.selectAllByOrderNo(orderNo);
        // 判断该订单是否定金支付
        if (orderInfoInDto.getIsPreSale() == 1) {
            if (orderInfoInDto.getPreSalePaymentStatus() == 1 || orderInfoInDto.getPreSalePaymentStatus() == 0) {
                // 定金支付
                payStatusResDto.setPayMethod(orderPayRecord.getPayMethod());
                payStatusResDto.setPaidAmount(orderPayRecord.getPayAmount());
                payStatusResDto.setPayableAmount(orderInfoInDto.getDepositAmount());
                payStatusResDto.setPayStatus(orderPayRecord.getPayStatus()); // 支付状态
            } else {
                // 尾款支付
                payStatusResDto.setPayMethod(orderPayRecord.getPayMethod());
                payStatusResDto.setPaidAmount(orderPayRecord.getPayAmount());
                payStatusResDto.setPayableAmount(orderInfoInDto.getBalanceAmount());
                payStatusResDto.setPayStatus(orderPayRecord.getPayStatus()); // 支付状态
            }
        } else {
            payStatusResDto.setPayMethod(orderPayRecord.getPayMethod()); // 支付方式
            payStatusResDto.setPayStatus(orderPayRecord.getPayStatus()); // 支付状态
            payStatusResDto.setPaidAmount(orderPayRecord.getPayAmount()); // 实付金额
            payStatusResDto.setPayableAmount(orderInfoInDto.getPayableAmount()); // 应付金额
        }
        return payStatusResDto;
    }

    /**
     * 订单详情删除商品
     */
    @Transactional
    public void delOderItemGoods(Long itemId, String orderNo) {
        // 查询订单及其明细
        List<GoodsOrderItem> goodsOrderItemList = goodsOrderItemDao.findItemByItemId(itemId);

        // 商品总价格
        BigDecimal totalGoodsPrice = BigDecimal.ZERO;

        // 商品总挂牌金额
        BigDecimal totalListedPrice = BigDecimal.ZERO;

        // 销售金额/实付金额/应付金额
        BigDecimal totalSalesAmount = BigDecimal.ZERO;

        // 总优惠金额
        BigDecimal totalDiscountAmount = BigDecimal.ZERO;

        // 商品总活动优惠金额
        BigDecimal totalActivityDiscountAmount = BigDecimal.ZERO;

        // 商品总卡券优惠金额
        BigDecimal totalCouponDiscountAmount = BigDecimal.ZERO;

        // 总手工改价优惠金额
        BigDecimal totalManualDiscountAmount = BigDecimal.ZERO;

        for (GoodsOrderItem orderItem : goodsOrderItemList) {
            // 过滤主品
            if (null != orderItem.getParentItemId() && orderItem.getSubGoodsType() == 1) {
                continue;
            }
            int buyNum = orderItem.getBuyNum();

            totalGoodsPrice = totalGoodsPrice.add(orderItem.getGoodsPrice().multiply(BigDecimal.valueOf(buyNum)));

            totalListedPrice = totalListedPrice.add(orderItem.getListedPrice().multiply(BigDecimal.valueOf(buyNum)));

            totalSalesAmount = totalSalesAmount.add(orderItem.getGoodsPrice().multiply(BigDecimal.valueOf(buyNum)));

            totalDiscountAmount = totalDiscountAmount.add(orderItem.getDiscountAmount());

            totalActivityDiscountAmount = totalActivityDiscountAmount.add(Optional.ofNullable(orderItem.getActivityDiscountAmount()).orElse(BigDecimal.ZERO));

            totalCouponDiscountAmount = totalCouponDiscountAmount.add(Optional.ofNullable(orderItem.getCouponDiscountAmount()).orElse(BigDecimal.ZERO));

            totalManualDiscountAmount = totalManualDiscountAmount.add(Optional.ofNullable(orderItem.getManualDiscountAmount()).orElse(BigDecimal.ZERO));
        }

        List<String> goodsCodes = goodsOrderItemList.stream().map(GoodsOrderItem::getGoodsCode).collect(Collectors.toList());
        // 删除商品行关联促销信息
        goodsOrderPromotionDao.update(null, new UpdateWrapper<GoodsOrderPromotion>().eq("order_no", orderNo).in("goods_code", goodsCodes).set("is_deleted", 1));
        // 删除商品
        goodsOrderItemDao.update(null,
                new UpdateWrapper<GoodsOrderItem>()
                        .in("id", goodsOrderItemList.stream().map(GoodsOrderItem::getId).collect(Collectors.toList()))
                        .eq("order_no", orderNo)
                        .set("is_deleted", 1L));

        // 查询订单
        OrderRecord order = orderDao.findOrderRecordByOrderNo(orderNo);
        // 修改订单优惠总金额
        order.setDiscountTotalAmount(order.getDiscountTotalAmount().subtract(totalDiscountAmount));
        // 修改订单支付金额
        order.setPayableAmount(order.getPayableAmount().subtract(totalSalesAmount));
        // 修改订单应付金额
        order.setPaidAmount(order.getPaidAmount().subtract(totalSalesAmount));
        // 修改订单活动优惠金额
        order.setActivityDiscountAmount(order.getActivityDiscountAmount().subtract(totalActivityDiscountAmount));
        // 修改订单卡券优惠金额
        order.setCouponDiscountAmount(order.getCouponDiscountAmount().subtract(totalCouponDiscountAmount));
        // 修改订单手动改价优惠金额
        order.setManualDiscountAmount(order.getManualDiscountAmount().subtract(totalManualDiscountAmount));
        // 修改订单总金额
        order.setTotalOrderAmount(order.getTotalOrderAmount().subtract(totalListedPrice));
        orderDao.updateById(order);
        // 记录日志
        addLog(orderNo, "删除商品", "删除商品:\"" + goodsOrderItemDao.selectById(itemId).getGoodsName() + "\"");
    }

    /**
     * 中台订单详情修改订单状态
     *
     * @param orderStatus
     * @param orderNo
     */
//    @TxcTransaction
    @GlobalTransactional
    public void updateOrderStatus(Long orderStatus, String orderNo) {
        // 查询原订单状态
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        Long oldStatus = orderInfoInDto.getOrderStatus();
        // 校验传入的状态是否可以进行编辑
        if (orderStatus == 3 || orderStatus == 4) {
            throw new BusinessException("该状态不可进行手动修改");
        }

        // 判断要修改状态是否和数据库一致
        if (orderStatus.equals(oldStatus)) {
            return;
        }
        UpdateWrapper<OrderRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", orderNo);
        if(oldStatus == 0){
            if(orderStatus.intValue() != 1 && orderStatus.intValue() != 2){
                throw new BusinessException("该订单不可修改此状态");
            }
            //查看活动
            MallActivity mallActivity = null;
            if(orderInfoInDto.getActivityId() != null){
                Result<MallActivity> mallActivityById = marketingClient.getMallActivityByIdOpen(orderInfoInDto.getActivityId());
                if (mallActivityById == null || !mallActivityById.getSuccess() || mallActivityById == null) {
                    throw new BusinessException("活动不存在");
                }
                mallActivity = mallActivityById.getData();
            }
            List<OrderGoodsInfoVO> orderGoodsInfoVOS = orderUtil.getOrderGoodsInfoVOS(orderNo);
            if(mallActivity == null || mallActivity.getActivityType() != 7){
                orderUtil.reduceChannelGoodsInventory(orderGoodsInfoVOS,false);
            }
            // 订单活动库存删减
            if(mallActivity != null){
                orderUtil.activityDeductionStock(orderGoodsInfoVOS,false);
            }

            //八年员工内购资格
            if(mallActivity != null && (mallActivity.getActivityType() == 7 || mallActivity.getActivityType() == 8)){
                //恢复其购买资格
                jobOrderRecordMapper.rollbackByOrderId(orderInfoInDto.getId());
            }

            if(orderStatus.intValue() == 1){
                updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.UNCONFIRMED.getCode());
                updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.TO_BE_CONFIRMED.getCode());
            }
            if(orderStatus.intValue() == 2){
                updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.ACKNOWLEDGED.getCode());
                updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
            }
        }else if (oldStatus == 1) {
            switch (orderStatus.intValue()) {
                case 0:
                    // 添加库存
                    orderUtil.cancelOrder(orderNo, null, false);
                    // 修改订单状态为已取消
//                    if (orderInfoInDto.getPayType() == 2 && orderInfoInDto.getSyncStatus() == 2){
//                        throw new BusinessException("订单已抓单，不可取消订单");
//                    }
                    updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.CANCELED.getCode());
                    updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.CANCELED.getCode());
//                    updateWrapper.set("pay_status", 1);
//                    System.out.println(orderInfoInDto.getPayStatus());
//                    System.out.println(Objects.equals(orderInfoInDto.getPayStatus(),OrderConstant.PayStatusEnum.NOT_PAY.getCode()));
                    if(orderInfoInDto.getPayStatus() != null && Objects.equals(orderInfoInDto.getPayStatus(),OrderConstant.PayStatusEnum.NOT_PAY.getCode())){
                        updateWrapper.set("customer_name", null);
                        updateWrapper.set("id_card", null);
                        updateWrapper.set("id_card_portrait_url", null);
                        updateWrapper.set("id_card_emblem_url", null);
                    }
                    break;
                case 2:
                    // 修改订单状态为已确认
                    updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.ACKNOWLEDGED.getCode());
                    updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.TO_BE_DELIVERED.getCode());
                    // 修改支付状态为已支付
                    updateWrapper.set("pay_status", 2);
                    break;
                default:
                    throw new BusinessException("该订单不可修改此状态");
            }
        } else if (oldStatus == 2) {
            // 添加库存
            orderUtil.cancelOrder(orderNo, null, false);
            // 修改订单状态为已取消
            updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.CANCELED.getCode());
            updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.CANCELED.getCode());
//            updateWrapper.set("pay_status", 1);
            if(orderInfoInDto.getPayStatus() != null && Objects.equals(orderInfoInDto.getPayStatus(),OrderConstant.PayStatusEnum.NOT_PAY.getCode())){
                updateWrapper.set("customer_name", null);
                updateWrapper.set("id_card", null);
                updateWrapper.set("id_card_portrait_url", null);
                updateWrapper.set("id_card_emblem_url", null);
            }
        } else if (oldStatus == 4) {
            if (orderStatus.intValue() == 5) {
                // 修改订单状态为已收货
                updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.RECEIVED.getCode());
                updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.COMMENT.getCode());
                updateWrapper.set("confirm_receipt_time", new Date());
                //收货发放积分成长值
                UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
                OperationOrderInDto operationOrderInDto = new OperationOrderInDto();
                operationOrderInDto.setOrderNo(orderNo);
                operationOrderInDto.setOperatorId(userAuthor.getUserId());
                operationOrderInDto.setOperatorName(userAuthor.getFirstName());
                operationOrderInDto.setUserId(orderInfoInDto.getBuyUserId());

                this.editGoodsOrderItem(operationOrderInDto);
            } else {
                throw new BusinessException("该订单不可修改此状态");
            }
        } else if (oldStatus == 5) {
            if (orderStatus.intValue() == 6) {
                updateWrapper.set("order_status", OrderConstant.OrderStatusEnum.FINISHED.getCode());
                updateWrapper.set("customer_order_status", OrderConstant.CustomerOrderStatusEnum.EVALUATED.getCode());
                updateWrapper.set("completion_time" , new Date());
                // 判断订单是否存在赠品
                List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectAllByOrderNo(orderNo);
//                List<Long> orderItemIds = goodsOrderItems.stream().filter(res -> res.getSubGoodsType() == 2 && null != res.getPointProductId()).map(GoodsOrderItem::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(goodsOrderItems)) {
                    for (GoodsOrderItem item : goodsOrderItems) {
                        //创建积分商城订单
                        Integer pointOrderCreateFlag = 2;
                        String text = "系统转单异常";
                        try {
                            Result<String> result = orderUtil.cratePointShopOrder(orderNo, "Z", item.getId());
                            if (null != result) {
                                if (result.getSuccess()) {
                                    pointOrderCreateFlag = 1;
                                    text = "积分商城订单号：\"" + result.getData() + "\"";
                                    goodsOrderItemDao.update(null, new UpdateWrapper<GoodsOrderItem>()
                                            .eq("id", item.getId())
                                            .set("description", text)
                                            .set("order_item_status", 4));
                                } else {
                                    text = result.getMsg();
                                }
                            }else{
                                text = text + "异常原因：创建积分订单返回数据为空！";
                            }
//                            addLog(orderNo, "系统转单", text);
//                            // 获取订单下所有赠品
                        } catch (Exception e) {
                            text = text + "异常原因：" + e.getMessage();
                            log.error("系统转单出现异常，异常原因：{}，订单号：{}，行id：{}，商品名称：{}",e.getMessage(),orderNo,item.getId(),item.getGoodsName());
//                        throw new BusinessException("转单异常:" + e.getMessage());
                        }
                        addLog(orderNo, "系统转单", text);
                        //更新积分订单创建
                        goodsOrderItemDao.updatePointOrderCreateInfo(item.getId(),pointOrderCreateFlag,text);
                    }
                }
            } else {
                throw new BusinessException("该订单不可修改此状态");
            }
        } else {
            throw new BusinessException("该订单不可修改此状态");
        }


        // 进行更新订单状态操作
        orderDao.update(null, updateWrapper);


        String oldStatusStr = "";
        switch (oldStatus.intValue()) {
            case 0:
                oldStatusStr = "已取消";
                break;
            case 1:
                oldStatusStr = "未确认";
                break;
            case 2:
                oldStatusStr = "已确认";
                break;
            case 3:
                oldStatusStr = "部分发货";
                break;
            case 4:
                oldStatusStr = "已发货";
                break;
            case 5:
                oldStatusStr = "已收货";
                break;
            case 6:
                oldStatusStr = "已完成";
                break;
            default:
                throw new BusinessException("未知订单头状态");
        }

        String newOrderStatusDescr = "";
        switch (orderStatus.intValue()) {
            case 0:
                newOrderStatusDescr = "已取消";
                break;
            case 1:
                newOrderStatusDescr = "未确认";
                break;
            case 2:
                newOrderStatusDescr = "已确认";
                break;
            case 3:
                newOrderStatusDescr = "部分发货";
                break;
            case 4:
                newOrderStatusDescr = "已发货";
                break;
            case 5:
                newOrderStatusDescr = "已收货";
                break;
            case 6:
                newOrderStatusDescr = "已完成";
                break;
            default:
                throw new BusinessException("订单状态不存在");
        }

        // 根据订单状态校验明细状态
        Long orderItemStatus = null;
        switch (orderStatus.intValue()) {
            case 0:
                orderItemStatus = 0L;
                break;
            case 1:
                orderItemStatus = 1L;
                break;
            case 2:
                orderItemStatus = 2L;
                break;
            case 3:
                break;
            case 4:
                orderItemStatus = 3L;
                break;
            case 5:
                orderItemStatus = 5L;
                break;
            case 6:
                orderItemStatus = 6L;
                break;
            default:
                throw new BusinessException("状态不存在");
        }

        // 修改订单明细状态
        if (null != orderItemStatus) {
            goodsOrderItemDao.update(null, new UpdateWrapper<GoodsOrderItem>()
                    .eq("order_no", orderNo)
                    .eq("is_deleted", 0)
                    .ne("sub_goods_type", 2)
                    .set("order_item_status", orderItemStatus)
            );
        }

        //取消发送消息通知云管理订单
        if(StringUtils.equalsIgnoreCase("已取消",newOrderStatusDescr) && orderInfoInDto.getCloudOrderNo() != null){
            UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderNo", orderInfoInDto.getOrderNo());
            jsonObject.put("orderId", orderInfoInDto.getCloudOrderNo());
            jsonObject.put("userId", userAuthor.getUserId());
            jsonObject.put("firstName", userAuthor.getFirstName());
            orderCancelChannel.send(MessageBuilder.withPayload(jsonObject).build());
        }

        // 添加日志
        addLog(orderNo, "修改订单状态", "把订单状态从：\"" + oldStatusStr + "\"改成：\"" + newOrderStatusDescr + "\"");
        if (orderStatus == 6) {
            // 更新订单商品销量
            List<OrderItemCodeAndNumDto> orderItemCodeAndNumDtos = goodsOrderItemDao.findAllByOrderNo(orderNo);
            Result result = productClient.updateSell(orderItemCodeAndNumDtos);
            if(!result.getSuccess()){
                throw new BusinessException(result.getMsg());
            }
        }

    }

    @Transactional
    public void updateOrderInvoice(OrderInvoice orderInvoice) {
        // 查询订单发票
        OrderInvoice invoice = orderInvoiceDao.selectByOrderNo(orderInvoice.getOrderNo());
        List<String> desc = new ArrayList<>();
        String operationType = ""; // 操作类型
        if (null != invoice) {
            // 对比修改了那些字段
            if (null != orderInvoice.getInvoiceType() && !invoice.getInvoiceType().equals(Optional.ofNullable(orderInvoice.getInvoiceType()).orElse(null))) {
                String newInvoiceType = "";
                switch (orderInvoice.getInvoiceType().intValue()) {
                    case 0:
                        newInvoiceType = "不开发票";
                        break;
                    case 1:
                        newInvoiceType = "增值税普通发票-电子";
                        break;
                    case 2:
                        newInvoiceType = "增值税普通发票-纸质";
                        break;
                    case 3:
                        newInvoiceType = "增值税专用发票-电子";
                        break;
                    case 4:
                        newInvoiceType = "增值税专用发票-纸质";
                        break;
                    default:
                        throw new BusinessException("状态不存在");
                }
                String oldInvoiceType = "";
                switch (invoice.getInvoiceType().intValue()) {
                    case 0:
                        oldInvoiceType = "不开发票";
                        break;
                    case 1:
                        oldInvoiceType = "增值税普通发票-电子";
                        break;
                    case 2:
                        oldInvoiceType = "增值税普通发票-纸质";
                        break;
                    case 3:
                        oldInvoiceType = "增值税专用发票-电子";
                        break;
                    case 4:
                        oldInvoiceType = "增值税专用发票-纸质";
                        break;
                    default:
                        throw new BusinessException("状态不存在");
                }
                desc.add("把发票类型" + "从：\"" + oldInvoiceType + "\"改为：\"" + newInvoiceType + "\"");
            }
            if (null != invoice.getRiseType() && !invoice.getRiseType().equals(Optional.ofNullable(orderInvoice.getRiseType()).orElse(null))) {
                String newRiseType = "";
                if (orderInvoice.getRiseType() == 0) {
                    newRiseType = "个人";
                }
                if (orderInvoice.getRiseType() == 1) {
                    newRiseType = "企业";
                }
                String oldRiseType = "";
                if (invoice.getRiseType() == 0) {
                    oldRiseType = "个人";
                }
                if (invoice.getRiseType() == 1) {
                    oldRiseType = "企业";
                }
                desc.add("把抬头类型从：\"" + oldRiseType + "\"改为：\"" + newRiseType + "\"");
            }
            if (null != invoice.getRiseName() && !invoice.getRiseName().equals(Optional.ofNullable(orderInvoice.getRiseName()).orElse(null))) {
                desc.add("把抬头名称从：\"" + invoice.getRiseName() + "\"改为：\"" + orderInvoice.getRiseName() + "\"");
            }
            if (null == invoice.getDutyParagraph() && orderInvoice.getDutyParagraph() != null) {
                desc.add("新增税号为：" + orderInvoice.getDutyParagraph());
            }
            if (null != invoice.getDutyParagraph() && !invoice.getDutyParagraph().equals(Optional.ofNullable(orderInvoice.getDutyParagraph()).orElse(null))) {
                desc.add("把税号从：\"" + invoice.getDutyParagraph() + "\"改为：\"" + orderInvoice.getDutyParagraph() + "\"");
            }
            if (null == invoice.getProvinceName() && orderInvoice.getProvinceName() != null) {
                desc.add("新增省名称为：\"" + orderInvoice.getProvinceName() + "\"");
            }
            if (null != invoice.getProvinceName() && !invoice.getProvinceName().equals(Optional.ofNullable(orderInvoice.getProvinceName()).orElse(null))) {
                desc.add("把省名称从：\"" + invoice.getProvinceName() + "\"改为：\"" + orderInvoice.getProvinceName() + "\"");
            }
            if (null == invoice.getCityName() && orderInvoice.getCityName() != null) {
                desc.add("新增市名称为：\"" + orderInvoice.getCityName() + "\"");
            }
            if (null != invoice.getCityName() && !invoice.getCityName().equals(Optional.ofNullable(orderInvoice.getCityName()).orElse(null))) {
                desc.add("把市名称从：\"" + invoice.getCityName() + "\"改为：\"" + orderInvoice.getCityName() + "\"");
            }
            if (null == invoice.getAreaName() && orderInvoice.getAreaName() != null) {
                desc.add("新增区名称为：\"" + orderInvoice.getAreaName() + "\"");
            }
            if (null != invoice.getAreaName() && !invoice.getAreaName().equals(Optional.ofNullable(orderInvoice.getAreaName()).orElse(null))) {
                desc.add("把区名称从：\"" + invoice.getAreaName() + "\"改为：\"" + orderInvoice.getAreaName() + "\"");
            }
            if (null == invoice.getAddress() && orderInvoice.getAddress() != null) {
                desc.add("新增单位详细地址为：\"" + orderInvoice.getAddress() + "\"");
            }
            if (null != invoice.getAddress() && !invoice.getAddress().equals(Optional.ofNullable(orderInvoice.getAddress()).orElse(null))) {
                desc.add("把单位详细地址从：\"" + invoice.getAddress() + "\"改为：\"" + orderInvoice.getAddress() + "\"");
            }
            if (null == invoice.getPhone() && orderInvoice.getPhone() != null) {
                desc.add("新增电话号码为：\"" + orderInvoice.getPhone() + "\"");
            }
            if (null != invoice.getPhone() && !invoice.getPhone().equals(Optional.ofNullable(orderInvoice.getPhone()).orElse(null))) {
                desc.add("把电话号码从：\"" + invoice.getPhone() + "\"改为：\"" + orderInvoice.getPhone() + "\"");
            }
            if (null == invoice.getBankAccountName() && orderInvoice.getBankAccountName() != null) {
                desc.add("新增开户银行为：\"" + orderInvoice.getBankAccountName() + "\"");
            }
            if (null != invoice.getBankAccountName() && !invoice.getBankAccountName().equals(Optional.ofNullable(orderInvoice.getBankAccountName()).orElse(null))) {
                desc.add("把开户银行从：\"" + invoice.getBankAccountName() + "\"改为：\"" + orderInvoice.getBankAccountName() + "\"");
            }
            if (null == invoice.getBankAccount() && orderInvoice.getBankAccount() != null) {
                desc.add("新增银行账户：\"" + orderInvoice.getBankAccount() + "\"");
            }
            if (null != invoice.getBankAccount() && !invoice.getBankAccount().equals(Optional.ofNullable(orderInvoice.getBankAccount()).orElse(null))) {
                desc.add("把银行账户从：\"" + invoice.getBankAccount() + "\"改为：\"" + orderInvoice.getBankAccount() + "\"");
            }
            orderInvoiceDao.updateByPrimaryKeySelective(orderInvoice);
            operationType = "修改发票信息";
        } else {
            orderInvoiceDao.insert(orderInvoice);
            operationType = "新增发票信息";
            desc.add("新增发票信息");
        }
        if (!CollectionUtils.isEmpty(desc)) {
            // 添加操作日志
            addLog(orderInvoice.getOrderNo(), operationType, String.join(",", desc));
        }
    }

    /**
     * 57491-【方太商城】以旧换新订单的发票抬头变更
     * @param orderNo 订单编号
     * @param riseName 发票抬头
     */
    @Transactional
    public void updateOrderInvoiceOldSupple(String orderNo,String riseName) {
        // 获取用户信息
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        // 查询订单发票
        OrderInvoice invoice = orderInvoiceDao.selectByOrderNo(orderNo);
        log.error("更新发票信，原发票信息：{}",JSON.toJSONString(invoice));
        log.error("更新发票信，订单号：{}，跟新名称：{}",orderNo,riseName);
        List<String> desc = new ArrayList<>();
        String operationType = ""; // 操作类型
        if (null != invoice) {
            if (null != invoice.getRiseName() && !invoice.getRiseName().equals(Optional.ofNullable(riseName).orElse(null))) {
                desc.add("把抬头名称从：\"" + invoice.getRiseName() + "\"改为：\"" + riseName + "\"");
            }
            OrderInvoice orderInvoice = new OrderInvoice();
            orderInvoice.setId(invoice.getId());
            orderInvoice.setRiseName(riseName);
            orderInvoice.setModifiedDate(new Date());
            orderInvoice.setModifiedBy("system");
            // 修改订单发票
            orderInvoiceDao.updateByPrimaryKeySelective(orderInvoice);
            operationType = "以旧换新修改发票信息";
        }
        if (!CollectionUtils.isEmpty(desc)) {
            // 创建操作日志
            addMobileLog(orderNo, customerInfo.getName(), customerInfo.getId(), operationType, String.join(",", desc), null);
        }
    }

    /**
     * 新增订单行
     *
     * @param dto
     */
    @Transactional
    public void addOrderLineInfo(AddOrderLineInfoInDto dto) {
        if (null == dto || null == dto.getOrderNo() || null == dto.getChannelGoodsId() || null == dto.getPrice()) {
            throw new BusinessException("参数错误!");
        }
        OrderRecord record = orderDao.selectByOrderNo(dto.getOrderNo());
        if (null == record) {
            throw new BusinessException("订单信息不存在!");
        }
        Result<FindProductByChannelGoodsIdVo> result = productClient.findProductByChannelGoodsId(dto.getChannelGoodsId());
        if (null != result) {
            if (result.getSuccess()) {
                try {
                    FindProductByChannelGoodsIdVo vo = result.getData();
                    if (vo.getListedPrice().compareTo(dto.getPrice()) == -1) {
                        throw new BusinessException("销售金额不能大于挂牌价：" + vo.getListedPrice());
                    }
                    BigDecimal discountAmount = vo.getListedPrice().subtract(dto.getPrice());
                    GoodsOrderItem goodsOrderItem = new GoodsOrderItem();
                    goodsOrderItem.setOrderNo(dto.getOrderNo());
                    goodsOrderItem.setBuyNum(1);

                    goodsOrderItem.setStoreId(record.getStoreId());
                    goodsOrderItem.setStoreName(record.getStoreName());
                    goodsOrderItem.setGoodsPrice(dto.getPrice());
                    goodsOrderItem.setSalesAmount(dto.getPrice());

                    goodsOrderItem.setSpecificationId(vo.getSpecificationId());
                    goodsOrderItem.setProductTypes(vo.getGoodsType());
                    goodsOrderItem.setPayAmount(dto.getPrice());
                    goodsOrderItem.setPaidAmount(dto.getPrice());
                    if (vo.getGoodsType() != 2) {
                        goodsOrderItem.setManualDiscountAmount(discountAmount);
                        goodsOrderItem.setDiscountAmount(discountAmount);
                    }
                    BeanUtils.copyProperties(vo, goodsOrderItem);
                    if (goodsOrderItem.getGoodsCategoryId() == 13) {
                        List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectItemByOrderNo(record.getOrderNo());
                        if (CollectionUtils.isEmpty(goodsOrderItems)
                                || CollectionUtils.isEmpty(goodsOrderItems.stream().filter(obj -> obj.getSubGoodsType() == 1).collect(Collectors.toList()))) {
                            throw new BusinessException("无法直接添加赠品,需先添加主品！");
                        }
                        GoodsOrderItem goodsOrderItemMian = goodsOrderItems.stream().filter(obj -> obj.getSubGoodsType() == 1).limit(1).collect(Collectors.toList()).get(0);
                        goodsOrderItem.setParentItemId(goodsOrderItemMian.getId());
                        goodsOrderItem.setSubGoodsType(2L);
                        goodsOrderItem.setPointProductId(vo.getPointGoodsId());
                        goodsOrderItem.setOrderItemStatus(1L);
                    } else {
                        goodsOrderItem.setOrderItemStatus(record.getOrderStatus() == 4 ? 3 : record.getOrderStatus());
                        goodsOrderItem.setSubGoodsType(1L);
                    }
                    goodsOrderItemDao.insertSelective(goodsOrderItem);
                    if (!CollectionUtils.isEmpty(vo.getGroupProducts())) {
                        BigDecimal afterDiscountAmount = discountAmount;
                        for (int i = 0; i < vo.getGroupProducts().size(); i++) {
                            FindProductByChannelGoodsIdVo group = vo.getGroupProducts().get(i);
                            BigDecimal afterAmount = BigDecimal.ZERO;
                            if (vo.getGroupProducts().size() - 1 == i) {
                                afterAmount = afterDiscountAmount;
                            } else {
                                afterAmount = imputedPrice(goodsOrderItem.getListedPrice(), group.getListedPrice(), discountAmount);
                                afterDiscountAmount = afterDiscountAmount.subtract(afterAmount);

                            }
                            GoodsOrderItem item = new GoodsOrderItem();
                            //先填充优惠金额
                            item.setDiscountAmount(afterAmount);
                            item.setManualDiscountAmount(afterAmount);
                            //计算优惠后金额
                            afterAmount = group.getListedPrice().subtract(afterAmount);
                            item.setOrderNo(dto.getOrderNo());
                            item.setBuyNum(1);
                            item.setSubGoodsType(1L);
                            item.setParentItemId(goodsOrderItem.getId());
                            item.setStoreId(record.getStoreId());
                            item.setStoreName(record.getStoreName());
                            item.setGoodsPrice(afterAmount);
                            item.setSalesAmount(afterAmount);
                            item.setOrderItemStatus(record.getOrderStatus() == 4 ? 3 : record.getOrderStatus());
                            item.setSpecificationId(group.getSpecificationId());
                            item.setProductTypes(group.getGoodsType());
                            item.setPayAmount(afterAmount);
                            item.setPaidAmount(afterAmount);
                            BeanUtils.copyProperties(group, item);
                            goodsOrderItemDao.insertSelective(item);
                        }
                    }

                    addLog(dto.getOrderNo(), "新增订单行", "新增商品:" + goodsOrderItem.getGoodsName());
                    //新增促销信息
                    if (discountAmount.compareTo(BigDecimal.ZERO) == 1) {
                        GoodsOrderPromotion goodsOrderPromotion = new GoodsOrderPromotion();
                        goodsOrderPromotion.setOrderNo(dto.getOrderNo());
                        goodsOrderPromotion.setPromotionType(0L);
                        goodsOrderPromotion.setGoodsCode(goodsOrderItem.getGoodsCode());
                        goodsOrderPromotion.setGoodsName(goodsOrderItem.getGoodsName());
                        goodsOrderPromotion.setGoodsModel(goodsOrderItem.getGoodsModel());
                        goodsOrderPromotion.setDiscountAmount(discountAmount);

                        goodsOrderPromotion.setDescription("新增订单行差价优惠");
                        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
                        goodsOrderPromotion.setCreatedByName(userAuthor.getFirstName());
                        goodsOrderPromotion.setModifiedByName(userAuthor.getFirstName());
                        goodsOrderPromotion.setIsDeleted(0L);
                        goodsOrderPromotion.setCreatedBy(userAuthor.getUserId());
                        goodsOrderPromotion.setCreatedDate(new Date());
                        goodsOrderPromotion.setModifiedBy(userAuthor.getUserId());
                        goodsOrderPromotion.setModifiedDate(new Date());
                        goodsOrderPromotionDao.insert(goodsOrderPromotion);
                        addLog(dto.getOrderNo(), "手工改价", "手工改价【" + goodsOrderItem.getGoodsName() + "】差价优惠：" + discountAmount);
                    }
                    record.setTotalOrderAmount(goodsOrderItem.getListedPrice().add(record.getTotalOrderAmount()));
                    record.setDiscountTotalAmount((null == record.getDiscountTotalAmount() ? BigDecimal.ZERO : record.getDiscountTotalAmount()).add(discountAmount));
                    record.setPayableAmount(dto.getPrice().add(record.getPayableAmount()));
                    record.setPaidAmount(dto.getPrice().add(record.getPaidAmount()));
                    record.setManualDiscountAmount((null == record.getManualDiscountAmount() ? BigDecimal.ZERO : record.getManualDiscountAmount()).add(discountAmount));
                    addLog(dto.getOrderNo(), "新增订单行", "更新订单总金额:" + record.getPayableAmount());
                    orderDao.updateById(record);

                    return;
                } catch (BusinessException e) {
                    throw new BusinessException(e.getMessage());
                } catch (Exception e) {
                    log.error("新增异常", e);
                    throw new BusinessException("新增异常！");
                }
            } else {
                throw new BusinessException(result.getMsg());
            }
        }
        throw new BusinessException("远程调用异常!");
    }

    public void addLog(String orderNo, String operationType, String description) {
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderNo);
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        orderOperationLog.setOperationByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedBy(userAuthor.getUserId());
        orderOperationLog.setOperationType(operationType);
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(description);
        orderOperationLog.setIsDeleted(0L);
        orderOperationLog.setCreatedDate(new Date());
        orderOperationLogDao.insert(orderOperationLog);
    }


    public void addMobileLog(String orderNo, String customerName, Long customerId, String operationType,
                             String description, String file) {
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderNo);
        orderOperationLog.setOperationByName(customerName);
        orderOperationLog.setOperationType(operationType);
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(description);
        orderOperationLog.setFile(file);
        orderOperationLog.setCreatedByName(customerName);
        orderOperationLog.setModifiedByName(customerName);
        orderOperationLog.setIsDeleted(0L);
        orderOperationLog.setCreatedBy(customerId + "");
        orderOperationLog.setCreatedDate(new Date());
//        orderOperationLog.setModifiedBy();
//        orderOperationLog.setModifiedDate();
        orderOperationLogDao.insert(orderOperationLog);
    }


    public void addLog1(String orderNo, String operationType, String description, String file) {
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderNo);
        orderOperationLog.setOperationByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedByName(userAuthor.getFirstName());
        orderOperationLog.setCreatedBy(userAuthor.getId());
        orderOperationLog.setOperationType(operationType);
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(description);
        orderOperationLog.setIsDeleted(0L);
        orderOperationLog.setFile(file);
        orderOperationLog.setCreatedDate(new Date());
        orderOperationLogDao.insert(orderOperationLog);
    }

    /**
     * 对不两个类不同的属性值
     *
     * @param obj1  修改前
     * @param obj2  修改后
     * @param clazz
     * @return
     */
    public List<String> compareFields(Object obj1, Object obj2, Class clazz) {
        //将需要比对的数据转换成map
        Map<String, Object> map1 = BeanUtil.beanToMap(obj1);
        Map<String, Object> map2 = BeanUtil.beanToMap(obj2);

        //获取转换的字段的数据
        List<Field> fieldList = new ArrayList<>();

        for (Field field : clazz.getDeclaredFields()) {
            fieldList.add(field);
        }

        for (Field field : clazz.getSuperclass().getDeclaredFields()) {
            fieldList.add(field);
        }

        List<String> desrList = new ArrayList<>();

        map1.forEach((map1Key, map1Value) -> {
            map2.forEach((map2Key, map2Value) -> {
                String name = "";
                for (Field field : fieldList) {
                    if (Objects.equals(map2Key, field.getName())) {
                        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                        name = annotation.value();
                        break;
                    }
                }
                if (Objects.equals(map1Key, map2Key)) {
                    if (null != map1Value && null != map2Value && !Objects.equals(map1Value, map2Value)) {
                        desrList.add("把" + name + "从:" + map1Value.toString() + "改为:" + map2Value.toString());
                    }
                }
            });
        });
        return desrList;
    }

    @Transactional
    public void editRemark(EditRemarkDto dto) {
        OrderRecord record = orderDao.selectByOrderNo(dto.getOrderNo());
        if (null == record) {
            throw new BusinessException("订单信息不存在!");
        }
        try {
            UpdateWrapper wrapper = new UpdateWrapper();
            wrapper.set("remark", dto.getRemark());
            wrapper.eq("order_no", dto.getOrderNo());
            orderDao.update(null, wrapper);
            if (null == record.getRemark()) {
                addLog(dto.getOrderNo(), "添加订单备注", dto.getRemark());
            } else {
                addLog(dto.getOrderNo(), "更新订单备注", record.getRemark() + "更新为 :" + dto.getRemark());
            }
        } catch (Exception e) {
            throw new BusinessException("更新异常!");
        }
    }


    /**
     * 中台订单详情编辑商品总价
     *
     * @param editDto
     */
    @Transactional
    public void editTotalPriceOfGoods(EditTotalPriceOfGoodsInDto editDto) {
        String orderNo = editDto.getOrderNo();
        // 查询订单下除赠品外所有商品
        List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectItemByOrderNo(orderNo);

        OrderRecord order = orderDao.findOrderRecordByOrderNo(orderNo);

        if (order.getPayStatus() == 0) {
            //删除支付缓存
            paymentService.delPayInfo(orderNo);
        }

        BigDecimal discountTotalAmount = order.getDiscountTotalAmount();

        // 过滤组合商品主品/虚拟商品/赠品
        List<GoodsOrderItem> orderItems = goodsOrderItems.stream().filter(res -> {
            if (res.getProductTypes() == 3 || (res.getParentItemId() == null && res.getProductTypes() == 2) || res.getSubGoodsType() == 2 || res.getSubGoodsType() == 3 || res.getSubGoodsType() == 4) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        // 获取商品核算价格
        List<ChannelGoodsMerchandising> channelGoodsMerchandisings = productClient.getChannelGoodsAccountingPrice(new ArrayList<String>(orderItems.stream().map(res -> res.getGoodsCode()).collect(Collectors.toList()))).getData();

        // 获取商品总金额
        BigDecimal itemTotalSalesAmount = orderItems.stream().map(GoodsOrderItem::getSalesAmount).reduce(BigDecimal::add).get();
        // 要优惠的金额
        BigDecimal discountAmount = editDto.getDiscountAmount();

        BigDecimal totalFareAmount = BigDecimal.ZERO;

        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        for (int i = 0; i < orderItems.size(); i++) {

            GoodsOrderItem item = orderItems.get(i);

            //定义每次分摊后的总优惠
            BigDecimal fareAmount = BigDecimal.ZERO;

            // 计算优惠应该分摊的百分比：当前sku售价*购买数量 /商品总金额
            BigDecimal scale = item.getGoodsPrice().multiply(new BigDecimal(item.getBuyNum() + ""))
                    .divide(itemTotalSalesAmount, 6, BigDecimal.ROUND_HALF_UP);

            //当前商品分摊到的优惠
            fareAmount = discountAmount.multiply(scale);

            // 乘以数量再除以数量，保留两位小数，减少误差
            BigDecimal finalDiscount = fareAmount.divide(new BigDecimal(item.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(item.getBuyNum())).setScale(2, BigDecimal.ROUND_HALF_DOWN);

            // 记录总分摊到的优惠
            totalFareAmount = totalFareAmount.add(finalDiscount);

            // 最后一次分摊到的优惠 = 总优惠-前(n-1)个商品分摊到的优惠
            if (i == orderItems.size() - 1) {
                finalDiscount = finalDiscount.add(discountAmount.subtract(totalFareAmount).setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }

            // 当前商品分摊到的最终优惠
            item.setManualDiscountAmount(finalDiscount); // 当前商品乘以数量的优惠金额
            // 当前商品优惠后销售单价金额
            BigDecimal afterSalesAmount = item.getSalesAmount().subtract(finalDiscount);

            // 判断是否小于核算金额
            if (!CollectionUtils.isEmpty(channelGoodsMerchandisings)) {
                for (ChannelGoodsMerchandising mer : channelGoodsMerchandisings) {
                    if (mer.getGoodsCode().equals(item.getGoodsCode())) {
                        if (afterSalesAmount.compareTo(mer.getAccountingPrice() == null ? BigDecimal.ZERO : mer.getAccountingPrice()) == -1) {
                            throw new BusinessException(item.getGoodsName() + "优惠后金额小于核算价格");
                        }
                    }
                }
            }

            item.setGoodsPrice(afterSalesAmount.divide(new BigDecimal(item.getBuyNum()), 2, BigDecimal.ROUND_HALF_DOWN));
            item.setSalesAmount(afterSalesAmount);
            item.setPaidAmount(afterSalesAmount);
            item.setPayAmount(afterSalesAmount);
            // 当前商品总的优惠金额
            item.setDiscountAmount(item.getDiscountAmount().add(finalDiscount));
            goodsOrderItemDao.updateById(item);

            // 新增促销信息
            GoodsOrderPromotion goodsOrderPromotion = new GoodsOrderPromotion();
            goodsOrderPromotion.setOrderNo(orderNo);
            goodsOrderPromotion.setPromotionType(0L);
            goodsOrderPromotion.setGoodsCode(orderItems.get(i).getGoodsCode());
            goodsOrderPromotion.setGoodsName(orderItems.get(i).getGoodsName());
            goodsOrderPromotion.setGoodsModel(orderItems.get(i).getGoodsModel());
            goodsOrderPromotion.setDiscountAmount(finalDiscount);
            String desc = "";
            if (null != editDto.getDescription() && !editDto.getDescription().isBlank()) {
                desc = editDto.getDescription();
            }
            if (null != editDto.getVoucher() && !editDto.getVoucher().isBlank()) {
                desc = "|" + String.join(",", editDto.getVoucher());
            }
            goodsOrderPromotion.setDescription(desc.isBlank() ? null : desc);
            goodsOrderPromotion.setIsDeleted(0L);
            goodsOrderPromotion.setCreatedDate(new Date());
            goodsOrderPromotion.setModifiedDate(new Date());
            goodsOrderPromotion.setCreatedByName(userAuthor.getFirstName());
            goodsOrderPromotion.setModifiedByName(userAuthor.getFirstName());
            goodsOrderPromotion.setModifiedBy(userAuthor.getUserId());
            goodsOrderPromotion.setCreatedBy(userAuthor.getUserId());
            goodsOrderPromotionDao.insert(goodsOrderPromotion);
        }

        // 更新组合商品价格
        updateGroupGoodsAmount(goodsOrderItems);
//        // 订单总金额
       // order.setTotalOrderAmount(order.getTotalOrderAmount().subtract(editDto.getDiscountAmount()));
        // 订单优惠总金额
        order.setDiscountTotalAmount(order.getDiscountTotalAmount().add(editDto.getDiscountAmount()));
        // 订单应付金额
        order.setPayableAmount(order.getPayableAmount().subtract(editDto.getDiscountAmount()));
        // 订单支付金额
        order.setPaidAmount(order.getPaidAmount().subtract(editDto.getDiscountAmount()));
        // 订单手工改价优惠总金额
        order.setManualDiscountAmount(order.getManualDiscountAmount().add(editDto.getDiscountAmount()));
        // 计算会员赠送积分
        Long memberGivePoints = computedMemberGivePointsWMS(order.getBuyUserId(), order.getPayableAmount());
        order.setMemberGiftPoints(memberGivePoints);
        // 计算总赠送积分
        order.setGiftPoints(memberGivePoints + Optional.ofNullable(order.getActivityGiftPoints()).orElse(0L));
        orderDao.updateById(order);
        //取消支付流水
        orderPayRecordDao.updateCancelStatusByOrderNo(orderNo,1);

        // 添加日志
        addLog(orderNo, "修改订单行总价", "把优惠金额从：\"" + discountTotalAmount + "\"改为:\"" + discountTotalAmount.add(editDto.getDiscountAmount()) + "\"");
    }

    /**
     * 更新组合商品价格
     *
     * @param goodsOrderItems
     */
    private void updateGroupGoodsAmount(List<GoodsOrderItem> goodsOrderItems) {
        for (GoodsOrderItem goodsOrderItem : goodsOrderItems) {
            if (goodsOrderItem.getProductTypes() == 2) {
                BigDecimal groupSalesAmount = BigDecimal.ZERO;
                BigDecimal groupDiscountAmount = BigDecimal.ZERO;
                BigDecimal groupActivityDiscountAmount = BigDecimal.ZERO;
                BigDecimal groupCouponDiscountAmount = BigDecimal.ZERO;
                BigDecimal groupManualDiscountAmount = BigDecimal.ZERO;
                BigDecimal groupGoodsPrice = BigDecimal.ZERO;
                for (GoodsOrderItem res : goodsOrderItems) {
                    if (null != res.getParentItemId() && res.getParentItemId().equals(goodsOrderItem.getId()) && res.getSubGoodsType() == 1) {
                        // 计算组合商品内金额
                        groupSalesAmount = groupSalesAmount.add(res.getSalesAmount());
                        groupDiscountAmount = groupDiscountAmount.add(res.getDiscountAmount());
                        groupActivityDiscountAmount = groupActivityDiscountAmount.add(res.getActivityDiscountAmount());
                        groupCouponDiscountAmount = groupCouponDiscountAmount.add(res.getCouponDiscountAmount());
                        groupManualDiscountAmount = groupManualDiscountAmount.add(Optional.ofNullable(res.getManualDiscountAmount()).orElse(BigDecimal.ZERO));
                        // 统计销售单价
                        groupGoodsPrice = groupGoodsPrice.add(res.getGoodsPrice());
                    }
                }
                // 更新组合商品金额
                goodsOrderItem.setGoodsPrice(groupGoodsPrice); // 组合商品销售单价
                goodsOrderItem.setDiscountAmount(groupDiscountAmount);
                goodsOrderItem.setSalesAmount(groupSalesAmount);
                goodsOrderItem.setPayAmount(groupSalesAmount);
                goodsOrderItem.setPaidAmount(groupSalesAmount);
                goodsOrderItem.setActivityDiscountAmount(groupActivityDiscountAmount);
                goodsOrderItem.setCouponDiscountAmount(groupCouponDiscountAmount);
                goodsOrderItem.setManualDiscountAmount(groupManualDiscountAmount);
                goodsOrderItemDao.updateById(goodsOrderItem);
            }
        }
    }

    public static BigDecimal imputedPrice(BigDecimal totalAmount, BigDecimal amount, BigDecimal discountAmount) {
        BigDecimal proportion = totalAmount.divide(amount, 2, RoundingMode.HALF_UP); // 占比
        // 要优惠金额
        BigDecimal discountsAmount = discountAmount.divide(proportion, 0, RoundingMode.HALF_UP);
        return discountsAmount;
    }

    public String createPointOrder(String orderNo, String type, Long itemId) {
        //创建积分商城订单
        List<GoodsOrderItem> goodsOrderItems = goodsOrderItemDao.selectAllByOrderNo(orderNo);
        if (null != itemId) {
            goodsOrderItems = goodsOrderItems.stream().filter(obj -> obj.getId().equals(itemId)).collect(Collectors.toList());
        }
        StringBuffer sb = new StringBuffer();
        List<Long> orderItemIds = goodsOrderItems.stream().filter(res -> res.getSubGoodsType() == 2 && res.getOrderItemStatus() == 1 && null != res.getPointProductId()).map(GoodsOrderItem::getId).collect(Collectors.toList());
        if (orderItemIds.size() > 0) {
            for (Long orderItemId : orderItemIds) {
                String text = "系统转单异常";
                try {
                    Result<String> result = orderUtil.cratePointShopOrder(orderNo, type, orderItemId);

                    if (null != result) {
                        if (result.getSuccess()) {
                            text = "积分商城订单号：\"" + result.getData() + "\"";
                            goodsOrderItemDao.update(null, new UpdateWrapper<GoodsOrderItem>()
                                    .eq("id", orderItemId)
                                    .set("description", "积分商城订单号：" + text)
                                    .set("order_item_status", 4));
                        } else {
                            text = result.getMsg();
                        }
                    }
                    addMobileLog(orderNo, "积分商城", null, "系统转单", text, null);
                    sb.append(itemId + "：" + text + "\n");
                } catch (Exception e) {
                    addMobileLog(orderNo, "积分商城", null, "系统转单", Optional.ofNullable(e.getMessage()).orElse("系统转单异常"), null);
                    throw new BusinessException("转单异常:" + e.getMessage());
                }
            }
            return sb.toString();
        }
        return null;
    }


    /**
     * 中台调用计算会员赠送积分
     * 0 < x <= 10000  = 2倍
     * 10000 < x <= 50000 = 4倍
     * 50000 < x <= 100000 = 五倍
     * x > 100000 = 6倍
     *
     * @return
     */
    public Long computedMemberGivePointsWMS(Long customerId, BigDecimal orderAmount) {
        // 获取当前成长值
        BigDecimal grow = getMemberGrow(customerId);
        BigDecimal givePoints = BigDecimal.ZERO;
        // 根据成长值计算积分
        if (grow.compareTo(BigDecimal.ZERO) >= 0 && grow.compareTo(new BigDecimal(20000)) <= 0) {
            // 成长值 >= 0 && 成长值 <= 10000
            givePoints = orderAmount.multiply(new BigDecimal(2));
        } else if (grow.compareTo(new BigDecimal(20000)) > 0 && grow.compareTo(new BigDecimal(50000)) <= 0) {
            // 成长值 > 10000 && 成长值 <= 50000
            givePoints = orderAmount.multiply(new BigDecimal(4));
        } else if (grow.compareTo(new BigDecimal(50000)) > 0 && grow.compareTo(new BigDecimal(80000)) <= 0) {
            // 成长值 > 50000 && 成长值 <= 100000
            givePoints = orderAmount.multiply(new BigDecimal(5));
        } else if (grow.compareTo(new BigDecimal(80000)) > 0) {
            // 成长值 > 100000
            givePoints = orderAmount.multiply(new BigDecimal(6));
        }
//        log.error("成长值：" + givePoints);
        return givePoints.setScale(2, RoundingMode.HALF_DOWN).longValue();
    }


    /**
     * 获取会员成长值
     *
     * @param customerId
     * @return
     */
    public BigDecimal getMemberGrow(Long customerId) {
        Result<GrowCustomer> growCustomerResult = pointClient.queryGrowCustomerByCustomerIdWMS(customerId);
        if (!growCustomerResult.getSuccess()) {
            throw new BusinessException("获取会员成长值失败！");
        }
        if (null == growCustomerResult.getData()) {
            return BigDecimal.ZERO;
        }
        return growCustomerResult.getData().getTotalIncome();
    }


    /**
     * 更新订单收获时间为客服手动确认收获时间
     * @param oldTime
     * @param newTime
     * @return
     */
    public String updateConfirmReceiptTime(Date oldTime , Date newTime){
        List<String> orderNoList =  orderDao.selectOrderNoConfirmReceiptTime(oldTime , newTime);
        if (orderNoList.size() > 0){
            List<OrderOperationLog> orderOperationLogs = orderOperationLogDao.selectOrderLogByOrderNos(orderNoList);
            for (String orderNo : orderNoList) {
                for (OrderOperationLog orderOperationLog : orderOperationLogs) {
                    if (orderNo.equals(orderOperationLog.getOrderNo())){
                        UpdateWrapper<OrderRecord> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("order_no" , orderNo);
                        updateWrapper.set("confirm_receipt_time" , orderOperationLog.getOperationTime());
                        orderDao.update(null ,updateWrapper);
                    }
                }
            }
        } else {
            return "暂无可以更新订单数据";
        }
        return "执行更新完成";
    }

    @Transactional
    public void compensationPoint(CompensationPointDto dto) {
        List<OrderRecord> orderListByOrderNoList = orderDao.getOrderListByOrderNoList(dto.getOrderNoList());
        if (!CollectionUtils.isEmpty(orderListByOrderNoList)) {
            orderListByOrderNoList.stream().forEach(orderInfoInDto -> {
                if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1) && null != orderInfoInDto.getPaidAmount() && orderInfoInDto.getPaidAmount().compareTo(new BigDecimal("0")) != -1) {
                    AlterGrowCustomerDto alterGrowCustomerDto = new AlterGrowCustomerDto();
                    alterGrowCustomerDto.setDirection(1);
                    alterGrowCustomerDto.setAvailable(orderInfoInDto.getPayableAmount());
                    alterGrowCustomerDto.setCustomerId(orderInfoInDto.getBuyUserId().toString());
                    alterGrowCustomerDto.setRemark("商城下单获得成长值");
                    alterGrowCustomerDto.setBillId(orderInfoInDto.getOrderNo());
                    alterGrowCustomerDto.setTypeCode("20");
                    alterGrowCustomerDto.setTypeName("商城下单送成长值");
                    alterGrowCustomerDto.setOrderNo(orderInfoInDto.getOrderNo());
                    pointService.sendGrow(alterGrowCustomerDto);
                }

                if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1) && null != orderInfoInDto.getActivityGiftPoints() && orderInfoInDto.getActivityGiftPoints() > 0) {
                    AlterPointDto alterPointDto = new AlterPointDto();
                    alterPointDto.setDirection(1);
                    alterPointDto.setAvailable(orderInfoInDto.getActivityGiftPoints());
                    alterPointDto.setTypeCode("20");
                    alterPointDto.setTypeName("下单赠送积分活动");
                    alterPointDto.setBillId(orderInfoInDto.getOrderNo());
                    alterPointDto.setSourceId(orderInfoInDto.getBuyUserId() + "");
                    alterPointDto.setOrderNo(orderInfoInDto.getOrderNo());
                    pointService.sendPoint(alterPointDto);
                }

                if (!Objects.equals(orderInfoInDto.getTradeInFlag(),1) && null != orderInfoInDto.getMemberGiftPoints() && orderInfoInDto.getMemberGiftPoints() > 0) {
                    AlterPointDto alterPointDto = new AlterPointDto();
                    alterPointDto.setDirection(1);
                    alterPointDto.setAvailable(orderInfoInDto.getMemberGiftPoints());
                    alterPointDto.setTypeCode("20");
                    alterPointDto.setTypeName("下单送积分");
                    alterPointDto.setBillId(orderInfoInDto.getOrderNo());
                    alterPointDto.setSourceId(orderInfoInDto.getBuyUserId() + "");
                    alterPointDto.setOrderNo(orderInfoInDto.getOrderNo());
                    pointService.sendPoint(alterPointDto);
                }
            });
        }
    }

    public PageInfo<OrderAfterSaleOperationLog> findOrderAfterSaleOperationLogById(Long id, Integer page, Integer size) {
        page = page == null ? 1 : page;
        size = size == null ? 10 : size;
        PageInfo<OrderAfterSaleOperationLog> pageInfo = new PageInfo<>(page, size);

        LambdaQueryWrapper<OrderAfterSaleOperationLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询条件 apply_id = id and is_deleted = 0
        lambdaQueryWrapper.eq(OrderAfterSaleOperationLog::getApplyId , id);
        lambdaQueryWrapper.eq(OrderAfterSaleOperationLog::getIsDeleted , 0);

        Long total = orderAfterSaleOperationLogDao.selectCount(lambdaQueryWrapper);
        List<OrderAfterSaleOperationLog> records = orderAfterSaleOperationLogDao.selectMyPage(id, pageInfo.getOffset(),size);

        pageInfo.setTotal(total).setRecords(records);
        return pageInfo;
    }

    /**
     * 根据订单号查询退换货操作日志
     * @param orderNo
     * @return
     */
    public List<OrderOperationLog> queryReturnLog(String orderNo) {
        return orderOperationLogDao.selectListByOrderNo(orderNo , "编辑退换货信息");
    }

    public boolean isFittingGoods(String orderNo) {
        List<String> goodsCodeList = goodsOrderItemDao.getGoodsCodeByOrderNo(orderNo);
        if(CollectionUtils.isNotEmpty(goodsCodeList)){
            List<String> data = cmsClient.findCategoryGoodsByCode(OrderConstant.channelCode, OrderConstant.channelCategoryName).getData();
            goodsCodeList.removeAll(data);
        }
        return CollectionUtils.isNotEmpty(goodsCodeList) ? false : true;
    }


    /**
     * 批量导入米博快递单号
     */
    @Transactional
    public Result<?> importMiBoOrder(MultipartFile file) {
        try {
            ImportExcelResultVo importExcelResultVo = validExcel(file);
            if(importExcelResultVo.isResult()){
                //插入数据
                List<ImportMiBoOrderVo> list = importExcelResultVo.getList();
                //根据商品编码查询订单明细信息，判断商品、赠品，
                if (CollectionUtils.isNotEmpty(list)) {
                    Set<String> orderNoSet = list.stream().map(l -> l.getOrderNo()).collect(Collectors.toSet());
                    List<String> orderNos = new ArrayList<>(orderNoSet);
                    List<GoodsOrderItem> orderItemList = goodsOrderItemDao.selectOrderItemByOrderNo(orderNos);
                    List<OrderDelivery> orderDeliveries = orderDeliveryDao.queryOrderDeliveryByOrderNos(orderNos);
                    List<OrderRecord> orderRecords = orderDao.getOrderListByOrderNoList(orderNos);

                    List<ImportMiBoOrderVo> updateBatchStatusVo = new ArrayList<>();
                    List<ImportMiBoOrderVo> insertList = new ArrayList<>();
                    for (String orderNo : orderNos) {
                        //订单信息
                        OrderRecord orderRecord = new OrderRecord();
                        Optional<OrderRecord> first1 = orderRecords.stream().filter(o -> o.getOrderNo().equals(orderNo)).findFirst();
                        if (first1.isPresent()){
                            orderRecord = first1.get();
                        }
                        //订单商品明细
                        List<GoodsOrderItem> goodsOrderItems = orderItemList.stream().filter(o -> orderNo.equals(o.getOrderNo())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(goodsOrderItems)) {
                            //赠品
                            List<GoodsOrderItem> goodsOrderItems1 = goodsOrderItems.stream().filter(g -> "赠品".equals(g.getGoodsCategoryName())).collect(Collectors.toList());
                            //主商品
                            List<GoodsOrderItem> goodsOrderItems2 = goodsOrderItems.stream().filter(g -> !"赠品".equals(g.getGoodsCategoryName())).collect(Collectors.toList());
                            List<GoodsOrderItem> isList = new ArrayList<>();
                            List<GoodsOrderItem> noList = new ArrayList<>();
                            //物流信息
                            List<ImportMiBoOrderVo> orderVos = list.stream().filter(l -> orderNo.equals(l.getOrderNo())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(goodsOrderItems2)) {
                                insertList.addAll(orderVos);
                                ImportMiBoOrderVo updateStatusVo = new ImportMiBoOrderVo();
                                updateStatusVo.setOrderNo(orderNo);
                                updateStatusVo.setOrderStatus(2);
                                String status = "";

                                if (CollectionUtils.isNotEmpty(orderVos)) {
                                    for (GoodsOrderItem goodsOrder : goodsOrderItems2) {
                                        //历史物流信息内是否存在相同商品的物流信息
                                        Optional<OrderDelivery> first = orderDeliveries.stream().filter(d -> goodsOrder.getOrderNo().equals(d.getOrderNo()) && goodsOrder.getId().equals(d.getOrderItemId())).findFirst();
                                        if (first.isPresent()) {
                                            isList.add(goodsOrder);
                                        }else {
                                            //新导入的物流信息
                                            List<ImportMiBoOrderVo> first2 = orderVos.stream().filter(o -> goodsOrder.getOrderNo().equals(o.getOrderNo()) && goodsOrder.getId().equals(o.getOrderItemId())).collect(Collectors.toList());
                                            if (CollectionUtils.isNotEmpty(first2)) {
                                                isList.add(goodsOrder);

                                            }else {
                                                noList.add(goodsOrder);
                                            }
                                        }

                                    }
                                    log.error("已存在物流信息的商品明细："+isList);
                                    log.error("未存在物流信息的商品明细："+noList);
                                    if (CollectionUtils.isNotEmpty(isList) && CollectionUtils.isNotEmpty(noList)) {
                                        updateStatusVo.setOrderStatus(3);
                                        updateStatusVo.setCustomerOrderStatus(4);
                                        status = "部分发货";
                                    }
                                    if (CollectionUtils.isNotEmpty(isList) && isList.size() >= goodsOrderItems2.size() && CollectionUtils.isEmpty(noList)) {
                                        updateStatusVo.setOrderStatus(4);
                                        updateStatusVo.setCustomerOrderStatus(5);
                                        status = "已发货";
                                    }
                                }
                                if (StringUtils.isNotBlank(status)) {
                                    if (orderRecord != null && (orderRecord.getOrderStatus().equals(1L)
                                            || orderRecord.getOrderStatus().equals(2L)
                                            || orderRecord.getOrderStatus().equals(3L) )) {
                                        log.error("更新订单状态：" + updateStatusVo);
                                        updateBatchStatusVo.add(updateStatusVo);
                                    }
                                }
                            }

                            if (CollectionUtils.isNotEmpty(goodsOrderItems1)){
                                for (GoodsOrderItem goodsOrder : goodsOrderItems1) {
                                    //历史物流信息内是否存在相同商品的物流信息
                                    Optional<OrderDelivery> first = orderDeliveries.stream().filter(d -> goodsOrder.getOrderNo().equals(d.getOrderNo()) && goodsOrder.getId().equals(d.getOrderItemId())).findFirst();
                                    if (first.isPresent()) {
                                        isList.add(goodsOrder);
                                    }else {
                                        //新导入的物流信息
                                        if (CollectionUtils.isNotEmpty(orderVos)) {
                                            List<ImportMiBoOrderVo> first2 = orderVos.stream().filter(o -> goodsOrder.getOrderNo().equals(o.getOrderNo()) && goodsOrder.getId().equals(o.getOrderItemId())).collect(Collectors.toList());
                                            if (CollectionUtils.isNotEmpty(first2)) {
                                                isList.add(goodsOrder);
//                                                insertList.addAll(first2);
                                            } else {
                                                noList.add(goodsOrder);
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(insertList)) {
                        orderDeliveryDao.insertBatch(insertList);
                    }
                    //更新订单表状态：已发货
                    if (CollectionUtils.isNotEmpty(updateBatchStatusVo)) {
                        orderDao.updateBatchStatus(updateBatchStatusVo);
                    }
                }
                return Result.buildSuccess(importExcelResultVo.getList().size() + "条数据导入成功!");
            }else{
                HashMap errorMap = importExcelResultVo.getErrorMap();
                List<String> wrongInfoList = (List<String>)errorMap.get("wrongInfoList");
                return Result.buildFailure(wrongInfoList.size() + "条数据导入失败！", errorMap);
            }
        }catch (Exception e){
            return Result.buildFailure(e.getMessage());
        }
    }

    public ImportExcelResultVo validExcel(MultipartFile file){
        ImportExcelResultVo validImportExcelResult = new ImportExcelResultVo();
        //获取文件名
        String fileName = file.getOriginalFilename();
        String targetName = this.getTargetName(fileName);
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            List<ImportMiBoOrderVo> list = new ArrayList<>();
            List<HashMap> fullList = new ArrayList<>();
            List<String> wrongInfoList = new ArrayList<>();
            List<String> errorIndexList = new ArrayList<>();
            //将文件的输入流转换成Workbook

            //获取第一个sheet页
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getPhysicalNumberOfRows();
            if(rows>1001){
                throw new BusinessException("导入数据暂不支持超过1000条");
            }
            //获取excel表格title
            Row titleRow = sheet.getRow(0);
            int cellCount = titleRow.getPhysicalNumberOfCells();
            if (titleRow.getCell(0) == null || StringUtils.isEmpty(titleRow.getCell(0).toString()) ||
                    titleRow.getCell(1) == null || StringUtils.isEmpty(titleRow.getCell(1).toString()) ||
                    titleRow.getCell(2) == null || StringUtils.isEmpty(titleRow.getCell(2).toString()) ||
                    !titleRow.getCell(0).toString().contains("订单号")
                    || !titleRow.getCell(1).toString().contains("快递单号")
                    || !titleRow.getCell(2).toString().contains("商品行编码")
                    || cellCount < 3) {
                throw new BusinessException("导入数据模板不正确,请核对！");
            }

            List<String> orderNos = new ArrayList<>();
            List<String> goodsCodes = new ArrayList<>();
            for (int j = 1; j < rows; j++) {
                // 获得第 j 行数据
                Row row = sheet.getRow(j);
                if (row != null ){
                    if (!isEmptyCell(row.getCell(0))) {
                        orderNos.add(row.getCell(0).toString());
                    }
                    if (!isEmptyCell(row.getCell(2))) {
                        goodsCodes.add(row.getCell(2).toString());
                    }
                }
            }
            List<OrderRecord> orderRecords = new ArrayList<>();
            List<GoodsOrderItem> goodsOrderItems = new ArrayList<>();
            List<OrderDelivery> orderDeliveries = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderNos)){
                orderRecords = orderDao.getOrderListByOrderNoList(orderNos);
                goodsOrderItems = goodsOrderItemDao.selectOrderItemByOrderNo(orderNos);
                orderDeliveries = orderDeliveryDao.queryOrderDeliveryByOrderNos(orderNos);

            }
            List<CommonGoodsVo> goodsVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(goodsCodes)){
                GetGoodsByCodeListByPostInDto postInDto = new GetGoodsByCodeListByPostInDto();
                postInDto.setCodeList(goodsCodes);
                goodsVoList = productClient.getGoodsByCodeListByPost(postInDto).getData();
            }

            //设置title列
            List<String> titleList = getTitleList(titleRow, cellCount);
            ImportMiBoOrderVo orderVo = null;
            HashMap contentMap = new HashMap();
            // 遍历每一行,注意：第 0 行为标题
            for (int j = 1; j < rows; j++) {
                orderVo = new ImportMiBoOrderVo();
                contentMap = new HashMap();
                StringBuilder rowInfo = new StringBuilder("第" + (j+1) + "行:");
                StringBuilder wrongInfo = new StringBuilder();
                // 获得第 j 行数据
                Row row = sheet.getRow(j);
                int currentRowIndex = j + 1;
                //校验数据：
                //订单号：不能为空,
                //快递单号：不能为空,
                //商品行编码：不能为空,
                if (row == null ){
                    //设置错误信息
                    row = sheet.createRow(j);
                    rowInfo.append("空数据行");
                    wrongInfoList.add(rowInfo.toString());
                    Cell cell = row.createCell(4);
                    cell.setCellValue("空数据行");
                    contentMap.put("orderNo","");
                    contentMap.put("logisticsNo","");
                    contentMap.put("goodsCode","");
                    contentMap.put("errorInfo", rowInfo.toString());
                    fullList.add(contentMap);
                    continue;
                }
                List<GoodsOrderItem> orderItemList = new ArrayList<>();
                List<OrderDelivery> orderDeliveryList = new ArrayList<>();
                if (isEmptyCell(row.getCell(0))) {
                    wrongInfo.append("订单号不能为空；");
                    errorIndexList.add("A" + currentRowIndex);
                } else {
                    String orderNo = row.getCell(0).toString();
                    orderVo.setOrderNo(orderNo);
                    Optional<OrderRecord> first = orderRecords.stream().filter(o -> o.getOrderNo().equals(orderNo)).findFirst();
                    if (first.isPresent()){
                        OrderRecord orderRecord = first.get();
                        orderItemList = goodsOrderItems.stream().filter(i -> i.getOrderNo().equals(orderNo)).collect(Collectors.toList());
                        orderDeliveryList = orderDeliveries.stream().filter(d -> d.getOrderNo().equals(orderNo)).collect(Collectors.toList());
                        if (orderRecord != null && (orderRecord.getOrderStatus().equals(1L)
                                || orderRecord.getOrderStatus().equals(2L)
                                || orderRecord.getOrderStatus().equals(3L) )) {


                        }else {
                            wrongInfo.append("订单号已处理完成，不可修改；");
                            errorIndexList.add("A" + currentRowIndex);
                        }
                    }else {
                        wrongInfo.append("订单号不存在；");
                        errorIndexList.add("A" + currentRowIndex);
                    }
                }
                contentMap.put("orderNo", row.getCell(0) == null ? null : row.getCell(0).toString());
                
//                if (StringUtils.isEmpty(row.getCell(1) == null ? null : row.getCell(1).toString())) {
                if (isEmptyCell(row.getCell(1))) {
                    wrongInfo.append("快递单号不能为空；");
                    errorIndexList.add("B" + currentRowIndex);
                } else {
                    orderVo.setLogisticsNo(row.getCell(1).toString());
                }
                contentMap.put("logisticsNo", row.getCell(1) == null ? null : row.getCell(1).toString());

                if (isEmptyCell(row.getCell(2))) {
                    wrongInfo.append("商品行编码不能为空；");
                    errorIndexList.add("C" + currentRowIndex);
                } else {
                    String goodsCode = row.getCell(2).toString();
                    orderVo.setGoodsCode(goodsCode);
                    Optional<CommonGoodsVo> first = goodsVoList.stream().filter(g -> goodsCode.equals(g.getCode())).findFirst();
                    if (first.isPresent()) {
                        List<GoodsOrderItem> items = new ArrayList<>();
                        //订单号下商品信息，取出商品编码
                        for (GoodsOrderItem item : orderItemList){
                            String code = StringUtils.isNotBlank(item.getSeriesGoodsCode()) ? item.getSeriesGoodsCode() : item.getGoodsCode();
                            if (goodsCode.equals(code)){
                                items.add(item);
                            }
                        }
                        if(CollectionUtils.isNotEmpty(items)) {
                            log.error("同一订单下商品编码："+goodsCode+"的对应订单商品明细："+items);

//                            List<Long> orderItemIds = list.stream().map(l -> l.getOrderItemId()).collect(Collectors.toList());
                            List<GoodsOrderItem> orderItems = new ArrayList<>();
                            for (GoodsOrderItem item : items){
                                ImportMiBoOrderVo finalOrderVo = orderVo;
                                Optional<OrderDelivery> first2 = orderDeliveryList.stream().filter(d -> item.getId().equals(d.getOrderItemId())).findFirst();
                                if (first2.isEmpty()){
                                    if (CollectionUtils.isNotEmpty(list)) {
                                        Optional<ImportMiBoOrderVo> first1 = list.stream().filter(l -> finalOrderVo.getOrderNo().equals(l.getOrderNo()) && finalOrderVo.getGoodsCode().equals(l.getGoodsCode()) && finalOrderVo.getLogisticsNo().equals(l.getLogisticsNo())).findFirst();
                                        if (first1.isEmpty()) {
                                            orderItems.add(item);
                                        }
                                    }else {
                                        orderItems.add(item);
                                    }
                                }else {
                                    if (!orderVo.getLogisticsNo().equals(first2.get().getLogisticsNo())){
                                        orderItems.add(item);
                                    }
                                }
                            }

                            if (CollectionUtils.isNotEmpty(orderItems)){
                                log.error("商品编码：" + row.getCell(2).toString());
                                orderVo.setOrderItemId(orderItems.get(0).getId());
                                orderVo.setGoodsNum(orderItems.get(0).getBuyNum());
                            }else {
                                wrongInfo.append("该订单号下商品物流信息已存在,请勿导入重复物流单号；");
                                errorIndexList.add("C" + currentRowIndex);
                            }
                        }else {
                            wrongInfo.append("商品行编码在订单中不存在；");
                            errorIndexList.add("C" + currentRowIndex);
                        }
                    }else {
                        wrongInfo.append("商品行编码不存在；");
                        errorIndexList.add("C" + currentRowIndex);
                    }
                }
                contentMap.put("goodsCode", row.getCell(2) == null ? null : row.getCell(2).toString());

                orderVo.setLogisticsCompanyCode("SF");
                orderVo.setLogisticsCompanyName("顺丰速运");
                list.add(orderVo);

                if (!StringUtils.isEmpty(wrongInfo.toString())) {
                    //设置错误信息
                    rowInfo.append(wrongInfo);
                    wrongInfoList.add(rowInfo.toString());
                    //创建新的单元格，记录错误信息
                    Cell cell = row.createCell(4);
                    cell.setCellValue(wrongInfo.toString());
                    contentMap.put("errorInfo", rowInfo.toString());
                } else {
                    contentMap.put("errorInfo", null);
                }
                fullList.add(contentMap);
            }
            if (wrongInfoList.size() > 0) {
                titleList.add("错误信息");
                HashMap map = new HashMap<>();
                map.put("title", titleList);
                map.put("content", fullList);
                map.put("name", targetName == null ? fileName : targetName);
                map.put("errorIndexList", errorIndexList);
                map.put("wrongInfoList", wrongInfoList);
                validImportExcelResult.setResult(false);
                validImportExcelResult.setErrorMap(map);
                return validImportExcelResult;
            } else {
                //插入数据
                validImportExcelResult.setResult(true);
                validImportExcelResult.setList(list);
                return validImportExcelResult;
            }
        } catch (Exception e) {
            log.error("导入数据失败,失败原因："+e);
            throw new BusinessException("导入数据失败,失败原因：" + e.getMessage());
        }
    }

    public  String getTargetName(String fileName) {
        String[] nameList = fileName.split("\\.");
        String targetName = null;
        if (nameList != null && nameList.length > 0) {
            targetName = nameList[0];
        }
        return targetName;
    }

    /**
     * 获取title
     *
     * @param titleRow
     * @param cellCount
     * @return
     */
    public List<String> getTitleList(Row titleRow, int cellCount) {
        List<String> titleList = new ArrayList<>();
        int targetCellCount = cellCount <= 3 ? cellCount : 3;
        for (int k = 0; k < targetCellCount; k++) {
            titleList.add(titleRow.getCell(k).toString());
        }
        return titleList;
    }

    //判断单个单元格是否为空
    public static boolean isEmptyCell(Cell cell){
        if(cell==null||cell.getCellType().equals(CellType.BLANK)){
            return true;
        }
        return false;
    }

    public void insertLog(String orderNo,String operationByName,String createdBy,String operationType,String desc){
        // 添加日志
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderNo(orderNo);
        orderOperationLog.setOperationByName(StringUtils.isNotBlank(operationByName) ? operationByName : "系统账号");
        orderOperationLog.setCreatedByName(StringUtils.isNotBlank(operationByName) ? operationByName : "系统账号");
        orderOperationLog.setOperationType(operationType);
        orderOperationLog.setOperationTime(new Date());
        orderOperationLog.setDescription(desc);
        orderOperationLog.setIsDeleted(0L);
        orderOperationLog.setCreatedBy(StringUtils.isNotBlank(createdBy) ? createdBy : "系统账号");
        orderOperationLog.setCreatedDate(new Date());
        orderOperationLogDao.insert(orderOperationLog);
    }


    public PageInfo<CardOrderDto> getCardOrderList(CardOrderParamDto cardOrderParamDto) {
        PageInfo<CardOrderDto> pageInfo = new PageInfo<>(cardOrderParamDto.getPage(),cardOrderParamDto.getSize());
        cardOrderParamDto.setOffset(pageInfo.getOffset());
        //先查询已经使用的券码列表
        Result<List<String>> result = promotionClient.getAllMallCardCode();
        if(result == null || !result.getSuccess()){
            throw new BusinessException("查询券码异常："+result.getMsg());
        }
        if(CollectionUtils.isEmpty(result.getData())){
            return pageInfo;
        }
        cardOrderParamDto.setCouponCodeIdList(result.getData());
        Long total = orderDao.getCardOrderCount(cardOrderParamDto);
        if(total > 0){
            List<CardOrderDto> list = orderDao.getCardOrderList(cardOrderParamDto);
            //查询商品信息
            if(CollectionUtils.isNotEmpty(list)){
                List<String> orderNoList = list.stream().map(t -> t.getOrderNo()).collect(Collectors.toList());
                List<GoodsOrderPromotion> orderItemInfoList = goodsOrderPromotionDao.selectByOrderNoList(orderNoList);
                Map<String, GoodsOrderPromotion> ordreItemMap = null;
                if(CollectionUtils.isNotEmpty(orderItemInfoList)){
                    ordreItemMap = orderItemInfoList.stream().collect(Collectors.toMap(GoodsOrderPromotion::getOrderNo, Function.identity(),(key1, key2) -> key1));
                }
                for (CardOrderDto cardOrderDto : list) {
                    cardOrderDto.setOrderStatusName(OrderConstant.CustomerOrderStatusEnum.getMsg(cardOrderDto.getOrderStatus()));
                    if(ordreItemMap != null && ordreItemMap.get(cardOrderDto.getOrderNo()) != null ){
                        GoodsOrderPromotion goodsOrderPromotion = ordreItemMap.get(cardOrderDto.getOrderNo());
                        cardOrderDto.setGoodsModel(goodsOrderPromotion.getGoodsModel());
                        cardOrderDto.setGoodsCode(goodsOrderPromotion.getGoodsCode());
                    }
                }
            }
            pageInfo.setRecords(list);
        }
        pageInfo.setTotal(total);
        return pageInfo;
    }

    public void fixOrderData() {
        List<JSONObject> list = orderDao.selectNeedFixData();
        if(CollectionUtils.isNotEmpty(list)){
            for (JSONObject jsonObject : list) {
                try{
                    String id = jsonObject.getString("id");
                    String buyer_message = jsonObject.getString("buyer_message");
                    String decode = URLDecoder.decode(URLDecoder.decode(buyer_message));
                    orderDao.fixData(id,decode);
                }catch(Exception e){
                    e.printStackTrace();
                    System.out.println(e);
                }

            }
        }
    }

    public String checkRepeatOrder(CheckRepeatOrderDto checkRepeatOrderDto) {
        String msg = "";
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if(customerInfo == null){
            throw new BusinessException("账号未登录或者账号存在问题！");
        }
        checkRepeatOrderDto.setCustomerId(customerInfo.getId());

        //查询预下单信息
        PreOrderOutDto preOrderOutDto = this.loadPreOrder(checkRepeatOrderDto.getPreOrderNo());
        List<OrderGoodsInfoVO> goodsInfoList = preOrderOutDto.getGoodsInfoList();

        boolean containsCategory = true;

        if(CollectionUtils.isNotEmpty(goodsInfoList)){
            List<String> discountCodeList = goodsInfoList.stream().filter(t -> StringUtils.isNotBlank(t.getDiscountCode())).map(t -> t.getDiscountCode()).collect(Collectors.toList());
            checkRepeatOrderDto.setDiscountCodeList(discountCodeList);
//            containsCategory = goodsInfoList.stream().anyMatch(t -> OrderConstant.CHECK_CATEGORY_IDS.contains(t.getGoodsCategoryId()));
        }
        if(CollectionUtils.isEmpty(checkRepeatOrderDto.getDiscountCodeList())){
            return null;
        }
        //查询当前用户已享受的云闪付的的订单
        checkRepeatOrderDto.setBeginDate(DateUtil.parseDateTime("2025-01-01 00:00:00"));
        List<String> orderNoList = orderDao.getChinaumsOrderNoList(checkRepeatOrderDto);
        if(CollectionUtils.isNotEmpty(orderNoList)){
            return "您已在订单"+orderNoList.get(0)+"中享受过本品类补贴优惠";
        }
        //校验品类和收货地址是否是浙江省
        if(checkRepeatOrderDto.getAddressId() != null && containsCategory){
            //查询地址信息
            Result<UserAddressFromMall> addressResult = customerClient.queryUserAddressById(checkRepeatOrderDto.getAddressId());
            if(addressResult != null && addressResult.getSuccess() && addressResult.getData() != null){
                String provinceName = addressResult.getData().getProvinceName();
                if(!Objects.equals(provinceName,OrderConstant.ZHE_JIANG)){
//                    return "仅限浙江区域可享受补贴，该订单将以正常价格进行支付!";
                }
            }
        }
        return msg;
    }


    public OrderRecordVoForCloud queryOrderByCardNo(String cardNo) {
        if(StringUtils.isBlank(cardNo)){
            throw new BusinessException("参数不能为空！");
        }
        GoodsOrderPromotion goodsOrderPromotion = goodsOrderPromotionDao.selectByCardNo(cardNo);
        if(goodsOrderPromotion == null || StringUtils.isBlank(goodsOrderPromotion.getOrderNo())){
            return null;
        }
        OrderRecordVoForCloud result = orderDao.selectByOrderNoForCloud(goodsOrderPromotion.getOrderNo());
        if(result != null){
            result.setGoodsCode(StringUtils.isNotBlank(goodsOrderPromotion.getSeriesGoodsCode()) ? goodsOrderPromotion.getSeriesGoodsCode() : goodsOrderPromotion.getGoodsCode());
            result.setGoodsModel(goodsOrderPromotion.getGoodsModel());
        }
        return result;
    }


    public OrderRecordVoForCloud queryOrderByOrderNoForCloud(String orderNo) {
        OrderInfoInDto orderInfoInDto = orderDao.selectByOrderNo(orderNo);
        if(orderInfoInDto == null){
            throw new BusinessException("商城订单号不存在,请重新输入");
        }
        if(orderInfoInDto.getCloudOrderNo() != null){
            throw new BusinessException("商城订单已关联其他订单,请重新输入");
        }
        if(!Objects.equals(orderInfoInDto.getPayMethod(),OrderConstant.PayMethod.CLOUD_QUICK_PAY.getCode())){
            throw new BusinessException("商城订单支付方式不符合以旧换新政策,请重新输入");
        }
        if(!Objects.equals(orderInfoInDto.getPayStatus(),OrderConstant.PayStatusEnum.SUCCESS.getCode())){
            throw new BusinessException("商城订单未支付");
        }
        OrderRecordVoForCloud result = orderDao.selectByOrderNoForCloud(orderNo);
        GoodsOrderPromotion goodsOrderPromotion = goodsOrderPromotionDao.selectByOrderNo(orderNo);
        if(result != null && goodsOrderPromotion != null){
            result.setGoodsCode(StringUtils.isNotBlank(goodsOrderPromotion.getSeriesGoodsCode()) ? goodsOrderPromotion.getSeriesGoodsCode() : goodsOrderPromotion.getGoodsCode());
            result.setGoodsModel(goodsOrderPromotion.getGoodsModel());
        }
        return result;
    }
}
