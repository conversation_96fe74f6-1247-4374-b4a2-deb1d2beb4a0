package com.fotile.shopordercenter.order.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface        UserCluesMsgChannel {
    /**
     * 处理放松【发送通道】
     */
    String DISPOSE_SEND_MSG_OUTPUT = "dispose_send_msg_output";

    /**
     * 发待处理消息【接收通道】
     */
    String DISPOSE_SEND_MSG_IPUNT = "dispose_send_msg_input";

    /**
     * 钉钉消息统一处理钉钉通知
     */
    String GENERAL_DING_MESSAGE_OUT = "blacklist_ding_message_out";

    @Output(GENERAL_DING_MESSAGE_OUT)
    MessageChannel sendGeneralMassage();

    /**
     * 消费钉钉通知 钉钉消息统一处理钉钉通知
     */
    String GENERAL_DING_MESSAGE_INPUT = "blacklist_ding_message_input";


    @Input(GENERAL_DING_MESSAGE_INPUT)
    SubscribableChannel recieveGeneralDingMassage();

    @Output(DISPOSE_SEND_MSG_OUTPUT)
    MessageChannel sendDisposeSendMsgMessage();

    /**
     * 发消息【j接受】
     *
     * @return
     */
    @Input(DISPOSE_SEND_MSG_IPUNT)
    SubscribableChannel disposeSendMsgInput();


}
