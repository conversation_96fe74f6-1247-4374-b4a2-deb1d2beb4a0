package com.fotile.shopordercenter.order.controller;


import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.shopordercenter.order.pojo.dto.HandleCommentDto;
import com.fotile.shopordercenter.order.pojo.dto.OrderCommentAuditInDto;
import com.fotile.shopordercenter.order.pojo.dto.OrderCommentInDto;
import com.fotile.shopordercenter.order.pojo.dto.OrderCommentOutDto;
import com.fotile.shopordercenter.order.pojo.entity.OrderComment;
import com.fotile.shopordercenter.order.service.OrderCommentService;
import com.fotile.shopordercenter.utils.RoleAndMenuUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 官方商城API
 */
@RestController
@RequestMapping("/api/orderComment")
@Slf4j
public class OrderCommentController extends BaseController {

    @Autowired
    OrderCommentService orderCommentService;

    @Autowired
    RoleAndMenuUtils roleAndMenuUtils;

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    /**
     * 【订单】订单评论分页查询
     */
    @RequestMapping(value = "/getList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<OrderCommentOutDto>> getList(OrderCommentInDto orderCommentInDto) {
        try {
            return success(orderCommentService.getList(orderCommentInDto));
        } catch (Exception e) {
            log.error("分页查询异常", e);
            return failure("分页查询异常");
        }
    }

    /**
     * 【订单】订单评论详情
     */
    @RequestMapping(value = "/getInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<OrderCommentOutDto> getInfo(Long id) {
        try {
            return success(orderCommentService.getInfo(id));
        } catch (Exception e) {
            log.error("详情查询异常", e);
            return failure("详情查询异常");
        }
    }

    /**
     * 【订单】新增/修改订单评论
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> edit(@RequestBody OrderComment orderComment) {
        try {
            orderCommentService.edit(orderComment);
            return success("操作成功");
        } catch (Exception e) {
            log.error("操作异常", e);
            return failure("操作异常");
        }
    }

    /**
     * 【订单】订单评论审核
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> editAudit(@RequestBody List<OrderCommentAuditInDto> orderCommentAuditInDtoList) {
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        if(userAuthor == null){
            return failure("请先登录");
        }
        try {
            for (OrderCommentAuditInDto orderCommentAuditInDto : orderCommentAuditInDtoList) {
                orderCommentService.editAudit(orderCommentAuditInDto.getId(), orderCommentAuditInDto.getAuditStatus(),userAuthor.getUserId(),userAuthor.getFirstName());
            }
            return success("操作成功");
        } catch (Exception e) {
            log.error("审核异常", e);
            return failure("审核异常");
        }
    }

    /**
     * 【订单】订单评论操作
     */
    @RequestMapping(value = "/handle", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> handle(@RequestBody HandleCommentDto dto) {
        try {
            orderCommentService.handle(dto);
            return success("操作成功");
        } catch (Exception e) {
            log.error("操作订单异常:{}", e);
            return failure("操作订单异常");
        }
    }

    /**
     * 【订单】测试权限
     */
    @RequestMapping(value = "/test", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result test() {
        return success(roleAndMenuUtils.getMapResult(userAuthorConfig.queryUserAuthor()));
    }
}
