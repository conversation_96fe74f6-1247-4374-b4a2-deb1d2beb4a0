package com.fotile.shopordercenter.order.dao;
import java.util.Collection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.shopordercenter.order.pojo.entity.OrderOperationLog;
import com.fotile.shopordercenter.order.pojo.dto.OrderOperationLogPageRequestVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrderOperationLogDao extends BaseMapper<OrderOperationLog> {

    List<OrderOperationLog> selectAll(@Param("page") Long page, @Param("size") Long size, @Param("orderNo") String orderNo);

    List<OrderOperationLog> selectOrderOperationLogPage(OrderOperationLogPageRequestVo requestVo);

    Long selectOrderOperationLogCount(OrderOperationLogPageRequestVo requestVo);
    // 根据订单号分页查询日志
    List<OrderOperationLog> selectAllByPage(@Param("orderNo") String orderNo, @Param("orderOperationLogPageInfo") PageInfo<OrderOperationLog> orderOperationLogPageInfo);
    // 查询日志总数
    long selectAllCount(String orderNo);

    int insertBatch(@Param("orderOperationLogCollection") Collection<OrderOperationLog> orderOperationLogCollection);

    List<OrderOperationLog> selectOrderLogByOrderNos(List<String> orderNoList);

    List<OrderOperationLog> selectListByOrderNo(@Param("orderNo") String orderNo, @Param("operationLogType") String operationLogType);
}