package com.fotile.shopordercenter.order.pojo.dto;

import lombok.Data;

import java.util.Date;

@Data
public class OrderCheckInDto {

    private Long id;

    /**
     * 查款类型1转账查款，2补差查款
     */
    private Long checkType;
    /**
     * 查款状态0待查款，1待确认，2已反驳，3已完成
     */
    private Long checkStatus;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 到账时间区间-开始
     */
    private Date arriveStartTime;

    /**
     * 到账时间区间-结束
     */
    private Date arriveEndTime;

    /**
     * 申请提交时间区间-开始
     */
    private Date applyStartTime;

    /**
     * 申请提交时间区间-结束
     */
    private Date applyEndTime;

    private Long page;

    private Long size;

}
