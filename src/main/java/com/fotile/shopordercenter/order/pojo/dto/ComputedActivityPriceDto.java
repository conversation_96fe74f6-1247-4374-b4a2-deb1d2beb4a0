package com.fotile.shopordercenter.order.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class ComputedActivityPriceDto {
    private Long activityId;

    /**
     * 商品信息
     */
    private List<Goods> goodsList;

    @Data
    public static class Goods {
        /**
         * 商品编码
         */
        @NotBlank(message = "商品编码不能为空！")
        private String goodsCode;

        /**
         * 购买数量
         */
        @NotNull(message = "购买数量不能为空")
        private int num;

        /**
         * 商品单价
         */
        @NotNull(message = "商品单价不能为空")
        private BigDecimal goodsPrice;

        /**
         * 商品类型 1 实物单品，2组合商品
         */
        private Integer goodsType = 1;

        /**
         * 组合内商品编码
         */
        private List<String> subGoodsCodes = new ArrayList<>();
    }
}
