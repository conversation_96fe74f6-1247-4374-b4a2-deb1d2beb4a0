package com.fotile.shopordercenter.order.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
@Data
public class FindGoodsRejectionGroupByGoroupCodeVO implements Serializable {
    /**
     * 产品组code
     */
    @TableField(value = "`product_group_code`")
    private String productGroupCode;

    /**
     * 产品组名称
     */
    @TableField(value = "`product_group_name`")
    private String productGroupName;

    /**
     * 产品组排斥code
     */
    @TableField(value = "`product_rejection_group_code`")
    private String productRejectionGroupCode;

    /**
     * 产品组排斥名称
     */
    @TableField(value = "`product_rejection_group_name`")
    private String productRejectionGroupName;
}
