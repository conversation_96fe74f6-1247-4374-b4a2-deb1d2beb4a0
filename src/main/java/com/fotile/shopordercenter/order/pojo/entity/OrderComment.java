package com.fotile.shopordercenter.order.pojo.entity;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * order_comment
 * <AUTHOR>
@Data
public class OrderComment implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单行id
     */
    private Long orderItemId;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品型号
     */
    private String goodsModel;

    /**
     * 商品图片
     */
    private String goodsImage;

    /**
     * 实付金额
     */
    private BigDecimal paidAmount;

    /**
     * 系列规格
     */
    private String specificationAttributes;

    /**
     * 评论时间
     */
    private Date commentTime;

    /**
     * 评论人ID
     */
    private Long commentUserId;

    /**
     * 评分
     */
    private String score;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 图片
     */
    private String image;

    /**
     * 视频
     */
    private String video;

    /**
     * 追评上级Id
     */
    private Long reviewId;

    /**
     * 是否追评
     */
    private Long isReview;

    /**
     * 审核状态 1:审核未通过 2:审核通过
     */
    private Long auditStatus;

    /**
     * 是否回复
     */
    private Long isReply;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复时间
     */
    private Date replyTime;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 编辑人名称
     */
    private String modifiedByName;

    /**
     * 是否删除
     */
    private Long isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改日期
     */
    private Date modifiedDate;

    /**
     * 用户头像
     */
    private String headPortrait;

    /**
     * 用户昵称
     */
    @FieldEncrypt
    private String nickName;

    /**
     * 用户等级
     */
    private Long gradeId;

    /**
     * 商品类型
     */
    private int goodsType;

    /**
     * 商品分类
     */
    private String goodsCategoryName;

    /**
     * 是否推荐
     */
    private Long isRecommend;

    /**
     * 是否隐藏
     */
    private Long isHide;
    /**
     * 是否可追评 0 否 1 是
     */
    private long isCanAfterReview;

    private static final long serialVersionUID = 1L;
}