package com.fotile.shopordercenter.order.pojo.dto;

import com.fotile.shopordercenter.client.dto.AvailableCardCodeVO;
import com.fotile.shopordercenter.client.dto.OrderGoodsInfoVO;
import com.fotile.shopordercenter.client.entity.UserAddressFromMall;
import com.fotile.shopordercenter.client.entity.UserInstallAddress;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预下单返回参数
 */
@Data
public class PreOrderOutDto {

    /**
     * 下单类型：cart 购物车，buy 直接购买
     */
    private String type;

    /**
     * 商品总金额
     */
    private BigDecimal goodsTotalAmount;

    /**
     * 活动优惠金额
     */
    private BigDecimal activityDiscountAmount;

    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponDeductionAmount = BigDecimal.ZERO;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 实付金额
     */
    private BigDecimal actualPaymentAmount;

    /**
     * 定金
     */
    private BigDecimal deposit;

    /**
     * 活动赠送积分
     */
    private Long giveAwayPoints;

    /**
     * 会员赠送积分
     */
    private Long memberGivePoints;

    /**
     * 赠送成长值
     */
    private Long giveGrowthValue;

    /**
     * 订单商品数量
     */
    private Integer orderProNum;

    /**
     * 商品列表
     */
    private List<OrderGoodsInfoVO> goodsInfoList;

    /**
     * 默认收货地址
     */
    private UserAddressFromMall userAddress;

    /**
     * 默认安装地址
     */
    private UserInstallAddress userInstallAddress;

    /**
     * 客户手机号
     */
    private String customerPhone;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 卡券明细ID
     */
    private String cardId;

    /**
     * 券码
     */
    private String cardNo;

    /**
     * 可用优惠券列表
     */
    private List<AvailableCardCodeVO> couponList;

    /**
     * 是否预售
     */
    private Boolean isPreSale = false;

    /**
     * 定金支付开始时间
     */
    private Date depositPayStartTime;

    /**
     * 定金支付结束时间
     */
    private Date depositPayEntTime;

    /**
     * 尾款支付开始时间
     */
    private Date finalPayStartTime;

    /**
     * 尾款支付结束时间
     */
    private Date finalPayEndTime;

    /**
     * 是否可用优惠券
     */
    private Boolean isAvailableCoupon = true;

    /**
     * 是否可用货到付款
     */
    private Boolean isAvailableCashOnDelivery = true;

    /**
     * 是否可用银行转账
     */
    private Boolean isAvailableBankTransfer = true;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 员工编码
     */
    private String jobId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    //private Long unpaidOrderCancellationTime;

    /**
     * 未支付取消时间（分钟）
     */
    private Long unpaidOrderCancellationMinutes;

    /**
     * 同一品类限购台数
     */
    private Long userLimitNum;

    /**
     * 用户IP
     */
    private String ip;

    /**
     * 管家ID
     */
    private String stewardId;

    /**
     * 管家名称
     */
    private String stewardName;

    /**
     * 活动开始时间
     */
    private Date activityStartTime;

    /**
     * 会员等级
     */
    private Long gradeId;
}
