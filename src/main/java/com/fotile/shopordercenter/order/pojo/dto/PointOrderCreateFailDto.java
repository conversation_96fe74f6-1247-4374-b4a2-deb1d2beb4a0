package com.fotile.shopordercenter.order.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

@Data
public class PointOrderCreateFailDto {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 订单创建时间
     */
    private String createOrderTime;
    /**
     * 收货人手机号
     */
    @FieldEncrypt
    private String receiverPhone;
    /**
     * 收货人姓名
     */
    @FieldEncrypt
    private String receiverName;
    /**
     * 购买数量
     */
    private Integer buyNum;
    /**
     * 积分商品id
     */
    private Long pointProductId;
    /**
     * 积分商品code
     */
    private String pointProductCode;
    /**
     * 积分商品name
     */
    private String pointProductName;
    /**
     * 积分订单创建时间
     */
    private String pointOrderCreateTime;
    /**
     * 积分订单创建错误信息
     */
    private String pointOrderCreateMsg;
}
