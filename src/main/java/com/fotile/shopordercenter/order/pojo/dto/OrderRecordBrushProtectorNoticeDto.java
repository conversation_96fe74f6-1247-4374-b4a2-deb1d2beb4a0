package com.fotile.shopordercenter.order.pojo.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * order_record
 *
 * <AUTHOR>
@Data
@HeadRowHeight(25)
@ColumnWidth(20)
@ContentRowHeight(25)
@ExcelIgnoreUnannotated
// 字段起名中文是为了同时兼容excel导出以及中台数据下载-报表编辑37执行
public class OrderRecordBrushProtectorNoticeDto implements Serializable {
    /**
     * 异常原因
     */
    @ExcelProperty(value = {"异常原因"}, index = 0)
    private String 异常原因;

    /**
     * 订单编号
     */
    @ExcelProperty(value = {"订单编号"}, index = 1)
    private String 订单编号;

    /**
     * 下单时间
     */
    @ExcelProperty(value = {"下单时间"}, index = 2)
    private Date 下单时间;

    /**
     * 支付方式
     */
    @ExcelProperty(value = {"支付方式"}, index = 3)
    private String 支付方式;

    /**
     * 支付时间
     */
    @ExcelProperty(value = {"支付时间"}, index = 4)
    private Date 支付时间;

    /**
     * 顾客支付金额
     */
    @ExcelProperty(value = {"顾客支付金额"}, index = 5)
    private String 顾客支付金额;

    /**
     * 产品品类
     */
    @ExcelProperty(value = {"产品品类"}, index = 6)
    private String 产品品类;

    /**
     * 产品编码
     */
    @ExcelProperty(value = {"产品编码"}, index = 7)
    private String 产品编码;

    /**
     * 产品型号
     */
    @ExcelProperty(value = {"产品型号"}, index = 8)
    private String 产品型号;

    /**
     * 购买数量
     */
    @ExcelProperty(value = {"购买数量"}, index = 9)
    private String 购买数量;

    /**
     * 产品挂牌价
     */
    @ExcelProperty(value = {"产品挂牌价"}, index = 10)
    private String 产品挂牌价;

    /**
     * 售价
     */
    @ExcelProperty(value = {"售价"}, index = 11)
    private String 售价;

    /**
     * 下单人手机号
     */
    @ExcelProperty(value = {"下单人手机号"}, index = 12)
    private String 下单人手机号;

    /**
     * IP地址
     */
    @ExcelProperty(value = {"IP地址"}, index = 13)
    private String IP地址;

    /**
     * 会员ID
     */
    @ExcelProperty(value = {"会员ID"}, index = 14)
    private String 会员ID;

    /**
     * 收货地址
     */
    @ExcelProperty(value = {"收货地址"}, index = 15)
    private String 收货地址;

    /**
     * 同步电商状态
     */
    @ExcelProperty(value = {"同步电商状态"}, index = 16)
    private String 同步电商状态;

    /**
     * 订单状态
     */
    @ExcelProperty(value = {"订单状态"}, index = 17)
    private String 订单状态;

    /**
     * 订单取消时间
     */
    @ExcelProperty(value = {"订单取消时间"}, index = 18)
    private Date 订单取消时间;

    /**
     * 中台退款时间
     */
    @ExcelProperty(value = {"中台退款时间"}, index = 19)
    private Date 中台退款时间;

    /**
     * 发货时间
     */
    @ExcelProperty(value = {"发货时间"}, index = 20)
    private Date 发货时间;

    /**
     * 其他说明
     */
    @ExcelProperty(value = {"其他说明"}, index = 21)
    private String 其他说明;

}