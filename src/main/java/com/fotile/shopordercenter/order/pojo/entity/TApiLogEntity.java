package com.fotile.shopordercenter.order.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import java.util.Date;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper=true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`shopordercenter`.`t_api_log`")
public class TApiLogEntity extends AuditingEntity {
    /**
     * 订单号
     */
    @TableField(value = "`order_no`")
    @Size(max = 30,message = "订单号最大长度要小于 30")
    private String orderNo;

    /**
     * 1-云闪付创建订单 2-云闪付支付回调 3-云闪付订单查询 6-凭证取消核销 7凭证生成 8凭证预核销  9 凭证上报  10凭证取消
     */
    @TableField(value = "`req_type`")
    private Integer reqType;

    /**
     * 请求方式
     */
    @TableField(value = "`http_method`")
    @Size(max = 10,message = "请求方式最大长度要小于 10")
    private String httpMethod;

    /**
     * 请求字符集
     */
    @TableField(value = "`charset`")
    @Size(max = 10,message = "请求字符集最大长度要小于 10")
    private String charset;

    /**
     * 请求格式
     */
    @TableField(value = "`content_type`")
    @Size(max = 30,message = "请求格式最大长度要小于 30")
    private String contentType;

    /**
     * 请求URL
     */
    @TableField(value = "`request_url`")
    @Size(max = 100,message = "请求URL最大长度要小于 100")
    private String requestUrl;

    /**
     * 请求参数
     */
    @TableField(value = "`request_params`")
    private String requestParams;

    /**
     * 响应代码
     */
    @TableField(value = "`response_code`")
    @Size(max = 10,message = "响应代码最大长度要小于 10")
    private String responseCode;

    /**
     * 响应内容
     */
    @TableField(value = "`response_result`")
    private String responseResult;

    /**
     * 调用时间
     */
    @TableField(value = "`created_time`")
    private Date createdTime;

    public static final String COL_ID = "id";

    public static final String COL_ORDER_NO = "order_no";

    public static final String COL_REQ_TYPE = "req_type";

    public static final String COL_HTTP_METHOD = "http_method";

    public static final String COL_CHARSET = "charset";

    public static final String COL_CONTENT_TYPE = "content_type";

    public static final String COL_REQUEST_URL = "request_url";

    public static final String COL_REQUEST_PARAMS = "request_params";

    public static final String COL_RESPONSE_CODE = "response_code";

    public static final String COL_RESPONSE_RESULT = "response_result";

    public static final String COL_CREATED_TIME = "created_time";

    public static final String COL_IS_DELETED = "is_deleted";
}