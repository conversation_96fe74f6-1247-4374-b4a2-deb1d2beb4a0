package com.fotile.websitecenter.client;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import com.fotile.websitecenter.client.pojo.*;
import com.fotile.websitecenter.pojo.ContentOperatorLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "cms-center", path = "/api")
public interface CmsClientService {
    /**
     * 新增操作日志
     */
    @RequestMapping(value = "/conentOperatorLog/insertContnetOperatorLog", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> insertContnetOperatorLog(@RequestBody ContentOperatorLog contentOperatorLog);

    @PostMapping(value = "/partner/api/open/savePartner")
    public Result savePartner(@RequestBody Partner partner);

    @RequestMapping(value = "/open/content/miniAppFindContentPage", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<ContentAllMiniVo>> miniAppFindContentPage(@RequestBody ContentParamDto contentParam);

    /**
     * 根据商品id，查询商品id关联的内容
     */
    @RequestMapping(value = "/open/content/findContentByGoodsId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ContentAllMiniVo>> findContentByGoodsId(@RequestParam("goodsId") Long goodsId);

    /**
     * 根据商品id，分页查询商品id关联的内容
     */
    @RequestMapping(value = "/open/content/findContentByGoodsIdPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<ContentAllMiniVo>> findContentByGoodsIdPage(@SpringQueryMap FindContentParamDto findContentParamDto);

    /**
     * 根据商品id，查询商品id关联的内容菜谱
     */
    @RequestMapping(value = "/open/contentMenu/findContentMenuByGoodsIdPage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result findContentMenuByGoodsIdPage(@SpringQueryMap FindContentParamDto findContentParamDto);

    /**
     * 根据id查询内容信息
     */
    @RequestMapping(value = "/open/content/findByIdContent", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ContentMiniVo> findByIdContent(@RequestParam Long contentId);

    /**
     * 查询内容菜谱详情
     */
    @RequestMapping(value = "/open/contentMenu/findContentMenuById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result findContentMenuById(@RequestParam Long id);
}
