package com.fotile.websitecenter.client.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserAddressDto implements Serializable {
    @ApiModelProperty("表主键")
    private Long id;
    @ApiModelProperty(value = "顾客id")
    private Long customerInfoId;

    @ApiModelProperty(value = "是否为默认地址, 0-不是 1-是")
    private Integer isDefault;

    private String type;

    @ApiModelProperty(value = "收货人姓名")
    private String name;


    @ApiModelProperty(value = "联系方式")
    private String phone;


    @ApiModelProperty(value = "省id")
    private Long provinceId;


    @ApiModelProperty(value = "市id")
    private Long cityId;


    @ApiModelProperty(value = "县/区id")
    private Long countyId;

    @ApiModelProperty(value = "省")
    private String provinceName;

    @ApiModelProperty(value = "市")
    private String cityName;

    @ApiModelProperty(value = "县")
    private String countyName;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "自建房-省名称")
    private String selfProviceName;

    @ApiModelProperty(value = "自建房-市名称")
    private String selfCityName;

    @ApiModelProperty(value = "自建房-区名称")
    private String selfCountyName;

    @ApiModelProperty(value = "街道")
    private String street;

    @ApiModelProperty(value = "楼栋")
    private String building;

    @ApiModelProperty(value = "单元")
    private String unit;

    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    @ApiModelProperty(value = "入住时间")
    private Date checkInDate;

    @ApiModelProperty(value = "安装时间")
    private Date installDate;


    @ApiModelProperty(value="房型图",example="房型图")
    private String pic;

    @ApiModelProperty(value="房屋类型",example="房屋类型")
    private String houseType;


    @ApiModelProperty(value="房型面积",example="房型面积")
    private String houseArea;

    @ApiModelProperty(value="厨房形状",example="厨房形状")
    private String kitchenType;

    @ApiModelProperty(value="厨房面积",example="厨房面积")
    private String kitchenArea;

    @ApiModelProperty(value="匹配码",example="匹配码")
    private String code;

    @ApiModelProperty(value="小区id",example="小区id")
    private Long villageId;
}
