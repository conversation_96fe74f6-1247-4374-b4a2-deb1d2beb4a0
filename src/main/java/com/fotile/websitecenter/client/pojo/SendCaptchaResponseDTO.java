package com.fotile.websitecenter.client.pojo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("发送短信验证码返回参数")
public class SendCaptchaResponseDTO implements Serializable {
    @ApiModelProperty("obj字段表示此次发送的验证码。")
    private String obj;
    @ApiModelProperty("msg字段表示此次发送的sendid")
    private String msg;
    @ApiModelProperty("主要的返回码 200、315、403、414、416、500 具体请参考code状态表 https://dev.yunxin.163.com/docs/product/IM%E5%8D%B3%E6%97%B6%E9%80%9A%E8%AE%AF/%E6%9C%8D%E5%8A%A1%E7%AB%AFAPI%E6%96%87%E6%A1%A3/code%E7%8A%B6%E6%80%81%E8%A1%A8")
    private Integer code;
    @ApiModelProperty(value ="验证码剩余使用次数，默认只能使用一次")
    private Integer validCount=1;

}
