package com.fotile.websitecenter.client.pojo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ContentParamDto implements Serializable {

    /**
     * 搜索关键字
     */
    private String keyword;
    private String key;

    /**
     * 渠道id,新媒体小程序默认43
     */
    @NotNull(message = "渠道ID不能为空")
    private Long channelId;

    /**
     * 内容类型，灵感案例传16
     */
    @NotBlank(message = "内容类型不能为空")
    private String type;

    /**
     * 页数
     */
    @NotNull(message = "页数不能为空")
    private Integer page;

    /**
     * 每页条数
     */
    @NotNull(message = "每页条数不能为空")
    private Integer size;

    private Long offset;

    private List<Long> contentIdList;
}
