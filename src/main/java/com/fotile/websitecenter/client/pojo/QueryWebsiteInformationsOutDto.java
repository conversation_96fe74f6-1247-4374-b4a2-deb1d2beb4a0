package com.fotile.websitecenter.client.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName QueryInformationsOutDto
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/3/10
 * @Version
 **/
@Data
public class QueryWebsiteInformationsOutDto implements Serializable {
    private Long id;
    private String title;
    private String coverUrl;
    private Date createdDate;
    private  String content;
    private String videoCoverUrl;
    private  String videoUrl;
    private Integer essence;
    private Date showDate;
    private String showDateStr;
    private String firstType;
    private String secondType;
    private String threeType;
    private  String summary;
    //用于取icon
    private Integer sort;
}
