package com.fotile.websitecenter.client.pojo;

import com.fotile.websitecenter.pojo.SellingPointDescriptionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ChannelGoods
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/3/12
 * @Version
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChannelGoods implements Serializable {
    //商品id
    private Long id;
    //展示型号(商品表)
    private String modelNum;
    //展示型号(商品详情表)
    private String showModel;
    //简介
    private String introduction;
    //产品图
    private String url;
    //产品图 -- 集合
    private List<String> urlList;
    //分类名称
    private  String name;
    //分类id
    private Long channelCategoryId;
    private  String categoryName;
    private Long sort;

    @ApiModelProperty("商品属性对象")
    private List<TGoodsAttributeMapping> tGoodsAttributeMappings;
    private String[] names;
    private String[] labels;

    /**
     * 卖点图
     */
    private String sellingPointPic;

    /**
     * 展示类型 1.a 级 2.b 级
     */
    private Integer showType;

    /**
     * 详情页链接
     */
    private String detailUrl;

    /**
     * 卖点信息
     */
    private List<SellingPointDescriptionEntity> sellingPointList;
    /**
     * 获奖信息
     */
    private List<GoodsPrize> goodsPrizes;
    /**
     * 是否新品，1是0否
     */
    private Integer newProducts;
}
