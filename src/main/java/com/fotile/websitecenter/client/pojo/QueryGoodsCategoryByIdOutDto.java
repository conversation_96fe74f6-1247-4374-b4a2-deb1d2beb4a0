package com.fotile.websitecenter.client.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 商品分类实体类
 * <AUTHOR>
 * @date 2018-11-16
 *
 */
@Data
public class QueryGoodsCategoryByIdOutDto implements Serializable {

	@ApiModelProperty(value="分类id")
	private Long id;
	
	@ApiModelProperty(value="是否删除 0:否；其他：是")
	private Long isDeleted;
	
    @ApiModelProperty(value="分类编码")
    private String code;

    @NotBlank(message="分类名称不能为空")
    @ApiModelProperty(value="分类名称")
    private String name;
    
    @ApiModelProperty(value="分类图片路径(选中)")
    private String categoryPath;
    
    @ApiModelProperty(value="分类图片路径(未选中)")
    private String categoryPathUnsel;
    
    @ApiModelProperty(value="备注")
    private String note;
    
    @ApiModelProperty(value = "商品排序")
    private Long sort;

}
