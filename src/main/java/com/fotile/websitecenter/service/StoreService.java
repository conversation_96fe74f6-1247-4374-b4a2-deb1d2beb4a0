package com.fotile.websitecenter.service;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.KeyConvertor;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.util.StringUtils;
import com.fotile.framework.web.Result;
import com.fotile.websitecenter.client.ContentChannel;
import com.fotile.websitecenter.client.MarketClientService;
import com.fotile.websitecenter.client.OrgClientService;
import com.fotile.websitecenter.client.SystemClientService;
import com.fotile.websitecenter.client.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName StoreService
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/3/18
 * @Version
 **/
@Service
@Slf4j
public class StoreService {
    @Autowired
    private SystemClientService systemClientService;
    @Autowired
    private OrgClientService orgClientService;
    @Resource(name = ContentChannel.DISPOSE_SEND_MSG_OUTPUT)
    private MessageChannel scheduleMsgChannel;
    @Autowired
    private MarketClientService marketClientService;

    @Cached(area = "websitecenter", name = "queryCity_", keyConvertor = KeyConvertor.FASTJSON, cacheType = CacheType.REMOTE, expire = 120, timeUnit = TimeUnit.HOURS)
    public Map<String, List<QueryCityOutDto>> queryCity() {
        return systemClientService.queryCity().getData();
    }

    @Cached(area = "websitecenter", name = "queryStore_", keyConvertor = KeyConvertor.FASTJSON, key = "#queryStoreInDto", cacheType = CacheType.REMOTE, expire = 120, timeUnit = TimeUnit.HOURS)
    public PageInfo<QueryStoreOutDto> queryStore(QueryStoreInDto queryStoreInDto) {
        PageInfo<QueryStoreOutDto> data = orgClientService.queryStore(queryStoreInDto.getPagination(), queryStoreInDto.getSize(), queryStoreInDto.getKey(), queryStoreInDto.getChannelCode()).getData();

        if (data != null && data.getList() != null && data.getList().size() > 0 ){
            data.getList().forEach(r ->{
                if (StringUtils.isEmpty(r.getTel())){
                    r.setTel("");
                }
                if (StringUtils.isEmpty(r.getAddress())){
                    r.setAddress("");
                }
                if (StringUtils.isEmpty(r.getShowAddress())){
                    r.setShowAddress("");
                }
                if (StringUtils.isEmpty(r.getUserPhone())){
                    r.setUserPhone("");
                }
            });
        }
        return data;
    }

    public boolean sendStoreInfo(QueryStoreOutDto queryStoreOutDto) {
        //获取信息
        List<String> userIdList = new ArrayList<>(Arrays.asList(queryStoreOutDto.getUserPhone()));
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("tel", queryStoreOutDto.getTel());
        hashMap.put("name", queryStoreOutDto.getName());
        hashMap.put("address", queryStoreOutDto.getAddress());
        StoreMsgDto storeMsgDto = new StoreMsgDto(null, "3", hashMap, null, "2", null, userIdList, "web-hq-store-0001");
        boolean send = scheduleMsgChannel.send(MessageBuilder.withPayload(storeMsgDto).build());
        UserCluesExtendDTO userCluesExtendDTO = new UserCluesExtendDTO();
        userCluesExtendDTO.setSite_id(4643L);
        userCluesExtendDTO.setChannelCode("s0022");
        userCluesExtendDTO.setCate("ch00220001");
        userCluesExtendDTO.setPhone(queryStoreOutDto.getUserPhone());
        userCluesExtendDTO.setFirstname(queryStoreOutDto.getUserPhone());
        userCluesExtendDTO.setUtm_source(queryStoreOutDto.getUtm_source());
        try {
            Result result = marketClientService.addUserCluesForBrand(userCluesExtendDTO);
            log.info("留资结果：" + JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("门店留资异常：" + e.getMessage());
        }
        return send;
    }

    public List<QueryStoreOutDto> storeNameAssociation(QueryStoreInDto queryStoreInDto) {
        return orgClientService.storeNameAssociation(queryStoreInDto).getData();
    }

    public PageInfo<QueryStoreOutDto> fuzzyQueryByStoreName(QueryStoreInDto inDto) {
        if (inDto.getPagination() == null || inDto.getSize() == null) {
            inDto.setPagination(0);
            inDto.setSize(3);
        }
        Result<PageInfo<QueryStoreOutDto>> result = orgClientService.fuzzyQueryByStoreName(inDto);
        result.getData().getList().stream().forEach(r -> {
            if (StringUtils.isEmpty(r.getTel())){
                r.setTel("");
            }
            if (StringUtils.isEmpty(r.getAddress())){
                r.setAddress("");
            }
            if (StringUtils.isEmpty(r.getShowAddress())){
                r.setShowAddress("");
            }
            if (StringUtils.isEmpty(r.getUserPhone())){
                r.setUserPhone("");
            }
        });
        return result.getData();
    }


    /**
     * 根据市的编码分页查询门店简略信息
     *
     * @param setAreaType
     * @return
     */
    public PageInfo<QueryStoreOutDto> findPlainStoreInfoByAreaCodeAndType(QueryStoreInDto setAreaType) {
        Result<PageInfo<QueryStoreOutDto>> result = orgClientService.findPlainStoreInfoByAreaCodeAndType(setAreaType);
        if (result.getData() != null && result.getData().getList() != null && result.getData().getList().size() > 0) {
            result.getData().getList().forEach(r ->{
                if (StringUtils.isEmpty(r.getTel())){
                    r.setTel("");
                }
                if (StringUtils.isEmpty(r.getAddress())){
                    r.setAddress("");
                }
                if (StringUtils.isEmpty(r.getShowAddress())){
                    r.setShowAddress("");
                }
                if (StringUtils.isEmpty(r.getUserPhone())){
                    r.setUserPhone("");
                }
            });
        }
        return result.getData();
    }

    /**
     * 根据经纬度查询最近的几个门店
     *
     * @param inDto
     * @return
     */
    public List<QueryStoreOutDto> findStoresByCoordinate(QueryStoreInDto inDto) {
        return orgClientService.findStoresByCoordinate(inDto).getData();
    }

    /**
     * 根据id查找门店详细信息
     *
     * @param id
     * @return
     */
    public CommonStoreDto findStoreById(Long id) {
        return orgClientService.findStoreById(id).getData();
    }
}
