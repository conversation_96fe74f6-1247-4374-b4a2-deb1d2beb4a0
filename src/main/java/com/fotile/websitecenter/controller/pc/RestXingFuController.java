package com.fotile.websitecenter.controller.pc;

import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.framework.web.BaseController;
import com.fotile.websitecenter.client.CmsOpenClientService;
import com.fotile.websitecenter.client.MsgClientService;
import com.fotile.websitecenter.client.pojo.*;
import com.fotile.websitecenter.common.ChannelInfo;
import com.fotile.websitecenter.service.InformationService;
import com.fotile.websitecenter.service.XingFuService;
import com.fotile.framework.core.common.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @ClassName RestXingFuController
 * @Description: 幸福家首页列表切换异步刷新
 * <AUTHOR>
 * @Date 2020/4/7
 * @Version
 **/
@RestController
@RequestMapping("")
@Slf4j
public class RestXingFuController extends BaseController {
    @Autowired
    private XingFuService xingFuService;

    @Autowired
    private CmsOpenClientService cmsOpenClientService;
    @Autowired
    private InformationService informationService;
    @Autowired
    private MsgClientService msgClientService;

    /**
     * 幸福家首页列表切换
     *
     * @param getContentListInDto
     * @return
     */
    @GetMapping(value = "content/getContentJson", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<GetContentIdsOut>> getContentJson(GetContentListInDto getContentListInDto) {
        getContentListInDto.setChannelId(ChannelInfo.PC_CHANNEL_ID);
        return success(xingFuService.getKitchenLifeSecond(getContentListInDto));
    }

    /**
     * 发布评论
     *
     * @param commentInfo
     * @return
     */
    @PostMapping(value = "detail/addComment", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> addComment(@RequestBody CommentInfo commentInfo) {
        return xingFuService.addComment(commentInfo);
    }


    /**
     * 厨房剧场列表切换
     */
    @GetMapping(value = "detail/videoListJson", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<GetContentIdsOut>> videoList(GetContentListInDto getContentListInDto) {
        getContentListInDto.setChannelId(ChannelInfo.PC_CHANNEL_ID);
        return success(success(xingFuService.videoList(getContentListInDto)));
    }

    /**
     * 回复
     *
     * @param commentDetails
     * @return
     */
    @PostMapping(value = "detail/answer", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> answer(@RequestBody CommentDetails commentDetails) {
        xingFuService.answer(commentDetails);
        return success("回复成功!");
    }

    /**
     * 评论列表
     *
     * @param getCommentsJsonInDto
     * @return
     */
    @GetMapping(value = "detail/getCommentsJson", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<CommentsOutDto>> getCommentsJson(GetCommentsJsonInDto getCommentsJsonInDto) {
        return success(success(xingFuService.getCommentsJson(getCommentsJsonInDto)));
    }

    /**
     * 我要提问
     *
     * @param addFeedBackInDto
     * @return
     */
    @PostMapping(value = "detail/addFeedBack", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> addFeedBack(@RequestBody AddFeedBackInDto addFeedBackInDto) {
        addFeedBackInDto.setChannelSource(ChannelInfo.PC_CHANNEL_ID + "");
        xingFuService.addFeedBack(addFeedBackInDto);
        return success("操作成功!");
    }

    /**
     * 点赞
     * 内容点赞table=content   评论点赞table=comment_info   回复点赞table=comment_detail
     *
     * @param contentId
     * @return
     */
    @GetMapping(value = "content/praise", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Result toPraise(String table, Long contentId) {
        StatisticsDTO statisticsDTO = new StatisticsDTO();
        statisticsDTO.setContentId(contentId);
        statisticsDTO.setLikeCount(1L);
        statisticsDTO.setTable(table);
        return cmsOpenClientService.contentStaticUpdate(statisticsDTO);
    }

    /**
     * 收藏
     *
     * @param collectionInDto
     * @return
     */
    @PostMapping(value = "content/collection", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> collection(@RequestBody CollectionInDto collectionInDto) {
        return xingFuService.collection(collectionInDto);
    }

    /**
     * 加盟
     */
    @PostMapping(value = "dealers/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result add(@RequestBody AddInDto addInDto) {
        try{
            ValidCaptchaRequestDTO validCaptchaRequestDto = new ValidCaptchaRequestDTO();
            validCaptchaRequestDto.setTemplateid(addInDto.getTemplateid());
            validCaptchaRequestDto.setMobile(addInDto.getMobile());
            validCaptchaRequestDto.setCode(addInDto.getCode());
            Result validcode = msgClientService.validcode(validCaptchaRequestDto);
            if(validcode.getSuccess()) {
                return cmsOpenClientService.add(addInDto);
            }else{
                return validcode;
            }
        } catch (BusinessException e) {
            log.error(e.getMessage());
            return failure(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
            return failure("system异常");
        }
    }

    /**
     * 联盟成员
     */
    @GetMapping(value = "friend/chains", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result chains() {
        Map<Long, List<FriendChain>> result = informationService.getFriendChain(Arrays.asList(4, 5));
        return success(result);
    }
}
