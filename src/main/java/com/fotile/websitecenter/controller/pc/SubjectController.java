package com.fotile.websitecenter.controller.pc;

import com.fotile.websitecenter.client.pojo.QueryWebsiteInformationsInDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @ClassName AboutController
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/4/1
 * @Version
 **/
@Controller
@RequestMapping("/subject")
public class SubjectController {
/**
 * 换装服务
 */
@GetMapping(value = "/about.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView about() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Brand/about");
    return modelAndView;
}

/**
 * 集成烹饪中心
 */
@GetMapping(value = "/xf2019.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView xf2019() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/xf2019");
    return modelAndView;
}

@GetMapping(value = "/jcprzx2.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jcprzx2() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/jcprzx2");
    return modelAndView;
}

@GetMapping(value = "/jcprzxX1.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jcprzxX1() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/jcprzxX1");
    return modelAndView;
}
@GetMapping(value = "/X20.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView X20() {
     ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/X20");
     return modelAndView;
    }
/**
 * 云魔方
 */
@GetMapping(value = "/ymf2017.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView ymf2017() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/youyanji/ymf2017");
    return modelAndView;
}

/**
 * 风魔方
 */
@GetMapping(value = "/fmf2018.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView fmf2018() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/fmf2018/index");
    return modelAndView;
}

/**
 * 嵌入式消毒柜
 */
@GetMapping(value = "/qrsxdg.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView qrsxdg() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/qrsxdg");
    return modelAndView;
}

/**
 * 消毒柜
 */
@GetMapping(value = "/xdgkm.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView xdgkm() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/xdgkm");
    return modelAndView;
}

/**
 * 蒸微一体机
 */
@GetMapping(value = "/zwytj.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView zwytj() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/zwytj");
    return modelAndView;
}

/**
 * 消毒柜升级
 */
@GetMapping(value = "/jkcd2020.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jkcd2020() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/jkcd2020");
    return modelAndView;
}

/**
 * 蒸烤烹饪机
 */
@GetMapping(value = "/zkprj2019.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView zkprj2019() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/zkprj2019");
    return modelAndView;
}

/**
 * 嵌入式蒸箱
 */
@GetMapping(value = "/qrszx.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView qrszx() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/qrszx");
    return modelAndView;
}
    /**
     * ai蒸箱
     */
    @GetMapping(value = "/aizhengxiang.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView aizhengxiang() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/aizhengxiang");
        return modelAndView;
    }
/**
 * 嵌入式烤箱
 */
@GetMapping(value = "/qrskx.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView qrskx() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/qrskx");
    return modelAndView;
}

/**
 *
 */
@GetMapping(value = "/ha30b.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView ha30b() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/zaoju/ha30b");
    return modelAndView;
}

/**
 * 嵌入式微波炉
 */
@GetMapping(value = "/qrswbl.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView qrswbl() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/qrswbl");
    return modelAndView;
}

/**
 * 水槽洗碗机
 */
@GetMapping(value = "/scxwj701.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView scxwj701() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/shuicao/scxwjq7_slide01");
    return modelAndView;
}

@GetMapping(value = "/scxwj702.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView scxwj702() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/shuicao/scxwjq7_slide02");
    return modelAndView;
}

@GetMapping(value = "/scxwj703.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView scxwj703() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/shuicao/scxwjq7_slide03");
    return modelAndView;
}

@GetMapping(value = "/scxwj7.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public String scxwj7() {
//    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/shuicao/scxwjq7");
//    return modelAndView;
//    if (StringUtils.isNotBlank(publish) && "active".equals(publish)) {
//        if (StringUtils.isNotBlank(utm_source)) {
//            return "redirect:"+url+"#/?utm_source=" + utm_source;
//        }
        return "redirect:/chanpin/559/";
//    }
}

/**
 * 净水机
 */
@GetMapping(value = "/jingshuiji.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jingshuiji() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/cover/jingshuiji");
    return modelAndView;
}

/**
 * 净水机2
 */
@GetMapping(value = "/jingshui2019.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jingshui2019() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/pcindex");
    return modelAndView;
}
    @GetMapping(value = "/jingshui2020.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jingshui2020() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/jingshui2020");
        return modelAndView;
    }

/**
 * jem7
 */
@GetMapping(value = "/jem7.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jem7() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/jem7");
    return modelAndView;
}

/**
 * Fiks
 */
@GetMapping(value = "/fiks.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView fiks() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/fiks/index");
    return modelAndView;
}

/**
 * 換裝
 */
//@GetMapping(value = "/Huanzhuang.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//public ModelAndView Huanzhuang() {
//    ModelAndView modelAndView = new ModelAndView("src/View/pc/Huanzhuang/index");
//    return modelAndView;
//}

/**
 * 电磁灶
 */
@GetMapping(value = "/diancizao.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView diancizao() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/diancizao");
    return modelAndView;
}

/**
 * 人文专题
 */
@GetMapping(value = "/humanism.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView humanism() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Humanism/index");
    return modelAndView;
}

@GetMapping(value = "/humanism6.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView humanism6() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Humanism/p6");
    return modelAndView;
}

//方太地图
@GetMapping(value = "/map.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView map() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Humanism/map");
    return modelAndView;
}

//
@GetMapping(value = "/jacbs.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView jacbs() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/jacbs");
    return modelAndView;
}

//
@GetMapping(value = "/chat.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView chat() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Chat/index-20181008");
    return modelAndView;
}

//test
@GetMapping(value = "/test.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView test() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/test");
    return modelAndView;
}
  //热门资讯
 @GetMapping(value = "/hot.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ModelAndView hot() {
      ModelAndView modelAndView = new ModelAndView("src/View/pc/hot");
      return modelAndView;
  }

//方太创新
@GetMapping(value = "/meishan.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView meishan() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/meishan");
    return modelAndView;
}

//2019年
@GetMapping(value = "/live.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView live() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/live");
    return modelAndView;
}

//2019年发布会直播页
@GetMapping(value = "/bingduzibai.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public ModelAndView bingduzibai() {
    ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/bingduzibai");
    return modelAndView;
}

    //imesfabuhui
    @GetMapping(value = "/imesfabuhui.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView imesfabuhui() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/imesfabuhui");
        return modelAndView;
    }

    @GetMapping(value = "/jcprzxNew.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jcprzxNew() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/jcprzxNew");
        return modelAndView;
    }
    @GetMapping(value = "/jc2.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jc2() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/jc2");
        return modelAndView;
    }
    /**
     * X10
     */
    @GetMapping(value = "/X10.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView X10() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/X10");
        return modelAndView;
    }
    /**
     * jcprZ
     */
    @GetMapping(value = "/jcprZ.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jcprZ() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/jcprZ");
        return modelAndView;
    }
    /**
     * jcprJ
     */
    @GetMapping(value = "/jkprJ.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jkprJ() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/jkprJ");
        return modelAndView;
    }
    /**
     * EM7TA
     */
    @GetMapping(value = "/EM7TA.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView EM7TA() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/EM7TA");
        return modelAndView;
    }
    /**
     * 百人大咖
     */
    @GetMapping(value = "/celebrity.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView celebrity() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/celebrity");
        return modelAndView;
    }
    /**
     * 方太高端集成灶
     */
    @GetMapping(value = "/integratedCooker.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView integratedCooker() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/integratedCooker");
        return modelAndView;
    }
    /**
     * 方太imes智联油烟净化系统
     */
    @GetMapping(value = "/imes.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView imes() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/imes");
        return modelAndView;
    }
    /**
     * 方太高端集成灶1
     */
    @GetMapping(value = "/IntegratedStove.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView IntegratedStove() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hd/IntegratedStove");
        return modelAndView;
    }

    @GetMapping(value = "/Z5.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView Z5() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Z5");
        return modelAndView;
    }
    @GetMapping(value = "/E5.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView E5() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/E5");
        return modelAndView;
    }
    // 明星的家
    // @GetMapping(value = "/yueYingHome.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    // public ModelAndView yueYingHome() {
    //     ModelAndView modelAndView = new ModelAndView("src/View/pc/yueYingHome");
    //     return modelAndView;
    // }
    /**
     * 极火®直喷Ⅲ代系列燃气灶
     */
    @GetMapping(value = "/fire3.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fire3() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/fire3");
        return modelAndView;
    }

     /**
     * 商用净水器
     */
    @GetMapping(value = "/clearwater.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater");
        return modelAndView;
    }
    @GetMapping(value = "/clearwater01.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater01() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater01");
        return modelAndView;
    }
    @GetMapping(value = "/clearwater02.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater02() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater02");
        return modelAndView;
    }
    @GetMapping(value = "/clearwater03.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater03() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater03");
        return modelAndView;
    }
    @GetMapping(value = "/clearwater04.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater04() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater04");
        return modelAndView;
    }
    @GetMapping(value = "/clearwater05.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView clearwater05() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/clearwater05");
        return modelAndView;
    }
//    洗碗机y系列pc
    @GetMapping(value = "/dishWashingY.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView dishWashingY() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/dishWashing");
        return modelAndView;
    }
    @GetMapping(value = "/dishWashing1.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView dishWashing1() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/dishWashing1");
        return modelAndView;
    }
    @GetMapping(value = "/dishWashing2.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView dishWashing2() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/dishWashing2");
        return modelAndView;
    }
    @GetMapping(value = "/dishWashing3.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView dishWashing3() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/dishWashing3");
        return modelAndView;
    }
//    集烹Y系列pc
    @GetMapping(value = "/cookie.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView cookie() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/cookie");
        return modelAndView;

    }
    // X1S系列烟机
    @GetMapping(value = "/X1Sseries.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView X1Sseries() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/series/X1Sseries");
        return modelAndView;
    }

    // Z系列烟机
    @GetMapping(value = "/zSeries.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView Zseries() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Hero/series/Zseries");
        return modelAndView;
    }
//
    @GetMapping(value = "/officialWebTech.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView officialWebTech() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/officialWebTech");
        return modelAndView;
    }
    // 高端全场景厨电专题页
    @GetMapping(value = "/kitchenAppliance.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView kitchenAppliance(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/kitchenAppliance");

        return modelAndView;
    }
//    j系列
    @GetMapping(value = "/jseries.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jseries(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/jseries");

        return modelAndView;
    }
    //净水
    @GetMapping(value = "/purifier.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView purifier(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/purifier");

        return modelAndView;
    }

    //集成烹饪中心j系列
    @GetMapping(value = "/integratedJSeries.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView IntegratedJSeries(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/integratedJSeries");

        return modelAndView;
    }
    //HR7
    @GetMapping(value = "/HR7.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView HR7(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/HR7");

        return modelAndView;
    }
    //HR7
    @GetMapping(value = "/fridge.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge");

        return modelAndView;
    }

    //第二代集成烹饪中心
    @GetMapping(value = "/integratedCookingCenter.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView integratedCookingCenter(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/integratedCookingCenter");

        return modelAndView;
    }

    // 23244-【官网】睿作套系专题页开发
    @GetMapping(value = "/ruizuo.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView ruizuo(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/ruizuo");
        return modelAndView;
    }
    //50337-23245【官网】集成净饮中心专题页
    @GetMapping(value = "/jingyin.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView jingyin(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/jingyin");
        return modelAndView;
    }


    //    50405-23258 【官网】二代冰箱专题页开发
    @GetMapping(value = "/fridge1.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge1(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridgeNew");

        return modelAndView;
    }
    @GetMapping(value = "/fridgeNew.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridgeNew(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridgeNew");

        return modelAndView;
    }
    @GetMapping(value = "/fridge2.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge2(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge2");

        return modelAndView;
    }
    @GetMapping(value = "/fridge3.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge3(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge3");

        return modelAndView;
    }

    @GetMapping(value = "/fridge4.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge4(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge4");

        return modelAndView;
    }

    @GetMapping(value = "/fridge5.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge5(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge5");

        return modelAndView;
    }
    @GetMapping(value = "/fridge6.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge6(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge6");

        return modelAndView;
    }
    @GetMapping(value = "/fridge7.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView fridge7(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/fridge7");

        return modelAndView;
    }
    @GetMapping(value = "/cloudShadow.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView cloudShadow(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/specialpage/cloudShadow");

        return modelAndView;
    }
    //看案例
    @GetMapping(value = "/caseList.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView caseList() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Product/caseList");
        return modelAndView;
    }
    //案例详情
    @GetMapping(value = "/caseDetail.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView caseDetail() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Product/caseDetail");
        return modelAndView;
    }
    //菜谱详情
    @GetMapping(value = "/menuDetail.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView menuDetail() {
        ModelAndView modelAndView = new ModelAndView("src/View/pc/Product/menuDetail");
        return modelAndView;
    }
}
