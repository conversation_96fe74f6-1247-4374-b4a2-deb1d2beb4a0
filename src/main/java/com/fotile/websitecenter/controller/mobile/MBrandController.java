package com.fotile.websitecenter.controller.mobile;


import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.websitecenter.client.CmsOpenClientService;
import com.fotile.websitecenter.client.pojo.AddInDto;
import com.fotile.websitecenter.client.pojo.QueryInformationByIdOutDto;
import com.fotile.websitecenter.client.pojo.QueryWebsiteInformationsInDto;
import com.fotile.websitecenter.client.pojo.QueryWebsiteInformationsOutDto;
import com.fotile.websitecenter.common.ChannelInfo;
import com.fotile.websitecenter.service.DicService;
import com.fotile.websitecenter.service.InformationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Controller
@RequestMapping("h5")
@Slf4j
public class MBrandController extends BaseController {
    @Autowired
    private InformationService informationService;
    @Autowired
    private DicService dicService;
    @Autowired
    private CmsOpenClientService cmsOpenClientService;
    @Autowired
    private RedisTemplate redisTemplate;

    @GetMapping(value = "/brand/provisions.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mprovisions() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/provisions");
        //删除缓存
        try {
            RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();
            try {
                connection.flushDb();
            } catch (Exception e) {
                log.error("清除缓存异常：" + e.getMessage());
            }finally {
                connection.close();
            }
        } catch (Exception e) {
            log.error("清除缓存异常：" + e.getMessage());
        }
        return modelAndView;
    }

    @GetMapping(value = "/brand/about.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mabout() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/about");
        return modelAndView;
    }

    @GetMapping(value = "/brand/contact.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mcontact() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/contact");
        return modelAndView;
    }

    @GetMapping(value = "/brand/power.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView power() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/power");
        return modelAndView;
    }

    @GetMapping(value = "/brand/dealer.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mdealer() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/dealer");
        return modelAndView;
    }

    @GetMapping(value = "/brand/index.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mindex() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/index");
        return modelAndView;
    }

    @GetMapping(value = "/brand/obligation.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mobligation() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/obligation");
        return modelAndView;
    }

    @GetMapping(value = "/brand/alliance.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView alliance() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/alliance");
        return modelAndView;
    }

    @GetMapping(value = "/video.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mvideo(QueryWebsiteInformationsInDto queryInformationsInDto) {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/video");
        queryInformationsInDto.setChannelId(ChannelInfo.H5_CHANNEL_ID);
        queryInformationsInDto.setFirstType("2");
        PageInfo<QueryWebsiteInformationsOutDto> res = informationService.queryAllInformationsWebsite(queryInformationsInDto);
        modelAndView.addObject("res", success(res));
        Map<String, String> dic = dicService.getDicNameCode("image_classification");
        modelAndView.addObject("dic", dic);
        return modelAndView;
    }

    @GetMapping(value = "/brand/rest/video", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Result<PageInfo<QueryWebsiteInformationsOutDto>> restVideo(String secondType, Integer page, Integer size) {
        QueryWebsiteInformationsInDto queryInformationsInDto = new QueryWebsiteInformationsInDto();
        queryInformationsInDto.setChannelId(ChannelInfo.H5_CHANNEL_ID);
        queryInformationsInDto.setFirstType("2");
        queryInformationsInDto.setPage(page);
        queryInformationsInDto.setSize(size);
        queryInformationsInDto.setSecondType(secondType);
        PageInfo<QueryWebsiteInformationsOutDto> res = informationService.queryAllInformationsWebsite(queryInformationsInDto);
        return success(res);
    }

    @GetMapping(value = "/video/{id}.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mvideoView(@PathVariable("id") Long id) {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/videoView");
        QueryInformationByIdOutDto res = informationService.querylByIdWebsite(id, ChannelInfo.H5_CHANNEL_ID);
        Map<String, String> dic = dicService.getDicCodeName("image_classification");
        if (res != null) {
            if (!dic.isEmpty()) {
                res.setSecondType(dic.get(res.getSecondType()));
            }
        }
        modelAndView.addObject("res", success(res));
        if (res != null && StringUtils.isNotBlank(res.getContent())) {
            Pattern p_html = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);
            Matcher m_html = p_html.matcher(res.getContent());
            String content = m_html.replaceAll("");
            if (StringUtils.isNotBlank(content) && content.length() > 75) {
                content = content.substring(0, 75);
            }
            modelAndView.addObject("description", content);
        }
        return modelAndView;
    }

    /**
     * 加盟
     */
    @PostMapping(value = "/brand/dealers/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Result add(@RequestBody AddInDto addInDto) {
        return cmsOpenClientService.add(addInDto);
    }

    /**
     * 发展历程
     */
    @GetMapping(value = "/brand/history.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView history() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/brand/history");

        return modelAndView;
    }

}
