package com.fotile.websitecenter.controller.mobile;

import com.fotile.framework.web.Result;
import com.fotile.framework.web.BaseController;
import com.fotile.websitecenter.client.pojo.GetFirstCategoryInDto;
import com.fotile.websitecenter.common.ChannelInfo;
import com.fotile.websitecenter.service.ChannelCategoryService;
import com.fotile.websitecenter.service.XingFuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用类？
 */
@Controller
@RequestMapping("h5")
public class MCommonController extends BaseController {
    @Autowired
    private ChannelCategoryService channelCategoryService;


    @GetMapping(value = "common/footer.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mfooter() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/common/footer");
        Map<String, String> category =new HashMap<>();
        List<GetFirstCategoryInDto> categoryList = channelCategoryService.getFirstCategoryMap(ChannelInfo.PC_CHANNEL_CODE);
        modelAndView.addObject("categoryList", categoryList);
        return modelAndView;
    }

    @GetMapping(value = "common/header.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mheader() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/common/header");
        Map<String, String> category =new HashMap<>();
        List<GetFirstCategoryInDto> categoryList = channelCategoryService.getFirstCategoryMap(ChannelInfo.PC_CHANNEL_CODE);
        modelAndView.addObject("categoryList", categoryList);
        return modelAndView;
    }


    /**
     * 获取产品品类
     */
    @GetMapping(value = "headerInfo/headerInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Result<List<GetFirstCategoryInDto>> restHeader() {
        Map<String, String> category =new HashMap<>();
        List<GetFirstCategoryInDto> categoryList = channelCategoryService.getFirstCategoryMap(ChannelInfo.PC_CHANNEL_CODE);
        return success(categoryList);
    }

    @GetMapping(value = "common/js.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mcommonJs() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/common/js");
        return modelAndView;
    }

    @GetMapping(value = "common/xingfuJs.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView xingfuJs() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/common/xingfuJs");
        return modelAndView;
    }

    @GetMapping(value = "common/login.html", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ModelAndView mcommonLogin() {
        ModelAndView modelAndView = new ModelAndView("src/mobile/common/login");
        return modelAndView;
    }
}
