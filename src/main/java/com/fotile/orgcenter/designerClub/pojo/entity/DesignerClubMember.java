package com.fotile.orgcenter.designerClub.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fotile.orgcenter.salesman.pojo.annotation.CompareField;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024/7/26 9:56
 */

/**
 * 设计师俱乐部会员信息表
 */
@Data
@TableName(value = "t_designer_club_member", schema = "orgcenter")
public class DesignerClubMember {
    /**
     * 设计师俱乐部会员表ID,PK,AI
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户账号ID(keycloakId) 唯一
     */
    @TableField(value = "user_id", updateStrategy = FieldStrategy.IGNORED)
    private String userId;

    /**
     * 会员编码
     */
    @TableField("code")
    private String code;

    /**
     * 会员手机号[加密]
     */
    @TableField(value = "phone", updateStrategy = FieldStrategy.IGNORED)
    @FieldEncrypt
    @CompareField(name = "手机号")
    private String phone;

    /**
     * 会员名称[加密]
     */
    @TableField(value = "`name`", updateStrategy = FieldStrategy.IGNORED)
    @FieldEncrypt
    @CompareField(name = "姓名")
    private String name;

    /**
     * 会员昵称[加密]
     */
    @TableField(value = "nickname", updateStrategy = FieldStrategy.IGNORED)
    @FieldEncrypt
    @CompareField(name = "昵称")
    private String nickname;

    /**
     * 注册渠道编码(t_channel.code)
     */
    @TableField(value = "register_channel_code", updateStrategy = FieldStrategy.IGNORED)
    private String registerChannelCode;

    /**
     * 注册频道编码(t_radio.code)
     */
    @TableField(value = "register_radio_code", updateStrategy = FieldStrategy.IGNORED)
    private String registerRadioCode;

    @TableField(exist = false)
    private String unionId;

    /**
     * 注册时间
     */
    @TableField(value = "register_time", updateStrategy = FieldStrategy.IGNORED)
    private Date registerTime;

    /**
     * 会员等级(1:注册会员; 2:认证会员; 默认1)
     */
    @TableField("grade")
    private String grade;

    /**
     * 头像(http地址)
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 照片(http地址)
     */
    @TableField("photo")
    private String photo;

    /**
     * 邮箱地址
     */
    @TableField("email")
    @FieldEncrypt
    private String email;

    /**
     * 个人简介
     */
    @TableField("profiles")
    private String profiles;

    /**
     * 出生年月日(yyyy-MM-dd)
     */
    @TableField(value = "birthday", updateStrategy = FieldStrategy.IGNORED)
    private Date birthday;

    /**
     * 性别(1:男; 2:女)
     */
    @TableField("gender")
    private String gender;

    /**
     * 权益金账户ID(pointcenter.benefits_designer.id)
     */
    @TableField(value = "benefit_account_id", updateStrategy = FieldStrategy.IGNORED)
    private Long benefitAccountId;

    /**
     * 推荐会员ID(t_designer_club_member.id)
     */
    @TableField(value = "referee_member_id", updateStrategy = FieldStrategy.IGNORED)
    private Long refereeMemberId;

    /**
     * 推荐业务员ID(t_salesman.id)
     */
    @TableField(value = "referee_charge_user_id", updateStrategy = FieldStrategy.IGNORED)
    private Long refereeChargeUserId;

    /**
     * 推广认证业务员ID(t_salesman.id)
     */
    @TableField(value = "referee_auth_charge_user_id", updateStrategy = FieldStrategy.IGNORED)
    private Long refereeAuthChargeUserId;

    /**
     * 推荐业务员信息(文本填写,用户分配业务员参考)
     */
    @TableField(value = "referee_charge_user", updateStrategy = FieldStrategy.IGNORED)
    private String refereeChargeUser;

    /**
     * 工作类型(1:公司就职[家装设计师]; 2:自由设计师[私人设计师])
     */
    @TableField(value = "work_type", updateStrategy = FieldStrategy.IGNORED)
    private String workType;

    /**
     * 就职公司(文本填写)
     */
    @TableField(value = "work_company", updateStrategy = FieldStrategy.IGNORED)
    private String workCompany;

    /**
     * 职务(来源于字典dic.designer_club_member_post)
     */
    @TableField(value = "post", updateStrategy = FieldStrategy.IGNORED)
    @CompareField(name = "职务")
    private String post;

    /**
     * 所属地区-省份编码(systemcenter.area.area_code)
     */
    @TableField(value = "province_code", updateStrategy = FieldStrategy.IGNORED)
    private Long provinceCode;

    @TableField(value = "province_name", updateStrategy = FieldStrategy.IGNORED)
    private String provinceName;

    /**
     * 所属地区-城市编码(systemcenter.area.area_code)
     */
    @TableField(value = "city_code", updateStrategy = FieldStrategy.IGNORED)
    private Long cityCode;

    @TableField(value = "city_name", updateStrategy = FieldStrategy.IGNORED)
    private String cityName;

    /**
     * 所属地区-区编码(systemcenter.area.area_code)
     */
    @TableField(value = "district_code", updateStrategy = FieldStrategy.IGNORED)
    private Long districtCode;

    @TableField(value = "district_name", updateStrategy = FieldStrategy.IGNORED)
    private String districtName;

    /**
     * 所属地区-详细地址[加密]
     */
    @TableField(value = "address", updateStrategy = FieldStrategy.IGNORED)
    @CompareField(name = "详细地址")
    private String address;

    /**
     * 主攻方向(来源字典,多个使用半角逗号分隔,dic.main_attack.value_code)
     */
    @TableField(value = "main_attack", updateStrategy = FieldStrategy.IGNORED)
    private String mainAttack;

    /**
     * 个性标签(来源字典,多个使用半角逗号分隔,dic.personal_tags.value_code)
     */
    @TableField(value = "personal_tags", updateStrategy = FieldStrategy.IGNORED)
    private String personalTags;

    /**
     * 设计风格(来源字典,多个使用半角逗号分隔,dic.design_styles.value_code)
     */
    @TableField(value = "design_styles", updateStrategy = FieldStrategy.IGNORED)
    private String designStyles;

    /**
     * 设计收费标准(来源字典dic.design_fee_range.value_code)
     */
    @TableField(value = "design_fee_range", updateStrategy = FieldStrategy.IGNORED)
    private String designFeeRange;

    /**
     * 教育学校ID(t_school.id)
     */
    @TableField(value = "school_id", updateStrategy = FieldStrategy.IGNORED)
    private Long schoolId;

    /**
     * 入学日期(yyyy-MM-dd)
     */
    @TableField(value = "enrollment_date", updateStrategy = FieldStrategy.IGNORED)
    private String enrollmentDate;

    /**
     * 毕业日期(yyyy-MM-dd)
     */
    @TableField("graduation_date")
    private String graduationDate;

    @TableField(value = "company_org_id", updateStrategy = FieldStrategy.IGNORED)
    private Long companyOrgId;


    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * UTM_SOURCE
     */
    @TableField("utm_source")
    private String utmSource;

    /**
     * 是否接收微信通知(0:否; 1:是, 默认0)
     */
    @TableField(value = "receive_wx_notice", updateStrategy = FieldStrategy.IGNORED)
    private String receiveWxNotice;

    /**
     * 是否删除(0:否; 1:是, 默认0)
     */
    @TableField("is_deleted")
    private Integer isDeleted = 0;

    /**
     * 创建人ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 修改人ID
     */
    @TableField("modified_by")
    private String modifiedBy;

    /**
     * 修改时间
     */
    @TableField("modified_date")
    private Date modifiedDate;

    /**
     * 创建来源(signup:登录注册; auth:提交认证申请)
     */
    @TableField(exist = false)
    private String createFrom;
}