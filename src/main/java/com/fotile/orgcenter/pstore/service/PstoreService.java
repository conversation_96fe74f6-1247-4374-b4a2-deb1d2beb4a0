package com.fotile.orgcenter.pstore.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.orgcenter.client.CmsClient2;
import com.fotile.orgcenter.client.pojo.FindContentTemplateListDTO;
import com.fotile.orgcenter.client.pojo.ImContentTemplateEntity;
import com.fotile.orgcenter.company.enums.CompanyConstants;
import com.fotile.orgcenter.company.mapper.CompanyDao;
import com.fotile.orgcenter.company.pojo.entity.CompanyEntity;
import com.fotile.orgcenter.company.pojo.entity.CompanyGd;
import com.fotile.orgcenter.company.service.CompanyGdService;
import com.fotile.orgcenter.distributor.dao.DistributorDao;
import com.fotile.orgcenter.distributor.pojo.DistributorEntity;
import com.fotile.orgcenter.distributor.pojo.dto.FindDistributorByStoreIdInDto;
import com.fotile.orgcenter.pstore.mapper.PstoreDao;
import com.fotile.orgcenter.pstore.mapper.TCompanyAreaStreetMapper;
import com.fotile.orgcenter.pstore.mapper.TStreetMappingMapper;
import com.fotile.orgcenter.pstore.pojo.TCompanyAreaMapping;
import com.fotile.orgcenter.pstore.pojo.TCompanyAreaStreetEntity;
import com.fotile.orgcenter.pstore.pojo.TStreetMappingEntity;
import com.fotile.orgcenter.pstore.pojo.dto.*;
import com.fotile.orgcenter.salesman.mapper.SalesmanDao;
import com.fotile.orgcenter.salesman.pojo.entity.SalesmanEntity;
import com.fotile.orgcenter.store.mapper.StoreDao;
import com.fotile.orgcenter.store.pojo.entity.StoreEntity;
import com.fotile.orgcenter.utils.WeightRoundRobinUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 3009-[线索]根据省市区自动匹配到门店service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class PstoreService {

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private PstoreDao pstoreDao;

    @Autowired
    private DistributorDao distributorDao;

    @Autowired
    private TCompanyAreaMappingService tCompanyAreaMappingService;

    @Autowired
    private SalesmanDao salesmanDao;
    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private StoreDao storeDao;

    @Autowired
    private TCompanyAreaStreetMapper tCompanyAreaStreetMapper;

    @Autowired
    private TStreetMappingMapper tStreetMappingMapper;

    @Autowired
    private CompanyGdService companyGdService;

    @Autowired
    private CmsClient2 cmsClient2;

    /**
     * 分页查询
     *
     * @param findPstoreAllInDto
     * @return
     */
    public PageInfo<FindPstoreAllOutDto> findPageAll(FindPstoreAllInDto findPstoreAllInDto) {
        log.info("******PstoreService.findAll 条件： findPstoreAllInDto=" + findPstoreAllInDto);
        PageInfo<FindPstoreAllOutDto> pageInfo = new PageInfo<FindPstoreAllOutDto>(findPstoreAllInDto.getPage(), findPstoreAllInDto.getSize());

        if (findPstoreAllInDto.getWithoutCompanyDataScope() != null && findPstoreAllInDto.getWithoutCompanyDataScope() == 1) {
            findPstoreAllInDto.setCompanyAuthorList(null);
        } else {
            findPstoreAllInDto.setCompanyAuthorList(userAuthorConfig.queryCompanyAuthorList());
        }

        pageInfo.setTotal(pstoreDao.findPageAllTotal(findPstoreAllInDto));
        List<FindPstoreAllOutDto> list = pstoreDao.findPageAll(findPstoreAllInDto, pageInfo);
        if (list != null && list.size() > 0) {
            QueryWrapper<TCompanyAreaMapping> wrapper = new QueryWrapper<>();
            wrapper.in("company_area_id", list.stream().map(FindPstoreAllOutDto::getId).collect(Collectors.toList()));
            wrapper.eq("is_deleted", 0);
            List<TCompanyAreaMapping> tCompanyAreaMappings = tCompanyAreaMappingService.list(wrapper);
            List<Long> storeIds = null;
            if (CollectionUtils.isNotEmpty(tCompanyAreaMappings)) {
                storeIds = tCompanyAreaMappings.stream().filter(tCompanyAreaMapping -> tCompanyAreaMapping.getStoreOrgId() != null).map(TCompanyAreaMapping::getStoreOrgId).distinct().collect(Collectors.toList());
            }
            List<StoreEntity> byOrgIdList = null;
            if (CollectionUtils.isNotEmpty(storeIds)) {
                byOrgIdList = storeDao.findByOrgIdList(storeIds);
            }

            List<Long> salesmanIds = null;
            if (CollectionUtils.isNotEmpty(tCompanyAreaMappings)) {
                salesmanIds = tCompanyAreaMappings.stream().map(TCompanyAreaMapping::getChargeUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            }
            List<SalesmanEntity> salesmanEntities = null;
            if (CollectionUtils.isNotEmpty(salesmanIds)) {
                salesmanEntities = salesmanDao.findByIds(salesmanIds);
            }
            List<StoreEntity> finalByOrgIdList = byOrgIdList;
            //获取街道信息
            QueryWrapper<TCompanyAreaStreetEntity> queryWrapper =
                    new QueryWrapper<TCompanyAreaStreetEntity>()
                            .in(TCompanyAreaStreetEntity.COL_COMPANY_AREA_ID, list.stream().map(FindPstoreAllOutDto::getId).collect(Collectors.toList()))
                            .eq(TCompanyAreaStreetEntity.COL_IS_DELETED, 0);

            List<TCompanyAreaStreetEntity> tCompanyAreaStreetEntityList = tCompanyAreaStreetMapper.selectList(queryWrapper);
            List<TStreetMappingEntity> tStreetMappingEntities = null;
            if (CollectionUtils.isNotEmpty(tCompanyAreaStreetEntityList)) {
                List<Long> companyAreaStreetIds = tCompanyAreaStreetEntityList.stream().map(TCompanyAreaStreetEntity::getId).collect(Collectors.toList());
                QueryWrapper<TStreetMappingEntity> tsIdQueryWrapper =
                        new QueryWrapper<TStreetMappingEntity>()
                                .in(TStreetMappingEntity.COL_STREET_ID, companyAreaStreetIds)
                                .eq(TStreetMappingEntity.COL_IS_DELETED, 0);
                tStreetMappingEntities = tStreetMappingMapper.selectList(tsIdQueryWrapper);
            }
            //获取业务员信息
            List<SalesmanEntity> salesmanEntityList = null;
            if (CollectionUtils.isNotEmpty(tStreetMappingEntities)) {
                List<Long> chargeUserIds = tStreetMappingEntities.stream().map(TStreetMappingEntity::getSourceId).distinct().collect(Collectors.toList());
                salesmanEntityList = salesmanDao.findByIds(chargeUserIds);
            }
            List<TStreetMappingEntity> finalTStreetMappingEntities = tStreetMappingEntities;
            List<SalesmanEntity> finalSalesmanEntityList = salesmanEntityList;
            List<SalesmanEntity> finalSalesmanEntities = salesmanEntities;
            list.forEach(findPstoreAllOutDto -> {
                if (tCompanyAreaMappings != null) {
                    List<TCompanyAreaMapping> collect = tCompanyAreaMappings.stream().filter(s -> s.getCompanyAreaId().equals(findPstoreAllOutDto.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        collect.forEach(co -> {
                            if (CollectionUtils.isNotEmpty(finalByOrgIdList)) {
                                Optional<StoreEntity> storeEntityOptional = finalByOrgIdList.stream().filter(storeEntity -> storeEntity.getOrgId().equals(co.getStoreOrgId())).findAny();
                                storeEntityOptional.ifPresent(store -> {
                                    co.setStatus(store.getStatus());
                                    co.setStoreCode(store.getCode());
                                    co.setStatusName(store.getStatusName());
                                });

                            }
                            if (CollectionUtils.isNotEmpty(finalSalesmanEntities)) {
                                Optional<SalesmanEntity> salesmanEntityOptional = finalSalesmanEntities.stream().filter(salesman -> salesman.getId().equals(co.getChargeUserId())).findAny();
                                salesmanEntityOptional.ifPresent(salesman -> {
                                    co.setSalesmanCode(salesman.getCode());
                                    co.setSalesmanStatusName(salesman.getStatusName());
                                    co.setQrcode(salesman.getQrcode());
                                });
                            }
                        });
                    }
                    findPstoreAllOutDto.setTCompanyAreaMappingList(collect);
                }
                if (CollectionUtils.isNotEmpty(tCompanyAreaStreetEntityList)) {
                    StringBuilder streetstr = new StringBuilder();

                    List<TCompanyAreaStreetEntity> collect = tCompanyAreaStreetEntityList.stream().filter(tCompanyAreaStreetEntity -> tCompanyAreaStreetEntity.getCompanyAreaId().equals(findPstoreAllOutDto.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        collect.forEach(co -> {
                            if (CollectionUtils.isNotEmpty(finalTStreetMappingEntities)) {
                                List<TStreetMappingEntity> collect1 = finalTStreetMappingEntities.stream().filter(tStreetMappingEntity -> tStreetMappingEntity.getStreetId().equals(co.getId())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(collect1)) {
                                    streetstr.append(co.getStreetName());
                                    streetstr.append("-");
                                    StringBuilder chargeUserNameStr = new StringBuilder();
                                    collect1.forEach(co1 -> {
                                        SalesmanEntity salesman = finalSalesmanEntityList.stream().filter(chargeUser -> chargeUser.getId().equals(co1.getSourceId())).findAny().orElse(null);
                                        if (salesman != null) {
                                            if (StringUtils.isNotBlank(chargeUserNameStr.toString())) {
                                                chargeUserNameStr.append(",").append(salesman.getName());
                                            } else {
                                                chargeUserNameStr.append(salesman.getName());
                                            }
                                        }

                                    });
                                    if (StringUtils.isNotEmpty(chargeUserNameStr.toString())) {
                                        streetstr.append(chargeUserNameStr);
                                    }
                                    streetstr.append(";");
                                }
                            }
                        });
                    }
                    findPstoreAllOutDto.setStreetChargeUser(streetstr.toString());
                }
            });

        }
        pageInfo.setRecords(list);
        return pageInfo;
    }


    /**
     * 根据id查询
     *
     * @param findPstoreByIdInDto
     * @return
     */
    public FindPstoreByIdOutDto findById(FindPstoreByIdInDto findPstoreByIdInDto) {
        log.info("******PstoreService.findById 条件： findPstoreByIdInDto=" + findPstoreByIdInDto);
        FindPstoreByIdOutDto findPstoreByIdOutDto = pstoreDao.findById(findPstoreByIdInDto.getId());
        QueryWrapper<TCompanyAreaMapping> wrapper = new QueryWrapper<>();
        wrapper.eq("company_area_id", findPstoreByIdOutDto.getId());
        wrapper.eq("is_deleted", 0);
        List<TCompanyAreaMapping> tCompanyAreaMappings = tCompanyAreaMappingService.list(wrapper);
        findPstoreByIdOutDto.setTCompanyAreaMappingList(tCompanyAreaMappings);
        return findPstoreByIdOutDto;
    }

    /**
     * 分配/批量分配
     *
     * @param updatePstoreInDto
     * @return
     */
    public int update(UpdatePstoreInDto updatePstoreInDto) {
        log.info("******PstoreService.update 条件：updatePstoreInDto=" + updatePstoreInDto);
        try {
            UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
            updatePstoreInDto.setModifiedBy(userAuthor.getUsername());
            pstoreDao.update(updatePstoreInDto);
            return 1;
        } catch (Exception e) {
            log.debug("ERROR***********PstoreService.update e=" + e);
            return 0;
        }
    }

    /**
     * 根据orgId查询门店信息
     *
     * @param findPstoreByIdInDto
     * @return
     */
    public FindPstoreByOrgIdOutDto findByOrgId(FindPstoreByOrgIdInDto findPstoreByIdInDto) {
        log.info("******PstoreService.findByOrgId 条件： findStoreByOrgIdInDto=" + findPstoreByIdInDto);
        FindPstoreByOrgIdOutDto result = pstoreDao.findByOrgId(findPstoreByIdInDto);
        if (result != null && result.getOrgId() != null) {
            FindDistributorByStoreIdInDto param = new FindDistributorByStoreIdInDto();
            param.setOrgIdNull(1);
            List<Long> storeIdList = new ArrayList<>();
            storeIdList.add(result.getOrgId());
            param.setStoreId(storeIdList);
            List<DistributorEntity> distributorList = distributorDao.findByStoreIdList(param);
            if (distributorList != null && distributorList.size() > 0) {
                DistributorEntity distributor = distributorList.get(0);
                result.setDistributorId(distributor != null ? distributor.getCode() : null);
                result.setDistributorName(distributor != null ? distributor.getName() : null);
                result.setChannelCategory(distributor != null ? distributor.getChannelCategory() : null);
                result.setChannelCategoryCode(distributor != null ? distributor.getChannelCategoryCode() : null);
                result.setChannelCategoryName(distributor != null ? distributor.getChannelCategoryName() : null);
                result.setChannelSubdivide(distributor != null ? distributor.getChannelSubdivide() : null);
                result.setChannelSubdivideCode(distributor != null ? distributor.getChannelSubdivideCode() : null);
                result.setChannelSubdivideName(distributor != null ? distributor.getChannelSubdivideName() : null);
            }
        }
        return result;
    }


    /**
     * 根据orgId查询门店信息的批量处理
     * @param findPstoreByIdInDto
     * @return
     */
    public List<FindPstoreByOrgIdOutDto> findByStoreIdList(FindPstoreByOrgIdInDto findPstoreByIdInDto) {
        log.info("******PstoreService.findByStoreIdList 条件： findStoreByOrgIdInDto=" + findPstoreByIdInDto);
        List<FindPstoreByOrgIdOutDto> list = pstoreDao.findByStoreIdList(findPstoreByIdInDto);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        FindDistributorByStoreIdInDto param = new FindDistributorByStoreIdInDto();
        param.setOrgIdNull(1);
        // 获取list中的orgId作为一个新的list
        List<Long> storeIdList = list.stream().map(FindPstoreByOrgIdOutDto::getOrgId).collect(Collectors.toList());
        param.setStoreId(storeIdList);
        List<DistributorEntity> distributorList = distributorDao.findByStoreIdList(param);
        if (CollectionUtils.isEmpty(distributorList)) {
            return list;
        }
        // distributorList转为以orgId为key，对象为value的map
        Map<Long, DistributorEntity> map = distributorList.stream().collect(Collectors.toMap(DistributorEntity::getOrgId, Function.identity()));
        for (FindPstoreByOrgIdOutDto dto : list) {
            if (!map.containsKey(dto.getOrgId())) {
                continue;
            }
            DistributorEntity distributor = map.get(dto.getOrgId());
            dto.setDistributorId(distributor.getCode());
            dto.setDistributorName(distributor.getName());
            dto.setChannelCategory(distributor.getChannelCategory());
            dto.setChannelCategoryCode(distributor.getChannelCategoryCode());
            dto.setChannelCategoryName(distributor.getChannelCategoryName());
            dto.setChannelSubdivide(distributor.getChannelSubdivide());
            dto.setChannelSubdivideCode(distributor.getChannelSubdivideCode());
            dto.setChannelSubdivideName(distributor.getChannelSubdivideName());
        }
        return list;
    }

    /**
     * 根据省市区id查询分公司，门店，业务员信息
     *
     * @param findPstoreByIdInDto
     * @return
     */
    public List<FindPstoreByAreaIdOutDto> findByAreaId(FindPstoreByAreaIdInDto findPstoreByIdInDto) {
        //返回
        List<FindPstoreByAreaIdOutDto> list = new ArrayList<>();
        log.info("******PstoreService.findByAreaId 条件： findPstoreByIdInDto=" + findPstoreByIdInDto);
        //第一步：根据省、市、区查询t_company_area
        List<FindPstoreByAreaIdOutDto> result = pstoreDao.findByAreaId(findPstoreByIdInDto);
        FindPstoreByAreaIdOutDto findPstoreByAreaIdOutDto = null;
        if (result == null || result.size() == 0) {
            return list;
        }
        if (result.size() > 1) {
            if(findPstoreByIdInDto.getWeightFlag() != null && findPstoreByIdInDto.getWeightFlag() == 1){
                //过滤只有启用的业务员
                List<Long> salesmanIdList = result.stream().map(t -> t.getChargeUserId()).collect(Collectors.toList());
                List<Long> filterIds = salesmanDao.filterIds(salesmanIdList);
                if(CollectionUtils.isEmpty(filterIds)){
                    return list;
                }
                findPstoreByAreaIdOutDto = result.stream().filter( t -> filterIds.contains(t.getChargeUserId())).max(Comparator.comparing(FindPstoreByAreaIdOutDto::getWeight)).get();
            }else{
                //1.1 计算权重门店
                Map<Long, Integer> weightRoundEntity = result.stream().collect(Collectors.toMap(FindPstoreByAreaIdOutDto::getId, FindPstoreByAreaIdOutDto::getWeight, (id, weight) -> weight));
                Long random = new WeightRoundRobinUtils<Long, Integer>(weightRoundEntity).random();
                if (random == null) {
                    return list;
                }
                findPstoreByAreaIdOutDto = result.stream().filter(s -> s.getId().equals(random)).findFirst().orElse(null);
            }
        } else {
            findPstoreByAreaIdOutDto = result.get(0);
        }
        if (findPstoreByAreaIdOutDto == null) {
            return list;
        }
        //获取分公司信息
        if (findPstoreByAreaIdOutDto.getCompanyId() != null) {
            CompanyEntity companyEntity = companyDao.findById(findPstoreByAreaIdOutDto.getCompanyId());
            if (companyEntity == null) {
                return list;
            }
            findPstoreByAreaIdOutDto.setCompanyOrgId(companyEntity.getOrgId());
            findPstoreByAreaIdOutDto.setCompanyName(companyEntity.getName());
            findPstoreByAreaIdOutDto.setAreaCode(companyEntity.getAreaCode());
            findPstoreByAreaIdOutDto.setAreaName(companyEntity.getAreaName());
            findPstoreByAreaIdOutDto.setShellStoreId(companyEntity.getShellStoreId());
            //如果是广东分公司，再查门店信息
            if(Objects.equals(companyEntity.getOrgId(), CompanyConstants.GD_COMPANY_ORG_ID)){
                findPstoreByAreaIdOutDto.setKaStoreId(companyGdService.getGdStore(findPstoreByAreaIdOutDto.getCityName()));
            }else{
                findPstoreByAreaIdOutDto.setKaStoreId(companyEntity.getKaStoreId());
            }
        }
        //第二步：根据门店id集合查询经销商信息
        try {
            //获取业务员所在门店 门店类型  等基本信息
            if (findPstoreByAreaIdOutDto.getStoreOrgId() != null) {
                FindSalesmanANdStoreBysalesmanIdINDTO findSalesmanANdStoreBysalesmanIdINDTO = new FindSalesmanANdStoreBysalesmanIdINDTO();
                findSalesmanANdStoreBysalesmanIdINDTO.setStoreOrgId(findPstoreByAreaIdOutDto.getStoreOrgId());
                FindSalesmanANdStoreBysalesmanIdOutDTO findSalesmanANdStoreBysalesmanIdOutDTO = storeDao.findStoreByStoreOrgId(findSalesmanANdStoreBysalesmanIdINDTO);
                //有门店的情况下  返回门店，经销商渠道，业务员信息  没有门店的情况下  啥也不返回
                //有门店但是差不到业务员的的情况  返回门店，经销商渠道信息 业务员不返回
                if (findSalesmanANdStoreBysalesmanIdOutDTO != null && (findSalesmanANdStoreBysalesmanIdOutDTO.getStatus() == 1 || findSalesmanANdStoreBysalesmanIdOutDTO.getStatus() == 2)) {
                    findPstoreByAreaIdOutDto.setStoreCode(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreCode());
                    findPstoreByAreaIdOutDto.setStoreName(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreName());
                    findPstoreByAreaIdOutDto.setStoreTypeCode(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreTypeCode());
                    findPstoreByAreaIdOutDto.setStoreType(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreType());
                    findPstoreByAreaIdOutDto.setStoreTypeName(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreTypeName());
                    findPstoreByAreaIdOutDto.setStoreSubChannelCode(findSalesmanANdStoreBysalesmanIdOutDTO.getStoreSubChannelCode());
                    List<Long> storeIdList = new ArrayList<>();
                    storeIdList.add(findPstoreByAreaIdOutDto.getStoreOrgId());
                    if (storeIdList != null && storeIdList.size() > 0) {
                        FindDistributorByStoreIdInDto param = new FindDistributorByStoreIdInDto();
                        param.setOrgIdNull(1);
                        param.setStoreId(storeIdList);
                        List<DistributorEntity> distributorList = distributorDao.findByStoreIdList(param);
                        if (distributorList != null && distributorList.size() > 0) {
                            FindPstoreByAreaIdOutDto finalFindPstoreByAreaIdOutDto = findPstoreByAreaIdOutDto;
                            DistributorEntity distributor = distributorList.stream().filter(d -> (finalFindPstoreByAreaIdOutDto.getStoreOrgId() + "").equals(d.getOrgId() + "")).findFirst().orElse(null);
                            findPstoreByAreaIdOutDto.setDistributorId(distributor != null ? distributor.getCode() : null);
                            findPstoreByAreaIdOutDto.setDistributorName(distributor != null ? distributor.getName() : null);
                            findPstoreByAreaIdOutDto.setChannelCategory(distributor != null ? distributor.getChannelCategory() : null);
                            findPstoreByAreaIdOutDto.setChannelCategoryCode(distributor != null ? distributor.getChannelCategoryCode() : null);
                            findPstoreByAreaIdOutDto.setChannelCategoryName(distributor != null ? distributor.getChannelCategoryName() : null);
                            findPstoreByAreaIdOutDto.setChannelSubdivide(distributor != null ? distributor.getChannelSubdivide() : null);
                            findPstoreByAreaIdOutDto.setChannelSubdivideCode(distributor != null ? distributor.getChannelSubdivideCode() : null);
                            findPstoreByAreaIdOutDto.setChannelSubdivideName(distributor != null ? distributor.getChannelSubdivideName() : null);

                        }
                    }
                    if (findPstoreByAreaIdOutDto.getChargeUserId() != null) {
                        SalesmanEntity salesmanEntity = salesmanDao.findById(findPstoreByAreaIdOutDto.getChargeUserId());
                        if (salesmanEntity != null && salesmanEntity.getStatus() == 1 && salesmanEntity.getStoreId() != null && salesmanEntity.getStoreId().equals(findPstoreByAreaIdOutDto.getStoreOrgId())) {
                            findPstoreByAreaIdOutDto.setSalesmanId(salesmanEntity.getId());
                            findPstoreByAreaIdOutDto.setSalesmanCode(salesmanEntity.getCode());
                            findPstoreByAreaIdOutDto.setSalesmanName(salesmanEntity.getName());
                            findPstoreByAreaIdOutDto.setSalesmanPhone(salesmanEntity.getPhone());
                            findPstoreByAreaIdOutDto.setPortrait(salesmanEntity.getPortrait());
                            findPstoreByAreaIdOutDto.setQrcode(salesmanEntity.getQrcode());
                        } else {
                            findPstoreByAreaIdOutDto.setChargeUserId(null);
                        }
                    }
                } else {
                    findPstoreByAreaIdOutDto.setStoreOrgId(null);
                    findPstoreByAreaIdOutDto.setChargeUserId(null);
                }
            }

            list.add(findPstoreByAreaIdOutDto);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("********************163" + e);
            log.info("*******************164" + e);
        }
        return list;
    }

    /**
     * 根据门店编码集合查询门店信息
     *
     * @param findPstoreByCodesInDto
     * @return
     */
    public List<FindPstoreByOrgIdOutDto> findByCode(FindPstoreByCodesInDto findPstoreByCodesInDto) {
        log.info("******PstoreService.findByCode 条件： findPstoreByCodesInDto=" + findPstoreByCodesInDto);
        return pstoreDao.findByCode(findPstoreByCodesInDto);
    }

    public List<FindPstoreAllOutDto> listByParam(CommonStoreDto dto) {
        if (dto.getSize() == null) {
            dto.setSize(20);
        }
        return pstoreDao.listByParam(dto);
    }

    public PageInfo<FindPstoreAllOutDto> pageByParam(CommonStoreDto dto) {
        if (dto.getPagination() == null || dto.getSize() == null) {
            dto.setPagination(1);
            dto.setSize(3);
        }
        PageInfo<FindPstoreAllOutDto> pageInfo = new PageInfo<>(dto.getPagination(), dto.getSize());
        Integer count = pstoreDao.queryByParamCount(dto);
        pageInfo.setTotal(count);
        if (count != null && count > 0) {
            List<FindPstoreAllOutDto> list = pstoreDao.queryByParam(dto, pageInfo);
            if (list != null && list.size() > 0) {

                Set<Long> hasActiveCompanySet = new HashSet<>();
                Map<Long, Set<String>> companyCityNameMap = new HashMap<>();

                if(StringUtils.isNotBlank(dto.getCityName()) && StringUtils.isNotBlank(dto.getSellType()) && dto.getChannelId() != null){
                    Set<Long> companyIdSet = list.stream().map(t -> t.getCompanyId()).collect(Collectors.toSet());

                    //查询门店是否有活动
                    FindContentTemplateListDTO param = new FindContentTemplateListDTO();
                    param.setSellType(dto.getSellType());
                    param.setChannelId(dto.getChannelId());
                    param.setCompanyIdList(companyIdSet);
                    Set<Long> data = cmsClient2.findContendTemplateCompanyIdList(param).getData();
                    hasActiveCompanySet.addAll(data);

                    //查询公司所管理的cityName
                    List<CompanyCityNameDto> cityNameByCompanyIdList = pstoreDao.findCityNameByCompanyIdList(companyIdSet);
                    if(CollectionUtils.isNotEmpty(cityNameByCompanyIdList)){
                        companyCityNameMap = cityNameByCompanyIdList.stream().collect(Collectors.groupingBy(CompanyCityNameDto::getCompanyId, Collectors.mapping(po -> po.getCityName(), Collectors.toSet())));
                    }
                }

                Set<Long> finalHasActiveCompanySet = hasActiveCompanySet;
                Map<Long, Set<String>> finalCompanyCityNameMap = companyCityNameMap;

                list.stream().forEach(x -> {
                    String addr = StringUtils.isNotBlank(x.getAddress2()) ? x.getAddress2() : x.getAddress();
                    x.setShowAddress(addr);
                    if(CollectionUtils.isNotEmpty(finalHasActiveCompanySet) && (finalHasActiveCompanySet.contains(-1L) || finalHasActiveCompanySet.contains(x.getCompanyId()))){
                        x.setActivityFlag(Boolean.TRUE);
                    }
                    if( StringUtils.isNotBlank(dto.getCityName())
                            && (finalCompanyCityNameMap == null || CollectionUtils.isEmpty(finalCompanyCityNameMap.get(x.getCompanyId())) || !finalCompanyCityNameMap.get(x.getCompanyId()).contains(dto.getCityName()))){
                        x.setActivityFlag(Boolean.FALSE);
                    }
                });
            }
            pageInfo.setRecords(list);
        }
        return pageInfo;
    }

    public List<FindPstoreAllOutDto> findStoresByCoordinate(CommonStoreDto dto) {
        if (dto.getSize() == null) {
            dto.setSize(3);
        }
        List<FindPstoreAllOutDto> list = pstoreDao.findStoresByCoordinate(dto);
        if (list != null && list.size() > 0) {

            Set<Long> hasActiveCompanySet = new HashSet<>();
            Map<Long, Set<String>> companyCityNameMap = new HashMap<>();

            if(StringUtils.isNotBlank(dto.getCityName()) && StringUtils.isNotBlank(dto.getSellType()) && dto.getChannelId() != null){
                Set<Long> companyIdSet = list.stream().map(t -> t.getCompanyId()).collect(Collectors.toSet());

                //查询门店是否有活动
                FindContentTemplateListDTO param = new FindContentTemplateListDTO();
                param.setSellType(dto.getSellType());
                param.setChannelId(dto.getChannelId());
                param.setCompanyIdList(companyIdSet);
                Set<Long> data = cmsClient2.findContendTemplateCompanyIdList(param).getData();
                hasActiveCompanySet.addAll(data);

                //查询公司所管理的cityName
                List<CompanyCityNameDto> cityNameByCompanyIdList = pstoreDao.findCityNameByCompanyIdList(companyIdSet);
                if(CollectionUtils.isNotEmpty(cityNameByCompanyIdList)){
                    companyCityNameMap = cityNameByCompanyIdList.stream().collect(Collectors.groupingBy(CompanyCityNameDto::getCompanyId, Collectors.mapping(po -> po.getCityName(), Collectors.toSet())));
                }
            }
            log.error("查询门店活动返回结果，结果是：{}",hasActiveCompanySet);
            Set<Long> finalHasActiveCompanySet = hasActiveCompanySet;
            Map<Long, Set<String>> finalCompanyCityNameMap = companyCityNameMap;
            list.stream().forEach(x -> {
                String addr = StringUtils.isNotBlank(x.getAddress2()) ? x.getAddress2() : x.getAddress();
                x.setShowAddress(addr);
                log.error("查询门店活动返回结果，公司id：{}",x.getCompanyId());
                log.error("查询门店活动返回结果，匹配结果：{}",CollectionUtils.isNotEmpty(finalHasActiveCompanySet) && (finalHasActiveCompanySet.contains(-1L) || finalHasActiveCompanySet.contains(x.getCompanyId())));
                if(CollectionUtils.isNotEmpty(finalHasActiveCompanySet) && (finalHasActiveCompanySet.contains(-1L) || finalHasActiveCompanySet.contains(x.getCompanyId()))){
                    x.setActivityFlag(Boolean.TRUE);
                }
                if( StringUtils.isNotBlank(dto.getCityName())
                        && (finalCompanyCityNameMap == null || CollectionUtils.isEmpty(finalCompanyCityNameMap.get(x.getCompanyId())) || !finalCompanyCityNameMap.get(x.getCompanyId()).contains(dto.getCityName()))){
                    x.setActivityFlag(Boolean.FALSE);
                }
            });
        }
        return list;
    }

    public List<PStoreFindPageAllByCompanyIdAndLevelVO> pStoreFindPageAllByCompanyIdAndLevel(PStoreFindPageAllByCompanyIdAndLevelInDTO pStoreFindPageAllByCompanyIdAndLevelInDTO) {
        return pstoreDao.pStoreFindPageAllByCompanyIdAndLevel(pStoreFindPageAllByCompanyIdAndLevelInDTO);
    }

    public PStoreFindPageAllByProCodeAndCityCodeVO pStoreFindPageAllByProCodeAndCityCode(PStoreFindPageAllByProCodeAndCityCodeDTO pStoreFindPageAllByCompanyIdAndLevelInDTO) {
        return pstoreDao.pStoreFindPageAllByProCodeAndCityCode(pStoreFindPageAllByCompanyIdAndLevelInDTO);
    }


    //省市区分配门店导出count
    public Long exportPstoreCount(FindPstoreAllInDto findPstoreAllInDto) {
        findPstoreAllInDto.setCompanyAuthorList(userAuthorConfig.queryCompanyAuthorList());
        Long count = pstoreDao.exportPstoreCount(findPstoreAllInDto);
        return count;
    }

    /**
     * 省市区分配门店导出
     */
    public List<ExportPstoreAllOutDto> exportPstoreList(FindPstoreAllInDto findPstoreAllInDto) {
        PageInfo<FindPstoreAllOutDto> pageInfo = new PageInfo<FindPstoreAllOutDto>(findPstoreAllInDto.getPage(), findPstoreAllInDto.getSize());
        findPstoreAllInDto.setCompanyAuthorList(userAuthorConfig.queryCompanyAuthorList());
        List<ExportPstoreAllOutDto> list = pstoreDao.exportPstoreList(findPstoreAllInDto, pageInfo);
        for (ExportPstoreAllOutDto pstoreAllOutDto : list) {
            if (pstoreAllOutDto.getType() != null) {
                if (pstoreAllOutDto.getType() == 1) {
                    pstoreAllOutDto.setTypeName("否");
                } else if (pstoreAllOutDto.getType() == 2) {
                    pstoreAllOutDto.setTypeName("是");
                }
            }
            if (pstoreAllOutDto != null) {
                if (pstoreAllOutDto.getWeight() != null && pstoreAllOutDto.getWeight() > 0) {
                    //传入格式模板
                    DecimalFormat df = new DecimalFormat("##.##%");
                    String weightProportion = df.format((float) pstoreAllOutDto.getWeight() / (float) pstoreAllOutDto.getWeightSum());
                    pstoreAllOutDto.setWeightProportion(weightProportion);
                } else {
                    pstoreAllOutDto.setWeightProportion("");
                }
            }
        }
        return list;
    }

    /**
     * 根据分公司orgid查询
     */
    public PStoreFindByCompanyOrgIdOutDto pStoreFindByCompanyOrgId(PStoreFindByCompanyOrgIdInDto idInDto) {
        return pstoreDao.pStoreFindByCompanyOrgId(idInDto);
    }

    public FindCompanyByCountyIdOutDTO findCompanyByCountyId(FindCompanyByCountyIdInDTO dto) {
        //0 参数校验
        if (Objects.isNull(dto)) {
            throw new BusinessException("[入参错误]参数对象不能为空");
        }

        dto.checkParam();

        FindCompanyByCountyIdOutDTO vo = pstoreDao.findCompanyByCountyId(dto);

        return vo;
    }
}
