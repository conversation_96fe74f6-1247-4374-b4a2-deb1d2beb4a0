package com.fotile.orgcenter.picture.dao;

import com.fotile.orgcenter.picture.pojo.entity.PictureEntity;
import com.fotile.orgcenter.picture.pojo.entity.PictureMappingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PictureDao {

    //新增图片
    public void addPic(PictureEntity pictureEntity);

    void addPic2(@Param("pic") PictureEntity pic);

    //新增图片关系表
    public void addPicMapping(PictureMappingEntity picMapping);

    //新增图片关系表
    public void addPicMapping2(@Param("mappings") PictureMappingEntity mappings);

    //根据sourceId，sourceTableName查询图片
    List<PictureEntity> queryPicBySourceIdAndSourceTable(@Param("sourceId") Long sourceId, @Param("sourceTableName") String sourceTableName);

    //根据sourceId，sourceTableName删除图片
    public void delPicBySourceIdAndSourceTable(@Param("sourceId") Long sourceId, @Param("sourceTableName") String sourceTableName);

    List<PictureEntity> getDecorateImageBox(@Param("sourceIds") List<Long> sourceIds,
                                            @Param("sourceTableName") String sourceTableName);


    void bathInsertPicture(@Param("list") List<PictureEntity> list);

    void batchInsertPictureMapping(@Param("mappings") List<PictureMappingEntity> mappings);
}
