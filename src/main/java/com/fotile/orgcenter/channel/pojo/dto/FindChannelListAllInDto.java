package com.fotile.orgcenter.channel.pojo.dto;

import lombok.Data;

import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class FindChannelListAllInDto {

    /**
     * 是否要渠道权限0：要；1：不要
     */
    @NotNull(message = "是否要渠道数据权限不能为空")
    private Integer isNeedAuthor;

    /**
     * 渠道编码
     */
    private String code;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 渠道编码(多个半角逗号分隔)
     */
    private String codes;

    /**
     * 渠道编码集
     */
    private List<String> codeList;
}
