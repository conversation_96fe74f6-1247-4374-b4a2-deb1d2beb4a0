package com.fotile.orgcenter.community.communityStore.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindCommunityStoreAllOutDto implements Serializable {
    /**
     * 门店类型，1-社区店，2-其他
     */
    private Integer type;

    private String name;
    private String storeCode;
    private Long id;
    private Long orgId;
    private String companyName;
    private Long companyId;

    private Integer appointmentType;

    private String startTime;

    private String endTime;

    private Long companyMainId;

    private Long storeMainId;
}
