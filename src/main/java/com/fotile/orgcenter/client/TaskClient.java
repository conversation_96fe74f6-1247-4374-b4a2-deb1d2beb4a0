package com.fotile.orgcenter.client;

import com.fotile.framework.web.Result;
import com.fotile.orgcenter.client.pojo.BenefitsTask;
import com.fotile.orgcenter.client.pojo.FindBenefitTaskDTO;
import com.fotile.orgcenter.designerClub.pojo.dto.BenefitsTaskRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @data 2024/8/31 19:03
 */
@FeignClient(value = "task-center", path = "")
public interface TaskClient {

    @RequestMapping(value = "/api/benefit/task/designer/api/open/queryLatestWorkProofRecord", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<BenefitsTaskRecord> queryLatestWorkProofRecord(@RequestParam("memberId") Long memberId);

    /**
     * 【权益金任务】查询符合条件的单条有效权益任务详情(OPEN)
     */
    @RequestMapping(value = "/api/open/benefit/task/findOne", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<BenefitsTask> findOneBenefitTask(@RequestBody @Valid FindBenefitTaskDTO find);
}
