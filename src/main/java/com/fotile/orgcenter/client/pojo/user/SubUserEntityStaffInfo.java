package com.fotile.orgcenter.client.pojo.user;

import com.fotile.framework.core.common.AuditingEntity;
import com.fotile.framework.data.auth.dataAuthor.pojo.ChannelAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.CompanyAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.RoleAuthor;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.List;

/**
 * 服务技师账号信息
 *
 * <AUTHOR>
 * @email
 * @date 2025-02-13 13:05:56
 */
@Data
public class SubUserEntityStaffInfo extends AuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @FieldEncrypt
    private String firstName;

    /**
     * 账号
     */
    private String username;

    /**
     * 手机号
     */
    @FieldEncrypt
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 头像url
     */
    private String headPortrait;

    /**
     * 关联业务员
     */
    private Long salesmanId;

    /**
     * 状态，0：禁用；1：启用
     */
    private Long status;

    /**
     * 业务员所属分公司id
     */
    private Long companyId;

    /**
     * 公司权限数组[{companyId: 2520, orgId: 179904}]
     */
    private List<CompanyAuthor> userEntityCompanyRoleList;

    /**
     * 角色[{roleId: "企微智慧导购-服务技师", roleName: "5fb686e9-60d9-4031-a6a5-07db6384d0e8"}]
     */
    private List<RoleAuthor> roleAuthorList;

    /**
     * 服务技师的服务门店编码集合
     */
    private List<String> cmsStoreCodeList;

    /**
     * 业务员岗位取字典值gw，服务技师-方太编制 201 服务技师-非方太编制  202
     */
    private String stationCode;

    /**
     * 渠道权限数组[{channelId: 11, code: "s0011"}]"]
     */
    private List<ChannelAuthor> userEntityChannelRoleList;



}
