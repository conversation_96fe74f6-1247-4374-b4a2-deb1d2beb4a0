package com.fotile.orgcenter.serviceinfo.controller;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.orgcenter.salesman.pojo.dto.SalesmanBaseInfo;
import com.fotile.orgcenter.serviceinfo.pojo.dto.*;
import com.fotile.orgcenter.serviceinfo.pojo.entity.ServiceInfo;
import com.fotile.orgcenter.serviceinfo.pojo.entity.ServiceStaffInfo;
import com.fotile.orgcenter.serviceinfo.pojo.vo.FindServiceStaffOutVo;
import com.fotile.orgcenter.serviceinfo.pojo.vo.ServiceStaffDetailVO;
import com.fotile.orgcenter.serviceinfo.pojo.vo.ServiceStaffListVO;
import com.fotile.orgcenter.serviceinfo.service.ServiceStaffService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务技师操作接口
 */
@RestController
@RequestMapping("/api/serviceStaffInfo")
@Slf4j
public class ServiceStaffController extends BaseController {

    @Autowired
    private ServiceStaffService serviceStaffService;

    /**
     * 分页查询服务技师
     */
    @RequestMapping(value = "/findServiceStaffInfoPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ServiceStaffListVO> findServiceStaffInfoPage(@Valid ServiceStaffDTO serviceStaffDTO) {
        log.debug("******ServiceStaffController.findServiceStaffInfoPage 条件：ServiceStaffDTO=" + serviceStaffDTO);
        return success(serviceStaffService.findServiceStaffInfoPage(serviceStaffDTO));
    }


    /**
     * 新增，修改接口
     */
    @RequestMapping(value = "/saveAndUpdateServiceStaff", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ServiceInfo> saveAndUpdateServiceInfo(@RequestBody ServiceStaffDetailVO serviceStaffDetailVO) {
        log.info("******ServiceStaffController.saveAndUpdateServiceStaff 入参：providerInfo=" + serviceStaffDetailVO);
        if(serviceStaffDetailVO == null || serviceStaffDetailVO.getServiceStaffInfo() == null){
            return failure("请传服务信息");
        }
        int result = serviceStaffService.saveAndUpdateServiceStaff(serviceStaffDetailVO);
        if (result >= 0) {
            return success("保存数据成功！");
        } else {
            return failure("保存数据失败！");
        }
    }

    /**
     * 查询详情
     */
    @RequestMapping(value = "/findServiceStaffById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ServiceStaffDetailVO> findServiceStaffById(@RequestParam Long id) {
        log.info("******ProviderInfoController.findServiceInfoById 条件：id=" + id);
        return success(serviceStaffService.findServiceStaffById(id));
    }


    /**
     *
     * 校验服务商名称是否存在
     */
    @RequestMapping(value = "/checkStaffPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ServiceStaffListVO> checkStaffPhone(ServiceStaffDTO serviceStaffDTO) {
        log.info("******ServiceStaffController.checkStaffPhone 条件：ServiceStaffDTO=" + serviceStaffDTO);
        if(serviceStaffDTO == null || StringUtils.isEmpty(serviceStaffDTO.getStaffPhone())){
            return failure("请传服务技师手机号！");
        }
        if(serviceStaffDTO == null || serviceStaffDTO.getCompanyId() == null){
            return failure("请传分公司id");
        }
        List<ServiceStaffInfo> list = serviceStaffService.checkStaffPhone(serviceStaffDTO);
        if(list != null && list.size() > 0){
            return success("该手机号已存在,请修改!",1);
        }else{
            return success("可以新增此服务技师",0);
        }

    }

    /**
     * 根据手机号查询技师
     */
    @RequestMapping(value = "/findServiceStaffByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhone(@Valid FindServiceStaffInDto serviceStaffInDto) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInDto.getStaffPhone());
        return success(serviceStaffService.findServiceStaffByPhone(serviceStaffInDto));
    }

    /**
     * [open]根据手机号查询技师
     */
    @RequestMapping(value = "/api/open/findServiceStaffByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhoneOpem(@Valid FindServiceStaffInDto serviceStaffInDto) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInDto.getStaffPhone());
        return success(serviceStaffService.findServiceStaffByPhone(serviceStaffInDto));
    }

    /**
     * [m]根据手机号查询技师
     */
    @RequestMapping(value = "/api/m/findServiceStaffByPhone", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhoneM(@Valid FindServiceStaffInDto serviceStaffInDto) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInDto.getStaffPhone());
        return success(serviceStaffService.findServiceStaffByPhone(serviceStaffInDto));
    }

    /**
     * 查询服务技师,可支持中台局改的选择框
     */
    @RequestMapping(value = "/findServiceStaffInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<StaffInfoForCEM> findServiceStaffInfo(ServiceStaffPageInfoDTO serviceStaffPageInfoDTO) {
        log.debug("******ServiceStaffController.findServiceStaffInfoPage 条件：ServiceStaffDTO=" );
        return success(serviceStaffService.findServiceStaffInfo(serviceStaffPageInfoDTO));
    }

    /**
     * 下拉，编辑二维码使用
     */
    @RequestMapping(value = "/findServiceStaffPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutVo> findServiceStaffPage(@Valid ServiceStaffDTO serviceStaffDTO) {
        log.debug("******ServiceStaffController.findServiceStaffInfoPage 条件：ServiceStaffDTO=" + serviceStaffDTO);
        return success(serviceStaffService.findServiceStaffPage(serviceStaffDTO));
    }

    /**
     * 根据手机号集合查询技师
     */
    @RequestMapping(value = "/findServiceStaffByPhoneList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhoneList(@RequestBody FindServiceStaffByPhonesInDto serviceStaffInDto) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInDto.getStaffPhones());
        return success(serviceStaffService.findServiceStaffByPhoneList(serviceStaffInDto));
    }

    /**
     * [open]根据手机号集合查询技师
     */
    @RequestMapping(value = "/api/open/findServiceStaffByPhoneList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindServiceStaffOutDto> findServiceStaffByPhoneListOpen(@RequestBody FindServiceStaffByPhonesInDto serviceStaffInDto) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInDto.getStaffPhones());
        return success(serviceStaffService.findServiceStaffByPhoneList(serviceStaffInDto));
    }

    /**
     * CSM同步服务技师数据
     */
    @RequestMapping(value = "/api/csmSyncStaffInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> csmSyncStaffInfo(@RequestBody ServiceStaffInfo serviceStaffInfo) {
        log.info("******ServiceStaffController.findServiceStaffByPhone 条件：staffPhone=" + serviceStaffInfo);
        if(StringUtils.isBlank(serviceStaffInfo.getCsmStaffCode())){
            return failure("请传入服务技师编码");
        }
        if(StringUtils.isBlank(serviceStaffInfo.getStaffName())){
            return failure("请传入服务技师名称");
        }
        if(StringUtils.isBlank(serviceStaffInfo.getStaffPhone())){
            return failure("请传入服务技师手机号");
        }
        if(StringUtils.isBlank(serviceStaffInfo.getCsmCompanyName())){
            return failure("请传入服务技师所属办事处");
        }
        return success(serviceStaffService.csmSyncStaffInfo(serviceStaffInfo));
    }


    /**
     * 到企微环境，标记服务技师
     */
    @RequestMapping(value = "/api/markStaffQYWXLabel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> markStaffQYWXLabel(@RequestBody MarkStaffLabelDTO markStaffLabelDTO) {
        log.info("******ServiceStaffController.markStaffQYWXLabel 条件：markStaffLabelDTO=" + markStaffLabelDTO);
        if(StringUtils.isBlank(markStaffLabelDTO.getUserId())){
            return failure("请传入服务技师编码");
        }
//        if(StringUtils.isBlank(markStaffLabelDTO.getStaffName())){
//            return failure("请传入服务技师名称");
//        }
        return success(serviceStaffService.markStaffQYWXLabel(markStaffLabelDTO));
    }


    /**
     * 根据服务技师编码获取关联业务员信息
     */
    @RequestMapping(value = "/getChargeUserInfoByStaffCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<SalesmanBaseInfo> getChargeUserInfoByStaffCode(@RequestParam("staffCode") String staffCode) {
        log.info("******ServiceStaffController.getChargeUserInfoByStaffCode 条件：staffCode=" + staffCode);
        if(StringUtils.isBlank(staffCode)){
            return failure("请传入服务技师编码");
        }
        return success(serviceStaffService.getChargeUserInfoByStaffCode(staffCode));
    }

}
