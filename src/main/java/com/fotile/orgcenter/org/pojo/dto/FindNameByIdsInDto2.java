package com.fotile.orgcenter.org.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindNameByIdsInDto2 implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织机构id
     */
    private List<Long> idList;

    /**
     * 类型 1：公司；2：部门；3：门店
     */
    private Integer type;

    /**
     * 状态 0：禁用；1：启用
     */
    private Integer status;
}
