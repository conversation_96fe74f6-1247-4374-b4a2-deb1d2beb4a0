package com.fotile.orgcenter.salesman.pojo.dto;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 家装/小B端引流跟随业务员迁移参数实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.orgcenter.decorate.pojo.dto
 * @date 2020/9/22 10:37
 */
@Data
public class ChangeSalesmanStoreInDto extends AuditingEntity {
    /**
     * 业务员Id
     */
    @NotNull(message = "业务员Id不能为空！")
    private Long salesmanId;

    /**
     * 业务员code
     **/
    private String salesmanCode;

    /**
     * 门店orgId
     */
    @NotNull(message = "门店OrgId不能为空！")
    private Long storeOrgId;

    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空！")
    private String storeName;

    private String dealersUserPhone;
    private String dealersUserName;
    private String dealersUserCode;

    private String operatorId;

    private String operatorName;
}
