package com.fotile.orgcenter.salesman.mapper;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.CompanyAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.orgcenter.common.KvLL;
import com.fotile.orgcenter.company.pojo.vo.FindCompanyClueChargeVO;
import com.fotile.orgcenter.decorate.pojo.dto.AppSearchSalesmanInDTO;
import com.fotile.orgcenter.department.pojo.dto.FindSubSalesmanInDTO;
import com.fotile.orgcenter.department.pojo.dto.FindSubSalesmanOutDTO;
import com.fotile.orgcenter.org.pojo.dto.ListAllSimpleSalesmanDTO;
import com.fotile.orgcenter.org.pojo.dto.ListAllSimpleSalesmanInDTO;
import com.fotile.orgcenter.salesman.domain.Salesman;
import com.fotile.orgcenter.salesman.pojo.bo.SalesmanBaseBo;
import com.fotile.orgcenter.salesman.pojo.bo.SalesmanMspDataBO;
import com.fotile.orgcenter.salesman.pojo.dto.*;
import com.fotile.orgcenter.salesman.pojo.dto.req.SalesmanInDto;
import com.fotile.orgcenter.salesman.pojo.dto.resp.SalesmanOutDto;
import com.fotile.orgcenter.salesman.pojo.entity.SalesmanEntity;
import com.fotile.orgcenter.salesman.pojo.vo.QuerySalesmanVo;
import com.fotile.orgcenter.salesman.pojo.vo.SalesmanBaseVo;
import com.fotile.orgcenter.salesman.pojo.vo.SalesmanDetailVo;
import com.fotile.orgcenter.salesman.pojo.vo.TiktokSalesmanDistributionVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 业务员dao
 *
 * <AUTHOR>
 */
@Validated
public interface SalesmanDao {

    //新增业务员
    void addSalesman(SalesmanEntity salesman);

    //分页查询
    int findPageAllTotal(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    int findPageQywxAllTotal(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds);
    @DruidTxcMultiDataSourceAnnontation("sixth")
    List<SalesmanEntity> findPageQywxAll(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds, @Param("pg") PageInfo page);


    List<SalesmanEntity> findPageAll(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds, @Param("pg") PageInfo page);

    int findSalesmanLogPageAllTotal(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds);

    List<SalesmanLogExportQueryDTO> findSalesmanLogPageAll(@Param("salesman") SalesmanEntity salesman, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("keyWord") String keyWord, @Param("subDepList") List<Long> subDepList, @Param("subDepListByDepOrgIds") List<Long> subDepListByDepOrgIds, @Param("pg") PageInfo page);

    //根据id查询
    SalesmanEntity findById(Long id);

    SalesmanEntity findSalesmanByIdNew(Long id);

    //根据id查询业务员(启用状态的业务员)
    SalesmanEntity findById2(Long id);

    //根据id查询业务员
    SalesmanEntity findById3(Long id);

    //根据id查询业务员
    SalesmanEntity findById4(@Param("id") Long id);

    //根据编码查询
    List<SalesmanEntity> findByCode(String code);

    //修改
    void update(SalesmanEntity salesman);

    //修改csm业务员
    void updateCsmSalesman(SalesmanEntity salesman);

    //禁用
    int disableById(SalesmanEntity salesman);

    //禁用
    int disableByIds(@Param("ids") List<Long> ids);

    //启动
    int enableById(SalesmanEntity salesman);

    //根据门店id查询门店的业务员
    int findSalesmanByStoreIdTotal(@Param("storeId") Long storeId, @Param("findOthers") Integer findOthers, @Param("station") Long station);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreId(@Param("storeId") Long storeId, @Param("findOthers") Integer findOthers, @Param("station") Long station, @Param("pg") PageInfo page);

    //排序
    void sort(@Param("id") Long id, @Param("sort") Integer sort, @Param("modifiedBy") String modifiedBy);

    //根据部门id查询门店的业务员
    List<SalesmanEntity> findSalesmanByDepId(@Param("depId") Long depId);

    List<SalesmanEntity> findSalesmanByDepIdAndSalesmanName(@Param("depId") Long depId, @Param("salesmanName") String salesmanName);

    //修改头像
    void updatePortrait(@Param("id") Long id, @Param("portrait") String portrait, @Param("modifiedBy") String modifiedBy);

    //修改手机号
    void updatePhone(@Param("id") Long id, @Param("phone") String phone, @Param("modifiedBy") String modifiedBy);

    //根据公司id查询公司的业务员
    List<SalesmanEntity> findSalesmanByComId(@Param("comId") Long comId);

    //根据门店id查询门店的业务员(不需要判断业务员岗位)
    int findSalesmanByStoreId2Total(@Param("salesmanName") String salesmanName,
                                    @Param("companyIdList") List<Long> companyIdList,
                                    @Param("storeIdListNest") List<List<Long>> storeIdListNest,
                                    @Param("disabledFlag") Integer disabledFlag);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreId2(@Param("salesmanName") String salesmanName,
                                                             @Param("companyIdList") List<Long> companyIdList,
                                                             @Param("storeIdListNest") List<List<Long>> storeIdListNest,
                                                             @Param("pg") PageInfo page,
                                                             @Param("ids") List<Long> ids,
                                                             @Param("disabledFlag") Integer disabledFlag);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreId30(@Param("salesmanName") String salesmanName,
                                                             @Param("companyIdList") List<Long> companyIdList,
                                                             @Param("storeIdListNest") List<List<Long>> storeIdListNest,
                                                             @Param("pg") PageInfo page,
                                                             @Param("ids") List<Long> ids,
                                                             @Param("disabledFlag") Integer disabledFlag,
                                                             @Param("mustContainsalesmanId") Long mustContainsalesmanId,
                                                              @Param("encryptPassword") String encryptPassword);

    Integer countFindSalesmanByStoreId30(@Param("salesmanName") String salesmanName,
                                         @Param("companyIdList") List<Long> companyIdList,
                                         @Param("storeIdListNest") List<List<Long>> storeIdListNest,
                                         @Param("ids") List<Long> ids,
                                         @Param("disabledFlag") Integer disabledFlag,
                                         @Param("mustContainsalesmanId") Long mustContainsalesmanId,
                                         @Param("encryptPassword") String encryptPassword);

    int findSalesmanByStoreId2NewTotal(@Param("findSalesmanByIdInDto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                       @Param("companyIdList") List<Long> companyIdList,
                                       @Param("storeIdListNest") List<List<Long>> storeIdListNest);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreId2New(@Param("findSalesmanByIdInDto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                                                @Param("companyIdList") List<Long> companyIdList,
                                                                @Param("storeIdListNest") List<List<Long>> storeIdListNest,
                                                                @Param("storeId") Long storeId,
                                                                @Param("pg") PageInfo page);

    long findStoreAuthorSalesmanNewByJoinTableTotal(@Param("userId") String userId,
                                                    @Param("dto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                                    @Param("useStoreAuthorFlag") Boolean useStoreAuthorFlag,
                                                    @Param("salesmanCompanyOrgIds") List<Long> salesmanCompanyOrgIds,
                                                    @Param("salesmanStoreOrgIds") List<Long> salesmanStoreOrgIds,
                                                    @Param("isAllStoreAuthor") Boolean isAllStoreAuthor,
                                                    @Param("companyOrgIdsForStoreAuthor") List<Long> companyOrgIdsForStoreAuthor);

    long findStoreAuthorSalesmanNewByJoinTableTotalAllStatus(@Param("userId") String userId,
                                                             @Param("dto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                                             @Param("useStoreAuthorFlag") Boolean useStoreAuthorFlag,
                                                             @Param("salesmanCompanyOrgIds") List<Long> salesmanCompanyOrgIds,
                                                             @Param("salesmanStoreOrgIds") List<Long> salesmanStoreOrgIds,
                                                             @Param("isAllStoreAuthor") Boolean isAllStoreAuthor,
                                                             @Param("companyOrgIdsForStoreAuthor") List<Long> companyOrgIdsForStoreAuthor);


    List<FindSalesmanByStoreIdOutDto> findStoreAuthorSalesmanNewByJoinTable(@Param("userId") String userId,
                                                                            @Param("dto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                                                            @Param("useStoreAuthorFlag") Boolean useStoreAuthorFlag,
                                                                            @Param("salesmanCompanyOrgIds") List<Long> salesmanCompanyOrgIds,
                                                                            @Param("salesmanStoreOrgIds") List<Long> salesmanStoreOrgIds,
                                                                            @Param("isAllStoreAuthor") Boolean isAllStoreAuthor,
                                                                            @Param("companyOrgIdsForStoreAuthor") List<Long> companyOrgIdsForStoreAuthor,
                                                                            @Param("pg") PageInfo<FindSalesmanByStoreIdOutDto> pageInfo);


    List<FindSalesmanByStoreIdOutDto> findStoreAuthorSalesmanNewByJoinTableAllStatus(@Param("userId") String userId,
                                                                                     @Param("dto") FindSalesmanByIdInDto findSalesmanByIdInDto,
                                                                                     @Param("useStoreAuthorFlag") Boolean useStoreAuthorFlag,
                                                                                     @Param("salesmanCompanyOrgIds") List<Long> salesmanCompanyOrgIds,
                                                                                     @Param("salesmanStoreOrgIds") List<Long> salesmanStoreOrgIds,
                                                                                     @Param("isAllStoreAuthor") Boolean isAllStoreAuthor,
                                                                                     @Param("companyOrgIdsForStoreAuthor") List<Long> companyOrgIdsForStoreAuthor,
                                                                                     @Param("pg") PageInfo<FindSalesmanByStoreIdOutDto> pageInfo);

    Integer countSalesmanByDiffAuth(@Param("currentUserId") String currentUserId,
                                    @Param("dto") SalesmanByDiffAuthInDTO dto);

    List<SalesmanByDiffAuthOutDTO> pageSalesmanByDiffAuth(@Param("currentUserId") String currentUserId,
                                                          @Param("dto") SalesmanByDiffAuthInDTO dto,
                                                          @Param("pg") PageInfo<SalesmanByDiffAuthOutDTO> pg);

    //根据部门id查询门店的业务员
    List<SalesmanEntity> findSalesmanByDepIds(@Param("orgList") List<Long> orgList);

    //根据门店id查询门店的业务员(只查询客户经理)
    int findSalesmanByStoreId3Total(@Param("storeId") Long storeId, @Param("findOthers") Integer findOthers, @Param("station") Long station);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreId3(@Param("storeId") Long storeId, @Param("findOthers") Integer findOthers, @Param("station") Long station, @Param("pg") PageInfo page);

    List<SalesmanEntity> findDepUser(@Param("depIdListNest") List<List<Long>> depIdListNest);

    List<SalesmanEntity> findDepUser3(@Param("depIdListNest") List<List<Long>> depIdListNest);

    List<FindDepUser3DTO> findDepUser33(@Param("depIdListNest") List<List<Long>> depIdListNest);

    //根据名称查询"客户经理"
    List<SalesmanEntity> findSalesmanByNameAndStation(@Param("name") String name, @Param("station") Long station);

    //根据编码查询
    List<SalesmanEntity> findByCodes(@Param("codeList") List<String> codeList);

    //根据id集合查询
    List<SalesmanEntity> findByIds(@Param("idList") List<Long> idList);

    //根据父id集合查询下级
    List<SalesmanEntity> findByParentIdList(@Param("parentIdList") List<Long> parentIdList);

    //查询家装公司下拉
    int selectSalesmanListCount(@Param("inDto") SelectSalesmanListInDto inDto, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList);

    List<SelectSalesmanListOutDto> selectSalesmanList(@Param("inDto") SelectSalesmanListInDto inDto, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("pg") PageInfo page);

    // 根据姓名，工号，手机号,公司id查询同公司其他业务员
    int findSalesmanByNameOrCodeOrPhoneTotal(@Param("inDto") FindSalesmanByNameOrCodeOrPhoneInDto findSalesmanByNameOrCodeOrPhoneInDto, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByNameOrCodeOrPhone(@Param("inDto") FindSalesmanByNameOrCodeOrPhoneInDto findSalesmanByNameOrCodeOrPhoneInDto, @Param("companyAuthorList") List<CompanyAuthor> companyAuthorList, @Param("pg") PageInfo page);

    List<SalesmanEntity> findListByCompanyIds(@Param("idList") List<Long> companyIds);


    SalesmanEntity findOneSalesmanInfoByStoreId(@Param("storeId") Long storeId, @Param("station") Long station);

    SalesmanInfoForEFotileDto findSalesmanForEFotileById(Long id);

    //根据分公司ID，查询该分公司下面所有的厨电顾问的业务员（角色）
    Integer findSalesmanByComPanyIdTotal(@Param("companyId") Long companyId, @Param("station") Long station);

    List<SalesmanEntity> findSalesmanByComPanyId(@Param("companyId") Long companyId, @Param("station") Long station, @Param("pg") PageInfo page);

    //根据经纬度获取附近门店业务员的二维码(只返回一条)
    FindSalesmanByPositionOutDto findSalesmanByPosition(FindSalesmanByPositionInDto findSalesmanByPositionInDto);

    //根据当前业务员id查询当前业务员及下级业务员
    List<SalesmanEntity> findSubByParentId(@Param("id") Long id);

    List<Long> findSubIdsByParentId(@Param("id") Long id);

    //更新fullPathId
    void updateFullPathId(SalesmanEntity salesman);

    //找上级业务员
    List<SalesmanEntity> findPreById(@Param("parentId") Long parentId);

    //根据门店id集合查询业务员
    List<SalesmanEntity> findSalesmanInfoByStoreIdList(@Param("storeIdList") List<Long> storeIdList);

    //修改fullPathId、fullPathName
    void updateFull(@Param("id") Long id, @Param("preFullPathId") String preFullPathId, @Param("fullPathId") String fullPathId, @Param("preFullPathName") String preFullPathName,
                    @Param("fullPathName") String fullPathName,
                    @Param("encryptPassword") String encryptPassword);

    List<ReportSalasmanInfo> getReportSalasManListInfo(@Param("storeIdList") List<Long> orgIds);

    //added by zhongdian,2020/09/24
    //业务员名下家装小B端引流以及设计师跟随业务员一同迁移到新门店
    void changeSalesmanStoreNeedMigration(@Param("request") ChangeSalesmanStoreInDto request);

    //家装小B端引流数据业务员字段置为空，数据留存在所属门店
    void changeSalesmanStoreNoMigration(@Param("request") ChangeSalesmanStoreInDto request, @Param("encryptPassword") String encryptPassword);

    List<GetSalesmanByStoreOrgIdsOutDTO> getSalesmanByStoreOrgIdsAndStation(GetSalesmanByStoreOrgIdsInDTO getSalesmanByStoreOrgIdsInDTO);

    List<GetSalesmanByStoreOrgIdsOutDTO> getSalesmanByStoreIdAndStation(GetSalesmanByStoreOrgIdsInDTO getSalesmanByStoreOrgIdsInDTO);

    //根据门店数据权限查询业务员
    int findSalesmanByStoreAuthorListTotal(@Param("salesmanName") String salesmanName,
                                           @Param("companyIdList") List<Long> companyIdList,
                                           @Param("storeAuthorList") List<StoreAuthor> storeAuthorList);

    //根据门店数据权限查询业务员
    List<SalesmanEntity> findSalesmanByAuthorList(@Param("salesmanName") String salesmanName,
                                                  @Param("companyIdList") List<Long> companyIdList,
                                                  @Param("storeAuthorList") List<StoreAuthor> storeAuthorList,
                                                  @Param("pg") PageInfo page);

    List<FindSalesmanByStoreIdOutDto> findSalesmanByStoreIds(@Param("storeIds") List<Long> storeIds);

    SalesmanEntity getChargeInfoForCRMByCode(@Param("code") String code);

    //根据id和编码查询业务员
    SalesmanSimpleInfo findByIdORCode(SalesmanSimpleInDto salesmanSimpleInDto);

    //业务员批量分配上级
    void batchSetParent(@Param("inDto") BatchSetParentInDto batchSetParentInDto, @Param("modifiedBy") String modifiedBy);
    //业务员批量分配岗位
    void batchSetStation(@Param("inDto") BatchSetParentInDto batchSetParentInDto, @Param("modifiedBy") String modifiedBy);

    void UpdateSalesmanByFullPathIdIs(@Param("name") String name, @Param("id") Long id, @Param("ids") List<Long> ids, @Param("encryptPassword") String encryptPassword);

    List<SalesmanStationDto> getSalesmanStationList(@Param("storeOrgId") Long storeOrgId);


    List<SalesmanStoreProtoTaskDto> getSalesmanStoreProtoTaskList(@Param("query") QueryStoreProtoTaskDto query);

    List<SalesmanBaseInfo> getSalesmanByPhoneList(@Param("phoneList") List<String> phoneList);

    List<SalesmanBaseInfo> getSalesmanByCsmStaffCodeList(@Param("csmStaffCodeList") List<String>  csmStaffCodeList);

    SalesmanBaseInfo getSalesmanByPhone(@Param("phone") String phone,@Param("status") Integer status);

    //根据id查询
    SalesmanTreeDto getTreeDtoById(@Param("id") Long id);

    List<SalesmanTreeDto> getChildrenByParentId(@Param("parentId") Long parentId);

    Integer getChildrenCountByParentId(@Param("parentId") Long parentId);


    List<SalesmanInfoForQYWX> getSalesmanInfoForQYWX(@Param("phoneList") List<String> phoneList);

    List<SalesmanInfoForQYWX> getSalesmanInfoForQYWXStatus(@Param("phoneList") List<String> phoneList);

    List<SalesmanInfoForQYWX> getSalesmanInfoByIdsForQYWX(@Param("ids") List<Long> ids);

    List<SalesmanInfoForQYWX> getSalesmanInfoByIdsForQYWXStatus(@Param("ids") List<Long> ids);

    //查询某个时间段内修改过的业务员（范围广东）--数量
    int queryChargeUserByTime(@Param("startTime") String startTime
            , @Param("endTime") String endTime, @Param("companyId") Long companyId, @Param("ids") List<Long> ids);

    List<SalesmanMspDataBO> queryChargeUserByTimeList(@Param("startTime") String startTime
            , @Param("endTime") String endTime, @Param("companyId") Long companyId
            , @Param("ids") List<Long> ids
            , @Param("size") Integer size, @Param("num") Integer num);

    List<GetSalesmanByStoreOrgIdsOutDTO> listSalesmanByStoreOrgIdsAndStationAndStatus(@Param("dto") ListSalesmanByStoreOrgIdsInDTO dto);

    int updateForAPP(UpdateSalesmanAppInDto updateSalesmanAppInDto);

    int updateSalesmanForAPP(UpdateSalesmanAppInDto updateSalesmanAppInDto);


    long getSalesmanByAuthorCount(@Param("param") FindSalesmanPageAllPcInDto param);

    long getAllStatusSalesmanByAuthorCount(@Param("param") FindSalesmanPageAllPcInDto param);

    List<SalesmanEntity> getSalesmanByAuthor(@Param("param") FindSalesmanPageAllPcInDto param, @Param("pageInfo") PageInfo<SalesmanEntity> pageInfo);

    List<SalesmanEntity> getAllStatusSalesmanByAuthor(@Param("param") FindSalesmanPageAllPcInDto param, @Param("pageInfo") PageInfo<SalesmanEntity> pageInfo);

    //临时方法，刷业务员的full_path_id和full_path_name 查询所有业务员
    List<SalesmanEntity> findAll(Long salesmanId);

    //更新fullPathId
    void updateAllFullPathId(@Param("salesmanList") List<SalesmanEntity> salesmanList);

    /**
     * 查询当前登录人门店权限下启用状态的门店启用的业务员
     */
    List<QuerySalesmanVo> getSalesmanByStoreAuthor(@Param("userId") String userId,
                                                   @Param("isAllCompanyAuthor") boolean isAllCompanyAuthor,
                                                   @Param("isAllStoreAuthor") boolean isAllStoreAuthor,
                                                   @Param("defaultStationIdList") List<Long> defaultStationIdList);

    /**
     * 查询当前登录人门店权限下启用状态的门店启用的业务员
     */
    List<QuerySalesmanVo> getSalesmanByStoreAuthorByCondition(@Param("userId") String userId,
                                                              @Param("isAllCompanyAuthor") boolean isAllCompanyAuthor,
                                                              @Param("isAllStoreAuthor") boolean isAllStoreAuthor,
                                                              @Param("req") GetSalesmanByConditionInDTO req,
                                                              @Param("defaultStationIdList") List<Long> defaultStationIdList);

    /**
     * 查询当前登录人分公司权限下启用状态的门店下启用的业务员
     */
    List<QuerySalesmanVo> getSalesmanByCompanyAuthor(@Param("userId") String userId,
                                                     @Param("isAllCompanyAuthor") boolean isAllCompanyAuthor,
                                                     @Param("defaultStationIdList") List<Long> defaultStationIdList);

    List<QuerySalesmanVo> getSalesmanByCompanyAuthorByCondition(@Param("userId") String userId,
                                                                @Param("isAllCompanyAuthor") boolean isAllCompanyAuthor,
                                                                @Param("req") GetSalesmanByConditionInDTO req,
                                                                @Param("defaultStationIdList") List<Long> defaultStationIdList);

    QuerySalesmanVo getSalesmanByUserId(@Param("userId") String userId);

    List<SalesmanBaseVo> getSalesmanBySalesmanBaseBo(SalesmanBaseBo salesmanBaseBo);

    List<SalesmanBaseVo> getSalesmanByParam(SalesmanBaseBo salesmanBaseBo);

    List<SalesmanEntity> findSalesmanOrderByOrderCount(@Param("param") FindSalesmanPageAllPcInDto dto);

    SalesmanEntity findChargeUserById(@Param("id") Long id);

    //更新fullPathId
    void updateFullPathIdByIdList(@Param("idList") List<Long> idList, @Param("encryptPassword") String encryptPassword);

    SalesmanDetailVo getSalesmanDetailById(@Param("id") Long id);

    void repairLoop(@Param("salesmanList") List<SalesmanEntity> salesmanList);

    List<SalesmanAuditLink> getSalesmanFullAuditLinks(@Param("companyId") Long companyId,
                                                      @Param("parentId") Long parentId,
                                                      @Param("status") String status,
                                                      @Param("stations") List<Integer> stations);

    //根据id集合查询
    List<SalesmanEntity> findByIds2(@Param("idList") List<Long> idList);

    Long getSalesmanStation(@Param("salesmanId") Long salesmanId,
                            @Param("code") String code);

    //【性能优化】获取部门权限下的业务员列表（通过关联usercenter解决权限性能问题）
    List<FindSalesmanByDepIdOutDto> findSimpleSalesmanListByDepAuthor(@Param("userId") String userId,
                                                                      @Param("depIdListNest") List<List<Long>> depIdListNest,
                                                                      @Param("depOrgId") Long depOrgId,
                                                                      @Param("salesmanName") String salesmanName);

    //【性能优化】获取部门权限下的业务员id列表（通过关联usercenter解决权限性能问题）
    List<String> findSalesmanIdStrsByDepAuthor(@Param("userId") String userId,
                                               @Param("depIdListNest") List<List<Long>> depIdListNest);

    List<Long> findSalesmanIdWithNoStatusByDepAuthor(@Param("userId") String userId,
                                                     @Param("depIdListNest") List<List<Long>> depIdListNest);

    List<KvLL> findSalesmanIdStrs(@Param("userId") String userId);

    List<KvLL> findSalesmanIdWithNoStatus(@Param("userId") String userId);

    //根据id查询
    SalesmanEntity findNameById(Long id);

    //根据id查询
    List<SalesmanEntity> findNameByIds(@Param("idList") List<Long> idList);

    //根据id查询
    List<SalesmanEntity> findNameByCodes(@Param("codeList") List<String> codeList);

    List<FindSalesmanSimpleInfoOutDto> getSalesmanSimpleInfoList(@Param("query") FindSalesmanSimpleInfoInDto query);


    SalesmanEntity findSalesmanById(Long salesmanId);

    List<SalesmanEntity> getByParam(SalesmanEntity param);

    List<FindSalesmanByStoreIdOutDto> findSalesmanSelectByStoreOrgId(FindSalesmanByStoreIdInDto findSalesmanByStoreIdInDto);

    /**
     * 查询业务员总记录数
     *
     * @param query 查询参数
     * @return int
     */
    Integer searchSalesmanCount(@Param("query") SearchSalesmanInDto query);

    /**
     * 查询业务员分页记录
     *
     * @param query    查询参数
     * @param pageInfo 分页参数
     * @return page of SearchSalesmanOutDto
     */
    List<SearchSalesmanOutDto> searchSalesman(@Param("query") SearchSalesmanInDto query,
                                              @Param("pageInfo") PageInfo<?> pageInfo);

    List<CountSalesmanByDepDTO> countSalesmanByDepIds(@Param("subDepIdListNest") List<List<Long>> subDepIdListNest);

    SearchSalesmanOutDto selectByPhone(@Param("phone") String phone);

    List<Long> listSubSalesmanIdsById(@Param("id") Long id);

    Integer findSalesmanByCompanyIdAndStatusCount(FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);

    List<SalesmanEntity> findSalesmanByCompanyIdAndStatus(FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);

    List<SalesmanDTO> findSalesmanByCompanyIdAndStatus2(FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);

    List<Salesman> findParam(SalesmanInDto inDto);

    List<SalesmanInfo> findSalesmanInfosUnderCurrentAccount(@Param("query") AppSearchSalesmanInDTO query);

    void updateByCode(@Param("newCode") String newCode, @Param("id") Long id);

    SalesmanEntity findSalesmanForCRMByCode(@Param("code") String code);

    Integer isApprovalStation(@Param("salesmanId") Long salesmanId,
                              @Param("auditLinkType") Integer auditLinkType,
                              @Param("bizDataType") Integer bizDataType,
                              @Param("postId") Long postId);

    int countSubSalesmanByDepAuthor(@Param("dto") FindSubSalesmanInDTO dto,
                                    @Param("depAuthorIdList") List<Long> depAuthorIdList,
                                    @Param("encryptPassword") String encryptPassword);

    List<FindSubSalesmanOutDTO> findSubSalesmanByDepAuthor(@Param("pg") PageInfo<FindSubSalesmanOutDTO> pg,
                                                           @Param("dto") FindSubSalesmanInDTO dto,
                                                           @Param("depAuthorIdList") List<Long> depAuthorIdList,
                                                           @Param("encryptPassword") String encryptPassword);

    List<FindSubSalesmanOutDTO> findSubSalesmanByDepAuthor2(@Param("depAuthorIdList") List<Long> depAuthorIdList);


    List<SalesmanOutDto> findSalesmanByOrgStation(FindSalesmanByOrgStationInDto dto);

    List<SalesmanOutDto> findSalesmanByOrgStation2(FindSalesmanByOrgStationInDto dto);

    Long findCompanyIdBySalemanId(Long salemanId);

    /**
     * 通过业务员编码获取业务ID列表
     */
    List<Long> getSalesmanIdsByCodes(@Param("codes") List<String> codes);

    List<ListSalesmanFromDataFormByStationsOutDTO> listSalesmanFromDataFromByStations(@Param("dto") ListSalesmanFromDataFormByStationsInDTO dto);

    //根据部门id查询门店的业务员
    List<String> findSalesmanIdsByDepId(@Param("depId") Long depId);

    Integer countPhoneNumByPhone(@Param("phone") String phone);

    Integer countPhoneNumByPhoneExtSelf(@Param("phone") String phone,
                                        @Param("id") Long id);

    //查询全部业务员列表
    List<ListAllSimpleSalesmanDTO> listAllSimpleSalesman(@Param("dto") ListAllSimpleSalesmanInDTO dto);

    List<SalesmanInfoForQR> getChargeUserInfoForQR(@Param("ids") List<Long> ids);

    void updateQrcode(@Param("updateQRList") List<SalesmanInfoForQR> updateQRList);

    @DruidTxcMultiDataSourceAnnontation(value = "sixth")
    Integer findEmployeeSaleManCount(@Param("dto") FindSalesmanPageAllPcInDto dto);

    @DruidTxcMultiDataSourceAnnontation(value = "sixth")
    List<SalesmanEntity> findEmployeeSaleManList(@Param("dto") FindSalesmanPageAllPcInDto dto, @Param("pg") PageInfo<SalesmanEntity> pageInfo);

    ListAllSimpleSalesmanDTO getSalesmanById(Long id);

    List<Long> getSalesmanIdsByConditions(QuerySalesmanConditionDTO querySalesmanConditionDTO);

    List<FindInviteUserAllPcOutDto> findInviteUserAll(@Param("invite") FindInviteUserAllPcInDto inDto);

    Long findInviteUserAllCount(@Param("invite") FindInviteUserAllPcInDto inDto);

    Long findAllInviteCount(@Param("invite") FindInviteUserAllPcInDto inDto);


    int clearQrCode(ClearQrCodeInDTO dto);

    List<Long> filterIds(@Param("idList") List<Long> idList);

    List<SalesmanEntity> findByCodesJurisdiction(@Param("codeList") List<String> codeList,@Param("collect") List<Long> collect);

    Integer querySalesmanSelectBoxCount(@Param("query") QuerySalesmanSelectBoxDTO query);

    List<SalesmanBaseVo> querySalesmanSelectBox(@Param("query") QuerySalesmanSelectBoxDTO query,
                                                @Param("pageInfo") PageInfo<SalesmanBaseVo> pageInfo);

    SalesmanBaseVo getSalesmanSelectBox(@Param("id")Long id, @Param("code") String code, @Param("phone") String phone);

    String getSalesmanCodeById(@Param("salesmanId") Long salesmanId);

    List<FindSalesmanByIdOutDto> findByUserIds(FindSalesmanByUserIdsInDto inDto);


    List<SalesmanInfo> findSalesmanStatus(FindSalesmanByIds2InDto query);

    Integer findSalesmanCountByStoreIdAndChannel(FindSalesmanCountByStoreIdAndChannelDTO findSalesmanCountByStoreIdAndChannelDTO);

    Integer findSalesmanCountByPost(FindSalesmanCountByPostDTO findSalesmanCountByPostDTO);

    List<String> findSalesmanCountByCode(FindSalesmanCountByCodeDTO findSalesmanCountByCodeDTO);

    //查询业务员信息（带门店地址）
    List<ListAllSimpleSalesmanDTO> listAllSimpleSalesman2(@Param("dto") ListAllSimpleSalesmanInDTO dto);


    Integer getKuJialeChargeUserAuthCount(@Param("param") FindSalesmanPageAllPcInDto param);
    List<SalesmanEntity> getKuJialeSalesmanByAuthor(@Param("param") FindSalesmanPageAllPcInDto param, @Param("pageInfo") PageInfo<SalesmanEntity> pageInfo);

    FindCompanyClueChargeVO querySalesmanBydouyinId(@Param("douyinChargeUserId") String douyinChargeUserId);

    FindCompanyClueChargeVO querySalesmanBydouyinStoreId(@Param("douyinStoreId") String douyinStoreId,@Param("companyId")Long companyId);

    FindCompanyClueChargeVO querySalesmanBydouyinStoreIdAndStation(@Param("douyinStoreId") String douyinStoreId, @Param("list") List<String> list,@Param("companyId")Long companyId);

    FindCompanyClueChargeVO querySalesmanDouyinById(@Param("salesmanId") Long salesmanId);

    SalesmanEntity getDefendantExecutor(@Param("storeId") Long storeId);
    List<SalesmanEntity> findNewBasisSalesmanEntity(@Param("salesmanIds") List<Long> salesmanIds);

    List<TiktokSalesmanDistributionVO> tiktokSalesmanDistribution();

    List<Long> findSalesmanIdByLabelValue(@Param("dto") FindSalesmanIdByLabelValueInDto dto);
}
