package com.fotile.orgcenter.decorate.service;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.orgcenter.attachment.pojo.dto.AttachmentInfo;
import com.fotile.orgcenter.attachment.service.AttachmentService;
import com.fotile.orgcenter.common.constant.AttachmentEnum;
import com.fotile.orgcenter.common.constant.ChannelAuditResult;
import com.fotile.orgcenter.common.constant.TargetTypeEnum;
import com.fotile.orgcenter.decorate.pojo.dto.*;
import com.fotile.orgcenter.decorate.pojo.entity.AgreementChangedRecordEntity;
import com.fotile.orgcenter.decorate.pojo.vo.ChannelAuditLinkVO;
import com.fotile.orgcenter.designer.pojo.dto.DesignerAuditRequest;
import com.fotile.orgcenter.designer.service.DesignerService;
import com.fotile.orgcenter.operatorOrgLog.pojo.dto.FollowUpAuditRequest;
import com.fotile.orgcenter.operatorOrgLog.service.OperatorOrgLogService;
import com.fotile.orgcenter.salesman.mapper.SalesmanDao;
import com.fotile.orgcenter.salesman.pojo.entity.SalesmanEntity;
import com.fotile.orgcenter.salesman.service.SalesmanService;
import com.fotile.orgcenter.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.orgcenter.decorate.service
 * @date 2023/2/2 16:06
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class AuditCommonService {
    @Autowired
    private DecorateService decorateService;

    @Autowired
    private DiffIndustryService diffIndustryService;

    @Autowired
    private DesignerService designerService;

    @Autowired
    private FieldChangeService fieldChangeService;

    @Autowired
    private DisableService disableService;

    @Autowired
    private OperatorOrgLogService operatorOrgLogService;

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private SalesmanDao salesmanDao;

    @Autowired
    private AppSearchReadOnlyService appSearchReadOnlyService;

    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private AgreementChangedService agreementChangedService;

    @Autowired
    private AttachmentService attachmentService;

    /**
     * 引流渠道批量审核
     *
     * @param request 审核请求
     * @return list of ReviewItemDTO
     */
    public ReviewResponse batchReview(ReviewRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getReviewItems()) || request.getTargetType() == null || request.getAuditStatus() == null) {
            throw new BusinessException("审核参数错误！");
        }
        Map<String, String> user = CommonUtils.getAuthorUser();
        request.setOperatorId(user.get("userid"));
        request.setOperatorName(user.get("name"));

        Long currentSalesmanId = CommonUtils.getSalesmanId(userAuthorConfig);
        if (currentSalesmanId == null || currentSalesmanId <= 0) {
            throw new BusinessException("当前业务员信息不存在！");
        }
        SalesmanEntity currentSalesman = salesmanDao.findById(currentSalesmanId);
        if (currentSalesman == null) {
            throw new BusinessException("当前业务员信息不存在！");
        }
        request.setCurrentSalesman(currentSalesman);


        int rows = 0;
        boolean hasCreated = false, hasChanged = false, hasDisabled = false, hasFollowUp = false;
        List<ReviewItemDTO> createdAuditItems = request.getReviewItems().stream().filter(x -> x.getAuditType() != null && x.getAuditType() == 1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(createdAuditItems)) {
            rows += createdAuditItems.size();
            hasCreated = true;
        }

        List<ReviewItemDTO> changedAuditItems = request.getReviewItems().stream().filter(x -> x.getAuditType() != null && x.getAuditType() == 2).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changedAuditItems)) {
            rows += changedAuditItems.size();
            hasChanged = true;
        }

        List<ReviewItemDTO> disabledAuditItems = request.getReviewItems().stream().filter(x -> x.getAuditType() != null && x.getAuditType() == 3).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledAuditItems)) {
            rows += disabledAuditItems.size();
            hasDisabled = true;
        }

        List<ReviewItemDTO> followUpAuditItems = request.getReviewItems().stream().filter(x -> x.getAuditType() != null && x.getAuditType() == 4).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(followUpAuditItems)) {
            rows += followUpAuditItems.size();
            hasFollowUp = true;
        }

        List<ReviewItemDTO> createdAuditResults = new ArrayList<>();
        if (hasCreated) {
            //批量审核新增数据
            try {
                if (request.getTargetType() == 1) {
                    AuditDecorateInDto auditInfo = null;
                    for (ReviewItemDTO createdAuditItem : createdAuditItems) {
                        try {
                            //家装公司新增审核
                            auditInfo = new AuditDecorateInDto();
                            auditInfo.setAuditStatus(request.getAuditStatus().toString());
                            auditInfo.setIdList(Collections.singletonList(createdAuditItem.getId()));
                            auditInfo.setIsFromApp(1);
                            auditInfo.setAuditMsg(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                            auditInfo.setOperatorId(request.getOperatorId());
                            auditInfo.setOperatorName(request.getOperatorName());

                            Integer ret = decorateService.auditDecorate(auditInfo);
                            if (ret != null && ret == 1) {
                                createdAuditItem.setAuditResults("succeed");
                            } else {
                                createdAuditItem.setAuditResults("审核失败，请与管理员联系！");
                            }
                        } catch (Exception ex) {
                            log.error("AuditCommonService.batchReview.createdAuditTask 家装公司新增审核失败！===> parameters : " + JSON.toJSONString(createdAuditItem), ex);
                            createdAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                        } finally {
                            createdAuditResults.add(createdAuditItem);
                        }
                    }

                } else if (request.getTargetType() == 2) {
                    AuditDiffIndustryInDto auditInfo = null;
                    for (ReviewItemDTO createdAuditItem : createdAuditItems) {
                        try {
                            //小B端引流新增审核
                            auditInfo = new AuditDiffIndustryInDto();
                            auditInfo.setId(createdAuditItem.getId());
                            auditInfo.setAuditStatus(request.getAuditStatus().toString());
                            auditInfo.setIsFromApp(1);
                            auditInfo.setAuditMsg(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                            auditInfo.setOperatorId(request.getOperatorId());
                            auditInfo.setOperatorName(request.getOperatorName());

                            Integer ret = diffIndustryService.auditDiffIndustry(auditInfo);
                            if (ret != null && ret == 1) {
                                createdAuditItem.setAuditResults("succeed");
                            } else {
                                createdAuditItem.setAuditResults("审核失败，请与管理员联系！");
                            }
                        } catch (Exception ex) {
                            log.error("AuditCommonService.batchReview.createdAuditTask 小B端引流新增审核失败！===> request : " + JSON.toJSONString(request), ex);
                            log.error("AuditCommonService.batchReview.createdAuditTask 小B端引流新增审核失败！===> parameters : " + JSON.toJSONString(createdAuditItem), ex);
                            createdAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                        } finally {
                            createdAuditResults.add(createdAuditItem);
                        }
                    }
                } else if (request.getTargetType() == 3) {
                    //设计师新增审核
                    DesignerAuditRequest auditInfo = null;
                    for (ReviewItemDTO createdAuditItem : createdAuditItems) {
                        try {
                            auditInfo = new DesignerAuditRequest();
                            auditInfo.setDesignerId(createdAuditItem.getId());
                            auditInfo.setDecorateCompanyId(createdAuditItem.getDecorateCompanyId());
                            auditInfo.setAuditStatus(request.getAuditStatus());
                            auditInfo.setIsFromApp(1);
                            auditInfo.setAuditRemark(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                            auditInfo.setOperatorId(request.getOperatorId());
                            auditInfo.setOperatorName(request.getOperatorName());

                            Long ret = designerService.auditDesigner(auditInfo);
                            if (ret != null && ret > 0) {
                                createdAuditItem.setAuditResults("succeed");
                            } else {
                                createdAuditItem.setAuditResults("审核失败，请与管理员联系！");
                            }
                        } catch (Exception ex) {
                            log.error("AuditCommonService.batchReview.createdAuditTask 设计师新增审核失败！===> request : " + JSON.toJSONString(request), ex);
                            log.error("AuditCommonService.batchReview.createdAuditTask 设计师新增审核失败！===> parameters : " + JSON.toJSONString(createdAuditItem), ex);
                            createdAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                        } finally {
                            createdAuditResults.add(createdAuditItem);
                        }

                    }
                }
            } catch (Exception ex) {
                log.error("AuditCommonService.batchReview.createdAuditTask ERROR >>>>>", ex);
            }
        }

        List<ReviewItemDTO> changedAuditResults = new ArrayList<>();
        if (hasChanged) {
            //批量审核字段变更数据
            try {
                FieldChangeAuditRequest auditInfo = null;
                for (ReviewItemDTO changedAuditItem : changedAuditItems) {
                    try {
                        //家装公司、小B端引流、设计师字段变更审核
                        auditInfo = new FieldChangeAuditRequest();
                        auditInfo.setCurrentSalesman(request.getCurrentSalesman());
                        auditInfo.setFieldChangeApplyId(changedAuditItem.getApplyId());
                        auditInfo.setAuditStatus(request.getAuditStatus());
                        auditInfo.setAuditRemark(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                        auditInfo.setOperatorId(request.getOperatorId());
                        auditInfo.setOperatorName(request.getOperatorName());
                        auditInfo.setTargetType(request.getTargetType());
                        auditInfo.setTargetId(changedAuditItem.getId());

                        Long ret = fieldChangeService.audit(auditInfo);
                        if (ret != null && ret > 0) {
                            changedAuditItem.setAuditResults("succeed");

                            //判断当前是否为终审通过(字段变更申请记录审核&清理图片)
                            if (ChannelAuditResult.PASSED.getStatus().equals(auditInfo.getAuditStatus())) {
                                FieldChangeApplyDTO applyInfo = fieldChangeService.getFieldChangeApplyInfo(changedAuditItem.getApplyId());
                                this.insertAgreementChangedRecordAfterFinalAuditPassed(applyInfo);
                            }
                        } else {
                            changedAuditItem.setAuditResults("审核失败，请与管理员联系！");
                        }
                    } catch (Exception ex) {
                        log.error("AuditCommonService.batchReview.changedAuditTask 引流渠道字段变更审核失败！===> request : " + JSON.toJSONString(request), ex);
                        log.error("AuditCommonService.batchReview.changedAuditTask 引流渠道字段变更审核失败！===> parameters : " + JSON.toJSONString(changedAuditItem), ex);
                        changedAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                    } finally {
                        changedAuditResults.add(changedAuditItem);
                    }
                }
            } catch (Exception ex) {
                log.error("AuditCommonService.batchReview.changedAuditTask ERROR >>>>>", ex);
            }
        }

        List<ReviewItemDTO> disabledAuditResults = new ArrayList<>();
        if (hasDisabled) {
            try {
                DisableAuditRequest auditInfo = null;
                for (ReviewItemDTO disabledAuditItem : disabledAuditItems) {
                    try {
                        //家装公司、小B端引流、设计师禁用审核
                        auditInfo = new DisableAuditRequest();
                        auditInfo.setCurrentSalesman(request.getCurrentSalesman());
                        auditInfo.setDisableApplyId(disabledAuditItem.getApplyId());
                        auditInfo.setAuditStatus(request.getAuditStatus());
                        auditInfo.setAuditRemark(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                        auditInfo.setOperatorId(request.getOperatorId());
                        auditInfo.setOperatorName(request.getOperatorName());
                        auditInfo.setTargetType(request.getTargetType());
                        auditInfo.setTargetId(disabledAuditItem.getId());

                        Long ret = disableService.audit(auditInfo);
                        if (ret != null && ret > 0) {
                            disabledAuditItem.setAuditResults("succeed");
                        } else {
                            disabledAuditItem.setAuditResults("审核失败，请与管理员联系！");
                        }
                    } catch (Exception ex) {
                        log.error("AuditCommonService.batchReview.changedAuditTask 引流渠道禁用审核失败！===> request : " + JSON.toJSONString(request), ex);
                        log.error("AuditCommonService.batchReview.changedAuditTask 引流渠道禁用审核失败！===> parameters : " + JSON.toJSONString(disabledAuditItem), ex);
                        disabledAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                    } finally {
                        disabledAuditResults.add(disabledAuditItem);
                    }
                }
            } catch (Exception ex) {
                log.error("AuditCommonService.batchReview.disabledAuditTask ERROR >>>>>", ex);
            }
        }

        List<ReviewItemDTO> followUpAuditResults = new ArrayList<>();
        if (hasFollowUp) {
            try {
                FollowUpAuditRequest auditInfo = null;
                for (ReviewItemDTO followUpAuditItem : followUpAuditItems) {
                    try {
                        //家装公司、小B端引流、设计师写跟进审核
                        auditInfo = new FollowUpAuditRequest();
                        auditInfo.setCurrentSalesman(request.getCurrentSalesman());
                        //写跟进日志ID
                        auditInfo.setLogId(followUpAuditItem.getApplyId());
                        auditInfo.setAuditStatus(request.getAuditStatus());
                        auditInfo.setAuditRemark(StringUtils.isBlank(request.getAuditRemark()) ? "" : request.getAuditRemark());
                        auditInfo.setOperatorId(request.getOperatorId());
                        auditInfo.setOperatorName(request.getOperatorName());
                        auditInfo.setTargetType(request.getTargetType());
                        //家装公司、小B端引流、设计师ID
                        auditInfo.setTargetId(followUpAuditItem.getId());

                        Long ret = operatorOrgLogService.audit(auditInfo);
                        if (ret != null && ret > 0) {
                            followUpAuditItem.setAuditResults("succeed");
                        } else {
                            followUpAuditItem.setAuditResults("审核失败，请与管理员联系！");
                        }
                    } catch (Exception ex) {
                        log.error("AuditCommonService.batchReview.followUpAuditTask 引流渠道写跟进审核失败！===> request : " + JSON.toJSONString(request), ex);
                        log.error("AuditCommonService.batchReview.followUpAuditTask 引流渠道写跟进审核失败！===> parameters : " + JSON.toJSONString(followUpAuditItem), ex);
                        followUpAuditItem.setAuditResults("审核异常：" + (StringUtils.isNotBlank(ex.getMessage()) ? ex.getMessage() : "请与管理员联系！"));
                    } finally {
                        followUpAuditResults.add(followUpAuditItem);
                    }
                }
            } catch (Exception ex) {
                log.error("AuditCommonService.batchReview.followUpAuditTask ERROR >>>>>", ex);
            }
        }

        ReviewResponse response = new ReviewResponse();
        try {
            //汇总执行结果
            long succeedCount = 0;
            long failedCount = 0;

            //新增审核
            if (hasCreated) {
                succeedCount = createdAuditResults.stream().filter(x -> "succeed".equals(x.getAuditResults())).count();
                failedCount = createdAuditResults.size() - succeedCount;

                response.setCreatedSucceedCount((int) succeedCount);
                response.setCreatedFailedCount((int) failedCount);

                response.getReviewResultItems().addAll(createdAuditResults);
            }

            //字段变更审核
            if (hasChanged) {
                succeedCount = changedAuditResults.stream().filter(x -> "succeed".equals(x.getAuditResults())).count();
                failedCount = changedAuditResults.size() - succeedCount;

                response.setChangedSucceedCount((int) succeedCount);
                response.setChangedFailedCount((int) failedCount);

                response.getReviewResultItems().addAll(changedAuditResults);
            }

            //禁用审核
            if (hasDisabled) {
                succeedCount = disabledAuditResults.stream().filter(x -> "succeed".equals(x.getAuditResults())).count();
                failedCount = disabledAuditResults.size() - succeedCount;

                response.setDisabledSucceedCount((int) succeedCount);
                response.setDisabledFailedCount((int) failedCount);

                response.getReviewResultItems().addAll(disabledAuditResults);
            }

            //写跟进审核
            if (hasFollowUp) {
                succeedCount = followUpAuditResults.stream().filter(x -> "succeed".equals(x.getAuditResults())).count();
                failedCount = followUpAuditResults.size() - succeedCount;

                response.setFollowUpSucceedCount((int) succeedCount);
                response.setFollowUpFailedCount((int) failedCount);

                response.getReviewResultItems().addAll(followUpAuditResults);
            }

            //汇总
            response.calc();

        } catch (Exception ex) {
            log.error("AuditCommonService.batchReview ERROR >>>>> Request : " + JSON.toJSONString(request), ex);
        }
        return response;
    }

    /**
     * 校验引流渠道审核操作
     */
    public CheckReviewResponse checkReview(CheckReviewRequest request) {
        //请求应答
        CheckReviewResponse response = new CheckReviewResponse();

        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        if (Objects.isNull(userAuthor)) {
            //throw new BusinessException("账号不存在，请联系管理员");
            return response;
        }
        if (Objects.isNull(userAuthor.getSalesmanId())) {
            //throw new BusinessException("账号未关联业务员，请联系管理员");
            return response;
        }

        SalesmanEntity salesmanEntity = salesmanService.findById3(userAuthor.getSalesmanId());
        if (Objects.isNull(salesmanEntity)) {
            //throw new BusinessException("账号查询对应业务员信息不存在，请联系管理员");
            return response;
        }

        List<Long> salesmanIds = salesmanService.findSalesmanIdsUnderCurrentAccount(null, salesmanEntity.getId(), salesmanEntity.getCompanyId());
        if (CollectionUtils.isEmpty(salesmanIds)) {
            //throw new BusinessException("没有业务员权限!");
            return response;
        }


        ReviewItemDTO reviewItem = new ReviewItemDTO();

        //请求参数
        AppSearchChannelAuditRequest checkRequest = new AppSearchChannelAuditRequest();
        checkRequest.setAuditType(request.getAuditType());
        checkRequest.setId(request.getTargetId());
        checkRequest.setApplyId(request.getApplyId());
        checkRequest.setStatus(1);
        checkRequest.setDecorateCompanyId(request.getDecorateCompanyId());
        checkRequest.setOperatorId(userAuthor.getUserId());
        checkRequest.setDataScopeCompany(CommonUtils.getUserCompanyDataScopeOrgId(userAuthorConfig));
        checkRequest.setCurrentSalesmanId(salesmanEntity.getId());
        checkRequest.setCurrentSalesmanCode(salesmanEntity.getCode());

        switch (request.getTargetType()) {
            case 1:
                //家装公司
                AppSearchDecorateAuditDTO decorateAudit = appSearchReadOnlyService.checkReviewDecorate(checkRequest);
                if (decorateAudit != null) {
                    response.setIsCanAudit(1);

                    reviewItem.setId(decorateAudit.getId());
                    reviewItem.setAuditType(decorateAudit.getAuditType());
                    reviewItem.setDecorateCompanyId(decorateAudit.getId());
                    reviewItem.setApplyId(decorateAudit.getApplyId());
                    reviewItem.setApprovalUserCode(decorateAudit.getApprovalUserCode());
                    reviewItem.setApprovalUserName(decorateAudit.getApprovalUserName());
                    reviewItem.setDecorateCompanyId(decorateAudit.getId());

                    response.setReviewItem(reviewItem);
                    response.setCategory(decorateAudit.getCategory());
                }
                break;
            case 2:
                //小B端引流
                AppSearchDiffIndustryAuditDTO diffIndustryAudit = appSearchReadOnlyService.checkReviewDiffIndustry(checkRequest);
                if (diffIndustryAudit != null) {
                    response.setIsCanAudit(1);

                    reviewItem.setId(diffIndustryAudit.getId());
                    reviewItem.setAuditType(diffIndustryAudit.getAuditType());
                    reviewItem.setDecorateCompanyId(diffIndustryAudit.getId());
                    reviewItem.setApplyId(diffIndustryAudit.getApplyId());
                    reviewItem.setApprovalUserCode(diffIndustryAudit.getApprovalUserCode());
                    reviewItem.setApprovalUserName(diffIndustryAudit.getApprovalUserName());
                    reviewItem.setDecorateCompanyId(diffIndustryAudit.getId());

                    response.setReviewItem(reviewItem);
                    response.setCategory(diffIndustryAudit.getCategory());
                }
                break;
            case 3:
                //设计师
                AppSearchDesignerAuditDTO designerAudit = appSearchReadOnlyService.checkReviewDesigner(checkRequest);
                if (designerAudit != null) {
                    response.setIsCanAudit(1);

                    reviewItem.setId(designerAudit.getId());
                    reviewItem.setAuditType(designerAudit.getAuditType());
                    reviewItem.setDecorateCompanyId(designerAudit.getId());
                    reviewItem.setApplyId(designerAudit.getApplyId());
                    reviewItem.setApprovalUserCode(designerAudit.getApprovalUserCode());
                    reviewItem.setApprovalUserName(designerAudit.getApprovalUserName());
                    reviewItem.setDecorateCompanyId(designerAudit.getDecorateCompanyId());

                    response.setReviewItem(reviewItem);
                    response.setDesignerCategory(designerAudit.getDesignerCategory() != null ? designerAudit.getDesignerCategory().toString() : "");
                }
                break;
            default:
                break;
        }

        response.setTargetStatus(appSearchReadOnlyService.getReviewObjectStatus(request.getTargetType(), request.getTargetId()));

        return response;
    }

    /**
     * 终审通过后调用 -> 新增协议签订记录(清理图片)
     */
    public Long insertAgreementChangedRecordAfterFinalAuditPassed(FieldChangeApplyDTO applyInfo) {
        if (applyInfo == null || applyInfo.getAuditStatus() == null || !ChannelAuditResult.PASSED.getStatus().equals(applyInfo.getAuditStatus())) {
            return null;
        }
        if (applyInfo.getTargetType() == null || applyInfo.getTargetId() == null) {
            return null;
        }

        AttachmentEnum ae = AttachmentEnum.NULL;
        if (TargetTypeEnum.DECORATE_COMPANY.getType().equals(applyInfo.getTargetType())) {
            ae = AttachmentEnum.DECORATE_AGREEMENT_FILES;
        } else if (TargetTypeEnum.DIFF_INDUSTRY.getType().equals(applyInfo.getTargetType())) {
            ae = AttachmentEnum.DIFF_INDUSTRY_AGREEMENT_FILES;
        } else if (TargetTypeEnum.DESIGNER.getType().equals(applyInfo.getTargetType())) {
            ae = AttachmentEnum.DESIGNER_AGREEMENT_FILES;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        String operatorId = user.get("userid");
        String operatorName = MybatisMateConfig.decrypt(user.get("name"));

        //1:如果是否签订协议从【是】变更成【否】则不记录协议签订日志
        //2:清除协议附件
        if (Objects.equals("否", applyInfo.getColSignCoAgreement()) && Objects.equals("是", applyInfo.getOldSignCoAgreement())) {
            if (ae != AttachmentEnum.NULL) {
                attachmentService.logicDeleteByRefId2(ae, applyInfo.getTargetId(), operatorId);
            }
            return null;
        }

        //解密对象
        MybatisMateConfig.decryptByReflect(applyInfo);

        //判断是否存在附件或合作协议修改
        boolean needInsert = false;
        boolean needResetImages = false;
        Set<String> imageSet = null, imageSetSrc = null;

        List<String> tmpImages = StringUtils.isNotBlank(applyInfo.getColCoAgreementFiles()) ? Arrays.asList(StringUtils.split(applyInfo.getColCoAgreementFiles(), ",")) : new ArrayList<>();
        List<String> agreementImages = CollectionUtils.isEmpty(tmpImages) ? new ArrayList<>() : tmpImages;
        imageSet = agreementImages.stream().sorted().map(String::toLowerCase).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(imageSet)) {
            imageSet = new HashSet<>();
        }

        List<String> tmpImagesSrc = StringUtils.isNotBlank(applyInfo.getOldCoAgreementFiles()) ? Arrays.asList(StringUtils.split(applyInfo.getOldCoAgreementFiles(), ",")) : new ArrayList<>();
        List<String> agreementImagesSrc = CollectionUtils.isEmpty(tmpImagesSrc) ? new ArrayList<>() : tmpImagesSrc;
        imageSetSrc = agreementImagesSrc.stream().sorted().map(String::toLowerCase).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(imageSetSrc)) {
            imageSetSrc = new HashSet<>();
        }

        if (!StringUtils.equals(applyInfo.getColCoValidStartTime(), applyInfo.getOldCoValidStartTime())) {
            needInsert = true;
        } else if (!StringUtils.equals(applyInfo.getColCoValidEndTime(), applyInfo.getOldCoValidEndTime())) {
            needInsert = true;
        }

        if (!SetUtils.isEqualSet(imageSet, imageSetSrc)) {
            needInsert = true;
            needResetImages = true;
        }

        if (!needInsert) {
            //不满足插入条件
            return null;
        }

        String remark = "";
        AgreementChangedRecordEntity record = new AgreementChangedRecordEntity();
        record.setTargetType(applyInfo.getTargetType());
        record.setTargetId(applyInfo.getTargetId());
        record.setTargetCode(applyInfo.getTargetCode());
        record.setTargetName(applyInfo.getTargetName());

        if (TargetTypeEnum.DESIGNER.getType().equals(applyInfo.getTargetType())) {
            remark = "设计师字段变更审核终审通过";
            record.setTargetCategory(applyInfo.getDesignerCategory() != null ? applyInfo.getDesignerCategory() : "");
        } else if (TargetTypeEnum.DECORATE_COMPANY.getType().equals(applyInfo.getTargetType())) {
            remark = "家装公司字段变更审核终审通过";
            record.setTargetCategory(applyInfo.getCategory() != null ? applyInfo.getCategory() : "");

            if (needResetImages && ae != AttachmentEnum.NULL) {
                //先删除协议附件
                attachmentService.logicDeleteByRefId2(ae, applyInfo.getTargetId(), operatorId);

                if (CollectionUtils.isNotEmpty(imageSet)) {
                    List<AttachmentInfo> attachmentInfos = new ArrayList<>();
                    AttachmentInfo attachmentInfo = null;
                    for (String url : imageSet) {
                        attachmentInfo = new AttachmentInfo(ae.getRefTable(), ae.getType(), applyInfo.getTargetId(), url);
                        attachmentInfos.add(attachmentInfo);
                    }

                    if (CollectionUtils.isNotEmpty(attachmentInfos)) {
                        attachmentService.batchInsertAttachment(attachmentInfos);
                    }
                }
            }
        } else if (TargetTypeEnum.DIFF_INDUSTRY.getType().equals(applyInfo.getTargetType())) {
            remark = "小B端引流字段变更审核终审通过";
            record.setTargetCategory(applyInfo.getCategory() != null ? applyInfo.getCategory() : "");
        }

        //是否签订协议
        Integer oldSignCoAgreement = StringUtils.isBlank(applyInfo.getOldSignCoAgreement()) ? null : Integer.valueOf(Objects.equals(applyInfo.getOldSignCoAgreement(), "是") ? "1" : "0");
        Integer colSignCoAgreement = StringUtils.isBlank(applyInfo.getColSignCoAgreement()) ? null : Integer.valueOf(Objects.equals(applyInfo.getColSignCoAgreement(), "是") ? "1" : "0");
        record.setSignCoAgreementSrc(oldSignCoAgreement);
        record.setSignCoAgreement(colSignCoAgreement);

        //协议等级
        record.setCoAgreementGradeSrc(applyInfo.getOldCoAgreementGrade());
        record.setCoAgreementGrade(applyInfo.getColCoAgreementGrade());

        //协议开始时间
        record.setCoValidStartTimeSrc(applyInfo.getOldCoValidStartTime());
        record.setCoValidStartTime(applyInfo.getColCoValidStartTime());

        //协议结束时间
        record.setCoValidEndTimeSrc(applyInfo.getOldCoValidEndTime());
        record.setCoValidEndTime(applyInfo.getColCoValidEndTime());

        //协议内容
        record.setCoContentSrc(applyInfo.getOldCoContent());
        record.setCoContent(applyInfo.getColCoContent());

        //协议附件
        record.setCoAttachmentFilesSrc(StringUtils.join(imageSetSrc, ","));
        record.setCoAttachmentFiles(StringUtils.join(imageSet, ","));

        record.setIsDeleted(0);
        record.setCreatedDate(new Date());
        record.setCreatedBy(operatorId);
        record.setCreatedUserName(operatorName);
        record.setModifiedBy(operatorId);
        record.setModifiedUserName(operatorName);
        record.setRemark(remark);

        return agreementChangedService.addAgreementChangedRecord(record);
    }

    public PageInfo<ChannelAuditLinkVO> queryChannelAuditLinks(QueryChannelAuditLinkDTO request) {
        if (request == null) {
            throw new BusinessException("参数错误");
        }
        String error = request.check();
        if (StringUtils.isNotBlank(error)) {
            throw new BusinessException(error);
        }

        Integer totalRows = appSearchReadOnlyService.countChannelAuditLinks(request);
        if (totalRows == null || totalRows <= 0) {
            return null;
        }

        PageInfo<ChannelAuditLinkVO> pageInfo = new PageInfo<>(request.getPage(), request.getSize(), totalRows);
        List<ChannelAuditLinkVO> list = appSearchReadOnlyService.queryChannelAuditLinks(request, pageInfo);

        pageInfo.setRecords(list);
        return pageInfo;
    }

    /*public static void main(String[] args) {
        String colImages = "";
        String oldImages = "";
        List<String> tmpImages = StringUtils.isNotBlank(colImages)
                ? Arrays.asList(StringUtils.split(colImages, ","))
                : new ArrayList<>();

        List<String> tmpImagesSrc = StringUtils.isNotBlank(oldImages)
                ? Arrays.asList(StringUtils.split(oldImages, ","))
                : new ArrayList<>();

        List<String> agreementImages = CollectionUtils.isEmpty(tmpImages) ? new ArrayList<>() : tmpImages;
        List<String> agreementImagesSrc = CollectionUtils.isEmpty(tmpImagesSrc) ? new ArrayList<>() : tmpImagesSrc;

        boolean needInsert = false;
        Set<String> imageSet = null, imageSetSrc = null;

        imageSet = agreementImages.stream().sorted().map(String::toLowerCase).collect(Collectors.toSet());
        imageSetSrc = agreementImagesSrc.stream().sorted().map(String::toLowerCase).collect(Collectors.toSet());

        if (!SetUtils.isEqualSet(imageSet, imageSetSrc)) {
            needInsert = true;
            System.out.println("insert");
        }


        System.out.println(needInsert);
        System.out.println(imageSet);
        System.out.println(imageSetSrc);
    }*/
}