package com.fotile.orgcenter.decorate.pojo.dto;

import com.fotile.framework.web.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 【整合营销-合作社】根据手机号获取家装公司/小B端引流/设计师的渠道信息及所属业务员信息入参DTO类
 */
@Data
public class Cooperative2DInfoChainCheckInDTO implements Serializable {

    /**
     * 合作社登录手机号
     */
    private String phone;

    public void checkValues() {
        if(StringUtils.isBlank(phone)){
            throw new BusinessException("[入参错误]手机号不能为空");
        }
    }
}
