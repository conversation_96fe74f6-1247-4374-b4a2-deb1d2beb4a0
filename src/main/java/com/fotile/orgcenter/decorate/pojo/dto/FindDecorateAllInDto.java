package com.fotile.orgcenter.decorate.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class FindDecorateAllInDto {

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer page;

    /**
     * 每页显示大小
     */
    @NotNull(message = "页码显示条数不能为空")
    private Integer size;

    /**
     * 家装公司编码
     */
    private String code;

    /**
     * 家装公司名称
     */
    private String name;

    /**
     * 公司类型 1：家装 2：橱柜 3：定制
     */
    private String category;

    /**
     * 审核状态 1：待审核 2：已通过 3：已拒绝
     */
    private String auditStatus;

    /**
     * 状态 0：禁用；1：启用
     */
    private String status;

    /**
     * 业务员id
     */
    private Long chargeUserId;

    /**
     * 经销商编码
     */
    private String dealersUserCode;

    /**
     * 分公司id(orgId)
     */
    private Long companyId;

    /**
     * 有效期开始时间
     */
    private String validStartTime;

    /**
     * 有效期截止时间
     */
    private String validEndTime;

}
