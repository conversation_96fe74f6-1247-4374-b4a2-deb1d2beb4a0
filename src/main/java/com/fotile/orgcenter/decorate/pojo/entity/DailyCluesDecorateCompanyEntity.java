package com.fotile.orgcenter.decorate.pojo.entity;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DailyCluesDecorateCompanyEntity extends AuditingEntity {
    /**
     * 家装公司编码
     */
    private String code;

    /**
     * 线索日期YYYY-MM-DD
     */
    private Date cluesDate;

    /**
     * 当日线索数
     */
    private Integer cluesDateCount;

    /**
     * 当日最新线索提交时间
     */
    private Date maxFundingTime;
}
