package com.fotile.orgcenter.decorate.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * 置顶操作
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.orgcenter.decorate.pojo.dto
 * @date 2021/10/14 16:06
 */
@Data
public class SetTopInDto implements Serializable {
    /**
     * 置顶ID
     */
    private Long topId;

    /**
     * 置顶数据类型(1:家装公司,2:小B端引流,3:设计师)
     */
    //@NotNull(message = "置顶数据类型不能为空！")
    //@Range(min = 1, max = 3, message = "错误的置顶数据类型！")
    private Integer type;

    /**
     * 数据主键ID(家装公司ID/小B端引流ID/设计师ID)
     */
    //@NotNull(message = "数据对象ID不能为空！")
    private Long refId;

    /**
     * 是否置顶(1:置顶,其他:非置顶)
     */
    private Integer isTop;

    @JsonIgnore
    private transient String operatorId;

    @JsonIgnore
    private transient String operatorName;

    @JsonIgnore
    private transient String refTable;
}
