package com.fotile.orgcenter.order.pojo.dto;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AddOrUpdateOrderInDto extends AuditingEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 预约表主键id
     */
    private Long id;

    /**
     * 预约类型
     */
    @NotNull(message = "预约类型不能为空")
    private String type;

    /**
     * 预约门店id
     */
    @NotNull(message = "预约场馆不能为空")
    private Long storeId;

    /**
     * 场地id
     */
    private Long areaId;

    /**
     * 预约时间
     */
    @NotNull(message = "预约时间不能为空")
    private Date visitDate;

    /**
     * 姓名
     */
    @FieldEncrypt
    @NotNull(message = "姓名不能为空")
    private String name;

    /**
     * 电话
     */
    @FieldEncrypt
    @NotNull(message = "电话不能为空")
    private String phone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private String status;

    /**
     * 预约说明
     */
    private String instructions;

    private String operator;

    private String userName;

    /**
     * 是否开车 0:否 1:是
     */
    private Integer isDrive;

    /**
     * 提交人的UID
     */
    private String uid;

    /**
     * 昵称
     */
    private String nickname;

}
