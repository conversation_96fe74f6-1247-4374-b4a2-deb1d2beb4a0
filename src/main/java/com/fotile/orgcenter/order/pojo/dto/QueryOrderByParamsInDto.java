package com.fotile.orgcenter.order.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class QueryOrderByParamsInDto {
    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer page;
    /**
     * 每页显示条数
     */
    @NotNull(message = "每页显示条数不能为空")
    private Integer size;
    /**
     * 预约id
     */
    private Long id;
    /**
     * 场馆名
     */
    private Long storeId;
    /**
     * 预约人姓名
     */
    private String name;
    /**
     * 预约人手机号
     */
    private String phone;
    /**
     * 状态
     */
    private String status;
    /**
     * 预约类型
     */
    private String type;
    /**
     * 预约时间Begin
     */
    private String visitDateBegin;
    /**
     * 预约时间End
     */
    private String visitDateEnd;
    /**
     * 分公司名称
     */
    private List<Long> companyIds;
    private Long companyId;
    /**
     * 预约提交时间Begin
     */
    private String createdDateBegin;
    /**
     * 预约提交时间End
     */
    private String createdDateEnd;
}
