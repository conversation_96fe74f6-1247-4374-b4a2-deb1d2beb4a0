package com.fotile.orgcenter.operatorOrgLog.mq;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.orgcenter.operatorOrgLog.pojo.dto.InsertOperatorOrgLogInDto;
import com.fotile.orgcenter.operatorOrgLog.service.OperatorOrgLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.orgcenter.salesman.service
 * @date 2021/4/8 9:59
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class ConsumeOrgOperateLogService {
    @Autowired
    OperatorOrgLogService logService;

    @StreamListener(OrgOperateLogChannel.ORG_OPERATE_LOG_INPUT)
    public void consumeOrgOperateLog(Message<InsertOperatorOrgLogInDto> message) {
        if (message != null) {
            try {
                InsertOperatorOrgLogInDto logInfo = message.getPayload();
                //本次日志消费，切记加入以下代码，防止消费死循环
                logInfo.setIsAsyncExecute(0);
                logService.insertOperatorOrgLog(logInfo);
            } catch (Exception ex) {
                //TODO:记录错误日志
                log.error("ConsumeOrgOperateLogService.consumeOrgOperateLog exception -> Parameters >>> " + JSON.toJSONString(message), ex);
            }
        }
    }
}
