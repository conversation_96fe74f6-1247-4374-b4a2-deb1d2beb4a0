package com.fotile.orgcenter.company.controller;

import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.orgcenter.company.pojo.dto.AddCompanyLogDto;
import com.fotile.orgcenter.company.pojo.dto.CompanyLogQueryDto;
import com.fotile.orgcenter.company.service.CompanyLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 公司操作日志
 */
@Slf4j
@RestController
@RequestMapping("/api/companyLog")
public class CompanyLogController extends BaseController {

    @Autowired
    private CompanyLogService companyLogService;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    /**
     * 获取公司日志
     */
    @GetMapping("/getCompanyLogListById")
    public Result<?> getCompanyLogListById(@Valid CompanyLogQueryDto queryDto){
        return success(companyLogService.getCompanyLogListById(queryDto));
    }

    /**
     * 新增公司日志
     */
    @PostMapping("/insertCompanyLog")
    public Result<?> insertCompanyLog(@Valid @RequestBody AddCompanyLogDto addDto){
        return success(companyLogService.saveLog("新增描述",addDto.getDescription(),userAuthorConfig.queryUserAuthor(), StringUtils.join(addDto.getAttachmentList(),","),addDto.getCompanyId()));
    }

}
