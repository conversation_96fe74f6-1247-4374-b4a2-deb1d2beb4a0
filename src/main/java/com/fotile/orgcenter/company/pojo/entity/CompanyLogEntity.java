package com.fotile.orgcenter.company.pojo.entity;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;
@Data
public class CompanyLogEntity {
    private Long id;
    private String createdBy;
    private Date createdDate;
    private String modifiedBy;
    private Date modifiedDate;
    private Long companyId;
    private String operatorId;
    @FieldEncrypt
    private String operatorName;
    private String operateType;
    private Date operateDate;
    private String description;
    private String attachmentUrl;
}