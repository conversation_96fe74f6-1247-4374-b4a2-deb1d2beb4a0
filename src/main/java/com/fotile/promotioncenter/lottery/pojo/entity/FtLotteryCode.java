package com.fotile.promotioncenter.lottery.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖码表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ft_lottery_code")
public class FtLotteryCode extends AuditingEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 分公司id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 分公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 抽奖码
     */
    @TableField("lottery_code")
    private String lotteryCode;

    /**
     * 是否已使用（1是，2否）
     */
    @TableField("status")
    private String status;

    /**
     * 所属顾问id
     */
    @TableField("adviser_id")
    private Long adviserId;

    /**
     * 所属顾问名称
     */
    @TableField("adviser_name")
    private String adviserName;

    /**
     * 兑换用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 兑换用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 顾问申请表id
     */
    @TableField("adviser_apply_id")
    private Long adviserApplyId;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 使用时间
     */
    @TableField("use_time")
    private Date useTime;



}
