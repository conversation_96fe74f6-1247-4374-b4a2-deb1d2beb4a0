package com.fotile.promotioncenter.order.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.promotioncenter.order.pojo.dto.FtOrderSelectDto;
import com.fotile.promotioncenter.order.pojo.dto.FtOrderStatisticsDto;
import com.fotile.promotioncenter.order.pojo.entity.FtOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
* Created by Mybatis Generator on 2020/05/07
 * <AUTHOR>
 */
@Repository
public interface FtOrderDao extends BaseMapper<FtOrder> {
    PageInfo<FtOrder> selAdviserOrderByType(@Param("page")PageInfo<FtOrder> page, @Param("dto")FtOrderSelectDto ftOrderSelectDTO);

    PageInfo<FtOrder> selUserOrderByType(@Param("page")PageInfo<FtOrder> page, @Param("dto")FtOrderSelectDto ftOrderSelectDTO);

    int updateByConfirmCode(@Param("confirmCode")String confirmCode);

    List<FtOrder> selOrderByConfirmCode(@Param("confirmCode")String confirmCode);

    PageInfo<FtOrder> selMyPartnerOrder(@Param("page")PageInfo<FtOrder> page, @Param("dto")FtOrderSelectDto ftOrderSelectDTO);

    BigDecimal selPartnerCommission(@Param("order")FtOrder order);

    FtOrderStatisticsDto selOrderStatistics();

    PageInfo<FtOrderStatisticsDto> selCompanyStatistics(@Param("page")PageInfo<FtOrderStatisticsDto> page, @Param("dto")FtOrderSelectDto ftOrderSelectDTO);

    PageInfo<FtOrderStatisticsDto> selCompanyInfo(PageInfo<FtOrderStatisticsDto> page);
}