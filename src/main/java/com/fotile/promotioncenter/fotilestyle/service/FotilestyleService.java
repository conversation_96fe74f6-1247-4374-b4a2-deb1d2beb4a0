package com.fotile.promotioncenter.fotilestyle.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fotile.framework.data.auth.utils.MD5Util;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.cardBag.dao.ExchangeCardBagRecordDao;
import com.fotile.promotioncenter.cardBag.pojo.entity.ExchangeCardBagRecord;
import com.fotile.promotioncenter.fotilestyle.dao.FotilestyleDao;
import com.fotile.promotioncenter.fotilestyle.pojo.CardBagDto;
import com.fotile.promotioncenter.fotilestyle.pojo.CardBagMappingDto;
import com.fotile.promotioncenter.fotilestyle.pojo.CouponDto;
import com.fotile.promotioncenter.util.RedisLock;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 幸福家Service
 */
@Service
public class FotilestyleService {

    private static final int TIMEOUT = 3 * 1000;

    @Autowired
    private FotilestyleDao fotilestyleDao;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ExchangeCardBagRecordDao cardBagRecordDao;

    @GlobalTransactional(rollbackFor = Exception.class)
    public Result receives(JSONObject map, String phone ) {
        List<String> ids = null;
        if(map.getJSONArray("ids") != null){
            ids =JSONArray.parseArray(map.getJSONArray("ids").toString(), String.class);
        }
        List<String> cardIds = null;
        if(map.getJSONArray("cardIds") != null){
            cardIds = JSONArray.parseArray(map.getJSONArray("cardIds").toString(), String.class);
        }
        String register = map.get("register").toString();
        if (null != ids) {
            for (String id : ids) {
                //查询卡券信息
                CouponDto couponDto = fotilestyleDao.getBaseInfoById(Long.valueOf(id));
                if (null != couponDto) {
                    //判断优惠券是否可领取
                    String target = couponDto.getTarget();
                    // 获取当前日期
                    LocalDate currentDate = LocalDate.now();
                    // 创建一个日期格式器
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    // 格式化日期
                    String fDate = currentDate.format(formatter);
                    //新用户领取
                    if(target.equals("1") && !register.equals(fDate)){
                        return Result.buildFailure("领取失败，仅新用户可领");
                    } else if (target.equals("2") && register.equals(fDate)) {
                        //只能老用户领取
                        return Result.buildFailure("领取失败，仅老用户可领");
                    }
                }
                if (checkCardReceiveLimit(Long.valueOf(id), phone)) {
                    CouponDto dto = new CouponDto();
                    if(null != couponDto && couponDto.getValidType() == 2){
                        dto.setCardVaildValue(couponDto.getCardVaildValue());
                    }
                    String saltMD5 = MD5Util.getSaltMD5(MybatisMateConfig.decrypt(phone));
                    dto.setPhoneEnc(saltMD5);
                    dto.setPhone(phone);
                    dto.setUserId("anonymousUser");
                    dto.setId(Long.valueOf(id));
                    fotilestyleDao.updateCouponGet(dto);
                } else {
                    return Result.buildFailure("该卡券无法领取");
                }
            }
        }
        if (null != cardIds) {
            for (String cardId : cardIds) {
                if (checkCodeReceiveLimit(cardId)) {
                    CouponDto dto = new CouponDto();
                    CouponDto couponDto = fotilestyleDao.getCardDetailByCardId(cardId);
                    if(null != couponDto && couponDto.getValidType() == 2){
                        dto.setCardVaildValue(couponDto.getCardVaildValue());
                    }
                    String saltMD5 = MD5Util.getSaltMD5(MybatisMateConfig.decrypt(phone));
                    dto.setPhoneEnc(saltMD5);
                    dto.setPhone(phone);
                    dto.setUserId("anonymousUser");
                    dto.setCardId(cardId);
                    fotilestyleDao.updateCouponBind(dto);
                } else {
                    return Result.buildFailure("该券码无法领取");
                }
            }
        }
        return Result.buildSuccess("领取成功");
    }

    private boolean checkCardReceiveLimit(Long id, String phone) {
        List<Integer> num = fotilestyleDao.checkCardReceiveLimit(id, phone);
        if (null != num && num.size() > 1) {
            if (null == num.get(0)) {
                return true;
            } else if (num.get(0) - num.get(1) > 0) {
                return true;
            }
        }
        return false;
    }

    private boolean checkCodeReceiveLimit(String cardId) {
        int num = fotilestyleDao.checkCodeReceiveLimit(cardId);
        if (num == 1) {
            return true;
        }
        return false;
    }

    public Integer getCouponsCountByPhone(String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsCountByPhone(phone);
    }

    public List<CouponDto> getCouponsByActivity(Integer type, Long id, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsByActivity(type, id, phone);
    }

    public Integer getHasCouponByActivity(Long id) {
        return fotilestyleDao.getHasCouponByActivity(id);
    }

    public List<CouponDto> getCouponsByServiceId(Long id, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsByServiceId(id, phone);
    }

    public List<CouponDto> getCouponsByContentId(Long id, Integer type, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsByContentId(id, type, phone);
    }

    public List<CouponDto> getCouponsByPhone(String phone,Integer type) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsByPhone(phone, type);
    }

    public List<CouponDto> getCouponsByGoodsCode(String goodsCode, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCouponsByGoodsCode(goodsCode, phone);
    }

    public List<CouponDto> getCouponsByListGoodsCode(List<String> listGoodsCode,String phone, Integer limit) {
        List<CouponDto> couponsByListGoodsCode = fotilestyleDao.getCouponsByListGoodsCode(listGoodsCode, limit);
        if(null !=couponsByListGoodsCode && !couponsByListGoodsCode.isEmpty()){
            if(StringUtils.isNotEmpty(phone)){
                phone = MybatisMateConfig.encrypt(phone);
                for (CouponDto c:couponsByListGoodsCode){
                    c.setUserStatus(getCouponsUserStatus(c.getId(),phone));
                }
            }
            return couponsByListGoodsCode;
        }else {
            return new ArrayList<>();
        }


    }


    public CouponDto getBaseInfoById(Long id) {
        return fotilestyleDao.getBaseInfoById(id);
    }

    public CouponDto getCardDetail(Long id, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getCardDetail(id, phone);
    }

    public Boolean getReceiveCard(Long id, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        Integer cnt = fotilestyleDao.getCouponCntByUser(id, phone);
        if (0 < cnt) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    public Integer getCouponsUserStatus(Long id, String phone) {
        phone = MybatisMateConfig.encrypt(phone);
        Integer userStatus = fotilestyleDao.getCouponsUserStatusCnt(id, phone);
        if (0 < userStatus) {
            return fotilestyleDao.getCouponsUserStatus(id, phone);
        }else {
            return 3;
        }
    }

    public CouponDto getCardDetailByCardId(String code) {
        return fotilestyleDao.getCardDetailByCardId(code);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCouponGet(CouponDto dto) {
        CouponDto couponDto = fotilestyleDao.getBaseInfoById(dto.getId());
        if(null != couponDto && couponDto.getValidType() == 2){
            dto.setCardVaildValue(couponDto.getCardVaildValue());
        }
        if (!getReceiveCard(dto.getId(), dto.getPhone())) {
            String time = String.valueOf(System.currentTimeMillis() + TIMEOUT);
            String lockId = "xinfu:coupon:" + dto.getId();

            if (redisLock.lock(lockId, time)) {
                String saltMD5 = MD5Util.getSaltMD5(dto.getPhone());
                String encrypt = MybatisMateConfig.encrypt(dto.getPhone());
                dto.setPhoneEnc(saltMD5);
                dto.setPhone(encrypt);
                int i = fotilestyleDao.updateCouponGet(dto);
                redisLock.unlock(lockId, time);

//                if (1 == i) {
//                    try {
//                        CouponDto coupon = getBaseInfoById(dto.getId());
//                        if(null != coupon) {
//                            List<String> phoneList = new ArrayList<String>();
//                            phoneList.add(dto.getPhone());
//                            cardService.sendNotice(phoneList, coupon.getPrice().doubleValue());
//                        }
//                    } catch (Exception e) {
//                    }
//                }
            }
        } else {
            throw new BusinessException("卡券已领取");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Result updateRepetCouponGet(CouponDto dto) {
        CouponDto couponDto = fotilestyleDao.getBaseInfoById(dto.getId());
        if(null != couponDto && couponDto.getValidType() == 2){
            dto.setCardVaildValue(couponDto.getCardVaildValue());
        }
        if (checkCardReceiveLimit(dto.getId(), MybatisMateConfig.encrypt(dto.getPhone()))) {
            String time = String.valueOf(System.currentTimeMillis() + TIMEOUT);
            String lockId = "xinfu:coupon:" + dto.getId();
            try {
                if (redisLock.lock(lockId, time)) {
                    String saltMD5 = MD5Util.getSaltMD5(dto.getPhone());
                    String encrypt = MybatisMateConfig.encrypt(dto.getPhone());
                    dto.setPhoneEnc(saltMD5);
                    dto.setPhone(encrypt);
                    int i = fotilestyleDao.updateCouponGet(dto);
                    return Result.buildSuccess("领取成功");
                }
            }catch (Exception e){
                return Result.buildFailure("优惠券领取失败请重试");
            }finally {
                redisLock.unlock(lockId, time);
            }
            return Result.buildFailure("优惠券领取失败请重试");
        }else {
            return Result.buildFailure("该券领取已达上限或券已领完");
        }
    }

    @GlobalTransactional
    public void updateCouponUse(CouponDto dto) {
        dto.setPhone(MybatisMateConfig.encrypt(dto.getPhone()));
        fotilestyleDao.updateCouponUse(dto);
        fotilestyleDao.updateCouponUsedNum(dto);
    }
    @GlobalTransactional
    public void updateCouponUseBy(CouponDto dto) {
        fotilestyleDao.updateCouponUse(dto);
        fotilestyleDao.updateCouponUsedNum(dto);
    }

    @GlobalTransactional
    public void updateCouponCancel(CouponDto dto) {
        dto.setPhone(MybatisMateConfig.encrypt(dto.getPhone()));
        fotilestyleDao.updateCouponCancel(dto);
    }

    public CouponDto getUsableCoupon(Long id) {
        return fotilestyleDao.getUsableCoupon(id);
    }

    @GlobalTransactional
    public void lotteryCard(CouponDto dto) {
        String saltMD5 = MD5Util.getSaltMD5(MybatisMateConfig.decrypt(dto.getPhone()));
        dto.setPhoneEnc(saltMD5);
        dto.setPhone(dto.getPhone());
        fotilestyleDao.updateCouponGet(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCard(String userId, String phone, Long[] ids) {
        fotilestyleDao.deleteCards(userId, phone, ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public void sendCardByVip(Long cardId,Integer cardCount,String phone,String userId) {
        CouponDto dto = new CouponDto();
        CouponDto couponDto = fotilestyleDao.getBaseInfoById(cardId);
        if(null != couponDto && couponDto.getValidType() == 2){
            dto.setCardVaildValue(couponDto.getCardVaildValue());
        }
        String time = String.valueOf(System.currentTimeMillis() + TIMEOUT);
        String lockId = "xinfu:coupon:" + cardId;
        if (redisLock.lock(lockId, time)) {
            //判断剩余卡券是否大于发放的库存
            if (checkCardIdReceiveLimit(cardId,cardCount)) {
                String saltMD5 = MD5Util.getSaltMD5(phone);
                String encrypt = MybatisMateConfig.encrypt(phone);
                dto.setId(cardId);
                dto.setPhoneEnc(saltMD5);
                dto.setPhone(encrypt);
                dto.setUserId(userId);
                dto.setCardCount(cardCount);
                fotilestyleDao.sendCardByVip(dto);
                redisLock.unlock(lockId, time);
            } else {
                throw new BusinessException("该券无法领取");
            }
        } else {
            throw new BusinessException("领取失败");
        }
   }

    public boolean checkCardIdReceiveLimit(Long cardId, Integer cardCount) {
        int num = fotilestyleDao.checkCardIdReceiveLimit(cardId);
        return num >= cardCount;
    }

    public List<List<CouponDto>> getChannelCoupon() {
        List<List<CouponDto>> list = new ArrayList<>();
        List<CouponDto> lessList = new ArrayList<>();
        //活动时间小于30天的卡券信息
        List<CouponDto> unarrivedLessTimeCoupon = fotilestyleDao.getLessUnarrivedTimeCoupon();
        List<CouponDto> validLessTimeCoupon = fotilestyleDao.getLessValidTimeCoupon();
        if (CollectionUtils.isNotEmpty(unarrivedLessTimeCoupon)) {
            for (CouponDto couponDto : unarrivedLessTimeCoupon){
                couponDto.setStock(Long.valueOf(fotilestyleDao.checkCardIdReceiveLimit(couponDto.getId())));
            }
            lessList.addAll(unarrivedLessTimeCoupon);
        }
        if (CollectionUtils.isNotEmpty(validLessTimeCoupon)) {
            for (CouponDto couponDto : validLessTimeCoupon){
                couponDto.setStock(Long.valueOf(fotilestyleDao.checkCardIdReceiveLimit(couponDto.getId())));
            }
            lessList.addAll(validLessTimeCoupon);
        }
        list.add(lessList);
        List<CouponDto> greaterList = new ArrayList<>();
        //活动时间大于30天的卡券信息
        List<CouponDto> unarrivedGreaterTimeCoupon = fotilestyleDao.getGreaterUnarrivedTimeCoupon();
        List<CouponDto> validGreaterTimeCoupon = fotilestyleDao.getGreaterValidTimeCoupon();
        if (CollectionUtils.isNotEmpty(unarrivedGreaterTimeCoupon)) {
            for (CouponDto couponDto : unarrivedGreaterTimeCoupon){
                couponDto.setStock(Long.valueOf(fotilestyleDao.checkCardIdReceiveLimit(couponDto.getId())));
            }
            greaterList.addAll(unarrivedGreaterTimeCoupon);
        }
        if (CollectionUtils.isNotEmpty(validGreaterTimeCoupon)) {
            for (CouponDto couponDto : validGreaterTimeCoupon){
                couponDto.setStock(Long.valueOf(fotilestyleDao.checkCardIdReceiveLimit(couponDto.getId())));
            }
            greaterList.addAll(validGreaterTimeCoupon);
        }
        list.add(greaterList);

        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void receiveCardBag(Long id, String userId, Long customerId, String nickName, String customerPhone, String register) {
        //获取卡券包下卡券信息
        List<CardBagMappingDto> cardBagMapping = fotilestyleDao.getCardBagMapping(id);
        //根据卡券id和数量领取卡券
        for (CardBagMappingDto cardBagMappingDto : cardBagMapping) {
            Long cardId = cardBagMappingDto.getCardId();
            Integer num = cardBagMappingDto.getNum();
            //查询卡券信息
            CouponDto couponDto = fotilestyleDao.getBaseInfoById(Long.valueOf(cardId));
            if (null != couponDto) {
                //判断优惠券是否可领取
                String target = couponDto.getTarget();
                // 获取当前日期
                LocalDate currentDate = LocalDate.now();
                // 创建一个日期格式器
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                // 格式化日期
                String fDate = currentDate.format(formatter);
                //新用户领取
                if(target.equals("1") && !register.equals(fDate)){
                    throw new BusinessException("领取失败，仅新用户可领");
                } else if (target.equals("2") && register.equals(fDate)) {
                    //只能老用户领取
                    throw new BusinessException("领取失败，仅老用户可领");
                }
            }
            CouponDto dto = new CouponDto();
            if(null != couponDto && couponDto.getValidType() == 2){
                dto.setCardVaildValue(couponDto.getCardVaildValue());
            }
            String time = String.valueOf(System.currentTimeMillis() + TIMEOUT);
            String lockId = "xinfu:coupon:" + cardId;
            if (redisLock.lock(lockId, time)) {
                //判断剩余卡券是否大于发放的库存
                if (checkCardBagReceiveLimit(cardId,num,customerPhone)) {
                    String saltMD5 = MD5Util.getSaltMD5(customerPhone);
                    dto.setId(cardId);
                    dto.setPhoneEnc(saltMD5);
                    dto.setPhone(customerPhone);
                    dto.setUserId(userId);
                    dto.setCardCount(num);
                    fotilestyleDao.sendCardByVip(dto);
                    redisLock.unlock(lockId, time);
                } else {
                    throw new BusinessException("领取失败");
                }
            } else {
                throw new BusinessException("领取失败");
            }
            //领取成功，更新卡券包领取记录
            //获取领取到的卡券详情里的cardId
            List<String> cardIds = fotilestyleDao.getCardId(cardId, customerPhone);
            List<ExchangeCardBagRecord> entities = new ArrayList<>();
            for (String cardIdNo : cardIds) {
                ExchangeCardBagRecord cardBagRecord = new ExchangeCardBagRecord();
                cardBagRecord.setIsDeleted(0L);
                cardBagRecord.setCreatedBy(userId);
                cardBagRecord.setModifiedBy(userId);
                cardBagRecord.setCreatedDate(new Date());
                cardBagRecord.setModifiedDate(new Date());
                cardBagRecord.setCardBagId(id);
                cardBagRecord.setTitle(cardBagMappingDto.getTitle());
                cardBagRecord.setCustomerId(customerId);
                cardBagRecord.setCustomerPhone(MybatisMateConfig.decrypt(customerPhone));
                cardBagRecord.setNickname(MybatisMateConfig.decrypt(nickName));
                cardBagRecord.setCardId(String.valueOf(cardId));
                cardBagRecord.setCardNo(cardIdNo);
                entities.add(cardBagRecord);
            }
            if(CollectionUtils.isNotEmpty(entities)){
                cardBagRecordDao.insertBatch(entities);
            }
        }
    }


    private boolean checkCardBagReceiveLimit(Long cardId, Integer num,String customerPhone) {
        int unUseCount = fotilestyleDao.checkCardIdReceiveLimit(cardId);
        if (unUseCount == 0 || num > unUseCount) {
            return false;
        }
        List<Integer> count = fotilestyleDao.checkCardReceiveLimit(cardId, customerPhone);
        if (null != count && count.size() > 1) {
            if (null == count.get(0)) {
                return true;
            } else if (num > count.get(0)) {
                return false;
            } else if ( count.get(0) - count.get(1) > 0 && (count.get(0) - count.get(1) >= num)) {
                return true;
            }
        }
        return false;
    }

    public CardBagDto getCardBagDetail(Long id) {
        return fotilestyleDao.getCardBagDetail(id);
    }

    public List<CouponDto> getNewCouponsByPhone(String phone,Integer type) {
        phone = MybatisMateConfig.encrypt(phone);
        return fotilestyleDao.getNewCouponsByPhone(phone, type);
    }
}
