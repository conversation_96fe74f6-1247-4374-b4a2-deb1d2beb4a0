package com.fotile.promotioncenter.operationlog.pojo;


import com.fotile.promotioncenter.operationlog.pojo.dto.FtOperationLogInsertDto;
import com.fotile.promotioncenter.operationlog.pojo.dto.FtOperationLogSelectDto;
import com.fotile.promotioncenter.operationlog.pojo.entity.FtOperationLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface FtOperationLogMapper {

    FtOperationLogMapper INSTANCE = Mappers.getMapper(FtOperationLogMapper.class);

    FtOperationLog ftOperationLogInsertDtoToFtOperationLog(FtOperationLogInsertDto ftOperationLogInsertDTO);

    FtOperationLogSelectDto ftOperationLogToFtOperationLogUpdateAndSelectDto(FtOperationLog ftOperationLog);
}
