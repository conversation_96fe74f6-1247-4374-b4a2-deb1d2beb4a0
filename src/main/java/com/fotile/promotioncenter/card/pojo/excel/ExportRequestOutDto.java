package com.fotile.promotioncenter.card.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class ExportRequestOutDto {
    @ColumnWidth(20)
    @ExcelProperty(value = "手机号", index = 0)
    @ApiModelProperty("手机号")
    private String phones;

    @ColumnWidth(20)
    @ExcelProperty(value = "绑券状态",index = 1)
    @ApiModelProperty("绑券状态")
    private String status;
}
