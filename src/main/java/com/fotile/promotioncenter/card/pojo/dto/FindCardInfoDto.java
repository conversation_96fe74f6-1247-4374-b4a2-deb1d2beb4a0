package com.fotile.promotioncenter.card.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.promotioncenter.card.pojo.dto
 * @date 2020/6/16 15:04
 */
@Data
public class FindCardInfoDto implements Serializable {
    /**
     * 券码
     */
    @NotBlank(message = "券码不能为空！")
    private String cardNo;

    /**
     * 分公司OrgID
     */
    private Long companyId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 门店OrgId
     */
    private Long storeOrgId;

    /**
     * 优惠券Id
     */
    private Long cardId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 渠道id
     */
    private String channelCode;
    /**
     * 1：智慧导购 s0001
     */
    private Integer source;
}
