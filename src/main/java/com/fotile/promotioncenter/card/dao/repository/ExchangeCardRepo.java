package com.fotile.promotioncenter.card.dao.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.promotioncenter.card.dao.CardDao;
import com.fotile.promotioncenter.card.pojo.ExchangeCard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:29
 */
@Slf4j
@Validated
@Repository
public class ExchangeCardRepo extends ServiceImpl<CardDao, ExchangeCard> {
    @Resource
    private CardDao cardDao;
}
