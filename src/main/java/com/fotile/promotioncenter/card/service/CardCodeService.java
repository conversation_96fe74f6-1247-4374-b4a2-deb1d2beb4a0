package com.fotile.promotioncenter.card.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceLookup;
import com.fotile.framework.util.DateUtil;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.card.constant.MallConstant;
import com.fotile.promotioncenter.card.dao.CardCodeDao;
import com.fotile.promotioncenter.card.dao.CardDao;
import com.fotile.promotioncenter.card.dao.TCardReceiveRecordMapper;
import com.fotile.promotioncenter.card.pojo.*;
import com.fotile.promotioncenter.card.pojo.dto.*;
import com.fotile.promotioncenter.card.service.client.CardClientService;
import com.fotile.promotioncenter.card.service.client.ProductClientService;
import com.fotile.promotioncenter.card.service.client.UserClientService;
import com.fotile.promotioncenter.client.OrgClient;
import com.fotile.promotioncenter.client.SystemClient;
import com.fotile.promotioncenter.client.pojo.dto.*;
import com.fotile.promotioncenter.mq.client.CustomerCenterFeign;
import com.fotile.promotioncenter.mq.pojo.FindCommunityMemberByPhonesInDto;
import com.fotile.promotioncenter.mq.pojo.FindQywxCutomerUserRelationListOutDto;
import com.fotile.promotioncenter.mq.pojo.GetCommunityMemberListOutDto;
import com.fotile.promotioncenter.mq.pojo.GetQywxCutomerByPhonesInDto;
import com.fotile.promotioncenter.util.DateStandardUtil;
import com.fotile.promotioncenter.util.UniquenessIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "fifth")
public class CardCodeService {

    @Autowired
    private CardCodeDao cardCodeDao;
    @Autowired
    private CardDao cardDao;
    @Autowired
    private CardService cardService;
    @Autowired
    private CardClientService cardClientService;
    @Autowired
    private UserClientService userClientService;
    @Autowired
    private TCardReceiveRecordMapper tCardReceiveRecordMapper;
    @Autowired
    private CustomerCenterFeign customerCenterFeign;
    @Autowired
    private ProductClientService productClientService;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private UserAuthorConfig userAuthorConfig;

    public PageInfo<ExchangeCardDetail> findCardCodePage(ExchangeCardDetail exchangeCardDetail,
                                                         List<String> channelCodes,
                                                         List<Long> companyOrgIds) {
        String encrypt = MybatisMateConfig.encrypt(exchangeCardDetail.getPhone());
        exchangeCardDetail.setPhone(encrypt);
        PageInfo<ExchangeCardDetail> page = new PageInfo<>(exchangeCardDetail.getPage(), exchangeCardDetail.getSize());
        exchangeCardDetail.setOffset(page.getOffset());

        //处理核销门店所属大区,多选下拉大区code
        if (StringUtils.isNotBlank(exchangeCardDetail.getAreaStore())) {
            List<String> areaCodes = Arrays.asList(exchangeCardDetail.getAreaStore().split(","));
            if (areaCodes != null && areaCodes.size() > 0) {
                FindStoreByAreaCodesInDto areaCodesInDto = new FindStoreByAreaCodesInDto();
                areaCodesInDto.setAreaCodes(areaCodes);
                List<StoreEntity> storeEntityList = orgClient.findStoreByAreaCodes(areaCodesInDto).getData();
                if (storeEntityList != null && storeEntityList.size() > 0){
                    List<Long> storeIds = storeEntityList.stream().map(s -> s.getId()).collect(Collectors.toList());
                    exchangeCardDetail.setAreaStoreIds(storeIds);
                }else {
                    return page;
                }
            }
        }
        if(StringUtils.isNotBlank(exchangeCardDetail.getActivityIds())) {
            List<String> activityIds = Arrays.asList(exchangeCardDetail.getActivityIds().split(","));
            List<Long> activityIdList = activityIds.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            exchangeCardDetail.setActivityIdList(activityIdList);
        }
        if(StringUtils.isNotBlank(exchangeCardDetail.getDrainageActivityIds())) {
            List<String> drainageActivityIds = Arrays.asList(exchangeCardDetail.getDrainageActivityIds().split(","));
            List<Long> drainageActivityIdList = drainageActivityIds.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            exchangeCardDetail.setDrainageActivityIdList(drainageActivityIdList);
        }
        List<GetCardIdByPageOutDto> cardIdByPage = cardCodeDao.getCardIdByPage(exchangeCardDetail, channelCodes, companyOrgIds);
        if (CollectionUtils.isEmpty(cardIdByPage)) {
            return page;
        }
        exchangeCardDetail.setCardIdList(cardIdByPage.stream().map(GetCardIdByPageOutDto::getId).collect(Collectors.toList()));

        List<ExchangeCardDetail> exchangeCardDetailList = null;
        DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("adb");
        Long count = cardCodeDao.findCardCodePageCount(exchangeCardDetail, channelCodes, companyOrgIds);
        if (count > 0) {
            exchangeCardDetailList = cardCodeDao.findCardCodePage(exchangeCardDetail, channelCodes, companyOrgIds);
            DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("fifth");
            List<String> idList = new ArrayList<>();
            List<Map<String, Object>> storeList;
            Map<String, Object> map = new HashMap<>();

            List<String> userIds = exchangeCardDetailList.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getCreateCardUser()))
                    .map(ExchangeCardDetail::getCreateCardUser)
                    .distinct()
                    .collect(Collectors.toList());

            List<Long> storeIdList = exchangeCardDetailList.stream()
                    .filter(x -> Objects.nonNull(x.getBindingStore()))
                    .map(ExchangeCardDetail::getBindingStore)
                    .distinct()
                    .collect(Collectors.toList());

            List<UserEntityExtend> userEntities = new ArrayList<>();
            if (userIds.size() > 0) {
                Result<List<UserEntityExtend>> data = userClientService.findUserEntityExtendByUserIds2(userIds);
                if (data != null && data.getData() != null && data.getData().size() > 0){
                    userEntities = data.getData();
/*                    List<Long> salesmanIds = data.getData().stream().map(UserEntityExtend::getSalesmanId).distinct().collect(Collectors.toList());
                    FindSalesmanByIds2InDto dto = new FindSalesmanByIds2InDto();
                    dto.setIdList(salesmanIds);
                    Result<List<SalesmanEntity>> salesmanResult = orgClient.findSalesmanByIds(dto);
                    if (salesmanResult.getSuccess()&& !CollectionUtils.isEmpty(salesmanResult.getData())){
                        salesmanEntityList = salesmanResult.getData();
                    }*/
                }
            }
            List<FindStoreByOrgIdOutDto> storeOrgList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(storeIdList)){
                FindStoreByOrgIdsInDto dto = new FindStoreByOrgIdsInDto();
                dto.setOrgIds(storeIdList);
                Result<List<FindStoreByOrgIdOutDto>> storeResult = orgClient.findByOrgIds(dto);
                if (storeResult.getSuccess()&&!CollectionUtils.isEmpty(storeResult.getData())){
                    storeOrgList = storeResult.getData();
                }
            }

            UserEntityExtend user;
            List<String> phones = new ArrayList<>();
            for (ExchangeCardDetail cardDetail : exchangeCardDetailList) {
                cardDetail.setPhone(MybatisMateConfig.decrypt(cardDetail.getPhone()));
                cardDetail.setSenderChargeUserName(MybatisMateConfig.decrypt(cardDetail.getSenderChargeUserName()));
                cardDetail.setSenderChargePhone(MybatisMateConfig.decrypt(cardDetail.getSenderChargePhone()));
                cardDetail.setVerificateChargeUserName(MybatisMateConfig.decrypt(cardDetail.getVerificateChargeUserName()));
                cardDetail.setVerificateChargePhone(MybatisMateConfig.decrypt(cardDetail.getVerificateChargePhone()));
                user = userEntities.stream().filter(u -> u.getUserEntityId().equals(cardDetail.getCreateCardUser())).findFirst().orElse(null);
                if (user != null){
                    cardDetail.setCreateCardUser(user.getFirstName());
                }
                if (!CollectionUtils.isEmpty(storeOrgList)&&cardDetail.getBindingStore()!=null){
                    FindStoreByOrgIdOutDto findStoreByOrgIdOutDto = storeOrgList.stream()
                            .filter(s -> s.getOrgId().equals(cardDetail.getBindingStore()))
                            .findFirst().orElse(null);
                    if (findStoreByOrgIdOutDto!=null){
                        cardDetail.setCardStore(findStoreByOrgIdOutDto.getName());
                    }
                }

                String store = cardDetail.getVerificateStore();
                if (null != store && !"".equals(store)) {
                    idList.add(store);
                }
                if (StringUtils.isNotBlank(cardDetail.getPhone())){
                    phones.add(cardDetail.getPhone());
                }
            }
            HashSet<String> strings = new HashSet<>(phones);
            phones = new ArrayList<>(strings);

            List<FindQywxCutomerUserRelationListOutDto> qywxCutomerList = new ArrayList<>();
            List<GetCommunityMemberListOutDto> memberList = new ArrayList<>();
            if (phones != null && phones.size() > 0) {
                //查询用户在企微加的好友的所属门店
                GetQywxCutomerByPhonesInDto phonesInDto = new GetQywxCutomerByPhonesInDto();
                phonesInDto.setPhones(phones);
                phonesInDto.setOrgIds(companyOrgIds);
                qywxCutomerList = customerCenterFeign.getQywxCutomerUserRelationByPhones(phonesInDto).getData();
                //查询用户关联的社区店
                FindCommunityMemberByPhonesInDto byPhonesInDto = new FindCommunityMemberByPhonesInDto();
                byPhonesInDto.setPhones(phones);
                byPhonesInDto.setOrgIds(companyOrgIds);
                memberList = customerCenterFeign.findCommunityMemberListByPhones(byPhonesInDto).getData();
            }

            List<FindStoreByIdOutDto> stores = new ArrayList<>();
            if (idList.size() > 0) {
                HashSet<String> idset = new HashSet<>(idList);
                idList = new ArrayList<>(idset);
//                storeList = (List<Map<String, Object>>) cardClientService.findStoreInfoByIdList(idList).getData();
                List<Long> ids = idList.stream().map(s -> Long.valueOf(s)).collect(Collectors.toList());
                stores = cardClientService.findByIds(new FindStoreByIdsInDto(ids)).getData();
            }

            //查询岗位
            List<QueryByParamsOutDto> dicList = systemClient.queryByType("gw", 1, 100).getData().getRecords();
            for (ExchangeCardDetail cardDetail : exchangeCardDetailList) {


//                    for (FindStoreByIdOutDto store : stores) {
//                        String storeId = cardDetail.getVerificateStore();
//                        if (null != storeId && !"".equals(storeId)) {
//                            if (storeId.equals(store.getId().toString())) {
//                                cardDetail.setVerificateStoreName(store.getCode()+"/"+store.getName());
//                            }
//                        }
//                    }

                if (null != cardDetail && StringUtils.isNotBlank(cardDetail.getVerificateStore())) {
                    Optional<FindStoreByIdOutDto> first = stores.stream().filter(s -> cardDetail.getVerificateStore().equals(s.getId().toString())).findFirst();
                    if (first.isPresent()) {
                        FindStoreByIdOutDto store = first.get();
                        cardDetail.setVerificateStoreName(store.getCode() + "/" + store.getName());
                        cardDetail.setAreaStoreName(store.getAreaName());
                    }
                }

                cardDetail.setChargeStoreNames("");
                cardDetail.setCommunityStoreNames("");
                if (StringUtils.isNotBlank(cardDetail.getPhone())) {
                    //会员门店判断
                    if (qywxCutomerList !=  null && qywxCutomerList.size() > 0){
                        List<Object> phoneList = new ArrayList<>();
                        List<FindQywxCutomerUserRelationListOutDto> collect = qywxCutomerList.stream().filter(q ->
                                q.getMobilesList() != null && q.getMobilesList().size() > 0
                                        && q.getMobilesList().contains(cardDetail.getPhone())
                                        && StringUtils.isNotBlank(q.getChargeStoreNames()) ).collect(Collectors.toList());
                        if (collect != null && collect.size() > 0){
                            List<String> collect1 = collect.stream().map(c -> c.getChargeStoreNames()).collect(Collectors.toList());
                            collect1 = new ArrayList<>(new HashSet<>(collect1));
                            cardDetail.setChargeStoreNames(StringUtils.join(collect1, ','));
                        }
                    }


                    //会员所属门店
                    if (memberList !=  null && memberList.size() > 0) {
                        List<GetCommunityMemberListOutDto> collect = memberList.stream().filter(m -> cardDetail.getPhone().equals(m.getPhone()) && StringUtils.isNotBlank(m.getCommunityStoreName())).collect(Collectors.toList());
                        if (collect != null && collect.size() > 0) {
                            List<String> collect1 = collect.stream().map(c -> c.getCommunityStoreName()).collect(Collectors.toList());
                            if (collect1 != null && collect1.size() > 0) {
                                collect1 = new ArrayList<>(new HashSet<>(collect1));
                                cardDetail.setCommunityStoreNames(StringUtils.join(collect1, ','));
                            }
                        }
                    }
                }

                //脱敏
                if (StringUtils.isNotBlank(cardDetail.getPhone())) {
                    cardDetail.setPhone(setCostomerPhone(cardDetail.getPhone()));
                }

                Optional<GetCardIdByPageOutDto> first = cardIdByPage.stream().filter(c -> c.getId().equals(cardDetail.getExchangeCardId())).findFirst();
                if (first.isPresent()){
                    cardDetail.setUseType(first.get().getUseType());
                    cardDetail.setCardType(first.get().getCardType());
                    cardDetail.setDenomination(first.get().getDenomination());
                }
                //发劵人门店
                if (StringUtils.isNotBlank(cardDetail.getSenderStoreCode())){
                    cardDetail.setSenderStoreName(cardDetail.getSenderStoreCode()+"/"+cardDetail.getSenderStoreName());
                }
                //岗位
                if (cardDetail.getVerificateStation() != null){
                    if (!CollectionUtils.isEmpty(dicList)){
                        Optional<QueryByParamsOutDto> first1 = dicList.stream().filter(d -> d.getId().equals(Long.valueOf(cardDetail.getVerificateStation().toString()))).findFirst();
                        if (first1.isPresent()){
                            cardDetail.setVerificateStationName(first1.get().getValueName());
                        }
                    }
                }
            }
        }

        page.setRecords(exchangeCardDetailList);
        page.setTotal(count);
        return page;
    }

    //脱敏方法
    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
    public int updCardCodeByStage(Map<String, Object> map) {
        List<String> idList = (List<String>) map.get("idList");
        if (null == idList || idList.size() < 1) {
            return -1;
        } else {
            List<String> idList2 = new ArrayList<>();
            for (String s : idList) {
                idList2.add(s);
                if (idList2.size() > 60) {
                    //卷码批量做废
                    cardCodeDao.updCardCodeByStage(idList2);
                    idList2 = new ArrayList<>();
                }
            }
            if (idList2.size() > 0) {
                //卷码批量做废
                cardCodeDao.updCardCodeByStage(idList2);
            }
        }
        return 1;
    }

    public void clearCardCode(String phone) throws BusinessException {
        String encrypt = MybatisMateConfig.encrypt(phone);
        cardCodeDao.clearCardCode(encrypt);
    }


    public Long findCardCodePageCount(ExchangeCardDetail exchangeCardDetail,
                                      List<String> channelCodes,
                                      List<Long> companyOrgIds) {
        String encrypt = MybatisMateConfig.encrypt(exchangeCardDetail.getPhone());
        exchangeCardDetail.setPhone(encrypt);
        //处理核销门店所属大区,多选下拉大区code
        if (StringUtils.isNotBlank(exchangeCardDetail.getAreaStore())) {
            List<String> areaCodes = Arrays.asList(exchangeCardDetail.getAreaStore().split(","));
            if (areaCodes != null && areaCodes.size() > 0) {
                FindStoreByAreaCodesInDto areaCodesInDto = new FindStoreByAreaCodesInDto();
                areaCodesInDto.setAreaCodes(areaCodes);
                List<StoreEntity> storeEntityList = orgClient.findStoreByAreaCodes(areaCodesInDto).getData();
                if (storeEntityList != null && storeEntityList.size() > 0){
                    List<Long> storeIds = storeEntityList.stream().map(s -> s.getId()).collect(Collectors.toList());
                    exchangeCardDetail.setAreaStoreIds(storeIds);
                }else {
                    return 0L;
                }
            }
        }
        if(StringUtils.isNotBlank(exchangeCardDetail.getActivityIds())) {
            List<String> activityIds = Arrays.asList(exchangeCardDetail.getActivityIds().split(","));
            List<Long> activityIdList = activityIds.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            exchangeCardDetail.setActivityIdList(activityIdList);
        }
        if (StringUtils.isNotBlank(exchangeCardDetail.getIds())){
            List<String> ids = Arrays.asList(exchangeCardDetail.getIds().split(","));
            List<Long> idList = ids.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            exchangeCardDetail.setIdList(idList);
        }
        if(StringUtils.isNotBlank(exchangeCardDetail.getDrainageActivityIds())) {
            List<String> drainageActivityIds = Arrays.asList(exchangeCardDetail.getDrainageActivityIds().split(","));
            List<Long> drainageActivityIdList = drainageActivityIds.stream().map(a -> Long.valueOf(a)).collect(Collectors.toList());
            exchangeCardDetail.setDrainageActivityIdList(drainageActivityIdList);
        }
        List<GetCardIdByPageOutDto> cardIdByPage = cardCodeDao.getCardIdByPage(exchangeCardDetail, channelCodes, companyOrgIds);
        if (CollectionUtils.isEmpty(cardIdByPage)) {
            return 0L;
        }
        exchangeCardDetail.setCardIdList(cardIdByPage.stream().map(GetCardIdByPageOutDto::getId).collect(Collectors.toList()));

        Long count = cardCodeDao.findCardCodePageCount(exchangeCardDetail, channelCodes, companyOrgIds);
        return count;
    }

//    public PageInfo<?> getCardByGoodsId(CardPageInDto cardPageInDto) {
//        PageInfo<ExchangeCard> page = new PageInfo<>(cardPageInDto.getPage(), cardPageInDto.getSize());
//        cardPageInDto.setPage(page.getOffset());
//        cardPageInDto.setSize(page.getSize());
//        List<ExchangeCard> exchangeCards = cardCodeDao.getCardPageByGoodsId(cardPageInDto);
//        if (null != exchangeCards && exchangeCards.size() > 0) {
//            Set<ExchangeCard> exchangeCardList = new HashSet<>();
//            if (cardPageInDto.getGoodsCategoryId() != null){
//                // 判断是否是全品类券
//                List<Long> exchangeCardIds = exchangeCards.stream().filter(res -> res.getIsCheckAll() == 0).map(ExchangeCard::getId).collect(Collectors.toList());
//                if (exchangeCardIds.size() > 0) {
//                    // 查询卡券是否关联此分类
//                    List<GoodsCategoryMapping> goodsCategoryMappings = cardDao.findGoodsCategoryByCardIds(exchangeCardIds, cardPageInDto.getGoodsCategoryId());
//                    if (null != goodsCategoryMappings && goodsCategoryMappings.size() > 0) {
//                        goodsCategoryMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getSourceId())).forEach(exchangeCardList::add));
//                    }
//                }
//                exchangeCardList.stream().filter(res -> res.getIsCheckAll() == 1).forEach(exchangeCardList::add);
//                // 是否绑定活动
//                if (cardPageInDto.getActivityId() != null) {
//                    List<CardActivityMapping> cardActivityMappings = cardDao.findMallCardActivityByCardIds(cardPageInDto.getActivityId() , exchangeCardIds);
//                    if (null != cardActivityMappings && cardActivityMappings.size() > 0){
//                        cardActivityMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getExchangeCardId())).forEach(exchangeCardList::add));
//                    }
//                }
//            } else {
//                exchangeCardList.addAll(exchangeCards);
//            }
//            List<Long> delCardIds = new ArrayList<>();
//            for (ExchangeCard exchangeCard : exchangeCardList) {
//                // 判断优惠发放券码是否领取完
//                Long count =  cardCodeDao.findExchangeCardDetail(exchangeCard.getId());
//                exchangeCard.setCardCodeCount(count);
//                if (cardPageInDto.getGoodsId() != null){
//                    if (count == 0){
//                        delCardIds.add(exchangeCard.getId());
//                    }
//                }
//                // 查询卡券生成券码已领取数量
//                Long useNum = cardCodeDao.findCardDetailByCardId(exchangeCard.getId());
//                exchangeCard.setReceivedNum(useNum);
//            }
//
//            if (null != cardPageInDto.getPhone()) {
//                List<Long> collect = exchangeCardList.stream().map(ExchangeCard::getId).collect(Collectors.toList());
//                exchangeCardList.stream().forEach(exchangeCard -> {
//                    exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum());//10
//                    List<QueryCanReceiveNum> queryCanReceiveNums = cardCodeDao.queryCanReceiveNum(collect, cardPageInDto.getPhone());
//                    queryCanReceiveNums.stream()
//                            .filter(queryCanReceiveNum -> exchangeCard.getId().equals(queryCanReceiveNum.getCardId()))
//                            .forEach(queryCanReceiveNum -> exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum() - queryCanReceiveNum.getCount()));
//                    // 判断优惠券是否领取完
//                   Long total =  cardDao.findValidCard(exchangeCard.getId());
//                   exchangeCard.setCanReceiveNum(total);
//                });
//                if (delCardIds.size() > 0){
//                    for (Long delCardId : delCardIds) {
//                        exchangeCardList.removeIf(res -> res.getId().equals(delCardId));
//                    }
//                }
//            }
//            page.setTotal(exchangeCardList.size());
//            page.setRecords(new ArrayList<>(exchangeCardList));
//
//        }
//        return page;
//    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 72000, rollbackFor = Exception.class)
    public void receiveCardCode(CardPageInDto cardPageInDto)throws Exception{
        if (cardPageInDto == null)
            throw new Exception("请求参数错误！");
        if (cardPageInDto.getPhone() == null || "".equals(cardPageInDto.getPhone()))
            throw new Exception("手机号不能为空！");

        ExchangeCard card = cardDao.findCardInfo(cardPageInDto.getExchangeCardId());
        cardPageInDto.setPhone(MybatisMateConfig.encrypt(cardPageInDto.getPhone()));
        if (card == null || card.getId() == null)
            throw new Exception("卡券信息不存在！");
        if (!card.getStage().equals("1"))
            throw new Exception("当前卡券已被禁用！");
        if(card.getLimitReceiveNum()!=null){
           if(card.getLimitReceiveNum()<=cardCodeDao.getCardCountByGoodsId(cardPageInDto)){
               throw new Exception("每人限领"+card.getLimitReceiveNum()+"张哦！");
           }

        }

        List<Long> cardIds = new ArrayList<>();
        cardIds.add(cardPageInDto.getExchangeCardId());
        List<BindCardCodeDto> allValidCodes = cardService.getAllValidCardCodeList(cardIds, 1);
        if (allValidCodes == null || allValidCodes.size() <= 0){
            throw new Exception("优惠券被领完了");
        }

        FindSalesmanByIdOutDto salesman = new FindSalesmanByIdOutDto();
        if (cardPageInDto.getChargeUserId() != null){
            salesman = orgClient.findSalesmanById(cardPageInDto.getChargeUserId()).getData();
        }

        UpdateWrapper<ExchangeCardDetail> wrapper = new UpdateWrapper();
        wrapper.set("phone",cardPageInDto.getPhone());
        wrapper.set("binding_card_date",new Date());
        wrapper.eq("card_id",allValidCodes.get(0).getCardNo());
        if (salesman != null){
//            checkSalesman(salesman,allValidCodes.get(0).getExchangeCardId());
            if (StringUtils.isNotEmpty(salesman.getAreaCode())) {
                wrapper.set("sender_area_code", salesman.getAreaCode());
            }
            if (StringUtils.isNotEmpty(salesman.getAreaName())) {
                wrapper.set("sender_area_name", salesman.getAreaName());
            }
            if (salesman.getCompanyId() != null) {
                wrapper.set("sender_company_id", salesman.getCompanyId());
            }
            if (StringUtils.isNotEmpty(salesman.getCompanyName())) {
                wrapper.set("sender_company_name", salesman.getCompanyName());
            }
            if (salesman.getStoreId() != null) {
                wrapper.set("sender_store_id", salesman.getStoreId());
            }
            if (StringUtils.isNotEmpty(salesman.getStoreCode())) {
                wrapper.set("sender_store_code", salesman.getStoreCode());
            }
            if (StringUtils.isNotEmpty(salesman.getStoreName())) {
                wrapper.set("sender_store_name", salesman.getStoreName());
            }
            if (salesman.getId() != null) {
                wrapper.set("sender_charge_user_id", salesman.getId());
            }
            if (StringUtils.isNotEmpty(salesman.getName())) {
                wrapper.set("sender_charge_user_name", MybatisMateConfig.encrypt(salesman.getName()));
            }
            if (StringUtils.isNotEmpty(salesman.getCode())) {
                wrapper.set("sender_charge_code", salesman.getCode());
            }
            if (StringUtils.isNotEmpty(salesman.getPhone())) {
                wrapper.set("sender_charge_phone", MybatisMateConfig.encrypt(salesman.getPhone()));
            }
            if (salesman.getStation() != null) {
                wrapper.set("sender_station", salesman.getStation());
            }
        }
        cardCodeDao.update(null,wrapper);

        TCardReceiveRecord tCardReceiveRecord = new TCardReceiveRecord();
        tCardReceiveRecord.setReceiveCustomerPhone(cardPageInDto.getPhone());
        tCardReceiveRecord.setOriginalCardId(allValidCodes.get(0).getCardNo());
        tCardReceiveRecord.setNowCardId(allValidCodes.get(0).getCardNo());
        tCardReceiveRecord.setCreatedDate(new Date());
        tCardReceiveRecord.setModifiedDate(new Date());
        tCardReceiveRecordMapper.insert(tCardReceiveRecord);
    }

    private int checkSalesman(FindSalesmanByIdOutDto salesman, Long cardId) {
        int result = 0;
        if (cardId != null) {
            List<ExchangeCardCompanyMapping> exchangeCardCompanyList = cardDao.getExchangeCardCompanyList(cardId);
            if (!CollectionUtils.isEmpty(exchangeCardCompanyList)){
                Optional<ExchangeCardCompanyMapping> first = exchangeCardCompanyList.stream().filter(c -> c.getCompanyId().equals(salesman.getCompanyId())).findFirst();
                if (first.isEmpty()){
                    return -1;
                }
            }
            List<String> storeIdList = cardDao.findStoreOrgIdByCardId(cardId);
            if (!CollectionUtils.isEmpty(storeIdList)){
                Optional<String> first = storeIdList.stream().filter(s -> s.equals(salesman.getStoreId())).findFirst();
                if (first.isEmpty()){
                    return -2;
                }
            }
            result = 1;
        }
        return result;
    }


    /**
     * 根据手机号查询可用优惠券
     * @param phone
     * @return
     */
    public List<AvailableCardCodeVO> getAvailableCardCodeByPhone(String phone, String channelCode){
        // 先找出用户所有的优惠券
        String encrypt = MybatisMateConfig.encrypt(phone);
        List<AvailableCardCodeVO> availableCardCodeVOList = cardCodeDao.getAvailableCardCodeByPhone(encrypt, channelCode);

        // 该用户没有优惠券，返回空数据
        if(availableCardCodeVOList.isEmpty()){
            return new ArrayList<>();
        }

        // 整理卡券ID
        List<Long> exchangeCardIds = availableCardCodeVOList.stream().map(AvailableCardCodeVO::getExchangeCardId).distinct().collect(Collectors.toList());

        // 根据卡券ID查询指定的厨电商品、厨店分类和活动信息
        List<KitchenProductMapping> kitchenProductMappings = cardDao.queryKitchenProductsByExchangeCardIds(exchangeCardIds);
        List<CardActivityMapping> cardActivityMappings = cardDao.queryMallactivityMappingsByExchangeCardIds(exchangeCardIds);
        List<GoodsCategoryMapping> cardCategoryMappings = cardDao.queryCategoryMappingsByExchangeCardIds(exchangeCardIds);


        for (AvailableCardCodeVO availableCardCodeVO : availableCardCodeVOList) {
            List<KitchenProductMapping> productMappings = new ArrayList<>();
            List<CardActivityMapping> activityMappings = new ArrayList<>();
            List<GoodsCategoryMapping> categoryMappings = new ArrayList<>();

            // 遍历厨电商品
            if (!CollectionUtils.isEmpty(kitchenProductMappings)) {
                List<KitchenProductMapping> first = kitchenProductMappings.stream().filter(k -> Objects.equals(availableCardCodeVO.getExchangeCardId(),k.getExchangeCardId())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(first)) {
                    first.stream().forEach(productMapping -> {
                        if (StringUtils.isNotBlank(productMapping.getCreatedName())) {
                            productMapping.setCreatedName(MybatisMateConfig.decrypt(productMapping.getCreatedName()));
                        }
                        if (StringUtils.isNotBlank(productMapping.getModifiedName())) {
                            productMapping.setModifiedName(MybatisMateConfig.decrypt(productMapping.getModifiedName()));
                        }
                    });
                    productMappings.addAll(first);
                }
            }

//            for (KitchenProductMapping productMapping : kitchenProductMappings) {
//                if (availableCardCodeVO.getExchangeCardId() == productMapping.getExchangeCardId()) {
//                    productMappings.add(productMapping);
//                }
//                if (StringUtils.isNotBlank(productMapping.getCreatedName())) {
//                    productMapping.setCreatedName(MybatisMateConfig.decrypt(productMapping.getCreatedName()));
//                }
//                if (StringUtils.isNotBlank(productMapping.getModifiedName())){
//                    productMapping.setModifiedName(MybatisMateConfig.decrypt(productMapping.getModifiedName()));
//                }
//            }

            // 遍历商品活动
            if (!CollectionUtils.isEmpty(cardActivityMappings)){
                List<CardActivityMapping> first = cardActivityMappings.stream().filter(a -> Objects.equals(availableCardCodeVO.getExchangeCardId(),a.getExchangeCardId())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(first)) {
                    first.stream().forEach(cardActivityMapping -> {
                        if (StringUtils.isNotBlank(cardActivityMapping.getCreatedName())){
                            cardActivityMapping.setCreatedName(MybatisMateConfig.decrypt(cardActivityMapping.getCreatedName()));
                        }
                        if (StringUtils.isNotBlank(cardActivityMapping.getModifiedName())){
                            cardActivityMapping.setModifiedName(MybatisMateConfig.decrypt(cardActivityMapping.getModifiedName()));
                        }
                    });
                    activityMappings.addAll(first);
                }
            }
//            for (CardActivityMapping cardActivityMapping : cardActivityMappings) {
//                if(availableCardCodeVO.getExchangeCardId() == cardActivityMapping.getExchangeCardId()){
//                    activityMappings.add(cardActivityMapping);
//                }
//                if (StringUtils.isNotBlank(cardActivityMapping.getCreatedName())){
//                    cardActivityMapping.setCreatedName(MybatisMateConfig.decrypt(cardActivityMapping.getCreatedName()));
//                }
//                if (StringUtils.isNotBlank(cardActivityMapping.getModifiedName())){
//                    cardActivityMapping.setModifiedName(MybatisMateConfig.decrypt(cardActivityMapping.getModifiedName()));
//                }
//            }

            // 遍历商品分类
//            for (GoodsCategoryMapping goodsCategoryMapping : cardCategoryMappings) {
//                if(availableCardCodeVO.getExchangeCardId() == goodsCategoryMapping.getSourceId()){
//                    categoryMappings.add(goodsCategoryMapping);
//                }
//            }

            if (!CollectionUtils.isEmpty(cardCategoryMappings)){
                List<GoodsCategoryMapping> first = cardCategoryMappings.stream().filter(c -> Objects.equals(availableCardCodeVO.getExchangeCardId(),c.getSourceId())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(first)) {
                    categoryMappings.addAll(first);
                }
            }

            availableCardCodeVO.setProductMappings(productMappings);
            availableCardCodeVO.setActivityMappings(activityMappings);
            availableCardCodeVO.setCategoryMappings(categoryMappings);

            Date nowTime = new Date();
            if (availableCardCodeVO.getUserStatus() == 0L){
                if (nowTime.after(availableCardCodeVO.getValidEndTime())){
                    availableCardCodeVO.setUserStatus(3L);
                }
            }
        }

        //根据优惠券使用状态排序
        return availableCardCodeVOList;
    }


    /**
     * 根据优惠券ID查询卡券
     * @param cardId
     * @return
     */
    public AvailableCardCodeVO getAvailableCardCodeByCardId(String cardId){
        // 找出优惠券信息
        ExchangeCardDetail exchangeCardDetail = cardCodeDao.queryByCardId(cardId);
        if(null == exchangeCardDetail){
            throw new BusinessException("该券码不存在！");
        }
        if("1".equals(exchangeCardDetail.getUserStatus())){
            throw new BusinessException("该券码已使用！");
        }else if("2".equals(exchangeCardDetail.getUserStatus())){
            throw new BusinessException("该券码已作废！");
        }
        if("0".equals(exchangeCardDetail.getStage())){
            throw new BusinessException("该券码已被禁止使用");
        }
        if(0 != exchangeCardDetail.getIsDeleted()){
            throw new BusinessException("该券码已被删除！");
        }
        if(2 == exchangeCardDetail.getGivingStatus()){
            throw new BusinessException("该券码已被转赠");
        }

        Date now = new Date();
        if (now.compareTo(cn.hutool.core.date.DateUtil.parseDateTime(exchangeCardDetail.getValidStartTime())) < 0
                || now.compareTo(cn.hutool.core.date.DateUtil.parseDateTime(exchangeCardDetail.getValidEndTime())) > 0)
            throw new BusinessException("当前券码不在有效期内！");

        ExchangeCard exchangeCard = cardDao.findCardInfo(exchangeCardDetail.getExchangeCardId());
        if(null == exchangeCard){
            throw new BusinessException("该卡券不存在！");
        }
        if("01".equals(exchangeCard.getUseType())){
            throw new BusinessException("兑换券不支持使用！");
        }
        if("0".equals(exchangeCard.getStage())){
            throw new BusinessException("该卡券已被禁止使用");
        }
        if(0 != exchangeCard.getIsDeleted()){
            throw new BusinessException("该卡券已被删除！");
        }

        List<Long> exchangeCardIds = new ArrayList<>();
        exchangeCardIds.add(exchangeCard.getId());

        // 根据卡券ID查询指定的厨电商品和活动信息
        List<KitchenProductMapping> kitchenProductMappings = cardDao.queryKitchenProductsByExchangeCardIds(exchangeCardIds);
        List<CardActivityMapping> cardActivityMappings = cardDao.queryMallactivityMappingsByExchangeCardIds(exchangeCardIds);
        List<GoodsCategoryMapping> cardCategoryMappings = cardDao.queryCategoryMappingsByExchangeCardIds(exchangeCardIds);

        // 遍历厨电商品
        for (KitchenProductMapping productMapping : kitchenProductMappings) {
            if (StringUtils.isNotBlank(productMapping.getCreatedName())){
                productMapping.setCreatedName(MybatisMateConfig.decrypt(productMapping.getCreatedName()));
            }
            if (StringUtils.isNotBlank(productMapping.getModifiedName())){
                productMapping.setModifiedName(MybatisMateConfig.decrypt(productMapping.getModifiedName()));
            }
        }

        // 遍历商品活动
        for (CardActivityMapping cardActivityMapping : cardActivityMappings) {
            if (StringUtils.isNotBlank(cardActivityMapping.getCreatedName())){
                cardActivityMapping.setCreatedName(MybatisMateConfig.decrypt(cardActivityMapping.getCreatedName()));
            }
            if (StringUtils.isNotBlank(cardActivityMapping.getModifiedName())){
                cardActivityMapping.setModifiedName(MybatisMateConfig.decrypt(cardActivityMapping.getModifiedName()));
            }
        }

        AvailableCardCodeVO availableCardCodeVO = new AvailableCardCodeVO();
        availableCardCodeVO.setExchangeCardId(exchangeCardDetail.getExchangeCardId());
        availableCardCodeVO.setExchangeCardName(exchangeCard.getTitle());
        availableCardCodeVO.setCardId(exchangeCardDetail.getCardId());
        availableCardCodeVO.setAssTitle(exchangeCard.getAssTitle());
        availableCardCodeVO.setUseType(Long.valueOf(exchangeCard.getUseType()));
        availableCardCodeVO.setDenomination(new BigDecimal(exchangeCard.getDenomination()));
        availableCardCodeVO.setThreshold(exchangeCard.getThreshold());
        availableCardCodeVO.setDiscount(exchangeCard.getDiscount());
        availableCardCodeVO.setProductMappings(kitchenProductMappings);
        availableCardCodeVO.setActivityMappings(cardActivityMappings);
        availableCardCodeVO.setCategoryMappings(cardCategoryMappings);
        availableCardCodeVO.setIsCheckAll(null == exchangeCard.getIsCheckAll() ? 0L : exchangeCard.getIsCheckAll());
        return availableCardCodeVO;
    }

    public List<ExchangeCard> getCouponInfoByCouponIds(CouponInfoResDto couponInfoResDto) {
        List<Long> couponIds = couponInfoResDto.getCouponIds();
        //  查询所有优惠券信息
        List<ExchangeCard> exchangeCards = cardCodeDao.getCouponInfoByCouponIds(couponIds);
        for (ExchangeCard exchangeCard : exchangeCards) {
            // 判断优惠发放券码是否领取完
            Long count =  cardCodeDao.findExchangeCardDetail(exchangeCard.getId());
            exchangeCard.setCardCodeCount(count);
            // 查询卡券生成券码已领取数量
            Long useNum = cardCodeDao.findCardDetailByCardId(exchangeCard.getId());
            exchangeCard.setReceivedNum(useNum);
        }
        // 判断优惠券是否领取完
        if (StringUtils.isNotEmpty(couponInfoResDto.getPhone())){
            String encrypt = MybatisMateConfig.encrypt(couponInfoResDto.getPhone());
            List<Long> collect = exchangeCards.stream().map(ExchangeCard::getId).collect(Collectors.toList());
            exchangeCards.stream().forEach(exchangeCard -> {
                exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum());
                List<QueryCanReceiveNum> queryCanReceiveNums = cardCodeDao.queryCanReceiveNum(collect, encrypt);
                queryCanReceiveNums.stream()
                        .filter(queryCanReceiveNum -> exchangeCard.getId().equals(queryCanReceiveNum.getCardId()))
                        .forEach(queryCanReceiveNum -> exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum() - queryCanReceiveNum.getCount()));
                // 判断优惠券是否领取完
                Long count =  cardDao.findValidCard(exchangeCard.getId());
                exchangeCard.setCanReceiveNum(count);
            });
        }
        return exchangeCards;
    }



    public PageInfo<ExchangeCard> selectCardByGoodsIdWithPage(CardPageInDto cardPageInDto) {
        if (StringUtils.isNotBlank(cardPageInDto.getPhone())){
            cardPageInDto.setPhone(MybatisMateConfig.encrypt(cardPageInDto.getPhone()));
        }


        PageInfo<ExchangeCard> page = new PageInfo<>(cardPageInDto.getPage(), cardPageInDto.getSize());
        // 查询所有优惠券
        List<ExchangeCard> exchangeCards = cardCodeDao.getCardPageByGoodsId1(cardPageInDto);
        if (null != exchangeCards && exchangeCards.size() > 0) {
            Set<ExchangeCard> exchangeCardList = new HashSet<>();
            if (null != cardPageInDto.getGoodsCode()){
                // 查询商品分类
                List<GoodsGoryInfoDto> goodsGoryInfoDtos = new ArrayList<>();
                try {
                    goodsGoryInfoDtos = productClientService.getGoodsGoryIdByGoodsId(cardPageInDto.getGoodsCode()).getData();
                } catch (Exception e) {
                    goodsGoryInfoDtos = productClientService.getGoodsGoryIdByGoodsIdM(cardPageInDto.getGoodsCode()).getData();
                }
                if (!CollectionUtils.isEmpty(goodsGoryInfoDtos)){
                    for (GoodsGoryInfoDto goodsGoryInfoDto : goodsGoryInfoDtos) {
                        Long isExcludeCoupon = Optional.ofNullable(goodsGoryInfoDto.getIsExcludeCoupon()).orElse(1L);
                        if (isExcludeCoupon == 0){
                            return null;
                        }
                    }
                    // 判断是否是全品类券
                    List<Long> exchangeCardIds = exchangeCards.stream().filter(res -> res.getIsCheckAll() == 0).map(ExchangeCard::getId).collect(Collectors.toList());
                    if (exchangeCardIds.size() > 0) {
                        // 查询卡券是否关联此分类
                        List<GoodsCategoryMapping> goodsCategoryMappings = cardDao.findGoodsCategoryByCardIds(exchangeCardIds,goodsGoryInfoDtos.stream().map(GoodsGoryInfoDto::getGoodsCateGoryId).collect(Collectors.toList()));
                        if (null != goodsCategoryMappings && goodsCategoryMappings.size() > 0) {
                            goodsCategoryMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getSourceId())).forEach(exchangeCardList::add));
                        }
                    }
                    exchangeCards.stream().filter(res -> res.getIsCheckAll() == 1).forEach(exchangeCardList::add);
                    // 是否绑定活动
                    List<Long> activityIds = goodsGoryInfoDtos.stream().map(GoodsGoryInfoDto::getActivityId).filter(Objects::nonNull).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(activityIds)) {
                        List<CardActivityMapping> cardActivityMappings = cardDao.findMallCardActivityByCardIds(activityIds , exchangeCardIds);
                        if (null != cardActivityMappings && cardActivityMappings.size() > 0){
                            cardActivityMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getExchangeCardId())).forEach(exchangeCardList::add));
                        }
                    }
                    // 查询关联商品
                    if (null != cardPageInDto.getGoodsCode()){
                        List<Long> ids = cardCodeDao.selectCardGoodsMapping(cardPageInDto.getGoodsCode(), exchangeCardIds);
                        if (!CollectionUtils.isEmpty(ids)){
                            ids.forEach(cardId -> {
                                exchangeCards.stream().filter(exchangeCard -> exchangeCard.getId().equals(cardId)).forEach(exchangeCardList::add);
                            });
                        }
                    }
                }
            } else {
                exchangeCardList.addAll(exchangeCards);
            }
//            if (cardPageInDto.getGoodsCategoryId() != null){
//                // 判断是否是全品类券
//                List<Long> exchangeCardIds = exchangeCards.stream().filter(res -> res.getIsCheckAll() == 0).map(ExchangeCard::getId).collect(Collectors.toList());
//                if (exchangeCardIds.size() > 0) {
//                    // 查询卡券是否关联此分类
//                    List<GoodsCategoryMapping> goodsCategoryMappings = cardDao.findGoodsCategoryByCardIds(exchangeCardIds, cardPageInDto.getGoodsCategoryId());
//                    if (null != goodsCategoryMappings && goodsCategoryMappings.size() > 0) {
//                        goodsCategoryMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getSourceId())).forEach(exchangeCardList::add));
//                    }
//                }
//                exchangeCardList.stream().filter(res -> res.getIsCheckAll() == 1).forEach(exchangeCardList::add);
//                // 是否绑定活动
//                if (cardPageInDto.getActivityId() != null) {
//                    List<CardActivityMapping> cardActivityMappings = cardDao.findMallCardActivityByCardIds(cardPageInDto.getActivityId() , exchangeCardIds);
//                    if (null != cardActivityMappings && cardActivityMappings.size() > 0){
//                        cardActivityMappings.forEach(item -> exchangeCards.stream().filter(res -> res.getId().equals(item.getExchangeCardId())).forEach(exchangeCardList::add));
//                    }
//                }
//                // 查询关联商品
//                if (null != cardPageInDto.getGoodsId()){
//                    List<Long> ids = cardCodeDao.selectCardGoodsMapping(cardPageInDto.getGoodsId(), exchangeCardIds);
//                    if (!CollectionUtils.isEmpty(ids)){
//                        ids.forEach(cardId -> {
//                            exchangeCards.stream().filter(exchangeCard -> exchangeCard.getId().equals(cardId)).forEach(exchangeCardList::add);
//                        });
//                    }
//                }
//            } else {
//                exchangeCardList.addAll(exchangeCards);
//            }
//            List<Long> delCardIds = new ArrayList<>();
            for (ExchangeCard exchangeCard : exchangeCardList) {
                // 判断优惠发放券码是否领取完
                long count =  exchangeCard.getTotalNum() - exchangeCard.getReceivedNum();
                exchangeCard.setCardCodeCount(count); // 优惠券未领取总数量
/*
                if (null != cardPageInDto.getGoodsCode()){
                    if (count == 0){
                        delCardIds.add(exchangeCard.getId());
                    }
                }
*/
            }
            if (null != cardPageInDto.getPhone()) {
                String encrypt = MybatisMateConfig.encrypt(cardPageInDto.getPhone());
                List<Long> collect = exchangeCardList.stream().map(ExchangeCard::getId).collect(Collectors.toList());
                exchangeCardList.forEach(exchangeCard -> {
                    exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum());
                    // 获取用户已领卡券数量
                    List<QueryCanReceiveNum> queryCanReceiveNums = cardCodeDao.queryCanReceiveNum(collect, encrypt);
                    // 匹配卡券
                    queryCanReceiveNums.stream()
                            .filter(queryCanReceiveNum -> exchangeCard.getId().equals(queryCanReceiveNum.getCardId()))
                            .forEach(queryCanReceiveNum -> exchangeCard.setCanReceiveNum(exchangeCard.getLimitReceiveNum() - queryCanReceiveNum.getCount()));
                    // 查询用户可领数
                    exchangeCard.setCanReceiveNum(cardCodeDao.findUserDrawNum(exchangeCard.getId() , encrypt));
                });
/*
                if (delCardIds.size() > 0){
                    for (Long delCardId : delCardIds) {
                        exchangeCardList.removeIf(res -> res.getId().equals(delCardId));
                    }
                }
 */
            }
            page.setTotal(exchangeCardList.size());

            List<ExchangeCard> collect = exchangeCardList.stream().sorted(Comparator.comparing(ExchangeCard::getCanReceiveNum).reversed()).collect(Collectors.toList()).stream().limit(page.getSize()).collect(Collectors.toList());
            page.setRecords(new ArrayList<>(collect));
        }
        return page;
    }

    /**
     * 根据手机号绑定券码
     * @param mallCardCodeBoundPhone
     * @return
     * @throws Exception
     */
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
    public List<ExchangeCardDetail> addMallCardCodeByPhone(MallCardCodeBoundPhone mallCardCodeBoundPhone) throws Exception {
        String userId = getCurrentUserId();
        Long cardId = mallCardCodeBoundPhone.getCardId();
        String phone = MybatisMateConfig.encrypt(mallCardCodeBoundPhone.getPhone());
        // 每次领取数量
        int quantity = 1;
        // 查询卡券信息
        ExchangeCard  exchangeCard =  cardCodeDao.selectCardById(cardId);
        String cardName = exchangeCard.getTitle();
        if (null == exchangeCard){
            throw new BusinessException(30000,"卡券信息不存在!");
        }
        // 校验卡券是否启用
        if ("0".equals(exchangeCard.getStage())) {
            throw new BusinessException(30001,"当前卡卷已被禁用");
        }
        // 判断是否超过有效期的截至时间
        Date nowTime = new Date();
        SimpleDateFormat sdfhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (!(nowTime.after(sdfhms.parse(exchangeCard.getValidStartTime())) && nowTime.before(sdfhms.parse(exchangeCard.getValidEndTime())))) {
            throw new BusinessException(30002,"活动不在有效期内，不可以领取。");
        }

        //判断是否超过领取次数
        if (exchangeCard.getLimitReceiveNum() != null && exchangeCard.getLimitReceiveNum() > 0) {
            Long count = cardCodeDao.findUserDrawNum(cardId, phone);
            if (count <= 0) {
                throw new BusinessException(30003,"您已领取过该卡券，请勿重复领取！");
            }
        }


        // 根据卡卷id查询卷码总数
        int count = cardDao.getCardCodeByCardIdCount(cardId);
        // 校验卡券可领取数量
        if ((count + quantity) > exchangeCard.getTotalNum()) {
            throw new BusinessException(30004,"来晚一步，券已领完");
        }
        String cardVaildValue = exchangeCard.getCardVaildValue();
        List<ExchangeCardDetail> exchangeCardDetailList = new ArrayList<>();
        ExchangeCardDetail exchangeCardDetail = null;
        List<ExchangeCardDetail> exchangeCardList = new ArrayList<>();

        Long cardCodeId = null;
        cardCodeId = 0L;

        String cardId2 = cardId + "";
        cardId2 = cardId2.length() >= 4 ? cardId2.substring(cardId2.length() - 4, cardId2.length()) : UniquenessIdUtil.addLeftZero(cardId2, 4);
        for (int i = 0; i < quantity; i++) {
            exchangeCardDetail = new ExchangeCardDetail();
            long num = (int) ((Math.random() * 9 + 1) * 10000000);
            exchangeCardDetail.setCardId(cardId2 + num + UniquenessIdUtil.addLeftZero(cardCodeId + i + "", 4));
            exchangeCardDetail.setExchangeCardId(cardId);
            exchangeCardDetail.setExchangeCardName(cardName);
            exchangeCardDetail.setUserStatus("0");
            exchangeCardDetail.setIsDeleted(0L);
            exchangeCardDetail.setStage("1");
            exchangeCardDetail.setCreatedDate(new Date());
            if ("1".equals(exchangeCard.getValidType())) {//券码有效期类型1.有效期 2.领取后 3.当月有效
                String[] split = cardVaildValue.split("~");
                if (split.length != 2) {
                    throw new BusinessException(30005,"券码有效期error！");
                }
                exchangeCardDetail.setValidStartTime(split[0]);
                exchangeCardDetail.setValidEndTime(split[1]);
            } else if ("2".equals(exchangeCard.getValidType())) {//券码有效期类型1.有效期 2.领取后 3.当月有效
                if (cardVaildValue == null || "".equals(cardVaildValue)) {
                    throw new BusinessException(30006,"券码配置有误，请输入领取后几天有效！");
                }
                exchangeCardDetail.setValidStartTime(DateStandardUtil.toDateFormat(new Date(), DateStandardUtil.DATE_FORMAT_FULL));
                exchangeCardDetail.setValidEndTime(DateStandardUtil.toDateFormat(DateStandardUtil.getAddDay(new Date(), new Long(cardVaildValue)), DateStandardUtil.DATE_FORMAT_FULL));
            } else if ("3".equals(exchangeCard.getValidType())) {//券码有效期类型1.有效期 2.领取后 3.当月有效
                exchangeCardDetail.setValidStartTime(DateStandardUtil.toDateFormat(DateStandardUtil.getMonthStartTime(), DateStandardUtil.DATE_FORMAT_FULL));
                exchangeCardDetail.setValidEndTime(DateStandardUtil.toDateFormat(DateStandardUtil.getMonthEndTime(), DateStandardUtil.DATE_FORMAT_FULL));
            } else {
                throw new BusinessException(30007,"券码有效期类型error！");
            }
            exchangeCardDetail.setCreatedDate(new Date());
            exchangeCardDetail.setModifiedDate(new Date());
            exchangeCardDetail.setIsGiving(Integer.parseInt(exchangeCard.getIsGiving()));//是否可赠送 0否 1是
            exchangeCardDetail.setShareCount(0);//转发次数
            exchangeCardDetail.setGivingStatus(1);//赠送状态 1.未转赠 2.已转赠
            // 手机号
            exchangeCardDetail.setPhone(phone);
            // 绑券时间
            exchangeCardDetail.setBindingCardDate(DateUtil.getDateTimeFormat(new Date()));

            exchangeCardDetailList.add(exchangeCardDetail);
            exchangeCardList.add(exchangeCardDetail);
            if (exchangeCardDetailList.size() > 50) {
                //添加劵码
                cardDao.addCardCode(exchangeCardDetailList);
                exchangeCardDetailList = new ArrayList<>();
            }
        }
        if (exchangeCardDetailList.size() > 0) {
            //添加劵码
            cardDao.addCardCode(exchangeCardDetailList);
        }
        cardDao.updCardByReceivedNum(cardDao.getCardCodeCount(cardId), cardId, userId);
        return exchangeCardList;
    }

    //上下文获取userID
    public String getCurrentUserId() {
        String user = "anonymousUser";

        try {
            SecurityContext context = SecurityContextHolder.getContext();
            user = context.getAuthentication().getPrincipal().toString();
        } catch (Exception var3) {
            user = "anonymousUser";
        }

        return user;
    }


    public ExchangeCardDetail findCardCodeById(String cardNo) {
        ExchangeCardDetail exchangeCardDetailNew = cardDao.selectCardDetailById(cardNo);
        return exchangeCardDetailNew;
    }

    /**
     * 根据卷码编码集合查询多条卷码信息-幸福家清洗服务接口调佣
     */
    public List<ExchangeCardDetail> findCardCodeByIds(GetCardCodeByIdsDto idsDto) {
        List<ExchangeCardDetail> cardDetails = new ArrayList<>();
        if (!CollectionUtils.isEmpty(idsDto.getIds())) {
            cardDetails = cardCodeDao.selectCardDetailByIds(idsDto);
        }
        return cardDetails;
    }


    public List<FindSenderChargeUserOutDto> findSenderChargeUserList(FindSenderChargeUserInDto inDto) {
        List<GetCardIdByPageOutDto> cardIdByPage = cardCodeDao.getCardIdByPage(new ExchangeCardDetail(), inDto.getChannelCodes(), inDto.getCompanyOrgIds());
        if (CollectionUtils.isEmpty(cardIdByPage)) {
            return new ArrayList<>();
        }
        inDto.setCardIdList(cardIdByPage.stream().map(GetCardIdByPageOutDto::getId).collect(Collectors.toList()));
        return cardCodeDao.findSenderChargeUserList(inDto);
    }

    public List<FindSenderChargeUserOutDto> findVerificateChargeUserList(FindSenderChargeUserInDto inDto) {
        List<GetCardIdByPageOutDto> cardIdByPage = cardCodeDao.getCardIdByPage(new ExchangeCardDetail(), inDto.getChannelCodes(), inDto.getCompanyOrgIds());
        if (CollectionUtils.isEmpty(cardIdByPage)) {
            return new ArrayList<>();
        }
        inDto.setCardIdList(cardIdByPage.stream().map(GetCardIdByPageOutDto::getId).collect(Collectors.toList()));
        return cardCodeDao.findVerificateChargeUserList(inDto);
    }

}
