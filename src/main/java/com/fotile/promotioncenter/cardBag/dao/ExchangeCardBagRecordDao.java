package com.fotile.promotioncenter.cardBag.dao;

import com.fotile.promotioncenter.cardBag.pojo.dto.GetRecordListInDto;
import com.fotile.promotioncenter.cardBag.pojo.dto.GetRecordListOutDto;
import com.fotile.promotioncenter.cardBag.pojo.entity.ExchangeCardBagRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 卡卷包领取记录表(ExchangeCardBagRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-03 10:41:09
 */
public interface ExchangeCardBagRecordDao {

    /**
     * 通过ID查询单条数据
     */
    ExchangeCardBagRecord queryById(Long id);

    /**
     * 查询指定行数据
     */
    List<GetRecordListOutDto> queryAllByLimit(GetRecordListInDto exchangeCardBagRecord);

    /**
     * 统计总行数
     */
    long count(GetRecordListInDto exchangeCardBagRecord);

    /**
     * 新增数据
     */
    int insert(ExchangeCardBagRecord exchangeCardBagRecord);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     */
    int insertBatch(@Param("entities") List<ExchangeCardBagRecord> entities);

    
    /**
     * 修改数据
     */
    int update(ExchangeCardBagRecord exchangeCardBagRecord);

    /**
     * 通过主键删除数据
     */
    int deleteById(Long id);

}

