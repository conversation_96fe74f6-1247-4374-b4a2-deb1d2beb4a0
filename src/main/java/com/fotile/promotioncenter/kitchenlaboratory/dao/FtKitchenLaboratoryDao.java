package com.fotile.promotioncenter.kitchenlaboratory.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.promotioncenter.kitchenlaboratory.pojo.entity.FtKitchenLaboratory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* Created by Mybatis Generator on 2020/05/08
 * <AUTHOR>
 */
@Repository
public interface FtKitchenLaboratoryDao extends BaseMapper<FtKitchenLaboratory> {
    /**
     * 查询所有集合信息
     * @param ftBanner
     * @return
     */
    List<FtKitchenLaboratory> selectListApi(@Param("bean") FtKitchenLaboratory ftBanner);

}