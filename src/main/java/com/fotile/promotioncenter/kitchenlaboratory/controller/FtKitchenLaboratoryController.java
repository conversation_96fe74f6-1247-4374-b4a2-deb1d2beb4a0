package com.fotile.promotioncenter.kitchenlaboratory.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.kitchenlaboratory.pojo.dto.FtKitchenLaboratorySelectDto;
import com.fotile.promotioncenter.kitchenlaboratory.pojo.entity.FtKitchenLaboratory;
import com.fotile.promotioncenter.kitchenlaboratory.service.FtKitchenLaboratoryService;
import com.fotile.promotioncenter.operationlog.service.FtOperationLogService;
import com.fotile.promotioncenter.userinfo.service.client.FtUserClientService;
import com.fotile.promotioncenter.util.CommonUtil;
import com.fotile.promotioncenter.util.UserAuthorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 厨房实验室列表接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/ft/kitchenLaboratory")
public class FtKitchenLaboratoryController extends BaseController {
    @Autowired
    private FtKitchenLaboratoryService ftKitchenLaboratoryService;
    @Autowired
    private FtOperationLogService ftOperationLogService;
    @Autowired
    private FtUserClientService ftUserClientService;

    /**
     * 新增厨房实验室内容
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public Result<FtKitchenLaboratory> insert(@RequestBody @Valid FtKitchenLaboratory ftKitchenLaboratory, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtKitchenLaboratoryController.insert()-"+ JSON.toJSONString(ftKitchenLaboratory));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        int add = ftKitchenLaboratoryService.insertSelective(ftKitchenLaboratory,request);
        if(add == -1) { return failure("该分公司已有数据，不能重复添加"); }
        if(add >0 ) {
            ftOperationLogService.insertOperationLog(request,"新增厨房实验室内容");
        }
        return success(ftKitchenLaboratory);
    }

    /**
     * 修改厨房实验室
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result<FtKitchenLaboratory> update(@RequestBody @Valid FtKitchenLaboratory ftKitchenLaboratory, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtKitchenLaboratoryController.update()-"+ JSON.toJSONString(ftKitchenLaboratory));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        if(ftKitchenLaboratory.getId() == null) {
            return failure("主键id不能为空");
        }
        if(!CommonUtil.isNotEmpty(ftKitchenLaboratory.getContent())) {
            return failure("内容不能为空");
        }
        int update = ftKitchenLaboratoryService.updateByPrimaryKeySelective(ftKitchenLaboratory);
        if(update > 0){
            ftOperationLogService.insertOperationLog(request,"修改厨房实验室：id="+ftKitchenLaboratory.getId());
        }
        return success("修改成功");
    }

    /**
     * 查询厨房实验室内容
     */
    @RequestMapping(value = "/select", method = RequestMethod.POST)
    public Result<PageInfo> select(@RequestBody @Valid FtKitchenLaboratorySelectDto ftKitchenLaboratorySelectDto, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtKitchenLaboratoryController.select()-"+ JSON.toJSONString(ftKitchenLaboratorySelectDto));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        return success(ftKitchenLaboratoryService.selectByFtKitchenLaboratory(ftKitchenLaboratorySelectDto,request));
    }

}