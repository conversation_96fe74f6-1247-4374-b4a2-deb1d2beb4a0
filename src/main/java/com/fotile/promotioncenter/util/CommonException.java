package com.fotile.promotioncenter.util;

public class CommonException extends Exception {
    /*无参构造函数*/
    public CommonException(){
        super();
    }
    
    //用详细信息指定一个异常
    public CommonException(String message){
        super(message);
    }
    
    //用指定的详细信息和原因构造一个新的异常
    public CommonException(String message, Throwable cause){
        super(message,cause);
    }
    
    //用指定原因构造一个新的异常
    public CommonException(Throwable cause) {
        super(cause);
    }
}