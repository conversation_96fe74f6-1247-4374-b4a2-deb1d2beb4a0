package com.fotile.promotioncenter.areas.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.areas.dao.FtCodeAreasDao;
import com.fotile.promotioncenter.areas.pojo.dto.FtCodeAreasSelectDto;
import com.fotile.promotioncenter.areas.pojo.entity.FtCodeAreas;
import com.fotile.promotioncenter.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 全国省市区地址信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-13
 */
@Slf4j
@Service
public class FtCodeAreasService extends ServiceImpl<FtCodeAreasDao, FtCodeAreas> {

    @Autowired
    FtCodeAreasDao ftCodeAreasDao;

    public Result<List> selectAreas(FtCodeAreasSelectDto ftCodeAreasSelectDto, HttpServletRequest request) {
        Result<List> result = new Result();
        String logInfo = ":" + Thread.currentThread().getStackTrace()[1].getMethodName() + ":";
        log.info("======" + logInfo + "begin======");
        try {
            LambdaQueryWrapper<FtCodeAreas> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CommonUtil.isNotEmpty(ftCodeAreasSelectDto.getLevelType()) , FtCodeAreas::getLevelType, ftCodeAreasSelectDto.getLevelType());
            queryWrapper.eq(CommonUtil.isNotEmpty(ftCodeAreasSelectDto.getParentId()) , FtCodeAreas::getParentId, ftCodeAreasSelectDto.getParentId());
            queryWrapper.select(FtCodeAreas::getAreaName, FtCodeAreas::getId, FtCodeAreas::getLevelType,FtCodeAreas::getCityCode);
            List<Map<String, Object>> maps = ftCodeAreasDao.selectMaps(queryWrapper);
            result = Result.buildSuccess("成功", maps);
        } catch (Exception e) {
            log.error("======error:" + e.toString() + "======");
            result = Result.buildFailure(e.toString());
        }
        log.info("======" + logInfo + "end======");
        return result;

    }
}
