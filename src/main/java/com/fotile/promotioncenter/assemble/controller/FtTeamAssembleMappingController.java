package com.fotile.promotioncenter.assemble.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.assemble.pojo.dto.FtTeamAssembleMappingSelectDto;
import com.fotile.promotioncenter.assemble.service.FtTeamAssembleMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 团队拼团关系表接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/ft/teamAssembleMapping")
public class FtTeamAssembleMappingController extends BaseController {
    @Autowired
    private FtTeamAssembleMappingService ftTeamAssembleMappingService;


    /**
     * 查询团队拼团关系表
     */
    @RequestMapping(value = "/select", method = RequestMethod.POST)
    public Result<PageInfo> select(@RequestBody @Valid FtTeamAssembleMappingSelectDto ftTeamAssembleMappingSelectDTO) {
        if(log.isDebugEnabled()) {
            log.debug("FtTeamAssembleMappingController.select()-"+ JSON.toJSONString(ftTeamAssembleMappingSelectDTO));
        }
        return success(ftTeamAssembleMappingService.selectByFtTeamAssembleMapping(ftTeamAssembleMappingSelectDTO));
    }
}