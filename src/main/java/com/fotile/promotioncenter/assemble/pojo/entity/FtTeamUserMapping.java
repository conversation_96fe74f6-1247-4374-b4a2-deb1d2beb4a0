package com.fotile.promotioncenter.assemble.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 用户团队成员关系表
* Created by Mybatis Generator on 2020/04/21
 * <AUTHOR>
 */
@Entity
@Table(name="ft_team_user_mapping", schema="promotioncenter")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FtTeamUserMapping extends AuditingEntity {
    /**
     * 团队id
     */
    @Column(name = "team_id")
    private Long teamId;

    /**
     * 团队名称
     */
    @Column(name = "team_name")
    private String teamName;

    /**
     * 入团时间
     */
    @Column(name = "join_time")
    private Date joinTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 核销码
     */
    @Column(name = "confirm_code")
    private String confirmCode;

    /**
     * 是否购买产品（1是，2否）
     */
    @Column(name = "buy_product_status")
    private String buyProductStatus;

    /**
     * 是否获得奖品
     */
    @Column(name = "get_prize_status")
    private String getPrizeStatus;

    /**
     * 获得奖品数量
     */
    @Column(name = "get_prize_num")
    private Long getPrizeNum;

    /**
     * 奖品名称
     */
    @Column(name = "prize_name")
    private String prizeName;

    /**
     * 奖品发送方式（1物流发货，2上门提货，3兑换礼品券，4兑换优惠券，5兑换现金券）
     */
    @Column(name = "prize_send_mode")
    private String prizeSendMode;

    /**
     * 收货地址
     */
    @Column(name = "get_address")
    private String getAddress;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户手机号
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 是否队长（1是，2否）
     */
    @Column(name = "captain_status")
    private String captainStatus;

    /**
     * 是否主动退出团队（1是，2否）
     */
    @Column(name = "active_exit_team_status")
    private String activeExitTeamStatus;

    /**
     * 是否被队长劝退（1是，2否）
     */
    @Column(name = "cover_captain_exit")
    private String coverCaptainExit;

    /**
     * 退出团队时间
     */
    @Column(name = "exit_team_time")
    private Date exitTeamTime;

    /**
     * 奖品id(奖品分配id)
     */
    @Column(name = "prize_id")
    private Long prizeId;

    /**
     * 拼团状态（1进行中，2已结束，3已退出）
     */
    @Column(name = "assemble_status")
    private String assembleStatus;

    /**
     * 奖品兑换码
     */
    @Column(name = "exchange_code")
    private String exchangeCode;

    /**
     * 是否已兑换（1是，2否）
     */
    @Column(name = "exchange_status")
    private String exchangeStatus;

    /**
     * 用户头像
     */
    @Column(name = "user_head")
    private String userHead;

    /**
     * 奖品兑换时间
     */
    @Column(name = "exchange_time")
    private Date exchangeTime;

    /**
     * 新增字段-奖品图片
     */
    @TableField(exist = false)
    @Column(name = "prize_img")
    private String prizeImg;

    /**
     * 新增字段-奖品类型（1现金券，2礼品券，3空奖，4优惠券）
     */
    @TableField(exist = false)
    @Column(name = "prize_type")
    private String prizeType;

    /**
     * 新增字段-中奖描述
     */
    @TableField(exist = false)
    @Column(name = "remark")
    private String remark;
}