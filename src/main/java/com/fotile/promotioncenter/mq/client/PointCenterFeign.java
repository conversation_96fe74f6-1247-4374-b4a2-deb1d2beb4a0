package com.fotile.promotioncenter.mq.client;

import com.fotile.promotioncenter.mq.FeignOAuthRequestInterceptor;
import com.fotile.promotioncenter.mq.pojo.AlterPointDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.fotile.framework.web.Result;

/**
 * <AUTHOR>
 */
@FeignClient(name = "point-center", configuration = FeignOAuthRequestInterceptor.class)
public interface PointCenterFeign {

    @RequestMapping(value = "/api/point/getCustomerPointGrow", method = RequestMethod.GET)
    public Result getCustomerPointGrow(@RequestParam("customerId") String customerId, @RequestParam("showCleanValue") boolean showCleanValue);

    @RequestMapping(value = "/api/point/alterPointRoute", method = RequestMethod.POST)
    public Result alterPointRoute(@RequestBody AlterPointDto alterPointDto);

    @RequestMapping(value = "/api/point/queryCustomerAndCompanyTid", method = RequestMethod.GET)
    public Result queryCustomerAndCompanyTid(@RequestParam("type") String type, @RequestParam("id") String id);

    @RequestMapping(value = "/api/point/queryPointRecord", method = RequestMethod.GET)
    public Result queryPointRecord(@RequestParam("page") String page, @RequestParam("size") String size,
            @RequestParam("subjectType") String subjectType, @RequestParam("subjectCode") String subjectCode);
}