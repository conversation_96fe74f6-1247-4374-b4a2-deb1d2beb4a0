package com.fotile.promotioncenter.prize.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.promotioncenter.operationlog.service.FtOperationLogService;
import com.fotile.promotioncenter.prize.pojo.dto.FtPrizeInsertDto;
import com.fotile.promotioncenter.prize.pojo.dto.FtPrizeSelectDto;
import com.fotile.promotioncenter.prize.pojo.entity.FtPrize;
import com.fotile.promotioncenter.prize.service.FtPrizeService;
import com.fotile.promotioncenter.userinfo.service.client.FtUserClientService;
import com.fotile.promotioncenter.util.UserAuthorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 奖品列表接口
 */
@Slf4j
@RestController
@RequestMapping("/api/ft/prize")
public class FtPrizeController extends BaseController {
    @Autowired
    private FtPrizeService ftPrizeService;
    @Autowired
    private FtOperationLogService ftOperationLogService;
    @Autowired
    private FtUserClientService ftUserClientService;

    /**
     * 新增奖品
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public Result<FtPrize> insert(@RequestBody @Valid FtPrizeInsertDto ftPrizeInsertDto, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtPrizeController.insert()-"+ JSON.toJSONString(ftPrizeInsertDto));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        int add = ftPrizeService.insertSelective(ftPrizeInsertDto,request);
        if(add >0 ) {
            ftOperationLogService.insertOperationLog(request,"新增奖品:"+ftPrizeInsertDto.getPrizeName());
        }
        return success(ftPrizeInsertDto);
    }

    /**
     * 修改奖品
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result<FtPrize> update(@RequestBody @Valid FtPrize ftPrize, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtPrizeController.update()-"+ JSON.toJSONString(ftPrize));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        int update = ftPrizeService.updateByPrimaryKeySelective(ftPrize);
        if(update > 0){
            ftOperationLogService.insertOperationLog(request,"修改奖品：id="+ftPrize.getId());
        }
        return success("修改成功");
    }

    /**
     * 修改奖品是否启用
     */
    @RequestMapping(value = "/updateOpenStatus", method = RequestMethod.POST)
    public Result<FtPrize> updateOpenStatus(@RequestBody @Valid FtPrize ftPrize, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtPrizeController.update()-"+ JSON.toJSONString(ftPrize));
        }
        //判断账号是否总部数据账号
        if(UserAuthorUtil.findPageAllByCurrentUser(ftUserClientService)){
            return failure("当前账号暂无权限操作");
        }
        if(ftPrize.getOpenStatus() == null) {
            return failure("是否启用不能为空");
        }
        String content = "启用id="+ftPrize.getId()+"的奖品";
        if(!"1".equals(ftPrize.getOpenStatus())){
            content = "禁用id="+ftPrize.getId()+"的奖品";
        }
        int update = ftPrizeService.updateByPrimaryKeySelective(ftPrize);
        if(update > 0){
            ftOperationLogService.insertOperationLog(request,content);
        }
        return success("修改成功");
    }

    /**
     * 查询奖品
     */
    @RequestMapping(value = "/select", method = RequestMethod.POST)
    public Result<PageInfo> select(@RequestBody @Valid FtPrizeSelectDto ftPrizeSelectDto, HttpServletRequest request) {
        if(log.isDebugEnabled()) {
            log.debug("FtPrizeController.select()-"+ JSON.toJSONString(ftPrizeSelectDto));
        }
        return success(ftPrizeService.selectByFtProductAssemble(ftPrizeSelectDto,request));
    }

    /**
     * 查看奖品
     */
    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public Result<FtPrizeSelectDto> view(@ApiParam("奖品id") @RequestParam Long id) {
        if(log.isDebugEnabled()) {
            log.debug("FtPrizeController.view()-"+ id);
        }
        return success(ftPrizeService.selectByPrimaryKey(id));
    }

}