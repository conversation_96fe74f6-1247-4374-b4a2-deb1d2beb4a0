package com.fotile.promotioncenter.redenvelope.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetRedEnvelopeAppInDto implements Serializable {
    /**
     * 分页
     */
    private Integer page;
    /**
     * 数量
     */
    private Integer size;
    /**
     * 业务员手机号
     */
    @NotNull(message = "业务员手机号不能为空！")
    private Long chargeUserId;
}
