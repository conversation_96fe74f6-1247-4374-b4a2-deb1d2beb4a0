package com.fotile.promotioncenter.client.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * 业务员实体类
 * <AUTHOR>
 *
 */
@Data
public class FindSalesmanByIdOutDto implements Serializable{

	@ApiModelProperty(value="业务员id",example="业务员id")
	private Long id;
	
	@ApiModelProperty(value="业务员工号",example="业务员工号")
    private String code;
    
    @ApiModelProperty(value="业务员姓名",example="业务员姓名")
    private String name;
    
    @ApiModelProperty(value="业务员手机号",example="业务员手机号")
    private String phone;
    
    @ApiModelProperty(value="所属公司名称",example="所属公司名称")
    private String companyName;
    
    @ApiModelProperty(value="所属部门名称",example="所属部门名称")
    private String departmentName;

    @ApiModelProperty(value="视觉系统_所属部门名称",example="视觉系统_所属部门名称")
    private String visualDepartmentName;
    
    @ApiModelProperty(value="所属门店名称",example="所属门店名称")
    private String storeName;
    
	@ApiModelProperty(value="员工id",example="关联员工id")
	private Integer employeeId;
    
    @ApiModelProperty(value="岗位",example="岗位")
    private Long station;
	
    @ApiModelProperty(value="岗位名称",example="岗位名称")
    private String stationName;

    /**
     * 岗位编码
     */
    private String stationCode;
    
    @ApiModelProperty(value="直接上级(上级的员工id)",example="直接上级(上级的员工id)")
    private Long parentId;

    @ApiModelProperty(value="直属领导名称",example="直属上级名称")
    private String parentName;

    private String parentPhone;
    
    @ApiModelProperty(value="微信号",example="微信号")
    private String wechatno;
    
    @ApiModelProperty(value="业务员头像",example="业务员头像")
    private String portrait;
    
    @ApiModelProperty(value="员工状态，0：禁用；1：启用",example="员工状态，0：禁用；1：启用")
	private Integer status;
    
    @ApiModelProperty(value="有效期-开始时间",example="有效期-开始时间")
    private String termStart;
    
    @ApiModelProperty(value="有效期-结束时间",example="有效期-结束时间")
    private String termEnd;
    
    @ApiModelProperty(value="所属公司fullpathId",example="所属公司fullpathid")
	private String companyFullPathId;
    
    @ApiModelProperty(value="所属部门fullpathId",example="所属部门fullpathid")
	private String departmentFullPathId;
    
    @ApiModelProperty(value="所属门店fullpathId",example="所属门店fullpathid")
	private String storeFullPathId;
    
    @ApiModelProperty(value="所属部门",example="所属部门id")
	private Long departmentId;

    @ApiModelProperty(value="所属部门",example="视觉系统_所属部门id")
    private Long visualDepartmentId;

    
    @ApiModelProperty(value="所属公司",example="所属公司id")
	private Long companyId;
	   
	@ApiModelProperty(value="所属门店",example="所属门店id")
	private Long storeId;
    
	@ApiModelProperty(value="所属门店编码",example="所属门店编码")
	private String storeCode;
	
	@ApiModelProperty(value="角色名称",example="角色名称")
	private String roleName;
	
	@ApiModelProperty(value="所属门店",example="所属门店id")
	private Long cityId;
	
	@ApiModelProperty(value="所属门店",example="所属门店id")
	private String cityName;
	
	@ApiModelProperty(value="二维码",example="二维码")
    private String qrcode;

    //门店所属省
    private Long storeProvicenId;
    private String storeProvicenName;
    //门店所属市
    private Long storeCityId;
    private String storeCityName;
    //门店所属区
    private Long storeCountyId;
    private String storeCountyName;
    
    private String email;

    /**
     * 门店简称
     */
    private String abbreviation;


    /**
     * 入职日期
     */
    private String entryDate;

    //门店地址
    private String storeAddress;
    private String areaCode;
    private String areaName;
}
