package com.fotile.promotioncenter.exchangecard.utils;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.framework.core.common.PageInfo;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.stream.Collectors;

public class PageUtils {

    /**
     * 分页pageable转Page，避免swagger过多
     *
     * @param pageable
     * @param <T>
     * @return
     */
    public static <T> Page<T> pageableToPage(Pageable pageable) {
        Page<T> p = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        p.addOrder(pageable.getSort().stream().map(o -> o.isAscending() ? OrderItem.asc(o.getProperty()) : OrderItem.desc(o.getProperty())).collect(Collectors.toList()));
        return p;
    }

    public static <T> PageInfo<T> pageToPageInfo(Page<T> page) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setRecords(page.getRecords());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        pageInfo.setSize(page.getSize());
        return pageInfo;
    }

    /**
     * mybatisPlus page转spring page实体类，全局统一
     *
     * @param result
     * @param pageable
     * @param <T>
     * @return
     */
    public static <T> org.springframework.data.domain.Page<T> plusPageToSpringPage(Page<T> result, Pageable pageable) {
        if (result == null) {
            return org.springframework.data.domain.Page.empty();
        }
        return new PageImpl<T>(result.getRecords(), pageable, result.getTotal());
    }

    public static <T> Page<T> listToPage(List<T> result) {
        if (result == null) {
            return new Page<>();
        }
        return new Page<T>(1, result.size()).setRecords(result);
    }

    public static <T> Page<T> listToPage(List<T> result, Pageable pageable) {
        if (result == null || result.size() == 0) {
            return new Page<>();
        }
        int fromIndex = (pageable.getPageNumber() - 1) * pageable.getPageSize();
        if (fromIndex >= result.size()) {
            return new Page<>();
        }

        int toIndex = Math.min(pageable.getPageNumber() * pageable.getPageSize() - 1, result.size() - 1);
        return new Page<T>(pageable.getPageNumber(), pageable.getPageSize()).setRecords(result.subList(fromIndex, toIndex));
    }
}
