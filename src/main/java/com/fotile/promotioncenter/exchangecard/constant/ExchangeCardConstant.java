package com.fotile.promotioncenter.exchangecard.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/11 10:36
 */
public class ExchangeCardConstant {


    /**
     * 企微端排序
     */
    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum QYWXOrderRule{
        ORDER_DEFAULT(0,"默认","(CASE\tecd.user_status \tWHEN 0 THEN\t100 \tWHEN 1 THEN\t80\tWHEN 2 THEN 30 ELSE 1 END ) DESC , ecd.created_date DESC"),
        ORDER_1(1,"按发送时间正序"," ecd.binding_card_date ASC"),
        ORDER_2(2,"按发送时间倒序"," ecd.binding_card_date DESC"),
        ;

        private final Integer code;

        private final String name;

        private final String order;

        QYWXOrderRule(Integer code,String name, String order) {
            this.code = code;
            this.name = name;
            this.order = order;
        }

        public static List<ImmutableMap<String,? extends Serializable>> getOptions(){
            List<ImmutableMap<String, ? extends Serializable>> list = Arrays.stream(QYWXOrderRule.values()).map(r -> ImmutableMap.of("name", r.name, "order", r.getCode())).collect(Collectors.toList());
            return list;
        }

        public static String getOrderRuleSql(Integer code){
           return Arrays.stream(QYWXOrderRule.values()).filter(r->r.getCode().equals(code))
                    .findFirst()
                    .map(QYWXOrderRule::getOrder)
                    .orElse("");

        }
    }

    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum CardUserUseType{
        TYPE_01("01","兑换券"),
        TYPE_02("02","抵用券"),
        TYPE_03("03","折扣券"),
        ;
        private final String type;

        private final String name;

        CardUserUseType(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public static String getName(String type){
            for (CardUserUseType value : CardUserUseType.values()) {
                if(value.getType().equals(type)){
                    return value.getName();
                }
            }
            return null;
        }
    }

    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum CardExchangeType{
        TYPE_01("01","课程"),
        TYPE_02("02","实物商品"),
        TYPE_03("03","服务商品"),
        TYPE_04("04","积分商品"),
        TYPE_05("05","活动"),
        ;


        private final String type;

        private final String name;

        CardExchangeType(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public static String getName(String type){
            for (CardExchangeType value : CardExchangeType.values()) {
                if(value.getType().equals(type)){
                    return value.getName();
                }
            }
            return null;
        }
    }

    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum CardUseStatus{
        STATUS_0("0","未使用"),
        STATUS_1("1","已使用"),
        STATUS_2("2","已作废"),
        ;
        private final String status;

        private final String name;

        CardUseStatus(String status, String name) {
            this.status = status;
            this.name = name;
        }

        public static String getName(String status){
            for (CardUseStatus value : CardUseStatus.values()) {
                if(value.getStatus().equals(status)){
                    return value.getName();
                }
            }
            return null;
        }
    }
}
