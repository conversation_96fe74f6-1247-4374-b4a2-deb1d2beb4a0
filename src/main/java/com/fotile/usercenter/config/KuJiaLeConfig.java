package com.fotile.usercenter.config;

import cn.hutool.extra.spring.SpringUtil;
import com.fotile.usercenter.common.utils.KujialeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PostConstruct;

@Slf4j
@Configuration
@EnableConfigurationProperties({KuJiaLeProperties.class})
public class KuJiaLeConfig {
    @PostConstruct
    public void update() {
        KujialeUtil.setStringRedisTemplate(SpringUtil.getBean(StringRedisTemplate.class));
    }
}
