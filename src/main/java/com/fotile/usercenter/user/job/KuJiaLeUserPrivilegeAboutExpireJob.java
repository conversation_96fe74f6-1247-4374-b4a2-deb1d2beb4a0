package com.fotile.usercenter.user.job;

import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.usercenter.common.pojo.dto.KuJiaLeAccountSearchDTO;
import com.fotile.usercenter.common.pojo.vo.KuJiaLeAccountSearchVO;
import com.fotile.usercenter.common.pojo.vo.KuJiaLeList;
import com.fotile.usercenter.common.utils.KujialeUtil;
import com.fotile.usercenter.user.dao.UserEntityDao;
import com.fotile.usercenter.user.pojo.dto.UserEntityExtend;
import com.fotile.usercenter.utils.SendMsgUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 酷家乐账号特权7天过期
 */
@Component
@Slf4j
public class KuJiaLeUserPrivilegeAboutExpireJob {
    @Autowired
    UserEntityDao userEntityDao;
    @Autowired
    SendMsgUtils sendMsgUtils;

    @XxlJob(value = "KuJiaLeUserPrivilegeAboutExpireJob")
    public ReturnT<String> execute(String s) {
        Long time = null;
        Long endTime = null;
        if (StringUtils.isNotBlank(s)) {
            //计算时间戳 s是天数
            time = System.currentTimeMillis() + (Long.parseLong(s) * 24 * 60 * 60 * 1000);
            endTime  =  System.currentTimeMillis() + ((Long.parseLong(s )+1L)  * 24 * 60 * 60 * 1000);
        } else {
            time = System.currentTimeMillis() + (7L * 24 * 60 * 60 * 1000);
            endTime  =  System.currentTimeMillis() + (8L * 24 * 60 * 60 * 1000);
        }
        List<UserEntityExtend> kuJiaLeUserPrivilegeAboutExpire = userEntityDao.getKuJiaLeUserPrivilegeAboutExpire(time,endTime);
        if (CollectionUtils.isNotEmpty(kuJiaLeUserPrivilegeAboutExpire)) {
            for (UserEntityExtend userEntityExtend : kuJiaLeUserPrivilegeAboutExpire) {
                //处理逻辑
                HashMap<String, Object> map = new HashMap<>();
                map.put("time", "7");
                map.put("email", (MybatisMateConfig.decrypt(userEntityExtend.getPhone()) + "@fotile.com"));

                sendMsgUtils.sendCommonSms(map, Collections.singletonList(MybatisMateConfig.decrypt(userEntityExtend.getPhone())), "app_cloud_kujiale_002");
            }
        }

//        userEntityDao.getKuJiaLeUserPrivilegeAboutExpire(s);
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "KuJiaLeUserEntityUpdate")
    public ReturnT<String> KuJiaLeUserEntityUpdate(String s) {
        Long time = null;

        List<UserEntityExtend> kuJiaLeUserPrivilegeAboutExpire =  userEntityDao.KuJiaLeUserEntity();
        List<UserEntityExtend> updateList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(kuJiaLeUserPrivilegeAboutExpire)) {
            List<String> appUids = kuJiaLeUserPrivilegeAboutExpire.stream().map(UserEntityExtend::getUserEntityId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (appUids.size() < 50) {
                int start = 0;
                int num = 50;
                KuJiaLeAccountSearchDTO kuJiaLeAccountSearchDTO = new KuJiaLeAccountSearchDTO();

                kuJiaLeAccountSearchDTO.setAppUids(appUids);
                kuJiaLeAccountSearchDTO.setStart(start);
                kuJiaLeAccountSearchDTO.setNum(num);
                KuJiaLeList<KuJiaLeAccountSearchVO> kuJiaLeAccountSearchVOKuJiaLeList = KujialeUtil.searchAccountV2(kuJiaLeAccountSearchDTO);
                if (kuJiaLeAccountSearchVOKuJiaLeList != null) {
                    extracted(kuJiaLeAccountSearchVOKuJiaLeList, updateList);
                    while (kuJiaLeAccountSearchVOKuJiaLeList.isHasMore()) {
                        start += num;
                        kuJiaLeAccountSearchDTO.setStart(start);
                        kuJiaLeAccountSearchVOKuJiaLeList = KujialeUtil.searchAccountV2(kuJiaLeAccountSearchDTO);
                        extracted(kuJiaLeAccountSearchVOKuJiaLeList, updateList);
                    }
                }
            }else{
                int appUidSize = appUids.size() / 50;
                int remainder = appUids.size() % 50;
                if (remainder > 0){
                    appUidSize += 1;
                }
                int appUidNum = 50;
                for (int i = 0; i < appUidSize ; i++) {
                    int start = 0;
                    int num = 50;
                    KuJiaLeAccountSearchDTO kuJiaLeAccountSearchDTO = new KuJiaLeAccountSearchDTO();
                    kuJiaLeAccountSearchDTO.setAppUids(appUids.stream().skip((long) i * appUidNum).limit(appUidNum).collect(Collectors.toList()));
                    kuJiaLeAccountSearchDTO.setStart(start);
                    kuJiaLeAccountSearchDTO.setNum(num);

                    KuJiaLeList<KuJiaLeAccountSearchVO> kuJiaLeAccountSearchVOKuJiaLeList = KujialeUtil.searchAccountV2(kuJiaLeAccountSearchDTO);


                    if (kuJiaLeAccountSearchVOKuJiaLeList!= null) {
                        
                        extracted(kuJiaLeAccountSearchVOKuJiaLeList, updateList);
                        while (kuJiaLeAccountSearchVOKuJiaLeList.isHasMore()) {
                            
                            start += num;

                            kuJiaLeAccountSearchVOKuJiaLeList = KujialeUtil.searchAccountV2(kuJiaLeAccountSearchDTO);
                            extracted(kuJiaLeAccountSearchVOKuJiaLeList, updateList);
                        }
                    }
                }
            }
        }
        userEntityDao.updateKujialeUserEntity(updateList);
//        userEntityDao.getKuJiaLeUserPrivilegeAboutExpire(s);
        return ReturnT.SUCCESS;
    }

    private static void extracted(KuJiaLeList<KuJiaLeAccountSearchVO> kuJiaLeAccountSearchVOKuJiaLeList, List<UserEntityExtend> updateList) {
        if (kuJiaLeAccountSearchVOKuJiaLeList != null) {
            List<KuJiaLeAccountSearchVO> result = kuJiaLeAccountSearchVOKuJiaLeList.getResult();
            if (CollectionUtils.isNotEmpty(result)) {
                for (KuJiaLeAccountSearchVO kuJiaLeAccountSearchVO : result) {
                    UserEntityExtend userEntityExtend = new UserEntityExtend();
                    userEntityExtend.setKujialePhone(kuJiaLeAccountSearchVO.getAccount().getEmail());
                    userEntityExtend.setUserEntityId(kuJiaLeAccountSearchVO.getAccount().getAppUid());
                    List<KuJiaLeAccountSearchVO.SiInstancesDTO> siInstances = kuJiaLeAccountSearchVO.getSiInstances();
                    if (CollectionUtils.isNotEmpty(siInstances)) {
                        KuJiaLeAccountSearchVO.SiInstancesDTO siInstancesDTO = siInstances.stream().filter(item -> Objects.equals(item.getSiCode(), "DESIGN_PLATINUM_VIP")
                                || Objects.equals(item.getSiCode(), "BRONZE_ACCOUNT")
                                || Objects.equals(item.getSiCode(), "DIAMOND_VIP")
                        ).findFirst().orElse(null);
                        if (Objects.nonNull(siInstancesDTO)) {
                            userEntityExtend.setKujialeSiInstancesName(siInstancesDTO.getSiName());
                            userEntityExtend.setKujialeSiInstancesEndTime(siInstancesDTO.getEndTime() < siInstancesDTO.getUserBindEndTime() ? siInstancesDTO.getEndTime() : siInstancesDTO.getUserBindEndTime());
                        }
                        updateList.add(userEntityExtend);
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis() + (Long.parseLong("83") * 24 * 60 * 60 * 1000));
        System.out.println( System.currentTimeMillis() + ((Long.parseLong("83")+1L)  * 24 * 60 * 60 * 1000));

    }
}
