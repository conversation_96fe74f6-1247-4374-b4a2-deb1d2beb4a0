package com.fotile.usercenter.user.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fotile.framework.core.common.AuditingEntity;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * 酷家乐账号信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "`usercenter`.`t_kujiale_account_info`")
public class TKujialeAccountInfoEntity extends AuditingEntity {
    /**
     * 酷家乐账号-邮箱
     */
    @TableField(value = "`kujiale_email`")
    private String kujialeEmail;

    /**
     * 酷家乐会员id
     */
    @TableField(value = "`kujiale_member_id`")
    private String kujialeMemberId;

    /**
     * appuid
     */
    @TableField(value = "`kujiale_app_uid`")
    private String kujialeAppUid;

    /**
     * keycloakid
     */
    @TableField(value = "`user_id`",updateStrategy = FieldStrategy.IGNORED)
    private String userId;

    /**
     * 权益名称
     */
    @TableField(value = "`kujiale_member_name`")
    private String kujialeMemberName;

    /**
     * 酷家乐会员编码-对应类型
     */
    @TableField(value = "`kujiale_member_code`")
    private String kujialeMemberCode;

    /**
     * 权益过期时间
     */
    @TableField(value = "`kujiale_member_expiration_time`")
    private Long kujialeMemberExpirationTime;

    /**
     * 方案数量
     */
    @TableField(value = "`kujiale_design_count`")
    private Long kujialeDesignCount;

    /**
     * 门店id
     */
    @TableField(value = "`store_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long storeId;

    /**
     * 分公司id
     */
    @TableField(value = "`company_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long companyId;

    /**
     * 前序业务员id
     */
    @TableField(value = "`preorder_charge_user_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long preorderChargeUserId;

    /**
     * 前序业务员code
     */
    @TableField(value = "`preorder_charge_code`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderChargeCode;

    /**
     * 前序业务员手机号
     */
    @FieldEncrypt
    @TableField(value = "`preorder_charge_phone`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderChargePhone;

    /**
     * 前序业务员名称
     */
    @FieldEncrypt
    @TableField(value = "`preorder_charge_user_name`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderChargeUserName;

    /**
     * 前序分公司id
     */
    @TableField(value = "`preorder_company_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long preorderCompanyId;

    /**
     * 前序分公司名称
     */
    @TableField(value = "`preorder_company_name`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderCompanyName;

    /**
     * 前序门店id
     */
    @TableField(value = "`preorder_store_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long preorderStoreId;

    /**
     * 前序门店名称
     */
    @TableField(value = "`preorder_store_name`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderStoreName;

    /**
     * 门店code
     */
    @TableField(value = "`preorder_store_code`",updateStrategy = FieldStrategy.IGNORED)
    private String preorderStoreCode;

    /**
     * 门店code
     */
    @TableField(value = "`store_code`",updateStrategy = FieldStrategy.IGNORED)
    private String storeCode;

    /**
     * 分公司名称
     */
    @TableField(value = "`company_name`",updateStrategy = FieldStrategy.IGNORED)
    private String companyName;

    /**
     * 门店名称
     */
    @TableField(value = "`store_name`",updateStrategy = FieldStrategy.IGNORED)
    private String storeName;

    /**
     * 业务员id
     */
    @TableField(value = "`charge_user_id`",updateStrategy = FieldStrategy.IGNORED)
    private Long chargeUserId;

    /**
     * 业务员名称
     */
    @FieldEncrypt
    @TableField(value = "`charge_user_name`",updateStrategy = FieldStrategy.IGNORED)
    private String chargeUserName;

    /**
     * 业务员code
     */
    @TableField(value = "`charge_code`",updateStrategy = FieldStrategy.IGNORED)
    private String chargeCode;

    /**
     * 业务员手机号
     */
    @FieldEncrypt
    @TableField(value = "`charge_phone`",updateStrategy = FieldStrategy.IGNORED)
    private String chargePhone;
    @TableField(exist = false)
    private Integer isRenewal;
}