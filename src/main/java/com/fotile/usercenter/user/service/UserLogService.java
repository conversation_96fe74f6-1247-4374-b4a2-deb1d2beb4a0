package com.fotile.usercenter.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.secure.util.AES;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.util.DateUtil;
import com.fotile.usercenter.user.dao.UserLogDao;
import com.fotile.usercenter.user.pojo.dto.GetUserLogListRequestDTO;
import com.fotile.usercenter.user.pojo.dto.LogDto;
import com.fotile.usercenter.user.pojo.dto.UserLogDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-12-01-10:27
 */
@Service
public class UserLogService {

    @Resource
   private  UserLogDao userLogDao;

    public boolean saveLog(LogDto logDto) {
        return this.saveLogs(Arrays.asList(logDto));
    }


    public boolean saveLogs(List<LogDto> list) {
      int result=  userLogDao.saveLogs(list);
      return result>0;
    }

    public PageInfo<LogDto> getLogs(LogDto dto) {
        PageInfo<LogDto> pageInfo = new PageInfo<>(dto.getPageNum(),dto.getPageSize());
        int count = userLogDao.getLogsCount(dto);
        pageInfo.setTotal(count);
        if(count>0){
          List<LogDto> list = userLogDao.getLogs(dto,pageInfo);
            pageInfo.setRecords(list);
        }
        return pageInfo;
    }

    /**
     * 查找账号修改日志记录
     * @return
     */
    public IPage getUserLogList(GetUserLogListRequestDTO requestDTO) {
        Page<Map<String,Object>> page  = new Page<Map<String,Object>>(requestDTO.getPageIndex(),requestDTO.getPageSize());
        Map<String,Object> param = new HashMap<>();
        if(!requestDTO.getSTime().equals("")){
            param.put("sTime", requestDTO.getSTime()+" 00:00:00");
        }
        if(!requestDTO.getETime().equals("")){
            param.put("eTime",requestDTO.getSTime()+ " 23:59:59");
        }
        QueryWrapper logParam = new QueryWrapper();
        logParam.in("operate_type", "素材修改");
        Long integer = userLogDao.selectCount(logParam);

        IPage<UserLogDto> iPage = new Page<>();
        iPage.setTotal(integer);
        iPage.setSize(requestDTO.getPageSize());
        iPage.setCurrent(requestDTO.getPageIndex());
        param.put("offset", (requestDTO.getPageIndex() - 1) * requestDTO.getPageSize());
        param.put("limit", requestDTO.getPageSize());
        iPage.setRecords(userLogDao.getUserLogList(param));
        return iPage;
    }
}
