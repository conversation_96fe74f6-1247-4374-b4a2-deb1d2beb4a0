package com.fotile.usercenter.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fotile.usercenter.user.pojo.dto.GetKujialeAccountInfoListInDto;
import com.fotile.usercenter.user.pojo.dto.KuJiaLePageDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.usercenter.user.pojo.entity.TKujialeAccountInfoEntity;
import java.util.List;
import com.fotile.usercenter.user.dao.TKujialeAccountInfoMapper;
@Service
public class TKujialeAccountInfoService extends ServiceImpl<TKujialeAccountInfoMapper, TKujialeAccountInfoEntity> {

    
    public int updateBatch(List<TKujialeAccountInfoEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TKujialeAccountInfoEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TKujialeAccountInfoEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TKujialeAccountInfoEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TKujialeAccountInfoEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public List<TKujialeAccountInfoEntity> getKujialeAccountInfoList(GetKujialeAccountInfoListInDto inDto) {
        return baseMapper.getKujialeAccountInfoList(inDto);
    }

    public long accountInfoPageCount(KuJiaLePageDTO kuJiaLePageDTO) {
        return baseMapper.accountInfoPageCount(kuJiaLePageDTO);
    }

    public List<TKujialeAccountInfoEntity> accountInfoPage(KuJiaLePageDTO kuJiaLePageDTO) {
        return baseMapper.accountInfoPage(kuJiaLePageDTO);
    }
}
