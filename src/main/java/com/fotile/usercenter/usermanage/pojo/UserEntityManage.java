package com.fotile.usercenter.usermanage.pojo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@Data
public class UserEntityManage {
    private Long id;

    private Long companyId;

    private Long storeId;

    private String station;

    @FieldEncrypt
    private String name;
    @FieldEncrypt
    private String phone;

    private Date entryDate;

    private Long parentId;

    private String jobContent;

    private String abilityDirection;

    private String recruitMethod;

    private String remark;

    private Integer sex;

    private String birthday;

    private Integer education;

    private Integer marital;
    @FieldEncrypt
    private String email;

    private String idCardPortrait;

    private String idCardEmblem;

    private String portrait;

    private String isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;

    private Integer auditStatus;

    /**
     * 申请类型，1入职 2离职 3异动 4重启
     */
    private Integer applyType;

    private Long applySalesmanId;

    private Date applyTime;

    private Long salesmanId;

    private Date leaveDate;

    private String leaveReason1;

    private String leaveReason2;

    private Integer leaveType;

    private Long cluesSalesmanId;

    private Long decorateSalesmanId;

    private Long customerSalesmanId;

    private String changeStation;

    private Long changeStoreId;

    private Long changeParentId;

    /**
     * 异动部门id
     */
    private Long changeDepartId;

    private Long procInstId;

    private Integer cluesCount;

    private Integer decorateCount;

    private Integer customerCount;

    private Integer agreeProtocolFlag;

    private Integer changePhoneStatus;
    /**
     * 省code
     */
    private Long provinceCodes;

    /**
     * 市code
     */
    private Long cityCodes;

    /**
     * 区code
     */
    private Long areaCodes;

    /**
     * 省名称
     */
    private String provinceNames;

    /**
     * 市名称
     */
    private String cityNames;

    /**
     * 区名称
     */
    private String areaNames;

    /**
     * 请假类型 1：事假，2：病假，3：年假，4：婚假，5：产检假，6:陪产假 7：产假，8：哺乳假，9：流产假，10：丧假，11：育儿假，12：工伤假，13：独生子女假
     */
    private Integer vacationType;

    /**
     * 请假开始时间
     */
    private Date vacationStartTime;

    /**
     * 请假结束时间
     */
    private Date vacationEndTime;

    /**
     * 酷家乐迁移业务员id
     */
    private Long kuJiaLeChargeUserId;
    private  Integer kuJiaLeCount;

    /**
     * 协议签名图片url
     */
    private String signatureUrl;
}