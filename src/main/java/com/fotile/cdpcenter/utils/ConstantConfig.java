package com.fotile.cdpcenter.utils;

/**
 * <AUTHOR>
 */
public class ConstantConfig {

    public static final String REDIS_JD_TOKEN_KEY = "REDIS_JD_TOKEN";


    public static final String GET_JD_TOKEN_URL = "https://cdp.efotile.com/openapi/token";

    public static final String CREATE_JD_CUSTOMER_URL = "https://cdp.efotile.com/openapi/identifiers";

    public static final String CREATE_JD_EVENT_URL = "https://cdp.efotile.com/openapi/ude";


    /*********************************CDP 2期*******************************************/

    /**
     * token:redisKey
     */
    public static final String REDIS_CDP_2_JD_TOKEN_KEY = "REDIS_CDP_2_JD_TOKEN_KEY";

    /**
     * get token url
     */
    public static final String CDP_2_JD_TOKEN_URL = "/openapi/token";

    /**
     * concat url
     */
    public static final String CDP_2_JD_MESSAGES_URL = "/openapi/open/messages";

    /**
     *orders url
     */
    public static final String CDP_2_JD_ORDERS_URL = "/openapi/open/messages/orders";

    /**
     * 行为轨迹url
     */
    public static final String CDP_2_JD_CUSTOMER_TRACE_URL = "/openapi/open/fotile/portrait/traces/list";


    /**
     * 基础标签
     */
    public static final String CDP_2_JD_BASE_TAG_URL = "/openapi/open/tags";

    /**
     * 导购标签
     */
    public static final String CDP_2_JD_CUSTOMER_TAG_URL = "/openapi/open/tags/values";

    /**
     * 上传标签至cdp
     */
    public static final String CDP_2_JD_UPLOAD_TAG_URL = "/openapi/open/tags/value";


    /**
     * 创建cdp群组与干法关系
     */
    public static final String CDP_SOP_CREATE_URL = "/openapi/open/fotile/methods";

    /**
     * 查询干法统计数据
     */
    public static final String CDP_SOP_INSIGHT_URL = "/openapi/open/fotile/methods/insight";

    /**
     * 获取cdp群组列表
     */
    public static final String CDP_GET_GROUPS_URL = "/openapi/open/guide_groups";


    /**
     * 获取cdp群组列表
     */
    public static final String CDP_GET_GROUPS_BY_IDS_URL = "/openapi/open/guide_groups/ids";


    /**
     * 根据生效员工ID获得导购群组预估人数
     */
    public static final String CDP_GET_GROUPS_ESTIMATION_URL = "/openapi/open/guide_groups/estimation";



    /**
     * 删除群组
     */
    public static final String CDP_SOP_DELETE_URL = "/openapi/open/fotile/methods";


    /**
     * 创建群发任务
     */
    public static final String CDP_GROUP_MSG_CREATE_URL = "/openapi/open/fotile/mission";



    /**
     * 预估群发人数
     */
    public static final String CDP_GROUP_MSG_ESTIMATION_URL = "/openapi/open/fotile/mission/estimation";


    /**
     * 查看群发统计数据
     */
    public static final String CDP_GROUP_MSG_INSIGHT_URL = "/openapi/open/fotile/insight/push";

    /**
     * 查看内容专题统计数据
     */
    public static final String CDP_SPECIAL_INSIGHT_URL = "/openapi/open/fotile/insight/specialInfo";


    /**
     * 根据userIds查看内容专题数据
     */
    public static final String CDP_SPECIAL_INSIGHT_BY_USERID_URL = "/openapi/open/fotile/insight/specialInfoByUserId";

    /**
     * 获取企微计算任务
     */
    public static final String CDP_SYNC_TASK_URL = "/openapi/open/fotile/mission/sync_send";


    /**
     *error code
     */
    public static final Integer CDP_2_JD_ERROR_CODE = 403;


    public static final Integer SUCCESS_FLAG = 0;

}
