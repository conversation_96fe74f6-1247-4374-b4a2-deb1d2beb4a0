package com.fotile.cdpcenter.oms.job;

import com.fotile.cdpcenter.oms.service.OrderInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderGoodsIdentifyJob extends IJobHandler {

    @Autowired
    private OrderInfoService orderInfoService;


    @Override
    @XxlJob(value = "orderGoodsIdentify2JDJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("同步syncOrderInfo定时任务======start=======");
        orderInfoService.syncOrderIdentify(param);
        XxlJobLogger.log("同步syncOrderInfo定时任务======end=======");
        return ReturnT.SUCCESS;
    }
}
