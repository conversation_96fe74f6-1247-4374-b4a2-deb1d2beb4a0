package com.fotile.cdpcenter.oms.job;

import com.fotile.cdpcenter.oms.service.MembersOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**幸福家VIP订单*/

@Component
@Slf4j
public class MembersOrderJob extends IJobHandler {
    @Autowired
    private MembersOrderService membersOrderService;


    @Override
    @XxlJob(value = "membersOrderJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("同步syncOrderInfo定时任务======start=======");
        membersOrderService.syncMembersOrder(param);
        XxlJobLogger.log("同步syncOrderInfo定时任务======end=======");
        return ReturnT.SUCCESS;
    }
}
