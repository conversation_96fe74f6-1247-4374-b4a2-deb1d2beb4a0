package com.fotile.cdpcenter.client;

import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "ma-center", path = "/api")
public interface SopClient {

    @RequestMapping(value = "/sopMsgCommon/api/getCdpGroupId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<Integer> getCdpGroupId(@RequestParam("cdpGroupId") String cdpGroupId);

    @RequestMapping(value = "/sopMsgCommon/api/getSopEffectChargeUser", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<String>> getSopEffectChargeUser(@RequestParam("sopMainId") Long sopMainId);

}
