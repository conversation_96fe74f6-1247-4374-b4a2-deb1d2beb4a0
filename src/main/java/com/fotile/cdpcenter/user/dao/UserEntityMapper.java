package com.fotile.cdpcenter.user.dao;

import com.fotile.cdpcenter.user.pojo.dto.UserEntityTalentDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserEntityMapper {
    List<UserEntityTalentDTO> getUserTalentList(@Param("list") List<String> userIds, @Param("aesKey") String aesKey);

    UserEntityTalentDTO getUserTalentByUserId(@Param("userId") String userId);
}
