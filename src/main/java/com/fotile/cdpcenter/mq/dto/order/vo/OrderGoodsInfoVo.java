package com.fotile.cdpcenter.mq.dto.order.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderGoodsInfoVo implements Serializable {
    //主键id
    private Integer goodsDeclarationId;
    //订单ID
    private String orderId;
    //商品分类ID
    private Integer goodsCategory;
    //商品ID
    private Integer goodsId;
    //商品名称
    private String goodsName;
    //商品编号
    private String goodsCode;
    //商品单价
    private BigDecimal goodsPrice;
    //商品数量
    private Integer goodsNum;
    //商品价格(总) =商品单价*商品数量
    private BigDecimal goodsTotal;
    //商品排序
    private Integer sort;
    //商品类型
    private String goodsType;
    //商品备注
    private String goodsNote;
    //是否赠品
    private String isGift;
    //物流单号
    private String logisticsNo;
    //物流状态
    private String logisticsStatus;
    //默认储位
    private String locationType;
    //订单状态
    private String orderStatus;
    //guid
    private String guidVarchar;
    //送货时间
    private String deliveryDate;
    //源订单商品的guid
    private String sourceGuid;
    //原商品行id
    private Integer originalGoodId;
    //结算状态
    private String settlementStage;
    //已出库数量
    private Integer quantity;
    //合同价
    private BigDecimal creditPrice;
    //信用价
    private BigDecimal price;
    //结算价
    private BigDecimal productSettlement;
    //商品code
    private String code;
    //线下渠道最低限价
    private BigDecimal lowestPrice;
    //成本估算
    private BigDecimal costEstimate;
    //上下架状态
    private String online;
    //Pos机单品已支付金额
    private BigDecimal posPayAmount;
    //是否智能产品
    private String isSmart;
    //智能商品名称
    private String smartName;
    //手机号，MD5加密
    private String phone;

}