package com.fotile.cdpcenter.mq.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MemberPropsDTO implements Serializable {


    private String grade_id;//	会员等级	String
    private String regist_channel;//	会员来源	String

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String register_time;//	会员注册时间	datetime

    private String customer_type;//	客户类型	String
    private String family_num;//	家庭结构	String
    private String professional_name;//	职业	String
    private Long customer_id;//	customer_id	Bigint
    private String is_staff;//	方太员工	boolean
    private String is_saler;//	导购	boolean
    private String saler_status;//	导购状态	String
    private String is_lead;//	线索	boolean
    private String intention;//	意向产品	String
    private String kitchen_area;//	厨房面积	String
    private String kitchen_type;//	厨房形状	String
    private String points_balance;//	积分余额	Int
    private Integer balance;
    private String is_talent;//	达人	boolean

}
