package com.fotile.cdpcenter.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据同步信息类（mq消息接收类）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CanalRowData implements Serializable {

    /**
     * 字段名
     */
    private String column;

    /**
     * 字段值
     */
    private String value;

    /**
     * 是否主键，注意复合主键
     */
    private boolean isKey;

    /**
     * 是否为空
     */
    private boolean isNull;

    /**
     * 是否该字段更新
     */
    private boolean updated;

    /**
     * 数据类型：int(10) bigint(20) varcahr(20)等
     */
    private String mysqlType;
}
