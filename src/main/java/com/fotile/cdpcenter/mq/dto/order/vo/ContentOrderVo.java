package com.fotile.cdpcenter.mq.dto.order.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ContentOrderVo implements Serializable {

    //来源渠道
    private String sourceChannel;
    //订单号
    private String orderId;
    //用户ID
    private String orderUserId;
    //用户名称
    private String orderUserName;
    //手机号
    private String phone;
    //价格ID
    private String priceId;
    //内容ID/专题ID
    private Integer contentId;
    //内容类型(中台使用)
    private String contentType;
    //标题
    private String title;
    //原价
    private BigDecimal originalPrice;
    //售价
    private BigDecimal sellingPrice;
    //活动价
    private BigDecimal activityPrice;
    //成交价
    private BigDecimal purchasePrice;
    //实付金额
    private BigDecimal paymentPrice;
    //优惠类型
    private String discountType;
    //商品总数
    private Integer totalNum;
    //订单状态
    private String paymentStatus;
    //退款状态
    private String refundSatus;
    //取消原因
    private String cancelReason;
    //IP购买者IP
    private String payIp;
    //支付渠道
    private String paymentType;
    //支付交易号
    private String payOrder;
    //支付时间
    private String payTime;
    //支付状态
    private String payStatus;
    //备注
    private String remark;
    //客服备注
    private String serviceRemarks;
    //内容类型(幸福家使用)
    private String reserveItem;
    //抵扣金额
    private BigDecimal deductionAmount;
    //卷码编码
    private String volumeCodeNumber;
    //卷码内容
    private String volumeCodeContent;
    //卷码类型
    private String volumeCodeType;
    //内容分类ID
    private Integer contentClass;
    //预留字段7
    private String reserveItem7;
    //预留字段8
    private String reserveItem8;
    //预留字段9
    private String reserveItem9;
    //预留字段10
    private String reserveItem10;

}
