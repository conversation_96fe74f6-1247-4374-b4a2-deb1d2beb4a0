package com.fotile.cdpcenter.tag.controller;

import com.fotile.cdpcenter.tag.pojo.dto.BaseTagDTO;
import com.fotile.cdpcenter.tag.pojo.dto.CustomerTagValueDTO;
import com.fotile.cdpcenter.tag.pojo.dto.GetTagInDto;
import com.fotile.cdpcenter.tag.pojo.dto.UploadTagValueDTO;
import com.fotile.cdpcenter.tag.service.TagService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/api/cdpTag")
@RestController
@Api(value = "标签", tags = {"标签"})
public class TagController extends BaseController {


    @Autowired
    private TagService tagService;


    /**
     * 获取cdp基础标签
     * @param type
     * @return
     */
    @RequestMapping(value = "/api/open/getBaseTag", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "场景化方案保存")
    public Result<List<BaseTagDTO>> getBaseTag(@RequestParam(value = "type",required = false) Integer type) {
        if(type == null){
            type = 0 ;
        }
        return success(tagService.getBaseTag(type));


    }




    /**
     * 获取cdp顾客标签
     * @param getTagInDto
     * @return
     */
    @RequestMapping(value = "/api/open/getCustomerTagValues", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "获取cdp顾客标签")
    public Result<List<CustomerTagValueDTO>> getCustomerTagValues(@RequestBody GetTagInDto getTagInDto) {
        if(getTagInDto == null){
            return failure("请传入数据");
        }
        if(StringUtils.isBlank(getTagInDto.getAccountId()) || StringUtils.isBlank(getTagInDto.getExternalUserid()) || StringUtils.isBlank(getTagInDto.getUserId())){
            return failure("请传入必填参数");
        }
        return success(tagService.getCustomerTagValues(getTagInDto));
    }



    /**
     * 上传cdp顾客标签
     * @param uploadTagValueDTO
     * @return
     */
    @RequestMapping(value = "/api/open/uploadTagValues", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "上传cdp顾客标签")
    public Result<List<CustomerTagValueDTO>> uploadTagValues(@RequestBody UploadTagValueDTO uploadTagValueDTO) {
        if(uploadTagValueDTO == null){
            return failure("请传入数据");
        }
        if(StringUtils.isBlank(uploadTagValueDTO.getAccountId()) || StringUtils.isBlank(uploadTagValueDTO.getAccountId()) || StringUtils.isBlank(uploadTagValueDTO.getUserId())){
            return failure("请传入必填参数");
        }
        if(CollectionUtils.isEmpty(uploadTagValueDTO.getTagList())){
            return failure("请传入必填参数");
        }
        return success(tagService.uploadTagValues(uploadTagValueDTO));
    }
}
