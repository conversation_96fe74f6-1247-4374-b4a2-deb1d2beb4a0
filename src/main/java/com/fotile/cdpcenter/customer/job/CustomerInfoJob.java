package com.fotile.cdpcenter.customer.job;


import com.fotile.cdpcenter.customer.service.CustomerInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CustomerInfoJob extends IJobHandler {

    @Autowired
    private CustomerInfoService customerInfoService;


    @Override
    @XxlJob(value = "customerInfoJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("同步customerInfo定时任务=============");
        customerInfoService.syncCustomerInfo(param);
        return ReturnT.SUCCESS;
    }
}
