package com.fotile.ap.client.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryGoodsCategoryByIdsInDTO implements Serializable {

    /**
     * 分类id
     */
    @NotNull(message = "分类id不能为空")
    private List<Long> ids;
}
