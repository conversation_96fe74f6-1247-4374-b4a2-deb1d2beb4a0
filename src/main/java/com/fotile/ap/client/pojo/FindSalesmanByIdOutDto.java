package com.fotile.ap.client.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 业务员
 */
@Data
public class FindSalesmanByIdOutDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务员id
     */
    private Long id;

    /**
     * 业务员名称
     */
    private String name;

    /**
     * 业务员编码
     */
    private String code;

    /**
     * 业务员手机号
     */
    private String phone;

    /**
     * 所属公司id
     */
    private Long companyId;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 所属门店
     */
    private Long storeId;
    /**
     * 所属门店名称
     */
    private String storeName;

    /**
     * 二维码
     */
    private String qrcode;

    /**
     * 门店地址
     */
    private String storeAddress;
}
