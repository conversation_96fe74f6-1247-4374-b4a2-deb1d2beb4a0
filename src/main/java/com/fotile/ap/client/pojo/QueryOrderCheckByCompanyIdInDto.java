package com.fotile.ap.client.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderCheckByCompanyIdInDto {

    @NotNull(message = "公司id不能为空!")
    private Long companyId;

    /**
     * 查询 0：order_check_rule；其他：查询order_supple_check_rule表
     */
    private Integer type;

    /**
     * 是否只查需要校验字段  1：只查需要校验字段；其他：查询所有
     */
    private Integer stage;
}
