package com.fotile.ap.client.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName CommentsOutDto
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/4/8
 * @Version
 **/
@Data
public class CommentsOutDto implements Serializable {
    private Long id;
    private String userName;
    private Date createdDate;
    private  String commentContent;
    private Long  likeCount=0L;
    private List<CommentsOutDto> answerList;
    private List<Pic>picList;
}
