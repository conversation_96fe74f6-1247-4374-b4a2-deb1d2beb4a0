package com.fotile.ap.client.pojo;

import lombok.Data;

import java.util.List;

/**
 * 中台门店排行榜条件DTO类
 */
@Data
public class StoreRankInDTO {

    /**
     * 门店orgid集合：筛选条件-取所属门店的orgId
     */
    private List<Long> storeIdList;

    /**
     * 公司orgid集合：筛选条件-取所属分公司的orgId
     */
    private List<Long> companyIdList;

    /**
     * 大区code集合：筛选条件-取所属大区的areaCode
     */
    private List<String> areaCodeList;

    /**
     * 门店渠道code集合：筛选条件-取门店渠道的code
     */
    private List<String> storeChannelCategoryCodeList;

    /**
     * 【导出使用】查询当前登录人分公司权限是否为全部：null-没有权限 true-全部 false-不是全部
     */
    private Boolean isAllCompanyAuthor;

    /**
     * 【导出使用】当前登录人id
     */
    private String currentUserId;
}
