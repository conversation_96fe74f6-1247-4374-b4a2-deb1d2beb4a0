package com.fotile.ap.client.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetCompanyGoodsListOutDto extends CompanyGoodsMapping {

    /**
     * 商品编号
     */
    private String code;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品型号
     */
    private String modelNum;

    /**
     * 商品类型
     */
    private int type;

    /**
     * 商品分类
     */
    private Long goodsCategoryId;

    /**
     * 所属分公司
     */
    private Long companyId;

    /**
     * 有效期-开始
     */
    private Date startDate;

    /**
     * 有效期-结束
     */
    private Date endDate;

    /**
     * 所属分公司名称
     */
    private String companyName;

    /**
     * 商品图片列表
     */
    private List<GoodsFile> goodsFiles;

    /**
     * 是否赠品 1:赠品 0：非赠品（正品）
     */
    private Long isGife;

    /**
     * 1上架 0下架
     */
    private int online;

    /**
     * 线下渠道最低限价
     */
    private BigDecimal lowestPrice;

    /**
     * 成本估算
     */
    private BigDecimal costEstimate;

}
