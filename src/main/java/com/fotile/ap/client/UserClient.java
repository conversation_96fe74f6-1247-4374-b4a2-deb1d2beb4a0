package com.fotile.ap.client;

import com.fotile.ap.client.pojo.GetRoleNameOutDto;
import com.fotile.ap.client.pojo.QueryAllMenuByRoleNamesOutDto;
import com.fotile.ap.client.pojo.UserEntityExtend;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "user-center")
public interface UserClient {
    //根据业务员id查询账号扩展信息
    @GetMapping("/api/user/findUserEntityExtendBySalesmanId")
    public Result<UserEntityExtend> findUserEntityExtendBySalesmanId(@RequestParam("salesmanId") Long salesmanId);

    //根据账号id查询账号扩展信息
    @GetMapping("/api/user/findUserEntityExtendByUserId")
    public Result<UserEntityExtend> findUserEntityExtendByUserId(@RequestParam("userId") String userId);

    //根据账号id查询账号信息多个
    @GetMapping(value = "/api/user/api/open/findUserEntityExtendByUserIds", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserEntityExtend>> findUserEntityExtendByUserIds(@RequestParam("userIds") List<String> userIds);

    //根据账号id查询账号信息多个
    @PostMapping(value = "/api/user/api/open/findUserEntityExtendByUserIdsPost", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<UserEntityExtend>> findUserEntityExtendByUserIdsPost(@RequestBody List<String> userIds);

    //根据账号id查询用户数据权限
    @GetMapping("/api/user/findUserEntityById")
    public Result<UserAuthor> findUserEntityById(@RequestParam("userId") String userId);

    //根据权限名称查询菜单
    @PostMapping(value = "/api/menu/queryAllMenuByRoleNames", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<QueryAllMenuByRoleNamesOutDto>> queryAllMenuByRoleNames();

    @RequestMapping(value = "/api/user/api/open/findUserEntityForOpenBySalesmanId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<UserAuthor> apiOpenFindUserEntityBySalesmanId(@RequestParam("salesmanId") Long salesmanId);

    //根据门店id查询拥有该门店权限的账号的角色
    @GetMapping("/api/user/api/open/getRoleByStoreId")
    public Result<List<GetRoleNameOutDto>> getRoleByStoreId(@RequestParam("storeId") Long storeId);


    //根据账号id查询用户数据权限
    @RequestMapping(value = "/api/user/api/open/findUserEntityById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<UserAuthor> findUserEntityById2(@RequestParam("userId") String userId);

    //根据员工号查询启用账号扩展信息
    @RequestMapping(value = "/api/open/user/findUserEntityExtendByEnableEmployeeId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<UserEntityExtend> findUserEntityExtendByEnableEmployeeId(@RequestParam("employeeId") String employeeId);

    //根据账号id查询启用账号扩展信息
    @RequestMapping(value = "/api/user/api/open/findUserEntityExtendByUserId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<UserEntityExtend> findUserEntityExtendByUserIdForOpen(@RequestParam("userId") String userId);


}
