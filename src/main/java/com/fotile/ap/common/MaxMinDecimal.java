package com.fotile.ap.common;

import com.fotile.ap.common.constant.CommonConst;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 用于计算归一化的最大最小值类
 *
 * <AUTHOR>
 * @since 2022/9/5 005 15:09
 */
@Data
public class MaxMinDecimal {

    /**
     * 业务类型
     */
    private String code;

    /**
     * 最大值
     */
    private BigDecimal max;

    /**
     * 最小值
     */
    private BigDecimal min;

    public static MaxMinDecimal init(String code) {
        MaxMinDecimal po = new MaxMinDecimal();
        po.setCode(code);
        if (Objects.equals(code, CommonConst.BmMapSiteSearchFieldEnum.CHANNEL.getCode())) {
            po.setMax(new BigDecimal(5));
            po.setMin(new BigDecimal(1));
        } else if (Objects.equals(code, CommonConst.BmMapSiteSearchFieldEnum.CITY_LEVEL.getCode())) {
            po.setMax(new BigDecimal(5));
            po.setMin(new BigDecimal(1));
        } else {
            po.setMax(BigDecimal.ZERO);
            po.setMin(BigDecimal.ZERO);
        }

        return po;
    }
}
