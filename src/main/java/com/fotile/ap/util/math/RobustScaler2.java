package com.fotile.ap.util.math;

import com.fotile.ap.bibattlemap.pojo.dto.BiMssBaseDataDTO;
import com.fotile.ap.bibattlemap.pojo.dto.BiMssBaseDataNormalDTO;
import com.fotile.ap.bibattlemap.pojo.vo.BiBmSourceDataSetVO;
import org.apache.commons.math3.stat.descriptive.rank.Median;
import org.apache.commons.math3.stat.descriptive.rank.Percentile;
import smile.data.DataFrame;
import smile.data.vector.BaseVector;
import smile.data.vector.DoubleVector;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class RobustScaler2 {
    private static final double[] medians =  {0.00000000e+00, 4.70000000e+01, 1.30000000e+01, 1.00000000e+00,
            2.00000000e+00, 0.00000000e+00, 1.00000000e+00, 0.00000000e+00,
            0.00000000e+00, 1.30000000e+01, 1.40000000e+01, 1.90000000e+01,
            1.00000000e+01, 3.00000000e+00, 1.00000000e+00, 8.00000000e+00,
            1.00000000e+00, 7.00000000e+00, 3.00000000e+00, 8.00000000e+00,
            1.00000000e+01, 3.70360000e+02, 4.58350000e+04, 1.87264000e+03,
            4.57217000e+03, 6.74614714e-01, 5.35242795e+02};

    //缩放因子
    private static final double[] iqr = {1.00000000e+00, 5.20000000e+01, 1.60000000e+01, 3.00000000e+00,
            3.00000000e+00, 1.00000000e+00, 1.00000000e+00, 1.00000000e+00,
            1.00000000e+00, 2.00000000e+01, 2.00000000e+01, 4.90000000e+01,
            1.60000000e+01, 4.00000000e+00, 4.00000000e+00, 1.50000000e+01,
            2.00000000e+00, 9.00000000e+00, 6.00000000e+00, 1.40000000e+01,
            1.60000000e+01, 5.46110000e+02, 2.04140000e+04, 4.32368000e+03,
            1.05985700e+04, 2.19508189e-01, 6.70341474e+02};

    public static DataFrame transform(DataFrame df) {
        int n = df.ncols();
        BaseVector[] scaledColumns = new BaseVector[n];

        for (int i = 0; i < n; i++) {
            double[] columnData = df.column(i).toDoubleArray();
            double[] scaledData = new double[columnData.length];

            for (int j = 0; j < columnData.length; j++) {
                scaledData[j] = (columnData[j] - medians[i]) / iqr[i];
            }

            scaledColumns[i] = DoubleVector.of(df.column(i).name(), scaledData);
        }

        return DataFrame.of(scaledColumns);
    }

    public static DataFrame fitTransform(DataFrame df) {
//        fit(df);
        return transform(df);
    }

    public static List<BiMssBaseDataNormalDTO> gen(List<BiMssBaseDataDTO> list){
        List<BiMssBaseDataNormalDTO> resultList = new ArrayList<>();
        double[][] data = new double[list.size()][27];

        for (int i = 0; i < list.size(); i++) {
            BiMssBaseDataDTO vo = list.get(i);
            data[i][0] = vo.getDitie();
            data[i][1] = vo.getZhuzhaiqu();
            data[i][2] = vo.getCaishichang();
            data[i][3] = vo.getHaier();
            data[i][4] = vo.getMedia();
            data[i][5] = vo.getHuadi();
            data[i][6] = vo.getLaoban();
            data[i][7] = vo.getKasadi();
            data[i][8] = vo.getXimenzi();
            data[i][9] = vo.getFczhongjie();
            data[i][10] = vo.getGongjiaozhan();
            data[i][11] = vo.getMeirong();
            data[i][12] = vo.getNaicha();
            data[i][13] = vo.getShangchang();
            data[i][14] = vo.getShequ();
            data[i][15] = vo.getShoujidian();
            data[i][16] = vo.getKaqudao();
            data[i][17] = vo.getXiyidian();
            data[i][18] = vo.getDiandongche();
            data[i][19] = vo.getYaodian();
            data[i][20] = vo.getZuyu();
            data[i][21] = vo.getPopulation().doubleValue();
            data[i][22] = vo.getPerIncome().doubleValue();
            data[i][23] = vo.getSocialTotalSales().doubleValue();
            data[i][24] = vo.getGdp().doubleValue();
            data[i][25] = vo.getCityRate().doubleValue();
            data[i][26] = vo.getPopulationDensity().doubleValue();
        }

        // 创建DataFrame
        //共27个指标,一定要按照这个顺序，业务给的
        DataFrame df = DataFrame.of(data,
                "ditie", "zhuzhaiqu", "caishichang", "haier", "media", "huadi", "laoban", "kasadi", "ximenzi",
                "fczhongjie", "gongjiaozhan", "meirong", "naicha", "shangchang", "shequ", "shoujidian", "kaqudao", "xiyidian", "diandongche", "yaodian", "zuyu",
                "population", "perIncome", "socialTotalSales", "gdp", "cityRate", "populationDensity");
        DataFrame scaledDf = RobustScaler2.fitTransform(df);
//        System.out.println(scaledDf);
        for (int i = 0; i < list.size(); i++) {
            BiMssBaseDataNormalDTO r = new BiMssBaseDataNormalDTO();
            r.setUuid(list.get(i).getUuid());
            r.setLat(list.get(i).getLat());
            r.setLng(list.get(i).getLng());

            BigDecimal ditie = new BigDecimal(scaledDf.get(i, "ditie").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setDitie(ditie);

            BigDecimal zhuzhaiqu = new BigDecimal(scaledDf.get(i, "zhuzhaiqu").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setZhuzhaiqu(zhuzhaiqu);

            BigDecimal caishichang = new BigDecimal(scaledDf.get(i, "caishichang").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setCaishichang(caishichang);

            BigDecimal haier = new BigDecimal(scaledDf.get(i, "haier").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setHaier(haier);

            BigDecimal media = new BigDecimal(scaledDf.get(i, "media").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setMedia(media);

            BigDecimal huadi = new BigDecimal(scaledDf.get(i, "huadi").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setHuadi(huadi);

            BigDecimal laoban = new BigDecimal(scaledDf.get(i, "laoban").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setLaoban(laoban);

            BigDecimal kasadi = new BigDecimal(scaledDf.get(i, "kasadi").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setKasadi(kasadi);

            BigDecimal ximenzi = new BigDecimal(scaledDf.get(i, "ximenzi").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setXimenzi(ximenzi);

            BigDecimal fczhongjie = new BigDecimal(scaledDf.get(i, "fczhongjie").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setFczhongjie(fczhongjie);

            BigDecimal gongjiaozhan = new BigDecimal(scaledDf.get(i, "gongjiaozhan").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setGongjiaozhan(gongjiaozhan);

            BigDecimal meirong = new BigDecimal(scaledDf.get(i, "meirong").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setMeirong(meirong);

            BigDecimal naicha = new BigDecimal(scaledDf.get(i, "naicha").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setNaicha(naicha);

            BigDecimal shangchang = new BigDecimal(scaledDf.get(i, "shangchang").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setShangchang(shangchang);

            BigDecimal shequ = new BigDecimal(scaledDf.get(i, "shequ").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setShequ(shequ);

            BigDecimal shoujidian = new BigDecimal(scaledDf.get(i, "shoujidian").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setShoujidian(shoujidian);

            BigDecimal kaqudao = new BigDecimal(scaledDf.get(i, "kaqudao").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setKaqudao(kaqudao);

            BigDecimal xiyidian = new BigDecimal(scaledDf.get(i, "xiyidian").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setXiyidian(xiyidian);

            BigDecimal diandongche = new BigDecimal(scaledDf.get(i, "diandongche").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setDiandongche(diandongche);

            BigDecimal yaodian = new BigDecimal(scaledDf.get(i, "yaodian").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setYaodian(yaodian);

            BigDecimal zuyu = new BigDecimal(scaledDf.get(i, "zuyu").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setZuyu(zuyu);

            BigDecimal population = new BigDecimal(scaledDf.get(i, "population").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setPopulation(population);

            BigDecimal perIncome = new BigDecimal(scaledDf.get(i, "perIncome").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setPerIncome(perIncome);

            BigDecimal socialTotalSales = new BigDecimal(scaledDf.get(i, "socialTotalSales").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setSocialTotalSales(socialTotalSales);

            BigDecimal gdp = new BigDecimal(scaledDf.get(i, "gdp").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setGdp(gdp);

            BigDecimal cityRate = new BigDecimal(scaledDf.get(i, "cityRate").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setCityRate(cityRate);

            BigDecimal populationDensity = new BigDecimal(scaledDf.get(i, "populationDensity").toString()).setScale(6, RoundingMode.HALF_UP);
            r.setPopulationDensity(populationDensity);

            resultList.add(r);
        }

        return resultList;
    }

    public static void main(String[] args) {
        // 示例数据
        double[][] data = {
                {1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 27.0},
                {28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0, 41.0, 42.0, 43.0,44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0},
                {55.0, 56.0, 57.0, 58.0, 59.0, 60.0, 61.0, 62.0, 63.0, 64.0, 65.0, 66.0, 67.0, 68.0, 69.0, 70.0, 71.0, 72.0, 73.0, 74.0, 75.0, 76.0, 77.0, 78.0, 79.0, 80.0, 81.0},
                {82.0, 83.0, 84.0, 85.0, 86.0, 87.0, 88.0, 89.0, 90.0, 91.0, 92.0, 93.0, 94.0, 95.0, 96.0, 97.0, 98.0, 99.0, 100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0},
                {109.0, 110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0, 120.0, 121.0, 122.0, 123.0, 124.0,125.0, 126.0, 127.0, 128.0, 129.0, 130.0, 131.0, 132.0, 133.0, 134.0, 135.0}
        };

        // 创建DataFrame
        DataFrame df = DataFrame.of(data, "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "AA");

        System.out.println(df.ncols());

        // 创建并应用RobustScaler
        RobustScaler2 scaler = new RobustScaler2();
        DataFrame scaledDf = scaler.fitTransform(df);

        // 打印结果
        System.out.println("Original DataFrame:");
        System.out.println(df);

        System.out.println("Scaled DataFrame:");
        System.out.println(scaledDf);
    }

}
