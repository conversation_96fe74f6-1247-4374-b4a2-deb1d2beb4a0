package com.fotile.ap.util;

public class PhoneUtils {

    /**
     * 手机脱敏（建议使用hutool的DesensitizedUtil.mobilePhone("18049531999")）
     *
     * @param phone 手机号
     * @return 手机脱敏后号码
     */
    public static String getEncryptPhone(String phone) {
        String phoneNum = phone;
        if (phoneNum.length() < 5) {
        } else if (phoneNum.length() >= 5 && phoneNum.length() < 9) {
            String s = phoneNum.substring(phoneNum.length() - 4, phoneNum.length());
            phoneNum = "****" + s;
        } else if (phoneNum.length() >= 9) {
            String s1 = phoneNum.substring(phoneNum.length() - 4, phoneNum.length());
            String s2 = phoneNum.substring(0, 3);
            phoneNum = s2 + "****" + s1;
        }
        return  phoneNum;
    }
}
