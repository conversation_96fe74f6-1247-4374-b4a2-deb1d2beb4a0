package com.fotile.ap.ai.pojo.dto;

import lombok.Data;

/**
 * @ClassName CategoryPropety
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/2/25
 * @Version
 **/
@Data
public class CategoryPropety {
    private Long id;

    /**
     * 分类属性id(原接口查询中台数据库字段)
     */
   private Long  categoryPropetyId;

    /**
     * 分类属性值
     */
    private  String categoryPropetyValue;

    /**
     * 分类属性
     */
    private String propertyName;

    /**
     *商品id(原接口查询中台数据库字段)
     */
    private Long goodsId;

    /**
     * 商品编码(对接客体数据字段，统一使用商品编码为唯一键)
     */
    private String productCode;

    /**
     * 分类属性排序
     */
    private Integer sort;
    /**
     * 属性组名称
     */
    private String propertyGroupName;
}
