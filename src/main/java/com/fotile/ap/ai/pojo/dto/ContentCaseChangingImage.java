package com.fotile.ap.ai.pojo.dto;

import lombok.Data;

import java.util.Date;

/**
    * 案例换装详情图表
    */
@Data
public class ContentCaseChangingImage {
    /**
    * id
    */
    private Long id;

    /**
    * 组ID
    */
    private String groupId;

    /**
    * 排序
    */
    private Long sort;

    /**
    * 换装前
    */
    private String beforeChangingImage;

    /**
    * 换装后
    */
    private String afterChangingImage;

    /**
    * 内容案例id
    */
    private Long contentId;

    /**
    * 是否删除
    */
    private Long isDeleted;

    /**
    * 创建人
    */
    private String createdBy;

    /**
    * 创建日期
    */
    private Date createdDate;

    /**
    * 修改人
    */
    private String modifiedBy;

    /**
    * 修改日期
    */
    private Date modifiedDate;
}