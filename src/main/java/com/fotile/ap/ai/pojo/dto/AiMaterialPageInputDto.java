package com.fotile.ap.ai.pojo.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AiMaterialPageInputDto {
    private Long id;

    private String ids;

    List<Long> idList;
    /**
     * 素材名称
     */
    private String materialName;

    /**
     * 素材文件类型
     */
    private String materialFileType;



    /**
     * 上下架状态，0-下架，1-上架
     */
    private Integer status;

    /**
     * 创建人user_entity_id
     */
    private String createdBy;

    /**
     * 创建开始时间
     */
    private Date createdDateStart;
    /**
     * 创建结束时间
     */
    private Date createdDateEnd;

    private Integer page;

    private Integer size;

    private Long offset;

    private String fileName;

    private Integer start;
}
