package com.fotile.ap.ai.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AiQuestionAnswerFeedbackOutDTO implements Serializable {

    /**
     * AI问答ID
     */
    @ApiModelProperty("AI问答ID")
    private Long id;

    /**
     * 用户反馈类型 0无用 1有用
     */
    @ApiModelProperty("用户反馈类型 0无用 1有用")
    private String customerFeedbackType;

    /**
     * 用户反馈内容
     */
    @ApiModelProperty("用户反馈内容")
    private String customerFeedbackContent;

}
