package com.fotile.ap.ai.pojo.dto;

import com.fotile.ap.client.pojo.AiQuestionResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AiAnswerDTO implements Serializable {
    /**
     * 问题id
     */
    private Long id;

    /**
     * 展示答案
     */
    private String content;

    /**
     * 答案是否有效：暂无法回答
     */
    private boolean  validFlag;

    /**
     * 图片集合
     */
    private List<String> imageList;

    /**
     * 视频集合
     */
    private List<String> videoList;

    /**
     * 关联数据
     */
    private List<AliAnswerReferenceDTO> referenceDTOList;

    /**
     * 拼接中台数据返回
     */

    private AiQuestionResp questionResp;

    /**
     * 文本中返回的引用数据
     */
    private List<Integer> matchReference;

    /**
     * 文件集合
     */
    private List<AliAnswerReferenceDTO> fileList;
}
