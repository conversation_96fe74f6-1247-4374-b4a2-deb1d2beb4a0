package com.fotile.ap.ai.dao;


import com.fotile.ap.ai.pojo.dto.AiMaterialAddOrUpdateInputDto;
import com.fotile.ap.ai.pojo.dto.AiMaterialDetailOutputDto;
import com.fotile.ap.ai.pojo.dto.AiMaterialPageInputDto;
import com.fotile.ap.ai.pojo.dto.AiMaterialPageOutputDto;
import com.fotile.ap.ai.pojo.entity.TAiMaterial;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TAiMaterialDao {
    int deleteByPrimaryKey(Long id);

    int insert(TAiMaterial record);

    int insertSelective(TAiMaterial record);

    TAiMaterial selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TAiMaterial record);

    int updateByPrimaryKey(TAiMaterial record);

    Integer selectAiMaterialPageCount(AiMaterialPageInputDto inDto);

    List<AiMaterialPageOutputDto> selectAiMaterialPageList(AiMaterialPageInputDto inDto);

    AiMaterialDetailOutputDto selectById(Long id);

    int insertAiMaterial(AiMaterialAddOrUpdateInputDto aiMaterialAddOrUpdateInputDto);

    int updateAiMaterial(AiMaterialAddOrUpdateInputDto aiMaterialAddOrUpdateInputDto);

    int updateSyncStatusStart(@Param("id") Long id,@Param("requestParam") String requestParam);

    int updateSyncStatusFailure(@Param("id") Long id,@Param("responseBody") String responseBody);

    int updateSyncStatusSuccess(@Param("id") Long id,@Param("responseBody") String responseBody);



}