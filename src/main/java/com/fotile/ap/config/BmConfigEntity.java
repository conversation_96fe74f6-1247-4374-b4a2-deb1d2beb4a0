package com.fotile.ap.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2021/11/22 18:20
 */
@Component
@Data
public class BmConfigEntity {

    /**
     * 地图key
     */
    @Value("${battlemap.mapKey}")
    private String mapKey;

    /**
     * 地图调用限制次数：一次圈址H3分割中心点列表根据预测值降序前N条
     */
    @Value("${battlemap.hotAddressNum:100}")
    private Integer hotAddressNum;
}
