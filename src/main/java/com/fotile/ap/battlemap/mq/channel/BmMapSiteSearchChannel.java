package com.fotile.ap.battlemap.mq.channel;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.dep.provider.drp.mq.channel
 * @date 2021/8/9 9:41
 */
@Component
public interface BmMapSiteSearchChannel {
    //消费
    String BM_MAP_SITE_SEARCH_INPUT = "bm_map_site_search_input";

    //生产
    String BM_MAP_SITE_SEARCH_OUTPUT = "bm_map_site_search_output";

    /**
     * 发送消息
     */
    @Output(BM_MAP_SITE_SEARCH_OUTPUT)
    MessageChannel send();

    /**
     * 订阅消息
     */
    @Input(BM_MAP_SITE_SEARCH_INPUT)
    SubscribableChannel subscribe();



    /*************************************************************************************************/


    //消费
    String AI_COMPLETION_CHARGE_USER_INPUT = "ai_completion_charge_user_input";

    //生产
    String AI_COMPLETION_CHARGE_USER_OUTPUT = "ai_completion_charge_user_output";

    /**
     * 发送消息
     */
    @Output(AI_COMPLETION_CHARGE_USER_OUTPUT)
    MessageChannel aiCompletionSend();

    /**
     * 订阅消息
     */
    @Input(AI_COMPLETION_CHARGE_USER_INPUT)
    SubscribableChannel aiCompletionSubscribe();

}
