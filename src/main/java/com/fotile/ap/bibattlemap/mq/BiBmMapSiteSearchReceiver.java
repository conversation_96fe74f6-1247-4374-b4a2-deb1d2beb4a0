package com.fotile.ap.bibattlemap.mq;

import com.fotile.ap.bibattlemap.mq.channel.BiBmMapSiteSearchChannel;
import com.fotile.ap.bibattlemap.pojo.entity.BiMapSiteSearch;
import com.fotile.ap.bibattlemap.service.BiBattleMapMqService;
import com.fotile.ap.common.KvLI;
import com.fotile.ap.common.KvSi;
import com.fotile.ap.common.constant.CommonConst;
import com.fotile.ap.util.LogUtils;
import com.fotile.ap.util.RedisUtils;
import com.fotile.ap.util.WebSocketUtils;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 作战地图-门店选址消费
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.ap.battlemap.mq
 * @date 2022/10/25 23:40
 */
@Slf4j
@Service
public class BiBmMapSiteSearchReceiver {

    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    BiBattleMapMqService biBattleMapMqService;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    WebSocketUtils webSocketUtils;

    @StreamListener(BiBmMapSiteSearchChannel.BI_BM_MAP_SITE_SEARCH_INPUT)
    public void receive(Message<BiMapSiteSearch> message) {

        //获取调用者的类名
        String className = new Exception().getStackTrace()[0].getClassName();
        //获取调用者的方法名
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        //消息转换业务对象
        BiMapSiteSearch info = message.getPayload();

        //有则删除的同时进行业务逻辑处理，没有则直接返回，防止kafka重复消费
//        Boolean b = stringRedisTemplate.hasKey(CommonConst.Redis.BM_MAP_SITE_SEARCH + info.getId());
//        if (Boolean.TRUE.equals(b)) {
//            stringRedisTemplate.delete(CommonConst.Redis.BM_MAP_SITE_SEARCH + info.getId());
//        } else {
//            return;
//        }

        String redisKey = CommonConst.Redis.BI_BM_MAP_SITE_SEARCH + info.getId();
        Boolean b = redisUtils.retryRedisConsume(redisKey, 5);
        if (Boolean.TRUE.equals(b)) {
            stringRedisTemplate.delete(redisKey);
        } else {
            return;
        }

        String logErrMsgHead = "门店选址长链接通道通知异常, goEasyChannelCode = " + info.getGoEasyChannelCode() + ", id = " + info.getId();
        //goeasy返回的消息体
        Result<BiMapSiteSearch> r = new Result<>();
        r.setData(info);

        try {
            biBattleMapMqService.mapSiteSearchTask(info);
            r.setSuccess(true);
            webSocketUtils.sendMessage(info.getGoEasyChannelCode(), r, logErrMsgHead);
        } catch (Exception e) {
            //更新任务为失败，记录失败信息
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw, true));
            biBattleMapMqService.mapSiteSearchUpdate(info, CommonConst.MapSiteSearchStatus.FAIL, sw.toString());

            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}",
                    String.format("门店选址计算异常, info = %s, errorMsg = %s", info, sw));

            r.setSuccess(false);
            webSocketUtils.sendMessage(info.getGoEasyChannelCode(), r, logErrMsgHead);
        }
    }

}
