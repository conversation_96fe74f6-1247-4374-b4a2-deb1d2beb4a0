package com.fotile.ap.bibattlemap.dao;

import com.fotile.ap.bibattlemap.pojo.dto.BiDelEnvCompareRecordInDTO;
import com.fotile.ap.bibattlemap.pojo.entity.BiEnvCompareRecord;
import com.fotile.ap.bibattlemap.pojo.vo.BiBmSearchCityRecordVO;
import com.fotile.ap.bibattlemap.pojo.vo.BiEnvCompareRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 零售作战地图Dao类
 *
 * <AUTHOR>
 * @since 2022/9/5 005 17:24
 */
@Mapper
public interface BiEnvCompareDao {

    Integer batchInsertEnvCompareRecordList(@Param("dataList") List<BiEnvCompareRecord> dataList);


    List<BiEnvCompareRecordVO> getEnvCompareRecordList(@Param("currentUserId") String currentUserId);

    Integer delEnvCompareRecord(@Param("dto") BiDelEnvCompareRecordInDTO dto,
                                @Param("currentUserId") String currentUserId);

    List<BiBmSearchCityRecordVO> searchEnvCompareCityRecordRankList(@Param("currentUserId") String currentUserId);
}
