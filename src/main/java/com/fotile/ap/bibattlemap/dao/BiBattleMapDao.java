package com.fotile.ap.bibattlemap.dao;

import com.fotile.ap.bibattlemap.pojo.dto.BiMssCityDataDTO;
import com.fotile.ap.bibattlemap.pojo.dto.BiMssKv;
import com.fotile.ap.bibattlemap.pojo.dto.BiPccDTO;
import com.fotile.ap.bibattlemap.pojo.vo.BiBmSearchCityRecordVO;
import com.fotile.ap.bibattlemap.pojo.vo.BiBmSourceDataSetVO;
import com.fotile.ap.util.geo.dto.GeoCoordUUID;
import com.uber.h3core.util.GeoCoord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 零售作战地图Dao类
 *
 * <AUTHOR>
 * @since 2022/9/5 005 17:24
 */
@Mapper
public interface BiBattleMapDao {

    BiMssCityDataDTO getSourceCityDataByCityId(@Param("cityId") Long cityId);

    BiPccDTO getPccByPccId(@Param("level") Integer level, @Param("pccId") Long pccId);

    BiPccDTO getPccOfAreaByPccId(@Param("level") Integer level, @Param("pccId") Long pccId);

    List<BiMssKv> listSourceBizDataGroupByType(@Param("gc") GeoCoordUUID gc,
                                               @Param("pccDTO") BiPccDTO pccDTO,
                                               @Param("distance") Integer distance);

    List<BiBmSearchCityRecordVO> searchCityRecordRankList(@Param("currentUserId") String currentUserId);

    List<BiBmSourceDataSetVO> loadSourceDatasetList();

    List<GeoCoord> listFotileGeoFromSourceBizDataByCityId(@Param("cityId") Long cityId);
}
