package com.fotile.macenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.macenter.rule.dao.TRuleMatchLogMapper;
import com.fotile.macenter.rule.pojo.entity.TRuleMatchLog;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class TRuleMatchLogService extends ServiceImpl<TRuleMatchLogMapper, TRuleMatchLog> {

    
    public int updateBatch(List<TRuleMatchLog> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TRuleMatchLog> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TRuleMatchLog> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TRuleMatchLog record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TRuleMatchLog record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
