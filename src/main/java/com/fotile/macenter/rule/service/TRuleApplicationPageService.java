package com.fotile.macenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.macenter.rule.dao.TRuleApplicationPageMapper;
import com.fotile.macenter.rule.pojo.entity.TRuleApplicationPage;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class TRuleApplicationPageService extends ServiceImpl<TRuleApplicationPageMapper, TRuleApplicationPage> {

    
    public int updateBatch(List<TRuleApplicationPage> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TRuleApplicationPage> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TRuleApplicationPage> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TRuleApplicationPage record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TRuleApplicationPage record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
