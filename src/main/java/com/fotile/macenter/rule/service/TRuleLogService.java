package com.fotile.macenter.rule.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.web.Result;

import com.fotile.macenter.rule.client.MarketingClient;
import com.fotile.macenter.rule.client.pojo.FollowServerFiledIdVO;
import com.fotile.macenter.rule.dao.RuleValueAttributeMapper;
import com.fotile.macenter.rule.dao.TRuleLogMapper;
import com.fotile.macenter.rule.dao.TVariableInfoDao;
import com.fotile.macenter.rule.pojo.dto.QueryRuleLogDTO;
import com.fotile.macenter.rule.pojo.dto.RuleValueAttribute;
import com.fotile.macenter.rule.pojo.dto.SaveOrUpdateRuleDTO;
import com.fotile.macenter.rule.pojo.entity.TRuleConEntity;
import com.fotile.macenter.rule.pojo.entity.TRuleLogEntity;
import com.fotile.macenter.rule.pojo.entity.TVariableInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class TRuleLogService extends ServiceImpl<TRuleLogMapper, TRuleLogEntity> {
    @Autowired
    private TVariableInfoDao tVariableInfoDao;
    @Autowired
    RuleValueAttributeMapper ruleValueAttributeMapper;
    @Autowired
    MarketingClient marketingClient;
    public int updateBatch(List<TRuleLogEntity> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<TRuleLogEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<TRuleLogEntity> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(TRuleLogEntity record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(TRuleLogEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public PageInfo<TRuleLogEntity> queryRuleLog(QueryRuleLogDTO queryRuleLogDTO) {
        PageInfo<TRuleLogEntity> pageInfo = new PageInfo<>(queryRuleLogDTO.getPageNum(), queryRuleLogDTO.getPageSize());

        Long aLong = baseMapper.selectCount(Wrappers.lambdaQuery(TRuleLogEntity.class)
                .eq(TRuleLogEntity::getIsDeleted, 0)
                .eq(TRuleLogEntity::getRuleId, queryRuleLogDTO.getRuleId()));
        pageInfo.setTotal(aLong);
        if (aLong > 0L) {
            List<TRuleLogEntity> tRuleLogEntities = baseMapper.selectList(Wrappers.lambdaQuery(TRuleLogEntity.class).eq(TRuleLogEntity::getIsDeleted, 0)
                    .eq(TRuleLogEntity::getRuleId, queryRuleLogDTO.getRuleId())
                    .last("limit " + pageInfo.getOffset() + "," + pageInfo.getSize())
                    .orderByDesc(TRuleLogEntity::getCreatedDate)
            );
            pageInfo.setRecords(tRuleLogEntities);
        }

        return pageInfo;
    }


    public void saveLog(SaveOrUpdateRuleDTO saveOrUpdateRuleDTO, UserAuthor userAuthor) {

        //新增数据
        TRuleLogEntity tRuleLogEntity = new TRuleLogEntity();
        tRuleLogEntity.setRuleId(saveOrUpdateRuleDTO.getId());
        tRuleLogEntity.setOperatorName(StringUtils.isEmpty(userAuthor.getFirstName()) ? userAuthor.getUsername() : userAuthor.getFirstName());
        tRuleLogEntity.setCreatedBy(userAuthor.getUserId());
        tRuleLogEntity.setModifiedBy(userAuthor.getUserId());
        tRuleLogEntity.setModifiedDate(new Date());
        tRuleLogEntity.setCreatedDate(new Date());
        tRuleLogEntity.setIsDeleted(0L);
        String description = "";
        //干法名称
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getRuleName())) {
            description += "干法名称:".concat(saveOrUpdateRuleDTO.getRuleName()).concat("\n");
        }
        //干法类型
        if (saveOrUpdateRuleDTO.getRuleCategory() > 0) {
            description += "干法分类:".concat(saveOrUpdateRuleDTO.getRuleCategory() == 1 ? "顾客交互" : "内部协调").concat("\n");
        }
        description += "状态:".concat(saveOrUpdateRuleDTO.getRuleStatus() == 0L ? "禁用" : "启用").concat("\n");

        //业务员消息-业务员提醒内容文本
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getTemplateContent())) {
            description += "业务员提醒内容文本:".concat(saveOrUpdateRuleDTO.getTemplateContent()).concat("\n");
        }
        //业务员消息-业务员跳转类型
        if (saveOrUpdateRuleDTO.getJumpType() != null) {
            Integer jumpType = saveOrUpdateRuleDTO.getJumpType();
            String jumpTypeValue = "";
            // H5 5  内容 12   活动 13 应用内页面 2
            if (jumpType == 1) {
                jumpTypeValue = "富文本";
            } else if (jumpType == 2) {
                jumpTypeValue = "应用内页面";
            } else if (jumpType == 5) {
                jumpTypeValue = "H5";
            } else if (jumpType == 12) {
                jumpTypeValue = "内容";
            } else if (jumpType == 13) {
                jumpTypeValue = "活动";
            }
            description += "干法内容链接：有" + "\n";
            //业务员消息-业务员跳转文本
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getJumpTitle())) {
                description += "业务员-超链文案:".concat(saveOrUpdateRuleDTO.getJumpTitle()).concat("\n");
            }
            description += "业务员-跳转类型:".concat(jumpTypeValue).concat("\n");
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getJumpUrl())) {
                if (saveOrUpdateRuleDTO.getJumpUrl().equals("schedule_manager")) {
                    description += "业务员-跳转链接:".concat("日程管理").concat("\n");
                }else if (saveOrUpdateRuleDTO.getJumpUrl().equals("check_out_message")) {
                    description += "业务员-跳转链接:".concat("离店短信").concat("\n");

                }else{
                    description += "业务员-跳转链接".concat(saveOrUpdateRuleDTO.getJumpUrl()).concat("\n");

                }
            }

        } else {
            if (StringUtils.isEmpty(saveOrUpdateRuleDTO.getEditorAttributeValue())) {
                description += "干法内容链接：无" + "\n";
            }
        }
        //获取值集合
        List<RuleValueAttribute> ruleValueAttributes = ruleValueAttributeMapper.selectList();
        //子模板
        if (CollectionUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList())) {
            description += "顾客消息:".concat("有") + "\n";
            for (int j = 0; j < saveOrUpdateRuleDTO.getChildTemplateInfoList().size(); j++) {
                String jumpTypeValue = "";
                Integer jumpType = saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJumpType();
//                { id: 11, name: '富文本' },
//                { id: 7, name: '小程序' },
//                { id: 10, name: '图片' },
//                { id: 9, name: '视频' },
//                { id: 12, name: '链接' },
//                // { id: 8, name: "视频号" },
//                // { id: 1, name: "内容" },
//                // { id: 2, name: "专题" },
//                // { id: 3, name: "活动" },
//                // { id: 4, name: "内部H5" },
//                { id: 5, name: '外部H5' },
//                // { id: 6, name: "弹框" },
                if (jumpType == 11) {
                    jumpTypeValue = "富文本";
                } else if (jumpType == 7) {
                    jumpTypeValue = "小程序";
                } else if (jumpType == 10) {
                    jumpTypeValue = "图片";
                } else if (jumpType == 9) {
                    jumpTypeValue = "视频";
                } else if (jumpType == 12) {
                    jumpTypeValue = "链接";
                } else if (jumpType == 8) {
                    jumpTypeValue = "视频号";
                } else if (jumpType == 1) {
                    jumpTypeValue = "内容";
                } else if (jumpType == 2) {
                    jumpTypeValue = "专题";
                } else if (jumpType == 3) {
                    jumpTypeValue = "活动";
                } else if (jumpType == 4) {
                    jumpTypeValue = "内部H5";
                } else if (jumpType == 5) {
                    jumpTypeValue = "外部H5";
                }

                description += "顾客消息ID:"
                        .concat(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getId() != null ?saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getId().toString() :"").concat(":").concat(",消息类型：")
                        .concat(jumpTypeValue)
                        .concat(",消息内容:")
                        .concat(StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getContentTemplate())
                                ? saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getContentTemplate() : StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getCoverPic())? saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getCoverPic():"")
                ;
                if (StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJumpTitle())) {
                    description =  description.concat(",链接标题:")
                            .concat(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJumpTitle());
                }
                if (StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJumpDescribe())) {
                    description =  description.concat(",链接描述:")
                            .concat(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJumpDescribe());
                }
                if (StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJump())) {
                    AtomicReference<String> jump = new AtomicReference<>(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getJump());
                    Optional<RuleValueAttribute> miniProgramPath = ruleValueAttributes.stream().filter(ruleValueAttribute ->
                                    ruleValueAttribute.getAttributeId().equals(jump.get())
                                            && ruleValueAttribute.getFieldId().equals("miniProgramPath"))
                            .findFirst();
                    if (miniProgramPath.isPresent()) {
                        jump.set(miniProgramPath.get().getAttributeValue());
                        description = description.concat(",页面路径:")
                                .concat(jump.get());
                    } else {
                        description = description.concat(",跳转链接:")
                                .concat(jump.get());
                    }

                }
                if (StringUtils.isNotEmpty(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getParam())) {
                    Integer paramType = saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getParamType();
                    String paramTypeValue = "";
                    if (paramType == 1) {
                        paramTypeValue = "根据“产品品类”匹配内容";
                    } else if (paramType == 2) {
                        paramTypeValue = "根据“产品型号”匹配内容";
                    }
                    description =  description.concat(",参数匹配关系:")
                            .concat("类型")
                            .concat(paramTypeValue).concat(",参数:").concat(saveOrUpdateRuleDTO.getChildTemplateInfoList().get(j).getParam());
                }
                description = description.concat("\n");
            }
        } else {
            if (StringUtils.isEmpty(saveOrUpdateRuleDTO.getEditorAttributeValue())) {
                description += "顾客消息:".concat("无").concat("\n");
            }
        }
        //记录日志
//        description += "规则名称:".concat(saveOrUpdateRuleDTO.getRuleName());
        //优先级
        if (saveOrUpdateRuleDTO.getPriorityLevel() != null) {
            description += "优先级:".concat(saveOrUpdateRuleDTO.getPriorityLevel() == null ? "0" : saveOrUpdateRuleDTO.getPriorityLevel().toString()).concat("\n");
        }



        //获取字段名称

        List<TVariableInfo> tVariableInfos = tVariableInfoDao.selectList(Wrappers.lambdaQuery(TVariableInfo.class).eq(TVariableInfo::getIsDeleted, 0));

        //记录条件
        if (CollectionUtils.isNotEmpty(saveOrUpdateRuleDTO.getRuleConList())) {
            int k = 1;
            for (TRuleConEntity tRuleConEntity : saveOrUpdateRuleDTO.getRuleConList()) {

                //变量判断符号 1. = 2. > 3. < 4.!= 5.like 6.非空 7.为空
                Integer symbol = tRuleConEntity.getSymbol();
                String symbolStr = "";
                if (symbol == 1) {
                    symbolStr = "=";
                } else if (symbol == 2) {
                    symbolStr = ">";
                } else if (symbol == 3) {
                    symbolStr = "<";
                }else if (symbol == 4) {
                    symbolStr = "!=";
                }else if (symbol == 5) {
                    symbolStr = "like";
                }else if (symbol == 6) {
                    symbolStr = "非空";
                }else if (symbol == 7) {
                    symbolStr = "为空";
                }
                /**
                 * 类别 1.字段 2.函数
                 */
                Integer category = tRuleConEntity.getCategory();
                String categoryStr = "";
                AtomicReference<String> var =  new AtomicReference<>(tRuleConEntity.getVariableValue());
                if (category != null) {
                    if (category == 1) {

                        if (symbol == 6) {
                        } else if (symbol == 7) {
                        } else {
                            categoryStr = "固定值";
                        }
                    } else if (category == 2) {
                        categoryStr = "条件字段";
                        tVariableInfos.stream().filter(tVariableInfo -> tVariableInfo.getFiledName().equals(tRuleConEntity.getOperator())).findFirst().ifPresent(tVariableInfo -> {
                            var.set(tVariableInfo.getName());
                        });
                    }
                }
                //变量名
                AtomicReference<String> operator = new AtomicReference<>(tRuleConEntity.getOperator());
                tVariableInfos.stream().filter( tVariableInfo -> tVariableInfo.getFiledName().equals(tRuleConEntity.getOperator())  ).findFirst().ifPresent(tVariableInfo -> {
                    operator.set(tVariableInfo.getName());
                });

                //值
                AtomicReference<String> variableValueDesc = new AtomicReference<>(tRuleConEntity.getVariableValueDesc() );

                if (StringUtils.equals(tRuleConEntity.getOperator(),"serviceAction")) {
                    Result<List<FollowServerFiledIdVO>> followServerList = marketingClient.getFollowServerList();
                    if (followServerList != null && followServerList.getData() != null) {
                        List<FollowServerFiledIdVO> data = followServerList.getData();
                        Optional<FollowServerFiledIdVO> first = data.stream().filter(followServerFiledIdVO -> followServerFiledIdVO.getAttributeId().equals(tRuleConEntity.getVariableValue())).findFirst();
                        first.ifPresent(followServerFiledIdVO -> variableValueDesc.set(followServerFiledIdVO.getAttributeValue()));
                    }
                }
                if (org.apache.commons.lang3.StringUtils.isEmpty(variableValueDesc.get())) {
                    if (StringUtils.isNotEmpty(tRuleConEntity.getVariableValue())) {
                        ruleValueAttributes.stream().filter(ruleValueAttribute ->
                                        ruleValueAttribute.getAttributeId().equals(tRuleConEntity.getVariableValue())
                                                && ruleValueAttribute.getFieldId().equals(tRuleConEntity.getOperator()))
                                .findFirst().ifPresent(ruleValueAttribute -> {
                        variableValueDesc.set(ruleValueAttribute.getAttributeValue());
                                });
                        if (StringUtils.isEmpty(variableValueDesc.get())) {
                            variableValueDesc.set(tRuleConEntity.getVariableValue());
                        }
                    }
                }
                if (category != null && category == 1) {
                    String concat = "行号".concat(String.valueOf(k)).concat(",条件字段:").concat(operator.get());
                    if (StringUtils.isNotEmpty(symbolStr)) {
                        concat = concat.concat(",比较符:").concat(symbolStr);
                    }
                    if (StringUtils.isNotEmpty(categoryStr)) {
                        concat = concat.concat(",值类型:").concat(categoryStr);
                    }
                    if (StringUtils.isNotEmpty(variableValueDesc.get())) {
                        concat = concat.concat(",值:").concat(variableValueDesc.get());
                    }
                    concat = concat.concat("\n");
                    description += concat;
                } else {
                    String concat = "行号".concat(String.valueOf(k)).concat(",条件字段:").concat(operator.get());
                    if (StringUtils.isNotEmpty(symbolStr)) {
                        concat = concat.concat(",比较符:").concat(symbolStr);
                    }
                    if (StringUtils.isNotEmpty(categoryStr)) {
                        concat = concat.concat(",值类型:").concat(categoryStr);
                    }
                    if (StringUtils.isNotEmpty(var.get())) {
                        concat = concat.concat(",值:").concat(var.get());
                    }
                    concat = concat.concat("\n");
                    description += concat;
                }
                k++;
            }
            description += "条件表达式:".concat(saveOrUpdateRuleDTO.getExpression());
            //触发时间
            if (saveOrUpdateRuleDTO.getRemindMechanismType() != null) {
                Integer remindMechanismType = saveOrUpdateRuleDTO.getRemindMechanismType();
                String remindMechanismTypeStr = "";
                if (remindMechanismType == 0) {
                    remindMechanismTypeStr = "实时";

                } else if (remindMechanismType == 1) {
                    remindMechanismTypeStr = "分";
                    remindMechanismTypeStr.concat(saveOrUpdateRuleDTO.getRemindMechanismMinutes().toString());
                } else if (remindMechanismType == 2) {
                    remindMechanismTypeStr = "小时";
                    remindMechanismTypeStr.concat(saveOrUpdateRuleDTO.getRemindMechanismHours().toString());
                } else if (remindMechanismType == 3) {
                    remindMechanismTypeStr = "天";
                    remindMechanismTypeStr.concat(saveOrUpdateRuleDTO.getRemindMechanismDay().toString()).concat("日").concat(saveOrUpdateRuleDTO.getRemindMechanismDayTime().format(java.time.format.DateTimeFormatter.ofPattern("HH::mm::ss")));
                }
                description += "\n" +"触发时间:".concat(remindMechanismTypeStr);
            }
            //触发频率
            if (saveOrUpdateRuleDTO.getFrequencyType() != null) {
                Integer remindFrequency = saveOrUpdateRuleDTO.getFrequencyType();
                String remindFrequencyStr = "";
                if (remindFrequency == 1) {
                    remindFrequencyStr = "每次";
                } else if (remindFrequency == 2) {
                    remindFrequencyStr = "首次";
                }
                //去重时间
                Integer frequencyHour = saveOrUpdateRuleDTO.getFrequencyHour();
                if (frequencyHour != null) {
                    remindFrequencyStr = remindFrequencyStr.concat("去重时限:").concat(frequencyHour.toString()).concat("小时");
                }
                //排斥组
                String excludeGroup = saveOrUpdateRuleDTO.getFrequencyGroup();
                if (StringUtils.isNotEmpty(excludeGroup)) {
                    remindFrequencyStr = remindFrequencyStr.concat("排斥组:").concat(excludeGroup);
                }

                description += "\n" + "触发频次:".concat(remindFrequencyStr);
            }
        }


        tRuleLogEntity.setDescription(description);
        baseMapper.insert(tRuleLogEntity);


    }
}
