package com.fotile.macenter.rule.service;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;

import com.fotile.macenter.rule.dao.TTemplateInfoDao;
import com.fotile.macenter.rule.pojo.dto.QueryRuleInfoByApplicationCodeAndTemplateTypeBO;
import com.fotile.macenter.rule.pojo.entity.TTemplateInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 规则模板表(TTemplateInfo)表服务实现类
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "fifth")
public class TTemplateInfoService {
    @Resource
    private TTemplateInfoDao tTemplateInfoDao;

    /**
     * 通过ID查询单条数据
     */
    public TTemplateInfo queryById(Long id) {
        return this.tTemplateInfoDao.queryById(id);
    }

    /**
     * 查询多条数据
     */
    public List<TTemplateInfo> queryAllByLimit(int offset, int limit) {
        return this.tTemplateInfoDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     */
    public TTemplateInfo insert(TTemplateInfo tTemplateInfo) {
        this.tTemplateInfoDao.insertOne(tTemplateInfo);
        return tTemplateInfo;
    }

    /**
     * 修改数据
     */
    public TTemplateInfo update(TTemplateInfo tTemplateInfo) {
        this.tTemplateInfoDao.update(tTemplateInfo);
        return this.queryById(tTemplateInfo.getId());
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.tTemplateInfoDao.deleteById(id) > 0;
    }

    /**
     * 获取模版
     */
    public List<TTemplateInfo> queryRuleInfoByApplicationCodeAndTemplateType(QueryRuleInfoByApplicationCodeAndTemplateTypeBO queryRuleInfoByApplicationCodeAndTemplateTypeBO) {
        //获取Info表数据

        return tTemplateInfoDao.queryRuleInfoByApplicationCodeAndTemplateType(queryRuleInfoByApplicationCodeAndTemplateTypeBO);
    }
}
