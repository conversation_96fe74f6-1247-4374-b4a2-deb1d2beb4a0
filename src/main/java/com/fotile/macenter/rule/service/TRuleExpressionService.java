package com.fotile.macenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.macenter.rule.dao.TRuleExpressionMapper;
import com.fotile.macenter.rule.pojo.entity.TRuleExpressionEntity;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class TRuleExpressionService extends ServiceImpl<TRuleExpressionMapper, TRuleExpressionEntity> {

    
    public int updateBatch(List<TRuleExpressionEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TRuleExpressionEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TRuleExpressionEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TRuleExpressionEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TRuleExpressionEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
