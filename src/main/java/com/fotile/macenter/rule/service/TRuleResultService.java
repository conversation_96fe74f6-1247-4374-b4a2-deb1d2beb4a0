package com.fotile.macenter.rule.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.fotile.macenter.rule.dao.TRuleResultDao;
import com.fotile.macenter.rule.pojo.entity.TRuleResultEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TRuleResultService extends ServiceImpl<TRuleResultDao, TRuleResultEntity> {


    public int updateBatch(List<TRuleResultEntity> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<TRuleResultEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<TRuleResultEntity> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(TRuleResultEntity record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(TRuleResultEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}

