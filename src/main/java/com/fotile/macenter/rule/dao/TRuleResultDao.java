package com.fotile.macenter.rule.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.macenter.rule.pojo.entity.TRuleResultEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TRuleResultDao extends BaseMapper<TRuleResultEntity> {
    int updateBatch(List<TRuleResultEntity> list);

    int updateBatchSelective(List<TRuleResultEntity> list);

    int batchInsert(@Param("list") List<TRuleResultEntity> list);

    int insertOrUpdate(TRuleResultEntity record);

    int insertOrUpdateSelective(TRuleResultEntity record);
}