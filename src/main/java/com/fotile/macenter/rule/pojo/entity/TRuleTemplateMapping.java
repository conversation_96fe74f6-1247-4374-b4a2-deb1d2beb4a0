package com.fotile.macenter.rule.pojo.entity;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * 规则-模板关联表(TRuleTemplateMapping)实体类
 *
 * <AUTHOR>
 * @since 2020-10-19 18:41:39
 */
@Data
public class TRuleTemplateMapping implements Serializable {
    private static final long serialVersionUID = -14887567446880947L;

    private Long id;

    private Long isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;
    /**
     * 修改人username
     */
    @FieldEncrypt
    private String reviseName;
    /**
     * 最后修改时间
     */
    private Date reviseTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 排序
     */
    private Long sort;


}