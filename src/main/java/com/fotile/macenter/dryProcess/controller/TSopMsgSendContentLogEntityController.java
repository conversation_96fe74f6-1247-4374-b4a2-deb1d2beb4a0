package com.fotile.macenter.dryProcess.controller;
import com.fotile.macenter.dryProcess.pojo.entity.TSopMsgSendContentLogEntity;
import com.fotile.macenter.dryProcess.service.TSopMsgSendContentLogService;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

/**
* 干法发送消息数据(t_sop_msg_send_content_log)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/t_sop_msg_send_content_log")
public class TSopMsgSendContentLogEntityController {
/**
* 服务对象
*/
    @Autowired
    private TSopMsgSendContentLogService tSopMsgSendContentLogService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public TSopMsgSendContentLogEntity selectOne(Integer id) {
    return tSopMsgSendContentLogService.getBaseMapper().selectById(id);
    }

}
