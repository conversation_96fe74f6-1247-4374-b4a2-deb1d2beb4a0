package com.fotile.macenter.dryProcess.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.macenter.dryProcess.pojo.entity.TQywxWelcomeStoreMappingEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
@DruidTxcMultiDataSourceAnnontation("first")
public interface TQywxWelcomeStoreMappingMapper extends BaseMapper<TQywxWelcomeStoreMappingEntity> {
    int insertSelective(TQywxWelcomeStoreMappingEntity record);

    int updateByPrimaryKeySelective(TQywxWelcomeStoreMappingEntity record);

    int updateBatch(List<TQywxWelcomeStoreMappingEntity> list);

    int updateBatchSelective(List<TQywxWelcomeStoreMappingEntity> list);

    int batchInsert(@Param("list") List<TQywxWelcomeStoreMappingEntity> list);

    int insertOrUpdate(TQywxWelcomeStoreMappingEntity record);

    int insertOrUpdateSelective(TQywxWelcomeStoreMappingEntity record);

    List<TQywxWelcomeStoreMappingEntity> queryByMainId(@Param("welcomeMainId") Long welcomeMainId, @Param("sourceType") Integer sourceType);
}