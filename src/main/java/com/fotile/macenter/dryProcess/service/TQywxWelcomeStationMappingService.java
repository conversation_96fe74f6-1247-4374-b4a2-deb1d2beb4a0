package com.fotile.macenter.dryProcess.service;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.macenter.dryProcess.dao.TQywxWelcomeStationMappingMapper;
import com.fotile.macenter.dryProcess.pojo.entity.TQywxWelcomeStationMappingEntity;
@Service
@DruidTxcMultiDataSourceAnnontation("first")
public class TQywxWelcomeStationMappingService extends ServiceImpl<TQywxWelcomeStationMappingMapper, TQywxWelcomeStationMappingEntity> {

    
    public int insertSelective(TQywxWelcomeStationMappingEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(TQywxWelcomeStationMappingEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateBatch(List<TQywxWelcomeStationMappingEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TQywxWelcomeStationMappingEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TQywxWelcomeStationMappingEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TQywxWelcomeStationMappingEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TQywxWelcomeStationMappingEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
