package com.fotile.macenter.dryProcess.service;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.macenter.dryProcess.pojo.entity.TSopCustomerContentConfigEntity;
import com.fotile.macenter.dryProcess.dao.TSopCustomerContentConfigMapper;
@Service
@DruidTxcMultiDataSourceAnnontation("first")
public class TSopCustomerContentConfigService extends ServiceImpl<TSopCustomerContentConfigMapper, TSopCustomerContentConfigEntity> {

    
    public int insertSelective(TSopCustomerContentConfigEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(TSopCustomerContentConfigEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateBatch(List<TSopCustomerContentConfigEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TSopCustomerContentConfigEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TSopCustomerContentConfigEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TSopCustomerContentConfigEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TSopCustomerContentConfigEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
