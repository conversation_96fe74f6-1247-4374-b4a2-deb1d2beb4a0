package com.fotile.macenter.dryProcess.service;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.macenter.dryProcess.pojo.entity.TQywxWelcomeStoreMappingEntity;
import com.fotile.macenter.dryProcess.dao.TQywxWelcomeStoreMappingMapper;
@Service
@DruidTxcMultiDataSourceAnnontation("first")
public class TQywxWelcomeStoreMappingService extends ServiceImpl<TQywxWelcomeStoreMappingMapper, TQywxWelcomeStoreMappingEntity> {

    
    public int insertSelective(TQywxWelcomeStoreMappingEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(TQywxWelcomeStoreMappingEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateBatch(List<TQywxWelcomeStoreMappingEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<TQywxWelcomeStoreMappingEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<TQywxWelcomeStoreMappingEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int insertOrUpdate(TQywxWelcomeStoreMappingEntity record) {
        return baseMapper.insertOrUpdate(record);
    }
    
    public int insertOrUpdateSelective(TQywxWelcomeStoreMappingEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
