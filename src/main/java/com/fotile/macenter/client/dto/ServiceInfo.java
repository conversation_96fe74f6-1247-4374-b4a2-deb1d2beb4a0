package com.fotile.macenter.client.dto;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 服务信息表
 *
 * @email
 * @date 2020-03-05 16:05:56
 */
@Data
public class ServiceInfo extends AuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人username
     */
    @FieldEncrypt
    private String reviseName;

    /**
     * 最后修改时间
     */
    private Date reviseTime;

    /**
     * 分公司id
     */
    private Long companyId;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 服务分类id
     */
    private Long serviceCategoryId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 副标题
     */
    private String serviceSubtitle;

    /**
     * 服务封面图
     */
    private String servicePicUrl;

    /**
     * 服务介绍
     */
    private String serviceIntroduce;

    /**
     * 支付方式（0：无，1：线上，2：线下）
     */
    private Long paymentType;

    /**
     * 支付价格
     */
    private BigDecimal paymentPrice;

    /**
     * 积分抵扣金额
     */
    private BigDecimal pointsPrice;

    /**
     * 门店状态，0：禁用；1：启用
     */
    private Long status;

    /**
     * 售价说明
     */
    private String paymentExplain;

    /**
     * 实际订单数
     */
    private BigDecimal realCount;

    /**
     * 显示订单数
     */
    private BigDecimal showCount;

    /**
     * 售价
     */
    private String salePrice;

    //cem服务编码
    private String cemServiceCode;
    //产品线
    private String cemProductCategory;
    //服务项目Id
    private String cemServiceCategoryId;
    //服务类型（1：泛家电，2：检测 3：局改）
    private Integer serviceType;
    //服务分类细分（1：厨房健康检测，2：烟机清洗检测 31：局改，32：微改）
    private Integer subServiceType;
    /**
     * CSM销售物料编码
     */
    private String csmSalesMaterialCode;
    /**
     * vip商品id
     */
    private Long vipGoodId;

}
