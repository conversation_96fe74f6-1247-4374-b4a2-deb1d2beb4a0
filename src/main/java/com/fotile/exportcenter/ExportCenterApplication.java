package com.fotile.exportcenter;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.fotile.exportcenter.cluesExaminationDetail.mq.CluesExaminationDetailExportChannel;
import com.fotile.exportcenter.marketing.mq.CluesExportChannel;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {"org.keycloak","com.fotile"},exclude = {DruidDataSourceAutoConfigure.class})
@EnableDiscoveryClient
@MapperScan(basePackages = {"com.fotile"})
//允许使用 spring cache
@EnableCaching
@EnableBinding(value = { CluesExportChannel.class, CluesExaminationDetailExportChannel.class})
//有外部系统调用，需要配置
@EnableFeignClients(basePackages = "com.fotile")
//允许使用jetcache 在方法
@EnableMethodCache(basePackages = "com.fotile.exportcenter")
//允许使用jetcache 创建缓存
@EnableCreateCacheAnnotation
@EnableAsync
public class ExportCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(ExportCenterApplication.class, args);
    }

}
