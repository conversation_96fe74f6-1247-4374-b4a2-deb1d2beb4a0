package com.fotile.exportcenter.cluesExaminationDetail.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.fotile.exportcenter.client.DataClientService;
import com.fotile.exportcenter.cluesExaminationDetail.mq.CluesExaminationDetailExportChannel;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListExportDTO;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListInDto;
import com.fotile.exportcenter.cluesExaminationDetail.pojo.dto.QueryExaminationListOutDto;
import com.fotile.exportcenter.marketing.pojo.dto.ExportTaskRecord;
import com.fotile.exportcenter.oss.service.OssService;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 服务报告异步导出service
 *
 * <AUTHOR>
 * @since 2025/4/8 周二 15:13
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class CluesExaminationDetailAsyncExportService {

    @Autowired
    private DataClientService dataClientService;

    @Autowired
    private TCluesExaminationDetailService tCluesExaminationDetailService;

    @Autowired
    private OssService ossService;

    /**
     * 服务报告列表导出【3001】
     */
    private static final String EXAMINATION_EXPORT_TASK_TYPE = "3001";

    @StreamListener(CluesExaminationDetailExportChannel.CLUES_EXAMINATION_DETAIL_INPUT)
    public void handleExportImMessage(Message<ExportTaskRecord> message) {
        ExportTaskRecord exportTaskRecord = message.getPayload();
        if (exportTaskRecord.getId() == null) {
            return;
        }

        if (!Objects.equals(exportTaskRecord.getType(), EXAMINATION_EXPORT_TASK_TYPE)) {
            return;
        }
        if (StringUtils.isBlank(exportTaskRecord.getParamJson())) {
            return;
        }
        try {
            Integer result = dataClientService.startTask(exportTaskRecord).getData();
            if (result != 1) {
                return;
            }
            switch (exportTaskRecord.getType()) {
                case EXAMINATION_EXPORT_TASK_TYPE:
                    this.doQueryExaminationListExport(exportTaskRecord);
                    break;
                default:
                    break;
            }

        } catch (Exception ex) {
            log.error(">>>>>>>>>>>>>> ImAsyncExportTaskService.handleExportImMessage >>>>>>>>>>>>> 导出任务参数：" + JSON.toJSONString(exportTaskRecord), ex);

            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(ex.getMessage());
            dataClientService.failureTask(exportTask);
        }
    }

    /**
     * 异步导出-fu
     *
     * @param exportTaskRecord 入参
     */
    public void doQueryExaminationListExport(ExportTaskRecord exportTaskRecord) {
        if (exportTaskRecord == null || StringUtils.isBlank(exportTaskRecord.getParamJson())) {
            return;
        }

        QueryExaminationListInDto dto = JSON.parseObject(exportTaskRecord.getParamJson(), QueryExaminationListInDto.class);
        if (dto == null) {
            return;
        }

        dto.setIzExport(1);

        String sheetName = "服务报告列表数据";
        String fileName = dto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            List<QueryExaminationListExportDTO> exportlist = new ArrayList<>();

            PageInfo<QueryExaminationListOutDto> pg = tCluesExaminationDetailService.queryExaminationExportList(dto);
            List<QueryExaminationListOutDto> list = pg.getRecords();

            if (CollectionUtils.isNotEmpty(list)) {
                for (QueryExaminationListOutDto po : list) {
                    QueryExaminationListExportDTO exportVO = QueryExaminationListExportDTO.toExportVO(po);
                    exportlist.add(exportVO);
                }
            }

            //写入excel文件流
            EasyExcel.write(os, QueryExaminationListExportDTO.class).sheet(sheetName).doWrite(exportlist);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);

            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            exportTask.setEndTime(new Date());
            exportTask.setTotalCount(list.size());
            dataClientService.successTask(exportTask);

            log.info(">>>>>>>>>>>> doQueryExaminationListExport.doQueryExaminationListExport >>>>>>>>>>>> data export successfully.");
        } catch (Exception ex) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(ex.getMessage());
            dataClientService.failureTask(exportTask);

            log.error(">>>>>>>>>>>>>> doQueryExaminationListExport.doQueryExaminationListExport ERROR>>>>>>>>>>>>> 导出任务参数：" + JSON.toJSONString(exportTaskRecord), ex);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException ex) {
                log.error(">>>>>>>>>>>> doQueryExaminationListExport.doQueryExaminationListExport IO ERROR >>>>>>>>>>>> ", ex);
            }
        }
    }
}
