package com.fotile.search.content.service;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.CompanyAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceLookup;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.search.client.CdpClient;
import com.fotile.search.client.CmsClient;
import com.fotile.search.client.ProductClient;
import com.fotile.search.client.pojo.cdp.QuerySpecialInsightOutDTO;
import com.fotile.search.client.pojo.cmsCenter.QueryWordDTO;
import com.fotile.search.client.pojo.cmsCenter.TreeContentListDto;
import com.fotile.search.content.pojo.content.*;
import com.fotile.search.content.pojo.mapper.ESContentMapper;
import com.fotile.search.content.service.interfaces.ContentSearchService;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.AnalyzeRequest;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.NestedSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2024/3/12.
 * 百景图&内容工厂融合
 */
@Slf4j
@Service("mixSearchService")
@DruidTxcMultiDataSourceAnnontation(value = "sixth")
public class MixSearchService implements ContentSearchService {
    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private RestHighLevelClient esClient;
    @Autowired
    private CmsClient cmsClient;
    @Autowired
    private ProductClient productClient;
    @Resource
    private TermBoostService termBoostService;

    @Autowired
    private CdpClient cdpClient;

    private static final ImmutableSet<String> INSPIRATION_FIELD_SET = ImmutableSet.of("inspirationCase.secondCategoryName");

    /**
     * 产品类型的排序字段
     */
    private static final ImmutableSet<String> PRODUCT_SCORE_FIELD_SET = ImmutableSet.of("productName", "productModel", "productShowModel");

    /**
     * 案例类型的排序字段
     */
    private static final ImmutableSet<String> CASE_SCORE_FIELD_SET = ImmutableSet.of("productName", "productModel", "productShowModel","originalId","communityName");

    /**
     * 我的收藏嵌套搜索字段
     */
    private static final ImmutableSet<String> MY_FAVORITE_NESTED_SET= ImmutableSet.of("materialContents.contentTitle",  "materialContents.productNames",
             "materialContents.content",   "materialContents.linkDescription");

    /**
     * 我的收藏-产品类型的搜索字段
     */
    private static final ImmutableSet<String> MY_FAVORITE_PRODUCT_SET = ImmutableSet.of("productShowModel");

    /**
     * 案例类型的排序字段
     */
    private static final ImmutableSet<String> MY_FAVORITE_CASE_SET = ImmutableSet.of("productName","communityName");


    /**
     * 灵感案例只查询这些二级分类下的，不全部都查询
     */
    private static final ImmutableSet<Long> INSPIRATION_SECOND_CATEGORY_IDS = ImmutableSet.of(5001L,4997L,5000L,4998L,4999L,5002L,5003L,5004L,5005L,5006L,5007L);

    private static final Float PHRASE_PREFIX_SCORE = 99999.0f;

    private static final Float PHRASE_FIRST_SCORE = 9999999999.0f;


    @Override
    public PageInfo<MixResultVO> searchMaterialContent(MaterialContentSearchRequest request) {
        if (request == null || request.getKeyword() == null) {
            throw new BusinessException("查询参数错误");
        }
        if ((request.getContentType() == null || Integer.valueOf(2).equals(request.getContentType())) && request.getCaseCompanyId() == null) {
            throw new BusinessException("查询案例必传公司id");
        }



      /*  BoolQueryBuilder boolQueryBuilder = buildTermQuery(request);
        BoolQueryBuilder keyWordQuery = buildMatchPhrasePrefixQuery(request.getKeyword());//先使用MatchPhrasePrefix匹配，如果匹配不到的话再使用Match去匹配下
        boolQueryBuilder.must(keyWordQuery);
        HighlightBuilder highlightBuilder = buildHighlightBuilder();
        PageInfo<MixResultVO> pageInfo = sendQuery(boolQueryBuilder,highlightBuilder, request);*/
        // if(pageInfo.getTotal()==0L){
        HighlightBuilder highlightBuilder = buildHighlightBuilder();
        BoolQueryBuilder boolQueryBuilder = buildTermQuery(request);
        BoolQueryBuilder keyWordQuery = buildPhrasePrefixMatchFirstQuery(request.getKeyword());//使用Match匹配
        boolQueryBuilder.must(keyWordQuery);
        PageInfo<MixResultVO> pageInfo = sendQuery(boolQueryBuilder, highlightBuilder, request);
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            for (MixResultVO mixResultVO : pageInfo.getList()) {
                if (mixResultVO.getMaterial() != null && mixResultVO.getMaterial().getResultType() == null) {
                    mixResultVO.setResultType(2).getMaterial().setResultType(2);
                } else if (mixResultVO.getProduct() != null && mixResultVO.getProduct().getResultType() == null) {
                    mixResultVO.setResultType(2).getProduct().setResultType(2);
                } else if (mixResultVO.getCaseDTO() != null && mixResultVO.getCaseDTO().getResultType() == null) {
                    mixResultVO.setResultType(2).getCaseDTO().setResultType(2);
                }
            }
        }
        //   }
        try {
            // 记录查询日志
            QueryWordDTO queryWordDTO = new QueryWordDTO();
            if (Integer.valueOf(2).equals(request.getContentType())) {
                //这里日志类型和内容类型对应关系有点不同，需要转换下
                queryWordDTO.setLogType(3);
                if(Objects.equals(request.getMaterialType(),2)){
                    queryWordDTO.setLogType(4);
                }else if(Objects.equals(request.getMaterialType(),3)){
                    queryWordDTO.setLogType(5);
                }
            } else if (Integer.valueOf(3).equals(request.getContentType())) {
                queryWordDTO.setLogType(2);
            } else {
                queryWordDTO.setLogType(1);
            }
            queryWordDTO.setWord(request.getKeyword());
            queryWordDTO.setPageInfo(pageInfo);
            queryWordDTO.setTreeId(request.getTreeId());
            cmsClient.saveQueryWord(queryWordDTO);
        } catch (Exception e) {
            log.error("记录日志报错，原因：{}", e.getMessage());
        }
        return pageInfo;
    }

    @Override
    public PageInfo searchMyFavorite(MaterialContentSearchRequest request) {
        if (request == null ||request.getSalesmanId()==null) {
            throw new BusinessException("查询参数错误");
        }
        if ((request.getContentType() == null || Integer.valueOf(2).equals(request.getContentType())) && request.getCaseCompanyId() == null) {
            throw new BusinessException("查询案例必传公司id");
        }
        request.setQueryMyFavorite(true);
        HighlightBuilder highlightBuilder = buildHighlightBuilder();
        BoolQueryBuilder boolQueryBuilder = buildTermQuery(request);

        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("collection", QueryBuilders.termQuery("collection.collector", request.getSalesmanId()), ScoreMode.None);
        boolQueryBuilder.must(nestedQueryBuilder);

        if(StringUtils.isNotBlank(request.getKeyword())){
            BoolQueryBuilder keyWordQuery = buildMyFavoriteQuery(request.getKeyword());//精确匹配
            boolQueryBuilder.must(keyWordQuery);
        }
        PageInfo<MixResultVO> pageInfo = sendQuery(boolQueryBuilder, highlightBuilder, request);
        return pageInfo;
    }



    private String boostKeyword(String keyword)  {
        if(StringUtils.isBlank(keyword)){
            return keyword;
        }
        String boostKeyword = keyword;
        AnalyzeRequest analyzeRequest = AnalyzeRequest.withGlobalAnalyzer("ik_max_word", keyword);
        try {
            AnalyzeResponse  response = esClient.indices().analyze(analyzeRequest, RequestOptions.DEFAULT);
            if(CollectionUtils.isNotEmpty(response.getTokens())){
                Set<String> tokens = response.getTokens().stream().map(AnalyzeResponse.AnalyzeToken::getTerm).collect(Collectors.toSet());
                DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("second");
                List<TermBoost> list = termBoostService.lambdaQuery().in(TermBoost::getWord, tokens).eq(TermBoost::getIsDeleted, 0).list();
                DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("sixth");
                if(CollectionUtils.isNotEmpty(list)){
                    StringBuilder stringBuilder = new StringBuilder(boostKeyword);
                    for (TermBoost termBoost : list) {
                        int index = stringBuilder.indexOf(termBoost.getWord());
                        if(index>=0){
                            stringBuilder.insert(index,termBoost.getWord());
                        }
                    }
                    boostKeyword = stringBuilder.toString();
                }
            }
        }catch (Exception e){
            log.error("调用es分词器报错，原因：{}",e);
            return keyword;
        }
        return boostKeyword;
    }

    public static void main(String[] args) {
        StringBuilder stringBuilder = new StringBuilder("一分钟了解净水机");
/*        String [] arr={"一分钟","了解","净水机"};
        for(String str:arr){
            int i = stringBuilder.indexOf(str);
            stringBuilder.insert(i,str);
            System.out.println(stringBuilder);
        }*/
        System.out.println(stringBuilder.indexOf("一分钟"));
    }

    /**
     * 获取es文档中根据关键词切词后能匹配上的三个词
     * @param keyword
     * @param analyzer
     * @return
     */
    @Override
    public List<String> getTop3AnalyWord(MaterialContentSearchRequest request, String analyzer){
        if(StringUtils.isBlank(request.getKeyword()) || StringUtils.isBlank(analyzer)){
            throw new BusinessException("参数错误");
        }
        AnalyzeRequest analyzeRequest = AnalyzeRequest.withGlobalAnalyzer(analyzer, request.getKeyword());
        List<String> tokens = Lists.newArrayList();
        try {
            AnalyzeResponse  response = esClient.indices().analyze(analyzeRequest, RequestOptions.DEFAULT);
            String originalText = request.getKeyword();
            if (response!=null&&CollectionUtils.isNotEmpty(response.getTokens())){
                for (AnalyzeResponse.AnalyzeToken token : response.getTokens()) {
                    if(request.getKeyword().equalsIgnoreCase(token.getTerm())){
                        continue;
                    }
                    request.setKeyword(token.getTerm());
                    Long count = queryCount(request);
                    if(count!=null && count>0){
                        tokens.add(token.getTerm());
                        if(tokens.size()==3){
                            break;
                        }
                    }
                }
                request.setKeyword(originalText);
            }
        } catch (Exception e) {
            log.error("调用es分词器报错，原因：{}",e);
        }
        log.error(JSON.toJSONString(tokens));
        return tokens;
    }

    private Long queryCount(MaterialContentSearchRequest request){
        BoolQueryBuilder termQuery = buildTermQuery(request);
        BoolQueryBuilder keyWordQuery = buildMatchQuery(request.getKeyword());
        termQuery.must(keyWordQuery);
       return sendCountQuery(termQuery);
    }

    private Long sendCountQuery(BoolQueryBuilder termQuery){
        Long count=0L;
        SearchRequest searchRequest = new SearchRequest(CONTENT_ES_ALIAS);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(termQuery)
                .trackTotalHits(true)
                .from(Math.toIntExact(0))
                .size(Math.toIntExact(0))
                .sort("_score", SortOrder.DESC)
                .sort("id", SortOrder.DESC);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits totalHits = searchResponse.getHits().getTotalHits();
            count= totalHits.value;
        } catch (Exception e) {
            log.error("获取总数报错，原因：{}", e.getMessage());
            throw new BusinessException("获取总数报错，请联系管理员");
        }
        return count;
    }

    public HashMap<Integer, Long> queryTypeCount(MaterialContentSearchRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getSalesmanId()) || CollectionUtils.isEmpty(request.getContentTypeList())) {
            return Maps.newHashMapWithExpectedSize(0);
        }
        HashMap<Integer, Long> resultMap = Maps.newHashMap();
        for (Integer type : request.getContentTypeList()) {
            MaterialContentSearchRequest queryParam = new MaterialContentSearchRequest();
            queryParam.setSalesmanId(request.getSalesmanId());
            queryParam.setContentType(type);
            queryParam.setQueryMyFavorite(true);
            request.setQueryMyFavorite(true);
            BoolQueryBuilder boolQueryBuilder = buildTermQuery(queryParam);
            NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("collection", QueryBuilders.termQuery("collection.collector", request.getSalesmanId()), ScoreMode.None);
            boolQueryBuilder.must(nestedQueryBuilder);
            resultMap.put(type, sendCountQuery(boolQueryBuilder));
        }
        return resultMap;
    }


    private PageInfo<MixResultVO> sendQuery(BoolQueryBuilder boolQueryBuilder, HighlightBuilder highlightBuilder, MaterialContentSearchRequest request) {
        SearchRequest searchRequest = new SearchRequest(CONTENT_ES_ALIAS);
        PageInfo<MixResultVO> pageInfo = new PageInfo<>(request.getPage(), request.getSize());

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .trackTotalHits(true)
                .from(Math.toIntExact(pageInfo.getOffset()))
                .size(Math.toIntExact(pageInfo.getSize()))
              ;
        if(Boolean.TRUE.equals(request.getQueryMyFavorite())){
            SortBuilder sortBuilder = SortBuilders.fieldSort("collection.collectionTime")
                    .order(SortOrder.DESC)
                    .setNestedSort(new NestedSortBuilder("collection")
                            .setFilter(QueryBuilders.termQuery("collection.collector", request.getSalesmanId())));
            sourceBuilder.sort(sortBuilder);
        }else{
            sourceBuilder.sort("_score", SortOrder.DESC)
                    .sort("id", SortOrder.DESC);
        }

        sourceBuilder.highlighter(highlightBuilder);
        searchRequest.source(sourceBuilder);
        if (request.getContentType() != null &&
                (
                        (Objects.equals(request.getContentType(), 2) && Objects.nonNull(request.getMaterialType()))
                        || request.getContentType() == 3
                )
        ) {
            sourceBuilder.from(0).size(0);//没法做排序，所以只能把符合条件的id都查出来，然后去mysql里排序查
            try {
                SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
                TotalHits totalHits = searchResponse.getHits().getTotalHits();
                System.out.println(totalHits);
                if (totalHits != null) {
                    sourceBuilder.size(Integer.valueOf(totalHits.value + ""));
                }
            } catch (Exception e) {
                log.error("获取总数报错，原因：{}", e.getMessage());
                throw new BusinessException("获取总数报错，请联系管理员");
            }
        }
        System.out.println("searchRequest:{}" + searchRequest.source().toString());
        try {
            SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse != null) {
                long total = searchResponse.getHits().getTotalHits().value;
                if (total == 0) {
                    return pageInfo;
                }

                pageInfo.setTotal(total);
                SearchHit[] hits = searchResponse.getHits().getHits();
                List<MaterialContentSearchResult> resultList = new ArrayList<>();
                HashSet<String> totalHighlightSet = Sets.newHashSet();
                for (SearchHit hit : hits) {
                    MaterialContentSearchResult result = JSON.parseObject(hit.getSourceAsString(), MaterialContentSearchResult.class);
                    Map<String, HighlightField> highlightFields = hit.getHighlightFields();

                    String source=hit.getSourceAsString();
                    if (!highlightFields.isEmpty()) {
                        if(CollectionUtils.isNotEmpty(result.getInspirationCase())){
                            result.setInspirationCase(result.getInspirationCase().stream().filter(c->INSPIRATION_SECOND_CATEGORY_IDS.contains(c.getSecondCategoryId())).collect(Collectors.toList()));
                            source=JSON.toJSONString(result);
                        }
                        if (source.contains(request.getKeyword())||source.toLowerCase().contains(request.getKeyword().toLowerCase())) {
                            result.setResultType(1);
                        }
                        Set<String> highlightFieldSet = highlightFields.entrySet().stream().map(Map.Entry::getValue).flatMap(h -> Arrays.stream(h.getFragments()))
                                .flatMap(text -> {
                                    //--获取模糊匹配的高亮文字--
                                    String startTag = "<b>";
                                    String endTag = "</b>";
                                    String string = text.string();
                                    int startIndex = string.indexOf(startTag);
                                    int endIndex = string.indexOf(endTag);
                                    HashSet<String> set = Sets.newHashSet();
                                    while (startIndex != -1 && endIndex != -1) {
                                        String highlightedField = string.substring(startIndex + startTag.length(), endIndex);
                                        set.add(highlightedField);
                                        startIndex = string.indexOf(startTag, endIndex + endTag.length());
                                        endIndex = string.indexOf(endTag, startIndex);
                                    }
                                    return set.stream();
                                }).collect(Collectors.toSet());
                        totalHighlightSet.addAll(highlightFieldSet);
                        result.setHighlightFields(highlightFieldSet);
                    }else{
                        if(Integer.valueOf(2).equals(result.getContentType())){
                            if(NumberUtil.isNumber(request.getKeyword())){
                                try {
                                    if(Long.valueOf(request.getKeyword()).equals(result.getOriginalId())){
                                        result.setResultType(1);
                                        result.setHighlightFields(Sets.newHashSet(request.getKeyword()));
                                    }
                                }catch (Exception e){
                                    log.error("将查询参数转换为id报错，原因：{}",e.getMessage());
                                }
                            }
                        }
                        if(CollectionUtils.isNotEmpty(result.getInspirationCase())){
                            result.setInspirationCase(result.getInspirationCase().stream().filter(c->INSPIRATION_SECOND_CATEGORY_IDS.contains(c.getSecondCategoryId())).collect(Collectors.toList()));
                            source=JSON.toJSONString(result);
                        }
                        if(StringUtils.containsIgnoreCase(source,request.getKeyword())){
                            result.setResultType(1);
                            result.setHighlightFields(Sets.newHashSet(request.getKeyword()));
                        }
                    }
                    resultList.add(result);
                }
                getMixResultDTO(resultList, request, pageInfo, totalHighlightSet);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return pageInfo;
    }

    private PageInfo<MixResultVO> getMixResultDTO(List<MaterialContentSearchResult> resultList,
                                                  MaterialContentSearchRequest request, PageInfo<MixResultVO> resultPage, HashSet<String> totalHighlightSet) {
        if (CollectionUtils.isEmpty(resultList)) {
            return new PageInfo<>();
        }
        Map<Integer, List<MaterialContentSearchResult>> resultMap = resultList.stream().collect(Collectors.groupingBy(MaterialContentSearchResult::getContentType));
        Map<Integer, TreeContentListDto> treeContentMap = Maps.newHashMap();
        Map<Long, MixProductOutDTO> productMap = Maps.newHashMap();
        Map<Long, MixCaseOutDTO> caseMap = Maps.newHashMap();
        Map<String, MixCaseOutDTO> newCaseMap = Maps.newHashMap();
        Map<Long, MixSpecialOutDTO> specialMap = Maps.newHashMapWithExpectedSize(0);
        Map<Long, Long> bowerCountMap =Maps.newHashMapWithExpectedSize(0);


        for (Integer type : resultMap.keySet()) {
            if (Integer.valueOf(1).equals(type)) {
                List<MaterialContentSearchResult> materialContentSearchResults = resultMap.get(1);
                if (CollectionUtils.isNotEmpty(materialContentSearchResults)) {
                    List<TreeContentListDto> contentListDtos = cmsClient.getTreeContentList(materialContentSearchResults).getData();
                    if (CollectionUtils.isNotEmpty(contentListDtos)) {
                        treeContentMap = contentListDtos.stream().collect(Collectors.toMap(TreeContentListDto::getMaterialId, Function.identity()));
                    }


                }
            } else if (Integer.valueOf(2).equals(type)) {
                List<MaterialContentSearchResult> materialContentSearchResults = resultMap.get(2);
                if (CollectionUtils.isNotEmpty(materialContentSearchResults)) {
                  //  Map<Long, MaterialContentSearchResult> esResultMap = materialContentSearchResults.stream().collect(Collectors.toMap(MaterialContentSearchResult::getOriginalId, Function.identity()));
                    Map<String, MaterialContentSearchResult> esMaterialResultMap = materialContentSearchResults.stream().collect(Collectors.toMap(MaterialContentSearchResult::getId, Function.identity()));
                  //  List<Long> type1CaseIds = materialContentSearchResults.stream().filter(m -> Objects.equals(m.getMaterialType(), 1)).map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
/*                    if (CollectionUtils.isNotEmpty(type1CaseIds)) {
                        //构建案例的查询参数
                        QueryCaseDTO queryCaseDTO = ESContentMapper.INSTANCE.queryContentDTO2QueryCaseDTO(request);
                        queryCaseDTO.setCaseIdList(type1CaseIds);
                        if (request.getContentType() != null) {
                            queryCaseDTO.setPage(request.getPage());
                            queryCaseDTO.setSize(request.getSize());
                        } else {
                            queryCaseDTO.setPage(1);
                            queryCaseDTO.setSize(type1CaseIds.size());
                        }

                        PageInfo<MixCaseOutDTO> casePage = cmsClient.getCaseList(queryCaseDTO).getData();
                        if (casePage != null) {
                            if (Integer.valueOf(2).equals(request.getContentType())) {
                                resultPage.setTotal(casePage.getTotal());
                            }
                            if (CollectionUtils.isNotEmpty(casePage.getRecords())) {
                                for (MixCaseOutDTO mixCaseOutDTO : casePage.getRecords()) {
                                    MaterialContentSearchResult esResult = esResultMap.get(mixCaseOutDTO.getCaseId());
                                    if (esResult != null) {
                                        mixCaseOutDTO.setHighlightFields(esResult.getHighlightFields());
                                    }
                                    caseMap.put(mixCaseOutDTO.getCaseId(), mixCaseOutDTO);
                                }
                            }
                        } else {
                            if (resultPage.getTotal() > 0) {
                                resultPage.setTotal(resultPage.getTotal() - type1CaseIds.size());
                            } else {
                                resultPage.setTotal(0);
                            }
                        }
                    }*/
                    putType1Case2CaseMap(resultList,newCaseMap,esMaterialResultMap,resultPage,request);
                    putDevise2CaseMap(resultList,newCaseMap,esMaterialResultMap,resultPage,request);
                }
            } else if (Integer.valueOf(3).equals(type)) {
                List<MaterialContentSearchResult> materialContentSearchResults = resultMap.get(3);
                if (CollectionUtils.isNotEmpty(materialContentSearchResults)) {
                    Map<Long, MaterialContentSearchResult> productEsMap = materialContentSearchResults.stream().collect(Collectors.toMap(MaterialContentSearchResult::getOriginalId, Function.identity()));
                    List<Long> goodsIdList = materialContentSearchResults.stream().map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
                    QueryProductDTO queryProductDTO = ESContentMapper.INSTANCE.queryContentDTO2QueryProductDTO(request);
                    if (request.getContentType() != null) {
                        queryProductDTO.setPage(request.getPage());
                        queryProductDTO.setSize(request.getSize());
                    } else {
                        queryProductDTO.setPage(0);
                        queryProductDTO.setSize(goodsIdList.size());
                    }
                    queryProductDTO.setMixGoodsIdList(goodsIdList);
                    PageInfo<MixProductOutDTO> pageInfo = productClient.getMixProductOutDTOPage(queryProductDTO).getData();
                    if (pageInfo != null) {
                        if (Integer.valueOf(3).equals(request.getContentType())) {
                            resultPage.setTotal(pageInfo.getTotal());
                        }
                        if (CollectionUtils.isNotEmpty(pageInfo.getRecords())) {
                            productMap = pageInfo.getRecords().stream().map(p -> {
                                MaterialContentSearchResult product = productEsMap.get(p.getProductId());
                                if (product != null) {
                                    p.setHighlightFields(product.getHighlightFields());
                                }
                                return p;
                            }).collect(Collectors.toMap(MixProductOutDTO::getProductId, Function.identity()));
                        } else {
                            if( resultPage.getTotal()>0){
                                resultPage.setTotal(resultPage.getTotal()-goodsIdList.size());
                            }else{
                                resultPage.setTotal(0);
                            }
                        }
                    } else {
                        resultPage.setTotal(0);
                    }
                }
            }else if(Integer.valueOf(4).equals(type)){
                List<MaterialContentSearchResult> specialInfoSearchResults = resultMap.get(4);
                if(CollectionUtils.isNotEmpty(specialInfoSearchResults)){
                    List<Long> specialIds = specialInfoSearchResults.stream().map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
                    try {
                        specialMap = Optional.ofNullable(cmsClient.querySpecialList(new JSONObject().fluentPut("ids", specialIds).fluentPut("salesmanId",request.getSalesmanId())))
                                .filter(Result::getSuccess)
                                .map(Result::getData)
                                .filter(CollectionUtils::isNotEmpty)
                                .map(l -> l.stream().collect(Collectors.toMap(MixSpecialOutDTO::getId, Function.identity())))
                                .orElse(Maps.newHashMapWithExpectedSize(0));
                    }catch (Exception e){
                      log.error("查询专题异常："+e.getMessage());
                  }
                    if(StringUtils.isNotBlank(request.getQywxUserId())){
                        JSONObject jsonObject = new JSONObject()
                                .fluentPut("userId", request.getQywxUserId())
                                .fluentPut("specialIds", specialIds);
                        try {
                            bowerCountMap = Optional.ofNullable(cdpClient.getSpecialInsightCount(jsonObject))
                                    .filter(Result::getSuccess)
                                    .map(Result::getData)
                                    .filter(CollectionUtils::isNotEmpty)
                                    .map(l -> l.stream().collect(Collectors.toMap(QuerySpecialInsightOutDTO::getId, QuerySpecialInsightOutDTO::getOpen, (v1, v2) -> v1)))
                                    .orElse(Maps.newHashMapWithExpectedSize(0));
                        }catch (Exception e){
                            log.error("查询专题浏览量异常："+JSONObject.toJSONString(e));
                        }
                    }
                }
            }
        }

        ArrayList<MixResultVO> result = Lists.newArrayList();
        for (MaterialContentSearchResult esResult : resultList) {
            if (Integer.valueOf(1).equals(esResult.getContentType())) {
                TreeContentListDto treeContentListDto = treeContentMap.get(esResult.getOriginalId().intValue());
                if (treeContentListDto != null) {
                    treeContentListDto.setResultType(esResult.getResultType());
                    result.add(new MixResultVO()
                            .setMaterial(treeContentListDto)
                            .setHighlightFields(totalHighlightSet)
                            .setResultType(treeContentListDto.getResultType())
                    );
                }
            } else if (Integer.valueOf(2).equals(esResult.getContentType())) {
                MixCaseOutDTO mixCaseOutDTO =  newCaseMap.get(esResult.getId());
               // MixCaseOutDTO mixCaseOutDTO = caseMap.get(esResult.getOriginalId());
                if (mixCaseOutDTO != null) {
                    mixCaseOutDTO.setResultType(esResult.getResultType());
                    result.add(new MixResultVO().setCaseDTO(mixCaseOutDTO)
                            .setHighlightFields(totalHighlightSet)
                            .setResultType(mixCaseOutDTO.getResultType()));
                }
            } else if (Integer.valueOf(3).equals(esResult.getContentType())) {
                MixProductOutDTO mixProductOutDTO = productMap.get(esResult.getOriginalId());
                if (mixProductOutDTO != null) {
                    mixProductOutDTO.setResultType(esResult.getResultType());
                    result.add(new MixResultVO().setProduct(mixProductOutDTO)
                            .setHighlightFields(totalHighlightSet)
                            .setResultType(mixProductOutDTO.getResultType()));
                }
            }else if(Objects.equals(esResult.getContentType(),4)){
                MixSpecialOutDTO mixSpecialOutDTO = specialMap.get(esResult.getOriginalId());
                if(Objects.nonNull(mixSpecialOutDTO)){
                    mixSpecialOutDTO.setResultType(esResult.getResultType());
                    String browseCount = Optional.ofNullable(bowerCountMap.get(mixSpecialOutDTO.getId()))
                            .map(String::valueOf)
                            .orElse("0");
                    mixSpecialOutDTO.setBrowseCount(browseCount);
                    result.add(new MixResultVO().setSpecial(mixSpecialOutDTO)
                            .setHighlightFields(totalHighlightSet)
                            .setResultType(mixSpecialOutDTO.getResultType()));
                }
            }
        }
        resultPage.setRecords(result);
        return resultPage;
    }

    /**
     * 将场景化的反查结果放入案例的map
     * @param caseResultList
     * @param caseMap
     * @param esMaterialResultMap
     */
    private void putDevise2CaseMap(List<MaterialContentSearchResult> caseResultList, Map<String, MixCaseOutDTO> caseMap,Map<String,
            MaterialContentSearchResult> esMaterialResultMap,
                                   PageInfo<MixResultVO> resultPage,
                                   MaterialContentSearchRequest request ){
        if(CollectionUtils.isEmpty(caseResultList)){
            return;
        }

        //去反查3d连接的冗余字段
        List<MaterialContentSearchResult> devise3dList = caseResultList.stream().filter(m -> ImmutableSet.of(2).contains(m.getMaterialType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(devise3dList)){
            List<Long> caseIds = devise3dList
                    .stream()
                    .map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
            QueryCaseDTO queryCaseDTO =new QueryCaseDTO();
            queryCaseDTO.setCaseIdList(caseIds);
            queryCaseDTO.setMaterialType(2);
            queryCaseDTO.setUserId(request.getQueryCaseUserId());
            if (Integer.valueOf(2).equals(request.getContentType()) && ImmutableSet.of(2).contains(request.getMaterialType())) {
                queryCaseDTO.setPage(request.getPage());
                queryCaseDTO.setSize(request.getSize());
            } else {
                queryCaseDTO.setPage(1);
                queryCaseDTO.setSize(caseIds.size());
            }

            List<MixCaseOutDTO> caseList = cmsClient.getByIds4ES(queryCaseDTO).getData();
            if(CollectionUtils.isNotEmpty(caseList)){
                Map<Long, MixCaseOutDTO> caseOutDTOMap = caseList.stream().collect(Collectors.toMap(MixCaseOutDTO::getCaseId, Function.identity()));
                for (MaterialContentSearchResult esDevise : devise3dList) {
                    MaterialContentSearchResult esResult = esMaterialResultMap.get(esDevise.getId());//设置高亮
                    MixCaseOutDTO mixCaseOutDTO = caseOutDTOMap.get(esDevise.getOriginalId());
                    if (esResult != null && mixCaseOutDTO!=null) {
                        mixCaseOutDTO.setHighlightFields(esResult.getHighlightFields());
                        mixCaseOutDTO.setMaterialType(esDevise.getMaterialType());
                    }
                    if(Objects.nonNull(mixCaseOutDTO)){
                        caseMap.put(esDevise.getId(),mixCaseOutDTO);
                    }
                }
            }
        }



        //去反查场景化的冗余字段
        List<MaterialContentSearchResult> deviseList = caseResultList.stream().filter(m -> ImmutableSet.of(3).contains(m.getMaterialType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(deviseList)){
            List<Long> caseIds = deviseList
                    .stream()
                    .map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
            QueryCaseDTO queryCaseDTO =new QueryCaseDTO();
            queryCaseDTO.setCaseIdList(caseIds);
            queryCaseDTO.setMaterialType(3);
            queryCaseDTO.setUserId(request.getQueryCaseUserId());
            if (Integer.valueOf(2).equals(request.getContentType()) && ImmutableSet.of(3).contains(request.getMaterialType())) {
                queryCaseDTO.setPage(request.getPage());
                queryCaseDTO.setSize(request.getSize());
            } else {
                queryCaseDTO.setPage(1);
                queryCaseDTO.setSize(caseIds.size());
            }

            List<MixCaseOutDTO> caseList = cmsClient.getByIds4ES(queryCaseDTO).getData();
            if(CollectionUtils.isNotEmpty(caseList)){
                Map<Long, MixCaseOutDTO> caseOutDTOMap = caseList.stream().collect(Collectors.toMap(MixCaseOutDTO::getCaseId, Function.identity()));
                for (MaterialContentSearchResult esDevise : deviseList) {
                    MaterialContentSearchResult esResult = esMaterialResultMap.get(esDevise.getId());//设置高亮
                    MixCaseOutDTO mixCaseOutDTO = caseOutDTOMap.get(esDevise.getOriginalId());
                    if (esResult != null && mixCaseOutDTO!=null) {
                        mixCaseOutDTO.setHighlightFields(esResult.getHighlightFields());
                        mixCaseOutDTO.setMaterialType(esDevise.getMaterialType());
                    }
                    if(Objects.nonNull(mixCaseOutDTO)){
                        caseMap.put(esDevise.getId(),mixCaseOutDTO);
                    }
                }
            }
        }


    }

    private void putType1Case2CaseMap(List<MaterialContentSearchResult> caseResultList, Map<String, MixCaseOutDTO> caseMap,
                                      Map<String, MaterialContentSearchResult> esMaterialResultMap,PageInfo<MixResultVO> resultPage,MaterialContentSearchRequest request){
        if(CollectionUtils.isEmpty(caseResultList)){
            return;
        }

        //原来百景图的案例
        List<MaterialContentSearchResult> inspireCaseList = caseResultList.stream().filter(m -> Objects.equals(1,m.getMaterialType())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(inspireCaseList)){
            return;
        }

        List<Long> inspireCaseIds = inspireCaseList.stream().map(MaterialContentSearchResult::getOriginalId).collect(Collectors.toList());
        //构建案例的查询参数
        QueryCaseDTO queryCaseDTO = ESContentMapper.INSTANCE.queryContentDTO2QueryCaseDTO(request);
        queryCaseDTO.setCaseIdList(inspireCaseIds);
        queryCaseDTO.setUserId(request.getQueryCaseUserId());
        if (Integer.valueOf(2).equals(request.getContentType()) && Objects.equals(request.getMaterialType(),1)) {
            queryCaseDTO.setPage(request.getPage());
            queryCaseDTO.setSize(request.getSize());
        } else {
            queryCaseDTO.setPage(1);
            queryCaseDTO.setSize(inspireCaseIds.size());
        }
        PageInfo<MixCaseOutDTO> casePage = cmsClient.getCaseList(queryCaseDTO).getData();
        if (casePage != null) {
            if (Integer.valueOf(2).equals(request.getContentType()) && Objects.equals(request.getMaterialType(),1)) {
                resultPage.setTotal(casePage.getTotal());
            }
            if (CollectionUtils.isNotEmpty(casePage.getRecords())) {
                Map<Long, MixCaseOutDTO> caseOutDTOMap = casePage.getRecords().stream().collect(Collectors.toMap(MixCaseOutDTO::getCaseId, Function.identity()));
                for (MaterialContentSearchResult esCaseResult : inspireCaseList) {
                    MaterialContentSearchResult esResult = esMaterialResultMap.get(esCaseResult.getId());//设置高亮
                    MixCaseOutDTO mixCaseOutDTO = caseOutDTOMap.get(esCaseResult.getOriginalId());
                    if (esResult != null && mixCaseOutDTO!=null) {
                        mixCaseOutDTO.setHighlightFields(esResult.getHighlightFields());
                        mixCaseOutDTO.setMaterialType(esResult.getMaterialType());
                    }
                    if(Objects.nonNull(mixCaseOutDTO)){
                        caseMap.put(esCaseResult.getId(),mixCaseOutDTO);
                    }
                }
            }
        } else {
            if (resultPage.getTotal() > 0) {
                resultPage.setTotal(resultPage.getTotal() - inspireCaseIds.size());
            } else {
                resultPage.setTotal(0);
            }
        }
    }

    private HighlightBuilder buildHighlightBuilder() {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        HashSet<String> fieldSet = Sets.newHashSet();
        for (String field : ES_FIELD_SET) {
            if(fieldSet.contains(field)){
                continue;
            }
            highlightBuilder.field(field);
            fieldSet.add(field);
        }
        for (String field : PRODUCT_SCORE_FIELD_SET) {
            if(fieldSet.contains(field)){
                continue;
            }
            highlightBuilder.field(field);
            fieldSet.add(field);
        }
        for (String field : CASE_SCORE_FIELD_SET) {
            if(fieldSet.contains(field)){
                continue;
            }
            highlightBuilder.field(field);
            fieldSet.add(field);
        }
        for (String field : INSPIRATION_FIELD_SET) {
            if(fieldSet.contains(field)){
                continue;
            }
            highlightBuilder.field(field);
            fieldSet.add(field);
        }
        highlightBuilder.field("materialContents.caseType")
                .field("title")
                .field("inspirationCase.title");
        highlightBuilder.preTags("<b>");
        highlightBuilder.postTags("</b>");
        return highlightBuilder;
    }


    /**
     * 构建不包括关键字查询的查询条件，因为关键字是要并集匹配多个字段，这个里的大部分都是交集匹配的
     *
     * @param request
     * @return
     */

    private BoolQueryBuilder buildTermQuery(MaterialContentSearchRequest request) {
        BoolQueryBuilder filterQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldBuilder = QueryBuilders.boolQuery();

        //构建权限过滤
        if (request.getContentType() == null) {
            BoolQueryBuilder contentQueryBuilder = buildContentQueryBuilder(request);
            BoolQueryBuilder hundredMapQueryBuilder = buildHundredMapQueryBuilder(request);
            shouldBuilder.should(contentQueryBuilder);//内容工厂
            shouldBuilder.should(hundredMapQueryBuilder);//百景图
            Optional.ofNullable(buildSpecialQuerySBuilder(request))
                    .filter(BoolQueryBuilder::hasClauses)
                    .ifPresent(shouldBuilder::should);//专题
        } else if (Integer.valueOf(1).equals(request.getContentType())) {
            BoolQueryBuilder contentQueryBuilder = buildContentQueryBuilder(request);
            shouldBuilder.filter(contentQueryBuilder);
        }else if(Objects.equals(request.getContentType(),4)){
            shouldBuilder.filter(buildSpecialQuerySBuilder(request));
        } else {
            BoolQueryBuilder hundredMapQueryBuilder = buildHundredMapQueryBuilder(request);
            shouldBuilder.filter(hundredMapQueryBuilder);
        }
        if (shouldBuilder.hasClauses()) {
            filterQuery.filter(shouldBuilder);
        }

        //先这样写，这种写法现在只支持我的收藏，内容搜索现在不需要显示专题
        if(!Objects.equals(request.getContentType(),4)){
            Date now = new Date();
            String format = DATE_TIME_FORMATTER.format(now);
            BoolQueryBuilder endTimeBuilder = QueryBuilders.boolQuery();
            endTimeBuilder.should(QueryBuilders.rangeQuery("effectiveEndTime").gte(format).lte("3000-12-31 23:59:59"))
                    .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("effectiveEndTime")));
            BoolQueryBuilder startTimeBuilder = QueryBuilders.boolQuery();
            startTimeBuilder.should(QueryBuilders.rangeQuery("effectiveStartTime").gte("2023-09-01 00:00:00").lte(format))
                    .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("effectiveStartTime")));
            filterQuery.filter(endTimeBuilder)
                    .filter(startTimeBuilder);
        }
        return filterQuery;
    }


    /** 构建专题的搜索条件
     * 0918版本仅在我的收藏里有专题，所以判断来源是我的收藏的话才构建查询条件
     * @param request
     * @return
     */
    private BoolQueryBuilder buildSpecialQuerySBuilder(MaterialContentSearchRequest request){
        BoolQueryBuilder contentQueryBuilder = QueryBuilders.boolQuery();
        if(Objects.isNull(request)|| !Objects.equals(request.getQueryMyFavorite(),true)){
            return contentQueryBuilder;
        }
        contentQueryBuilder.filter(QueryBuilders.termQuery("contentType", 4));
        return contentQueryBuilder;
    }

    /**
     * 构建原来内容工厂的权限查询条件 contentType必为1，有树id和分类id的话就构建，此Builder内的条件为and关系
     *
     * @param request
     * @return
     */
    private BoolQueryBuilder buildContentQueryBuilder(MaterialContentSearchRequest request) {
        BoolQueryBuilder contentQueryBuilder = QueryBuilders.boolQuery();
        contentQueryBuilder.filter(QueryBuilders.termQuery("contentType", 1));
        if (request.getTreeId() != null) {
            contentQueryBuilder.filter(QueryBuilders.termQuery("treeId", request.getTreeId()));
        }
        if (request.getNode1Id() != null) {
            contentQueryBuilder.filter(QueryBuilders.termQuery("node1Id", request.getNode1Id()));
        }
        if (request.getNode2Id() != null) {
            contentQueryBuilder.filter(QueryBuilders.termQuery("node2Id", request.getNode2Id()));
        }
        if (request.getNode3Id() != null) {
            contentQueryBuilder.filter(QueryBuilders.termQuery("node3Id", request.getNode3Id()));
        }
        if (request.getNode4Id() != null) {
            contentQueryBuilder.filter(QueryBuilders.termQuery("node4Id", request.getNode4Id()));
        }
        if (CollectionUtils.isNotEmpty(request.getContentTypeList())) {
            TermsQueryBuilder termsQuery = QueryBuilders.termsQuery("materialType", request.getContentTypeList());
            contentQueryBuilder.filter(termsQuery);
        }
        //增加标签
        if (CollectionUtils.isNotEmpty(request.getMaterialTagList())){
            TermsQueryBuilder termsQuery = QueryBuilders.termsQuery("tagIds", request.getMaterialTagList());
            contentQueryBuilder.filter(termsQuery);
        }

        return contentQueryBuilder;
    }


    /**
     * 产品： ContentType为3，没有分公司权限
     * 或者ContentType为2，分公司是前端传过来的分公司或者showType=2,上海的不带showType=2条件，只看上海
     *
     * @param request
     * @return
     */
    private BoolQueryBuilder buildHundredMapQueryBuilder(MaterialContentSearchRequest request) {
        BoolQueryBuilder hundredMapQueryBuilder = QueryBuilders.boolQuery();
        if (request.getContentType() == null) {
            hundredMapQueryBuilder.should(buildProductQueryBuilder(request));
            hundredMapQueryBuilder.should(buildCaseQueryBuilder(request));
        } else if (Integer.valueOf(2).equals(request.getContentType())) {
            hundredMapQueryBuilder.filter(buildCaseQueryBuilder(request));
        } else if (Integer.valueOf(3).equals(request.getContentType())) {
            hundredMapQueryBuilder.filter(buildProductQueryBuilder(request));
        }
        return hundredMapQueryBuilder;
    }

    /**
     * 构建案例的查询条件，ContentType为2，分公司是前端传过来的分公司或者showType=2,上海的不带showType=2条件，只看上海
     * 1030迭代案例融合了场景化方案，用字段materialType区分，1是老的案例，3是场景化方案
     * 新的案例查询是两个条件并出来的，
     *
     * @param request
     * @return
     */
    private BoolQueryBuilder buildCaseQueryBuilder(MaterialContentSearchRequest request) {
        BoolQueryBuilder caseQueryBuilder = QueryBuilders.boolQuery();
//
        if(Objects.isNull(request.getMaterialType())){
            //百景图案例
            BoolQueryBuilder oldCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
            oldCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 1));
            caseQueryBuilder.should(oldCaseQueryBuilder);
           /* if (Long.valueOf(71).equals(request.getCaseCompanyId())) {
                oldCaseQueryBuilder.filter(QueryBuilders.termQuery("companyIds", 71));
            } else {*/
                BoolQueryBuilder should = QueryBuilders.boolQuery();
                should.should(QueryBuilders.termQuery("showType", 2));
               /* List<CompanyAuthor> companyAuthors = userAuthorConfig.queryCompanyAuthorList();
                if (CollectionUtils.isNotEmpty(companyAuthors)) {
                    List<Long> companyIds = companyAuthors.stream().map(CompanyAuthor::getOrgId).collect(Collectors.toList());
                    should.should(QueryBuilders.termsQuery("companyIds", companyIds));
                }*/
                if(Objects.nonNull(request.getCaseCompanyId())){
                    should.should(QueryBuilders.termsQuery("companyIds", Lists.newArrayList(request.getCaseCompanyId())));
                }
                oldCaseQueryBuilder.filter(should);
         //   }

            //场景化-3d
            BoolQueryBuilder threeDCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
            threeDCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 2));
            if (CollectionUtils.isNotEmpty(request.getTagIds())) {
                threeDCaseQueryBuilder.filter(QueryBuilders.termsQuery("tagIds", request.getTagIds()));
            }
            //每个人都能看属于全部的数据，有权限的人可以看权限内的数据
            //分公司权限
            List<CompanyAuthor> companyAuthors = userAuthorConfig.queryCompanyAuthorList();
            if (CollectionUtils.isNotEmpty(companyAuthors)) {//为空则不是全部分公司权限，需要添加过滤条件
                List<Long> companyIds =Lists.newArrayList(0l);//都可以看全国
                companyIds.addAll(companyAuthors.stream().map(CompanyAuthor::getOrgId).collect(Collectors.toList()));//有权限的人看权限内的数据
                threeDCaseQueryBuilder.filter(QueryBuilders.termsQuery("companyIds", companyIds));
            }

            //门店权限
            List<StoreAuthor> storeAuthors = userAuthorConfig.queryStoreAuthorList();
            if (storeAuthors != null && storeAuthors.size() > 0) {
                List<Long> authStoreIds =Lists.newArrayList(0l);//都可以看全国
                authStoreIds.addAll(storeAuthors.stream().map(StoreAuthor::getOrgId).collect(Collectors.toList()));
                threeDCaseQueryBuilder.filter(QueryBuilders.termsQuery("storeIds", authStoreIds));
            }

            caseQueryBuilder.should(threeDCaseQueryBuilder);

            //场景化
            BoolQueryBuilder deviseCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
            deviseCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 3));
            if (CollectionUtils.isNotEmpty(request.getTagIds())) {
                deviseCaseQueryBuilder.filter(QueryBuilders.termsQuery("tagIds", request.getTagIds()));
            }
            caseQueryBuilder.should(deviseCaseQueryBuilder);
        }else{

            if(Objects.equals(request.getMaterialType(),1)){
                //百景图案例
                BoolQueryBuilder oldCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
                oldCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 1));

              /*  if (Long.valueOf(71).equals(request.getCaseCompanyId())) {
                    oldCaseQueryBuilder.filter(QueryBuilders.termQuery("companyIds", 71));
                } else {*/
                    BoolQueryBuilder should = QueryBuilders.boolQuery();
                    should.should(QueryBuilders.termQuery("showType", 2));
                    List<CompanyAuthor> companyAuthors = userAuthorConfig.queryCompanyAuthorList();
                    if (CollectionUtils.isNotEmpty(companyAuthors)) {
                        List<Long> companyIds = companyAuthors.stream().map(CompanyAuthor::getOrgId).collect(Collectors.toList());
                        should.should(QueryBuilders.termsQuery("companyIds", companyIds));
                    }
                    oldCaseQueryBuilder.filter(should);
               // }
                caseQueryBuilder.filter(oldCaseQueryBuilder);
            }else if(Objects.equals(request.getMaterialType(),2)){
                //场景化-3d
                BoolQueryBuilder deviseCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
                deviseCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 2));
                if (CollectionUtils.isNotEmpty(request.getTagIds())) {
                    deviseCaseQueryBuilder.filter(QueryBuilders.termsQuery("tagIds", request.getTagIds()));
                }
                //每个人都能看属于全部的数据，有权限的人可以看权限内的数据
                //分公司权限
                List<CompanyAuthor> companyAuthors = userAuthorConfig.queryCompanyAuthorList();
                if (CollectionUtils.isNotEmpty(companyAuthors)) {
                    List<Long> companyIds =Lists.newArrayList(0l);//都可以看全国
                    companyIds.addAll(companyAuthors.stream().map(CompanyAuthor::getOrgId).collect(Collectors.toList()));//有权限的人看权限内的数据
                    deviseCaseQueryBuilder.filter(QueryBuilders.termsQuery("companyIds", companyIds));
                }

                //门店权限
                List<StoreAuthor> storeAuthors = userAuthorConfig.queryStoreAuthorList();
                if (storeAuthors != null && storeAuthors.size() > 0) {
                    List<Long> authStoreIds =Lists.newArrayList(0l);//都可以看全国
                    authStoreIds.addAll(storeAuthors.stream().map(StoreAuthor::getOrgId).collect(Collectors.toList()));
                    deviseCaseQueryBuilder.filter(QueryBuilders.termsQuery("storeIds", authStoreIds));
                }


                caseQueryBuilder.filter(deviseCaseQueryBuilder);

            }else if(Objects.equals(request.getMaterialType(),3)){
                //场景化
                BoolQueryBuilder deviseCaseQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 2));
                deviseCaseQueryBuilder.filter(QueryBuilders.termQuery("materialType", 3));
                //场景化融合
                if (CollectionUtils.isNotEmpty(request.getTagIds())) {
                    deviseCaseQueryBuilder.filter(QueryBuilders.termsQuery("tagIds", request.getTagIds()));
                }
                caseQueryBuilder.filter(deviseCaseQueryBuilder);
            }
        }

        return caseQueryBuilder;
    }



    /**
     * 构建产品的查询条件，ContentType为3
     *
     * @param request
     * @return
     */
    private BoolQueryBuilder buildProductQueryBuilder(MaterialContentSearchRequest request) {
        return QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("contentType", 3));
    }


    private BoolQueryBuilder buildMatchQuery(String keyWord) {
        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldFitler = QueryBuilders.boolQuery();
        BoolQueryBuilder inspirationNestedShould = QueryBuilders.boolQuery();
        for (String field : ES_FIELD_SET) {
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(field, keyWord);
            if(SCORE_ES_FIELD_SET.contains(field)){
                matchQueryBuilder.boost(5.0f);

            }
            nestedShould.should(matchQueryBuilder);
        }

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("contentType", 3);
        termQueryBuilder.boost(100.0f);
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] functions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(1000000.0f))
        };
        for (String productField : PRODUCT_SCORE_FIELD_SET) {
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(productField, keyWord);
            FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, functions);
            shouldFitler.should(functionScoreQueryBuilder);
        }

        //构建案例的特殊排序条件
        TermQueryBuilder caseQueryBuilder = QueryBuilders.termQuery("contentType", 2);
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] caseLowScoreFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(caseQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(5.0f))
        };
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] casePhrasePrefixFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(caseQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(9999999999.0f))
        };
        for (String field : CASE_SCORE_FIELD_SET) {
            if(field.equals("originalId") ){
                if(NumberUtil.isNumber(keyWord)){
                    TermQueryBuilder originalIdQueryBuilder = QueryBuilders.termQuery(field, keyWord).boost(9999999999.0f);
                    BoolQueryBuilder must = QueryBuilders.boolQuery().must(originalIdQueryBuilder).must(caseQueryBuilder);
                    shouldFitler.should(must);
                }
                continue;
            }
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(field, keyWord);
            FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, caseLowScoreFunctions);
            shouldFitler.should(functionScoreQueryBuilder);
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field + ".raw", "*" + keyWord + "*");
            FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(wildcardQueryBuilder, casePhrasePrefixFunctions);
            shouldFitler.should(functionScoreQuery);
        }
        //灵感案例，titile最高优先级
        TermsQueryBuilder secondCategoryFilter = QueryBuilders.termsQuery("inspirationCase.secondCategoryId", INSPIRATION_SECOND_CATEGORY_IDS);
        WildcardQueryBuilder inCaseTitleRawFilter = QueryBuilders.wildcardQuery("inspirationCase.title.raw", "*" + keyWord + "*").boost(PHRASE_FIRST_SCORE);
        inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(inCaseTitleRawFilter));
        for (String field : INSPIRATION_FIELD_SET) {
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field+".raw", "*" + keyWord + "*").boost(PHRASE_PREFIX_SCORE);
            inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(wildcardQueryBuilder));
        }


        nestedShould.should(QueryBuilders.matchQuery("materialContents.caseType",keyWord));
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("materialContents", nestedShould,ScoreMode.Total);
        NestedQueryBuilder inspirationNestedQuery = QueryBuilders.nestedQuery("inspirationCase", inspirationNestedShould, ScoreMode.Total);
        shouldFitler.should(inspirationNestedQuery);
        shouldFitler.should(nestedQuery);
        shouldFitler.should(QueryBuilders.matchQuery("title",keyWord).analyzer("ik_max_word").boost(1000000.0f));
        return shouldFitler;
    }


    //todo 确认匹配字段
    private BoolQueryBuilder buildMyFavoriteQuery(String keyWord){
        BoolQueryBuilder shouldFitler = QueryBuilders.boolQuery();
        shouldFitler.should(QueryBuilders.wildcardQuery("title.raw", "*" + keyWord + "*"));

        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        for (String field : MY_FAVORITE_NESTED_SET) {
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field+".raw", "*" + keyWord + "*");
            nestedShould.should(wildcardQueryBuilder);
        }
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("materialContents", nestedShould, ScoreMode.None);
        shouldFitler.should(nestedQuery);

        //构建商品的特殊排序条件
        BoolQueryBuilder inspirationNestedShould = QueryBuilders.boolQuery();
        BoolQueryBuilder productShould = QueryBuilders.boolQuery();
        for (String productField : MY_FAVORITE_PRODUCT_SET) {
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(productField + ".raw", "*" + keyWord + "*");
            productShould.should(wildcardQueryBuilder);
        }
        BoolQueryBuilder productBoolQueryBuilder = QueryBuilders.boolQuery().must(productShould).must(QueryBuilders.termQuery("contentType", 3));
        shouldFitler.should(productBoolQueryBuilder);
        //灵感案例
        TermsQueryBuilder secondCategoryFilter = QueryBuilders.termsQuery("inspirationCase.secondCategoryId", INSPIRATION_SECOND_CATEGORY_IDS);
        WildcardQueryBuilder inCaseTitleFilter = QueryBuilders.wildcardQuery("inspirationCase.title.raw", "*" + keyWord + "*");
        inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(inCaseTitleFilter));
        for (String field : INSPIRATION_FIELD_SET) {
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field+".raw", "*" + keyWord + "*").boost(PHRASE_PREFIX_SCORE);
            inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(wildcardQueryBuilder));
        }
        NestedQueryBuilder inspirationNestedQuery = QueryBuilders.nestedQuery("inspirationCase", inspirationNestedShould, ScoreMode.Total);
        shouldFitler.should(inspirationNestedQuery);

        //构建案例的特殊排序条件
        BoolQueryBuilder caseShould = QueryBuilders.boolQuery();
        for (String field : MY_FAVORITE_CASE_SET) {
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field + ".raw", "*" + keyWord + "*");
            caseShould.should(wildcardQueryBuilder);
        }
        BoolQueryBuilder caseBoolQueryBuilder = QueryBuilders.boolQuery().must(caseShould).must(QueryBuilders.termQuery("contentType", 2));
        shouldFitler.should(caseBoolQueryBuilder);

        return shouldFitler;
    }

    /**
     * 精确匹配和模糊匹配组合起来的，精确匹配的权重高一些，这样可以排在前面，如果精确匹配不到的话，返回的都是模糊匹配的结果
     * 1、最高优先级 标题
     * @param keyWord
     * @return
     */
    //TODO 新增查询字段了要更新buildMatchQuery、buildHighlightBuilder，这个是查询切词需要的
    private BoolQueryBuilder buildPhrasePrefixMatchFirstQuery(String keyWord){
        String boostKeyword = this.boostKeyword(keyWord);
        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldFitler = QueryBuilders.boolQuery();
        for (String field : ES_FIELD_SET) {
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(field, boostKeyword);
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field+".raw", "*" + keyWord + "*").boost(PHRASE_PREFIX_SCORE);
            if (SCORE_ES_FIELD_SET.contains(field)) {
                matchQueryBuilder.boost(5.0f);
            }
            nestedShould.should(wildcardQueryBuilder);
            nestedShould.should(matchQueryBuilder);
        }

        //构建商品的特殊排序条件
        BoolQueryBuilder inspirationNestedShould = QueryBuilders.boolQuery();
        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("contentType", 3);
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] lowScoreFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(5.0f))
        };
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] functions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(1000.0f))
        };
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] phrasePrefixFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(99999999.0f))
        };
        // 商品的名称和其他类型的title保持同一优先级，其他的字段权重和别的字段保持一致
        for (String productField : PRODUCT_SCORE_FIELD_SET) {
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(productField, boostKeyword);
            FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, lowScoreFunctions);
            if(productField.equals("productModel")){
                functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, functions);
            }
            shouldFitler.should(functionScoreQueryBuilder);
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(productField + ".raw", "*" + keyWord + "*");
            FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(wildcardQueryBuilder, phrasePrefixFunctions);
            shouldFitler.should(functionScoreQuery);
        }
        //灵感案例，titile最高优先级
        TermsQueryBuilder secondCategoryFilter = QueryBuilders.termsQuery("inspirationCase.secondCategoryId", INSPIRATION_SECOND_CATEGORY_IDS);
        MatchQueryBuilder inCaseTitleFilter = QueryBuilders.matchQuery("inspirationCase.title", boostKeyword).analyzer("ik_max_word").boost(1000.0f);
        inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(inCaseTitleFilter));
        WildcardQueryBuilder inCaseTitleRawFilter = QueryBuilders.wildcardQuery("inspirationCase.title.raw", "*" + keyWord + "*").boost(PHRASE_FIRST_SCORE);
        inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(inCaseTitleRawFilter));
        for (String field : INSPIRATION_FIELD_SET) {
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(field, boostKeyword);
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field+".raw", "*" + keyWord + "*").boost(PHRASE_PREFIX_SCORE);
            inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(wildcardQueryBuilder));
            inspirationNestedShould.should(QueryBuilders.boolQuery().must(secondCategoryFilter).must(matchQueryBuilder));
        }


        //构建案例的特殊排序条件
        TermQueryBuilder caseQueryBuilder = QueryBuilders.termQuery("contentType", 2);
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] caseLowScoreFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(caseQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(5.0f))
        };
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] casePhrasePrefixFunctions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(caseQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(PHRASE_PREFIX_SCORE))
        };
        for (String field : CASE_SCORE_FIELD_SET) {
            if(field.equals("originalId") ){
                if(NumberUtil.isNumber(keyWord)){
                    TermQueryBuilder originalIdQueryBuilder = QueryBuilders.termQuery(field, keyWord).boost(PHRASE_PREFIX_SCORE);
                    BoolQueryBuilder must = QueryBuilders.boolQuery().must(originalIdQueryBuilder).must(caseQueryBuilder);
                    shouldFitler.should(must);
                }
                continue;
            }
            MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery(field, boostKeyword);
            FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, caseLowScoreFunctions);
            shouldFitler.should(functionScoreQueryBuilder);
            WildcardQueryBuilder wildcardQueryBuilder = QueryBuilders.wildcardQuery(field + ".raw", "*" + keyWord + "*");
            FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(wildcardQueryBuilder, casePhrasePrefixFunctions);
            shouldFitler.should(functionScoreQuery);
        }


        nestedShould.should(QueryBuilders.matchQuery("materialContents.caseType", boostKeyword));
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("materialContents", nestedShould, ScoreMode.Total);
        NestedQueryBuilder inspirationNestedQuery = QueryBuilders.nestedQuery("inspirationCase", inspirationNestedShould, ScoreMode.Total);
        shouldFitler.should(inspirationNestedQuery);
        shouldFitler.should(nestedQuery);
        shouldFitler.should(QueryBuilders.matchQuery("title", boostKeyword).analyzer("ik_max_word").boost(1000.0f));
        shouldFitler.should(QueryBuilders.wildcardQuery("title.raw", "*" + keyWord + "*").boost(PHRASE_FIRST_SCORE));
        return shouldFitler;
    }

    private BoolQueryBuilder buildMatchPhrasePrefixQuery(String keyWord) {
        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldFitler = QueryBuilders.boolQuery();
        for (String field : ES_FIELD_SET) {
            MatchPhrasePrefixQueryBuilder matchQueryBuilder = QueryBuilders.matchPhrasePrefixQuery(field, keyWord);
            if (SCORE_ES_FIELD_SET.contains(field)) {
                matchQueryBuilder.boost(5.0f);
            }
            nestedShould.should(matchQueryBuilder);
        }


        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("contentType", 3);
        termQueryBuilder.boost(100.0f);
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] functions = {
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(1000000.0f))
        };
        for (String productField : PRODUCT_SCORE_FIELD_SET) {
            MatchPhrasePrefixQueryBuilder matchQueryBuilder = QueryBuilders.matchPhrasePrefixQuery(productField, keyWord);
            FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, functions);
            shouldFitler.should(functionScoreQueryBuilder);
        }

        nestedShould.should(QueryBuilders.matchQuery("materialContents.caseType", keyWord));
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("materialContents", nestedShould, ScoreMode.Total);
        shouldFitler.should(nestedQuery);
        shouldFitler.should(QueryBuilders.matchPhrasePrefixQuery("title", keyWord).analyzer("ik_max_word").boost(100000.0f));
        return shouldFitler;
    }


    @Deprecated
    public PageInfo<MaterialContentSearchResult> searchMaterialContent2(MaterialContentSearchRequest request) {
        if (request == null || request.getKeyword() == null) {
            throw new BusinessException("查询参数错误");
        }


        BoolQueryBuilder filterQuery = QueryBuilders.boolQuery();

        //分公司权限
        // List<Long> companyAuthorIdList = userAuthorConfig.queryCompanyAuthorIdList(1);
/*        if(CollectionUtils.isNotEmpty(companyAuthorIdList)){
            companyAuthorIdList.add(0L);
            filterQuery.filter(QueryBuilders.termsQuery("companyIds",companyAuthorIdList));
        }*/
        if (request.getTreeId() != null) {
            filterQuery.filter(QueryBuilders.termQuery("treeId", request.getTreeId()));
        }
        if (request.getNode1Id() != null) {
            filterQuery.filter(QueryBuilders.termQuery("node1Id", request.getNode1Id()));
        }
        if (request.getNode2Id() != null) {
            filterQuery.filter(QueryBuilders.termQuery("node2Id", request.getNode2Id()));
        }
        if (request.getNode3Id() != null) {
            filterQuery.filter(QueryBuilders.termQuery("node3Id", request.getNode3Id()));
        }
        if (CollectionUtils.isNotEmpty(request.getContentTypeList())) {
            TermsQueryBuilder termsQuery = QueryBuilders.termsQuery("materialType", request.getContentTypeList());
            filterQuery.filter(termsQuery);
        }
        PageInfo<MaterialContentSearchResult> pageInfo = new PageInfo<>(request.getPage(), request.getSize());
        BoolQueryBuilder shouldFitler = QueryBuilders.boolQuery();
        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        for (String field : ES_FIELD_SET) {
            MatchPhrasePrefixQueryBuilder matchQueryBuilder = QueryBuilders.matchPhrasePrefixQuery(field, request.getKeyword());
            if (SCORE_ES_FIELD_SET.contains(field)) {
                matchQueryBuilder.boost(5.0f);
/*                if("materialContents.contentTitle".equals(field)){
                    TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("materialContents.contentType", 1);
                    termQueryBuilder.boost(100.0f);
                    FunctionScoreQueryBuilder.FilterFunctionBuilder[] functions = {
                            new FunctionScoreQueryBuilder.FilterFunctionBuilder(termQueryBuilder, ScoreFunctionBuilders.weightFactorFunction(1000000.0f))
                    };
                    FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(matchQueryBuilder, functions);
                    nestedShould.should(functionScoreQueryBuilder);
                }*/
            }
            nestedShould.should(matchQueryBuilder);
        }
        nestedShould.should(QueryBuilders.matchQuery("materialContents.caseType", request.getKeyword()));
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("materialContents", nestedShould, ScoreMode.Total);
        shouldFitler.should(nestedQuery);
        shouldFitler.should(QueryBuilders.matchPhrasePrefixQuery("title", request.getKeyword()).analyzer("ik_max_word").boost(100000.0f));


        Date now = new Date();
        String format = DATE_TIME_FORMATTER.format(now);
        //todo 有效值为空的数据也要查出来
        BoolQueryBuilder endTimeBuilder = QueryBuilders.boolQuery();
        endTimeBuilder.should(QueryBuilders.rangeQuery("effectiveEndTime").gte(format).lte("3000-12-31 23:59:59"))
                .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("effectiveEndTime")));
        BoolQueryBuilder startTimeBuilder = QueryBuilders.boolQuery();
        startTimeBuilder.should(QueryBuilders.rangeQuery("effectiveStartTime").gte("2023-09-01 00:00:00").lte(format))
                .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("effectiveStartTime")));
        filterQuery.must(shouldFitler)
                .filter(endTimeBuilder)
                .filter(startTimeBuilder);

        SearchRequest searchRequest = new SearchRequest(CONTENT_ES_ALIAS);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(filterQuery)
                .trackTotalHits(true)
                .from(Math.toIntExact(pageInfo.getOffset()))
                .size(Math.toIntExact(pageInfo.getSize()))
                .sort("_score", SortOrder.DESC)
                .sort("id", SortOrder.DESC);
        searchRequest.source(sourceBuilder);
        System.out.println("searchRequest:{}" + searchRequest.source().toString());
        try {
            SearchResponse searchResponse = esClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse != null) {
                long total = searchResponse.getHits().getTotalHits().value;
                if (total == 0) {
                    return pageInfo;
                }

                pageInfo.setTotal(total);
                SearchHit[] hits = searchResponse.getHits().getHits();
                List<MaterialContentSearchResult> resultList = new ArrayList<>();

                for (SearchHit hit : hits) {
                    MaterialContentSearchResult result = JSON.parseObject(hit.getSourceAsString(), MaterialContentSearchResult.class);
                    resultList.add(result);
                }
                pageInfo.setRecords(resultList);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return pageInfo;
    }
}
