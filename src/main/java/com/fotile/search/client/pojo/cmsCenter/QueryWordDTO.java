package com.fotile.search.client.pojo.cmsCenter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fotile.framework.core.common.PageInfo;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

/**
 * 百景图&内容搜索的查询日志对象
 * <AUTHOR>
 * @data 2024/3/19 13:29
 */
@Data
public class QueryWordDTO {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 搜索词
     */
    private String word;

    /**
     * 搜索类型,1:内容、2：白景图-产品、3：白景图：案例
     */
    private Integer logType=1;

    /**
     * 创建人
     */
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 修改人
     */
    private String modifiedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    /**
     * 修改时间
     */
    private Date modifiedTime;

    /**
     * 逻辑删除标识
     */
    private Integer isDeleted;

    /**
     * 返回条数
     */
    private Long resultCount;
    /**
     * 返回数据体
     */
    private String resultData;
    /**
     * 登陆人名称
     */
    @FieldEncrypt
    private String userName;
    /**
     * user_entity_extend表中first_name值
     */
    @FieldEncrypt
    private String firstName;
    /**
     * 大区编码
     */
    private String areaCode;
    /**
     * 大区名
     */
    private String areaName;
    /**
     * 分公司id
     */
    private Long companyId;
    /**
     * 分公司名称
     */
    private String companyName;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 门店编码
     */
    private String storeCode;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 业务员id
     */
    private Long chargeUserId;
    /**
     * 业务员名称
     */
    @FieldEncrypt
    private String chargeUserName;
    /**
     * 业务员编码
     */
    private String chargeCode;
    /**
     * 业务员手机号
     */
    @FieldEncrypt
    private String chargePhone;
    /**
     * 岗位:1:客户经理；2：厨电顾问，同业务员边存取一致
     */
    private Long station;


    private Long treeId;

    /**
     * 返回值信息
     */
    private PageInfo pageInfo;
}
