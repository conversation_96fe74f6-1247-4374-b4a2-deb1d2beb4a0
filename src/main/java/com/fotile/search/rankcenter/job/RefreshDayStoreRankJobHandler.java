package com.fotile.search.rankcenter.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fotile.search.common.constant.CommonConstant;
import com.fotile.search.config.SearchConfigEntity;
import com.fotile.search.rankcenter.pojo.entity.AppPersonalRank;
import com.fotile.search.rankcenter.pojo.entity.AppStoreRank;
import com.fotile.search.rankcenter.service.HistoryRankService;
import com.fotile.search.util.LogUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 排行榜-重刷门店实时数据
 *
 * <AUTHOR>
 * @since 2022-06-24
 */
@Component
@Slf4j
public class RefreshDayStoreRankJobHandler extends IJobHandler {
    @Autowired
    private HistoryRankService historyRankService;
    @Autowired
    private SearchConfigEntity searchConfig;

    @Override
    @XxlJob("RefreshDayStoreRankJob")
    public ReturnT<String> execute(String s) throws Exception {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        String beginDateStr = null;
        String endDateStr = null;
        Date beginDate = null;
        Date endDate = null;
        Integer refreshDay = null;

        try {
            if (StringUtils.isNotEmpty(s)) {
                JSONObject params = JSON.parseObject(s);

                beginDateStr = params.getString("beginDate");
                endDateStr = params.getString("endDate");
                refreshDay = params.getInteger("refreshDay");
            }

            if (StringUtils.isNotBlank(beginDateStr) || StringUtils.isNotBlank(endDateStr) || Objects.nonNull(refreshDay)) {

                //若指定日期不为空，则以此（beginDate、endDate）为第一优先级，刷新数据
                if (StringUtils.isNotBlank(beginDateStr) || StringUtils.isNotBlank(endDateStr)) {
                    if (StringUtils.isBlank(beginDateStr) || StringUtils.isBlank(endDateStr)) {
                        throw new RuntimeException("入参的开始或结束日期都不能为空");
                    }
                } else {
                    //根据refreshDay天数计算beginDateStr、endDateStr，refreshDay填写的值必须为负整数
                    beginDateStr = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), refreshDay), CommonConstant.DateFormat.Y_M_D);
                    endDateStr = DateUtil.format(DateUtil.yesterday(), CommonConstant.DateFormat.Y_M_D);
                }

                beginDate = DateUtil.parse(beginDateStr);
                endDate = DateUtil.parse(endDateStr);

                List<DateTime> dateTimes = DateUtil.rangeToList(beginDate, endDate, DateField.DAY_OF_YEAR);
                Date nowDate = DateUtil.parse(DateUtil.today(), CommonConstant.DateFormat.Y_M_D);
                for (DateTime dateTime : dateTimes) {
                    if (DateUtil.compare(dateTime, nowDate) <= 0) {
                        //获取业务员排行数据
                        List<AppStoreRank> rankList = historyRankService.refreshDayStoreRankAll(
                                DateUtil.format(dateTime, CommonConstant.DateFormat.Y_M_D),
                                DateUtil.format(dateTime, CommonConstant.DateFormat.Y_M_D));
                        if (CollectionUtils.isNotEmpty(rankList)) {
                            //批量插入修改，不能用getId,要根据storeid及createdDate生成es的_id
                            historyRankService.refreshResultBulkInsert(searchConfig.getStoreIndexName(), rankList);
                        }
                    }
                }
            } else {
                //统计日期
                DateTime statisticsDate = DateUtil.yesterday();
                beginDateStr = DateUtil.format(statisticsDate, CommonConstant.DateFormat.Y_M_D);
                endDateStr = DateUtil.format(statisticsDate, CommonConstant.DateFormat.Y_M_D);
                //获取业务员排行数据
                List<AppStoreRank> rankList = historyRankService.refreshDayStoreRankAll(
                        beginDateStr,
                        endDateStr);
                if (CollectionUtils.isNotEmpty(rankList)) {
                    //批量插入修改，不能用getId,要根据storeid及createdDate生成es的_id
                    historyRankService.refreshResultBulkInsert(searchConfig.getStoreIndexName(), rankList);
                }
            }

        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw, true));
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", sw);
            String datePeriod = (StringUtils.isBlank(beginDateStr) ? "" : beginDateStr) + " - " + (StringUtils.isBlank(endDateStr) ? "" : endDateStr);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重刷每日门店排行实时数据执行失败, 统计日期 = " + datePeriod + ", errMsg = " + sw);
        }

        String datePeriod = (StringUtils.isBlank(beginDateStr) ? "" : beginDateStr) + " - " + (StringUtils.isBlank(endDateStr) ? "" : endDateStr);
        log.error("重刷每日门店排行实时数据执行成功, 统计日期 = " + datePeriod);
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "重刷每日门店排行实时数据执行成功, 统计日期 = " + datePeriod);
    }
}
