package com.fotile.lotterycenter.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import java.util.Date;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动次数获取时间配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "`lotterycenter`.`activity_frequency_time_config`")
public class ActivityFrequencyTimeConfigEntity extends AuditingEntity {
    /**
     * 次数上线
     */
    @TableField(value = "`online_use`")
    private Integer onlineUse;

    /**
     * 次数使用类别  0 每日  1.指定日期（2023-08-01）    2.指定星期（指定每周的第 1 天）   3.指定天（例如指定每月的第 10 天） 4.不指定  5.首次
     */
    @TableField(value = "`frequency_time_category`")
    private Integer frequencyTimeCategory;

    /**
     * 时间(1.如果是日期格式则存储 日期   如果是周  则用 编码表示例如 mon Tues  Wed Thur  Fri Sat sun) 每日则为 null
     */
    @TableField(value = "`frequency_time`")
    @Size(max = 32, message = " 时间(1.如果是日期格式则存储 日期   如果是周  则用 编码表示例如 mon Tues  Wed Thur  Fri Sat sun) 每日则为 null最大长度要小于 32")
    private String frequencyTime;

    /**
     * 规则关联 id
     */
    @TableField(value = "`frequency_config_id`")
    private Long frequencyConfigId;
}