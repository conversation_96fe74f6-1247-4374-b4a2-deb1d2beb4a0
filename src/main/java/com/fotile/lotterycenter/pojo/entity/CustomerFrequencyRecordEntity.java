package com.fotile.lotterycenter.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动次数流水表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "`lotterycenter`.`customer_frequency_record`")
public class CustomerFrequencyRecordEntity extends AuditingEntity {
    /**
     * 活动 id
     */
    @TableField(value = "`actitvity_id`")
    @NotNull(message = "活动 id不能为null")
    private Long actitvityId;

    /**
     * 顾客 id
     */
    @TableField(value = "`customer_id`")
    @NotNull(message = "顾客 id不能为null")
    private Long customerId;

    /**
     * 获取次数 code: enter_activity 无条件抽奖 login 登录抽奖  friend_help 好友助力
     */
    @TableField(value = "`frequency_code`")
    @Size(max = 32, message = "获取次数 code: enter_activity 无条件抽奖 login 登录抽奖  friend_help 好友助力最大长度要小于 32")
    @NotBlank(message = "获取次数 code: enter_activity 无条件抽奖 login 登录抽奖  friend_help 好友助力不能为空")
    private String frequencyCode;

    /**
     * 次数
     */
    @TableField(value = "`number`")
    private Integer number = 1;

}