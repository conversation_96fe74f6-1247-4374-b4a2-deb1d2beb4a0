package com.fotile.lotterycenter.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface LotteryChannel {
    /**
     * 处理放松【发送通道】
     */
    String DISPOSE_SEND_MSG_OUTPUT = "dispose_send_msg_output";
    /**
     * 处理放松【发送通道】
     */
    String DISPOSE_SEND_MSG_DELAY_OUTPUT = "dispose_send_msg_delay_output";

    /**
     * 发待处理消息【接收通道】
     */
    String DISPOSE_SEND_MSG_IPUNT = "dispose_send_msg_input";
    /**
     * 推送流水【接收通道】
     */
    String PUSH_FLOW_IPUNT = "push_flow_input";

    /**
     * 推送流水【发送通道】
     */
    String PUSH_FLOW_OUTPUT = "push_flow_output";

    String SEND_BENEFIT_AMOUNT_TASK_OUTPUT = "send_benefit_amount_task_output";
    String EXPEND_BENEFIT_AMOUNT_TASK_OUTPUT = "expend_benefit_amount_task_output";

    @Output(EXPEND_BENEFIT_AMOUNT_TASK_OUTPUT)
    MessageChannel sendEXPEND_BENEFIT_AMOUNT_TASK_OUTPUT();

    @Output(SEND_BENEFIT_AMOUNT_TASK_OUTPUT)
    MessageChannel sendSEND_BENEFIT_AMOUNT_TASK_OUTPUT();



    @Output(PUSH_FLOW_OUTPUT)
    MessageChannel sendPushFlowMessage();
    @Input(PUSH_FLOW_IPUNT)
    SubscribableChannel pushFlowInput();

    @Output(DISPOSE_SEND_MSG_OUTPUT)
    MessageChannel sendDisposeSendMsgMessage();

    @Output(DISPOSE_SEND_MSG_DELAY_OUTPUT)
    MessageChannel sendDisposeSendMsgDelayMessage();

    /**
     * 发消息【j接受】
     *
     * @return
     */
    @Input(DISPOSE_SEND_MSG_IPUNT)
    SubscribableChannel disposeSendMsgInput();


}