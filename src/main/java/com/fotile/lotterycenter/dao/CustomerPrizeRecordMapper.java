package com.fotile.lotterycenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.lotterycenter.pojo.entity.CustomerPrizeRecordEntity;

import java.util.List;

@DruidTxcMultiDataSourceAnnontation("sixth")
public interface CustomerPrizeRecordMapper  {
    /**
     * 查询顾客中奖流水
     * @param entity
     * @return
     */
    @DruidTxcMultiDataSourceAnnontation("sixth")
    Integer queryCustomerPrizeRecord(CustomerPrizeRecordEntity entity);
}