package com.fotile.questioncenter.challenge.pojo.dto;

import com.fotile.framework.core.common.AuditingEntity;
import com.fotile.questioncenter.exam.pojo.dto.ExamSettingInDto;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * 顾客挑战-得分规则
 */
@Data
public class ChallengeRuleInDto extends AuditingEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 顾客挑战考试ID(cop_exam.id)
     */
    private Long examId;

    /**
     * 考试挑战成功分数线(大于等于此值则通过,小于则表示未通过)
     */
     private BigDecimal passValues;

    /**
     * 考试通过显示文案
     */
     private String succeedDisplayText;

    /**
     * 考试通过显示图片
     */
     private String succeedDisplayImg;

    /**
     * 考试未通过显示文案
     */
     private String failedDisplayText;

    /**
     * 考试未通过显示图片
     */
     private String failedDisplayImg;
}
