package com.fotile.questioncenter.challenge.pojo.dto;

import com.fotile.questioncenter.exam.pojo.dto.ExamSettingInDto;
import lombok.Data;

import javax.persistence.Transient;

/**
 * 顾客挑战-考试设置
 */
@Data
public class ChallengeSettingInDto extends ExamSettingInDto {

    private static final long serialVersionUID = 1L;

    /**
     * 考试ID(cop_exam.id)
     */
    @Transient
    private Long examId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色图片
     */
    private String roleImg;

    /**
     * 角色说明
     */
    private String roleDescription;

    /**
     * 考试说明
     */
    private String examDescription;

    /**
     * 挑战难度星级(1~4)
     */
    private Integer difficultyStars;

    /**
     * 挑战难度星级名称
     */
    private String difficultyStarsName;

}
