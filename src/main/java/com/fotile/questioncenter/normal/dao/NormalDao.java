package com.fotile.questioncenter.normal.dao;

import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.questioncenter.exam.pojo.entity.CopExamGradeConfigEntity;
import com.fotile.questioncenter.exam.pojo.entity.CopExamNormalEntity;
import com.fotile.questioncenter.exam.pojo.entity.CopExamNormalRuleEntity;
import com.fotile.questioncenter.normal.pojo.dto.NormalRuleInDto;
import com.fotile.questioncenter.normal.pojo.dto.NormalSettingInDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Set;

/**
 * 普通考试dao
 *
 * <AUTHOR>
 */
@Validated
public interface NormalDao {

    /**
     * 新增普通考试-考试设置
     */
    void addNormalSetting(@Param("examId") Long examId,
                          @Param("inDto") NormalSettingInDto normalSettingInDto,
                          @Param("operatorId") String operatorId);

    /**
     * 根据exam_id查询普通考试设置
     */
    NormalSettingInDto findNormalSettingByExamId(Long examId);

    /**
     * 修改普通考试-考试设置
     */
    Integer updateNormalSetting(@Param("examId") Long examId,
                                @Param("inDto") NormalSettingInDto normalSettingInDto,
                                @Param("operatorId") String operatorId);

    /**
     * 考题-删除考试
     */
    void delNormalExam(@Param("examId") Long examId,
                       @Param("operatorId") String operatorId);

    /**
     * 考题-新增考题
     */
    void addNormalQuestion(@Param("examId") Long examId,
                           @Param("questionIdList") List<Long> questionIdList,
                           @Param("user") UserAuthor userAuthor);

    /**
     * 考题-删除考题
     */
    void delNormalQuestion(@Param("questionId") Long questionId,
                           @Param("user") UserAuthor userAuthor);

    /**
     * 新增普通考试-得分规则
     */
    void addNormalRule(@Param("inDto") NormalRuleInDto normalRuleInDto,
                       @Param("operatorId") String operatorId);

    /**
     * 根据exam_id查询普通考试得分规则
     */
    NormalRuleInDto findNormalRuleByExamId(Long examId);

    List<CopExamGradeConfigEntity> getCopExamGradeConfigList(@Param("examId") Long examId,
                                                             @Param("ruleId") Long ruleId,
                                                             @Param("tid") String tid);

    Integer batchInsertCopExamGradeConfig(@Param("list") List<CopExamGradeConfigEntity> list);

    Integer batchUpdateCopExamGradeConfig(@Param("list") List<CopExamGradeConfigEntity> list);

    Integer batchDelCopExamGradeConfig(@Param("examId") Long examId,
                                       @Param("examRuleIds") Set<Long> examRuleIds,
                                       @Param("ids") Set<Long> ids,
                                       @Param("updateTid") String updateTid,
                                       @Param("operatorId") String operatorId);

    /**
     * 修改普通考试-得分规则
     */
    Integer updateNormalRule(@Param("inDto") NormalRuleInDto normalRuleInDto,
                             @Param("operatorId") String operatorId);

    /**
     * 获取基础闯关考试信息(cop_exam_normal)
     *
     * @param examId 考试ID
     * @return CopExamNormalEntity
     */
    CopExamNormalEntity getCopExamNormalEntity(@Param("examId") Long examId);

    /**
     * 基础闯关-得分规则信息
     *
     * @param examId 考试ID
     * @return CopExamNormalRuleEntity
     */
    CopExamNormalRuleEntity getCopExamNormalRuleEntity(@Param("examId") Long examId);
}
