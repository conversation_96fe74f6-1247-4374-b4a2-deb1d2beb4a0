package com.fotile.questioncenter.exam.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@Data
public class FindPageAllOutDto {
    
	/**
	 * id
	 */
	private Long id;

	/**
	 * 考试ID
	 */
	private String code;

	/**
	 * 考试名称
	 */
	private  String name;

	/**
	 * 考试类型(1:普通考试,2:顾客挑战,3:抽考)
	 */
	private Integer type;

	/**
	 * 考试分类(来源字典-cop_exam_category.value_code)
	 */
	private String category;

	/**
	 * 考试分类(来源字典-cop_exam_category.value_name)
	 */
	private String categoryName;

	/**
	 * 考试开始时间
	 */
	private Date startTime;

	/**
	 * 考试结束时间
	 */
	private Date endTime;

	/**
	 * 状态(0:下架,1:上架,默认0)
	 */
	private Integer onlineStatus;

	/**
	 * 排序标识(数字越大越靠前)
	 */
	private Integer sort;

	/**
	 * 最新编辑人
	 */
	@FieldEncrypt
	private String modifiedUserName;

	/**
	 * 最新编辑时间
	 */
	private String modifiedDate;

}