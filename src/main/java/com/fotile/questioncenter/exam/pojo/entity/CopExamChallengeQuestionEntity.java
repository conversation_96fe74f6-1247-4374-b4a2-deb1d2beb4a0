package com.fotile.questioncenter.exam.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;

/**
 * 顾客挑战考试题库表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@TableName(value = "cop_exam_challenge_question", schema = "questioncenter")
public class CopExamChallengeQuestionEntity extends AuditingEntity {

    /**
     * 考试信息表ID(cop_exam.id)
     **/
    @TableField(value = "exam_id")
    private Long examId;

    /**
     * 题库题目ID(cop_question.id)
     **/
    @TableField(value = "question_id")
    private Long questionId;
}
