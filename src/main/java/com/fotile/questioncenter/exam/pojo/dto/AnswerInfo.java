package com.fotile.questioncenter.exam.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.questioncenter.exam.pojo.dto
 * @date 2022/4/27 22:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AnswerInfo implements Serializable {
    /**
     * 答案Id
     */
    private Long id;

    /**
     * 题目ID(cop_question.id)
     **/
    private Long questionId;

    /**
     * 选择编码(A/B/C/D...)
     **/
    private String optionCode;

    /**
     * 选项值
     **/
    private String optionValue;

    /**
     * 选中得分
     **/
    private BigDecimal selectedPoint;

    /**
     * 选中跳转到指定题目ID(cop_question.id)
     **/
    private Long gotoQuestionId;

    /**
     * 选中跳转到指定题目数组下标(前端使用)
     **/
    private Integer jumpIndex;

    /**
     * 排序标识(数字越大越靠前)
     **/
    private Integer sort;

    /**
     * 是否正确答案(0:否,1:是)
     **/
    private Integer isCorrect;
}
