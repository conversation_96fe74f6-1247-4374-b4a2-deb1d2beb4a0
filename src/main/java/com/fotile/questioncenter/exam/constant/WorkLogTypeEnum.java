package com.fotile.questioncenter.exam.constant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.usercenter.worklog.constant
 * @date 2022/6/14 10:21
 */
public enum WorkLogTypeEnum {

    /**
     * 默认0(日志不入库)
     */
    DEFAULT(0, ""),

    /**
     * 创建线索 100
     */
    CLUE_CREATED(100, "创建线索"),

    /**
     * 编辑线索 101
     */
    CLUE_UPDATED(101, "编辑线索"),

    /**
     * 线索写跟进 102
     */
    CLUE_FOLLOW_UP(102, "线索写跟进"),

    /**
     * 订单录入 200
     */
    ORDER_CREATED(200, "订单录入"),

    /**
     * 定位打卡 300
     */
    LOCATION_CLOCKED(300, "定位打卡"),

    /**
     * 创建家装公司 400
     */
    DECORATE_CREATED(400, "创建家装公司"),

    /**
     * 家装公司写跟进 401
     */
    DECORATE_FOLLOW_UP(401, "家装公司写跟进"),

    /**
     * 创建家装公司设计师 402
     */
    DECORATE_DESIGNER_CREATED(402, "创建家装公司设计师"),

    /**
     * 家装公司设计师写跟进 403
     */
    DECORATE_DESIGNER_FOLLOW_UP(403, "家装公司设计师写跟进"),

    /**
     * 创建异业三工 404
     */
    DIFF_INDUSTRY_CREATED(404, "创建异业三工"),

    /**
     * 异业三工写跟进 405
     */
    DIFF_INDUSTRY_FOLLOW_UP(405, "异业三工写跟进"),

    /**
     * 训练基地 500
     */
    COP_EXAM(500, "训练基地"),

    /**
     * 门店样机盘点 600
     */
    STORE_PROTO(600, "门店样机盘点"),

    /**
     * 楼盘地图跟进 601
     */
    ESTATE_MAP_FOLLOW_UP(601, "楼盘地图跟进"),

    /**
     * 发送离店短信 602
     */
    LEAVE_STORE_SMS_SEND(602, "发送离店短信");

    /**
     * 工作日志类型
     */
    private Integer logType;

    /**
     * 工作日志类型名称
     */
    private String logTypeName;

    WorkLogTypeEnum(Integer logType, String logTypeName) {
        this.logType = logType;
        this.logTypeName = logTypeName;
    }

    /**
     * 获取工作日志枚举
     */
    public static WorkLogTypeEnum getWorkLogType(Integer logType) {
        if (logType == null || logType <= 0) {
            return WorkLogTypeEnum.DEFAULT;
        }

        return Arrays.stream(values())
                .filter(x -> logType.equals(x.getLogType()))
                .findFirst()
                .orElse(WorkLogTypeEnum.DEFAULT);
    }

    /**
     * 获取工作日志类型名称
     */
    public static String getWorkLogTypeName(Integer logType) {
        return getWorkLogType(logType).getLogTypeName();
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public String getLogTypeName() {
        return logTypeName;
    }

    public void setLogTypeName(String logTypeName) {
        this.logTypeName = logTypeName;
    }
}
