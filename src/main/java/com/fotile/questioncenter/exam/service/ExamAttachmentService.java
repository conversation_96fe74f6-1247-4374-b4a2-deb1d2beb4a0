package com.fotile.questioncenter.exam.service;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.questioncenter.exam.constant.ExamAttachmentEnum;
import com.fotile.questioncenter.exam.dao.ExamAttachmentDao;
import com.fotile.questioncenter.exam.pojo.dto.ExamAttachmentInfo;
import com.fotile.questioncenter.exam.pojo.dto.ExamAttachmentQuery;
import com.fotile.questioncenter.exam.pojo.entity.CopExamAttachmentEntity;
import com.fotile.questioncenter.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公用附件服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class ExamAttachmentService {

    @Autowired
    private ExamAttachmentDao attachmentDao;

    /**
     * 获取附件总记录数
     *
     * @param refTable 关联表名称
     * @param refId    关联表主键Id
     * @param refType  关联类型(详见AttachmentEnum枚举)
     */
    public Long getExamAttachmentCount(String refTable, Long refId, Integer refType) {
        return attachmentDao.getExamAttachmentCount(refTable, refId, refType);
    }

    /**
     * 获取附件分页数据
     *
     * @param refTable 关联表名称
     * @param refId    关联表主键Id
     * @param refType  关联类型(详见AttachmentEnum枚举)
     * @param page     当前页(默认1)
     * @param size     页大小(默认10)
     */
    public PageInfo<CopExamAttachmentEntity> getExamAttachmentList(String refTable, Long refId, Integer refType, Integer page, Integer size) {
        if (page == null) {
            page = 1;
        }

        if (size <= 0) {
            page = 10;
        }

        long total = attachmentDao.getExamAttachmentCount(refTable, refId, refType);
        if (total <= 0) {
            return null;
        }

        PageInfo<CopExamAttachmentEntity> pageInfo = new PageInfo<>(page, size, total);
        pageInfo.setRecords(attachmentDao.getExamAttachmentList(refTable, refId, refType, pageInfo));

        return pageInfo;
    }

    /**
     * 新增附件
     *
     * @param attachment 附件信息
     */
    public Long insertExamAttachment(ExamAttachmentInfo attachment) {
        if (attachment == null || StringUtils.isBlank(attachment.getUrl())) {
            return 0L;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        return attachmentDao.insertExamAttachment(attachment, user.get("userid"));
    }

    /**
     * 批量插入附件(同refId)
     *
     * @param batch 附件信息集合
     */
    public Long batchInsertExamAttachment(List<ExamAttachmentInfo> batch) {
        if (batch == null || batch.size() <= 0) {
            return 0L;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        return attachmentDao.batchInsertExamAttachment(batch, user.get("userid"));
    }

    /**
     * 批量插入附件(同refId)
     *
     * @param batch 附件信息集合
     */
    public Long batchInsertExamAttachmentByRefId(List<ExamAttachmentInfo> batch, Long refId) {
        if (batch == null || batch.size() <= 0) {
            return 0L;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        return attachmentDao.batchInsertExamAttachmentByRefId(batch, refId, user.get("userid"));
    }

    /**
     * 逻辑删除附件
     *
     * @param id 附件id(attachment.id)
     */
    public Long delExamAttachment(Long id) {
        if (id == null || id <= 0) {
            return 0L;
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        return attachmentDao.delExamAttachment(id, user.get("userid"));
    }

    /**
     * 批量逻辑删除附件。特别说明：[ids]与[ae,refId]不能同时为null
     *
     * @param ids   附件Id集合(可为null)
     * @param ae    附件类型(AttachmentEnum枚举,可为null)
     * @param refId 引用表主键Id(可为null)
     */
    public void batchDelExamAttachment(List<Long> ids, ExamAttachmentEnum ae, Long refId) {
        boolean canDel = false;
        if (ids != null && ids.size() >= 0) {
            canDel = true;
        } else if (ae != null && ae != ExamAttachmentEnum.NULL && refId != null && refId > 0) {
            canDel = true;
        }

        if (canDel) {
            Map<String, String> user = CommonUtils.getAuthorUser();
            attachmentDao.batchDelExamAttachment(ids, ae.getRefTable(), refId, ae.getType(), user.get("userid"));
        }
    }

    /**
     * 通过单个枚举+List[refId]批量查询附件
     */
    public List<ExamAttachmentInfo> batchGetExamAttachmentList(ExamAttachmentEnum ae, List<Long> refIds) {
        if (ae == null || ae == ExamAttachmentEnum.NULL || refIds == null || refIds.size() <= 0) {
            return null;
        }
        return attachmentDao.batchGetExamAttachmentList(ae, refIds);
    }

    /**
     * 批量查询附件
     */
    public List<ExamAttachmentInfo> queryExamAttachmentList(List<ExamAttachmentQuery> query) {
        if (query == null || query.size() <= 0) {
            return null;
        }
        return attachmentDao.queryExamAttachmentList(query);
    }

    /**
     * 内存中查询附件数据
     */
    public List<ExamAttachmentInfo> findAll(List<ExamAttachmentInfo> attachments, ExamAttachmentEnum ae, Long refId) {
        if (attachments != null && attachments.size() > 0 && refId != null && refId > 0) {
            return attachments.stream().filter(x -> x.getRefTable().equals(ae.getRefTable())
                    && x.getRefType().equals(ae.getType())
                    && x.getRefId().equals(refId)).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 内存中分组
     */
    public Map<Integer, List<ExamAttachmentInfo>> getMap(List<ExamAttachmentEnum> aes, Long refId) {
        ExamAttachmentQuery query = new ExamAttachmentQuery();
        List<ExamAttachmentQuery> queries = query.getExamAttachmentQueries(aes, refId);
        if (queries != null && queries.size() > 0) {
            List<ExamAttachmentInfo> attachments = this.queryExamAttachmentList(queries);
            if (attachments != null && attachments.size() > 0) {
                return attachments.stream().collect(Collectors.groupingBy(ExamAttachmentInfo::getRefType));
            }
        }
        return null;
    }
}
