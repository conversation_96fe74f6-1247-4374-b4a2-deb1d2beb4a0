package com.fotile.questioncenter.app.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.questioncenter.app.pojo.dto.*;
import com.fotile.questioncenter.app.service.AppExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * COP训练台云管理API
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.questioncenter.app.controller
 * @date 2022/4/5 16:31
 */
@Slf4j
@RestController
@RequestMapping("/api/app/exam")
public class AppExamController extends BaseController {
    @Autowired
    private AppExamService examAppService;

    /**
     * 首页消息跑马灯
     */
    @RequestMapping(value = {"/messages","/api/open/messages"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<AppExamMessageOutDTO>> getAppExamMessageList(@Valid AppExamQueryMessageDTO query) {
        List<AppExamMessageOutDTO> results = null;
        try {
            results = examAppService.getAppExamMessageList(query);
            return success("获取考试推送消息成功！", results);
        } catch (BusinessException ex) {
            log.error("AppExamController.getMessages error. request->" + JSON.toJSONString(query), ex);
            return failure("获取考试推送消息失败：" + ex.getMessage());
        } catch (Exception ex) {
            log.error("AppExamController.getMessages error. request->" + JSON.toJSONString(query), ex);
            return failure("获取考试推送消息异常：" + ex.getMessage());
        }
    }

    /**
     * 首页数据统计显示
     */
    @RequestMapping(value = "/stats", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<AppExamStatsOutDTO> getStats(@Valid AppExamQueryStatsDTO query) {
        AppExamStatsOutDTO results = null;
        try {
            results = examAppService.getStats(query);
            return success("获取统计数据成功！", results);
        } catch (BusinessException ex) {
            log.error("AppExamController.getStats error. request->" + JSON.toJSONString(query), ex);
            return failure("获取统计数据失败：" + ex.getMessage());
        } catch (Exception ex) {
            log.error("AppExamController.getStats error. request->" + JSON.toJSONString(query), ex);
            return failure("获取统计数据异常：" + ex.getMessage());
        }
    }

    /**
     * 获取APP端指定考试的题库
     */
    @RequestMapping(value = "/questions", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<AppQuestionInfo>> getExamQuestionAnswers(@Valid AppExamQueryQuestionDTO query) {
        List<AppQuestionInfo> results = null;
        try {
            results = examAppService.getExamQuestionAnswers(query);
            return success("获取考试题库数据成功！", results);
        } catch (BusinessException ex) {
            log.error("AppExamController.getExamQuestionAnswers error. request->" + JSON.toJSONString(query), ex);
            return failure("获取考试题库数据失败：" + ex.getMessage());
        } catch (Exception ex) {
            log.error("AppExamController.getExamQuestionAnswers error. request->" + JSON.toJSONString(query), ex);
            return failure("获取考试题库数据异常：" + ex.getMessage());
        }
    }
}
