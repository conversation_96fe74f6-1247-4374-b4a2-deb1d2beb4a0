package com.fotile.questioncenter.app.service;

import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.questioncenter.app.dao.AppExamNormalDao;
import com.fotile.questioncenter.app.pojo.dto.*;
import com.fotile.questioncenter.client.OrgClient;
import com.fotile.questioncenter.client.SystemClient;
import com.fotile.questioncenter.client.pojo.AddWorkLogInDTO;
import com.fotile.questioncenter.client.pojo.Dic;
import com.fotile.questioncenter.client.pojo.FindSalesmanByIdOutDto;
import com.fotile.questioncenter.client.pojo.QueryDictInDto;
import com.fotile.questioncenter.exam.constant.CommonConstant;
import com.fotile.questioncenter.exam.constant.ExamAttachmentEnum;
import com.fotile.questioncenter.exam.constant.ExamTypeEnum;
import com.fotile.questioncenter.exam.constant.WorkLogTypeEnum;
import com.fotile.questioncenter.exam.pojo.dto.ExamAttachmentInfo;
import com.fotile.questioncenter.exam.pojo.dto.ExamAttachmentQuery;
import com.fotile.questioncenter.exam.pojo.entity.*;
import com.fotile.questioncenter.exam.service.ExamAttachmentService;
import com.fotile.questioncenter.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销中台-普通挑战考试API接口实现
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.questioncenter.app.service
 * @date 2022/4/5 17:07
 */
@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class AppExamNormalService {
    @Autowired
    private AppExamNormalDao appExamNormalDao;

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private SystemClient systemClient;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private AppExamService appExamService;

    @Autowired
    private ExamAttachmentService examAttachmentService;

    /* ****************************************普通考试开始***************************************** */

    /**
     * APP普通考试-分页列表
     */
    public List<AppExamNormalOutDTO> getNormalList(AppExamQueryListDTO query) {
        if (query == null) {
            throw new BusinessException("参数错误！");
        }

        //业务员信息
        Long salesmanId = CommonUtils.getSalesmanId(userAuthorConfig);
        if (salesmanId == null || salesmanId <= 0) {
            throw new BusinessException("业务员信息不存在！");
        }

        query.setSalesmanId(salesmanId);
        query.setSalesmanPostId(orgClient.getSalesmanStation(salesmanId).getData());

        //查询考试分类
        QueryDictInDto search = new QueryDictInDto();
        search.setActive(1);
        search.setNeedDeletedRows(0);
        search.setTypeCode(CommonConstant.DICT_EXAM_CATEGORY);

        List<Dic> categories = systemClient.searchDict(search).getData();
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }

        //获取考试分类编码，并按照sort降序排列
        List<String> categoryCodes = categories.stream()
                .sorted(Comparator.comparing(Dic::getSort).reversed())
                .map(Dic::getValueCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        query.setCategoryCodes(categoryCodes);

        //获取所有考试分类下的考试列表(仅上架状态的基础闯关)
        List<AppExamNormalInfo> allExams = appExamNormalDao.getExamNormalList(query);
        if (CollectionUtils.isEmpty(allExams)) {
            return null;
        }
        //填充字段
        allExams.forEach(x -> {
            if (x.getExamTotalQuestions() != null && x.getLatestExamPassedTotalQuestions() != null) {
                x.setIsExamUpdated(x.getExamTotalQuestions() > x.getLatestExamPassedTotalQuestions() ? 1 : 0);
            }
            Integer status = null;
            if (x.getExamRecordCount() == null || x.getExamRecordCount() <= 0) {
                status = 0;
            } else {
                if (x.getExamPassedCount() != null && x.getExamPassedCount() > 0) {
                    status = 1;
                } else {
                    status = 2;
                }
            }
            x.setExamStatus(status);
        });

        List<AppExamNormalOutDTO> results = new ArrayList<>();
        List<AppExamNormalInfo> list;
        AppExamNormalOutDTO exam;
        Dic dic;
        for (String categoryCode : categoryCodes) {
            dic = categories.stream().filter(x -> x.getValueCode().equalsIgnoreCase(categoryCode)).findFirst().orElse(null);
            list = allExams.stream().filter(x -> x.getCategory().equalsIgnoreCase(categoryCode)).collect(Collectors.toList());

            if (dic != null && CollectionUtils.isNotEmpty(list)) {
                exam = new AppExamNormalOutDTO();
                exam.setCategory(categoryCode);
                exam.setCategoryName(dic.getValueName());
                exam.setExamList(list);
                results.add(exam);
            }
        }
        return results;
    }

    /**
     * APP普通考试-考试详情(考前学习)
     */
    public AppExamNormalDetailOutDTO getNormalDetails(AppExamQueryDetailDTO query) {
        if (query == null || (query.getExamId() == null && StringUtils.isBlank(query.getExamCode()))) {
            throw new BusinessException("参数错误！");
        }

        CopExamEntity examEntity = appExamService.getCopExamEntity(query.getExamId(), query.getExamCode());
        if (examEntity == null) {
            throw new BusinessException("基础闯关信息不存在！");
        }
        if (examEntity.getType() == null || !examEntity.getType().equals(ExamTypeEnum.Normal.getType())) {
            throw new BusinessException("错误的考试类型！");
        }

        AppExamNormalDetailOutDTO results = new AppExamNormalDetailOutDTO();
        results.setExamId(examEntity.getId());
        results.setExamInfo(examEntity);

        //是否需要返回考试扩展信息(基础闯关,默认需要)
        if (query.getNeedExtendInfo() != null && query.getNeedExtendInfo() == 1) {
            CopExamNormalEntity copExamNormalEntity = appExamService.getCopExamNormalEntity(examEntity.getId());
            if (copExamNormalEntity != null) {
                results.setNormalInfo(copExamNormalEntity);

                //是否需要返回考试附件信息(默认需要)
                if (query.getNeedAttachment() != null && query.getNeedAttachment() == 1) {
                    ExamAttachmentQuery queryAttachment = new ExamAttachmentQuery();
                    List<ExamAttachmentEnum> aes = Collections.singletonList(ExamAttachmentEnum.NormalVideoCoverImg);

                    Long normalId = copExamNormalEntity.getId();
                    List<ExamAttachmentQuery> queries = queryAttachment.getExamAttachmentQueries(aes, normalId);
                    if (queries != null && queries.size() > 0) {
                        List<ExamAttachmentInfo> attachments = examAttachmentService.queryExamAttachmentList(queries);
                        if (attachments != null && attachments.size() > 0) {
                            results.setAttachments(examAttachmentService.findAll(attachments, ExamAttachmentEnum.NormalVideoCoverImg, normalId));
                        }
                    }
                }
            }
        }
        return results;
    }

    /**
     * APP普通考试-开始答题操作
     */
    public AppExamStartOutDTO<CopExamNormalRecordEntity> normalStart(AppExamStartDTO request) {
        if (request == null || request.getExamId() == null || request.getExamId() <= 0) {
            throw new BusinessException("参数错误！");
        }

        Long salesmanId = CommonUtils.getSalesmanId(userAuthorConfig);
        if (salesmanId == null || salesmanId <= 0) {
            throw new BusinessException("业务员参数错误！");
        }

        FindSalesmanByIdOutDto salesman = orgClient.findSalesmanById(salesmanId).getData();
        if (salesman == null) {
            throw new BusinessException("业务员信息不存在！");
        }

        CopExamEntity examEntity = appExamService.getCopExamEntity(request.getExamId(), null);
        if (examEntity == null) {
            throw new BusinessException("基础闯关信息不存在！");
        }
        if (examEntity.getType() == null || !examEntity.getType().equals(ExamTypeEnum.Normal.getType())) {
            throw new BusinessException("错误的考试类型！");
        }

        List<AppQuestionInfo> questions = appExamService.getExamQuestionList(new AppExamQueryQuestionDTO(
                examEntity.getId(), null).setRandomType(2).setExamType(examEntity.getType()));
        if (CollectionUtils.isEmpty(questions)) {
            throw new BusinessException("未查询到题库信息，请与管理联系！");
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        request.setOperatorId(user.get("userid"));
        request.setOperatorName(user.get("username"));

        //查看考试记录表中是否存在未提交的考试记录
        CopExamNormalRecordEntity recordEntity = appExamService.getCopExamNormalRecordEntity(
                null, examEntity.getId(), salesman.getId(), null, 0);
        if (recordEntity == null) {
            recordEntity = new CopExamNormalRecordEntity();
            recordEntity.setExamId(examEntity.getId());
            recordEntity.setExamCode(examEntity.getCode());
            recordEntity.setExamName(examEntity.getName());
            recordEntity.setExamType(examEntity.getType());
            recordEntity.setExamCategory(examEntity.getCategory());
            recordEntity.setExamPoint(BigDecimal.ZERO);
            recordEntity.setExamCorrectRate(BigDecimal.ZERO);
            recordEntity.setExamStartTime(new Date());
            recordEntity.setExamSubmitTime(null);
            recordEntity.setExamStatus(0);
            recordEntity.setExamPassed(0);
            recordEntity.setExamTotalPoint(BigDecimal.ZERO);
            recordEntity.setExamQuestionTotalNum(questions.size());
            recordEntity.setExamQuestionCorrectNum(0);
            recordEntity.setExamDifficultyStars(0);
            recordEntity.setSalesmanId(salesman.getId());
            recordEntity.setSalesmanName(salesman.getName());
            recordEntity.setSalesmanCode(salesman.getCode());
            recordEntity.setSalesmanPhone(salesman.getPhone());
            recordEntity.setSalesmanPostId(salesman.getStation());
            recordEntity.setSalesmanPostName(salesman.getStationName());
            recordEntity.setRegionId(salesman.getArea() != null ? Long.valueOf(salesman.getArea()) : null);
            recordEntity.setRegionName(salesman.getAreaName());
            recordEntity.setCompanyOrgId(salesman.getCompanyId());
            recordEntity.setCompanyCode(salesman.getCompanyCode());
            recordEntity.setCompanyName(salesman.getCompanyName());
            recordEntity.setStoreOrgId(salesman.getStoreId());
            recordEntity.setStoreCode(salesman.getStoreCode());
            recordEntity.setStoreName(salesman.getStoreName());

            //添加考试记录
            appExamNormalDao.normalStart(recordEntity, request.getOperatorId(), MybatisMateConfig.encrypt(request.getOperatorName()));
        }

        //返回结果
        AppExamStartOutDTO<CopExamNormalRecordEntity> results = new AppExamStartOutDTO<>();
        results.setExamId(request.getExamId());
        results.setRecordId(recordEntity.getId());
        results.setRecordInfo(recordEntity);

        if (request.getNeedQuestions() != null && request.getNeedQuestions() == 1) {
            results.setQuestions(questions);
        }

        return results;
    }

    /**
     * APP普通考试-提交答案操作
     */
    @Transactional
    public AppExamSubmitOutDTO<CopExamNormalRuleEntity> normalSubmit(AppExamSubmitDTO submit) {
        if (submit == null || submit.getExamId() == null || submit.getExamRecordId() == null) {
            throw new BusinessException("参数错误！");
        }

        Long salesmanId = CommonUtils.getSalesmanId(userAuthorConfig);
        if (salesmanId == null || salesmanId <= 0) {
            throw new BusinessException("业务员参数错误！");
        }

        CopExamEntity examEntity = appExamService.getCopExamEntity(submit.getExamId(), null);
        if (examEntity == null) {
            throw new BusinessException("基础闯关信息不存在！");
        }
        if (examEntity.getType() == null || !examEntity.getType().equals(ExamTypeEnum.Normal.getType())) {
            throw new BusinessException("错误的考试类型！");
        }
        CopExamNormalRuleEntity ruleEntity = appExamService.getCopExamNormalRuleEntity(examEntity.getId());
        if (ruleEntity == null) {
            throw new BusinessException("考试信息错误，请与管理联系！");
        }

        CopExamNormalRecordEntity recordEntity = appExamService.getCopExamNormalRecordEntity(
                submit.getExamRecordId(), null, null, null, 0);
        if (recordEntity == null) {
            throw new BusinessException("考试记录信息错误！");
        }
        if (recordEntity.getExamStatus() != null && recordEntity.getExamStatus() == 1) {
            throw new BusinessException("考试记录状态错误！");
        }
        if (!salesmanId.equals(recordEntity.getSalesmanId())) {
            throw new BusinessException("考试人身份信息错误，请与管理员联系！");
        }

        List<AppQuestionInfo> questions = appExamService.getExamQuestionList(new AppExamQueryQuestionDTO(
                examEntity.getId(), null).setRandomType(2).setExamType(examEntity.getType()));
        if (CollectionUtils.isEmpty(questions)) {
            throw new BusinessException("未查询到题库信息，请与管理联系！");
        }

        Map<String, String> user = CommonUtils.getAuthorUser();
        submit.setOperatorId(user.get("userid"));
        submit.setOperatorName(user.get("username"));
        submit.setExamType(examEntity.getType());

        //开始校验答案
        submit = appExamService.checkExamAnswers(submit, questions);

        if (submit.getExamCorrectRate().doubleValue() >= ruleEntity.getPassValues().doubleValue()) {
            submit.setExamPassed(1);
        } else {
            submit.setExamPassed(0);
        }

        Date time = new Date();
        recordEntity.setExamCorrectRate(submit.getExamCorrectRate());
        if (submit.getExamCorrectRate() != null) {
            String rateText = submit.getExamCorrectRate().toString();
            if (rateText.contains(".")) {
                String[] numArrays = rateText.split("\\.");
                if (numArrays.length == 2 && Integer.valueOf(numArrays[1]) == 0) {
                    submit.setExamCorrectRateText(numArrays[0]);
                } else {
                    submit.setExamCorrectRateText(rateText);
                }
            } else {
                submit.setExamCorrectRateText(rateText);
            }
        }

        recordEntity.setExamPassed(submit.getExamPassed());
        recordEntity.setExamSubmitTime(time);
        recordEntity.setExamStatus(1);
        recordEntity.setExamQuestionCorrectNum(submit.getExamQuestionCorrectNum());
        recordEntity.setModifiedBy(submit.getOperatorId());
        recordEntity.setModifiedDate(time);
        recordEntity.setExamPoint(submit.getExamPoint());
        recordEntity.setExamQuestionTotalNum(submit.getExamQuestionTotalNum());
        recordEntity.setExamTotalPoint(BigDecimal.ZERO);
        recordEntity.setExamPassValues(ruleEntity.getPassValues());

        CopExamGradeConfigEntity examGradeInfo = this.getExamGradeInfo(submit.getExamId(), recordEntity.getExamCorrectRate());
        if (examGradeInfo != null) {
            recordEntity.setExamGradeId(examGradeInfo.getId());
            recordEntity.setExamGradeName(examGradeInfo.getGradeName());
            recordEntity.setExamGradeCorrectRate(examGradeInfo.getCorrectRate());

            //考试等级
            submit.setExamGradeName(examGradeInfo.getGradeName());
            submit.setExamGradeDisplayText(examGradeInfo.getDisplayText());
            submit.setExamGradeDisplayImg(examGradeInfo.getDisplayImg());
        }

        //更新考试记录表
        appExamNormalDao.updateExamRecordInfo(recordEntity);

        //插入考试答案表
        appExamNormalDao.insertExamAnswers(submit);

        //返回结果
        AppExamSubmitOutDTO<CopExamNormalRuleEntity> results = new AppExamSubmitOutDTO<>();
        if (submit.getExamQuestionTotalNum() != null && submit.getExamQuestionCorrectNum() != null) {
            submit.setExamQuestionErrorNum(submit.getExamQuestionTotalNum() - submit.getExamQuestionCorrectNum());
        }
        results.setSubmitInfo(submit);
        results.setRuleInfo(ruleEntity);

        if (submit.getIsCloudMgrRequest()) {
            //收集工作日志 COP_EXAM issue:20834 工作日程-调整工作日程逻辑新增工作日志
            AddWorkLogInDTO workLogInfo = new AddWorkLogInDTO(
                    WorkLogTypeEnum.COP_EXAM.getLogType(), time, salesmanId);
            //考试记录ID
            workLogInfo.setTargetId(recordEntity.getId());
            //考试ID
            workLogInfo.setTargetCode(examEntity.getCode());
            //考试名称
            workLogInfo.setTargetName(examEntity.getName());
            //考试结果
            workLogInfo.setLogContent("正确率" + submit.getExamCorrectRate() + "%");

            appExamService.asyncInsertWorkLog(workLogInfo);
        }

        return results;
    }

    public List<AppExamNormalResult> getNormalExamResults(AppExamQueryDetailDTO query) {
        if (query == null) {
            throw new BusinessException("参数错误！");
        }
        if (query.getSalesmanId() == null || query.getSalesmanId() <= 0) {
            throw new BusinessException("业务员ID不能为空！");
        }

        return appExamNormalDao.getNormalExamResults(query);
    }


    public CopExamGradeConfigEntity getExamGradeInfo(Long examId, BigDecimal correctRate) {
        if (examId == null || examId <= 0 || correctRate == null) {
            return null;
        }
        return appExamNormalDao.getExamGradeInfo(examId, correctRate);
    }
    /* ****************************************普通考试结束***************************************** */
}
