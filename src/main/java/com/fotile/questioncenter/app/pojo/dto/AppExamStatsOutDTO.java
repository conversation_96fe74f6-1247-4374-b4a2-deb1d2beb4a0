package com.fotile.questioncenter.app.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务员COP首页数据返回实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.questioncenter.app.pojo.dto
 * @date 2022/4/6 11:45
 */
@Data
public class AppExamStatsOutDTO implements Serializable {
    /**
     * 训练成就(1~5)星级
     */
    private Integer stars = 0;

    /**
     * 基础闯关考试通过次数
     */
    private Integer normalExamPassedCount = 0;

    /**
     * 基础闯关考试总场次数(可用来判断是否有基础闯关入口,以产品设计为准)
     */
    private Integer normalExamTotalCount = 0;

    /**
     * 完成基础闯关总次数(考试记录)
     */
    private Integer normalExamCompletedCount = 0;

    /**
     * 顾客挑战考试通过次数
     */
    private Integer challengeExamPassedCount = 0;

    /**
     * 顾客挑战考试总场次数(可用来判断是否有顾客挑战入口,以产品设计为准)
     */
    private Integer challengeExamTotalCount = 0;

    /**
     * 完成顾客挑战总次数(考试记录)
     */
    private Integer challengeExamCompletedCount = 0;


    /**
     * 抽考考试ID(如果此值大于0,则显示抽考入口)
     */
    private Long randomExamId;

    /**
     * 抽考考试名称
     */
    private String randomExamName;

    /**
     * 抽考考试编码
     */
    private String randomExamCode;

    /**
     * 抽考考试开始时间
     */
    private Date randomStartDate;

    /**
     * 抽考考试结束时间
     */
    private Date randomEndDate;

    /**
     * 抽考限考次数
     */
    private Integer randomExamLimitTimes;

    /**
     * 抽考已考试次数
     */
    private Integer randomExamCount = 0;
}
