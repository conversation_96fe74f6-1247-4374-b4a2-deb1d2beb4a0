package com.fotile.questioncenter.client.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询系统字典入参实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.systemcenter.dictionary.pojo.dto
 * @date 2022/4/11 0:17
 */
@Data
public class QueryDictInDto implements Serializable {
    /**
     * 字典Id
     */
    private Long id;

    /**
     * 字典Id集合字符(多个使用半角逗号分隔)
     */
    private String ids;

    /**
     * 是否需要已删除的数据(默认不需要)
     */
    private Integer needDeletedRows = 0;

    /**
     * 字典valueCode
     */
    private String valueCode;

    /**
     * 字典valueCode集合字符(多个使用半角逗号分隔)
     */
    private String valueCodes;

    /**
     * 字典typeCode
     */
    private String typeCode;

    /**
     * 字典typeCode集合字符(多个使用半角逗号分隔)
     */
    private String typeCodes;

    /**
     * 是否生效(0:失效,1:生效,其他值:全部,默认:1-生效)
     */
    private Integer active = 1;
}
