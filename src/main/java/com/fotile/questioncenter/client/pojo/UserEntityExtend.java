package com.fotile.questioncenter.client.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserEntityExtend implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * keycloak用户ID
     */
    @NotNull(message = "账号id不能为空!")
    private String userEntityId;

    /**
     * 账号
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 名称
     */
    private String firstName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 关联业务员
     */
    private Long salesmanId;

    /**
     * 关联业务员名称
     */
    @FieldEncrypt
    private String salesmanName;

    /**
     * 关联业务员编码
     */
    private String salesmanCode;

    /**
     * 关联业务员手机号
     */
    @FieldEncrypt
    private String salesmanPhone;

    /**
     * 最后一次登录时间
     */
    private String lastLoginDate;

    /**
     * 头像url
     */
    private String headPortrait;

    /**
     * 手机唯一标识
     */
    private String registrationId;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * qq
     */
    private String customerQQ;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册ip
     */
    private String registerIp;

    /**
     * 设备类型
     */
    private String mobileSystemType;

    /**
     * 是否启用
     */
    private String stage;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 常用工具，多个值以逗号分隔
     */
    private String commonTools;

    /**
     * 首页页面布局
     */
    private String pageLayout;

    /**
     * 域id
     */
    private String realmId;
}
