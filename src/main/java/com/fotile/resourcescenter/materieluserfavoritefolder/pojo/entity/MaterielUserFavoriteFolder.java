package com.fotile.resourcescenter.materieluserfavoritefolder.pojo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * materiel_user_favorite_folder
 * <AUTHOR>
@Data
public class MaterielUserFavoriteFolder implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 收藏夹名称
     */
    private String folderName;

    /**
     * 收藏夹类型 1默认文件夹 2自建文件夹
     */
    private String folderType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后更新时间
     */
    private Date mtime;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 删除标识1删除,2不删除
     */
    private String isDel;

    /**
     * 创建者名称
     */
    @FieldEncrypt
    private String reserveItem1;

    /**
     * 更新者名称
     */
    @FieldEncrypt
    private String reserveItem2;

    /**
     * 预留字段3
     */
    private String reserveItem3;

    /**
     * 预留字段4
     */
    private String reserveItem4;

    /**
     * 预留字段5
     */
    private String reserveItem5;

    /**
     * 预留字段6
     */
    private String reserveItem6;

    /**
     * 预留字段7
     */
    private String reserveItem7;

    /**
     * 预留字段8
     */
    private String reserveItem8;

    /**
     * 预留字段9
     */
    private String reserveItem9;

    /**
     * 预留字段10
     */
    private String reserveItem10;

    private static final long serialVersionUID = 1L;
}