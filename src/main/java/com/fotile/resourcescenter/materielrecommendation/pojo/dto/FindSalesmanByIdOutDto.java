package com.fotile.resourcescenter.materielrecommendation.pojo.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("业务员")
public class FindSalesmanByIdOutDto implements Serializable{
    
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("业务员id")
    private Long id;
	
    @ApiModelProperty("业务员名称")
    private String name;
    
    @ApiModelProperty("业务员编码")
    private String code;
    
    @ApiModelProperty("业务员手机号")
    private String phone;
    
    @ApiModelProperty(value="所属公司",example="所属公司id")
	private Long companyId;

    @ApiModelProperty(value="所属公司名称",example="所属公司名称")
    private String companyName;
    
	@ApiModelProperty(value="所属门店",example="所属门店id")
	private Long storeId;
	@ApiModelProperty(value="所属门店名称",example="所属门店名称")
    private String storeName;

    @ApiModelProperty(value="所属部门",example="所属部门id")
    private Long departmentId;
    @ApiModelProperty(value="所属部门名称",example="所属部门名称")
    private String departmentName;
    @ApiModelProperty(value="所属部门",example="视觉系统_所属部门id")
    private Long visualDepartmentId;
    @ApiModelProperty(value="视觉系统_所属部门名称",example="视觉系统_所属部门名称")
    private String visualDepartmentName;
}
