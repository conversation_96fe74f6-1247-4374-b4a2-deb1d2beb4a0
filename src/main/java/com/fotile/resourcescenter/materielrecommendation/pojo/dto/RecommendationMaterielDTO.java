package com.fotile.resourcescenter.materielrecommendation.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2020/9/16 11:45
 */
@Data
public class RecommendationMaterielDTO {

    /**
    * 页码
    */
    @NotNull(message = "页码不能为空")
    private Integer pageIndex;
    /**
    * 条数
    */
    @NotNull(message = "条数")
    private Integer pageSize;
    /**
    * 分类ID
    */
    private String categoryId;
    /**
     * 关键字
     */
    private String keywords;
    /**
     * 有效类型 1、不限 2、一年有效 3、永久有效
     */
    private String validType;


}
