package com.fotile.resourcescenter.test.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.resourcescenter.test.pojo.entity.TDepartment;
import com.fotile.resourcescenter.test.pojo.entity.TDepartmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TDepartmentDao extends BaseMapper<TDepartment> {
//    long countByExample(TDepartmentExample example);
//
//    int deleteByExample(TDepartmentExample example);
//
//    int deleteByPrimaryKey(Long id);
//
//    int insert(TDepartment record);
//
//    int insertSelective(TDepartment record);
//
//    List<TDepartment> selectByExample(TDepartmentExample example);
//
//    TDepartment selectByPrimaryKey(Long id);
//
//    int updateByExampleSelective(@Param("record") TDepartment record, @Param("example") TDepartmentExample example);
//
//    int updateByExample(@Param("record") TDepartment record, @Param("example") TDepartmentExample example);
//
//    int updateByPrimaryKeySelective(TDepartment record);
//
//    int updateByPrimaryKey(TDepartment record);
}