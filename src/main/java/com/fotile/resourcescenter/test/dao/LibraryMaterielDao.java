package com.fotile.resourcescenter.test.dao;

import com.fotile.resourcescenter.test.pojo.entity.*;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LibraryMaterielDao {
    long countByExample(LibraryMaterielExample example);

    int deleteByExample(LibraryMaterielExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(LibraryMaterielWithBLOBs record);

    int insertSelective(LibraryMaterielWithBLOBs record);

    List<LibraryMaterielWithBLOBs> selectByExampleWithBLOBs(LibraryMaterielExample example);

    List<LibraryMateriel> selectByExample(LibraryMaterielExample example);

    LibraryMaterielWithBLOBs selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LibraryMaterielWithBLOBs record, @Param("example") LibraryMaterielExample example);

    int updateByExampleWithBLOBs(@Param("record") LibraryMaterielWithBLOBs record, @Param("example") LibraryMaterielExample example);

    int updateByExample(@Param("record") LibraryMateriel record, @Param("example") LibraryMaterielExample example);

    int updateByPrimaryKeySelective(LibraryMaterielWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(LibraryMaterielWithBLOBs record);

    int updateByPrimaryKey(LibraryMateriel record);

    List<MaterielVO> selectMaterielList();

    List<MaterielListVO> selectMaterielGroupList();

    List<LibraryMaterielListItem> selectMaterielGroupItem(String materielListId);
}