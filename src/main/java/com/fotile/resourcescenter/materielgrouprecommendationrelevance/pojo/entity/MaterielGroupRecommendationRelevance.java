package com.fotile.resourcescenter.materielgrouprecommendationrelevance.pojo.entity;

import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * materiel_group_recommendation_relevance
 * <AUTHOR>
public class MaterielGroupRecommendationRelevance implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 素材组id
     */
    private String groupId;

    /**
     * 素材id
     */
    private String materielId;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后更新时间
     */
    private Date mtime;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 删除标识1删除,2不删除
     */
    private String isDel;

    /**
     * 创建者名称
     */
    @FieldEncrypt
    private String reserveItem1;

    /**
     * 更新者名称
     */
    @FieldEncrypt
    private String reserveItem2;

    /**
     * 预留字段3
     */
    private String reserveItem3;

    /**
     * 预留字段4
     */
    private String reserveItem4;

    /**
     * 预留字段5
     */
    private String reserveItem5;

    /**
     * 预留字段6
     */
    private String reserveItem6;

    /**
     * 预留字段7
     */
    private String reserveItem7;

    /**
     * 预留字段8
     */
    private String reserveItem8;

    /**
     * 预留字段9
     */
    private String reserveItem9;

    /**
     * 预留字段10
     */
    private String reserveItem10;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getMaterielId() {
        return materielId;
    }

    public void setMaterielId(String materielId) {
        this.materielId = materielId;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    public String getReserveItem1() {
        return reserveItem1;
    }

    public void setReserveItem1(String reserveItem1) {
        this.reserveItem1 = reserveItem1;
    }

    public String getReserveItem2() {
        return reserveItem2;
    }

    public void setReserveItem2(String reserveItem2) {
        this.reserveItem2 = reserveItem2;
    }

    public String getReserveItem3() {
        return reserveItem3;
    }

    public void setReserveItem3(String reserveItem3) {
        this.reserveItem3 = reserveItem3;
    }

    public String getReserveItem4() {
        return reserveItem4;
    }

    public void setReserveItem4(String reserveItem4) {
        this.reserveItem4 = reserveItem4;
    }

    public String getReserveItem5() {
        return reserveItem5;
    }

    public void setReserveItem5(String reserveItem5) {
        this.reserveItem5 = reserveItem5;
    }

    public String getReserveItem6() {
        return reserveItem6;
    }

    public void setReserveItem6(String reserveItem6) {
        this.reserveItem6 = reserveItem6;
    }

    public String getReserveItem7() {
        return reserveItem7;
    }

    public void setReserveItem7(String reserveItem7) {
        this.reserveItem7 = reserveItem7;
    }

    public String getReserveItem8() {
        return reserveItem8;
    }

    public void setReserveItem8(String reserveItem8) {
        this.reserveItem8 = reserveItem8;
    }

    public String getReserveItem9() {
        return reserveItem9;
    }

    public void setReserveItem9(String reserveItem9) {
        this.reserveItem9 = reserveItem9;
    }

    public String getReserveItem10() {
        return reserveItem10;
    }

    public void setReserveItem10(String reserveItem10) {
        this.reserveItem10 = reserveItem10;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MaterielGroupRecommendationRelevance other = (MaterielGroupRecommendationRelevance) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGroupId() == null ? other.getGroupId() == null : this.getGroupId().equals(other.getGroupId()))
            && (this.getMaterielId() == null ? other.getMaterielId() == null : this.getMaterielId().equals(other.getMaterielId()))
            && (this.getCtime() == null ? other.getCtime() == null : this.getCtime().equals(other.getCtime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getMtime() == null ? other.getMtime() == null : this.getMtime().equals(other.getMtime()))
            && (this.getModifier() == null ? other.getModifier() == null : this.getModifier().equals(other.getModifier()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getReserveItem1() == null ? other.getReserveItem1() == null : this.getReserveItem1().equals(other.getReserveItem1()))
            && (this.getReserveItem2() == null ? other.getReserveItem2() == null : this.getReserveItem2().equals(other.getReserveItem2()))
            && (this.getReserveItem3() == null ? other.getReserveItem3() == null : this.getReserveItem3().equals(other.getReserveItem3()))
            && (this.getReserveItem4() == null ? other.getReserveItem4() == null : this.getReserveItem4().equals(other.getReserveItem4()))
            && (this.getReserveItem5() == null ? other.getReserveItem5() == null : this.getReserveItem5().equals(other.getReserveItem5()))
            && (this.getReserveItem6() == null ? other.getReserveItem6() == null : this.getReserveItem6().equals(other.getReserveItem6()))
            && (this.getReserveItem7() == null ? other.getReserveItem7() == null : this.getReserveItem7().equals(other.getReserveItem7()))
            && (this.getReserveItem8() == null ? other.getReserveItem8() == null : this.getReserveItem8().equals(other.getReserveItem8()))
            && (this.getReserveItem9() == null ? other.getReserveItem9() == null : this.getReserveItem9().equals(other.getReserveItem9()))
            && (this.getReserveItem10() == null ? other.getReserveItem10() == null : this.getReserveItem10().equals(other.getReserveItem10()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGroupId() == null) ? 0 : getGroupId().hashCode());
        result = prime * result + ((getMaterielId() == null) ? 0 : getMaterielId().hashCode());
        result = prime * result + ((getCtime() == null) ? 0 : getCtime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getMtime() == null) ? 0 : getMtime().hashCode());
        result = prime * result + ((getModifier() == null) ? 0 : getModifier().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getReserveItem1() == null) ? 0 : getReserveItem1().hashCode());
        result = prime * result + ((getReserveItem2() == null) ? 0 : getReserveItem2().hashCode());
        result = prime * result + ((getReserveItem3() == null) ? 0 : getReserveItem3().hashCode());
        result = prime * result + ((getReserveItem4() == null) ? 0 : getReserveItem4().hashCode());
        result = prime * result + ((getReserveItem5() == null) ? 0 : getReserveItem5().hashCode());
        result = prime * result + ((getReserveItem6() == null) ? 0 : getReserveItem6().hashCode());
        result = prime * result + ((getReserveItem7() == null) ? 0 : getReserveItem7().hashCode());
        result = prime * result + ((getReserveItem8() == null) ? 0 : getReserveItem8().hashCode());
        result = prime * result + ((getReserveItem9() == null) ? 0 : getReserveItem9().hashCode());
        result = prime * result + ((getReserveItem10() == null) ? 0 : getReserveItem10().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupId=").append(groupId);
        sb.append(", materielId=").append(materielId);
        sb.append(", ctime=").append(ctime);
        sb.append(", creator=").append(creator);
        sb.append(", mtime=").append(mtime);
        sb.append(", modifier=").append(modifier);
        sb.append(", isDel=").append(isDel);
        sb.append(", reserveItem1=").append(reserveItem1);
        sb.append(", reserveItem2=").append(reserveItem2);
        sb.append(", reserveItem3=").append(reserveItem3);
        sb.append(", reserveItem4=").append(reserveItem4);
        sb.append(", reserveItem5=").append(reserveItem5);
        sb.append(", reserveItem6=").append(reserveItem6);
        sb.append(", reserveItem7=").append(reserveItem7);
        sb.append(", reserveItem8=").append(reserveItem8);
        sb.append(", reserveItem9=").append(reserveItem9);
        sb.append(", reserveItem10=").append(reserveItem10);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}