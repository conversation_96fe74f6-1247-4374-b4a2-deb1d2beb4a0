package com.fotile.resourcescenter.materielgrouplist.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.resourcescenter.materielgrouplist.dao.MaterielGroupListDao;
import com.fotile.resourcescenter.materielgrouplist.pojo.dto.*;
import com.fotile.resourcescenter.materielgrouplist.pojo.entity.MaterielGroupList;
import com.fotile.resourcescenter.materielgrouplistrelevance.dao.MaterielGroupListRelevanceDao;
import com.fotile.resourcescenter.materielgrouplistrelevance.pojo.dto.InsertBatchMaterielGroupListRelevance;
import com.fotile.resourcescenter.materielgrouplistrelevance.pojo.entity.MaterielGroupListRelevance;
import com.fotile.resourcescenter.materielgrouprecommendation.dao.MaterielGroupRecommendationDao;
import com.fotile.resourcescenter.materielgrouprecommendation.pojo.entity.MaterielGroupRecommendation;
import com.fotile.resourcescenter.materielgrouprecommendationrelevance.pojo.entity.MaterielGroupRecommendationRelevance;
import com.fotile.resourcescenter.materielrecommendation.pojo.dto.FindSalesmanByIdOutDto;
import com.fotile.resourcescenter.materielrecommendation.service.OrgService;
import com.fotile.resourcescenter.materielrecommendation.service.UserDeptService;
import com.fotile.resourcescenter.materieluserfavorite.dao.MaterielUserFavoriteDao;
import com.fotile.resourcescenter.materieluserfavorite.pojo.entity.MaterielUserFavorite;
import com.fotile.resourcescenter.util.UUIDUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2020/9/8 14:42
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "fifth")
public class MaterielGroupListService {

    @Autowired
    private MaterielGroupListDao materielGroupListDao;
    @Autowired
    private MaterielGroupListRelevanceDao materielGroupListRelevanceDao;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private MaterielGroupRecommendationDao materielGroupRecommendationDao;
    @Autowired
    private MaterielUserFavoriteDao materielUserFavoriteDao;
    @Autowired
    private OrgService orgService;
    @Autowired
    private UserDeptService userDeptService;

    /**
     * 部门素材组列表
     *
     * @param inDto
     * @return
     */
    public IPage<List<GetMaterielGroupDto>> getMaterielGroupList(MaterielGroupListRequestDto inDto) {
        Page<List<GetMaterielGroupDto>> page = new Page(inDto.getPageIndex(), inDto.getPageSize());

        Map<String, Object> params = new HashMap<>();

        // 素材组ID
        if (!StringUtils.isEmpty(inDto.getId())) {
            params.put("id", inDto.getId());
        }

        // 素材标题
        if (!StringUtils.isEmpty(inDto.getTitle())) {
            params.put("title", inDto.getTitle());
        }

        // 创建人
        if (!StringUtils.isEmpty(inDto.getCreatorName())) {
            params.put("creatorName", inDto.getCreatorName());
        }

        // 状态
        if (!StringUtils.isEmpty(inDto.getStatus())) {
            params.put("status", inDto.getStatus());
        }

        // 素材分类
        if(!StringUtils.isEmpty(inDto.getCategoryId())){
            params.put("categoryId", inDto.getCategoryId());
        }
        // 素材用途
        if(!StringUtils.isEmpty(inDto.getUsesId())){
            params.put("usesId", inDto.getUsesId());
        }
        // 素材产品类别ID
        if(!StringUtils.isEmpty(inDto.getProductCategoryId())){
            params.put("productCategoryId", inDto.getProductCategoryId());
        }

        // 创建时间开始
        if (!StringUtils.isEmpty(inDto.getStartTime())) {
            params.put("startTime", inDto.getStartTime());
        }

        // 创建时间结束
        if (!StringUtils.isEmpty(inDto.getEndTime())) {
            params.put("endTime", inDto.getEndTime());
        }

        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        // 取得部门ID
        Result<Map<String, Object>> subUserDeptEntity = userDeptService.findSubUserEntityById(userAuthor.getUserId());
//        Result<FindSalesmanByIdOutDto> salesmanDetail = orgService.findSalesmanById(userAuthor.getSalesmanId());
        if (null == subUserDeptEntity || null == subUserDeptEntity.getData() || null == subUserDeptEntity.getData().get("materialDep")) {
        } else {
            params.put("departmentId", subUserDeptEntity.getData().get("materialDep").toString());
        }

        return materielGroupListDao.getMaterielGroupList(page, params);

    }


    /**
     * 部门素材组置顶/取消
     *
     * @param id
     * @param isTopping
     * @return
     */
    public int SetTopMaterielGroup(String id, String isTopping) {
        // 取得用户ID
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();

        MaterielGroupList materielGroupList = new MaterielGroupList();
        materielGroupList.setId(id);
        materielGroupList.setIsTopping(isTopping);
        materielGroupList.setMtime(new Date());
        materielGroupList.setModifier(userAuthor.getUserId());
        materielGroupList.setReserveItem2(userAuthor.getFirstName());
        return materielGroupListDao.updateByPrimaryKeySelective(materielGroupList);
    }

    /**
     * 部门素材组添加
     *
     * @param inDto
     * @return
     * @Param userAuthor
     */
    public int addMaterielGroup(AddOrEditMaterielGroupRequestDto inDto, UserAuthor userAuthor, Map<String, Object> subUserDeptEntity) {
        //添加蔬菜组推荐
        MaterielGroupList materielGroupList = new MaterielGroupList();
        String uuid = UUIDUtil.getUUID();
        materielGroupList.setId(uuid);
        materielGroupList.setTitle(inDto.getTitle());
        materielGroupList.setDepartmentId(subUserDeptEntity.get("materialDep").toString());
        materielGroupList.setDepartmentName(subUserDeptEntity.get("materialDepName").toString());
        materielGroupList.setRemok(inDto.getRemok());
        materielGroupList.setMaterielNum(inDto.getMaterielIds().split(",").length + "");
        materielGroupList.setDownloadNum("0");
        materielGroupList.setStatus(inDto.getStatus());
        materielGroupList.setIsDel("2");
        materielGroupList.setCtime(new Date());
        materielGroupList.setCreator(userAuthor.getUserId());
        materielGroupList.setMtime(new Date());
        materielGroupList.setModifier(userAuthor.getUserId());
        materielGroupList.setReserveItem1(userAuthor.getFirstName());
        materielGroupList.setReserveItem2(userAuthor.getFirstName());
        int flag = materielGroupListDao.insert(materielGroupList);

        if (flag != 0) {
            //素材组推荐关联表追加
            List<MaterielGroupListRelevance> list = new ArrayList<MaterielGroupListRelevance>();
            String[] materielIds = inDto.getMaterielIds().split(",");
            for (int i = 0; i < materielIds.length; i++) {
                MaterielGroupListRelevance materielGroupListRelevance = new MaterielGroupListRelevance();
                materielGroupListRelevance.setId(UUIDUtil.getUUID());
                materielGroupListRelevance.setGroupId(uuid);
                materielGroupListRelevance.setMaterielId(materielIds[i]);
                materielGroupListRelevance.setCtime(new Date());
                materielGroupListRelevance.setCreator(userAuthor.getUserId());
                materielGroupListRelevance.setMtime(new Date());
                materielGroupListRelevance.setIsDel("2");
                materielGroupListRelevance.setModifier(userAuthor.getUserId());
                materielGroupListRelevance.setReserveItem1(userAuthor.getFirstName());
                materielGroupListRelevance.setReserveItem2(userAuthor.getFirstName());
                list.add(materielGroupListRelevance);
            }
            List<InsertBatchMaterielGroupListRelevance> insertBatchList = JSON.parseArray(JSON.toJSONString(list), InsertBatchMaterielGroupListRelevance.class);
            flag = materielGroupListRelevanceDao.insertBatch(insertBatchList);
        }

        return flag;
    }

    /**
     * 部门素材组编辑
     *
     * @param inDto
     * @param userAuthor
     * @return
     */
    public int editMaterielGroup(AddOrEditMaterielGroupRequestDto inDto, UserAuthor userAuthor, Map<String, Object> subUserDeptEntity) {
        //添加蔬菜组推荐
        MaterielGroupList materielGroupList = new MaterielGroupList();
        materielGroupList.setId(inDto.getId());
        materielGroupList.setTitle(inDto.getTitle());
        materielGroupList.setStatus(inDto.getStatus());
        materielGroupList.setDepartmentId(subUserDeptEntity.get("materialDep").toString());
        materielGroupList.setDepartmentName(subUserDeptEntity.get("materialDepName").toString());
        materielGroupList.setRemok(inDto.getRemok());
        materielGroupList.setMaterielNum(inDto.getMaterielIds().split(",").length + "");
        materielGroupList.setMtime(new Date());
        materielGroupList.setModifier(userAuthor.getUserId());
        materielGroupList.setReserveItem2(userAuthor.getFirstName());
        int flag = materielGroupListDao.updateByPrimaryKeySelective(materielGroupList);

        if (flag != 0) {
            //删除首页推荐关联
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("group_id", inDto.getId());
            flag = materielGroupListRelevanceDao.deleteByMap(param);
            if (flag != 0) {
                //素材组推荐关联表追加
                List<MaterielGroupListRelevance> list = new ArrayList<MaterielGroupListRelevance>();
                String[] materielIds = inDto.getMaterielIds().split(",");
                for (int i = 0; i < materielIds.length; i++) {
                    MaterielGroupListRelevance materielGroupListRelevance = new MaterielGroupListRelevance();
                    materielGroupListRelevance.setId(UUIDUtil.getUUID());
                    materielGroupListRelevance.setGroupId(inDto.getId());
                    materielGroupListRelevance.setMaterielId(materielIds[i]);
                    materielGroupListRelevance.setCtime(new Date());
                    materielGroupListRelevance.setCreator(userAuthor.getUserId());
                    materielGroupListRelevance.setMtime(new Date());
                    materielGroupListRelevance.setIsDel("2");
                    materielGroupListRelevance.setModifier(userAuthor.getUserId());
                    materielGroupListRelevance.setReserveItem1(userAuthor.getFirstName());
                    materielGroupListRelevance.setReserveItem2(userAuthor.getFirstName());
                    list.add(materielGroupListRelevance);
                }
                List<InsertBatchMaterielGroupListRelevance> insertBatchList = JSON.parseArray(JSON.toJSONString(list), InsertBatchMaterielGroupListRelevance.class);
                flag = materielGroupListRelevanceDao.insertBatch(insertBatchList);
            }
        }

        return flag;
    }


    /**
     * 批量删除素材组
     *
     * @param requestTO
     * @return
     */
    public int delMaterielGroup(DelMaterielGroupRequestTO requestTO) {
        List<String> ids = Arrays.asList(requestTO.getIds().split(","));
        return materielGroupListDao.deleteBatchIds(ids);
    }


    /**
     * 获取素材组详情
     *
     * @param id
     * @return
     */
    public Map<String, Object> getMaterielGroupDetail(String id) {
        // 取得用户ID
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();

        // 先查询素材组
        MaterielGroupList groupList = materielGroupRecommendationDao.selectMaterielBroupById(id);
        if (null == groupList) {
            throw new BusinessException("素材组不存在！");
        }

        // 查询是否收藏
        QueryWrapper favoriteQw = new QueryWrapper();
        favoriteQw.eq("user_id", userAuthor.getUserId());
        favoriteQw.eq("materiel_id", id);
        List<MaterielUserFavorite> favoriteList = materielUserFavoriteDao.selectList(favoriteQw);

        // 查询素材文件
        Map<String, Object> param = new HashMap<>();
        param.put("groupId", id);
        param.put("userId", userAuthor.getUserId());
        List<Map<String, Object>> materielList = materielGroupListRelevanceDao.selectMaterielGroupRelevanceList(param);


        // 设定返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("id", groupList.getId());
        data.put("title", groupList.getTitle());
        data.put("remok", groupList.getRemok());
        data.put("materielNum", groupList.getMaterielNum());
        data.put("createName", groupList.getReserveItem1());
        data.put("ctime", groupList.getCtime());
        data.put("mtime", groupList.getMtime());
        data.put("departmentName", groupList.getDepartmentName());
        data.put("materielList", materielList);

        // 没有记录，未收藏
        if (favoriteList.isEmpty()) {
            data.put("isFavorite", "2");
        } else {
            data.put("isFavorite", "1");
        }

        return data;
    }

    /**
     * 素材组编辑详情取得
     *
     * @param requestTO
     * @return
     */
    public IPage<Map<String, Object>> getMaterielGroupEditDetail(GetMaterielGroupEditDetailRequestTO requestTO) {

        Page<Map<String, Object>> page = new Page<Map<String, Object>>(requestTO.getPageIndex(), requestTO.getPageSize());
        // 查询参数设定
        Map<String, Object> param = new HashMap<>();
        // 素材组id
        param.put("id", requestTO.getId());

        IPage<Map<String, Object>> supertubeMaterielList = materielGroupListRelevanceDao.getMaterielGroupEditDetail(page, param);
        return supertubeMaterielList;
    }


    /**
     * 部门素材组添加
     *
     * @param
     * @return
     * @Param userAuthor
     */
    public int addMaterielGroupData(AddOrEditMaterielGroupDataRequestDto requestTO) {
        //添加蔬菜组推荐
        MaterielGroupList materielGroupList = new MaterielGroupList();
//        String uuid = UUIDUtil.getUUID();
        materielGroupList.setId(requestTO.getId()); // ID由用户提交
        materielGroupList.setTitle(requestTO.getTitle());
        materielGroupList.setDepartmentId(requestTO.getDepartmentId());
        materielGroupList.setDepartmentName(requestTO.getDepartmentName());
        materielGroupList.setRemok(requestTO.getRemok());
        materielGroupList.setMaterielNum(requestTO.getMaterielNum());
        materielGroupList.setDownloadNum("0");
        materielGroupList.setStatus(requestTO.getStatus());
        materielGroupList.setIsDel("2");
        materielGroupList.setCtime(requestTO.getCtime());
        materielGroupList.setCreator(requestTO.getUserId());
        materielGroupList.setMtime(requestTO.getMtime());
        materielGroupList.setModifier(requestTO.getUserId());
        materielGroupList.setReserveItem1(requestTO.getUserName());
        materielGroupList.setReserveItem2(requestTO.getUserName());
        int flag = materielGroupListDao.insert(materielGroupList);

        if (flag != 0 && !requestTO.getRelevances().isEmpty()) {
            List<InsertBatchMaterielGroupListRelevance> insertBatchList = JSON.parseArray(JSON.toJSONString(requestTO.getRelevances()), InsertBatchMaterielGroupListRelevance.class);
            flag = materielGroupListRelevanceDao.insertBatch(insertBatchList);
        }

        return flag;
    }

    /**
     * 取部门推荐素材组列表
     *
     * @param requestTO
     * @return
     */
    public List<Map<String, Object>> getDepartmentMaterielGroup(DepartmentMaterielGroupRequestDto requestTO) {
        //取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userAuthor.getUserId());
        // 取得部门ID
        Result<Map<String, Object>> subUserDeptEntity = userDeptService.findSubUserEntityById(userAuthor.getUserId());
//        Result<FindSalesmanByIdOutDto> salesmanDetail = orgService.findSalesmanById(userAuthor.getSalesmanId());
        if (null == subUserDeptEntity || null == subUserDeptEntity.getData() || null == subUserDeptEntity.getData().get("materialDep")) {
            throw new BusinessException("账号部门id不存在，请设定账号部门！");
        } else {
            param.put("departmentId", subUserDeptEntity.getData().get("materialDep").toString());
        }
        param.put("offset", (requestTO.getPageIndex() - 1) * requestTO.getPageSize());
        param.put("limit", requestTO.getPageSize());
        return materielGroupRecommendationDao.selectDepartmentMaterielGroupList(param);
    }
}
