package com.fotile.resourcescenter.materieluserfavorite.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 获取用户收藏列表
 * <AUTHOR>
 * @create 2020/9/8 15:41
 */
@Data
public class GetUserFavoriteListRequestTO {
    /**
    * 标题
    */
    private String title;
    /**
    * 分类ID
    */
    private String categoryId;
    /**
    * 途径ID
    */
    private String usesId;
    /**
    * 产品类别ID
    */
    private String productCategoryId;

    /**
    * 用户ID
    */
    private String userId;
    /**
    * 收藏夹ID
    */
    private String reserveItem4;
    /**
     * 素材分类id
     */
    private Long materielCategoryId;
    /**
     * 素材分类明细id
     */
    private List<Long> materielCategoryPropertyDicIds;
    /**
     * 产品型号
     */
    private String goodsName;
    /**
    * 页码
    */
    @NotNull(message = "页码不能为空")
    private Integer pageIndex;
    /**
    * 条数
    */
    @NotNull(message = "条数不能为空")
    private Integer pageSize;

}
