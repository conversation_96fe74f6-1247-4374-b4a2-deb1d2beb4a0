package com.fotile.resourcescenter.materieluserfavorite.pojo.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddMaterielUserFavoriteRequestDto implements Serializable {
    /**
     * 素材id
     */
    @NotNull(message = "素材id不能为空")
    private String materielId; //素材id
    /**
     * 收藏类型（1：收藏，2：取消）
     */
    @NotNull(message = "收藏类型不能为空")
    private String type;//收藏类型（1：收藏，2：取消）
    /**
    * 素材类型 1素材，2素材组
    */
    private String materielType = "1";
    /**
    * 收藏夹ID
    */
    private String reserveItem4;
}
