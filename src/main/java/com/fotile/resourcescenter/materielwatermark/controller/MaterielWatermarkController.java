package com.fotile.resourcescenter.materielwatermark.controller;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.resourcescenter.materielwatermark.pojo.dto.GetMaterielWatermarkPageListRequestTO;
import com.fotile.resourcescenter.materielwatermark.service.MaterielWatermarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 素材水印文件
 * <AUTHOR>
 * @version V1.0
 * @date 2022/11/27 22:38 
 * @Description 
 */
@Slf4j
@RestController
//@CrossOrigin
@RequestMapping("/api/materielWatermark")
public class MaterielWatermarkController extends BaseController {

    @Autowired
    private MaterielWatermarkService materielWatermarkService;

    /**
     * 素材水印文件列表接口
     */
    @RequestMapping(value = "/getMaterielWatermarkPageList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getUserFavoriteList(@Valid GetMaterielWatermarkPageListRequestTO requestTO) {
        try {
            return success("查询成功", materielWatermarkService.getMaterielWatermarkPageList(requestTO));
        } catch (Exception e) {
            return failure("失败");
        }
    }

}
