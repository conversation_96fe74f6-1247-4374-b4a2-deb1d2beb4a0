package com.fotile.resourcescenter.materiel.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.resourcescenter.materiel.pojo.entity.MaterielYearSummarizationEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MaterielYearSummarizationMapper extends BaseMapper<MaterielYearSummarizationEntity> {
    int updateBatch(List<MaterielYearSummarizationEntity> list);

    int updateBatchSelective(List<MaterielYearSummarizationEntity> list);

    int batchInsert(@Param("list") List<MaterielYearSummarizationEntity> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<MaterielYearSummarizationEntity> list);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insertOrUpdate(MaterielYearSummarizationEntity record);

    int insertOrUpdateSelective(MaterielYearSummarizationEntity record);
}