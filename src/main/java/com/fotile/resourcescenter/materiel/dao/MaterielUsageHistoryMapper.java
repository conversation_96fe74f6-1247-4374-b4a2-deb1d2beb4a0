package com.fotile.resourcescenter.materiel.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.resourcescenter.materiel.pojo.dto.DetailListDTO;
import com.fotile.resourcescenter.materiel.pojo.entity.MaterielUsageHistoryEntity;

import java.time.LocalDateTime;
import java.util.List;

import com.fotile.resourcescenter.materiel.pojo.vo.MaterielDashboardVO;
import com.fotile.resourcescenter.materiel.pojo.vo.MaterielDetailPageInfoVO;
import com.fotile.resourcescenter.materiel.pojo.vo.MaterielDetailVO;
import org.apache.ibatis.annotations.Param;

public interface MaterielUsageHistoryMapper extends BaseMapper<MaterielUsageHistoryEntity> {
    int updateBatch(List<MaterielUsageHistoryEntity> list);

    int updateBatchSelective(List<MaterielUsageHistoryEntity> list);

    int batchInsert(@Param("list") List<MaterielUsageHistoryEntity> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<MaterielUsageHistoryEntity> list);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insertOrUpdate(MaterielUsageHistoryEntity record);

    int insertOrUpdateSelective(MaterielUsageHistoryEntity record);

    MaterielDashboardVO dashboard(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end, @Param("firstDayOfMonth") LocalDateTime firstDayOfMonth, @Param("lastDayOfMonth") LocalDateTime lastDayOfMonth, @Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);

    Integer detailListCount(DetailListDTO detailListDTO);

    List<MaterielDetailVO> detailList(DetailListDTO detailListDTO);

    MaterielDetailPageInfoVO materielDetailSummarization(DetailListDTO detailListDTO);
}