package com.fotile.resourcescenter.materiel.client;

import com.fotile.framework.web.Result;
import com.netflix.ribbon.proxy.annotation.Http;
import feign.Headers;
import io.swagger.annotations.ResponseHeader;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;


import javax.servlet.annotation.HttpMethodConstraint;
import java.net.http.HttpConnectTimeoutException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020/11/12 13:40
 * 视觉中国
 */
@FeignClient(name = "test", url = "http://api.fotomore.com")
public interface VcgUtilClient {

    /**
     * 授权访问接口
     * @param param
     * @param accepte
     * @return access_token    用户授权的唯一票据，用于调用接口，同时也是第三方应用验证用户登录的唯一票据，第三方应用用该票据和自己应用内的用户建立唯一影射关系，来识别登录状态
     * @return expires_in	access_token的生命周期，单位是秒数。
     * @return token_type	access_token的类型
     * @return refresh_token	刷新令牌，用于刷新access_token
     */
    @RequestMapping(method = RequestMethod.POST, value = "/api/oauth2/access_token",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map<String,Object> vcgAccessToken(MultiValueMap<String,Object> param, @RequestHeader("accept-encoding") String accepte);




    /***
     * 刷新访问令牌
     * @param client_id    true	String	API用户鉴权 id；申请API时分配的API Key
     * @param client_secret	true	String	申请API应用时分配的密钥
     * @paramc grant_type	true	String	请求的类型，统一填写：authorization_code
     * @param refresh_token	true	String	刷新令牌，用于刷新access_token
     *
     * @return access_token	用户授权的唯一票据，用于调用接口，同时也是第三方应用验证用户登录的唯一票据，第三方应用用该票据和自己应用内的用户建立唯一影射关系，来识别登录状态
     * @return expires_in	access_token的生命周期，单位是秒数。
     * @return token_type	access_token的类型
     * @return refresh_token	刷新令牌，用于刷新access_token
     *
     *
     */
    @RequestMapping(method = RequestMethod.POST, value = "/api/oauth2/refresh_token",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Map<String,Object> vcgRefreshToken(MultiValueMap<String,Object> param, @RequestHeader("accept-encoding") String accepte);


    /***
     * 获取购买记录
     *  @param  api-key	true	string	API用户鉴权 id
     *  @param  authorization	true	string	Bearer+空格+access token值
     *  @param  photo_id	false	string	图片的res id，仅可输入一张图片的res id（使用此项参数，将导致请求的“购买开始时间”和“购买结束时间”无效）
     *  @param  start_date	false	string	请求查询购买记录明细的开始日期。格式：2017-07-01
     *  @param  end_date	false	string	请求查询购买记录明细的结束日期（与“开始时间”的时间差不能超过31天）。格式：2017-07-31
     *  @param  page	false	integer	哪一页
     *  @param  nums	false	integer	每页返回数据的最大值
     *  @param  account_id	false	integer	请求查询的客户id(该字段请求成功后，将返回目标企业下属所有合同及用户的购买记录)
     *  @param down_id	false	string	根据自定义的下载记录id获取下载记录，多条id用“;”分隔。当请求down_id成功时，photo_id、account_id、start_date、end_date请求失效
     *
     * @return photoid	图片的res id
     * @return small_url	图片的缩略图地址（尺寸：长边176）
     * @return purchase_id	购买记录id（每条购买记录唯一代码）
     * @return price	本条购买记录的价格
     * @return asset_type	媒资类型：1=图片、2=视频
     * @return asset_family	媒资分类：1=编辑类 2=创意类
     * @return license_type	图片的授权类型：rm、rf、rr
     * @return product_size	尺寸
     * @return purpose_code	用途编码
     * @return purpose_name	用途名称
     * @return description	图片说明
     * @return downloadTime	所购尺寸图片的下载时间
     * @return effective_time	该条购买记录生效的时间
     * @return User_name	该条购买记录对应的vcg用户名
     * @return real_name	客户系统的内部子账号昵称
     * @return down_id	下载记录id，由视觉中国为您存储，便于核对记录
     * @return total_count	总专题数量
     * @return total_page	总页数
     * @return per_page	每页数量
     * @return cur_page	当前页码
     *
     *
     */
    @RequestMapping(method = RequestMethod.GET, value = "/api/purchase/log",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String purchaseLog(@RequestHeader("api-key") String apiKey,@RequestHeader("authorization")  String authorization,@RequestHeader("accept-encoding") String accepte,@RequestParam String start_date,@RequestParam String end_date,@RequestParam String account_id, @RequestParam Integer page, @RequestParam Integer nums);


    /***
     * 图片详情接口
     *
     *  @param  api-key	true	string	API用户鉴权 id
     *  @param  authorization	true	string	Bearer+空格+access token值
     *  @param  ids	true	string	计划获取图片的id集合，最多50个，id间用逗号分割
     *  @param  type	false	integer	标识所查询的图片id类型:1=res id;2=图片主键id，默认为1
     *
     * @return id	图片的主键id
     * @return  title	图片的标题（仅创意图片）
     * @return res_id	图片的res id
     * @return credit_line	图片的署名
     * @return  caption	图片的说明（备注）
     * @return  width	图片的原图宽度
     * @return height	图片的原图高度
     * @return  asset_format	图片的图片格式，如：jpg
     * @return  asset_family	图片的类型（创意图片或编辑图片）
     * @return  channel_id	根据频道id进行筛选
     * @return license_type	图片的授权方式：RM、RF
     * @return  online_time	图片的上线时间
     * @return  origen_pic_byte	图片的原图文件存储大小（字节）
     * @return  have_limit_size	当前账号可以下载的图片尺寸 arg为请求尺寸的代码（仅创意图片）
     * @return  brand_id	图片所属品牌id（仅创意图片）
     * @return  brand_name	图片所属品牌名称（仅创意图片）
     * @return  copyright	图片的物权/肖像权信息
     * @return  brand_type	图片品牌属性（创意图： istock图片属性 1--Essentials,2--Signature）；（编辑图：图片属性'8--非新闻信息图片,9--新闻信息图片）
     * @return  keywords	图片所包含的关键词（仅创意图片）
     * @return  Keywords_placename	地点关键词
     * @return  small_url	图片的缩略图地址（尺寸：长边176）
     * @return  preview_url	图片的预览图地址（尺寸：长边400）
     * @return  middle_url	图片的中图地址（尺寸：长边800 带水印）
     * @return  country	国家
     * @return  province 	省份
     * @return  city	城市
     * @return  location	地点
     * @return  If_high_price	是否限价图：1=是；0=不是
     * @return  graphical_style	图片类型：1-摄影图片 2-插画 3-漫画 4-图表 5-矢量图 6-psd 7-普通全景 8-360全景 9-gif 10-模板 11-icon
     * @return  color_type	图片的色彩类别：彩色；黑白
     * @return  orientation	图片的构图方式：1横图；2竖图；3方图
     * @return  dpi	图片的dpi单位信息
     * @return  groupId	图片所属的组照id（仅编辑图片）
     * @return  groupTitle	图片所属组照的标题，（仅编辑图片）
     * @return  groupExplain	图片所属组照的图说（仅编辑图片）
     * @return  oneCategory	图片的一级分类（国内/国际/娱乐/体育等）（仅编辑图片）
     * @return  oneCategoryCn
     * @return  category	图片分类（仅编辑图片）
     *
     *
     *
     */
    @RequestMapping(method = RequestMethod.GET, value = "/api/images/detail",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String imagesDetail(@RequestHeader("api-key") String apiKey,@RequestHeader("authorization")  String authorization,@RequestHeader("accept-encoding") String accepte,@RequestParam String ids,@RequestParam String type);


    /***
     * 下载通用接口（图片/视频/音频）
     *
     *  @param  api-key	true	string	API用户鉴权 id
     * @param  authorization	true	string	Bearer+空格+access token值
     * @param  asset_type	ture	string	资源类型，1-图片，2-视频，3-音乐
     * @param  ids	true	string	图片 id，逗号分隔，最多20张
     * @param  specification	false	string	素材规格, 选填,数值范围1,2,5,8,10,28,48.对应细节1(750px 1M),2(1024px 2M),5(1700px 5M),8(2048px 8M),10(2500px 10M),28(4000px 28M),48(5000px 48M)，视频尺寸WEB/SD/HD/4K ，音频mp3/wav。如果为空，则下载最大授权尺寸
     * @param  username	false	string	此次下载所归属的用户名
     * @param  remark	false	string	选填，备注，仅记录使用
     *
     * @return id    id	图片的id
     * @return photo_id	图片的res id
     * @return auth	授权状态，成功or失败
     * @return auth_code	授权编码
     * @return auth_message	授权信息
     * @return specification	资源规格
     * @return down_id	本条下载记录id
     * @return down_url	此URL可获得根据需求下载的素材，此URL在72小时后过期
     *
     *
     *
     */
    @RequestMapping(method = RequestMethod.GET, value = "/api/auth_download_iva",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String vcgAuthDownloadIva(@RequestHeader("api-key") String apiKey,@RequestHeader("authorization")  String authorization,@RequestHeader("accept-encoding") String accepte,@RequestParam String asset_type,@RequestParam String ids,@RequestParam String specification,@RequestParam String username,@RequestParam String remark);


}
