package com.fotile.logcenter.circle.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

@Data
public class CircleExportOutput {
    /**
     * 业务员编码
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"企微成员userid"})
    @ApiModelProperty("企微成员userid")
    private String qywxUserId;
    /**
     * 业务员编码
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员编码"})
    @ApiModelProperty("业务员编码")
    private String chargeUserCode;
    /**
     * 业务员姓名
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员姓名"})
    @ApiModelProperty("业务员姓名")
    private String chargeUserName;
    /**
     * 业务员手机号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"业务员手机号"})
    @ApiModelProperty("业务员手机号")
    private String chargeUserPhone;
    /**
     * 岗位
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员岗位"})
    @ApiModelProperty("业务员岗位")
    private String station;
    /**
     * 业务员状态
     */
    @ColumnWidth(4)
    @ExcelProperty(value = {"业务员状态"})
    @ApiModelProperty("业务员状态")
    private String chargeUserStatus;
    /**
     * 大区
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员所属大区"})
    @ApiModelProperty("业务员所属大区")
    private String areaName;
    /**
     * 分公司ID
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"业务员所属分公司"})
    @ApiModelProperty("业务员所属分公司")
    private String companyName;
    /**
     * 门店
     */
    @ColumnWidth(20)
    @ExcelProperty(value = {"业务员所属门店"})
    @ApiModelProperty("业务员所属门店")
    private String storeName;
    /**
     * 成人积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"立一个志（成人）"})
    @ApiModelProperty("立一个志（成人）")
    private Long highAmbitionsPointCount;
    /**
     * 成事积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"立一个志（成事）"})
    @ApiModelProperty("立一个志（成事）")
    private Long successWorkPointCount;
    /**
     * 健身积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"立一个志（健身）"})
    @ApiModelProperty("立一个志（健身）")
    private Long goodPhysicalPointCount;
    /**
     * 读经积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"读一本经"})
    @ApiModelProperty("读一本经")
    private Long readingBookPointCount;
    /**
     * 改过积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"改一个过"})
    @ApiModelProperty("改一个过")
    private Long reformingErrorPointCount;
    /**
     * 行孝积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"行一次孝"})
    @ApiModelProperty("行一次孝")
    private Long performPietyPointCount;
    /**
     * 行善积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"日行一善"})
    @ApiModelProperty("日行一善")
    private Long goodDeedPointCount;
    /**
     * 总积分
     */
    @ColumnWidth(10)
    @ExcelProperty(value = {"总积分"})
    @ApiModelProperty("总积分")
    private Long sumCount;
}
