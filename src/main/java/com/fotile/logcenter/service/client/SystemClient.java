package com.fotile.logcenter.service.client;

import com.fotile.framework.web.Result;
import com.fotile.logcenter.pojo.dto.DicDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-12-09-14:09
 */
@Service
@FeignClient(value = "system-center" ,path="/api")
public interface SystemClient {

    /** 根据编码查询字典表
     * @param dto
     * @return
     */
    @GetMapping("/dic/api/open/queryByTypeCodes")
    Result<List<DicDto>> getDicByCodes(@SpringQueryMap DicDto dto);

}
