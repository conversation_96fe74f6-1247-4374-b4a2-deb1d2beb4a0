package com.fotile.logcenter.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class SalesmanInfoForQYWX implements Serializable {

    @ApiModelProperty(value="业务员id",example="业务员id")
    private Long id;

    @ApiModelProperty(value="业务员工号",example="业务员工号")
    private String code;

    @FieldEncrypt
    @ApiModelProperty(value="业务员姓名",example="业务员姓名")
    private String name;

    @FieldEncrypt
    @ApiModelProperty(value="业务员手机号",example="业务员手机号")
    private String phone;

    /**
     * 业务员头像
     */
    private String portrait;

    /**
     * 岗位
     */
    private Integer station;


    @ApiModelProperty(value="所属公司",example="所属公司id")
    private Long companyId;

    @ApiModelProperty(value="所属公司名称",example="所属公司名称")
    private String companyName;

    @ApiModelProperty(value="所属门店",example="所属门店id")
    private Long storeId;

    @ApiModelProperty(value="所属门店编码",example="所属门店编码")
    private String storeCode;

    @ApiModelProperty(value="所属门店名称",example="所属门店名称")
    private String storeName;

    @ApiModelProperty(value="所属门店名称简称",example="所属门店名称简称")
    private String abbreviation;
}
