package com.fotile.measuringscale.user.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("线索数据")
@Data
public class UserCluesForMarketDTO implements Serializable {

    @ApiModelProperty("站点")
    private int site_id;

    @ApiModelProperty("频道编码")
    private String cate;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("姓名")
    private String firstname;

    @ApiModelProperty("负责人id")
    private int chargeUserId;

    @ApiModelProperty("负责人姓名")
    private String chargeUserName;

//    @ApiModelProperty("性别")
//    private enum sex;

    @ApiModelProperty("省id")
    private String sheng_id;

    @ApiModelProperty("市id")
    private String shi_id;

    @ApiModelProperty("地区id")
    private String qu_id;

    @ApiModelProperty("省份")
    private String sheng;

    @ApiModelProperty("市")
    private String shi;

    @ApiModelProperty("地区")
    private String qu;

    @ApiModelProperty("数据来源")
    private String utm_source;

    @ApiModelProperty("详细地址")
    private String address;

}
