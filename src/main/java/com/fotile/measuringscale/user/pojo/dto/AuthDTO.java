package com.fotile.measuringscale.user.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("授权接口")
@Data
public class AuthDTO  implements Serializable {

    @ApiModelProperty("账号")
    private String username = "chicunbao";

    @ApiModelProperty("密码")
    private String password = "fotile818";

//    @ApiModelProperty("验证码")
//    private String validate = "";



    

}
