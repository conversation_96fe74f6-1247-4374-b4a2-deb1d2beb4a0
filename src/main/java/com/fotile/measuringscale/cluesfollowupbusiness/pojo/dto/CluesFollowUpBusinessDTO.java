package com.fotile.measuringscale.cluesfollowupbusiness.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel("跟进记录")
@Data
public class CluesFollowUpBusinessDTO implements Serializable {

    @ApiModelProperty("线索id")
    private String sourceId;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("跟进内容")
    private String contentText;

    @ApiModelProperty("跟进时间")
    private String followDate;

    @ApiModelProperty("服务类型")
    private String serverType;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("输入内容")
    private String problemList;

    @ApiModelProperty("省ID")
    private String provinceId;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private String cityId;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区ID")
    private String regionId;

    @ApiModelProperty("区名称")
    private String regionName;

    @ApiModelProperty("小区名称")
    private String cellName;

    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty("活动ID")
    private String site_id;

    @ApiModelProperty("数据来源")
    private String utm_source;

    @ApiModelProperty("业务员id")
    private String chargeUserId;

    @ApiModelProperty("业务员姓名")
    private String chargeUserName;

    @ApiModelProperty("顾客姓名")
    private String name;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("产品系列ID")
    private String productLineName;

}
