package com.fotile.framework.data.statistical.charge.utils;


import com.alibaba.fastjson.JSON;
import com.fotile.framework.data.statistical.charge.pojo.bo.ChargeToolAngleBO;

import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ChargeToolAngleCommonUilts {
    private static StringRedisTemplate stringRedisTemplate;
    //业务员工具角标前一天归档数据--CHARGE_%s_TOOL_ANGLE_ARCHIVE_%s
    //第一个%s为业务员id，第二个百分号s为前一天日期 日期为yyyyMMdd
    private static final String CHARGE_TOOL_ANGLE_ARCHIVE = "CHARGE_%s_TOOL_ANGLE_ARCHIVE_%s";
    //业务员工具角标每日操作数据--CHARGE_%s_TOOL_ANGLE_OPERATION_%s
    //第一个%s为业务员id，第二个百分号s为前一天日期 日期为yyyyMMdd
    private static final String CHARGE_TOOL_ANGLE_OPERATION = "CHARGE_%s_TOOL_ANGLE_OPERATION_%s";
    private static final String zero = "0";
    private static final long validityTime = 60 * 60 * 24;
    private static final int num = 12;

    @Autowired
    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        ChargeToolAngleCommonUilts.stringRedisTemplate = stringRedisTemplate;

    }


    /**
     * @param filed        字段 1.线索 2.订单 3.引流
     * @param symbol       符号 1.+ 2.- 3.* 4./
     * @param number       数量
     * @param chargeUserId 业务员id
     */
    public static void updateToolAngleNumber(Integer filed, Integer symbol, Integer number, Long chargeUserId) {

        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//        String yesterday = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = String.format(CHARGE_TOOL_ANGLE_OPERATION, chargeUserId, today);
        Boolean aBoolean = stringRedisTemplate.hasKey(key);
        ChargeToolAngleBO chargeToolAngleBO = null;
        if (Boolean.FALSE.equals(aBoolean)) {
            chargeToolAngleBO = extracted(chargeUserId);
        } else {
            try {
                String chargeToolAngleJson = stringRedisTemplate.opsForValue().get(key);
                chargeToolAngleBO = JSON.parseObject(chargeToolAngleJson, ChargeToolAngleBO.class);
            } catch (Exception e) {
                log.error("解析redis对象失败:", e);
                chargeToolAngleBO = extracted(chargeUserId);
            }
        }
        if (chargeToolAngleBO != null) {
            switch (filed) {
                case 1:
                    chargeToolAngleBO.setCluesNumber(calculate(symbol, number, chargeToolAngleBO.getCluesNumber()));

                    break;
                case 2:
                    chargeToolAngleBO.setOrderNumber(calculate(symbol, number, chargeToolAngleBO.getOrderNumber()));

                    break;
                case 3:
                    chargeToolAngleBO.setDrainageNumber(calculate(symbol, number, chargeToolAngleBO.getDrainageNumber()));
                    break;
            }
        }
        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(chargeToolAngleBO), validityTime, TimeUnit.MINUTES);

    }

    /**
     * 计算
     *
     * @param symbol
     * @param number
     * @param originalNumber
     * @return
     */
    public static String calculate(Integer symbol, Integer number, String originalNumber) {
        BigDecimal bigDecimal = new BigDecimal(originalNumber);
        switch (symbol) {
            case 1:
                bigDecimal = bigDecimal.add(new BigDecimal(number));
                break;
            case 2:
                bigDecimal = bigDecimal.subtract(new BigDecimal(number));
                break;
        }
        return bigDecimal.toString();
    }

    /**
     * 初始化
     *
     * @param chargeUserId
     * @return
     */
    public static ChargeToolAngleBO extracted(Long chargeUserId) {
        ChargeToolAngleBO chargeToolAngleBO = new ChargeToolAngleBO();
        chargeToolAngleBO.setChargeUserId(chargeUserId);
        chargeToolAngleBO.setCluesNumber(zero);
        chargeToolAngleBO.setDrainageNumber(zero);
        chargeToolAngleBO.setOrderNumber(zero);
        return chargeToolAngleBO;
    }

    /**
     * @param chargeUserId
     * @return
     */
    public static ChargeToolAngleBO show(Long chargeUserId) {
        ChargeToolAngleBO chargeToolAngleBO = new ChargeToolAngleBO();
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String yesterday = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //获取昨天合计
        String archiveKey = String.format(CHARGE_TOOL_ANGLE_ARCHIVE, chargeUserId, yesterday);
        String operationKey = String.format(CHARGE_TOOL_ANGLE_OPERATION, chargeUserId, today);

        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(archiveKey))) {
            return chargeToolAngleBO;
        }
        String archiveJson = stringRedisTemplate.opsForValue().get(archiveKey);
        chargeToolAngleBO = JSON.parseObject(archiveJson, ChargeToolAngleBO.class);
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(operationKey))) {
            return chargeToolAngleBO;
        }
        String operationJson = stringRedisTemplate.opsForValue().get(operationKey);
        ChargeToolAngleBO operationBo = JSON.parseObject(operationJson, ChargeToolAngleBO.class);
        if (operationBo != null && chargeToolAngleBO != null) {
            getCalculateChargeToolAngleBO(chargeToolAngleBO, chargeToolAngleBO, operationBo.getCluesNumber(), operationBo.getOrderNumber(), operationBo.getDrainageNumber());
        }
        return chargeToolAngleBO;
    }

    /**
     * 昨天新增的数据
     *
     * @param chargeToolAngleBO
     */
    public static void archive(ChargeToolAngleBO chargeToolAngleBO) {
        String yesterday = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String archiveday = LocalDateTime.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //昨日数据
        String yesterdayKey = String.format(CHARGE_TOOL_ANGLE_ARCHIVE, chargeToolAngleBO.getChargeUserId(), yesterday);
        //已经归档过的数据
        String archivedayKey = String.format(CHARGE_TOOL_ANGLE_ARCHIVE, chargeToolAngleBO.getChargeUserId(), archiveday);
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(archivedayKey))) {
            throw new BusinessException("512", "未初始化");
        }
        String archivedayJson = stringRedisTemplate.opsForValue().get(archivedayKey);
        ChargeToolAngleBO archivedayBo = JSON.parseObject(archivedayJson, ChargeToolAngleBO.class);
        if (archivedayBo != null) {
            getCalculateChargeToolAngleBO(chargeToolAngleBO, archivedayBo, chargeToolAngleBO.getCluesNumber(), chargeToolAngleBO.getOrderNumber(), chargeToolAngleBO.getDrainageNumber());
            stringRedisTemplate.opsForValue().set(yesterdayKey, JSON.toJSONString(archivedayBo), validityTime, TimeUnit.MINUTES);
            stringRedisTemplate.delete(archivedayKey);
        }
    }

    private static void getCalculateChargeToolAngleBO(ChargeToolAngleBO chargeToolAngleBO, ChargeToolAngleBO archivedayBo, String cluesNumber, String orderNumber, String drainageNumber) {
        archivedayBo.setCluesNumber(new BigDecimal(chargeToolAngleBO.getCluesNumber()).add(new BigDecimal(cluesNumber)).toString());
        archivedayBo.setOrderNumber(new BigDecimal(chargeToolAngleBO.getOrderNumber()).add(new BigDecimal(orderNumber)).toString());
        archivedayBo.setDrainageNumber(new BigDecimal(chargeToolAngleBO.getDrainageNumber()).add(new BigDecimal(drainageNumber)).toString());
    }

}
