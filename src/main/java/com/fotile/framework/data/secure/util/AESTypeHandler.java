package com.fotile.framework.data.secure.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(value = String.class)
public class AESTypeHandler extends BaseTypeHandler {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, MybatisMateConfig.encrypt((String) parameter));
        } catch (Exception e) {
            throw new SQLException(e);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            return StringUtils.isBlank(rs.getString(columnName)) ? rs.getString(columnName) : MybatisMateConfig.decrypt(rs.getString(columnName));
        } catch (Exception e) {
            throw new SQLException(e);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            return StringUtils.isBlank(rs.getString(columnIndex)) ? rs.getString(columnIndex) : MybatisMateConfig.decrypt(rs.getString(columnIndex));
        } catch (Exception e) {
            throw new SQLException(e);
        }
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            return StringUtils.isBlank(cs.getString(columnIndex)) ? cs.getString(columnIndex) : MybatisMateConfig.decrypt(cs.getString(columnIndex));
        } catch (Exception e) {
            throw new SQLException(e);
        }
    }
}
