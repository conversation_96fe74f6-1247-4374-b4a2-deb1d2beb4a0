package com.fotile.customercenter.customerQuestionnaire.service;

import com.fotile.customercenter.client.MarketingClient;
import com.fotile.customercenter.client.UserClient;
import com.fotile.customercenter.client.pojo.InsertSalesLeadsBusinessInDto;
import com.fotile.customercenter.client.pojo.QueryAllMenuByRoleNamesOutDto;
import com.fotile.customercenter.customerExport.pojo.excel.ExportQuestionDto;
import com.fotile.customercenter.customerQuestionnaire.dao.CustomerSubmitDao;
import com.fotile.customercenter.customerQuestionnaire.pojo.dto.CustomerAnswerDto;
import com.fotile.customercenter.customerQuestionnaire.pojo.dto.CustomerQuestionDto;
import com.fotile.customercenter.customerQuestionnaire.pojo.dto.CustomerSubmitDto;
import com.fotile.customercenter.customerQuestionnaire.pojo.dto.CustomerSubmitQueryDto;
import com.fotile.customercenter.customerQuestionnaire.pojo.entity.CustomerAnswer;
import com.fotile.customercenter.customerQuestionnaire.pojo.entity.CustomerQuestion;
import com.fotile.customercenter.customerQuestionnaire.pojo.entity.CustomerSubmit;
import com.fotile.customercenter.customerQuestionnaire.pojo.interfaces.Save;
import com.fotile.customercenter.customerQuestionnaire.pojo.mapper.QuestionnaireMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.StoreAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceLookup;
import com.fotile.framework.web.Result;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021-12-21-18:10
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "sixth")
@Slf4j
@Validated
public class CustomerSubmitService {
    @Resource
    private CustomerSubmitDao submitDao;
    @Resource
    private CustomerAnswerService answerService;
    @Resource
    private CustomerQuestionService questionService;
    @Resource
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private UserClient userClient;
    @Autowired
    private MarketingClient marketingClient;

    private static final ImmutableSet<String> storeMangers = ImmutableSet.of("customer_scan_auth");

    private static final String signType="SERVICE_EVALUATION";



    @Transactional(rollbackFor = Exception.class)
    @Validated({CustomerSubmitDto.Save.class,Save.class})
    @DruidTxcMultiDataSourceAnnontation(value = "first")
    public boolean saveSubmit(@Valid CustomerSubmitDto submitDto){
    //    DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("");
        String currentUserId = userAuthorConfig.getCurrentUserId();
        Date now = new Date();
        submitDto.setCreatedBy(currentUserId);
        submitDto.setCreatedDate(now);
        CustomerSubmit customerSubmit = QuestionnaireMapper.INSTANCE.dto2Entity(submitDto);
        int insert = submitDao.insert(customerSubmit);

        List<CustomerQuestionDto> questionsWithAnswer = submitDto.getQuestionsWithAnswer();
        if(questionsWithAnswer!=null && questionsWithAnswer.size()>0){
            List<CustomerAnswer> answerList = questionsWithAnswer.stream()
                    .map(answer -> {
                        CustomerAnswer answer1 = new CustomerAnswer();
                        answer1.setSubmitId(customerSubmit.getId());
                        answer1.setCreatedDate(now);
                        answer1.setCreatedBy(currentUserId);
                        answer1.setAnswers(answer.getAnswers());
                        answer1.setQuestionId(answer.getId());
                        return answer1;
                    }).collect(Collectors.toList());
            answerService.saveBatch(answerList);
        }
        //第三步：调用第三方接口(新增线索--签到)
        try {
            InsertSalesLeadsBusinessInDto salesLeadsBusinessInDto = new InsertSalesLeadsBusinessInDto();
            salesLeadsBusinessInDto.setChargeUserId(submitDto.getChargeUserId() + "");
            salesLeadsBusinessInDto.setCustomerName(submitDto.getCustomerName());
            salesLeadsBusinessInDto.setCustomerPhone(submitDto.getCustomerPhone());
            salesLeadsBusinessInDto.setActivityId(submitDto.getActivityId());
            salesLeadsBusinessInDto.setCategory(2);
            salesLeadsBusinessInDto.setCheckManagerIn2Sea(true);
            salesLeadsBusinessInDto.setConcealId(submitDto.getConcealId());
            salesLeadsBusinessInDto.setSignType(signType);
            marketingClient.insertUserCluesOrLog(salesLeadsBusinessInDto);
        } catch (Exception e) {
            log.error("ERROR*********CustomerService.customerSignIn marketingClient " + e);
        }
        return insert>0;
    }

    public PageInfo<CustomerSubmitDto> page(CustomerSubmitDto submitDto) {
        List<QueryAllMenuByRoleNamesOutDto> menus = null;
        Result<List<QueryAllMenuByRoleNamesOutDto>> menuResult = userClient.queryAllMenuByRoleNames();
        if(menuResult!=null && menuResult.getSuccess() && menuResult.getData()!=null){
            menus=  menuResult.getData();
        }
        if(menus!=null&& !menus.isEmpty()){
            boolean b = menus.stream().anyMatch(menu -> storeMangers.contains(menu.getUri()));
            if(b){
                submitDto.setChargeUserId(null);
                List<StoreAuthor> storeAuthors = userAuthorConfig.queryCompanyCrossStoreAuthorList(-1);
                List<Long> storeOrgIds = storeAuthors.stream().map(StoreAuthor::getOrgId).collect(Collectors.toList());
                submitDto.setStoreIds(storeOrgIds);
            }
        }
        PageInfo<CustomerSubmitDto> pageInfo = new PageInfo<>(submitDto.getPage(), submitDto.getSize());
      int count=  submitDao.queryCount(submitDto);
        pageInfo.setTotal(count);
        if(count>0){
          List<CustomerSubmitDto> list=  submitDao.queryPage(submitDto,pageInfo);
            fillCustomerPhone(list,null);
            pageInfo.setRecords(list);
        }
        return pageInfo;
    }

    public CustomerSubmitDto getById(Long id) {
        CustomerSubmitDto result=null;
        if(id!=null){
            CustomerSubmit customerSubmit = submitDao.selectByPrimaryKey(id);
            if(customerSubmit!=null){
                result = QuestionnaireMapper.INSTANCE.entity2Dto(customerSubmit);
                LinkedList<CustomerSubmitDto> dtoList = Lists.newLinkedList();
                dtoList.add(result);
                fillCustomerPhone(dtoList,null);
                CustomerAnswerDto answerDto = new CustomerAnswerDto();
                answerDto.setSubmitId(result.getId());
                List<CustomerAnswer> list = answerService.list(answerDto);
                if(!list.isEmpty()){
                    LinkedList<Long> questionIds = new LinkedList<>();
                    HashMap<Long, CustomerAnswer> answerMap = new HashMap<>();
                    for (CustomerAnswer answer:list){
                        answerMap.put(answer.getQuestionId(),answer);
                        questionIds.add(answer.getQuestionId());
                    }
                    CustomerQuestionDto customerQuestionDto = new CustomerQuestionDto().setIds(questionIds);
                    List<CustomerQuestion> questions = questionService.list(customerQuestionDto);
                   if(questions!=null && !questions.isEmpty()){
                       List<CustomerQuestionDto> questionDtos = questions.stream().map(question -> {
                           CustomerAnswer answer = answerMap.get(question.getId());
                           CustomerQuestionDto questionDto = QuestionnaireMapper.INSTANCE.entity2Dto(question);
                           questionDto.setAnswers(answer.getAnswers());
                           return questionDto;
                       }).collect(Collectors.toList());
                       result.setQuestionsWithAnswer(questionDtos);
                   }
                }
            }
        }
        return result;
    }



    public PageInfo<CustomerSubmitDto> htmlPage(CustomerSubmitQueryDto queryDto) {
        List<StoreAuthor> storeAuthors = userAuthorConfig.queryCompanyCrossStoreAuthorList(-1);
        List<Long> storeIds=null;
        if(storeAuthors != null && storeAuthors.size()>0){
            storeIds=storeAuthors.stream().map(StoreAuthor::getOrgId).collect(Collectors.toList());
        }
        queryDto.setStoreIds(storeIds);
        PageInfo<CustomerSubmitDto> pageInfo = new PageInfo<>(queryDto.getPage(), queryDto.getSize());
        Integer count= submitDao.htmlPageCount(queryDto,pageInfo);
        pageInfo.setTotal(count);
        if(count>0){
            List<CustomerSubmitDto> list= submitDao.htmlPage(queryDto,pageInfo);
            if (CollectionUtils.isNotEmpty(list)) {
                fillSubmitAnswer(list);
            }
            pageInfo.setRecords(list);
        }

        return pageInfo;
    }

    private List<CustomerSubmitDto> fillSubmitAnswer(List<CustomerSubmitDto> list){
        if(list!=null && !list.isEmpty()){
            HashMap<Long, CustomerSubmitDto> submitDtoHashMap = Maps.newHashMap();
            List<Long> submitIds = Lists.newLinkedList();
            for (CustomerSubmitDto customerSubmitDto : list) {
                submitDtoHashMap.put(customerSubmitDto.getId(),customerSubmitDto);
                submitIds.add(customerSubmitDto.getId());
            }
            fillCustomerPhone(list,submitIds);
            CustomerAnswerDto answerDto = new CustomerAnswerDto();
            answerDto.setSubmitIds(submitIds);
            List<CustomerAnswer> answerList = answerService.list(answerDto);
            if(!answerList.isEmpty()){
                for (CustomerAnswer answer : answerList) {
                    CustomerSubmitDto customerSubmitDto = submitDtoHashMap.get(answer.getSubmitId());
                    if(customerSubmitDto!=null){
                        if(customerSubmitDto.getAnswerList()==null){
                            customerSubmitDto.setAnswerList(Lists.newLinkedList());
                        }
                        customerSubmitDto.getAnswerList().add(answer);
                    }
                }
            }
        }
        return list;
    }

    @DruidTxcMultiDataSourceAnnontation(value = "first")
    public List<CustomerSubmitDto> fillCustomerPhone(List<CustomerSubmitDto> list,  List<Long> submitIds){

        if(list!=null && !list.isEmpty()){
            if( submitIds==null || submitIds.isEmpty()){
                submitIds = Lists.newLinkedList();
                for (CustomerSubmitDto customerSubmitDto : list) {
                    submitIds.add(customerSubmitDto.getId());
                }
            }
            if(!submitIds.isEmpty()){
                CustomerSubmitDto queryDto = new CustomerSubmitDto();
                queryDto.setIds(submitIds);
                DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("first");
                Map<Long, CustomerSubmitDto> idMap = submitDao.queryList(queryDto)
                        .stream()
                        .collect(Collectors.toMap(CustomerSubmitDto::getId, dto -> dto));
                for (CustomerSubmitDto submitDto : list) {
                    CustomerSubmitDto phoneDto = idMap.get(submitDto.getId());
                    if(phoneDto!=null){
                        submitDto.setCustomerPhone(phoneDto.getCustomerPhone());
                    }
                }
            }
        }
        return list;
    }


    @DruidTxcMultiDataSourceAnnontation(value = "first")
    public List<ExportQuestionDto> fillExportCustomerPhone(List<ExportQuestionDto> list,  List<Long> submitIds){

        if(list!=null && !list.isEmpty()){
            if( submitIds==null || submitIds.isEmpty()){
                submitIds = Lists.newLinkedList();
                for (ExportQuestionDto customerSubmitDto : list) {
                    submitIds.add(customerSubmitDto.getId());
                }
            }
            if(!submitIds.isEmpty()){
                CustomerSubmitDto queryDto = new CustomerSubmitDto();
                queryDto.setIds(submitIds);
                DruidTxcMultiDataSourceLookup.setThreadLocalDBKey("first");
                Map<Long, CustomerSubmitDto> idMap = submitDao.queryList(queryDto)
                        .stream()
                        .collect(Collectors.toMap(CustomerSubmitDto::getId, dto -> dto));
                for (ExportQuestionDto submitDto : list) {
                    CustomerSubmitDto phoneDto = idMap.get(submitDto.getId());
                    if(phoneDto!=null){
                        submitDto.setCustomerPhone(phoneDto.getCustomerPhone());
                    }
                }
            }
        }
        return list;
    }


    public Integer count(CustomerSubmitQueryDto inDto) {
        List<StoreAuthor> storeAuthors = userAuthorConfig.queryCompanyCrossStoreAuthorList(-1);
        List<Long> storeIds=null;
        if(storeAuthors.size()>0){
            storeIds=storeAuthors.stream().map(StoreAuthor::getOrgId).collect(Collectors.toList());
        }
        inDto.setStoreIds(storeIds);
        Integer count= submitDao.htmlPageCount(inDto,null);
        return count;
    }

    public List<ExportQuestionDto> getExportList(CustomerSubmitQueryDto queryParam, PageInfo<ExportQuestionDto> pageInfo) throws NoSuchFieldException, IllegalAccessException {
        List<ExportQuestionDto> list=  submitDao.getExportList(queryParam,pageInfo);
        if(list!=null && !list.isEmpty()){
            HashMap<Long, ExportQuestionDto> submitDtoHashMap = Maps.newHashMap();
            List<Long> submitIds = Lists.newLinkedList();
            for (ExportQuestionDto dto : list) {
                submitDtoHashMap.put(dto.getId(),dto);
                submitIds.add(dto.getId());
            }
            fillExportCustomerPhone(list,submitIds);
            CustomerAnswerDto answerDto = new CustomerAnswerDto();
            answerDto.setSubmitIds(submitIds);
            List<CustomerAnswer> answerList = answerService.list(answerDto);
            if(!answerList.isEmpty()){
                Class<ExportQuestionDto> exportQuestionDtoClass = ExportQuestionDto.class;
                for (CustomerAnswer answer : answerList) {
                    ExportQuestionDto exportQuestionDto = submitDtoHashMap.get(answer.getSubmitId());
                    if(exportQuestionDto!=null){
                        if(Boolean.TRUE.equals(queryParam.getDesensitization()) ){
                            exportQuestionDto.setCustomerPhone(setCostomerPhone(exportQuestionDto.getCustomerPhone()));
                        }
                        Field declaredField = exportQuestionDtoClass.getDeclaredField("q" + answer.getQuestionId());
                        declaredField.setAccessible(true);
                        declaredField.set(exportQuestionDto,answer.getAnswers());
                    }
                }
            }
        }
        return list;
    }

    //手机号脱敏
    private String setCostomerPhone(String phone) {
        if (phone == null || phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }
}
