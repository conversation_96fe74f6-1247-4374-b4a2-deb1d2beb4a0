package com.fotile.customercenter.feedback.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "意见反馈", description = "意见反馈")
public class FeedBackDetail{
	@TableField(value = "id")
    /** * id */
    private Long id;
	
	@TableField(value = "user_name")
    /** * 用户名 */
    @FieldEncrypt
    private String userName;
	
	@TableField(value = "user_id")
    /** * 用户id */
    private String userId;
	
	@TableField(value = "phone")
    /** * 手机号 */
    @FieldEncrypt
    private String phone;

	@TableField(value = "type")
    /** * 意见类型 */
    private String type;
	
	@TableField(value = "status")
    /** * 状态 */
    private String status;
	
	@TableField(value = "created_date")
    /** * 意見創建時間 */
	private Date createdDate;
	
	@TableField(value = "deal_date")
    /** * 意見處理時間 */
	private Date dealDate;
	
	@TableField(value = "resolve_date")
    /** * 意見解決時間 */
	private Date resolveDate;
	
	@TableField(value = "close_date")
    /** * 關閉時間 */
	private Date closeDate;
	
	@TableField(value = "channel_source")
    /** * 渠道来源 */
    private String channelSource;
	
	@TableField(value = "charge_user_name")
    /** * 经办人 */
	@FieldEncrypt
    private String chargeUserName;
	
	@TableField(value = "service_score")
    /** * 评分 */
    private String serviceScore;
	
	@TableField(value = "describe")
    /** * 意见描述 */
    private String describe;
	
	@TableField(value = "score_note")
    /** * 評分備注 */
    private String scoreNote;
	
	@TableField(value = "note")
    /** * 備注 */
    private String note;
	private String pictureUrl;
	private String chargeUserId;
	private String feedbackTypes;
    @FieldEncrypt
    private String userPhone;
    @TableField(value = "user_email")
    /** * 意见用户邮箱 */
	private String userEmail;

    //用户意见反馈类型
    private String opinionType;
    //设备
    private String device;

    private List<Picture> pictures;
	private String appVersion;
	@FieldEncrypt
	private String subUser;
	@FieldEncrypt
	private String subUserName;

	private String radioCode;
	private String areaName;
	private String companyName;
	private String storeName;
	private String stationName;
	private String channelName;

	/**
	 * 最终意见类型=其他时，添加描述
	 */
	private String otherNote;
	/**
	 * 跟进人
	 */
	@FieldEncrypt
	private String followUser;
	/**
	 * 跟进人姓名
	 */
	@FieldEncrypt
	private String followUserName;

}
