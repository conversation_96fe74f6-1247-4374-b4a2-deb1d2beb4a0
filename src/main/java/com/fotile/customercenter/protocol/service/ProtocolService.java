package com.fotile.customercenter.protocol.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.customercenter.client.UserClient;
import com.fotile.customercenter.protocol.dao.ProtocolDao;
import com.fotile.customercenter.protocol.pojo.ProtocolConverter;
import com.fotile.customercenter.protocol.pojo.dto.*;
import com.fotile.customercenter.protocol.pojo.entity.Protocol;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class ProtocolService extends ServiceImpl<ProtocolDao, Protocol> {
    @Autowired
    private ProtocolDao protocolDao;

    @Autowired
    private UserClient userClient;

    public PageInfo<FindAllOutDto> findProtocolPageAll(FindProtocolAllDto query) {
        if (query == null) {
            return null;
        }

        Integer totalRows = protocolDao.findProtocolCount(query);
        if (totalRows == null || totalRows <= 0) {
            return null;
        }

        PageInfo<FindAllOutDto> pageInfo = new PageInfo<>(query.getPage(), query.getSize(), totalRows);
        List<FindAllOutDto> list = protocolDao.findProtocolPageAll(query, pageInfo);
        pageInfo.setRecords(list);

        return pageInfo;
    }

    //@Cached(area = "customercenter",name = "protocol-findAll",cacheType = CacheType.REMOTE,expire = 3600)
    public List<FindAllOutDto> findAll(FindProtocolAllDto findProtocolAllDto) {
        List<Protocol> list = protocolDao.findAll(findProtocolAllDto);
        //ProtocolConverter.INSTANCE.preotocol2FindAllOutDto(list);
        return ProtocolConverter.INSTANCE.preotocol2FindAllOutDto(list);
    }

    public FindAllOutDto findProtocolById(FindByIdInDto inDto) {
        Protocol protocol = protocolDao.selectById(inDto.getId());
        //  ProtocolConverter.INSTANCE.preotocol2FindAllOutDto(list);
        return ProtocolConverter.INSTANCE.preotocol2FindAllOutDto2(protocol);
    }

    public List<FindAllOutDto> findProtocolByIdList(ProtocolParamDto dto) {
        return protocolDao.getProtocolByIdList(dto.getIdList());
    }


    //@CacheInvalidateByExp(patternKey = "customercenter_protocol*",area = "customercenter")
    public int insertProtocol(InsertProtocolInDto inDto) {

        Protocol protocol = ProtocolConverter.INSTANCE.insert2protocol(inDto);
        int i = protocolDao.insert(protocol);
        return i;
    }

    //@CacheInvalidateByExp(patternKey = "customercenter_protocol*",area = "customercenter")
    public int updateProtocolById(UpdateProtocolInDto inDto) {

        Protocol oldProtocol = protocolDao.selectById(inDto.getId());

        Protocol protocol = ProtocolConverter.INSTANCE.upodate2protocol(inDto);
//        int i = protocolDao.updateById(protocol);
        int i = protocolDao.updateOne(protocol);
        if (oldProtocol.getReagreeFlag() != null && oldProtocol.getReagreeFlag() == 1 && !StringUtils.equals(oldProtocol.getContext(), protocol.getContext())) {
            userClient.updateConfirmStatus(protocol.getId());
        }
        return i;
    }

}
