package com.fotile.customercenter.protocol.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class FindAllOutDto {
    private Long id;

    /**
     * 文档名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 文档内容
     */
    @TableField(value = "content")
    private String context;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 更新提示标题
     */
    @TableField(value = "update_title")
    private String updateTitle;
    /**
     * 更新提示内容
     */
    @TableField(value = "update_content")
    private String updateContent;
    /**
     * 更新提示时间
     */
    @TableField(value = "revise_time")
    private Date reviseTime;
    /**
     * 版本号
     */
    private String versionNumber;
    /**
     * 类型（数据字典取protocol_type）
     */
    private Integer type;
    /**
     * 分类（数据字典取protocol_category）
     */
    private Integer category;
    /**
     * 是否撤回：1是/0否
     */
    private Integer isRevoke;
    /**
     * 协议更新之后是否需要用户重新查看并同意，0否，1是
     */
    @TableField(value = "reagree_flag")
    private Integer reagreeFlag;

}
