package com.fotile.customercenter.customerlogout.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class AlterCustomerPointNewDto {
    private Long id;
    //方向(-1:减少,1:增加)
    private Integer direction;
    //顾客id
    private String sourceId;
    //顾客编码
    private String customerCode;
    //顾客名称
    private String customerName;
    //顾客电话
    private String customerPhone;
    //公司id
    private Long sourceIdBy;
    //公司code
    private String companyCode;
    private String companyName;
    //变动类型
    private String typeCode;
    //变动类型名称
    private String typeName;
    //订单id
    private String orderId;
    //流水备注
    private String remark;
    private String modifiedName;
    //变动积分
    private BigDecimal available;
    private String sourceTidBy;
    private String sourceTid;
    private String channelCode;
    private String channelName;
}