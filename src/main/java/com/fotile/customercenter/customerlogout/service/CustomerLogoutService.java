package com.fotile.customercenter.customerlogout.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fotile.customercenter.constant.LogConstant;
import com.fotile.customercenter.customer.dao.CustomerInfoDao;
import com.fotile.customercenter.customer.pojo.dto.CustomerInfoVto;
import com.fotile.customercenter.customer.pojo.dto.QueryCustomerPointDto;
import com.fotile.customercenter.customer.pojo.dto.QueryLogInDto;
import com.fotile.customercenter.customer.pojo.dto.QueryOperatorLogDto;
import com.fotile.customercenter.customer.pojo.dto.org.FindNameByIdsInDto;
import com.fotile.customercenter.customer.pojo.dto.org.FindOrgByIdOutDto;
import com.fotile.customercenter.customer.pojo.entity.CustomerInfo;
import com.fotile.customercenter.customer.pojo.entity.OperatorLog;
import com.fotile.customercenter.customer.service.OperatorLogService;
import com.fotile.customercenter.customer.service.client.OrgClientService;
import com.fotile.customercenter.customer.service.client.PointClientService;
import com.fotile.customercenter.customer.service.client.PromotionClientService;
import com.fotile.customercenter.customer.service.client.UserClientService;
import com.fotile.customercenter.customerlogout.dao.CustomerLogoutDao;
import com.fotile.customercenter.customerlogout.mq.AuditLogOutApply;
import com.fotile.customercenter.customerlogout.mq.AuditLogOutApplyMember;
import com.fotile.customercenter.customerlogout.pojo.dto.*;
import com.fotile.customercenter.customerlogout.pojo.entity.CustomerLogout;
import com.fotile.customercenter.util.EmptyUtil;
import com.fotile.customercenter.util.KeycloakTemplate;
import com.fotile.customercenter.util.UserAuthorUtils;
import com.fotile.framework.auth.config.MultiKeycloakConfigResolverWapper;
import com.fotile.framework.auth.config.MyDeploymentDelegate;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.web.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @Auther: ldp
 * @Date: 2020/11/16
 * @Description:
 */
@Slf4j
@Service
public class CustomerLogoutService {
    @Autowired
    private CustomerLogoutDao customerLogoutDao;
    @Autowired
    private CustomerInfoDao customerDao;
    @Autowired
    private UserClientService userClientService;
    @Autowired
    private OrgClientService orgClientService;
    @Autowired
    private PointClientService pointClientService;
    @Autowired
    private PromotionClientService promotionClientService;
    @Resource
    private UserAuthorConfig userAuthorConfig;
    @Resource(name = AuditLogOutApply.SEND_AUDIT_LOGOUT_APPLY_MSG )
    private MessageChannel sendApplyMsg;
    @Resource(name = AuditLogOutApplyMember.SEND_AUDIT_LOGOUT_APPLY_MSG_M)
    private MessageChannel sendApplyMsgMember;
    @Resource
    private OperatorLogService operatorLogService;

    public PageInfo<QueryCustomerLogoutOutDto> list(QueryCustomerLogoutInDto inDto) {
        if (StringUtils.isNotBlank(inDto.getId())) {
            List<Long> ids = new ArrayList();
            String[] cids = inDto.getId().split(",");
            for (int i = 0; i < cids.length; i++) {
                if (StringUtils.isNotBlank(cids[i])) {
                    ids.add(Long.parseLong(cids[i]));
                }
            }
            inDto.setIds(ids);
        }

        PageInfo<QueryCustomerLogoutOutDto> pageInfo = new PageInfo<>(inDto.getPageNum(), inDto.getPageSize());
        Long count = customerLogoutDao.getCounts(inDto);
        List<QueryCustomerLogoutOutDto> outDtoList = new ArrayList<>();
        if (count > 0) {
            inDto.setOffset(pageInfo.getOffset());
            outDtoList = customerLogoutDao.list(inDto);
            //手机号码脱敏-start
            for (QueryCustomerLogoutOutDto outDto : outDtoList) {
                if (StringUtils.isNotEmpty(outDto.getPhone())) {
                    outDto.setPhone(setCostomerPhone(outDto.getPhone()));
                }
            }
            //手机号码脱敏-end
            List<Long> conpanyIds = outDtoList.stream().distinct()
                    .filter(outDto -> EmptyUtil.isNotEmpty(outDto.getCompanyId()) && outDto.getCompanyId() != -1L)
                    .map(QueryCustomerLogoutOutDto::getCompanyId)
                    .collect(Collectors.toList());
            List<Long> storeIds = outDtoList.stream().distinct()
                    .filter(outDto -> EmptyUtil.isNotEmpty(outDto.getStoreId()) && outDto.getStoreId() != -1L)
                    .map(QueryCustomerLogoutOutDto::getStoreId)
                    .collect(Collectors.toList());
            conpanyIds.addAll(storeIds);
            if (conpanyIds != null && conpanyIds.size() != 0) {
                try {
                    List<FindOrgByIdOutDto> idOutdtos = orgClientService.findNameByIds(new FindNameByIdsInDto(conpanyIds)).getData();
                    Map<Long, String> idMap = idOutdtos.stream().collect(Collectors.toMap(FindOrgByIdOutDto::getId, FindOrgByIdOutDto::getName));
                    // List<FindOrgByIdOutDto> storeIdoutdtos = orgClientService.findNameByIds(new FindNameByIdsInDto3(storeIds)).getData();

                    for (QueryCustomerLogoutOutDto outDto : outDtoList) {
                        if (EmptyUtil.isNotEmpty(outDto.getCompanyId()) && outDto.getCompanyId() != -1L) {
                            outDto.setCompanyName(idMap.get(outDto.getCompanyId()));
                        }
                        if (EmptyUtil.isNotEmpty(outDto.getStoreId()) && outDto.getStoreId() != -1L) {
                            outDto.setStore(idMap.get(outDto.getStoreId()));
                        }
                        outDto.setNickname(encryptNickname(outDto.getNickname()));
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(outDtoList);
        return pageInfo;
    }

    private String encryptNickname(String nickname){
        if(StringUtils.isNotBlank(nickname)){
            int length = nickname.length();
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(nickname.charAt(0));
                stringBuffer.append("****");
                stringBuffer.append(nickname.charAt(length-1));
                nickname=stringBuffer.toString();
        }
        return nickname;
    }

    private String setCostomerPhone(String phone) {
        if (phone.length() < 5) {
        } else if (phone.length() >= 5 && phone.length() < 9) {
            String s = phone.substring(phone.length() - 4, phone.length());
            phone = "****" + s;
        } else if (phone.length() >= 9) {
            String s1 = phone.substring(phone.length() - 4, phone.length());
            String s2 = phone.substring(0, 3);
            phone = s2 + "****" + s1;
        }
        return phone;
    }


    /**
     * 根据条件查询用户注销信息并导出
     *
     * @param inDto 用户条件
     * @return
     */
    public List<QueryCustomerLogoutOutDto> findDecCustomerLogoutList(QueryCustomerLogoutInDto inDto) {
        //把id拆分到集合中，可以通过多个id共同查询
        if (StringUtils.isNotBlank(inDto.getId())) {
            List<Long> ids = new ArrayList();
            String[] cids = inDto.getId().split(",");
            for (int i = 0; i < cids.length; i++) {
                if (StringUtils.isNotBlank(cids[i])) {
                    ids.add(Long.parseLong(cids[i]));
                }
            }
            inDto.setIds(ids);
        }
        PageInfo<QueryCustomerLogoutOutDto> pageInfo = new PageInfo<>();
        Long count = customerLogoutDao.getCounts(inDto);
        List<QueryCustomerLogoutOutDto> outDtoList = new ArrayList<>();
        if (count > 0) {
            inDto.setOffset(pageInfo.getOffset());
            outDtoList = customerLogoutDao.findDecCustomerLogoutList(inDto);
            List<Long> conpanyIds = outDtoList.stream().distinct()
                    .filter(outDto -> EmptyUtil.isNotEmpty(outDto.getCompanyId()) && outDto.getCompanyId() != -1L)
                    .map(QueryCustomerLogoutOutDto::getCompanyId)
                    .collect(Collectors.toList());
            List<Long> storeIds = outDtoList.stream().distinct()
                    .filter(outDto -> EmptyUtil.isNotEmpty(outDto.getStoreId()) && outDto.getStoreId() != -1L)
                    .map(QueryCustomerLogoutOutDto::getStoreId)
                    .collect(Collectors.toList());
            conpanyIds.addAll(storeIds);
            if (conpanyIds != null && conpanyIds.size() != 0) {
                try {
                    List<FindOrgByIdOutDto> idOutdtos = orgClientService.findNameByIds(new FindNameByIdsInDto(conpanyIds)).getData();
                    Map<Long, String> idMap = idOutdtos.stream().collect(Collectors.toMap(FindOrgByIdOutDto::getId, FindOrgByIdOutDto::getName));
                    // List<FindOrgByIdOutDto> storeIdoutdtos = orgClientService.findNameByIds(new FindNameByIdsInDto3(storeIds)).getData();

                    for (QueryCustomerLogoutOutDto outDto : outDtoList) {
                        if (EmptyUtil.isNotEmpty(outDto.getCompanyId()) && outDto.getCompanyId() != -1L) {
                            outDto.setCompanyName(idMap.get(outDto.getCompanyId()));
                        }
                        if (EmptyUtil.isNotEmpty(outDto.getStoreId()) && outDto.getStoreId() != -1L) {
                            outDto.setStore(idMap.get(outDto.getStoreId()));
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
        return outDtoList;
    }

    private String getCurrentUserId() {
        String user;
        try {
            SecurityContext context = SecurityContextHolder.getContext();
            user = MybatisMateConfig.decrypt(context.getAuthentication().getPrincipal().toString());
        } catch (Exception var3) {
            user = "anonymousUser";
        }

        return user;
    }


    public void logout(Long id) {
        String userId = getCurrentUserId();
        logout(id,userId);
    }

    public void logout(Long id,String userId){
        try {
            /**
             * 第一步：修改顾客注销表状态,逻辑删除顾客表信息
             */
            customerLogoutDao.setStatus(id, userId);
            GetCustomerOut customer = customerLogoutDao.getCustomerById(id);
            customerLogoutDao.delCustomer(id, userId);

            /**
             * 第二步：keyclocks:user_entity 信息清除,user-center:user_entity_extend 信息清除
             */
            if (customer != null && StringUtils.isNotBlank(customer.getUserEntity())) {
                userClientService.mdeleteUser(customer.getUserEntity());
            }
            /**
             * 第三步：积分清除
             */
            log.error("顾客信息：{}", customer);
            //查询剩余可用积分
            if (customer != null && customer.getId() != null) {
                QueryCustomerPointDto pointDto = pointClientService.syncCustomerInfo2(customer.getId()).getData();
                if (pointDto != null) {
                    log.error("剩余可用积分：{}", pointDto);
                    AlterCustomerPointDto alterPointDto = new AlterCustomerPointDto();
                    alterPointDto.setCustomerId(customer.getId().toString());

                    if (StringUtils.isNotBlank(pointDto.getAvailable()) && !"0".equals(pointDto.getAvailable())) {
                        PointConfig pointConfig = new PointConfig();
                        Result<String> customerTid = pointClientService.queryCustomerAndCompanyTid(null, "1", customer.getId().toString());
                        if (StringUtils.isNotBlank(customerTid.getData())) {
                            //积分清零
                            alterPointDto.setDirection(-1);
                            alterPointDto.setAvailable(BigDecimal.valueOf(Long.parseLong(pointDto.getAvailable())));
                            alterPointDto.setCompanyId(pointConfig.getCompanyId());
                            alterPointDto.setCompanyName(pointConfig.getCompanyName());
                            alterPointDto.setTypeCode(pointConfig.getTypeCode());
                            alterPointDto.setTypeName(pointConfig.getTypeName());
                            alterPointDto.setCustomerTid(customerTid.getData());
                            alterPointDto.setCustomerName(customer.getName());
                            alterPointDto.setCustomerPhone(customer.getPhone());
                            alterPointDto.setRemark(pointConfig.getOperationRemark());
                            alterPointDto.setChannelCode(pointConfig.getChannelCode());
                            alterPointDto.setChannelName(pointConfig.getChannelName());
                            pointClientService.alterCustomerPointRecycle(alterPointDto);
                        }
                    }

                    pointClientService.delCustomerPoint(alterPointDto);
                    log.error("积分账号删除成功");
                }
            }
            /**
             * 第四步：优惠卷清除
             */
            promotionClientService.clearCardCode(customer.getPhone());
        }catch (Exception e){
            log.error("注销申请异常："+JSONObject.toJSONString(e));
        }

    }


    /**
     * 申请注销
     */
    public void ApplyLogout(LogoutMallInDto dto) {
        dto.setType(10);
        dto.setSource("s0010");
        QueryCustomerLogoutOutDto one = customerLogoutDao.getOne(dto.getId());
        if (null == one) {
            customerLogoutDao.accountCancelApply(dto);
        }
    }

    /** 新增从h5过来的申请
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AddH5ApplyDTO addH5Apply(AddH5ApplyDTO dto) {
        StringBuffer stringBuffer = new StringBuffer();
        try {
            QueryCustomerLogoutOutDto customerByPhone = customerLogoutDao.getCustomerByPhone(dto.getPhone());
            for (Integer type : dto.getTypes()) {
                //会员要判断是否有未审核的申请，有的话忽略本次申请
                if(CustomerLogout.typeEnum.MEMBER.getValue().equals(type)){
                    if(customerByPhone!=null){
                        if(customerByPhone.getLogOutId()==null){
                            dto.setType(CustomerLogout.typeEnum.MEMBER.getValue());
                            dto.setCustomerId(customerByPhone.getId());
                            customerLogoutDao.addH5Apply(dto);
                            MybatisMateConfig.decryptByReflect(dto);
                            operatorLogService.saveLog(createLog(dto));
                            addMsg(stringBuffer,"会员注销申请成功");
                        }else{
                            addMsg(stringBuffer,"会员注销存在“待处理”的单据，请查询历史数据，不再生成新的单据。");
                        }
                    }else{
                        addMsg(stringBuffer,"会员不存在。");
                    }
                }else if(CustomerLogout.typeEnum.CLUE.getValue().equals(type)){
                    if(customerByPhone!=null){
                        if( customerByPhone.getClueOutId()==null){
                            dto.setType(CustomerLogout.typeEnum.CLUE.getValue());
                          //  dto.setCustomerId(customerByPhone.getId());
                            customerLogoutDao.addH5Apply(dto);
                            MybatisMateConfig.decryptByReflect(dto);
                            operatorLogService.saveLog(createLog(dto));
                            addMsg(stringBuffer,"线索注销申请成功");
                        }else{
                            addMsg(stringBuffer,"线索注销存在“待处理”的单据，请查询历史数据，不再生成新的单据。");
                        }
                    }else{
                        dto.setType(CustomerLogout.typeEnum.CLUE.getValue());
                        customerLogoutDao.addH5Apply(dto);
                        MybatisMateConfig.decryptByReflect(dto);
                        operatorLogService.saveLog(createLog(dto));
                        addMsg(stringBuffer,"线索注销申请成功");
                    }

                }
            }
            dto.setMsg(stringBuffer.toString());
            dto.setResult(true);
            return dto;
        }catch (Exception e){
            log.error("创建用户注销申请失败"+e);
            dto.setResult(false);
            return dto;
        }
    }

    private void addMsg(StringBuffer sb,String msg){
        if(StringUtils.isNotBlank(sb.toString())){
            sb.append("\n").append(msg);
        }else{
            sb.append(msg);
        }
    }

    private OperatorLog createLog(AddH5ApplyDTO dto){
        OperatorLog operatorLog = new OperatorLog();
        operatorLog.setSourceId(dto.getId()+"");
        operatorLog.setSourceTableName("customer_logout");
        operatorLog.setCustomerType("1");
        operatorLog.setOperatorName(StringUtils.isNotBlank(dto.getOperatorName())?dto.getOperatorName(): dto.getPhone());
        operatorLog.setType("2");
        operatorLog.setEventDescriptionCode("100");
        operatorLog.setEventDescription("发起注销申请");
        operatorLog.setDescription("申请手机号“"+ dto.getPhone() +"”的用户信息清除");
        return operatorLog;
    }


    public void batchAuditApply(AuditApplyDTO auditApplyDTO) {
        if(CustomerLogout.statusEnum.REJECTION.getValue().equals(auditApplyDTO.getResult())){
            //审核拒绝不需要调别的接口，直接更新申请表即可
            batchAuditNLog(auditApplyDTO,LogConstant.CUSTOMER_LOGOUT_REJECTION);
        }else if(CustomerLogout.statusEnum.APPROVED.getValue().equals(auditApplyDTO.getResult())){
            if(auditApplyDTO.getLogouts()!=null && !auditApplyDTO.getLogouts().isEmpty()){
                LinkedList<QueryCustomerLogoutOutDto> memberList = Lists.newLinkedList();
                LinkedList<QueryCustomerLogoutOutDto> clueList = Lists.newLinkedList();
                for (QueryCustomerLogoutOutDto logout : auditApplyDTO.getLogouts()) {
                    if(CustomerLogout.typeEnum.MEMBER.getValue().equals(logout.getLogoutType())){
                        memberList.add(logout);
                    }else if(CustomerLogout.typeEnum.CLUE.getValue().equals(logout.getLogoutType())){
                        clueList.add(logout);
                    }
                }
                auditApplyDTO.setOperatorId(getCurrentUserId());
                auditApplyDTO.setOperatorName(UserAuthorUtils.getFirstNameByToken());

                //发送线索的mq
                if(!clueList.isEmpty()){
                    List<Long> logoutIds = clueList.stream().map(QueryCustomerLogoutOutDto::getLogOutId).collect(Collectors.toList());
                    QueryCustomerLogoutInDto inDto = new QueryCustomerLogoutInDto();
                    inDto.setIds(logoutIds);
                    Map<Long, String> phoneMap = customerLogoutDao.listByParams(inDto).stream().collect(Collectors.toMap(CustomerLogout::getId, CustomerLogout::getPhone));
                    //获取日志里的创建人
                    QueryLogInDto queryLogInDto = new QueryLogInDto();
                    queryLogInDto.setSourceIds(logoutIds.stream().map(String::valueOf).collect(Collectors.toList()));
                    queryLogInDto.setSourceTableName("customer_logout");
                    queryLogInDto.setEventDescriptionCode("100");
                    Map<Long, String> applyName = operatorLogService.queryList(queryLogInDto)
                            .stream()
                            .collect(Collectors.toMap(d -> Long.valueOf(d.getSourceId()), QueryOperatorLogDto::getOperatorName));
                    log.info("打印注销申请参数：" + applyName.toString());
                    for (QueryCustomerLogoutOutDto queryCustomerLogoutOutDto : clueList) {
                        queryCustomerLogoutOutDto.setPhone(phoneMap.get(queryCustomerLogoutOutDto.getLogOutId()));
                        queryCustomerLogoutOutDto.setApplyName(applyName.get(queryCustomerLogoutOutDto.getLogOutId()));
                    }
                    auditApplyDTO.setLogouts(clueList);
                    batchAuditNLog(auditApplyDTO,LogConstant.CUSTOMER_LOGOUT_APPROVED);

                    sendApplyMsg.send(MessageBuilder.withPayload(auditApplyDTO).build());
                }
                //发送会员的mq
                if(!memberList.isEmpty()){
                    auditApplyDTO.setLogouts(memberList);
                    sendApplyMsgMember.send(MessageBuilder.withPayload(auditApplyDTO).build());
                }
            }
        }
    }

    @Autowired
    private MultiKeycloakConfigResolverWapper multiKeycloakConfigResolverWapper;
    @Autowired
    private KeycloakTemplate keycloakRestTemplate;
    @Autowired
    private Executor executor;

    private void batchAuditNLog(AuditApplyDTO auditApplyDTO, LogConstant logConstant){
        if(auditApplyDTO.getResult()!=null && auditApplyDTO.getLogouts()!=null && !auditApplyDTO.getLogouts().isEmpty()){
            int count= customerLogoutDao.batchAuditApply(auditApplyDTO);

            List<Long> ids = new ArrayList<>();

            for (QueryCustomerLogoutOutDto logout : auditApplyDTO.getLogouts()) {
                logout = (QueryCustomerLogoutOutDto) MybatisMateConfig.decryptByReflect(logout);
                OperatorLog operatorLogDto = new OperatorLog();
                operatorLogDto.setSourceId(logout.getLogOutId()+"");
                operatorLogDto.setSourceTableName(logConstant.getSourceTable());
                operatorLogDto.setCustomerType(logConstant.getCustomerType());
                operatorLogDto.setOperatorName(UserAuthorUtils.getFirstNameByToken());
                operatorLogDto.setType("2");
                operatorLogDto.setEventDescriptionCode(logConstant.getEventCode());
                operatorLogDto.setEventDescription(logConstant.getEventDescription());
                operatorLogDto.setDescription(logConstant.getDescription());
                operatorLogService.saveLog(operatorLogDto);
                ids.add(logout.getId());

            }
            List<CustomerInfoVto> customerInfosByIds = customerDao.getCustomerInfosByIds(ids);
            if(CollectionUtils.isNotEmpty(customerInfosByIds)){
                executor.execute(() -> {
                    for (CustomerInfoVto customerInfo : customerInfosByIds) {
                        if(StringUtils.isNotBlank(customerInfo.getKeycloakds())){
                            try {
                                String uri = "/api/m/enable";
                                MyDeploymentDelegate myDeploymentDelegate = multiKeycloakConfigResolverWapper.resolveByPath(uri);
                                String realm = myDeploymentDelegate.getRealm();
                                String baseUrl = myDeploymentDelegate.getAuthServerBaseUrl();
                                ResponseEntity<UserRepresentation> userRepresentationResponseEntity = keycloakRestTemplate.getUser(realm, customerInfo.getKeycloakds(), baseUrl,uri);
                                UserRepresentation userRepresentation = userRepresentationResponseEntity.getBody();
                                userRepresentation.setEnabled(true);//启用
                                //第一步：调用keycloak接口，修改账号信息
                                keycloakRestTemplate.updateUser(realm, baseUrl, userRepresentation,uri);
                            }catch (Exception e) {
                                log.error("用户注销审核拒绝启用账号失败，失败账号：{}，失败原因：{}",customerInfo.getKeycloakds(),e.getMessage());
                            }
                        }
                    }
                });
            }
        }
    }

    public CustomerLogout getById(Long id){
        CustomerLogout entity=  customerLogoutDao.getById(id);
        return entity;
    }


    public void batchUpdateClueStatus(AuditApplyDTO dto) {
        if(dto!=null && dto.getLogouts()!=null){
            for (QueryCustomerLogoutOutDto logout : dto.getLogouts()) {
                try {
                    CustomerLogout customerLogout = new CustomerLogout();
                    customerLogout.setType(20);
                    customerLogout.setPhone(logout.getPhone());
                    customerLogout.setStatus(1);
                    customerLogout.setModifiedBy(dto.getOperatorId());
                    List<CustomerLogout> list=  customerLogoutDao.getByParam(customerLogout);
                    customerLogout.setStatus(1);
                    if(list!=null && list.size()>0){
                        for (CustomerLogout entity : list) {
                                if(logout.getClueIds()!=null && !logout.getClueIds().isEmpty()){
                                    OperatorLog operatorLogDto = new OperatorLog();
                                    operatorLogDto.setSourceId(entity.getId()+"");
                                    operatorLogDto.setSourceTableName(LogConstant.CUSTOMER_LOGOUT_APPROVED.getSourceTable());
                                    operatorLogDto.setCustomerType(LogConstant.CUSTOMER_LOGOUT_APPROVED.getCustomerType());
                                    operatorLogDto.setOperatorName(dto.getOperatorName());
                                    operatorLogDto.setType("2");
                                    operatorLogDto.setEventDescriptionCode(LogConstant.CUSTOMER_LOGOUT_APPROVED.getEventCode());
                                    operatorLogDto.setEventDescription(LogConstant.CUSTOMER_LOGOUT_APPROVED.getEventDescription());
                                    String des="";
                                    des+="处理线索数据："+ Joiner.on(",").join(logout.getClueIds());
                                    operatorLogDto.setDescription(des);
                                    operatorLogService.saveLog(operatorLogDto);
                                }
                        }
                    }
                }catch (Exception e){
                    log.error(logout.getPhone()+ "更新线索类型注销失败,失败原因："+ JSONObject.toJSONString(e));
                }
            }
        }
    }
}
