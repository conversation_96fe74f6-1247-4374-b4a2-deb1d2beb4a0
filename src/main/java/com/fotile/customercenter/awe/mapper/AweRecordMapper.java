package com.fotile.customercenter.awe.mapper;

import com.fotile.customercenter.awe.pojo.AweRecord;
import org.apache.ibatis.annotations.Param;

public interface AweRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AweRecord record);

    int insertSelective(AweRecord record);

    AweRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AweRecord record);

    int updateByPrimaryKey(AweRecord record);

    AweRecord getAweRecordByCustomerId(@Param("customerId") Long customerId);
}
