package com.fotile.customercenter.awe.service;


import com.fotile.customercenter.awe.constant.AweConstant;
import com.fotile.customercenter.awe.mapper.AweSceneRecordMapper;
import com.fotile.customercenter.awe.pojo.AweRecord;
import com.fotile.customercenter.awe.pojo.AweSceneRecord;
import com.fotile.customercenter.awe.pojo.AweSceneRecordBo;
import com.fotile.customercenter.customer.pojo.entity.CustomerInfo;
import com.fotile.customercenter.util.CustomerUtil;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
@Slf4j
public class AweSceneRecordService {
    
    @Autowired
    private AweSceneRecordMapper aweSceneRecordMapper;
    
    @Autowired
    private CustomerUtil customerUtil;
    
    @Autowired
    private AweService aweService;

    public List<AweSceneRecord> getAweSceneRecordList() {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) {
            throw new BusinessException("请先登录！");
        }
        return this.getAweSceneRecordList(customerInfo.getId());
    }
    
    
    public List<AweSceneRecord> getAweSceneRecordList(Long customerId){
        return aweSceneRecordMapper.getAweSceneRecordList(customerId);
    }

    public AweSceneRecord getAweSceneRecordList(Long customerId,String sceneType){
        return aweSceneRecordMapper.getAweSceneRecordByCustomerIdAndSceneType(customerId,sceneType);
    }
    
    @Transactional
    public void lightScene(AweSceneRecordBo aweSceneRecordBo) {
        String sceneTypeName = AweConstant.SceneTypeEnum.getValueByCode(aweSceneRecordBo.getSceneType());
        if(StringUtils.isBlank(sceneTypeName)){
            throw new BusinessException("场景码不正确！");
        }
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) {
            throw new BusinessException("请先登录！");
        }
        AweRecord aweRecord = aweService.getAweRecordByCustomerId(customerInfo.getId());
        if(aweRecord == null){
            aweRecord = new AweRecord();
            aweRecord.setCustomerId(customerInfo.getId());
            aweRecord.setCreatedBy(customerInfo.getUserEntity());
            aweRecord.setModifiedBy(customerInfo.getUserEntity());
            aweService.saveAweRecord(aweRecord);
        }
        AweSceneRecord aweSceneRecord = this.getAweSceneRecordList(customerInfo.getId(), aweSceneRecordBo.getSceneType());
        if(aweSceneRecord != null){
            throw new BusinessException(sceneTypeName + "场景已经点亮，请去点亮其他场景吧！");
        }
        aweSceneRecord = new AweSceneRecord();
        aweSceneRecord.setSceneType(aweSceneRecordBo.getSceneType());
        aweSceneRecord.setCustomerId(customerInfo.getId());
        aweSceneRecord.setCreatedBy(customerInfo.getUserEntity());
        aweSceneRecord.setModifiedBy(customerInfo.getUserEntity());
        aweSceneRecordMapper.insert(aweSceneRecord);
    }
}
