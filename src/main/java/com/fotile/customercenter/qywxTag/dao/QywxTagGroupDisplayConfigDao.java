package com.fotile.customercenter.qywxTag.dao;

import com.fotile.customercenter.qywxTag.dto.*;
import com.fotile.customercenter.qywxTag.entity.QywxTagGroupDisplayConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信-标签展示配置表(QywxTagGroupDisplayConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-04 11:06:31
 */
public interface QywxTagGroupDisplayConfigDao {

    /**
     * 通过ID查询单条数据
     */
    QueryQywxTagGroupDisplayConfigOutDto queryById(Long id);

    /**
     * 查询指定行数据
     */
    List<QueryQywxTagGroupDisplayConfigOutDto> queryAllByLimit(QueryQywxTagGroupDisplayConfigInDto QywxTagGroupDisplayConfig);

    /**
     * 统计总数
     */
    long queryAllByCount(QueryQywxTagGroupDisplayConfigInDto inDto);

    /**
     * 新增数据
     */
    int insert(QywxTagGroupDisplayConfig QywxTagGroupDisplayConfig);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     */
    int insertBatch(@Param("entities") List<QywxTagGroupDisplayConfig> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     */
    int insertOrUpdateBatch(@Param("entities") List<QywxTagGroupDisplayConfig> entities);

    /**
     * 修改数据
     */
    int update(QywxTagGroupDisplayConfig QywxTagGroupDisplayConfig);

    /**
     * 通过主键删除数据
     */
    int deleteById(DelDisplayInDto inDto);

    Long queryByDisplayCount(QywxTagGroupDisplayInDto inDto);
    List<QywxTagGroupDisplayOutDto> queryByDisplay(QywxTagGroupDisplayInDto inDto);

    /**修改排序*/
    Integer updateSort(UpdateDisplaySortInDto inDto);

    QueryGroupOutDto queryGroupById(@Param("id") Long id);


    List<QywxTagGroupDisplayDTO> getDisplayGroupALL(@Param("list") List<Integer> displayTypeIds);

    Long findByDisplayTypeGroupId(QywxTagGroupDisplayConfig displayConfig);

    List<QywxTagGroupTagDisplayDTO> getDisplayGroupTagList(@Param("list")List<Integer> types);
}

