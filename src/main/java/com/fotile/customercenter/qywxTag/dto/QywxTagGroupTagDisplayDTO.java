package com.fotile.customercenter.qywxTag.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 企业微信-标签展示配置表(QywxTagGroupDisplayConfig)实体类
 *
 * <AUTHOR>
 * @since 2022-07-04 11:06:31
 */

@Data
public class QywxTagGroupTagDisplayDTO implements Serializable {
    private static final long serialVersionUID = 856995725038667185L;

    private Integer displayType;
    /**
     * t_qywx_tag_group_config主键id
     */
    private Long tagGroupId;
    /**
     * 标签组名称
     */
    private String groupName;
    /**
     * 标签组英文编码
     */
    private String groupCode;
    private String treeName;
    /**
     * 分类树id
     */
    private String treeCode;

    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 标签编码
     */
    private String tagCode;

    /**
     * 1:自动 2:手动
     */
    private Integer groupType;
}

