package com.fotile.customercenter.invoice.controller;


import com.fotile.customercenter.customer.pojo.entity.CustomerInfo;
import com.fotile.customercenter.invoice.pojo.dto.CustomerInvoiceInDto;
import com.fotile.customercenter.invoice.pojo.entity.CustomerInvoice;
import com.fotile.customercenter.invoice.service.CustomerInvoiceService;
import com.fotile.customercenter.util.CustomerUtil;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;


/**
 * 顾客发票管理
 *
 * <AUTHOR>
 */
@RestController
/** * 顾客发票管理 */
@RequestMapping("/api/customerInvoice")
public class CustomerInvoiceController {

    @Autowired
    private CustomerInvoiceService customerInvoiceService;

    /**
     * 根据id查询发票信息
     *
     * @param id
     * @return
     */
    /** * 根据id查询发票信息 */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "发票id", required = true),

    })
    @RequestMapping(value = "/m/queryInvoiceById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<CustomerInvoice> queryInvoiceById( @Valid @RequestParam Long id) {
        return Result.buildSuccess(customerInvoiceService.queryInvoiceById(id));
    }


    @Autowired
    CustomerUtil customerUtil;
    /**
     * 根据顾客id查询发票
     *
     * @param customerInvoiceInDto
     * @return
     */
    /** * 根据客户id查询客户发票信息 */
    @RequestMapping(value = "/m/queryInvoiceByCustomerId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result queryInvoiceById( @Valid CustomerInvoiceInDto customerInvoiceInDto) {
        CustomerInfo customerInfoByUserEntity = customerUtil.getCustomerInfoByUserEntity();
//        if (customerInfoByUserEntity == null || (customerInvoiceInDto.getCustomerId() != null && !customerInfoByUserEntity.getId().equals(customerInvoiceInDto.getCustomerId()))) {
//            throw new BusinessException("用户不一致");
//        }
        customerInvoiceInDto.setCustomerId(String.valueOf(customerInfoByUserEntity.getId()));
        return Result.buildSuccess(customerInvoiceService.queryInvoiceByCustomerId(customerInvoiceInDto));
    }

    @RequestMapping(value = "/m/deleteInvoiceByIds", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据发票ids删除发票 */
    public Result deleteInvoiceById(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return Result.buildFailure("地址id不能为空");
        }
        customerInvoiceService.deleteInvoiceByIds(Arrays.asList(ids));
        return Result.buildSuccess("删除成功");
    }

    @RequestMapping(value = "/m/editInvoiceById", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 新增编辑发票 */
    public Result editInvoiceById(@RequestBody CustomerInvoice customerInvoice) {

        CustomerInfo customerInfoByUserEntity = customerUtil.getCustomerInfoByUserEntity();
//        if (customerInfoByUserEntity == null || (customerInvoice.getCustomerId() != null && !customerInfoByUserEntity.getId().equals(customerInvoice.getCustomerId()))) {
//            throw new BusinessException("用户不一致");
//        }
        customerInvoice.setCustomerId(String.valueOf(customerInfoByUserEntity.getId()));
        return Result.buildSuccess("操作成功",customerInvoiceService.editAddressById(customerInvoice));
    }

}
