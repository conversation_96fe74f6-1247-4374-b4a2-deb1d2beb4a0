package com.fotile.customercenter.cdpManualTag.dao;


import com.fotile.customercenter.cdpManualTag.pojo.dto.GetQywxCdpTagMainInfoInDto;
import com.fotile.customercenter.cdpManualTag.pojo.dto.GetQywxCdpTagMainInfoOutDto;
import com.fotile.customercenter.cdpManualTag.pojo.entity.QywxCdpTagMainInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信-CDP标签配置主信息表 QywxCdpTagMainInfoMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-28 11:26:54
 */
public interface QywxCdpTagMainInfoMapper {

    int deleteByPrimaryKey(Long id);
	
    int insert(QywxCdpTagMainInfo record);

    int insertSelective(QywxCdpTagMainInfo record);

    QywxCdpTagMainInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QywxCdpTagMainInfo record);

    int updateByPrimaryKey(QywxCdpTagMainInfo record);

    long queryByPageCount(GetQywxCdpTagMainInfoInDto inDto);

    List<GetQywxCdpTagMainInfoOutDto> queryByPage(GetQywxCdpTagMainInfoInDto inDto);

    QywxCdpTagMainInfo queryByCompanyId(@Param("companyId") Long companyId);

}