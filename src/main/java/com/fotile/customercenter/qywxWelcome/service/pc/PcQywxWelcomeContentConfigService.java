package com.fotile.customercenter.qywxWelcome.service.pc;

import com.fotile.customercenter.qywx.service.QywxUploadMediaService;
import com.fotile.customercenter.qywxWelcome.dao.QywxWelcomeContentConfigDao;
import com.fotile.customercenter.qywxWelcome.dto.QywxWelcomeContentConfigDto;
import com.fotile.customercenter.qywxWelcome.entity.QywxWelcomeContentConfig;
import com.fotile.customercenter.qywxWelcome.mapper.QywxWelcomeContentConfigMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 欢迎语内容配置表(PcQywxWelcomeContentConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-01 10:12:31
 */
@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class PcQywxWelcomeContentConfigService {
    @Autowired
    private QywxWelcomeContentConfigDao qywxWelcomeContentConfigDao;
    @Resource
    private QywxUploadMediaService qywxUploadMediaService;

    /**
     * 通过ID查询单条数据
     */
    public QywxWelcomeContentConfig queryById(Long id) {
        return this.qywxWelcomeContentConfigDao.queryById(id);
    }

    /**
     * 分页查询
     */
    public PageInfo<QywxWelcomeContentConfig> queryByPage(QywxWelcomeContentConfig inDto) {
//        PageInfo<QywxWelcomeContentConfig> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        PageInfo<QywxWelcomeContentConfig> pageInfo = new PageInfo<>();
        List<QywxWelcomeContentConfig> contentConfigList = qywxWelcomeContentConfigDao.queryAllByLimit(inDto);
        return pageInfo;
    }

    /**
     * 新增数据
     */
    public QywxWelcomeContentConfig insert(QywxWelcomeContentConfig PcQywxWelcomeContentConfig) {
        this.qywxWelcomeContentConfigDao.insert(PcQywxWelcomeContentConfig);
        return PcQywxWelcomeContentConfig;
    }

    /**
     * 修改数据
     */
    public QywxWelcomeContentConfig update(QywxWelcomeContentConfig PcQywxWelcomeContentConfig) {
        this.qywxWelcomeContentConfigDao.update(PcQywxWelcomeContentConfig);
        return this.queryById(PcQywxWelcomeContentConfig.getId());
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.qywxWelcomeContentConfigDao.deleteById(id) > 0;
    }

    //上传图片或视频到企业微信获取media_id，只支持1，3，4 传别的返回null,上传失败会抛错需要处理
    private String getMediaIdFromUrl(String accountId , String urlPath, Integer msgType) throws Exception {
        String fileType=null;
        if(Integer.valueOf(1).equals(msgType) || Integer.valueOf(3).equals(msgType)){
            fileType="image";
        }else if(Integer.valueOf(4).equals(msgType)){
            fileType="video";
        }
        if (StringUtils.isBlank(fileType)){
            return null;
        }
       return  qywxUploadMediaService.getMediaIdFromUrl(accountId,urlPath,fileType);
    }

    @Async
    public void updateMediaId(QywxWelcomeContentConfigDto dto) throws Exception {
        try {
            if (Integer.valueOf(1).equals(dto.getMsgType())  ) {
                dto.setImageMediaId(getMediaIdFromUrl(dto.getAccountId(), dto.getImageUrl(), dto.getMsgType()));
            } else if(Integer.valueOf(3).equals(dto.getMsgType())) {
                dto.setMiniprogramPicMediaId(getMediaIdFromUrl(dto.getAccountId(), dto.getMiniprogramPicUrl(), dto.getMsgType()));
            }else if (Integer.valueOf(4).equals(dto.getMsgType())) {
                dto.setVideoMediaId(getMediaIdFromUrl(dto.getAccountId(), dto.getVideoUrl(), dto.getMsgType()));
            }
            QywxWelcomeContentConfig entity = QywxWelcomeContentConfigMapper.INSTANCE.dto2Entity(dto);
            if(StringUtils.isNotBlank(dto.getImageMediaId()) || StringUtils.isNotBlank(dto.getVideoMediaId()) || StringUtils.isNotBlank(dto.getMiniprogramPicMediaId())){
                entity.setMediaUploadDate(new Date());
                qywxWelcomeContentConfigDao.update(entity);
            }
        }catch (Exception e){
            log.error("更新微信媒体id任务失败",e);
        }

    }


    @XxlJob(value = "updateMediaIdJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("更新微信媒体id任务开始");
        List<QywxWelcomeContentConfigDto> list = qywxWelcomeContentConfigDao.getNeedUpdateMediaIdList();
        if (list != null && list.size() > 0) {
            for (QywxWelcomeContentConfigDto dto : list) {
                updateMediaId(dto);
            }
            XxlJobLogger.log("更新微信媒体id任务结束");
        }
        return ReturnT.SUCCESS;
    }


}
