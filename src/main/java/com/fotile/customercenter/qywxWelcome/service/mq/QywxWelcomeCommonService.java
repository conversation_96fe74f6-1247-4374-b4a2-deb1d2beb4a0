package com.fotile.customercenter.qywxWelcome.service.mq;

import com.fotile.customercenter.client.OrgClient;
import com.fotile.customercenter.client.pojo.FindSalesmanByOrgStationInDto;
import com.fotile.customercenter.client.pojo.FindStoreByCategoryInDto;
import com.fotile.customercenter.client.pojo.SalesmanOutDto;
import com.fotile.customercenter.client.pojo.StoreEntity;
import com.fotile.customercenter.qywx.dao.QywxGroupMemberInfoMapper;
import com.fotile.customercenter.qywx.dao.QywxUserInfoMapper;
import com.fotile.customercenter.qywx.pojo.dto.ChargeUserVO;
import com.fotile.customercenter.qywx.pojo.dto.QueryWxUserDTO;
import com.fotile.customercenter.qywx.pojo.entity.QywxUserInfo;
import com.fotile.customercenter.qywxWelcome.dao.*;
import com.fotile.customercenter.qywxWelcome.dto.GetQywxWelcomeMainInfoOutDto;
import com.fotile.customercenter.qywxWelcome.entity.*;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class QywxWelcomeCommonService {
    @Autowired
    private QywxWelcomeMainInfoDao qywxWelcomeMainInfoDao;
    @Autowired
    private QywxWelcomeCompanyMappingDao qywxWelcomeCompanyMappingDao;
    @Autowired
    private QywxWelcomeStationMappingDao qywxWelcomeStationMappingDao;
    @Autowired
    private QywxWelcomeChannelCategoryMappingDao qywxWelcomeChannelCategoryMappingDao;
    @Autowired
    private QywxWelcomeChargeMappingDao qywxWelcomeChargeMappingDao;
    @Autowired
    private QywxWelcomeStatisticsRecordDao qywxWelcomeStatisticsRecordDao;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private QywxUserInfoMapper qywxUserInfoMapper;
    @Autowired
    private QywxWelcomeStoreMappingDao qywxWelcomeStoreMappingDao;
    @Autowired
    private QywxGroupMemberInfoMapper groupMemberInfoMapper;

    @Async
    public void UpdateEffectCount(String param) {
        if (StringUtils.isNotBlank(param)){//ID不等于空，查询更新一条
            QywxWelcomeMainInfo qywxWelcomeMainInfo = qywxWelcomeMainInfoDao.queryById(Long.valueOf(param));
            if (qywxWelcomeMainInfo != null){
                List<QywxWelcomeCompanyMapping> companyMappingList = qywxWelcomeCompanyMappingDao.queryByMainId(qywxWelcomeMainInfo.getId(),1);
                List<QywxWelcomeStationMapping> stationMappingList = qywxWelcomeStationMappingDao.queryByMainId(qywxWelcomeMainInfo.getId(),1);
                List<QywxWelcomeChannelCategoryMapping> channelCategoryMappingList = qywxWelcomeChannelCategoryMappingDao.queryByMainId(qywxWelcomeMainInfo.getId(),1);
                List<QywxWelcomeStoreMapping> storeMappingList = qywxWelcomeStoreMappingDao.queryByMainId(qywxWelcomeMainInfo.getId(),1);
                //sql返回时已解密
                List<QywxWelcomeChargeMapping> chargeMappingList = qywxWelcomeChargeMappingDao.queryByMainId(qywxWelcomeMainInfo.getId(),1);

                if (CollectionUtils.isNotEmpty(companyMappingList) || CollectionUtils.isNotEmpty(stationMappingList)
                        || CollectionUtils.isNotEmpty(channelCategoryMappingList) || CollectionUtils.isNotEmpty(chargeMappingList) || CollectionUtils.isNotEmpty(storeMappingList)) {
                    if (1 == qywxWelcomeMainInfo.getStatus()) {//上线状态,有效期内
                        if (null != qywxWelcomeMainInfo.getEffectiveEndTime() && qywxWelcomeMainInfo.getEffectiveEndTime().before(new Date())){
                            qywxWelcomeStatisticsRecordDao.UpdateEffectCount(qywxWelcomeMainInfo.getId(), 0,0);
                        }else {
                            //生效的组织
                            List<Integer> companyIds = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(companyMappingList)) {
                                companyIds = companyMappingList.stream().map(c -> c.getCompanyId().intValue()).collect(Collectors.toList());
                            }

                            //查询岗位信息对照赋值
                            List<Integer> stationIds = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(stationMappingList)) {
                                stationIds = stationMappingList.stream().map(s -> s.getStation()).collect(Collectors.toList());
                            }

                            //门店渠道类型
                            List<String> storeCodes = new ArrayList<>();
                            List<String> subdivideCodeList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(channelCategoryMappingList)) {
//                                Set<String> categoryCodeSet = new HashSet<>();
                                Set<String> subdivideCodeSet = new HashSet<>();
//                                categoryCodeSet = channelCategoryMappingList.stream().map(c -> c.getChannelCategoryCode()).collect(Collectors.toSet());
                                subdivideCodeSet = channelCategoryMappingList.stream().map(c -> c.getChannelSubdivideCode()).collect(Collectors.toSet());
                                subdivideCodeList = new ArrayList<>(subdivideCodeSet);
//                                List<String> categoryCodeList = new ArrayList<>(categoryCodeSet);
//                                List<String> subdivideCodeList = new ArrayList<>(subdivideCodeSet);
//                                if (CollectionUtils.isNotEmpty(categoryCodeList) && CollectionUtils.isNotEmpty(subdivideCodeList)) {
//                                    FindStoreByCategoryInDto findStoreByCategoryInDto = new FindStoreByCategoryInDto();
//                                    findStoreByCategoryInDto.setCategoryCodeList(categoryCodeList);
//                                    findStoreByCategoryInDto.setSubdivideCodeList(subdivideCodeList);
//                                    storeEntities = orgClient.findStoreByCategoryCodes(findStoreByCategoryInDto).getData();
//                                }
//                                if (CollectionUtils.isNotEmpty(storeEntities)) {
//                                    Set<Long> storeIdSet = storeEntities.stream().map(s -> s.getOrgId()).collect(Collectors.toSet());
//                                    storeIds = new ArrayList<>(storeIdSet);
//                                }
                            }else {
                                if (CollectionUtils.isNotEmpty(storeMappingList)) {
                                    storeCodes = storeMappingList.stream().map(s ->s.getStoreCode()).collect(Collectors.toList());
                                }
                            }
                            //指定人员
                            List<Integer> chargeUserIds = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(chargeMappingList)) {
                                chargeUserIds = chargeMappingList.stream().map(c -> c.getChargeUserId().intValue()).collect(Collectors.toList());
                            }
                            Integer effectCount = 0;
                            QueryWxUserDTO wxUserDTO = new QueryWxUserDTO();
                            wxUserDTO.setAccountId(qywxWelcomeMainInfo.getAccountId());
                            if (CollectionUtils.isNotEmpty(companyIds) || CollectionUtils.isNotEmpty(storeCodes) || CollectionUtils.isNotEmpty(stationIds) || CollectionUtils.isNotEmpty(subdivideCodeList)) {
                                wxUserDTO.setCompanyIds(companyIds);
                                wxUserDTO.setStations(stationIds);
                                wxUserDTO.setStoreCodes(storeCodes);
                                wxUserDTO.setChannelSubdivideCodes(subdivideCodeList);
                                List<ChargeUserVO> qywxUserInfoList = groupMemberInfoMapper.findQywxUserIds(wxUserDTO);
                                effectCount = qywxUserInfoList.size();
                            }
                            if (CollectionUtils.isNotEmpty(chargeUserIds)){
                                wxUserDTO.setChargeUserIds(chargeUserIds);
                                List<ChargeUserVO> qywxUserIdsByChargeUserIds = groupMemberInfoMapper.findQywxUserIdsByChargeUserIds(wxUserDTO);
                                effectCount = effectCount + qywxUserIdsByChargeUserIds.size();
                            }
                            log.error("xxljob--欢迎语查询生效业务员信息：size:"+effectCount);
                            if (effectCount > 0) {
                                qywxWelcomeStatisticsRecordDao.UpdateEffectCount(qywxWelcomeMainInfo.getId(), effectCount,1);
                            }
                            //根据分公司、岗位、门店渠道，and并行逻辑查询业务员
//                            List<SalesmanOutDto> salesman = new ArrayList<>();
//                            if (CollectionUtils.isNotEmpty(companyIds) || CollectionUtils.isNotEmpty(stationIds) || CollectionUtils.isNotEmpty(storeIds)) {
//                                FindSalesmanByOrgStationInDto byOrgStationInDto = new FindSalesmanByOrgStationInDto();
//                                byOrgStationInDto.setCompanyIds(companyIds);
//                                byOrgStationInDto.setStationIds(stationIds);
//                                byOrgStationInDto.setStoreIds(storeIds);
//                                log.error("xxljob查询业务员参数1：" + byOrgStationInDto);
//                                salesman = orgClient.findSalesmanByOrgStation2(byOrgStationInDto).getData();
//                            }
//                            Set<String> phones1 = new HashSet<>();
//                            if (CollectionUtils.isNotEmpty(salesman)) {
//                                phones1 = salesman.stream().map(s -> s.getSalesmanPhone()).collect(Collectors.toSet());
//                            }
//                            //指定的人员,sql返回时已解密
//                            Set<String> phones2 = chargeMappingList.stream().map(c -> MybatisMateConfig.encrypt(c.getChargePhone())).collect(Collectors.toSet());
//                            //合并业务员手机号
//                            phones1.addAll(phones2);
//                            List<String> phones = new ArrayList<>(phones1);
//
//                            //手机号不为空，去企微用户表查询注册人数并记为生效人数
//                            if (CollectionUtils.isNotEmpty(phones)) {
//                                List<String> accountIds = new ArrayList<>();
//                                accountIds.add(qywxWelcomeMainInfo.getAccountId());
//                                log.error("xxljob查询业务员参数2："+phones);
//                                List<QywxUserInfo> qywxUserInfoList = qywxUserInfoMapper.selectCountByPhones(phones, accountIds);
//                                log.error("xxljob查询业务员参数3：size:"+qywxUserInfoList.size()+"dto:"+qywxUserInfoList);
//                                if (CollectionUtils.isNotEmpty(qywxUserInfoList)) {
//                                    qywxWelcomeStatisticsRecordDao.UpdateEffectCount(qywxWelcomeMainInfo.getId(), qywxUserInfoList.size(),1);
//                                }
//                            }
                        }
                    } else {//下线状态,清零
                        qywxWelcomeStatisticsRecordDao.UpdateEffectCount(qywxWelcomeMainInfo.getId(), 0,0);
                    }
                } else {//如果没有分配则记为0
                    qywxWelcomeStatisticsRecordDao.UpdateEffectCount(qywxWelcomeMainInfo.getId(), 0,0);
                }
            }
        }else {
            List<QywxWelcomeStatisticsRecord> qywxWelcomeStatisticsRecords = qywxWelcomeStatisticsRecordDao.selectAll();
            if (CollectionUtils.isNotEmpty(qywxWelcomeStatisticsRecords)) {
                List<Long> ids = qywxWelcomeStatisticsRecords.stream().map(s -> s.getWelcomeMainId()).collect(Collectors.toList());
                List<GetQywxWelcomeMainInfoOutDto> mainInfoOutDtoList = qywxWelcomeMainInfoDao.selectAllByIds(ids);
                for (GetQywxWelcomeMainInfoOutDto mainInfoOutDto : mainInfoOutDtoList) {
                    if (mainInfoOutDto != null) {
                        List<QywxWelcomeCompanyMapping> companyMappingList = qywxWelcomeCompanyMappingDao.queryByMainId(mainInfoOutDto.getId(),1);
                        List<QywxWelcomeStationMapping> stationMappingList = qywxWelcomeStationMappingDao.queryByMainId(mainInfoOutDto.getId(),1);
                        List<QywxWelcomeChannelCategoryMapping> channelCategoryMappingList = qywxWelcomeChannelCategoryMappingDao.queryByMainId(mainInfoOutDto.getId(),1);
                        List<QywxWelcomeStoreMapping> storeMappingList = qywxWelcomeStoreMappingDao.queryByMainId(mainInfoOutDto.getId(),1);
                        List<QywxWelcomeChargeMapping> chargeMappingList = qywxWelcomeChargeMappingDao.queryByMainId(mainInfoOutDto.getId(),1);

                        if (CollectionUtils.isNotEmpty(companyMappingList) || CollectionUtils.isNotEmpty(stationMappingList)
                                || CollectionUtils.isNotEmpty(channelCategoryMappingList) || CollectionUtils.isNotEmpty(chargeMappingList) || CollectionUtils.isNotEmpty(storeMappingList)) {
                            if (1 == mainInfoOutDto.getStatus()) {//上线状态,有效期内
                                if (null != mainInfoOutDto.getEffectiveEndTime() && mainInfoOutDto.getEffectiveEndTime().before(new Date())) {
                                    qywxWelcomeStatisticsRecordDao.UpdateEffectCount(mainInfoOutDto.getId(), 0,0);
                                } else {
                                    //生效的组织
                                    List<Long> companyIds = new ArrayList<>();
                                    if (CollectionUtils.isNotEmpty(companyMappingList)) {
                                        companyIds = companyMappingList.stream().map(c -> c.getCompanyId()).collect(Collectors.toList());
                                    }

                                    //查询岗位信息对照赋值
                                    List<Integer> stationIds = new ArrayList<>();
                                    if (CollectionUtils.isNotEmpty(stationMappingList)) {
                                        stationIds = stationMappingList.stream().map(s -> s.getStation()).collect(Collectors.toList());
                                    }

                                    //门店渠道类型
                                    List<Long> storeIds = new ArrayList<>();
                                    List<StoreEntity> storeEntities = new ArrayList<>();
                                    if (CollectionUtils.isNotEmpty(channelCategoryMappingList)) {
                                        Set<String> categoryCodeSet = new HashSet<>();
                                        Set<String> subdivideCodeSet = new HashSet<>();
                                        categoryCodeSet = channelCategoryMappingList.stream().map(c -> c.getChannelCategoryCode()).collect(Collectors.toSet());
                                        subdivideCodeSet = channelCategoryMappingList.stream().map(c -> c.getChannelSubdivideCode()).collect(Collectors.toSet());
                                        List<String> categoryCodeList = new ArrayList<>(categoryCodeSet);
                                        List<String> subdivideCodeList = new ArrayList<>(subdivideCodeSet);
                                        if (CollectionUtils.isNotEmpty(categoryCodeList) && CollectionUtils.isNotEmpty(subdivideCodeList)) {
                                            FindStoreByCategoryInDto findStoreByCategoryInDto = new FindStoreByCategoryInDto();
                                            findStoreByCategoryInDto.setCategoryCodeList(categoryCodeList);
                                            findStoreByCategoryInDto.setSubdivideCodeList(subdivideCodeList);
                                            storeEntities = orgClient.findStoreByCategoryCodes(findStoreByCategoryInDto).getData();
                                        }
                                        if (CollectionUtils.isNotEmpty(storeEntities)) {
                                            Set<Long> storeIdSet = storeEntities.stream().map(s -> s.getOrgId()).collect(Collectors.toSet());
                                            storeIds = new ArrayList<>(storeIdSet);
                                        }
                                    }else {
                                        if (CollectionUtils.isNotEmpty(storeMappingList)) {
                                            storeIds = storeMappingList.stream().map(s ->s.getStoreOrgId()).collect(Collectors.toList());
                                        }
                                    }

                                    //根据分公司、岗位、门店渠道，and并行逻辑查询业务员
                                    List<SalesmanOutDto> salesman = new ArrayList<>();
                                    if (CollectionUtils.isNotEmpty(companyIds) || CollectionUtils.isNotEmpty(stationIds) || CollectionUtils.isNotEmpty(storeIds)) {
                                        FindSalesmanByOrgStationInDto byOrgStationInDto = new FindSalesmanByOrgStationInDto();
                                        byOrgStationInDto.setCompanyIds(companyIds);
                                        byOrgStationInDto.setStationIds(stationIds);
                                        byOrgStationInDto.setStoreIds(storeIds);
                                        log.error("xxljob查询业务员参数1：" + byOrgStationInDto);
                                        salesman = orgClient.findSalesmanByOrgStation2(byOrgStationInDto).getData();
                                    }
                                    Set<String> phones1 = new HashSet<>();
                                    if (CollectionUtils.isNotEmpty(salesman)) {
                                        phones1 = salesman.stream().map(s -> s.getSalesmanPhone()).collect(Collectors.toSet());
                                    }
                                    //指定的人员,sql返回时已解密
                                    Set<String> phones2 = chargeMappingList.stream().map(c -> MybatisMateConfig.encrypt(c.getChargePhone())).collect(Collectors.toSet());
                                    //合并业务员手机号
                                    phones1.addAll(phones2);
                                    List<String> phones = new ArrayList<>(phones1);

                                    //手机号不为空，去企微用户表查询注册人数并记为生效人数
                                    if (CollectionUtils.isNotEmpty(phones)) {
                                        List<String> accountIds = new ArrayList<>();
                                        accountIds.add(mainInfoOutDto.getAccountId());
                                        log.error("xxljob批量查询业务员参数2："+phones);
                                        List<QywxUserInfo> qywxUserInfoList = qywxUserInfoMapper.selectCountByPhones(phones, accountIds);
                                        log.error("xxljob查询业务员参数3：size:"+qywxUserInfoList.size()+"dto:"+qywxUserInfoList);
                                        if (CollectionUtils.isNotEmpty(qywxUserInfoList)) {
                                            qywxWelcomeStatisticsRecordDao.UpdateEffectCount(mainInfoOutDto.getId(), qywxUserInfoList.size(),1);
                                        }
                                    }
                                }
                            } else {//下线状态,清零
                                qywxWelcomeStatisticsRecordDao.UpdateEffectCount(mainInfoOutDto.getId(), 0,0);
                            }
                        }
                    }
                }
            }
        }

    }




}
