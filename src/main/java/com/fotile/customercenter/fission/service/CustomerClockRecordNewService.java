package com.fotile.customercenter.fission.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fotile.customercenter.client.MarketingClient;
import com.fotile.customercenter.client.pojo.*;
import com.fotile.customercenter.constant.CustomerConstant;
import com.fotile.customercenter.customer.dao.CustomerInfoDao;
import com.fotile.customercenter.customer.pojo.entity.CustomerInfo;
import com.fotile.customercenter.enums.TaskTypeEnum;
import com.fotile.customercenter.fission.dao.CustomerActivityInfoMapper;
import com.fotile.customercenter.fission.dao.CustomerClockRecordMapper;
import com.fotile.customercenter.fission.dao.CustomerInviteJoinInfoMapper;
import com.fotile.customercenter.fission.dao.CustomerLotteryCodeCountMapper;
import com.fotile.customercenter.fission.pojo.CustomerClockRecord;
import com.fotile.customercenter.fission.pojo.dto.CustomerLotteryBroadCastDto;
import com.fotile.customercenter.fission.pojo.dto.CustomerTaskRecordParamDto;
import com.fotile.customercenter.fission.pojo.entity.*;
import com.fotile.customercenter.util.CustomerUtil;
import com.fotile.customercenter.util.RedisUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerClockRecordNewService {

    private final CustomerActivityInfoMapper customerActivityInfoMapper;

    private final SystemPropertiesService systemPropertiesService;

    private final CustomerUtil customerUtil;

    private final CustomerClockRecordMapper clockRecordMapper;

    private final MarketingClient marketingClient;

    private final CustomerLotteryCodeCountService customerLotteryCodeCountService;

    private final CustomerLotteryCodeCountMapper customerLotteryCodeCountMapper;

    private final CustomerLotteryCommonService customerLotteryCommonService;

    private final CustomerPrizeRecordService customerPrizeRecordService;

    private final CustomerInfoDao customerInfoDao;

    private final CustomerActivityReserveService customerActivityReserveService;

    private final CustomerShareAppService customerShareAppService;

    private final CustomerInviteJoinInfoMapper customerInviteJoinInfoMapper;

    private final CustomerTaskRecordService customerTaskRecordService;

    private final CustomerLotteryCodeService customerLotteryCodeService;

    private final RedisUtils redisUtils;

    /**
     * 小程序新版本打卡签到抽奖活动(抽签码形式)
     *
     * @param activityId
     * @param id
     * @param customerInfo
     * @return
     */
    @GlobalTransactional
    public Map<String, Object> completeClock(Long activityId, Long id, CustomerInfo customerInfo, boolean lotteryFlag) {
        Map<String, Object> map = new HashMap<>();

        Date now = new Date();
        String nowStr = systemPropertiesService.getNowDate();
        CustomerClockRecord clockRecord = null;
        if (id == null) {
            //如果id为空代表是正常打卡操作,查询当前用户对当前活动的历史所有打卡记录,如果已经有过记录直接返回,否则计算活动周期以每日为粒度进行批量插入
            List<CustomerClockRecord> clockRecordList = this.getClockRecordListV2(activityId, customerInfo);
            Optional<CustomerClockRecord> optional = clockRecordList.stream().filter(t -> StringUtils.equals(t.getClockDate(), nowStr)).findFirst();
            if (optional.isPresent()) {
                clockRecord = optional.get();
            }
        } else {
            clockRecord = clockRecordMapper.selectByPrimaryKey(id);
        }
        if (clockRecord == null) throw new BusinessException("记录不存在,无法打卡/补卡");
        if (clockRecord.getClockFlag() == 1) throw new BusinessException("打卡已完成，无需重复签到！");
        //查询活动信息
        Result<ActivityForNewMediaDto> activityByIdForNewMedia2 = marketingClient.getActivityByIdForNewMedia2(activityId);
        if (!activityByIdForNewMedia2.getSuccess()) throw new BusinessException("活动信息不存在");
        ActivityForNewMediaDto activityInfo = activityByIdForNewMedia2.getData();
        if (activityInfo == null) throw new BusinessException("活动信息不存在");
        if (activityInfo.getIsCompleted() == 1 || now.after(activityInfo.getEndTime()))
            throw new BusinessException("活动已结束");

        //查询用户的活动记录
        CustomerActivityInfo customerActivityInfo = customerActivityInfoMapper.selectInfoByUK(customerInfo.getId(), activityId);
        if (clockRecord.getClockDate().compareTo(nowStr) < 0) {
            Result<ActivityClockTask> byActivityIdAndTaskType = marketingClient.getByActivityIdAndTaskType(activityId, TaskTypeEnum.PUNCH.name());
            if (!byActivityIdAndTaskType.getSuccess()) throw new BusinessException("未查询到活动任务");
            ActivityClockTask punchClockTask = byActivityIdAndTaskType.getData();
            if (punchClockTask == null) throw new BusinessException("该活动不能补卡");
            if (customerActivityInfo.getEnableOmitClockCount() == 0) throw new BusinessException("补卡机会已用完");

            //补卡
            clockRecord.setOmitClockFlag(1);
            customerActivityInfo.setUsedOmitClockCount(customerActivityInfo.getUsedOmitClockCount() + 1);
            customerActivityInfo.setEnableOmitClockCount(customerActivityInfo.getEnableOmitClockCount() - 1);
            if (customerActivityInfo.getUsedOmitClockCount() > customerActivityInfo.getOmitClockCount())
                throw new BusinessException("补卡次数已达上限");

        }
        clockRecord.setClockFlag(1);
        clockRecord.setModifiedBy(customerInfo.getId() + "");
        clockRecord.setModifiedDate(now);

        //打卡前先清除当前用户的打卡记录缓存
        String redisKey = CustomerConstant.CLOCK_ACTIVITY_RECORD_LIST.concat(customerInfo.getId() + "").concat(activityId + "");
        redisUtils.delByKey(redisKey);

        clockRecordMapper.updateByPrimaryKeySelective(clockRecord);
        //更新打卡次数
        customerActivityInfo.setClockCount(customerActivityInfo.getClockCount() + 1);
        customerActivityInfoMapper.updateByPrimaryKeySelective(customerActivityInfo);

        if (lotteryFlag) {
            //打卡完成生成抽奖码
            //打卡和补卡都需要查询每次操作能获得几个抽奖码
            Result<ActivityClockTask> byActivityIdAndTaskType = marketingClient.getByActivityIdAndTaskType(activityId, id == null ? TaskTypeEnum.CLOCK.name() : TaskTypeEnum.PUNCH.name());
            if (!byActivityIdAndTaskType.getSuccess()) {
                throw new BusinessException("未查询到活动任务");
            }
            ActivityClockTask activityClockTask = byActivityIdAndTaskType.getData();
            log.info("activityClockTask:{}", activityClockTask);
            if (activityClockTask != null) {
                Integer drawCodeQuantity = activityClockTask.getDrawCodeQuantity();

                if (drawCodeQuantity != null && drawCodeQuantity > 0) {
                    map.put("lotteryCodeCount", drawCodeQuantity);
                    //记录每次任务应得的抽奖码的数量及每组抽奖码
                    customerLotteryCommonService.saveLotteryCodeAndCount(drawCodeQuantity, customerInfo.getId(), activityId, id == null ? TaskTypeEnum.CLOCK.name() : TaskTypeEnum.PUNCH.name(), activityClockTask.getId().toString(), clockRecord.getId());
                }
            }

            ActivityClockPrize activityClockPrize = customerLotteryCommonService.judgeClockDaysGetPrize(activityInfo, customerInfo, clockRecord);
            if (activityClockPrize != null) {
                //将奖品查看状态置为1
                customerPrizeRecordService.update(null, new LambdaUpdateWrapper<CustomerPrizeRecord>()
                        .eq(CustomerPrizeRecord::getCustomerId, customerInfo.getId())
                        .eq(CustomerPrizeRecord::getActivityId, activityId)
                        .eq(CustomerPrizeRecord::getPrizeId, activityClockPrize.getId())
                        .set(CustomerPrizeRecord::getViewFlag, true)
                );
            }
            map.put("prize", activityClockPrize);
        }

        return map;
    }

    @Transactional
    public List<CustomerClockRecord> getClockRecordList(Long activityId, CustomerInfo customerInfo) {
        //先查询用户信息
        if (customerInfo == null) {
            customerInfo = customerUtil.getCustomerInfoByUserEntity();
            if (customerInfo == null) {
                throw new BusinessException("未查询到用户信息");
            }
        }

        //查询活动信息
        SimpleActivityDto activityInfo = marketingClient.getSimpleActivityInfoById(activityId, TaskTypeEnum.CLOCK.name()).getData();
        if (activityInfo == null) throw new BusinessException("未查询到活动信息");


        Map<Integer, ActivityClockDays> clockDaysMap = activityInfo.getActivityClockDays().stream().filter(s -> s.getPrizeId() != null).collect(Collectors.toMap(ActivityClockDays::getPunchDays, s -> s));

        ActivityClockTask activityClockTask = activityInfo.getActivityClockTask();

        Date now = new Date();
        String nowDate = systemPropertiesService.getNowDate();
        //先查询是否有记录
        List<CustomerClockRecord> clockRecordList = clockRecordMapper.getClockRecordList(customerInfo.getId(), activityId, activityInfo.getType());
        if (CollectionUtils.isNotEmpty(clockRecordList)) {
            for (CustomerClockRecord t : clockRecordList) {
                t.setNowDate(nowDate);
                t.setLotteryCodeCount(activityClockTask == null ? 0 : activityClockTask.getDrawCodeQuantity());
            }
            return clockRecordList;
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(activityInfo.getStartTime(), activityInfo.getEndTime(), DateField.DAY_OF_YEAR);
        log.error("dateTimes:{}", dateTimes);

        int i = 1;
        for (DateTime dateTime : dateTimes) {
            CustomerClockRecord clockRecord = new CustomerClockRecord();
            clockRecord.setCustomerId(customerInfo.getId());
            clockRecord.setActivityId(activityInfo.getId());
            clockRecord.setActivityType(activityInfo.getType());
            clockRecord.setOmitClockFlag(0);
            clockRecord.setClockFlag(0);
            clockRecord.setClockDate(dateTime.toDateStr());
            clockRecord.setCreatedBy(customerInfo.getId() + "");
            clockRecord.setCreatedDate(now);
            clockRecord.setNowDate(nowDate);
            clockRecord.setLotteryCodeCount(activityClockTask == null ? 0 : activityClockTask.getDrawCodeQuantity());

            ActivityClockDays rule = clockDaysMap.get(i);
            clockRecord.setPrizeFlag(rule != null && rule.getPrizeId() != null);
            clockRecord.setDay(i++);
            clockRecordList.add(clockRecord);
        }
        //如果正在活动日期内，插入记录
        if (activityInfo.getStartTime().before(now) && activityInfo.getEndTime().after(now)) {
            clockRecordMapper.batchInsert(clockRecordList);
        }
        return clockRecordList;
    }

    public List<CustomerLotteryBroadCastDto> getLotteryCodeUserList(Long activityId) {
        String redisKey = CustomerConstant.CLOCK_ACTIVITY_USER_LIST.concat(activityId + "");
        String lotteryCodeUserListByRedis = redisUtils.getByKey(redisKey);
        List<CustomerLotteryBroadCastDto> lotteryCodeUserList = JSON.parseObject(lotteryCodeUserListByRedis, new TypeReference<>() {
        });
        if (CollectionUtils.isEmpty(lotteryCodeUserList)) {
            lotteryCodeUserList = customerLotteryCodeCountService.getLotteryCodeUserList(activityId);
            redisUtils.setKey(redisKey, JSON.toJSONString(lotteryCodeUserList), 300);
        }
        return lotteryCodeUserList;
    }

    public Integer getCanOmitClockCount(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        //查询当前活动可补卡几次
        ActivityClockTask activityClockTask = marketingClient.getByActivityIdAndTaskType(activityId, TaskTypeEnum.PUNCH.name()).getData();
        if (activityClockTask == null) return 0;
        Integer cardReplacementCount = activityClockTask.getCardReplacementCount();

        //查询当前活动已补卡几次
        Integer omitCount = clockRecordMapper.getOmitCountByCustomerIdAndActivityId(customerInfo.getId(), activityId);

        return Math.max(cardReplacementCount - omitCount, 0);
    }

    public Map<String, Object> getInviteList(Long activityId) {
        Map<String, Object> map = new HashMap<>();
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        String redisListKey = CustomerConstant.CLOCK_INVITE_LIST.concat(activityId + "").concat(customerInfo.getId() + "");
        String redisListCountKey = CustomerConstant.CLOCK_INVITE_LIST_COUNT.concat(activityId + "").concat(customerInfo.getId() + "");
        String redisList = redisUtils.getByKey(redisListKey);
        String redisListCount = redisUtils.getByKey(redisListCountKey);

        List<CustomerLotteryBroadCastDto> inviteList = JSON.parseObject(redisList, new TypeReference<>() {
        });
        Integer maxInviteCount = Integer.parseInt(StringUtils.isEmpty(redisListCount) ? "0" : redisListCount);
        if (CollectionUtils.isNotEmpty(inviteList)) {
            map.put("inviteList", inviteList);
            map.put("inviteSettingCount", maxInviteCount);
            return map;
        }


        maxInviteCount = marketingClient.getByActivityId(activityId).getData();
        inviteList = customerInviteJoinInfoMapper.getInviteList(activityId, customerInfo.getId(), maxInviteCount);
        map.put("inviteList", inviteList);
        map.put("inviteSettingCount", maxInviteCount);

        redisUtils.setKey(redisListKey, JSON.toJSONString(inviteList));
        redisUtils.setKey(redisListCountKey, maxInviteCount + "");
        return map;
    }

    public PrizeInfoDto getBigPrizeInfo(Long activityId) {
        PrizeInfoDto prizeInfoDto = new PrizeInfoDto();

        Long allLotteryCodeCount = customerLotteryCodeCountService.getAllLotteryCodeCountByActivityId(activityId);
        prizeInfoDto.setLotteryAllCodeCount(allLotteryCodeCount);

        CustomerPrizeRecord customerPrizeRecord = customerPrizeRecordService.getBigPrizeByActivityId(activityId);
        if (customerPrizeRecord != null) {
            prizeInfoDto.setPrizeName(customerPrizeRecord.getPrizeName());

            Optional.of(customerPrizeRecord)
                    .map(CustomerPrizeRecord::getCustomerId)
                    .map(customerInfoDao::findById)
                    .ifPresent(customerInfo -> prizeInfoDto.setAwardeeName(customerInfo.getName() == null ? customerInfo.getNickname() : customerInfo.getName()));
        }
        return prizeInfoDto;
    }

    public void reserveActivity(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();

        customerActivityReserveService.saveCustomerActivityReserve(activityId, customerInfo.getId());
    }

    public boolean ifReserve(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        return customerActivityReserveService.ifReserve(activityId, customerInfo.getId());
    }

    public Long reserveActivityCount(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        return customerActivityReserveService.reserveActivityCount(activityId, customerInfo.getId());
    }

    public Long joinCount(Long activityId) {
        return customerActivityInfoMapper.selectCount(new LambdaQueryWrapper<CustomerActivityInfo>()
                .eq(CustomerActivityInfo::getActivityId, activityId)
        );
    }

    public Boolean isWinner(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        //查询该活动的大奖
        List<ActivityClockPrize> prizeList = marketingClient.getBigPrizeAndLuckyPrizeByActivityId(activityId).getData();
        ActivityClockPrize bigPrize = prizeList.stream().filter(s -> s.getPrizeFlag() == 1).findAny().orElse(null);
        if (bigPrize == null) return false;

        Integer count = customerPrizeRecordService.getCountByCustomerIdAndActivityIdAndPrizeFlag(customerInfo.getId(), activityId);
        return count > 0;
    }

    public CustomerShareApp getPrizeChargeUserId(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) throw new BusinessException("未查询到用户信息");

        CustomerInviteJoinInfo customerInviteJoinInfo = customerInviteJoinInfoMapper.selectOne(new LambdaQueryWrapper<CustomerInviteJoinInfo>()
                .eq(CustomerInviteJoinInfo::getJoinCustomerId, customerInfo.getId())
                .eq(CustomerInviteJoinInfo::getActivityId, activityId)
                .eq(CustomerInviteJoinInfo::getIsDeleted, 0)
                .eq(CustomerInviteJoinInfo::getIsFirst, 1)
                .isNotNull(CustomerInviteJoinInfo::getChargeUserId)
        );
        if (customerInviteJoinInfo == null || customerInviteJoinInfo.getChargeUserId() == null) return null;

        return customerShareAppService.getByActivityIdAndChargeUserId(activityId, customerInviteJoinInfo.getChargeUserId());
    }

    public List<CustomerPrizeRecord> getAllClockPrize(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) throw new BusinessException("未查询到用户信息");

        List<CustomerPrizeRecord> customerPrizeRecords = customerPrizeRecordService.getAllClockPrizeByCustomerIdAndActivityId(activityId, customerInfo.getId());
        for (CustomerPrizeRecord customerPrizeRecord : customerPrizeRecords) {
            //[已改] 23694	【方太官方小程序】打卡活动积分类型修改
            if (!"10".equals(customerPrizeRecord.getPrizeType())) {
                continue;
            }
            ActivityClockPrize activityClockPrize = marketingClient.getByPrizeIdAndActivityId(customerPrizeRecord.getPrizeId(), activityId).getData();
            customerPrizeRecord.setLeadPageLink(activityClockPrize == null ? null : activityClockPrize.getLeadPageLink());
        }
        return customerPrizeRecords;
    }

    public Map<String, Integer> getAllLotteryCodeCount(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        if (customerInfo == null) throw new BusinessException("未查询到用户信息");

        Map<String, Integer> map = new HashMap<>();
        map.put("INVITE", 0);
        map.put("PUNCH", 0);

        List<CustomerLotteryCodeCount> customerLotteryCodeCounts = customerLotteryCodeCountMapper.selectList(new LambdaQueryWrapper<CustomerLotteryCodeCount>()
                .eq(CustomerLotteryCodeCount::getActivityId, activityId)
                .eq(CustomerLotteryCodeCount::getCustomerId, customerInfo.getId())
                .in(CustomerLotteryCodeCount::getTaskType, Lists.newArrayList(TaskTypeEnum.INVITE.name(), TaskTypeEnum.PUNCH.name()))
                .eq(CustomerLotteryCodeCount::getViewFlag, 0)
        );
        if (CollectionUtils.isEmpty(customerLotteryCodeCounts)) return map;

        customerLotteryCodeCountMapper.update(null, new LambdaUpdateWrapper<CustomerLotteryCodeCount>()
                .eq(CustomerLotteryCodeCount::getActivityId, activityId)
                .eq(CustomerLotteryCodeCount::getCustomerId, customerInfo.getId())
                .in(CustomerLotteryCodeCount::getTaskType, Lists.newArrayList(TaskTypeEnum.INVITE.name(), TaskTypeEnum.PUNCH.name()))
                .eq(CustomerLotteryCodeCount::getViewFlag, 0)
                .set(CustomerLotteryCodeCount::getViewFlag, 1)
        );
        return customerLotteryCodeCounts.stream()
                .collect(Collectors.groupingBy(CustomerLotteryCodeCount::getTaskType,
                        Collectors.summingInt(CustomerLotteryCodeCount::getLotteryCodeCount)));
    }

    public MarketingCustomerDto getCustomerByActivity(Long activityId, Long customerId, Integer activityType) {
        MarketingCustomerDto marketingCustomerDto = new MarketingCustomerDto();
        String nowDate = systemPropertiesService.getNowDate();

        CustomerTaskRecordParamDto customerTaskRecordParamDto = new CustomerTaskRecordParamDto();
        customerTaskRecordParamDto.setCustomerId(customerId);
        customerTaskRecordParamDto.setActivityId(activityId);
        customerTaskRecordParamDto.setActivityType(activityType);

        List<CustomerTaskRecord> taskRecordList = customerTaskRecordService.getTaskRecordList(customerTaskRecordParamDto);

        /*Long lotteryCount = customerLotteryCodeService.getLotteryCountByActivityIdAndCustomerId(activityId, customerId);
        PrizeInfoDto bigPrizeInfo = getBigPrizeInfo(activityId);*/

        marketingCustomerDto.setNowDate(nowDate);
        marketingCustomerDto.setTaskRecordList(taskRecordList);
        /*marketingCustomerDto.setLotteryCount(lotteryCount);
        marketingCustomerDto.setBigPrizeInfo(bigPrizeInfo);*/

        return marketingCustomerDto;
    }

    public String getMyLotteryCodeCount(Long activityId) {
        CustomerInfo customerInfo = customerUtil.getCustomerInfoByUserEntity();
        String key = CustomerConstant.CLOCK_LOTTERY_CODE_COUNT.concat(customerInfo.getId() + "");
        String count = redisUtils.getByKey(key);
        if (StringUtils.isEmpty(count)) {
            count = customerLotteryCodeService.getLotteryCountByActivityIdAndCustomerId(activityId, customerInfo.getId()) + "";
            redisUtils.setKey(key, count, 600);
        }
        return count;
    }


    @Transactional
    public List<CustomerClockRecord> getClockRecordListV2(Long activityId, CustomerInfo customerInfo) {
        //先查询用户信息
        if (customerInfo == null) {
            customerInfo = customerUtil.getCustomerInfoByUserEntity();
            if (customerInfo == null) {
                throw new BusinessException("未查询到用户信息");
            }
        }

        //查询活动信息
        SimpleActivityDto activityInfo = marketingClient.getSimpleActivityInfoById(activityId, TaskTypeEnum.CLOCK.name()).getData();
        if (activityInfo == null) throw new BusinessException("未查询到活动信息");


        Map<Integer, ActivityClockDays> clockDaysMap = activityInfo.getActivityClockDays().stream().filter(s -> s.getPrizeId() != null).collect(Collectors.toMap(ActivityClockDays::getPunchDays, s -> s));

        ActivityClockTask activityClockTask = activityInfo.getActivityClockTask();

        Date now = new Date();
        String nowDate = systemPropertiesService.getNowDate();
        //先查询是否有记录
        String redisKey = CustomerConstant.CLOCK_ACTIVITY_RECORD_LIST.concat(customerInfo.getId() + "").concat(activityId + "");
        String key = redisUtils.getByKey(redisKey);
        log.info("redisKey:{},key:{}", redisKey, key);
        List<CustomerClockRecord> clockRecordList = JSON.parseObject(key, new TypeReference<>() {
        });


        if (CollectionUtils.isNotEmpty(clockRecordList)) return clockRecordList;


        clockRecordList = clockRecordMapper.getClockRecordList(customerInfo.getId(), activityId, activityInfo.getType());
        if (CollectionUtils.isNotEmpty(clockRecordList)) {
            for (CustomerClockRecord t : clockRecordList) {
                t.setNowDate(nowDate);
                t.setLotteryCodeCount(activityClockTask == null ? 0 : activityClockTask.getDrawCodeQuantity());
            }
        } else {
            List<DateTime> dateTimes = DateUtil.rangeToList(activityInfo.getStartTime(), activityInfo.getEndTime(), DateField.DAY_OF_YEAR);
            log.error("dateTimes:{}", dateTimes);

            int i = 1;
            for (DateTime dateTime : dateTimes) {
                CustomerClockRecord clockRecord = new CustomerClockRecord();
                clockRecord.setCustomerId(customerInfo.getId());
                clockRecord.setActivityId(activityInfo.getId());
                clockRecord.setActivityType(activityInfo.getType());
                clockRecord.setOmitClockFlag(0);
                clockRecord.setClockFlag(0);
                clockRecord.setClockDate(dateTime.toDateStr());
                clockRecord.setCreatedBy(customerInfo.getId() + "");
                clockRecord.setCreatedDate(now);
                clockRecord.setNowDate(nowDate);
                clockRecord.setLotteryCodeCount(activityClockTask == null ? 0 : activityClockTask.getDrawCodeQuantity());

                ActivityClockDays rule = clockDaysMap.get(i);
                clockRecord.setPrizeFlag(rule != null && rule.getPrizeId() != null);
                clockRecord.setDay(i++);
                clockRecordList.add(clockRecord);
            }
            //如果正在活动日期内，插入记录
            if (activityInfo.getStartTime().before(now) && activityInfo.getEndTime().after(now)) {
                clockRecordMapper.batchInsert(clockRecordList);
                redisUtils.setKey(redisKey, JSON.toJSONString(clockRecordList), 600);
            }

        }
        redisUtils.setKey(redisKey, JSON.toJSONString(clockRecordList), 600);
        return clockRecordList;
    }

    public Boolean deleteRedisByKey(String key) {
        return redisUtils.delByKey(key);
    }
}
