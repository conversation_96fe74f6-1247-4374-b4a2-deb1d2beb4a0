package com.fotile.customercenter.fission.controller;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.fotile.customercenter.fission.entity.CustomerSubscribeMsgRecord;
import com.fotile.customercenter.fission.service.CustomerSubscribeMsgRecordService;
import com.fotile.customercenter.util.RedisUtils;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 账号活动订阅记录controller
 */
@RequestMapping("/api/m/customer/subscribe/msg")
@RestController
@Slf4j
public class CustomerSubscribeMsgRecordController extends BaseController {

    @Autowired
    private CustomerSubscribeMsgRecordService customerSubscribeMsgRecordService;

    @Autowired
    private RedisUtils redisUtils;


    /**
     * 订阅活动通知
     */
    @PostMapping(value = "/subscribeMsg", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result subscribeMsg(@Valid @RequestBody CustomerSubscribeMsgRecord customerSubscribeMsgRecord){
        String md5 = MD5.create().digestHex(JSON.toJSONString(customerSubscribeMsgRecord));
        if(!redisUtils.setIfAbsentKey(md5,md5,30)){
            throw new BusinessException("操作频繁，请稍后尝试！");
        }
        int i = customerSubscribeMsgRecordService.subscribeMsg(customerSubscribeMsgRecord);
        redisUtils.delByKey(md5);
        return success(i);
    }














}
