package com.fotile.customercenter.usercard.dao;


import com.fotile.customercenter.usercard.pojo.dto.GetQywxUserCardPageInDto;
import com.fotile.customercenter.usercard.pojo.dto.GetUserCardPageOutDto;
import com.fotile.customercenter.usercard.pojo.entity.QywxUserCardEffectChargeMapping;
import com.fotile.customercenter.usercard.pojo.entity.QywxUserCardMasterMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 导购名片生效业务员关联表 QywxUserCardEffectChargeMappingMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2024-05-28 14:18:30
 */
public interface QywxUserCardEffectChargeMappingMapper {

    int insert(QywxUserCardEffectChargeMapping record);

    int insertSelective(QywxUserCardEffectChargeMapping record);

    QywxUserCardEffectChargeMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QywxUserCardEffectChargeMapping record);

    int updateByPrimaryKey(QywxUserCardEffectChargeMapping record);

    List<QywxUserCardEffectChargeMapping> queryCountByMainId(@Param("userCardId") Long id, @Param("accountId") String accountId);

    List<QywxUserCardEffectChargeMapping> queryCountByMainIds(@Param("userCardIds") List<Long> ids, @Param("accountIds") List<String> accountIds);


    void deleteByMainId(@Param("userCardId") Long id, @Param("accountId") String accountId);

    void deleteByMainIdType(@Param("userCardId") Long id, @Param("accountId") String accountId, @Param("type") Integer type);

    void insertBatch(@Param("list") List<QywxUserCardEffectChargeMapping> effectChargeList);

    void updateBatch(@Param("list") List<QywxUserCardEffectChargeMapping> effectChargeList);

    List<QywxUserCardEffectChargeMapping> queryByUserId(@Param("userCardId") Long id, @Param("userIds") List<String> userIds, @Param("accountId") String accountId);



    QywxUserCardEffectChargeMapping selectChargeInfoForSend(@Param("userCardId") Long userCardId, @Param("userId") String userId, @Param("accountId") String accountId);


    void deleteBatch(@Param("chargeUserIds") List<Long> chargeUserIds, @Param("accountId") String accountId, @Param("userCardId") Long userCardId);

    void deleteByIds(@Param("ids") List<Long> ids);
}