package com.fotile.customercenter.usercard.dao;


import com.fotile.customercenter.usercard.pojo.dto.*;
import com.fotile.customercenter.usercard.pojo.entity.QywxUserCardMainInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 导购二维码主表 QywxUserCardMainInfoMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2024-05-28 14:18:10
 */
public interface QywxUserCardMainInfoMapper {
    int insert(QywxUserCardMainInfo record);

    int insertSelective(QywxUserCardMainInfo record);

    QywxUserCardMainInfo selectByPrimaryKey(Long id);

    QywxUserCardMainInfo getUserCardInfoById(@Param("id") Long id);


    QywxUserCardMainInfo getUserCardInfoForRefresh(@Param("id") Long id , @Param("currentDayDate") Date currentDayDate);


    int updateByPrimaryKeySelective(QywxUserCardMainInfo record);

    int updateByPrimaryKey(QywxUserCardMainInfo record);

    int update(QywxUserCardMainInfo record);

    int updateStatus(QywxUserCardMainInfo record);
    int updateShowFlag(QywxUserCardMainInfo record);

    Long queryAllCount(GetUserCardPageInDto inDto);

    List<GetUserCardPageOutDto> queryAllByLimit(GetUserCardPageInDto inDto);


    Long queryQrAllCount(GetQywxUserCardPageInDto inDto);

    List<GetQywxUserCardPageOutDto> queryQrByPage(GetQywxUserCardPageInDto inDto);

    GetQywxUserCardPageOutDto queryQrById(@Param("id") Long id);

    int updateAutoRefreshTime(@Param("id") Long id, @Param("autoRefreshTime") Date autoRefreshTime);

    void deleteByUserCardId(@Param("id") Long userCardId);

    String getVillageName(@Param("id") Long userCardId);


    QywxUserCardMainInfo getUserCardInfoForRefreshDrainage(@Param("id") Long id , @Param("currentDayDate") Date currentDayDate);

}