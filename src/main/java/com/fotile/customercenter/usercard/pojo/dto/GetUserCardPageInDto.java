package com.fotile.customercenter.usercard.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GetUserCardPageInDto implements Serializable {
    private Integer page;
    private Integer size;
    private Long offset;

    /**1：中台创建，2：导购端创建*/
    private Integer createType;

    private Long id;
    /**
     * 名片名称
     */
    private String title;
    /**
     * 0下线，1上线
     */
    private Integer status;
    /**
     * 有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveStartTime;
    /**
     * 有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveEndTime;
    /**
     * 创建时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdStartDate;
    /**
     * 创建时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdEndDate;
    /**
     * 分公司
     */
    private List<Long> companyIds;
    /**
     * 展示标志 默认0展示，1不展示
     */
    private Integer showFlag;
    /**
     * 引流渠道分类，数据字典：diffIndustry_company_class，加一个值集100：家装设计师
     */
    private Integer drainageChannelCode;

    //权限
    private List<Long> authCompanyIds;
}
