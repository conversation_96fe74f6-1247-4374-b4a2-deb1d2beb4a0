package com.fotile.customercenter.drainage.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

/**
    * 权益发放记录表统计
    */
@Data
public class InterestsTransmitRecordStatistics {
    /**
     * 引流id
     */
    private Long drainageId;
    /**
     * 权益发放记录数
     */
    private Integer interestsCount;
}