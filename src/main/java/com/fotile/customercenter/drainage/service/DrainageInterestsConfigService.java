package com.fotile.customercenter.drainage.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.customercenter.drainage.dao.DrainageInterestsConfigDao;
import com.fotile.customercenter.drainage.pojo.dto.DrainageInterestsConfigDto;
import com.fotile.customercenter.drainage.pojo.entity.DrainageInterestsConfig;
import com.fotile.customercenter.drainage.service.DrainageInterestsConfigService;
import com.fotile.customercenter.util.UserAuthorUtils;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 引流权益配置表(DrainageInterestsConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-31 16:11:03
 */
@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class DrainageInterestsConfigService extends ServiceImpl<DrainageInterestsConfigDao, DrainageInterestsConfig> {
    @Autowired
    private DrainageInterestsConfigDao drainageInterestsConfigDao;

    @Autowired
    private UserAuthorUtils userAuthorUtils;

    //根据关联表id查询权益
    public List<DrainageInterestsConfigDto> queryBySourceId(Long sourceId) {
        return this.drainageInterestsConfigDao.queryBySourceId(sourceId);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public DrainageInterestsConfig queryById(Long id) {
        return this.drainageInterestsConfigDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    public List<DrainageInterestsConfig> queryAllByLimit(int offset, int limit) {
        return this.drainageInterestsConfigDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param drainageInterestsConfig 实例对象
     * @return 实例对象
     */
    public DrainageInterestsConfig insert(DrainageInterestsConfig drainageInterestsConfig) {
        this.drainageInterestsConfigDao.insert(drainageInterestsConfig);
        return drainageInterestsConfig;
    }

    /**
     * 修改数据
     *
     * @param drainageInterestsConfig 实例对象
     * @return 实例对象
     */
    public DrainageInterestsConfig update(DrainageInterestsConfig drainageInterestsConfig) {
        this.drainageInterestsConfigDao.update(drainageInterestsConfig);
        return this.queryById(drainageInterestsConfig.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Integer id) {
        return this.drainageInterestsConfigDao.deleteById(id) > 0;
    }

    public int updateByIdToStatus(DrainageInterestsConfig drainageInterestsConfig) {
        String userName = userAuthorUtils.getUserName();
        String userId = getCurrentUserId();
        drainageInterestsConfig.setReviseName(userName);
        drainageInterestsConfig.setModifiedBy(userId);
        return drainageInterestsConfigDao.updateByIdToStatus(drainageInterestsConfig);
    }

    public int updateByIdToIsDeleted(DrainageInterestsConfig drainageInterestsConfig) {
        String userName = userAuthorUtils.getUserName();
        String userId = getCurrentUserId();
        drainageInterestsConfig.setReviseName(userName);
        drainageInterestsConfig.setModifiedBy(userId);
        return drainageInterestsConfigDao.updateByIdToIsDeleted(drainageInterestsConfig);
    }

    public String getCurrentUserId() {
        UserAuthor userAuthor = userAuthorUtils.getUserAuthor();
        return userAuthor.getUserId();
    }

    public Long getOneInterestsConfig(String pageType) {
        return drainageInterestsConfigDao.getOneInterestsConfig(pageType);
    }
}