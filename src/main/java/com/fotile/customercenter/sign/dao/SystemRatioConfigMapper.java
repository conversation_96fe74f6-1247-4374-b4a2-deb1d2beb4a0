package com.fotile.customercenter.sign.dao;


import com.fotile.customercenter.sign.pojo.entity.SystemRatioConfig;

/**
 *  SystemRatioConfigMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2021-04-20 14:15:50
 */
public interface SystemRatioConfigMapper {
		
    int deleteByPrimaryKey(Long id);
		
    int insert(SystemRatioConfig record);

    int insertSelective(SystemRatioConfig record);

    SystemRatioConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemRatioConfig record);

    int updateByPrimaryKey(SystemRatioConfig record);

    SystemRatioConfig selectByUK(String variable);

    void updateByVariable(SystemRatioConfig updateBean);
}