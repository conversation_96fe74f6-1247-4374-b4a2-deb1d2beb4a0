package com.fotile.customercenter.groupmsg.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@TableName("t_qywx_group_msg_main_info")
public class QywxGroupMsgMainInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 
     */
	private Long id;
	/**
	 * 父群发内容id
	 */
	private Long fullGroupMsgId;
	
	/**
     * 标题
     */
	private String title;
	
	/**
     * 配置类型
     */
	private Integer configType;
	
	/**
     * 文本内容
     */
	private String content;
	
	/**
     * 是否带门店信息
     */
	private Integer storeConfigFlag;
	
	/**
     * 账户Id，企业微信id
     */
	private String accountId;
	
	/**
     * 发送好友类型 1：全部好友，2：筛选好友
     */
	private Integer sendMemberType;
	
	/**
     * 发送类型
     */
	private Integer sendType;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 发送时间
     */
	private Date sendTime;
	
	/**
     * 状态
     */
	private Integer status;
	
	/**
     * 通知状态
     */
	private Integer noticeStatus;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 通知时间
     */
	private Date noticeTime;
	
	/**
     * 配置端
     */
	private Integer sourceType;
	
	/**
     * 备注
     */
	private String remark;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Integer isDeleted;
	
	/**
     * 
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date createdDate;
	
	/**
     * 
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date modifiedDate;
	
	/**
     * 最新修改人名称
     */
	private String reviseName;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 最新修改时间
     */
	private Date reviseTime;
	/**
	 * 任务通知截止时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
	private Date noticeEndDate;
	/**
	 * 是否可以发送标志 0否1是
	 */
	private Integer canSendFlag;
	/**
	 * 停止群发：0否1是
	 */
	private Integer stopMsg;
	/**
	 * 人群包id
	 */
	private Long cdpGroupId;
	/**
	 * 选择人群包名称
	 */
	private String cdpGroupName;
	/**
	 * 二次群发标识0：否，1：是
	 */
	private Integer secondMsgFlag;
	/**
	 * 二次群发时勾选的发送对象 1:未打开链接的顾客 2：已打开链接未参与的顾客
	 */
	private Integer sendObjectType;
	/**
	 * 配置导购数
	 */
	private Integer chargeCount;
	/**
	 * 配置好友数
	 */
	private Long customerCount;
	/**
	 * 岗位类型  1：技师 2：非技师
	 */
	private Integer stationType;

}