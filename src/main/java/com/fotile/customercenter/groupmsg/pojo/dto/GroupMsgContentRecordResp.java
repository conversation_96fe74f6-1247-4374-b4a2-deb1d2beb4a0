package com.fotile.customercenter.groupmsg.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GroupMsgContentRecordResp implements Serializable {


    /**
     * 群发内容id
     */
    @ApiModelProperty("群发内容id")
    private Long id;
    /**
     * 群发标题
     */
    @ApiModelProperty("群发标题")
    private String title;

    /**
     * 上架状态 状态 0:下架 1：上架
     */
    @ApiModelProperty("上架状态")
    private String status;
    /**
     * 通知状态
     */
    @ApiModelProperty("通知状态")
    private String noticeStatus;
    /**
     * 分公司权限
     */
    private String companyAuthDesc;
    /**
     * 发送好友类型 1：全部好友，2：筛选好友
     */
    private String sendMemberDesc;
    /**
     * 生效人数
     */
    private Long chargeUserCount;
    /**
     * 已通知员工数
     */
    private Long noticChargeUserCount;
    /**
     * 群发创建时间
     */
    @ApiModelProperty("群发创建时间")
    private Date createdStartDate;
    /**
     * 账号
     */
    @ApiModelProperty("账号")
    @FieldEncrypt
    private String userAccountId;
    /**
     * 业务员id
     */
    @ApiModelProperty("业务员id")
    private Long chargeUserId;
    /**
     * 业务员姓名
     */
    @ApiModelProperty("业务员姓名")
    @FieldEncrypt
    private String chargeUserName;
    /**
     * 员工总已发送数量
     */
    @ApiModelProperty("总已发送数量")
    private Long msgSendCount=0L;
    /**
     * 员工总未发送好友数量
     */
    @ApiModelProperty("总未发送好友数量")
    private Long msgUpSendCount=0L;
    /**
     * 该发送好友数量
     */
    @ApiModelProperty("该发送好友数量")
    private Long userCount=0L;
    /**
     * 外部成员集合
     */
    @ApiModelProperty("外部成员集合")
    private String qywxExternalUserids;
    /**
     * 发送失败成员
     */
    @ApiModelProperty("发送失败成员")
    private String failExternalUserids;
    /**
     * 已发送数量
     */
    @ApiModelProperty("已发送数量")
    private Long sendCount=0L;
    /**
     * 已接收数量
     */
    @ApiModelProperty("已接收数量")
    private Long receptionCount=0L;
    /**
     * 未发送好友数量
     */
    @ApiModelProperty("未发送好友数量")
    private Long upSendCount=0L;
    /**
     * 不是好友导致发送失败
     */
    @ApiModelProperty("不是好友导致发送失败")
    private Long notUserSendCount=0L;
    /**
     * 发送上限数量
     */
    @ApiModelProperty("发送上限数量")
    private Long sendUpLimitCount=0L;
    /**
     * 发送好友时间
     */
    @ApiModelProperty("发送好友时间")
    private Date sendUserTime;
    /**
     * 发送状态
     */
    @ApiModelProperty("发送状态")
    private Integer sendStatus;
    /**
     * 岗位
     */
    @ApiModelProperty("岗位")
    private String station;
    /**
     * 大区
     */
    @ApiModelProperty("大区")
    private String areaName;
    /**
     * 分公司
     */
    @ApiModelProperty("分公司")
    private String companyName;
    /**
     * 门店编码
     */
    @ApiModelProperty("门店")
    private String storeCode;
    /**
     * 门店
     */
    @ApiModelProperty("门店")
    private String storeName;
    /**
     * 最新一次拉取时间
     */
    @ApiModelProperty("最新一次拉取时间")
    private Date lastPullTime;
    /**
     * 是否展示
     */
    private Boolean isShow;
    /**
     * 线索数量
     */
    private Long cluesCount=0L;
    /**
     * 成员id
     */
    private String qywxUserId;
    /**
     * 父群发内容id
     */
    private Long fullGroupMsgId;
    /**
     * 顾客查看人数
     */
    private Long customerOpenNum = 0L;
    /**
     * 顾客是否查看 0：无查看 展示/ 1：存在查看 展示顾客查看人数
     */
    private Integer customerOpenFlag = 0;

    private Integer configType;

    private Long groupMsgId;
}
