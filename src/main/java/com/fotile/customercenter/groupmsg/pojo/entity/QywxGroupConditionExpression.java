package com.fotile.customercenter.groupmsg.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
@TableName("t_qywx_group_condition_expression")
public class QywxGroupConditionExpression implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 
     */
	private Long id;
	
	/**
     * 群发筛选条件配置表id
     */
	private Long conditionId;
	
	/**
     * 1：好友；2：排除好友
     */
	private Integer type;
	
	/**
     * 1:or;2:in
     */
	private Integer expressionType;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Integer isDeleted;
	
	/**
     * 
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date createdDate;
	
	/**
     * 
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date modifiedDate;

	

}