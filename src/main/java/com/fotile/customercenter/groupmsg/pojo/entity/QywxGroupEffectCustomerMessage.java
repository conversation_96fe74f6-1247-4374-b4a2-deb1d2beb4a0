package com.fotile.customercenter.groupmsg.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fotile.customercenter.client.pojo.TagQueryDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QywxGroupEffectCustomerMessage implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 
     */
	private String currentUserId;
	
	/**
     * 群发主记录表id
     */
	private Long groupMsgId;
	
	private TagQueryDTO tagQueryDTO;

	private String accountId;

}