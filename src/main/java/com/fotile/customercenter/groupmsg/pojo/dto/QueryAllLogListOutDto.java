package com.fotile.customercenter.groupmsg.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class QueryAllLogListOutDto implements Serializable {

    /**
     *
     */
    private Integer id;

    /**
     * 关联库表
     */
    private String refTable;

    /**
     * 关联类型
     */
    private Integer refType;

    /**
     * 关联id
     */
    private Long refId;

    /**
     * 操作类型
     */
    private Integer type;

    /**
     * 操作类型名称
     */
    private String typeName;

    /**
     * 老数据
     */
    private String oldData;

    /**
     * 新数据
     */
    private String newData;

    /**
     * 描述内容
     */
    private String remark;

    /**
     * 附件地址
     */
    private String attachmentUrl;

    /**
     * 操作人名称
     */
    @FieldEncrypt
    private String operatorName;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdDate;

    /**
     * 逻辑删除状态，1删除，0未删除
     */
    private Integer isDeleted;

}
