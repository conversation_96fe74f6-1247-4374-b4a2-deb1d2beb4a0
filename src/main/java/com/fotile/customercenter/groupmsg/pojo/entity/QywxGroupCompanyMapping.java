package com.fotile.customercenter.groupmsg.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
@TableName("t_qywx_group_company_mapping")
public class QywxGroupCompanyMapping implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/**
     * 
     */
	private Long id;
	
	/**
     * 群发主记录表id
     */
	private Long groupMsgId;
	
	/**
     * 分公司id
     */
	private Long companyId;
	
	/**
     * 分公司名称
     */
	private String companyName;
	
	/**
     * 数据类型--1：权限，2：作用范围
     */
	private Integer type;

	/**
     * 公司权限配置类型  -1：全部 0：部分
     */
	private Integer companyMappingType;
	
	/**
     * 是否删除：0：否；其它：是
     */
	private Integer isDeleted;
	
	/**
     * 
     */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date createdDate;
	
	/**
     * 
     */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/**
     * 
     */
	private Date modifiedDate;

}