package com.fotile.customercenter.groupmsg.pojo.entity.mapper;

import com.fotile.customercenter.groupmsg.pojo.entity.QywxGroupEffectCustomerMapping;
import com.fotile.customercenter.groupmsg.pojo.entity.TQywxGroupEffectCustomerMappingBak;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TQywxGroupEffectCustomerMappingBakMapper  {
    TQywxGroupEffectCustomerMappingBakMapper INSTANCE = Mappers.getMapper(TQywxGroupEffectCustomerMappingBakMapper.class);

    List<TQywxGroupEffectCustomerMappingBak> mappingToBak(List<QywxGroupEffectCustomerMapping> qywxGroupEffectCustomerMappingList);

}