package com.fotile.customercenter.groupmsg.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PreviewInfo implements Serializable {

    /**
     * 类型
     */
    private Integer msgType;

    /**
     *
     *  图片地址
     */
    private String imageUrl;
    /**
     * 文本
     */
    private String content;
    /**
     * type=4时，视频链接
     */
    private String videoPictureUrl;
    /**
     * 顾客展示平台，1-5S小程序,存储appid
     */
    private String showPlatform;
    /**
     * 落地页显示图片地址
     */
    private String displayPictureUrl;


}
