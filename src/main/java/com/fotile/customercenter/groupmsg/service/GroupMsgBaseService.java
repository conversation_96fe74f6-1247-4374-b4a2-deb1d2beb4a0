package com.fotile.customercenter.groupmsg.service;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fotile.customercenter.client.*;
import com.fotile.customercenter.client.pojo.*;
import com.fotile.customercenter.customer.pojo.dto.user.UserEntityExtend;
import com.fotile.customercenter.drainage.dao.DrainageActivityInfoDao;
import com.fotile.customercenter.drainage.dao.DrainageQrInfoDao;
import com.fotile.customercenter.drainage.pojo.dto.DrainageActivityDetailDto;
import com.fotile.customercenter.drainage.pojo.entity.DrainageQrInfo;
import com.fotile.customercenter.feedback.mq.ScheduleMsgChannel;
import com.fotile.customercenter.groupmsg.Enum.ConfigTypeEnum;
import com.fotile.customercenter.groupmsg.Enum.ContentMsgTypeEnum;
import com.fotile.customercenter.groupmsg.Enum.GroupMsgOperationTypeEnum;
import com.fotile.customercenter.groupmsg.Enum.SendMemberTypeEnum;
import com.fotile.customercenter.groupmsg.dao.*;
import com.fotile.customercenter.groupmsg.pojo.dto.*;
import com.fotile.customercenter.groupmsg.pojo.entity.*;
import com.fotile.customercenter.groupmsg.pojo.entity.mapper.TQywxGroupEffectCustomerCdpPushService;
import com.fotile.customercenter.groupmsg.pojo.entity.mapper.TQywxGroupEffectCustomerMappingBakMapper;
import com.fotile.customercenter.qywx.dao.QywxCutomerUserRelationMapper;
import com.fotile.customercenter.qywx.dao.QywxUserInfoMapper;
import com.fotile.customercenter.qywx.pojo.entity.QywxCutomerUserRelation;
import com.fotile.customercenter.qywx.pojo.entity.QywxUserInfo;
import com.fotile.customercenter.qywx.service.QywxUploadMediaService;
import com.fotile.customercenter.qywxWelcome.dao.QywxWelcomeRelationEventLogDao;
import com.fotile.customercenter.qywxWelcome.entity.EventLogDto;
import com.fotile.customercenter.qywxWelcome.entity.QywxWelcomeRelationEventLog;
import com.fotile.customercenter.util.UserAuthorUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.util.DateUtil;
import com.fotile.framework.web.BusinessException;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fotile.framework.web.Result;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;
import static org.codehaus.groovy.runtime.DefaultGroovyMethods.tr;

@Slf4j
@Service
public class GroupMsgBaseService {

    @Autowired
    private UserAuthorUtils userAuthorUtils;
    @Autowired
    private QywxGroupMsgMainInfoMapper groupMsgMainInfoMapper;
    @Autowired
    private QywxGroupCompanyMappingMapper qywxGroupCompanyMappingMapper;
    @Autowired
    private QywxGroupContentConfigMapper qywxGroupContentConfigMapper;
    @Autowired
    private QywxGroupEffectCustomerMappingMapper qywxGroupEffectCustomerMappingMapper;
    @Autowired
    private QywxGroupSendConditionMapper qywxGroupSendConditionMapper;
    @Autowired
    private QywxGroupConditionExpressionMapper qywxGroupConditionExpressionMapper;
    @Autowired
    private QywxGroupConditionValueMapper qywxGroupConditionValueMapper;
    @Autowired
    private QywxGroupStationMappingMapper qywxGroupStationMappingMapper;
    @Autowired
    private QywxGroupStoreMappingMapper qywxGroupStoreMappingMapper;
    @Autowired
    private QywxGroupChannelCategoryMappingMapper qywxGroupChannelCategoryMappingMapper;
    @Autowired
    private QywxGroupChargeMappingMapper qywxGroupChargeMappingMapper;
    @Autowired
    private QywxGroupEffectChargeMappingMapper qywxGroupEffectChargeMappingMapper;
    @Autowired
    private QywxGroupEffectCustomerAsyncLogMapper qywxGroupEffectCustomerAsyncLogMapper;
    @Resource
    private QywxUploadMediaService qywxUploadMediaService;
    @Autowired
    private QywxCutomerUserRelationMapper qywxCutomerUserRelationMapper;
    @Autowired
    private QywxGroupUserSendLogMapper qywxGroupUserSendLogMapper;
    @Autowired
    private QywxGroupSendResultLogMapper qywxGroupSendResultLogMapper;
    @Autowired
    private QywxUserInfoMapper qywxUserInfoMapper;
    @Autowired
    private CmsClient cmsClient;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private SearchClient searchClient;
    @Autowired
    private SendMsgCommonService sendMsgCommonService;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private SystemClient systemClient;
    @Resource(name = ScheduleMsgChannel.QYWX_CUSTOMER_GROUP_MSG_OUTPUT)
    private MessageChannel messageChannel;
    @Resource(name = ScheduleMsgChannel.QYWX_EFFECT_CUSTOMER_GROUP_MSG_OUTPUT)
    private MessageChannel customerMessageChannel;

    @Resource(name = ScheduleMsgChannel.QYWX_STOP_GROUP_MSG_OUTPUT)
    private MessageChannel stopMessageChannel;
    @Autowired
    private OperationLogService logService;
    @Autowired
    private QywxWelcomeRelationEventLogDao eventLogDao;
    @Autowired
    private MarketingClient marketingClient;
    @Autowired
    private DrainageQrInfoDao drainageQrInfoDao;
    @Autowired
    private DrainageActivityInfoDao drainageActivityInfoDao;
    @Autowired
    private ToolsClient toolsClient;
    @Autowired
    private TQywxGroupEffectCustomerMappingBakDao tQywxGroupEffectCustomerMappingBakDao;
    @Autowired
    private TQywxGroupSendTaskInfoDao tQywxGroupSendTaskInfoDao;
    @Autowired
    private TQywxGroupEffectCustomerCdpPushService cdpPushService;
    @Autowired
    private CdpClient cdpClient;
    @Autowired
    private UserClient userClient;
    @Autowired
    private TQywxGroupCompleteDataLogDao tQywxGroupCompleteDataLogDao;
    @Resource(name = ScheduleMsgChannel.CDP_COMPLETE_DATA_OUTPUT)
    private MessageChannel cdpCompleteDataChannel;
    @Autowired
    private TCustomerCommonMaterialContentMappingDao materialContentMappingDao;
    @Autowired
    private TCustomerCommonMaterialMappingDao materialMappingDao;
    @Autowired
    private TQywxGroupMsgVillageMappingService villageMappingDao;

    private static final ImmutableSet<String> BLANK_PAGE = ImmutableSet.of("pages/memberRights/getRights/main",
            "pages/mainOther/VipMemberCard/main",
            "pages/mainOther/VipMemberCardOther/main",
            "pages/pageTemplate/landingPage/main",
            "pages/activityDetail/main",
            "pages/happyBBS/serviceDetail/main",
            "pages/activity/appt",
            "pages/activity/templateWebview");


    private static QywxGroupConditionExpression getQywxGroupConditionExpression(QywxGroupSendCondition sendCondition, int type,
                                                                                String currentUserId, Integer expressionType) {
        QywxGroupConditionExpression qywxGroupConditionExpression = new QywxGroupConditionExpression();
        qywxGroupConditionExpression.setConditionId(sendCondition.getId());
        qywxGroupConditionExpression.setType(type);
        qywxGroupConditionExpression.setExpressionType(expressionType);
        qywxGroupConditionExpression.setCreatedBy(currentUserId);
        qywxGroupConditionExpression.setCreatedDate(new Date());
        qywxGroupConditionExpression.setModifiedBy(currentUserId);
        qywxGroupConditionExpression.setModifiedDate(new Date());
        qywxGroupConditionExpression.setIsDeleted(0);
        return qywxGroupConditionExpression;
    }

    private static QywxGroupSendCondition getQywxGroupSendCondition(GroupMsgBaseDTO groupMsgBaseDTO,
                                                                    QywxGroupMsgMainInfo qywxGroupMsgMainInfo, String currentUserId,CdpByTag cdpByTag) {
        QywxGroupSendCondition sendCondition = new QywxGroupSendCondition();
        sendCondition.setGroupMsgId(qywxGroupMsgMainInfo.getId());
        if (groupMsgBaseDTO.getGroupSendConditionDTO() != null) {
            sendCondition.setStartTime(groupMsgBaseDTO.getGroupSendConditionDTO().getStartTime());
            sendCondition.setEndTime(groupMsgBaseDTO.getGroupSendConditionDTO().getEndTime());
            cdpByTag.setStartTime(DateUtil.dateFormat(groupMsgBaseDTO.getGroupSendConditionDTO().getStartTime(),"yyyy-MM-dd"));
            cdpByTag.setEndTime(DateUtil.dateFormat(groupMsgBaseDTO.getGroupSendConditionDTO().getEndTime(),"yyyy-MM-dd"));
        }
        sendCondition.setCreatedBy(currentUserId);
        sendCondition.setCreatedDate(new Date());
        sendCondition.setModifiedBy(currentUserId);
        sendCondition.setModifiedDate(new Date());
        sendCondition.setIsDeleted(0);
        return sendCondition;
    }

    @Transactional
    public Result insertGroupMsg(GroupMsgBaseDTO groupMsgBaseDTO) {
        String currentUserId = userAuthorUtils.getCurrentUserId();
        String firstName = userAuthorUtils.getUserAuthor().getFirstName();
        if (Objects.equals(1,groupMsgBaseDTO.getSecondMsgFlag())){
            //二次群发创建
            return insertSecondGroupMsg(groupMsgBaseDTO,currentUserId,firstName);
        }
        //region参数校验
        String validateParam = validateParam(groupMsgBaseDTO);
        if (StringUtils.isNotBlank(validateParam)) {
            return Result.buildFailure(validateParam);
        }
        //endregion
        if (groupMsgBaseDTO.getId() == null) {
            //region 新增
            //region 增加群发主表记录
            QywxGroupMsgMainInfo qywxGroupMsgMainInfo = new QywxGroupMsgMainInfo();
            BeanUtils.copyProperties(groupMsgBaseDTO, qywxGroupMsgMainInfo);
            qywxGroupMsgMainInfo.setCreatedDate(new Date());
            qywxGroupMsgMainInfo.setCreatedBy(currentUserId);
            qywxGroupMsgMainInfo.setModifiedBy(currentUserId);
            qywxGroupMsgMainInfo.setModifiedDate(new Date());
            qywxGroupMsgMainInfo.setReviseTime(new Date());
            qywxGroupMsgMainInfo.setReviseName(MybatisMateConfig.encrypt(firstName));
            qywxGroupMsgMainInfo.setNoticeStatus(0);
            qywxGroupMsgMainInfo.setIsDeleted(0);
            qywxGroupMsgMainInfo.setSourceType(1);
            qywxGroupMsgMainInfo.setCanSendFlag(2);
            qywxGroupMsgMainInfo.setCdpGroupId(groupMsgBaseDTO.getCdpGroupId());
            qywxGroupMsgMainInfo.setCdpGroupName(groupMsgBaseDTO.getCdpGroupName());
            qywxGroupMsgMainInfo.setSecondMsgFlag(groupMsgBaseDTO.getSecondMsgFlag());
            qywxGroupMsgMainInfo.setNoticeEndDate(groupMsgBaseDTO.getNoticeEndDate());
            qywxGroupMsgMainInfo.setFullGroupMsgId(groupMsgBaseDTO.getFullGroupMsgId());
            Integer chargeCount = 0;
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupEffectChargeMappingDTOList())){
                chargeCount = groupMsgBaseDTO.getGroupEffectChargeMappingDTOList().size();
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList())){
                chargeCount += groupMsgBaseDTO.getGroupChargeMappingDTOList().size();
            }
            qywxGroupMsgMainInfo.setChargeCount(chargeCount);
            qywxGroupMsgMainInfo.setCustomerCount(0L);
            groupMsgMainInfoMapper.insert(qywxGroupMsgMainInfo);
            groupMsgBaseDTO.setId(qywxGroupMsgMainInfo.getId());
            //endregion
            //region 群发分公司权限
            insertQywxGroupCompanyMapping(groupMsgBaseDTO.getGroupCompanyMappingDTOList(), currentUserId,
                    qywxGroupMsgMainInfo.getId(), 1, groupMsgBaseDTO.getCompanyMappingType());
            //endregion
            //region 群发内容
            insertContentMapping(groupMsgBaseDTO, qywxGroupMsgMainInfo.getId(), currentUserId);
            //endregion
            //region 生效员工配置
            insertEffectCharge(groupMsgBaseDTO, currentUserId, qywxGroupMsgMainInfo.getId());
            //endregion
            //region 群发好友配置
            insertEffectCustomer(groupMsgBaseDTO, currentUserId, qywxGroupMsgMainInfo);
            //endregion
            //endregion
            String remark = "";
            if (groupMsgBaseDTO.getCopyId()!=null){
                remark = "从群发id:"+groupMsgBaseDTO.getCopyId()+"复制";
            }

            logService.insertOperationLog2(qywxGroupMsgMainInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.ADD.getType(), "新增群发",
                    null, null, remark,
                    null, currentUserId, firstName);
        } else {
            //region 编辑
            QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper
                    .selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class)
                            .eq(QywxGroupMsgMainInfo::getId, groupMsgBaseDTO.getId())
                            .eq(QywxGroupMsgMainInfo::getIsDeleted, 0));
            if (qywxGroupMsgMainInfo == null) {
                log.error("未查询到对应的群发内容");
                return Result.buildFailure("未查询到当前群发内容");
            }
            QywxGroupMsgMainInfo oldInfo = new QywxGroupMsgMainInfo();
            BeanUtils.copyProperties(qywxGroupMsgMainInfo, oldInfo);
            //记录分公司权限日志
            companyLog(groupMsgBaseDTO,oldInfo.getId(),currentUserId,firstName);
            //region 主表
            BeanUtils.copyProperties(groupMsgBaseDTO, qywxGroupMsgMainInfo);
            qywxGroupMsgMainInfo.setModifiedBy(currentUserId);
            qywxGroupMsgMainInfo.setModifiedDate(new Date());
            qywxGroupMsgMainInfo.setReviseName(MybatisMateConfig.encrypt(firstName));
            qywxGroupMsgMainInfo.setReviseTime(new Date());
            qywxGroupMsgMainInfo.setSourceType(1);
            qywxGroupMsgMainInfo.setCanSendFlag(1);
            if (groupMsgBaseDTO.getStatus().equals(0)){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            if (groupMsgBaseDTO.getSendType().equals(1)&&groupMsgBaseDTO.getSendTime().before(new Date())){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            qywxGroupMsgMainInfo.setCdpGroupId(groupMsgBaseDTO.getCdpGroupId());
            qywxGroupMsgMainInfo.setCdpGroupName(groupMsgBaseDTO.getCdpGroupName());
            qywxGroupMsgMainInfo.setSecondMsgFlag(groupMsgBaseDTO.getSecondMsgFlag());
            qywxGroupMsgMainInfo.setFullGroupMsgId(groupMsgBaseDTO.getFullGroupMsgId());
            groupMsgMainInfoMapper.updateByPrimaryKey(qywxGroupMsgMainInfo);


            qywxGroupCompanyMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupCompanyMapping.class)
                    .set(QywxGroupCompanyMapping::getIsDeleted, 1)
                    .set(QywxGroupCompanyMapping::getModifiedBy, currentUserId)
                    .set(QywxGroupCompanyMapping::getModifiedDate, new Date())
                    .eq(QywxGroupCompanyMapping::getType, 1)
                    .eq(QywxGroupCompanyMapping::getGroupMsgId, qywxGroupMsgMainInfo.getId()));
            insertQywxGroupCompanyMapping(groupMsgBaseDTO.getGroupCompanyMappingDTOList(), currentUserId,
                    qywxGroupMsgMainInfo.getId(), 1, groupMsgBaseDTO.getCompanyMappingType());
            //endregion

            //群发内容记录日志
            groupMsgContentLog(groupMsgBaseDTO,oldInfo,currentUserId,firstName);
            //region 逻辑删除群发内容配置

            materialMappingDao.update(null, Wrappers.lambdaUpdate(TCustomerCommonMaterialMapping.class)
                    .set(TCustomerCommonMaterialMapping::getIsDeleted, 1).set(TCustomerCommonMaterialMapping::getModifiedBy, currentUserId)
                    .set(TCustomerCommonMaterialMapping::getModifiedDate, new Date())
                    .eq(TCustomerCommonMaterialMapping::getSourceId, qywxGroupMsgMainInfo.getId()).eq(TCustomerCommonMaterialMapping::getSourceType,2));
            materialContentMappingDao.update(null, Wrappers.lambdaUpdate(TCustomerCommonMaterialContentMapping.class)
                    .set(TCustomerCommonMaterialContentMapping::getIsDeleted, 1).set(TCustomerCommonMaterialContentMapping::getModifiedBy, currentUserId)
                    .set(TCustomerCommonMaterialContentMapping::getModifiedDate, new Date())
                    .eq(TCustomerCommonMaterialContentMapping::getSourceId, qywxGroupMsgMainInfo.getId())
                    .eq(TCustomerCommonMaterialContentMapping::getSourceType,2));

            insertContentMapping(groupMsgBaseDTO, qywxGroupMsgMainInfo.getId(), currentUserId);
            //endregion

            //endregion
            String groupMsgLog = groupMsgLog(groupMsgBaseDTO, oldInfo);
            //endregion
            if (StringUtils.isNotBlank(groupMsgLog)){
                logService.insertOperationLog2(qywxGroupMsgMainInfo.getId(),
                        "t_qywx_group_msg_main_info",
                        1, GroupMsgOperationTypeEnum.UPDATE.getType(), "编辑群发",
                        null, null, groupMsgLog,
                        null, currentUserId, firstName);
            }
        }
        return Result.buildSuccess("保存成功");
    }


    public Integer groupMsgCustomerCount(GroupMsgQueryCustomerDTO groupMsgBaseDTO) throws IOException {
        String configType="GROUP";
        CdpByTag cdpByTag = null ;

        if (Objects.equals(0,groupMsgBaseDTO.getSecondMsgFlag())&&Objects.equals(SendMemberTypeEnum.SEND_MEMBER_TYPE_PART.getSendMemberType(),groupMsgBaseDTO.getSendMemberType())){
            cdpByTag = new CdpByTag();
            cdpByTag.setStartTime(null);
            cdpByTag.setEndTime(null);
            if (groupMsgBaseDTO.getGroupSendConditionDTO() != null) {
                cdpByTag.setStartTime(DateUtil.dateFormat(groupMsgBaseDTO.getGroupSendConditionDTO().getStartTime(),"yyyy-MM-dd"));
                cdpByTag.setEndTime(DateUtil.dateFormat(groupMsgBaseDTO.getGroupSendConditionDTO().getEndTime(),"yyyy-MM-dd"));
                if (groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser()!=null){
                    //群发好友条件
                    GroupConditionExpressionDTO includeUser = groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser();
                    cdpByTag.setOperator(Objects.equals(includeUser.getExpressionType(),1)?"OR":"AND");
                    //标签
                    List<CdpTagDTO> cdpTagDtos = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(includeUser.getGroupConditionValueDTOList())) {
                        includeUser.getGroupConditionValueDTOList().forEach(dto -> {
                            CdpTagDTO cdpTagDTO = new CdpTagDTO();
                            cdpTagDTO.setTagKey(dto.getGroupCode());
                            cdpTagDTO.setTagValue(dto.getTagName());
                            cdpTagDtos.add(cdpTagDTO);
                        });
                        cdpByTag.setTags(cdpTagDtos);
                    }
                }
                if (groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser()!=null){
                    //群发排除好友条件
                    GroupConditionExpressionDTO excludeUser = groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser();
                    cdpByTag.setExcludeOperator(Objects.equals(excludeUser.getExpressionType(),1)?"OR":"AND");
                    //标签
                    List<CdpTagDTO> cdpTagDtos = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(excludeUser.getGroupConditionValueDTOList())) {
                        excludeUser.getGroupConditionValueDTOList().forEach(dto -> {
                            CdpTagDTO cdpTagDTO = new CdpTagDTO();
                            cdpTagDTO.setTagKey(dto.getGroupCode());
                            cdpTagDTO.setTagValue(dto.getTagName());
                            cdpTagDtos.add(cdpTagDTO);
                        });
                        cdpByTag.setExcludeTags(cdpTagDtos);
                    }
                }
            }
            //小区
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getVillageIds())){
                List<CdpTagDTO> cdpTagDtos = Lists.newArrayList();
                groupMsgBaseDTO.getVillageIds().forEach(v->{
                    CdpTagDTO cdpTagDTO = new CdpTagDTO();
                    cdpTagDTO.setTagKey("tag_hygx_xqxx");
                    cdpTagDTO.setTagValue(String.valueOf(v.getVillageId()));
                    cdpTagDtos.add(cdpTagDTO);
                });
                cdpByTag.setTags(cdpTagDtos);
            }
            configType = "TAG";
        }
        //1.如果全部好友，直接查询下t_qywx_cutomer_user_relation 全部好友导入群发好友表
        if (Objects.equals(0,groupMsgBaseDTO.getSecondMsgFlag())&&Objects.equals(SendMemberTypeEnum.SEND_MEMBER_TYPE_ALL.getSendMemberType(),groupMsgBaseDTO.getSendMemberType())){
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getUserIds())){
                List<List<?>> lists = splitList(groupMsgBaseDTO.getUserIds(), 500);
                int count = 0;
                for (List<?> list: lists) {
                    if (CollectionUtils.isNotEmpty(list)){
                        count += qywxCutomerUserRelationMapper.queryCustomerRelationByUserId((List<String>) list);
                    }
                }
                return count;
            }
        }else {
            //调用cdp通知开始计算
            MissionCreateDTO missionCreateDTO = new MissionCreateDTO();
            missionCreateDTO.setConfigType(configType);
            missionCreateDTO.setUserIds(groupMsgBaseDTO.getUserIds());
            CdpByGroup cdpByGroup = new CdpByGroup();
            if (Objects.equals(3,groupMsgBaseDTO.getSendMemberType())){
                cdpByGroup.setGroupId(groupMsgBaseDTO.getCdpGroupId());
                missionCreateDTO.setByGroup(cdpByGroup);
            }
            if (cdpByTag != null){
                missionCreateDTO.setByTag(cdpByTag);
            }
            if (Objects.equals(1,groupMsgBaseDTO.getSecondMsgFlag())){
                CdpByPush cdpByPush = new CdpByPush();
                cdpByPush.setMPushId(groupMsgBaseDTO.getFullPathId());
                cdpByPush.setPhase(Objects.equals(groupMsgBaseDTO.getSendObjectType(),1)?"SENT_NOT_OPEN":"OPENED_NOT_JOIN");
                missionCreateDTO.setByPush(cdpByPush);
            }
            Result<MissionGroupCountDTO> groupMsgCount;
            try {
                log.error("请求cdp入参：{}",missionCreateDTO);
                groupMsgCount = cdpClient.getGroupMsgCount(missionCreateDTO);
                log.error("cdp响应结果：{}",groupMsgCount.getData());
            }catch (Exception e){
                log.error("当前批次:{}调用异常{}",e);
                throw new BusinessException("调用查询异常");
            }
            if (groupMsgCount.getSuccess()&&groupMsgCount.getData()!=null){
                return groupMsgCount.getData().getTotal() == null?0:groupMsgCount.getData().getTotal();
            }
            //throw new BusinessException("调用查询异常");
            /*ExecutorService executor = ThreadUtil.newExecutor(10);
            List<CompletableFuture<MissionGroupCountDTO>> resultList = new ArrayList<>();
            List<List<?>> lists = splitList(missionCreateDTO.getUserIds(), 10000);
            for (List<?> list: lists) {
                missionCreateDTO.setUserIds((List<String>) list);
                CompletableFuture<MissionGroupCountDTO> result =
                        CompletableFuture.supplyAsync(() -> buildResult(missionCreateDTO),executor);
                result.exceptionally(ex->{
                    log.error("计算失败：{}",ex.getMessage());
                    executor.shutdownNow();
                    return null;
                });
                resultList.add(result);
            }

            if (CollectionUtils.isNotEmpty(resultList)){
                int count = 0;
                for (CompletableFuture<MissionGroupCountDTO> dto: resultList) {
                    if (dto!=null&&dto.join()!=null){
                        count+=dto.join().getTotal()==null?0:dto.join().getTotal();
                    }
                }
                return count;
            }*/
        }
        return 0;
    }

    private MissionGroupCountDTO buildResult(MissionCreateDTO missionCreateDTO) {
        Result<MissionGroupCountDTO> groupMsgCount;
        try {
            log.error("请求cdp入参：{}",missionCreateDTO);
            groupMsgCount = cdpClient.getGroupMsgCount(missionCreateDTO);
            log.error("cdp响应结果：{}",groupMsgCount.getData());
        }catch (Exception e){
            log.error("当前批次:{}调用异常{}",e);
            throw new BusinessException("调用查询异常");
        }
        if (groupMsgCount.getSuccess()){
            return groupMsgCount.getData();
        }
        throw new BusinessException("调用查询异常");
    }


    @Transactional
    public Result insertSecondGroupMsg(GroupMsgBaseDTO groupMsgBaseDTO,String currentUserId,String firstName){
        QywxGroupMsgMainInfo groupMsgMainInfo = null;
        Integer isUpGroupMsgFlag = 0;
        if (groupMsgBaseDTO.getId() == null){
            //新增
            //创建群发
            groupMsgMainInfo=buildGroupMsg(groupMsgBaseDTO,currentUserId,firstName);
            groupMsgMainInfoMapper.insert(groupMsgMainInfo);
            groupMsgBaseDTO.setId(groupMsgMainInfo.getId());
        }else {
            //修改
            groupMsgMainInfo=buildGroupMsg(groupMsgBaseDTO,currentUserId,firstName);
            groupMsgMainInfo.setCanSendFlag(1);
            if (groupMsgBaseDTO.getStatus().equals(0)){
                groupMsgMainInfo.setCanSendFlag(0);
            }
            if (groupMsgBaseDTO.getSendType().equals(1)&&groupMsgBaseDTO.getSendTime().before(new Date())){
                groupMsgMainInfo.setCanSendFlag(0);
            }
            groupMsgMainInfoMapper.updateByPrimaryKey(groupMsgMainInfo);
            isUpGroupMsgFlag = 1;

        }
        groupMsgBaseDTO.setId(groupMsgMainInfo.getId());
        //配置群发内容
        buildContentConfig(groupMsgBaseDTO,currentUserId,firstName);
        //配置生效员工列表

        //配置发送对象，创建任务通知cdp
        if (Objects.equals(isUpGroupMsgFlag,0)){
            //分公司配置，沿用父群发id配置一样
            List<QywxGroupCompanyMapping> qywxGroupCompanyMappings = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                    .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                    .eq(QywxGroupCompanyMapping::getGroupMsgId, groupMsgBaseDTO.getFullGroupMsgId())
                    .eq(QywxGroupCompanyMapping::getType, 1));
            if (CollectionUtils.isNotEmpty(qywxGroupCompanyMappings)){
                qywxGroupCompanyMappings.forEach(company->{
                    company.setGroupMsgId(groupMsgBaseDTO.getId());
                    company.setCreatedBy(currentUserId);
                    company.setCreatedDate(new Date());
                    company.setModifiedBy(currentUserId);
                    company.setModifiedDate(new Date());
                });
                qywxGroupCompanyMappingMapper.insertBatch(qywxGroupCompanyMappings);
            }
            //region 生效员工配置
            GroupMsgInfoDTO groupMsgInfoDTO = new GroupMsgInfoDTO();
            List<QywxGroupEffectChargeMapping> qywxGroupEffectChargeMappings = queryEffectCharge(groupMsgBaseDTO.getFullGroupMsgId(), groupMsgInfoDTO);
            groupMsgBaseDTO.setEffectChargeMappingList(qywxGroupEffectChargeMappings);
            groupMsgInfoDTO.setId(groupMsgBaseDTO.getId());
            //endregion
            insertCharge(groupMsgInfoDTO,currentUserId);
            insertEffectCustomer(groupMsgBaseDTO,currentUserId,groupMsgMainInfo);
        }
        return Result.buildSuccess("创建成功");
    }

    private void insertCharge(GroupMsgInfoDTO groupMsgInfoDTO,String currentUserId){
        Date date = new Date();
        if (groupMsgInfoDTO.getOrganizeMappingDTO()!=null&&CollectionUtils.isNotEmpty(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList())){
            groupMsgInfoDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList().forEach(category->{
                category.setGroupMsgId(groupMsgInfoDTO.getId());
                category.setCreatedBy(currentUserId);
                category.setModifiedBy(currentUserId);
                category.setCreatedDate(date);
                category.setModifiedDate(date);
            });
            qywxGroupChannelCategoryMappingMapper.insertBatch(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList());
        }
        if (groupMsgInfoDTO.getOrganizeMappingDTO()!=null&&CollectionUtils.isNotEmpty(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList())){
            groupMsgInfoDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList().forEach(company->{
                company.setGroupMsgId(groupMsgInfoDTO.getId());
                company.setCreatedBy(currentUserId);
                company.setModifiedBy(currentUserId);
                company.setCreatedDate(date);
                company.setModifiedDate(date);
            });
            qywxGroupCompanyMappingMapper.insertBatch(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList());
        }

        if (groupMsgInfoDTO.getOrganizeMappingDTO()!=null&&CollectionUtils.isNotEmpty(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList())){
            groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList().forEach(store->{
                store.setGroupMsgId(groupMsgInfoDTO.getId());
                store.setCreatedBy(currentUserId);
                store.setModifiedBy(currentUserId);
                store.setCreatedDate(date);
                store.setModifiedDate(date);
            });
            qywxGroupStoreMappingMapper.insertBatch(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList());
        }

        if (groupMsgInfoDTO.getOrganizeMappingDTO()!=null&&CollectionUtils.isNotEmpty(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList())){
            groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList().forEach(station->{
                station.setGroupMsgId(groupMsgInfoDTO.getId());
                station.setCreatedBy(currentUserId);
                station.setModifiedBy(currentUserId);
                station.setCreatedDate(date);
                station.setModifiedDate(date);
            });
            qywxGroupStationMappingMapper.insertBatch(groupMsgInfoDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList());
        }
        if (CollectionUtils.isNotEmpty(groupMsgInfoDTO.getGroupOrganizeChargeMappingList())){
            groupMsgInfoDTO.getGroupOrganizeChargeMappingList().forEach(charge->{
                charge.setGroupMsgId(groupMsgInfoDTO.getId());
                charge.setCreatedBy(currentUserId);
                charge.setModifiedBy(currentUserId);
                charge.setChargeUserName(MybatisMateConfig.encrypt(charge.getChargeUserName()));
                charge.setChargePhone(MybatisMateConfig.encrypt(charge.getChargePhone()));
                charge.setCreatedDate(date);
                charge.setModifiedDate(date);
            });
            qywxGroupEffectChargeMappingMapper.insertBatch(groupMsgInfoDTO.getGroupOrganizeChargeMappingList());
        }
        if (CollectionUtils.isNotEmpty(groupMsgInfoDTO.getGroupChargeMappingDTOList())){
            groupMsgInfoDTO.getGroupChargeMappingDTOList().forEach(charge->{
                charge.setGroupMsgId(groupMsgInfoDTO.getId());
                charge.setCreatedBy(currentUserId);
                charge.setModifiedBy(currentUserId);
                charge.setChargeUserName(MybatisMateConfig.encrypt(charge.getChargeUserName()));
                charge.setChargePhone(MybatisMateConfig.encrypt(charge.getChargePhone()));
                charge.setCreatedDate(date);
                charge.setModifiedDate(date);
            });
            qywxGroupEffectChargeMappingMapper.insertBatch(groupMsgInfoDTO.getGroupChargeMappingDTOList());
        }
    }

    public QywxGroupMsgMainInfo buildGroupMsg(GroupMsgBaseDTO groupMsgBaseDTO,String currentUserId,String firstName){
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = new QywxGroupMsgMainInfo();
        BeanUtils.copyProperties(groupMsgBaseDTO, qywxGroupMsgMainInfo);
        qywxGroupMsgMainInfo.setCreatedDate(new Date());
        qywxGroupMsgMainInfo.setCreatedBy(currentUserId);
        qywxGroupMsgMainInfo.setModifiedBy(currentUserId);
        qywxGroupMsgMainInfo.setModifiedDate(new Date());
        qywxGroupMsgMainInfo.setReviseTime(new Date());
        qywxGroupMsgMainInfo.setReviseName(MybatisMateConfig.encrypt(firstName));
        qywxGroupMsgMainInfo.setNoticeStatus(0);
        qywxGroupMsgMainInfo.setIsDeleted(0);
        qywxGroupMsgMainInfo.setSourceType(1);
        qywxGroupMsgMainInfo.setCanSendFlag(2);
        qywxGroupMsgMainInfo.setCdpGroupId(groupMsgBaseDTO.getCdpGroupId());
        qywxGroupMsgMainInfo.setSecondMsgFlag(groupMsgBaseDTO.getSecondMsgFlag());
        qywxGroupMsgMainInfo.setNoticeEndDate(groupMsgBaseDTO.getNoticeEndDate());
        qywxGroupMsgMainInfo.setFullGroupMsgId(groupMsgBaseDTO.getFullGroupMsgId());
        qywxGroupMsgMainInfo.setSendObjectType(groupMsgBaseDTO.getSendObjectType());
        return qywxGroupMsgMainInfo;
    }

    public void buildContentConfig(GroupMsgBaseDTO groupMsgBaseDTO,String currentUserId,String firstName){
        //删除历史数据
        qywxGroupContentConfigMapper.update(null,Wrappers.lambdaUpdate(QywxGroupContentConfig.class)
                .set(QywxGroupContentConfig::getIsDeleted,1)
                .eq(QywxGroupContentConfig::getGroupMsgId,groupMsgBaseDTO.getId()));
        //保存新的群发内容
        if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupContentConfigDTOList())){
            List<QywxGroupContentConfig> recordList = Lists.newArrayList();
            groupMsgBaseDTO.getGroupContentConfigDTOList().forEach(groupContentConfigDTO -> {
                QywxGroupContentConfig groupContentConfig = new QywxGroupContentConfig();
                BeanUtils.copyProperties(groupContentConfigDTO,groupContentConfig);
                groupContentConfig.setIsDeleted(0);
                groupContentConfig.setGroupMsgId(groupMsgBaseDTO.getId());
                groupContentConfig.setCreatedBy(currentUserId);
                groupContentConfig.setCreatedDate(new Date());
                groupContentConfig.setModifiedBy(currentUserId);
                groupContentConfig.setModifiedDate(new Date());
                recordList.add(groupContentConfig);
            });
            qywxGroupContentConfigMapper.insertBatch(recordList);
        }
    }

    public void groupMsgCustomerLog(GroupMsgBaseDTO groupMsgBaseDTO,QywxGroupMsgMainInfo oldInfo,String currentUserId,String firstName){
        String groupMsgLog = "";
        if (groupMsgBaseDTO.getSendMemberType().equals(1)||oldInfo.getSendMemberType().equals(1)){
            groupMsgLog = fieldStrDispose(logService.getSendMemberTypeName(groupMsgBaseDTO.getSendMemberType()), logService.getSendMemberTypeName(oldInfo.getSendMemberType()), "选择群发好友", groupMsgLog);
        }
        QywxGroupSendCondition qywxGroupSendCondition = qywxGroupSendConditionMapper.selectOne(Wrappers.lambdaQuery(QywxGroupSendCondition.class)
                .eq(QywxGroupSendCondition::getIsDeleted, 0)
                .eq(QywxGroupSendCondition::getGroupMsgId, oldInfo.getId()));
        //好友添加时间比较

        String oldStartTime= "";
        String oldEndTime= "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (qywxGroupSendCondition!=null){
            oldStartTime = qywxGroupSendCondition.getStartTime() != null ? sdf.format(qywxGroupSendCondition.getStartTime()) : "";
            oldEndTime = qywxGroupSendCondition.getEndTime() != null ? sdf.format(qywxGroupSendCondition.getEndTime()) : "";
        }
        String newStartTime= "";
        String newEndTime= "";
        if (groupMsgBaseDTO.getGroupSendConditionDTO()!=null){
            newStartTime = groupMsgBaseDTO.getGroupSendConditionDTO() .getStartTime()!= null ? sdf.format(groupMsgBaseDTO.getGroupSendConditionDTO() .getStartTime()) : "";
            newEndTime = groupMsgBaseDTO.getGroupSendConditionDTO() .getEndTime()!= null ? sdf.format(groupMsgBaseDTO.getGroupSendConditionDTO() .getEndTime()) : "";
        }
        groupMsgLog = fieldStrDispose(newStartTime+"/"+newEndTime, oldStartTime+"/"+oldEndTime, "好友添加时间筛选标签", groupMsgLog);
        Optional<QywxGroupConditionExpression> includeOldFirst = null;
        Optional<QywxGroupConditionExpression> excludeOldFirst = null;
        String oldIncludeLog = "";
        String oldExcludeLog = "";
        if (qywxGroupSendCondition!=null){
            Set<String> oldIncludeValueLog = Sets.newHashSet();
            Set<String> oldExcludeValueLog = Sets.newHashSet();
            List<QywxGroupConditionExpression> qywxGroupConditionExpressions = qywxGroupConditionExpressionMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionExpression.class)
                    .eq(QywxGroupConditionExpression::getIsDeleted, 0)
                    .eq(QywxGroupConditionExpression::getConditionId, qywxGroupSendCondition.getId()));
            List<QywxGroupConditionValue> conditionValues = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(qywxGroupConditionExpressions)){
                List<Long> expressionIdList = qywxGroupConditionExpressions.stream().map(QywxGroupConditionExpression::getId).collect(Collectors.toList());
                conditionValues = qywxGroupConditionValueMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionValue.class)
                        .eq(QywxGroupConditionValue::getIsDeleted, 0)
                        .in(QywxGroupConditionValue::getConditionExpressionId, expressionIdList));
                includeOldFirst = qywxGroupConditionExpressions.stream().filter(expression -> expression.getType().equals(1)).findFirst();
                excludeOldFirst = qywxGroupConditionExpressions.stream().filter(expression -> expression.getType().equals(2)).findFirst();
                if(includeOldFirst.isPresent()){
                    oldIncludeLog+=includeOldFirst.get().getExpressionType().equals(2)? "以下标签满足其中之一的":"以下标签同时满足的";
                    oldIncludeValueLog.addAll(getOldValueLog(conditionValues, includeOldFirst));
                    oldIncludeLog+=oldIncludeValueLog;
                }
                if(excludeOldFirst.isPresent()){
                    oldExcludeLog+=excludeOldFirst.get().getExpressionType().equals(2)? "以下标签满足其中之一的":"以下标签同时满足的";
                    oldExcludeValueLog.addAll(getOldValueLog(conditionValues, excludeOldFirst));
                    oldExcludeLog+=oldExcludeValueLog;
                }
            }
        }
        String newIncludeLog = "";
        String newExcludeLog = "";
        if (groupMsgBaseDTO.getGroupSendConditionDTO()!=null){
            if (groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser()!=null){
                newIncludeLog+= groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser().getExpressionType().equals(2)? "以下标签满足其中之一的":"以下标签同时满足的";
                newIncludeLog+=getNewValueLog(groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser().getGroupConditionValueDTOList());
            }
            if (groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser()!=null){
                newExcludeLog += groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser().getExpressionType().equals(2)? "以下标签满足其中之一的":"以下标签同时满足的";
                newExcludeLog+=getNewValueLog(groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser().getGroupConditionValueDTOList());
            }
        }
        groupMsgLog = fieldStrDispose(newIncludeLog, oldIncludeLog, "标签：", groupMsgLog);
        groupMsgLog = fieldStrDispose(newExcludeLog, oldExcludeLog, "排除好友", groupMsgLog);

        //好友筛选 人群包日志
        groupMsgLog = fieldStrDispose(String.valueOf(groupMsgBaseDTO.getCdpGroupName()), String.valueOf(oldInfo.getCdpGroupName()), "按人群包筛选好友：", groupMsgLog);

        if (StringUtils.isNotBlank(groupMsgLog)){
            logService.insertOperationLog2(oldInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_CUSTOMER.getType(), "群发好友变更",
                    null, null, groupMsgLog,
                    null, currentUserId, firstName);
        }
    }


    public Set<String> getOldValueLog(List<QywxGroupConditionValue> conditionValues,Optional<QywxGroupConditionExpression> first){
        Set<String> valueLog = Sets.newHashSet();
        List<QywxGroupConditionValue> valueList = conditionValues.stream().filter(value -> first.get().getId().equals(value.getConditionExpressionId())).collect(Collectors.toList());
        Map<String, List<String>> valueMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(valueList)){
            valueMap = valueList.stream().collect(Collectors.groupingBy(QywxGroupConditionValue::getGroupName, Collectors.mapping(QywxGroupConditionValue::getTagName, Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(valueMap)){
            for (Map.Entry<String, List<String>> map: valueMap.entrySet()) {
                valueLog.add(map.getKey()+"："+map.getValue().stream().collect(Collectors.joining("、")));
            }
        }
        return valueLog;
    }

    public Set<String> getNewValueLog(List<GroupConditionValueDTO> groupConditionValueDTOList){
        Set<String> valueLog = Sets.newHashSet();
        Map<String, List<String>> groupMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupConditionValueDTOList)){
            groupMap = groupConditionValueDTOList.stream()
                    .collect(Collectors.groupingBy(GroupConditionValueDTO::getGroupName, Collectors.mapping(GroupConditionValueDTO::getTagName, Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(groupMap)){
            for (Map.Entry<String, List<String>> map: groupMap.entrySet()) {
                valueLog.add(map.getKey()+"："+map.getValue().stream().collect(Collectors.joining("、")));
            }
        }
        return valueLog;
    }

    public void companyLog(GroupMsgBaseDTO groupMsgBaseDTO,Long oldId,String currentUserId,String firstName){
        List<QywxGroupCompanyMapping> qywxGroupCompanyMapping = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .eq(QywxGroupCompanyMapping::getType, 1)
                .eq(QywxGroupCompanyMapping::getGroupMsgId, oldId));
        String str = "";
        String oldData = "";
        String newData = "";
        if (CollectionUtils.isNotEmpty(qywxGroupCompanyMapping)){
            Optional<QywxGroupCompanyMapping> first = qywxGroupCompanyMapping.stream().filter(item -> item.getCompanyMappingType().equals(-1)).findFirst();
            String oldCompanyName = "";
            if (first.isPresent()){
                oldCompanyName = "全部";
            }else {
                oldCompanyName = "【"+qywxGroupCompanyMapping.stream().map(QywxGroupCompanyMapping::getCompanyName).collect(Collectors.joining(","))+"】";
            }
            String newCompanyName = "";
            if (groupMsgBaseDTO.getCompanyMappingType().equals(-1)){
                newCompanyName = "全部";
            }else {
                newCompanyName = "【"+groupMsgBaseDTO.getGroupCompanyMappingDTOList().stream().map(GroupCompanyMappingDTO::getCompanyName).collect(Collectors.joining(","))+"】";
            }
            str = fieldStrDispose(newCompanyName,oldCompanyName, "分公司权限", str);
        }
        if (StringUtils.isNotBlank(str)){
            logService.insertOperationLog2(oldId,
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_COMPANY.getType(), "分公司权限变更",
                    oldData, newData, str,
                    null, currentUserId, firstName);
        }
    }

    public void groupMsgContentLog(GroupMsgBaseDTO groupMsgBaseDTO,QywxGroupMsgMainInfo oldInfo,String currentUserId,String firstName){
        // 记录分类树选取日志
        String groupMsgLog = "";
        if (groupMsgBaseDTO.getConfigType().equals(2)&&groupMsgBaseDTO.getConfigType().equals(oldInfo.getConfigType())){
            TCustomerCommonMaterialMapping tCustomerCommonMaterialMapping = materialMappingDao.selectOne(Wrappers.lambdaQuery(TCustomerCommonMaterialMapping.class)
                    .eq(TCustomerCommonMaterialMapping::getIsDeleted, 0)
                    .eq(TCustomerCommonMaterialMapping::getSourceId, oldInfo.getId())
                    .eq(TCustomerCommonMaterialMapping::getSourceType,2));
            if (!groupMsgBaseDTO.getMaterialId().equals(tCustomerCommonMaterialMapping.getMaterialId())||
                    !groupMsgBaseDTO.getTreeId().equals(tCustomerCommonMaterialMapping.getTreeId())){
                Result<TreeMaterialInfo> resultNew = cmsClient.getTreeMaterialInfo(groupMsgBaseDTO.getTreeId(), groupMsgBaseDTO.getMaterialId());
                String newData = "";
                if (resultNew.getSuccess()&&resultNew.getData()!=null){
                    Optional<Material> first = resultNew.getData().getMaterialList()
                            .stream().filter(item -> item.getId().equals(Integer.valueOf(String.valueOf(groupMsgBaseDTO.getMaterialId())))).findFirst();
                    if (first.isPresent()){
                        newData = "内容树："+resultNew.getData().getMaterialCategoryTree().getTitle()+"，分类树："+first.get().getTitle();
                    }
                }
                Result<TreeMaterialInfo> resuleOld = cmsClient.getTreeMaterialInfo(tCustomerCommonMaterialMapping.getTreeId(), tCustomerCommonMaterialMapping.getMaterialId());
                String oldData = "";
                if (resuleOld.getSuccess()&&resuleOld.getData()!=null){
                    Optional<Material> first = resuleOld.getData().getMaterialList()
                            .stream().filter(item -> item.getId().equals(Integer.valueOf(String.valueOf(tCustomerCommonMaterialMapping.getMaterialId())))).findFirst();
                    if (first.isPresent()){
                        oldData = "内容树："+resuleOld.getData().getMaterialCategoryTree().getTitle()+"，分类树："+first.get().getTitle();
                    }
                }
                groupMsgLog = fieldStrDispose(newData,oldData, "群发内容", groupMsgLog);
            }
        }
        if (groupMsgBaseDTO.getConfigType().equals(1)&&groupMsgBaseDTO.getConfigType().equals(oldInfo.getConfigType())){
            List<QywxGroupContentConfig> qywxGroupContentConfigList = qywxGroupContentConfigMapper.selectList(Wrappers.lambdaQuery(QywxGroupContentConfig.class)
                    .eq(QywxGroupContentConfig::getIsDeleted, 0)
                    .eq(QywxGroupContentConfig::getGroupMsgId, oldInfo.getId()));
            if (CollectionUtils.isNotEmpty(qywxGroupContentConfigList)&&CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupContentConfigDTOList())){
                if (qywxGroupContentConfigList.size() != groupMsgBaseDTO.getGroupContentConfigDTOList().size()){
                    String oldType = qywxGroupContentConfigList.stream().sorted(Comparator.comparing(QywxGroupContentConfig::getSort))
                            .map(obj->String.valueOf(ContentMsgTypeEnum.getContentMsg(obj.getMsgType()).getMsgName())).collect(Collectors.joining("，"));
                    String newType = groupMsgBaseDTO.getGroupContentConfigDTOList().stream().sorted(Comparator.comparing(GroupContentConfigDTO::getSort))
                            .map(obj->String.valueOf(ContentMsgTypeEnum.getContentMsg(obj.getMsgType()).getMsgName())).collect(Collectors.joining("，"));
                    groupMsgLog = fieldStrDispose(newType,oldType, "群发内容", groupMsgLog);
                }
            }
        }
        if (StringUtils.isNotBlank(groupMsgLog)){
            logService.insertOperationLog2(oldInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_CONTENT.getType(), "群发内容变更",
                    null, null, groupMsgLog,
                    null, currentUserId, firstName);
        }
    }

    private String chargeType(Object object,Integer type,String log){
        if (ObjectUtils.isEmpty(object)||ObjectUtils.isNull(object)){
            return log;
        }else {
            if (type.equals(0)){
                return StringUtils.isBlank(log)?"组织配置":log+"和组织配置";
            }else {
                return "指定人员";
            }
        }
    }

    public void groupMsgChargeLog(GroupMsgBaseDTO groupMsgBaseDTO,QywxGroupMsgMainInfo oldInfo,String currentUserId,String firstName){

        String groupMsgLog = "";
        List<QywxGroupCompanyMapping> qywxGroupCompanyMappings = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .eq(QywxGroupCompanyMapping::getType, 2)
                .eq(QywxGroupCompanyMapping::getGroupMsgId, oldInfo.getId()));
        List<QywxGroupEffectChargeMapping> qywxGroupEffectChargeMappings = qywxGroupEffectChargeMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupEffectChargeMapping.class)
                .eq(QywxGroupEffectChargeMapping::getIsDeleted, 0)
                .eq(QywxGroupEffectChargeMapping::getType,1)
                .eq(QywxGroupEffectChargeMapping::getGroupMsgId, oldInfo.getId()));
        //指定人员
        List<Long> oldChargeUserIdList=Lists.newArrayList();
        String oldChargeNameLog="";
        if (CollectionUtils.isNotEmpty(qywxGroupEffectChargeMappings)){
            oldChargeUserIdList = qywxGroupEffectChargeMappings.stream().map(QywxGroupEffectChargeMapping::getChargeUserId).distinct().collect(Collectors.toList());
            oldChargeNameLog = qywxGroupEffectChargeMappings.stream().map(obj->MybatisMateConfig.decrypt(obj.getChargeUserName())).collect(Collectors.joining(","));
        }
        String newChargeNameLog = "";
        List<Long> newChargeUserIdList= Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList())){
            newChargeUserIdList = groupMsgBaseDTO.getGroupChargeMappingDTOList().stream().map(GroupChargeMappingDTO::getChargeUserId).distinct().collect(Collectors.toList());
            newChargeNameLog = groupMsgBaseDTO.getGroupChargeMappingDTOList().stream().map(obj->MybatisMateConfig.decrypt(obj.getChargeUserName())).collect(Collectors.joining(","));
        }
        String chargeLog = "";
        if (!newChargeUserIdList.equals(oldChargeUserIdList)){
            chargeLog = fieldStrDispose(newChargeNameLog, oldChargeNameLog, "指定人员", chargeLog);
        }

        if (StringUtils.isNotBlank(chargeLog)){
            logService.insertOperationLog2(oldInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_CHARGE.getType(), "指定人员变更",
                    null, null, chargeLog,
                    null, currentUserId, firstName);
        }

        //分公司权限
        if (groupMsgBaseDTO.getOrganizeMappingDTO()!=null){
            List<Long> newCompanyNameList = Lists.newArrayList();
            String newStr="";
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList())){
                newCompanyNameList = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList()
                        .stream().map(GroupCompanyMappingDTO::getCompanyId).collect(Collectors.toList());
                newStr = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList().stream().map(GroupCompanyMappingDTO::getCompanyName).collect(Collectors.joining(","));
            }
            List<Long> oldCompanyNameList = Lists.newArrayList();
            String oldStr="";
            if (CollectionUtils.isNotEmpty(qywxGroupCompanyMappings)){
               oldCompanyNameList = qywxGroupCompanyMappings.stream().map(QywxGroupCompanyMapping::getCompanyId).collect(Collectors.toList());
               oldStr = qywxGroupCompanyMappings.stream().map(QywxGroupCompanyMapping::getCompanyName).collect(Collectors.joining(","));
            }
            if (!newCompanyNameList.equals(oldCompanyNameList)){
                groupMsgLog = fieldStrDispose(newStr, oldStr, "选择分公司", groupMsgLog);
            }
        }

        //岗位
        List<QywxGroupStationMapping> qywxGroupStationMappings = qywxGroupStationMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStationMapping.class)
                .eq(QywxGroupStationMapping::getIsDeleted, 0)
                .eq(QywxGroupStationMapping::getGroupMsgId, oldInfo.getId()));

        if (groupMsgBaseDTO.getOrganizeMappingDTO()!=null){
            List<String> newStationIdList = Lists.newArrayList();
            List<String> typeCodes = new ArrayList<>();
            typeCodes.add("gw");
            List<Dic> dicList = systemClient.queryByTypeCodes(typeCodes).getData();
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList())){
                groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList().forEach(station->{
                    Optional<Dic> first = dicList.stream().filter(d -> d.getId().equals(Long.valueOf(String.valueOf(station)))).findFirst();
                    if (first.isPresent()) {
                        Dic dic = first.get();
                        newStationIdList.add(dic.getValueName());
                    }
                });
            }
            List<String> oldStationIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(qywxGroupStationMappings)){
                qywxGroupStationMappings.forEach(station->{
                    Optional<Dic> first = dicList.stream().filter(d -> d.getId().equals(Long.valueOf(String.valueOf(station.getStation())))).findFirst();
                    if (first.isPresent()) {
                        Dic dic = first.get();
                        oldStationIdList.add(dic.getValueName());
                    }
                });
            }
            if (!newStationIdList.equals(oldStationIdList)){
                String newStr = newStationIdList.stream().collect(Collectors.joining(","));
                String oldStr = oldStationIdList.stream().collect(Collectors.joining(","));
                groupMsgLog = fieldStrDispose(newStr, oldStr, "岗位", groupMsgLog);
            }
            //渠道
            List<QywxGroupChannelCategoryMapping> qywxGroupChannelCategoryMappings = qywxGroupChannelCategoryMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupChannelCategoryMapping.class)
                    .eq(QywxGroupChannelCategoryMapping::getIsDeleted, 0)
                    .eq(QywxGroupChannelCategoryMapping::getGroupMsgId, oldInfo.getId()));
            //门店
            List<QywxGroupStoreMapping> qywxGroupStoreMappings = qywxGroupStoreMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStoreMapping.class)
                    .eq(QywxGroupStoreMapping::getIsDeleted, 0)
                    .eq(QywxGroupStoreMapping::getGroupMsgId, oldInfo.getId()));

            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList())&&
                    CollectionUtils.isNotEmpty(qywxGroupStoreMappings)){
                groupMsgLog = fieldStrDispose("门店渠道", "门店", "适用门店", groupMsgLog);
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList())
                    &&CollectionUtils.isNotEmpty(qywxGroupChannelCategoryMappings)){
                groupMsgLog = fieldStrDispose("门店", "门店渠道", "适用门店", groupMsgLog);
            }
            List<String> newStoreNameList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList())){
                FindStoreByCodesInDto byCodesInDto = new FindStoreByCodesInDto();
                byCodesInDto.setCodeList(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList());
                List<FindStoreByIdOutDto> storeList = orgClient.findByCodes(byCodesInDto).getData();
                groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList().forEach(dto->{
                    Optional<FindStoreByIdOutDto> first = storeList.stream().filter(s -> s.getCode().equals(dto)).findFirst();
                    if (first.isPresent()) {
                        FindStoreByIdOutDto store = first.get();
                        newStoreNameList.add(store.getName());
                    }
                });

            }
            List<String> oldStoreNameList = qywxGroupStoreMappings.stream().map(QywxGroupStoreMapping::getStoreName).distinct().collect(Collectors.toList());
            String newStr="";
            if (CollectionUtils.isNotEmpty(newStoreNameList)){
                newStr = newStoreNameList.stream().collect(Collectors.joining(","));
            }
            String oldStr="";
            if (CollectionUtils.isNotEmpty(oldStoreNameList)){
                oldStr = oldStoreNameList.stream().collect(Collectors.joining(","));
            }
            groupMsgLog = fieldStrDispose(newStr, oldStr, "适用门店", groupMsgLog);
            List<String> newChannelCategory=Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList())){
                newChannelCategory = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList()
                        .stream()
                        .map(OrganizeMappingDTO.GroupChannelCategoryMappingDTO::getChannelSubdivideName).collect(Collectors.toList());
            }
            List<String> oldChannelCategory=Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(qywxGroupChannelCategoryMappings)){
                oldChannelCategory = qywxGroupChannelCategoryMappings
                        .stream()
                        .map(QywxGroupChannelCategoryMapping::getChannelSubdivideName).collect(Collectors.toList());
            }
            String newCategoryLog="";
            if (CollectionUtils.isNotEmpty(newChannelCategory)){
                newCategoryLog = newChannelCategory.stream().collect(Collectors.joining(","));
            }
            String oldCategoryLog="";
            if (CollectionUtils.isNotEmpty(oldChannelCategory)){
                oldCategoryLog = oldChannelCategory.stream().collect(Collectors.joining(","));
            }
            groupMsgLog = fieldStrDispose(newCategoryLog, oldCategoryLog, "适用门店渠道", groupMsgLog);
        }
        if (StringUtils.isNotBlank(groupMsgLog)){
            logService.insertOperationLog2(oldInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_CHARGE.getType(), "从组织配置变更",
                    null, null, groupMsgLog,
                    null, currentUserId, firstName);
        }
    }




    public String groupMsgLog(GroupMsgBaseDTO newInfo, QywxGroupMsgMainInfo oldInfo) {
        String str = fieldStrDispose(newInfo.getTitle(), oldInfo.getTitle(), "群发标题", "");
        str = fieldStrDispose(logService.getDataName(newInfo.getNoticeEndDate()), logService.getDataName(oldInfo.getNoticeEndDate()), "任务截止日期", str);
        str = fieldStrDispose(logService.getConfigName(newInfo.getConfigType()), logService.getConfigName(oldInfo.getConfigType()), "群发内容", str);
        str = fieldStrDispose(logService.getSendTypeName(newInfo.getSendType()), logService.getSendTypeName(oldInfo.getSendType()), "选择群发时间", str);
        str = fieldStrDispose(logService.getTypeName(newInfo.getStoreConfigFlag()), logService.getTypeName(oldInfo.getStoreConfigFlag()), "选择导购门店名称及地址", str);
        str = fieldStrDispose(logService.getDataName(newInfo.getSendTime()),logService.getDataName(oldInfo.getSendTime()), "选择群发时间", str);
        str = fieldStrDispose(logService.getAccountIdName(newInfo.getAccountId()),logService.getAccountIdName(oldInfo.getAccountId()), "生效企微", str);
        str = fieldStrDispose(logService.getStatusName(newInfo.getStatus()), logService.getStatusName(oldInfo.getStatus()), "状态", str);
        log.info("群发内容编辑日志：{}", str);
        return str;
    }



    public static String fieldStrDispose(String newData, String oldData, String fieldName, String str) {
        //判断该字段是否在该页面中呈现
        String old = StringUtils.isEmpty(oldData) ? "空" : oldData;
        String new1 = StringUtils.isEmpty(newData) ? "空" : newData;
        if (!old.equals(new1)) {
            if (StringUtils.isNotEmpty(str)) {
                str += "," + fieldName + "从" + old + "修改成" + new1;
            } else {
                str += fieldName + "从" + old + "修改成" + new1;
            }
        }
        return str;
    }

    public Result stopGroupMsg(Long id){
        String currentUserId = userAuthorUtils.getCurrentUserId();
        String firstName = userAuthorUtils.getUserAuthor().getFirstName();
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class)
                .eq(QywxGroupMsgMainInfo::getId, id)
                .eq(QywxGroupMsgMainInfo::getNoticeStatus, 2)
                .eq(QywxGroupMsgMainInfo::getStopMsg,0)
                .eq(QywxGroupMsgMainInfo::getIsDeleted, 0));
        if (qywxGroupMsgMainInfo == null){
            log.error("未查询到当前群发内容");
            return Result.buildFailure("未查询到当前群发内容");
        }
        StopGroupMsgLogDTO stopGroupMsgLogDTO = new StopGroupMsgLogDTO();
        stopGroupMsgLogDTO.setGroupMsgId(id);
        stopGroupMsgLogDTO.setAccountId(qywxGroupMsgMainInfo.getAccountId());
        stopMessageChannel.send(MessageBuilder.withPayload(stopGroupMsgLogDTO).build());
        groupMsgMainInfoMapper.update(null, Wrappers.lambdaUpdate(QywxGroupMsgMainInfo.class)
               .set(QywxGroupMsgMainInfo::getStopMsg,1)
               .set(QywxGroupMsgMainInfo::getModifiedDate,new Date())
               .set(QywxGroupMsgMainInfo::getModifiedBy,currentUserId)
               .set(QywxGroupMsgMainInfo::getReviseName,MybatisMateConfig.encrypt(firstName))
                .set(QywxGroupMsgMainInfo::getReviseTime,new Date())
                .eq(QywxGroupMsgMainInfo::getId,id));

        logService.insertOperationLog2(qywxGroupMsgMainInfo.getId(),
                "t_qywx_group_msg_main_info",
                1, GroupMsgOperationTypeEnum.STOP.getType(), "停止群发",
                null, null, "",
                null, currentUserId, firstName);
        return Result.buildSuccess("停发成功");
    }



    public GroupMsgInfoDTO getGroupMsgInfo(Long id) {
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class)
                .eq(QywxGroupMsgMainInfo::getIsDeleted, 0).eq(QywxGroupMsgMainInfo::getId, id));
        if (qywxGroupMsgMainInfo == null){
            log.error("未查询到当前群发内容");
            return null;
        }
        GroupMsgInfoDTO groupMsgBaseDTO = new GroupMsgInfoDTO();
        BeanUtils.copyProperties(qywxGroupMsgMainInfo,groupMsgBaseDTO);
        //region 查询公司权限配置
        List<QywxGroupCompanyMapping> qywxGroupCompanyMappingList = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .eq(QywxGroupCompanyMapping::getType, 1)
                .eq(QywxGroupCompanyMapping::getGroupMsgId, id));
        if (CollectionUtils.isNotEmpty(qywxGroupCompanyMappingList)){
            boolean result = qywxGroupCompanyMappingList.stream().anyMatch(company -> company.getCompanyMappingType().equals(-1));
            if (result){
                groupMsgBaseDTO.setCompanyMappingType(-1);
                qywxGroupCompanyMappingList.forEach(obj->{
                        obj.setCompanyName("全部");
                        obj.setCompanyId(-1L);
                });
            }else {
                groupMsgBaseDTO.setCompanyMappingType(0);
            }
            groupMsgBaseDTO.setGroupCompanyMappingDTOList(qywxGroupCompanyMappingList);
        }else {
            log.error("未查询到当前群发内容配置分公司权限");
        }
        //endregion

        //写一个select方法


        //region 查询群发内容信息
        if (ConfigTypeEnum.CONFIG_TYPE_ZERO.getConfigType().equals(groupMsgBaseDTO.getConfigType())){
            //本地配置
            List<QywxGroupContentConfig> contentConfigList = qywxGroupContentConfigMapper.selectList(Wrappers.lambdaQuery(QywxGroupContentConfig.class)
                    .eq(QywxGroupContentConfig::getIsDeleted, 0)
                    .eq(QywxGroupContentConfig::getGroupMsgId, id).orderByAsc(QywxGroupContentConfig::getSort));
            if (CollectionUtils.isNotEmpty(contentConfigList)){
                contentConfigList.stream().forEach(c -> {
                    if (c.getMsgType() == 3 ) {
                        if (StringUtils.isNotBlank(c.getMiniprogramParam()) && c.getMiniprogramParam().indexOf("=") != -1) {
                            String qrId = c.getMiniprogramParam().substring(c.getMiniprogramParam().indexOf("=") + 1);
                            if (StringUtils.isNotBlank(qrId)) {
                                if ("pages/memberRights/getRights/main".equals(c.getMiniprogramPage()) ||
                                        "pages/mainOther/VipMemberCard/main".equals(c.getMiniprogramPage()) ||
                                        "pages/mainOther/VipMemberCardOther/main".equals(c.getMiniprogramPage())) {
                                    //权益领取页、上海/北京会员卡领取
                                    DrainageQrInfo drainageQrInfo = drainageQrInfoDao.queryById(Long.valueOf(qrId));
                                    if (drainageQrInfo != null) {
                                        c.setQrId(Long.valueOf(qrId));
                                        c.setQrTitle(drainageQrInfo.getQrName());
                                        if (drainageQrInfo.getDrainageId() != null) {
                                            c.setParamId(drainageQrInfo.getDrainageId().toString());
                                            DrainageActivityDetailDto drainageActivityDetailDto = drainageActivityInfoDao.queryById(drainageQrInfo.getDrainageId());
                                            if (drainageActivityDetailDto != null) {
                                                c.setParamTitle(drainageActivityDetailDto.getName());
                                            }
                                        }
                                    }
                                } else if ("pages/pageTemplate/landingPage/main".equals(c.getMiniprogramPage()) ||
                                        "pages/activity/templateWebview".equals(c.getMiniprogramPage()) ||
                                        "pages/marketingPayment/index/main".equals(c.getMiniprogramPage()) ) {
                                    GetSingleImByIdInDTO imByIdInDTO = new GetSingleImByIdInDTO();
                                    imByIdInDTO.setId(Long.valueOf(qrId));
                                    GetSingleImByIdVO singleImByIdVO = marketingClient.getIntegrateById(imByIdInDTO).getData();
                                    if (singleImByIdVO != null) {
                                        c.setParamId(Long.valueOf(qrId).toString());
                                        c.setParamTitle(singleImByIdVO.getIntegrateTitle());
                                        if (CollectionUtils.isNotEmpty(singleImByIdVO.getRefContentTemplateList())) {
                                            List<Long> templateIds = singleImByIdVO.getRefContentTemplateList().stream().filter(x-> 1 == x.getType()).map(SimpleContentTemplateDTO::getId).collect(Collectors.toList());
                                            c.setTemplateIds(templateIds);
                                        }
                                    }
                                    if (StringUtils.isNotBlank(c.getMiniprogramContentTemplateId())){
                                        if (c.getMiniprogramContentTemplateId().indexOf("=") != -1) {
                                            String templateId = c.getMiniprogramContentTemplateId().substring(c.getMiniprogramContentTemplateId().indexOf("=") + 1);

                                            if (org.apache.commons.lang3.StringUtils.isNotBlank(templateId)) {
                                                if ("pages/marketingPayment/index/main".equals(c.getMiniprogramPage()) ){
                                                    String data = marketingClient.findByIdTitle(Long.valueOf(templateId)).getData();
                                                    if (data != null){
                                                        c.setQrId(Long.valueOf(templateId));
                                                        c.setQrTitle(data);
                                                    }
                                                }else {
                                                    FindContentTemplateByIdVO imContentTemplate = cmsClient.findContentTemplateById(Long.valueOf(templateId)).getData();
                                                    if (imContentTemplate != null) {
                                                        c.setQrId(imContentTemplate.getId());
                                                        c.setQrTitle(imContentTemplate.getContentTemplateTitle());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }else if ("pages/activityDetail/main".equals(c.getMiniprogramPage()) ||
                                        "pages/lottery/main".equals(c.getMiniprogramPage())  ||
                                        "pages/newPrize/main".equals(c.getMiniprogramPage())) {
                                    //分公司活动页
                                    String activityTitle = marketingClient.findByIdTitle(Long.valueOf(qrId)).getData();
                                    if (StringUtils.isNotBlank(activityTitle)){
                                        c.setParamId(qrId);
                                        c.setParamTitle(activityTitle);
                                    }
                                } else if ("pages/happyBBS/serviceDetail/main".equals(c.getMiniprogramPage())) {
                                    ServiceInfoDetailVO serviceInfoDetail = orgClient.findServiceInfoById(Long.valueOf(qrId)).getData();
                                    if (serviceInfoDetail != null && serviceInfoDetail.getServiceInfo() != null){
                                        c.setParamId(qrId);
                                        c.setParamTitle(serviceInfoDetail.getServiceInfo().getServiceName());
                                    }
                                } else if ("pages/activity/appt".equals(c.getMiniprogramPage())){
                                    Map<String, Object> changingDetail = toolsClient.getChangingDetailOpen(qrId).getData();
                                    if(changingDetail != null) {
                                        if (changingDetail.get("id") != null) {
                                            c.setParamId(changingDetail.get("id").toString());
                                        }
                                        if (changingDetail.get("name") != null) {
                                            c.setParamTitle(String.valueOf(changingDetail.get("name")));
                                        }
                                    }
                                }
                            }
                        }
//                        if ("pages/pageTemplate/landingPage/main".equals(c.getMiniprogramPage())) {
//                            if( c.getMiniprogramParam().contains("activityId=")) {
//                                if (c.getMiniprogramParam().indexOf("=") != -1) {
//                                    String qrId = c.getMiniprogramParam().substring(c.getMiniprogramParam().indexOf("=") + 1);
//                                    if (StringUtils.isNotBlank(qrId)) {
//                                        GetSingleImByIdInDTO imByIdInDTO = new GetSingleImByIdInDTO();
//                                        imByIdInDTO.setId(Long.valueOf(qrId));
//                                        GetSingleImByIdVO singleImByIdVO = marketingClient.getIntegrateById(imByIdInDTO).getData();
//                                        if (singleImByIdVO != null) {
//                                            if (CollectionUtils.isNotEmpty(singleImByIdVO.getRefContentTemplateList())) {
//                                                List<Long> templateIds = singleImByIdVO.getRefContentTemplateList().stream().map(s -> s.getId()).collect(Collectors.toList());
//                                                c.setTemplateIds(templateIds);
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        }
                    }
                });
                groupMsgBaseDTO.setGroupContentConfigDTOList(contentConfigList);
            }
        }else {
            MaterialDTO materialDTO = getMaterialDetailDto(id, 2);
            groupMsgBaseDTO.setMaterialContentConfigDTOList(materialDTO.getMaterialContentConfigDTOList());
            groupMsgBaseDTO.setMaterialContentDesc("已调用"+materialDTO.getCount()+"条内容，生效"+materialDTO.getEffectiveCount()+"条，失效"+materialDTO.getInvalidCount()+"条");
            groupMsgBaseDTO.setTreeId(materialDTO.getTreeId());
            groupMsgBaseDTO.setMaterialId(materialDTO.getMaterialId());
            groupMsgBaseDTO.setMaterialTitle(materialDTO.getTitle());
        }
        //endregion

        //region 生效员工
        List<QywxGroupEffectChargeMapping> qywxGroupEffectChargeMappings = queryEffectCharge(id, groupMsgBaseDTO);
        //endregion

        //region 查询群发好友
        if (SendMemberTypeEnum.SEND_MEMBER_TYPE_PART.getSendMemberType().equals(groupMsgBaseDTO.getSendMemberType())){
            QywxGroupSendCondition sendCondition = qywxGroupSendConditionMapper.selectOne(Wrappers.lambdaQuery(QywxGroupSendCondition.class)
                    .eq(QywxGroupSendCondition::getIsDeleted, 0)
                    .eq(QywxGroupSendCondition::getGroupMsgId, id));
            if (sendCondition!=null){
                GroupSendConditionDTO groupSendConditionDTO = new GroupSendConditionDTO();
                groupSendConditionDTO.setStartTime(sendCondition.getStartTime());
                groupSendConditionDTO.setEndTime(sendCondition.getEndTime());
                List<QywxGroupConditionExpression> expressionList = qywxGroupConditionExpressionMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionExpression.class)
                        .eq(QywxGroupConditionExpression::getIsDeleted, 0)
                        .eq(QywxGroupConditionExpression::getConditionId, sendCondition.getId()));
                if (CollectionUtils.isNotEmpty(expressionList)){
                    Optional<QywxGroupConditionExpression> includeOptional = expressionList.stream().filter(obj -> obj.getType().equals(1)).findFirst();
                    if (includeOptional.isPresent()){
                        QywxGroupConditionExpression includeExpression = includeOptional.get();
                        GroupConditionExpressionDTO includeUser  = new GroupConditionExpressionDTO();
                        includeUser.setExpressionType(includeExpression.getExpressionType());
                        List<QywxGroupConditionValue> qywxGroupConditionValueList = qywxGroupConditionValueMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionValue.class)
                                .eq(QywxGroupConditionValue::getIsDeleted, 0)
                                .eq(QywxGroupConditionValue::getConditionExpressionId, includeExpression.getId()));
                        if (CollectionUtils.isNotEmpty(qywxGroupConditionValueList)){
                            List<GroupConditionValueDTO> dtoList = Lists.newArrayList();
                            qywxGroupConditionValueList.forEach(obj->{
                                GroupConditionValueDTO value = new GroupConditionValueDTO();
                                BeanUtils.copyProperties(obj,value);
                                dtoList.add(value);
                            });
                            includeUser.setGroupConditionValueDTOList(dtoList);
                        }
                        groupSendConditionDTO.setIncludeUser(includeUser);
                    }
                    Optional<QywxGroupConditionExpression> excludeOptional = expressionList.stream().filter(obj -> obj.getType().equals(2)).findFirst();
                    if (excludeOptional.isPresent()){
                        QywxGroupConditionExpression excludeExpression = excludeOptional.get();
                        GroupConditionExpressionDTO excludeUser  = new GroupConditionExpressionDTO();
                        excludeUser.setExpressionType(excludeExpression.getExpressionType());
                        List<QywxGroupConditionValue> qywxGroupConditionValueList = qywxGroupConditionValueMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionValue.class)
                                .eq(QywxGroupConditionValue::getIsDeleted, 0)
                                .eq(QywxGroupConditionValue::getConditionExpressionId, excludeExpression.getId()));
                        if (CollectionUtils.isNotEmpty(qywxGroupConditionValueList)){
                            List<GroupConditionValueDTO> dtoList = Lists.newArrayList();
                            qywxGroupConditionValueList.forEach(obj->{
                                GroupConditionValueDTO value = new GroupConditionValueDTO();
                                BeanUtils.copyProperties(obj,value);
                                dtoList.add(value);
                            });
                            excludeUser.setGroupConditionValueDTOList(dtoList);
                        }
                        groupSendConditionDTO.setExcludeUser(excludeUser);
                    }
                }
                groupMsgBaseDTO.setGroupSendConditionDTO(groupSendConditionDTO);
            }
            List<TQywxGroupMsgVillageMapping> villageMappings = villageMappingDao.list(Wrappers.lambdaQuery(TQywxGroupMsgVillageMapping.class)
                    .eq(TQywxGroupMsgVillageMapping::getIsDeleted, 0L)
                    .eq(TQywxGroupMsgVillageMapping::getGroupMsgId, id));
            if (CollectionUtils.isNotEmpty(villageMappings)){
                groupMsgBaseDTO.setVillageIds(villageMappings);
            }
        }

        List<String> userId = qywxGroupEffectChargeMappings.parallelStream()
                .map(QywxGroupEffectChargeMapping::getUserId).limit(2).collect(Collectors.toList());

        List<String> userRelationName = qywxCutomerUserRelationMapper.findUserRelationName(userId);
        if (CollectionUtils.isNotEmpty(userRelationName)){
            List<String> nameList = Lists.newArrayList();
            userRelationName.forEach(name->{
                nameList.add(MybatisMateConfig.decrypt(name));
            });
            groupMsgBaseDTO.setCustomerNameList(nameList);
        }

        if (Objects.equals(qywxGroupMsgMainInfo.getSecondMsgFlag(),1)){
            GroupMsgDataResp groupMsgDataResp = qywxGroupUserSendLogMapper.queryGroupMsgData(qywxGroupMsgMainInfo.getFullGroupMsgId());
            //未打开链接 = 顾客总数-打开链接顾客数
            groupMsgBaseDTO.setUnOpenLinkCustomerNum(groupMsgDataResp.getSendCustomerNum());
            //未参与人数 = 打开链接顾客数-参与顾客数
            groupMsgBaseDTO.setCustomerNotJoinNum(groupMsgDataResp.getOpenLinkCustomerNum());
            //调用cdp查询当前群发打开链接顾客数，参与顾客数量
            TQywxGroupCompleteDataLog completeGroupMsgData = tQywxGroupCompleteDataLogDao.getGroupMsgData(qywxGroupMsgMainInfo.getFullGroupMsgId());
            if (completeGroupMsgData != null ){
                groupMsgBaseDTO.setNotOpenChargeNum(completeGroupMsgData.getChargeSendNotOpenCount());
                groupMsgBaseDTO.setNotJoinChargeNum(completeGroupMsgData.getChargeOpenNotJoin());
                //未打开链接 = 顾客总数-打开链接顾客数
                groupMsgBaseDTO.setUnOpenLinkCustomerNum(groupMsgDataResp.getSendCustomerNum()-(completeGroupMsgData.getOpenCount()==null?0:completeGroupMsgData.getOpenCount()));
                //打开链接人数
                groupMsgBaseDTO.setOpenLinkCustomerNum(completeGroupMsgData.getOpenCount());
                //未参与人数 = 打开链接顾客数-参与顾客数
                groupMsgBaseDTO.setCustomerNotJoinNum((completeGroupMsgData.getOpenCount()==null?0:completeGroupMsgData.getOpenCount()) - (completeGroupMsgData.getJoinCount()==null?0:completeGroupMsgData.getJoinCount()));
                //参与顾客数
                groupMsgBaseDTO.setCustomerJoinNum(completeGroupMsgData.getJoinCount()==null?0:completeGroupMsgData.getJoinCount());

                groupMsgBaseDTO.setCompleteGroupMsgData(completeGroupMsgData.getCompleteTime());
            }
        }
        //endregion
        return groupMsgBaseDTO;
    }

    public MaterialDTO getMaterialDetailDto(Long id,Integer sourceType){
        MaterialDTO materialDTO = new MaterialDTO();
        //从聊天树获取
        TCustomerCommonMaterialMapping materialMapping = materialMappingDao.selectOne(Wrappers.lambdaQuery(TCustomerCommonMaterialMapping.class)
                .eq(TCustomerCommonMaterialMapping::getIsDeleted, 0)
                .eq(TCustomerCommonMaterialMapping::getSourceId, id).eq(TCustomerCommonMaterialMapping::getSourceType,sourceType));
        if (materialMapping!=null){
            materialDTO.setTreeId(materialMapping.getTreeId());
            materialDTO.setMaterialId(materialMapping.getMaterialId());
            //调用cms查询树名称跟内容组名称
            Result<List<MaterialGroupContentDTO>> result = cmsClient.getMaterialContentUpById(materialMapping.getMaterialId().intValue());
            if (result.getSuccess()&&CollectionUtils.isNotEmpty(result.getData())){
                materialDTO.setTitle(result.getData().get(0).getTitle());
                List<TCustomerCommonMaterialContentMapping> contentMappings = materialContentMappingDao.selectList(Wrappers.lambdaQuery(TCustomerCommonMaterialContentMapping.class)
                        .eq(TCustomerCommonMaterialContentMapping::getMaterialId, materialMapping.getMaterialId())
                        .eq(TCustomerCommonMaterialContentMapping::getSourceType, sourceType)
                        .eq(TCustomerCommonMaterialContentMapping::getSourceId, id)
                        .eq(TCustomerCommonMaterialContentMapping::getIsDeleted, 0));
                Integer effectiveNum = 0;
                if (CollectionUtils.isNotEmpty(contentMappings)){
                    Map<Integer,TCustomerCommonMaterialContentMapping> materialContentMap = contentMappings.stream()
                            .collect(Collectors.toMap(TCustomerCommonMaterialContentMapping::getMaterialContentId, Function.identity()));
                    List<MaterialGroupContentDTO> materialGroupContentDTOS = Lists.newArrayList();
                    //生效条数

                    for (MaterialGroupContentDTO groupContentDTO:result.getData()){
                        MaterialGroupContentDTO materialGroupContentDTO = new MaterialGroupContentDTO();
                        List<MaterialContentDetailDTO> materialContentDetailDTOS = Lists.newArrayList();
                        groupContentDTO.getContentList().forEach(dto->{
                            if (materialContentMap.containsKey(dto.getMaterialContentId())){
                                MaterialContentDetailDTO materialContentDetailDTO = new MaterialContentDetailDTO();
                                BeanUtils.copyProperties(dto,materialContentDetailDTO);
                                if (StringUtils.isNotBlank(materialContentMap.get(dto.getMaterialContentId()).getPageTitle())){
                                    materialContentDetailDTO.setLandingPageTitle(materialContentMap.get(dto.getMaterialContentId()).getPageTitle());
                                }
                                if (StringUtils.isNotBlank(materialContentMap.get(dto.getMaterialContentId()).getPageDescription())){
                                    materialContentDetailDTO.setContent(materialContentMap.get(dto.getMaterialContentId()).getPageDescription());
                                }
                                if (StringUtils.isNotBlank(materialContentMap.get(dto.getMaterialContentId()).getPageDescription())){
                                    materialContentDetailDTO.setLinkDescription(materialContentMap.get(dto.getMaterialContentId()).getPageDescription());
                                }
                                if (materialContentMap.get(dto.getMaterialContentId()).getSort()!=null){
                                    materialContentDetailDTO.setSort(materialContentMap.get(dto.getMaterialContentId()).getSort());
                                }
                                materialContentDetailDTOS.add(materialContentDetailDTO);
                            }
                        });
                        if (CollectionUtils.isNotEmpty(materialContentDetailDTOS)){
                            BeanUtils.copyProperties(groupContentDTO,materialGroupContentDTO);
                            materialGroupContentDTO.setContentList(materialContentDetailDTOS);
                            materialGroupContentDTOS.add(materialGroupContentDTO);
                            effectiveNum+=materialContentDetailDTOS.size();
                        }
                    }
                    materialDTO.setMaterialContentConfigDTOList(materialGroupContentDTOS);
                    materialDTO.setCount(contentMappings.size());
                }
                materialDTO.setEffectiveCount(effectiveNum);
                materialDTO.setInvalidCount(materialDTO.getCount()-effectiveNum);
                return materialDTO;
            }
        }
        return materialDTO;
    }

    private List<QywxGroupEffectChargeMapping> queryEffectCharge(Long groupMsgId,GroupMsgInfoDTO groupMsgInfoDTO){
        //region 生效员工
        // 从组织选择人员
        List<QywxGroupChannelCategoryMapping> channelCategoryMappings = qywxGroupChannelCategoryMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupChannelCategoryMapping.class)
                .eq(QywxGroupChannelCategoryMapping::getIsDeleted, 0)
                .eq(QywxGroupChannelCategoryMapping::getGroupMsgId, groupMsgId));
        List<QywxGroupCompanyMapping> companyMappingList = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .eq(QywxGroupCompanyMapping::getType,2)
                .eq(QywxGroupCompanyMapping::getGroupMsgId, groupMsgId));
        List<QywxGroupStoreMapping> storeMappingList = qywxGroupStoreMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStoreMapping.class)
                .eq(QywxGroupStoreMapping::getIsDeleted, 0)
                .eq(QywxGroupStoreMapping::getGroupMsgId, groupMsgId));
        List<QywxGroupStationMapping> stationMappingList = qywxGroupStationMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStationMapping.class)
                .eq(QywxGroupStationMapping::getIsDeleted, 0)
                .eq(QywxGroupStationMapping::getGroupMsgId, groupMsgId));
        OrganizeMappingInfoDTO organizeMappingDTO = new OrganizeMappingInfoDTO();
        if (CollectionUtils.isNotEmpty(channelCategoryMappings)||CollectionUtils.isNotEmpty(companyMappingList)
                ||CollectionUtils.isNotEmpty(storeMappingList)||CollectionUtils.isNotEmpty(stationMappingList)){
            organizeMappingDTO.setGroupChannelCategoryMappingList(channelCategoryMappings);
            organizeMappingDTO.setGroupCompanyMappingMappingList(companyMappingList);
            organizeMappingDTO.setGroupStoreMappingDTOList(storeMappingList);
            organizeMappingDTO.setGroupStationMappingDTOList(stationMappingList);
            groupMsgInfoDTO.setOrganizeMappingDTO(organizeMappingDTO);
        }
        // 生效员工
        List<QywxGroupEffectChargeMapping> effectChargeMappingList = qywxGroupEffectChargeMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupEffectChargeMapping.class)
                .eq(QywxGroupEffectChargeMapping::getIsDeleted, 0)
                .eq(QywxGroupEffectChargeMapping::getGroupMsgId, groupMsgId));
        List<QywxGroupEffectChargeMapping> organizeChargeList = effectChargeMappingList.stream().filter(obj -> obj.getType().equals(0)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(organizeChargeList)){
            organizeChargeList.forEach(obj->{
                obj.setChargeUserName(MybatisMateConfig.decrypt(obj.getChargeUserName()));
                obj.setChargePhone(MybatisMateConfig.decrypt(obj.getChargePhone()));
            });
            groupMsgInfoDTO.setGroupOrganizeChargeMappingList(organizeChargeList);
        }

        List<QywxGroupEffectChargeMapping> chargeList = effectChargeMappingList.stream().filter(obj -> obj.getType().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chargeList)){
            chargeList.forEach(obj->{
                obj.setChargeUserName(MybatisMateConfig.decrypt(obj.getChargeUserName()));
                obj.setChargePhone(MybatisMateConfig.decrypt(obj.getChargePhone()));
            });
            groupMsgInfoDTO.setGroupChargeMappingDTOList(chargeList);
        }
        //endregion
       return effectChargeMappingList;
    }

    public OrganizeMappingInfoDTO getGroupOrganizationMapping(Long id){
        List<QywxGroupChannelCategoryMapping> channelCategoryMappings = qywxGroupChannelCategoryMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupChannelCategoryMapping.class)
                .eq(QywxGroupChannelCategoryMapping::getIsDeleted, 0)
                .eq(QywxGroupChannelCategoryMapping::getGroupMsgId, id));
        List<QywxGroupCompanyMapping> companyMappingList = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .eq(QywxGroupCompanyMapping::getType,2)
                .eq(QywxGroupCompanyMapping::getGroupMsgId, id));
        List<QywxGroupStoreMapping> storeMappingList = qywxGroupStoreMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStoreMapping.class)
                .eq(QywxGroupStoreMapping::getIsDeleted, 0)
                .eq(QywxGroupStoreMapping::getGroupMsgId, id));
        List<QywxGroupStationMapping> stationMappingList = qywxGroupStationMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupStationMapping.class)
                .eq(QywxGroupStationMapping::getIsDeleted, 0)
                .eq(QywxGroupStationMapping::getGroupMsgId, id));
        OrganizeMappingInfoDTO organizeMappingDTO = new OrganizeMappingInfoDTO();
        if (CollectionUtils.isNotEmpty(channelCategoryMappings)||CollectionUtils.isNotEmpty(companyMappingList)
                ||CollectionUtils.isNotEmpty(storeMappingList)||CollectionUtils.isNotEmpty(stationMappingList)){
            organizeMappingDTO.setGroupChannelCategoryMappingList(channelCategoryMappings);
            organizeMappingDTO.setGroupCompanyMappingMappingList(companyMappingList);
            organizeMappingDTO.setGroupStoreMappingDTOList(storeMappingList);
            organizeMappingDTO.setGroupStationMappingDTOList(stationMappingList);
        }
        return organizeMappingDTO;
    }

    public List<QywxGroupChargeMapping> getGroupChargeMapping(Long id){
        List<QywxGroupChargeMapping> groupChargeMappingList = qywxGroupChargeMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupChargeMapping.class)
                .eq(QywxGroupChargeMapping::getIsDeleted, 0)
                .eq(QywxGroupChargeMapping::getGroupMsgId, id));
        if (CollectionUtils.isNotEmpty(groupChargeMappingList)){
            //指定人员
            groupChargeMappingList.forEach(obj->{
                obj.setChargeUserName(MybatisMateConfig.decrypt(obj.getChargeUserName()));
                obj.setChargePhone(MybatisMateConfig.decrypt(obj.getChargePhone()));
            });
        }
        return groupChargeMappingList;
    }

    public GroupSendConditionDTO getGroupSendCondition(Long id){
        QywxGroupSendCondition sendCondition = qywxGroupSendConditionMapper.selectOne(Wrappers.lambdaQuery(QywxGroupSendCondition.class)
                .eq(QywxGroupSendCondition::getIsDeleted, 0)
                .eq(QywxGroupSendCondition::getGroupMsgId, id));
        GroupSendConditionDTO groupSendConditionDTO = new GroupSendConditionDTO();
        if (sendCondition!=null){
            groupSendConditionDTO.setStartTime(sendCondition.getStartTime());
            groupSendConditionDTO.setEndTime(sendCondition.getEndTime());
            List<QywxGroupConditionExpression> qywxGroupConditionExpressions = qywxGroupConditionExpressionMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionExpression.class)
                    .eq(QywxGroupConditionExpression::getIsDeleted, 0)
                    .eq(QywxGroupConditionExpression::getConditionId, sendCondition.getId()));
            if (CollectionUtils.isNotEmpty(qywxGroupConditionExpressions)){
                List<QywxGroupConditionExpression> friendsExpressionDTOList = qywxGroupConditionExpressions.stream()
                        .filter(obj -> obj.getType().equals(1)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(friendsExpressionDTOList)){
                    GroupConditionExpressionDTO dto = new GroupConditionExpressionDTO();
                    dto.setExpressionType(friendsExpressionDTOList.get(0).getExpressionType());
                    List<Long> idList = friendsExpressionDTOList.stream().map(QywxGroupConditionExpression::getId).collect(Collectors.toList());
                    List<QywxGroupConditionValue> qywxGroupConditionValueList = qywxGroupConditionValueMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionValue.class)
                            .eq(QywxGroupConditionValue::getIsDeleted, 0)
                            .in(QywxGroupConditionValue::getConditionExpressionId, idList));
                    if (CollectionUtils.isNotEmpty(qywxGroupConditionValueList)){
                        List<GroupConditionValueDTO> dtoList = Lists.newArrayList();
                        qywxGroupConditionValueList.forEach(obj->{
                            GroupConditionValueDTO value = new GroupConditionValueDTO();
                            BeanUtils.copyProperties(obj,value);
                            dtoList.add(value);
                        });
                        dto.setGroupConditionValueDTOList(dtoList);
                    }
                    groupSendConditionDTO.setIncludeUser(dto);
                }
                List<QywxGroupConditionExpression> excludeExpressionDTOList = qywxGroupConditionExpressions.stream()
                        .filter(obj -> obj.getType().equals(2)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(excludeExpressionDTOList)){
                    GroupConditionExpressionDTO dto = new GroupConditionExpressionDTO();
                    dto.setExpressionType(excludeExpressionDTOList.get(0).getExpressionType());
                    List<Long> idList = excludeExpressionDTOList.stream().map(QywxGroupConditionExpression::getId).collect(Collectors.toList());
                    List<QywxGroupConditionValue> qywxGroupConditionValueList = qywxGroupConditionValueMapper.selectList(Wrappers.lambdaQuery(QywxGroupConditionValue.class)
                            .eq(QywxGroupConditionValue::getIsDeleted, 0)
                            .in(QywxGroupConditionValue::getConditionExpressionId, idList));
                    if (CollectionUtils.isNotEmpty(qywxGroupConditionValueList)){
                        List<GroupConditionValueDTO> dtoList = Lists.newArrayList();
                        qywxGroupConditionValueList.forEach(obj->{
                            GroupConditionValueDTO value = new GroupConditionValueDTO();
                            BeanUtils.copyProperties(obj,value);
                            dtoList.add(value);
                        });
                        dto.setGroupConditionValueDTOList(dtoList);
                    }
                    groupSendConditionDTO.setExcludeUser(dto);
                }
            }
        }
        return groupSendConditionDTO;
    }

    public PageInfo<GroupMsgListResponse> getGroupMsgList(GetGroupMsgListDTO getGroupMsgListDTO) {
        PageInfo<GroupMsgListResponse> pageInfo = new PageInfo<>(getGroupMsgListDTO.getPageNum(),getGroupMsgListDTO.getPageSize());
        try {
            List<Long> userCompanyIds = userAuthorConfig.queryCompanyAuthorIdList(1);
            if (CollectionUtils.isNotEmpty(userCompanyIds)){
                if (CollectionUtils.isNotEmpty(getGroupMsgListDTO.getCompanyList())){
                    getGroupMsgListDTO.getCompanyList().retainAll(userCompanyIds);
                }else {
                    getGroupMsgListDTO.setCompanyList(userCompanyIds);
                }
                if (CollectionUtils.isEmpty(getGroupMsgListDTO.getCompanyList())){
                    return pageInfo;
                }
            }
        }catch (Exception e){
            return pageInfo;
        }
        int count = groupMsgMainInfoMapper.selectQywxGroupMsgMainInfoCount(getGroupMsgListDTO);
        if (count<1){
            return pageInfo;
        }
        pageInfo.setTotal(count);
        List<QywxGroupMsgMainInfo> qywxGroupMsgMainInfoList =
                groupMsgMainInfoMapper.selectQywxGroupMsgMainInfoList(pageInfo, getGroupMsgListDTO);
        List<GroupMsgListResponse> responseList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(qywxGroupMsgMainInfoList)) {

            //20240912 群发列表配置了人群包，通过人群包id 判断该人群包是否已删除
            List<Long> ids = qywxGroupMsgMainInfoList.stream()
                    .map(QywxGroupMsgMainInfo::getCdpGroupId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Long> notDelCdpGroupIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(ids)){
                //查询CDP 判断该人群包是否已删除,批量查询群组信息
/*                CdpGroupByIdSDTO cdpGroupByIdSDTO = new CdpGroupByIdSDTO();
                cdpGroupByIdSDTO.setIds(cdpGroupIdList);*/
/*                Result<List<CdpGroupDTO>> cdpGroupsResult = cdpClient.getCdpGroupsByIds(ids);
                if (cdpGroupsResult.getSuccess()&&CollectionUtils.isNotEmpty(cdpGroupsResult.getData())){
                    notDelCdpGroupIdList = cdpGroupsResult.getData().stream().map(CdpGroupDTO::getId).collect(Collectors.toList());
                }*/
            }

            List<String> userIds = qywxGroupMsgMainInfoList.stream().map(QywxGroupMsgMainInfo::getCreatedBy).distinct().collect(Collectors.toList());
            Result<List<UserEntityExtend>> userEntityExtendByUserIds = userClient.findUserEntityExtendByUserIds(userIds);
            List<UserEntityExtend> userEntityExtends = Lists.newArrayList();
            if (userEntityExtendByUserIds.getSuccess()&&CollectionUtils.isNotEmpty(userEntityExtendByUserIds.getData())){
                userEntityExtends = userEntityExtendByUserIds.getData();
            }
            for (QywxGroupMsgMainInfo groupMsg: qywxGroupMsgMainInfoList) {
                GroupMsgListResponse groupMsgListResponse = new GroupMsgListResponse();
                BeanUtils.copyProperties(groupMsg, groupMsgListResponse);
                groupMsgListResponse.setReviseName(MybatisMateConfig.decrypt(groupMsg.getReviseName()));
                if (CollectionUtils.isNotEmpty(userEntityExtends)){
                    Optional<String> first = userEntityExtends.stream().filter(u -> Objects.equals(u.getUserEntityId(),groupMsg.getCreatedBy())).map(UserEntityExtend::getFirstName).findFirst();
                    first.ifPresent(groupMsgListResponse::setCreatedName);
                }
                if (groupMsg.getConfigType().equals(1)){
                    groupMsgListResponse.setConfigType("本地配置内容");
                }else {
                    groupMsgListResponse.setConfigType("从聊天分类树选取");
                }
                if (groupMsg.getCdpGroupId()!=null){
                    if (CollectionUtils.isEmpty(notDelCdpGroupIdList)){
                        groupMsgListResponse.setPeoplePackage(1);
                    }else if (!notDelCdpGroupIdList.contains(groupMsg.getCdpGroupId())){
                        groupMsgListResponse.setPeoplePackage(1);
                    }
                }
                responseList.add(groupMsgListResponse);
            }
            List<Long> groupMsgIdList = qywxGroupMsgMainInfoList.stream()
                    .map(QywxGroupMsgMainInfo::getId).collect(Collectors.toList());
            //region 分公司权限获取
            List<QywxGroupCompanyMapping> qywxGroupCompanyMappingList = qywxGroupCompanyMappingMapper
                    .selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                            .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                            .eq(QywxGroupCompanyMapping::getType, 1)
                            .in(QywxGroupCompanyMapping::getGroupMsgId, groupMsgIdList));
            Map<Long, List<QywxGroupCompanyMapping>> companyMap = qywxGroupCompanyMappingList.stream()
                    .filter(company -> company.getCompanyMappingType().equals(0))
                    .collect(Collectors.groupingBy(QywxGroupCompanyMapping::getGroupMsgId));
            if (CollectionUtils.isNotEmpty(companyMap)) {
                responseList.forEach(resp -> {
                    List<QywxGroupCompanyMapping> companyMappingList = companyMap.get(resp.getId());
                    List<GroupCompanyMappingDTO> companyMappingDtoList = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(companyMappingList)) {
                        companyMappingList.forEach(mapping -> {
                            GroupCompanyMappingDTO dto = new GroupCompanyMappingDTO();
                            BeanUtils.copyProperties(mapping, dto);
                            companyMappingDtoList.add(dto);
                        });
                        resp.setCompanyMappingType(companyMappingList.get(0).getCompanyMappingType());
                        resp.setGroupCompanyMappingDTOList(companyMappingDtoList);
                    }else {
                        resp.setCompanyMappingType(-1);
                        resp.setGroupCompanyMappingDTOList(Lists.newArrayList());
                    }
                });
            }else {
                responseList.forEach(obj->{
                    obj.setCompanyMappingType(-1);
                    obj.setGroupCompanyMappingDTOList(Lists.newArrayList());
                });
            }
            //endregion

            List<UserSendStatistics> userSendLogList = qywxGroupUserSendLogMapper.getUserSendLogList(groupMsgIdList);

            List<SendResultLogDto> sendResultLogDtos = qywxGroupSendResultLogMapper.selectAll(groupMsgIdList);


            for (GroupMsgListResponse resp : responseList) {
                //群发内容
                List<QywxGroupContentConfig> qywxGroupContentConfigList = qywxGroupContentConfigMapper.selectList(Wrappers.lambdaQuery(QywxGroupContentConfig.class)
                        .eq(QywxGroupContentConfig::getGroupMsgId, resp.getId())
                        .eq(QywxGroupContentConfig::getIsDeleted, 0)
                        .eq(QywxGroupContentConfig::getMsgType, 3));
                if (CollectionUtils.isNotEmpty(qywxGroupContentConfigList)){
                    Set<String> miniprogramPage = qywxGroupContentConfigList.stream().map(QywxGroupContentConfig::getMiniprogramPage).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(miniprogramPage)){
                        if (miniprogramPage.stream().filter(BLANK_PAGE::contains).findAny().isEmpty()) {
                            resp.setActivityUrl(0);
                        } else {
                            resp.setActivityUrl(1);
                        }
                    }
                }

                //region 生效员工获取
                //Integer chargeCount = effectChargeMap.get(resp.getId());
                List<String> userIdList = Lists.newArrayList();
                List<QywxGroupEffectChargeMapping> effectChargeMappingList = qywxGroupEffectChargeMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupEffectChargeMapping.class)
                        .eq(QywxGroupEffectChargeMapping::getIsDeleted, 0)
                        .eq(QywxGroupEffectChargeMapping::getGroupMsgId, resp.getId())
                        .groupBy(QywxGroupEffectChargeMapping::getChargeUserName)
                        .last("limit 2"));
                Set<String> set = Sets.newHashSet();
                if (CollectionUtils.isNotEmpty(effectChargeMappingList)) {
                    List<String> chargeUserName = effectChargeMappingList.stream().map(QywxGroupEffectChargeMapping::getChargeUserName).collect(Collectors.toList());
                    chargeUserName.forEach(name -> {
                        set.add(MybatisMateConfig.decrypt(name));
                    });
                    userIdList = effectChargeMappingList.stream().map(QywxGroupEffectChargeMapping::getUserId).distinct().collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(set)){
                    if (resp.getChargeCount()!=null&&resp.getChargeCount()>2){
                        resp.setEffectChargeMappingDesc(String.join("、",set)+"...等"+resp.getChargeCount()+"人");
                    }else {
                        resp.setEffectChargeMappingDesc(String.join("、",set)+"，"+resp.getChargeCount()+"人");
                    }
                }
                //endregion

                //region 好友列表获取
                Set<String> setCustomer = Sets.newHashSet();
                List<String> userId = effectChargeMappingList.stream().map(QywxGroupEffectChargeMapping::getUserId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userId)){
                    List<String> userRelationName = qywxCutomerUserRelationMapper.findUserRelationName(userId);
                    if (CollectionUtils.isNotEmpty(userRelationName)){
                        userRelationName.forEach(name->{
                            setCustomer.add(MybatisMateConfig.decrypt(name));
                        });
                    }
                }

                if (CollectionUtils.isNotEmpty(setCustomer)){
                    if (resp.getCustomerCount()!=null&&resp.getCustomerCount()>2){
                        resp.setEffectCustomerMappingDesc(String.join("、",setCustomer)+"...等"+resp.getCustomerCount()+"人");
                    }else {
                        resp.setEffectCustomerMappingDesc(String.join("、",setCustomer)+"，"+resp.getCustomerCount()+"人");
                    }
                }
                //endregion

                //region 统计数据
                if (CollectionUtils.isNotEmpty(userSendLogList)) {
                    Optional<UserSendStatistics> first = userSendLogList.stream().filter(log -> log.getGroupMsgId().equals(resp.getId())).findFirst();
                    if (first.isPresent()){
                        resp.setNoticeUserCount(first.get().getSendCount()+first.get().getUpSendCount());
                        resp.setSendCount(first.get().getSendCount());
                        resp.setUnsentCount(first.get().getUpSendCount());
                    }
                }

                if (CollectionUtils.isNotEmpty(sendResultLogDtos)) {
                    Optional<SendResultLogDto> first = sendResultLogDtos.stream().filter(log -> log.getGroupMsgId().equals(resp.getId())).findFirst();
                    if (first.isPresent()){
                        resp.setSendCustomerCount(first.get().getSendCount());
                    }
                }
                if (resp.getSendCount()!=null&&resp.getSendCount()!=0&&resp.getNoticeUserCount()!=null&&resp.getNoticeUserCount()!=0){
                    double v = Double.valueOf(resp.getSendCount()) / Double.valueOf(resp.getNoticeUserCount());
                    BigDecimal multiply = new BigDecimal(v).multiply(BigDecimal.valueOf(100));
                    resp.setChargeCoverageRate(multiply.setScale(2,RoundingMode.HALF_UP)+"%");
                }else {
                    resp.setChargeCoverageRate("0%");
                }

                if (resp.getSendCustomerCount()!=null&&resp.getSendCustomerCount()!=0&&resp.getCustomerCount()!=null&&resp.getCustomerCount()!=0){
                    double v = Double.valueOf(resp.getSendCustomerCount()) / Double.valueOf(resp.getCustomerCount());
                    BigDecimal multiply = new BigDecimal(v).multiply(BigDecimal.valueOf(100));
                    resp.setCustomerCoverageRate(multiply.setScale(2,RoundingMode.HALF_UP)+"%");
                }else {
                    resp.setCustomerCoverageRate("0%");
                }
                //endregion

                //查询cdp顾客打开人数，顾客参与人数
                if (Objects.equals(getGroupMsgListDTO.getSecondMsgFlag(),1)){
                    QueryInsightUserCountInDTO queryInsightUserCountInDTO = new QueryInsightUserCountInDTO();
                    queryInsightUserCountInDTO.setUserIds(userIdList);
                    queryInsightUserCountInDTO.setSubPushId(resp.getId());
                    queryInsightUserCountInDTO.setPushId(resp.getFullGroupMsgId());
                    Result<List<QueryInsightUserCountOutDTO>> pushInsightUserCountResult = cdpClient.getPushInsightUserCount(queryInsightUserCountInDTO);
                    if (pushInsightUserCountResult.getSuccess()&&CollectionUtils.isNotEmpty(pushInsightUserCountResult.getData())){
                        resp.setOpenLinkCustomerNum(pushInsightUserCountResult.getData().stream().filter(Objects::nonNull).filter(item -> item.getOpen() > 0).count());
                        resp.setCustomerJoinNum(pushInsightUserCountResult.getData().stream().filter(Objects::nonNull).filter(item -> item.getJoin() > 0).count());
                    }
                }
            }

        }
        pageInfo.setRecords(responseList);
        return pageInfo;
    }

    @Transactional
    public Result upGroupMsgStatus(Long id, Integer status) {
        String currentUserId = userAuthorUtils.getCurrentUserId();
        String firstName = userAuthorUtils.getUserAuthor().getFirstName();
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class)
                .eq(QywxGroupMsgMainInfo::getIsDeleted, 0)
                .eq(QywxGroupMsgMainInfo::getId, id));
        if (qywxGroupMsgMainInfo.getStatus().equals(status)) {
            log.info("无需更改，状态一致");
            return Result.buildSuccess("修改成功");
        }
        Integer canSendFlag = qywxGroupMsgMainInfo.getCanSendFlag();
        if (!qywxGroupMsgMainInfo.getCanSendFlag().equals(2)){
            canSendFlag = 1;
            if (status.equals(0)){
                canSendFlag=0;
            }
            if (qywxGroupMsgMainInfo.getSendType().equals(1)&&qywxGroupMsgMainInfo.getSendTime().before(new Date())){
                canSendFlag=0;
            }
        }
        groupMsgMainInfoMapper.update(null, Wrappers.lambdaUpdate(QywxGroupMsgMainInfo.class)
                .set(QywxGroupMsgMainInfo::getStatus, status)
                .set(QywxGroupMsgMainInfo::getModifiedBy, currentUserId)
                .set(QywxGroupMsgMainInfo::getModifiedDate, new Date())
                .set(QywxGroupMsgMainInfo::getReviseName, MybatisMateConfig.encrypt(firstName))
                .set(QywxGroupMsgMainInfo::getReviseTime, new Date())
                .set(QywxGroupMsgMainInfo::getCanSendFlag,canSendFlag)
                .eq(QywxGroupMsgMainInfo::getId, id));
        if (qywxGroupMsgMainInfo.getSendType().equals(0)&&status.equals(1)){
            log.info("发送群发");
            sendMsgCommonService.sendGroupMsgEntry(id);
        }
        String str = fieldStrDispose(logService.getStatusName(status), logService.getStatusName(qywxGroupMsgMainInfo.getStatus()), "状态", "");
        if (StringUtils.isNotBlank(str)){
            logService.insertOperationLog2(qywxGroupMsgMainInfo.getId(),
                    "t_qywx_group_msg_main_info",
                    1, GroupMsgOperationTypeEnum.UPDATE_STATUS.getType(), "群发状态",
                    null, null, str,
                    null, currentUserId, firstName);
        }
        return Result.buildSuccess("修改状态成功");
    }

    @Transactional
    public Result delGroupMsg(Long id) {
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class).eq(QywxGroupMsgMainInfo::getIsDeleted, 0).eq(QywxGroupMsgMainInfo::getId, id));
        if (qywxGroupMsgMainInfo == null) {
            return Result.buildSuccess("删除成功");
        }
        String currentUserId = userAuthorUtils.getCurrentUserId();
        String firstName = userAuthorUtils.getUserAuthor().getFirstName();
        //region 删除主表
        groupMsgMainInfoMapper.update(null, Wrappers.lambdaUpdate(QywxGroupMsgMainInfo.class).set(QywxGroupMsgMainInfo::getIsDeleted, 1).set(QywxGroupMsgMainInfo::getModifiedBy, currentUserId).set(QywxGroupMsgMainInfo::getModifiedDate, new Date()).set(QywxGroupMsgMainInfo::getReviseName, MybatisMateConfig.encrypt(firstName)).set(QywxGroupMsgMainInfo::getReviseTime, new Date()).eq(QywxGroupMsgMainInfo::getId, id));
        //endregion
        //region 删除公司配置表
        qywxGroupCompanyMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupCompanyMapping.class)
                .set(QywxGroupCompanyMapping::getIsDeleted, 1)
                .set(QywxGroupCompanyMapping::getModifiedBy, currentUserId)
                .set(QywxGroupCompanyMapping::getModifiedDate, new Date())
                .eq(QywxGroupCompanyMapping::getGroupMsgId, id));
        //endregion
        //region 删除员工配置表
        qywxGroupChargeMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupChargeMapping.class).set(QywxGroupChargeMapping::getIsDeleted, 1)
                .set(QywxGroupChargeMapping::getModifiedBy, currentUserId)
                .set(QywxGroupChargeMapping::getModifiedDate, new Date())
                .eq(QywxGroupChargeMapping::getGroupMsgId, id));
        qywxGroupChannelCategoryMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupChannelCategoryMapping.class).set(QywxGroupChannelCategoryMapping::getIsDeleted, 1).set(QywxGroupChannelCategoryMapping::getModifiedBy, currentUserId).set(QywxGroupChannelCategoryMapping::getModifiedDate, new Date()).eq(QywxGroupChannelCategoryMapping::getGroupMsgId, id));
        qywxGroupEffectChargeMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupEffectChargeMapping.class).set(QywxGroupEffectChargeMapping::getIsDeleted, 1).set(QywxGroupEffectChargeMapping::getModifiedBy, currentUserId).set(QywxGroupEffectChargeMapping::getModifiedDate, new Date()).eq(QywxGroupEffectChargeMapping::getGroupMsgId, id));
        qywxGroupStationMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupStationMapping.class).set(QywxGroupStationMapping::getIsDeleted, 1).set(QywxGroupStationMapping::getModifiedBy, currentUserId).set(QywxGroupStationMapping::getModifiedDate, new Date()).eq(QywxGroupStationMapping::getGroupMsgId, id));
        qywxGroupStoreMappingMapper.update(null, Wrappers.lambdaUpdate(QywxGroupStoreMapping.class).set(QywxGroupStoreMapping::getIsDeleted, 1).set(QywxGroupStoreMapping::getModifiedBy, currentUserId).set(QywxGroupStoreMapping::getModifiedDate, new Date()).eq(QywxGroupStoreMapping::getGroupMsgId, id));
        //endregion
        materialMappingDao.update(null,Wrappers.lambdaUpdate(TCustomerCommonMaterialMapping.class)
                .set(TCustomerCommonMaterialMapping::getIsDeleted,1).set(TCustomerCommonMaterialMapping::getModifiedBy,currentUserId)
                .set(TCustomerCommonMaterialMapping::getModifiedDate,new Date())
                .eq(TCustomerCommonMaterialMapping::getSourceId,id)
                .eq(TCustomerCommonMaterialMapping::getSourceType,2));
        materialContentMappingDao.update(null,Wrappers.lambdaUpdate(TCustomerCommonMaterialContentMapping.class)
                .set(TCustomerCommonMaterialContentMapping::getIsDeleted,1).set(TCustomerCommonMaterialContentMapping::getModifiedBy,currentUserId)
                .set(TCustomerCommonMaterialContentMapping::getModifiedDate,new Date())
                .eq(TCustomerCommonMaterialContentMapping::getSourceId,id)
                .eq(TCustomerCommonMaterialContentMapping::getSourceType,2));
        //region 删除好友配置表
        List<QywxGroupSendCondition> qywxGroupSendConditions = qywxGroupSendConditionMapper
                .selectList(Wrappers.lambdaQuery(QywxGroupSendCondition.class)
                        .eq(QywxGroupSendCondition::getIsDeleted, 0)
                        .eq(QywxGroupSendCondition::getGroupMsgId, id));
        qywxGroupSendConditionMapper.update(null, Wrappers.lambdaUpdate(QywxGroupSendCondition.class)
                .set(QywxGroupSendCondition::getIsDeleted, 1)
                .set(QywxGroupSendCondition::getModifiedBy, currentUserId)
                .set(QywxGroupSendCondition::getModifiedDate, new Date())
                .eq(QywxGroupSendCondition::getGroupMsgId, id));
        if (CollectionUtils.isNotEmpty(qywxGroupSendConditions)) {
            List<Long> conditionIdList = qywxGroupSendConditions.stream().map(QywxGroupSendCondition::getId).collect(Collectors.toList());
            List<QywxGroupConditionExpression> expressionIdList = qywxGroupConditionExpressionMapper
                    .selectList(Wrappers.lambdaQuery(QywxGroupConditionExpression.class)
                            .eq(QywxGroupConditionExpression::getIsDeleted, 0)
                            .in(QywxGroupConditionExpression::getConditionId, conditionIdList));
            qywxGroupConditionExpressionMapper.update(null, Wrappers.lambdaUpdate(QywxGroupConditionExpression.class)
                    .set(QywxGroupConditionExpression::getIsDeleted, 1)
                    .set(QywxGroupConditionExpression::getModifiedBy, currentUserId)
                    .set(QywxGroupConditionExpression::getModifiedDate, new Date())
                    .in(QywxGroupConditionExpression::getConditionId, conditionIdList));
            if (CollectionUtils.isNotEmpty(expressionIdList)) {
                List<Long> idList = expressionIdList.stream().map(QywxGroupConditionExpression::getId).collect(Collectors.toList());
                qywxGroupConditionValueMapper.update(null, Wrappers.lambdaUpdate(QywxGroupConditionValue.class)
                        .set(QywxGroupConditionValue::getIsDeleted, 1)
                        .set(QywxGroupConditionValue::getModifiedBy, currentUserId)
                        .set(QywxGroupConditionValue::getModifiedDate, new Date())
                        .in(QywxGroupConditionValue::getConditionExpressionId, idList));
            }
        }
        //endregion
        logService.insertOperationLog2(qywxGroupMsgMainInfo.getId(),
                "t_qywx_group_msg_main_info",
                1, GroupMsgOperationTypeEnum.DEL.getType(), "删除群发",
                null, null,"删除群发",
                null, currentUserId, firstName);
        return Result.buildSuccess("删除成功");
    }

    public PageInfo<GroupMsgExportResp> getGroupMsgContentRecordExtend(GroupMsgListExportDTO dto){
        if (dto.getId()!=null){
            dto.setIds(Arrays.asList(dto.getId()));
        }
        PageInfo<GroupMsgExportResp> pageInfo = new PageInfo<>(dto.getPageNum(),dto.getPageSize());
        try {
            List<Long> userCompanyIds = userAuthorConfig.queryCompanyAuthorIdList(1);
            if (CollectionUtils.isNotEmpty(userCompanyIds)){
                if (CollectionUtils.isNotEmpty(dto.getCompanyList())){
                    dto.getCompanyList().retainAll(userCompanyIds);
                }else {
                    dto.setCompanyList(userCompanyIds);
                }
                if (CollectionUtils.isEmpty(dto.getCompanyList())){
                    return pageInfo;
                }
            }
        }catch (Exception e){
            return pageInfo;
        }
        List<GroupMsgExportResp> groupMsgContentRecordResps = qywxGroupUserSendLogMapper.selectGroupMsgUserSendExport(pageInfo, dto);
        if (CollectionUtils.isEmpty(groupMsgContentRecordResps)){
            return pageInfo;
        }
        List<Long> groupMsgIdList = groupMsgContentRecordResps.stream().map(GroupMsgExportResp::getId).collect(Collectors.toList());
        //查询分公司权限
        List<QywxGroupCompanyMapping> qywxGroupCompanyMappings = qywxGroupCompanyMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupCompanyMapping.class)
                .eq(QywxGroupCompanyMapping::getIsDeleted, 0)
                .in(QywxGroupCompanyMapping::getGroupMsgId, groupMsgIdList));

        if (CollectionUtils.isNotEmpty(qywxGroupCompanyMappings)){
            Map<Long, List<QywxGroupCompanyMapping>> companyMap = qywxGroupCompanyMappings.stream().collect(Collectors.groupingBy(QywxGroupCompanyMapping::getGroupMsgId));
            List<Integer> companyIdList = qywxGroupCompanyMappings.stream().filter(c->Objects.equals(c.getType(),2)).map(QywxGroupCompanyMapping::getCompanyId)
                    .map(Long::intValue).distinct().collect(Collectors.toList());
            Map<Long, String> areaName = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(companyIdList)){
                FindCompanyByIdsInDto findCompanyDto = new FindCompanyByIdsInDto();
                findCompanyDto.setIdList(companyIdList);
                Result<List<FindCompanyAreaByOrgIdsOutDto>> result = orgClient.findCompanyByOrgIdList(findCompanyDto);
                if (result.getSuccess()&&CollectionUtils.isNotEmpty(result.getData())){
                    areaName = result.getData().stream()
                            .filter(c->c!=null&&c.getOrgId()!=null&&Objects.nonNull(c.getValueName()))
                            .collect(Collectors.toMap(FindCompanyAreaByOrgIdsOutDto::getOrgId,FindCompanyAreaByOrgIdsOutDto::getValueName));
                }
            }
            Map<Long, String> finalAreaName = areaName;
            companyMap.forEach((groupMsgId, companyList)->{
                GroupMsgExportResp groupMsgExportResp = groupMsgContentRecordResps.stream().filter(g -> ObjectUtil.equals(g.getId(), groupMsgId)).findFirst().get();
                String companyAuthDesc = "";
                String companyName = "";
                Optional<QywxGroupCompanyMapping> first = companyList.stream().filter(company -> Objects.equals(company.getType(),1)&&ObjectUtil.equals(company.getCompanyMappingType(), -1)).findFirst();
                if (first.isPresent()){
                    companyAuthDesc = "全部";
                }else {
                    companyName = companyList.stream()
                            .filter(company -> Objects.equals(company.getType(),2))
                            .map(QywxGroupCompanyMapping::getCompanyName)
                            .collect(Collectors.joining(","));
                    companyAuthDesc = companyList.stream().filter(c->Objects.equals(c.getType(),1)).map(QywxGroupCompanyMapping::getCompanyName).distinct().collect(Collectors.joining(","));
                }
                groupMsgExportResp.setCompanyAuthDesc(companyAuthDesc);
                groupMsgExportResp.setCompanyName(companyName);
                List<Long> companyIds = companyList.stream()
                        .filter(company -> Objects.equals(company.getType(), 2))
                        .map(QywxGroupCompanyMapping::getCompanyId)
                        .collect(Collectors.toList());
                Set<String> effAreaName = Sets.newHashSet();
                if (!finalAreaName.isEmpty()&&CollectionUtils.isNotEmpty(companyIds)){
                    finalAreaName.forEach((companyId,area)->{
                        if (companyIds.contains(companyId)){
                            effAreaName.add(area);
                        }
                    });
                }
                groupMsgExportResp.setAreaName(String.join(",",effAreaName));
            });
        }
        //查询员工生效范围
        List<EffectChargeCountList> qywxGroupEffectChargeMappings = qywxGroupEffectChargeMappingMapper.selectEffectChargeCountList(groupMsgIdList);

        Map<Long, EffectChargeCountList> chargeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxGroupEffectChargeMappings)){
            chargeMap = qywxGroupEffectChargeMappings.stream().collect(Collectors.toMap(EffectChargeCountList::getGroupMsgId,e->e));
        }
        List<UserSendStatistics> userSendLogList = qywxGroupUserSendLogMapper.getUserSendLogList(groupMsgIdList);
        Map<Long, UserSendStatistics> customerMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userSendLogList)){
            customerMap = userSendLogList.stream().collect(Collectors.toMap(UserSendStatistics::getGroupMsgId,e->e));
        }

        //计算群发该发送好友数
        List<EffectCustomerCountList> effectCustomerCountList = qywxGroupEffectCustomerMappingMapper.selectEffectCustomerCountList(groupMsgIdList);
        Map<Long, Integer> effectCustomerMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(effectCustomerCountList)){
            effectCustomerMap = effectCustomerCountList.stream().collect(Collectors.toMap(EffectCustomerCountList::getGroupMsgId, EffectCustomerCountList::getCustomerCount));
        }
        //线索数
        List<EventLogDto> qywxWelcomeRelationEventLogs = eventLogDao.selectAllByWelcomeRelationId(groupMsgIdList);
        Map<Long,Long> eventLogMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(qywxWelcomeRelationEventLogs)){
            eventLogMap=qywxWelcomeRelationEventLogs.stream().collect(Collectors.toMap(EventLogDto::getWelcomeRelationId,EventLogDto::getEventLogCount));
        }
        //计算群发已发送好友数量
        List<SendResultLogDto> qywxGroupSendResultLogs = qywxGroupSendResultLogMapper.selectAllBySendSuccessCount(groupMsgIdList);
        Map<Long, Long> sendLogMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxGroupSendResultLogs)){
            sendLogMap = qywxGroupSendResultLogs.stream()
                    .collect(Collectors.toMap(SendResultLogDto::getGroupMsgId,SendResultLogDto::getSendCount));
        }
        //查询创建人
        List<String> userIdList = groupMsgContentRecordResps.stream().map(GroupMsgExportResp::getCreatedBy).distinct().collect(Collectors.toList());
        Result<List<UserEntityExtend>> userResult = userClient.findUserEntityExtendByUserIds(userIdList);
        Map<String, String> userNameMap = Maps.newHashMap();
        if (userResult.getSuccess()&&CollectionUtils.isNotEmpty(userResult.getData())){
            userNameMap = userResult.getData().stream().collect(Collectors.toMap(UserEntityExtend::getUserEntityId, UserEntityExtend::getFirstName));
        }

        List<SendResultLogDto> sendResultLogDtos = qywxGroupSendResultLogMapper.selectAll(groupMsgIdList);
        Map<Long, Long> sendResultMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sendResultLogDtos)) {
            sendResultMap= sendResultLogDtos.stream().collect(Collectors.toMap(SendResultLogDto::getGroupMsgId, SendResultLogDto::getSendCount));
        }
        for (GroupMsgExportResp resp: groupMsgContentRecordResps) {
            if (!chargeMap.isEmpty()&&chargeMap.containsKey(resp.getId())){
                resp.setChargeUserCount(chargeMap.get(resp.getId()).getChargeCount());
                resp.setEffectChargeType(chargeMap.get(resp.getId()).getChargeType());
            }
            if (!customerMap.isEmpty()&&customerMap.containsKey(resp.getId())){
                UserSendStatistics userSendStatistics = customerMap.get(resp.getId());
                resp.setNoticeChargeUserCount(userSendStatistics.getUserCount());
                resp.setSendChargeUserCount(userSendStatistics.getSendCount());
                resp.setNotSendChargeUserCount(userSendStatistics.getUpSendCount());
            }
            if (!effectCustomerMap.isEmpty()&&effectCustomerMap.containsKey(resp.getId())){
                resp.setUserCount(effectCustomerMap.get(resp.getId()));
            }
            if (!eventLogMap.isEmpty()&&eventLogMap.containsKey(resp.getId())){
                resp.setCluesCount(eventLogMap.get(resp.getId()));
            }
            if (!sendLogMap.isEmpty()&&sendLogMap.containsKey(resp.getId())){
                resp.setSendCount(sendLogMap.get(resp.getId()));
            }
            if (!userNameMap.isEmpty()&&userNameMap.containsKey(resp.getCreatedBy())){
                resp.setCreatedName(userNameMap.get(resp.getCreatedBy()));
            }
            if (resp.getSendCount()!=null&&resp.getSendCount()!=0&&resp.getNotSendChargeUserCount()!=null&&resp.getNotSendChargeUserCount()!=0){
                double v = Double.valueOf(resp.getSendCount()) / Double.valueOf(resp.getNotSendChargeUserCount());
                BigDecimal multiply = new BigDecimal(v).multiply(BigDecimal.valueOf(100));
                resp.setChargeCoverageRate(multiply.setScale(2,RoundingMode.HALF_UP)+"%");
            }else {
                resp.setChargeCoverageRate("0%");
            }

            if (resp.getSendCustomerCount()!=null&&resp.getSendCustomerCount()!=0&&resp.getCustomerCount()!=null&&resp.getCustomerCount()!=0){
                double v = Double.valueOf(resp.getSendCustomerCount()) / Double.valueOf(resp.getCustomerCount());
                BigDecimal multiply = new BigDecimal(v).multiply(BigDecimal.valueOf(100));
                resp.setCustomerCoverageRate(multiply.setScale(2,RoundingMode.HALF_UP)+"%");
            }else {
                resp.setCustomerCoverageRate("0%");
            }

            if (CollectionUtils.isNotEmpty(sendResultMap)&&sendResultMap.containsKey(resp.getId())) {
                resp.setSendCustomerCount(sendResultMap.get(resp.getId()));
            }
        }

        pageInfo.setRecords(groupMsgContentRecordResps);
        pageInfo.setTotal(groupMsgContentRecordResps.size());
        return pageInfo;
    }
    public PageInfo<GroupMsgContentRecordResp> getGroupMsgContentRecordList(GroupMsgContentRecordDto dto){
        // 根据门店渠道，标签，关键字获取门店列表
        List<Long> storeIds = getStoreIds(dto);
        if (CollectionUtils.isNotEmpty(dto.getStoreIdList())) {
            // 取并集
            storeIds.addAll(dto.getStoreIdList());
        }
        PageInfo<GroupMsgContentRecordResp> pageInfo = new PageInfo<>(dto.getPageNum(),dto.getPageSize());
        List<GroupMsgContentRecordResp> groupMsgContentRecordResps = qywxGroupUserSendLogMapper.selectGroupMsgUserSend(pageInfo,dto, storeIds);
        int count = qywxGroupUserSendLogMapper.selectGroupMsgUserSendCount(dto, storeIds);
        Long msgUpSendCount = qywxGroupUserSendLogMapper.selectCount(Wrappers.lambdaQuery(QywxGroupUserSendLog.class)
                .eq(QywxGroupUserSendLog::getIsDeleted, 0)
                .eq(QywxGroupUserSendLog::getGroupMsgId, dto.getId())
                .eq(QywxGroupUserSendLog::getSendUserResult,1)
                .eq(QywxGroupUserSendLog::getSendStatus, 0));
        Long msgSendCount = qywxGroupUserSendLogMapper.selectCount(Wrappers.lambdaQuery(QywxGroupUserSendLog.class)
                .eq(QywxGroupUserSendLog::getIsDeleted, 0)
                .eq(QywxGroupUserSendLog::getGroupMsgId, dto.getId())
                .eq(QywxGroupUserSendLog::getSendUserResult,1)
                .eq(QywxGroupUserSendLog::getSendStatus, 2));
        if (CollectionUtils.isNotEmpty(groupMsgContentRecordResps)){
            List<String> typeCodes = new ArrayList<>();
            typeCodes.add("gw");
            List<Dic> dicList= systemClient.queryByTypeCodesOpen(typeCodes).getData();
            List<QywxWelcomeRelationEventLog> qywxWelcomeRelationEventLogs = eventLogDao.selectList(Wrappers.lambdaQuery(QywxWelcomeRelationEventLog.class)
                    .eq(QywxWelcomeRelationEventLog::getEventType, 30)
                    .eq(QywxWelcomeRelationEventLog::getSourceType, 2)
                    .isNotNull(QywxWelcomeRelationEventLog::getChargeUserId)
                    .eq(QywxWelcomeRelationEventLog::getWelcomeRelationId, dto.getId())
                    .eq(QywxWelcomeRelationEventLog::getIsDeleted, 0));
            Map<Long,Long> eventLogMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(qywxWelcomeRelationEventLogs)){
                eventLogMap=qywxWelcomeRelationEventLogs.stream().collect(Collectors.groupingBy(QywxWelcomeRelationEventLog::getChargeUserId,Collectors.counting()));
            }

            List<String> userIdList = groupMsgContentRecordResps.stream().map(GroupMsgContentRecordResp::getQywxUserId).distinct().collect(Collectors.toList());
            QueryInsightUserCountInDTO queryInsightUserCountInDTO = new QueryInsightUserCountInDTO();
            queryInsightUserCountInDTO.setUserIds(userIdList);
            if(groupMsgContentRecordResps.get(0).getFullGroupMsgId() == null){
                //说明是外层的首次群发的统计
                queryInsightUserCountInDTO.setPushId(groupMsgContentRecordResps.get(0).getGroupMsgId());
            }else{
                //说明是外层的二次群发的统计
                queryInsightUserCountInDTO.setSubPushId(groupMsgContentRecordResps.get(0).getGroupMsgId());
            }
            Result<List<QueryInsightUserCountOutDTO>> pushInsightUserCountResult = cdpClient.getPushInsightUserCount(queryInsightUserCountInDTO);
            Map<String, QueryInsightUserCountOutDTO> queryInsightUserCountOutDTOMap = Maps.newHashMap();
            if (pushInsightUserCountResult.getSuccess()&&CollectionUtils.isNotEmpty(pushInsightUserCountResult.getData())){
                queryInsightUserCountOutDTOMap = pushInsightUserCountResult.getData().stream().collect(Collectors.toMap(QueryInsightUserCountOutDTO::getUserId,q->q));
            }

            //获取配置从内容树获取且内容树配置内容为小程序
            Integer customerOpenFlag = 0;
            if (Objects.equals(groupMsgContentRecordResps.get(0).getConfigType(),1)){
                List<QywxGroupContentConfig> qywxGroupContentConfigList = qywxGroupContentConfigMapper.selectList(Wrappers.lambdaQuery(QywxGroupContentConfig.class)
                        .eq(QywxGroupContentConfig::getGroupMsgId, groupMsgContentRecordResps.get(0).getGroupMsgId())
                        .eq(QywxGroupContentConfig::getMsgType, 3)
                        .eq(QywxGroupContentConfig::getIsDeleted, 0));
                if (CollectionUtils.isNotEmpty(qywxGroupContentConfigList)){
                    Set<String> miniprogramPage = qywxGroupContentConfigList.stream().map(QywxGroupContentConfig::getMiniprogramPage).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(miniprogramPage)){
                        if (miniprogramPage.stream().filter(BLANK_PAGE::contains).findAny().isEmpty()) {
                            customerOpenFlag = 0;
                        } else {
                            customerOpenFlag = 1;
                        }
                    }
                }
            }


            for (GroupMsgContentRecordResp obj: groupMsgContentRecordResps) {
                long userCount=0;
                if (StringUtils.isNotBlank(obj.getQywxExternalUserids())){
                    List<String> strings = JSONArray.parseArray(obj.getQywxExternalUserids(), String.class);
                    userCount = CollectionUtils.isNotEmpty(strings)?strings.size():0;
                }
                long failUserCount=0;
                if (StringUtils.isNotBlank(obj.getFailExternalUserids())){
                    List<String> strings = JSONArray.parseArray(obj.getFailExternalUserids(), String.class);
                    failUserCount = CollectionUtils.isNotEmpty(strings)?strings.size():0;
                }
                obj.setCluesCount(eventLogMap.get(obj.getChargeUserId()));
                obj.setUserCount(userCount-failUserCount);
                obj.setIsShow(!obj.getUserCount().equals(obj.getSendCount()));
                if (CollectionUtils.isNotEmpty(dicList)){
                    Optional<Dic> first = dicList.stream().filter(dic -> String.valueOf(dic.getId()).equals(String.valueOf(obj.getStation()))).findFirst();
                    first.ifPresent(dic -> obj.setStation(dic.getValueName()));
                }
                obj.setMsgSendCount(msgSendCount);
                obj.setMsgUpSendCount(msgUpSendCount);
                obj.setFailExternalUserids("");
                obj.setQywxExternalUserids("");
                obj.setCustomerOpenFlag(customerOpenFlag);
                if (Objects.equals(customerOpenFlag,1)&&Objects.equals(obj.getSendStatus(),0)){
                    obj.setCustomerOpenFlag(0);
                }

                if (CollectionUtils.isNotEmpty(queryInsightUserCountOutDTOMap)){
                    if (queryInsightUserCountOutDTOMap.containsKey(obj.getQywxUserId())){
                        obj.setCustomerOpenNum(queryInsightUserCountOutDTOMap.get(obj.getQywxUserId()).getOpen());
                    }
                }
            }
        }
        pageInfo.setRecords(groupMsgContentRecordResps);
        pageInfo.setTotal(count);
        return pageInfo;
    }

    public Result<GroupMsgDataResp> getGroupMsgData(Long groupMsgId){
        //获取 共需发送顾客人数，已通知导购人数  已发送顾客人数
        GroupMsgDataResp groupMsgDataResp = qywxGroupUserSendLogMapper.queryGroupMsgData(groupMsgId);
        //调用cdp查询当前群发打开链接顾客数，参与顾客数量
        TQywxGroupCompleteDataLog completeGroupMsgData = tQywxGroupCompleteDataLogDao.getCompleteGroupMsgData(groupMsgId);
        if (completeGroupMsgData==null){
            groupMsgDataResp.setStatus(0);
            return Result.buildSuccess(groupMsgDataResp);
        }
        if (Objects.equals(completeGroupMsgData.getStatus(),1)){
            groupMsgDataResp.setStatus(1);
            return Result.buildSuccess(groupMsgDataResp);
        }
        if (Objects.equals(completeGroupMsgData.getStatus(),3)){
            groupMsgDataResp.setStatus(3);
            return Result.buildSuccess(groupMsgDataResp);
        }
        groupMsgDataResp.setGroupMsgId(groupMsgId);
        groupMsgDataResp.setStatus(2);
        groupMsgDataResp.setNotOpenChargeNum(completeGroupMsgData.getChargeSendNotOpenCount());
        groupMsgDataResp.setNotJoinChargeNum(completeGroupMsgData.getChargeOpenNotJoin());
        //未打开链接 = 顾客总数-打开链接顾客数
        groupMsgDataResp.setUnOpenLinkCustomerNum(groupMsgDataResp.getSendCustomerNum()-(completeGroupMsgData.getOpenCount()==null?0:completeGroupMsgData.getOpenCount()));
        //打开链接人数
        groupMsgDataResp.setOpenLinkCustomerNum(completeGroupMsgData.getOpenCount());
        //未参与人数 = 打开链接顾客数-参与顾客数
        groupMsgDataResp.setCustomerNotJoinNum((completeGroupMsgData.getOpenCount()==null?0:completeGroupMsgData.getOpenCount()) - (completeGroupMsgData.getJoinCount()==null?0:completeGroupMsgData.getJoinCount()));
        //参与顾客数
        groupMsgDataResp.setCustomerJoinNum(completeGroupMsgData.getJoinCount()==null?0:completeGroupMsgData.getJoinCount());
        //未打开链接 = 顾客总数-打开链接顾客数
        //groupMsgDataResp.setUnOpenLinkCustomerNum(groupMsgDataResp.getSendCustomerNum());
        //未参与人数 = 打开链接顾客数-参与顾客数
/*        groupMsgDataResp.setCustomerNotJoinNum(groupMsgDataResp.getOpenLinkCustomerNum());*/
        groupMsgDataResp.setCompleteGroupMsgData(completeGroupMsgData.getCompleteTime());
        return Result.buildSuccess(groupMsgDataResp);
    }

    public Result groupMsgCompleteData(Long groupMsgId){
        TQywxGroupCompleteDataLog log = new TQywxGroupCompleteDataLog();
        log.setGroupMsgId(groupMsgId);
        log.setCompleteTime(new Date());
        log.setStatus(1);
        log.setIsDeleted(0);
        log.setCreatedBy(userAuthorConfig.getCurrentUserId());
        log.setModifiedBy(userAuthorConfig.getCurrentUserId());
        log.setCreatedDate(new Date());
        log.setModifiedDate(new Date());
        tQywxGroupCompleteDataLogDao.insert(log);
        //同步result_log
        sendMsgCommonService.completeData(groupMsgId,log.getId());

/*        //调用cdp重新计算
        Result<Integer> result = cdpClient.getCDPSyncTaskInfo();
        if (result.getSuccess()&&result.getData()!=null){
            tQywxGroupCompleteDataLogDao.update(null,Wrappers.lambdaUpdate(TQywxGroupCompleteDataLog.class)
                    .set(TQywxGroupCompleteDataLog::getSyncId,result.getData()).eq(TQywxGroupCompleteDataLog::getId,log.getId()));
        }*/
        return Result.buildSuccess("数据正在计算中");
    }

    public Result cdpCompleteData(CdpCompleteDataDto cdpCompleteDataDto){
        log.error("cdp计算结束入参：{}",cdpCompleteDataDto);
        if (cdpCompleteDataDto.getSyncId()==null){
            log.error("参数异常");
            return Result.buildFailure("参数异常，任务ID必填");
        }

        List<TQywxGroupCompleteDataLog> tQywxGroupCompleteDataLogs = tQywxGroupCompleteDataLogDao.selectList(Wrappers.lambdaQuery(TQywxGroupCompleteDataLog.class)
                .eq(TQywxGroupCompleteDataLog::getSyncId, cdpCompleteDataDto.getSyncId())
                .eq(TQywxGroupCompleteDataLog::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(tQywxGroupCompleteDataLogs)){
            log.error("参数异常");
            return Result.buildFailure("参数异常，任务ID必填");
        }
        CdpCompleteDataOutDto cdpCompleteDataOutDto = new CdpCompleteDataOutDto();
        cdpCompleteDataOutDto.setTQywxGroupCompleteDataLogs(tQywxGroupCompleteDataLogs);
        cdpCompleteDataOutDto.setStatus(cdpCompleteDataDto.getStatus());
        cdpCompleteDataOutDto.setSyncId(cdpCompleteDataDto.getSyncId());
        cdpCompleteDataOutDto.setCompleteTime(cdpCompleteDataDto.getCompleteTime());
        cdpCompleteDataChannel.send(MessageBuilder.withPayload(cdpCompleteDataOutDto).build());
        return Result.buildSuccess("计算结束");
    }


    public Result cdpPushEffectCustomer(CdpPushCustomerDTO cdpPushCustomerDTO){
        log.error("推送好友关系列表:{}",cdpPushCustomerDTO);
        if (cdpPushCustomerDTO.getBatchId()==null){
            log.error("消息批次id不可为空");
            return Result.buildFailure("消息批次id不可为空");
        }
        if (CollectionUtils.isEmpty(cdpPushCustomerDTO.getEffectCustomerDtoList())){
            log.error("推送好友关系列表不可为空");
            return Result.buildFailure("推送好友关系列表不可为空");
        }
        List<TQywxGroupEffectCustomerCdpPush> cdpPushEntityList = Lists.newArrayList();
        for (CdpPushCustomerDTO.EffectCustomerDto effectCustomerDto : cdpPushCustomerDTO.getEffectCustomerDtoList()) {
/*            if (effectCustomerDto.getCdpPushId()==null){
                log.error("cdp推送id不可为空");
                return Result.buildFailure("cdp推送id不可为空");
            }*/
            TQywxGroupEffectCustomerCdpPush cdpPushEntity = new TQywxGroupEffectCustomerCdpPush();
            cdpPushEntity.setBatchId(cdpPushCustomerDTO.getBatchId());
            cdpPushEntity.setCdpPushId(effectCustomerDto.getCdpPushId());
            cdpPushEntity.setAccountId(effectCustomerDto.getAccountId());
            cdpPushEntity.setUserId(effectCustomerDto.getUserId());
            cdpPushEntity.setTaskId(cdpPushCustomerDTO.getTaskId());
            cdpPushEntity.setLastBatchFlag(cdpPushCustomerDTO.getLastBatchFlag());
            cdpPushEntity.setExternalUserid(effectCustomerDto.getExternalUserId());
            cdpPushEntity.setIsDeleted(0L);
            cdpPushEntityList.add(cdpPushEntity);
        }
        if (CollectionUtils.isNotEmpty(cdpPushEntityList)){
            List<List<?>> lists = splitList(cdpPushEntityList, 500);
            lists.forEach(mappingList->{
                cdpPushService.saveBatch((List<TQywxGroupEffectCustomerCdpPush>) mappingList);
            });
        }
        return Result.buildSuccess("推送完成");
    }

    /**
     * 根据门店渠道，标签，关键字获取门店列表
     * @param dto
     * @return
     */
    private List<Long> getStoreIds(GroupMsgContentRecordDto dto) {
        if (CollectionUtils.isEmpty(dto.getStoreTagCodeList()) &&
                CollectionUtils.isEmpty(dto.getStoreKeywordList()) &&
               CollectionUtils.isEmpty(dto.getStoreChannelCodeList())) {
            return new ArrayList<>();
        }
        SelectStoreByStoreLevelsInDto param = SelectStoreByStoreLevelsInDto
                .builder()
                .storeChannelCodeList(dto.getStoreChannelCodeList())
                .keyWordSet(CollectionUtils.isNotEmpty(dto.getStoreTagCodeList()) ? new HashSet<>(dto.getStoreTagCodeList()) : null)
                .realKeyWordsList(dto.getStoreKeywordList())
                .build();
        log.info("orgClient.queryStoreByConditions input param:{}",param);
        Result<List<StoreEntity>> result = orgClient.queryStoreByConditions(param);
        log.info("orgClient.queryStoreByConditions output result:{}", JSON.toJSONString(result));
        if (result.getSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            List<StoreEntity> storeList = result.getData();
            return storeList.stream().map(StoreEntity::getOrgId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    private void insertEffectCustomer(GroupMsgBaseDTO groupMsgBaseDTO, String currentUserId, QywxGroupMsgMainInfo qywxGroupMsgMainInfo) {
        QywxGroupSendCondition sendCondition;
        String configType="GROUP";
        CdpByTag cdpByTag = null;
        if (Objects.equals(0,groupMsgBaseDTO.getSecondMsgFlag())&&Objects.equals(SendMemberTypeEnum.SEND_MEMBER_TYPE_PART.getSendMemberType(),groupMsgBaseDTO.getSendMemberType())){
            cdpByTag = new CdpByTag();
            cdpByTag.setStartTime(null);
            cdpByTag.setEndTime(null);
            sendCondition = getQywxGroupSendCondition(groupMsgBaseDTO, qywxGroupMsgMainInfo, currentUserId,cdpByTag);
            qywxGroupSendConditionMapper.insert(sendCondition);
            if (groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser()!=null){
                //群发好友条件
                GroupConditionExpressionDTO includeUser = groupMsgBaseDTO.getGroupSendConditionDTO().getIncludeUser();
                QywxGroupConditionExpression qywxGroupConditionExpression = getQywxGroupConditionExpression(sendCondition, 1,
                        currentUserId,includeUser.getExpressionType());
                qywxGroupConditionExpressionMapper.insert(qywxGroupConditionExpression);
                cdpByTag.setOperator(Objects.equals(includeUser.getExpressionType(),1)?"OR":"AND");
                //标签
                getQywxGroupConditionValue(includeUser.getGroupConditionValueDTOList(), currentUserId, qywxGroupConditionExpression.getId(),cdpByTag);
            }
            if (groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser()!=null){
                //群发排除好友条件
                GroupConditionExpressionDTO excludeUser = groupMsgBaseDTO.getGroupSendConditionDTO().getExcludeUser();
                QywxGroupConditionExpression qywxGroupConditionExpression = getQywxGroupConditionExpression(sendCondition, 2,
                        currentUserId,excludeUser.getExpressionType());
                qywxGroupConditionExpressionMapper.insert(qywxGroupConditionExpression);
                cdpByTag.setExcludeOperator(Objects.equals(excludeUser.getExpressionType(),1)?"OR":"AND");
                //标签
                getQywxGroupConditionValue(excludeUser.getGroupConditionValueDTOList(), currentUserId, qywxGroupConditionExpression.getId(),cdpByTag);
            }
            //小区
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getVillageIds())){
                List<CdpTagDTO> cdpTagDtos = Lists.newArrayList();
                List<TQywxGroupMsgVillageMapping> villageMappings = Lists.newArrayList();
                groupMsgBaseDTO.getVillageIds().forEach(v->{
                    CdpTagDTO cdpTagDTO = new CdpTagDTO();
                    cdpTagDTO.setTagKey("tag_hygx_xqxx");
                    cdpTagDTO.setTagValue(String.valueOf(v.getVillageId()));
                    cdpTagDtos.add(cdpTagDTO);
                    TQywxGroupMsgVillageMapping villageMapping = new TQywxGroupMsgVillageMapping();
                    villageMapping.setGroupMsgId(groupMsgBaseDTO.getId());
                    villageMapping.setVillageId(v.getVillageId());
                    villageMapping.setVillageName(v.getVillageName());
                    villageMapping.setIsDeleted(0L);
                    villageMappings.add(villageMapping);
                });
                cdpByTag.setTags(cdpTagDtos);
                villageMappingDao.saveBatch(villageMappings);
            }
            configType = "TAG";

        }
        //1.如果全部好友，直接查询下t_qywx_cutomer_user_relation 全部好友导入群发好友表
        if (Objects.equals(0,groupMsgBaseDTO.getSecondMsgFlag())&&Objects.equals(SendMemberTypeEnum.SEND_MEMBER_TYPE_ALL.getSendMemberType(),groupMsgBaseDTO.getSendMemberType())){
            //cdpPushServiceImpl.buildSendMemberAll(groupMsgBaseDTO,currentUserId);
            qywxGroupMsgMainInfo.setCanSendFlag(1);
            log.error("当前执行群发信息：{}",qywxGroupMsgMainInfo);
            if (qywxGroupMsgMainInfo.getStatus().equals(0)){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            Date date = DateUtils.addHours(new Date(), -2);
            if (qywxGroupMsgMainInfo.getSendType().equals(1)&&qywxGroupMsgMainInfo.getSendTime().before(date)){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            List<String> userIds = groupMsgBaseDTO.getEffectChargeMappingList().stream().map(QywxGroupEffectChargeMapping::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)){
                List<List<?>> lists = splitList(userIds, 500);
                int count = 0;
                for (List<?> list: lists) {
                    if (CollectionUtils.isNotEmpty(list)){
                        count += qywxCutomerUserRelationMapper.queryCustomerRelationByUserId((List<String>) list);
                    }
                }
                qywxGroupMsgMainInfo.setCustomerCount(Long.valueOf(String.valueOf(count)));
            }
            groupMsgMainInfoMapper.updateByPrimaryKey(qywxGroupMsgMainInfo);
            if (qywxGroupMsgMainInfo.getSendType().equals(0)&&qywxGroupMsgMainInfo.getStatus().equals(1)){
                log.error("发送群发");
                sendMsgCommonService.sendGroupMsgEntry(qywxGroupMsgMainInfo.getId());
                //sendMsgCommonService.sendGroupMsgEntry(qywxGroupMsgMainInfo.getId());
            }
        }else {
            //2.如果为标签或人群包，创建cdp任务
            TQywxGroupSendTaskInfo taskInfo = new TQywxGroupSendTaskInfo();
            taskInfo.setGroupMsgId(groupMsgBaseDTO.getId());
            taskInfo.setStatus(1);
            taskInfo.setSendCdpTime(new Date());
            taskInfo.setIsDeleted(0);
            taskInfo.setCreatedBy(currentUserId);
            taskInfo.setCreatedDate(new Date());
            taskInfo.setModifiedBy(currentUserId);
            taskInfo.setModifiedDate(new Date());
            tQywxGroupSendTaskInfoDao.insert(taskInfo);
            //调用cdp通知开始计算
            MissionCreateDTO missionCreateDTO = new MissionCreateDTO();
            missionCreateDTO.setMissionId(taskInfo.getId());
            if (Objects.equals(1,groupMsgBaseDTO.getSecondMsgFlag())){
                configType = "PUSH";
            }
            missionCreateDTO.setConfigType(configType);
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getEffectChargeMappingList())){
                missionCreateDTO.setUserIds(groupMsgBaseDTO.getEffectChargeMappingList().stream().map(QywxGroupEffectChargeMapping::getUserId).collect(Collectors.toList()));
            }
            if (groupMsgBaseDTO.getCdpGroupId()!=null){
                CdpByGroup cdpByGroup = new CdpByGroup();
                cdpByGroup.setGroupId(groupMsgBaseDTO.getCdpGroupId());
                missionCreateDTO.setByGroup(cdpByGroup);
            }
            if (cdpByTag!=null){
                missionCreateDTO.setByTag(cdpByTag);
            }
            if (Objects.equals(1,groupMsgBaseDTO.getSecondMsgFlag())){
                CdpByPush cdpByPush = new CdpByPush();
                cdpByPush.setMPushId(groupMsgBaseDTO.getFullGroupMsgId());
                cdpByPush.setPhase(Objects.equals(groupMsgBaseDTO.getSendObjectType(),1)?"SENT_NOT_OPEN":"OPENED_NOT_JOIN");
                missionCreateDTO.setByPush(cdpByPush);
            }
            log.error("推送cdp信息：{}",missionCreateDTO);
            cdpClient.createGroupMsg(missionCreateDTO);
        }

    }



    // 废弃，已切换为cdp计算好友
    public void buildEffectCustomer(String currentUserId,GroupMsgBaseDTO groupMsgBaseDTO,TagQueryDTO tagQueryDTO){
        QywxGroupEffectCustomerMessage message = new QywxGroupEffectCustomerMessage();
        message.setTagQueryDTO(tagQueryDTO);
        message.setGroupMsgId(groupMsgBaseDTO.getId());
        message.setAccountId(groupMsgBaseDTO.getAccountId());
        message.setCurrentUserId(currentUserId);
        messageChannel.send(MessageBuilder.withPayload(message).build());
    }

    // 废弃，已切换为cdp计算好友
    public void sendMsgConsume(QywxGroupEffectCustomerMessage message){
        log.info("组织群发好友消息：{}",message);
        if (message == null){
            log.error("组织好友消息topic消息体为空");
            return;
        }
        Result<SaleManCustomerResult> saleManCustomerResultResult = searchClient.searchByTag(message.getTagQueryDTO());
        if (saleManCustomerResultResult.getSuccess()&&saleManCustomerResultResult.getData()!=null&&saleManCustomerResultResult.getData().getCount()>0){
            List<ESCustomerTagVO> list = saleManCustomerResultResult.getData().getList();
            List<List<?>> lists = splitList(list, 1000);
            int size = lists.size();
            CustomerMessage customerMessage = new CustomerMessage();
            customerMessage.setGroupMsgId(message.getGroupMsgId());
            customerMessage.setCurrentUserId(message.getCurrentUserId());
            customerMessage.setAccountId(message.getAccountId());
            QywxGroupEffectCustomerAsyncLog asyncLog;
            Date date = new Date();
            for(int i = 0;i<lists.size();i++){
                customerMessage.setEsCustomerTagVOList((List<ESCustomerTagVO>) lists.get(i));
                customerMessage.setBatchId(Long.valueOf(i+1));
                if (size == (i+1)){
                    customerMessage.setLastBatchFlag(true);
                }
                asyncLog = new QywxGroupEffectCustomerAsyncLog();
                asyncLog.setCreatedBy(message.getCurrentUserId());
                asyncLog.setGroupMsgId(message.getGroupMsgId());
                asyncLog.setLastBatchFlag(customerMessage.isLastBatchFlag()?1:0);
                asyncLog.setBatchId(Long.valueOf(i+1));
                asyncLog.setStatus(0);
                asyncLog.setDataBody(JSONObject.toJSONString(customerMessage));
                asyncLog.setIsDeleted(0);
                asyncLog.setCreatedDate(date);
                asyncLog.setModifiedDate(date);
                asyncLog.setCreatedBy(message.getCurrentUserId());
                asyncLog.setModifiedBy(message.getCurrentUserId());
                qywxGroupEffectCustomerAsyncLogMapper.insert(asyncLog);
                customerMessageChannel.send(MessageBuilder.withPayload(customerMessage).build());
            }
        }
    }

    // 废弃，已切换为cdp计算好友
    public void sendMsgCommonService(CustomerMessage customerMessage){
        Date date = new Date();
        try{
            List<String> collect = customerMessage.getEsCustomerTagVOList().stream().map(ESCustomerTagVO::getUserId).distinct().collect(Collectors.toList());
            List<QywxUserInfo> qywxUserInfos = qywxUserInfoMapper.selectUserByAccountId(customerMessage.getAccountId(), collect);
            QywxGroupEffectCustomerMapping customer = new QywxGroupEffectCustomerMapping();
            List<QywxGroupEffectCustomerMapping> customerMappings   = Lists.newArrayList();
            if (CollectionUtils.isEmpty(qywxUserInfos)){
                log.error("分批插入好友异常 ,批次【{}】,{}",customerMessage.getBatchId(),"未查询到当前批次对于的业务员id信息");
                qywxGroupEffectCustomerAsyncLogMapper.update(null, Wrappers.lambdaUpdate(QywxGroupEffectCustomerAsyncLog.class)
                        .set(QywxGroupEffectCustomerAsyncLog::getStatus, 2)
                        .set(QywxGroupEffectCustomerAsyncLog::getFailReason,"未查询到当前批次对于的业务员id信息")
                        .set(QywxGroupEffectCustomerAsyncLog::getModifiedDate,new Date())
                        .set(QywxGroupEffectCustomerAsyncLog::getModifiedBy,customerMessage.getCurrentUserId())
                        .eq(QywxGroupEffectCustomerAsyncLog::getBatchId,customerMessage.getBatchId())
                        .eq(QywxGroupEffectCustomerAsyncLog::getGroupMsgId, customerMessage.getGroupMsgId()));
            }else {
                for (ESCustomerTagVO dto : customerMessage.getEsCustomerTagVOList()) {
                    customer = new QywxGroupEffectCustomerMapping();
                    customer.setCreatedBy(customerMessage.getCurrentUserId());
                    customer.setCreatedDate(date);
                    customer.setModifiedBy(customerMessage.getCurrentUserId());
                    customer.setModifiedDate(date);
                    customer.setIsDeleted(0);
                    customer.setGroupMsgId(customerMessage.getGroupMsgId());
                    customer.setAccountId(dto.getAccountId());
                    customer.setExternalUserid(dto.getExternalUserid());
                    customer.setUserId(dto.getUserId());
                    Optional<QywxUserInfo> first = qywxUserInfos.parallelStream().filter(obj -> obj.getUserId().equals(dto.getUserId())).findFirst();
                    if (first.isPresent()&&first.get().getChargeUserId()!=null){
                        customer.setChargeUserId(first.get().getChargeUserId());
                        customerMappings.add(customer);
                    }
                }
                //qywxGroupEffectCustomerMappingMapper.insertBatch(customerMappings);
                qywxGroupEffectCustomerAsyncLogMapper.update(null,Wrappers.lambdaUpdate(QywxGroupEffectCustomerAsyncLog.class)
                        .set(QywxGroupEffectCustomerAsyncLog::getStatus, 1)
                        .set(QywxGroupEffectCustomerAsyncLog::getModifiedDate,new Date())
                        .set(QywxGroupEffectCustomerAsyncLog::getModifiedBy,customerMessage.getCurrentUserId())
                        .eq(QywxGroupEffectCustomerAsyncLog::getBatchId,customerMessage.getBatchId())
                        .eq(QywxGroupEffectCustomerAsyncLog::getGroupMsgId, customerMessage.getGroupMsgId()));
            }
        }catch (Exception e){
            log.error("分批插入好友异常 ,批次【{}】,{}",customerMessage.getBatchId(),e);
            qywxGroupEffectCustomerAsyncLogMapper.update(null, Wrappers.lambdaUpdate(QywxGroupEffectCustomerAsyncLog.class)
                    .set(QywxGroupEffectCustomerAsyncLog::getStatus, 2)
                    .set(QywxGroupEffectCustomerAsyncLog::getFailReason,e.getMessage())
                    .set(QywxGroupEffectCustomerAsyncLog::getModifiedDate,new Date())
                    .set(QywxGroupEffectCustomerAsyncLog::getModifiedBy,customerMessage.getCurrentUserId())
                    .eq(QywxGroupEffectCustomerAsyncLog::getBatchId,customerMessage.getBatchId())
                    .eq(QywxGroupEffectCustomerAsyncLog::getGroupMsgId, customerMessage.getGroupMsgId()));
        }
        if (customerMessage.isLastBatchFlag()){
            QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class).eq(QywxGroupMsgMainInfo::getIsDeleted, 0)
                    .eq(QywxGroupMsgMainInfo::getId, customerMessage.getGroupMsgId()));
            qywxGroupMsgMainInfo.setCanSendFlag(1);
            if (qywxGroupMsgMainInfo.getStatus().equals(0)){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            if (qywxGroupMsgMainInfo.getSendType().equals(1)&&qywxGroupMsgMainInfo.getSendTime().before(new Date())){
                qywxGroupMsgMainInfo.setCanSendFlag(0);
            }
            groupMsgMainInfoMapper.updateByPrimaryKey(qywxGroupMsgMainInfo);
            if (qywxGroupMsgMainInfo.getSendType().equals(0)&&qywxGroupMsgMainInfo.getStatus().equals(1)){
                log.info("发送群发");
                //sendMsgCommonService.sendGroupMsgEntry(qywxGroupMsgMainInfo.getId());
            }
        }
    }

    private void insertEffectCharge(GroupMsgBaseDTO groupMsgBaseDTO, String currentUserId, Long groupMsgId) {
        if (groupMsgBaseDTO.getOrganizeMappingDTO() != null) {
            //组织配置
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList())) {
                insertQywxGroupCompanyMapping(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList(),
                        currentUserId, groupMsgId, 2, 0);
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList())) {
                //岗位配置
                List<QywxGroupStationMapping> stationMappingList = Lists.newArrayList();
                groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList().forEach(dto -> {
                    QywxGroupStationMapping stationMapping = new QywxGroupStationMapping();
                    stationMapping.setStation(dto);
                    stationMapping.setGroupMsgId(groupMsgId);
                    stationMapping.setCreatedBy(currentUserId);
                    stationMapping.setModifiedBy(currentUserId);
                    stationMappingList.add(stationMapping);
                });
                qywxGroupStationMappingMapper.insertBatch(stationMappingList);
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList())) {
                //门店配置
                FindStoreByCodesInDto byCodesInDto = new FindStoreByCodesInDto();
                byCodesInDto.setCodeList(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList());
                List<FindStoreByIdOutDto> storeList = orgClient.findByCodes(byCodesInDto).getData();
                if (CollectionUtils.isNotEmpty(storeList)) {
                    List<QywxGroupStoreMapping> storeMappingList = Lists.newArrayList();
                    groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList().forEach(dto -> {
                        Optional<FindStoreByIdOutDto> first = storeList.stream().filter(s -> s.getCode().equals(dto)).findFirst();
                        if (first.isPresent()) {
                            FindStoreByIdOutDto store = first.get();
                            QywxGroupStoreMapping storeMapping = new QywxGroupStoreMapping();
                            storeMapping.setStoreOrgId(store.getOrgId());
                            storeMapping.setStoreCode(store.getCode());
                            storeMapping.setStoreName(store.getName());
                            storeMapping.setGroupMsgId(groupMsgId);
                            storeMapping.setCreatedBy(currentUserId);
                            storeMapping.setModifiedBy(currentUserId);
                            storeMappingList.add(storeMapping);
                        }

                    });
                    qywxGroupStoreMappingMapper.insertBatch(storeMappingList);
                }
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList())) {
                //渠道配置
                List<QywxGroupChannelCategoryMapping> channelCategoryMappingList = Lists.newArrayList();
                groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList().forEach(dto -> {
                    QywxGroupChannelCategoryMapping channelCategoryMapping = new QywxGroupChannelCategoryMapping();
                    BeanUtils.copyProperties(dto, channelCategoryMapping);
                    channelCategoryMapping.setGroupMsgId(groupMsgId);
                    channelCategoryMapping.setCreatedBy(currentUserId);
                    channelCategoryMapping.setModifiedBy(currentUserId);
                    channelCategoryMappingList.add(channelCategoryMapping);
                });
                qywxGroupChannelCategoryMappingMapper.insertBatch(channelCategoryMappingList);
            }
        }
        List<QywxGroupEffectChargeMapping> effectChargeMappingList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList())) {
            //指定人员配置
            List<QywxGroupChargeMapping> groupChargeMappingList = Lists.newArrayList();
            groupMsgBaseDTO.getGroupChargeMappingDTOList().forEach(dto -> {
                QywxGroupChargeMapping groupChargeMapping = new QywxGroupChargeMapping();
                BeanUtils.copyProperties(dto, groupChargeMapping);
                groupChargeMapping.setGroupMsgId(groupMsgId);
                groupChargeMapping.setCreatedBy(currentUserId);
                groupChargeMapping.setModifiedBy(currentUserId);
                groupChargeMapping.setChargeUserName(MybatisMateConfig.encrypt(dto.getChargeUserName()));
                groupChargeMapping.setChargePhone(MybatisMateConfig.encrypt(dto.getChargePhone()));
                groupChargeMappingList.add(groupChargeMapping);
            });
            qywxGroupChargeMappingMapper.insertBatch(groupChargeMappingList);

            for (GroupChargeMappingDTO dto: groupMsgBaseDTO.getGroupChargeMappingDTOList()) {
                QywxGroupEffectChargeMapping effectChargeMapping = new QywxGroupEffectChargeMapping();
                BeanUtils.copyProperties(dto, effectChargeMapping);
                effectChargeMapping.setType(1);
                effectChargeMapping.setGroupMsgId(groupMsgId);
                effectChargeMapping.setCreatedBy(currentUserId);
                effectChargeMapping.setModifiedBy(currentUserId);
                effectChargeMapping.setAccountId(groupMsgBaseDTO.getAccountId());
                effectChargeMapping.setChargeUserName(MybatisMateConfig.encrypt(dto.getChargeUserName()));
                effectChargeMapping.setChargePhone(MybatisMateConfig.encrypt(dto.getChargePhone()));
                effectChargeMappingList.add(effectChargeMapping);
            }
        }

        if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupEffectChargeMappingDTOList())) {
            //生效员工配置
            for (GroupEffectChargeMappingDTO dto: groupMsgBaseDTO.getGroupEffectChargeMappingDTOList()) {
                QywxGroupEffectChargeMapping effectChargeMapping = new QywxGroupEffectChargeMapping();
                BeanUtils.copyProperties(dto, effectChargeMapping);
                effectChargeMapping.setType(0);
                effectChargeMapping.setGroupMsgId(groupMsgId);
                effectChargeMapping.setCreatedBy(currentUserId);
                effectChargeMapping.setAccountId(groupMsgBaseDTO.getAccountId());
                effectChargeMapping.setModifiedBy(currentUserId);
                effectChargeMapping.setAccountId(groupMsgBaseDTO.getAccountId());
                effectChargeMapping.setChargeUserName(MybatisMateConfig.encrypt(dto.getChargeUserName()));
                effectChargeMapping.setChargePhone(MybatisMateConfig.encrypt(dto.getChargePhone()));
                effectChargeMappingList.add(effectChargeMapping);
            }
        }
        groupMsgBaseDTO.setEffectChargeMappingList(effectChargeMappingList);
        if (CollectionUtils.isNotEmpty(effectChargeMappingList)){
            List<List<?>> lists = splitList(effectChargeMappingList, 1000);
            lists.forEach(mappingList->{
                qywxGroupEffectChargeMappingMapper.insertBatch((List<QywxGroupEffectChargeMapping>) mappingList);
            });
        }
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    private void insertContentMapping(GroupMsgBaseDTO groupMsgBaseDTO, Long groupMsgId, String currentUserId) {
        if (ConfigTypeEnum.CONFIG_TYPE_ONE.getConfigType().equals(groupMsgBaseDTO.getConfigType())) {
            //region 从聊天树内容获取 2024/11/04  改版，群发与欢迎语融合
            TCustomerCommonMaterialMapping tCustomerCommonMaterialMapping = new TCustomerCommonMaterialMapping();
            tCustomerCommonMaterialMapping.setMaterialId(groupMsgBaseDTO.getMaterialId());
            tCustomerCommonMaterialMapping.setTreeId(groupMsgBaseDTO.getTreeId());
            tCustomerCommonMaterialMapping.setSourceId(groupMsgBaseDTO.getId());
            tCustomerCommonMaterialMapping.setSourceType(2);
            tCustomerCommonMaterialMapping.setIsDeleted(0L);
            materialMappingDao.insert(tCustomerCommonMaterialMapping);
            List<TCustomerCommonMaterialContentMapping> contentMappings = Lists.newArrayList();
            groupMsgBaseDTO.getMaterialContentConfigDTOList().forEach(dto->{
                TCustomerCommonMaterialContentMapping contentMapping = new TCustomerCommonMaterialContentMapping();
                contentMapping.setSourceId(groupMsgBaseDTO.getId());
                contentMapping.setSourceType(2);
                contentMapping.setSort(dto.getSort());
                contentMapping.setTreeId(groupMsgBaseDTO.getTreeId());
                contentMapping.setMaterialId(groupMsgBaseDTO.getMaterialId());
                contentMapping.setMaterialContentId(dto.getMaterialContentId());
                if (StringUtils.isNotBlank(dto.getLandingPageTitle())){
                    contentMapping.setPageTitle(dto.getLandingPageTitle());
                }
                if (StringUtils.isNotBlank(dto.getLinkDescription())){
                    contentMapping.setPageDescription(dto.getLinkDescription());
                }
                if (Objects.equals(dto.getType(),5)){
                    if (StringUtils.isNotBlank(dto.getContent())){
                        contentMapping.setPageDescription(dto.getContent());
                    }
                }
                contentMapping.setIsDeleted(0L);
                contentMappings.add(contentMapping);
            });
            materialContentMappingDao.insertBatch(contentMappings);
            //endregion
        } else {
            //region 本地配置内容
            List<QywxGroupContentConfig> qywxGroupContentConfigList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupContentConfigDTOList())) {
                groupMsgBaseDTO.getGroupContentConfigDTOList().forEach(dto -> {
                    ContentMsgTypeEnum contentMsg = ContentMsgTypeEnum.getContentMsg(dto.getMsgType());
                    if (contentMsg == null) {
                        return;
                    }
                    QywxGroupContentConfig contentConfig = new QywxGroupContentConfig();
                    contentConfig.setMsgType(dto.getMsgType());
                    contentConfig.setGroupMsgId(groupMsgId);
                    contentConfig.setSort(dto.getSort());
                    contentConfig.setCreatedBy(currentUserId);
                    contentConfig.setModifiedBy(currentUserId);
                    switch (contentMsg.getMsgType()) {
                        case 1:
                            contentConfig.setImageUrl(dto.getImageUrl());
                            break;
                        case 2:
                            contentConfig.setLinkPicUrl(dto.getLinkPicUrl());
                            contentConfig.setLinkUrl(dto.getLinkUrl());
                            contentConfig.setLinkTitle(dto.getLinkTitle());
                            contentConfig.setChargeUserFlag(dto.getChargeUserFlag());
                            contentConfig.setIsAssignSharer(dto.getIsAssignSharer());
                            break;
                        case 3:
                            contentConfig.setMiniprogramAppid(dto.getMiniprogramAppid());
                            contentConfig.setMiniprogramParam(dto.getMiniprogramParam());
                            contentConfig.setMiniprogramPage(dto.getMiniprogramPage());
                            contentConfig.setMiniprogramPicUrl(dto.getMiniprogramPicUrl());
                            contentConfig.setChargeUserFlag(dto.getChargeUserFlag());
                            contentConfig.setMiniprogramTitle(dto.getMiniprogramTitle());
                            contentConfig.setMiniprogramContentTemplateId(dto.getMiniprogramContentTemplateId());
                            break;
                        case 4:
                            contentConfig.setVideoUrl(dto.getVideoUrl());
                            contentConfig.setVideoPictureUrl(dto.getVideoPictureUrl());
                            break;
                    }
                    qywxGroupContentConfigList.add(contentConfig);
                });
                qywxGroupContentConfigMapper.insertBatch(qywxGroupContentConfigList);
                //region 上传图片 获取mediaId
                buildMediaId(groupMsgBaseDTO.getAccountId(), qywxGroupContentConfigList);
                //endregion
            }
            //endregion
        }
    }

    public void buildMediaId(String accountId, List<QywxGroupContentConfig> qywxGroupContentConfigList) {
        if (CollectionUtils.isNotEmpty(qywxGroupContentConfigList)) {
            qywxGroupContentConfigList.forEach(content -> {
                String mediaIdFromUrl = "";
                if (content.getMsgType().equals(1)) {
                    try {
                        mediaIdFromUrl = getMediaIdFromUrl(accountId, content.getImageUrl(), content.getMsgType());
                    } catch (Exception e) {
                        log.error("上传图片报错");
                    }
                    content.setImageMediaId(mediaIdFromUrl);
                }
                if (content.getMsgType().equals(3)) {
                    try {
                        mediaIdFromUrl = getMediaIdFromUrl(accountId, content.getMiniprogramPicUrl(), content.getMsgType());
                    } catch (Exception e) {
                        log.error("上传图片报错");
                    }
                    content.setMiniprogramPicMediaId(mediaIdFromUrl);
                }
                if (content.getMsgType().equals(4)) {
                    try {
                        mediaIdFromUrl = getMediaIdFromUrl(accountId, content.getVideoUrl(), content.getMsgType());
                    } catch (Exception e) {
                        log.error("上传图片报错");
                    }
                    content.setVideoMediaId(mediaIdFromUrl);
                }
            });
            qywxGroupContentConfigMapper.updateContentMedia(qywxGroupContentConfigList);
        }
    }

    private String getMediaIdFromUrl(String accountId, String urlPath, Integer msgType) throws Exception {
        String fileType = null;
        if (Integer.valueOf(1).equals(msgType) || Integer.valueOf(3).equals(msgType)) {
            fileType = "image";
        } else if (Integer.valueOf(4).equals(msgType)) {
            fileType = "video";
        }
        if (org.apache.commons.lang.StringUtils.isBlank(fileType)) {
            return null;
        }
        return qywxUploadMediaService.getMediaIdFromUrl(accountId, urlPath, fileType);
    }

    private void insertQywxGroupCompanyMapping(List<GroupCompanyMappingDTO> groupCompanyMappingDTOList, String currentUserId,
                                               Long groupMsgId, Integer type, Integer companyMappingType) {
        if (companyMappingType.equals(-1)) {
            QywxGroupCompanyMapping qywxGroupCompanyMapping = new QywxGroupCompanyMapping();
            qywxGroupCompanyMapping.setType(type);
            qywxGroupCompanyMapping.setCompanyMappingType(companyMappingType);
            qywxGroupCompanyMapping.setCreatedBy(currentUserId);
            qywxGroupCompanyMapping.setCreatedDate(new Date());
            qywxGroupCompanyMapping.setModifiedBy(currentUserId);
            qywxGroupCompanyMapping.setModifiedDate(new Date());
            qywxGroupCompanyMapping.setGroupMsgId(groupMsgId);
            qywxGroupCompanyMapping.setIsDeleted(0);
            int result = qywxGroupCompanyMappingMapper.insert(qywxGroupCompanyMapping);
            if (result < 1) {
                log.error("插入公司配置信息异常");
            }
        } else {
            if (CollectionUtils.isNotEmpty(groupCompanyMappingDTOList)) {
                List<QywxGroupCompanyMapping> qywxGroupCompanyMappingList = Lists.newArrayList();
                groupCompanyMappingDTOList.forEach(dto -> {
                    QywxGroupCompanyMapping qywxGroupCompanyMapping = new QywxGroupCompanyMapping();
                    BeanUtils.copyProperties(dto, qywxGroupCompanyMapping);
                    qywxGroupCompanyMapping.setType(type);
                    qywxGroupCompanyMapping.setCompanyMappingType(companyMappingType);
                    qywxGroupCompanyMapping.setCreatedBy(currentUserId);
                    qywxGroupCompanyMapping.setModifiedBy(currentUserId);
                    qywxGroupCompanyMapping.setGroupMsgId(groupMsgId);
                    qywxGroupCompanyMappingList.add(qywxGroupCompanyMapping);
                });
                int result = qywxGroupCompanyMappingMapper.insertBatch(qywxGroupCompanyMappingList);
                if (result < 1) {
                    log.error("插入公司配置信息异常");
                }
            }
        }
    }

    private void getQywxGroupConditionValue(List<GroupConditionValueDTO> expressionDTOList, String currentUserId,
                                            Long conditionExpressionId,CdpByTag cdpByTag) {
        List<QywxGroupConditionValue> qywxGroupConditionValueList = Lists.newArrayList();
        List<CdpTagDTO> cdpTagDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(expressionDTOList)) {
            expressionDTOList.forEach(dto -> {
                QywxGroupConditionValue qywxGroupConditionValue = new QywxGroupConditionValue();
                CdpTagDTO cdpTagDTO = new CdpTagDTO();
                BeanUtils.copyProperties(dto, qywxGroupConditionValue);
                cdpTagDTO.setTagKey(dto.getGroupCode());
                cdpTagDTO.setTagValue(dto.getTagName());
                cdpTagDtos.add(cdpTagDTO);
                qywxGroupConditionValue.setConditionExpressionId(conditionExpressionId);
                qywxGroupConditionValue.setCreatedBy(currentUserId);
                qywxGroupConditionValue.setModifiedBy(currentUserId);
                qywxGroupConditionValueList.add(qywxGroupConditionValue);
            });
            if (StringUtils.isNotBlank(cdpByTag.getOperator())&&CollectionUtils.isEmpty(cdpByTag.getTags())){
                cdpByTag.setTags(cdpTagDtos);
            }
            if (StringUtils.isNotBlank(cdpByTag.getExcludeOperator())&&CollectionUtils.isEmpty(cdpByTag.getExcludeTags())){
                cdpByTag.setExcludeTags(cdpTagDtos);
            }

        }
        if (CollectionUtils.isNotEmpty(qywxGroupConditionValueList)){
            qywxGroupConditionValueMapper.insertBatch(qywxGroupConditionValueList);
        }
    }

    /**
     * 校验是否触发了敏感词
     * @param sensitiveWordList
     * @param content
     */
    private void checkHitSw(List<SensitiveWordDTO> sensitiveWordList,String content,Set<String> swSets) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sensitiveWordList)) {
            Set<String> mappingSensitiveWordList = getMappingSensitiveWordList(sensitiveWordList, content);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mappingSensitiveWordList)) {
                swSets.addAll(mappingSensitiveWordList);
            }
        }
    }

    /**
     * 校验是否命中敏感词
     * @param sensitiveWordList
     * @param content
     * @return
     */
    private Set<String> getMappingSensitiveWordList(List<SensitiveWordDTO> sensitiveWordList, String content) {
        Set<String> swSets = new HashSet<>();
        for (SensitiveWordDTO sw : sensitiveWordList) {
            if (content.contains(sw.getWord())) {
                swSets.add(sw.getWord());
            }
        }
        return swSets;
    }

    private String validateParam(GroupMsgBaseDTO groupMsgBaseDTO) {
        StringBuilder paramResult = new StringBuilder();
        //敏感词校验
        Set<String> swSets = new LinkedHashSet<>(16);
        Result<List<SensitiveWordDTO>> swListByWordResult = cmsClient.findSwListByWord("");
        if (swListByWordResult.getSuccess()&&CollectionUtils.isNotEmpty(swListByWordResult.getData())){
            List<SensitiveWordDTO> swListByWord = swListByWordResult.getData();
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupContentConfigDTOList())){
                groupMsgBaseDTO.getGroupContentConfigDTOList().forEach(content->{
                    if (StringUtils.isNotBlank(content.getLinkTitle())){
                        checkHitSw(swListByWord,content.getLinkTitle(),swSets);
                    }
                    if (StringUtils.isNotBlank(content.getMiniprogramTitle())){
                        checkHitSw(swListByWord,content.getMiniprogramTitle(),swSets);
                    }
                });
            }
            if (StringUtils.isNotBlank(groupMsgBaseDTO.getTitle())){
                checkHitSw(swListByWord,groupMsgBaseDTO.getTitle(),swSets);
            }
            if (StringUtils.isNotBlank(groupMsgBaseDTO.getContent())){
                checkHitSw(swListByWord,groupMsgBaseDTO.getContent(),swSets);
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getMaterialContentConfigDTOList())){
                groupMsgBaseDTO.getMaterialContentConfigDTOList().forEach(content->{
                    if (StringUtils.isNotBlank(content.getContent())){
                        checkHitSw(swListByWord,content.getContent(),swSets);
                    }
                    if (StringUtils.isNotBlank(content.getMaterialName())){
                        checkHitSw(swListByWord,content.getMaterialName(),swSets);
                    }
                    if (StringUtils.isNotBlank(content.getLandingPageTitle())){
                        checkHitSw(swListByWord,content.getLandingPageTitle(),swSets);
                    }
                    if (StringUtils.isNotBlank(content.getLinkDescription())){
                        checkHitSw(swListByWord,content.getLinkDescription(),swSets);
                    }
                    if (StringUtils.isNotBlank(content.getRefTitle())){
                        checkHitSw(swListByWord,content.getRefTitle(),swSets);
                    }
                });
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(swSets)) {
                throw new BusinessException("素材触发了敏感词，请检查并修改后再保存。\n\n敏感词:" + swSets.toString());
            }
        }
        //region 基本参数校验
        if (StringUtils.isBlank(groupMsgBaseDTO.getAccountId())) {
            log.error("生效企微不可为空");
            paramResult.append("生效企微必填");
        }
        if (groupMsgBaseDTO.getSendType().equals(1) && groupMsgBaseDTO.getSendTime() == null) {
            log.error("群发时间勾选定时发送，定时时间不可为空");
            paramResult.append("群发时间勾选定时发送，定时时间不可为空");
        }
        if (groupMsgBaseDTO.getStatus() == null) {
            log.error("状态必填");
            paramResult.append("状态必填");
        }
        if (groupMsgBaseDTO.getTitle() == null) {
            log.error("群发标题必填");
            paramResult.append("群发标题必填");
        }
        Date dateByFormat = DateUtil.getDateByFormat(DateUtil.dateFormat(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        if (groupMsgBaseDTO.getNoticeEndDate().getTime() < dateByFormat.getTime()){
            log.error("任务通知截止时间不可小于当前时间");
            paramResult.append("任务通知截止时间不可小于当前时间");
        }
        //endregion

        //region 分公司权限参数校验
        if (!groupMsgBaseDTO.getCompanyMappingType().equals(-1)) {
            if (CollectionUtils.isEmpty(groupMsgBaseDTO.getGroupCompanyMappingDTOList())) {
                log.error("分公司权限必须配置");
                paramResult.append("分公司权限必填");
            } else {
                boolean result = groupMsgBaseDTO.getGroupCompanyMappingDTOList().stream().anyMatch(dto -> ObjectUtils.isNull(dto) || ObjectUtils.isNull(dto.getCompanyId()) || ObjectUtils.isNull(dto.getCompanyName()));
                if (result) {
                    log.error("分公司权限配置内，分公司id，分公司名称不可为空");
                    paramResult.append("分公司权限配置，分公司id或分公司名称必填");
                }
            }
        }
        //endregion

        //region群发内容参数校验
        if (ConfigTypeEnum.CONFIG_TYPE_ZERO.getConfigType().equals(groupMsgBaseDTO.getConfigType())) {
            //本地配置内容
            if (StringUtils.isBlank(groupMsgBaseDTO.getContent())){
                paramResult.append("勾选本地配置内容，文本必须输入");
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupContentConfigDTOList())) {
                if (groupMsgBaseDTO.getGroupContentConfigDTOList().size()>9){
                    paramResult.append("勾选本地配置内容，内容配置上限9个");
                }else {
                    groupMsgBaseDTO.getGroupContentConfigDTOList().forEach(dto -> {
                        ContentMsgTypeEnum contentMsg = ContentMsgTypeEnum.getContentMsg(dto.getMsgType());
                        if (contentMsg == null) {
                            log.error("内容类型不符");
                            return;
                        }
                        switch (contentMsg.getMsgType()) {
                            case 1:
                                if (StringUtils.isBlank(dto.getImageUrl())) {
                                    paramResult.append("内容选择图片时，图片，必填");
                                }
                                break;
                            case 2:
                                if (StringUtils.isBlank(dto.getLinkPicUrl()) || StringUtils.isBlank(dto.getLinkUrl()) ||
                                        StringUtils.isBlank(dto.getLinkTitle()) || dto.getChargeUserFlag() == null) {
                                    paramResult.append("内容选择链接时，h5链接、h5链接标题、图片、是否附带业务员参数必填");
                                }
                                break;
                            case 3:
                                if (StringUtils.isBlank(dto.getMiniprogramAppid()) || StringUtils.isBlank(dto.getMiniprogramPage())
                                        || StringUtils.isBlank(dto.getMiniprogramPicUrl())||dto.getChargeUserFlag()==null||
                                        StringUtils.isBlank(dto.getMiniprogramTitle())) {
                                    paramResult.append("内容选择小程序时，小程序标题、是否附带业务员参数、封面图片必填");
                                }
                                break;
                            case 4:
                                if (StringUtils.isBlank(dto.getVideoUrl())||StringUtils.isBlank(dto.getVideoPictureUrl())) {
                                    paramResult.append("内容选择视频时，视频名称、视频必填");
                                }
                                break;
                            default:
                        }
                    });
                }
            }
        } else if (ConfigTypeEnum.CONFIG_TYPE_ONE.getConfigType().equals(groupMsgBaseDTO.getConfigType())) {
            //从聊天树获取
            if (groupMsgBaseDTO.getTreeId() == null || groupMsgBaseDTO.getMaterialId() == null) {
                paramResult.append("勾选从聊天分类树获取群发内容，分类树或内容素材必填");
            }
        } else {
            log.error("群发内容类型不符");
            paramResult.append("群发内容类型不符");
        }
        //endregion

        //region 员工配置信息参数校验
        if ((CollectionUtils.isEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList()) && groupMsgBaseDTO.getOrganizeMappingDTO() == null)
        ||(CollectionUtils.isEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList()) &&
                CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList()))) {
            log.error("生效员工不可为空");
            paramResult.append("生效员工必须配置");
        }  else if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList())) {
            boolean result = groupMsgBaseDTO.getGroupChargeMappingDTOList().stream()
                    .anyMatch(dto -> ObjectUtils.isNull(dto) ||
                            ObjectUtils.isNull(dto.getChargeUserId()) || ObjectUtils.isNull(dto.getChargeUserName()) ||
                            ObjectUtils.isNull(dto.getChargeCode()) || ObjectUtils.isNull(dto.getChargePhone()));
            if (result) {
                paramResult.append("指定人员配置信息，成员id,业务员id,业务员名称，业务员编码，业务员手机号必填");
            }
        }else if (CollectionUtils.isEmpty(groupMsgBaseDTO.getGroupChargeMappingDTOList())&&groupMsgBaseDTO.getOrganizeMappingDTO() != null) {
            // 23914-【智慧导购】企微群发&个性化欢迎语等，配置对象为技师时，无需选择门店或门店渠道

/*            if (CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList()) &&
                    CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList()) &&
                    CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList()) &&
                    CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStoreMappingDTOList())) {
                paramResult.append("勾选从组织配置人员配置生效员工，组织配置信息必填");
            }*/
/*            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList())) {
                boolean result = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupChannelCategoryMappingList().stream().anyMatch(dto -> ObjectUtils.isNull(dto) || ObjectUtils.isNull(dto.getChannelCategoryCode()) || ObjectUtils.isNull(dto.getChannelCategoryName()) || ObjectUtils.isNull(dto.getChannelSubdivideCode()) || ObjectUtils.isNull(dto.getChannelSubdivideName()));
                if (result) {
                    paramResult.append("渠道配置信息对应编码，名称，细分编码，细分名称必填");
                }
            }*/
            if (CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList()) &&
                    CollectionUtils.isEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList())) {
                paramResult.append("勾选从组织配置人员配置生效员工，组织配置信息必填");
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList())) {
                boolean result = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupCompanyMappingMappingList().stream().anyMatch(dto -> ObjectUtils.isNull(dto) || ObjectUtils.isNull(dto.getCompanyId()) || ObjectUtils.isNull(dto.getCompanyName()));
                if (result) {
                    paramResult.append("公司配置信息对应公司id,公司名称必填");
                }
            }
            if (CollectionUtils.isNotEmpty(groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList())) {
                boolean result = groupMsgBaseDTO.getOrganizeMappingDTO().getGroupStationMappingDTOList().stream().anyMatch(ObjectUtils::isNull);
                if (result) {
                    paramResult.append("岗位配置信息对应岗位id必填");
                }
            }
        }
        //endregion

        //region 好友配置参数校验
        if (SendMemberTypeEnum.SEND_MEMBER_TYPE_PART.getSendMemberType().equals(groupMsgBaseDTO.getSendMemberType())) {
            //筛选好友
            if (groupMsgBaseDTO.getGroupSendConditionDTO()==null && CollectionUtils.isEmpty(groupMsgBaseDTO.getVillageIds())){
                groupMsgBaseDTO.setSendMemberType(1);
            }else {
                GroupSendConditionDTO sendConditionDTO = groupMsgBaseDTO.getGroupSendConditionDTO();
                if (sendConditionDTO.getStartTime() == null
                        && sendConditionDTO.getEndTime() == null
                        && CollectionUtils.isEmpty(groupMsgBaseDTO.getVillageIds())
                        && (sendConditionDTO.getIncludeUser() == null || CollectionUtils.isEmpty(sendConditionDTO.getIncludeUser().getGroupConditionValueDTOList())
                ) && (sendConditionDTO.getExcludeUser() == null || CollectionUtils.isEmpty(sendConditionDTO.getExcludeUser().getGroupConditionValueDTOList())) ){
                    groupMsgBaseDTO.setSendMemberType(1);
                }
            }
        }
        //endregion
        return String.join(",",paramResult);
    }

    public PreviewInfoDto  getPreviewInfo(Long id,Long materialId){
        PreviewInfoDto previewInfoDto = new PreviewInfoDto();
        List<PreviewInfo> previewInfoList = Lists.newArrayList();
        if (id != null){
            QywxGroupMsgMainInfo qywxGroupMsgMainInfo = groupMsgMainInfoMapper.selectOne(Wrappers.lambdaQuery(QywxGroupMsgMainInfo.class)
                    .eq(QywxGroupMsgMainInfo::getIsDeleted, 0)
                    .eq(QywxGroupMsgMainInfo::getId, id));
            previewInfoDto.setConfigType(qywxGroupMsgMainInfo.getConfigType());
            if (ConfigTypeEnum.CONFIG_TYPE_ZERO.getConfigType().equals(qywxGroupMsgMainInfo.getConfigType())){
                //本地配置
                List<QywxGroupContentConfig> contentConfigList = qywxGroupContentConfigMapper.selectList(Wrappers.lambdaQuery(QywxGroupContentConfig.class)
                        .eq(QywxGroupContentConfig::getIsDeleted, 0)
                        .eq(QywxGroupContentConfig::getGroupMsgId, id).orderByAsc(QywxGroupContentConfig::getSort));
                PreviewInfo previewInfo = new PreviewInfo();
                previewInfo.setMsgType(5);
                previewInfo.setContent(qywxGroupMsgMainInfo.getContent());
                previewInfoList.add(previewInfo);
                if (CollectionUtils.isNotEmpty(contentConfigList)){
                    contentConfigList.forEach(content->{
                        ContentMsgTypeEnum contentMsg = ContentMsgTypeEnum.getContentMsg(content.getMsgType());
                        if (contentMsg!=null){
                            PreviewInfo info = new PreviewInfo();
                            //previewInfo.setContent(qywxGroupMsgMainInfo.getContent());
                            switch (contentMsg.getMsgType()){
                                case 1:
                                    info.setMsgType(content.getMsgType());
                                    info.setImageUrl(content.getImageUrl());
                                    break;
                                case 2:
                                case 3:
                                    info.setMsgType(content.getMsgType());
                                    info.setImageUrl(content.getMiniprogramPicUrl());
                                    info.setContent(content.getMiniprogramTitle());
                                    break;
                                case 4:
                                    info.setMsgType(content.getMsgType());
                                    info.setVideoPictureUrl(content.getVideoPictureUrl());
                                    break;
                            }
                            previewInfoList.add(info);
                        }
                    });
                }
                previewInfoDto.setPreviewInfo(previewInfoList);
                return previewInfoDto;
            }else {
                //从聊天分类树获取
                TCustomerCommonMaterialMapping tCustomerCommonMaterialMapping = materialMappingDao.selectOne(Wrappers.lambdaQuery(TCustomerCommonMaterialMapping.class)
                        .eq(TCustomerCommonMaterialMapping::getIsDeleted, 0)
                        .eq(TCustomerCommonMaterialMapping::getSourceType, 2)
                        .eq(TCustomerCommonMaterialMapping::getSourceId, id));
                materialId = tCustomerCommonMaterialMapping.getMaterialId();
            }
        }
        if (materialId!=null){
            Result<List<MaterialContentDetailDTO>> materialResult = cmsClient.getContentById(Math.toIntExact(materialId), "qywx_sh_1");

            List<TCustomerCommonMaterialContentMapping> contentMappings = materialContentMappingDao.selectList(Wrappers.lambdaQuery(TCustomerCommonMaterialContentMapping.class)
                    .eq(TCustomerCommonMaterialContentMapping::getIsDeleted, 0)
                    .eq(TCustomerCommonMaterialContentMapping::getSourceType, 2)
                    .eq(TCustomerCommonMaterialContentMapping::getSourceId, id));
            Map<Integer,TCustomerCommonMaterialContentMapping> materialContentMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(contentMappings)){
               materialContentMap = contentMappings.stream()
                        .collect(Collectors.toMap(TCustomerCommonMaterialContentMapping::getMaterialContentId, Function.identity()));
            }
            List<MaterialContentDetailDTO> materialContentDetailDTOS = Lists.newArrayList();
            if (materialResult.getSuccess()&&CollectionUtils.isNotEmpty(materialResult.getData())){
                List<MaterialContentDetailDTO> collect = materialResult.getData().stream().sorted(Comparator.comparing(MaterialContentDetailDTO::getSort)).collect(Collectors.toList());
                for (MaterialContentDetailDTO obj: collect) {
                    MaterialContentDetailDTO materialContentDetailDTO = new MaterialContentDetailDTO();
                    if (CollectionUtils.isNotEmpty(materialContentMap)&&materialContentMap.containsKey(obj.getMaterialContentId())){
                        BeanUtils.copyProperties(obj,materialContentDetailDTO);
                        if (StringUtils.isNotBlank(materialContentMap.get(obj.getMaterialContentId()).getPageDescription())){
                            materialContentDetailDTO.setLinkDescription(materialContentMap.get(obj.getMaterialContentId()).getPageDescription());
                        }
                        if (StringUtils.isNotBlank(materialContentMap.get(obj.getMaterialContentId()).getPageTitle())){
                            materialContentDetailDTO.setLandingPageTitle(materialContentMap.get(obj.getMaterialContentId()).getPageTitle());
                        }
                        if (Objects.equals(obj.getType(),5)&&StringUtils.isNotBlank(materialContentMap.get(obj.getMaterialContentId()).getPageDescription())){
                            materialContentDetailDTO.setContent(materialContentMap.get(obj.getMaterialContentId()).getPageDescription());
                        }
                        materialContentDetailDTOS.add(materialContentDetailDTO);
                    }
                }
                previewInfoDto.setMaterialContentConfigDTOList(materialContentDetailDTOS);
            }
        }
        return previewInfoDto;
    }

    public void effectCustomerBak(String param){
        /*log.info("effectCustomerBak param:{}",param);
        Date startTime = DateUtils.addDays(new Date(), -30);
        Date endTime = new Date();
        if (StringUtils.isNotBlank(param)){
            try {
                endTime = DateUtils.parseDate(param, "yyyy-MM-dd HH:mm:ss");
                startTime = DateUtils.addDays(endTime, -30);
            }catch (Exception e){
                startTime = DateUtils.addDays(new Date(), -30);
            }
        }
        List<QywxGroupEffectCustomerMapping> effectCustomerMappingList = qywxGroupEffectCustomerMappingMapper.selectList(Wrappers.lambdaQuery(QywxGroupEffectCustomerMapping.class)
                .eq(QywxGroupEffectCustomerMapping::getIsDeleted, 1)
                .between(QywxGroupEffectCustomerMapping::getCreatedDate, startTime, endTime));
        if (CollectionUtils.isEmpty(effectCustomerMappingList)){
            return;
        }
        List<List<?>> lists = splitList(effectCustomerMappingList, 500);
        lists.forEach(list->{
            List<TQywxGroupEffectCustomerMappingBak> tQywxGroupEffectCustomerMappingBaks = TQywxGroupEffectCustomerMappingBakMapper.INSTANCE.mappingToBak((List<QywxGroupEffectCustomerMapping>)list);
            tQywxGroupEffectCustomerMappingBakDao.insertBatch(tQywxGroupEffectCustomerMappingBaks);
            //qywxGroupEffectCustomerMappingMapper.deleteBatchIds(effectCustomerMappingList.stream().map(QywxGroupEffectCustomerMapping::getId).collect(Collectors.toList()));
        });*/
    }
}
