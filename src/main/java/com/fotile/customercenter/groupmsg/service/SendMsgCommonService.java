package com.fotile.customercenter.groupmsg.service;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fotile.customercenter.client.CdpClient;
import com.fotile.customercenter.client.CmsClient;
import com.fotile.customercenter.client.OrgClient;
import com.fotile.customercenter.client.UserClient;
import com.fotile.customercenter.client.pojo.*;
import com.fotile.customercenter.feedback.mq.ScheduleMsgChannel;
import com.fotile.customercenter.groupmsg.dao.*;
import com.fotile.customercenter.groupmsg.pojo.dto.*;
import com.fotile.customercenter.groupmsg.pojo.entity.*;
import com.fotile.customercenter.qywx.dao.QywxCutomerUserRelationMapper;
import com.fotile.customercenter.qywx.util.QywxCommonUtil;
import com.fotile.customercenter.qywx.util.QywxConstantConfig;
import com.fotile.customercenter.util.UserAuthorUtils;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SendMsgCommonService {
    @Autowired
    private QywxCommonUtil qywxCommonUtil;


    @Autowired
    private UserAuthorUtils userAuthorConfig;

    @Autowired
    private QywxGroupMsgMainInfoMapper qywxGroupMsgMainInfoMapper;

    @Autowired
    private QywxGroupContentConfigMapper qywxGroupContentConfigMapper;


    @Autowired
    private QywxGroupEffectCustomerMappingMapper qywxGroupEffectCustomerMappingMapper;

    @Autowired
    private QywxGroupEffectChargeMappingMapper qywxGroupEffectChargeMappingMapper;

    @Autowired
    private QywxGroupUserSendLogMapper qywxGroupUserSendLogMapper;

    @Autowired
    private QywxGroupSendResultLogMapper qywxGroupSendResultLogMapper;
    @Autowired
    private CmsClient cmsClient;

    @Autowired
    private OrgClient orgClient;

    @Autowired
    private UserClient userClient;

    @Resource(name = ScheduleMsgChannel.QYWX_SEND_GROUP_MSG_OUTPUT)
    private MessageChannel messageChannel;

    @Resource(name = ScheduleMsgChannel.SUPPLEMENT_GROUP_SEND_INFO_OUTPUT)
    private MessageChannel supplementInfoProduceChannel;


    @Resource(name = ScheduleMsgChannel.PULL_QYWX_MSG_STATUS_OUTPUT)
    private MessageChannel pullMsgProduceChannel;

    @Resource(name = ScheduleMsgChannel.MATERIAL_SEND_LOG_OUTPUT)
    private MessageChannel materialLogProduceChannel;

    @Resource(name = ScheduleMsgChannel.MATERIAL_SHARE_OUTPUT)
    private MessageChannel materialShareProduceChannel;
    @Autowired
    private CdpClient cdpClient;
    @Autowired
    private TQywxGroupCompleteDataLogDao tQywxGroupCompleteDataLogDao;
    @Autowired
    private TCustomerCommonMaterialContentMappingDao materialContentMappingDao;
    @Autowired
    private TCustomerCommonMaterialMappingDao materialMappingDao;
    @Resource(name = ScheduleMsgChannel.BUILD_GROUP_CHARGE_OUTPUT)
    private MessageChannel buildGroupChargeChannel;
    @Resource(name = ScheduleMsgChannel.BUILD_CHARGE_CUSTOMER_OUTPUT)
    private MessageChannel buildChargeCustomerChannel;
    @Resource(name = ScheduleMsgChannel.BUILD_ALL_CHARGE_OUTPUT)
    private MessageChannel buildAllCustomerChannel;
    @Autowired
    private QywxCutomerUserRelationMapper qywxCutomerUserRelationMapper;
    /**
     * 测试欢迎语
     */
    public int test(String accountId){
        //装配内容，发送欢迎语
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.ADD_MSG_TEMPLATE_URL + "access_token=" + token;
        //String mediaId = customerSignService.getSystemConfig("QG_QYWX_MEDIA_ID");
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("chat_type", "single");
            List<String> externalUserList = new ArrayList<>();
//            externalUserList.add("wmD6OtBgAAXQs_rrgsLtwGa2LdYG81wQ");
//            externalUserList.add("wmD6OtBgAAsEl-Qz44iyfjmBoJWuV2cg");
//            externalUserList.add("wmD6OtBgAAuKR81c8mGxvwvaXWD02_yw");
            externalUserList.add("wmXtA6DAAAjaGf4pFQ6eAHv0REn5aang");
            externalUserList.add("wmXtA6DAAAXQosHluNneZKOGT3ZIznIw");
            map.put("external_userid", externalUserList);
            map.put("sender", "FengXueDong");
            map.put("allow_select", true);
            Map contentMap = new HashMap();
            String storeInfoContent = "";

            contentMap.put("content", "就是测试测试222" + "\n" + storeInfoContent);
            map.put("text", contentMap);
//            List<Map> attachmentsList = new ArrayList<>();
//            if (contentConfigList != null && contentConfigList.size() > 0) {
//                for (QywxWelcomeContentConfig contentConfig : contentConfigList) {
//                    if (contentConfig.getMsgType() != null) {
//                        if (contentConfig.getMsgType() == 1) {
////                            "msgtype": "image",
////                                    "image": {
////                                "media_id": "MEDIA_ID",
////                                        "pic_url": "http://p.qpic.cn/pic_wework/3474110808/7a6344sdadfwehe42060/0"
////                            }
//                            if (StringUtils.isNotBlank(contentConfig.getImageMediaId())) {
//                                Map imageMap = new HashMap();
//                                imageMap.put("media_id", contentConfig.getImageMediaId());
//                                Map attachmentsMap = new HashMap();
//                                attachmentsMap.put("msgtype", "image");
//                                attachmentsMap.put("image", imageMap);
//                                attachmentsList.add(attachmentsMap);
//                            }
//                        } else if (contentConfig.getMsgType() == 2) {
//                            if (StringUtils.isNotBlank(contentConfig.getLinkUrl())) {
//                                Map linkMap = new HashMap();
//                                linkMap.put("picurl", contentConfig.getLinkPicUrl());
//                                linkMap.put("url", contentConfig.getLinkUrl());
//                                linkMap.put("title", contentConfig.getLinkTitle());
//                                Map attachmentsMap = new HashMap();
//                                attachmentsMap.put("msgtype", "link");
//                                attachmentsMap.put("link", linkMap);
//                                attachmentsList.add(attachmentsMap);
//                            }
//                        } else if (contentConfig.getMsgType() == 3) {
////            Map miniprogramMap = new HashMap();
////            miniprogramMap.put("title", "方太太粉消费券");
////            miniprogramMap.put("pic_media_id", mediaId);
////            miniprogramMap.put("appid", "wxc9fb43067fde0c84");
////            miniprogramMap.put("page", "/pages/index/index.html?sId=" + chargeUserId);
//                            if (StringUtils.isNotBlank(contentConfig.getMiniprogramPicMediaId()) && StringUtils.isNotBlank(contentConfig.getMiniprogramAppid()) && StringUtils.isNotBlank(contentConfig.getMiniprogramPage())) {
//                                Map miniprogramMap = new HashMap();
//                                miniprogramMap.put("title", contentConfig.getMiniprogramTitle());
//                                miniprogramMap.put("pic_media_id", contentConfig.getMiniprogramPicMediaId());
//                                miniprogramMap.put("appid", contentConfig.getMiniprogramAppid());
//                                miniprogramMap.put("page", "/" + contentConfig.getMiniprogramPage() + ".html?" + contentConfig.getMiniprogramParam() + "&sId=" + chargeUserId + "&welcomeId=" + welcomeId + "&wrmId=" + wrmId);
//
//                                Map attachmentsMap = new HashMap();
//                                attachmentsMap.put("msgtype", "miniprogram");
//                                attachmentsMap.put("miniprogram", miniprogramMap);
//                                attachmentsList.add(attachmentsMap);
//                            }
//                        } else if (contentConfig.getMsgType() == 4) {
//                            if (StringUtils.isNotBlank(contentConfig.getVideoMediaId())) {
//                                Map videoMap = new HashMap();
//                                videoMap.put("media_id", contentConfig.getVideoMediaId());
//                                Map attachmentsMap = new HashMap();
//                                attachmentsMap.put("msgtype", "video");
//                                attachmentsMap.put("video", videoMap);
//                                attachmentsList.add(attachmentsMap);
//                            }
//                        }
//                    }
//                }
//            }
//
//            map.put("attachments", attachmentsList);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.error(map.toString());
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.error(jsonObject.toString());
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                //记录发送成功数
//                QywxWelcomeStatisticsRecord statisticsRecord = new QywxWelcomeStatisticsRecord();
//                statisticsRecord.setWelcomeMainId(welcomeId);
//                statisticsRecord.setSendCount(1L);
//                String content = JSONObject.toJSON(statisticsRecord).toString();
//                updateSendCountChannel.send(MessageBuilder.withPayload(content).build());

                String msgId = jsonObject.getString("msgid");
                if (StringUtils.isNotBlank(jsonObject.getString("fail_list"))) {
                }
                {
                    List<String> arr = JSONArray.parseArray(jsonObject.getString("fail_list"), String.class);
                    log.error(arr.toString());
                }
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("发送欢迎语失败：{}", e.toString());
            throw new BusinessException("发送欢迎语失败：" + e.getMessage());
        }
    }


    public int testPull(String accountId) {
        //装配内容，发送欢迎语
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.GET_GROUP_MSG_RESULT_URL + "access_token=" + token;
        //String mediaId = customerSignService.getSystemConfig("QG_QYWX_MEDIA_ID");
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", "msgD6OtBgAAQSWriQI1WKY2CYTHdkSPFg");
            map.put("userid", "FengXueDong");
            map.put("limit", 50);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.error(map.toString());
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.error(jsonObject.toString());
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("发送欢迎语失败：{}", e.toString());
            throw new BusinessException("发送欢迎语失败：" + e.getMessage());
        }
    }



    public int testPullStatus(String accountId) {
        //装配内容，发送欢迎语
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.GET_GROUP_MSG_TASK_URL + "access_token=" + token;
        //String mediaId = customerSignService.getSystemConfig("QG_QYWX_MEDIA_ID");
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", "msgD6OtBgAAQSWriQI1WKY2CYTHdkSPFg");
            map.put("limit", 50);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.error(map.toString());
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.error(jsonObject.toString());
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("发送欢迎语失败：{}", e.toString());
            throw new BusinessException("发送欢迎语失败：" + e.getMessage());
        }
    }


    public void sendGroupMsgSelect(String param) {
        log.info("发送群发消息入参{}", param);
        Long groupMsgId = null;
        if(StringUtils.isNotBlank(param)){
            groupMsgId = Long.valueOf(param);
        }
        //根据id获取最新的未发送消息
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = qywxGroupMsgMainInfoMapper.getMsgMainInfoForSend(groupMsgId);
        if(qywxGroupMsgMainInfo != null && qywxGroupMsgMainInfo.getId() != null){
            log.error("本次执行群发消息{}",qywxGroupMsgMainInfo);
            sendGroupMsgEntry(qywxGroupMsgMainInfo.getId());
        }else{
            return;
        }
    }


    @StreamListener(ScheduleMsgChannel.BUILD_ALL_CHARGE_INPUT)
    public void allCustomerSendGroupMsg(Message<SendAllChargeDto> message){
        SendAllChargeDto sendAllChargeDto = message.getPayload();
        log.error("群发好友关系执行消费信息：{}",sendAllChargeDto.getQywxGroupMsgMainInfo().getId());
        if (CollectionUtils.isEmpty(sendAllChargeDto.getUserIdList())){
            log.error("发送人员为空，发送失败！");
            this.updateNoticeStatus(sendAllChargeDto.getQywxGroupMsgMainInfo().getId(), 3, "发送人员为空，发送失败!");
            return;
        }
        ChargeCustomerDto chargeCustomerDto = new ChargeCustomerDto();
        chargeCustomerDto.setContentConfigList(sendAllChargeDto.getContentConfigList());
        chargeCustomerDto.setMaterialContentDetailDTOList(sendAllChargeDto.getMaterialContentDetailDTOList());
        chargeCustomerDto.setQywxGroupMsgMainInfo(sendAllChargeDto.getQywxGroupMsgMainInfo());
        sendAllChargeDto.getUserIdList().forEach(userId->{
            chargeCustomerDto.setUserId(userId);
            buildChargeCustomerChannel.send(MessageBuilder.withPayload(chargeCustomerDto).build());
        });
    }


    @StreamListener(ScheduleMsgChannel.BUILD_CHARGE_CUSTOMER_INPUT)
    public void buildChargeCustomer(Message<ChargeCustomerDto> message){
        ChargeCustomerDto chargeCustomerDto = message.getPayload();
        log.error("群发好友执行消费：{}",chargeCustomerDto.getQywxGroupMsgMainInfo().getId());
        if (StringUtils.isBlank(chargeCustomerDto.getUserId())){
            log.error("发送人员为空，发送失败！");
            this.updateNoticeStatus(chargeCustomerDto.getQywxGroupMsgMainInfo().getId(), 3, "发送人员为空，发送失败!");
            return;
        }
        GroupSendMsgDto groupSendMsgDto = new GroupSendMsgDto();
        groupSendMsgDto.setContentConfigList(chargeCustomerDto.getContentConfigList());
        groupSendMsgDto.setMaterialContentDetailDTOList(chargeCustomerDto.getMaterialContentDetailDTOList());
        groupSendMsgDto.setQywxGroupMsgMainInfo(chargeCustomerDto.getQywxGroupMsgMainInfo());
        //查询人员
        QywxGroupEffectCustomerMapping customerMapping = qywxCutomerUserRelationMapper.findUserRelationByUserId(chargeCustomerDto.getUserId());
        if (customerMapping == null) {
            log.error("发送人员为空，发送失败！");
            this.updateNoticeStatus(chargeCustomerDto.getQywxGroupMsgMainInfo().getId(), 3, "发送人员为空，发送失败!");
            return;
        }
        groupSendMsgDto.setQywxGroupEffectCustomerMapping(customerMapping);
        buildGroupChargeChannel.send(MessageBuilder.withPayload(groupSendMsgDto).build());
    }

    @StreamListener(ScheduleMsgChannel.BUILD_GROUP_CHARGE_INPUT)
    public void groupSendMsg(Message<GroupSendMsgDto> message){
        GroupSendMsgDto groupSendMsgDto = message.getPayload();
        log.error("群发消息信息数据：{}",groupSendMsgDto);
        if (groupSendMsgDto == null){
            log.error("异步发送群发信息，发送对象为空，执行结束");
            return;
        }
        //根据userId 分组，
        List<QywxGroupUserSendLog> contentMap = new ArrayList<>();
        //查询内容
        if (Objects.equals(groupSendMsgDto.getQywxGroupMsgMainInfo().getConfigType(),1)) {
            //装配内容数据
            contentMap = setMsgConfigContent(groupSendMsgDto.getQywxGroupMsgMainInfo(), groupSendMsgDto.getContentConfigList(), groupSendMsgDto.getQywxGroupEffectCustomerMapping());
            log.info("本地配置的参数{}",contentMap.toString());
        } else {
            //装配内容数据
            contentMap = setMsgMaterialContent(groupSendMsgDto.getQywxGroupMsgMainInfo(),groupSendMsgDto.getMaterialContentDetailDTOList(), groupSendMsgDto.getQywxGroupEffectCustomerMapping());
            log.info("内容树配置的参数{}",contentMap.toString());
        }
        //发送数据
        if(CollectionUtils.isNotEmpty(contentMap)){
            for(QywxGroupUserSendLog userSendLog : contentMap){
                //丢入消息通道
                messageChannel.send(MessageBuilder.withPayload(userSendLog).build());
            }
        }
    }


    public void sendGroupMsgEntry(Long groupMsgId){
        //首先查询主表数据
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = qywxGroupMsgMainInfoMapper.getMsgMainInfoForSend(groupMsgId);
        log.info("发送消息的内容{}", qywxGroupMsgMainInfo);
        if(qywxGroupMsgMainInfo == null || qywxGroupMsgMainInfo.getId() == null){
            log.error("发送群发消息入口，无此数据");
            return;
        }
        if(qywxGroupMsgMainInfo.getNoticeStatus() != 0 && qywxGroupMsgMainInfo.getNoticeStatus() != 3){
            log.error("消息状态是待发送或者发送失败才可以发送");
            return;
        }
        //优先修改状态为：发送中
        int i  = this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(),1,null);
        //查询本地内容
        List<QywxGroupContentConfig> contentConfigList = Lists.newArrayList();;
        //查询内容详情
        List<MaterialContentDetailDTO> materialContentDetailDTOList;
        if (qywxGroupMsgMainInfo.getConfigType() == 1) {
            materialContentDetailDTOList = Lists.newArrayList();
            contentConfigList = qywxGroupContentConfigMapper.getContentConfigByGroupId(qywxGroupMsgMainInfo.getId());
            if (StringUtils.isBlank(qywxGroupMsgMainInfo.getContent()) && CollectionUtils.isEmpty(contentConfigList)) {
                log.error("本地配置内容位空，发送失败！");
                this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(), 3, "本地配置内容为空，发送失败!");
                return;
            }
        }else {
            //获取内容树的数据
            TCustomerCommonMaterialMapping tCustomerCommonMaterialMapping = materialMappingDao.selectOne(Wrappers.lambdaQuery(TCustomerCommonMaterialMapping.class)
                    .eq(TCustomerCommonMaterialMapping::getIsDeleted, 0)
                    .eq(TCustomerCommonMaterialMapping::getSourceType, 2)
                    .eq(TCustomerCommonMaterialMapping::getSourceId, qywxGroupMsgMainInfo.getId()));
            if (tCustomerCommonMaterialMapping != null && tCustomerCommonMaterialMapping.getMaterialId() != null) {
                materialContentDetailDTOList = cmsClient.getContentById(tCustomerCommonMaterialMapping.getMaterialId().intValue(), qywxGroupMsgMainInfo.getAccountId()).getData();
                if (CollectionUtils.isEmpty(materialContentDetailDTOList)) {
                    log.error("内容树的内容配置为空，发送失败！");
                    this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(), 3, "内容树的内容配置为空，发送失败!");
                    return;
                }
            } else {
                materialContentDetailDTOList = Lists.newArrayList();
            }
        }

        if (Objects.equals(qywxGroupMsgMainInfo.getSendMemberType(),1)){
            List<String> userIdList = qywxGroupEffectChargeMappingMapper.selectEffectCharge(qywxGroupMsgMainInfo.getId());
            if (CollectionUtils.isEmpty(userIdList)){
                log.error("发送人员为空，发送失败！");
                this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(), 3, "发送人员为空，发送失败!");
                return;
            }
            SendAllChargeDto sendAllChargeDto = new SendAllChargeDto();
            sendAllChargeDto.setQywxGroupMsgMainInfo(qywxGroupMsgMainInfo);
            sendAllChargeDto.setUserIdList(userIdList);
            sendAllChargeDto.setMaterialContentDetailDTOList(materialContentDetailDTOList);
            sendAllChargeDto.setContentConfigList(contentConfigList);
            buildAllCustomerChannel.send(MessageBuilder.withPayload(sendAllChargeDto).build());
        }else {
            try {
                //查询人员
                List<QywxGroupEffectCustomerMapping> groupEffectCustomerMappingList = qywxGroupEffectCustomerMappingMapper.getEffectCustomerListByGroupId(qywxGroupMsgMainInfo.getId());
                if (CollectionUtils.isEmpty(groupEffectCustomerMappingList)) {
                    log.error("发送人员为空，发送失败！");
                    this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(), 3, "发送人员为空，发送失败!");
                    return;
                }
                List<QywxGroupContentConfig> finalContentConfigList = contentConfigList;
                GroupSendMsgDto groupSendMsgDto = new GroupSendMsgDto();
                groupSendMsgDto.setContentConfigList(finalContentConfigList);
                groupSendMsgDto.setMaterialContentDetailDTOList(materialContentDetailDTOList);
                groupSendMsgDto.setQywxGroupMsgMainInfo(qywxGroupMsgMainInfo);
                groupEffectCustomerMappingList.forEach(customerMapping -> {
                    groupSendMsgDto.setQywxGroupEffectCustomerMapping(customerMapping);
                    buildGroupChargeChannel.send(MessageBuilder.withPayload(groupSendMsgDto).build());
                });
            }catch (Exception e){
                log.error("发送数据异常，原因{}", e.toString());
                this.updateNoticeStatus(qywxGroupMsgMainInfo.getId(), 3, "内容树的内容配置为空，发送失败!");
                return;
            }
        }
    }



    private int updateNoticeStatus(Long id, Integer status, String remark){
        UpdateNoticeStatusDTO updateNoticeStatusDTO = new UpdateNoticeStatusDTO();
        updateNoticeStatusDTO.setId(id);
        updateNoticeStatusDTO.setNoticeStatus(status);
        updateNoticeStatusDTO.setRemark(remark);
        return  qywxGroupMsgMainInfoMapper.updateNoticeStatus(updateNoticeStatusDTO);
    }


    //根据业务员获取业务员门店信息，门店名称与地址
    private List<SalesmanInfoForQYWX> setChargeUserStoreInfo(List<QywxGroupEffectCustomerMapping> groupEffectCustomerMappingList){
        //去重获取chargeUserId
        List<Long> chargeUserIds = groupEffectCustomerMappingList.parallelStream().map(QywxGroupEffectCustomerMapping :: getChargeUserId).distinct().collect(Collectors.toList());
        //获取业务员的门店信息
        List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(chargeUserIds).getData();
        return salesmanInfoForQYWXList;
    }

    private List<SalesmanInfoForQYWX> setChargeUserStoreInfo(QywxGroupEffectCustomerMapping customerMapping){
        //获取业务员的门店信息
        List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(Arrays.asList(customerMapping.getChargeUserId())).getData();
        return salesmanInfoForQYWXList;
    }

    private List<QywxGroupUserSendLog> setMsgConfigContentTwo(QywxGroupMsgMainInfo qywxGroupMsgMainInfo,List<QywxGroupContentConfig> contentConfigList, List<QywxGroupEffectCustomerMapping> groupEffectCustomerMappingList) {
        List<QywxGroupUserSendLog> resultList = new ArrayList<>();
        Map<String, List<QywxGroupEffectCustomerMapping>> groupedMap = groupEffectCustomerMappingList.stream().collect(Collectors.groupingBy(QywxGroupEffectCustomerMapping::getUserId));
        List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = setChargeUserStoreInfo(groupEffectCustomerMappingList);
        if(groupedMap != null){
            //code 是userId，value 是QywxGroupEffectCustomerMapping list
            groupedMap.forEach((userId,list) ->{
                if(StringUtils.isNotBlank(userId) && CollectionUtils.isNotEmpty(list)) {
                    Map contentMap = new HashMap();
                    QywxGroupUserSendLog qywxGroupUserSendLog = new QywxGroupUserSendLog();
                    contentMap.put("chat_type", "single");
                    List<String> externalUserList = list.parallelStream().map(QywxGroupEffectCustomerMapping::getExternalUserid).collect(Collectors.toList());
                    contentMap.put("external_userid", externalUserList);
                    contentMap.put("sender", userId);
                    contentMap.put("allow_select", true);

                    Long chargeUserId = list.get(0).getChargeUserId();
                    Optional<SalesmanInfoForQYWX> first = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(chargeUserId)).findFirst();
                    Long storeId = null;
                    if (first.isPresent()){
                        storeId = first.get().getStoreId();
                    }
                    //设置text.content
                    if (StringUtils.isNotBlank(qywxGroupMsgMainInfo.getContent())) {
                        String storeInfoContent = "";
                        if(qywxGroupMsgMainInfo.getStoreConfigFlag() != null && qywxGroupMsgMainInfo.getStoreConfigFlag() == 1){
                            //拼接门店名称和地址
                            if(CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)){
                                SalesmanInfoForQYWX salesmanInfoForQYWX = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(chargeUserId)).findFirst().orElse(null);
                                if (salesmanInfoForQYWX != null && StringUtils.isNotBlank(salesmanInfoForQYWX.getStoreName())) {
                                    String storeName = "门店：" + (StringUtils.isNotBlank(salesmanInfoForQYWX.getAbbreviation()) ? salesmanInfoForQYWX.getAbbreviation() : salesmanInfoForQYWX.getName()) + " ";
                                    String address = StringUtils.isNotBlank(salesmanInfoForQYWX.getAddress2()) ? ("地址：" + salesmanInfoForQYWX.getAddress2()) : "";
                                    storeInfoContent = storeName + "\n" + address;
                                }
                            }
                        }
                        Map textMap = new HashMap();
                        String content = qywxCommonUtil.replaceContent(chargeUserId, qywxGroupMsgMainInfo.getContent());


                        textMap.put("content", content + "\n" + storeInfoContent);
                        contentMap.put("text", textMap);
                    }
                    if (CollectionUtils.isNotEmpty(contentConfigList)) {
                        List<Map> attachmentsMapList = new ArrayList<>();
                        for (QywxGroupContentConfig contentConfig : contentConfigList) {
                            if (contentConfig.getMsgType() != null) {
                                if (contentConfig.getMsgType() == 1) {
                                    if (StringUtils.isNotBlank(contentConfig.getImageMediaId())) {
                                        Map imageMap = new HashMap();
                                        imageMap.put("media_id", contentConfig.getImageMediaId());
                                        Map attachmentsMap = new HashMap();
                                        attachmentsMap.put("msgtype", "image");
                                        attachmentsMap.put("image", imageMap);
                                        attachmentsMapList.add(attachmentsMap);
                                    }
                                } else if (contentConfig.getMsgType() == 2) {
                                    if (StringUtils.isNotBlank(contentConfig.getLinkUrl())) {
                                        //判断是否携带参数
                                        String addParam = "";
/*                                        if(contentConfig.getChargeUserFlag() != null && contentConfig.getChargeUserFlag() == 1){
                                            addParam = addParam+"&chargeUserId=" + chargeUserId;
                                        }*/
                                        if(contentConfig.getIsAssignSharer() != null && contentConfig.getIsAssignSharer() == 1){
                                            if(contentConfig.getLinkUrl().contains("?")){
                                                if(contentConfig.getLinkUrl().endsWith("?")){
                                                    addParam = addParam+"&salesmanId="+chargeUserId;
                                                }else {
                                                    addParam = addParam+"&salesmanId="+chargeUserId;
                                                }
                                            }else {
                                                addParam = addParam+"?salesmanId="+chargeUserId;
                                            }
                                        }
                                        Map linkMap = new HashMap();
                                        linkMap.put("picurl", contentConfig.getLinkPicUrl());
                                        linkMap.put("url", contentConfig.getLinkUrl()+addParam);
                                        linkMap.put("title", contentConfig.getLinkTitle());
                                        Map attachmentsMap = new HashMap();
                                        attachmentsMap.put("msgtype", "link");
                                        attachmentsMap.put("link", linkMap);
                                        attachmentsMapList.add(attachmentsMap);
                                    }
                                } else if (contentConfig.getMsgType() == 3) {
                                    if (StringUtils.isNotBlank(contentConfig.getMiniprogramPicMediaId()) && StringUtils.isNotBlank(contentConfig.getMiniprogramAppid()) && StringUtils.isNotBlank(contentConfig.getMiniprogramPage())) {
                                        //判断是否携带参数
                                        String addParam = "";
                                        //if(contentConfig.getChargeUserFlag() != null && contentConfig.getChargeUserFlag() == 1){
                                            addParam = "&gmId=" + qywxGroupMsgMainInfo.getId();
                                            if(contentConfig.getMiniprogramPage().contains("pages/activityDetail/main")){
                                                addParam = addParam + "&salesmanId=" + chargeUserId+"&storeId="+(storeId==null?"":storeId);
                                            }else if(contentConfig.getMiniprogramPage().contains("pages/marketingPayment/index/main")){
                                                if (storeId == null){
                                                    log.error("当前门店未开启收银支付和营销支付，不进行发送");
                                                    continue;
                                                }
                                                FindStoreByIdOutDto dto = new FindStoreByIdOutDto();
                                                dto.setOrgId(storeId);
                                                //23347 配置营销支付活动，校验门店
                                                Result<FindStoreByIdOutDto> result = orgClient.findStoreByOrgId(dto);
                                                if (!result.getSuccess()||result.getData()==null||
                                                        !Objects.equals(result.getData().getIsCashierEnabled(),1)
                                                        ||!Objects.equals(result.getData().getIzImPayActivityEnabled(),1)){
                                                    log.error("当前门店未开启收银支付和营销支付，不进行发送");
                                                    continue;
                                                }
                                                addParam = addParam +  "&sId=" + chargeUserId + "&salesmanId=" + chargeUserId+"&storeOrgId="+(storeId==null?"":storeId)+  "&forwardType=8";
                                            }else  {
                                                addParam = addParam +  "&sId=" + chargeUserId + "&salesmanId=" + chargeUserId+"&storeId="+(storeId==null?"":storeId);
                                            }
                                        //}
                                        addParam = addParam +  "&subPlatformType=3";

                                        Map miniprogramMap = new HashMap();
                                        miniprogramMap.put("title", contentConfig.getMiniprogramTitle());
                                        miniprogramMap.put("pic_media_id", contentConfig.getMiniprogramPicMediaId());
                                        miniprogramMap.put("appid", contentConfig.getMiniprogramAppid());
                                        //增加发送消息的判断F
                                        String page = "/" + contentConfig.getMiniprogramPage() + ".html?" + contentConfig.getMiniprogramParam() + addParam;
                                        if(StringUtils.isNotBlank(contentConfig.getMiniprogramContentTemplateId())){
                                            page = page + "&" +  contentConfig.getMiniprogramContentTemplateId() + "&platformType=1&cate=s0011c0010&channelCode=s0011";
                                        }

                                        if(qywxGroupMsgMainInfo.getFullGroupMsgId() == null){
                                            //一级
                                            page = page + "&mPushId=" + qywxGroupMsgMainInfo.getId();
                                        }else {
                                            //二级
                                            page = page + "&mPushId=" + qywxGroupMsgMainInfo.getFullGroupMsgId() + "&mSubPushId=" + qywxGroupMsgMainInfo.getId();
                                        }

                                        miniprogramMap.put("page", page);
                                        Map attachmentsMap = new HashMap();
                                        attachmentsMap.put("msgtype", "miniprogram");
                                        attachmentsMap.put("miniprogram", miniprogramMap);
                                        attachmentsMapList.add(attachmentsMap);
                                    }
                                } else if (contentConfig.getMsgType() == 4) {
                                    if (StringUtils.isNotBlank(contentConfig.getVideoMediaId())) {
                                        Map videoMap = new HashMap();
                                        videoMap.put("media_id", contentConfig.getVideoMediaId());
                                        Map attachmentsMap = new HashMap();
                                        attachmentsMap.put("msgtype", "video");
                                        attachmentsMap.put("video", videoMap);
                                        attachmentsMapList.add(attachmentsMap);
                                    }
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(attachmentsMapList)) {
                            contentMap.put("attachments", attachmentsMapList);
                        }
                    }

                    qywxGroupUserSendLog.setGroupMsgId(qywxGroupMsgMainInfo.getId());
                    qywxGroupUserSendLog.setQywxUserId(userId);
                    qywxGroupUserSendLog.setAccountId(qywxGroupMsgMainInfo.getAccountId());
                    qywxGroupUserSendLog.setQywxExternalUserids(JSONObject.toJSONString(externalUserList));
                    qywxGroupUserSendLog.setSendContent(JSONObject.toJSONString(contentMap));
                    qywxGroupUserSendLog.setChargeUserId(chargeUserId);
                    resultList.add(qywxGroupUserSendLog);
                }
            });
        }
        return resultList;
    }




    private List<QywxGroupUserSendLog> setMsgConfigContent(QywxGroupMsgMainInfo qywxGroupMsgMainInfo,
                                                           List<QywxGroupContentConfig> contentConfigList, QywxGroupEffectCustomerMapping customerMapping) {
        List<QywxGroupUserSendLog> resultList = new ArrayList<>();
/*        Map<String, QywxGroupEffectCustomerMapping> groupedMap = groupEffectCustomerMappingList.stream()
                .collect(Collectors.toMap(QywxGroupEffectCustomerMapping::getUserId,q->q));*/
        List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = setChargeUserStoreInfo(customerMapping);
        if(customerMapping != null){
            //code 是userId，value 是QywxGroupEffectCustomerMapping list
                if(StringUtils.isNotBlank(customerMapping.getUserId()) && customerMapping!=null) {
                    List<String> externalUserList = JSONArray.parseArray(customerMapping.getExternalUserid(), String.class);
                    if (CollectionUtils.isEmpty(externalUserList)){
                        return null;
                    }
                    List<List<?>> lists = splitList(externalUserList, 10000);
                    for (List<?> list: lists) {
                        Map contentMap = new HashMap();
                        QywxGroupUserSendLog qywxGroupUserSendLog = new QywxGroupUserSendLog();
                        contentMap.put("chat_type", "single");
                        contentMap.put("external_userid", list);
                        contentMap.put("sender", customerMapping.getUserId());
                        contentMap.put("allow_select", true);
                        Long chargeUserId = customerMapping.getChargeUserId();
                        Long storeId = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(chargeUserId)).findFirst().orElse(null).getStoreId();
                        //设置text.content
                        if (StringUtils.isNotBlank(qywxGroupMsgMainInfo.getContent())) {
                            String storeInfoContent = "";
                            if(qywxGroupMsgMainInfo.getStoreConfigFlag() != null && qywxGroupMsgMainInfo.getStoreConfigFlag() == 1){
                                //拼接门店名称和地址
                                if(CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)){
                                    SalesmanInfoForQYWX salesmanInfoForQYWX = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(chargeUserId)).findFirst().orElse(null);
                                    if (salesmanInfoForQYWX != null && StringUtils.isNotBlank(salesmanInfoForQYWX.getStoreName())) {
                                        String storeName = "门店：" + (StringUtils.isNotBlank(salesmanInfoForQYWX.getAbbreviation()) ? salesmanInfoForQYWX.getAbbreviation() : salesmanInfoForQYWX.getName()) + " ";
                                        String address = StringUtils.isNotBlank(salesmanInfoForQYWX.getAddress2()) ? ("地址：" + salesmanInfoForQYWX.getAddress2()) : "";
                                        storeInfoContent = storeName + "\n" + address;
                                    }
                                }
                            }
                            Map textMap = new HashMap();
                            String content = qywxCommonUtil.replaceContent(chargeUserId, qywxGroupMsgMainInfo.getContent());


                            textMap.put("content", content + "\n" + storeInfoContent);
                            contentMap.put("text", textMap);
                        }
                        if (CollectionUtils.isNotEmpty(contentConfigList)) {
                            List<Map> attachmentsMapList = new ArrayList<>();
                            for (QywxGroupContentConfig contentConfig : contentConfigList) {
                                if (contentConfig.getMsgType() != null) {
                                    if (contentConfig.getMsgType() == 1) {
                                        if (StringUtils.isNotBlank(contentConfig.getImageMediaId())) {
                                            Map imageMap = new HashMap();
                                            imageMap.put("media_id", contentConfig.getImageMediaId());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "image");
                                            attachmentsMap.put("image", imageMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getMsgType() == 2) {
                                        if (StringUtils.isNotBlank(contentConfig.getLinkUrl())) {
                                            //判断是否携带参数
                                            String addParam = "";
/*                                        if(contentConfig.getChargeUserFlag() != null && contentConfig.getChargeUserFlag() == 1){
                                            addParam = addParam+"&chargeUserId=" + chargeUserId;
                                        }*/
                                            if(contentConfig.getIsAssignSharer() != null && contentConfig.getIsAssignSharer() == 1){
                                                if(contentConfig.getLinkUrl().contains("?")){
                                                    if(contentConfig.getLinkUrl().endsWith("?")){
                                                        addParam = addParam+"&salesmanId="+chargeUserId;
                                                    }else {
                                                        addParam = addParam+"&salesmanId="+chargeUserId;
                                                    }
                                                }else {
                                                    addParam = addParam+"?salesmanId="+chargeUserId;
                                                }
                                            }
                                            Map linkMap = new HashMap();
                                            linkMap.put("picurl", contentConfig.getLinkPicUrl());
                                            linkMap.put("url", contentConfig.getLinkUrl()+addParam);
                                            linkMap.put("title", contentConfig.getLinkTitle());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "link");
                                            attachmentsMap.put("link", linkMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getMsgType() == 3) {
                                        if (StringUtils.isNotBlank(contentConfig.getMiniprogramPicMediaId()) && StringUtils.isNotBlank(contentConfig.getMiniprogramAppid()) && StringUtils.isNotBlank(contentConfig.getMiniprogramPage())) {
                                            //判断是否携带参数
                                            String addParam = "";
                                            //if(contentConfig.getChargeUserFlag() != null && contentConfig.getChargeUserFlag() == 1){
                                            addParam = "&gmId=" + qywxGroupMsgMainInfo.getId();
                                            if(contentConfig.getMiniprogramPage().contains("pages/activityDetail/main")){
                                                addParam = addParam + "&salesmanId=" + chargeUserId+"&storeId="+storeId;
                                            }else if(contentConfig.getMiniprogramPage().contains("pages/marketingPayment/index/main")){
                                                FindStoreByIdOutDto dto = new FindStoreByIdOutDto();
                                                dto.setOrgId(storeId);
                                                //23347 配置营销支付活动，校验门店
                                                Result<FindStoreByIdOutDto> result = orgClient.findStoreByOrgId(dto);
                                                if (!result.getSuccess()||result.getData()==null||
                                                        !Objects.equals(result.getData().getIsCashierEnabled(),1)
                                                        ||!Objects.equals(result.getData().getIzImPayActivityEnabled(),1)){
                                                    log.error("当前门店未开启收银支付和营销支付，不进行发送");
                                                    continue;
                                                }
                                                addParam = addParam +  "&sId=" + chargeUserId + "&salesmanId=" + chargeUserId+"&storeOrgId="+storeId+  "&forwardType=8";
                                            }else  {
                                                addParam = addParam +  "&sId=" + chargeUserId + "&salesmanId=" + chargeUserId+"&storeId="+storeId;
                                            }
                                            //}
                                            addParam = addParam +  "&subPlatformType=3";

                                            Map miniprogramMap = new HashMap();
                                            miniprogramMap.put("title", contentConfig.getMiniprogramTitle());
                                            miniprogramMap.put("pic_media_id", contentConfig.getMiniprogramPicMediaId());
                                            miniprogramMap.put("appid", contentConfig.getMiniprogramAppid());
                                            //增加发送消息的判断F
                                            String page = "/" + contentConfig.getMiniprogramPage() + ".html?" + contentConfig.getMiniprogramParam() + addParam;
                                            if(StringUtils.isNotBlank(contentConfig.getMiniprogramContentTemplateId())){
                                                page = page + "&" +  contentConfig.getMiniprogramContentTemplateId() + "&platformType=1&cate=s0011c0010&channelCode=s0011";
                                            }

                                            if(qywxGroupMsgMainInfo.getFullGroupMsgId() == null){
                                                //一级
                                                page = page + "&mPushId=" + qywxGroupMsgMainInfo.getId();
                                            }else {
                                                //二级
                                                page = page + "&mPushId=" + qywxGroupMsgMainInfo.getFullGroupMsgId() + "&mSubPushId=" + qywxGroupMsgMainInfo.getId();
                                            }

                                            miniprogramMap.put("page", page);
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "miniprogram");
                                            attachmentsMap.put("miniprogram", miniprogramMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getMsgType() == 4) {
                                        if (StringUtils.isNotBlank(contentConfig.getVideoMediaId())) {
                                            Map videoMap = new HashMap();
                                            videoMap.put("media_id", contentConfig.getVideoMediaId());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "video");
                                            attachmentsMap.put("video", videoMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(attachmentsMapList)) {
                                contentMap.put("attachments", attachmentsMapList);
                            }
                        }

                        qywxGroupUserSendLog.setGroupMsgId(qywxGroupMsgMainInfo.getId());
                        qywxGroupUserSendLog.setQywxUserId(customerMapping.getUserId());
                        qywxGroupUserSendLog.setAccountId(qywxGroupMsgMainInfo.getAccountId());
                        qywxGroupUserSendLog.setQywxExternalUserids(JSONObject.toJSONString(list));
                        qywxGroupUserSendLog.setSendContent(JSONObject.toJSONString(contentMap));
                        qywxGroupUserSendLog.setChargeUserId(chargeUserId);
                        resultList.add(qywxGroupUserSendLog);
                    }
                }
        }
        return resultList;
    }

    private List<QywxGroupUserSendLog> setMsgMaterialContent(QywxGroupMsgMainInfo qywxGroupMsgMainInfo ,
                                                             List<MaterialContentDetailDTO> materialContentDetailDTOList ,
                                                             QywxGroupEffectCustomerMapping customerMapping) {
        List<QywxGroupUserSendLog> resultList = new ArrayList<>();
        //先分类，文本合并一起，附件限制9个
        List<MaterialContentDetailDTO> textList = new ArrayList<>();
        List<MaterialContentDetailDTO> attachmentList = new ArrayList<>();

        List<TCustomerCommonMaterialContentMapping> contentMappings = materialContentMappingDao.selectList(Wrappers.lambdaQuery(TCustomerCommonMaterialContentMapping.class)
                .eq(TCustomerCommonMaterialContentMapping::getIsDeleted, 0)
                .eq(TCustomerCommonMaterialContentMapping::getSourceType, 2)
                .eq(TCustomerCommonMaterialContentMapping::getSourceId, qywxGroupMsgMainInfo.getId()));
        Map<Integer,TCustomerCommonMaterialContentMapping> materialContentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(contentMappings)){
            materialContentMap= contentMappings.stream().collect(Collectors.toMap(TCustomerCommonMaterialContentMapping::getMaterialContentId, Function.identity()));
        }

        for (MaterialContentDetailDTO materialContentDetailDTO : materialContentDetailDTOList) {
            if (!materialContentMap.isEmpty()&&materialContentMap.containsKey(materialContentDetailDTO.getMaterialContentId())){
                TCustomerCommonMaterialContentMapping contentMapping = materialContentMap.get(materialContentDetailDTO.getMaterialContentId());
                if (materialContentDetailDTO.getType() == 5) {
                    //如果是文本，并且文本内容不为空
                    if (StringUtils.isNotBlank(materialContentDetailDTO.getContent())) {
                        if (StringUtils.isNotBlank(contentMapping.getPageDescription())){
                            materialContentDetailDTO.setContent(contentMapping.getPageDescription());
                        }
                        textList.add(materialContentDetailDTO);
                    }
                } else {
                    if (StringUtils.isNotBlank(contentMapping.getPageTitle())){
                        materialContentDetailDTO.setLandingPageTitle(contentMapping.getPageTitle());
                    }
                    if (StringUtils.isNotBlank(contentMapping.getPageDescription())){
                        materialContentDetailDTO.setLinkDescription(contentMapping.getPageDescription());
                    }
                    attachmentList.add(materialContentDetailDTO);
                }
            }
        }
        String content = "";
        if (CollectionUtils.isNotEmpty(textList)) {
            for (MaterialContentDetailDTO materialContentDetailDTO : textList) {
                content = content + materialContentDetailDTO.getContent() + "\n";
            }
        }

        if (CollectionUtils.isNotEmpty(attachmentList) && attachmentList.size() > 9) {
            //获取前九个
            attachmentList = attachmentList.subList(0, 9);
        }

/*        Map<String, QywxGroupEffectCustomerMapping> groupedMap = groupEffectCustomerMappingList.stream()
                .collect(Collectors.toMap(QywxGroupEffectCustomerMapping::getUserId,q->q));*/

        if (customerMapping != null) {
            //code 是userId，value 是QywxGroupEffectCustomerMapping list
            String finalContent = content;
            List<MaterialContentDetailDTO> finalAttachmentList = attachmentList;
            List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = setChargeUserStoreInfo(customerMapping);

                if (StringUtils.isNotBlank(customerMapping.getUserId()) && customerMapping!=null) {
                    List<String> externalUserList = JSONArray.parseArray(customerMapping.getExternalUserid(), String.class);
                    if (CollectionUtils.isEmpty(externalUserList)){
                        return null;
                    }
                    List<List<?>> lists = splitList(externalUserList, 10000);
                    for (List<?> list:lists){
                        Map contentMap = new HashMap();
                        QywxGroupUserSendLog qywxGroupUserSendLog = new QywxGroupUserSendLog();
                        contentMap.put("chat_type", "single");
                        contentMap.put("external_userid", list);
                        contentMap.put("sender", customerMapping.getUserId());
                        contentMap.put("allow_select", true);
                        Long chargeUserId = customerMapping.getChargeUserId();
                        Map textMap = new HashMap();
                        String storeInfoContent = "";
                        if (StringUtils.isNotBlank(finalContent)){
                            if(qywxGroupMsgMainInfo.getStoreConfigFlag() != null && qywxGroupMsgMainInfo.getStoreConfigFlag() == 1){
                                //拼接门店名称和地址
                                if(CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)){
                                    SalesmanInfoForQYWX salesmanInfoForQYWX = salesmanInfoForQYWXList.parallelStream().filter(x -> x.getId().equals(chargeUserId)).findFirst().orElse(null);
                                    if (salesmanInfoForQYWX != null && StringUtils.isNotBlank(salesmanInfoForQYWX.getStoreName())) {
                                        String storeName = "门店：" + (StringUtils.isNotBlank(salesmanInfoForQYWX.getAbbreviation()) ? salesmanInfoForQYWX.getAbbreviation() : salesmanInfoForQYWX.getName()) + " ";
                                        String address = StringUtils.isNotBlank(salesmanInfoForQYWX.getAddress2()) ? ("地址：" + salesmanInfoForQYWX.getAddress2()) : "";
                                        storeInfoContent = storeName + "\n" + address;
                                    }
                                }
                            }
                            textMap.put("content", finalContent+ "\n" + storeInfoContent);
                            contentMap.put("text", textMap);
                        }
                        if (CollectionUtils.isNotEmpty(finalAttachmentList)) {
                            List<Map> attachmentsMapList = new ArrayList<>();
                            for (MaterialContentDetailDTO contentConfig : finalAttachmentList) {
                                if (contentConfig.getType() != null) {
                                    if (contentConfig.getType() == 1 || contentConfig.getType() == 2) {
                                        if (StringUtils.isNotBlank(contentConfig.getWechatMaterialId())) {

                                            Map miniprogramMap = new HashMap();
                                            miniprogramMap.put("title", contentConfig.getLandingPageTitle());
                                            miniprogramMap.put("pic_media_id", contentConfig.getWechatMaterialId());
                                            miniprogramMap.put("appid", contentConfig.getShowPlatform());

                                            //拼接参数
                                            if (StringUtils.isNotBlank(contentConfig.getLandingPage())) {
                                                String[] strArr = contentConfig.getLandingPage().split("\\?");
                                                if (strArr.length == 2) {
                                                    String page = strArr[0];
                                                    String param = strArr[1];
                                                    String addParam = "";

                                                    //5s小程序
                                                    addParam = addParam +  "&subPlatformType=3";

                                                    if (contentConfig.getType() == 1) {
                                                        miniprogramMap.put("page", "/" + page + ".html?" + param + contentConfig.getRefId()+addParam);
                                                    }
                                                    if (contentConfig.getType() == 2) {
                                                        miniprogramMap.put("page", "/" + page + ".html?" + param + contentConfig.getRefCode()+addParam);
                                                    }
                                                }
                                            }

                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "miniprogram");
                                            attachmentsMap.put("miniprogram", miniprogramMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getType() == 3) {
                                        if (StringUtils.isNotBlank(contentConfig.getWechatMaterialId())) {
                                            Map videoMap = new HashMap();
                                            videoMap.put("media_id", contentConfig.getWechatMaterialId());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "video");
                                            attachmentsMap.put("video", videoMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getType() == 4) {
                                        if (StringUtils.isNotBlank(contentConfig.getWechatMaterialId())) {
                                            Map fileMap = new HashMap();
                                            fileMap.put("media_id", contentConfig.getWechatMaterialId());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "file");
                                            attachmentsMap.put("file", fileMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getType() == 6) {
                                        if (StringUtils.isNotBlank(contentConfig.getWechatMaterialId())) {
                                            Map imageMap = new HashMap();
                                            imageMap.put("media_id", contentConfig.getWechatMaterialId());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "image");
                                            attachmentsMap.put("image", imageMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    } else if (contentConfig.getType() == 7) {
                                        if (StringUtils.isNotBlank(contentConfig.getLinkAddress())) {
                                            Map linkMap = new HashMap();
                                            linkMap.put("picurl", contentConfig.getDisplayPictureUrl());
                                            linkMap.put("url", contentConfig.getLinkAddress());
                                            linkMap.put("desc", contentConfig.getLinkDescription());
                                            linkMap.put("title", contentConfig.getLandingPageTitle());
                                            Map attachmentsMap = new HashMap();
                                            attachmentsMap.put("msgtype", "link");
                                            attachmentsMap.put("link", linkMap);
                                            attachmentsMapList.add(attachmentsMap);
                                        }
                                    }
                                }
                            }
                            contentMap.put("attachments", attachmentsMapList);
                        }
                        qywxGroupUserSendLog.setGroupMsgId(qywxGroupMsgMainInfo.getId());
                        qywxGroupUserSendLog.setQywxUserId(customerMapping.getUserId());
                        qywxGroupUserSendLog.setAccountId(qywxGroupMsgMainInfo.getAccountId());
                        qywxGroupUserSendLog.setQywxExternalUserids(JSONObject.toJSONString(list));
                        qywxGroupUserSendLog.setSendContent(JSONObject.toJSONString(contentMap));
                        qywxGroupUserSendLog.setChargeUserId(chargeUserId);
                        resultList.add(qywxGroupUserSendLog);
                    }
                }
        }
        return resultList;
    }


    public void sendMsgConsume(QywxGroupUserSendLog qywxGroupUserSendLog) {
        //发送消息
        String token = qywxCommonUtil.getQYWXToken(qywxGroupUserSendLog.getAccountId());
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + qywxGroupUserSendLog.getAccountId() + "获取token失败！");
        }
        String uri = QywxConstantConfig.ADD_MSG_TEMPLATE_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            String bodyJsonData = qywxGroupUserSendLog.getSendContent();
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.info("打印企微群发发送消息体{}", qywxGroupUserSendLog.getSendContent());
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.info("打印企微群发发送结果体{}", jsonObject.toString());
            //插入数据
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                //记录发送成功数
                String msgId = jsonObject.getString("msgid");
                qywxGroupUserSendLog.setQywxMsgId(msgId);
                if (StringUtils.isNotBlank(jsonObject.getString("fail_list"))) {
                    List<String> arr = JSONArray.parseArray(jsonObject.getString("fail_list"), String.class);
                    qywxGroupUserSendLog.setFailExternalUserids(JSONObject.toJSONString(arr));
                }
                //发送业务员的结果
                qywxGroupUserSendLog.setSendUserResult(1);
                //默认未发送
                qywxGroupUserSendLog.setSendStatus(0);
                qywxGroupUserSendLog.setIsDeleted(0);
                qywxGroupUserSendLog.setCreatedBy("kafka");
                qywxGroupUserSendLog.setCreatedDate(new Date());
                qywxGroupUserSendLog.setModifiedBy("kafka");
                qywxGroupUserSendLog.setModifiedDate(new Date());
                qywxGroupUserSendLogMapper.insertSelective(qywxGroupUserSendLog);
                //更新主表状态:如果是1个成功就是都成功；1，3的时候可以修成为2
                qywxGroupMsgMainInfoMapper.updateSuccessNoticeStatus(qywxGroupUserSendLog.getGroupMsgId());
            } else {
                qywxGroupUserSendLog.setSendUserResult(0);
                qywxGroupUserSendLog.setFailReason(jsonObject.toString());
                //默认未发送
                qywxGroupUserSendLog.setSendStatus(0);
                qywxGroupUserSendLog.setIsDeleted(0);
                qywxGroupUserSendLog.setCreatedBy("kafka");
                qywxGroupUserSendLog.setCreatedDate(new Date());
                qywxGroupUserSendLog.setModifiedBy("kafka");
                qywxGroupUserSendLog.setModifiedDate(new Date());
                qywxGroupUserSendLogMapper.insertSelective(qywxGroupUserSendLog);
                //更新主表状态:如果是1个成功就是都成功；1，3的时候可以修成为2
                UpdateNoticeStatusDTO updateNoticeStatusDTO = new UpdateNoticeStatusDTO();
                updateNoticeStatusDTO.setId(qywxGroupUserSendLog.getGroupMsgId());
                updateNoticeStatusDTO.setRemark(jsonObject.toString());
                qywxGroupMsgMainInfoMapper.updateFailNoticeStatus(updateNoticeStatusDTO);
            }
            //发送补充数据的消息，补充用户信息，生成发送结果记录数据
            supplementInfoProduceChannel.send(MessageBuilder.withPayload(qywxGroupUserSendLog.getId()).build());
            return;
        } catch (Exception e) {
            log.error("发送企微群发消息失败：{}", e.toString());
            //默认未发送
            qywxGroupUserSendLog.setSendUserResult(0);
            qywxGroupUserSendLog.setFailReason(e.toString());
            qywxGroupUserSendLog.setSendStatus(0);
            qywxGroupUserSendLog.setIsDeleted(0);
            qywxGroupUserSendLog.setCreatedBy("kafka");
            qywxGroupUserSendLog.setCreatedDate(new Date());
            qywxGroupUserSendLog.setModifiedBy("kafka");
            qywxGroupUserSendLog.setModifiedDate(new Date());
            qywxGroupUserSendLogMapper.insertSelective(qywxGroupUserSendLog);
            //更新主表状态：1的时候可以修成为3
            UpdateNoticeStatusDTO updateNoticeStatusDTO = new UpdateNoticeStatusDTO();
            updateNoticeStatusDTO.setId(qywxGroupUserSendLog.getGroupMsgId());
            updateNoticeStatusDTO.setRemark(e.toString());
            qywxGroupMsgMainInfoMapper.updateFailNoticeStatus(updateNoticeStatusDTO);
            //发送补充数据的消息，补充用户信息，生成发送结果记录数据
            supplementInfoProduceChannel.send(MessageBuilder.withPayload(qywxGroupUserSendLog.getId()).build());
            return;
        }

    }

    public void suppleMsgConsume(Long groupUserSendLogId) {
        //查询数据
        QywxGroupUserSendLog qywxGroupUserSendLog = qywxGroupUserSendLogMapper.selectByPrimaryKey(groupUserSendLogId);
        if(qywxGroupUserSendLog == null || qywxGroupUserSendLog.getId() == null){
            return;
        }
        //补充信息项
        if(qywxGroupUserSendLog.getChargeUserId() != null){
            QywxGroupUserSendLog updateBean = new QywxGroupUserSendLog();
            updateBean.setId(groupUserSendLogId);
            UserAuthor userAuthor = userClient.apiOpenFindUserEntityBySalesmanId(qywxGroupUserSendLog.getChargeUserId()).getData();
            SalesmanInfoForQYWX salesmanInfoForQYWX = new SalesmanInfoForQYWX();
            FindCompanyAreaByOrgIdsOutDto companyAreaByOrgIdsOutDto = new FindCompanyAreaByOrgIdsOutDto();
            //账号，分公司，门店，大区，岗位
            List<SalesmanInfoForQYWX> salesmanInfoForQYWXList = orgClient.getSalesmanInfoByIdsForQYWX(Arrays.asList(qywxGroupUserSendLog.getChargeUserId())).getData();
            if(CollectionUtils.isNotEmpty(salesmanInfoForQYWXList)){
                salesmanInfoForQYWX = salesmanInfoForQYWXList.get(0);
                if(salesmanInfoForQYWX != null && salesmanInfoForQYWX.getCompanyId() != null){
                    List<FindCompanyAreaByOrgIdsOutDto> findCompanyAreaByOrgIdsOutDtos = getCompanyIds(Arrays.asList(salesmanInfoForQYWX.getCompanyId().intValue()));
                    if(CollectionUtils.isNotEmpty(findCompanyAreaByOrgIdsOutDtos)){
                        companyAreaByOrgIdsOutDto = findCompanyAreaByOrgIdsOutDtos.get(0);
                    }
                }
            }
            if(userAuthor != null){
                updateBean.setUserId(userAuthor.getUserId());
                updateBean.setUserName(userAuthor.getUsername());
                updateBean.setFirstName(userAuthor.getFirstName());
            }
            if(salesmanInfoForQYWX != null && salesmanInfoForQYWX.getId() != null){
                updateBean.setCompanyId(salesmanInfoForQYWX.getCompanyId());
                updateBean.setCompanyName(salesmanInfoForQYWX.getCompanyName());
                updateBean.setStoreId(salesmanInfoForQYWX.getStoreId());
                updateBean.setStoreCode(salesmanInfoForQYWX.getStoreCode());
                updateBean.setStoreName(salesmanInfoForQYWX.getStoreName());
                updateBean.setChargeUserName(salesmanInfoForQYWX.getName());
                updateBean.setChargeCode(salesmanInfoForQYWX.getCode());
                updateBean.setChargePhone(salesmanInfoForQYWX.getPhone());
                updateBean.setStation(salesmanInfoForQYWX.getStation());
            }
            if(companyAreaByOrgIdsOutDto != null && companyAreaByOrgIdsOutDto.getOrgId() != null){
                updateBean.setAreaCode(companyAreaByOrgIdsOutDto.getValueCode());
                updateBean.setAreaName(companyAreaByOrgIdsOutDto.getValueName());
            }
            qywxGroupUserSendLogMapper.updateByPrimaryKeySelective(updateBean);
        }

        //先注释掉这一部分的代码，因为无效的人员是直接返回的，先记录在userlog中，后续需要处理再说


//        if(qywxGroupUserSendLog.getSendUserResult() != null && qywxGroupUserSendLog.getSendUserResult() == 1){
//            //拆解数据
//            if(StringUtils.isNotBlank(qywxGroupUserSendLog.getQywxExternalUserids())){
//                List<String> externalUserIds = JSONArray.parseArray(qywxGroupUserSendLog.getQywxExternalUserids(),String.class);
//                List<String> failExternalUserIds = new ArrayList<>();
//                if(StringUtils.isNotBlank(qywxGroupUserSendLog.getFailExternalUserids())){
//                    failExternalUserIds = JSONArray.parseArray(qywxGroupUserSendLog.getFailExternalUserids(),String.class);
//                }
//                List<QywxGroupSendResultLog> resultLogList = new ArrayList<>();
//                QywxGroupSendResultLog qywxGroupSendResultLog = new QywxGroupSendResultLog();
//                if(CollectionUtils.isNotEmpty(externalUserIds)){
//                    for(String externalUserId : externalUserIds){
//                        qywxGroupSendResultLog = new QywxGroupSendResultLog();
//                        qywxGroupSendResultLog.setGroupMsgId(qywxGroupUserSendLog.getGroupMsgId());
//                        qywxGroupSendResultLog.setUserId(qywxGroupUserSendLog.getQywxUserId());
//                        qywxGroupSendResultLog.setExternalUserid(externalUserId);
//                        qywxGroupSendResultLog.setQywxMsgId(groupUserSendLogId);
//                        if(CollectionUtils.isNotEmpty(failExternalUserIds)){
//                            String userId = failExternalUserIds.parallelStream().filter(x -> x.equals(externalUserId)).findFirst().orElse(null);
//                            if(StringUtils.isNotBlank(userId)){
//                                qywxGroupSendResultLog.setStatus(2);
//                                qywxGroupSendResultLog.setSendTime(new Date());
//                            }else {
//                                qywxGroupSendResultLog.setStatus(0);
//                            }
//                        }else {
//                            qywxGroupSendResultLog.setStatus(0);
//                        }
//                        resultLogList.add(qywxGroupSendResultLog);
//                    }
//                }
//                if(CollectionUtils.isNotEmpty(resultLogList)){
//                    qywxGroupSendResultLogMapper.insertBatch(resultLogList);
//                }
//            }
//        }
        return;
    }


    public List<FindCompanyAreaByOrgIdsOutDto> getCompanyIds(List<Integer> orgIds) {
        FindCompanyByIdsInDto companyByIdsInDto = new FindCompanyByIdsInDto();
        companyByIdsInDto.setIdList(orgIds);
        return orgClient.findCompanyByOrgIdList(companyByIdsInDto).getData();
    }

    /**
     *
     * @param param
     */
    public void pullMsgStatusFromQYWXJob(String param) {
        Long groupMsgId = null;
        if(StringUtils.isNotBlank(param)){
            groupMsgId = Long.valueOf(param);
        }
        List<QywxGroupUserSendLog> list = this.getUnsentUserSendLogList(groupMsgId);
        if(CollectionUtils.isNotEmpty(list)){
            //丢入消息通道，消费
            List<Long> groupMsgIdList = list.parallelStream().map(QywxGroupUserSendLog ::getGroupMsgId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(groupMsgIdList)){
                qywxGroupMsgMainInfoMapper.updateLastPullTime(groupMsgIdList);
            }
            //批量修改最新一次拉取时间
            asyncUnsentLog(list,null);
        }
    }


    public List<QywxGroupUserSendLog> getUnsentUserSendLogList(Long groupMsgId){
        List<QywxGroupUserSendLog> list = qywxGroupUserSendLogMapper.getUnsentUserSendLogList(groupMsgId);
        return list;
    }

    public List<QywxGroupUserSendLog> getUserSendLogList(Long groupMsgId){
        List<QywxGroupUserSendLog> list = qywxGroupUserSendLogMapper.getDataUserSendLogList(groupMsgId);
        return list;
    }


    public void asyncUnsentLog(List<QywxGroupUserSendLog> list,Long dataTaskId){
        for(QywxGroupUserSendLog qywxGroupUserSendLog : list){
            qywxGroupUserSendLog.setDataTaskId(dataTaskId);
            pullMsgProduceChannel.send(MessageBuilder.withPayload(qywxGroupUserSendLog).build());
        }
    }


    public void pullMsgConsume(QywxGroupUserSendLog qywxGroupUserSendLog) {
        if(qywxGroupUserSendLog == null || StringUtils.isBlank(qywxGroupUserSendLog.getAccountId()) || StringUtils.isBlank(qywxGroupUserSendLog.getQywxMsgId())){
            log.error("拉群企微消息状态，不满足拉取数据需求");
            return;
        }
        //先拉取发送状态，再拉取发送明细
        List<GroupMsgTaskDTO> list = new ArrayList<>();
        list = getGroupMsgTask(qywxGroupUserSendLog.getQywxMsgId(), qywxGroupUserSendLog.getAccountId(), null, list);
        if(CollectionUtils.isNotEmpty(list)){
            //获取当前userId的消息
            GroupMsgTaskDTO groupMsgTaskDTO = list.parallelStream().filter(x -> x.getUserid().equals(qywxGroupUserSendLog.getQywxUserId())).findFirst().orElse(null);
            if(groupMsgTaskDTO != null && groupMsgTaskDTO.getStatus() == 2){
                //存在数据，且状态是已发送，则修改当前数据，
                QywxGroupUserSendLog updateBean = new QywxGroupUserSendLog();
                updateBean.setId(qywxGroupUserSendLog.getId());
                updateBean.setSendStatus(2);
                updateBean.setSendTime( groupMsgTaskDTO.getSend_time() == null ? null : new Date(groupMsgTaskDTO.getSend_time()*1000));
                updateBean.setModifiedDate(new Date());
                //修改发送状态和发送时间
                qywxGroupUserSendLogMapper.updateByPrimaryKeySelective(updateBean);
                //群发成员执行结果
                dealResultLog(qywxGroupUserSendLog);
            }
            log.error("调用cdp通知开始计算：{}",qywxGroupUserSendLog.getDataTaskId());
            Result<Integer> result = cdpClient.getCDPSyncTaskInfo();
            if (result.getSuccess()&&result.getData()!=null){
                log.error("cdp通知：{}",result.getData());
                tQywxGroupCompleteDataLogDao.update(null,Wrappers.lambdaUpdate(TQywxGroupCompleteDataLog.class)
                        .set(TQywxGroupCompleteDataLog::getSyncId,result.getData()).eq(TQywxGroupCompleteDataLog::getId,qywxGroupUserSendLog.getDataTaskId()));
            }
        }
        return;
    }


    private List<GroupMsgTaskDTO> getGroupMsgTask(String msgId,String accountId, String cursor, List<GroupMsgTaskDTO> list){
        //装配内容，发送欢迎语
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
            return list;
        }
        String uri = QywxConstantConfig.GET_GROUP_MSG_TASK_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", msgId);
            map.put("limit", 50);
            map.put("cursor", cursor);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                if (StringUtils.isNotBlank(jsonObject.getString("task_list"))) {
                    List<GroupMsgTaskDTO> arr = JSONArray.parseArray(jsonObject.getString("task_list"), GroupMsgTaskDTO.class);
                    if(CollectionUtils.isNotEmpty(arr)){
                        list.addAll(arr);
                    }
                }
                if(StringUtils.isNotBlank(jsonObject.getString("next_cursor"))){
                    cursor = jsonObject.getString("next_cursor");
                    //因为存在分页，所以需要递归自己，查询全部的数据记录
                    return getGroupMsgTask(msgId,accountId,cursor,list);
                }else {
                    return list;
                }
            } else {
                return list;
            }
        } catch (Exception e) {
            log.error("获取群发成员发送任务列表：{}", e.toString());
            throw new BusinessException("获取群发成员发送任务列表：" + e.getMessage());
        }
    }


    private void dealResultLog(QywxGroupUserSendLog qywxGroupUserSendLog){
        //先获取企微记录
        List<GroupMsgSendResultDTO> list = new ArrayList<>();
        list = getGroupMsgResult(qywxGroupUserSendLog.getQywxMsgId(), qywxGroupUserSendLog.getAccountId(), qywxGroupUserSendLog.getQywxUserId(), null, list);
        //设置参数
        if(CollectionUtils.isNotEmpty(list)) {
            List<String> externalUserIds = new ArrayList<>();
            List<QywxGroupSendResultLog> resultLogList = new ArrayList<>();
            QywxGroupSendResultLog qywxGroupSendResultLog = new QywxGroupSendResultLog();
            //去重
            Set<String> externalUserIdSet = Sets.newHashSet();
            for (GroupMsgSendResultDTO groupMsgSendResultDTO : list) {
                String userId = qywxGroupSendResultLog.getGroupMsgId() + "_" + qywxGroupUserSendLog.getQywxUserId() + "_" + groupMsgSendResultDTO.getExternal_userid();
                if (externalUserIdSet.contains(userId)){
                    continue;
                }
                externalUserIdSet.add(userId);
                qywxGroupSendResultLog = new QywxGroupSendResultLog();
                qywxGroupSendResultLog.setGroupMsgId(qywxGroupUserSendLog.getGroupMsgId());
                qywxGroupSendResultLog.setUserId(qywxGroupUserSendLog.getQywxUserId());
                qywxGroupSendResultLog.setExternalUserid(groupMsgSendResultDTO.getExternal_userid());
                qywxGroupSendResultLog.setQywxMsgId(qywxGroupUserSendLog.getId());
                qywxGroupSendResultLog.setStatus(groupMsgSendResultDTO.getStatus());
                qywxGroupSendResultLog.setSendTime(groupMsgSendResultDTO.getSend_time() != null ? new Date(groupMsgSendResultDTO.getSend_time()* 1000) : null);
                //
                if(groupMsgSendResultDTO.getStatus() != null && groupMsgSendResultDTO.getStatus() ==1){
                    externalUserIds.add(groupMsgSendResultDTO.getExternal_userid());
                }
                resultLogList.add(qywxGroupSendResultLog);
            }
            if (CollectionUtils.isNotEmpty(resultLogList)) {
                qywxGroupSendResultLogMapper.insertBatch(resultLogList);
            }
            if(CollectionUtils.isNotEmpty(externalUserIds)){
                GroupMaterialSendLogDTO groupMaterialSendLogDTO = new GroupMaterialSendLogDTO();
                groupMaterialSendLogDTO.setGroupMsgId(qywxGroupUserSendLog.getGroupMsgId());
                groupMaterialSendLogDTO.setUserId(qywxGroupUserSendLog.getUserId());
                groupMaterialSendLogDTO.setUserName(qywxGroupUserSendLog.getUserName());
                groupMaterialSendLogDTO.setFirstName(qywxGroupUserSendLog.getFirstName());
                groupMaterialSendLogDTO.setExternalUserIds(externalUserIds);
                materialLogProduceChannel.send(MessageBuilder.withPayload(groupMaterialSendLogDTO).build());
            }
        }
        return;
        //插入数据
    }


    private List<GroupMsgSendResultDTO> getGroupMsgResult(String msgId,String accountId,String userId, String cursor, List<GroupMsgSendResultDTO> list) {
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
            return list;
        }
        String uri = QywxConstantConfig.GET_GROUP_MSG_RESULT_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", msgId);
            map.put("userid", userId);
            map.put("limit", 500);
            map.put("cursor", cursor);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                if (StringUtils.isNotBlank(jsonObject.getString("send_list"))) {
                    List<GroupMsgSendResultDTO> arr = JSONArray.parseArray(jsonObject.getString("send_list"), GroupMsgSendResultDTO.class);
                    if(CollectionUtils.isNotEmpty(arr)){
                        list.addAll(arr);
                    }
                }
                if(StringUtils.isNotBlank(jsonObject.getString("next_cursor"))){
                    cursor = jsonObject.getString("next_cursor");
                    //因为存在分页，所以需要递归自己，查询全部的数据记录
                    return getGroupMsgResult(msgId,accountId,userId,cursor,list);
                }else {
                    return list;
                }
            } else {
                return list;
            }
        } catch (Exception e) {
            log.error("获取企业群发成员执行结果：{}", e.toString());
            throw new BusinessException("获取企业群发成员执行结果：" + e.getMessage());
        }

    }

    public Result completeData(Long groupMsgId,Long dataTaskId) {
        List<QywxGroupUserSendLog> list = this.getUserSendLogList(groupMsgId);
        if(CollectionUtils.isNotEmpty(list)){
            //丢入消息通道，消费
            asyncUnsentLog(list,dataTaskId);
            return Result.buildSuccess("手动刷新成功",new Date());
        }else {
            //返回错误
            return Result.buildSuccess("通知已经全部发送，不需要再手动刷新");
        }
    }


    public Result refreshNotice(Long groupMsgId) {
        List<QywxGroupUserSendLog> list = this.getUnsentUserSendLogList(groupMsgId);
        if(CollectionUtils.isNotEmpty(list)){
            //修改主表时间记录
            String userId = userAuthorConfig.getCurrentUserId();
            qywxGroupMsgMainInfoMapper.refreshNotice(groupMsgId,userId);
            //丢入消息通道，消费
            asyncUnsentLog(list,null);
            return Result.buildSuccess("手动刷新成功",new Date());
        }else {
            //返回错误
            return Result.buildSuccess("通知已经全部发送，不需要再手动刷新");
        }
    }

    public String noticeChargeUserAgain(List<Long> ids) {
        //根据id获取需要通知的列表，再逐条发送
        List<QywxGroupUserSendLog> list = qywxGroupUserSendLogMapper.getUnsentListByIds(ids);
        if(CollectionUtils.isNotEmpty(list)){
            for(QywxGroupUserSendLog qywxGroupUserSendLog : list){
                if(StringUtils.isNotBlank(qywxGroupUserSendLog.getAccountId()) && StringUtils.isNotBlank(qywxGroupUserSendLog.getQywxMsgId())){
                    remindUserNotice(qywxGroupUserSendLog.getAccountId(),qywxGroupUserSendLog.getQywxMsgId());
                }
            }
        }
        return "通知成功";
    }


    public int remindUserNotice(String accountId,String msgId) {
        //装配内容，发送欢迎语
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.REMIND_GROUP_MSG_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", msgId);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.error(map.toString());
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.error(jsonObject.toString());
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("再次通知：{}", e.toString());
            return 0;
        }
    }

    /**
     * 创建记录
     * @param groupMaterialSendLogDTO
     */
    public void materialLogConsume(GroupMaterialSendLogDTO groupMaterialSendLogDTO) {
        QywxGroupMsgMainInfo qywxGroupMsgMainInfo = qywxGroupMsgMainInfoMapper.selectByPrimaryKey(groupMaterialSendLogDTO.getGroupMsgId());
        if (qywxGroupMsgMainInfo != null && qywxGroupMsgMainInfo.getConfigType() != null && qywxGroupMsgMainInfo.getConfigType() == 2) {
            TCustomerCommonMaterialMapping tCustomerCommonMaterialMapping = materialMappingDao.selectOne(Wrappers.lambdaQuery(TCustomerCommonMaterialMapping.class)
                    .eq(TCustomerCommonMaterialMapping::getIsDeleted, 0)
                    .eq(TCustomerCommonMaterialMapping::getSourceType, 2)
                    .eq(TCustomerCommonMaterialMapping::getSourceId, qywxGroupMsgMainInfo.getId()));
            if(tCustomerCommonMaterialMapping != null && tCustomerCommonMaterialMapping.getMaterialId() != null){
                //设置参数，调用接口
                if(CollectionUtils.isNotEmpty(groupMaterialSendLogDTO.getExternalUserIds())){
                    try {
                        MaterialContentDto materialContentDto = new MaterialContentDto();
                        materialContentDto.setMaterialId(tCustomerCommonMaterialMapping.getMaterialId().intValue());
                        materialContentDto.setUserId(groupMaterialSendLogDTO.getUserId());
                        materialContentDto.setFirstName(groupMaterialSendLogDTO.getFirstName());
                        materialContentDto.setObjectUserIdList(groupMaterialSendLogDTO.getExternalUserIds());
                        materialShareProduceChannel.send(MessageBuilder.withPayload(materialContentDto).build());
                    } catch (Exception e) {
                        log.error("群发消息生成企微内容树发送日志错误{}",e.toString());
                    }
                    /*for (String externalUserId : groupMaterialSendLogDTO.getExternalUserIds()) {
                        try {
                            MaterialContentDto materialContentDto = new MaterialContentDto();
                            materialContentDto.setMaterialId(qywxGroupMaterialMapping.getMaterialId().intValue());
                            materialContentDto.setUserId(groupMaterialSendLogDTO.getUserId());
                            materialContentDto.setFirstName(groupMaterialSendLogDTO.getFirstName());
                            materialContentDto.setObjectUserId(externalUserId);
                            cmsClient.saveMaterialContentShareOpen(materialContentDto);
                            //materialShareProduceChannel.send(MessageBuilder.withPayload(materialContentDto).build());
                        } catch (Exception e) {
                            log.error("群发消息生成企微内容树发送日志错误{}",e.toString());
                        }
                    }*/
                }
            }
        }
        return;

    }

    public void stopMsgConsume(StopGroupMsgLogDTO stopGroupMsgLogDTO){
        List<QywxGroupUserSendLog> qywxGroupUserSendLogs = qywxGroupUserSendLogMapper.selectList(Wrappers.lambdaQuery(QywxGroupUserSendLog.class)
                .eq(QywxGroupUserSendLog::getGroupMsgId, stopGroupMsgLogDTO.getGroupMsgId())
                .eq(QywxGroupUserSendLog::getSendStatus,0)
                .eq(QywxGroupUserSendLog::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(qywxGroupUserSendLogs)){
            log.info("未查询到当前群发对应的业务员日志信息");
            return;
        }
        String token = qywxCommonUtil.getQYWXToken(stopGroupMsgLogDTO.getAccountId());
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + stopGroupMsgLogDTO.getAccountId() + "获取token失败！");
        }

        String url = QywxConstantConfig.STOP_MSG_TEMPLATE_URL + "access_token=" + token;
        ExecutorService executor = ThreadUtil.newExecutor(4, 8);
        List<List<?>> lists = splitList(qywxGroupUserSendLogs, 1000);
        for (int i = 0; i < lists.size(); i++){
            try {
                int finalI = i;
                executor.execute(()->{
                    stopMsgUserSendLog((List<QywxGroupUserSendLog>)lists.get(finalI),url);
                });
            }catch (Exception e){
                log.error("停止群发当前批次执行失败：{}",i);
            }
        }
    }

    private void stopMsgUserSendLog(List<QywxGroupUserSendLog> qywxGroupUserSendLogs,String url){
        qywxGroupUserSendLogs.forEach(qywxGroupUserSendLog -> {
            stopMsg(qywxGroupUserSendLog.getQywxMsgId(), url);
        });
    }

    public List<List<?>> splitList(List<?> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<?>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<?> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    private void stopMsg(String msgId,String url){
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("msgid", msgId);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            log.info("打印企微群发发送消息体{}", bodyJsonData);
            String strbody = restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody();
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            log.info("打印企微群发发送结果体{}", jsonObject.toString());
            //插入数据
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                //停止群发失败
                log.error("停止企微群发消息失败：{}", jsonObject.toString());
            }
        } catch (Exception e) {
            log.error("发送企微群发消息失败：{}", e.toString());
        }
    }
}
