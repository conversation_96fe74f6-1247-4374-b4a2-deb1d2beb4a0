package com.fotile.customercenter.groupmsg.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.customercenter.groupmsg.pojo.entity.TCustomerCommonMaterialContentMapping;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TCustomerCommonMaterialContentMappingDao extends BaseMapper<TCustomerCommonMaterialContentMapping> {
    int deleteByPrimaryKey(Integer id);

    int insert(TCustomerCommonMaterialContentMapping record);

    int insertSelective(TCustomerCommonMaterialContentMapping record);

    TCustomerCommonMaterialContentMapping selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TCustomerCommonMaterialContentMapping record);

    int updateByPrimaryKey(TCustomerCommonMaterialContentMapping record);

    int insertBatch(List<TCustomerCommonMaterialContentMapping> list);
}