package com.fotile.customercenter.groupmsg.Enum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChatOperationTypeEnum {

    CREATE(100, "创建群组"),
    UPDATE_CHAT_NAME(200, "群名称变更"),
    UPDATE_CHAT_ADMIN(201, "群主变更"),
    DISMISS(300, "解散群组"),
    ;

    private Integer type;

    private String typeName;

}
