package com.fotile.customercenter.client.pojo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

@Data
public class FindDecorateListByTypeCodeOutDto {
    /**
     * 引流渠道ID
     */
    private Long id;
    /**
     * 引流渠道名称
     */
    @FieldEncrypt
    private String name;
    /**
     * 引流渠道编码
     */
    private String code;
    /**
     * 引流渠道手机号
     */
    @FieldEncrypt
    private String phone;
    /**
     * 类型 1：家装；2：异业三工'
     */
    private Integer type;
    /**
     * 引流渠道类型code
     */
    private String category;
    /**
     * 引流渠道类型名称
     */
    private String categoryName;
    /**
     * 业务员ID
     */
    private Long chargeUserId;
    /**
     * 业务员编码
     */
    private String chargeUserCode;
    /**
     * 业务员名称
     */
    @FieldEncrypt
    private String chargeUserName;
    /**
     * 业务员手机号
     */
    @FieldEncrypt
    private String chargeUserPhone;
    /**
     * 状态 0：禁用；1：启用'
     */
    private Integer status;
    /**
     * 审核状态,1：待审核 2：已通过 3：已拒绝'
     */
    private Integer auditStatus;

}
