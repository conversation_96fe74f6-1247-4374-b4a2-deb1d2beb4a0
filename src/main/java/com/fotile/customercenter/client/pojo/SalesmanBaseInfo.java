package com.fotile.customercenter.client.pojo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

/**
 * @Description 业务员基本信息
 * <AUTHOR>
 * @Date 2021/9/8 10:40
 * @Version 1.0
 */
@Data
public class SalesmanBaseInfo {

    private Integer id;
    @FieldEncrypt
    private String name;
    @FieldEncrypt
    private String phone;
    private Integer station;
    private String stationName;
    private String areaName;
    private Long companyId;
    private String companyName;
    private Long storeId;
    private String storeName;
    private Integer orderCount;
    private Date orderCreateDate;

}
