package com.fotile.customercenter.client.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MaterialContentDetailDTO implements Serializable {


    /**
     *
     */
    private Integer id;

    /**
     * 聊天素材id
     */
    private Integer materialId;

    /**
     * 聊天素材内容id
     */
    private Integer materialContentId;

    /**
     * 基础素材id
     */
    private Integer basicMaterialId;

    /**
     * 关联基础素材展开标识，0-不展开，1-展开
     */
    private Integer expandFlag;

    /**
     * 落地页标题
     */
    private String landingPageTitle;

    /**
     * 落地页显示图片地址
     */
    private String displayPictureUrl;

    /**
     * 链接地址
     */
    private String linkAddress;

    /**
     * 链接描述
     */
    private String linkDescription;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 修改人
     */
    private String modifiedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 逻辑删除标识
     */
    private Integer isDeleted;


    /**
     * 以下为基础素材的字段属性
     */


    /**
     * 来源类型，1-内容列表，2-商品列表，3-视频，4-文件，5-文字，6-图片，7-外链
     *
     * type =1 ：内容列表，是分享方太5s小程序找案例的详情页面，landing_page为页面路径，ref_id为参数值，landing_page_title为小程序标题，display_picture_url为小程序分享的图片，wechat_material_id为企微分享的图片；
     * type =2 ：商品列表，是分享方太5s小程序找产品的详情页面，landing_page为页面路径，ref_code为参数值，landing_page_title为小程序标题，display_picture_url为小程序分享的图片，wechat_material_id为企微分享的图片；
     * type =3 ：视频类型，material_url为原链接地址，wechat_material_id为分享链接的地址；
     * type =4 : 文件类型，material_url为原链接地址，material_url直接分享地址；
     * type =5 ：文字内容，content为文字内容；
     * type =6 ：图片类型，material_url为原链接地址，wechat_material_id为分享链接的地址；
     * type =7 ：链接类型，landing_page_title为链接标题，link_description为链接描述，link_address为链接地址，display_picture_url为链接url；
     *
     */


    /**
     * 聊天素材内容id
     */
    private Integer contentId;

    @TableField(value = "type")
    private Integer type;

    /**
     * 关联id，内容id或商品id
     */
    @TableField(value = "ref_id")
    private Long refId;

    /**
     * 关联code，目前只有商品编码
     */
    @TableField(value = "ref_code")
    private String refCode;

    /**
     * 关联名称，内容标题或商品名
     */
    @TableField(value = "ref_title")
    private String refTitle;

    /**
     * 素材名称，文件名/图片名
     */
    @TableField(value = "material_name")
    private String materialName;

    /**
     * 素材头图地址
     */
    @TableField(value = "material_cover_url")
    private String materialCoverUrl;

    /**
     * 基础素材素材地址，文件地址/图片地址
     */
    @TableField(value = "material_url")
    private String materialUrl;

    /**
     * 基础素材跳转链接
     */
    @TableField(value = "link_address")
    private String basicLinkAddress;

    /**
     * 文字话术
     */
    @TableField(value = "content")
    private String content;

    /**
     * 顾客展示平台，1-5S小程序,存储appid
     */
    @TableField(value = "show_platform")
    private String showPlatform;

    /**
     * 顾客展示平台名称
     */
    @TableField(value = "show_platform_name")
    private String showPlatformName;

    /**
     * 页面路径
     */
    @TableField(value = "landing_page")
    private String landingPage;


    /**
     * 企微多媒体id
     */
    private String wechatMaterialId;

}
