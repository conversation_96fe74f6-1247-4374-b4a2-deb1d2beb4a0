package com.fotile.customercenter.client.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MaterialDetailDTO implements Serializable {

    /**
     *
     */
    private Integer id;

    private Integer treeId;

    /**
     * 聊天素材id
     */
    private Integer materialId;

    /**
     * 分类节点id
     */
    private Long nodeId;

    /**
     * 标题
     */
    private String title;

    /**
     * 生效开始时间
     */
    private Date effectiveStartTime;

    /**
     * 生效结束时间
     */
    private Date effectiveEndTime;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态 1：上架 0：下架
     */
    private Integer status;

    /**
     * 类型
     */
    private Integer type;
    /**
     * 支持转发 0：不支持 1：支持
     *
     */
    private Integer supportTransPond;


    /**
     * 内容组数据
     */
    private List<MaterialGroupContentDTO> materialGroupContentDTOS;
    /**
     * 征集素材id
     *
     */
    private Long materialCollectId;
    /**
     * 上新
     */
    private Integer remindType;

    /**
     * 内容标签
     */
    private List<Long> contentTagList;
    /**
     * 是否上架过
     */
    private Integer statusFlag;

}
