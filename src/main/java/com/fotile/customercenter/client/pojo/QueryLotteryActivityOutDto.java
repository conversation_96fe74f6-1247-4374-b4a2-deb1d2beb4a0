package com.fotile.customercenter.client.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class QueryLotteryActivityOutDto implements Serializable {
    /**
     * 活动id
     */
    private Long id;
    /**
     * 活动类型 1.展示 2.报名 3.菜谱 4.留资 5.话题 6.幸福家抽奖 7.助力 11.抽奖 12.助力拼团 13.积分红包 20.引流渠道 21.有奖互动话题类 30.新媒体小程序打卡活动
     */
    private int type;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String insideTitle;
    /**
     * 活动规则
     */
    private String activityExplain;

    /**
     * 活动有效时间开始
     */
    private Date startTime;
    /**
     * 活动有效时间开始 - 字符串
     */
    private String startTimeStr;
    /**
     * 活动有效时间结束
     */
    private Date endTime;
    /**
     * 活动有效时间结束 - 字符串
     */
    private String endTimeStr;
    /**
     * 活动状态: 空:全部  1:进行中 2:未开始 3:已结束 4:已下线
     */
    private Integer activityStatus;
    /**
     * 发帖人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 更新人
     */
    private String modifiedBy;
    /**
     * 更新人
     */
    private Date modifiedDate;
    /**
     * 在线状态  ：0：下线 1：上线(新媒体不看这个状态)
     */
    private int onlineStatus;

    /**
     * 活动关联的商品的图片信息
     */
    private List<String> goodsPics;

    /**
     * 抽奖奖品id
     */
    private String prizeId;
    /**
     * markNum
     */
    private Integer markNum;

}
