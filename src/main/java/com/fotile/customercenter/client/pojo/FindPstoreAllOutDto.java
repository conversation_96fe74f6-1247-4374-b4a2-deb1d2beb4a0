package com.fotile.customercenter.client.pojo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.List;

@Data
public class FindPstoreAllOutDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 省id
     */
    private Long provicenId;

    /**
     * 市id
     */
    private Long cityId;

    /**
     * 区县id
     */
    private Long countyId;

    /**
     * 大区
     */
    private Long area;

    /**
     * 分公司id
     */
    private Long companyId;

    /**
     * 默认门店
     */
    private Long storeId;

    /**
     * 默认业务员
     */
    private Long salesmanId;

    /**
     * 省名称
     */
    private String provicenName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String countyName;

    /**
     * 大区名称
     */
    private String areaName;

    /**
     * 分公司
     */
    private String companyName;

    /**
     * 默认门店
     */
    private String storeName;

    /**
     * 门店简称
     */
    private String abbreviation;

    /**
     * 默认业务员
     */
    private String salesmanName;

    /**
     * 最新编辑人
     */
    private String modifiedBy;

    /**
     * 最新编辑时间
     */
    private String modifiedDate;

    /**
     * 分公司orgId
     */
    private Long companyOrgId;

    private String channel;

    @FieldEncrypt
    private String tel;

    /**
     * 门店距离
     */
    private String distance;

    /**
     * 门店地址
     */
    private String showAddress;
    /**
     * 显示地址
     */
    private String address2;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 市场等级
     */
    private String zscdj;

    private Integer enable;



    private String streetChargeUser;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
