package com.fotile.customercenter.client.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;

@Data
@Entity
@ApiModel("数据字典")
@TableName(value="dic",schema = "systemcenter")

public class Dic extends AuditingEntity implements Serializable {



    @TableField(value = "value_code")
    @ApiModelProperty("数据字典具体的值编码。用于外键存储")
    private String valueCode;

    @TableField(value = "type_code")
    @ApiModelProperty("分类编码")
    private String typeCode;
    @TableField(value = "value_name")
    @ApiModelProperty("数据字典下拉框显示的名称")
    private String valueName;
    @TableField(value = "active")
    @ApiModelProperty("是否有效")
    private Integer active;
    @TableField(value = "sort")
    @ApiModelProperty("排序")
    private Integer sort;

}
