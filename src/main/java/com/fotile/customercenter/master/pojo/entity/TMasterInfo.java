package com.fotile.customercenter.master.pojo.entity;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * 幸福家达人信息管理表(TMasterInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-12-01 13:44:31
 */

@Data
public class TMasterInfo implements Serializable {
    private static final long serialVersionUID = -33483991794009941L;
    /**
     * 顾客id
     */
    private Long id;
    /**
     * 是否删除 0未删除 非0已删除
     */
    private Long isDeleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建日期
     */
    private Date createdDate;
    /**
     * 编辑人
     */
    private String modifiedBy;
    /**
     * 编辑日期
     */
    private Date modifiedDate;
    /**
     * 用户id
     */
    private Long customerId;
    /**
     * 手机号
     */
    @FieldEncrypt
    private String phone;
    /**
     * 达人类型id
     */
    private Long type;
    /**
     * 达人类型名称
     */
    private String typeTitle;
    /**
     * 达人状态 1考核期；2正常；3过期
     */
    private Integer status;
    /**
     * 有效期开始
     */
    private Date startTime;
    /**
     * 有效期结束
     */
    private Date endTime;

    @Transient
    private Integer index;
}

