package com.fotile.customercenter.backqywx.dao;


import com.fotile.customercenter.backqywx.pojo.entity.QywxBackGroupConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信控制标签组回传配置表 QywxBackGroupConfigMapper
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-15 10:55:00
 */
public interface QywxBackGroupConfigMapper {
		
    int deleteByPrimaryKey(Long id);
		
    int insert(QywxBackGroupConfig record);

    int insertSelective(QywxBackGroupConfig record);

    QywxBackGroupConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QywxBackGroupConfig record);

    int updateByPrimaryKey(QywxBackGroupConfig record);

    List<QywxBackGroupConfig> selectBackGroupList(@Param("accountId") String accountId);
  	  	
}