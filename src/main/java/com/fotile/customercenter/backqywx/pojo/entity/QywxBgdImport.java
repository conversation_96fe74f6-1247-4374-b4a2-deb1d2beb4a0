package com.fotile.customercenter.backqywx.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class QywxBgdImport implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/** *  */
	private Long id;
	
	/** * 是否删除：0：否；其它：是 */
	private Integer isDeleted;
	
	/** *  */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date createdDate;
	
	/** *  */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date modifiedDate;
	
	/** * 账户Id */
	private String accountId;
	
	/** * 唯一身份标识 */
	private String unionId;
	
	/** * 标签组名称 */
	private String groupName;
	
	/** * 标签名称 */
	private String tagName;
	
	/** * 数据同步状态，0，位同步，1：成功，2失败 */
	private Integer syncStatus;

}