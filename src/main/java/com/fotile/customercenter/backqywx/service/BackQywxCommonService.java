package com.fotile.customercenter.backqywx.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fotile.customercenter.backqywx.dao.*;
import com.fotile.customercenter.backqywx.pojo.dto.*;
import com.fotile.customercenter.backqywx.pojo.entity.*;
import com.fotile.customercenter.qywx.dao.QywxCutomerUserRelationMapper;
import com.fotile.customercenter.qywx.pojo.dto.TagGroupWXDTO;
import com.fotile.customercenter.qywx.pojo.dto.TagWXOutDTO;
import com.fotile.customercenter.qywx.util.QywxCommonUtil;
import com.fotile.customercenter.qywx.util.QywxConstantConfig;
import com.fotile.customercenter.qywxTag.dto.QywxGroupTagForBackDTO;
import com.fotile.customercenter.qywxTag.dto.QywxTagGroupDisplayDTO;
import com.fotile.customercenter.qywxTag.dto.QywxTagGroupTagDisplayDTO;
import com.fotile.customercenter.qywxTag.service.QywxTagGroupDisplayConfigService;
import com.fotile.customercenter.qywxTag.service.QywxTagRelationMappingService;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
@Slf4j
public class BackQywxCommonService {

    @Autowired
    private QywxCommonUtil qywxCommonUtil;

    @Autowired
    private QywxBackTagMappingMapper qywxBackTagMappingMapper;

    @Autowired
    private QywxBgdImportMapper qywxBgdImportMapper;

    @Autowired
    private QywxCutomerUserRelationMapper qywxCutomerUserRelationMapper;

    @Autowired
    private QywxBackTagRelationMapper qywxBackTagRelationMapper;

    @Autowired
    private QywxBackGroupConfigMapper qywxBackGroupConfigMapper;

    @Autowired
    private QywxBackGroupListMapper qywxBackGroupListMapper;


    @Autowired
    private QywxTagRelationMappingService qywxTagRelationMappingService;

    @Autowired
    private QywxTagGroupDisplayConfigService qywxTagGroupDisplayConfigService;


    @Value("${jd.data.domain}")
    private String DIG_DATA_TAG_URL;



    public void getCorpBackTagJob(String param) {
        List<String> groupList = new ArrayList<>();
        if (StringUtils.isNotBlank(param)) {
            String[] groupIds = param.split(",");
            if (groupIds.length > 0) {
                groupList = Arrays.asList(groupIds);
            }
        }
        //根据标签分组去获取企业微信列表
        //本期做上海企业微信的功能，后期如果要做其他，此参数可以用xxljob传入
        String accountId = "qywx_sh_1";
        getWXTagByGroupId(accountId, groupList);
    }


    public void getWXTagByGroupId(String accountId, List<String> groupList) {
        Map groupMap = new HashMap();
        String groupName = null;
        if (groupList != null && groupList.size() > 0) {
            groupMap.put("group_id", groupList);
        }
        try {
            List<TagGroupWXDTO> groupWXList = getWXTagForWX(accountId, groupMap);
            List<QywxBackGroupConfig> groupConfigList = qywxBackGroupConfigMapper.selectBackGroupList(accountId);
            if (groupWXList != null && groupWXList.size() > 0) {
                //插入关系表
                for (TagGroupWXDTO tagGroupWXDTO : groupWXList) {
                    //根据标签组id删除数据
                    if (isExistBackGroup(tagGroupWXDTO.getGroup_id() , groupConfigList)) {
                        qywxBackTagMappingMapper.deleteByGroupId(tagGroupWXDTO.getGroup_id());
                        //设置数据
                        List<QywxBackTagMapping> insertList = new ArrayList<>();
                        if (tagGroupWXDTO.getTag() != null && tagGroupWXDTO.getTag().size() > 0) {
                            for (TagWXOutDTO tag : tagGroupWXDTO.getTag()) {
                                QywxBackTagMapping insertTag = new QywxBackTagMapping();
                                insertTag.setAccountId(accountId);
                                insertTag.setGroupId(tagGroupWXDTO.getGroup_id());
                                insertTag.setGroupName(tagGroupWXDTO.getGroup_name());
                                insertTag.setWxTagId(tag.getId());
                                insertTag.setWxTagName(tag.getName());
                                insertList.add(insertTag);
                            }
                        }
                        //插入数据
                        if (insertList != null && insertList.size() > 0) {
                            qywxBackTagMappingMapper.insertList(insertList);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("新增企业标签：" + accountId + ":" + e.getMessage());
        }
    }

    /**
     * false:不操作，不存入数据，
     * true:存入数据
     * @param groupId
     * @param groupConfigList
     * @return
     */
    public Boolean isExistBackGroup(String groupId, List<QywxBackGroupConfig> groupConfigList) {
        if (groupConfigList == null || groupConfigList.size() <= 0) {
            return false;
        }
        QywxBackGroupConfig groupConfig = groupConfigList.parallelStream().filter(x -> x.getGroupId().equals(groupId)).findFirst().orElse(null);
        if (groupConfig == null || groupConfig.getGroupId() == null) {
            return false;
        } else {
            return true;
        }
    }

    public List<TagGroupWXDTO> getWXTagForWX(String accountId, Map groupMap) {
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.GET_TAG_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            String bodyJsonData = JSON.toJSONString(groupMap);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            log.info("获取企业微信账户：" + accountId + "的微信部门列表数据：" + strbody);
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                List<TagGroupWXDTO> groupWXDTO = JSON.parseArray(jsonObject.getString("tag_group").toString(), TagGroupWXDTO.class);
                return groupWXDTO;
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new BusinessException("获取企业标签失败：" + e.getMessage());
        }
    }


    public Result<?> bgdBackTag(List<QywxBgdImport> bgdImportList) {
        Integer count = 0;
        try {
            String accountId = "qywx_sh_1";
            count = qywxBgdImportMapper.insertList(accountId, bgdImportList);
        } catch (Exception e) {
            log.error("大数据回传标签数据报错，" + e.getMessage());
        }
        return Result.buildSuccess("回传数据成功", count);
    }

    public void createUserTagJob(String param) {
        String accountId = "qywx_sh_1";
        List<UnionidTagDTO> list = qywxBgdImportMapper.getListByUnionId(param);
        if (list != null && list.size() > 0) {
            //获取关系数据
            List<String> unionIds = list.parallelStream().map(UnionidTagDTO::getUnionId).collect(Collectors.toList());
            List<Long> ids = list.parallelStream().map(UnionidTagDTO::getId).collect(Collectors.toList());
            if (unionIds != null && unionIds.size() > 0) {
                try {
                    List<CustomerUnionRelationDTO> relationList = qywxCutomerUserRelationMapper.getRelationByUnionIds(accountId, unionIds);
                    if (relationList != null && relationList.size() > 0) {
                        List<QywxBackTagRelation> insertList = new ArrayList<>();
                        //拼接关系与标签
                        for (CustomerUnionRelationDTO unionRelationDTO : relationList) {
                            for (UnionidTagDTO unionidTagDTO : list) {
                                QywxBackTagRelation insertBean = new QywxBackTagRelation();
                                if (unionidTagDTO.getUnionId().equals(unionRelationDTO.getUnionId()) && StringUtils.isNotBlank(unionidTagDTO.getWxTagId())) {
                                    insertBean.setAccountId(unionRelationDTO.getAccountId());
                                    insertBean.setExternalUserid(unionRelationDTO.getExternalUserid());
                                    insertBean.setUnionId(unionRelationDTO.getUnionId());
                                    insertBean.setUserId(unionRelationDTO.getUserId());
                                    insertBean.setGroupId(unionidTagDTO.getGroupId());
                                    insertBean.setGroupName(unionidTagDTO.getGroupName());
                                    insertBean.setTagId(unionidTagDTO.getWxTagId());
                                    insertBean.setTagName(unionidTagDTO.getWxTagName());
                                    insertList.add(insertBean);
                                }
                            }
                        }
                        //插入数据库
                        if (insertList != null && insertList.size() > 0) {
                            qywxBackTagRelationMapper.insertList(insertList);
                        }
                    }
                } catch (Exception e) {
                    log.error("生成上传标签表数据异常：" + e.getMessage());
                    qywxBgdImportMapper.updateStatus(2, ids);
                }
            }
            qywxBgdImportMapper.updateStatus(1, ids);
        }
        //修改状态
    }

    public void backQywxTagJob(String param) {
        String accountId = "qywx_sh_1";
        List<QywxBackTagRelation> list = qywxBackTagRelationMapper.selectList(accountId,param);
        if(list != null && list.size() > 0){
            List<Long> successIds = new ArrayList<>();
            List<Long> errorIds = new ArrayList<>();
            for(QywxBackTagRelation tagRelation : list){
                if(StringUtils.isNotBlank(tagRelation.getTagId())){
                    int result = backQywxTag(accountId , tagRelation.getUserId(),tagRelation.getExternalUserid(),tagRelation.getTagId());
                    if(result == 1){
                        successIds.add(tagRelation.getId());
                    }else{
                        errorIds.add(tagRelation.getId());
                    }
                }else{
                    errorIds.add(tagRelation.getId());
                }
            }
            if(successIds != null && successIds.size() > 0){
                qywxBackTagRelationMapper.updateStatus(1,successIds);
            }
            if(errorIds != null && errorIds.size() > 0){
                qywxBackTagRelationMapper.updateStatus(2,errorIds);
            }
        }
    }

    public int backQywxTag(String accountId, String userId, String external_userid, String tagId) {
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.MAKE_TAG_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("userid", userId);
            map.put("external_userid", external_userid);
            List<String> tags = new ArrayList<>();
            tags.add(tagId);
            map.put("add_tag", tags);
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            log.info("获取企业微信账户：" + accountId + "的微信部门列表数据：" + strbody);
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("回传企业微信标签数据错误：" + e.getMessage());
            return 0;
        }
    }


    public Result<?> bgdBackGroup(List<QywxBackGroupList> list) {
        Integer count = 0;
        //查询全量的需要展示的数据
//        List<QywxTagGroupDisplayDTO> displayGroupList = getDisplayGroupList();
//        if(CollectionUtils.isEmpty(displayGroupList)){
//            throw new BusinessException("大数据回传标签：未配置展示数据错误");
//        }
        try {
            for(QywxBackGroupList groupList : list){
                if(StringUtils.isBlank(groupList.getAccountId()) || StringUtils.isBlank(groupList.getExternalUserid()) || StringUtils.isBlank(groupList.getUserId()) || StringUtils.isBlank(groupList.getGroupCode())){
                    throw new BusinessException("请传入必填值");
                }
            }
            count = qywxBackGroupListMapper.insertList(list);
        } catch (Exception e) {
            log.error("大数据回传标签数据报错，" + e.toString());
            throw new BusinessException("大数据回传标签数据报错" + e.getMessage());
        }
        return Result.buildSuccess("回传数据成功", count);
    }


    public List<QywxTagGroupDisplayDTO> getDisplayGroupList(){
        List<Integer> types = new ArrayList<>(Arrays.asList(1,2));
        return  qywxTagGroupDisplayConfigService.getDisplayGroupALL(types);
    }

    public Result<List<QywxTagGroupDisplayDTO>> getDisplayGroupLists(List<Integer> types){
        return  Result.buildSuccess(qywxTagGroupDisplayConfigService.getDisplayGroupALL(types));
    }


    public void backQywxGroupTagValueJob(String param) {
        //查询需要回传的数据
        List<QywxBackGroupListDTO> list = qywxBackGroupListMapper.selectList(param);
        List<List<QywxBackGroupListDTO>> lists = splitList(list, 50);

        try {
            if (lists != null && lists.size() > 0) {
                CountDownLatch countDownLatch = new CountDownLatch(lists.size());
                for (List<QywxBackGroupListDTO> singleList : lists) {
                    try {
                        CompletableFuture.runAsync(() -> {
                            //同步企业微信
                            snycToQYWX(singleList);
                        });
                    } catch (Exception e) {
                        throw new BusinessException("调用京东接口报错：" + e.getMessage());
                    } finally {
                        countDownLatch.countDown();
                    }
                }
            }
        } catch (Exception e) {
            log.error("回传企微标签错误：" + e.toString());
            throw new BusinessException("");
        }
    }

    public void snycToQYWX(List<QywxBackGroupListDTO> list){
        if (list != null && list.size() > 0) {
            //获取全部的mapping关系
            List<QywxGroupTagForBackDTO> groupTagForBackDTOList = qywxTagRelationMappingService.getAllTagList();
            if(groupTagForBackDTOList == null || groupTagForBackDTOList.size() == 0){
                return;
            }
            Map<String, Map<String, List<QywxGroupTagForBackDTO>>> groupMap = groupTagForBackDTOList.stream()
                    .collect(Collectors.groupingBy(QywxGroupTagForBackDTO::getAccountId, Collectors.groupingBy(QywxGroupTagForBackDTO::getGroupCode)));

            //先修改状态，将这些数据挂起，不让后续数据影响   1:挂起；2：成功；3：失败；
            List<Long> executeIds = list.parallelStream().map(QywxBackGroupListDTO::getId).collect(Collectors.toList());
            qywxBackGroupListMapper.updateStatus(1, executeIds);
            //调用大数据的接口
            List<QywxOutDTO> bgBackList = getTagsByUserIdList(list);
            if(bgBackList != null && bgBackList.size() > 0){
                List<Long> successIds = new ArrayList<>();
                List<Long> errorIds = new ArrayList<>();
                for(QywxOutDTO qywxOutDTO : bgBackList){
                    //获取上传的标签
                    if(StringUtils.isBlank(qywxOutDTO.getTagValue())){
                        log.error("获取用户标签信息异常：" + qywxOutDTO.toString());
                        errorIds.add(qywxOutDTO.getId());
                        continue;
                    }
                    List<QywxGroupTagForBackDTO> targetGroup = groupMap.get(qywxOutDTO.getAccountId()).get(qywxOutDTO.getGroupCode());
                    if(targetGroup == null || targetGroup.size() == 0){
                        log.error("获取标签组基础信息异常：" + qywxOutDTO.toString());
                        errorIds.add(qywxOutDTO.getId());
                        continue;
                    }
                    List<String> tags = Arrays.asList(qywxOutDTO.getTagValue().split(","));
                    List<String> addTagIds =new ArrayList<>();
                    List<String> removeTagIds =new ArrayList<>();
                    targetGroup.forEach( target -> {
                        String tag1 =  tags.parallelStream().filter( tag -> target.getTagCode().equals(tag) ).findFirst().orElse(null);
                        if(StringUtils.isNotBlank(tag1)){
                            addTagIds.add(target.getWxTagId());
                        }else {
                            removeTagIds.add(target.getWxTagId());
                        }
                    });
                    int result= backQywxTag(qywxOutDTO.getAccountId() , qywxOutDTO.getUserId(), qywxOutDTO.getExternalUserId(),addTagIds,removeTagIds );
                    if(result == 1){
                        successIds.add(qywxOutDTO.getId());
                    }else{
                        errorIds.add(qywxOutDTO.getId());
                    }
                }
                if(successIds != null && successIds.size() > 0){
                    qywxBackGroupListMapper.updateStatus(2,successIds);
                }
                if(errorIds != null && errorIds.size() > 0){
                    qywxBackGroupListMapper.updateStatus(3,errorIds);
                }
            }
        }
    }


    public List<List<QywxBackGroupListDTO>> splitList(List<QywxBackGroupListDTO> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        List<List<QywxBackGroupListDTO>> result = new ArrayList<>();
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<QywxBackGroupListDTO> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }

    public List<QywxOutDTO> getTagsByUserIdList(List<QywxBackGroupListDTO> list) {
        try {
            String url = DIG_DATA_TAG_URL + "/getCdpTagByUserIdAndCol";
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            HttpEntity httpEntity = new HttpEntity<>(list,headers);
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            requestFactory.setConnectTimeout(5 * 1000);// 设置超时
            requestFactory.setReadTimeout(5 * 1000);
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            Result<List<QywxOutDTO>> resp  = restTemplate.postForObject(url, list,Result.class);
            List<QywxOutDTO> result = JSON.parseArray(JSON.toJSONString(resp.getData()),QywxOutDTO.class);
            return result;
        } catch (Exception e) {
            throw new BusinessException("获取数据异常：" + e.getMessage());
        }
    }


    public int backQywxTag(String accountId, String userId, String external_userid, List<String> addTagIds , List<String> removeTagIds) {
        String token = qywxCommonUtil.getQYWXToken(accountId);
        if (StringUtils.isBlank(token)) {
            log.error("企业微信账户：" + accountId + "获取token失败！");
        }
        String uri = QywxConstantConfig.MAKE_TAG_URL + "access_token=" + token;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            Map map = new HashMap();
            map.put("userid", userId);
            map.put("external_userid", external_userid);
            if(addTagIds != null && addTagIds.size() > 0){
                map.put("add_tag", addTagIds);
            }
            if(removeTagIds != null && removeTagIds.size() > 0){
                map.put("remove_tag", removeTagIds);
            }
            String bodyJsonData = JSON.toJSONString(map);
            HttpEntity<String> entity = new HttpEntity<String>(bodyJsonData, headers);
            RestTemplate restTemplate = new RestTemplate();
            String strbody = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class).getBody();
            log.info("上传大数据标签：" + accountId + "：" + strbody);
            JSONObject jsonObject = JSONObject.parseObject(strbody);
            if (jsonObject.getString("errcode") != null && (QywxConstantConfig.ERR_CODE).equals(jsonObject.getString("errcode").toString())) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            log.error("回传企业微信标签数据错误：" + e.getMessage());
            return 0;
        }
    }


    public Result<List<QywxTagGroupTagDisplayDTO>> getDisplayGroupTagLists(List<Integer> types) {
        List<QywxTagGroupTagDisplayDTO> list = qywxTagGroupDisplayConfigService.getDisplayGroupTagList(types);
        return Result.buildSuccess(list);
    }
}
