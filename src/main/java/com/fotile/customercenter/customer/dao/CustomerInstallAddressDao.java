package com.fotile.customercenter.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.customercenter.customer.pojo.entity.UserAddress;
import com.fotile.customercenter.customer.pojo.entity.UserInstallAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 顾客dao方法
 *
 * <AUTHOR>
 */
public interface CustomerInstallAddressDao extends BaseMapper<UserInstallAddress> {



    List<UserAddress> queryAddressListByParams(@Param("customerId") Long customerId);

    void updateOtherNoDefalut(@Param("id") Long id,@Param("customerId") Long customerId);

    int findCountByCustomerId(@Param("id") Long id, @Param("customerId") Long customerId);
}
