package com.fotile.customercenter.customer.pojo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@Data
@ApiModel(value = "个人信息收集", description = "个人信息收集")
public class CustomerPersonalInfo {

	@ApiModelProperty(value="id",example="id")
	private Long id;

	@ApiModelProperty(value="小程序标识",example="小程序标识")
	private String openId;

	@ApiModelProperty(value="是否删除 0：否；其他：是",example="是否删除 0：否；其他：是")
	private Long isDeleted;
	  
	@ApiModelProperty(value="创建者",example="创建者")
	private String createdBy;
	  
	@ApiModelProperty(value="创建时间",example="创建时间")
	private Date createdDate;
	  
	@ApiModelProperty(value="修改者",example="修改者")
	private String modifiedBy;
	  
	@ApiModelProperty(value="修改时间",example="修改时间")
	private Date modifiedDate;

	@ApiModelProperty(value="开始时间",example="开始时间")
	private Date startDate;

	@ApiModelProperty(value="结束时间",example="结束时间")
	private Date endDate;
	
	@ApiModelProperty(value="次数",example="次数")
	private Integer amount ;

	/**
	 * 1：个人信息-修改头像
	 * 2：个人信息-修改微信昵称
	 * 3：个人信息修改-完善个人信息-选中的图片/视频
	 * 4：参与话题活动选中的图片/视频
	 * 5：会员登录-用户注册手机号
	 * 6：会员登录-用户登录手机号
	 * 7：自助预约线下门店服务-留资手机号
	 * 8：自助查询附近门店-位置信息
	 * 9：自助预约线下门店服务-留资位置信息
	 * 10:用户基础信息-头像（商城小程序）
	 * 11:用户基础信息-微信昵称（商城小程序）
	 * 12：用户基础信息-评价（商城小程序）
	 * 13:用户身份信息-用户注册登陆手机号（商城小程序）
	 * 14:用户身份信息-用户实名认证姓名（商城小程序）
	 * 15:用户身份信息-实名认证手机号（商城小程序）
	 * 16:用户身份信息-用户实名认证身份证号（商城小程序）
	 * 18:交易信息-收货地址（商城小程序）
	 * 19:交易信息-发票（商城小程序）
	 * 20:位置信息（商城小程序）
	 * 21:用户基础信息-评价（商城H5）
	 * 22:用户身份信息-用户注册登陆手机号（商城H5）
	 * 23:交易信息-收货地址（商城H5）
	 * 24:交易信息-发票（商城H5）
	 * 25:位置信息（商城H5）
	 */
	@ApiModelProperty(value="类型",example="类型")
	private Integer type ;

	@ApiModelProperty(value="来源",example="来源")
	private Integer source ;

	@ApiModelProperty(value="操作类型",example="操作类型，1：新增，2：修改")
	private Integer operationType ;

	@ApiModelProperty(value="手机号",example="手机号")
	@FieldEncrypt
	private String phone;

	@ApiModelProperty(value="地址",example="地址")
	private String address;
	  
}
