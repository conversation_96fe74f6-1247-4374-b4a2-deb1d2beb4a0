package com.fotile.customercenter.customer.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditMiniCustomerInfoDTO implements Serializable {

    private Long id;
    /** * 顾客－头像 */
    private String headPortrait;

    /** * 顾客－生日 */
    private Date birthday;

    /** * 顾客－性别 0未知  1 男  2女   */
    private Integer sex;

    /** * 职业code */
    private String professionalCode;

    /** * 职业名称 */
    private String professionalName;


    /** * 顾客－邮箱 */
    private String email;

    /** * 小区id */
    private Long villageId;


    private String nickName;

//    /** * 详细地址 */
//    private String address;

}
