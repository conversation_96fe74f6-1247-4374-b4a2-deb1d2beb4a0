package com.fotile.customercenter.customer.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ Author     ：liangfei
 * @ Date       ：16点58分
 * @ Description：
 * @ Modified By：
 * @Version: $
 */
@Data
public class UpdateMallCustomerInfoDto implements Serializable {
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("头像")
    private String headPortrait;
    @ApiModelProperty("性别 0-未知 1-男 2-女")
    private Integer sex;
    @ApiModelProperty("姓名")
    private String nickName;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("生日")
    private String birthday;
    @ApiModelProperty("真实姓名")
    private String realName;
    @ApiModelProperty("类型")
    private Integer type = 1;

}
