package com.fotile.customercenter.customer.pojo;

import com.fotile.customercenter.customer.pojo.dto.OperatorLogDto;
import com.fotile.customercenter.customer.pojo.entity.OperatorLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OperatorLogConverter {
    public OperatorLogConverter INSTANCE = Mappers.getMapper(OperatorLogConverter.class);

    public OperatorLog operatorLogDao2OperatorLog(OperatorLogDto dto);
    public List<OperatorLog> operatorLogDaos2OperatorLogs(List<OperatorLogDto> dtos);

   // OperatorLogDto operatorLog2OperatorLogDao(OperatorLog operatorLog);

   // QueryOperatorLogDto operatorLog2QueryOperatorLogOutDao(OperatorLog operatorLog);


}

