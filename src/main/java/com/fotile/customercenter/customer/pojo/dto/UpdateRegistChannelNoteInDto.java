package com.fotile.customercenter.customer.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
public class UpdateRegistChannelNoteInDto implements Serializable {
    private Long id;

    @TableField(value = "regist_channel_note")
    /** * 注册来源说明 */
    private String registChannelNote;

    @TableField(value = "type")
    /** * 顾客－类型 1-线上会员 2-线上粉丝 3-门店顾客  */
    private Integer type;


}
