package com.fotile.customercenter.customer.service;

import com.fotile.customercenter.customer.dao.UserPopConfirmMapper;
import com.fotile.customercenter.customer.pojo.entity.UserPopConfirmInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class UserPopConfirmService {

    @Autowired
    private UserPopConfirmMapper userPopConfirmMapper;

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    public int addPopConfirmInfo(UserPopConfirmInfo userPopConfirmInfo) {
        String popConfirmInfo = userPopConfirmMapper.queryPopConfirmInfo(userPopConfirmInfo.getOpenId(), userPopConfirmInfo.getAdverpositionCode());
        if(StringUtils.isNotBlank(popConfirmInfo)){
            userPopConfirmInfo.setStatus(1);
            return userPopConfirmMapper.updatePopConfirmInfo(userPopConfirmInfo);
        }
        String currentUserId = userAuthorConfig.getCurrentUserId();
        userPopConfirmInfo.setStatus(1);
        userPopConfirmInfo.setCreatedBy(currentUserId);
        userPopConfirmInfo.setModifiedBy(currentUserId);
        return userPopConfirmMapper.addPopConfirmInfo(userPopConfirmInfo);
    }

    public Boolean isPopConfirm(String openId, String adverpositionCode) {
        String popConfirmInfo = userPopConfirmMapper.queryPopConfirmInfo(openId, adverpositionCode);
        return StringUtils.isNotBlank(popConfirmInfo);
    }

    public int updatePopConfirmInfo(UserPopConfirmInfo userPopConfirmInfo) {
        userPopConfirmInfo.setStatus(0);
        return userPopConfirmMapper.updatePopConfirmInfo(userPopConfirmInfo);
    }
}
