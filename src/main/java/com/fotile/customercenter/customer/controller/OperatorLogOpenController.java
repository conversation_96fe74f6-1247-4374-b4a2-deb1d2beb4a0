package com.fotile.customercenter.customer.controller;

import com.fotile.customercenter.customer.pojo.dto.OperatorLogDto;
import com.fotile.customercenter.customer.service.OperatorLogService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
/** * 操作日志信息接口open */
@RequestMapping("/api/open/operatorLog")
@Slf4j
public class OperatorLogOpenController extends BaseController {
    @Autowired
    private OperatorLogService operatorLogService;

    @RequestMapping(value = "/insertOperatorLog", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 插入日志 */
    public Result insertOperatorLog(@RequestBody OperatorLogDto operatorLogDto) {
        int i = operatorLogService.insertOperatorLog(operatorLogDto, true);
        return success(i);
    }

    @RequestMapping(value = "/insertBatchOperatorLog", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 批量插入操作日志 */
    public Result insertBatchOperatorLog(@RequestBody List<OperatorLogDto> operatorLogDtos) {
        int i = operatorLogService.insertBatchOperatorLog(operatorLogDtos);
        return success(i);
    }
}
