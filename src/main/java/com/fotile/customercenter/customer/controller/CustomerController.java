package com.fotile.customercenter.customer.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.customercenter.customer.mq.UserAccountChannel;
import com.fotile.customercenter.customer.mq.vo.UpdateMobileMqDTO;
import com.fotile.customercenter.customer.pojo.dto.*;
import com.fotile.customercenter.customer.pojo.dto.NiuBang.UpdateGradeIdAnduserIdDto;
import com.fotile.customercenter.customer.pojo.entity.CustomerInfo;
import com.fotile.customercenter.customer.pojo.entity.CustomerPersonalInfo;
import com.fotile.customercenter.customer.pojo.entity.CustomerSignInEntity;
import com.fotile.customercenter.customer.service.CustomerBatchService;
import com.fotile.customercenter.customer.service.CustomerService;
import com.fotile.customercenter.customer.service.UserAddressService;
import com.fotile.customercenter.fotilestyle.pojo.FollowInfoStatisticsVO;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.ChannelAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 门店顾客列表接口
 */
@RestController
@Api(value = "门店顾客列表接口", tags = {"顾客地址信息"})
@RequestMapping({"/api/customer","/m/api/customer"})
@Slf4j
public class CustomerController extends BaseController {
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerBatchService customerBatchService;
    @Autowired
    private UserAddressService customerAddressService;
    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @RequestMapping(value = "/queryCustomersByParams", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 多参数查询客户列表 */
    public Result<Page<QueryCustomersByParamsOutDto>> queryCustomersByParams(@RequestBody @Valid QueryCustomersByParamsInDto inDto) {
        List<ChannelAuthor> channelAuthorList = userAuthorConfig.queryChannelAuthorList();
        Set<Long> channels = new HashSet<>();
        if (channelAuthorList != null && channelAuthorList.size() > 0) {
            channelAuthorList.stream().forEach(x -> {
                channels.add(x.getChannelId());
            });
            inDto.setChannels(new ArrayList<>(channels));
        }
        return success(customerService.queryCustomersByParams(inDto));
    }

    /**
     * 多参数查询客户列表-小程序使用（无渠道权限）
     * @param inDto
     * @return
     */
    @RequestMapping(value = "/queryCustomersByParamsList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<QueryCustomersByParamsOutDto>> queryCustomersByParamsList(@RequestBody QueryCustomersByParamsInDto inDto) {
        return success(customerService.queryCustomersByParamsList(inDto));
    }

    @RequestMapping(value = {"/queryCustomerDetailById", "/api/open/queryCustomerDetailById"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据Id查询详细客户信息 */
    public Result<CustomerDetailOutDto> queryCustomerDetailById(@Valid QueryCustomerDetailByIdInDto inDto) {
        CustomerDetailOutDto outDto = customerService.queryCustomerDetailById(inDto);
        return success(outDto);
    }

    @RequestMapping(value = {"/saveOrUpdateCustomerInfoById"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据Id更新或新增客户信息-- */
    public Result<CustomerInfoDto> saveOrUpdateCustomerInfoById(@RequestBody CustomerInfoDto dto) {
        return success(customerService.saveOrUpdateCustomerInfoById(dto));
    }

    @RequestMapping(value = {"/queryOrSaveCustomerInfoByPhone", "/m/queryOrSaveCustomerInfoByPhone"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号码更新或新增客户信息--门店顾客使用 */
    public Result<CustomerInfoDto> queryOrSaveCustomerInfoByPhone(@RequestBody CustomerInfoDto customerInfoDto) {
        log.info("********queryOrSaveCustomerInfoByPhone" + customerInfoDto.toString());
        customerInfoDto.setType(3);
        int i = customerService.checkPhone(customerInfoDto.getPhone());
        log.info("******checkPhone检查此手机号有: " + i + "条数据");
        if (i > 1) {
            return failure("此手机号码存在1条以上的可用数据");
        } else if (i == 0) {
            return success("新增数据成功", customerService.saveCustomerByPhone(customerInfoDto));
        } else {
            return success("已匹配或修改数据", customerService.updateAndQueryCustomerByPhone(customerInfoDto));
        }
        //return failure("未知错误");
    }

    @RequestMapping(value = "/queryOrSaveCustomerInfo2ByPhone", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号码更新或新增客户信息--中台的新增线索接口使用 */
    public Result<CustomerInfoDto> queryOrSaveCustomerInfo2ByPhone(@RequestBody CustomerInfoDto customerInfoDto) {
        log.info("********queryOrSaveCustomerInfo2ByPhone" + customerInfoDto.toString());
        customerInfoDto.setType(3);
        int i = customerService.checkPhone(customerInfoDto.getPhone());
        if (i > 1) {
            return failure("此手机号码存在1条以上的可用数据");
        } else if (i == 0) {
            return success("新增数据成功", customerService.saveCustomerDirectByPhone(customerInfoDto));
        } else {
            return success("已匹配或修改数据", customerService.updateAndQueryCustomerByPhone(customerInfoDto));
        }
        //return failure("未知错误");
    }

    @RequestMapping(value = "/saveOrUpdateCustomerInfoListByPhone", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号码批量更新更新或新增客户信息--门店顾客使用 */
    public Result<List<CustomerInfoDto>> saveOrUpdateCustomerInfoListByPhone(@RequestBody List<CustomerInfoDto> dtos) {
        log.info("********根据手机号码批量更新更新或新增客户信息{}" , dtos);
        List<CustomerInfoDto> dtoList = customerBatchService.saveOrUpdateCustomerInfoListByPhone(dtos);
        return success("批量更新或新增数据成功", dtoList);
    }


    /**
     * 顾客签到
     *
     * @param customerSignInInDto
     * @return
     */
    @RequestMapping(value = "/api/open/customerSignIn", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 顾客签到 */
    public Result<CustomerSignInInDto> customerSignIn(@Valid @RequestBody CustomerSignInInDto customerSignInInDto) {
        log.info("***********CustomerController.customerSignIn customerSignInInDto=" + customerSignInInDto);
        customerSignInInDto = customerService.customerSignIn(customerSignInInDto);
        if (customerSignInInDto != null && customerSignInInDto.getId() != null)
            return success("签到成功!", customerSignInInDto);
        return failure("签到失败!");
    }

    @RequestMapping(value = "/queryInfoForClue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号,姓名,返回客户的基础信息和地址i信息 */
    public Result<UserAddressOutDto> queryInfoForClue(QueryInfoForClueInDto inDto) {
        //  PageInfo<?> pageInfo=
        return success(customerService.queryInfoForClue(inDto));
    }

    @RequestMapping(value = "/updatePasswd", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 修改密码 */
    public Result updatePasswd(@Valid @RequestBody UpdatePasswdInDto inDto) {
        return success(customerService.updatePasswd(inDto));
    }

    @RequestMapping(value = "/updateAccountState", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 修改状态 */
    public Result updateAccountState(@Valid @RequestBody UpdateAccountStateInDto inDto) {
        if (StringUtils.isBlank(inDto.getUserEntry())) {
            return failure("该会员已长时间未使用此帐号,请先联系会员登录后再进行修改!");
        }
        return success(customerService.updateAccountState(inDto));
    }

    @RequestMapping(value = "/updatePhone", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 修改手机号 */
    public Result updatePhone(@Valid @RequestBody UpdatePhoneInDto inDto) {
        return success(customerService.updatePhone(inDto));
    }

    @RequestMapping(value = "/updateSex", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 修改性别 */
    public Result updateSex(@Valid @RequestBody UpdateSexInDto inDto) {
        return success(customerService.updateSex(inDto));
    }

    @RequestMapping(value = "/updateRegistChannelNote", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 修改注册来源备注 */
    public Result updateRegistChannelNote(@Valid @RequestBody UpdateRegistChannelNoteInDto inDto) {
        return success(customerService.updateRegistChannelNote(inDto));
    }


    /**
     * 顾客签到一栏表
     *
     * @param customerSignInPageAllInDto
     * @return
     */
    @RequestMapping(value = "/customerSignInPageAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 顾客签到顾客签到一栏表 */
    public Result<List<CustomerSignInEntity>> customerSignInPageAll(CustomerSignInPageAllInDto customerSignInPageAllInDto) {
        log.info("***********CustomerController.customerSignInPageAll customerSignInPageAllInDto=" + customerSignInPageAllInDto);
        return success(customerService.customerSignInPageAll(customerSignInPageAllInDto));
    }

    /**
     * 获取顾客keyclockid
     */
    @RequestMapping(value = "/queryKeyclockIds", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 获取顾客keyclockid */
    public Result<List<String>> queryKeyclockIds(@Valid QueryCustomerInfoByPhoneTypeNickName queryCustomerInfoByPhoneTypeNickName) {
        log.info("***********CustomerController.queryKeyclockId queryCustomerInfoByPhoneTypeNickName=" + queryCustomerInfoByPhoneTypeNickName);
        return success(customerService.queryCustomerInfoByPhoneAndNickName(queryCustomerInfoByPhoneTypeNickName));
    }

    /**
     * 获取顾客id
     */
    @RequestMapping(value = "/queryCustomerIds", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 获取顾客id */
    public Result<List<String>> queryCustomerIds(@Valid QueryCustomerInfoByPhoneTypeNickName queryCustomerInfoByPhoneTypeNickName) {
        log.info("***********CustomerController.queryCustomerIds queryCustomerInfoByPhoneTypeNickName=" + queryCustomerInfoByPhoneTypeNickName);
        return success(customerService.queryCustomerIdByPhoneAndNickName(queryCustomerInfoByPhoneTypeNickName));
    }

    @RequestMapping(value = "/api/open/updateGrade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 批量修改会员等级 */
    public Result updateGrade(@Valid @RequestBody List<UpdateGradeIdAnduserIdDto> list) {
        log.info("批量修改会员等级:" + list);
        customerService.updateGradeAndUserid(list);
        return success();
    }

    @RequestMapping(value = "/api/open/foreignAddOrAmendCustomerInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 对外新增或修改用户基本信息 */
    public Result foreignAddOrAmendCustomerInfo(@RequestBody ForeignAddOrAmendCustomerInfoInDto foreignAddOrAmendCustomerInfoInDto) {
        log.info("对外新增或修改用户基本信息:" + foreignAddOrAmendCustomerInfoInDto);
        return success(customerService.foreignAddOrAmendCustomerInfo(foreignAddOrAmendCustomerInfoInDto));
    }


    @RequestMapping(value = {"/queryCustomerIdByPhone", "/api/open/queryCustomerIdByPhone"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号查询客户id */
    public Result<Long> queryCustomersByPhone(String phone) {
        List<CustomerInfo> list = customerService.getCustomerByPhone(phone);
        if (list != null && list.size() != 0) {
            return success(list.get(0).getId());
        }
        return success();
    }

    /**
     * 插入用户基本信息与地址信息
     */
    @RequestMapping(value = {"/addUserInfoAndAddress", "/m/addUserInfoAndAddress"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 插入用户基本信息与地址信息 */
    public Result addUserInfoAndAddress(@RequestBody CustomerInfoDto customerInfoDto) {
        List<CustomerInfo> list = customerService.getCustomerByPhone(customerInfoDto.getPhone());
        CustomerInfoDto customerInfo = new CustomerInfoDto();
        if (list == null || list.size() <= 0) {
            customerInfo = customerService.saveOrUpdateCustomerInfoById(customerInfoDto);
        } else {
            customerInfo.setId(list.get(0).getId());
        }

        //绑定地址
        UserAddressOutDto userAddressOutDto = new UserAddressOutDto();
        userAddressOutDto.setCustomerInfoId(customerInfo.getId());

        if (StringUtils.isNotEmpty(customerInfoDto.getCountyCode())) {
            userAddressOutDto.setCountyId(Long.valueOf(customerInfoDto.getCountyCode()));
            userAddressOutDto.setProvinceId(Long.valueOf(customerInfoDto.getProvinceCode()));
            userAddressOutDto.setProvinceName(customerInfoDto.getProvinceName());
            userAddressOutDto.setCityId(Long.valueOf(customerInfoDto.getCityCode()));
            userAddressOutDto.setCityName(customerInfoDto.getCityName());
            userAddressOutDto.setCountyName(customerInfoDto.getCountyName());
            customerAddressService.saveOrUpdateUserAddressById(userAddressOutDto);
        }


        return success(customerInfoDto);
    }

    /**
     * 批量修改顾客信息
     */
    @RequestMapping(value = {"/batchUpdateCustomerInfo", "/m/batchUpdateCustomerInfo"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 批量修改顾客信息 */
    public Result batchUpdateCustomerInfo(@RequestBody List<BatchUpdateCustomerInfoInDto> batchUpdateCustomerInfoInDto) {
        log.info("批量修改顾客信息入参" + JSON.toJSONString(batchUpdateCustomerInfoInDto));

        return success(customerService.batchUpdateCustomerInfo(batchUpdateCustomerInfoInDto));
    }

    @RequestMapping(value = "/queryCustomerPhoneById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据Id查询顾客手机号 */
    public Result<String> queryCustomerPhoneById(Long uid) {
        String phone = customerService.queryCustomerPhoneById(uid);
        return success("成功", phone);
    }


    @RequestMapping(value = "/findVipMemberList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 查询幸福家会员列表 */
    public Result queryCustomfindRegisterMemberListerPhoneById(@RequestBody HappyRegisterMemberListInDto inDto) {
        return success(customerService.findRegisterMemberList(inDto));
    }

    @RequestMapping(value = "/refundOrder", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 幸福家VIP订单退款，修改到期时间修改到期时间剪少365天 */
    public String refundOrder(String userId) {
        return customerService.refundOrder(userId);
    }

    @RequestMapping(value = "/findCustomerInfoByUserEntity", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据条件模糊查询 */
    public Result<List<CustomerInfo>> findCustomerInfoByUserEntity(@RequestBody CustomerInfoByUserEntityDto userEntityDto){
        return success(customerService.findCustomerInfoByUserEntity(userEntityDto));
    }

    @RequestMapping(value = "/findCustomerInfoByUserEntityId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据userEntity单个查询 */
    public Result<CustomerInfo> findCustomerInfoByUserEntityId(@RequestParam(value = "userEntityId") String userEntityId){
        return success(customerService.findCustomerInfoByUserEntityId(userEntityId));
    }

    @RequestMapping(value = "/findCustomerInfoByUserEntityIds", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据userEntitys查询多个 */
    public Result<List<CustomerInfo>> findCustomerInfoByUserEntityIds(@RequestBody List<String> userEntityIds){
        return success(customerService.findCustomerInfoByUserEntityIds(userEntityIds));
    }

    @RequestMapping(value = "/addOrUpdateMemberShip", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 新增/修改 */
    public Result addOrUpdate(@RequestBody AddOrUpdateMemberInDto inDto) throws Exception {
        return success(customerService.addVipMember(inDto));
    }


    @RequestMapping(value = "/setVip", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 查询幸福家会员列表 */
    public Result setVip() {
        try {
            customerService.setVip();
            return success("成功");
        }catch (Exception e) {
            e.printStackTrace();
            return failure("失败");
        }
    }
    @RequestMapping(value = {"/queryCustomerInfoByIds","/api/open/queryCustomerInfoByIds"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 查询幸福家会员列表 */
    public Result queryCustomerInfoByIds(@RequestParam List<Long> ids) {
        try {
            return success(customerService.queryCustomerInfoByIds(ids));
        }catch (Exception e) {
            e.printStackTrace();
            return failure("失败");
        }
    }
    @RequestMapping(value = {"/deletedCustomerInfo","/api/open/m/deletedCustomerInfo","/api/m/deletedCustomerInfo"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 查询幸福家会员列表 */
    public Result deletedCustomerInfo(@RequestParam String accountId) {
        try {
            return success(customerService.deletedCustomerInfo(accountId));
        }catch (Exception e) {
            e.printStackTrace();
            return failure("失败");
        }
    }
    /**
     * 用户管理-统计粉丝列表
     */
    @RequestMapping(value = "/queryFansList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)

    public Result<PageInfo<FollowInfoStatisticsVO>> queryFansList(@RequestBody QueryFansListInDto inDto) {
        return success(customerService.queryFansList(inDto));
    }

    /**
     * 查询顾客的社区店会员
     * @param customerId
     * @return
     */
    @RequestMapping(value ={"/getCustomerStoreIds"} , method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result <List<Long>> getCustomerStoreIds(@RequestParam(value = "customerId",required = false) Long customerId){
        List<Long> customerIds =  customerService.getCustomerStoreIds(customerId);
        return success(customerIds);
    }

    /**
     * 根据手机号查询用户指定信息
     * @param phone
     * @return
     */

    @RequestMapping(value = {"/queryCustomerInfoByPhone", "/api/open/queryCustomerInfoByPhone"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result queryCustomerInfoByPhone(@RequestParam String phone, @RequestParam String dateType) {
        return success(customerService.queryCustomerInfoByPhone(phone, dateType));
    }

    /**
     * 查询个人信息收集
     * @param openId
     * @param source
     * @param dateType
     * @return
     */

    @RequestMapping(value = {"/queryPersonalInfo", "/api/open/queryPersonalInfo"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result queryPersonalInfo(@RequestParam String openId, @RequestParam Integer source, @RequestParam String dateType) {
        return success(customerService.queryPersonalInfoList(openId, source, dateType));
    }

    /**
     * 新增个人信息收集
     * @param inDto
     * @return
     */

    @RequestMapping(value = {"/addPersonalInfo", "/api/open/addPersonalInfo"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result addPersonalInfo(PersonalInfoInDto inDto) {
        try {
            customerService.addPersonalInfo(inDto);
            return success("操作成功");
        } catch (Exception e) {
            return failure("异常", e);
        }
    }

    /**
     * 个人信息收集更新mq【接收通道】
     */
    @StreamListener(UserAccountChannel.CUSTOMER_PERSONAL_INFO_INPUT)
    public void addPersonalInfo(Message<String> message) {
        log.info(">>>个人信息收集更新MQ+UserAccountChannel.CUSTOMER_PERSONAL_INFO_INPUT" +
                message.getPayload());
        CustomerPersonalInfo customerPersonalInfo = JSONObject.parseObject(message.getPayload(), CustomerPersonalInfo.class);
        if (customerPersonalInfo != null && Objects.equals(customerPersonalInfo.getOperationType(), 2)) {
           customerService.updatePersonalInfo(customerPersonalInfo);
           return;
        }
        customerService.addPersonalInfo(customerPersonalInfo);
    }

}
