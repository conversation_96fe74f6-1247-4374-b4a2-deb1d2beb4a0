package com.fotile.customercenter.customer.controller;


import com.fotile.customercenter.customer.pojo.dto.*;
import com.fotile.customercenter.customer.pojo.entity.UserAddress;
import com.fotile.customercenter.customer.service.UserAddressService;
import com.fotile.customercenter.util.EmptyUtil;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 门店顾客地址列表接口
 */
@RestController
@Api(value = "门店顾客列表接口", tags = {"顾客地址信息"})
@RequestMapping("/api/userAddress")
@Slf4j
public class UserAddressController extends BaseController {
    @Autowired
    private UserAddressService userAddressService;


    @RequestMapping(value = "/queryUserAddressByCustomerId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户Id查询地址信息 */
    public Result queryUserAddressByCustomerId(@Valid QueryUserAddressByCustomerIdInDto inDto) {
        return success(userAddressService.queryUserAddressByCustomerId(inDto));
    }

    @RequestMapping(value = "/queryUserInfoByAddressId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id查询用户地址和房型信息 */
    public Result<QueryUserInfoByAddressIdOutDto> queryUserInfoByAddressId(@Valid @RequestParam Long id) {

        return success(userAddressService.queryUserInfoByAddressId(id));
    }

    @RequestMapping(value = {"/queryUserAddressById","/api/open/queryUserAddressById"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id查询用户地址信息 */
    public Result<UserAddressOutDto> queryUserAddressById(@Valid @RequestParam Long id) {

        return success(userAddressService.queryUserAddressById(id));
    }

    @RequestMapping(value = "/saveOrUpdateUserAddressById", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id查询用户地址信息--门店顾客使用 */
    public Result<UserAddressOutDto> saveOrUpdateUserAddressById(@Valid @RequestBody UserAddressOutDto outDto) {
        if (EmptyUtil.isEmpty(outDto.getProvinceId())) {
            outDto.setProvinceId(-1L);
        }
        if (EmptyUtil.isEmpty(outDto.getCityId())) {
            outDto.setCityId(-1L);
        }
        if (EmptyUtil.isEmpty(outDto.getVillageId())) {
            outDto.setVillageId(-1L);
        }
        if (EmptyUtil.isEmpty(outDto.getCountyId())) {
            outDto.setCountyId(-1L);
        }
        //return success(userAddressService.queryUserAddressById(id));
        return success(userAddressService.saveOrUpdateUserAddressById(outDto));
    }

    @RequestMapping(value = "/m/saveOrUpdateUserAddressByCustomerId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id查询用户地址信息--门店顾客使用 */
    public Result<UserAddressOutDto> saveOrUpdateUserAddressByCustomerId(@Valid @RequestBody UserAddressOutDto userAddressOutDto) {
        if (EmptyUtil.isEmpty(userAddressOutDto.getProvinceId())) {
            userAddressOutDto.setProvinceId(-1L);
        }
        if (EmptyUtil.isEmpty(userAddressOutDto.getCityId())) {
            userAddressOutDto.setCityId(-1L);
        }
        if (EmptyUtil.isEmpty(userAddressOutDto.getVillageId())) {
            userAddressOutDto.setVillageId(-1L);
        }
       /* if (EmptyUtil.isEmpty(userAddressOutDto.getCountyId())) {
            userAddressOutDto.setCountyId(-1L);
        }*/
        //return success(userAddressService.queryUserAddressById(id));
        return success(userAddressService.saveOrUpdateUserAddressByCustomerId(userAddressOutDto));
    }

    @RequestMapping(value = {"/selectUserAddressByIdList","/api/open/selectUserAddressByIdList"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id列表查询用户地址信息列表--门店顾客使用 */
    public Result<List<UserAddressOutDto>> selectUserAddressByIdList(@Valid @RequestBody List<Long> idList) {
        return success(userAddressService.selectUserAddressByIdList(idList));
    }

    @RequestMapping(value = {"/selectSimpleUserAddressByIdList","/api/open/selectSimpleUserAddressByIdList"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户地址Id列表查询用户地址信息列表--门店顾客使用 */
    public Result<List<UserAddressSimpleOutDTO>> selectSimpleUserAddressByIdList(@Valid @RequestBody UserAddressSimpleInDTO dto) {
        return success(userAddressService.selectSimpleUserAddressByIdList(dto));
    }


    @RequestMapping(value = {"/getUserAddressByPhone","/api/open/getUserAddressByPhone"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据手机号查询地址 */
    public Result<List<UserAddressOutDto>> getUserAddressByPhone(String phone,Integer source) {
        if (StringUtils.isBlank(phone)) {
            return failure("手机号不能空");
        }
        if(source==null){
            return failure("来源不能空");
        }
        return success(userAddressService.queryUserAddressByPhone(phone,source));
    }

    /**
     * 批量新增会员地址
     * @param batchAddVillageDto
     * @return
     */
    @RequestMapping(value = {"/addCustomerAddress","/api/open/addCustomerAddress"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 批量新增会员地址 */
    public Result<Long> addCustomerAddress(@Valid @RequestBody BatchAddVillageDto batchAddVillageDto) {
        Long id = userAddressService.addCustomerAddress(batchAddVillageDto);
        return success("查询成功",id);
    }

    @RequestMapping(value = {"/deleteAddressById","/api/open/deleteAddressById"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据地址id删除地址 */
    public Result deleteAddressById(Long id) {
        if (id == null) {
            return failure("地址id不能为空");
        }
        userAddressService.deleteAddressById(id);
        return success();
    }

    @RequestMapping(value = {"/updateAddressById","/api/open/updateAddressById"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据地址id更新地址 */
    public Result updateAddressById(@RequestBody UserAddress userAddress) {
        if (userAddress.getId() == null) {
            return failure("地址id不能为空");
        }
        userAddressService.updateAddressById(userAddress);
        return success();
    }

    @RequestMapping(value = {"/editAddressById","/api/open/editAddressById"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 新增编辑地址 */
    public Result editAddressById(@RequestBody UserAddress userAddress) {
        userAddressService.editAddressById(userAddress);
        return success("操作成功");
    }


    @RequestMapping(value = {"/m/queryDefaultAddressByCustomerId"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户ID查询默认收货地址 */
    public Result<UserAddress> queryDefaultAddressByCustomerId(@RequestParam(value = "customerId") Long customerId) {
        if (null == customerId) {
            return failure("客户ID不能为空");
        }
        return success(userAddressService.queryDefaultAddressByCustomerId(customerId));
    }

    @RequestMapping(value = {"/getUserAddressByIdList","/api/open/getUserAddressByIdList"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据客户ID查询默认收货地址 */
    public Result getUserAddressByIdList(@RequestBody List<Long> addressIdList) {
        return success(userAddressService.queryAddressListByIdList(addressIdList));
    }
    /**
     * 根据小区id以及楼栋，门牌号
     */
    @RequestMapping(value = {"/queryByVillageIdAndBulidingAndHouserNumber"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /** * 根据小区id以及楼栋，门牌号 */
    public Result<List<Long>> queryByVillageIdAndBulidingAndHouserNumber(QueryByVillageIdAndBulidingAndHouserNumberDTO queryByVillageIdAndBulidingAndHouserNumberDTO) {

        return success(userAddressService.queryByVillageIdAndBulidingAndHouserNumber(queryByVillageIdAndBulidingAndHouserNumberDTO));
    }

    /**
     * 根据小区id以及楼栋，门牌号
     */
    @RequestMapping(value = {"/api/open/queryByVillageHouse"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Long>> queryByVillageHouse(QueryByVillageIdAndBulidingAndHouserNumberDTO queryByVillageIdAndBulidingAndHouserNumberDTO) {
        return success(userAddressService.queryByVillageHouse(queryByVillageIdAndBulidingAndHouserNumberDTO));
    }

}
