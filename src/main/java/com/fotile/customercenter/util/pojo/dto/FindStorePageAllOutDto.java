package com.fotile.customercenter.util.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindStorePageAllOutDto implements Serializable {

	@ApiModelProperty(value="门店id",example="门店id")
	private Long id;

	@ApiModelProperty(value="组织机构id",example="组织机构id")
	private Long orgId;

	@ApiModelProperty(value="关键字",example="关键字")
	private String keyWord;

	@ApiModelProperty(value="编码",example="编码")
	private String code;

	@ApiModelProperty(value="名称",example="名称")
	private String name;

	@ApiModelProperty(value="封面图片",example="封面图片")
	private String coverurl;

	@ApiModelProperty(value="详细地址",example="详细地址")
	private String address;

	@ApiModelProperty(value="地址-省",example="地址-省id")
	private Long provicenId;

	@ApiModelProperty(value="地址-市",example="地址-市id")
	private Long cityId;

	@ApiModelProperty(value="地址-区",example="地址-区id")
	private Long countyId;

	@ApiModelProperty(value="经度",example="经度")
	private String longitude;

	@ApiModelProperty(value="纬度",example="纬度")
	private String latitude;

	@ApiModelProperty(value="营业时间-开始时间",example="营业时间-开始时间")
	private String shoppingStart;

	@ApiModelProperty(value="营业时间-结束时间",example="营业时间-结束时间")
	private String shoppingEnd;

	@ApiModelProperty(value="电话",example="电话")
	private String tel;

	@ApiModelProperty(value="店长",example="店长id")
	private Integer leaderId;

	@ApiModelProperty(value="支付宝收款账号",example="支付宝收款账号")
	private String alipayno;

	@ApiModelProperty(value="支付宝收款账号二维码图片地址",example="支付宝收款账号二维码图片地址")
	private String alipaynourl;

	@ApiModelProperty(value="微信收款账号",example="微信收款账号")
	private String wechatno;

	@ApiModelProperty(value="微信收款账号二维码图片地址",example="微信收款账号二维码图片地址")
	private String wechatnourl;

	@ApiModelProperty(value="备注",example="备注")
	private String note;

	@ApiModelProperty(value="门店状态，0：禁用；1：启用",example="门店状态，0：禁用；1：启用")
	private Byte status;

	@ApiModelProperty(value="所属部门id",example="所属部门id")
	private Long parentId;

	@ApiModelProperty(value="部门列表中'上级部门'",example="部门列表中'上级部门'")
	private String fullPathName;

	@ApiModelProperty(value="部门列表中'上级部门'id",example="部门列表中'上级部门'id")
	private String fullPathId;

	@ApiModelProperty(value="详细地址2",example="详细地址2")
	private String address2;
}
