package com.fotile.customercenter.util;

import com.alibaba.fastjson.JSON;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class CreateShortUrl {

    /**
     * 短网址生成接口
    */
    public static void main(String[] args) throws UnsupportedEncodingException {
        RestTemplate restTemplate = new RestTemplate();
        //三维推api接口url地址
//        String apiUrl = "http://api.3wt.cn/api.htm?url={url}&format=json&key={key}&expireDate={expireDate}&domain={domain}";
        String apiUrl = "http://api.suolink.cn/api?url={url}&format=json&key={key}&expireDate={expireDate}&domain={domain}";

//        String aaa = "http://api.suolink.cn/api?format=json&url=urlencode('要缩短的网址')&key=64fc72e9294b5066933bc659fe@9ca761d0d963cba1537fc771c21a3ecd&expireDate=yyyy-MM-dd(或yyyy-MM-dd HH:mm:ss)&domain=独享域名"；


        Map<String, String> paraMap = new HashMap<String, String>();
        //要缩短的长网址
        paraMap.put("url", "www.baidu.com");
        //用户自己的key，扫码登录后可见
//        paraMap.put("key", "64a3dbb9d2a3f76cf14820b94c05@cc4b35f432587aa82bf3bd1ce8582111");
        paraMap.put("key", "64fc72e9294b5066933bc659fe@9ca761d0d963cba1537fc771c21a3ecd");
        //过期时间，若expireDate为空，默认有效期3个月。若expireDate>=2040-01-01,则永久有效
        paraMap.put("expireDate", "2040-01-01");
        //可选择域名。“0”代表b6i.cn；“1”代表nxw.so。若为空，默认为b6i.cn
        paraMap.put("domain", "nxw.so");

        Result1 result = restTemplate.getForObject(apiUrl, Result1.class, paraMap);
        System.out.println(JSON.toJSONString(result));
        if (result != null && "0".equals(result.getCode())) {
            System.out.println(result.getUrl());

        } else {
            //此处调用接口失败，请根据自己的业务逻辑进行容错处理
        }

    }

}

/***返回值类
 *  用于接收接口返回数据
 *  <AUTHOR>
 *  @date 2020-03-13
 */
class Result1 {
    //处理结果：‘0’代表成功，‘1’代表失败
    private String code;
    //生成的短网址，如果生成失败，则返回原链接
    private String url;
    //异常描述
    private String err;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getErr() {
        return err;
    }

    public void setErr(String err) {
        this.err = err;
    }
}
