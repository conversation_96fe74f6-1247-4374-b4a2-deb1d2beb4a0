package com.fotile.customercenter.inviteUserRecord.service;

import com.fotile.customercenter.client.OrgClient;
import com.fotile.customercenter.client.pojo.FindInviteUserAllPcInDto;
import com.fotile.customercenter.inviteUserRecord.dao.InviteUserRecordDao;
import com.fotile.customercenter.inviteUserRecord.pojo.dto.InviteUserRecordOutDto;
import com.fotile.customercenter.inviteUserRecord.pojo.entity.InviteUserRecord;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 邀请用户记录(InviteUserRecordOutDto)表服务实现类
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class InviteUserRecordService {
    @Autowired
    private InviteUserRecordDao inviteUserRecordDao;
    @Autowired
    private OrgClient orgClient;

    /**
     * 通过ID查询单条数据
     */
    public PageInfo<InviteUserRecord> queryBySalesmanId(FindInviteUserAllPcInDto inDto) {
        PageInfo<InviteUserRecord> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        inDto.setOffset(pageInfo.getOffset());
        Long count = inviteUserRecordDao.queryCount(inDto);
        List<InviteUserRecord> inviteUserRecords = new ArrayList<>();
        if (count > 0) {
            inviteUserRecords = inviteUserRecordDao.queryBySalesmanId(inDto);
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(inviteUserRecords);
        return pageInfo;
    }

    /**
     * 分页查询
     */
    public List<InviteUserRecordOutDto> queryInviteUserPage(FindInviteUserAllPcInDto inDto) {
        List<InviteUserRecordOutDto> inviteUserRecords = inviteUserRecordDao.queryAllByLimit(inDto);
        return inviteUserRecords;
    }


}
