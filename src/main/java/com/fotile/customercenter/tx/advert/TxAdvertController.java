package com.fotile.customercenter.tx.advert;

import com.alibaba.fastjson.JSON;
import com.fotile.customercenter.tx.advert.pojo.Action;
import com.fotile.customercenter.tx.advert.service.TxAdvertService;
import com.fotile.customercenter.util.LogUtils;
import com.fotile.customercenter.util.RedisUtils;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

/**
 * 腾讯广告接口
 * 41034	【品牌体系小程序】41034-方太官方小程序腾讯广告投放对接
 */
@RestController
@RequestMapping("/api/tx/advert")
@Slf4j
public class TxAdvertController extends BaseController {

    @Autowired
    private TxAdvertService txAdvertService;

    @Autowired
    private TxadConfig txadConfig;
    
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 生产token
     */
    @RequestMapping(value = "/api/open/getTxToken/{appId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getTxToken(String authorization_code, String state,@PathVariable("appId") String appId) {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();
        String txToken = null;
        try {
            txToken = txAdvertService.getTxToken(authorization_code, state,appId);
        }catch (Exception e){
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw, true));
            log.error("获取token异常："+ LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", sw);
            log.error("txToken："+ JSON.toJSONString(e));
            return failure("获取token失败："+e.getMessage());
        }
        return success("获取token成功");
    }

    @GetMapping(value = "/api/open/getTxToken2/{appId}",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getTxToken2(@PathVariable("appId") String appId) {
        return success(txAdvertService.getToken(appId));
    }
    
    @GetMapping(value = "/api/open/setRedis",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result setRedis(String key,String value) {
        redisUtils.setKey(key,value);
        return success();
    }

    /**
     * 获取clickId
     * 参数示例：<br/>
     *  [
     *     {
     *         "action_time": **********,
     *         "action_type": "COMPLETE_ORDER",
     *         "user_id": {
     *             "wechat_openid": "rqsdfqwrqdfqdqfrf",
     *             "wechat_app_id": "wx123451sfdqdq"
     *         },
     *         "trace": {
     *             "click_id": "wx1234455"
     *         },
     *         "action_param": {
     *             "value": 28
     *         }
     *     }
     * ]
     */
    @PostMapping(value = {"/api/open/userAction","/api/open/userAction/{accountId}"}, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getTxToken(@RequestBody List<Action> actions,@PathVariable(required = false,name = "accountId") String accountId) {
        if(StringUtils.isBlank(accountId)){
            accountId = "********";
        }
        return success(txAdvertService.userAction(actions,accountId));
    }
    
    
    
}
