package com.fotile.customercenter.operatorFollowCustomerLog.service;

import com.fotile.customercenter.client.UserClient;
import com.fotile.customercenter.customer.pojo.dto.user.UserEntityExtend;
import com.fotile.customercenter.operatorFollowCustomerLog.dao.OperatorFollowCustomerLogDAO;
import com.fotile.customercenter.operatorFollowCustomerLog.pojo.OperatorFollowCustomerLogMapper;
import com.fotile.customercenter.operatorFollowCustomerLog.pojo.dto.OperatorFollowCustomerLogInsertDTO;
import com.fotile.customercenter.operatorFollowCustomerLog.pojo.dto.OperatorFollowCustomerLogSelectDTO;
import com.fotile.customercenter.operatorFollowCustomerLog.pojo.dto.SelectByOperatorFollowCustomerLogOutDTO;
import com.fotile.customercenter.operatorFollowCustomerLog.pojo.entity.OperatorFollowCustomerLog;
import com.fotile.customercenter.picture.pojo.dto.FindPictureBySourceIdOutDto;
import com.fotile.customercenter.util.PictureUtils;
import com.fotile.customercenter.util.UserAuthorUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
//@DruidTxcMultiDataSourceAnnontation(value = "first")
public class OperatorFollowCustomerLogService {
    @Autowired
    private OperatorFollowCustomerLogDAO operatorFollowCustomerLogDAO;
    @Autowired
    private PictureUtils pictureUtils;
    @Autowired
    UserAuthorUtils userAuthorUtils;
    @Autowired
    UserClient userClient;

    /**
     * 保存一个实体，null的属性不会保存，会使用数据库默认值
     *
     * @param operatorFollowCustomerLogInsertDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int insertSelective(OperatorFollowCustomerLogInsertDTO operatorFollowCustomerLogInsertDTO) {
        OperatorFollowCustomerLog operatorFollowCustomerLog = OperatorFollowCustomerLogMapper.INSTANCE.OperatorFollowCustomerLogInsertDTOTOOperatorFollowCustomerLog(operatorFollowCustomerLogInsertDTO);
        operatorFollowCustomerLog.setIsDeleted(0L);
        operatorFollowCustomerLog.setRecordCreatedTime(new Date());


        operatorFollowCustomerLog.setRecordCreatedName(getName(operatorFollowCustomerLogInsertDTO.getSource(), operatorFollowCustomerLogInsertDTO.getCurrentlyLogonUserName(), operatorFollowCustomerLogInsertDTO.getCurrentlyLogonUserPhone()));
        int id = operatorFollowCustomerLogDAO.insert(operatorFollowCustomerLog);
        if (operatorFollowCustomerLogInsertDTO.getPictureList() != null && operatorFollowCustomerLogInsertDTO.getPictureList().size() > 0) {
            pictureUtils.insertDesingerPic(operatorFollowCustomerLogInsertDTO.getPictureList(), "operator_follow_customer_log", operatorFollowCustomerLog.getId());
        }
        return id;
    }


    /**
     *
     */
    public String getName(String source, String currentlyLogonUserName, String currentlyLogonUserPhone) {
        String name = "超级管理员";
        //判断登录人是否登录
        SecurityContext context = SecurityContextHolder.getContext();
        if(context.getAuthentication() == null){
            return name;
        }
        String userId = SecurityContextHolder.getContext().getAuthentication().getPrincipal().toString();
        UserEntityExtend userEntityExtend = null;
        if ("1".equals(source)) {
            if ("anonymousUser".equals(userId)) {
                name = "超级管理员";
            } else {
                userEntityExtend = userClient.findUserEntityExtendByUserId(userId).getData();
            }
        } else if ("2".equals(source)) {
            userEntityExtend = new UserEntityExtend();
            userEntityExtend.setFirstName(StringUtils.isEmpty(currentlyLogonUserName) ? currentlyLogonUserPhone : currentlyLogonUserName);
        }
        if (userEntityExtend != null) {
            name = StringUtils.isNotEmpty(userEntityExtend.getFirstName()) ? userEntityExtend.getFirstName() : userEntityExtend.getUserName();
        }
        // SimpleKeycloakAccount simpleKeycloakAccount = (SimpleKeycloakAccount) context.getAuthentication().getDetails();

        return name;
    }


    /**
     * 根据主键更新属性不为null的值
     *
     * @param operatorFollowCustomerLog
     * @return
     */
    public int updateByPrimaryKeySelective(OperatorFollowCustomerLog operatorFollowCustomerLog) {
        return operatorFollowCustomerLogDAO.updateById(operatorFollowCustomerLog);
    }

    /**
     * 根据主键查询一个示例，常用于查看
     *
     * @param id
     * @return
     */
    public OperatorFollowCustomerLog selectByPrimaryKey(Long id) {
        OperatorFollowCustomerLog operatorFollowCustomerLog = operatorFollowCustomerLogDAO.selectById(id);
        return operatorFollowCustomerLog;
    }

    /**
     * 分页查询
     *
     * @param operatorFollowCustomerLogSelectDTO
     * @return
     */
    public PageInfo<SelectByOperatorFollowCustomerLogOutDTO> selectByOperatorFollowCustomerLog(OperatorFollowCustomerLogSelectDTO operatorFollowCustomerLogSelectDTO) {
        //获取日志
        PageInfo<SelectByOperatorFollowCustomerLogOutDTO> pageInfo = new PageInfo<>(operatorFollowCustomerLogSelectDTO.getPageNum(), operatorFollowCustomerLogSelectDTO.getPageSize());
//        QueryWrapper example = new QueryWrapper();
        //  OperatorFollowCustomerLog operatorFollowCustomerLog = OperatorFollowCustomerLogMapper.INSTANCE.OperatorFollowCustomerLogTOOperatorFollowCustomerLogSelectDTO(operatorFollowCustomerLogSelectDTO);
        //example.in(operatorFollowCustomerLog);
//        Map map = BeanMap.create(operatorFollowCustomerLog);
//        example.allEq(MapUtils.map2Underline(map));
//        example.eq("is_deleted", "0");
//        example.orderByDesc("created_date");
        Integer count = operatorFollowCustomerLogDAO.selectCount1(operatorFollowCustomerLogSelectDTO);
        List<OperatorFollowCustomerLog> logList = new ArrayList<>();
        if (count > 0) {
            operatorFollowCustomerLogSelectDTO.setOffset(pageInfo.getOffset());
            logList = operatorFollowCustomerLogDAO.selectList1(operatorFollowCustomerLogSelectDTO);
        }

        if (logList == null || logList.size() <= 0) {
            return new PageInfo<SelectByOperatorFollowCustomerLogOutDTO>();
        }

        List<SelectByOperatorFollowCustomerLogOutDTO> selectByOperatorFollowMarketingLogOutDTOPageInfo = OperatorFollowCustomerLogMapper.INSTANCE.pageInfoSelectByOperatorFollowMarketingLogOutDTOTOPageInfoOperatorFollowOrgLog(logList);

        pageInfo.setTotal(count);
        pageInfo.setRecords(selectByOperatorFollowMarketingLogOutDTOPageInfo);
        //插入图片
        List<FindPictureBySourceIdOutDto> list = pictureUtils.getPictureListBysourceIds("operator_follow_customer_log", pageInfo.getRecords().stream().map(s -> s.getOperatorId()).collect(Collectors.toList()));

        //无图片可以插入
        if (list == null) {
            return pageInfo;
        }
        for (SelectByOperatorFollowCustomerLogOutDTO selectByOperatorFollowOrgLogOutDTO : pageInfo.getRecords()) {
            List<FindPictureBySourceIdOutDto> myPic = new ArrayList<>();
            for (FindPictureBySourceIdOutDto pictureMappingDTO : list) {
                //匹配图片
                if (selectByOperatorFollowOrgLogOutDTO.getOperatorId().equals(pictureMappingDTO.getSourceId())) {
                    FindPictureBySourceIdOutDto picture = new FindPictureBySourceIdOutDto();
                    picture.setCoverUrl(pictureMappingDTO.getCoverUrl());
                    picture.setName(pictureMappingDTO.getName());
                    picture.setId(pictureMappingDTO.getId());
                    myPic.add(picture);
                }
            }
            if (myPic != null && myPic.size() > 0) {
                selectByOperatorFollowOrgLogOutDTO.setPictureList(myPic);
            }
        }
        return pageInfo;
    }
}