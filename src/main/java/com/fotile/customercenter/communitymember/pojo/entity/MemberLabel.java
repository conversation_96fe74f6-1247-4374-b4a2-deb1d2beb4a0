package com.fotile.customercenter.communitymember.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;

@Data
@Entity
@ApiModel(value = "社区店会员标签", description = "社区店会员标签")
@TableName(value = "t_member_mark_mapping",schema = "customercenter")
public class MemberLabel extends AuditingEntity {
    @TableField(value = "remark")
    /** * 备注 */
    private String remark;

    @TableField(value = "company_id")
    /** * 分公司id */
    private Long companyId;

    @TableField(value = "company_name")
    /** * 分公司名称 */
    private String companyName;

    @TableField(value = "community_member_id")
    /** * 社区店会员id */
    private Long communityMemberId;

    @TableField(value = "label_ename")
    /** * 标签英文名称 */
    private String labelEname;

    @TableField(value = "label_cname")
    /** * 标签中文名 */
    private String labelCname;

    @TableField(value = "label_type")
    /** * 标签类型 */
    private Long labelType;

    @TableField(value = "label_value")
    /** * 标签值 */
    private String labelValue;

    @TableField(value = "dic_type_code")
    /** * 数据字典code */
    private String dicTypeCode;

    @TableField(value = "sort")
    /** * 顺序 */
    private Long sort;


    @TableField(value = "source_id")
    /** * 标签源表id */
    private Long sourceId;

    @TableField(value = "label_value_name")
    /** * 标签值展示值 */
    private String labelValueName;

}
