package com.fotile.customercenter.communitymember.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.math.BigDecimal;


/** app-会员信息 */
@Data
public class GetAppCommunityMemberOutDto {
    private Long id;//id
    private Long customerId;//会员id
    @FieldEncrypt
    private String noteName;//备注名称
    @FieldEncrypt
    private String nickname;//昵称
    @FieldEncrypt
    private String phone;//手机号
    private Long companyId;//分公司
    private String companyName;//分公司
    private Long communityStoreId;//所属社区店
    private String communityStoreName;//所属社区店
    private String provinceName;//省
    private Long provinceCode;//省code
    private String cityName;//市
    private Long cityCode;
    private String areaName;//区
    private Long areaCode;
    @FieldEncrypt
    private String address;//详细地址
    private Long villageId;//小区id
    private String villageName;//小区名称
    private Long chargeUserId;//业务员
    @FieldEncrypt
    private String chargeUserName;//业务员
    private BigDecimal maturityValue;//成熟度
    private Long gradeId;//会员等级id
    private String gradeName;//会员等级名称
    private String  createdDate;//绑定时间
}
