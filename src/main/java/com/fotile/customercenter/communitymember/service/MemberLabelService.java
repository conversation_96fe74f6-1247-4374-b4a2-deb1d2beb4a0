package com.fotile.customercenter.communitymember.service;

import com.fotile.customercenter.communitymember.dao.MemberLabelDao;
import com.fotile.customercenter.communitymember.pojo.dto.SaveOrUpdateMemberLabelDto;
import com.fotile.customercenter.communitymember.pojo.entity.MemberLabel;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;


/** 社区店会员标签 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
@Slf4j
public class MemberLabelService {
    @Autowired
    private MemberLabelDao memberLabelDao;


    public Integer saveMemberLabels(List<SaveOrUpdateMemberLabelDto> memberLabels) {
        return memberLabelDao.saveMemberLabels(memberLabels);
    }

    public Integer delMemberLabels(Long communityMemberId) {
        String modifiedBy = getCurrentUserId();
        return memberLabelDao.delMemberLabels(communityMemberId,modifiedBy);
    }

    public List<MemberLabel> findMemberLabels(Long communityMemberId) {
        return memberLabelDao.findMemberLabels(communityMemberId);
    }

    public List<MemberLabel> findMemberLabelByIds(List<Long> ids) {
        return memberLabelDao.findMemberLabelByIds(ids);
    }

    public int updateLabelValue(SaveOrUpdateMemberLabelDto memberLabel) {
        String modifiedBy = getCurrentUserId();
        memberLabel.setModifiedBy(modifiedBy);
        return memberLabelDao.updateLabelValue(memberLabel);
    }

    public MemberLabel findMemberLabel(Long communityMemberId, String labelEname, Long companyId) {
        return memberLabelDao.findMemberLabel(communityMemberId,labelEname,companyId);
    }


    private String getCurrentUserId() {
        String user = "anonymousUser";

        try {
            SecurityContext context = SecurityContextHolder.getContext();
            user = context.getAuthentication().getPrincipal().toString();
        } catch (Exception var3) {
            user = "anonymousUser";
        }

        return user;
    }
}
