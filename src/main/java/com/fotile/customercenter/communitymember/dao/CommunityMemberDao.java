package com.fotile.customercenter.communitymember.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.customercenter.communitymember.pojo.dto.*;
import com.fotile.customercenter.communitymember.pojo.entity.CommunityMemberInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 社区店会员信息 dao
 * wangzz
 * */

@Validated
public interface CommunityMemberDao extends BaseMapper<CommunityMemberInfo> {

    /** 社区店会员列表-查询count */
    Long selectCountCommunityMember(GetCommunityMemberListInDto communityMemberListInDto);

    /** 社区店会员列表-查询 */
    List<GetCommunityMemberListOutDto> findCommunityMemberList(GetCommunityMemberListInDto communityMemberListInDto);


    /** 只查询社区会员信息 */
    CommunityMemberInfoDto findCommunityMemberById(@Param("id")Long id);

    /** 关联顾客表查询所有顾客信息 */
    GetCommunityMemberByIdOutDto findAllCommunityMemberById(@Param("id")Long id);

    GetCommunityMemberByIdOutDto findCompanyMemberById(@Param("id")Long id);

    Integer updateCommunityMember(CommunityMemberInfo communityMemberInfo);

    Integer saveCommunityMember(CommunityMemberInfo communityMemberInfo);

    /** APP-会员-查询count */
    Long selectCountAppCommunityMember(FindAppCommunityMemberListInDto appCommunityMemberInList);

    /** APP-会员-查询 */
    List<FindAppCommunityMemberListOutDto> findAppCommunityMemberList(@Param("chargeUserId")Long chargeUserId,
                                                                      @Param("orderBy")String orderBy,
                                                                      @Param("labels")List<MemberLabelsInDto> labels,
                                                                      @Param("communityStoreId")Long communityStoreId,
                                                                      @Param("offset")Long offset,
                                                                      @Param("size")Integer size );

    /** APP-会员-查询会员信息 */
    GetAppCommunityMemberOutDto findAppCommunityMemberById(@Param("id") Long id);


    /** APP-会员详情-修改信息 */
    Integer updateAppCommunityMember(UpdateAppCommunityMember communityMemberDto);

    /** 社区店会员去重查询 */
    GetCommunityMemberOutDto findCommunityMemberByCustomerId(@Param("customerId") Long customerId, @Param("companyId") Long companyId, @Param("communityStoreId") Long communityStoreId);

    Integer saveAppCommunityMember(AddAppCommunityMemberInDto communityMemberInDto);

    /** APP-会员-查询社区店下的会员数 */
    List<FindAppCommunityMemberListOutDto> getAppCommunityMemberCounts(@Param("communityStoreId") Long communityStoreId);

    /**APP-会员详情-修改推荐业务员*/
    Integer updateAppCommunityMemberCharge(UpdateAppCommunityMemberChargeDto communityMemberDto);

    /**小程序-幸福社区-查询会员社区店*/
    List<FindAppCommunityMemberOutDto> findAppCommunityMember(@Param("customerId") Long customerId);

    /**小程序-幸福社区-更新会员社区店最新时间，用于排序*/
    Integer updateAppCommunityMemberTime(@Param("communityMemberId") Long communityMemberId);

    List<Long> getCommunityMemberListByChargeUserId(@Param("chargeUserId")Long chargeUserId);

    List<CommunityChargeUserOutDto> getChargeUserInfoListByIds(@Param("ids")List<Long> ids);

    /** 分页查询会员详情导出count */
    Long selectCountCommunityMemberById(GetCommunityMemberByIdListInDto communityMemberListInDto);

    /** 分页查询会员详情导出 */
    List<GetCommunityMemberByIdListOutDto> findCommunityMemberByIdList(GetCommunityMemberByIdListInDto communityMemberListInDto);

    /** 根据ids查询社区会员信息（只查询本表） */
    List<FindCommunityMemberOutDto> findCommunityMemberByIds(@Param("ids") List<Long> ids);

    AddAppCommunityMemberInDto findCommunityMemberByCustomerId2(@Param("customerId") Long customerId, @Param("companyId") Long companyId, @Param("communityStoreId") Long communityStoreId);

    //更新推荐会员
    Integer updateRecommendCustomer(AddAppCommunityMemberInDto communityMemberIn);

    //会员查询下拉框count
    Long findCommunityMembersCount(FindCommunityMembersInDto communityMembersInDto);

    //会员查询下拉框
    List<FindCommunityMembersOutDto> findCommunityMembers(FindCommunityMembersInDto communityMembersInDto);

    int updateShowFlagById(GetCommunityMemberOutDto communityMemberByCustomerId);

    int updateShowFlagById2(AddAppCommunityMemberInDto communityMemberIn);

    List<AddAppCommunityMemberInDto> findCommunityMemberListForDrainage(@Param("customerId") Long customerId, @Param("companyId") Long companyId);

    List<AddAppCommunityMemberInDto> findCompanyMemberListForDrainage(@Param("customerId") Long customerId, @Param("companyId") Long companyId);

    //查询业务员下关联会员数
    Long findCommunityMemberCount(FindCommunityMemberCountInDto communityMemberCountInDto);

    Integer deleteByCharge(UpdateCommunityMemberByChargeUserDto byChargeUserDto);

    Integer deleteByRemoveCharge(@Param("ids") List<Long> ids, @Param("reviseName") String reviseName);

    List<FindCommunityMemberOutDto> findCommunityMemberByCharge(@Param("chargeUserId") Long chargeUserId);
    List<FindCommunityMemberOutDto> findCommunityMemberByStoreId(@Param("storeId") Long storeId);

    List<FindCommunityMemberOutDto> findCommunityMemberByCharge2(@Param("customerIds") List<Long> customerIds,
                                                                 @Param("storeId") Long storeId,
                                                                 @Param("companyId")Long companyId);

    Integer updateBatchCommunityMember(@Param("list") List<CommunityMemberInfo> communityMemberInfos);
    Integer insetBatchCommunityMember(@Param("list") List<CommunityMemberInfo> communityMemberInfos);

//    根据手机号等查询社区店会员列表
    List<GetCommunityMemberListOutDto> findCommunityMemberListByPhones(FindCommunityMemberByPhonesInDto inDto);

    void updateChargeUserInfo(AddAppCommunityMemberInDto communityMemberIn);
}
