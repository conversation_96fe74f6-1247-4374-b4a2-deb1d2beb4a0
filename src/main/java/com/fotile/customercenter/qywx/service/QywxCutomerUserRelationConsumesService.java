package com.fotile.customercenter.qywx.service;

import com.fotile.customercenter.client.DataClient;
import com.fotile.customercenter.client.pojo.ExportTaskRecord;
import com.fotile.customercenter.communitymember.mq.CommunityMemberExportConsumesChannel;
import com.fotile.customercenter.communitymember.mq.CommunityMemberExportProducesChannel;
import com.fotile.customercenter.communitymember.service.mq.CommunityMemberExportContext;
import com.fotile.customercenter.qywx.mq.QywxTagConfigConsumesChannel;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class QywxCutomerUserRelationConsumesService {

    @Autowired
    private DataClient dataClient;
    @Autowired
    private CommunityMemberExportContext memberExportContext;


    /**接收异步导出请求，查询导出*/
//    @StreamListener(CommunityMemberExportConsumesChannel.COMMUNITY_MEMBER_EXPORT_CONSUMES_INPUT)
//    public void exportUserRelation(Message<ExportTaskRecord> message) {
//        ExportTaskRecord exportTaskRecord = message.getPayload();
//        if(exportTaskRecord == null || exportTaskRecord.getId() == null){
//            return;
//        }
//        Integer result = dataClient.startTask(exportTaskRecord).getData();
//        if(result != 1){
//            //不等于1，则说明任务状态不是未进行，直接return
//            return;
//        }
//        //调用查询任务接口，判断状态是否可以进行下载
//        try {
//            //根据类型调用不同的方法生产不同的数据
//            memberExportContext.getOrderExportImpl(exportTaskRecord.getType()).exportFile(exportTaskRecord);
//        } catch (Exception e) {
//            log.error("错误数据" + exportTaskRecord);
//            log.error("报错信息" + e.getMessage());
//            ExportTaskRecord exportTask = new ExportTaskRecord();
//            exportTask.setId(exportTaskRecord.getId());
//            exportTask.setFailReason(e.getMessage());
//            dataClient.failureTask(exportTask);
//        }
//        return;
//    }



}
