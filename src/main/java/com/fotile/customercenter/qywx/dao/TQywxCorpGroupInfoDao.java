package com.fotile.customercenter.qywx.dao;

import com.fotile.customercenter.qywx.pojo.entity.TQywxCorpGroupInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 企业微信企业客户标签组表(TQywxCorpGroupInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-02 13:53:58
 */
public interface TQywxCorpGroupInfoDao {

    /**
     * 通过ID查询单条数据
     */
    TQywxCorpGroupInfo queryById(Long id);

    /**
     * 查询指定行数据
     */
    List<TQywxCorpGroupInfo> queryAllByLimit(TQywxCorpGroupInfo tQywxCorpGroupInfo, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     */
    long count(TQywxCorpGroupInfo tQywxCorpGroupInfo);

    /**
     * 新增数据
     */
    int insert(TQywxCorpGroupInfo tQywxCorpGroupInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     */
    int insertBatch(@Param("entities") List<TQywxCorpGroupInfo> entities);

    /**
     * 修改数据
     */
    int update(TQywxCorpGroupInfo tQywxCorpGroupInfo);

    /**
     * 通过主键删除数据
     */
    int deleteById(Long id);

    /**
     * 查询对应账户下的标签组
     */
    List<TQywxCorpGroupInfo> queryByAccountId(@Param("accountId") String accountId);

    void deleteAll(@Param("accountId") String accountId);

}

