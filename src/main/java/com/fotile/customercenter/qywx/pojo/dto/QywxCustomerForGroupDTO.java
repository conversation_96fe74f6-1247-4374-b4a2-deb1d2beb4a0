package com.fotile.customercenter.qywx.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class QywxCustomerForGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;


	/** *  */
	private Long id;

	/** * 是否删除：0：否；其它：是 */
	private Integer isDeleted;

	/** *  */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date createdDate;

	/** *  */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date modifiedDate;

	/** * 账户Id */
	private String accountId;

	/** * 外部联系人的userid */
	private String externalUserid;

	/** * 外部联系人的名称 */
	@FieldEncrypt
	private String name;

	/** * 头像url */
	private String avatar;

	/** * 类型:1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户 */
	private Integer type;

	/** * 性别:0表示未定义，1表示男性，2表示女性 */
	private Integer gender;

	/** * 唯一身份标识 */
	private String unionId;

	/** * 职务信息 */
	private String position;

	/** * 企业的简称 */
	private String corpName;

	/** * 主体名称 */
	private String corpFullName;

	/** * 自定义展示信息 */
	private String externalProfile;

	/** * 成员userid */
	private String userId;

	/** * 备注 */
	private String remark;

	/** * 描述 */
	private String description;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** * 创建时间 */
	private Date createTime;

	/** * 标签名称 */
	private String tagNames;

	/** * 标签名称 */
	private String remarkCorpName;

	/** * 备注的手机号码 */
	private String remarkMobiles;

	/** * 客户的来源 */
	private Integer addWay;

	/** * 发起添加的userid */
	private String openUserid;

	/** * 企业自定义的state参数 */
	private String state;

	/** * 分公司id */
	private Long companyId;

	/** * 分公司名称 */
	private String companyName;

	/** * 门店orgid */
	private Long storeOrgId;

	/** * 门店code */
	private String storeCode;

	/** * 门店name */
	private String storeName;

	/** * 门店简称 */
	private String abbreviation;

	/** * 分配业务员id */
	private Long chargeUserId;

	/** * 分配业务员名称 */
	@FieldEncrypt
	private String chargeUserName;

	/** * 分配业务员code */
	private String chargeCode;

	/** * 分配业务员手机号 */
	private String chargePhone;

	private String mobiles;

	private String qywxId;

	private Long provinceId;

	private String provinceName;

	private Long cityId;

	private String cityName;

	private Long countyId;

	private String countyName;

	private Long villageId;

	private String villageName;
	@FieldEncrypt
	private String address;
}