package com.fotile.customercenter.qywx.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class ExportUserRelationListOutDto implements Serializable {

    @ColumnWidth(15)
    @ExcelProperty(value = {"external_userid"})
    @ApiModelProperty("external_userid")
    private String externalUserid;

    @ColumnWidth(15)
    @ExcelProperty(value = {"unionid"})
    @ApiModelProperty("unionid")
    private String unionId;

    @ColumnWidth(15)
    @ExcelProperty(value = {"联系人类型"})
    @ApiModelProperty("联系人类型")
    private String typeValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"性别"})
    @ApiModelProperty("性别")
    private String genderValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"微信昵称"})
    @ApiModelProperty("微信昵称")
    private String name;

    @ColumnWidth(15)
    @ExcelProperty(value = {"小程序授权手机号"})
    @ApiModelProperty("小程序授权手机号")
    private String mobiles;
    @ColumnWidth(15)
    @ExcelProperty(value = {"小区名称"})
    @ApiModelProperty("小区名称")
    private String villageName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"详细地址"})
    @ApiModelProperty("详细地址")
    private String address;
    @ColumnWidth(15)
    @ExcelProperty(value = {"企业成员userid"})
    @ApiModelProperty("企业成员userid")
    private String userId;

    @ColumnWidth(15)
    @ExcelProperty(value = {"业务员手机号"})
    @ApiModelProperty("业务员手机号")
    private String chargePhone;

    @ColumnWidth(15)
    @ExcelProperty(value = {"业务员"})
    @ApiModelProperty("业务员")
    private String chargeUser;
    @ColumnWidth(15)
    @ExcelProperty(value = {"业务员岗位"})
    @ApiModelProperty("业务员")
    private String stationName;
    @ColumnWidth(5)
    @ExcelProperty(value = {"业务员状态"})
    @ApiModelProperty("业务员状态")
    private String chargeStatus;
    @ColumnWidth(15)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty("大区")
    private String areaName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty("分公司")
    private String companyName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"客户名称"})
    @ApiModelProperty("客户名称")
    private String distributorName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"客户渠道大类"})
    @ApiModelProperty("客户渠道大类")
    private String channelCategoryName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"客户渠道细分"})
    @ApiModelProperty("客户渠道细分")
    private String channelSubdivideName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty("门店渠道")
    private String storeChannelName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty("门店渠道细分")
    private String storeSubChannelName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})
    @ApiModelProperty("门店编码")
    private String storeCode;
    @ColumnWidth(15)
    @ExcelProperty(value = {"所属门店"})
    @ApiModelProperty("所属门店")
    private String storeKey;

    @ColumnWidth(15)
    @ExcelProperty(value = {"备注名"})
    @ApiModelProperty("备注名")
    private String remark;

    @ColumnWidth(15)
    @ExcelProperty(value = {"加入时间"})
    @ApiModelProperty("加入时间")
    private String createTimeValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"标签"})
    @ApiModelProperty("标签")
    private String tagNames;

    @ColumnWidth(15)
    @ExcelProperty(value = {"备注企业名称"})
    @ApiModelProperty("备注企业名称")
    private String remarkCorpName;

    @ColumnWidth(15)
    @ExcelProperty(value = {"备注手机号"})
    @ApiModelProperty("备注手机号")
    private String remarkMobiles;

    @ColumnWidth(15)
    @ExcelProperty(value = {"好友关系状态"})
    @ApiModelProperty("好友关系状态")
    private String friendStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = {"好友来源"})
    @ApiModelProperty("好友来源")
    private String addWayValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"名片ID"})
    @ApiModelProperty("名片ID")
    private String state;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否入群"})
    @ApiModelProperty("是否入群")
    private String isJoinGroupValue;

    @ColumnWidth(15)
    @ExcelProperty(value = {"是否有手机号"})
    @ApiModelProperty("有手机号")
    private String isPhoneValue;
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否关联线索"})
    @ApiModelProperty("是否关联线索")
    private String isRelationValue;
    @ColumnWidth(15)
    @ExcelProperty(value = {"线索是否成交"})
    @ApiModelProperty("线索是否成交")
    private String isCluesDealValue;
    @ColumnWidth(15)
    @ExcelProperty(value = {"线索id"})
    @ApiModelProperty("线索id")
    private String cluesId;
    @ColumnWidth(15)
    @ExcelProperty(value = {"线索名称"})
    @ApiModelProperty("线索名称")
    private String cluesName;
    @ColumnWidth(15)
    @ExcelProperty(value = {"线索联系方式"})
    @ApiModelProperty("线索联系方式")
    private String cluesPhone;

    @ColumnWidth(15)
    @ExcelProperty(value = {"最新更新时间"})
    @ApiModelProperty("最新更新时间")
    private String modifiedDateValue;
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否建过用户服务卡"})
    @ApiModelProperty("是否建过用户服务卡")
    private String createDscCardValue;
    @ColumnWidth(15)
    @ExcelProperty(value = {"是否推送用户服务卡"})
    @ApiModelProperty("是否推送用户服务卡")
    private String sendDscCardValue;

}

