package com.fotile.customercenter.qywx.pojo.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 企业微信企业客户标签表(TQywxCorpTagInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-11-02 13:54:00
 */
@Data
public class TQywxCorpTagInfo implements Serializable {
    private static final long serialVersionUID = -88295427747994297L;
    
    private Long id;
    /**
     * 是否删除：0：否；其它：是
     */
    private Integer isDeleted;
    
    private String createdBy;
    
    private Date createdDate;
    
    private String modifiedBy;
    
    private Date modifiedDate;
    /**
     * 账户Id
     */
    private String accountId;
    /**
     * 微信标签组id
     */
    private String wxGroupId;
    /**
     * 微信标签组名称
     */
    private String wxGroupName;
    /**
     * 微信标签id
     */
    private String wxTagId;
    /**
     * 微信标签名称
     */
    private String wxTagName;
    /**
     * 微信标签创建时间
     */
    private Date wxTagCreateTime;
    /**
     * 排序
     */
    private Long sort;


}

