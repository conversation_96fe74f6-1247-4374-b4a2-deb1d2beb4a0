package com.fotile.customercenter.qywx.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QywxDepartmentInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	
	/** *  */
	private Long id;
	
	/** * 是否删除：0：否；其它：是 */
	private Integer isDeleted;
	
	/** *  */
	private String createdBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date createdDate;
	
	/** *  */
	private String modifiedBy;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	/** *  */
	private Date modifiedDate;
	
	/** * 账户Id */
	private String accountId;
	
	/** * 部门id */
	private Long departmentId;
	
	/** * 部门名称 */
	private String name;
	
	/** * 英文名称 */
	private String nameEn;
	
	/** * 父部门id */
	private Long parentId;
	
	/** * 在父部门中的次序值 */
	private Long order;


}