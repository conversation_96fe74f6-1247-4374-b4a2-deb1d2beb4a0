package com.fotile.customercenter.qywx.mq;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component
public interface QywxTagConfigProducesChannel {

    /**
     * 处理发送【发送通道】
     */
    String QYWX_TAG_PRODUCES_OUTPUT = "qywx_tag_produces_output";

    String QYWX_CUSTOMER_USER_EXPORT_OUTPUT = "qywx_customer_user_export_output";

    @Output(QYWX_TAG_PRODUCES_OUTPUT)
    MessageChannel sendMemberExportProduces();

    @Output(QYWX_CUSTOMER_USER_EXPORT_OUTPUT)
    MessageChannel sendUserExportProduces();

}
