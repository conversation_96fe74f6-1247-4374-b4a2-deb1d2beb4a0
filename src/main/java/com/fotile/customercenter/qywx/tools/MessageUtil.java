package com.fotile.customercenter.qywx.tools;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class MessageUtil {
    /**
     * 返回消息类型：文本.
     */
    public static final String RESP_MESSAGE_TYPE_TEXT = "text";

    /**
     * 返回消息类型：音乐.
     */
    public static final String RESP_MESSAGE_TYPE_MUSIC = "music";

    /**
     * 返回消息类型：图文.
     */
    public static final String RESP_MESSAGE_TYPE_NEWS = "news";

    /**
     * 请求消息类型：文本.
     */
    public static final String REQ_MESSAGE_TYPE_TEXT = "text";

    /**
     * 请求消息类型：图片.
     */
    public static final String REQ_MESSAGE_TYPE_IMAGE = "image";

    /**
     * 请求消息类型：链接.
     */
    public static final String REQ_MESSAGE_TYPE_LINK = "link";

    /**
     * 请求消息类型：地理位置.
     */
    public static final String REQ_MESSAGE_TYPE_LOCATION = "location";

    /**
     * 请求消息类型：音频.
     */
    public static final String REQ_MESSAGE_TYPE_VOICE = "voice";

    /**
     * 请求消息类型：推送.
     */
    public static final String REQ_MESSAGE_TYPE_EVENT = "event";

    /**
     * 事件类型：subscribe(订阅).
     */
    public static final String EVENT_TYPE_SUBSCRIBE = "subscribe";

    /**
     * 事件类型：unsubscribe(取消订阅).
     */
    public static final String EVENT_TYPE_UNSUBSCRIBE = "unsubscribe";

    /**
     * 事件类型：CLICK(自定义菜单点击事件).
     */
    public static final String EVENT_TYPE_CLICK = "CLICK";

    /**
     * 事件类型：taskcard_click(点击任务卡片按钮).
     */
    public static final String EVENT_TYPE_TASKCARD_CLICK = "taskcard_click";

    /**
     * 事件类型：open_approval_change(审批状态通知事件).
     */
    public static final String EVENT_TYPE_OPEN_APPROVAL_CHANGE = "open_approval_change";

    public static final String EVENT_TYPE_ENTER_AGENT = "enter_agent";

    /**
     * 解析微信发来的请求（XML）.
     *
     * @param msg 消息
     * @return map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, String> parseXml(final String msg) {
        // 将解析结果存储在HashMap中
        Map<String, String> map = new HashMap<String, String>();

        // 从request中取得输入流
        try (InputStream inputStream = new ByteArrayInputStream(msg.getBytes(StandardCharsets.UTF_8.name()))) {
            // 读取输入流
            SAXReader reader = new SAXReader();
            Document document = reader.read(inputStream);
            // 得到xml根元素
            Element root = document.getRootElement();
            // 得到根元素的所有子节点
            List<Element> elementList = root.elements();

            // 遍历所有子节点
            for (Element e : elementList) {
                map.put(e.getName(), e.getText());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return map;
    }





    public static Map<String, Object> parseXml2Obj(final String msg) {
        Map<String, Object> resultMap = new LinkedHashMap<>();  // 保持插入顺序

        try (InputStream inputStream = new ByteArrayInputStream(msg.getBytes(StandardCharsets.UTF_8.name()))) {
            SAXReader reader = new SAXReader();
            Document document = reader.read(inputStream);
            Element root = document.getRootElement();

            // 递归解析所有节点
            parseElement(root, resultMap);

        } catch (Exception e) {
            throw new RuntimeException("XML解析失败", e);
        }

        return resultMap;
    }

    private static void parseElement(Element element, Map<String, Object> parentMap) {
        String elementName = element.getName();

        // 处理特殊嵌套结构
        if ("MemChangeList".equals(elementName)) {
            List<String> memberList = new ArrayList<>();
            for (Element item : element.elements("Item")) {
                memberList.add(item.getTextTrim());
            }
            parentMap.put(elementName, memberList);
        }
        // 可扩展其他特殊节点（如多级嵌套）
        else if (element.elements().isEmpty()) {
            // 叶子节点直接取值
            parentMap.put(elementName, element.getTextTrim());
        } else {
            // 处理普通嵌套节点
            Map<String, Object> childMap = new LinkedHashMap<>();
            for (Element child : element.elements()) {
                parseElement(child, childMap);
            }
            parentMap.put(elementName, childMap);
        }
    }




//    public static void main(String[] args) {
//        String msg = "<xml>\n" +
//                "\t<ToUserName><![CDATA[ww55ca070cb9b7eb22]]></ToUserName>\n" +
//                "\t<FromUserName><![CDATA[sys]]></FromUserName>\n" +
//                "\t<CreateTime>1403610513</CreateTime>\n" +
//                "\t<MsgType><![CDATA[event]]></MsgType>\n" +
//                "\t<Event><![CDATA[change_external_chat]]></Event>\n" +
//                "\t<ChatId><![CDATA[wrx7HUARsKwGRaQBVKPBTcEyzdHA4HrQ]]></ChatId>\n" +
//                "\t<ChangeType><![CDATA[update]]></ChangeType>\n" +
//                "\t<UpdateDetail><![CDATA[add_member]]></UpdateDetail>\n" +
//                "\t<JoinScene>1</JoinScene>\n" +
//                "\t<QuitScene>0</QuitScene>\n" +
//                "\t<MemChangeCnt>10</MemChangeCnt>\n" +
//                "\t<MemChangeList>\n" +
//                "\t\t<Item>Jack</Item>\n" +
//                "\t</MemChangeList>\n" +
//                "\t<LastMemVer>9c3f97c2ada667dfb5f6d03308d963e1</LastMemVer>\n" +
//                "\t<CurMemVer>71217227bbd112ecfe3a49c482195cb4</CurMemVer>\n" +
//                "</xml>";
//        Map<String, Object> parseXml = parseXml2Obj(msg);
//        JSONObject obj = (JSONObject) JSONObject.toJSON(parseXml.get("xml"));
//        JSONArray memChangeList = (JSONArray) obj.get("MemChangeList");
//        System.out.println(obj);
//        System.out.println(obj.get("ChatId"));
//        System.out.println(obj.get("MemChangeList"));
//        System.out.println(memChangeList);
//        System.out.println(parseXml);
//    }
}
