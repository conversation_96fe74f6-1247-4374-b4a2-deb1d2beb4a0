package com.fotile.customercenter.qywx.job;

import com.fotile.customercenter.fission.service.DrawLotteryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class DrawLotteryJob extends IJobHandler {

    private final DrawLotteryService drawLotteryService;

    /**
     * 定时每天发送签到提醒
     * @param s
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob(value = "DrawLotteryJob")
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("=============活动结束抽大奖和幸运奖开始=============");
        drawLotteryService.sendBigPrizeAndLuckyPrize();
        XxlJobLogger.log("=============活动结束抽大奖和幸运奖结束=============");
        return ReturnT.SUCCESS;
    }
}
