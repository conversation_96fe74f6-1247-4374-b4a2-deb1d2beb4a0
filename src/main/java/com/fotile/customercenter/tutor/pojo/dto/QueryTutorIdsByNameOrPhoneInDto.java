package com.fotile.customercenter.tutor.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryTutorIdsByNameOrPhoneInDto implements Serializable {

    @TableField(value = "nickname")
    /** * 昵称 */
    private String nickName;

    @TableField(value = "phone")
    /** * 手机号 */
    private String phone;
}
