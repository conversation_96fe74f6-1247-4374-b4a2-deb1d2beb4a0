package com.fotile.toolscenter.configurationuserinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.toolscenter.configurationuserinfo.pojo.dto.GetMyRecordListRequestTO;
import com.fotile.toolscenter.configurationuserinfo.pojo.dto.GetMyRecordListVO;
import com.fotile.toolscenter.configurationuserinfo.pojo.entity.DtToolConfigurationUserinfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DtToolConfigurationUserinfoDao extends BaseMapper<DtToolConfigurationUserinfo> {
    int deleteByPrimaryKey(String id);

    int insert(DtToolConfigurationUserinfo record);

    int insertSelective(DtToolConfigurationUserinfo record);

    DtToolConfigurationUserinfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(DtToolConfigurationUserinfo record);

    int updateByPrimaryKey(DtToolConfigurationUserinfo record);

    /**
     *我的记录列表取得
     * @param page
     * @param param
     * @return
     */
    IPage<List<GetMyRecordListVO>> getMyRecordList(Page<Map<String, Object>> page, @Param("requestTO") GetMyRecordListRequestTO requestTO);
}