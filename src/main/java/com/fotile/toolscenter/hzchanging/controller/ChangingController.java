package com.fotile.toolscenter.hzchanging.controller;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.toolscenter.hzchanging.pojo.dto.AddOrEditChangingRequestTO;
import com.fotile.toolscenter.hzchanging.pojo.dto.GetChangingDetailRequestTO;
import com.fotile.toolscenter.hzchanging.pojo.dto.GetChangingListRequestTO;
import com.fotile.toolscenter.hzchanging.pojo.dto.SetChangingSortRequestTO;
import com.fotile.toolscenter.hzchanging.service.ChangingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 换装管理
 *
 * <AUTHOR>
 * @create 2021/02/02 11:50
 */
@Slf4j
@RestController
//@CrossOrigin
@RequestMapping("/api/changing")
public class ChangingController extends BaseController {

    @Autowired
    private ChangingService changingService;


    /**
     * 获取换装列表
     */
    @RequestMapping(value = "/getChangingList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getChangingList(@Valid GetChangingListRequestTO requestTO) {
        try {
            return success(changingService.getChangingList(requestTO));
        } catch (Exception e) {
//            e.printStackTrace();
            return failure(e.getMessage());
        }
    }


    /**
     * 设定排序
     */
    @RequestMapping(value = "/setChangingSort", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result setChangingSort(@Valid @RequestBody SetChangingSortRequestTO requestTO) {
        try {
            return success(changingService.setChangingSort(requestTO));
        } catch (Exception e) {
            return failure(e.getMessage());
        }
    }

    /**
     * 新建和编辑换装接口
     */
    @RequestMapping(value = "/addOrEditChanging", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result addOrEditChanging(@Valid @RequestBody AddOrEditChangingRequestTO requestTO) {
        try {
            List<Map<String, String>> sectionList = requestTO.getSectionList();
            if(null != sectionList){
                for (int i = 0; i <sectionList.size() ; i++) {
                    Map<String, String> section = sectionList.get(i);
                    if(null == section.get("style") || section.get("style").equals("")){
                        return failure("样式不能为空");
                    }
                    if(section.get("style").equals("1")){
                        if(null == section.get("imageUrl") || section.get("imageUrl").equals("")){
                            return failure("图片不能为空");
                        }
                    }else if(section.get("style").equals("2")){
                        if(null == section.get("buttonName") || section.get("buttonName").equals("")){
                            return failure("按钮名称不能为空");
                        }
                    }else if(section.get("style").equals("3")){
                        if(null == section.get("carouselPic") || StringUtils.isBlank(section.get("carouselPic"))){
                            return failure("轮播图不能为空");
                        }
                    }
                    if(null == section.get("sort") || section.get("sort").equals("")){
                        return failure("排序不能为空");
                    }
                    if(null == section.get("status") || section.get("status").equals("")){
                        return failure("状态不能为空");
                    }
                }
            }

            return success(changingService.addOrEditChanging(requestTO));
        } catch (Exception e) {
//            e.printStackTrace();
            return failure(e.getMessage());
        }
    }


    /**
     * 获取换装详情接口
     */
    @RequestMapping(value = "/getChangingDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getChangingDetail(@Valid GetChangingDetailRequestTO requestTO) {
        try {
            Map<String,Object> changingDetail = changingService.getChangingDetail(requestTO);
            return success(changingDetail);
        } catch (Exception e) {
            return failure(e.getMessage());
        }
    }

    /**
     * [open]获取换装详情接口
     */
    @RequestMapping(value = "/api/open/getChangingDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result getChangingDetailOpen(@Valid GetChangingDetailRequestTO requestTO) {
        try {
            Map<String,Object> changingDetail = changingService.getChangingDetail(requestTO);
            return success(changingDetail);
        } catch (Exception e) {
            return failure(e.getMessage());
        }
    }

}
