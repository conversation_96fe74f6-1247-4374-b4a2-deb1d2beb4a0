package com.fotile.toolscenter.tconfig.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.toolscenter.tconfig.pojo.entity.TConfig;

/**
 * @Entity generator.domain.TConfig
 */
public interface TConfigMapper extends BaseMapper<TConfig> {

    int deleteByPrimaryKey(Long id);

    int insert(TConfig record);

    int insertSelective(TConfig record);

    TConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TConfig record);

    int updateByPrimaryKey(TConfig record);

}




