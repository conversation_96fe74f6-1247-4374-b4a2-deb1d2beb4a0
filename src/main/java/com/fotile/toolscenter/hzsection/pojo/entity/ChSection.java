package com.fotile.toolscenter.hzsection.pojo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * ch_section
 * <AUTHOR>
@Data
public class ChSection implements Serializable {
    /**
     * Id
     */
    private String id;

    /**
     * 换装ID
     */
    private String changingId;

    /**
     * 样式1单图，2留资表单
     */
    private String style;

    /**
     * 板块名称
     */
    private String plateName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态1启用，2禁用
     */
    private String status;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 背景色
     */
    private String backgroundColor;

    /**
     * 按钮名称
     */
    private String buttonName;

    /**
     * 轮播图
     */
    private String carouselPic;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区ID
     */
    private Long areaId;

    /**
     * 区名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 显示地址
     */
    private String showAddress;

    /**
     * 描述
     */
    private String describe;

    /**
     * 创建者名称
     */
    @FieldEncrypt
    private String creatorName;

    /**
     * 最后更新者名称
     */
    @FieldEncrypt
    private String modifierName;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后更新时间
     */
    private Date mtime;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 删除标识1删除,2不删除
     */
    private String isDel;

    /**
     * 预留字段2
     */
    private String reserveItem1;

    /**
     * 预留字段2
     */
    private String reserveItem2;

    /**
     * 预留字段3
     */
    private String reserveItem3;

    /**
     * 预留字段4 
     */
    private String reserveItem4;

    /**
     * 预留字段5
     */
    private String reserveItem5;

    /**
     * 预留字段6
     */
    private String reserveItem6;

    /**
     * 预留字段7
     */
    private String reserveItem7;

    /**
     * 预留字段8
     */
    private String reserveItem8;

    /**
     * 预留字段9
     */
    private String reserveItem9;

    /**
     * 预留字段10
     */
    private String reserveItem10;

    /**
     * 开始时间（小时）
     */
    private String startHour;

    /**
     * 结束时间（小时）
     */
    private String endHour;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ChSection other = (ChSection) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getChangingId() == null ? other.getChangingId() == null : this.getChangingId().equals(other.getChangingId()))
            && (this.getStyle() == null ? other.getStyle() == null : this.getStyle().equals(other.getStyle()))
            && (this.getPlateName() == null ? other.getPlateName() == null : this.getPlateName().equals(other.getPlateName()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getImageUrl() == null ? other.getImageUrl() == null : this.getImageUrl().equals(other.getImageUrl()))
            && (this.getBackgroundColor() == null ? other.getBackgroundColor() == null : this.getBackgroundColor().equals(other.getBackgroundColor()))
            && (this.getButtonName() == null ? other.getButtonName() == null : this.getButtonName().equals(other.getButtonName()))
            && (this.getDescribe() == null ? other.getDescribe() == null : this.getDescribe().equals(other.getDescribe()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getModifierName() == null ? other.getModifierName() == null : this.getModifierName().equals(other.getModifierName()))
            && (this.getCtime() == null ? other.getCtime() == null : this.getCtime().equals(other.getCtime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getMtime() == null ? other.getMtime() == null : this.getMtime().equals(other.getMtime()))
            && (this.getModifier() == null ? other.getModifier() == null : this.getModifier().equals(other.getModifier()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getReserveItem1() == null ? other.getReserveItem1() == null : this.getReserveItem1().equals(other.getReserveItem1()))
            && (this.getReserveItem2() == null ? other.getReserveItem2() == null : this.getReserveItem2().equals(other.getReserveItem2()))
            && (this.getReserveItem3() == null ? other.getReserveItem3() == null : this.getReserveItem3().equals(other.getReserveItem3()))
            && (this.getReserveItem4() == null ? other.getReserveItem4() == null : this.getReserveItem4().equals(other.getReserveItem4()))
            && (this.getReserveItem5() == null ? other.getReserveItem5() == null : this.getReserveItem5().equals(other.getReserveItem5()))
            && (this.getReserveItem6() == null ? other.getReserveItem6() == null : this.getReserveItem6().equals(other.getReserveItem6()))
            && (this.getReserveItem7() == null ? other.getReserveItem7() == null : this.getReserveItem7().equals(other.getReserveItem7()))
            && (this.getReserveItem8() == null ? other.getReserveItem8() == null : this.getReserveItem8().equals(other.getReserveItem8()))
            && (this.getReserveItem9() == null ? other.getReserveItem9() == null : this.getReserveItem9().equals(other.getReserveItem9()))
            && (this.getReserveItem10() == null ? other.getReserveItem10() == null : this.getReserveItem10().equals(other.getReserveItem10()))
            && (this.getStartHour() == null ? other.getStartHour() == null : this.getStartHour().equals(other.getStartHour()))
            && (this.getEndHour() == null ? other.getEndHour() == null : this.getEndHour().equals(other.getEndHour()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getChangingId() == null) ? 0 : getChangingId().hashCode());
        result = prime * result + ((getStyle() == null) ? 0 : getStyle().hashCode());
        result = prime * result + ((getPlateName() == null) ? 0 : getPlateName().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getImageUrl() == null) ? 0 : getImageUrl().hashCode());
        result = prime * result + ((getBackgroundColor() == null) ? 0 : getBackgroundColor().hashCode());
        result = prime * result + ((getButtonName() == null) ? 0 : getButtonName().hashCode());
        result = prime * result + ((getDescribe() == null) ? 0 : getDescribe().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getModifierName() == null) ? 0 : getModifierName().hashCode());
        result = prime * result + ((getCtime() == null) ? 0 : getCtime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getMtime() == null) ? 0 : getMtime().hashCode());
        result = prime * result + ((getModifier() == null) ? 0 : getModifier().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getReserveItem1() == null) ? 0 : getReserveItem1().hashCode());
        result = prime * result + ((getReserveItem2() == null) ? 0 : getReserveItem2().hashCode());
        result = prime * result + ((getReserveItem3() == null) ? 0 : getReserveItem3().hashCode());
        result = prime * result + ((getReserveItem4() == null) ? 0 : getReserveItem4().hashCode());
        result = prime * result + ((getReserveItem5() == null) ? 0 : getReserveItem5().hashCode());
        result = prime * result + ((getReserveItem6() == null) ? 0 : getReserveItem6().hashCode());
        result = prime * result + ((getReserveItem7() == null) ? 0 : getReserveItem7().hashCode());
        result = prime * result + ((getReserveItem8() == null) ? 0 : getReserveItem8().hashCode());
        result = prime * result + ((getReserveItem9() == null) ? 0 : getReserveItem9().hashCode());
        result = prime * result + ((getReserveItem10() == null) ? 0 : getReserveItem10().hashCode());
        result = prime * result + ((getStartHour() == null) ? 0 : getStartHour().hashCode());
        result = prime * result + ((getEndHour() == null) ? 0 : getEndHour().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", changingId=").append(changingId);
        sb.append(", style=").append(style);
        sb.append(", plateName=").append(plateName);
        sb.append(", sort=").append(sort);
        sb.append(", status=").append(status);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", backgroundColor=").append(backgroundColor);
        sb.append(", buttonName=").append(buttonName);
        sb.append(", describe=").append(describe);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", modifierName=").append(modifierName);
        sb.append(", ctime=").append(ctime);
        sb.append(", creator=").append(creator);
        sb.append(", mtime=").append(mtime);
        sb.append(", modifier=").append(modifier);
        sb.append(", isDel=").append(isDel);
        sb.append(", reserveItem1=").append(reserveItem1);
        sb.append(", reserveItem2=").append(reserveItem2);
        sb.append(", reserveItem3=").append(reserveItem3);
        sb.append(", reserveItem4=").append(reserveItem4);
        sb.append(", reserveItem5=").append(reserveItem5);
        sb.append(", reserveItem6=").append(reserveItem6);
        sb.append(", reserveItem7=").append(reserveItem7);
        sb.append(", reserveItem8=").append(reserveItem8);
        sb.append(", reserveItem9=").append(reserveItem9);
        sb.append(", reserveItem10=").append(reserveItem10);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}