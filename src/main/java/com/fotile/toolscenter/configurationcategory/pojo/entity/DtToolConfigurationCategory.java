package com.fotile.toolscenter.configurationcategory.pojo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * dt_tool_configuration_category
 * <AUTHOR>
@Data
public class DtToolConfigurationCategory implements Serializable {
    /**
     * Id
     */
    private String id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 图片图片以，分割
     */
    private String images;

    /**
     * 排序
     */
    private String sortNo;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后更新时间
     */
    private Date mtime;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 删除标识1删除,2不删除
     */
    private String is_del;

    /**
     * 预留字段2
     */
    @FieldEncrypt
    private String reserve_item1;

    /**
     * 预留字段2
     */
    private String reserve_item2;

    /**
     * 预留字段3
     */
    private String reserve_item3;

    /**
     * 预留字段4 
     */
    private String reserve_item4;

    /**
     * 预留字段5
     */
    private String reserve_item5;

    /**
     * 预留字段6
     */
    private String reserve_item6;

    /**
     * 预留字段7
     */
    private String reserve_item7;

    /**
     * 预留字段8
     */
    private String reserve_item8;

    /**
     * 预留字段9
     */
    private String reserve_item9;

    /**
     * 预留字段10
     */
    private String reserve_item10;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DtToolConfigurationCategory other = (DtToolConfigurationCategory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getImages() == null ? other.getImages() == null : this.getImages().equals(other.getImages()))
            && (this.getSortNo() == null ? other.getSortNo() == null : this.getSortNo().equals(other.getSortNo()))
            && (this.getCtime() == null ? other.getCtime() == null : this.getCtime().equals(other.getCtime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getMtime() == null ? other.getMtime() == null : this.getMtime().equals(other.getMtime()))
            && (this.getModifier() == null ? other.getModifier() == null : this.getModifier().equals(other.getModifier()))
            && (this.getIs_del() == null ? other.getIs_del() == null : this.getIs_del().equals(other.getIs_del()))
            && (this.getReserve_item1() == null ? other.getReserve_item1() == null : this.getReserve_item1().equals(other.getReserve_item1()))
            && (this.getReserve_item2() == null ? other.getReserve_item2() == null : this.getReserve_item2().equals(other.getReserve_item2()))
            && (this.getReserve_item3() == null ? other.getReserve_item3() == null : this.getReserve_item3().equals(other.getReserve_item3()))
            && (this.getReserve_item4() == null ? other.getReserve_item4() == null : this.getReserve_item4().equals(other.getReserve_item4()))
            && (this.getReserve_item5() == null ? other.getReserve_item5() == null : this.getReserve_item5().equals(other.getReserve_item5()))
            && (this.getReserve_item6() == null ? other.getReserve_item6() == null : this.getReserve_item6().equals(other.getReserve_item6()))
            && (this.getReserve_item7() == null ? other.getReserve_item7() == null : this.getReserve_item7().equals(other.getReserve_item7()))
            && (this.getReserve_item8() == null ? other.getReserve_item8() == null : this.getReserve_item8().equals(other.getReserve_item8()))
            && (this.getReserve_item9() == null ? other.getReserve_item9() == null : this.getReserve_item9().equals(other.getReserve_item9()))
            && (this.getReserve_item10() == null ? other.getReserve_item10() == null : this.getReserve_item10().equals(other.getReserve_item10()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getImages() == null) ? 0 : getImages().hashCode());
        result = prime * result + ((getSortNo() == null) ? 0 : getSortNo().hashCode());
        result = prime * result + ((getCtime() == null) ? 0 : getCtime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getMtime() == null) ? 0 : getMtime().hashCode());
        result = prime * result + ((getModifier() == null) ? 0 : getModifier().hashCode());
        result = prime * result + ((getIs_del() == null) ? 0 : getIs_del().hashCode());
        result = prime * result + ((getReserve_item1() == null) ? 0 : getReserve_item1().hashCode());
        result = prime * result + ((getReserve_item2() == null) ? 0 : getReserve_item2().hashCode());
        result = prime * result + ((getReserve_item3() == null) ? 0 : getReserve_item3().hashCode());
        result = prime * result + ((getReserve_item4() == null) ? 0 : getReserve_item4().hashCode());
        result = prime * result + ((getReserve_item5() == null) ? 0 : getReserve_item5().hashCode());
        result = prime * result + ((getReserve_item6() == null) ? 0 : getReserve_item6().hashCode());
        result = prime * result + ((getReserve_item7() == null) ? 0 : getReserve_item7().hashCode());
        result = prime * result + ((getReserve_item8() == null) ? 0 : getReserve_item8().hashCode());
        result = prime * result + ((getReserve_item9() == null) ? 0 : getReserve_item9().hashCode());
        result = prime * result + ((getReserve_item10() == null) ? 0 : getReserve_item10().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", images=").append(images);
        sb.append(", sortNo=").append(sortNo);
        sb.append(", ctime=").append(ctime);
        sb.append(", creator=").append(creator);
        sb.append(", mtime=").append(mtime);
        sb.append(", modifier=").append(modifier);
        sb.append(", is_del=").append(is_del);
        sb.append(", reserve_item1=").append(reserve_item1);
        sb.append(", reserve_item2=").append(reserve_item2);
        sb.append(", reserve_item3=").append(reserve_item3);
        sb.append(", reserve_item4=").append(reserve_item4);
        sb.append(", reserve_item5=").append(reserve_item5);
        sb.append(", reserve_item6=").append(reserve_item6);
        sb.append(", reserve_item7=").append(reserve_item7);
        sb.append(", reserve_item8=").append(reserve_item8);
        sb.append(", reserve_item9=").append(reserve_item9);
        sb.append(", reserve_item10=").append(reserve_item10);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}