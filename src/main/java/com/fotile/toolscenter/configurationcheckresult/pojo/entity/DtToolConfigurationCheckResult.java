package com.fotile.toolscenter.configurationcheckresult.pojo.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * dt_tool_configuration_check_result
 * <AUTHOR>
@Data
public class DtToolConfigurationCheckResult implements Serializable {
    /**
     * Id
     */
    private String id;

    /**
     * 设计工具配置用户信息id
     */
    private String userinfoId;

    /**
     * 商品型号
     */
    private String productModel;

    /**
     * 商品标题
     */
    private String productName;

    /**
     * 商品图片
     */
    private String image;

    /**
     * 测试结果多个结果以,分割
     */
    private String resut;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 最后更新时间
     */
    private Date mtime;

    /**
     * 更新者
     */
    private String modifier;

    /**
     * 删除标识1删除,2不删除
     */
    private String isDel;

    /**
     * 是否有改造项目（1：无法安装，2：符合安装要求，3：需要改造）
     */
    private String reserveItem1;

    /**
     * 产品id
     */
    private String reserveItem2;

    /**
     * 预留字段3
     */
    private String reserveItem3;

    /**
     * 预留字段4 
     */
    private String reserveItem4;

    /**
     * 预留字段5
     */
    private String reserveItem5;

    /**
     * 预留字段6
     */
    private String reserveItem6;

    /**
     * 预留字段7
     */
    private String reserveItem7;

    /**
     * 预留字段8
     */
    private String reserveItem8;

    /**
     * 预留字段9
     */
    private String reserveItem9;

    /**
     * 预留字段10
     */
    private String reserveItem10;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DtToolConfigurationCheckResult other = (DtToolConfigurationCheckResult) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserinfoId() == null ? other.getUserinfoId() == null : this.getUserinfoId().equals(other.getUserinfoId()))
            && (this.getProductModel() == null ? other.getProductModel() == null : this.getProductModel().equals(other.getProductModel()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getImage() == null ? other.getImage() == null : this.getImage().equals(other.getImage()))
            && (this.getResut() == null ? other.getResut() == null : this.getResut().equals(other.getResut()))
            && (this.getCtime() == null ? other.getCtime() == null : this.getCtime().equals(other.getCtime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getMtime() == null ? other.getMtime() == null : this.getMtime().equals(other.getMtime()))
            && (this.getModifier() == null ? other.getModifier() == null : this.getModifier().equals(other.getModifier()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getReserveItem1() == null ? other.getReserveItem1() == null : this.getReserveItem1().equals(other.getReserveItem1()))
            && (this.getReserveItem2() == null ? other.getReserveItem2() == null : this.getReserveItem2().equals(other.getReserveItem2()))
            && (this.getReserveItem3() == null ? other.getReserveItem3() == null : this.getReserveItem3().equals(other.getReserveItem3()))
            && (this.getReserveItem4() == null ? other.getReserveItem4() == null : this.getReserveItem4().equals(other.getReserveItem4()))
            && (this.getReserveItem5() == null ? other.getReserveItem5() == null : this.getReserveItem5().equals(other.getReserveItem5()))
            && (this.getReserveItem6() == null ? other.getReserveItem6() == null : this.getReserveItem6().equals(other.getReserveItem6()))
            && (this.getReserveItem7() == null ? other.getReserveItem7() == null : this.getReserveItem7().equals(other.getReserveItem7()))
            && (this.getReserveItem8() == null ? other.getReserveItem8() == null : this.getReserveItem8().equals(other.getReserveItem8()))
            && (this.getReserveItem9() == null ? other.getReserveItem9() == null : this.getReserveItem9().equals(other.getReserveItem9()))
            && (this.getReserveItem10() == null ? other.getReserveItem10() == null : this.getReserveItem10().equals(other.getReserveItem10()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserinfoId() == null) ? 0 : getUserinfoId().hashCode());
        result = prime * result + ((getProductModel() == null) ? 0 : getProductModel().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getImage() == null) ? 0 : getImage().hashCode());
        result = prime * result + ((getResut() == null) ? 0 : getResut().hashCode());
        result = prime * result + ((getCtime() == null) ? 0 : getCtime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getMtime() == null) ? 0 : getMtime().hashCode());
        result = prime * result + ((getModifier() == null) ? 0 : getModifier().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getReserveItem1() == null) ? 0 : getReserveItem1().hashCode());
        result = prime * result + ((getReserveItem2() == null) ? 0 : getReserveItem2().hashCode());
        result = prime * result + ((getReserveItem3() == null) ? 0 : getReserveItem3().hashCode());
        result = prime * result + ((getReserveItem4() == null) ? 0 : getReserveItem4().hashCode());
        result = prime * result + ((getReserveItem5() == null) ? 0 : getReserveItem5().hashCode());
        result = prime * result + ((getReserveItem6() == null) ? 0 : getReserveItem6().hashCode());
        result = prime * result + ((getReserveItem7() == null) ? 0 : getReserveItem7().hashCode());
        result = prime * result + ((getReserveItem8() == null) ? 0 : getReserveItem8().hashCode());
        result = prime * result + ((getReserveItem9() == null) ? 0 : getReserveItem9().hashCode());
        result = prime * result + ((getReserveItem10() == null) ? 0 : getReserveItem10().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userinfoId=").append(userinfoId);
        sb.append(", productModel=").append(productModel);
        sb.append(", productName=").append(productName);
        sb.append(", image=").append(image);
        sb.append(", resut=").append(resut);
        sb.append(", ctime=").append(ctime);
        sb.append(", creator=").append(creator);
        sb.append(", mtime=").append(mtime);
        sb.append(", modifier=").append(modifier);
        sb.append(", isDel=").append(isDel);
        sb.append(", reserveItem1=").append(reserveItem1);
        sb.append(", reserveItem2=").append(reserveItem2);
        sb.append(", reserveItem3=").append(reserveItem3);
        sb.append(", reserveItem4=").append(reserveItem4);
        sb.append(", reserveItem5=").append(reserveItem5);
        sb.append(", reserveItem6=").append(reserveItem6);
        sb.append(", reserveItem7=").append(reserveItem7);
        sb.append(", reserveItem8=").append(reserveItem8);
        sb.append(", reserveItem9=").append(reserveItem9);
        sb.append(", reserveItem10=").append(reserveItem10);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}