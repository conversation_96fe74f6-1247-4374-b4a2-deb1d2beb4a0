package com.fotile.toolscenter.hzadvertise.client;

import com.fotile.framework.web.Result;
import com.fotile.toolscenter.hzadvertise.pojo.dto.AreaFullPathInfoVto;
import com.fotile.toolscenter.hzadvertise.pojo.dto.AreaOutDto;
import com.fotile.toolscenter.hzadvertise.pojo.dto.FindAllByParentNameNotDeletedInDto;
import com.fotile.toolscenter.hzadvertise.pojo.dto.FindByNameAndLevelInDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @create 2021-09-28 11:10
 */
@FeignClient(value = "system-center" ,path="/api")
public interface SystemClient {

    @RequestMapping(value = "open/area/findByNameAndLevel", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<AreaOutDto>> findByNameAndLevel(@RequestBody FindByNameAndLevelInDto dto);
    @RequestMapping(value = "/open/media/deleted", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> deleted(@RequestParam(value = "keys", required = true) List<String> keys);
}
