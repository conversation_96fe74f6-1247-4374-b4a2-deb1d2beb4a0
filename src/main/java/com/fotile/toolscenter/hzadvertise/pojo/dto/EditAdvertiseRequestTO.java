package com.fotile.toolscenter.hzadvertise.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 描述:
 *  修改广告
 * <AUTHOR>
 * @create 2021-09-27 17:27
 */
@Data
public class EditAdvertiseRequestTO {
    /**
     * 广告id
     */
    private String id;
    /**
     * 标题名称
     */
    private String name;
    /**
     * 图片
     */
    private String image;
    /**
     * 跳转
     */
    private String jumpContent;
    /**
     * 排序
     */
    private Long sort;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 分公司ID
     */
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;
}
