package com.fotile.toolscenter.hzadvertise.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.toolscenter.auth.service.AuthService;
import com.fotile.toolscenter.configuration.client.OpenOrgClient;
import com.fotile.toolscenter.configuration.client.OrgClient;
import com.fotile.toolscenter.hzadvertise.client.SystemClient;
import com.fotile.toolscenter.hzadvertise.dao.ChAdvertiseDao;
import com.fotile.toolscenter.hzadvertise.pojo.dto.*;
import com.fotile.toolscenter.hzadvertise.pojo.entity.ChAdvertise;
import com.fotile.toolscenter.utils.UserAuthorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.util.*;

/**
 * 描述:
 *  广告管理实现类
 * <AUTHOR>
 * @create 2021-09-27 17:06
 */
@Service
@Slf4j
public class ChAdvertiseService {

    @Autowired
    private ChAdvertiseDao chAdvertiseDao;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private SystemClient systemClient;
    @Autowired
    private OpenOrgClient openOrgClient;
    @Autowired
    private AuthService authService;
    @Autowired
    private UserAuthorUtils userAuthorUtils;


    public IPage<Map<String,Object>> getAdvertiseList(GetAdvertiseListRequestTO requestTO){
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        Page page = new Page<>(requestTO.getPageIndex(), requestTO.getPageSize());

        // 查询条件
        Map<String,Object> param = new HashMap<>();


        // 取分公司ID列表
        List<Long> companyIds = userAuthorUtils.getAuthorCompanyIds(userAuthor);
        if(null == companyIds || companyIds.contains(requestTO.getCompanyId())){
            param.put("companyId", requestTO.getCompanyId());
        }

        IPage<Map<String,Object>> advertiseIPage = chAdvertiseDao.selectAllByCompanyId(page, param);
        return advertiseIPage;
    }


    public int editAdverise(EditAdvertiseRequestTO requestTO){
        // 获取用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();

        ChAdvertise advertise = new ChAdvertise();
        advertise.setName(requestTO.getName());
        advertise.setImage(requestTO.getImage());
        advertise.setJumpContent(requestTO.getJumpContent());
        advertise.setSort(requestTO.getSort());
        advertise.setStartTime(requestTO.getStartTime());
        advertise.setEndTime(requestTO.getEndTime());

        // 如果是新增，则绑定分公司ID
        if(StringUtils.isEmpty(requestTO.getId())){
            // 查询是否超过5个广告
            QueryWrapper countQw = new QueryWrapper();
            countQw.eq("company_id", requestTO.getCompanyId());
            countQw.eq("is_deleted", "0");
            Integer count = chAdvertiseDao.selectCount(countQw).intValue();
            if(count >= 5){
                throw new BusinessException("分公司最多创建5个广告");
            }


            advertise.setCreatedBy(userAuthor.getId());
            advertise.setCreatedDate(new Date());
            advertise.setIsDeleted("0");
//            if(null == userAuthor.getSalesmanId()){
//                throw new BusinessException("没有绑定业务员ID");
//            }
//            Result<FindSalesmanByIdOutDto> orgResult = orgClient.findSalesmanById(userAuthor.getSalesmanId());
////            log.error("业务员信息" + JSON.toJSONString(orgResult.getData()));
//            if(null == orgResult.getData()){
//                throw new BusinessException("业务员信息不存在！");
//            }
//            if(orgResult.getData().getCompanyId() == null){
//                throw new BusinessException("业务员没有绑定所属分公司！");
//            }
            advertise.setCompanyId(requestTO.getCompanyId());
            return chAdvertiseDao.insert(advertise);
        }else{
            advertise.setId(requestTO.getId());
            advertise.setModifiedBy(userAuthor.getId());
            advertise.setModifiedDate(new Date());
            return chAdvertiseDao.updateById(advertise);
        }
    }


    public int deleteAdvertise(DelAdvertiseRequestTO requestTO){
        ChAdvertise advertise = chAdvertiseDao.selectById(requestTO.getId());
        advertise.setIsDeleted("1");
        return chAdvertiseDao.updateById(advertise);
    }


    public int editAdvertiseSort(EditAdvertiseSortRequestTO requestTO){
        ChAdvertise advertise = chAdvertiseDao.selectById(requestTO.getId());
        advertise.setSort(requestTO.getSort());
        return chAdvertiseDao.updateById(advertise);
    }


    public List<ChAdvertise> getMobileAdvertiseList(FindAllByParentNameNotDeletedInDto dto){
        FindByNameAndLevelInDto findByNameAndLevelInDto = new FindByNameAndLevelInDto();
        findByNameAndLevelInDto.setAreaName(dto.getArea());
        findByNameAndLevelInDto.setLevel(3);
        List<AreaOutDto> areaList = systemClient.findByNameAndLevel(findByNameAndLevelInDto).getData();
        AreaOutDto areaOutDto = null;
        for (AreaOutDto outDto : areaList) {
            if(outDto.getFullPathName().equals(dto.getProvince() + "-" + dto.getCity() + "-" + dto.getArea())){
                areaOutDto = outDto;
            }
        }
        if(areaOutDto == null){
            return new ArrayList<>();
        }

        // 根据省市区ID获取分公司ID
        Result companyResult = openOrgClient.findCompanyByArea((long) 1,(long) 1, areaOutDto.getFullPathId().split("-")[0], areaOutDto.getFullPathId().split("-")[1], areaOutDto.getFullPathId().split("-")[2]);
        Map<String,Object> companyData = (Map<String,Object>)companyResult.getData();
        List<Map<String,Object>> companyList = (List<Map<String, Object>>) companyData.get("list");
        if(companyList.isEmpty()){
            return new ArrayList<>();
        }
        List<Long> companyIds = new ArrayList<>();
        for (Map<String, Object> map : companyList) {
            companyIds.add(Long.valueOf(map.get("orgId").toString()));
        }
        // 根据分公司ID获取广告列表
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("company_id", companyIds);
        queryWrapper.eq("is_deleted", "0");
//        queryWrapper.ge("end_time", new Date());
//        queryWrapper.le("start_time", new Date());
        return chAdvertiseDao.selectList(queryWrapper);
    }

}
