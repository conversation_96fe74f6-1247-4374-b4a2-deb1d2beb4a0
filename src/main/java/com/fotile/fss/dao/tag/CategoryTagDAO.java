package com.fotile.fss.dao.tag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.fss.pojo.dto.tag.CategoryInfoDTO;
import com.fotile.fss.pojo.entity.tag.CategoryTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2020/01/06
 */
@Mapper
public interface CategoryTagDAO extends BaseMapper<CategoryTag> {

    /**
     * 根据标签id查询所在类目列表
     *
     * @param tagInfoId
     * @return
     */
    List<CategoryInfoDTO> selectCategoryList(@Param("tagInfoId") Long tagInfoId);
}