package com.fotile.fss.dao.ticket;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.fss.pojo.dto.ticket.CardCodeDTO;
import com.fotile.fss.pojo.dto.ticket.CardTicketSelectDTO;
import com.fotile.fss.pojo.dto.ticket.ChannIdUpdateDTO;
import com.fotile.fss.pojo.dto.ticket.DownloadTicketDTO;
import com.fotile.fss.pojo.entity.ticket.CardTicket;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2019/11/05
 */
@Mapper
public interface CardTicketDAO extends BaseMapper<CardTicket> {

    /**
     * 根据条件查询售卡礼活动 - 数量
     *
     * @param selectDTO
     * @return
     */
    int selectCardTicketCount(@Param("de") CardTicketSelectDTO selectDTO);

    /**
     * 根据条件查询售卡礼活动 - 列表
     *
     * @param page
     * @param selectDTO
     * @return
     */
    List<CardTicketSelectDTO> selectCardTicketList(@Param("pg") PageInfo<?> page, @Param("de") CardTicketSelectDTO selectDTO);

    /**
     * 查询指定活动下的卡券列表，用于卡券去重
     *
     * @param activeId
     * @return
     */
    List<String> selectCardCodeList(@Param("activeId") Long activeId);

    /**
     * 查询需要下载的卡券列表
     *
     * @param activeId
     * @param channelId
     * @return
     */
    List<DownloadTicketDTO> selectDownloadList(@Param("activeId") Long activeId, @Param("channelId") Long channelId);

    /**
     * 批量更新卡券渠道
     *
     * @param channIdUpdateDTO
     * @return
     */
    int updateChannelId(@Param("de") ChannIdUpdateDTO channIdUpdateDTO);

    /**
     * 根据渠道id查询卡券列表
     *
     * @param channelId
     * @return
     */
    List<CardCodeDTO> selectCardTicketListByChannelId(@Param("channelId") Long channelId);
}