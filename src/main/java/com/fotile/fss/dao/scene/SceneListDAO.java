package com.fotile.fss.dao.scene;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.fss.pojo.dto.scene.ChildSceneListDTO;
import com.fotile.fss.pojo.dto.scene.MainSceneListDTO;
import com.fotile.fss.pojo.dto.scene.SceneListSelectDTO;
import com.fotile.fss.pojo.entity.scene.SceneList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2020/05/29
 */
@Mapper
public interface SceneListDAO extends BaseMapper<SceneList> {

    /**
     * 根据条件查询 - 数量
     *
     * @param selectDTO
     * @return
     */
    int selectCount(@Param("de") SceneListSelectDTO selectDTO);

    /**
     * 根据条件查询 - 列表
     *
     * @param selectDTO
     * @return
     */
    List<MainSceneListDTO> selectPage(@Param("pg") PageInfo<?> page, @Param("de") SceneListSelectDTO selectDTO);

    /**
     * 查询子场景列表
     *
     * @param appInfoId
     * @param parentId
     * @return
     */
    List<ChildSceneListDTO> selectChildList(@Param("appInfoId") Long appInfoId, @Param("parentId") Long parentId);

    /**
     * 设置场景状态
     *
     * @param sceneStatus
     * @param id
     */
    void updateSceneStatus(@Param("sceneStatus") Integer sceneStatus, @Param("id") Long id);
}