package com.fotile.fss.business.controller.material;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.fss.business.service.material.NewsInfoService;
import com.fotile.fss.pojo.dto.material.NewsInfoSelectDTO;
import com.fotile.fss.pojo.entity.material.NewsInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(value = "单图文素材接口")
@RequestMapping("/api/newsInfo")
public class NewsInfoController extends BaseController {

    @Autowired
    private NewsInfoService newsInfoService;

    @RequestMapping(value = "/selectTempContent", method = RequestMethod.GET)
    @ApiOperation("根据id查询")
    public Result selectTempContent(@RequestParam("id") Long id) {
        return success(newsInfoService.selectById(id));
    }

    @RequestMapping(value = "/select", method = RequestMethod.POST)
    @ApiOperation("查询图文列表")
    public Result select(@RequestBody NewsInfoSelectDTO newsListSelectDTO) {
        List<NewsInfo> newsInfos = newsInfoService.selectNewsInfoList(newsListSelectDTO);
        return success(newsInfos);
    }

    @RequestMapping(value = "/selectList", method = RequestMethod.POST)
    @ApiOperation("查询图文列表 - 微信自定义菜单")
    public Result selectList(@RequestBody @Valid NewsInfoSelectDTO newsListSelectDTO) {
        return success(newsInfoService.selectPage(newsListSelectDTO));
    }
}
