package com.fotile.fss.business.controller.report;

import com.fotile.framework.util.DateUtil;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.fss.business.service.appInfo.AppInfoService;
import com.fotile.fss.business.service.report.NewsReportService;
import com.fotile.fss.pojo.entity.report.NewsReport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeArticleTotal;
import me.chanjar.weixin.mp.bean.datacube.WxDataCubeArticleTotalDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@Api(value = "图文消息统计接口")
@RequestMapping("/api/newsReport")
public class NewsReportController extends BaseController {

    @Autowired
    private AppInfoService appInfoService;

    @Resource
    private WxMpService wxMpService;

    @Autowired
    private NewsReportService newsReportService;

    @RequestMapping(value = "/qryLastNewsReport", method = RequestMethod.GET)
    @ApiOperation("获取上次图文统计数据")
    public Result qryLastNewsReport(@RequestParam("appInfoId") Long appInfoId) {
        return success(newsReportService.selectLastNewsReport(appInfoId));
    }

    @RequestMapping(value = "/qryNewsReport", method = RequestMethod.GET)
    @ApiOperation("过去30天图文统计")
    public Result qryNewsReport(@RequestParam("appInfoId") Long appInfoId) {
        return success(newsReportService.selectNewsReport(appInfoId));
    }

    @RequestMapping(value = "/synchronizeReport", method = RequestMethod.GET)
    @ApiOperation("同步上次群发报表")
    public Result synchronizeReport(@RequestParam("appInfoId") Long appInfoId) {
        this.getArticleTotal(appInfoId);
        return success("报表同步成功");
    }

    /**
     * 调用微信统计接口
     *
     * @param appInfoId
     * @throws WxErrorException
     */
    private void getArticleTotal(Long appInfoId) {
        // 获取上次群发是否统计过
        List<NewsReport> reportList = newsReportService.selectLastNewsReport(appInfoId);
        if (reportList == null || reportList.size() == 0) {
            // 切换公众号
            String appId = appInfoService.selectAppId(appInfoId);
            if (!wxMpService.switchover(appId)) {
                throw new BusinessException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
            }

            // 获取前一天的日期（格式yyyy-MM-dd）
            Date sendDate = null;
            for (int i = 1; i <= 30; i++) {
                // 最多统计到前30天的数据
                sendDate = DateUtil.getCalcDate(new Date(), -i);
                // 调用微信接口查询统计数据
                List<WxDataCubeArticleTotal> list = null;
                try {
                    list = wxMpService.getDataCubeService().getArticleTotal(sendDate, sendDate);
                } catch (WxErrorException e) {
                    throw new BusinessException(e.getMessage());
                }
                if (list != null && list.size() > 0) {
                    for (int j = 0; j < list.size(); j++) {
                        WxDataCubeArticleTotal wxDataCubeArticleTotal = list.get(j);
                        String title = wxDataCubeArticleTotal.getTitle();
                        List<WxDataCubeArticleTotalDetail> details = wxDataCubeArticleTotal.getDetails();
                        for (WxDataCubeArticleTotalDetail detail : details) {
                            // 图文顺序
                            int sorted = j + 1;
                            newsReportService.saveNewsReport(appInfoId, sendDate, title, sorted, detail);
                        }
                    }
                    break;
                }
            }
        }
    }
}