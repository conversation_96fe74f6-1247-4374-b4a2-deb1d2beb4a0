package com.fotile.fss.business.controller.tag;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.fss.business.service.tag.TagInfoService;
import com.fotile.fss.pojo.dto.tag.TagInfoInsertDTO;
import com.fotile.fss.pojo.dto.tag.TagInfoSelectDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@Api(value = "标签接口")
@RequestMapping("/api/tag")
public class TagInfoController extends BaseController {

    @Autowired
    private TagInfoService tagInfoService;

    @RequestMapping(value = "/select", method = RequestMethod.POST)
    @ApiOperation("查询规则列表 - 分页查询")
    public Result select(@RequestBody @Valid TagInfoSelectDTO selectDTO) {
        return success(tagInfoService.selectPage(selectDTO));
    }

    @RequestMapping(value = "/view", method = RequestMethod.GET)
    @ApiOperation("查看")
    public Result view(@RequestParam("id") Long id) {
        return success(tagInfoService.selectTagInfo(id));
    }

    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    @ApiOperation("新增标签")
    public Result insert(@RequestBody @Valid TagInfoInsertDTO tagInfo) {
        tagInfoService.insert(tagInfo);
        return success("新增成功");
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation("修改标签")
    public Result update(@RequestBody @Valid TagInfoInsertDTO insertDTO) {
        tagInfoService.updateById(insertDTO);
        return success("修改成功");
    }

    @RequestMapping(value = "/updateStatus", method = RequestMethod.GET)
    @ApiOperation("修改标签状态")
    public Result updateStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        tagInfoService.updateStatus(id, status);
        return success("修改成功");
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation("查询标签列表 - 规则使用")
    public Result list(@ApiParam("appInfoId") Long appInfoId, @ApiParam("categoryInfoId") Long categoryInfoId) {
        return success(tagInfoService.selectTagList(appInfoId, categoryInfoId));
    }

    @RequestMapping(value = "/syncFwsTag", method = RequestMethod.GET)
    @ApiOperation("同步FWS标签")
    public Result syncFwsTag(HttpServletRequest request) {
        return success(tagInfoService.syncFwsTag(request));
    }

    @RequestMapping(value = "/syncFwsTagUserNum", method = RequestMethod.GET)
    @ApiOperation("同步FWS标签用户数量")
    public Result syncFwsTagUserNum(HttpServletRequest request) {
        return success(tagInfoService.syncFwsTagUserNum(request));
    }

    @RequestMapping(value = "/updateTagDesc", method = RequestMethod.GET)
    @ApiOperation("修改标签备注")
    public Result updateTagDesc(HttpServletRequest request) {
        return success(tagInfoService.updateTagDesc(request));
    }
//
//    @RequestMapping(value = "/testInsert", method = RequestMethod.GET)
//    @ApiOperation("testInsert")
//    public Result testInsert(HttpServletRequest request) {
//        tagInfoService.testInsert(request);
//        return success();
//    }



}
