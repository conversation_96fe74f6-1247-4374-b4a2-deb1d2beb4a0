package com.fotile.fss.business.service.ticket;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.fss.dao.ticket.UserTicketDAO;
import com.fotile.fss.pojo.dto.ticket.*;
import com.fotile.fss.pojo.entity.ticket.UserTicket;
import com.fotile.fss.pojo.mapper.UserTicketMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class UserTicketService {

    @Resource
    private UserTicketDAO userTicketDAO;

    /**
     * 保存一个实体，null的属性不会保存，会使用数据库默认值
     *
     * @param userTicketInsertDTO
     * @return
     */
    public int insertSelective(UserTicketInsertDTO userTicketInsertDTO) {
        UserTicket userTicket = UserTicketMapper.INSTANCE.UserTicketInsertDTOTOUserTicket(userTicketInsertDTO);
        userTicket.setIsDeleted(0L);
        userTicket.setIsSalesLeads(999L);
        return userTicketDAO.insert(userTicket);
    }

    /**
     * 根据主键更新属性不为null的值
     *
     * @param userTicket
     * @return
     */
    public int updateById(UserTicket userTicket) {
        return userTicketDAO.updateById(userTicket);
    }

    /**
     * 根据主键删除一条记录
     *
     * @param id
     * @return
     */
    public int deleteById(Long id) {
        // 更新内容
        UserTicket userTicket = new UserTicket();
        userTicket.setIsDeleted(id);
        // 更新条件
        UpdateWrapper<UserTicket> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        return userTicketDAO.update(userTicket, updateWrapper);
    }

    /**
     * 根据条件查询售卡礼用户信息
     *
     * @param userTicketDTO
     * @return
     */
    public UserTicketViewDTO selectUserTicketInfo(UserTicketDTO userTicketDTO) {
        List<UserTicketViewDTO> list = userTicketDAO.selectUserTicketInfo(userTicketDTO);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据条件查询售卡礼用户列表
     *
     * @param selectDTO
     * @return
     */
    public PageInfo<UserTicketViewDTO> selectUserTicketList(UserTicketSelectDTO selectDTO) {

        PageInfo page = new PageInfo(selectDTO.getPageNum(), selectDTO.getPageSize());

        int count = userTicketDAO.selectUserTicketCount(selectDTO);

        List<UserTicketViewDTO> list = new ArrayList<>();
        if (count > 0) {
            list = userTicketDAO.selectUserTicketList(page, selectDTO);
        }

        page.setRecords(list);
        page.setTotal(count);
        return page;
    }

    /**
     * 查询指定活动的手机号参与个数
     *
     * @param userTicketInsertDTO
     * @return
     */
    public int selectMobileByActive(UserTicketInsertDTO userTicketInsertDTO) {
        UserTicketSelectDTO selectDTO = UserTicketMapper.INSTANCE.UserTicketInsertDTOTOUserTicketSelectDTO(userTicketInsertDTO);
        selectDTO.setCardCode(null);
        List<UserTicketViewDTO> list = userTicketDAO.selectUserTicketList(new PageInfo(0, 5), selectDTO);
        if (list == null) {
            return 0;
        }
        return list.size();
    }

    /**
     * 获取已抽奖抽奖用户列表
     *
     * @return
     */
    public List<UserDrawViewDTO> selectLuckedUserList(Long id, String luckLevel) {
        UserDrawSelectDTO userDrawSelect = new UserDrawSelectDTO();
        userDrawSelect.setId(id);
        userDrawSelect.setDrawStated(999L);
        userDrawSelect.setLuckLevel(luckLevel);
        return userTicketDAO.selectLuckedUserList(userDrawSelect);
    }

    /**
     * 获取符合抽奖条件的用户列表
     *
     * @return
     */
    public List<UserDrawViewDTO> selectLuckUserList(Long id) {
        UserDrawSelectDTO userDrawSelect = new UserDrawSelectDTO();
        userDrawSelect.setId(id);
        return userTicketDAO.selectLuckUserList(userDrawSelect);
    }

}