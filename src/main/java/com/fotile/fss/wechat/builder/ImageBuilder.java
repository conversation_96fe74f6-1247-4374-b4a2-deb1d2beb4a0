package com.fotile.fss.wechat.builder;

import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutImageMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;

/**
 * 同步回复消息时使用 - 图片消息
 */
public class ImageBuilder extends AbstractBuilder {

    @Override
    public WxMpXmlOutMessage build(String mediaId, WxMpXmlMessage wxMessage, WxMpService service) {

        WxMpXmlOutImageMessage m = WxMpXmlOutMessage.IMAGE().mediaId(mediaId)
                .fromUser(wxMessage.getToUser())
                .toUser(wxMessage.getFromUser())
                .build();

        return m;
    }

}
