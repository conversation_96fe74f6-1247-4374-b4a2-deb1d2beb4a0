package com.fotile.fss.wechat.service;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.fss.business.service.appInfo.AppInfoService;
import com.fotile.fss.business.service.material.NewsInfoService;
import com.fotile.fss.pojo.dto.wx.WxMenuDTO;
import com.fotile.fss.pojo.dto.wx.WxMenuRuleDTO;
import com.fotile.fss.pojo.entity.wx.WxMenu;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.menu.WxMpGetSelfMenuInfoResult;
import me.chanjar.weixin.mp.bean.menu.WxMpSelfMenuInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 与微信平台交互
 */
@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "third")
public class WxMenuService {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxMenuDbService wxMenuDbService;

    @Autowired
    private AppInfoService appInfoService;

    @Autowired
    private NewsInfoService newsInfoService;

    /**
     * 获取自定义菜单配置接口
     *
     * @param appInfoId
     * @throws WxErrorException
     */
    public List<WxMenuDTO.WxMenuButtonDTO> getWxMenu(Long appInfoId) throws WxErrorException {

        // 获取本地保存的自定义菜单信息
        List<WxMenu> list = wxMenuDbService.selectList(appInfoId);
        // 根据公众号id查询appId

        String appId = appInfoService.selectAppId(appInfoId);
        if (!wxMpService.switchover(appId)) {
            throw new BusinessException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
        }

        // 返回结果
        List<WxMenuDTO.WxMenuButtonDTO> resButtons = new ArrayList<>();

        // 调用获取自定义菜单配置接口
        WxMpGetSelfMenuInfoResult wxMpGetSelfMenuInfoResult = wxMpService.getMenuService().getSelfMenuInfo();
        if (ObjectUtils.isNotEmpty(wxMpGetSelfMenuInfoResult)) {
            // 菜单信息
            WxMpSelfMenuInfo wxMpSelfMenuInfo = wxMpGetSelfMenuInfoResult.getSelfMenuInfo();
            if(ObjectUtils.isNotEmpty(wxMpSelfMenuInfo)) {
                // 一级菜单列表
                List<WxMpSelfMenuInfo.WxMpSelfMenuButton> buttons = wxMpSelfMenuInfo.getButtons();
                if (CollectionUtils.isNotEmpty(buttons)) {
                    for (WxMpSelfMenuInfo.WxMpSelfMenuButton wxMpSelfMenuButton : buttons) {
                        // 一级菜单匹配
                        WxMenuDTO.WxMenuButtonDTO wxMenuDTO = this.menuButtonMatch(list, wxMpSelfMenuButton);
                        // 二级菜单信息
                        WxMpSelfMenuInfo.WxMpSelfMenuButton.SubButtons subButtons = wxMpSelfMenuButton.getSubButtons();
                        if (subButtons != null) {
                            // 二级菜单列表
                            List<WxMpSelfMenuInfo.WxMpSelfMenuButton> selfMenuButtons = subButtons.getSubButtons();
                            if (selfMenuButtons != null && selfMenuButtons.size() > 0) {
                                for (WxMpSelfMenuInfo.WxMpSelfMenuButton selfMenuButton : selfMenuButtons) {
                                    // 二级菜单匹配
                                    wxMenuDTO.addData(this.menuButtonMatch(list, selfMenuButton));
                                }
                            }
                        }
                        resButtons.add(wxMenuDTO);
                    }
                }
            }
        }
        return resButtons;
    }

    /**
     * 菜单匹配
     *
     * @param list
     * @param selfMenuButton
     * @return
     */
    public WxMenuDTO.WxMenuButtonDTO menuButtonMatch(List<WxMenu> list, WxMpSelfMenuInfo.WxMpSelfMenuButton selfMenuButton) {
        WxMenuDTO.WxMenuButtonDTO wxMenuDTO = new WxMenuDTO.WxMenuButtonDTO();
        wxMenuDTO.setMainType(selfMenuButton.getType());
        wxMenuDTO.setType(selfMenuButton.getType());
        wxMenuDTO.setName(selfMenuButton.getName());
        wxMenuDTO.setUrl(selfMenuButton.getUrl());
        wxMenuDTO.setAppId(selfMenuButton.getAppId());
        wxMenuDTO.setPagePath(selfMenuButton.getPagePath());
        // 遍历已保存菜单并匹配
        if (list != null && list.size() > 0) {
            for (WxMenu wxMenu : list) {
                // 点击事件的key
                String key = selfMenuButton.getKey();
                if (selfMenuButton.getName().equals(wxMenu.getName())) {
                    // 文本消息、图片消息、语音消息、语音消息、图文消息
                    if ("click".equals(selfMenuButton.getType()) && String.valueOf(wxMenu.getId()).equals(key)) {
                        wxMenuDTO.setContent(wxMenu.getContent());
                        wxMenuDTO.setMainType("news");
                        wxMenuDTO.setType(wxMenu.getType());
                        if ("image".equals(wxMenu.getType())) {
                            wxMenuDTO.setImageUrl(wxMenu.getMediaUrl());
                        }
                        if ("voice".equals(wxMenu.getType())) {
                            wxMenuDTO.setVoiceUrl(wxMenu.getMediaUrl());
                        }
                        if ("video".equals(wxMenu.getType())) {
                            wxMenuDTO.setVideoUrl(wxMenu.getMediaUrl());
                        }
                        if ("mpnews".equals(wxMenu.getType())) {
                            wxMenuDTO.setNewsInfo(newsInfoService.selectMenuNewsInfo(wxMenu.getNewInfoId()));
                            wxMenuDTO.setNewInfoId(wxMenu.getNewInfoId());
                        }
                    }
                }
            }
        }
        return wxMenuDTO;
    }

    /**
     * 创建自定义菜单
     *
     * @param wxMenuInfo
     */
    @Transactional
    public void menuCreate(WxMenuDTO wxMenuInfo) throws WxErrorException {
        // 根据公众号id查询appId
        String appId = appInfoService.selectAppId(wxMenuInfo.getAppInfoId());
        if (!wxMpService.switchover(appId)) {
            throw new BusinessException(String.format("未找到对应appId=[%s]的配置，请核实！", appId));
        }

        // 删除历史菜单（永久素材库中的不能删除，需管理员在mp后台手动管理）
        wxMenuDbService.deleteWxMenu(wxMenuInfo.getAppInfoId());

        // 组装微信菜单内容
        me.chanjar.weixin.common.bean.menu.WxMenu wxMenu = new me.chanjar.weixin.common.bean.menu.WxMenu();

        // 微信一级菜单内容
        List<WxMenuButton> buttons = new ArrayList<>();
        for (WxMenuDTO.WxMenuButtonDTO menuDTO : wxMenuInfo.getButtons()) {
            // 获取二级菜单
            List<WxMenuDTO.WxMenuButtonDTO> subButtons = menuDTO.getSubButtons();
            if (subButtons != null && subButtons.size() > 0) {
                // 组装微信二级菜单内容
                List<WxMenuButton> wxMenuButtons = new ArrayList<>();

                // 遍历二级菜单，并判断格式是否正确
                for (WxMenuDTO.WxMenuButtonDTO wxMenuButton : subButtons) {

                    // 校验菜单
                    this.verificationMenu(wxMenuButton);

                    // 二级菜单添加到本地微信菜单表
                    this.insertWxMenu(wxMenuInfo.getAppInfoId(), wxMenuButton);

                    // 添加到微信二级菜单
                    wxMenuButtons.add(this.createButtons(wxMenuButton, null));
                }

                // 添加到微信一级菜单(子菜单不为空)
                buttons.add(this.createButtons(menuDTO, wxMenuButtons));
            } else {

                // 校验菜单
                this.verificationMenu(menuDTO);

                // 一级菜单添加到本地微信菜单表
                this.insertWxMenu(wxMenuInfo.getAppInfoId(), menuDTO);

                // 添加到微信一级菜单（子菜单为空）
                buttons.add(this.createButtons(menuDTO, null));
            }
        }

        // 添加到微信菜单内容中
        wxMenu.setButtons(buttons);

        wxMpService.getMenuService().menuCreate(wxMenu);
    }

    /**
     * 组装微信菜单
     *
     * @param menu
     * @return
     */
    public WxMenuButton createButtons(WxMenuDTO.WxMenuButtonDTO menu, List<WxMenuButton> subButtons) {
        WxMenuButton wxMenu = new WxMenuButton();
        wxMenu.setType(menu.getType());
        wxMenu.setName(menu.getName());
        wxMenu.setUrl(menu.getUrl());
        wxMenu.setKey(menu.getKey());
        wxMenu.setAppId(menu.getAppId());
        wxMenu.setPagePath(menu.getPagePath());
        wxMenu.setSubButtons(subButtons);
        return wxMenu;
    }

    /**
     * 添加到本地微信菜单表
     *
     * @param menu
     * @return
     */
    public void insertWxMenu(Long appInfoId, WxMenuDTO.WxMenuButtonDTO menu) {
        if (StringUtils.isNotEmpty(menu.getType())) {
            WxMenu wxMenu = new WxMenu();
            wxMenu.setAppInfoId(appInfoId);
            wxMenu.setName(menu.getName());
            wxMenu.setType(menu.getType());
            wxMenu.setContent(menu.getContent());
            wxMenu.setMediaUrl(menu.getUrl());
            wxMenu.setAppId(menu.getAppId());
            wxMenu.setPagepath(menu.getPagePath());
            if ("image".equals(menu.getType())) {
                wxMenu.setMediaUrl(menu.getImageUrl());
            }
            if ("voice".equals(menu.getType())) {
                wxMenu.setMediaUrl(menu.getVoiceUrl());
            }
            if ("video".equals(menu.getType())) {
                wxMenu.setMediaUrl(menu.getVideoUrl());
            }
            wxMenu.setNewInfoId(menu.getNewInfoId());
            wxMenuDbService.insert(wxMenu);
            menu.setKey(String.valueOf(wxMenu.getId()));
            // 菜单类型转换（自定义类型 -> 微信类型）
            menu.setType(this.getMenuType(menu.getType()));
        }
    }

    /**
     * 菜单校验
     *
     * @param wxMenuButton
     */
    public void verificationMenu(WxMenuDTO.WxMenuButtonDTO wxMenuButton) {
        // 菜单主类型为miniprogram时
        if ("miniprogram".equals(wxMenuButton.getMainType())) {
            wxMenuButton.setType(wxMenuButton.getMainType());
        }
        if (StringUtils.isEmpty(wxMenuButton.getType())) {
            throw new BusinessException("菜单格式不正确");
        }
        if ("view".equals(wxMenuButton.getType()) && StringUtils.isEmpty(wxMenuButton.getUrl())) {
            throw new BusinessException(wxMenuButton.getName() + " : 请填写页面地址");
        }
        if ("text".equals(wxMenuButton.getType()) && StringUtils.isEmpty(wxMenuButton.getContent())) {
            throw new BusinessException(wxMenuButton.getName() + " : 请填写文本内容");
        }
        if ("image".equals(wxMenuButton.getType()) && StringUtils.isEmpty(wxMenuButton.getImageUrl())) {
            throw new BusinessException(wxMenuButton.getName() + " : 请选择图片");
        }
        if ("voice".equals(wxMenuButton.getType()) && StringUtils.isEmpty(wxMenuButton.getVoiceUrl())) {
            throw new BusinessException(wxMenuButton.getName() + " : 请选择语音");
        }
        if ("video".equals(wxMenuButton.getType()) && StringUtils.isEmpty(wxMenuButton.getVideoUrl())) {
            throw new BusinessException(wxMenuButton.getName() + " : 请选择视频");
        }
        if ("mpnews".equals(wxMenuButton.getType()) && wxMenuButton.getNewInfoId() == null) {
            throw new BusinessException(wxMenuButton.getName() + " : 请选择图文消息");
        }
        if ("miniprogram".equals(wxMenuButton.getType())) {
            if (StringUtils.isEmpty(wxMenuButton.getAppId())) {
                throw new BusinessException(wxMenuButton.getName() + " : 请填写小程序appId");
            }
            if (StringUtils.isEmpty(wxMenuButton.getPagePath())) {
                throw new BusinessException(wxMenuButton.getName() + " : 请填写小程序路径");
            }
        }
    }

    /**
     * 菜单类型转换
     * 文本消息、图片消息、图文消息、语音消息、视频消息全部转换为click类型
     *
     * @param type
     * @return
     */
    private String getMenuType(String type) {
        if (StringUtils.isNotEmpty(type)) {
            switch (type) {
                case "view":
                    return "view";
                case "miniprogram":
                    return "miniprogram";
                default:
                    return "click";
            }
        }
        return null;
    }

    /**
     * 获取不同类型菜单列表 - 规则列表使用
     *
     * @param appInfoId
     * @param menuType
     * @return
     */
    public List<WxMenuRuleDTO> selectBtnList(Long appInfoId, String menuType) {
        // 返回结果
        List<WxMenuRuleDTO> list = new ArrayList<WxMenuRuleDTO>();
        // 查询保存的微信自定义菜单列表
        List<WxMenu> selectList = wxMenuDbService.selectList(appInfoId);
        if (selectList != null && selectList.size() > 0) {
            for (WxMenu wxMenu : selectList) {
                // 跳转网页类型的
                if ("view".equals(menuType) && menuType.equals(wxMenu.getType())) {
                    WxMenuRuleDTO wxMenuRuleDTO = new WxMenuRuleDTO();
                    wxMenuRuleDTO.setId(wxMenu.getId());
                    wxMenuRuleDTO.setName(wxMenu.getName());
                    wxMenuRuleDTO.setUrl(wxMenu.getMediaUrl());
                    list.add(wxMenuRuleDTO);
                }
                // 按钮类型
                if ("click".equals(menuType) && !"view".equals(wxMenu.getType()) && !"miniprogram".equals(wxMenu.getType())) {
                    WxMenuRuleDTO wxMenuRuleDTO = new WxMenuRuleDTO();
                    wxMenuRuleDTO.setId(wxMenu.getId());
                    wxMenuRuleDTO.setName(wxMenu.getName());
                    wxMenuRuleDTO.setUrl(wxMenu.getMediaUrl());
                    list.add(wxMenuRuleDTO);
                }
            }
        }
        return list;
    }

}
