package com.fotile.fss.pojo.dto.scene;

import com.fotile.fss.pojo.entity.scene.SceneCrowd;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("子营销场景显示列表")
@Data
public class ChildSceneInfoDTO implements Serializable {

    @ApiModelProperty("场景人群包列表")
    private List<SceneCrowd> sceneCrowdList;

    @ApiModelProperty("场景流程")
    private SceneProcessViewDTO sceneProcess;

}
