package com.fotile.fss.pojo.dto.live;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("新增参数")
@Data
public class LiveDisinfectionHistoryInsertDTO implements Serializable {

    @ApiModelProperty("门店id")
    private Long storeId;

    @ApiModelProperty("描述")
    private String content;

    @ApiModelProperty("消毒日期")
    private String disinfectionDate;

    @ApiModelProperty("消毒时间")
    private String disinfectionTime;

    @ApiModelProperty("消毒状态：1完成消毒，0未完成")
    private Integer state;

}
