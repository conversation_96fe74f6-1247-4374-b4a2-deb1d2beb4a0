package com.fotile.fss.pojo.dto.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class RuleListDTO implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("公众号标识")
    @NotNull(message = "公众号标识不能为空")
    private Long appInfoId;

    @ApiModelProperty("规则名称")
    @NotNull(message = "规则名称不能为空")
    private String ruleName;

    @ApiModelProperty("状态（0：禁用，1启用）")
    @NotNull(message = "状态不能为空")
    private Integer state;

    @ApiModelProperty("或条件规则")
    @NotNull(message = "或条件规则不能为空")
    private List<RuleInfoViewDTO> orList;

    @ApiModelProperty("和条件规则")
    private List<RuleInfoViewDTO> andList;

    @ApiModelProperty("触发动作")
    @NotNull(message = "触发动作不能为空")
    private List<RuleMsgViewDTO> actionList;
}
