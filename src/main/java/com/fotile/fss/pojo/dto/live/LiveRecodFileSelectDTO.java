package com.fotile.fss.pojo.dto.live;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@ApiModel("")
@Data
public class LiveRecodFileSelectDTO implements Serializable {

    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;

    @ApiModelProperty("")
    private Long streamId;

    @ApiModelProperty("")
    private String streamName;

    @ApiModelProperty("录制文件起始时间戳")
    private Date startTime;

    @ApiModelProperty("录制文件结束时间戳")
    private Date endTime;

    @ApiModelProperty("录制文件文件下载 URL")
    private String videoUrl;

    @ApiModelProperty("点播 file ID，在点播平台可以唯一定位一个点播视频文件")
    private String fileId;

    @ApiModelProperty("录制文件时长，单位秒")
    private Long duration;

    @ApiModelProperty("录制文件大小，单位字节")
    private Long fileSize;

    @ApiModelProperty("")
    private String streamParam;

    @ApiModelProperty("状态（0：禁用，1启用）")
    private String state;

    @ApiModelProperty("是否删除（0：未删除，其它值删除）")
    private Long isDeleted;

    @ApiModelProperty("创建者")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private Date createdDate;

    @ApiModelProperty("修改者")
    private String modifiedBy;

    @ApiModelProperty("修改时间")
    private Date modifiedDate;

}
