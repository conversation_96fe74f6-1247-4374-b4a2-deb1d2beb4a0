package com.fotile.fss.pojo.dto.live;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("新增参数")
@Data
public class LiveRoomInsertDTO implements Serializable {

    @ApiModelProperty("主播id")
    private String masterUserId;

    @ApiModelProperty("分公司id")
    private Long companyId;

    @ApiModelProperty("直播流id")
    private String streamName;

    @ApiModelProperty("门店id")
    private String storeId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("头像地址")
    private String headImage;

    @ApiModelProperty("播放地址")
    private String rtmp;

    @ApiModelProperty("播放地址")
    private String flv;

    @ApiModelProperty("播放地址")
    private String hls;

    @ApiModelProperty("主播名称")
    private String masterUserName;

    @ApiModelProperty("状态（0：禁用，1启用）")
    private Integer state;

}
