package com.fotile.fss.pojo.dto.live;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@ApiModel
@Data
public class ListLiveHistoryDTO implements Serializable {

    @ApiModelProperty("主播名")
    private String masterUserName;

    @ApiModelProperty("主播id")
    private String masterUserId;

    @ApiModelProperty("直播状态")
    private Integer state;

    @ApiModelProperty("直播流id")
    private String streamName;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("直播时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty("直播间标题")
    private String title;

    @ApiModelProperty("关播时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date closeDate;

    @ApiModelProperty("在线人数")
    private Long closeOnline;

    @ApiModelProperty("点赞数")
    private Long clickLike;


}
