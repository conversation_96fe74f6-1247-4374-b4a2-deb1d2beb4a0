package com.fotile.fss.pojo.dto.qrCode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QrCodeViewDTO implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("公众号标识")
    private Long appInfoId;

    @ApiModelProperty("公众号名称")
    private String appName;

    @ApiModelProperty("分公司id（站点id）")
    private Long siteId;

    @ApiModelProperty("QR_SCENE：临时二维码，QR_LIMIT_SCENE：永久二维码")
    private String qrCodeType;

    @ApiModelProperty("二维码名称")
    private String qrCodeName;

    @ApiModelProperty("二维码地址")
    private String qrCodeUrl;

    @ApiModelProperty("二维码描述")
    private String qrCodeStr;

    @ApiModelProperty("场景值描述")
    private String sceneStr;

    @ApiModelProperty("过期时间")
    private Date expireDate;

    @ApiModelProperty("创建时间")
    private Date createdDate;

}
