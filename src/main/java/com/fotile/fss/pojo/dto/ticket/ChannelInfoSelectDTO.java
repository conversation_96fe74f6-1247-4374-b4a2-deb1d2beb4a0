package com.fotile.fss.pojo.dto.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("")
@Data
public class ChannelInfoSelectDTO implements Serializable {

    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;

    @ApiModelProperty("表主键")
    private Long id;

    @ApiModelProperty("状态（0：禁用，1启用）")
    private String state;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty("站点名称")
    private String siteName;

    @ApiModelProperty("总数")
    private Long totalNum;

    @ApiModelProperty("留资数量")
    private Long salesLeadsNum;

    @ApiModelProperty("检票数量")
    private Long sceneCheckedNum;

    @ApiModelProperty("下单数量")
    private Long preOrderNum;

    @ApiModelProperty("抽奖数量")
    private Long prizeDrawNum;

    @ApiModelProperty("售卡礼活动id")
    private Long activeId;

}
