package com.fotile.fss.pojo.dto.scene;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SceneProcessInsertDTO implements Serializable {

    @ApiModelProperty("发送渠道类型（优先：priority，固定：fixed）")
    @NotNull(message = "发送渠道类型不能为空")
    private String channelType;

    @ApiModelProperty("发送类型（48小时互动：customer，模版消息：template，群发消息: sendAll）")
    @NotNull(message = "发送类型不能为空")
    private String sendType;

    @ApiModelProperty("定时类型（1:绝对定时；2：相对时间）")
    private String cronType;

    @ApiModelProperty("发送优先级（最高优先级：1）")
    @NotNull(message = "发送优先级不能为空")
    private Integer sorted;

    @ApiModelProperty("群发素材id")
    private Long newsListId;

    @ApiModelProperty("执行时间表达式")
    @NotNull(message = "执行时间表达式不能为空")
    private String cronTime;

    @ApiModelProperty("定时相对时间")
    private String relativeTime;

    @ApiModelProperty("任务开始时间")
    private Date jobStartDate;

    @ApiModelProperty("任务结束时间")
    private Date jobEndDate;

    @ApiModelProperty("模版消息")
    private TemplateContent tempContent;

    @ApiModelProperty("48小时消息列表")
    private List<MsgContent> msgContent;

    @Getter
    @Setter
    public static class TemplateContent {

        @ApiModelProperty("模版消息id")
        private String templateId;

        @ApiModelProperty("详情地址")
        private String url;

        @ApiModelProperty("模板data参数")
        private JSONObject data;

    }

    @Getter
    @Setter
    public static class MsgContent {

        @ApiModelProperty("消息类型（文本消息：text，图文消息: news，图片消息：image，语音消息：voice，视频消息：video）")
        private String msgType;

        @ApiModelProperty("群发素材id")
        private Long newsInfoId;

        @ApiModelProperty("图片/语音/视频链接地址")
        private String mediaUrl;

        @ApiModelProperty("图片链接地址")
        private String imageUrl;

        @ApiModelProperty("语音链接地址")
        private String voiceUrl;

        @ApiModelProperty("视频链接地址")
        private String videoUrl;

        @ApiModelProperty("文本内容/模版内容")
        private String content;
    }

}
