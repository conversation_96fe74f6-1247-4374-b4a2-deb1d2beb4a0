package com.fotile.fss.pojo.dto.tag;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class CategoryInfoViewDTO implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("父级id")
    private Long parentId;

    @ApiModelProperty("类目层级")
    private Integer categoryLevel;

    @ApiModelProperty("类目名称")
    private String categoryName;

    @ApiModelProperty("子类目列表")
    private List<CategoryInfoViewDTO> childList;

    /**
     * 添加数据
     *
     * @param datum
     * @return
     */
    public CategoryInfoViewDTO addData(CategoryInfoViewDTO datum) {
        if (this.childList == null) {
            this.childList = new ArrayList<>();
        }
        this.childList.add(datum);
        return this;
    }
}
