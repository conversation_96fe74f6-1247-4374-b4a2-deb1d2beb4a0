package com.fotile.fss.pojo.dto.tag;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TagDefineInsertDTO implements Serializable {

    @ApiModelProperty("标签id")
    private Long tagInfoId;

    @ApiModelProperty("类型(或：or，且：and)")
    private String defineType;

    @ApiModelProperty("用户属性（互动：interact，固有：fixed）")
    private String userAttribute;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("条件起始时间")
    private Date condStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("条件截至时间")
    private Date condEndDate;

    @ApiModelProperty("用户行为")
    private String userAction;

    @ApiModelProperty("渠道")
    private String channel;

    @ApiModelProperty("时长条件")
    private String durCond;

    @ApiModelProperty("用户停留时长")
    private Integer duration;

    @ApiModelProperty("用户访问次数条件")
    private String visitsCond;

    @ApiModelProperty("用户访问次数")
    @Column(name = "visits_num")
    private Integer visitsNum;

    @ApiModelProperty("数据源")
    private String dataSource;

    @ApiModelProperty("数据字段")
    private String dataField;

    @ApiModelProperty("字段条件（in：包含，notIn：不包含）")
    private String condField;

    @ApiModelProperty("数据值")
    private String dataValue;

    @ApiModelProperty("用户行为限制条件")
    private List<TagRestrictDTO> restrict;
}
