package com.fotile.fss.pojo.dto.ticket;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserTicketDTO implements Serializable {

    @ApiModelProperty("表主键")
    private Long id;

    @ApiModelProperty("活动id")
    private Long activeId;

    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty("公众号客户id")
    private String openId;

    @ApiModelProperty("卡券code")
    private String cardCode;
}
