package com.fotile.fss.pojo.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("")
@Data
public class UserReportViewDTO implements Serializable {

    @ApiModelProperty("在线用户")
    private List<UserReportDTO> onlineUser;

    @ApiModelProperty("粉丝增长趋势")
    private List<UserReportDTO> fansGrowTrend;

    @ApiModelProperty("受众构成/用户类型")
    private UserTypeDataDTO userType;

    @ApiModelProperty("性别分布")
    private UserSexDataDTO userSex;

    @ApiModelProperty("消息类型")
    private UserMsgTypeDataDTO msgTypep;

    @ApiModelProperty("粉丝地理分布（省份）")
    private List<UserReportDTO> localProvince;

    @ApiModelProperty("粉丝地理分布（城市）")
    private List<UserReportDTO> localCity;

    @ApiModelProperty("注册渠道")
    private UserSubSceneDataDTO subScene;

    @ApiModelProperty("参数二维码使用情况")
    private List<UserReportDTO> qrCode;

    @ApiModelProperty("粉丝标签分析")
    private List<UserReportDTO> fansTag;

    @ApiModelProperty("菜单点击数量明细")
    private List<UserReportDTO> menuClick;

    @ApiModelProperty("关键词触发明细")
    private List<UserReportDTO> keyWord;
}
