package com.fotile.fss.pojo.dto.scene;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SceneCrowdCountDTO implements Serializable {

    @ApiModelProperty("公众号id")
    private Long appInfoId;

    @ApiModelProperty("消息类型")
    private String msgType;

    @ApiModelProperty("二维码场景描述")
    private String sceneStr;

    @ApiModelProperty("点击链接地址")
    private String url;

    @ApiModelProperty("回复关键字")
    private String keyWord;

    @ApiModelProperty("客服对话")
    private String customer;

    @ApiModelProperty("匹配类型")
    private String matchType;

    @ApiModelProperty("条件")
    private String visitsCond;

    @ApiModelProperty("次数")
    private Integer visitsNum;

    @ApiModelProperty("起始时间")
    private Date startDate;

    @ApiModelProperty("结束时间")
    private Date endDate;

}
