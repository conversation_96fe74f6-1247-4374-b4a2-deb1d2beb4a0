package com.fotile.fss.pojo.dto.scene;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("查询场景列表参数")
@Data
public class SceneListSelectDTO implements Serializable {

    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;

    @ApiModelProperty("公众号标识")
    @NotNull(message = "公众号标识不能为空")
    private Long appInfoId;

    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty("场景状态（已上线：1，未上线：0）")
    private Integer sceneStatus;

    @ApiModelProperty("场景类型（beforSale：售前，onSale：售中，afterSale：售后）")
    private String sceneType;

    @ApiModelProperty("场景名称")
    private String sceneName;

}
