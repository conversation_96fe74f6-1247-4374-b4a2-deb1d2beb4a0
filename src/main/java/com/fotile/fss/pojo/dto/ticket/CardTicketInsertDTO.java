package com.fotile.fss.pojo.dto.ticket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("新增参数")
@Data
public class CardTicketInsertDTO implements Serializable {

    @ApiModelProperty("公众号id")
    private Long appInfoId;

    @ApiModelProperty("卡券数量")
    private Long cardTivketTotal;

    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty("站点名称")
    private String siteName;

    @ApiModelProperty("售卡礼活动id")
    private Long activeId;

    @ApiModelProperty("渠道Id")
    private Long channelId;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("二维码存放地址")
    private String qrCodeUrl;

    @ApiModelProperty("二维码存放内容")
    private String qrCodeContent;

}
