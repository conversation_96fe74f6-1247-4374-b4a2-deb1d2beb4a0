package com.fotile.fss.pojo.dto.live;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("新增参数")
@Data
public class LiveProductInsertDTO implements Serializable {

    @ApiModelProperty("公众号标识")
    private Long appInfoId;

    @ApiModelProperty("站点id")
    private Long siteId;

    @ApiModelProperty("产品名称")
    @NotNull(message = "产品名称不能为空")
    private String productName;

    @ApiModelProperty("产品图片url")
    @NotNull(message = "产品图片url不能为空")
    private String productImgUrl;

    @ApiModelProperty("购买链接")
    @NotNull(message = "购买链接不能为空")
    private String purchaseUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态（0：禁用，1启用）")
    private Integer state;

}
