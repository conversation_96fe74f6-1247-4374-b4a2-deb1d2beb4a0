package com.fotile.fss.pojo.entity.ticket;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.math.BigDecimal;
import java.util.Date;

/**
 * fss_user_ticket
 *
 * <AUTHOR>

/**
 * Created by Mybatis Generator on 2019/11/05
 */
@Entity
@ApiModel("")
@TableName(value = "fss_user_ticket", schema = "fss")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTicket extends AuditingEntity {

    /**
     * 卡券code
     */
    @ApiModelProperty("卡券code")
    @Column(name = "card_code")
    private String cardCode;

    /**
     * 公众号客户id
     */
    @ApiModelProperty("公众号客户id")
    @Column(name = "open_id")
    private String openId;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    @Column(name = "user_name")
    @FieldEncrypt
    private String userName;

    /**
     * 用户手客户
     */
    @ApiModelProperty("用户手机号")
    @Column(name = "mobile")
    @FieldEncrypt
    private String mobile;

    /**
     * 省份id
     */
    @ApiModelProperty("省份id")
    @Column(name = "province_id")
    private String provinceId;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    @Column(name = "province_name")
    private String provinceName;

    /**
     * 市id
     */
    @ApiModelProperty("市id")
    @Column(name = "city_id")
    private String cityId;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    @Column(name = "city_name")
    private String cityName;

    /**
     * 区di
     */
    @ApiModelProperty("区di")
    @Column(name = "area_id")
    private String areaId;

    /**
     * 区名称
     */
    @ApiModelProperty("区名称")
    @Column(name = "area_name")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @Column(name = "address")
    @FieldEncrypt
    private String address;

    @ApiModelProperty("是否留资（0：否，其它值是）")
    @Column(name = "is_sales_leads")
    private Long isSalesLeads;

    /**
     * 是否参与抽奖（0：不参与，其它值参）
     */
    @ApiModelProperty("是否参与抽奖（0：不参与，其它值参与）")
    @Column(name = "prize_draw")
    private Long prizeDraw;

    /**
     * 交款方式（full:全款，reserve:定金）
     */
    @ApiModelProperty("交款方式（full:全款，reserve:定金）")
    @Column(name = "pay_type")
    private String payType;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    @Column(name = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 购买产品意向
     */
    @ApiModelProperty("购买产品意向")
    @Column(name = "goods_category")
    private String goodsCategory;

    /**
     * 中奖状态（0：未中奖，其它值中奖）
     */
    @ApiModelProperty("中奖状态（0：未中奖，其它值中奖）")
    @Column(name = "draw_stated")
    private Long drawStated;

    /**
     * 奖项等级
     */
    @ApiModelProperty("奖项等级")
    @Column(name = "luck_level")
    private String luckLevel;

    /**
     * 领奖状态（0：未领取，其它值已领取）
     */
    @ApiModelProperty("领奖状态（0：未领取，其它值已领取）")
    @Column(name = "receive_draw_stated")
    private Long receiveDrawStated;

    /**
     * 到场检票状态（0：未检票，其它值已检票）
     */
    @ApiModelProperty("到场检票状态（0：未检票，其它值已检票）")
    @Column(name = "scene_checked")
    private Long sceneChecked;

    /**
     * 检票时间
     */
    @ApiModelProperty("检票时间")
    @Column(name = "ticket_checked_date")
    private Date ticketCheckedDate;

    /**
     * 下单时间
     */
    @ApiModelProperty("是否下单（0：未下单，其它值已下单）")
    @Column(name = "is_pre_order")
    private Long isPreOrder;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    @Column(name = "pre_order_date")
    private Date preOrderDate;


}