package com.fotile.fss.pojo.entity.scene;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * fss_scene_list
 *
 * <AUTHOR>

/**
 * Created by Mybatis Generator on 2020/05/29
 */
@Entity
@ApiModel("")
@TableName(value = "fss_scene_list", schema = "fss")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneList extends AuditingEntity {

    /**
     * 公众号标识
     */
    @ApiModelProperty("公众号标识")
    @Column(name = "app_info_id")
    private Long appInfoId;

    /**
     * 站点id
     */
    @ApiModelProperty("站点id")
    @Column(name = "site_id")
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    @Column(name = "site_name")
    private String siteName;

    /**
     * 场景类型（已上线：1，未上线：0）
     */
    @ApiModelProperty("场景类型（已上线：1，未上线：0）")
    @Column(name = "scene_status")
    private Integer sceneStatus;

    /**
     * 父级id
     */
    @ApiModelProperty("父级id")
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 场景类型（beforSale：售前，onSale：售中，afterSale：售后）
     */
    @ApiModelProperty("场景类型（beforSale：售前，onSale：售中，afterSale：售后）")
    @Column(name = "scene_type")
    private String sceneType;

    /**
     * 场景名称
     */
    @ApiModelProperty("场景名称")
    @Column(name = "scene_name")
    private String sceneName;

    /**
     * 场景描述
     */
    @ApiModelProperty("场景描述")
    @Column(name = "scene_desc")
    private String sceneDesc;

    @ApiModelProperty("是否发送全部粉丝")
    @Column(name = "is_to_all")
    private Boolean isToAll;
}
