package com.fotile.fss.pojo.entity.scene;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * fss_scene_process
 *
 * <AUTHOR>

/**
 * Created by Mybatis Generator on 2020/05/29
 */
@Entity
@ApiModel("")
@TableName(value = "fss_scene_process", schema = "fss")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneProcess extends BaseEntity {

    /**
     * 场景id
     */
    @ApiModelProperty("场景id")
    @Column(name = "scene_id")
    private Long sceneId;

    /**
     * 渠道类型（优先：priority，固定：fixed）
     */
    @ApiModelProperty("渠道类型（优先：priority，固定：fixed）")
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 发送类型（48小时互动：custom，模版消息：template，群发消息: news）
     */
    @ApiModelProperty("发送类型（48小时互动：custom，模版消息：template，群发消息: sendAll）")
    @Column(name = "send_type")
    private String sendType;

    /**
     * 定时类型（1:绝对定时；2：相对时间）
     */
    @ApiModelProperty("定时类型（1:绝对定时；2：相对时间）")
    @Column(name = "cron_type")
    private String cronType;

    /**
     * 发送优先级（最高优先级：1）
     */
    @ApiModelProperty("发送优先级（最高优先级：1）")
    @Column(name = "sorted")
    private Integer sorted;
}
