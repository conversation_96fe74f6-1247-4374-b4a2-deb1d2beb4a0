package com.fotile.fss.pojo.entity.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@ApiModel("地区字典表")
@TableName(value = "fss_em_dict", schema = "fss")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmDict extends BaseEntity {
    /**
     * 上级id
     */
    @ApiModelProperty("上级id")
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    @Column(name = "em_type")
    private String emType;

    /**
     * 地区代码
     */
    @ApiModelProperty("地区代码")
    @Column(name = "code")
    private String code;

    /**
     * 地区名称
     */
    @ApiModelProperty("地区名称")
    @Column(name = "name")
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    @Column(name = "sorted")
    private Integer sorted;
}