package com.fotile.fss.pojo.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * fss_user_opr
 *
 * <AUTHOR>

/**
 * Created by Mybatis Generator on 2020/04/04
 */
@Entity
@ApiModel("")
@TableName(value = "fss_user_opr", schema = "fss")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOpr extends BaseEntity {

    /**
     * 公众号标识（预留）
     */
    @ApiModelProperty("公众号标识（预留）")
    @Column(name = "app_info_id")
    private Long appInfoId;

    /**
     * 分公司id
     */
    @ApiModelProperty("分公司id")
    @Column(name = "site_id")
    private Long siteId;

    /**
     * 用户的微信公众号id
     */
    @ApiModelProperty("用户的微信公众号id")
    @Column(name = "open_id")
    private String openId;

    /**
     * 操作类型
     */
    @ApiModelProperty("操作类型")
    @Column(name = "opr_type")
    private String oprType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Column(name = "created_date")
    private Date createdDate;
}