package com.fotile.fss.pojo.mapper;

import com.fotile.fss.pojo.dto.ticket.UserTicketInsertDTO;
import com.fotile.fss.pojo.dto.ticket.UserTicketSelectDTO;
import com.fotile.fss.pojo.entity.ticket.UserTicket;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserTicketMapper {

    UserTicketMapper INSTANCE = Mappers.getMapper(UserTicketMapper.class);

    UserTicket UserTicketInsertDTOTOUserTicket(UserTicketInsertDTO userTicketInsertDTO);

    UserTicketSelectDTO UserTicketTOUserTicketUpdateAndSelectDTO(UserTicket userTicket);

    UserTicketSelectDTO UserTicketInsertDTOTOUserTicketSelectDTO(UserTicketInsertDTO userTicketInsertDTO);
}
