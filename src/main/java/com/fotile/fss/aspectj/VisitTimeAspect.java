package com.fotile.fss.aspectj;

import com.fotile.framework.web.Result;
import com.fotile.fss.annotation.VisitTime;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class VisitTimeAspect {

    // 配置织入点
    @Pointcut("@annotation(com.fotile.fss.annotation.VisitTime)")
    public void visitTimePointCut()
    {
    }

    @Before("visitTimePointCut()")
    public synchronized void beforeRequestFlat(final JoinPoint joinPoint) throws Exception {
        // 获得注解
        VisitTime visitTime = getAnnotationLog(joinPoint);
        if (visitTime == null)
        {
            return;
        }
        long start = System.currentTimeMillis()/1000;
        if (null != System.getProperty("timeFlag")) {//表示这个接口至少被调用一次
            long timeFlag = Long.parseLong(System.getProperty("timeFlag"));
            if (timeFlag + Long.parseLong(visitTime.value()) < start) {//取出来第一次调用的时间戳 加上你规定的时间毫秒数
            //这样如果小于start表示 超过你规定5分钟调用一次的需求 所以需要重新把timeFlag 在刷新一下时间进入下一个3小时算
                System.setProperty("timeFlag", start + "");
            } else {
                throw new RuntimeException("方法正在执行中，请稍后再试");
            }
        } else {//如果else 表示接口第一次调用记录一个时间戳
            System.setProperty("timeFlag", start + "");
        }

    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private VisitTime getAnnotationLog(JoinPoint joinPoint)
    {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null)
        {
            return method.getAnnotation(VisitTime.class);
        }
        return null;
    }
}
