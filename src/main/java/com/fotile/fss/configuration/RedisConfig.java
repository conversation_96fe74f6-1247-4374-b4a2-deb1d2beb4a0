package com.fotile.fss.configuration;

import com.fotile.fss.live.webSocket.RedisMessageListener;
import com.fotile.fss.receive.RedisKeyExpirationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.Duration;

/**
 * 配置redis开启
 *
 * @return
 */
@Configuration
public class RedisConfig {

    /**
     * 这里使用RedisTemplate<String, Object>的方式代替RedisTemplate<Object, Object>避免使用时进行代码转换
     *
     * @param factory
     * @return
     */
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<String, Object>();
        template.setConnectionFactory(factory);

        // String序列化方式
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        // jackson2JsonRedisSerializer的序列化方式
        JdkSerializationRedisSerializer jackson2JsonRedisSerializer = new JdkSerializationRedisSerializer();

        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson2JsonRedisSerializer的序列化方式也可以采用jackson（jackson2JsonRedisSerializer）
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson的序列化方式也可以采用jackson（jackson2JsonRedisSerializer）
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory factory) {
        return new StringRedisTemplate(factory);
    }

    @Bean(name = "redisCacheManager")
    public RedisCacheManager initRedisCacheManager(RedisConnectionFactory factory) {
        //redis加锁的写入器
        RedisCacheWriter writer = RedisCacheWriter.lockingRedisCacheWriter(factory);
        //启动redis缓存的默认配置
        RedisCacheConfiguration configuration = RedisCacheConfiguration.defaultCacheConfig();
        //设置JDK序列化器
        configuration = configuration.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new JdkSerializationRedisSerializer()));
        //禁止前缀
        configuration = configuration.disableKeyPrefix();
        //设置10Min超时
        configuration = configuration.entryTtl(Duration.ofMinutes(10));
        //创建redis缓存管理器
        RedisCacheManager redisCacheManager = new RedisCacheManager(writer, configuration);
        return redisCacheManager;
    }

    @Bean(name = "container")
    public RedisMessageListenerContainer container(RedisConnectionFactory factory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        LettuceConnectionFactory lettuceConnectionFactory = (LettuceConnectionFactory) factory;
        //设置存储的节点
        lettuceConnectionFactory.setDatabase(0);
        container.setConnectionFactory(lettuceConnectionFactory);
        return container;
    }

    @Bean
    public RedisKeyExpirationListener keyExpirationListener(RedisMessageListenerContainer container) {
        return new RedisKeyExpirationListener(container);
    }

    @Bean
    public MessageListenerAdapter listenerAdapter(RedisMessageListener receiver) {
        return new MessageListenerAdapter(receiver);
    }

    /**
     * 用于spring session，防止每次创建一个线程
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor springSessionRedisTaskExecutor() {
        ThreadPoolTaskExecutor springSessionRedisTaskExecutor = new ThreadPoolTaskExecutor();
        springSessionRedisTaskExecutor.setCorePoolSize(10);
        springSessionRedisTaskExecutor.setMaxPoolSize(200);
        springSessionRedisTaskExecutor.setKeepAliveSeconds(180);
        springSessionRedisTaskExecutor.setQueueCapacity(3000);
        springSessionRedisTaskExecutor.setThreadNamePrefix("Spring session redis executor thread: ");
        return springSessionRedisTaskExecutor;
    }

}
