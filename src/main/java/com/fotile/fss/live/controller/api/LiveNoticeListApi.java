package com.fotile.fss.live.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.fss.business.service.common.CommonService;
import com.fotile.fss.live.service.LiveNoticeListService;
import com.fotile.fss.pojo.dto.live.LiveNoticeListSelectDTO;
import com.fotile.fss.pojo.dto.live.LiveNoticeListSetDTO;
import com.fotile.fss.pojo.entity.live.LiveNoticeList;
import com.fotile.fss.wechat.config.WeiXinHttps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(value = "直播预告接口")
@RequestMapping("/api/open/live")
public class LiveNoticeListApi extends BaseController {

    @Autowired
    private LiveNoticeListService liveNoticeListService;

    @Autowired
    private CommonService commonService;

    @RequestMapping(value = "/v2.0/notice/list", method = RequestMethod.POST)
    @ApiOperation("查询直播预告列表")
    public Result<?> list(@RequestBody @Valid LiveNoticeListSelectDTO liveNoticeListSelectDTO) {
        liveNoticeListSelectDTO.setStoreId(commonService.getStoreId());
        return success(liveNoticeListService.selectByLiveNoticeList(liveNoticeListSelectDTO));
    }

    @RequestMapping(value = "/v2.0/notice/set", method = RequestMethod.POST)
    @ApiOperation("设置直播预告")
    public Result<LiveNoticeListSelectDTO> set(@RequestBody LiveNoticeListSetDTO liveNoticeListSetDTO) throws Exception {
        String locationAddress = null;
        String url = "https://api.map.baidu.com/reverse_geocoding/v3/?ak=w6P5OguoqGUGpVpdcolQpq6HC7Qhhrj4&output=json&coordtype=wgs84ll&" +
                "location=" + liveNoticeListSetDTO.getLatitude() + "," + liveNoticeListSetDTO.getLongitude();
        // 根据经纬度获取地理位置信息
        String response = WeiXinHttps.httpsSend(url, "GET", null);
        JSONObject resJson = JSON.parseObject(response);
        JSONObject result = resJson.getJSONObject("result");
        if (result.containsKey("addressComponent")) {
            JSONObject addressComponent = result.getJSONObject("addressComponent");
            if (addressComponent.containsKey("city")) {
                locationAddress = addressComponent.getString("city");
            }
        }

        if (StringUtils.isEmpty(locationAddress)) {
            return failure("经纬度信息不正确");
        }
        liveNoticeListSetDTO.setLocationAddress(locationAddress);
        int num = liveNoticeListService.setNoticeList(liveNoticeListSetDTO);
        return success(num == 1 ? "设置成功" : "设置失败");
    }

    @RequestMapping(value = "/v2.0/notice/subscribe", method = RequestMethod.GET)
    @ApiOperation("查询已设置的预约直播")
    public Result<List<LiveNoticeList>> subscribeList(@ApiParam("storeId") Long storeId) {
        LiveNoticeListSetDTO liveNoticeListSetDTO = new LiveNoticeListSetDTO();
        liveNoticeListSetDTO.setStoreId(storeId);
        liveNoticeListSetDTO.setState(0);
        return success(liveNoticeListService.selectList(liveNoticeListSetDTO));
    }

}