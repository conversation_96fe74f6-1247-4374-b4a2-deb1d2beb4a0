package com.fotile.fss.live.controller.backend;

import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.fotile.fss.business.service.common.CommonService;
import com.fotile.fss.live.service.LiveNoticeUserService;
import com.fotile.fss.pojo.dto.live.LiveNoticeUserSelectDTO;
import com.fotile.fss.pojo.entity.live.LiveNoticeUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@Api(value = "直播预约backend接口")
@RequestMapping("/api/live/noticeUser")
public class LiveNoticeUserController extends BaseController {

    @Autowired
    private LiveNoticeUserService liveNoticeUserService;

    @Autowired
    private CommonService commonService;

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation("修改")
    public Result<LiveNoticeUser> update(@RequestBody @Valid LiveNoticeUser liveNoticeUser) {
        liveNoticeUserService.updateById(liveNoticeUser);
        return success("修改成功");
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation("删除")
    public Result<LiveNoticeUser> delete(@ApiParam("id") Long id) {
        liveNoticeUserService.deleteById(id);
        return success("删除成功");
    }

    @RequestMapping(value = "/select", method = RequestMethod.POST)
    @ApiOperation("查询已报名直播预告客户列表")
    public Result<?> select(@RequestBody @Valid LiveNoticeUserSelectDTO selectDTO) {
        // 获取门店权限
        selectDTO.setStoreIds(commonService.listStoreId());
        return success(liveNoticeUserService.selectLiveNoticeUserList(selectDTO));
    }
}