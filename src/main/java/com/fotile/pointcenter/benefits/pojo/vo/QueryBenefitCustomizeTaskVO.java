package com.fotile.pointcenter.benefits.pojo.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 自定义任务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/11/7 14:37
 */
@Data
public class QueryBenefitCustomizeTaskVO implements Serializable {
    /**
     * 自定义权益任务ID,PK,AI
     */
    private Long id;

    /**
     * 自定义权益任务编码
     */
    private String code;

    /**
     * 自定义权益金任务名称
     */
    private String name;

    /**
     * 自定义权益金任务描述
     */
    private String descriptions;

    /**
     * 分公司orgId
     */
    private Long companyOrgId;

    /**
     * 分公司权益金方向(-1:减少,1:增加)
     */
    private Integer companyDirection;

    /**
     * 设计师会员ID
     */
    private Long memberId;

    /**
     * 设计师权益金方向(-1:减少,1:增加)
     */
    private Integer memberDirection;

    /**
     * 排序(数字越大越考前)
     */
    private Long sort;
}
