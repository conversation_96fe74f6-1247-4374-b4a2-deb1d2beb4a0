package com.fotile.pointcenter.benefits.pojo.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 设计师权益金交易结果实体类（获取/支出）
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/10/24 14:41
 */
@Data
public class DesignerExchangeResultDTO implements Serializable {
    /**
     * 设计师权益金账户ID
     */
    private Long accountId;

    /**
     * 设计师俱乐部会员ID
     */
    private Long memberId;

    /**
     * 总收入
     */
    private Long totalIncome;

    /**
     * 总支出
     */
    private Long totalPay;

    /**
     * 当前可用
     */
    private Long available;

    /**
     * 获取/支出权益金值
     */
    private Long value;
}
