package com.fotile.pointcenter.benefits.pojo.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BenefitsCustomizeTask implements Serializable {
    /**
     * 自定义权益任务ID,PK,AI
     */
    private Long id;

    /**
     * 自定义权益任务编码
     */
    private String code;

    /**
     * 自定义权益金任务名称
     */
    private String name;

    /**
     * 自定义权益金任务描述
     */
    private String descriptions;

    /**
     * 分公司orgId
     */
    private Long companyOrgId;

    /**
     * 分公司权益金方向(-1:减少,1:增加)
     */
    private Integer companyDirection;

    /**
     * 设计师会员ID
     */
    private Long memberId;

    /**
     * 设计师权益金方向(-1:减少,1:增加)
     */
    private Integer memberDirection;

    /**
     * 排序(数字越大越考前)
     */
    private Long sort;

    /**
     * 记录状态(0未删除,id已删除)
     */
    private Long isDeleted;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 修改人ID
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedDate;
}