package com.fotile.pointcenter.benefits.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;

/**
 * 更新分公司账户权益金值
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/13 15:19
 */
@Data
public class UpdateCompanyBenefitAmountDTO implements Serializable {
    /**
     * PK,AI
     */
    private Long accountId;

    /**
     * 交易方向(-1:减少,1:增加 枚举BenefitDirectionEnum.getValue)
     */
    private Integer direction;

    /**
     * 交易值
     */
    private Long amount;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名[加密]
     */
    @FieldEncrypt
    private String operatorName;
}
