package com.fotile.pointcenter.benefits.mq.dto.rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/9/27 11:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BenefitTaskRuleMemberGrade {
    /**
     * 会员等级名称
     */
    private String name;

    /**
     * 会员等级值(1:注册会员; 2:认证会员; 默认1)
     */
    private Integer value;
}
