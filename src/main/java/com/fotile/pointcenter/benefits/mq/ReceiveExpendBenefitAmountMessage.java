package com.fotile.pointcenter.benefits.mq;

import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.pointcenter.benefits.mq.dto.SendBenefitAmountDTO;
import com.fotile.pointcenter.benefits.pojo.entity.BenefitsSendRecord;
import com.fotile.pointcenter.benefits.service.BenefitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 监听消费权益金
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/13 9:59
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "fourth")
@Slf4j
public class ReceiveExpendBenefitAmountMessage {
    @Resource
    private BenefitService benefitService;

    /**
     * 权益金消费
     */
    @StreamListener(BenefitsMgrChannel.EXPEND_BENEFIT_AMOUNT_TASK_INPUT)
    public void receive(Message<SendBenefitAmountDTO> message) {
        SendBenefitAmountDTO info = message.getPayload();
        BenefitsSendRecord record = null;
        try {
            record = benefitService.getBenefitsExpendRecord(info, "expend: queue message consume.");
            String error = info.check();
            if (StringUtils.isNotBlank(error)) {
                log.error("ReceiveExpendBenefitAmountMessage.receive error parameters : " + error);
                //参数错误记录日志
                record.setSucceed(0);
                record.setExecResults("执行消费失败：" + error);
                benefitService.insertBenefitSendRecord(record);
            } else {
                //记录发送记录(默认成功)
                record.setSucceed(1);
                record.setExecResults("执行消费成功！");
                benefitService.insertBenefitSendRecord(record);

                if (record.getId() != null && record.getId() > 0) {
                    //花费权益金
                    info.setSendRecordId(record.getId());

                    //消费权益金
                    benefitService.expendBenefitAmountHandler(info);
                }
            }
        } catch (Exception ex) {
            String error = ex.getMessage();
            log.error("ReceiveExpendBenefitAmountMessage.receive exception : " + error, ex);

            if (record != null && info.getSendRecordId() != null && info.getSendRecordId() > 0) {
                //更新结果
                record.setSucceed(0);
                record.setExecResults("执行消费失败：" + error);
                benefitService.updateBenefitSendRecordById(record);
            }
        }
    }
}
