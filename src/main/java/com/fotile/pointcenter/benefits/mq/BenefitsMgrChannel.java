package com.fotile.pointcenter.benefits.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * 权益金管理消息队列
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/7/29 14:26
 */
@Component
public interface BenefitsMgrChannel {
    /* --------------- 初始化设计师权益金账户kafka配置 start --------------- */

    //消费
    String INIT_DESIGNER_BENEFIT_ACCOUNT_INPUT = "init_designer_benefit_account_input";

    @Input(INIT_DESIGNER_BENEFIT_ACCOUNT_INPUT)
    SubscribableChannel disposeInitDesignerBenefitAccountInput();


    /* --------------- 初始化设计师权益金账户kafka配置 end --------------- */

    /* --------------- 权益金消费kafka配置 start --------------- */

    //获取权益金(消费)
    String SEND_BENEFIT_AMOUNT_TASK_INPUT = "send_benefit_amount_task_input";

    @Input(SEND_BENEFIT_AMOUNT_TASK_INPUT)
    SubscribableChannel sendBenefitAccountInput();

    //生产
    String SEND_BENEFIT_AMOUNT_TASK_OUTPUT = "send_benefit_amount_task_output";

    @Output(SEND_BENEFIT_AMOUNT_TASK_OUTPUT)
    MessageChannel sendBenefitTaskMassage();


    //使用权益金(消费)
    String EXPEND_BENEFIT_AMOUNT_TASK_INPUT = "expend_benefit_amount_task_input";

    @Input(EXPEND_BENEFIT_AMOUNT_TASK_INPUT)
    SubscribableChannel expendBenefitAccountInput();

    /* --------------- 权益金消费kafka配置 end --------------- */

    //事件产生
    String BENEFIT_EVENT_PRODUCER="benefit_event_producer";

    @Output(BENEFIT_EVENT_PRODUCER)
    MessageChannel benefitEventProducer();
}
