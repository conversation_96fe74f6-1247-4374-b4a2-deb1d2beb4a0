package com.fotile.pointcenter.client.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 积分变动类型实体
 *
 * <AUTHOR>
 */
@Data
public class QueryByParamsOutDto implements Serializable {
    /**
     * 字典id
     */
    private Long id;

    /**
     * 数据字典具体的值编码
     */
    private String valueCode;

    /**
     * 分类编码
     */
    private String typeCode;

    /**
     * 数据字典下拉框显示的名称
     */
    private String valueName;

    /**
     * 是否有效
     */
    private Integer active;

    /**
     * 排序
     */
    private Integer sort;
}
