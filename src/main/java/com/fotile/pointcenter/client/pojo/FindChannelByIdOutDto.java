package com.fotile.pointcenter.client.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FindChannelByIdOutDto {
    /**
     * 渠道id
     */
    private Long id;

    /**
     * 渠道编码
     */
    private String code;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 渠道类型：1：App；2：H5；3：小程序；4：PC
     */
    private Byte type;

    /**
     * 备注
     */
    private String note;

    /**
     * 状态,0:禁用；1：启用
     */
    private byte status = 1;

    /**
     * 父渠道id
     */
    private Long parentId;

    /**
     * 同名称，用于建立索引检索
     */
    private String fullPathId;

    /**
     * XXA-XXB-XXC 用减号隔离
     */
    private String fullPathName;

    /**
     * 层级从1开始
     */
    private Integer level;
}
