package com.fotile.pointcenter.client.pojo;

import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Entity;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 公司信息表
 */
@Data
@Entity
public class CompanyEntity extends AuditingEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    @NotEmpty(message = "公司编码不能为空")
    private String code;

    /**
     * 名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String name;

    /**
     * 组织机构id
     */
    private Long orgId;

    /**
     * 地址-省id
     */
    @NotNull(message = "地址-省不能为空")
    private Long provicenId;

    /**
     * 地址-省名称
     */
    private String provicenName;

    /**
     * 地址-市id
     */
    @NotNull(message = "地址-市不能为空")
    private Long cityId;

    /**
     * 地址-市名称
     */
    private String cityName;

    /**
     * 地址-区id
     */
    @NotNull(message = "地址-区不能为空")
    private Long countyId;

    /**
     * 地址-区名称
     */
    private String countyName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 总经理id
     */
    private Integer leaderId;

    /**
     * 总经理名称
     */
    @FieldEncrypt
    @Transient
    private String leaderName;

    /**
     * 备注
     */
    private String note;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 所属部门id
     */
    @Transient
    private Long parentId;

    /**
     * 所属部门名称
     */
    @Transient
    private String parentName;

    /**
     * 留资通知邮箱
     */
    private String email;

    /**
     * 所属大区;1:华东；2：华南；3：华西；4：华中；5：华北；6：中原
     */
    private Integer area;

    /**
     * 选择区域数组[{provicenId:省id,cityId:市id,countyId:区县id}]
     */
    private List<CompanyAreaEntity> companyAreaList;

    /**
     * 部门列表中 -> 上级部门
     */
    private String fullPathName;

    /**
     * 部门列表中 -> 上级部门id
     */
    private String fullPathId;

    /**
     * 是否同步至DRP，1：不同步；2：同步至总部DRP，3：同步至事业上DRP
     */
    private String isToDrp;

    /**
     * 订单传送至DRP状态：1：打开；2：审核 默认值1
     */
    private String toDrpStage;

    /**
     * 家装公司一级审批岗位
     */
    private String firstApproval;

    /**
     * 家装公司二级审批岗位
     */
    private String secondApproval;

    /**
     * 截单时间
     */
    private String cutTime;

    /**
     * 大区编码
     */
    private String areaCode;

    /**
     * 大区名称
     */
    private String areaName;

    /**
     * 订购时间提前天数(截单时间)
     */
    private Integer orderAhead;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 是否开启线索审核 0：关闭；1：开启
     */
    private Integer cluesOpenExamine;

    /**
     * 线索未跟进天数提醒阈值
     */
    private Integer cluesRemind;

    /**
     * 是否开启手机号检查 0:否；1：是
     */
    private Integer cluesCheckPhone;

    /**
     * 处理结果 1：提示并保存成功；2：提示并保存失败
     */
    private Integer cluesProcessResult;

    /**
     * 查重范围 1：门店；2：客户；3：公司
     */
    private Integer cluesCheckScope;

    /**
     * 查重时段 1：7天以内；2：30天以内；3：90天以内；4:180天以内
     */
    private Integer cluesCheckPeriod;

    /**
     * 跟进状态 1：未跟进；2：已跟进；3：已进店；4:已成交；5：已丢单
     */
    private String cluesFollowStatus;

    /**
     * 异业三工一级审批岗位
     */
    private String diffIndustryFirstApproval;

    /**
     * 异业三工二级审批岗位
     */
    private String diffIndustrySecondApproval;

    /**
     * B2B业务类型订单是否拆行 0：否；1：是
     */
    private Integer isBreakUp;

    /**
     * 安装单号不检查订单类型,多个值逗号分隔
     */
    private String orderTypeCheckInstall;
    /**
     * 线索下发是否短信通知顾客 0:否；1：是
     */
    private Integer isSms;

    /**
     * 线索超期投入公海天数
     */
    private Integer cluesOverdue;

    /**
     * 是否进行线索跟进审核 0：否；1：是
     */
    private Integer cluesFollow;

    /**
     * 是否进行线索丢掉审核 0：否；1：是
     */
    private Integer cluesLose;

    /**
     * 安装单号检查接口 0:drp；1：云管理
     */
    private Integer installCheck;

    /**
     * 是否开启财务审核 1开启 0不开启
     */
    private Integer financeAuditFlag;

    /**
     * 定金订单是否同步到drp,0不同，1同步
     */
    private Integer depositOrderToDrp;

    /**
     * 顾客画像0禁用 1启用
     */
    private Integer customerPortraitFlag;

    /**
     * 1开启快捷订单，0不开启
     */
    private Integer quickOrderFlag;

    /**
     * 线索引导开关 0否 1是
     */
    private Integer clueGuideFlag;

    /**
     * 仅店长修改线索手机号和地址 0否 1是
     */
    private Integer clueSmPhoneAddressFlag;

    /**
     * 是否同步订单到cem,1是，0否
     */
    private Integer syncOrderCem;

    /**
     * 是否使用小区主记录,1是，0否
     */
    private Integer useMasterVillage;

    /**
     * 定金订单超期投入公海1是 0否
     */
    private Integer depositOrderCluesOverdue;

    /**
     * 是否开启排行榜数据脱敏: 默认0 - 不开启 1-开启
     */
    private Integer isRankDesensitize;
}
