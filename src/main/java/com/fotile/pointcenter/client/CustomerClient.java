package com.fotile.pointcenter.client;

import com.fotile.framework.web.Result;
import com.fotile.pointcenter.client.pojo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "customer-center")
public interface CustomerClient {
    @GetMapping("/api/customer/queryCustomerDetailById")
    Result queryCustomerDetailById(@RequestParam("id") Long id);

    @GetMapping("/api/customer/api/open/queryCustomerDetailById")
    Result queryCustomerDetailByIdOpen(@RequestParam("id") Long id);

    @GetMapping("/m/api/customer/queryCustomerDetailById")
    Result queryCustomerDetailByIdForC(@RequestParam("id") Long id);

    @PostMapping(value = "/api/open/customer/queryCustomerIdByPhones", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result queryCustomerIdByPhones(@RequestBody List<String> list);

    @GetMapping("/api/open/grade/queryAllGrade")
    Result<List<QueryAllGradeOutDto>> queryAllGrade();

    @PostMapping(value = "/api/customer/api/open/updateGrade", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result updateGrade(@RequestBody List<UpdateGradeIdAnduserIdDto> list);

    /**
     * 写社区会员操作日志
     * /api/operatorFollowCustomerLog/api/open/insert
     */
    @RequestMapping(value = "/api/operatorFollowCustomerLog/insert", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CommunityMemberLogDto> insertCommunityMemberLog(@RequestBody @Valid CommunityMemberLogDto log);

    /**
     * 写中台会员操作日志
     */
    @RequestMapping(value = "/api/open/operatorLog/insertOperatorLog", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result insertMemberLog(@RequestBody MemberLogDto log);

    /**
     * 根据手机号查询客户id
     */
    @RequestMapping(value = "/api/customer/queryCustomerIdByPhone", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<Long> queryCustomersByPhone(@RequestParam("phone") String phone);

    /**
     * 根据账号id查询顾客信息
     */
    @RequestMapping(value = "/api/m/customer/queryCustomerInfoByUserEntity", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<CustomerInfo> queryCustomerInfoByUserEntity(@RequestParam("userEntity") String userEntity);

    /**
     * 根据手机号列表查询客户信息
     */
    @RequestMapping(value = "/api/open/customer/queryCustomerInfoByPhones", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<CustomerInfoVto>> queryCustomerInfoByPhones(@RequestBody List<String> phones);

    /**
     * 根据ids获取顾客信息
     */
    @RequestMapping(value = "/api/open/customer/getCustomerInfosByIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<CustomerInfoVto>> getCustomerInfosByIds(@RequestBody List<Long> ids);
}
