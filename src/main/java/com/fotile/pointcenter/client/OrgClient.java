package com.fotile.pointcenter.client;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import com.fotile.pointcenter.client.pojo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 外部接口 -> OrgCenter
 *
 * <AUTHOR>
 */
@FeignClient(value = "org-center")
public interface OrgClient {
    /**
     * 获取渠道分页列表
     *
     * @param page   页码
     * @param size   页大小
     * @param status 状态,0:禁用；1：启用
     * @return 渠道分页列表
     */
    @GetMapping("/api/open/channel/findChannelAll")
    Result<PageInfo<FindChannelAllOutDto>> findChannelAll(@Valid @RequestParam("page") Integer page,
                                                          @RequestParam("size") Integer size,
                                                          @RequestParam("status") Integer status);

    /**
     * 获取渠道明细
     *
     * @param id 通过渠道id获取渠道明细
     * @return 渠道
     */
    @GetMapping(value = "/api/channel/findById")
    Result<FindChannelByIdOutDto> findById(@Valid @RequestParam("id") Long id);

    /**
     * 查询公司详情
     *
     * @param id 公司id
     * @return 公司信息
     */
    @GetMapping(value = "/api/company/findById")
    Result<FindCompanyByIdOutDto> findCompanyById(@RequestParam("id") Long id);

    /**
     * 查询公司详情open
     *
     * @param ids 公司ids
     * @return 公司信息
     */
    @GetMapping(value = "/api/company/api/open/findByIds")
    Result<List<FindCompanyByIdOutDto>> openApiFindByIds(@RequestParam("ids") List<Long> ids);

    /**
     * 通过渠道code，获取渠道明细
     *
     * @param code 通过渠道id获取渠道明细
     * @return 渠道
     */
    @RequestMapping(value = "/findByCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<FindChannelByIdOutDto> findByCode(@Valid @RequestParam("code") String code);

    //根据公司编码集合查询公司信息
    @RequestMapping(value = "/api/company/api/open/findByCodes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<CompanyEntity>> findByCodes(@RequestBody FindCompanyByCodesInDto findCompanyByCodesInDto);

    /**
     * 据公司名称集合查询公司信息
     */
    @RequestMapping(value = "/api/company/findByNames", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<CompanyEntity>> findByNames(@RequestBody List<String> nameList);


    @RequestMapping(value = "/api/designer/club/member/api/open/bind-benefit-account", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<Integer> bindDesignerClubMemberBenefitAccount(@RequestBody BindDesignerBenefitAccountDTO request);

    @GetMapping(value = "/api/org/api/open/getCompanyOrgInfo")
    Result<CompanyOrgInfo> getCompanyOrgInfo(@RequestParam(value = "companyId", required = false) Long companyId,
                                                    @RequestParam(value = "companyOrgId", required = false) Long companyOrgId,
                                                    @RequestParam(value = "companyCode", required = false) String companyCode);
}
