package com.fotile.pointcenter.point.Util;

import com.alibaba.fastjson.JSONObject;
import com.fotile.pointcenter.point.mq.PointCenterChannel;
import com.fotile.pointcenter.point.mq.pojo.DisposeSendMsgDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SendMsgUtil {

    @Resource(name = PointCenterChannel.DISPOSE_SEND_MSG_OUTPUT)
    private MessageChannel pointCenterChannel;

//    /**
//     * 发送业务员通用 站内信
//     */
//    public void sendCommonMessage(Long chargeUserId, HashMap<String, Object> map, String nodeCode) {
//        UserAuthor userEntityExtend = null;
//        try {
//            userEntityExtend = userEntityClientService.apiOpenFindUserEntityBySalesmanId(chargeUserId).getData();
//        } catch (Exception e) {
//            throw new BusinessException("获取业务员失败");
//        }
//        DisposeSendMsgDto disposeSendMsgDto = new DisposeSendMsgDto();
//        disposeSendMsgDto.setNodeCode(nodeCode);
//        disposeSendMsgDto.setHashMap(map);
//        disposeSendMsgDto.setUserType("2");
//        disposeSendMsgDto.setSendType("1");
//        if (userEntityExtend != null && !StringUtils.isEmpty(userEntityExtend.getUserId())) {
//            disposeSendMsgDto.setUserIdList(Arrays.asList(userEntityExtend.getUserId().split(",")).stream().map(s -> s.trim()).collect(Collectors.toList()));
//            String content = JSONObject.toJSON(disposeSendMsgDto).toString();
//            userCluesMsgChannel.send(MessageBuilder.withPayload(content).build());
//        }
//    }

    /**
     * 发送短信通用方法 手机短信
     *
     * @param map          Template parameters
     * @param phone        User phone
     * @param templateCode SMS Template ID
     */
    public void sendSMS(HashMap map, String phone, String templateCode) {
        try {
            DisposeSendMsgDto disposeSendMsgDto = new DisposeSendMsgDto();
            disposeSendMsgDto.setNodeCode(templateCode);

            disposeSendMsgDto.setHashMap(map);
            disposeSendMsgDto.setUserType("2");
            disposeSendMsgDto.setSendType("3");
            disposeSendMsgDto.setUserIdList(Arrays.stream(phone.split(",")).map(String::trim).collect(Collectors.toList()));
            String content = JSONObject.toJSON(disposeSendMsgDto).toString();
            pointCenterChannel.send(MessageBuilder.withPayload(content).build());
        } catch (Exception ex) {
            //ex.printStackTrace();
            log.error("POINTCENTER SENDSMS ERROR:" + ex.getMessage());
        }
    }
}
