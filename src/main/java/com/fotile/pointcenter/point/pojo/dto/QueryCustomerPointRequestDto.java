package com.fotile.pointcenter.point.pojo.dto;

import lombok.Data;

/**
 * 查询会员积分余额实体
 */
@Data
public class QueryCustomerPointRequestDto {
    /**
     * 会员id
     */
    private Long customerId;

    /**
     * 会员名称
     */
    private String customerName;

    /**
     * 会员手机号
     */
    private String customerPhone;

    /**
     * 会员等级id
     */
    private Long gradeId;

    /**
     * 会员开始成长值
     */
    private Long growthValueStart;

    /**
     * 会员结束成长值
     */
    private Long growthValueEnd;

    /**
     * 会员开始积分值
     */
    private Long availableStart;

    /**
     * 会员结束积分值
     */
    private Long availableEnd;

    /**
     * 最后编辑开始时间
     */
    private String modifiedDateStart;

    /**
     * 最后编辑结束时间
     */
    private String modifiedDateEnd;

    /**
     * 当前页
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;

    /**
     * 排序
     */
    private String order;

    /**
     * 是否需要获取积分清零值(默认false)
     */
    private Boolean needCleanValue = false;


    /**
     * 是否需要inner join grow_customer表
     */
    private Boolean needJoinGrowCustomerTable = false;


    public void initQuery() {
        if (this.gradeId != null && this.gradeId > 0) {
            this.needJoinGrowCustomerTable = true;
        } else if (this.growthValueStart != null && this.growthValueStart > 0) {
            this.needJoinGrowCustomerTable = true;
        } else if (this.growthValueEnd != null && this.growthValueEnd > 0) {
            this.needJoinGrowCustomerTable = true;
        }

    }
}
