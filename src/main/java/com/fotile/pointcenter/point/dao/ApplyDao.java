package com.fotile.pointcenter.point.dao;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.pointcenter.point.pojo.dto.AuditPointApplyDto;
import com.fotile.pointcenter.point.pojo.dto.QueryPointApplyDto;
import com.fotile.pointcenter.point.pojo.entity.PointApply;
import com.fotile.pointcenter.point.pojo.entity.PointPicture;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.pointcenter.point.dao
 * @date 2020/5/26 11:09
 */
public interface ApplyDao {
    /*-----------------------------------20200610迭代----------------------------------**/

    /**
     * 获取积分申请总记录数
     *
     * @param query 查询参数
     * @return int
     */
    int getApplyRecordTotalRows(@Param("query") QueryPointApplyDto query);

    /**
     * 获取积分申请分页数据
     *
     * @param query    查询参数
     * @param pageInfo 分页信息
     * @return page of PointApply
     */
    List<PointApply> queryApplyRecord(@Param("query") QueryPointApplyDto query,
                                      @Param("pageInfo") PageInfo<?> pageInfo);

    /**
     * 获取积分申请明细
     *
     * @param id 申请记录ID
     * @return PointApply
     */
    PointApply getApplyRecord(@Param("id") Long id);

    /**
     * 获取积分申请图片集合
     *
     * @param applyId 申请记录ID
     * @return list of PointPicture
     */
    List<PointPicture> getApplyRecordPictures(@Param("applyId") Long applyId);

    /**
     * 插入积分申请图片列表
     *
     * @param pictures 图片
     */
    void insertApplyRecordPictures(@Param("pictures") List<PointPicture> pictures);


    /**
     * 创建积分申请
     *
     * @param apply 申请信息
     * @return long
     */
    Long insertApplyRecord(@Param("apply") PointApply apply);


    /**
     * 编辑积分申请
     *
     * @param model 更新申请记录
     * @return long
     */
    Long updateApplyRecord(@Param("audit") PointApply model);


    /**
     * 审核积分申请-通过
     *
     * @param audit 审核操作信息
     * @return int
     */
    int passApplyRecord(@Param("audit") AuditPointApplyDto audit);


    /**
     * 拒绝积分申请-拒绝
     *
     * @param audit 审核操作信息
     * @return int
     */
    int denyApplyRecord(@Param("audit") AuditPointApplyDto audit);
}
