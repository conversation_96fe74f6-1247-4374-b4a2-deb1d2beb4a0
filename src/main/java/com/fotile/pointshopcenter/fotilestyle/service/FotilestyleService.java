package com.fotile.pointshopcenter.fotilestyle.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.pointshopcenter.fotilestyle.dao.FotilestyleDao;
import com.fotile.pointshopcenter.fotilestyle.pojo.*;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class FotilestyleService {

    @Autowired
    private FotilestyleDao fotilestyleDao;

    public ProductInfoDto findProductInfo(String id) {
        return fotilestyleDao.findProductInfo(Long.valueOf(id));
    }

    public ProductInfoDto findEnergyProdInfo(Long id) {
        return fotilestyleDao.findEnergyProdInfo(id);
    }

    public PageInfo<ProductDto> getEnergyProductList(int pageNum, int pageSize) {
        PageInfo<ProductDto> pageInfo = new PageInfo<>(pageNum, pageSize);
        pageInfo.setRecords(fotilestyleDao.findEnergyProdList(pageInfo));
        return pageInfo;

    }

    public PageInfo<ProductDto> findProduct(ProductInput p, int pageNum, int pageSize) {
        Integer total = fotilestyleDao.countProduct(p);
        PageInfo<ProductDto> pageInfo = new PageInfo<ProductDto>(pageNum, pageSize,total);
        pageInfo.setRecords(fotilestyleDao.findProduct(p, pageInfo));
        return pageInfo;
    }

    public Map<String, Object> getShopHome() {
        Map<String, Object> m = new HashMap<String, Object>();
        List<ProductDto> p1 = fotilestyleDao.findHotProduct();
        List<ProductDto> p2 = fotilestyleDao.findWelfareProduct();
        List<CategoryCount> p3 = fotilestyleDao.findCategoryCount();
        m.put("WelfareProduct", p2 != null ? p2 : new ArrayList<ProductDto>());
        m.put("IntegralProuct", p1);
        m.put("categroyCount", p3);
        return m;
    }

    public List<ProductDto> findHotProduct() {
        return fotilestyleDao.findHotProduct();
    }

    public PageInfo<OrderDto> getShopOrderPageById(String customId, int pageNum, int pageSize) {
        PageInfo<OrderDto> page = new PageInfo<OrderDto>(pageNum, pageSize);
        page.setRecords(fotilestyleDao.getShopOrderPageById(Long.valueOf(customId), page));
        return page;
    }

    public Long getShopOrderCount(Long customId) {
        return fotilestyleDao.getShopOrderCount(customId);
    }

    public OrderDto getExchangeGoods(String id) {
        return fotilestyleDao.getExchangeGoods(Long.valueOf(id));
    }

    @Transactional
    public void savePointShopOrder(OrderParam param, ProductInfoDto prod) {
        Date dt = new Date();
        Long total;

        if (null != prod.getSpecialValue()) {
            total = Long.valueOf(prod.getSpecialValue()) * Long.valueOf(param.getProductNum());
        } else {
            total = prod.getPointValue() * Long.valueOf(param.getProductNum());
        }

        // 订单主表
        OrderMasterPo orderMaster = new OrderMasterPo();
        orderMaster.setOrderCode(param.getOrderCode());
        orderMaster.setCustomerId(Long.valueOf(param.getCustomerId()));
        orderMaster.setCustomerName(param.getUsername());
        orderMaster.setPhone(param.getUsername());
        orderMaster.setTotalValue(total);
        orderMaster.setReceiver(param.getAddressName());
        orderMaster.setContactTel(param.getAddressPhone());

        if (StringUtils.isNotEmpty(param.getProvinceId())) {
            orderMaster.setProvinceId(Long.valueOf(param.getProvinceId()));
        }
        orderMaster.setProvinceName(param.getProvinceName());
        if (StringUtils.isNotEmpty(param.getCityId())) {
            orderMaster.setCityId(Long.valueOf(param.getCityId()));
        }
        orderMaster.setCityName(param.getCityName());
        if (StringUtils.isNotEmpty(param.getCountyId())) {
            orderMaster.setDistrictId(Long.valueOf(param.getCountyId()));
        }
        orderMaster.setDistrictName(param.getCountyName());
        orderMaster.setAddress(param.getAddress());
        orderMaster.setAddressId(Long.valueOf(param.getAddressId()));
        
        orderMaster.setCompanyId(Long.valueOf(param.getCompanyId()));
        orderMaster.setCreatedBy(param.getUserId());
        orderMaster.setCreatedDate(dt);
        fotilestyleDao.insertShopOrder(orderMaster);

        // 订单详情表
        OrderDetailPo orderDetail = new OrderDetailPo();
        orderDetail.setProductId(Long.valueOf(param.getProductId()));
        orderDetail.setProductName(prod.getProductName());
        orderDetail.setProductCode(prod.getProductCode());
        orderDetail.setNum(Long.valueOf(param.getProductNum()));
        orderDetail.setCategoryId(Long.valueOf(prod.getCategoryId()));
        orderDetail.setTypeCode(prod.getTypeCode());

        orderDetail.setTypeName(param.getTypeName());
        orderDetail.setSupplierCode(prod.getSupplierCode());
        orderDetail.setSupplierName(param.getSupplierName());
        orderDetail.setValue(prod.getPointValue());
        orderDetail.setTotalValue(total);

        orderDetail.setOrderId(orderMaster.getId());
        orderDetail.setCreatedBy(param.getUserId());
        orderDetail.setCreatedDate(dt);
        fotilestyleDao.insertShopOrderDetail(orderDetail);

        // 修改库存
        fotilestyleDao.updateProduct(orderDetail);

        // 订单日志表
        OrderLogDto orderLogDto = new OrderLogDto();
        orderLogDto.setOrderId(orderMaster.getId());
        orderLogDto.setCreatedName(orderMaster.getCustomerName());
        orderLogDto.setActionType("创建");
        orderLogDto.setActionTime(orderMaster.getCreatedDate());
        orderLogDto.setContents("创建订单");
        orderLogDto.setCreatedBy(param.getUserId());
        orderLogDto.setCreatedDate(dt);
        fotilestyleDao.insertShopOrderLog(orderLogDto);
    }

    @Transactional
    public void updateShopOrder(OrderMasterPo order) {
        Date dt = new Date();
        order.setModifiedDate(dt);
        fotilestyleDao.updateShopOrder(order);
    }

    @Transactional
    public void deleteShopOrder(OrderMasterPo order) {
        Date dt = new Date();
        order.setModifiedDate(dt);
        fotilestyleDao.deleteShopOrder(order);
    }

    public Long getEnergyTotal(String userId) {
        return fotilestyleDao.getEnergyTotal(userId);
    }

    @Transactional
    public void saveEnergyRecord(EnergyDto energyDto) {
        fotilestyleDao.insertEnergyRecord(energyDto);
        // 修改库存
        fotilestyleDao.updateEnergyProduct(energyDto);
    }

    public PageInfo<EnergyDto> findEnergyRecord(String userId, int pageNum, int pageSize) {
        PageInfo<EnergyDto> page = new PageInfo<>(pageNum, pageSize);
        page.setRecords(fotilestyleDao.findEnergyRecord(userId, page));
        return page;
    }

    public Map<String,Long> findEnergyProdMapping(Long id) {
        return fotilestyleDao.findEnergyProdMapping(id);
    }
}
