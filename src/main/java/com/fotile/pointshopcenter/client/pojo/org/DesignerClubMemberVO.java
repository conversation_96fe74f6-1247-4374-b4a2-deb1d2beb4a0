package com.fotile.pointshopcenter.client.pojo.org;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @data 2024/11/1 11:35
 */
@Data
@Accessors(chain = true)
public class DesignerClubMemberVO {


    /**
     * 设计师俱乐部会员表ID,PK,AI
     */
    private Long id;

    /**
     * 用户账号ID(keycloakId) 唯一
     */
    private String userId;

    /**
     * 会员编码
     */
    private String code;

    /**
     * 会员手机号[加密]
     */
    private String phone;

    /**
     * 会员名称[加密]
     */
    private String name;

    /**
     * 会员昵称[加密]
     */
    private String nickname;

    /**
     * 注册渠道编码(t_channel.code)
     */
    private String registerChannelCode;

    /**
     * 注册频道编码(t_radio.code)
     */
    private String registerRadioCode;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 会员等级(1:注册会员; 2:认证会员; 默认1)
     */
    private String grade;

    /**
     * 头像(http地址)
     */
    private String avatar;

    /**
     * 出生年月日(yyyy-MM-dd)
     */
    private Date birthday;

    /**
     * 性别(1:男; 2:女)
     */
    private String gender;

    /**
     * 权益金账户ID(pointcenter.benefits_designer.id)
     */
    private Long benefitAccountId;

    /**
     * 推荐会员ID(t_designer_club_member.id)
     */
    private Long refereeMemberId;

    /**
     * 推荐业务员ID(t_salesman.id)
     */
    private Long refereeChargeUserId;

    /**
     * 推荐业务员信息(文本填写,用户分配业务员参考)
     */
    private String refereeChargeUser;

    /**
     * 工作类型(1:公司就职[家装设计师]; 2:自由设计师[私人设计师])
     */
    private String workType;

    /**
     * 就职公司(文本填写)
     */
    private String workCompany;

    /**
     * 职务(来源于字典dic.designer_club_member_post)
     */
    private String post;

    /**
     * 所属地区-省份编码(systemcenter.area.area_code)
     */
    private Long provinceCode;

    private String provinceName;

    /**
     * 所属地区-城市编码(systemcenter.area.area_code)
     */
    private Long cityCode;

    private String cityName;

    /**
     * 所属地区-区编码(systemcenter.area.area_code)
     */
    private Long districtCode;

    private String districtName;

    /**
     * 所属地区-详细地址[加密]
     */
    private String address;

    /**
     * 主攻方向(来源字典,多个使用半角逗号分隔,dic.main_attack.value_code)
     */
    private String mainAttack;

    /**
     * 个性标签(来源字典,多个使用半角逗号分隔,dic.personal_tags.value_code)
     */
    private String personalTags;

    /**
     * 设计风格(来源字典,多个使用半角逗号分隔,dic.design_styles.value_code)
     */
    private String designStyles;

    /**
     * 设计收费标准(来源字典dic.design_fee_range.value_code)
     */
    private String designFeeRange;

    /**
     * 教育学校ID(t_school.id)
     */
    private Long schoolId;

    /**
     * 入学日期(yyyy-MM-dd)
     */
    private String enrollmentDate;

    /**
     * 毕业日期(yyyy-MM-dd)
     */
    private String graduationDate;

    private Long companyOrgId;

    /**
     * 备注信息
     */
    private String remark;


    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 修改人ID
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedDate;
}
