package com.fotile.pointshopcenter.client.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 业务员实体类
 *
 * <AUTHOR>
 */
@Data
public class FindSalesmanByIdOutDto implements Serializable {
    /**
     * 业务员id
     */
    private Long id;

    /**
     * 业务员工号
     */
    private String code;

    /**
     * 业务员姓名
     */
    private String name;

    /**
     * 业务员手机号
     */
    private String phone;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 所属部门名称
     */
    private String departmentName;

    /**
     * 所属门店名称
     */
    private String storeName;

    /**
     * 关联员工id
     */
    private Integer employeeId;

    /**
     * 岗位
     */
    private Long station;

    /**
     * 岗位名称
     */
    private String stationName;

    /**
     * 直接上级(上级的员工id)
     */
    private Long parentId;

    /**
     * 直属上级名称
     */
    private String parentName;

    /**
     * 微信号
     */
    private String wechatno;

    /**
     * 业务员头像
     */
    private String portrait;

    /**
     * 员工状态，0：禁用；1：启用
     */
    private Byte status;

    /**
     * 有效期-开始时间
     */
    private String termStart;

    /**
     * 有效期-结束时间
     */
    private String termEnd;

    /**
     * 所属公司fullpathid
     */
    private String companyFullPathId;

    /**
     * 所属部门fullpathid
     */
    private String departmentFullPathId;

    /**
     * 所属门店fullpathid
     */
    private String storeFullPathId;

    /**
     * 所属部门id
     */
    private Long departmentId;

    /**
     * 所属公司id
     */
    private Long companyId;

    /**
     * 所属门店id
     */
    private Long storeId;

    /**
     * 所属门店编码
     */
    private String storeCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 市id
     */
    private Long cityId;

    /**
     * 市名称
     */
    private String cityName;
}
