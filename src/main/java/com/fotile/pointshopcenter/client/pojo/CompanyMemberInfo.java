package com.fotile.pointshopcenter.client.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CompanyMemberInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人username
     */
    private String reviseName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviseTime;

    /**
     * 分公司id
     */
    private Long companyId;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 会员id
     */
    private Long customerId;

    /**
     * 名称
     */
    private String noteName;

    /**
     * 会员等级id
     */
    private Long gradeId;

    /**
     * 状态（1:启用，0：禁用）
     */
    private Integer status;

    /**
     * 引流id
     */
    private Long drainageId;

    /**
     * 引流名称
     */
    private String drainageName;

    /**
     * 落地页类型（数据字典）
     */
    private String pageType;

    /**
     * 引流页面
     */
    private String drainagePage;

    /**
     * 引流来源值（数据字典）
     */
    private String sourceCode;

    /**
     * 引流来源细分（数据字典）
     */
    private String sourceDetailCode;

    /**
     * 是否删除：0：否；1：是
     */
    private Integer isDeleted;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 修改者
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedDate;

    /**
     * 分配业务员id
     */
    private Long chargeUserId;

    /**
     * 分配业务员名称
     */
    private String chargeUserName;

    /**
     * 分配业务员code
     */
    private String chargeCode;

    /**
     * 分配业务员手机号
     */
    private String chargePhone;

    /**
     * 推荐会员id
     */
    private Long recommendCustomerId;

    /**
     * 推荐会员手机号
     */
    private String recommendCustomerPhone;

    /**
     * 服务技师id
     */
    private Integer staffId;

    /**
     * 服务技师名称
     */
    private String staffName;

    /**
     * 服务技师手机号
     */
    private String staffPhone;

    /**
     * 服务技师编码
     */
    private String staffCode;

}