package com.fotile.pointshopcenter.client.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UpdateItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Integer id;

    /**
     * 拼团id
     */
    private Integer helpGrouponId;

    /**
     * 会员id
     */
    private String customerId;

    /**
     * 拼团资格状态，0-不可用，1-可用，2-已用
     */
    private Integer usableStatus;

    /**
     * 订单类型，1-服务订单，2-积分订单
     */
    private Integer orderType;

    /**
     * 关联订单id
     */
    private Long orderId;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedDate;

    /**
     * 1使用，0回退；
     */
    private Integer usedFlag;
}