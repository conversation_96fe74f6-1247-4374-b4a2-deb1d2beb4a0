package com.fotile.pointshopcenter.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR>
 * @data 2024/10/16 21:00
 */
public class AddressMaskingSerializer extends StdSerializer<String> {
    public AddressMaskingSerializer() {
        this(null);
    }

    public AddressMaskingSerializer(Class<String> t) {
        super(t);
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeString(maskAddress(value));
    }

    public static String maskAddress(String address){
        return Optional.ofNullable(address)
                        .filter(StringUtils::isNotBlank)
                        .map(a->a.replaceAll("\\d", "*"))
                        .orElse(address);
    }
}
