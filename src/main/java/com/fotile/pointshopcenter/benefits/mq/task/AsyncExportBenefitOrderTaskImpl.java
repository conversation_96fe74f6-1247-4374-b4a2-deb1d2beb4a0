package com.fotile.pointshopcenter.benefits.mq.task;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.pointshopcenter.benefits.pojo.dto.BenefitOrderQueryDTO;
import com.fotile.pointshopcenter.benefits.pojo.vo.BenefitOrderQueryVO;
import com.fotile.pointshopcenter.benefits.service.BenefitCommonService;
import com.fotile.pointshopcenter.benefits.service.BenefitOrderService;
import com.fotile.pointshopcenter.client.DataClient;
import com.fotile.pointshopcenter.client.pojo.ChannelInfo;
import com.fotile.pointshopcenter.client.pojo.ExportTaskRecord;
import com.fotile.pointshopcenter.client.pojo.OrgComboboxDto;
import com.fotile.pointshopcenter.pointshop.Util.OssService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 权益商城 -> 订单导出任务实现 1809
 *
 * <AUTHOR>
 */
@Component("1809")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class AsyncExportBenefitOrderTaskImpl implements AsyncExportBenefitOrderTask {
    @Autowired
    private BenefitOrderService benefitOrderService;

    @Autowired
    private BenefitCommonService benefitCommonService;

    @Autowired
    private DataClient dataClient;

    @Autowired
    private OssService ossService;

    @Override
    public void exportBenefitOrderTask(ExportTaskRecord exportTaskRecord) {
        if (exportTaskRecord == null || StringUtils.isBlank(exportTaskRecord.getParamJson())) {
            return;
        }

        BenefitOrderQueryDTO inDto = JSON.parseObject(exportTaskRecord.getParamJson(), BenefitOrderQueryDTO.class);
        if (inDto == null) {
            return;
        }

        //设置为数据导出
        inDto.setExport(1);
        inDto.initQuery();

        //批量查询总条数
        Integer totalCount = exportTaskRecord.getTotalCount();
        //进度
        BigDecimal oldProgress = exportTaskRecord.getProgress();
        Integer count = 50000;
        Integer start1 = inDto.getStart();

        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        //生成Excel，并上传oss
        String sheetName = "权益金兑换订单列表导出";
        String fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        List<BenefitOrderQueryVO> list = new ArrayList<>();

        try {
            for (int j = 0; j <= i; j++) {
                int start = start1 + (j) * count;
                size = count;
                if (j == i) {
                    size = lastCount;
                }
                inDto.setOffset((long) start);
                inDto.setSize(size);

                List<BenefitOrderQueryVO> result = benefitOrderService.getExportOrderList(inDto);
                if (CollectionUtils.isNotEmpty(result)) {
                    list.addAll(result);
                }

                //更新任务进度
                BigDecimal newProgress = new BigDecimal(inDto.getSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
                oldProgress = newProgress.add(oldProgress);

                ExportTaskRecord exportTask = new ExportTaskRecord();
                exportTask.setId(exportTaskRecord.getId());
                exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
                exportTask.setExt1(String.valueOf(list.size()));
                dataClient.updateTask(exportTask);
            }

            //格式化输出结果
            if (CollectionUtils.isNotEmpty(list)) {
                List<ChannelInfo> channels = benefitCommonService.getAllChannelList();
                List<OrgComboboxDto> companies = benefitCommonService.getAllCompanyList(true);

                list.forEach(item -> item.formatOutput(inDto.getFlag(), channels, companies, null));
            }

            //写入excel文件流
            EasyExcel.write(os, BenefitOrderQueryVO.class).sheet(sheetName).doWrite(list);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);

            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            dataClient.successTask(exportTask);

            log.info(">>>>>>>>>>>> AsyncExportBenefitOrderTaskImpl.exportBenefitOrderTask >>>>>>>>>>>> benefit order data export successfully.");
        } catch (Exception ex) {
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(ex.getMessage());
            dataClient.failureTask(exportTask);

            log.error(">>>>>>>>>>>> AsyncExportBenefitOrderTaskImpl.exportBenefitOrderTask >>>>>>>>>>>> ", ex);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException ex) {
                log.error(">>>>>>>>>>>> AsyncExportBenefitOrderTaskImpl.exportBenefitOrderTask >>>>>>>>>>>> ", ex);
            }
        }
    }
}
