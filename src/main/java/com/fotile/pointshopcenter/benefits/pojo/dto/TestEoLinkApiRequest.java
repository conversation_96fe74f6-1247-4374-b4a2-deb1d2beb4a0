package com.fotile.pointshopcenter.benefits.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * ClassName: TestEoLinkApiRequest
 * Package: com.fotile.pointshopcenter.benefits.pojo.dto
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/12/18 17:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TestEoLinkApiRequest implements Serializable {
    /**
     * api接口uri
     */
    private String apiUri;

    /**
     * 请求参数
     */
    private Map<String, Object> params;
}
