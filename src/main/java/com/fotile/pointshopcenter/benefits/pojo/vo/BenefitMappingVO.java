package com.fotile.pointshopcenter.benefits.pojo.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 权益商品通用映射实体
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/10/14 14:35
 */
@Data
public class BenefitMappingVO implements Serializable {
    /**
     * 权益商城映射关系表ID,PK,AI
     */
    private Long id;

    /**
     * 源表名称
     */
    private String sourceTableName;

    /**
     * 源表主键ID
     */
    private Long sourceId;

    /**
     * 关联对象类型(1:分公司; 2:门店; 3:部门; 4:渠道; 5:频道)
     */
    private Integer targetType;

    /**
     * 关联对象ID(-1:代表全部、分公司orgId、门店orgId、部门orgId、渠道Id、频道Id)
     */
    private Long targetId;

    /**
     * 扩展字段-关联对象名称
     */
    private String targetName;

    /**
     * 扩展字段-关联对象编码
     */
    private String targetCode;
}
