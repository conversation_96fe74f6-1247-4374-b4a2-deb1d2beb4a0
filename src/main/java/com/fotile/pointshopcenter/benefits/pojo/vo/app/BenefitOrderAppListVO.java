package com.fotile.pointshopcenter.benefits.pojo.vo.app;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.pointshopcenter.benefits.pojo.factory.BenefitPOJOFactory;
import com.fotile.pointshopcenter.constant.BenefitConstant;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/** 小程序使用列表查询返回数据包
 * <AUTHOR>
 * @data 2024/11/1 11:18
 */
@Data
public class BenefitOrderAppListVO {

    /**
     * 订单id
     */
    private Long id;
    /**
     * 【订单信息】订单编号(系统生成)
     */
    private String orderCode;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 【订单信息】订单总权益金值
     */
    private Long totalBenefitValue;

    /**
     * 【订单信息】订单状态(10:待发货; 20:已发货; 30:已完成; 40:已取消; 详见枚举)
     */
    private Integer orderStatus;

    @Setter(AccessLevel.NONE)
    private String orderStatusName;

    /**
     * 商品类型(1:实物商品; 2:虚拟商品)
     */
    private Integer productType;

    /**
     * 头图(多张使用半角逗号分隔,最多支持10张)
     */
    private String headImages;

    /**
     * 是否有券码
     */
    private boolean hasCouponCode=false;

    /**
     * 创建日期
     */
    private Date createdDate;

    /**
     * 物流类型(0:无需快递, 1:快递, 实际选择)
     */
    private Integer logisticsType;

    /**
     * 物流信息
     */
    @Setter(AccessLevel.NONE)
    private List<BenefitOrderLogistics> logisticsList;

    /**
     * 商品行信息
     */
    @JsonIgnore
    private List<BenefitOrderDetailAppVO> detailList;


    public String getOrderStatusName() {
        if(StringUtils.isBlank(this.orderStatusName)){
            this.orderStatusName= BenefitConstant.OrderStatusEnum.getStatusName(this.orderStatus);
        }
        return orderStatusName;
    }


    public List<BenefitOrderLogistics> getLogisticsList() {
/*        if(!Objects.equals(this.logisticsType,1)){
            return logisticsList;
        }
        if(Objects.equals(orderStatus, BenefitConstant.OrderStatusEnum.WAIT_DELIVER.getStatus())){
            return logisticsList;
        }*/

        if(CollectionUtils.isEmpty(logisticsList)){
            if(CollectionUtils.isNotEmpty(this.detailList)){
                this.logisticsList=  this.detailList.stream()
                        .map(BenefitPOJOFactory::createOrderLogistics)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }
        return logisticsList;
    }
}
