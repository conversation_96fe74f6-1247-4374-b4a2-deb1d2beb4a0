package com.fotile.pointshopcenter.benefits.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <AUTHOR>
* @data 2024/12/10 10:31
*/
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName(value = "pointshopcenter.benefits_eolink_log")
public class BenefitsEolinkLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "notice_id")
    private Long noticeId;

    @TableField(value = "notice_type")
    private Integer noticeType;

    /**
     * 同步的内容
     */
    @TableField(value = "content_json")
    private String contentJson;

    @TableField(value = "order_id")
    private Long orderId;

    @TableField(value = "notice_create_time")
    private Date noticeCreateTime;

    /**
     * 0:未同步，1：同步成功，2：同步失败
     */
    @TableField(value = "`status`")
    private Integer status=0;

    /**
     * 响应
     */
    @TableField(value = "response")
    private String response;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "modified_date")
    private Date modifiedDate;
}