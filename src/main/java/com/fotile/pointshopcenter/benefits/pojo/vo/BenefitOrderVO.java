package com.fotile.pointshopcenter.benefits.pojo.vo;

import com.fotile.pointshopcenter.benefits.pojo.entity.BenefitOrder;
import com.fotile.pointshopcenter.benefits.pojo.entity.BenefitOrderDetail;
import com.fotile.pointshopcenter.benefits.pojo.entity.BenefitOrderExtend;
import com.fotile.pointshopcenter.client.pojo.ChannelInfo;
import com.fotile.pointshopcenter.client.pojo.OrgComboboxDto;
import com.fotile.pointshopcenter.constant.BenefitConstant;
import com.fotile.pointshopcenter.pointshop.Util.CommonUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 权益订单详情实体
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/10/15 9:59
 */
@Data
public class BenefitOrderVO implements Serializable {
    /**
     * 权益订单信息
     */
    private BenefitOrder orderInfo;

    /**
     * 权益订单扩展信息
     */
    private BenefitOrderExtend orderExtendInfo;

    /**
     * 权益订单详情信息
     */
    private List<BenefitOrderDetail> productInfos;

    /**
     * 同步状态(0:不同步; 1:同步成功; 2:同步失败)
     * 已作废，取 orderInfo.syncStatus字段(定义相同)
     */
    @Deprecated
    private Integer syncStatus;

    /**
     * 外部订单号(百礼汇)
     */
    private String blhOrderSn;

    /**
     * 是否为自营商品
     */
    private Boolean isSelfOperatedProduct;


    /**
     * 格式化输出
     *
     * @param flag 是否脱敏
     */
    public void formatOutput(boolean flag, List<ChannelInfo> channels, List<OrgComboboxDto> companies) {
        if (this.orderInfo != null) {
            this.orderInfo.setCompanyName(CommonUtil.getCompanyName(this.orderInfo.getCompanyOrgId(), companies));
            this.orderInfo.setChannelName(CommonUtil.getChannelName(this.orderInfo.getChannelCode(), channels));
            this.orderInfo.setOrderStatusName(BenefitConstant.OrderStatusEnum.getStatusName(this.orderInfo.getOrderStatus()));

            if (Objects.equals(1, this.orderInfo.getOrderType())) {
                this.orderInfo.setOrderTypeName("实物商品订单");
            } else if (Objects.equals(2, this.orderInfo.getOrderType())) {
                this.orderInfo.setOrderTypeName("虚拟商品订单");
            }

            if (flag) {
                this.orderInfo.setMemberNickName(CommonUtil.setName(this.orderInfo.getMemberNickName()));

                this.orderInfo.setReceiverName(CommonUtil.setName(this.orderInfo.getReceiverName()));
                this.orderInfo.setReceiverPhone(CommonUtil.setPhone(this.orderInfo.getReceiverPhone()));
                this.orderInfo.setReceiverAddress(CommonUtil.setAddress(this.orderInfo.getReceiverAddress()));

                this.orderInfo.setDeliveryName(CommonUtil.setName(this.orderInfo.getDeliveryName()));
                this.orderInfo.setDeliveryPhone(CommonUtil.setPhone(this.orderInfo.getDeliveryPhone()));
                this.orderInfo.setDeliveryAddress(CommonUtil.setAddress(this.orderInfo.getDeliveryAddress()));
            }
        }
        if (CollectionUtils.isNotEmpty(this.productInfos)) {
            this.productInfos.forEach(product -> {
                product.setIsSelfOperatedProduct(Objects.equals(BenefitConstant.FOTILE_SUPPLIER_CODE, product.getProductSupplierCode()));
                product.setProductTypeName(BenefitConstant.BenefitProductType.getTypeName(product.getProductType()));

                if (Objects.equals(1, product.getProductLogisticsType())) {
                    product.setProductLogisticsTypeName("快递");
                } else {
                    product.setProductLogisticsTypeName("无需快递");
                }

                if (Objects.equals(1, product.getLogisticsType())) {
                    product.setLogisticsTypeName("快递");
                } else {
                    product.setLogisticsTypeName("无需快递");
                }
            });
        }
    }
}
