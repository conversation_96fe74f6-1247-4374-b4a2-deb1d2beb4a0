package com.fotile.pointshopcenter.benefits.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 查询权益商品请求参数实体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BenefitProductQueryDTO implements Serializable {
    /**
     * 权益商品ID
     */
    private Long id;

    /**
     * 权益商品ID集合
     */
    private List<Long> ids;

    /**
     * 商品编码(精准查询)
     */
    private String code;

    /**
     * 商品名称(模糊查询)
     */
    private String name;

    /**
     * 展示标题(模糊查询)
     */
    private String title;

    /**
     * 商品类型(1:实物商品; 2:虚拟商品)
     */
    private Integer type;

    /**
     * 商品分类ID(渠道分类树编码D74476下分类ID)
     */
    private Long categoryId;

    /**
     * 商品分类ID集合(渠道分类树编码D74476下分类ID)
     */
    private Set<Long> categoryIds;

    /**
     * 商品所属渠道Id
     */
    private Long channelId;

    /**
     * 商品所属渠道Id集合
     */
    private Set<Long> channelIds;

    /**
     * 启用状态(0:禁用, 1:启用)
     */
    private Integer status;

    /**
     * 上架状态(0:下架, 1:上架)
     */
    private Integer onlineStatus;

    /**
     * 查询关键词(模糊查询,支持商品编码、名称、展示标题、型号)
     */
    private String keywords;

    /**
     * 创建时间(开始时间)
     */
    private Date createdStartDate;

    /**
     * 创建时间(结束时间)
     */
    private Date createdEndDate;

    /**
     * 最新编辑时间(开始时间)
     */
    private Date modifiedStartDate;

    /**
     * 最新编辑时间(结束时间)
     */
    private Date modifiedEndDate;

    /**
     * 权益金下限
     */
    private Long benefitValueStart;

    /**
     * 权益金上限，-1表示设计师拥有的权益金总额，我能兑换的 条件 使用
     */
    private Long benefitValueEnd;

    /**
     * 内部参数-分公司数据隔离
     */
    private List<Long> dataScopeCompany;

    /**
     * 【百礼汇】商品上下架状态(0:下架; 1:上架;)
     */
    private Integer blhProductOnlineState;


    /**
     * 排序方式(单个字段：id desc / 多个字段：id asc, modified_date desc)
     * 支持排序字段(id, created_date, modified_date, sort ...)
     * eg:
     * [{"id","asc"},{"created_date":"desc"}]
     */
    private Map<String, String> sort;

    /**
     * 支持排序字段(id, created_date, modified_date, sort ...)
     */
    private String orderBy = "id";

    /**
     * 排序方式(desc:降序, asc:降序, 默认desc)
     */
    private String sortBy = "desc";

    /**
     * 当前页码
     */
    private Integer page = 1;

    /**
     * 每页条数
     */
    private Integer size = 10;

    /* --------------------- 异步导出相关参数 start --------------------- */

    /**
     * 是否是导出 1：导出；其他：否
     */
    private Integer export;

    /**
     * 导出数据文件名称
     */
    private String fileName;

    /**
     * 是否脱敏操作 0否1是(传递则根据脱敏规则脱敏)
     */
    private String flag;

    /**
     * 开始数
     */
    private Integer start;

    /**
     * 偏移量
     */
    private Long offset;

    /**
     * 会员id
     */
    private Long memberId;

    public void initQuery() {
        if (StringUtils.isBlank(this.orderBy)) {
            this.orderBy = "id";
        }
        if (StringUtils.isBlank(this.sortBy)) {
            this.sortBy = "desc";
        }
        if (MapUtils.isEmpty(this.sort)) {
            this.sort = null;
        }
    }
}
