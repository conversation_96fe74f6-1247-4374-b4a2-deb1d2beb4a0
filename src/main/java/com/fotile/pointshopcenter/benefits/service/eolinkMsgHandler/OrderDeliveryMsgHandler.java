package com.fotile.pointshopcenter.benefits.service.eolinkMsgHandler;

import com.alibaba.fastjson.JSONObject;
import com.fotile.pointshopcenter.benefits.service.BenefitEoLinkService;
import com.fotile.pointshopcenter.component.pojo.eolink.notic.NoticeOrderDelivery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/** 订单出库消息处理器
 * <AUTHOR>
 * @data 2024/11/20 20:59
 */
@Component("orderDeliveryMsgHandler")
public class OrderDeliveryMsgHandler implements EoLinkMsgHandler{

    @Resource
    private BenefitEoLinkService benefitEoLinkService;
    @Override
    public void consumeMsg(String json) {
        if(StringUtils.isBlank(json)){
            return;
        }
        NoticeOrderDelivery noticeOrderDelivery = JSONObject.parseObject(json, NoticeOrderDelivery.class);
        if(Objects.isNull(noticeOrderDelivery)){
            return;
        }

        //设置发货时间
        benefitEoLinkService.setOrderDelivery(noticeOrderDelivery);
    }
}
