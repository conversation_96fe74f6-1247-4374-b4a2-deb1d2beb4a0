package com.fotile.pointshopcenter.component.pojo.eolink.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fotile.pointshopcenter.component.pojo.eolink.IdNameInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 接口：/Area/mapping 中文地址映射 返回实体类
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.pointshopcenter.component.pojo.eolink.resp
 * @date 2024/10/26 23:41
 */
@Data
public class AreaMappingResp implements Serializable {
    /**
     * 百礼汇地址ID字符串 eg:19_1704_1800_37842
     */
    @JSONField(name = "areaIdStr")
    private String areaIdStr;

    /**
     * 百礼汇地址列表
     */
    @JSONField(name = "areaList")
    private List<IdNameInfo> areaList;
}
