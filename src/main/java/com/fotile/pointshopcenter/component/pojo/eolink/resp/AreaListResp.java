package com.fotile.pointshopcenter.component.pojo.eolink.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fotile.pointshopcenter.component.pojo.eolink.AreaInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 接口：/Area/getProvince、/Area/getCity、/Area/getCounty、/Area/getTown 返回实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @create 2024/10/22 13:16
 */
@Data
public class AreaListResp implements Serializable {
    /**
     * 数量
     */
    @JSONField(name = "num")
    private Integer num;

    /**
     * 省份列表
     */
    @JSONField(name = "list")
    private List<AreaInfo> list;
}
