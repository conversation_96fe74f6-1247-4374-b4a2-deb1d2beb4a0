package com.fotile.pointshopcenter.pointshop.dao;

import com.fotile.pointshopcenter.pointshop.pojo.entity.EnergyProductContentPriceMapping;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 卡券与内容定价关系表(EnergyProductContentPriceMapping)表数据库访问层
 */
public interface EnergyProductContentPriceMappingDao {

    /**
     * 通过ID查询单条数据
     */
    List<EnergyProductContentPriceMapping> queryByEnergyProductId(@Param("energyProductId") Long energyProductId);

    /**
     * 查询指定行数据
     */
    List<EnergyProductContentPriceMapping> queryAllByLimit(EnergyProductContentPriceMapping energyProductContentPriceMapping, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     */
    long count(EnergyProductContentPriceMapping energyProductContentPriceMapping);

    /**
     * 新增数据
     */
    int insert(EnergyProductContentPriceMapping energyProductContentPriceMapping);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     */
    int insertBatch(@Param("entities") List<EnergyProductContentPriceMapping> entities);

    /**
     * 修改数据
     */
    int update(EnergyProductContentPriceMapping energyProductContentPriceMapping);

    /**
     * 通过主键删除数据
     */
    int deleteById(Long id);

    void deleteByEnergyProductId(@Param("energyProductId") Long energyProductId);

}

