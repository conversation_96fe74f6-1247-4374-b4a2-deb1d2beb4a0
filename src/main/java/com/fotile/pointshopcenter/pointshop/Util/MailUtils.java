package com.fotile.pointshopcenter.pointshop.Util;

import com.fotile.pointshopcenter.pointshop.Util.dto.MailConfig;
import com.fotile.pointshopcenter.pointshop.Util.dto.SendMailRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.util.Calendar;
import java.util.Date;
import java.util.Properties;

/**
 * 邮件发送帮助类
 * 通过JavaMail类发送邮件
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.marketingcenter.userClues.util
 * @date 2020/8/6 17:32
 */
@Slf4j
@Component
public class MailUtils {
    private final MailConfig mailConfig;

    @Autowired
    public MailUtils(MailConfig mailConfig) {
        this.mailConfig = mailConfig;
    }

    /**
     * 发送邮件(仅支持单个接收人/单个附件)
     *
     * @param request 邮件发送请求
     * @return true/false 发送成功/失败
     */
    public boolean sendMail(SendMailRequest request) {
        boolean succeed = false;
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", mailConfig.getHost());
            //指定SMTP服务器
            props.put("mail.smtp.auth", "true");
            //指定是否需要SMTP验证

            Session session = Session.getDefaultInstance(props);
            session.setDebug(false);

            try {
                MimeMessage message = new MimeMessage(session);
                //指定发送人
                message.setFrom(new InternetAddress(mailConfig.getUserName()));
                if (StringUtils.isNotBlank(request.getFromPersonalName())) {
                    message.setFrom(new InternetAddress(mailConfig.getUserName(), request.getFromPersonalName()));
                }
                //指定接收人
                message.addRecipients(Message.RecipientType.TO, request.getTo());
                //指定抄送人
                if (!StringUtils.isBlank(request.getCc())) {
                    message.addRecipients(Message.RecipientType.CC, request.getCc());
                }

                //设置标题
                message.setSubject(request.getSubject());
                message.addHeader("charset", "UTF-8");

                //添加正文内容
                Multipart multipart = new MimeMultipart();
                MimeBodyPart contentPart = new MimeBodyPart();
                contentPart.setText(request.getContent(), "UTF-8");
                contentPart.setHeader("Content-Type", "text/html; charset=UTF-8");
                multipart.addBodyPart(contentPart);

                //添加附件
                if (request.getIs() != null) {
                    MimeBodyPart fileBody = new MimeBodyPart();
                    DataSource source = new ByteArrayDataSource(request.getIs(), request.getFileContentType());
                    fileBody.setDataHandler(new DataHandler(source));
                    //中文乱码问题
                    fileBody.setFileName(MimeUtility.encodeText(request.getFileName()));
                    multipart.addBodyPart(fileBody);
                }
                message.setContent(multipart);
                message.setSentDate(new Date());
                message.saveChanges();

                Transport transport = session.getTransport("smtp");
                transport.connect(mailConfig.getHost(), mailConfig.getUserName(), mailConfig.getPassword());
                transport.sendMessage(message, message.getAllRecipients());
                transport.close();

                succeed = true;
                log.info("MailUtils.sendMail -> " + Calendar.getInstance().getTime() + ":Send mail to " + request.getTo() + " success!");
            } catch (Exception e) {
                log.info("MailUtils.sendMail -> " + Calendar.getInstance().getTime() + ":Send mail to " + request.getTo() + " error!");
                log.error("MailUtils.sendMail -> exception:" + e.toString());
            } finally {
                if (request.getIs() != null)
                    request.getIs().close();
            }
        } catch (Exception e) {
            log.error("MailUtils.sendMail -> last exception:" + e.toString());
        }
        return succeed;
    }
}
