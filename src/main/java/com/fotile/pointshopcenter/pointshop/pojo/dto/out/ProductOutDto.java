package com.fotile.pointshopcenter.pointshop.pojo.dto.out;

import com.fotile.pointshopcenter.client.pojo.FindGrouponGoodsOutDto;
import com.fotile.pointshopcenter.pointshop.pojo.entity.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询商品响应实体
 *
 * <AUTHOR>
 */
@Data
public class ProductOutDto extends Product {
    /**
     * 商品分类名称
     **/
    private String categoryName;

    /**
     * 供应商名称
     **/
    private String supplierName;

    /**
     * 商品类型名称
     **/
    private String typeName;

    /**
     * 特价id
     **/
    private Long specialId;

    /**
     * 特价值
     **/
    private BigDecimal specialValues;

    /**
     * 商品描述
     **/
    private ProductDescription productDesc;

    /**
     * 商品品牌标签
     **/
    private List<Tag> tags;

    /**
     * 商品关键字标签
     **/
    private List<Tag> keywordTags;

    /**
     * 商品图片
     **/
    private List<ProductPicture> pictures;

    /**
     * 商品特价
     **/
    private List<ProductSpecial> specials;

    /**
     * 组团商品
     */
    private FindGrouponGoodsOutDto grouponGoodsOutDto;
}

