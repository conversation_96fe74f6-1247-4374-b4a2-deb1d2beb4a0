package com.fotile.pointshopcenter.pointshop.pojo.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 积分订单商品实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.pointshopcenter.pointshop.pojo.dto
 * @date 2020/5/28 14:33
 */
@Data
public class OrderDetailDto implements Serializable {
    private Long id;

    /**
     * *商品ID
     */
    @NotNull(message = "商品ID不能为空！")
    @Min(value = 1, message = "商品ID必须大于0！")
    private Long productId;

    /**
     * *购买数量
     */
    @NotNull(message = "购买数量不能为空！")
    @Min(value = 1, message = "购买数量必须大于0！")
    private Integer num = 1;

    /**
     * 积分商品订单id(order.id)
     */
    private Long orderId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 商品规格
     */
    private String specification;

    /**
     * 商品分类id
     */
    private Long categoryId;

    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 商品类型code(来源字典typecode:pointshop_product_type)
     */
    private String typeCode;

    /**
     * 商品类型名称(实物/虚拟/社区服务)
     */
    private String typeName;

    /**
     * 商品供应商code(来源字典typcode:pointshop_supplier)
     */
    private String supplierCode;

    /**
     * 商品供应商名称
     */
    private String supplierName;

    /**
     * 特价商品id
     */
    private Long specialId;

    /**
     * 特价商品兑换值
     */
    private BigDecimal specialValues;

    /**
     * 商品市场价
     */
    private BigDecimal marketPrice;

    /**
     * *单价(value_type)
     */
    private BigDecimal value;

    /**
     * *总价(value_type)
     */
    private BigDecimal totalValue;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 拼团活动价
     */
    private BigDecimal activityPrice;
}
