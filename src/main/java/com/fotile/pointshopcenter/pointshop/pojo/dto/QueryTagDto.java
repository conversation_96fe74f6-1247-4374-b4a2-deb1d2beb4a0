package com.fotile.pointshopcenter.pointshop.pojo.dto;

import lombok.Data;

/**
 * 查询标签
 *
 * <AUTHOR>
 */
@Data
public class QueryTagDto {
    /**
     * 标签名称(模糊搜索)
     */
    private String name;

    /**
     * 类型(1:积分商品,2:积分商品订单,3:积分商品关键字标签,默认全部)
     */
    private Integer type = 1;

    /**
     * 目标id(eg:商品id/订单id)
     */
    private Long target_id;

    /**
     * 记录状态(0未删除,id已删除,默认0)
     */
    private Long is_deleted = 0L;

    /**
     * 当前页(默认1)
     */
    private Integer page_index = 1;

    /**
     * 页大小(默认10)
     */
    private Integer page_size = 10;

    /**
     * 分公司ID
     */
    private Long companyId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 请求来源(1:小程序)
     */
    private Integer requestSource;
}
