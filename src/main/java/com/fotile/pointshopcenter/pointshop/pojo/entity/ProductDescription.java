package com.fotile.pointshopcenter.pointshop.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;

import javax.persistence.Entity;

/**
 * 积分商品描述表实体类
 *
 * <AUTHOR>
 */
@Entity
@Data
@TableName(value = "product_description", schema = "pointshopcenter")
public class ProductDescription extends AuditingEntity {
    /**
     * 商品描述id,PK,AI
     **/
    @TableField(value = "id")
    private Long id;

    /**
     * 商品id
     **/
    @TableField(value = "product_id")
    private Long productId;

    /**
     * wap商品描述
     **/
    @TableField(value = "desc_wap")
    private String descWap;

    /**
     * pc商品描述
     **/
    @TableField(value = "desc_pc")
    private String descPc;

    /**
     * 备注
     **/
    @TableField(value = "remark")
    private String remark;
}
