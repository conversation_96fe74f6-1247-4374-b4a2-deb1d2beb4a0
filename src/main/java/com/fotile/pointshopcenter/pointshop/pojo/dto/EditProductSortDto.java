package com.fotile.pointshopcenter.pointshop.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * 新增/编辑积分商品分类
 *
 * <AUTHOR>
 */
@Data
public class EditProductSortDto {
    /**
     * 排序列表(支持多个商品批量设置[{key:value}]),eg:[{productid1:sortnum1},{productid2:sortnum2}]
     **/
    private List<KeyValueEntry> sorted_list;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
