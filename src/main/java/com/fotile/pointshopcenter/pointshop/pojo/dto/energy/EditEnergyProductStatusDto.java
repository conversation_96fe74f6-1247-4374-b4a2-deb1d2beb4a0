package com.fotile.pointshopcenter.pointshop.pojo.dto.energy;

import com.fotile.pointshopcenter.pointshop.pojo.dto.KeyValueEntry;
import lombok.Data;

import java.util.List;

/**
 * 编辑积分商品状态实体
 *
 * <AUTHOR>
 */
@Data
public class EditEnergyProductStatusDto {
    /**
     * 排序列表(支持多个商品批量设置[{key:value}]),eg:[{productid1:sortnum1},{productid2:sortnum2}]
     * value -> 1:上架,2:下架
     **/
    private List<KeyValueEntry> status_list;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}

