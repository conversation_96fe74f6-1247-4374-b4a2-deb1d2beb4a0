package com.fotile.msg.rule.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.msg.rule.pojo.entity.TRuleResultEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TRuleResultDao extends BaseMapper<TRuleResultEntity> {
    int updateBatch(List<TRuleResultEntity> list);

    int updateBatchSelective(List<TRuleResultEntity> list);

    int batchInsert(@Param("list") List<TRuleResultEntity> list);

    int insertOrUpdate(TRuleResultEntity record);

    int insertOrUpdateSelective(TRuleResultEntity record);
}