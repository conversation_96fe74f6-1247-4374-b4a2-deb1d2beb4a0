package com.fotile.msg.rule.xxl;

import com.alibaba.fastjson.JSON;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.msg.client.MarketingClient;
import com.fotile.msg.client.pojo.vo.QueryOriginalBySourceIdVO;
import com.fotile.msg.client.pojo.vo.UserCluesVO;
import com.fotile.msg.rule.context.RuleReceContext;
import com.fotile.msg.rule.pojo.dto.RuleMatchDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Component
public class MarketingRuleXXlHandler {
    @Autowired
    MarketingClient marketingClient;
    @Autowired
    RuleReceContext receContext;

    @XxlJob(value = "marketingRuleXXlHandler")
    public ReturnT<String> execute(String s) throws Exception {
        //获取今日线索--020
        Result<List<QueryOriginalBySourceIdVO>> listResult = marketingClient.queryCluesByTodayO2O();
        if (listResult == null || listResult.getData() == null) {
            return new ReturnT<>("暂无数据");
        }
        listResult.getData().forEach(pushDecorateData -> {
            /**
             * 公司分类(1:家装,2:橱柜,3:定制,4:异业门店,5:三工师傅,6:私人设计师,7:异业员工,8:异业小伙伴,9:带单老用户)
             */
            RuleMatchDTO<String> ruleMatchDTO = new RuleMatchDTO<>();
            ruleMatchDTO.setApplicationPageCode("001");
            ruleMatchDTO.setData(JSON.toJSONString(pushDecorateData));
            receContext.match(ruleMatchDTO);
        });
        return new ReturnT<>("1");
    }

    @XxlJob(value = "marketingRuleUpdateTimeXXlHandler")
    public ReturnT<String> marketingRuleUpdateTimeXXlHandler(String s) throws Exception {

        if (StringUtils.isEmpty(s)) {
            throw new BusinessException("时间不能为空");
        }
        String code = "107";
        if (s.equals("3")){
            code = "107";
        }else if (s.equals("7")){
            code = "108";
        }else if (s.equals("30")){
            code = "109";
        }
        Integer size = 300;
        //计算时间
        String format = LocalDateTime.now().minusDays(Long.valueOf(s)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        //获取今日线索--020
        Integer count = marketingClient.queryUserCluesCountByLastFollowTime(format).getData();
        if (count > 0 ) {

            for (Integer page = 1;page <= (count/size)+1; page ++) {
                PageInfo pageInfo = new PageInfo(page,size);
                Result<List<UserCluesVO>> listResult = marketingClient.queryUserCluesByLastFollowTime(format, pageInfo.getOffset(), size);
                if (listResult == null || listResult.getData() == null) {
                    return new ReturnT<>("暂无数据");
                }
                String finalCode = code;
                listResult.getData().forEach(pushDecorateData -> {
                    /**
                     * 公司分类(1:家装,2:橱柜,3:定制,4:异业门店,5:三工师傅,6:私人设计师,7:异业员工,8:异业小伙伴,9:带单老用户)
                     */
                    RuleMatchDTO<String> ruleMatchDTO = new RuleMatchDTO<>();
                    ruleMatchDTO.setData(JSON.toJSONString(pushDecorateData));
                    ruleMatchDTO.setApplicationPageCode(finalCode);
                    receContext.updateTimeXXlHandler(ruleMatchDTO, finalCode);
                });
            }
        }
        return new ReturnT<>("1");
    }
}
