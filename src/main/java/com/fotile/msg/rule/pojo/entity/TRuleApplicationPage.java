package com.fotile.msg.rule.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "com-fotile-msg-rule-pojo-entity-TRuleApplicationPage")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "msgcenter.t_rule_application_page")
public class TRuleApplicationPage extends AuditingEntity {
    /**
     * 应用页面
     */
    @TableField(value = "application_page_name")
    @ApiModelProperty(value = "应用页面")
    private String applicationPageName;

    /**
     * 应用页面code
     */
    @TableField(value = "application_page_code")
    @ApiModelProperty(value = "应用页面code")
    private String applicationPageCode;

    /**
     * 模块code
     */
    @TableField(value = "module_code")
    @ApiModelProperty(value = "模块code")
    private String moduleCode;

    /**
     * 是否启用
     */
    @TableField(value = "`enable`")
    @ApiModelProperty(value = "是否启用")
    private Integer enable;

    public static final String COL_ID = "id";

    public static final String COL_IS_DELETED = "is_deleted";

    public static final String COL_CREATED_BY = "created_by";

    public static final String COL_CREATED_DATE = "created_date";

    public static final String COL_MODIFIED_BY = "modified_by";

    public static final String COL_MODIFIED_DATE = "modified_date";

    public static final String COL_APPLICATION_PAGE_NAME = "application_page_name";

    public static final String COL_APPLICATION_PAGE_CODE = "application_page_code";

    public static final String COL_MODULE_CODE = "module_code";

    public static final String COL_ENABLE = "enable";
}