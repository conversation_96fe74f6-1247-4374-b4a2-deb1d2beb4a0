package com.fotile.msg.rule.pojo.export;


import lombok.Data;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class TemplateDetailExportOutDto implements Serializable {
    @ColumnWidth(10)
    @ExcelProperty(value = {"模板id"}, index = 0)
    private Long id;
    @ColumnWidth(25)
    @ExcelProperty(value = {"短信模板标题"}, index = 1)
    private String title;
    @ColumnWidth(10)
    @ExcelProperty(value = {"短信模板内容"}, index = 2)
    private String context;
    @ColumnWidth(10)
    @ExcelProperty(value = {"所属大区"}, index = 3)
    private String areaName;
    @ColumnWidth(10)
    @ExcelProperty(value = {"所属分公司"}, index = 4)
    private String companyName;
    @ColumnWidth(10)
    @ExcelProperty(value = {"所属门店编码"}, index = 5)
    private String storeCode;
    @ColumnWidth(30)
    @ExcelProperty(value = {"所属门店"}, index = 6)
    private String storeName;
    @ColumnWidth(10)
    @ExcelProperty(value = {"发送端"}, index = 7)
    private String sendSource;
    @ColumnWidth(10)
    @ExcelProperty(value = {"应用界面"}, index = 8)
    private String sourceValue;
    @ColumnWidth(10)
    @ExcelProperty(value = {"发送人编码"}, index = 9)
    private String chargeUserCode;
    @ColumnWidth(10)
    @ExcelProperty(value = {"发送人姓名"}, index = 10)
    private String chargeUserName;
    @ColumnWidth(20)
    @ExcelProperty(value = {"发送对象"}, index = 11)
    private String unionId;
    @ColumnWidth(10)
    @ExcelProperty(value = {"线索id"}, index = 12)
    private Long cluesId;
    @ColumnWidth(10)
    @ExcelProperty(value = {"创建人编码"}, index = 13)
    private String createdCode;
    @ColumnWidth(10)
    @ExcelProperty(value = {"创建人"}, index = 14)
    private String createdBy;
    @ColumnWidth(15)
    @ExcelProperty(value = {"创建时间"}, index = 15)
    private String createdTime;
    @ColumnWidth(15)
    @ExcelProperty(value = {"发送时间"}, index = 16)
    private String sendTime;
    @ExcelIgnore
    private Long logId;

}
