package com.fotile.msg.rule.pojo.dto;

import com.fotile.msg.rule.dao.RuleTemplatePageMappingDao;
import com.fotile.msg.rule.pojo.entity.RuleTemplateCompanyMapping;
import com.fotile.msg.rule.pojo.entity.RuleTemplatePageMapping;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/16 14:07
 */
@Data
public class TemplateSaveOrUpdateInDto {

    private Long id;

    private Long ruleId;

    private Long templateId;

    //标题
    private String name;

    //备注
    private String remark;

    private String topTemplate;
    /**
     * 正文模板
     */
    private String contentTemplate;
    //内容
    private String bottomTemplate;

    //类型
    private Integer type;

    //状态
    private Long status;

    //所属分公司
    private List<RuleTemplateCompanyMapping> companyIds;

    //应用页面
    private List<Long> pageIds;

    //是否复制
    private Integer isCopy;

    private Integer templateType;

    private String createdName;

    private Long pageId;

    private  String pageCode;

    /**
     * 调研问卷 cmscenter.channel_category主键id
     */
    private Long channelCategoryId;
    /**
     * 调研问卷 外链
     */
    private String channelCategoryUrl;
}
