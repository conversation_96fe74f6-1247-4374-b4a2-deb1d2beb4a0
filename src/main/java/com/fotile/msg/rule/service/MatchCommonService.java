package com.fotile.msg.rule.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.msg.rule.dao.TVariableInfoDao;
import com.fotile.msg.rule.enums.CanalEventTypeEnum;
import com.fotile.msg.rule.mq.pojo.CanalMapProcessInfo;
import com.fotile.msg.rule.mq.pojo.CanalRowData;
import com.fotile.msg.rule.pojo.entity.TVariableInfo;
import com.fotile.msg.rule.util.RuleConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Service
public class MatchCommonService {
    @Autowired
    TVariableInfoDao tVariableInfoDao;

    public List<TVariableInfo> getTvariableInfoByTableName(String tableName) {

        //获取线索需要匹配的字段
        QueryWrapper<TVariableInfo> queryWrapper =
                new QueryWrapper<TVariableInfo>()
                        .eq("source_table_name", tableName);
        List<TVariableInfo> tVariableInfos = tVariableInfoDao.selectList(queryWrapper);
        return tVariableInfos;
    }

    public void extracted(CanalMapProcessInfo canalMapProcessInfo, List<TVariableInfo> tVariableInfos, Map<String, String> fact) {
        //新对象
        List<CanalRowData> afterRowDatas = canalMapProcessInfo.getAfterRowDatas();
        fact.put("eventCategory", canalMapProcessInfo.getEventType());
        afterRowDatas.forEach(afterRowData -> {

            TVariableInfo tVariableInfo = tVariableInfos.stream().filter(t ->
                    t.getSourceTableField().equals(afterRowData.getColumn())
            ).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(tVariableInfo)) {
                if (afterRowData.getMysqlType().equals("datetime")) {
                    if (!StringUtils.isEmpty(afterRowData.getValue())) {
                        fact.put(tVariableInfo.getFiledName(), String.valueOf(LocalDateTime.parse(afterRowData.getValue(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
                        fact.put(tVariableInfo.getFiledName() + "Flag", afterRowData.isUpdated() ? "1" : "0");
                    }
                } else {
                    if (!StringUtils.isEmpty(afterRowData.getValue())) {
                        fact.put(tVariableInfo.getFiledName(), afterRowData.getValue());
                        fact.put(tVariableInfo.getFiledName() + "Flag", afterRowData.isUpdated() ? "1" : "0");
                    }
                }
            }

        });
    }
}
