//package com.fotile.msg.compiler.ReceiptController;
//
//import com.fotile.framework.web.BaseController;
//import com.fotile.msg.compiler.mq.SendMsgChannel;
//import com.fotile.msg.compiler.service.CompilerService;
//import com.fotile.msg.wait.pojo.dto.MsgSendWaitMQDTO;
//import io.swagger.annotations.Api;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cloud.stream.annotation.StreamListener;
//import org.springframework.messaging.Message;
//import org.springframework.messaging.MessageHeaders;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequestMapping("/api/compiler")
//@Api("编译消息处理器")
//@Slf4j
//public class CompilerController extends BaseController {
//    private CompilerService compilerService;
//
//    @Autowired
//    public CompilerController(CompilerService compilerService) {
//        this.compilerService = compilerService;
//    }
//
//    @StreamListener(SendMsgChannel.SEND_MSG_INPUT)
//    public void receive(Message<MsgSendWaitMQDTO> message) {
//        MessageHeaders headers = message.getHeaders();
//        try {
//            compilerService.compileMsg(message.getPayload());
//
//        }catch (Exception e){
//            log.error("编译消息的mq处理失败",e);
//        }
//
//    }
//}
