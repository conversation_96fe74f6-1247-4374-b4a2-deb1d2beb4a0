package com.fotile.msg.sender.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.msg.sender.pojo.entity.TemplateSmsRecordEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateSmsRecordMapper extends BaseMapper<TemplateSmsRecordEntity> {
    int updateBatch(List<TemplateSmsRecordEntity> list);

    int updateBatchSelective(List<TemplateSmsRecordEntity> list);

    int batchInsert(@Param("list") List<TemplateSmsRecordEntity> list);

    int insertOrUpdate(TemplateSmsRecordEntity record);

    int insertOrUpdateSelective(TemplateSmsRecordEntity record);
}