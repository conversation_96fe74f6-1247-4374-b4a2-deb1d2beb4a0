package com.fotile.msg.sender.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.msg.sender.pojo.entity.MsgSendRecordEntity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface MsgSendRecordMapper extends BaseMapper<MsgSendRecordEntity> {
    int updateBatch(List<MsgSendRecordEntity> list);

    int updateBatchSelective(List<MsgSendRecordEntity> list);

    int batchInsert(@Param("list") List<MsgSendRecordEntity> list);

    int insertOrUpdate(MsgSendRecordEntity record);

    int insertOrUpdateSelective(MsgSendRecordEntity record);
}