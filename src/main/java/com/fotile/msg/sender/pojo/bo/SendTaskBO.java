package com.fotile.msg.sender.pojo.bo;

import com.fotile.msg.sender.pojo.entity.MsgUserConfigEntity;
import com.fotile.msg.sender.pojo.model.content.ContentModel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.Set;
/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
public class SendTaskBO {
    /**
     * 消息模板Id
     */
    private Long messageTemplateId;
    /**
     * 业务Id(数据追踪使用)
     */
    private String businessId;

    /**
     * 接收者
     */
    private Map<String,String> receiver;



    /**
     * 发送的Id类型
     */
    @Deprecated
    private Integer idType;

    /**
     * 发送渠道
     * 10:网易云短信
     */
    private Integer sendChannel;

    /**
     * 屏蔽类型
     */
    @Deprecated
    private Integer shieldType;
    /**
     * 渠道: s0011 幸福家
     * s0010 云管理
     */
    private String channelCode;

    /**
     * 发送文案模型
     * message_template表存储的content是JSON(所有内容都会塞进去)
     * 不同的渠道要发送的内容不一样(比如发push会有img，而短信没有)
     * 所以会有ContentModel
     */
    private ContentModel contentModel;



    /**
     * 消息类型
     */
    private String msgType;

    private String secondCategory;

    /**
     * 发送账号--appkey
     */
    private String sendAccount;
    /**
     * 声音
     */
    private String voice;
    /**
     * 声音编码
     */
    private Integer voiceCode;

    private String nodeCode;

    private String userType;
    /**
     * 发送者
     */
    private List<MsgUserConfigEntity> msgUserConfigEntityList;
}
