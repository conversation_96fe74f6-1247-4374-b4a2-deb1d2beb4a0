package com.fotile.msg.sender.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "`msgcenter`.`msg_type_config`")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "`msgcenter`.`msg_type_config`")
public class MsgTypeConfigEntity extends AuditingEntity {
    /**
     * 消息类型
     */
    @TableField(value = "`msg_type`")
    @ApiModelProperty(value = "消息类型")
    private String msgType;

    /**
     * 是否开启 0 否 1 是
     */
    @TableField(value = "`enable`")
    @ApiModelProperty(value = "是否开启 0 否 1 是")
    private Integer enable;

    /**
     * 图标
     */
    @TableField(value = "`icon`")
    @ApiModelProperty(value = "图标")
    private String icon;

    /**
     * 消息类型名称
     */
    @TableField(value = "`msg_type_name`")
    @ApiModelProperty(value = "消息类型名称")
    private String msgTypeName;

    /**
     * 渠道 code
     */
    @TableField(value = "`channel_code`")
    @ApiModelProperty(value = " 渠道 code")
    private String channelCode;

    /**
     * 顺序
     */
    @TableField(value = "`sort`")
    @ApiModelProperty(value = "顺序")
    private Integer sort;
}