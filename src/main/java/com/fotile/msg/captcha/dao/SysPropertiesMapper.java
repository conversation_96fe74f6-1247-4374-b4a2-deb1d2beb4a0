package com.fotile.msg.captcha.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.msg.captcha.pojo.SysPropertiesEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysPropertiesMapper extends BaseMapper<SysPropertiesEntity> {
    int updateBatch(List<SysPropertiesEntity> list);

    int updateBatchSelective(List<SysPropertiesEntity> list);

    int batchInsert(@Param("list") List<SysPropertiesEntity> list);

    int insertOrUpdate(SysPropertiesEntity record);

    int insertOrUpdateSelective(SysPropertiesEntity record);
}