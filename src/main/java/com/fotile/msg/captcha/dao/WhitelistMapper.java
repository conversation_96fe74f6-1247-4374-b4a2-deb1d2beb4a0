package com.fotile.msg.captcha.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.msg.captcha.pojo.entity.WhitelistEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WhitelistMapper extends BaseMapper<WhitelistEntity> {
    int updateBatch(List<WhitelistEntity> list);

    int updateBatchSelective(List<WhitelistEntity> list);

    int batchInsert(@Param("list") List<WhitelistEntity> list);

    int insertOrUpdate(WhitelistEntity record);

    int insertOrUpdateSelective(WhitelistEntity record);
}