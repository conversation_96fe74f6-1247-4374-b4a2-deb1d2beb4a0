package com.fotile.msg.wait.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@ApiModel(value = "待发消息的目标用户", parent = AuditingEntity.class)
@Entity
@TableName(value = "msg_send_target", schema = "msgcenter")
@EqualsAndHashCode(of = {"userId","agentToken"})
public class MsgSendTargetEntity extends AuditingEntity {
    @ApiModelProperty("keycloak 用户id")
    private String userId;
    @ApiModelProperty("keycloak 用户realm域")
    private String userRealm;
    @ApiModelProperty("待发消息id")
    private Long msgSendWaitId;
    @ApiModelProperty("发送对象的唯一标示[比如微信的openid,又比如用户手机号，或者极光的regid 或者极光的别名，或者极光tag,或者email]，当有这个值时，忽略user_id和user_realm.直接针对agent_token进行发送. [当有agentToken时会忽略userId]")
    private String agentToken;
}
