package com.fotile.marketingcenter.userClues.pojo.entity;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Entity;
import java.util.Date;

@Data
@Entity
@ApiModel(value = "销售线索表", description = "销售线索表")
@TableName(value = "user_clues", schema = "marketingcenter")
//原表salesLeads
public class UserClues extends AuditingEntity {

    /**
     * 销售线索类型 1.内购留资 2.外购留资
     */
    private String type;

    /**
     * 客户ID
     */
    private String userId;

    /**
     * 小区ID
     */
    private Long villageId;

    /**
     * 地址ID
     */
    private Long addressId;


    /**
     * 负责人ID
     */
    private String chargeUserId;

    /**
     * 负责人名字
     */
    @FieldEncrypt
    private String chargeUserName;

    /**
     * 负责人编码
     */
    private String chargeCode;

    /**
     * 负责人手机
     */
    @FieldEncrypt
    private String chargePhone;
    /**
     * 协助人名字
     */
    @FieldEncrypt
    private String assistUserName;

    /**
     * 协助人ID
     */
    private String assistUserId;

    /**
     * 顾客名称 -- 门店运营中获取
     */
    @FieldEncrypt
    private String customerName;

    /**
     * 顾客电话 -- 门店运营中获取
     */
    @FieldEncrypt
    private String customerPhone;

    /**
     * 授权截图 -- 门店运营中获取
     */
    private String screenshots;

    /**
     * 线索分类 -- 门店运营中获取
     */
    private String cluesType;

    /**
     * 线索来源 -- 门店运营中获取
     */
    private String cluesSource;

    /**
     * 线索备注 -- 门店运营中获取
     */
    private String cluesNote;

    /**
     * 线索等级 -- 门店运营中获取
     */
    private String cluesLevel;

    /**
     * 是否进店 -- 门店运营中获取0:否 1:是（默认为否）
     */
    private String isIntoStores;

    /**
     * 预埋烟管 -- 门店运营中获取0:否 1:是（默认为否）
     */
    private String buryPipe;

    /**
     * 装修进度 -- 门店运营中获取
     */
    private String decorateProgres;

    /**
     * 意向产品 -- 门店运营中获取
     */
    private String intentionProduct;

    /**
     * 服务流程 -- 门店运营中获取
     */
    private String serviceProcess;

    /**
     * 活动ID
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 活动名称
     */
    @TableField(value = "activity_name")
    private String activityName;

    /**
     * 状态: 1.未处理 2.已处理 3.已成单
     */
    @TableField(value = "status")
    private String status;

    /**
     * 状态名称
     */
    @TableField(value = "state_name")
    private String stateName;

    /**
     * 其他说明
     */
    @TableField(value = "others")
    private String others;

    /**
     * 关系
     */
    @TableField(value = "nexus")
    private String nexus;

    /**
     * 意向
     */
    @TableField(value = "intention")
    private String intention;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 分公司名称
     */
    @TableField(value = "company_name")
    private String companyName;
    /**
     * 分公司id
     */
    @TableField(value = "company_id")
    private Long companyId;
    /**
     * 上级员工名称
     */
    @TableField(value = "superior_staff_name")
    @FieldEncrypt
    private String superiorStaffName;
    /**
     * 上级员工userId
     */
    @TableField(value = "superior_staff_id")
    private String superiorStaffId;
    /**
     * 上级员工手机号
     */
    @TableField(value = "superior_staff_mobile")
    @FieldEncrypt
    private String superiorStaffMobile;
    //    @TableField(value = "superior_staff_department")
//    @ApiModelProperty("上级员工所属部门")
//    private String superiorStaffDepartment;
//    @TableField(value = "superior_staff_order")
//    @ApiModelProperty("上级员工所属部门排序")
//    private String superiorStaffOrder;
//    @TableField(value = "superior_staff_position")
//    @ApiModelProperty("上级员工职务信息")
//    private String superiorStaffPosition;
    /**
     * 性别
     */
    @TableField(value = "gender")
    private String gender;
    /**
     * 门店名称
     */
    @TableField(value = "stroe_name")
    private String stroeName;
    /**
     * 门店Code
     */
    @TableField(value = "stroe_code")
    private String stroeCode;
    /**
     * 门店id
     */
    @TableField(value = "stroe_id")
    private String stroeId;

    /**
     * 来源频道Code
     */
    @TableField(value = "radio_code")
    private String radioCode;

    /**
     * 来源渠道Code
     */
    @TableField(value = "channel_code")
    private String channelCode;

    /**
     * 水厨电主键
     */
    @TableField(value = "source_id")
    private String sourceId;
    /**
     * 顾客id
     */
    @TableField(value = "customer_id")
    private Long customerId;


    /**
     * 客服
     */
    @TableField(value = "custom_service_code")
    private String customServiceCode;

    /**
     * 审核状态 1.未审核 2.审核通过 3未通过 40 未提交
     */
    @TableField(value = "audit_status")
    private String auditStatus;
    /**
     * 跟进状态
     */
    @TableField(value = "follow_up_status")
    private String followUpStatus;
    /**
     * 户型", example = "户型
     */
    @TableField(value = "house_type")
    private String houseType;

    /**
     * 房型面积", example = "房型面积
     */
    @TableField(value = "house_area")
    private String houseArea;
    /**
     * 厨房形状", example = "厨房形状
     */
    @TableField(value = "kitchen_type")
    private String kitchenType;
    /**
     * 厨房面积", example = "厨房面积
     */
    @TableField(value = "kitchen_area")
    private String kitchenArea;
    /**
     * 总得分", example = "总得分
     */
    @TableField(value = "total_score")
    private Long totalScore;
    /**
     * 省code
     */
    private Long provinceCode;
    /**
     * 省名字
     */
    private String province;
    /**
     * 城市code
     */
    private Long cityCode;
    /**
     * 城市名字
     */
    private String city;
    /**
     * 县/区code
     */
    private Long areaCode;
    /**
     * 县/区
     */
    private String area;
    /**
     * 审核时间", example = "审核时间
     */
    @TableField(value = "audit_time")
    private Date auditTime;
    /**
     * 是否加微信", example = "1
     */
    @TableField(value = "is_join_wechat")
    private String isJoinWechat;
    /**
     * 是否成交 1是 0否 ", example = "1
     */
    @TableField(value = "is_deal")
    private String isDeal;
    /**
     * 回访状态", example = "1
     */
    @TableField(value = "visit_status")
    private String visitStatus;
    /**
     * 回访时间", example = "1
     */
    @TableField(value = "visit_time")
    private Date visitTime;
    /**
     * 跟进内容
     */
    private String contentText = "";
    /**
     * 服务动作
     */
    private String serviceAction;
    /**
     * 最后修改时间
     */
    private Date reviseTime;
    //新增字段
    /**
     * 家居服务类
     */
    private String homeService;
    /**
     * 基础装修类
     */
    private String baseDecoration;
    /**
     * 非方太家电服务类
     */
    private String noApplianceService;
    /**
     * 木质服务类
     */
    private String woodService;
    /**
     * 潜客服务
     */
    private String latentCustomerService;
    /**
     * 成交服务
     */
    private String dealService;
    /**
     * 用户服务
     */
    private String customerService;
    /**
     * 来源说明", example = "1
     */
    private String sourcesInstructions;
    /**
     * 最新服务动作", example = "1
     */
    private String lastServiceAction;
    /**
     * 数据来源", example = "1
     */
    private String utmSource;

    /**
     * 数据来源", example = "1
     */
    private Date fundingTime;
    /**
     * 创建人", example = "111
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 回访状态", example = "111
     */
    @TableField(value = "review_status")
    private String reviewStatus;
    /**
     * 老用户", example = "111
     */
    @TableField(value = "regular_subscriber")
    private String regularSubscriber;


    /**
     * 用户互动类型", example = "1
     */
    @TableField(value = "detail_type_value")
    private Long detailTypeValue;

    /**
     * 生日
     */
    private Date birthday;

    @FieldEncrypt
    private String address;

    /**
     * 设计师id", example = "1
     */
    @TableField(value = "decorate_designer_id")
    private Long decorateDesignerId;
    /**
     * 设计师Json", example = "1
     */
    @TableField(value = "decorate_designer_json")
    private String decorateDesignerJson;
    /**
     * 所属大区Code", example = "1
     */
    @TableField(value = "district_code")
    private String districtCode;
    /**
     * 所属大区展示", example = "1
     */
    @TableField(value = "district_value")
    private String districtValue;
    /**
     * 经销商id", example = "1
     */
    @TableField(value = "distributor_id")
    private String distributorId;
    /**
     * 经销商名称", example = "1
     */
    @TableField(value = "distributor_name")
    private String distributorName;
    /**
     * 订单状态
     */
    @TableField(value = "order_status")
    private String orderStatus;
    /**
     * 成交时间
     */
    @TableField(value = "deal_order_time")
    private Date dealOrderTime;
    /**
     * 是否成交 0否 1是
     */
    @TableField(value = "is_make_bargain")
    private String isMakeBargain;
    /**
     * 交易状态 1001未交款2001交定金3001交全款
     */
    @TableField(value = "pay_status")
    private String payStatus;
    /**
     * 成交商品  1001含X套系2001不含X套系
     */
    @TableField(value = "make_bargain_product")
    private String makeBargainProduct;
    /**
     * 成交套系：1001四件套及以上，2001四件套以下
     */
    @TableField(value = "make_bargain_system")
    private String makeBargainSystem;
    /**
     * 成交套系：1001四件套及以上，2001四件套以下
     */
    @TableField(value = "clues_source_value")
    private String cluesSourceValue;
    /**
     * 社区会员id
     */
    @TableField(value = "community_member_id")
    private Long communityMemberId;
    /**
     * 社区会员对象json
     */
    @TableField(value = "community_member_json")
    private String communityMemberJson;
    /**
     * 门店类型code
     */
    @TableField(value = "store_type_code")
    private String storeTypeCode;
    /**
     * 门店类型名称
     */
    @TableField(value = "store_type_name")
    private String storeTypeName;

    /**
     * 是否上门设计
     */
    @TableField(value = "is_come_devise")
    private String isComeDevise;


    /**
     * 是否上门设计时间，否变更为是的时间
     */
    @TableField(value = "is_come_devise_time")
    private Date isComeDeviseTime;


    /**
     * 渠道大类code
     */
    @TableField(value = "channel_category_code")
    private String channelCategoryCode;
    /**
     * 渠道大类名称
     */
    @TableField(value = "channel_category_name")
    private String channelCategoryName;
    /**
     * 渠道细分code
     */
    @TableField(value = "channel_subdivide_code")
    private String channelSubdivideCode;
    /**
     * 渠道细分名称
     */
    @TableField(value = "channel_subdivide_name")
    private String channelSubdivideName;
    /**
     * 小区名称
     */
    @TableField(value = "village_name")
    private String villageName;
    /**
     * 创建人名称
     */
    @TableField(value = "create_user_name")
    @FieldEncrypt
    private String createUserName;

    /**
     * 丢单说明
     */
    @TableField(value = "lost_order_value")
    private String lostOrderValue;
    /**
     * 丢单说明code
     */
    @TableField(value = "lost_order_code")
    private String lostOrderCode;


    /**
     * 家装公司编码
     */
    private String decorateCompanyCode;

    /**
     * 家装公司名称
     */
    @FieldEncrypt
    private String decorateCompanyName;

    /**
     * 设计师编码
     */
    private String designerCode;

    /**
     * 设计师名称
     */
    @FieldEncrypt
    private String designerName;

    /**
     * 设计师手机号
     */
    @FieldEncrypt
    private String designerPhone;

    /**
     * 类型 1：家装  2：异业三工 3 设计师
     */
    private Long decorateCompanyType;

    /**
     * 备用电话
     */
    private String standbyPhone;
    /**
     * 入户回访
     */
    private String isHouserholdsVisit;
    /**
     * 交房时间
     */
    private Date makeRoomTime;
    /**
     * 装修类型
     */
    private String decorateType;
    /**
     * 首次跟进时间
     */
    private Date firstFollowTime;
    /**
     * 首次进店时间
     */
    private Date firstInstoreTime;
    /**
     * 丢单时间
     */
    private Date lostOrderTime;
    /**
     * 最后跟进时间
     */
    private Date lastFollowTime;
    /**
     * 预算
     */
    private String budgetAccounting;
    /**
     * 修改业务员时间
     */
    private Date modifiedChargeUserDate;

    /**
     * 线索分配前序负责人时间
     */
    private Date preSalesmanChangeDate;

    /**
     * 二级丢单原因
     */
    private String lostOrderSubValue;
    /**
     * 二级丢单code
     */
    private String lostOrderSubCode;
    /**
     * 家装公司类别
     */
    private String decorateCompanyCategory;
    /**
     * 业务员ID
     */
    private Long salesmanId;
    /**
     * 业务员
     */
    @FieldEncrypt
    private String salesman;
    /**
     * 丢单原因-自我不足code
     */
    private String selfLackReviewStatusCode;

    /**
     * 丢单原因-自我不足
     */
    private String selfLackReviewStatusValue;
    /**
     * 是否成交定金订单， 1.是  0.否
     */
    private Integer orderEarnestFlag;
    /**
     * 订单确定时间
     */
    private Date orderEarnestTime;
    /**
     * 删除跟进审核标志
     */
    private Integer deleteInformationFlag;
    /**
     * 开关
     */
    private Integer keepTouchFlag;
    /**
     * 楼栋
     */
    @TableField(exist = false)
    private String building;
    /**
     * 单元
     */
    @TableField(exist = false)
    private String unit;
    /**
     * 门牌号
     */
    @TableField(exist = false)
    private String houseNumber;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    @FieldEncrypt
    private String operatorName;

    /**
     * 最近编辑时间
     */
    private Date lastEditTime;

    /**
     * 下次跟进时间
     */
    @TableField(value = "`next_follow_up_time`")
    private Date nextFollowUpTime;


    /**
     * 上门设计状态,1:带上门，2：已测量，3：已出方案
     */
    private Integer comeDeviseStatus;

    /**
     * 是否场景化交互：1是 0否
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Integer isSceneInteraction;

    /**
     * 客情带单类型 1.门店员工亲戚朋友
     * 2.老板亲戚朋友
     * 3.办事处员工亲戚朋友
     * 4.总部优惠券（仅慈溪、宁波）
     */
    private Integer customerBringType;
    /**
     * 券码 code
     */
    private String cardNo;
    /**
     * 是否存在券码
     */
    private Integer couponFlag;

    /**
     * 更新时间
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;


    //整合营销相关
    @TableField(exist = false)
    private Long integrateActivityId;
    @TableField(exist = false)
    private String integrateActivityCode;
    @TableField(exist = false)
    private String integrateActivityName;

    private Date firstCancelTime;

    /**
     *是否测量完整尺寸 （1.是 0.否）
     */
    private Integer kitchenDesignFlag;
    /**
     * 是否上传手绘图（1.是 0.否）
     */
    private Integer freehandSketchingFlag;
    /**
     * 是否含换装报告（1.是 0.否）
     */
    private Integer costumeChangeReportFlag;

    private Integer frequencyOpenSeas;
    /**
     * 营销支付活动线索来源类型 0：新增线索 1：存留线索
     */
    @TableField(exist = false)
    private Integer clueType;

    /**
     * 设计师俱乐部会员id
     */
    private Long designerClubMemberId;
    /**
     * 设计师俱乐部会员等级
     */
    private String designerClubMemberGrade;

    private String storeSubChannelCode;

    private Integer omoFlag;
}
