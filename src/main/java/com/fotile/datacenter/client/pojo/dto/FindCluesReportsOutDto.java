package com.fotile.datacenter.client.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class FindCluesReportsOutDto implements Serializable {
    @ApiModelProperty("所属大区Code")
    private String districtCode;
    @ApiModelProperty("所属大区展示")
    private String districtValue;
    @ApiModelProperty("所属公司")
    private String companyName;
    @ApiModelProperty("所属公司id")
    private Long companyId;
    @ApiModelProperty("经销商id")
    private String distributorId;
    @ApiModelProperty("经销商名称")
    private String distributorName;
    @ApiModelProperty("所属门店id")
    private Long stroeId;
    @ApiModelProperty("所属门店")
    private String stroeName;
    @ApiModelProperty(value = "负责人ID")
    private String chargeUserId;
    @ApiModelProperty(value = "负责人名字")
    private String chargeUserName;
    @ApiModelProperty(value = "线索数")
    private Long cluesNums;
    @ApiModelProperty(value = "进店数")
    private Long intoStoresNums;
    @ApiModelProperty(value = "预埋烟管数")
    private Long buryPipeNums;
    @ApiModelProperty(value = "成交数")
    private Long dealNums;
    @ApiModelProperty(value = "上门设计")
    private Long designNums;
    private Long fzrjdNums;//分自然进店数
    private Long stockCluesNums;//总存量数
    private Long followStockCluesNums;//跟进存量数
    private Long manualCluesFollowNums;//人工写跟进次数
    /**
     * 引流渠道保有量
     */
    @ApiModelProperty(value = "引流渠道保有量")
    private BigDecimal drainageChannelRetain;
    /**
     * 引流渠道活跃量
     */
    @ApiModelProperty(value = "引流渠道活跃量")
    private BigDecimal drainageChannelActive;

    private String developSalesmanId;
    private String developSalesmanCode;
    private String developSalesmanName;
    private String developSalesmanPhone;

    //2021-1-6 迭代新增2个参数
    @ApiModelProperty(value = "跟进数")
    private Long followUpNums;
    @ApiModelProperty(value = "丢单数")
    private Long lostOrderNums;

}
