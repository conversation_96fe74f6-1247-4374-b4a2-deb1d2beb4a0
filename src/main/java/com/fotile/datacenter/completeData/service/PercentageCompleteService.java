package com.fotile.datacenter.completeData.service;

import com.fotile.datacenter.client.OmsCenterclient;
import com.fotile.datacenter.client.UserCluesClient;
import com.fotile.datacenter.client.pojo.dto.FindCluesReportsInDto;
import com.fotile.datacenter.client.pojo.dto.FindCluesReportsOutDto;
import com.fotile.datacenter.common.constant.CommonConst;
import com.fotile.datacenter.completeData.pojo.dto.*;
import com.fotile.datacenter.completeData.pojo.mapper.PercentageCompleteMapper;
import com.fotile.datacenter.marketClues.service.CluesReportsService;
import com.fotile.datacenter.tMarketTargetData.dao.MarketDateInfoDao;
import com.fotile.datacenter.tMarketTargetData.dao.MarketTargetDataDao;
import com.fotile.datacenter.tMarketTargetData.pojo.dto.SelectMarketDateInfoOutDto;
import com.fotile.datacenter.util.*;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.adapters.RefreshableKeycloakSecurityContext;
import org.keycloak.adapters.springsecurity.account.SimpleKeycloakAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class PercentageCompleteService {
    @Autowired
    UserCluesClient userCluesClient;
    @Autowired
    CompleteDataService completeDataService;
    @Autowired
    UserAuthorConfig userAuthorConfig;
    @Autowired
    OmsCenterclient omsCenterclient;

    @Autowired
    SelectStatementUtils selectStatementUtils;

    @Autowired
    private MarketDateInfoDao marketDateInfoDao;

    @Autowired
    private MarketTargetDataDao marketTargetDataDao;

    @Autowired
    private UserAuthorUtil userAuthorUtil;

    @Autowired
    private CluesReportsService cluesReportsService;


    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd hh:mm:ss");

    /**
     * 门店业务目标完成率
     *
     * @param storePercentageCompleteDTO
     */
    public List<StorePercentageCompleteOutDto> storePercentageComplete(StorePercentageCompleteDTO storePercentageCompleteDTO) {
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();


        String startTime = null;
        String endTime = null;

        Map map = completeDataService.getDateValue(storePercentageCompleteDTO.getDateType(), storePercentageCompleteDTO.getDateValue(), storePercentageCompleteDTO.getWeeklyShowName());
        if (map != null && map.get("startTime") != null && map.get("endTime") != null) {
            startTime = map.get("startTime").toString();
            endTime = map.get("endTime").toString();
        } else {
            throw new BusinessException("传入时间值错误，请核对！");
        }
        //初始化查询条件--订单
        StatementInDTO statementInDTO = null;
        try {
            statementInDTO = setStatementInDTO(sdf.parse(startTime), sdf.parse(endTime), null);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //初始化查询条件--线索
        FindCluesReportsInDto cluesReportsInDto = null;
        try {
            cluesReportsInDto = getFindCluesReportsInDto(sdf.parse(startTime), sdf.parse(endTime), null);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //2020.8.4  为解决避免因查询结果为空时throw掉，特此修改记录接口的执行位置在查询之前
        String queryWordValue = storePercentageCompleteDTO.getQueryWordValue();
        //3.写入业务员查询字段记录-mq
        try {
            AddQueryRecordInDto addQueryRecordInDto = new AddQueryRecordInDto();
            addQueryRecordInDto.setChargeUesrId(String.valueOf(userAuthor.getSalesmanId() == null ? "" : userAuthor.getSalesmanId()));
            addQueryRecordInDto.setQueryWordValue(queryWordValue);
            addQueryRecordInDto.setDateType(storePercentageCompleteDTO.getDateType());
            addQueryRecordInDto.setDateValue(storePercentageCompleteDTO.getDateValue());
            addQueryRecordInDto.setWeeklyShowName(storePercentageCompleteDTO.getWeeklyShowName());
            completeDataService.insertQueryRecord(addQueryRecordInDto);
        } catch (Exception e) {
            log.error("业务员查询字段SQL错误：" + e.getMessage());
        }

        //1.拆分查询字段
        if (StringUtils.isEmpty(storePercentageCompleteDTO.getQueryWordValue())) {
            throw new BusinessException("查询字段不能为空");
        }
        //2.根据权限门店查询目标信息/或者根据获得的数据查询门店目标
        GetCompleteDataInDto getCompleteDataInDto = PercentageCompleteMapper.INSTANCE.getCompleteDataInDtoTOStorePercentageCompleteDTO(storePercentageCompleteDTO);
        List<GetStoreCompleteOutDto> storeCompleteDtos = completeDataService.selectStoreCompleteData(getCompleteDataInDto);
        if (CollectionUtils.isEmpty(storeCompleteDtos) || storeCompleteDtos.size() == 0) {
            throw new BusinessException("当前门店还未制定目标,\n" +
                    "请前往页面右上角的\"目标编辑\"添加");
        }


        //1.1根据查询字段判断调用接口

        Integer isQueryClues = 0;
        Integer isQueryOrder = 0;
        Integer isQueryDrainage = 0;
        for (String s : queryWordValue.split(",")) {
            //1.2新增线索  从线索表获取，成交 进店 上门设计 预埋烟管从跟进表获取
            if ("clues_nums".equals(s) || "deal_nums".equals(s) || "into_stores_nums".equals(s) || "design_nums".equals(s) || "bury_pipe_nums".equals(s)) {
                isQueryClues = 1;
            }
            //1.3销售额，台数从订单获取
            if ("goods_count".equals(s) || "sales_amount".equals(s)) {
                isQueryOrder = 1;
            }
            //1.4 引流渠道
            if ("drainage_channel_retain".equals(s) || "drainage_channel_active".equals(s)) {
                isQueryDrainage = 1;
            }

        }
        //查询线索
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = new LinkedList<>();
        if (isQueryClues.equals(1)) {
            try {

                cluesReportsInDto.setStoreIds(storeCompleteDtos.stream().map(s -> s.getStoreOrgId()).collect(Collectors.toList()));
                findCluesReportsOutDtos = cluesReportsService.findCluesReportsStoreAll(cluesReportsInDto);
            } catch (Exception e) {

                log.error(String.format("门店业务目标完成率,原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
                throw new BusinessException("系统异常");
            }
        }

        //查询订单
        SelectStatementOutDTO selectStatementOutDTO = new SelectStatementOutDTO();
        if (isQueryOrder.equals(1)) {
            statementInDTO.setStoreIds(storeCompleteDtos.stream().map(s -> s.getStoreOrgId()).collect(Collectors.toList()));
            selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = new HashedMap<>();
        if (isQueryDrainage.equals(1)) {
            statementInDTO.setStoreIds(storeCompleteDtos.stream().map(s -> s.getStoreOrgId()).collect(Collectors.toList()));
            drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);
        }

        //4.计算完成率
        //目标
        Map<Long, GetStoreCompleteOutDto> storeCompleteDtoMap = storeCompleteDtos.stream().collect(Collectors.toMap(GetStoreCompleteOutDto::getStoreOrgId, Function.identity(), (key1, key2) -> key2));

        //4.1计算新增线索  从线索表获取，成交 进店 上门设计 预埋烟管从跟进表获取
        //判断是否线索是否为空
        Map<Long, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(storeCompleteDtos) && storeCompleteDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(FindCluesReportsOutDto::getStroeId, Function.identity(), (key1, key2) -> key2));
        }
        //订单
        Map<Long, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(StatementOutDTO::getStoreId, Function.identity(), (key1, key2) -> key2));
        }
        //两个map都不能为空
        LinkedList<StorePercentageCompleteOutDto> storePercentageCompleteOutDtos = new LinkedList<>();
        BigDecimal totalCluesCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesTargetNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherTargetNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresCompleteNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresTargetNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsCompleteNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsTargetNums = BigDecimal.ZERO;
        BigDecimal totalDesignCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDesignTargetNums = BigDecimal.ZERO;
        BigDecimal totalDealCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDealTargetNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountTargetNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountTargetNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowCompleteNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveTargetNums = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(storeCompleteDtoMap)) {
            for (Map.Entry<Long, GetStoreCompleteOutDto> entry : storeCompleteDtoMap.entrySet()) {

                //获取目标值
                GetStoreCompleteOutDto addStoreCompleteDto = entry.getValue();
                if (addStoreCompleteDto == null) {
                    addStoreCompleteDto = new GetStoreCompleteOutDto();
                }

                //合计目标

                totalSalesAmountTargetNums = totalSalesAmountTargetNums.add(addStoreCompleteDto.getSalesAmount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getSalesAmount());
                totalGoodsCountTargetNums = totalGoodsCountTargetNums.add(addStoreCompleteDto.getGoodsCount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getGoodsCount());
                totalBuryPipeNumsTargetNums = totalBuryPipeNumsTargetNums.add(addStoreCompleteDto.getBuryPipeNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getBuryPipeNums());
                totalIntoStoresTargetNums = totalIntoStoresTargetNums.add(addStoreCompleteDto.getIntoStoresNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getIntoStoresNums());
                totalCluesTargetNums = totalCluesTargetNums.add(addStoreCompleteDto.getCluesNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesNums());
                totalCluesOtherTargetNums = totalCluesTargetNums.add(addStoreCompleteDto.getCluesOtherNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesOtherNums());
                totalDealTargetNums = totalDealTargetNums.add(addStoreCompleteDto.getDealNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDealNums());
                totalDesignTargetNums = totalDesignTargetNums.add(addStoreCompleteDto.getDesignNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDesignNums());
                totalManualCluesFollowTargetNums = totalManualCluesFollowTargetNums.add(addStoreCompleteDto.getManualCluesFollowNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getManualCluesFollowNums());
                totalDrainageChannelRetainTargetNums = totalDrainageChannelRetainTargetNums.add(addStoreCompleteDto.getDrainageChannelRetain() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelRetain());
                totalDrainageChannelActiveTargetNums = totalDrainageChannelActiveTargetNums.add(addStoreCompleteDto.getDrainageChannelActive() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelActive());

                //初始化返回数据
                StorePercentageCompleteOutDto storePercentageCompleteOutDto = new StorePercentageCompleteOutDto();
                storePercentageCompleteOutDto.setStoreName(addStoreCompleteDto.getStoreName());
                storePercentageCompleteOutDto.setStoreId(addStoreCompleteDto.getStoreOrgId());
                storePercentageCompleteOutDto.setStoreCode(addStoreCompleteDto.getStoreCode());
                //判断key是否存在

                BigDecimal cluesNums = BigDecimal.ZERO;
                BigDecimal cluesOtherNums = BigDecimal.ZERO;
                BigDecimal intoStoresNums = BigDecimal.ZERO;
                BigDecimal buryPipeNums = BigDecimal.ZERO;
                BigDecimal dealNums = BigDecimal.ZERO;
                BigDecimal designNums = BigDecimal.ZERO;
                BigDecimal manualCluesFollowNums = BigDecimal.ZERO;
                BigDecimal drainageChannelRetain = BigDecimal.ZERO;
                BigDecimal drainageChannelActive = BigDecimal.ZERO;

                List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();
                if (findCluesReportsOutDtoMap != null && findCluesReportsOutDtoMap.containsKey(entry.getKey())) {
                    //获取实际对象
                    FindCluesReportsOutDto findCluesReportsOutDto = findCluesReportsOutDtoMap.get(entry.getKey());

                    if (findCluesReportsOutDto != null) {
                        cluesNums = new BigDecimal(findCluesReportsOutDto.getCluesNums() == null ? 0L : findCluesReportsOutDto.getCluesNums());
                        cluesOtherNums = new BigDecimal(findCluesReportsOutDto.getCluesOtherNums() == null ? 0L : findCluesReportsOutDto.getCluesOtherNums());
                        intoStoresNums = new BigDecimal(findCluesReportsOutDto.getIntoStoresNums() == null ? 0L : findCluesReportsOutDto.getIntoStoresNums());
                        buryPipeNums = new BigDecimal(findCluesReportsOutDto.getBuryPipeNums() == null ? 0L : findCluesReportsOutDto.getBuryPipeNums());
                        dealNums = new BigDecimal(findCluesReportsOutDto.getDealNums() == null ? 0L : findCluesReportsOutDto.getDealNums());
                        designNums = new BigDecimal(findCluesReportsOutDto.getDesignNums() == null ? 0L : findCluesReportsOutDto.getDesignNums());
                        manualCluesFollowNums = new BigDecimal(findCluesReportsOutDto.getManualCluesFollowNums() == null ? 0L : findCluesReportsOutDto.getManualCluesFollowNums());

                        totalCluesCompleteNums = totalCluesCompleteNums.add(cluesNums);
                        totalCluesOtherCompleteNums = totalCluesOtherCompleteNums.add(cluesOtherNums);
                        totalIntoStoresCompleteNums = totalIntoStoresCompleteNums.add(intoStoresNums);
                        totalBuryPipeNumsCompleteNums = totalBuryPipeNumsCompleteNums.add(buryPipeNums);
                        totalDesignCompleteNums = totalDesignCompleteNums.add(designNums);
                        totalDealCompleteNums = totalDealCompleteNums.add(dealNums);
                        totalManualCluesFollowCompleteNums = totalManualCluesFollowCompleteNums.add(manualCluesFollowNums);
                    }

                }

                BigDecimal goodsCount = BigDecimal.ZERO;
                BigDecimal salesAmount = BigDecimal.ZERO;
                //订单
                if (statementOutDTOMap != null && statementOutDTOMap.containsKey(entry.getKey())) {

                    //获取实际对象
                    StatementOutDTO findCluesReportsOutDto = statementOutDTOMap.get(entry.getKey());
                    if (findCluesReportsOutDto != null) {
                        goodsCount = new BigDecimal(findCluesReportsOutDto.getGoodsNum() == null ? "0" : findCluesReportsOutDto.getGoodsNum());
                        salesAmount = new BigDecimal(findCluesReportsOutDto.getTotalPrice() == null ? "0" : findCluesReportsOutDto.getTotalPrice());

                        totalSalesAmountCompleteNums = totalSalesAmountCompleteNums.add(salesAmount);
                        totalGoodsCountCompleteNums = totalGoodsCountCompleteNums.add(goodsCount);
                    }

                }
                //引流渠道
                if (!CollectionUtils.isEmpty(drainageChannelOutDtoMap) && drainageChannelOutDtoMap.containsKey(entry.getKey())) {
                    DrainageChannelOutDto drainageChannelOutDto = drainageChannelOutDtoMap.get(entry.getKey());
                    if (drainageChannelOutDto != null) {
                        drainageChannelRetain = drainageChannelOutDto.getDrainageChannelRetain();
                        totalDrainageChannelRetainCompleteNums = totalDrainageChannelRetainCompleteNums.add(drainageChannelRetain);
                        drainageChannelActive = drainageChannelOutDto.getDrainageChannelActive();
                        totalDrainageChannelActiveCompleteNums = totalDrainageChannelActiveCompleteNums.add(drainageChannelActive);
                    }
                }
                if (percentageCompleteDtos == null || percentageCompleteDtos.size() == 0) {
                    //目标完成率标记点
                    percentageCompleteDtos = getPercentageCompleteDtos(queryWordValue,
                            addStoreCompleteDto.getCluesNums(),
                            addStoreCompleteDto.getCluesOtherNums(),addStoreCompleteDto.getDealNums(),
                            addStoreCompleteDto.getIntoStoresNums(), addStoreCompleteDto.getDesignNums(), addStoreCompleteDto.getBuryPipeNums(),
                            addStoreCompleteDto.getGoodsCount(), addStoreCompleteDto.getSalesAmount(), cluesNums, cluesOtherNums, dealNums,
                            intoStoresNums, designNums, buryPipeNums, goodsCount, salesAmount,addStoreCompleteDto.getManualCluesFollowNums(), manualCluesFollowNums,
                            addStoreCompleteDto.getDrainageChannelRetain(),drainageChannelRetain,addStoreCompleteDto.getDrainageChannelActive(),drainageChannelActive);
                }
                storePercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
                storePercentageCompleteOutDtos.add(storePercentageCompleteOutDto);
            }

        }
        //生成合计对象
        StorePercentageCompleteOutDto storePercentageCompleteOutDto = new StorePercentageCompleteOutDto();
        storePercentageCompleteOutDto.setStoreName("合计");
        storePercentageCompleteOutDto.setStoreId(-1L);
        List<PercentageCompleteDto> percentageCompleteDtos = getPercentageCompleteDtos(queryWordValue,
                totalCluesTargetNums, totalCluesOtherTargetNums, totalDealTargetNums,
                totalIntoStoresTargetNums, totalDesignTargetNums, totalBuryPipeNumsTargetNums,
                totalGoodsCountTargetNums, totalSalesAmountTargetNums, totalCluesCompleteNums, totalCluesOtherCompleteNums,totalDealCompleteNums,
                totalIntoStoresCompleteNums, totalDesignCompleteNums, totalBuryPipeNumsCompleteNums, totalGoodsCountCompleteNums, totalSalesAmountCompleteNums,totalManualCluesFollowTargetNums,totalManualCluesFollowCompleteNums,
                totalDrainageChannelRetainTargetNums,totalDrainageChannelRetainCompleteNums,totalDrainageChannelActiveTargetNums,totalDrainageChannelActiveCompleteNums);
        storePercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
        //重组数组
        storePercentageCompleteOutDtos.sort((x, y) -> StringUtils.compare(x.getStoreCode(), y.getStoreCode()));
        storePercentageCompleteOutDtos.addFirst(storePercentageCompleteOutDto);
        //5.返回
        return storePercentageCompleteOutDtos;
    }


    public List<ChargeUserPercentageCompleteOutDto> chageUserPercentageComplete(ChargeUserPercentageCompleteInDTO chargeUserPercentageCompleteInDTO) {

        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        //1.拆分查询字段
        if (StringUtils.isEmpty(chargeUserPercentageCompleteInDTO.getQueryWordValue())) {
            throw new BusinessException("查询字段不能为空");
        }
        //2020.8.4 特别调高了记录接口的执行顺序优先
        //3.写入业务员查询字段记录-mq
        String queryWordValue = chargeUserPercentageCompleteInDTO.getQueryWordValue();
//        try {
//            AddQueryRecordInDto addQueryRecordInDto = new AddQueryRecordInDto();
//            addQueryRecordInDto.setChargeUesrId(String.valueOf(userAuthor.getSalesmanId() == null ? "" : userAuthor.getSalesmanId()));
//            addQueryRecordInDto.setQueryWordValue(queryWordValue);
//            addQueryRecordInDto.setDateType(chargeUserPercentageCompleteInDTO.getDateType());
//            addQueryRecordInDto.setDateValue(chargeUserPercentageCompleteInDTO.getDateValue());
//            addQueryRecordInDto.setWeeklyShowName(chargeUserPercentageCompleteInDTO.getWeeklyShowName());
//            completeDataService.insertQueryRecord(addQueryRecordInDto);
//        }catch (Exception e){
//            log.error("业务员查询字段SQL错误："+e.getMessage());
//        }

        //2.根据权限门店查询目标信息/或者根据获得的数据查询门店目标
        GetCompleteDataInDto getCompleteDataInDto = PercentageCompleteMapper.INSTANCE.getCompleteDataInDtoTOChageUserPercentageCompleteDTO(chargeUserPercentageCompleteInDTO);
        List<GetStoreCompleteOutDto> storeCompleteDtos = completeDataService.selectChargeCompleteData(getCompleteDataInDto);
        if (CollectionUtils.isEmpty(storeCompleteDtos) || storeCompleteDtos.size() == 0) {
            throw new BusinessException("当前门店还未制定目标,\n" +
                    "请前往页面右上角的\"目标编辑\"添加");
        }
        String startTime = null;
        String endTime = null;

        Map map = completeDataService.getDateValue(chargeUserPercentageCompleteInDTO.getDateType(), chargeUserPercentageCompleteInDTO.getDateValue(), chargeUserPercentageCompleteInDTO.getWeeklyShowName());
        if (map != null && map.get("startTime") != null && map.get("endTime") != null) {
            startTime = map.get("startTime").toString();
            endTime = map.get("endTime").toString();
        } else {
            throw new BusinessException("传入时间值错误，请核对！");
        }
        //初始化查询条件--订单

        StatementInDTO statementInDTO = null;
        try {
            statementInDTO = setStatementInDTO(sdf.parse(startTime), sdf.parse(endTime), chargeUserPercentageCompleteInDTO.getStoreOrgId());
        } catch (ParseException e) {
            log.error(String.format("业务员业务目标完成率,原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
        }
        //初始化查询条件--线索
        FindCluesReportsInDto cluesReportsInDto = null;
        try {
            cluesReportsInDto = getFindCluesReportsInDto(sdf.parse(startTime), sdf.parse(endTime), chargeUserPercentageCompleteInDTO.getStoreOrgId());
        } catch (ParseException e) {
            log.error(String.format("业务员业务目标完成率,原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
        }
        //1.1根据查询字段判断调用接口

        Integer isQueryClues = 0;
        Integer isQueryOrder = 0;
        Integer isQueryDrainage = 0;
        for (String s : queryWordValue.split(",")) {
            //1.2新增线索  从线索表获取，成交 进店 上门设计 预埋烟管从跟进表获取
            if ("clues_nums".equals(s) || "deal_nums".equals(s) || "into_stores_nums".equals(s) || "design_nums".equals(s) || "bury_pipe_nums".equals(s)) {
                isQueryClues = 1;
            }
            //1.3销售额，台数从订单获取
            if (s.equals("goods_count") || "sales_amount".equals(s)) {
                isQueryOrder = 1;
            }
            //1.4 引流渠道
            if ("drainage_channel_retain".equals(s) || "drainage_channel_active".equals(s)) {
                isQueryDrainage = 1;
            }
        }
        //查询线索
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = new LinkedList<>();
        if (isQueryClues.equals(1)) {
            try {
                findCluesReportsOutDtos = cluesReportsService.findCluesReportsChargeUserAll(cluesReportsInDto);
            } catch (Exception e) {
                log.error(String.format("业务员业务目标完成率,原因：%s. 消息：%s", JsonUtils.toJson(e.getCause()), e.getMessage()), e);
                throw new BusinessException("系统异常");
            }
        }

        //查询订单
        SelectStatementOutDTO selectStatementOutDTO = new SelectStatementOutDTO();
        if (isQueryOrder.equals(1)) {
            selectStatementOutDTO = selectStatementUtils.getSelectStatementChargeUserOutDTO(statementInDTO);
        }
        //引流渠道分公司/设计师
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = new HashedMap<>();
        if (isQueryDrainage.equals(1)) {
            statementInDTO.setStoreIds(storeCompleteDtos.stream().map(s -> s.getStoreOrgId()).collect(Collectors.toList()));
            drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);
        }
        //4.计算完成率
        //目标
//        Map<String, GetChargeCompleteOutDto> chargeCompleteDtoMap = storeCompleteDtos.stream().collect(Collectors.toMap(GetChargeCompleteOutDto::getChargeUesrId, Function.identity(), (key1, key2) -> key2));
        Map<Long, GetStoreCompleteOutDto> chargeCompleteDtoMap = storeCompleteDtos.stream().collect(Collectors.toMap(GetStoreCompleteOutDto::getChargeUesrId, Function.identity(), (key1, key2) -> key2));
        //4.1计算新增线索  从线索表获取，成交 进店 上门设计 预埋烟管从跟进表获取
        //判断是否线索是否为空
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(storeCompleteDtos) && storeCompleteDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(FindCluesReportsOutDto::getChargeUserId, Function.identity(), (key1, key2) -> key2));
        }
        //订单
        Map<Long, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(StatementOutDTO::getChargeUserId, Function.identity(), (key1, key2) -> key2));
        }
        //两个map都不能为空
        LinkedList<ChargeUserPercentageCompleteOutDto> chargeUserPercentageCompleteOutDtos = new LinkedList<>();
        BigDecimal totalCluesCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesTargetNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherTargetNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresCompleteNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresTargetNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsCompleteNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsTargetNums = BigDecimal.ZERO;
        BigDecimal totalDesignCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDesignTargetNums = BigDecimal.ZERO;
        BigDecimal totalDealCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDealTargetNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountTargetNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountTargetNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowCompleteNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveTargetNums = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(chargeCompleteDtoMap)) {
//            for (Map.Entry<String, GetChargeCompleteOutDto> entry : chargeCompleteDtoMap.entrySet()) {
            for (Map.Entry<Long, GetStoreCompleteOutDto> entry : chargeCompleteDtoMap.entrySet()) {

                //获取目标值
                GetStoreCompleteOutDto addStoreCompleteDto = entry.getValue();
                if (addStoreCompleteDto == null) {
                    addStoreCompleteDto = new GetStoreCompleteOutDto();
                }

                //合计目标

                totalSalesAmountTargetNums = totalSalesAmountTargetNums.add(addStoreCompleteDto.getSalesAmount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getSalesAmount());
                totalGoodsCountTargetNums = totalGoodsCountTargetNums.add(addStoreCompleteDto.getGoodsCount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getGoodsCount());
                totalBuryPipeNumsTargetNums = totalBuryPipeNumsTargetNums.add(addStoreCompleteDto.getBuryPipeNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getBuryPipeNums());
                totalIntoStoresTargetNums = totalIntoStoresTargetNums.add(addStoreCompleteDto.getIntoStoresNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getIntoStoresNums());
                totalCluesTargetNums = totalCluesTargetNums.add(addStoreCompleteDto.getCluesNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesNums());
                totalCluesOtherTargetNums = totalCluesTargetNums.add(addStoreCompleteDto.getCluesOtherNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesOtherNums());
                totalDealTargetNums = totalDealTargetNums.add(addStoreCompleteDto.getDealNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDealNums());
                totalDesignTargetNums = totalDesignTargetNums.add(addStoreCompleteDto.getDesignNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDesignNums());
                totalManualCluesFollowTargetNums = totalManualCluesFollowTargetNums.add(addStoreCompleteDto.getManualCluesFollowNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getManualCluesFollowNums());
                totalDrainageChannelRetainTargetNums = totalDrainageChannelRetainTargetNums.add(addStoreCompleteDto.getDrainageChannelRetain() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelRetain());
                totalDrainageChannelActiveTargetNums = totalDrainageChannelActiveTargetNums.add(addStoreCompleteDto.getDrainageChannelActive() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelActive());

                //初始化返回数据
                ChargeUserPercentageCompleteOutDto chargeUserPercentageCompleteOutDto = new ChargeUserPercentageCompleteOutDto();
                chargeUserPercentageCompleteOutDto.setChargeUserName(addStoreCompleteDto.getChargeUesrName());
                chargeUserPercentageCompleteOutDto.setChargeUserId(addStoreCompleteDto.getChargeUesrId().toString());
                chargeUserPercentageCompleteOutDto.setChargeUesrCode(addStoreCompleteDto.getChargeUesrCode());
                //判断key是否存在

                BigDecimal cluesNums = BigDecimal.ZERO;
                BigDecimal cluesOtherNums = BigDecimal.ZERO;
                BigDecimal intoStoresNums = BigDecimal.ZERO;
                BigDecimal buryPipeNums = BigDecimal.ZERO;
                BigDecimal dealNums = BigDecimal.ZERO;
                BigDecimal designNums = BigDecimal.ZERO;
                BigDecimal manualCluesFollowNums = BigDecimal.ZERO;
                BigDecimal drainageChannelRetain = BigDecimal.ZERO;
                BigDecimal drainageChannelActive = BigDecimal.ZERO;

                List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();
                if (!CollectionUtils.isEmpty(findCluesReportsOutDtoMap) && findCluesReportsOutDtoMap.containsKey(entry.getKey().toString())) {
                    //获取实际线索对象
                    FindCluesReportsOutDto findCluesReportsOutDto = findCluesReportsOutDtoMap.get(entry.getKey().toString());

                    //线索
                    if (findCluesReportsOutDto != null) {
                        cluesNums = new BigDecimal(findCluesReportsOutDto.getCluesNums() == null ? 0L : findCluesReportsOutDto.getCluesNums());
                        cluesOtherNums = new BigDecimal(findCluesReportsOutDto.getCluesOtherNums() == null ? 0L : findCluesReportsOutDto.getCluesOtherNums());
                        intoStoresNums = new BigDecimal(findCluesReportsOutDto.getIntoStoresNums() == null ? 0L : findCluesReportsOutDto.getIntoStoresNums());
                        buryPipeNums = new BigDecimal(findCluesReportsOutDto.getBuryPipeNums() == null ? 0L : findCluesReportsOutDto.getBuryPipeNums());
                        dealNums = new BigDecimal(findCluesReportsOutDto.getDealNums() == null ? 0L : findCluesReportsOutDto.getDealNums());
                        designNums = new BigDecimal(findCluesReportsOutDto.getDesignNums() == null ? 0L : findCluesReportsOutDto.getDesignNums());
                        manualCluesFollowNums = new BigDecimal(findCluesReportsOutDto.getManualCluesFollowNums() == null ? 0L : findCluesReportsOutDto.getManualCluesFollowNums());
                        totalCluesCompleteNums = totalCluesCompleteNums.add(cluesNums);
                        totalCluesOtherCompleteNums = totalCluesOtherCompleteNums.add(cluesOtherNums);
                        totalIntoStoresCompleteNums = totalIntoStoresCompleteNums.add(intoStoresNums);
                        totalBuryPipeNumsCompleteNums = totalBuryPipeNumsCompleteNums.add(buryPipeNums);
                        totalDesignCompleteNums = totalDesignCompleteNums.add(designNums);
                        totalDealCompleteNums = totalDealCompleteNums.add(dealNums);
                        totalManualCluesFollowCompleteNums = totalManualCluesFollowCompleteNums.add(manualCluesFollowNums);
                    }


                }
                BigDecimal goodsCount = BigDecimal.ZERO;
                BigDecimal salesAmount = BigDecimal.ZERO;
                //订单
                if (!CollectionUtils.isEmpty(statementOutDTOMap) && statementOutDTOMap.containsKey(entry.getKey().toString())) {
                    //获取实际对象
                    StatementOutDTO findCluesReportsOutDto = statementOutDTOMap.get(entry.getKey().toString());
                    if (findCluesReportsOutDto != null) {
                        goodsCount = new BigDecimal(findCluesReportsOutDto.getGoodsNum() == null ? "0" : findCluesReportsOutDto.getGoodsNum());
                        salesAmount = new BigDecimal(findCluesReportsOutDto.getTotalPrice() == null ? "0" : findCluesReportsOutDto.getTotalPrice());

                        totalSalesAmountCompleteNums = totalSalesAmountCompleteNums.add(salesAmount);
                        totalGoodsCountCompleteNums = totalGoodsCountCompleteNums.add(goodsCount);
                    }


                }
                //引流渠道
                if (!CollectionUtils.isEmpty(drainageChannelOutDtoMap) && drainageChannelOutDtoMap.containsKey(entry.getKey())) {
                    DrainageChannelOutDto drainageChannelOutDto = drainageChannelOutDtoMap.get(entry.getKey());
                    if (drainageChannelOutDto != null) {
                        drainageChannelRetain = drainageChannelOutDto.getDrainageChannelRetain();
                        totalDrainageChannelRetainCompleteNums = totalDrainageChannelRetainCompleteNums.add(drainageChannelRetain);
                        drainageChannelActive = drainageChannelOutDto.getDrainageChannelActive();
                        totalDrainageChannelActiveCompleteNums = totalDrainageChannelActiveCompleteNums.add(drainageChannelActive);
                    }
                }
                if (percentageCompleteDtos == null || percentageCompleteDtos.size() == 0) {
                    //目标完成率标记点
                    percentageCompleteDtos = getPercentageCompleteDtos(queryWordValue, addStoreCompleteDto.getCluesNums(), addStoreCompleteDto.getCluesOtherNums(),addStoreCompleteDto.getDealNums(),
                            addStoreCompleteDto.getIntoStoresNums(), addStoreCompleteDto.getDesignNums(), addStoreCompleteDto.getBuryPipeNums(),
                            addStoreCompleteDto.getGoodsCount(), addStoreCompleteDto.getSalesAmount(), cluesNums,cluesOtherNums, dealNums,
                            intoStoresNums, designNums, buryPipeNums, goodsCount, salesAmount, addStoreCompleteDto.getManualCluesFollowNums(), manualCluesFollowNums,
                            addStoreCompleteDto.getDrainageChannelRetain(),drainageChannelRetain,addStoreCompleteDto.getDrainageChannelActive(),drainageChannelActive);
                }

                chargeUserPercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
                chargeUserPercentageCompleteOutDtos.add(chargeUserPercentageCompleteOutDto);
            }
        }
        //生成合计对象
        ChargeUserPercentageCompleteOutDto storePercentageCompleteOutDto = new ChargeUserPercentageCompleteOutDto();
        storePercentageCompleteOutDto.setChargeUserName("合计");
        storePercentageCompleteOutDto.setChargeUserId("-1");
        storePercentageCompleteOutDto.setChargeUesrCode("-1");
        List<PercentageCompleteDto> percentageCompleteDtos = getPercentageCompleteDtos(queryWordValue, totalCluesTargetNums,totalCluesOtherTargetNums, totalDealTargetNums,
                totalIntoStoresTargetNums, totalDesignTargetNums, totalBuryPipeNumsTargetNums,
                totalGoodsCountTargetNums, totalSalesAmountTargetNums, totalCluesCompleteNums,totalCluesOtherCompleteNums, totalDealCompleteNums,
                totalIntoStoresCompleteNums, totalDesignCompleteNums, totalBuryPipeNumsCompleteNums, totalGoodsCountCompleteNums, totalSalesAmountCompleteNums,totalManualCluesFollowTargetNums,totalManualCluesFollowCompleteNums,
                totalDrainageChannelRetainTargetNums,totalDrainageChannelRetainCompleteNums,totalDrainageChannelActiveTargetNums,totalDrainageChannelActiveCompleteNums);
        storePercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
        //重组数组
//        List<ChargeUserPercentageCompleteOutDto> resultStorePercentageCompleteOutDtos =  new LinkedList<>();

        //5.返回
        chargeUserPercentageCompleteOutDtos.sort((x, y) -> StringUtils.compare(x.getChargeUesrCode(), y.getChargeUesrCode()));

        chargeUserPercentageCompleteOutDtos.addFirst(storePercentageCompleteOutDto);
        return chargeUserPercentageCompleteOutDtos;
    }


    private PercentageCompleteDto getpercentageComplete(BigDecimal totalDecimal, String wordValue, String wordValueId, BigDecimal completedecimal) {
        PercentageCompleteDto percentageCompleteDto = new PercentageCompleteDto();
        percentageCompleteDto.setWordValue(wordValue);
        percentageCompleteDto.setWordValueId(wordValueId);
        percentageCompleteDto.setCompleteNumber(completedecimal.toString());
        percentageCompleteDto.setTargetNumber(totalDecimal.toString());
        if (totalDecimal.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal percentage = completedecimal.divide(totalDecimal, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100L)).setScale(2, BigDecimal.ROUND_HALF_UP);
            percentageCompleteDto.setPercentage(percentage.toString().concat("%"));
        } else {
            percentageCompleteDto.setTargetNumber("-");
            percentageCompleteDto.setPercentage("0%");
        }
        return percentageCompleteDto;
    }

    /**
     * 初始化查询提交统一处理线索
     */

    private FindCluesReportsInDto getFindCluesReportsInDto(Date startTime, Date endTime, Long stroeId) {
        return new FindCluesReportsInDto(startTime, endTime, stroeId);
    }

    private StatementInDTO setStatementInDTO(Date startTime, Date endTime, Long stroeId) {
        return new StatementInDTO(startTime, endTime, stroeId);
    }

    /**
     * 如果完成对象都为空则手动赋值
     *
     * @param queryWordValue
     * @param cluesNums
     * @param dealNums
     * @param intoStoresNums
     * @param designNums
     * @param buryPipeNums
     * @param goodsCount
     * @param salesAmount
     * @param completeCluesNums
     * @param completeDealNums
     * @param completeIntoStoresNums
     * @param completeDesignNums
     * @param completeBuryPipeNums
     * @param completeGoodsCount
     * @param completeSalesAmount
     * @return
     */
    private List<PercentageCompleteDto> getPercentageCompleteDtos(
            String queryWordValue, BigDecimal cluesNums, BigDecimal cluesOtherNums, BigDecimal dealNums, BigDecimal intoStoresNums,
            BigDecimal designNums, BigDecimal buryPipeNums, BigDecimal goodsCount, BigDecimal salesAmount,
            BigDecimal completeCluesNums,BigDecimal completeCluesOtherNums , BigDecimal completeDealNums, BigDecimal completeIntoStoresNums,
            BigDecimal completeDesignNums, BigDecimal completeBuryPipeNums, BigDecimal completeGoodsCount, BigDecimal completeSalesAmount,
            BigDecimal manualCluesFollowNums,BigDecimal completeManualCluesFollowNums,
            BigDecimal drainageChannelRetain,BigDecimal completeDrainageChannelRetain,
            BigDecimal drainageChannelActive, BigDecimal completedrainageChannelActive) {
        List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();
        //销售金额(万)
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtoSalesAmount = getpercentageComplete(
                    salesAmount == null ? BigDecimal.ZERO : salesAmount, CommonConst.TargetCompleteEnum.SALES_AMOUNT.getName(), CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol(), completeSalesAmount);
            percentageCompleteDtos.add(percentageCompleteDtoSalesAmount);

        }
        //成交均价(元)
        /*if (queryWordValue.contains(CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol())) {
            PercentageCompleteDto percentageCompleteDtoUnitPrice = getpercentageComplete(
                    unitPrice == null ? BigDecimal.ZERO : unitPrice, CommonConst.TargetCompleteEnum.UNIT_PRICE.getName(), CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol(), completeUnitPrice);
            percentageCompleteDtos.add(percentageCompleteDtoUnitPrice);

        }*/
        //台数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtogoods = getpercentageComplete(
                    goodsCount == null ? BigDecimal.ZERO : goodsCount, CommonConst.TargetCompleteEnum.GOODS_COUNT.getName(), CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol(), completeGoodsCount);
            percentageCompleteDtos.add(percentageCompleteDtogoods);
        }
        //新增线索
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesNums = getpercentageComplete(
                    cluesNums == null ? BigDecimal.ZERO : cluesNums, CommonConst.TargetCompleteEnum.CLUES_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol(), completeCluesNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesNums);
        }
        //新增线索-非O2O
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesNums = getpercentageComplete(
                    cluesOtherNums == null ? BigDecimal.ZERO : cluesOtherNums, CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol(), completeCluesOtherNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesNums);
        }
        //进店
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtointoStoresNums = getpercentageComplete(
                    intoStoresNums == null ? BigDecimal.ZERO : intoStoresNums, CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getName(), CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol(), completeIntoStoresNums);
            percentageCompleteDtos.add(percentageCompleteDtointoStoresNums);
        }
        //上门设计数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoDesign = getpercentageComplete(
                    designNums == null ? BigDecimal.ZERO : designNums, CommonConst.TargetCompleteEnum.DESIGN_NUMS.getName(), CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol(), completeDesignNums);
            percentageCompleteDtos.add(percentageCompleteDtoDesign);
        }
        //预埋烟管数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoBuryPipeNums = getpercentageComplete(
                    buryPipeNums == null ? BigDecimal.ZERO : buryPipeNums, CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getName(), CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol(), completeBuryPipeNums);
            percentageCompleteDtos.add(percentageCompleteDtoBuryPipeNums);
        }
        //成交
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtodeal = getpercentageComplete(
                    dealNums == null ? BigDecimal.ZERO : dealNums, CommonConst.TargetCompleteEnum.DEAL_NUMS.getName(), CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol(), completeDealNums);
            percentageCompleteDtos.add(percentageCompleteDtodeal);
        }
        //人工写跟进次数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoManualCluesFollowNums = getpercentageComplete(
                    manualCluesFollowNums == null ? BigDecimal.ZERO : manualCluesFollowNums, CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getName(), CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol(), completeManualCluesFollowNums);
            percentageCompleteDtos.add(percentageCompleteDtoManualCluesFollowNums);
        }
        //引流渠道保有量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    drainageChannelRetain == null ? BigDecimal.ZERO : drainageChannelRetain, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol(), completeDrainageChannelRetain));
        }
        //引流渠道活跃量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    drainageChannelActive == null ? BigDecimal.ZERO : drainageChannelActive, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol(), completedrainageChannelActive));
        }
        return percentageCompleteDtos;
    }

    public PageInfo<TargetCompleteOutDTO> targetComplete(TargetCompleteDTO targetCompleteDTO) {
        if (!StringUtils.isBlank(targetCompleteDTO.getStoreTypes())) {
            targetCompleteDTO.setStoreTypeList(Arrays.stream(targetCompleteDTO.getStoreTypes().split(",")).collect(Collectors.toList()));
        }
        if (!StringUtils.isBlank(targetCompleteDTO.getStoreLabels())) {
            targetCompleteDTO.setStoreLabelList(Arrays.stream(targetCompleteDTO.getStoreLabels().split(",")).collect(Collectors.toList()));
        }

        //判断权限
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        if ((ConstantConfig.TARGET_LEVEL_AREA).equals(targetCompleteDTO.getDataTargetCode())) {
            List<Long> companyOrgIds = userAuthorUtil.getCompanyOrgIds();
            List<String> regionCodes = userAuthorUtil.getAreaCodes(companyOrgIds);
            targetCompleteDTO.setRegionCodes(regionCodes);
        } else if ((ConstantConfig.TARGET_LEVEL_COMPANY).equals(targetCompleteDTO.getDataTargetCode())) {
            List<Long> companyOrgIds = userAuthorUtil.getCompanyOrgIds();
            targetCompleteDTO.setCompanyIds(companyOrgIds);
        } else if ((ConstantConfig.TARGET_LEVEL_MANAGER).equals(targetCompleteDTO.getDataTargetCode())) {
            //分公司交门店权限下的门店业务发展主管
            List<Long> managerIds = userAuthorUtil.getManagerIds();
            targetCompleteDTO.setManagerIds(managerIds);
        } else if ((ConstantConfig.TARGET_LEVEL_STORE).equals(targetCompleteDTO.getDataTargetCode())) {
//            List<Long> storeIds = userAuthorUtil.getStoreOrgIds();
            //分公司交门店权限
            List<Long> storeIds = DataScopeUtil.getUserCompanyCrossStoreOrgId(userAuthorConfig);
            targetCompleteDTO.setStoreIds(storeIds);
        } else if ((ConstantConfig.TARGET_LEVEL_CHARGE).equals(targetCompleteDTO.getDataTargetCode())){
            if (targetCompleteDTO.getChargeFlag() != null ) {
                if (targetCompleteDTO.getChargeFlag() == 1) {
                    //分公司交门店权限
                    List<Long> storeIds = DataScopeUtil.getUserCompanyCrossStoreOrgId(userAuthorConfig);
                    targetCompleteDTO.setStoreIds(storeIds);
                } else if(targetCompleteDTO.getChargeFlag() == 2){
                    //查看业务员自己的
                    targetCompleteDTO.setChargeUserId(userAuthor.getSalesmanId());
                }
            } else {
                throw new BusinessException("当前账号尚未配置查看权限，请核对！");
            }
        }

        Date startTime = null;
        Date endTime = null;
        //设置时间
        List<Long> companyOrgIds = userAuthorUtil.getCompanyOrgIds();
        SelectMarketDateInfoOutDto marketDateInfoOutDto = marketDateInfoDao.getListByTypeValue(targetCompleteDTO.getDateType(), targetCompleteDTO.getDateValue(),companyOrgIds);
        if (marketDateInfoOutDto != null) {
            startTime = ToolsUtils.getMinTime(marketDateInfoOutDto.getStartTime());
            endTime = ToolsUtils.getMinTime(marketDateInfoOutDto.getEndTime());
        } else {
            throw new BusinessException("传入时间值错误，请核对！");
        }

        //2020.8.4  为解决避免因查询结果为空时throw掉，特此修改记录接口的执行位置在查询之前


        String queryWordValue = targetCompleteDTO.getQueryWordValue();
        if (StringUtils.isNotBlank(targetCompleteDTO.getExportFlag()) && "1".equals(targetCompleteDTO.getExportFlag())) {

        } else {
            //3.写入业务员查询字段记录-mq
            try {
                AddQueryRecordInDto addQueryRecordInDto = new AddQueryRecordInDto();
                addQueryRecordInDto.setChargeUesrId(String.valueOf(userAuthor.getSalesmanId() == null ? "" : userAuthor.getSalesmanId()));
                addQueryRecordInDto.setQueryWordValue(queryWordValue);
                addQueryRecordInDto.setDateType(targetCompleteDTO.getDateType().intValue());
                addQueryRecordInDto.setDateValue(targetCompleteDTO.getDateValue());
                addQueryRecordInDto.setWeeklyShowName(targetCompleteDTO.getWeeklyShowName());
                completeDataService.insertQueryRecord(addQueryRecordInDto);
            } catch (Exception e) {
                log.error("业务员查询字段SQL错误：" + e.getMessage());
            }
        }
        //1.拆分查询字段
        if (StringUtils.isEmpty(targetCompleteDTO.getQueryWordValue())) {
            throw new BusinessException("查询字段不能为空");
        }
        //用于获取查询字段有目标id
        if (StringUtils.isNotBlank(targetCompleteDTO.getQueryWordValueStr())) {
            String[] split = targetCompleteDTO.getQueryWordValueStr().split(",");
            targetCompleteDTO.setQueryWordValueList(Arrays.asList(split));
        }else {
            String[] split = targetCompleteDTO.getQueryWordValue().split(",");
            targetCompleteDTO.setQueryWordValueList(Arrays.asList(split));
        }

        //2.根据权限门店查询目标信息/或者根据获得的数据查询门店目标
        PageInfo pageInfo = new PageInfo(targetCompleteDTO.getPageNum(), targetCompleteDTO.getPageSize());
        List<QueryTargetDataDTO> storeCompleteDtos = null;
        Integer count = 0;
        if ((ConstantConfig.TARGET_LEVEL_CHARGE).equals(targetCompleteDTO.getDataTargetCode())) {
            count = completeDataService.selectChagreCount(targetCompleteDTO);
        } else {
            count = marketTargetDataDao.selectListForCompleteCount(targetCompleteDTO);
        }

        if (count == 0) {

            if (targetCompleteDTO.getPageNum() == 1) {
                    throw new BusinessException("尚未制定目标,\n".concat(ConstantConfig.TARGET_LEVEL_CHARGE.equals(targetCompleteDTO.getDataTargetCode())
                            || (ConstantConfig.TARGET_LEVEL_STORE).equals(targetCompleteDTO.getDataTargetCode()) ? "需由店长/门店业务前往目标列表编辑目标" : "请到中台导入"));
            } else {
                return pageInfo;
            }
        }

        if ((ConstantConfig.TARGET_LEVEL_CHARGE).equals(targetCompleteDTO.getDataTargetCode())) {
            //todo:目标完成率修改点
            storeCompleteDtos = completeDataService.selectChagreList(targetCompleteDTO, pageInfo);
        } else {
            //todo:目标完成率修改点
            storeCompleteDtos = marketTargetDataDao.selectListForComplete(targetCompleteDTO, pageInfo);
        }

        if (CollectionUtils.isEmpty(storeCompleteDtos) || storeCompleteDtos.size() == 0) {
            if (targetCompleteDTO.getPageNum() == 1) {
                throw new BusinessException("尚未制定目标,\n".concat(ConstantConfig.TARGET_LEVEL_CHARGE.equals(targetCompleteDTO.getDataTargetCode())
                        || (ConstantConfig.TARGET_LEVEL_STORE).equals(targetCompleteDTO.getDataTargetCode()) ? "需由店长/门店业务前往目标列表编辑目标" : "请到中台导入"));
            } else {
                return pageInfo;
            }
        }else {
            if ("30".equals(targetCompleteDTO.getDataTargetCode()) || "50".equals(targetCompleteDTO.getDataTargetCode())){
                storeCompleteDtos.stream().forEach(c -> {
                    if (StringUtils.isNotBlank(c.getTargetName())) {
                        c.setTargetName(MybatisMateConfig.decrypt(c.getTargetName()));
                    }
                });
            }
        }
        List<TargetCompleteOutDTO> list = null;
        if ((ConstantConfig.TARGET_LEVEL_AREA).equals(targetCompleteDTO.getDataTargetCode())) {
            list = percentMarketArea(queryWordValue, storeCompleteDtos, startTime, endTime);
        } else if ((ConstantConfig.TARGET_LEVEL_COMPANY).equals(targetCompleteDTO.getDataTargetCode())) {
            list = percentMarketCompany(queryWordValue, storeCompleteDtos, startTime, endTime);
        } else if ((ConstantConfig.TARGET_LEVEL_MANAGER).equals(targetCompleteDTO.getDataTargetCode())) {
            list = percentMarketManageUser(queryWordValue, storeCompleteDtos, startTime, endTime);
        } else if ((ConstantConfig.TARGET_LEVEL_STORE).equals(targetCompleteDTO.getDataTargetCode())) {
            list = percentMarketStore(queryWordValue, storeCompleteDtos, startTime, endTime);
        } else if ((ConstantConfig.TARGET_LEVEL_CHARGE).equals(targetCompleteDTO.getDataTargetCode())) {
            list = percentMarketChargerUser(queryWordValue, storeCompleteDtos, startTime, endTime);
        }
        pageInfo.setRecords(list);
        //1.1根据查询字段判断调用接口

//        Integer isQueryClues = 0;
//        Integer isQueryOrder = 0;
//        for (String s : queryWordValue.split(",")) {
//            //1.2新增线索  从线索表获取，成交 进店 上门设计 预埋烟管从跟进表获取
//            if ("clues_nums".equals(s) || "deal_nums".equals(s) || "into_stores_nums".equals(s) || "design_nums".equals(s) || "bury_pipe_nums".equals(s)) {
//                isQueryClues = 1;
//            }
//            //1.3销售额，台数从订单获取
//            if ("goods_count".equals(s) || "sales_amount".equals(s) || "unit_price".equals(s)) {
//                isQueryOrder = 1;
//            }
//
//        }

        return pageInfo;
    }

    /***
     * @description 大区
     */
    private List<TargetCompleteOutDTO> percentMarketArea(String queryWordValue, List<QueryTargetDataDTO> list, Date startTime, Date endTime) {
        StatementInDTO statementInDTO;
        //获取大区code
        List<String> regionCodes = list.stream().map(QueryTargetDataDTO::getTargetCode).collect(Collectors.toList());
        //大区
        //获取订单数据入参条件
        statementInDTO = this.setStatementInAreaDTO(startTime, endTime, regionCodes);
        //获取线索数据入参条件
        FindCluesReportsInDto cluesReportsInDto = setFindCluesReportsInDtoAreaCodeDTO(startTime, endTime, regionCodes);
        /**
         * 异步
         */
        log.error("订单大区查询时间开始"+System.currentTimeMillis());
        SelectStatementOutDTO selectStatementOutDTO = getSelectStatementOutDTO(statementInDTO);
        log.error("订单大区查询时间结束"+System.currentTimeMillis());
//        selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        /**
         * 异步
         */
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = getFindCluesReportsOutDtos(cluesReportsInDto);
//        findCluesReportsOutDtos = userCluesClient.findCluesReportsStore(cluesReportsInDto).getData();
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(findCluesReportsOutDtos) && findCluesReportsOutDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(FindCluesReportsOutDto::getDistrictCode, Function.identity(), (key1, key2) -> key2));
        }
        //4.计算完成率
        //目标
        Map<String, QueryTargetDataDTO> chargeCompleteDtoMap = list.stream().collect(Collectors.toMap(QueryTargetDataDTO::getTargetCode, Function.identity(), (key1, key2) -> key2));
        //订单
        Map<String, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(StatementOutDTO::getDistrictCode, Function.identity(), (key1, key2) -> key2));
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);

        return getPercentageMarketOutDTOS(queryWordValue, chargeCompleteDtoMap, statementOutDTOMap, findCluesReportsOutDtoMap, drainageChannelOutDtoMap);
    }


    private SelectStatementOutDTO getSelectStatementOutDTO(StatementInDTO statementInDTO) {
        SelectStatementOutDTO selectStatementOutDTO = null;
        Object details = SecurityContextHolder.getContext().getAuthentication().getDetails();
        RefreshableKeycloakSecurityContext keycloakSecurityContext = ((SimpleKeycloakAccount) details).getKeycloakSecurityContext();
        //获取当前账户
        String accessToken = "Bearer " + keycloakSecurityContext.getTokenString();

        CompletableFuture<SelectStatementOutDTO> futureA = CompletableFuture.supplyAsync(() ->{
            return   selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO,accessToken);
        });// 业务A
        try {
            selectStatementOutDTO = futureA.get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return selectStatementOutDTO;
    }


    private List<FindCluesReportsOutDto> getFindCluesReportsOutDtos(FindCluesReportsInDto cluesReportsInDto) {
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = null;
        Object details = SecurityContextHolder.getContext().getAuthentication().getDetails();
        RefreshableKeycloakSecurityContext keycloakSecurityContext = ((SimpleKeycloakAccount) details).getKeycloakSecurityContext();
        //获取当前账户
//        String accessToken = "Bearer " + keycloakSecurityContext.getTokenString();

//        CompletableFuture<List<FindCluesReportsOutDto>> futureB = CompletableFuture.supplyAsync(() -> {
            //return   userCluesClient.findCluesReportsStore(cluesReportsInDto,accessToken);
            return cluesReportsService.findCluesReportsStoreAll(cluesReportsInDto);
//     }
//    );// 业务B

//        try {
//
//            findCluesReportsOutDtos = futureB.get();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } catch (ExecutionException e) {
//            e.printStackTrace();
//        }
//        return findCluesReportsOutDtos;
    }

    /***
     * @description 公司
     */
    private List<TargetCompleteOutDTO> percentMarketCompany(String queryWordValue, List<QueryTargetDataDTO> list, Date startTime, Date endTime) {
        StatementInDTO statementInDTO;
        //获取大区code
        List<Long> companyIdList = list.stream().map(s -> Long.valueOf(s.getTargetCode())).collect(Collectors.toList());
        //获取订单条件组合  时间  公司code
        statementInDTO = this.setStatementInCompanyDTO(startTime, endTime, companyIdList);
//        selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        //获取线索
        FindCluesReportsInDto cluesReportsInDto = setFindCluesReportsInDtoCompanyDTO(startTime, endTime, companyIdList);
//        userCluesClient.findCluesReportsStore(cluesReportsInDto).getData();
        /**
         * 异步
         */
        SelectStatementOutDTO selectStatementOutDTO = getSelectStatementOutDTO(statementInDTO);
//        selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        /**
         * 异步
         */
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = getFindCluesReportsOutDtos(cluesReportsInDto);
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(findCluesReportsOutDtos) && findCluesReportsOutDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(s -> String.valueOf(s.getCompanyId()), Function.identity(), (key1, key2) -> key2));
        }
        //4.计算完成率
        //目标
        Map<String, QueryTargetDataDTO> chargeCompleteDtoMap = list.stream().collect(Collectors.toMap(s -> s.getTargetCode(), Function.identity(), (key1, key2) -> key2));
        //订单
        Map<String, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(s -> String.valueOf(s.getCompanyId()), Function.identity(), (key1, key2) -> key2));
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);

        //计算集合
        LinkedList<TargetCompleteOutDTO> chargeUserPercentageCompleteOutDtos = getPercentageMarketOutDTOS(queryWordValue, chargeCompleteDtoMap, statementOutDTOMap, findCluesReportsOutDtoMap, drainageChannelOutDtoMap);
        return chargeUserPercentageCompleteOutDtos;
    }

    /**
     * @description 门店
     */
    private List<TargetCompleteOutDTO> percentMarketStore(String queryWordValue, List<QueryTargetDataDTO> list, Date startTime, Date endTime) {
        StatementInDTO statementInDTO;
        List<Long> storeIds = list.stream().map(s -> Long.valueOf(s.getTargetCode())).collect(Collectors.toList());
        //获取订单条件组合  时间  门店
        statementInDTO = this.setStatementInStoreIdsDTO(startTime, endTime, storeIds);
        //获取线索条件组合 时间  门店
//        SelectStatementOutDTO selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        //4.计算完成率
        FindCluesReportsInDto cluesReportsInDto = setFindCluesReportsInDtoStoreIdsDTO(startTime, endTime, storeIds);
        /**
         * 异步
         */
        SelectStatementOutDTO selectStatementOutDTO = getSelectStatementOutDTO(statementInDTO);
//        selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        /**
         * 异步
         */
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = getFindCluesReportsOutDtos(cluesReportsInDto);
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(findCluesReportsOutDtos) && findCluesReportsOutDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(s -> String.valueOf(s.getStroeId()), Function.identity(), (key1, key2) -> key2));
        }
        //目标
        Map<String, QueryTargetDataDTO> chargeCompleteDtoMap = list.stream().collect(Collectors.toMap(s -> s.getTargetCode(), Function.identity(), (key1, key2) -> key2));
        //订单
        Map<String, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(s -> String.valueOf(s.getStoreId()), Function.identity(), (key1, key2) -> key2));
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);

        //计算集合
        LinkedList<TargetCompleteOutDTO> chargeUserPercentageCompleteOutDtos = getPercentageMarketOutDTOS(queryWordValue, chargeCompleteDtoMap, statementOutDTOMap, findCluesReportsOutDtoMap,drainageChannelOutDtoMap);
        return chargeUserPercentageCompleteOutDtos;
    }

    /**
     * 业务员
     *
     * @param queryWordValue
     * @param list
     * @param startTime
     * @param endTime
     * @return
     */
    private List<TargetCompleteOutDTO> percentMarketChargerUser(String queryWordValue, List<QueryTargetDataDTO> list, Date startTime, Date endTime) {
        StatementInDTO statementInDTO;
        List<Long> chargeUserId = list.stream().map(s -> Long.valueOf(s.getTargetCode())).collect(Collectors.toList());
        //获取订单条件组合  时间  业务员
        statementInDTO = this.setStatementInChargeUserIdsDTO(startTime, endTime, chargeUserId);
        statementInDTO.setSecretKey(MybatisMateConfig.getPassword());
        //获取线索条件组合 时间  业务员
//        SelectStatementOutDTO selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        long a1 = System.currentTimeMillis();
        //4.计算完成率
        FindCluesReportsInDto cluesReportsInDto = setFindCluesReportsInDtoChargeUserIdsDTO(startTime, endTime, chargeUserId);

        /**
         * 异步
         */
        SelectStatementOutDTO selectStatementOutDTO = getSelectStatementOutDTO(statementInDTO);

//        selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        /**
         * 异步
         */
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = getFindCluesReportsOutDtos(cluesReportsInDto);
        long a2 = System.currentTimeMillis();
        System.out.println(a2 - a1);
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(findCluesReportsOutDtos) && findCluesReportsOutDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(s -> String.valueOf(s.getChargeUserId()), Function.identity(), (key1, key2) -> key2));
        }
        //目标
        Map<String, QueryTargetDataDTO> chargeCompleteDtoMap = list.stream().collect(Collectors.toMap(s -> s.getTargetCode(), Function.identity(), (key1, key2) -> key2));
        //订单
        Map<String, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(s -> String.valueOf(s.getChargeUserId()), Function.identity(), (key1, key2) -> key2));
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);

        //计算集合
        LinkedList<TargetCompleteOutDTO> chargeUserPercentageCompleteOutDtos = getPercentageMarketOutDTOS(queryWordValue, chargeCompleteDtoMap, statementOutDTOMap, findCluesReportsOutDtoMap,drainageChannelOutDtoMap);
        return chargeUserPercentageCompleteOutDtos;
    }

    /**
     * 门店主管
     */
    private List<TargetCompleteOutDTO> percentMarketManageUser(String queryWordValue, List<QueryTargetDataDTO> list, Date startTime, Date endTime) {
        StatementInDTO statementInDTO;
        List<Long> chargeUserIds = list.stream().map(s -> Long.valueOf(s.getTargetCode())).collect(Collectors.toList());
        //获取订单条件组合  时间  主管
        statementInDTO = this.setStatementInChargeUserIdDTO(startTime, endTime, chargeUserIds);
//        SelectStatementOutDTO selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        //获取线索
        FindCluesReportsInDto cluesReportsInDto = setFindCluesReportsInDtoChargeIdsDTO(startTime, endTime, chargeUserIds);
        /**
         * 异步
         */
        SelectStatementOutDTO selectStatementOutDTO = getSelectStatementOutDTO(statementInDTO);
//        selectStatementOutDTO = selectStatementUtils.getSelectStatementStoreOutDTO(statementInDTO);
        /**
         * 异步
         */
        List<FindCluesReportsOutDto> findCluesReportsOutDtos = getFindCluesReportsOutDtos(cluesReportsInDto);
        Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap = null;
        if (!CollectionUtils.isEmpty(findCluesReportsOutDtos) && findCluesReportsOutDtos.size() > 0) {
            findCluesReportsOutDtoMap = findCluesReportsOutDtos.stream().collect(Collectors.toMap(s -> s.getDevelopSalesmanId(), Function.identity(), (key1, key2) -> key2));
        }
        //4.计算完成率
        //目标
        Map<String, QueryTargetDataDTO> chargeCompleteDtoMap = list.stream().collect(Collectors.toMap(s -> s.getTargetCode(), Function.identity(), (key1, key2) -> key2));
        // 订单
        Map<String, StatementOutDTO> statementOutDTOMap = null;
        if (selectStatementOutDTO != null && !CollectionUtils.isEmpty(selectStatementOutDTO.getList()) && selectStatementOutDTO.getList().size() > 0) {
            statementOutDTOMap = selectStatementOutDTO.getList().stream().collect(Collectors.toMap(StatementOutDTO::getDevelopSalesmanId, Function.identity(), (key1, key2) -> key2));
        }
        //引流渠道分公司/设计师
        Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap = selectStatementUtils.getSelectDrainageChannelOutDTO(statementInDTO);

        //计算集合
        LinkedList<TargetCompleteOutDTO> chargeUserPercentageCompleteOutDtos = getPercentageMarketOutDTOS(queryWordValue, chargeCompleteDtoMap, statementOutDTOMap, findCluesReportsOutDtoMap,drainageChannelOutDtoMap);
        return chargeUserPercentageCompleteOutDtos;
    }

    private StatementInDTO setStatementInChargeUserIdDTO(Date startTime, Date endTime, List<Long> chargeUserIds) {
        return new StatementInDTO(startTime, endTime, null, null, null, null, chargeUserIds, 6);
    }

    /**
     * 主管
     *
     * @param startTime
     * @param endTime
     * @param
     * @return
     */
    private FindCluesReportsInDto setFindCluesReportsInDtoChargeIdsDTO(Date startTime, Date endTime, List<Long> chargeIds) {
        return new FindCluesReportsInDto(startTime, endTime, null, null, null, null, chargeIds, 6);
    }

    private StatementInDTO setStatementInStoreIdsDTO(Date startTime, Date endTime, List<Long> storeIds) {
        return new StatementInDTO(startTime, endTime, null, null, null, storeIds, null, 4);
    }

    private StatementInDTO setStatementInChargeUserIdsDTO(Date startTime, Date endTime, List<Long> chargeUserIds) {
        return new StatementInDTO(startTime, endTime, null, null, chargeUserIds, null, null, 5);
    }

    private FindCluesReportsInDto setFindCluesReportsInDtoStoreIdsDTO(Date startTime, Date endTime, List<Long> storeIds) {
        return new FindCluesReportsInDto(startTime, endTime, null, null, storeIds, null, null, 4);
    }

    private FindCluesReportsInDto setFindCluesReportsInDtoChargeUserIdsDTO(Date startTime, Date endTime, List<Long> chargeUserIds) {
        return new FindCluesReportsInDto(startTime, endTime, chargeUserIds, null, null, null, null, 5);
    }

    private FindCluesReportsInDto setFindCluesReportsInDtoCompanyDTO(Date startTime, Date endTime, List<Long> companyIds) {
        return new FindCluesReportsInDto(startTime, endTime, null, null, null, companyIds, null, 3);
    }


    private StatementInDTO setStatementInCompanyDTO(Date startTime, Date endTime, List<Long> companyIdList) {
        return new StatementInDTO(startTime, endTime, companyIdList, null, null, null, null, 2);
    }

    private StatementInDTO setStatementInAreaDTO(Date startTime, Date endTime, List<String> areaCodeList) {
        return new StatementInDTO(startTime, endTime, null, areaCodeList, null, null, null, 1);
    }

    private FindCluesReportsInDto setFindCluesReportsInDtoAreaCodeDTO(Date startTime, Date endTime, List<String> areaIds) {
        return new FindCluesReportsInDto(startTime, endTime, null, areaIds, null, null, null, 2);
    }

    private LinkedList<TargetCompleteOutDTO> getPercentageMarketOutDTOS(String queryWordValue, Map<String, QueryTargetDataDTO> chargeCompleteDtoMap, Map<String, StatementOutDTO> statementOutDTOMap, Map<String, FindCluesReportsOutDto> findCluesReportsOutDtoMap,Map<String, DrainageChannelOutDto> drainageChannelOutDtoMap) {
        //todo:明天
        LinkedList<TargetCompleteOutDTO> chargeUserPercentageCompleteOutDtos = new LinkedList<>();
        BigDecimal totalCluesCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesTargetNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherCompleteNums = BigDecimal.ZERO;
        BigDecimal totalCluesOtherTargetNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresCompleteNums = BigDecimal.ZERO;
        BigDecimal totalIntoStoresTargetNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsCompleteNums = BigDecimal.ZERO;
        BigDecimal totalBuryPipeNumsTargetNums = BigDecimal.ZERO;
        BigDecimal totalDesignCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDesignTargetNums = BigDecimal.ZERO;
        BigDecimal totalDealCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDealTargetNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalSalesAmountTargetNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalGoodsCountTargetNums = BigDecimal.ZERO;
        BigDecimal totalUnitPriceTargetNums = BigDecimal.ZERO;
        BigDecimal totalCountCompleteNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowCompleteNums = BigDecimal.ZERO;
        BigDecimal totalManualCluesFollowTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelRetainTargetNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveCompleteNums = BigDecimal.ZERO;
        BigDecimal totalDrainageChannelActiveTargetNums = BigDecimal.ZERO;


        if (!CollectionUtils.isEmpty(chargeCompleteDtoMap)) {
            for (Map.Entry<String, QueryTargetDataDTO> entry : chargeCompleteDtoMap.entrySet()) {
                //获取目标值
                QueryTargetDataDTO addStoreCompleteDto = entry.getValue();
                if (addStoreCompleteDto == null) {
                    addStoreCompleteDto = new QueryTargetDataDTO();
                }

                //初始化返回数据
                TargetCompleteOutDTO chargeUserPercentageCompleteOutDto = getTargetCompleteOutDTO(addStoreCompleteDto);

                List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();
                //判断key是否存在
                //合计目标
                BigDecimal totalDrainageTargetNums = BigDecimal.ZERO;//目标
                BigDecimal totalDrainageCompleteNums = BigDecimal.ZERO;//完成%
                BigDecimal totalStockTargetNums = BigDecimal.ZERO;
                BigDecimal totalStockCompleteNums = BigDecimal.ZERO;

                totalSalesAmountTargetNums = totalSalesAmountTargetNums.add(addStoreCompleteDto.getSalesAmount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getSalesAmount());
                totalGoodsCountTargetNums = totalGoodsCountTargetNums.add(addStoreCompleteDto.getGoodsCount() == null ? BigDecimal.ZERO : addStoreCompleteDto.getGoodsCount());
                totalBuryPipeNumsTargetNums = totalBuryPipeNumsTargetNums.add(addStoreCompleteDto.getBuryPipeNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getBuryPipeNums());
                totalIntoStoresTargetNums = totalIntoStoresTargetNums.add(addStoreCompleteDto.getIntoStoresNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getIntoStoresNums());
                totalCluesTargetNums = totalCluesTargetNums.add(addStoreCompleteDto.getCluesNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesNums());
                totalCluesOtherTargetNums = totalCluesOtherTargetNums.add(addStoreCompleteDto.getCluesOtherNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getCluesOtherNums());
                totalDealTargetNums = totalDealTargetNums.add(addStoreCompleteDto.getDealNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDealNums());
                totalDesignTargetNums = totalDesignTargetNums.add(addStoreCompleteDto.getDesignNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDesignNums());
//                totalDrainageTargetNums = totalDrainageTargetNums.add(addStoreCompleteDto.getDrainageCluesProportion() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageCluesProportion());
//                totalStockTargetNums = totalStockTargetNums.add(addStoreCompleteDto.getStockCluesProportion() == null ? BigDecimal.ZERO : addStoreCompleteDto.getStockCluesProportion());
                totalUnitPriceTargetNums = totalUnitPriceTargetNums.add(addStoreCompleteDto.getUnitPrice() == null ? BigDecimal.ZERO : addStoreCompleteDto.getUnitPrice());
                totalManualCluesFollowTargetNums = totalManualCluesFollowTargetNums.add(addStoreCompleteDto.getManualCluesFollowNums() == null ? BigDecimal.ZERO : addStoreCompleteDto.getManualCluesFollowNums());
                totalDrainageChannelRetainTargetNums = totalDrainageChannelRetainTargetNums.add(addStoreCompleteDto.getDrainageChannelRetain() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelRetain());
                totalDrainageChannelActiveTargetNums = totalDrainageChannelActiveTargetNums.add(addStoreCompleteDto.getDrainageChannelActive() == null ? BigDecimal.ZERO : addStoreCompleteDto.getDrainageChannelActive());

                BigDecimal cluesNums = BigDecimal.ZERO;
                BigDecimal cluesOtherNums = BigDecimal.ZERO;
                BigDecimal intoStoresNums = BigDecimal.ZERO;
                BigDecimal buryPipeNums = BigDecimal.ZERO;
                BigDecimal dealNums = BigDecimal.ZERO;
                BigDecimal designNums = BigDecimal.ZERO;
                BigDecimal fzrjdNums = BigDecimal.ZERO;
                BigDecimal stockCluesNums = BigDecimal.ZERO;
                BigDecimal followStockCluesNums = BigDecimal.ZERO;
                BigDecimal goodsCount = BigDecimal.ZERO;
                BigDecimal count = BigDecimal.ZERO;
                BigDecimal salesAmount = BigDecimal.ZERO;
                BigDecimal transaction = BigDecimal.ZERO;

                BigDecimal drainageChannelRetain = BigDecimal.ZERO;
                BigDecimal drainageChannelActive = BigDecimal.ZERO;

                BigDecimal drainageNums = BigDecimal.ZERO;
                BigDecimal stockNums = BigDecimal.ZERO;
                BigDecimal manualCluesFollowNums = BigDecimal.ZERO;

                if (findCluesReportsOutDtoMap != null && findCluesReportsOutDtoMap.containsKey(entry.getKey())) {
                    //获取实际对象
                    FindCluesReportsOutDto findCluesReportsOutDto = findCluesReportsOutDtoMap.get(entry.getKey());

                    if (findCluesReportsOutDto != null) {
                        cluesNums = new BigDecimal(findCluesReportsOutDto.getCluesNums() == null ? 0L : findCluesReportsOutDto.getCluesNums());
                        cluesOtherNums = new BigDecimal(findCluesReportsOutDto.getCluesOtherNums() == null ? 0L : findCluesReportsOutDto.getCluesOtherNums());
                        intoStoresNums = new BigDecimal(findCluesReportsOutDto.getIntoStoresNums() == null ? 0L : findCluesReportsOutDto.getIntoStoresNums());
                        buryPipeNums = new BigDecimal(findCluesReportsOutDto.getBuryPipeNums() == null ? 0L : findCluesReportsOutDto.getBuryPipeNums());
                        dealNums = new BigDecimal(findCluesReportsOutDto.getDealNums() == null ? 0L : findCluesReportsOutDto.getDealNums());
                        designNums = new BigDecimal(findCluesReportsOutDto.getDesignNums() == null ? 0L : findCluesReportsOutDto.getDesignNums());
                        fzrjdNums = new BigDecimal(findCluesReportsOutDto.getFzrjdNums() == null ? 0L : findCluesReportsOutDto.getFzrjdNums());
                        stockCluesNums = new BigDecimal(findCluesReportsOutDto.getStockCluesNums() == null ? 0L : findCluesReportsOutDto.getStockCluesNums());
                        followStockCluesNums = new BigDecimal(findCluesReportsOutDto.getFollowStockCluesNums() == null ? 0L : findCluesReportsOutDto.getFollowStockCluesNums());
                        manualCluesFollowNums = new BigDecimal(findCluesReportsOutDto.getManualCluesFollowNums() == null ? 0L : findCluesReportsOutDto.getManualCluesFollowNums());
                        //计算引流线索占比、存量占比完成率 %
                        drainageNums = fzrjdNums.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : fzrjdNums.multiply(new BigDecimal("100")).divide(cluesNums, 2, RoundingMode.HALF_UP);
                        stockNums = followStockCluesNums.equals(BigDecimal.ZERO) || stockCluesNums.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : followStockCluesNums.multiply(new BigDecimal("100")).divide(stockCluesNums, 2, RoundingMode.HALF_UP);

                        totalCluesCompleteNums = totalCluesCompleteNums.add(cluesNums);
                        totalCluesOtherCompleteNums = totalCluesOtherCompleteNums.add(cluesOtherNums);
                        totalIntoStoresCompleteNums = totalIntoStoresCompleteNums.add(intoStoresNums);
                        totalBuryPipeNumsCompleteNums = totalBuryPipeNumsCompleteNums.add(buryPipeNums);
                        totalDesignCompleteNums = totalDesignCompleteNums.add(designNums);
                        totalDealCompleteNums = totalDealCompleteNums.add(dealNums);
                        totalDrainageCompleteNums = totalDrainageCompleteNums.add(drainageNums);
                        totalStockCompleteNums = totalStockCompleteNums.add(stockNums);
                        totalManualCluesFollowCompleteNums = totalManualCluesFollowCompleteNums.add(manualCluesFollowNums);
                    }

                }
                //订单
                if (!CollectionUtils.isEmpty(statementOutDTOMap) && statementOutDTOMap.containsKey(entry.getKey())) {


                    //获取实际对象
                    StatementOutDTO findCluesReportsOutDto = statementOutDTOMap.get(entry.getKey());
                    if (findCluesReportsOutDto != null) {
                        count = new BigDecimal(findCluesReportsOutDto.getCount() == null ? "0" : findCluesReportsOutDto.getCount());
                        goodsCount = new BigDecimal(findCluesReportsOutDto.getGoodsNum() == null ? "0" : findCluesReportsOutDto.getGoodsNum());
                        salesAmount = new BigDecimal(findCluesReportsOutDto.getTotalPrice() == null ? "0" : findCluesReportsOutDto.getTotalPrice());
                        transaction = new BigDecimal(findCluesReportsOutDto.getTransaction() == null ? "0" : findCluesReportsOutDto.getTransaction());
                        totalSalesAmountCompleteNums = totalSalesAmountCompleteNums.add(salesAmount);
                        totalGoodsCountCompleteNums = totalGoodsCountCompleteNums.add(goodsCount);
                        totalCountCompleteNums = totalCountCompleteNums.add(count);
                    }
                }
                //引流渠道
                if (!CollectionUtils.isEmpty(drainageChannelOutDtoMap) && drainageChannelOutDtoMap.containsKey(entry.getKey())) {
                    DrainageChannelOutDto drainageChannelOutDto = drainageChannelOutDtoMap.get(entry.getKey());
                    if (drainageChannelOutDto != null) {
                        drainageChannelRetain = drainageChannelOutDto.getDrainageChannelRetain();
                        totalDrainageChannelRetainCompleteNums = totalDrainageChannelRetainCompleteNums.add(drainageChannelRetain);
                        drainageChannelActive = drainageChannelOutDto.getDrainageChannelActive();
                        totalDrainageChannelActiveCompleteNums = totalDrainageChannelActiveCompleteNums.add(drainageChannelActive);
                    }
                }
                if (percentageCompleteDtos == null || percentageCompleteDtos.size() == 0) {
                    //目标完成率标记点
                    percentageCompleteDtos = getPercentageCompleteDtos1(queryWordValue,
                            addStoreCompleteDto.getCluesNums(), addStoreCompleteDto.getCluesOtherNums(), addStoreCompleteDto.getDealNums(),
                            addStoreCompleteDto.getIntoStoresNums(), addStoreCompleteDto.getDesignNums(), addStoreCompleteDto.getBuryPipeNums(),
                            addStoreCompleteDto.getGoodsCount(), addStoreCompleteDto.getSalesAmount(), cluesNums, cluesOtherNums, dealNums,
                            intoStoresNums, designNums, buryPipeNums, goodsCount, salesAmount, addStoreCompleteDto.getUnitPrice(), transaction,
                            addStoreCompleteDto.getDrainageCluesProportion(), addStoreCompleteDto.getStockCluesProportion(),
                            addStoreCompleteDto.getManualCluesFollowNums(), manualCluesFollowNums,totalDrainageCompleteNums, totalStockCompleteNums,
                            addStoreCompleteDto.getDrainageChannelRetain(),drainageChannelRetain,addStoreCompleteDto.getDrainageChannelActive(),drainageChannelActive);
                }
                chargeUserPercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
                chargeUserPercentageCompleteOutDtos.add(chargeUserPercentageCompleteOutDto);
            }
        }
        //生成合计对象
        TargetCompleteOutDTO storePercentageCompleteOutDto = new TargetCompleteOutDTO();
        storePercentageCompleteOutDto.setTargetName("合计");
        storePercentageCompleteOutDto.setTargetCode("-1");
        storePercentageCompleteOutDto.setTargetId(-1L);
        List<PercentageCompleteDto> percentageCompleteDtos = getPercentageCompleteDtos2(queryWordValue, totalCluesTargetNums,totalCluesOtherTargetNums, totalDealTargetNums,
                totalIntoStoresTargetNums, totalDesignTargetNums, totalBuryPipeNumsTargetNums,
                totalGoodsCountTargetNums, totalSalesAmountTargetNums, totalCluesCompleteNums,totalCluesOtherCompleteNums, totalDealCompleteNums,
                totalIntoStoresCompleteNums, totalDesignCompleteNums, totalBuryPipeNumsCompleteNums, totalGoodsCountCompleteNums,
                totalSalesAmountCompleteNums, totalManualCluesFollowTargetNums, totalManualCluesFollowCompleteNums,totalUnitPriceTargetNums, totalCountCompleteNums.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : totalSalesAmountCompleteNums.multiply(new BigDecimal("10000")).divide(totalCountCompleteNums, 0, RoundingMode.HALF_UP),
                totalDrainageChannelRetainTargetNums,
                totalDrainageChannelRetainCompleteNums,
                totalDrainageChannelActiveTargetNums,
                totalDrainageChannelActiveCompleteNums);
        storePercentageCompleteOutDto.setPercentageComplete(percentageCompleteDtos);
        //重组数组
//        List<ChargeUserPercentageCompleteOutDto> resultStorePercentageCompleteOutDtos =  new LinkedList<>();

        //5.返回
        chargeUserPercentageCompleteOutDtos.sort((x, y) -> StringUtils.compare(x.getTargetCode(), y.getTargetCode()));

        chargeUserPercentageCompleteOutDtos.addFirst(storePercentageCompleteOutDto);
        return chargeUserPercentageCompleteOutDtos;
    }

    /**
     * 初始化返回数据
     */

    private TargetCompleteOutDTO getTargetCompleteOutDTO(QueryTargetDataDTO addStoreCompleteDto) {
        TargetCompleteOutDTO targetCompleteOutDTO = new TargetCompleteOutDTO();
        targetCompleteOutDTO.setTargetCode(addStoreCompleteDto.getTargetCode());
        targetCompleteOutDTO.setTargetName(addStoreCompleteDto.getTargetName());
        targetCompleteOutDTO.setTargetId(addStoreCompleteDto.getTargetId());
        return targetCompleteOutDTO;
    }

    /**
     * 如果完成对象都为空则手动赋值
     */

    private List<PercentageCompleteDto> getPercentageCompleteDtos1(String queryWordValue,
                                                                   BigDecimal cluesNums,BigDecimal cluesOtherNums, BigDecimal dealNums, BigDecimal intoStoresNums,
                                                                   BigDecimal designNums, BigDecimal buryPipeNums, BigDecimal goodsCount, BigDecimal salesAmount,
                                                                   BigDecimal completeCluesNums,BigDecimal completeCluesOtherNums, BigDecimal completeDealNums, BigDecimal completeIntoStoresNums,
                                                                   BigDecimal completeDesignNums, BigDecimal completeBuryPipeNums, BigDecimal completeGoodsCount, BigDecimal completeSalesAmount, BigDecimal unitPrice, BigDecimal completeUnitPrice,
                                                                   BigDecimal drainage, BigDecimal stock, BigDecimal manualCluesFollowNums,BigDecimal completeManualCluesFollowNums,BigDecimal totalDrainageCompleteNums, BigDecimal totalStockCompleteNums,
                                                                   BigDecimal drainageChannelRetain,BigDecimal completeDrainageChannelRetain, BigDecimal drainageChannelActive, BigDecimal completedrainageChannelActive) {
        List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();

        //销售金额(万)
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtoSalesAmount = getpercentageComplete(
                    salesAmount == null ? BigDecimal.ZERO : salesAmount, CommonConst.TargetCompleteEnum.SALES_AMOUNT.getName(), CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol(), completeSalesAmount);
            percentageCompleteDtos.add(percentageCompleteDtoSalesAmount);

        }
        //成交均价(元)
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol())) {
            PercentageCompleteDto percentageCompleteDtoUnitPrice = getpercentageComplete(
                    unitPrice == null ? BigDecimal.ZERO : unitPrice, CommonConst.TargetCompleteEnum.UNIT_PRICE.getName(), CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol(), completeUnitPrice);
            percentageCompleteDtos.add(percentageCompleteDtoUnitPrice);

        }
        //台数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtogoods = getpercentageComplete(
                    goodsCount == null ? BigDecimal.ZERO : goodsCount, CommonConst.TargetCompleteEnum.GOODS_COUNT.getName(), CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol(), completeGoodsCount);
            percentageCompleteDtos.add(percentageCompleteDtogoods);
        }
        //新增线索
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesNums = getpercentageComplete(
                    cluesNums == null ? BigDecimal.ZERO : cluesNums, CommonConst.TargetCompleteEnum.CLUES_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol(), completeCluesNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesNums);
        }
        //新增线索数-不含O2O
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesOtherNums = getpercentageComplete(
                    cluesOtherNums == null ? BigDecimal.ZERO : cluesOtherNums, CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol(), completeCluesOtherNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesOtherNums);
        }
        //进店
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtointoStoresNums = getpercentageComplete(
                    intoStoresNums == null ? BigDecimal.ZERO : intoStoresNums, CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getName(), CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol(), completeIntoStoresNums);
            percentageCompleteDtos.add(percentageCompleteDtointoStoresNums);
        }
        //上门设计数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoDesign = getpercentageComplete(
                    designNums == null ? BigDecimal.ZERO : designNums, CommonConst.TargetCompleteEnum.DESIGN_NUMS.getName(), CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol(), completeDesignNums);
            percentageCompleteDtos.add(percentageCompleteDtoDesign);
        }
        //预埋烟管数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoBuryPipeNums = getpercentageComplete(
                    buryPipeNums == null ? BigDecimal.ZERO : buryPipeNums, CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getName(), CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol(), completeBuryPipeNums);
            percentageCompleteDtos.add(percentageCompleteDtoBuryPipeNums);
        }
        //成交
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtodeal = getpercentageComplete(
                    dealNums == null ? BigDecimal.ZERO : dealNums, CommonConst.TargetCompleteEnum.DEAL_NUMS.getName(), CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol(), completeDealNums);
            percentageCompleteDtos.add(percentageCompleteDtodeal);
        }
        //人工写跟进次数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoManualCluesFollowNums = getpercentageComplete(
                    manualCluesFollowNums == null ? BigDecimal.ZERO : manualCluesFollowNums, CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getName(), CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol(), completeManualCluesFollowNums);
            percentageCompleteDtos.add(percentageCompleteDtoManualCluesFollowNums);
        }
        //引流渠道保有量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    drainageChannelRetain == null ? BigDecimal.ZERO : drainageChannelRetain, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol(), completeDrainageChannelRetain));
        }
        //引流渠道活跃量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    drainageChannelActive == null ? BigDecimal.ZERO : drainageChannelActive, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol(), completedrainageChannelActive));
        }

        //主动引流线索占比
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CLUES_PROPORTION.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    drainage == null ? BigDecimal.ZERO : drainage, CommonConst.TargetCompleteEnum.DRAINAGE_CLUES_PROPORTION.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CLUES_PROPORTION.getCol(), totalDrainageCompleteNums));

        }
        //存量线索跟进占比
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.STOCK_CLUES_PROPORTION.getCol())) {
            percentageCompleteDtos.add(getpercentageComplete(
                    stock == null ? BigDecimal.ZERO : stock, CommonConst.TargetCompleteEnum.STOCK_CLUES_PROPORTION.getName(), CommonConst.TargetCompleteEnum.STOCK_CLUES_PROPORTION.getCol(), totalStockCompleteNums));
        }
        return percentageCompleteDtos;
    }


    private List<PercentageCompleteDto> getPercentageCompleteDtos2(String queryWordValue,
                                                                   BigDecimal cluesNums,BigDecimal cluesOtherNums, BigDecimal dealNums, BigDecimal intoStoresNums,
                                                                   BigDecimal designNums, BigDecimal buryPipeNums, BigDecimal goodsCount, BigDecimal salesAmount,
                                                                   BigDecimal completeCluesNums,BigDecimal completeCluesOtherNums, BigDecimal completeDealNums, BigDecimal completeIntoStoresNums,
                                                                   BigDecimal completeDesignNums, BigDecimal completeBuryPipeNums, BigDecimal completeGoodsCount, BigDecimal completeSalesAmount,
                                                                   BigDecimal manualCluesFollowNums,BigDecimal completeManualCluesFollowNums,BigDecimal unitPrice, BigDecimal completeUnitPrice,
                                                                   BigDecimal drainageChannelRetainNums,
                                                                   BigDecimal completeDrainageChannelRetainNums,
                                                                   BigDecimal drainageChannelActiveNums,
                                                                   BigDecimal completeDrainageChannelActiveNums) {
        List<PercentageCompleteDto> percentageCompleteDtos = new LinkedList<>();
        //销售金额(万)
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtoSalesAmount = getpercentageComplete(
                    salesAmount == null ? BigDecimal.ZERO : salesAmount, CommonConst.TargetCompleteEnum.SALES_AMOUNT.getName(), CommonConst.TargetCompleteEnum.SALES_AMOUNT.getCol(), completeSalesAmount);
            percentageCompleteDtos.add(percentageCompleteDtoSalesAmount);

        }
        //成交均价(元)
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol())) {
            PercentageCompleteDto percentageCompleteDtoUnitPrice = getpercentageComplete(
                    unitPrice == null ? BigDecimal.ZERO : unitPrice, CommonConst.TargetCompleteEnum.UNIT_PRICE.getName(), CommonConst.TargetCompleteEnum.UNIT_PRICE.getCol(), completeUnitPrice);
            percentageCompleteDtos.add(percentageCompleteDtoUnitPrice);

        }
        //台数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol())) {
            PercentageCompleteDto percentageCompleteDtogoods = getpercentageComplete(
                    goodsCount == null ? BigDecimal.ZERO : goodsCount, CommonConst.TargetCompleteEnum.GOODS_COUNT.getName(), CommonConst.TargetCompleteEnum.GOODS_COUNT.getCol(), completeGoodsCount);
            percentageCompleteDtos.add(percentageCompleteDtogoods);
        }
        //新增线索
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesNums = getpercentageComplete(
                    cluesNums == null ? BigDecimal.ZERO : cluesNums, CommonConst.TargetCompleteEnum.CLUES_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_NUMS.getCol(), completeCluesNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesNums);
        }

        //新增线索数-不含O2O
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoCluesOtherNums = getpercentageComplete(
                    cluesOtherNums == null ? BigDecimal.ZERO : cluesOtherNums, CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getName(), CommonConst.TargetCompleteEnum.CLUES_OTHER_NUMS.getCol(), completeCluesOtherNums);
            percentageCompleteDtos.add(percentageCompleteDtoCluesOtherNums);
        }
        //进店
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtointoStoresNums = getpercentageComplete(
                    intoStoresNums == null ? BigDecimal.ZERO : intoStoresNums, CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getName(), CommonConst.TargetCompleteEnum.INTO_STORES_NUMS.getCol(), completeIntoStoresNums);
            percentageCompleteDtos.add(percentageCompleteDtointoStoresNums);
        }
        //上门设计数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoDesign = getpercentageComplete(
                    designNums == null ? BigDecimal.ZERO : designNums, CommonConst.TargetCompleteEnum.DESIGN_NUMS.getName(), CommonConst.TargetCompleteEnum.DESIGN_NUMS.getCol(), completeDesignNums);
            percentageCompleteDtos.add(percentageCompleteDtoDesign);
        }
        //预埋烟管数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoBuryPipeNums = getpercentageComplete(
                    buryPipeNums == null ? BigDecimal.ZERO : buryPipeNums, CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getName(), CommonConst.TargetCompleteEnum.BURY_PIPE_NUMS.getCol(), completeBuryPipeNums);
            percentageCompleteDtos.add(percentageCompleteDtoBuryPipeNums);
        }
        //成交
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtodeal = getpercentageComplete(
                    dealNums == null ? BigDecimal.ZERO : dealNums, CommonConst.TargetCompleteEnum.DEAL_NUMS.getName(), CommonConst.TargetCompleteEnum.DEAL_NUMS.getCol(), completeDealNums);
            percentageCompleteDtos.add(percentageCompleteDtodeal);
        }
        //人工写跟进次数
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol())) {
            PercentageCompleteDto percentageCompleteDtoManualCluesFollowNums = getpercentageComplete(
                    manualCluesFollowNums == null ? BigDecimal.ZERO : manualCluesFollowNums, CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getName(), CommonConst.TargetCompleteEnum.MANUAL_CLUES_FOLLOW_NUMS.getCol(), completeManualCluesFollowNums);
            percentageCompleteDtos.add(percentageCompleteDtoManualCluesFollowNums);
        }
        //引流渠道保有量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol())) {
            PercentageCompleteDto percentageCompleteDto = getpercentageComplete(
                    drainageChannelRetainNums == null ? BigDecimal.ZERO : drainageChannelRetainNums, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_RETAIN.getCol(), completeDrainageChannelRetainNums);
            percentageCompleteDtos.add(percentageCompleteDto);
        }
        //引流渠道活跃量
        if (queryWordValue.contains(CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol())) {
            PercentageCompleteDto percentageCompleteDto = getpercentageComplete(
                    drainageChannelActiveNums == null ? BigDecimal.ZERO : drainageChannelActiveNums, CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getName(), CommonConst.TargetCompleteEnum.DRAINAGE_CHANNEL_ACTIVE.getCol(), completeDrainageChannelActiveNums);
            percentageCompleteDtos.add(percentageCompleteDto);
        }

        return percentageCompleteDtos;
    }


}
