package com.fotile.datacenter.completeData.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetStoreCompleteOutDto {
    /**
     * id
     */
    private Long id;
    /**
     * 日期类型，1：月；2：周；3：日
     */
    private Integer dateType;//日期类型，1：月；2：周；3：日
    /**
     * 时间值
     */
    private String dateValue;//时间值
    /**
     * 开始时间
     */
    private Date startTime;//开始时间
    /**
     * 结束时间
     */
    private Date endTime;//结束时间
    /**
     * 周次
     */
    private String weeklyShowName;
    /**
     * 所属大区编码
     */
    private String regionCode;
    /**
     * 所属大区名称
     */
    private String regionName;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 业务员id
     */
    private Long chargeUesrId;
    /**
     * 业务员编码
     */
    private String chargeUesrCode;
    /**
     * 业务员名称
     */
    private String chargeUesrName;
    /**
     * 门店org_id
     */
    private Long storeOrgId;//门店org_id
    /**
     * 门店编码
     */
    private String storeCode;//门店编码
    /**
     * 门店名称
     */
    private String storeName;//门店名称

    /**
     * 线索数
     */
    private BigDecimal cluesNums;//线索数

    /**
     * 新增线索数-不含O2O
     */
    private BigDecimal cluesOtherNums;//新增线索数-不含O2O
    /**
     * 进店数
     */
    private BigDecimal intoStoresNums;//进店数
    /**
     * 上门设计数
     */
    private BigDecimal designNums;//上门设计数
    /**
     * 预埋烟管数
     */
    private BigDecimal buryPipeNums;//预埋烟管数
    /**
     * 成交数
     */
    private BigDecimal dealNums;//成交数
    /**
     * 总台数
     */
    private BigDecimal goodsCount;//总台数
    /**
     * 销售额
     */
    private BigDecimal salesAmount;//销售额
    /**
     * 成交均价 目标完成率标记点
     */
    private BigDecimal unitPrice;//成交均价 目标完成率标记点
    /**
     * 人工写跟进数
     */
    private BigDecimal manualCluesFollowNums;
    /**
     * 引流线索占比
     */
    @ApiModelProperty("引流线索占比")
    @TableField(value = "drainage_clues_proportion")
    private BigDecimal drainageCluesProportion;
    /**
     * 存量跟进占比
     */
    @ApiModelProperty("存量跟进占比")
    @TableField(value = "stock_clues_proportion")
    private BigDecimal stockCluesProportion;
    /**
     * 引流渠道保有量
     */
    @ApiModelProperty("引流渠道保有量")
    @TableField(value = "drainage_channel_retain")
    private BigDecimal drainageChannelRetain;
    /**
     * 引流渠道活跃量
     */
    @ApiModelProperty("引流渠道活跃量")
    @TableField(value = "drainage_channel_active")
    private BigDecimal drainageChannelActive;

    /**
     * 业务员目标list
     */
    List<GetChargeCompleteOutDto> chargeCompleteDtoList;
}
