package com.fotile.datacenter.completeData.pojo.dto.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fotile.datacenter.completeData.pojo.dto.PercentageCompleteDto;
import com.fotile.datacenter.completeData.pojo.dto.TargetCompleteOutDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.datacenter.completeData.pojo.dto.report
 * @date 2020/9/24 17:36
 */
@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class OutAreaTargetCompleteDto implements Serializable {

    /**
     * 大区名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = {"大区"}, index = 0)
    @ApiModelProperty("大区名称")
    private String areaName;

    /**
     * 开始时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"开始时间"}, index = 1)
    @ApiModelProperty("开始时间")
    private String startDate;

    /**
     * 结束时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"结束时间"}, index = 2)
    @ApiModelProperty("结束时间")
    private String endDate;


    /**
     * 目标销售额（万）
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标销售额（万）"}, index = 3)
    @ApiModelProperty("目标销售额（万）")
    private String salesTarget;

    /**
     * 目标成交均价（元）
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标成交均价（元）"}, index = 4)
    @ApiModelProperty("目标成交均价（元）")
    private String cpTarget;

    /**
     * 目标成交台量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标成交台量"}, index = 5)
    @ApiModelProperty("目标成交台量")
    private String goodsTarget;

    /**
     * 目标录入线索数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标录入线索数"}, index = 6)
    @ApiModelProperty("目标录入线索数")
    private String cluesTarget;

    /**
     * 目标进店数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标进店线索数"}, index = 7)
    @ApiModelProperty("目标进店线索数")
    private String intoTarget;

    /**
     * 目标上门设计
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标上门设计"}, index = 8)
    @ApiModelProperty("目标上门设计")
    private String designTarget;

    /**
     * 目标预埋烟管
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标预埋烟管"}, index = 9)
    @ApiModelProperty("目标预埋烟管")
    private String buryTarget;

    /**
     * 目标成交
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标成交"}, index = 10)
    @ApiModelProperty("目标成交")
    private String dealTarget;

    /**
     * 目标人工写跟进次数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标人工写跟进次数"}, index = 11)
    @ApiModelProperty("目标人工写跟进次数")
    private String manualCluesFollowTarget;

    /**
     * 目标引流渠道保有量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标引流渠道保有量"}, index = 12)
    @ApiModelProperty("目标引流渠道保有量")
    private String drainageChannelRetainTarget;


    /**
     * 目标引流渠道活跃量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标引流渠道活跃量"}, index = 13)
    @ApiModelProperty("目标引流渠道活跃量")
    private String drainageChannelActiveTarget;

    /**
     * 主动引流线索占比%
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标主动引流线索占比%"}, index = 14)
    @ApiModelProperty("目标主动引流线索占比%")
    private String drainageCluesTarget;

    /**
     * 目标存量线索跟进占比%
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"目标存量线索跟进占比%"}, index = 15)
    @ApiModelProperty("目标存量线索跟进占比%")
    private String stockCluesTarget;


    /**
     * 实际销售额（万元）
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际销售额（万）"}, index = 16)
    @ApiModelProperty("实际销售额（万）")
    private String salesComplete;

    /**
     * 实际成交均价（元）
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际成交均价（元）"}, index = 17)
    @ApiModelProperty("实际成交均价（元）")
    private String cpComplete;

    /**
     * 实际成交台量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际成交台量"}, index = 18)
    @ApiModelProperty("实际成交台量")
    private String goodsComplete;

    /**
     * 实际录入线索数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际录入线索数"}, index = 19)
    @ApiModelProperty("实际录入线索数")
    private String cluesComplete;

    /**
     * 实际进店线索数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际进店线索数"}, index = 20)
    @ApiModelProperty("实际进店线索数")
    private String intoComplete;

    /**
     * 实际上门设计
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际上门设计"}, index = 21)
    @ApiModelProperty("实际上门设计")
    private String designComplete;

    /**
     * 实际预埋烟管
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际预埋烟管"}, index = 22)
    @ApiModelProperty("实际预埋烟管")
    private String buryComplete;

    /**
     * 实际成交
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际成交"}, index = 23)
    @ApiModelProperty("实际成交")
    private String dealComplete;

    /**
     * 实际人工写跟进次数
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际人工写跟进次数"}, index = 24)
    @ApiModelProperty("实际人工写跟进次数")
    private String manualCluesFollowComplete;

    /**
     * 实际引流渠道保有量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际引流渠道保有量"}, index = 25)
    @ApiModelProperty("实际引流渠道保有量")
    private String drainageChannelRetainComplete;


    /**
     * 实际引流渠道活跃量
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际引流渠道活跃量"}, index = 26)
    @ApiModelProperty("实际引流渠道活跃量")
    private String drainageChannelActiveComplete;

    /**
     * 实际主动引流线索占比%
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际主动引流线索占比%"}, index = 27)
    @ApiModelProperty("实际主动引流线索占比%")
    private String drainageCluesComplete;

    /**
     * 实际存量线索跟进占比%
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"实际存量线索跟进占比%"}, index = 28)
    @ApiModelProperty("实际存量线索跟进占比%")
    private String stockCluesComplete;


    /**
     * 销售额完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"销售额完成率"}, index = 29)
    @ApiModelProperty("销售额完成率")
    private String salesPct;

    /**
     * 成交均价完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"成交均价完成率"}, index = 30)
    @ApiModelProperty("成交均价完成率")
    private String cpPct;

    /**
     * 成交台量完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"成交台量完成率"}, index = 31)
    @ApiModelProperty("成交台量完成率")
    private String goodsPct;

    /**
     * 录入线索数完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"录入线索数完成率"}, index = 32)
    @ApiModelProperty("录入线索数完成率")
    private String cluesPct;

    /**
     * 进店线索数完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"进店线索数完成率"}, index = 33)
    @ApiModelProperty("进店线索数完成率")
    private String intoPct;

    /**
     * 上门设计完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"上门设计完成率"}, index = 34)
    @ApiModelProperty("上门设计完成率")
    private String designPct;

    /**
     * 预埋烟管完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"预埋烟管完成率"}, index = 35)
    @ApiModelProperty("预埋烟管完成率")
    private String buryPct;

    /**
     * 成交完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"成交完成率"}, index = 36)
    @ApiModelProperty("成交完成率")
    private String dealPct;

    /**
     * 人工写跟进次数完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"人工写跟进次数完成率"}, index = 37)
    @ApiModelProperty("人工写跟进次数完成率")
    private String manualCluesFollowPct;

    /**
     * 引流渠道保有量完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"引流渠道保有量完成率"}, index = 38)
    @ApiModelProperty("引流渠道保有量完成率")
    private String drainageChannelRetainPct;


    /**
     * 引流渠道活跃量完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"引流渠道活跃量完成率"}, index = 39)
    @ApiModelProperty("引流渠道活跃量完成率")
    private String drainageChannelActivePct;

    /**
     * 主动引流线索占比%完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"主动引流线索占比%完成率"}, index = 40)
    @ApiModelProperty("主动引流线索占比%完成率")
    private String drainageCluesPct;

    /**
     * 存量线索跟进占比%完成率
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"存量线索跟进占比%完成率"}, index = 41)
    @ApiModelProperty("存量线索跟进占比%完成率")
    private String stockCluesPct;


    //设置销售金额信息
    private void setSalesData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setSalesTarget(data.getTargetNumber());
        this.setSalesComplete(data.getCompleteNumber());
        this.setSalesPct(data.getPercentage());
    }

    //设置成交均价信息
    private void setCustomPriceData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setCpTarget(data.getTargetNumber());
        this.setCpComplete(data.getCompleteNumber());
        this.setCpPct(data.getPercentage());
    }

    //设置录入订单台数信息
    private void setGoodsData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setGoodsTarget(data.getTargetNumber());
        this.setGoodsComplete(data.getCompleteNumber());
        this.setGoodsPct(data.getPercentage());
    }

    //设置录入线索数量信息
    private void setCluesData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setCluesTarget(data.getTargetNumber());
        this.setCluesComplete(data.getCompleteNumber());
        this.setCluesPct(data.getPercentage());
    }

    //设置进店数信息
    private void setIntoStoreData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setIntoTarget(data.getTargetNumber());
        this.setIntoComplete(data.getCompleteNumber());
        this.setIntoPct(data.getPercentage());
    }

    //设置上门设计信息
    private void setDesignData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setDesignTarget(data.getTargetNumber());
        this.setDesignComplete(data.getCompleteNumber());
        this.setDesignPct(data.getPercentage());
    }

    //设置预埋烟管信息
    private void setBuryData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setBuryTarget(data.getTargetNumber());
        this.setBuryComplete(data.getCompleteNumber());
        this.setBuryPct(data.getPercentage());
    }

    //设置成交信息
    private void setDealData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setDealTarget(data.getTargetNumber());
        this.setDealComplete(data.getCompleteNumber());
        this.setDealPct(data.getPercentage());
    }

    //人工写跟进次数
    private void setManualCluesFollowData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setManualCluesFollowTarget(data.getTargetNumber());
        this.setManualCluesFollowComplete(data.getCompleteNumber());
        this.setManualCluesFollowPct(data.getPercentage());
    }

    //主动引流线索占比
    private void setDrainageCluesData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setDrainageCluesTarget(data.getTargetNumber());
        this.setDrainageCluesComplete(data.getCompleteNumber());
        this.setDrainageCluesPct(data.getPercentage());
    }

    //存量线索跟进占比
    private void setStockCluesData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setStockCluesTarget(data.getTargetNumber());
        this.setStockCluesComplete(data.getCompleteNumber());
        this.setStockCluesPct(data.getPercentage());
    }

    private void setDrainageChannelRetainData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setDrainageChannelRetainTarget(data.getTargetNumber());
        this.setDrainageChannelRetainComplete(data.getCompleteNumber());
        this.setDrainageChannelRetainPct(data.getPercentage());
    }

    private void setDrainageChannelActiveData(PercentageCompleteDto data) {
        if (data == null) return;
        this.setDrainageChannelActiveTarget(data.getTargetNumber());
        this.setDrainageChannelActiveComplete(data.getCompleteNumber());
        this.setDrainageChannelActivePct(data.getPercentage());
    }

    private void generateColumns(List<PercentageCompleteDto> list) {
        if (list == null || list.size() <= 0)
            return;

        for (PercentageCompleteDto data : list) {
            switch (data.getWordValueId()) {
                case "clues_nums":
                    this.setCluesData(data);
                    break;
                case "deal_nums":
                    this.setDealData(data);
                    break;
                case "into_stores_nums":
                    this.setIntoStoreData(data);
                    break;
                case "design_nums":
                    this.setDesignData(data);
                    break;
                case "bury_pipe_nums":
                    this.setBuryData(data);
                    break;
                case "goods_count":
                    this.setGoodsData(data);
                    break;
                case "sales_amount":
                    this.setSalesData(data);
                    break;
                case "unit_price"://目标完成率标记点
                    this.setCustomPriceData(data);
                    break;
                case "manual_clues_follow_nums":
                    this.setManualCluesFollowData(data);
                    break;
                case "drainage_clues_proportion":
                    this.setDrainageCluesData(data);
                    break;
                case "stock_clues_proportion":
                    this.setStockCluesData(data);
                    break;
                case "drainage_channel_retain":
                    this.setDrainageChannelRetainData(data);
                    break;
                case "drainage_channel_active":
                    this.setDrainageChannelActiveData(data);
                    break;
                default:
                    break;
            }
        }
    }

    public List<OutAreaTargetCompleteDto> getReportData(List<TargetCompleteOutDTO> basicTargetComplete, String startDate, String endDate) {
        if (basicTargetComplete == null || basicTargetComplete.size() <= 0)
            return null;

        List<OutAreaTargetCompleteDto> results = new ArrayList<>();
        OutAreaTargetCompleteDto item = null;
        for (TargetCompleteOutDTO target : basicTargetComplete) {
            if (target.getTargetName().equals("合计"))
                continue;

            item = new OutAreaTargetCompleteDto();
            item.setAreaName(target.getTargetName());
            item.setStartDate(startDate);
            item.setEndDate(endDate);
            item.generateColumns(target.getPercentageComplete());
            results.add(item);
        }
        return results;
    }
}
