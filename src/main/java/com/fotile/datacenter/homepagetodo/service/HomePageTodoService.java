package com.fotile.datacenter.homepagetodo.service;

import com.alibaba.fastjson.JSONObject;
import com.fotile.datacenter.client.OmsCenterclient;
import com.fotile.datacenter.client.OrgClient;
import com.fotile.datacenter.client.UserClient;
import com.fotile.datacenter.client.UserCluesClient;
import com.fotile.datacenter.client.pojo.dto.MenuUrisWithRoleAuthorInDTO;
import com.fotile.datacenter.client.pojo.dto.StoreEntity;
import com.fotile.datacenter.client.pojo.marketing.AttributeVO;
import com.fotile.datacenter.client.pojo.marketing.SelectAllBySearchInDto;
import com.fotile.datacenter.client.pojo.marketing.SelectAllBySearchOutDto;
import com.fotile.datacenter.client.pojo.oms.FindDecOrderByCluesIdInDto;
import com.fotile.datacenter.client.pojo.oms.FindDecOrderByCluesIdOutDto;
import com.fotile.datacenter.client.pojo.org.AppSearchChannelRequest;
import com.fotile.datacenter.client.pojo.org.AppSearchChannelResponse;
import com.fotile.datacenter.common.constant.CommonConst;
import com.fotile.datacenter.homepagetodo.config.HomePageThreadConfig;
import com.fotile.datacenter.homepagetodo.pojo.dto.HomePageTodoOutDTO;
import com.fotile.datacenter.homepagetodo.pojo.dto.HomePageTodoReqDto;
import com.fotile.datacenter.homepagetodo.pojo.dto.HomePageTodoResDto;
import com.fotile.datacenter.util.CommonUtils;
import com.fotile.datacenter.util.LogUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.fotile.datacenter.common.constant.CommonConst.TodoType.*;

/**
 * 22015-APP首页-代办事项service
 */
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
@Slf4j
public class HomePageTodoService {

    @Autowired
    private OrgClient orgClient;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private HomePageTodoAdbDbService homePageTodoAdbDbService;
    @Autowired
    private UserClient userClient;
    @Qualifier(value = HomePageThreadConfig.HOME_PAGE_THREAD_EXECUTOR)
    @Autowired
    private ExecutorService executorService;
    @Qualifier(value = HomePageThreadConfig.DAY15_NOT_FOLLOW_CHANNEL_THREAD_EXECUTOR)
    @Autowired
    private ExecutorService day15NotFollowChannelThreadExecutor;
    @Autowired
    private UserCluesClient userCluesClient;
    @Autowired
    private OmsCenterclient omsCenterclient;

    /**
     * APP首页代办事项列表（实时-limit100优化版）
     *
     * @return 代办事项列表
     */
    public List<HomePageTodoOutDTO> realtimeTodoListLimit() {
        //原入参（HomePageTodoInDTO类），现改为后端获取登录人信息
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        if (Objects.isNull(userAuthor)) {
            throw new BusinessException("代办事项查询异常：账号异常，请联系管理员");
        }
        String userId = userAuthor.getUserId();
        Long salesmanId = userAuthor.getSalesmanId();
        if (Objects.isNull(salesmanId)) {
            throw new BusinessException("代办事项查询异常：账号未关联业务员，数据无查看权限");
        }

        List<HomePageTodoOutDTO> resultList = new ArrayList<>();

        //获取业务员岗位，若岗位不在范围内，不显示代办事项
        Long stationId = getSalesmanStationRemote(salesmanId);
        if (Objects.isNull(stationId) || !CommonConst.TODO_STATION_ID_SET.contains(stationId)) {
            return null;
        }

        //获取部门权限下的业务员id集合, 若没有部门权限或全部部门权限，不显示 1 统计10天未跟进线索（部门权限） 2 明日将有 XXX条线索，进入公海，请及时处理 代办事项
        //todo:引入common包2.4版本时 修改为userAuthorConfig.isAllDepAuthorPacking(false);

        List<String> salesmanIdStrs = new ArrayList<>();

        if (CommonConst.TODO_STATION_ID_SELF_SET.contains(stationId)) {
            salesmanIdStrs.add(String.valueOf(salesmanId));
        } else {
            Boolean isAllDepAuthor = userAuthorConfig.isAllDepAuthorPacking(false);
            if (Objects.nonNull(isAllDepAuthor) && !isAllDepAuthor) {
                salesmanIdStrs = findSalesmanIdStrsByDepAuthorRemote();
            }
        }

        if(CollectionUtils.isNotEmpty(salesmanIdStrs)){
            List<List<String>> salesmanIdStrsNest = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(salesmanIdStrs)){
                salesmanIdStrsNest = CommonUtils.splitList(salesmanIdStrs, 1000);
            }

            //1 统计10天未跟进线索（部门权限）
            List<Long> cluesIdList = homePageTodoAdbDbService.statisticsTodoForDay10NotFollowCluesBySalesmanIdsForIdList(salesmanIdStrsNest);
            if (CollectionUtils.isNotEmpty(cluesIdList)) {
                HomePageTodoOutDTO po = new HomePageTodoOutDTO();
                po.setTodoType(DAY10_NOT_FOLLOW_CLUES);
                po.setContent("10天未跟进线索:" + (cluesIdList.size() >= 100 ? "100+" : cluesIdList.size()) + "条; 快去跟进吧");
                po.setSalesmanId(salesmanId);
                po.setIds(cluesIdList);

                resultList.add(po);
            }

            //2 明日将有 XXX条线索，进入公海，请及时处理
            //2.1 获取该业务员的线索去重后有多少种门店或公司的过期时间配置
//        List<TodoCluesOverdueDTO> storeCluesOverdueList = homePageTodoSixthDbService.listStoreCluesOverdueOnUserCluesBySalesmanId(salesmanId);
//        List<TodoCluesOverdueDTO> companyCluesOverdueList = homePageTodoSixthDbService.listCompanyCluesOverdueOnUserCluesBySalesmanId(salesmanId);

            //2.2 查出负责人是该业务员的所有线索

            //优化后直接sql算出总数,与产品沟通，原要求部门权限，但性能问题，现修改为查询当前业务员的数据
            List<Long> tomorrowIntoSeaIdList = homePageTodoAdbDbService.countTomorrowIntoSeaOnUserCluesBySalesmanIdsForIdList(salesmanIdStrsNest);
            if (CollectionUtils.isNotEmpty(tomorrowIntoSeaIdList)) {
                HomePageTodoOutDTO po = new HomePageTodoOutDTO();
                po.setTodoType(TMR_CLUES_INTO_SEA);
                po.setContent("明天将有" + (tomorrowIntoSeaIdList.size() >= 100 ? "100+" : tomorrowIntoSeaIdList.size()) + "条线索, 进入公海, 请及时处理");
                po.setSalesmanId(salesmanId);
                po.setIds(tomorrowIntoSeaIdList);
                resultList.add(po);
            }
        }

        //3 你有X条公海线索未分配
        //3.1 判断是否有线索看自己和看全部权限
        MenuUrisWithRoleAuthorInDTO menuUriInDTO = new MenuUrisWithRoleAuthorInDTO();
        menuUriInDTO.setUserId(userId);
        //添加菜单配置中的uri（公海线索列表、公海线索自己）
        menuUriInDTO.setMenuUriList(new ArrayList<>(Arrays.asList(CommonConst.CluesMenuAuthorType.ALL, CommonConst.CluesMenuAuthorType.SELF)));
        List<String> menuUriList = listMenuUriListWithRoleAuthorByUrisRemote(menuUriInDTO);

//        Integer seaCluesCountNum = 0;
        List<Long> seaCluesIdList = new ArrayList<>();
        //菜单权限有的情况下计算公海线索未分配数
        if (CollectionUtils.isNotEmpty(menuUriList)) {
            if (menuUriList.contains(CommonConst.CluesMenuAuthorType.ALL)) {
                //3.2 获取公司交门店的权限（启用&筹备）
//                List<Long> storeAuthorIdList = userAuthorUtils.getCompanyAuthorAndStoreAuthorList(userAuthorConfig.queryUserAuthor());

                List<Long> companyAuthorIdList = userAuthorConfig.queryCompanyAuthorIdList(1);
                List<Long> storeAuthorIdList = userAuthorConfig.queryStoreAuthorIdList(1);
                //门店id转字符串防止sql隐式转换
                List<String> storeAuthorIdStrList = null;
                if (CollectionUtils.isNotEmpty(storeAuthorIdList)) {
                    storeAuthorIdStrList = storeAuthorIdList.stream().map(String::valueOf).collect(Collectors.toList());
                }

                //分隔list防止大数据量影响sql性能
                List<List<String>> storeAuthorIdStrListNest = CommonUtils.splitList(storeAuthorIdStrList, 1000);
                //查菜单线索配置是全部的类型
                seaCluesIdList = homePageTodoAdbDbService.statisticsNotDistributebSeaCluesBySalesmanIdForIdList(companyAuthorIdList, storeAuthorIdStrListNest, null);
            }

            //若菜单线索配置有且仅有看自己
            if (!menuUriList.contains(CommonConst.CluesMenuAuthorType.ALL) && menuUriList.contains(CommonConst.CluesMenuAuthorType.SELF)) {
                seaCluesIdList = homePageTodoAdbDbService.statisticsNotDistributebSeaCluesBySalesmanIdForIdList(null,null, salesmanId);
            }
        }

        if (CollectionUtils.isNotEmpty(seaCluesIdList)) {
            HomePageTodoOutDTO po = new HomePageTodoOutDTO();
            po.setTodoType(SEA_CLUES_NOT_DISTRIBUTE);
            po.setContent("你有" + (seaCluesIdList.size() >= 100 ? "100+" : seaCluesIdList.size()) + "条公海线索未处理");
            po.setSalesmanId(salesmanId);
            po.setIds(seaCluesIdList);
            resultList.add(po);
        }

        //4 统计15天未跟进引流渠道
        //添加菜单配置中的uri（家装公司列表、异业三工列表）
        MenuUrisWithRoleAuthorInDTO decorateCompanyMenuUriInDTO = new MenuUrisWithRoleAuthorInDTO();
        decorateCompanyMenuUriInDTO.setUserId(userId);
        decorateCompanyMenuUriInDTO.setMenuUriList(new ArrayList<>(Arrays.asList(CommonConst.DecorateCompanyMenuAuthorType.DECORATE_COMPANY, CommonConst.DecorateCompanyMenuAuthorType.DIFF_INDUSTRIES)));
        List<String> decorateCompanyMenuUriList = listMenuUriListWithRoleAuthorByUrisRemote(decorateCompanyMenuUriInDTO);

        if (CollectionUtils.isNotEmpty(decorateCompanyMenuUriList)) {
            if (decorateCompanyMenuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.DECORATE_COMPANY)
                    || decorateCompanyMenuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.DIFF_INDUSTRIES)) {

                Integer channelCountNum = statisticsTodoForDay15NotFollowChannelBySalesmanIdRemote(salesmanId);
                if (channelCountNum > 0) {
                    HomePageTodoOutDTO po = new HomePageTodoOutDTO();
                    po.setTodoType(CommonConst.TodoType.DAY15_NOT_FOLLOW_CHANNEL);
                    po.setContent("15天未跟进引流渠道:" + (channelCountNum >= 100 ? "100+" : channelCountNum) + "条; 快去跟进吧");
                    po.setSalesmanId(salesmanId);

                    resultList.add(po);
                }
            }
        }

        return resultList;
    }

    /**
     * 调用usercenter获取根据uri列表查询角色权限下的菜单是否存在并返回存在的uri
     *
     * @return uri列表
     */
    public List<String> listMenuUriListWithRoleAuthorByUrisRemote(MenuUrisWithRoleAuthorInDTO dto) {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        Result<List<String>> result = null;
        try {
            result = userClient.listMenuUriListWithRoleAuthorByUris(dto);
        } catch (Exception e) {
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", e.getMessage());
            throw new BusinessException("远程调用根据uri列表查询角色权限下的菜单是否存在并返回存在的uri异常");
        }

        if (Objects.isNull(result)) {
            throw new BusinessException("远程调用根据uri列表查询角色权限下的菜单是否存在并返回存在的uri异常, result为null");
        }
        if (!result.getSuccess()) {
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 调用orgcenter获取单个业务员的15天未跟进引流渠道的代办事项
     *
     * @return 统计数
     */
    public Integer statisticsTodoForDay15NotFollowChannelBySalesmanIdRemote(Long salesmanId) {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        Result<Integer> result = null;
        try {
            result = orgClient.day15NotFollowChannelListBySalesmanId(salesmanId);
        } catch (Exception e) {
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", e.getMessage());
            throw new BusinessException("远程调用获取单个业务员的15天未跟进引流渠道的代办事项异常");
        }

        if (Objects.isNull(result)) {
            throw new BusinessException("远程调用获取单个业务员的15天未跟进引流渠道的代办事项异常, result为null");
        }
        if (!result.getSuccess()) {
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 调用orgcenter获取业务员岗位id
     *
     * @return 统计数
     */
    public Long getSalesmanStationRemote(Long salesmanId) {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        Result<Long> result = null;
        try {
            result = orgClient.getSalesmanStation(salesmanId);
        } catch (Exception e) {
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", e.getMessage());
            throw new BusinessException("远程调用获取业务员岗位id异常");
        }

        if (Objects.isNull(result)) {
            throw new BusinessException("远程调用获取业务员岗位id异常, result为null");
        }
        if (!result.getSuccess()) {
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }

    /**
     * 调用orgcenter获取业务员岗位id
     *
     * @return 统计数
     */
    public List<String> findSalesmanIdStrsByDepAuthorRemote() {
        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();

        Result<List<String>> result = null;
        try {
            result = orgClient.findSalesmanIdStrsByDepAuthor();
        } catch (Exception e) {
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", e.getMessage());
            throw new BusinessException("远程调用根据部门权限获取业务员id集合异常");
        }

        if (Objects.isNull(result)) {
            throw new BusinessException("远程调用根据部门权限获取业务员id集合异常, result为null");
        }
        if (!result.getSuccess()) {
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }

    public List<HomePageTodoResDto> queryTodoList(HomePageTodoReqDto dto) throws Exception {

        UserAuthor userAuthor = getSafeUserAuthor();
        List<HomePageTodoResDto> results = new ArrayList<>();
        if (CollectionUtils.isEmpty(dto.getTodoTypes())) {
            return results;
        }
        // 获取业务员岗位，若岗位不在范围内，不显示代办事项
        Long stationId = getSalesmanStationRemote(userAuthor.getSalesmanId());
        if (Objects.isNull(stationId) || !CommonConst.TODO_STATION_ID_SET.contains(stationId)) {
            return results;
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
            .getRequestAttributes();
        if (attributes == null) {
            return results;
        }
        HttpServletRequest request = attributes.getRequest();
        String authorization = request.getHeader("Authorization");
        String channelType = request.getHeader("channelType");
        String platformType = request.getHeader("platformType");
        String userId = userAuthor.getUserId();
        Long salesmanId = userAuthor.getSalesmanId();

        MenuUrisWithRoleAuthorInDTO menuUriInDTO = new MenuUrisWithRoleAuthorInDTO();
        menuUriInDTO.setUserId(userId);
        //添加菜单配置中的uri
        menuUriInDTO.setMenuUriList(new ArrayList<>(Arrays.asList(
            CommonConst.DecorateCompanyMenuAuthorType.DECORATE_COMPANY, CommonConst.DecorateCompanyMenuAuthorType.DIFF_INDUSTRIES,
            CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_WY_MENU, CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_ZQ_MENU,
            CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_RQ_MENU)));
        List<String> menuUriList = listMenuUriListWithRoleAuthorByUrisRemote(menuUriInDTO);

        // 待执行事项
        List<Future<HomePageTodoResDto>> futureList = new ArrayList<>();
        for (Integer todoType : dto.getTodoTypes()) {
            // 线程池执行异步任务
            CompletableFuture<HomePageTodoResDto> future = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                return statisticsHomePageTodoByRemote(salesmanId, todoType, authorization, channelType, platformType, menuUriList);
            }), executorService);
            futureList.add(future);
        }

        for (Future<HomePageTodoResDto> future : futureList) {
            try {
                // 10秒钟超时
//                HomePageTodoResDto pageTodoResDto = future.get(10, TimeUnit.SECONDS);
                // 不添加超时时间
                HomePageTodoResDto pageTodoResDto = future.get();
                if (Objects.nonNull(pageTodoResDto)) {
                    results.add(pageTodoResDto);
                }
            } catch (Exception e) {
                log.error("获取待办事项请求异常：", e);
            }
        }

        return results;
    }

    private UserAuthor getSafeUserAuthor() {
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        if (Objects.isNull(userAuthor)) {
            throw new BusinessException("代办事项查询异常：账号异常，请联系管理员");
        }
        if (Objects.isNull(userAuthor.getSalesmanId())) {
            throw new BusinessException("代办事项查询异常：账号未关联业务员，数据无查看权限");
        }
        return userAuthor;
    }

    public HomePageTodoResDto statisticsHomePageTodoByRemote(Long salesmanId, Integer todoType, String authorization, String channelType, String platformType,
        List<String> menuUriList) {

        if (Objects.isNull(todoType) || !CommonConst.TodoType.ALL_TODO_TYPES.contains(todoType)) {
            return null;
        }
        HomePageTodoResDto homePageTodo = new HomePageTodoResDto();
        try {
            switch (todoType) {
                // 10天未跟进线索
                case 1:
                    homePageTodo.setTodoType(DAY10_NOT_FOLLOW_CLUES);
                    homePageTodo.setTodoTypeName("10天未跟进线索");
                    homePageTodo.setTodoCount(getDay10NotFollowClueCount(authorization, channelType, platformType));
                    break;
                // 明日投公海线索
                case 2:
                    homePageTodo.setTodoType(TMR_CLUES_INTO_SEA);
                    homePageTodo.setTodoTypeName("明日投公海线索");
                    homePageTodo.setTodoCount(getTomorrowInfoSeaClueCount(authorization, channelType, platformType));
                    break;
                // 公海待处理线索
                case 3:
                    homePageTodo.setTodoType(SEA_CLUES_NOT_DISTRIBUTE);
                    homePageTodo.setTodoTypeName("公海待处理线索");
                    homePageTodo.setTodoCount(getNotDistributeSeaClueCount(authorization, channelType, platformType));
                    break;
                // 15天未跟进引流渠道
                case 4:
                    homePageTodo.setTodoType(DAY15_NOT_FOLLOW_CHANNEL);
                    homePageTodo.setTodoTypeName("15天未跟进引流渠道");
                    homePageTodo.setTodoCount(getDay15NotFollowChannelCount(authorization, channelType, platformType, salesmanId, menuUriList));
                    break;
                // 下发类线索未跟进数
                case 5:
                    homePageTodo.setTodoType(NOT_FOLLOW_CLUES);
                    homePageTodo.setTodoTypeName("下发类线索未跟进数");
                    homePageTodo.setTodoCount(getNotFollowClueCount(authorization, channelType, platformType));
                    break;
                // 待提交线索
                case 6:
                    homePageTodo.setTodoType(NOT_SUBMIT_CLUES);
                    homePageTodo.setTodoTypeName("待提交线索");
                    homePageTodo.setTodoCount(getNotSubmitClueCount(authorization, channelType, platformType));
                    break;
                // 审核拒绝线索
                case 7:
                    homePageTodo.setTodoType(AUDIT_REJECT_CLUES);
                    homePageTodo.setTodoTypeName("审核拒绝线索");
                    homePageTodo.setTodoCount(getAuditRejectClueCount(authorization, channelType, platformType));
                    break;
                // 审核拒绝订单
                case 8:
                    homePageTodo.setTodoType(AUDIT_REJECT_ORDERS);
                    homePageTodo.setTodoTypeName("审核拒绝订单");
                    homePageTodo.setTodoCount(getAuditRejectOrderCount(authorization, channelType, platformType));
                    break;
                default:
                    throw new BusinessException("代办事项查询异常：未知代办事项类型");
            }
        } catch (Exception e) {
            log.error("代办事项查询异常：", e);
        }
        if (Objects.isNull(homePageTodo.getTodoCount())) {
            homePageTodo.setTodoCount(-1L);
        }
        return homePageTodo;
    }

    /**
     * 获取审核拒绝订单数
     */
    public Long getAuditRejectOrderCount(String authorization, String channelType, String platformType) {
        FindDecOrderByCluesIdInDto request = new FindDecOrderByCluesIdInDto();
        request.setPage(1);
        request.setSize(1);
        request.setStageList(Lists.newArrayList(3));
        return getOrderCount(authorization, channelType, platformType, request);
    }

    /**
     * 获取审核拒绝线索数
     */
    public Long getAuditRejectClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setAuditStatusIds("3");
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(1L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    /**
     * 获取待提交线索数
     */
    public Long getNotSubmitClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setAuditStatusIds("40");
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(1L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    /**
     * 获取未跟进线索数
     */
    public Long getNotFollowClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setStatusIds("1");
        attributeVO.setAuditStatusIds("2");
        // 线索来源=o2o导入&crm系统导入&社区店服务引流
        attributeVO.setCluesSourceIds("7,8,10");
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(1L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    /**
     * 获取15天未跟进引流渠道数
     */
    public Long getDay15NotFollowChannelCount(String authorization, String channelType, String platformType,
        Long salesmanId, List<String> menuUriList) throws ExecutionException, InterruptedException {

        List<Future<Long>> futureList = new ArrayList<>();

        Long sum = 0L;
        // 家装公司
        if (menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.DIFF_INDUSTRIES)) {

            // 线程池执行异步任务
            CompletableFuture<Long> notFollowDecorateCompanyCount = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                return getDay15NotFollowDecorateCompanyCount(authorization, channelType, platformType);
            }), day15NotFollowChannelThreadExecutor);
            futureList.add(notFollowDecorateCompanyCount);

            CompletableFuture<Long> notFollowDesignerCount = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                return getDay15NotFollowDesignerCount(authorization, channelType, platformType);
            }), day15NotFollowChannelThreadExecutor);
            futureList.add(notFollowDesignerCount);

        }

        // 小B端引流
        if (menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.DECORATE_COMPANY)) {

            CompletableFuture<Long> notFollowSmallBEndCount = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                return getDay15NotFollowSmallBEndCount(authorization, channelType, platformType);
            }), day15NotFollowChannelThreadExecutor);
            futureList.add(notFollowSmallBEndCount);

        }

        // 新业务引流
        if (menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_WY_MENU)
            || menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_ZQ_MENU)
            || menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_RQ_MENU)) {

            CompletableFuture<Long> notFollowNewBusinessCount = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                return getDay15NotFollowNewBusinessCount(authorization, channelType, platformType, salesmanId, menuUriList);
            }), day15NotFollowChannelThreadExecutor);
            futureList.add(notFollowNewBusinessCount);

        }

        for (Future<Long> future : futureList) {
            Long count = future.get();
            if (count != null) {
                sum += count;
            }
        }

        return sum;
    }

    /**
     * 获取15天未跟进新业务数
     */
    public Long getDay15NotFollowNewBusinessCount(String authorization, String channelType, String platformType,
        Long salesmanId, List<String> menuUriList) {
        try {

            Result<StoreEntity> storeEntityResult = orgClient.findBySalesmanIdOpen(salesmanId);
            if (!storeEntityResult.getSuccess() || storeEntityResult.getData() == null) {
                log.error("业务员门店不存在，不计算15天未跟进新业务数");
                return null;
            }
            StoreEntity storeEntity = storeEntityResult.getData();

            if ((Objects.equals(storeEntity.getOpenWyChannel(), 1) && menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_WY_MENU))
                || (Objects.equals(storeEntity.getOpenZqChannel(), 1) && menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_ZQ_MENU))
                || (Objects.equals(storeEntity.getOpenRqChannel(), 1) && menuUriList.contains(CommonConst.DecorateCompanyMenuAuthorType.NEW_BIZ_RQ_MENU))) {
                AppSearchChannelRequest request = new AppSearchChannelRequest();
                request.setPage(1);
                request.setSize(1);
                request.setNotFollowDays(15);
                request.setBizDataType(4);
                Result<AppSearchChannelResponse> result = orgClient.appSearchDiffIndustry(authorization, channelType, platformType, request);
                if (!result.getSuccess()) {
                    log.error("获取15天未跟进新业务数异常：{}", result.getMsg());
                    throw new BusinessException("获取15天未跟进新业务数异常");
                }
                if (result.getData() != null && result.getData().getStats() != null && result.getData().getStats().getTotalCount() != null) {
                    return Long.valueOf(result.getData().getStats().getTotalCount());
                }
            }
        } catch (Exception e) {
            log.error("获取15天未跟进新业务数异常：", e);
            throw new BusinessException("获取15天未跟进新业务数异常");
        }
        return null;
    }

    /**
     * 获取15天未跟进小B端数
     */
    private Long getDay15NotFollowSmallBEndCount(String authorization, String channelType, String platformType) {
        AppSearchChannelRequest request = new AppSearchChannelRequest();
        request.setPage(1);
        request.setSize(1);
        request.setNotFollowDays(15);
        request.setBizDataType(2);
        try {
            Result<AppSearchChannelResponse> result = orgClient.appSearchDiffIndustry(authorization, channelType, platformType, request);
            if (!result.getSuccess()) {
                log.error("获取15天未跟进小B端数异常：{}", result.getMsg());
                throw new BusinessException("获取15天未跟进小B端数异常");
            }
            if (result.getData() != null && result.getData().getStats() != null && result.getData().getStats().getTotalCount() != null) {
                return Long.valueOf(result.getData().getStats().getTotalCount());
            }
        } catch (Exception e) {
            log.error("获取15天未跟进小B端数异常：", e);
            throw new BusinessException("获取15天未跟进小B端数异常");
        }
        return null;
    }

    /**
     * 获取15天未跟进设计师数
     */
    public Long getDay15NotFollowDesignerCount(String authorization, String channelType, String platformType) {
        AppSearchChannelRequest request = new AppSearchChannelRequest();
        request.setPage(1);
        request.setSize(1);
        request.setNotFollowDays(15);
        request.setBizDataType(3);
        try {
            Result<AppSearchChannelResponse> result = orgClient.appSearchDesigner(authorization, channelType, platformType, request);
            if (!result.getSuccess()) {
                log.error("获取15天未跟进设计师数异常：{}", result.getMsg());
                throw new BusinessException("获取15天未跟进设计师数异常");
            }
            if (result.getData() != null && result.getData().getStats() != null && result.getData().getStats().getTotalCount() != null) {
                return Long.valueOf(result.getData().getStats().getTotalCount());
            }
        } catch (Exception e) {
            log.error("获取15天未跟进设计师数异常：", e);
            throw new BusinessException("获取15天未跟进设计师数异常");
        }
        return null;
    }

    /**
     * 查询15天未跟进家装公司数
     */
    public Long getDay15NotFollowDecorateCompanyCount(String authorization, String channelType, String platformType) {
        AppSearchChannelRequest request = new AppSearchChannelRequest();
        request.setPage(1);
        request.setSize(1);
        request.setNotFollowDays(15);
        request.setBizDataType(1);
        try{
            Result<AppSearchChannelResponse> result = orgClient.appSearchDecorate(authorization, channelType, platformType, request);
            if (!result.getSuccess()) {
                log.error("查询15天未跟进家装公司数异常：{}", result.getMsg());
                throw new BusinessException("查询15天未跟进家装公司数异常");
            }
            if (result.getData() != null && result.getData().getStats() != null && result.getData().getStats().getTotalCount() != null) {
                return Long.valueOf(result.getData().getStats().getTotalCount());
            }
        }catch (Exception e){
            log.error("查询15天未跟进家装公司数异常：", e);
            throw new BusinessException("查询15天未跟进家装公司数异常");
        }
        return null;
    }

    /**
     * 获取公海待处理线索数
     */
    public Long getNotDistributeSeaClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setStatusIds("1,2,3");
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(0L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    /**
     * 获取明日公海线索数
     */
    public Long getTomorrowInfoSeaClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setStatusIds("1,2,3");
        attributeVO.setAuditStatusIds("2");
        attributeVO.setTomorrowOpenSeaFlag(1);
        // 是否明日投公海条件
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(1L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    /**
     * 获取10天未跟进线索数
     */
    public Long getDay10NotFollowClueCount(String authorization, String channelType, String platformType) {
        SelectAllBySearchInDto searchInDto = new SelectAllBySearchInDto();
        AttributeVO attributeVO = new AttributeVO();
        attributeVO.setStatusIds("1,2,3");
        attributeVO.setAuditStatusIds("2");
        attributeVO.setReviseTimeIds("10");
        searchInDto.setAttributeValueOutDtos(JSONObject.toJSONString(attributeVO));
        searchInDto.setIsPersonage(1L);
        // searchInDto.setOrderName("muc.last_edit_time desc");
        searchInDto.setPage(1);
        searchInDto.setSize(1);
        return getClueCount(authorization, channelType, platformType, searchInDto);
    }

    @Retryable(value = { Exception.class }, maxAttempts = 3, backoff = @Backoff(delay = 0, multiplier = 0))
    public Long getOrderCount(String authorization, String channelType, String platformType, FindDecOrderByCluesIdInDto request) {
        Result<PageInfo<FindDecOrderByCluesIdOutDto>> result = omsCenterclient.findDecOrderByCluesId(authorization, channelType, platformType, request);
        if (!result.getSuccess()) {
            log.error("获取订单数异常：{}", result.getMsg());
            throw new BusinessException(result.getMsg());
        }
        return result.getData().getTotal();
    }

    @Retryable(value = { Exception.class }, maxAttempts = 3, backoff = @Backoff(delay = 0, multiplier = 0))
    public Long getClueCount(String authorization, String channelType, String platformType, SelectAllBySearchInDto searchInDto) {
        Result<PageInfo<SelectAllBySearchOutDto>> result = userCluesClient.selectAllBySelectAllBySearch(authorization, channelType, platformType, searchInDto);
        if (!result.getSuccess()) {
            log.error("获取线索数异常：{}", result.getMsg());
            throw new BusinessException(result.getMsg());
        }
        return result.getData().getTotal();
    }

}
