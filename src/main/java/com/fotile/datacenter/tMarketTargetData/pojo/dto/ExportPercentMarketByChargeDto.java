package com.fotile.datacenter.tMarketTargetData.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class ExportPercentMarketByChargeDto implements Serializable {
    /**
     * 目标层级
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标层级"})
    @ApiModelProperty("目标层级")
    private String dataTargetCode;
    /**
     * 大区
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty("大区")
    private String regionName;

    /**
     * 分公司
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty("分公司")
    private String companyName;

    /**
     * 门店业务发展主管-业务员编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店业务发展主管-业务员编码"})
    @ApiModelProperty("门店业务发展主管-业务员编码")
    private String chargeUesrCode;
    /**
     * 门店业务发展主管
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店业务发展主管"})
    @ApiModelProperty("门店业务发展主管")
    private String chargeUesrName;

    /**
     * 门店编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})
    @ApiModelProperty("门店编码")
    private String storeCode;
    /**
     * 门店名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店名称"})
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 门店渠道
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty("门店渠道")
    private String storeTypeName;

    /**
     * 门店渠道细分
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty("门店渠道细分")
    private String storeSubChannelName;

    /**
     * 期间类型
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"期间类型"})
    @ApiModelProperty("期间类型")
    private String dateTypeValue;
    /**
     * 统计期间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"统计期间"})
    @ApiModelProperty("统计期间")
    private String dateValue;
    /**
     * 销售额（万元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"销售额（万元）"})
    @ApiModelProperty("销售额（万元）")
    private String salesAmount;
    /**
     * 成交均价（元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交均价（元）"})
    @ApiModelProperty("成交均价（元）")
    private String unitPrice;
    /**
     * 成交台量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交台量"})
    @ApiModelProperty("成交台量")
    private String goodsCount;
    /**
     * 录入线索数量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"录入线索数量"})
    @ApiModelProperty("录入线索数量")
    private String cluesNums;
    /**
     * 进店线索数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"进店线索数"})
    @ApiModelProperty("进店线索数")
    private String intoStoresNums;
    /**
     * 上门设计
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"上门设计"})
    @ApiModelProperty("上门设计")
    private String designNums;
    /**
     * 预埋烟管
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"预埋烟管"})
    @ApiModelProperty("预埋烟管")
    private String buryPipeNums;
    /**
     * 成交
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交"})
    @ApiModelProperty("成交")
    private String dealNums;
    /**
     * 人工写跟进次数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"人工写跟进次数"})
    @ApiModelProperty("人工写跟进次数")
    private String manualCluesFollowNums;
    /**
     * 引流渠道保有量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"引流渠道保有量"})
    @ApiModelProperty("引流渠道保有量")
    private String drainageChannelRetain;
    /**
     * 引流渠道活跃量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"引流渠道活跃量"})
    @ApiModelProperty("引流渠道活跃量")
    private String drainageChannelActive;
    /**
     * 主动引流线索占比%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"主动引流线索占比%"})
    @ApiModelProperty("主动引流线索占比%")
    private String drainageCluesProportion;
    /**
     * 存量线索跟进占比率%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"存量线索跟进占比率%"})
    @ApiModelProperty("存量线索跟进占比率%")
    private String stockCluesProportion;
}
