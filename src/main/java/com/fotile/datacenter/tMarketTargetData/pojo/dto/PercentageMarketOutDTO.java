package com.fotile.datacenter.tMarketTargetData.pojo.dto;

import com.fotile.datacenter.completeData.pojo.dto.PercentageCompleteDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PercentageMarketOutDTO implements Serializable {

    private String dataTargetCode;

    private String regionCode;
    private String regionName;

    private Long companyId;
    private String companyName;

    private Long chargeUesrId;
    private String chargeUesrCode;
    private String chargeUesrName;

    private Long storeOrgId;
    private String storeCode;
    private String storeName;
    private String storeTypeName;
    private String storeSubChannelName;
    private Long dateType;
    private String dateTypeValue;
    private String dateValue;
    private Date startTime;
    private Date endTime;
    private String weeklyShowName;
    private String targetWeeklyShowName;
    private String targetCluesNums;
    private String targetDealNums;
    private String targetIntoStoresNums;
    private String targetDesignNums;
    private String targetBuryPipeNums;
    private String targetGoodsCount;
    private String targetSalesAmount;
    private String targetUnitPrice;
    private String targetManualCluesFollowNums;
    private String targetDrainageChannelRetainNums;
    private String targetDrainageChannelActiveNums;
    private String targetDrainageNums;
    private String targetStockNums;
    private String completeCluesNums;
    private String completeDealNums;
    private String completeIntoStoresNums;
    private String completeDesignNums;
    private String completeBuryPipeNums;
    private String completeGoodsCount;
    private String completeSalesAmount;
    private String completeUnitPrice;
    private String completeManualCluesFollowNums;
    private String completeDrainageChannelRetainNums;
    private String completeDrainageChannelActiveNums;
    private String completeDrainageNums;
    private String completeStockNums;
    private String percentageWeeklyShowName;
    private String percentageCluesNums;
    private String percentageDealNums;
    private String percentageIntoStoresNums;
    private String percentageDesignNums;
    private String percentageBuryPipeNums;
    private String percentageGoodsCount;
    private String percentageSalesAmount;
    private String percentageUnitPrice;
    private String percentageManualCluesFollowNums;
    private String percentageDrainageChannelRetainNums;
    private String percentageDrainageChannelActiveNums;
    private String percentageDrainageNums;
    private String percentageStockNums;
    private List<PercentageCompleteDto> percentageComplete;
}
