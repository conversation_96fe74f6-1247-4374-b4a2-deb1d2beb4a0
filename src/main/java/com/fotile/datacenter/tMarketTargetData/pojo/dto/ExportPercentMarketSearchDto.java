package com.fotile.datacenter.tMarketTargetData.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(25)
@ContentRowHeight(25)
public class ExportPercentMarketSearchDto implements Serializable {

    /**
     * 目标层级
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标层级"})
    @ApiModelProperty("目标层级")
    private String dataTargetCode;
    /**
     * 大区
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"大区"})
    @ApiModelProperty("大区")
    private String regionName;

    /**
     * 分公司
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"分公司"})
    @ApiModelProperty("分公司")
    private String companyName;

    /**
     * 门店业务发展主管-业务员编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店业务发展主管-业务员编码"})
    @ApiModelProperty("门店业务发展主管-业务员编码")
    private String chargeUesrCode;
    /**
     * 门店业务发展主管
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店业务发展主管"})
    @ApiModelProperty("门店业务发展主管")
    private String chargeUesrName;

    /**
     * 门店编码
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店编码"})
    @ApiModelProperty("门店编码")
    private String storeCode;
    /**
     * 门店名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店名称"})
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 门店渠道
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道"})
    @ApiModelProperty("门店渠道")
    private String storeTypeName;

    /**
     * 门店渠道细分
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"门店渠道细分"})
    @ApiModelProperty("门店渠道细分")
    private String storeSubChannelName;

    /**
     * 期间类型
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"期间类型"})
    @ApiModelProperty("期间类型")
    private String dateTypeValue;
    /**
     * 统计期间
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"统计期间"})
    @ApiModelProperty("统计期间")
    private String dateValue;

    /**
     * 目标销售额（万元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标销售额（万元）"})
    @ApiModelProperty("目标销售额（万元）")
    private String targetSalesAmount;
    /**
     * 目标成交均价（元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标成交均价（元）"})
    @ApiModelProperty("目标成交均价（元）")
    private String targetUnitPrice;
    /**
     * 目标成交台量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标成交台量"})
    @ApiModelProperty("目标成交台量")
    private String targetGoodsCount;

    /**
     * 目标新增线索数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标新增线索数"})
    @ApiModelProperty("目标新增线索数")
    private String targetCluesNums;

    /**
     * 目标新增线索数-不含O2O
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标新增线索数-不含O2O"})
    @ApiModelProperty("目标新增线索数-不含O2O")
    private String targetCluesOtherNums;

    /**
     * 目标进店线索数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标进店线索数"})
    @ApiModelProperty("目标进店线索数")
    private String targetIntoStoresNums;
    /**
     * 目标上门设计
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标上门设计"})
    @ApiModelProperty("目标上门设计")
    private String targetDesignNums;
    /**
     * 目标预埋烟管
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标预埋烟管"})
    @ApiModelProperty("目标预埋烟管")
    private String targetBuryPipeNums;
    /**
     * 目标成交
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标成交"})
    @ApiModelProperty("目标成交")
    private String targetDealNums;
    /**
     * 目标人工写跟进次数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标人工写跟进次数"})
    @ApiModelProperty("目标人工写跟进次数")
    private String targetManualCluesFollowNums;
    /**
     * 目标引流渠道保有量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标引流渠道保有量"})
    @ApiModelProperty("目标引流渠道保有量")
    private String targetDrainageChannelRetainNums;
    /**
     * 目标引流渠道活跃量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标引流渠道活跃量"})
    @ApiModelProperty("目标引流渠道活跃量")
    private String targetDrainageChannelActiveNums;
    /**
     * 目标主动引流线索占比%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标主动引流线索占比%"})
    @ApiModelProperty("目标主动引流线索占比%")
    private String targetDrainageNums;
    /**
     * 目标存量线索跟进占比率%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"目标存量线索跟进占比率%"})
    @ApiModelProperty("目标存量线索跟进占比率%")
    private String targetStockNums;
    /**
     * 实际销售额（万元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际销售额（万元）"})
    @ApiModelProperty("实际销售额（万元）")
    private String completeSalesAmount;
    /**
     * 实际成交均价（万元）
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际成交均价（万元）"})
    @ApiModelProperty("实际成交均价（万元）")
    private String completeUnitPrice;
    /**
     * 实际成交台量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际成交台量"})
    @ApiModelProperty("实际成交台量")
    private String completeGoodsCount;

    /**
     * 实际新增线索数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际新增线索数"})
    @ApiModelProperty("实际新增线索数")
    private String completeCluesNums;

    /**
     * 实际新增线索数-不含O2O
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际新增线索数-不含O2O"})
    @ApiModelProperty("实际新增线索数-不含O2O")
    private String completeCluesOtherNums;

    /**
     * 实际进店线索数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际进店线索数"})
    @ApiModelProperty("实际进店线索数")
    private String completeIntoStoresNums;
    /**
     * 实际上门设计
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际上门设计"})
    @ApiModelProperty("实际上门设计")
    private String completeDesignNums;
    /**
     * 实际预埋烟管
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际预埋烟管"})
    @ApiModelProperty("实际预埋烟管")
    private String completeBuryPipeNums;
    /**
     * 实际成交
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际成交"})
    @ApiModelProperty("实际成交")
    private String completeDealNums;
    /**
     * 实际人工写跟进次数
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际人工写跟进次数"})
    @ApiModelProperty("实际人工写跟进次数")
    private String completeManualCluesFollowNums;
    /**
     * 实际引流渠道保有量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际引流渠道保有量"})
    @ApiModelProperty("实际引流渠道保有量")
    private String completeDrainageChannelRetainNums;
    /**
     * 实际引流渠道活跃量
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际引流渠道活跃量"})
    @ApiModelProperty("实际引流渠道活跃量")
    private String completeDrainageChannelActiveNums;
    /**
     * 实际主动引流线索占比%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际主动引流线索占比%"})
    @ApiModelProperty("实际主动引流线索占比%")
    private String completeDrainageNums;
    /**
     * 实际存量线索跟进占比率%
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"实际存量线索跟进占比率%"})
    @ApiModelProperty("实际存量线索跟进占比率%")
    private String completeStockNums;
    /**
     * 销售额完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"销售额完成率"})
    @ApiModelProperty("销售额完成率")
    private String percentageSalesAmount;
    /**
     * 成交均价完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交均价完成率"})
    @ApiModelProperty("成交均价完成率")
    private String percentageUnitPrice;
    /**
     * 成交台量完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交台量完成率"})
    @ApiModelProperty("成交台量完成率")
    private String percentageGoodsCount;

    /**
     * 新增线索数完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"新增线索数完成率"})
    @ApiModelProperty("新增线索数完成率")
    private String percentageCluesNums;

    /**
     * 新增线索数-不含O2O-完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"新增线索数-不含O2O-完成率"})
    @ApiModelProperty("新增线索数-不含O2O-完成率")
    private String percentageCluesOtherNums;

    /**
     * 进店完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"进店完成率"})
    @ApiModelProperty("进店完成率")
    private String percentageIntoStoresNums;
    /**
     * 上门设计完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"上门设计完成率"})
    @ApiModelProperty("上门设计完成率")
    private String percentageDesignNums;
    /**
     * 预埋烟管完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"预埋烟管完成率"})
    @ApiModelProperty("预埋烟管完成率")
    private String percentageBuryPipeNums;
    /**
     * 成交完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"成交完成率"})
    @ApiModelProperty("成交完成率")
    private String percentageDealNums;
    /**
     * 人工写跟进次数完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"人工写跟进次数完成率"})
    @ApiModelProperty("人工写跟进次数完成率")
    private String percentageManualCluesFollowNums;
    /**
     * 引流渠道保有量完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"引流渠道保有量完成率"})
    @ApiModelProperty("引流渠道保有量完成率")
    private String percentageDrainageChannelRetainNums;
    /**
     * 引流渠道活跃量完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"引流渠道活跃量完成率"})
    @ApiModelProperty("引流渠道活跃量完成率")
    private String percentageDrainageChannelActiveNums;
    /**
     * 主动引流线索占比%完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"主动引流线索占比%完成率"})
    @ApiModelProperty("主动引流线索占比%完成率")
    private String percentageDrainageNums;
    /**
     * 存量线索跟进占比%完成率
     */
    @ColumnWidth(15)
    @ExcelProperty(value = {"存量线索跟进占比%完成率"})
    @ApiModelProperty("存量线索跟进占比%完成率")
    private String percentageStockNums;


}
