package com.fotile.devops.serviceRequest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestIssueMapping;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TServiceRequestIssueMappingMapper extends BaseMapper<TServiceRequestIssueMapping> {
    int updateBatch(List<TServiceRequestIssueMapping> list);

    int updateBatchSelective(List<TServiceRequestIssueMapping> list);

    int batchInsert(@Param("list") List<TServiceRequestIssueMapping> list);

    int insertOrUpdate(TServiceRequestIssueMapping record);

    int insertOrUpdateSelective(TServiceRequestIssueMapping record);
}