package com.fotile.devops.serviceRequest.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.devops.serviceRequest.dao.TServiceRequestAttentionMapper;
import com.fotile.devops.serviceRequest.dao.TServiceRequestMarkMapper;
import com.fotile.devops.serviceRequest.pojo.bo.UpdateServiceRequestAttentionBO;
import com.fotile.devops.serviceRequest.pojo.dto.UpdateServiceRequestMarkDTO;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestAttention;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestMark;
import com.fotile.devops.utils.Utils;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TServiceRequestMarkService extends ServiceImpl<TServiceRequestMarkMapper, TServiceRequestMark> {

    @Transactional
    public void updateMark(UpdateServiceRequestMarkDTO updateServiceRequestMarkDTO) throws Exception {
        if(updateServiceRequestMarkDTO.getId()==null){
            throw new Exception("参数错误");
        }
        if(StringUtils.isEmpty(updateServiceRequestMarkDTO.getUserId())){
            throw new Exception("参数错误");
        }
        if(StringUtils.isEmpty(updateServiceRequestMarkDTO.getStatus())){
            throw new Exception("参数错误");
        }
        QueryWrapper<TServiceRequestMark> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("service_request_id", updateServiceRequestMarkDTO.getId());
        objectQueryWrapper.eq("user_id",updateServiceRequestMarkDTO.getUserId());
        List<TServiceRequestMark> tServiceRequestMarks = baseMapper.selectList(objectQueryWrapper);
        if (tServiceRequestMarks != null && tServiceRequestMarks.size() > 0 && "0".equals(updateServiceRequestMarkDTO.getStatus())) {
            for(TServiceRequestMark tServiceRequestMark : tServiceRequestMarks){
                baseMapper.deleteById(tServiceRequestMark.getId());
            }
        }
        if (tServiceRequestMarks != null && tServiceRequestMarks.size() > 0 && "1".equals(updateServiceRequestMarkDTO.getStatus())) {
            throw new Exception("您已标签过了");
        }
        if ((tServiceRequestMarks == null || tServiceRequestMarks.size() == 0) && "1".equals(updateServiceRequestMarkDTO.getStatus())) {
            TServiceRequestMark tServiceRequestMark= new TServiceRequestMark();
            tServiceRequestMark.setServiceRequestId(updateServiceRequestMarkDTO.getId());
            tServiceRequestMark.setUserId(updateServiceRequestMarkDTO.getUserId());
            tServiceRequestMark.setIsDeleted(new Long(0));
            tServiceRequestMark.setCreatedBy(updateServiceRequestMarkDTO.getUserId());
            tServiceRequestMark.setCreatedDate(new Date());
            tServiceRequestMark.setModifiedBy(updateServiceRequestMarkDTO.getUserId());
            tServiceRequestMark.setModifiedDate(new Date());
            baseMapper.insert(tServiceRequestMark);
        }
    }
}
