package com.fotile.devops.serviceRequest.service.impl;

import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.devopsUsers.service.DevopsUsersService;
import com.fotile.devops.serviceRequest.dao.TServiceRequestMapper;
import com.fotile.devops.serviceRequest.pojo.bo.SelectBoardBO;
import com.fotile.devops.serviceRequest.pojo.dto.SelectBoardDTO;
import com.fotile.devops.serviceRequest.pojo.vo.SelectServiceRequestVO;
import com.fotile.devops.serviceRequest.service.BoardService;
import com.fotile.framework.core.common.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("schedulingOngoing")
public class BoardSchedulingOngoingServiceImpl implements BoardService {

    @Autowired
    private DevopsUsersService devopsUsersService;

    @Autowired
    private TServiceRequestMapper tServiceRequestMapper;

    /**
     * 进行中的看板服务请求数据  数据原包括我关注 我创建 我提出以及我经办的所有数据
     *
     * @param selectBoardDTO
     * @return
     */
    @Override
    public PageInfo selectBoard(SelectBoardDTO selectBoardDTO) {
        PageInfo pageInfo = new PageInfo(selectBoardDTO.getPageNum(), selectBoardDTO.getPageSize());
        SelectBoardBO selectBoardBO = new SelectBoardBO();
        String userId = selectBoardDTO.getUserId();
        //获取我经办人id
        UserOutDto userOutDto = devopsUsersService.selectAllBykeycloakId(userId);
        selectBoardBO.setType("4");
        selectBoardBO.setUserId(userId);
        selectBoardBO.setStatus(selectBoardDTO.getStatus());
        selectBoardBO.setRequestType(selectBoardDTO.getRequestType());
        if(userOutDto==null) {//非系统用户登录
            selectBoardBO.setOwnerId(-1L);
        }else{
            selectBoardBO.setOwnerId(userOutDto.getId());
        }
        selectBoardBO.setOffset(pageInfo.getOffset());
        selectBoardBO.setSize(pageInfo.getSize());
        //排序
        if(StringUtils.isNotEmpty(selectBoardDTO.getSortOrder())&&StringUtils.isNotEmpty(selectBoardDTO.getSortName())) {
            selectBoardBO.setSortName(selectBoardDTO.getSortName());
            selectBoardBO.setSortOrder(selectBoardDTO.getSortOrder());
        }else{
            selectBoardBO.setSortName("createdDate");
            selectBoardBO.setSortOrder("desc");
        }
        int count = tServiceRequestMapper.selectBoardCount(selectBoardBO).intValue();
        if (count == 0) {
            return pageInfo;
        }
        //计算条数


        List<SelectServiceRequestVO> list = tServiceRequestMapper.selectBoard(selectBoardBO);
        if (list == null || list.size() == 0){
            return pageInfo;
        }
        pageInfo.setRecords(list);
        pageInfo.setTotal(count);
        return pageInfo;
    }
}
