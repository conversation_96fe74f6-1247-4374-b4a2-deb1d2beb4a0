package com.fotile.devops.serviceRequest.controller;

import com.fotile.devops.serviceRequest.service.TServiceRequestAttachmentsService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(value = "附件")
@RestController
@RequestMapping(value = "/api", produces = MediaType.APPLICATION_JSON_VALUE)
public class ServiceRequestAttachmentsController  extends BaseController {
    @Autowired
    TServiceRequestAttachmentsService tServiceRequestAttachmentsService;
    /**
     * 根据服务id查询 获取附件列表
     */
    @RequestMapping(value = "/attachments/selectByServiceRequestId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("评论打分")
    public Result comment(@RequestParam Long serviceRequestId) {
        return success(tServiceRequestAttachmentsService.getTServiceRequestAttachmentsByServiceRequestId(serviceRequestId));
    }

}
