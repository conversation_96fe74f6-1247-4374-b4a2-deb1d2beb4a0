package com.fotile.devops.issueRemarks.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("issue评论列表新增参数")
@Data
public class IssueRemarksInsertDTO implements Serializable {

    @ApiModelProperty("issueid")
    private Long issueId;
    @ApiModelProperty("评论人id")
    private Long userId;
    @ApiModelProperty("开发工时")
    private Integer kaifaHours;

    @ApiModelProperty("评论内容")
    private String remark;

}
