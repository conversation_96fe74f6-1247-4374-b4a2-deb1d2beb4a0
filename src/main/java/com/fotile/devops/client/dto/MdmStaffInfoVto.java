package com.fotile.devops.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MdmStaffInfoVto implements Serializable {

    private Integer id;
    /**
     * 工号
     */
    private String jobNum;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 组织名称
     */
    private String departName;
    /**
     * 数据库中的组织名称
     */
    private String realDepartName;

    /**
     * 部门id
     */
    private String departCode;

    /**
     * 事业部名称
     */
    private String businessName;

    /**
     * 单元名称
     */
    private String unitName;
    /**
     * 一级组织名称
     */
    private String firstName;

    /**
     * 二级组织名称
     */
    private String secondName;

    /**
     * 四级组织名称
     */
    private String fourName;

    /**
     * 三级组织名称
     */
    private String threeName;


    /**
     * 员工当前状态
     */
    private String status;
    /**
     * 员工当前状态描述
     */
    private String statusDes;
    /**
     * 工号List
     */
    private List<String> jobNumList;
}

