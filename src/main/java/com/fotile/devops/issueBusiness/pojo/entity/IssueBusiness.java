package com.fotile.devops.issueBusiness.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;

/**
 * issue_business
 * <AUTHOR>
/**
* Created by Mybatis Generator on 2020/01/10
*/
@Entity
@ApiModel("issue和业务方id关联表")
@TableName(value="issue_business", schema="devops")
@Data
public class IssueBusiness extends AuditingEntity {
    /**
     * issueid
     */
    @ApiModelProperty("issueid")
    @TableField(value = "issue_id")
    private Long issueId;

    /**
     * 业务方id
     */
    @ApiModelProperty("业务方id")
    @TableField(value = "business_id")
    private Long businessId;

}