package com.fotile.devops.sprint.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

/**
 * sprint
 * <AUTHOR>
/**
* Created by Mybatis Generator on 2019/10/18
*/
@Entity
@ApiModel("迭代")
@TableName(value="sprint", schema="devops")
@Data
public class Sprint extends AuditingEntity {
    /**
     * 迭代名称
     */
    @ApiModelProperty("迭代名称")
    @TableField(value = "name")
    private String name;

    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @TableField(value = "version")
    private String version;

    /**
     * 负责人id
     */
    @ApiModelProperty("负责人id")
    @TableField(value = "owner_id")
    private Long ownerId;

    /**
     * 项目id
     */
    /*@ApiModelProperty("项目id")
    @TableField(value = "project_id")
    private Long projectId;*/

    /**
     * 状态 0新建 1进行中 2已上线
     */
    @ApiModelProperty("状态 0新建 1进行中 2已上线")
    @TableField(value = "status")
    private Integer status;

    @TableField(value = "start_date")
    @ApiModelProperty("到期时间")
    private Date startDate;

    @TableField(value = "end_date")
    @ApiModelProperty("上线")
    private Date endDate;

}