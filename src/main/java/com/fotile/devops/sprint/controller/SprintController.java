package com.fotile.devops.sprint.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.devops.sprint.pojo.dto.GetSprintDetialInDto;
import com.fotile.devops.sprint.pojo.dto.SprintInsertDTO;
import com.fotile.devops.sprint.pojo.dto.SprintSelectDTO;
import com.fotile.devops.sprint.pojo.dto.UpdateSprintDto;
import com.fotile.devops.sprint.pojo.entity.Sprint;
import com.fotile.devops.sprint.service.SprintService;
import com.fotile.framework.web.Result;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.core.common.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@Api(value = "版本迭代接口")
@RequestMapping("/api/sprint")
public class SprintController extends BaseController {
    @Autowired
    private SprintService sprintService;

    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("新增版本迭代")
    public Result<SprintInsertDTO> insert(@RequestBody @Valid SprintInsertDTO sprint) {
        if (log.isDebugEnabled()) log.debug("SprintController.insert()-" + JSON.toJSONString(sprint));
        sprintService.insertSelective(sprint);
        return success(sprint);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("修改版本迭代")
    public Result<Sprint> update(@RequestBody @Valid UpdateSprintDto dto) {
        if (log.isDebugEnabled()) log.debug("SprintController.update()-" + JSON.toJSONString(dto));
        sprintService.updateByPrimaryKeySelective(dto);
        return success("修改成功");
    }

    @RequestMapping(value = "/delete", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("删除版本迭代")
    public Result<Sprint> delete(@ApiParam("版本迭代id") Long id) {

       int i= sprintService.delete(id);
        return success("删除成功");
    }

    @RequestMapping(value = "/view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查看版本迭代")
    public Result<?> view(@ApiParam("版本迭代id") @RequestParam Long id) {
        if (log.isDebugEnabled()) log.debug("SprintController.view()-" + id);
        return success(sprintService.selectByPrimaryKey(id));
    }

    @RequestMapping(value = "/select", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询版本迭代")
    public Result<PageInfo> select(@RequestBody @Valid SprintSelectDTO sprintSelectDTO) {
        if (log.isDebugEnabled()) log.debug("SprintController.select()-" + JSON.toJSONString(sprintSelectDTO));
        return success(sprintService.selectBySprint(sprintSelectDTO));
    }

    /**
     * 根据项目权限过滤版本
     * @param sprintSelectDTO
     * @return
     */
    @RequestMapping(value = "/selectWithProjectAuth", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo> selectWithProjectAuth(@RequestBody @Valid SprintSelectDTO sprintSelectDTO) {
        if (log.isDebugEnabled()) log.debug("SprintController.select()-" + JSON.toJSONString(sprintSelectDTO));
        return success(sprintService.selectWithProjectAuth(sprintSelectDTO));
    }

    @RequestMapping(value = "/getSprintIssue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("获取版本详情")
    public Result<?> getSprintIssue( @Valid GetSprintDetialInDto inDto) {
        return success(sprintService.getSprintIssue(inDto.getSprintId()));
    }


    @RequestMapping(value = "/getCurrentSprint", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("获取当前的所有版本")
    public Result<?> getCurrentSprint(){
        return success(sprintService.getCurrentSprint());
    }


    @RequestMapping(value = "/getSprintIssueInModule", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("获取版本详情根据木块中心")
    public Result<?> getSprintIssueInModule( @Valid GetSprintDetialInDto inDto) {
        return success(sprintService.getSprintIssueInModule(inDto.getSprintId()));
    }

}