package com.fotile.devops.dingtalk.pojo.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Create By lubing on 2022/12/19.
 */
@Data
@Accessors(chain = true)
public class OAMessage {

    private List<KeyValContent> content= Lists.newLinkedList();

    private List<String> employeeCodes=Lists.newLinkedList(); //接收人工号

    private String source;

    private String title;

    private String url;

    private String pcMessageUrl;

    @Data
    public static class KeyValContent{
        String key;
        Object value;

        public KeyValContent(String key, Object value) {
            this.key = key;
            this.value = value;
        }
    }
}
