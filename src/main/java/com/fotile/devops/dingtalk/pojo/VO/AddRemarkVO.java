package com.fotile.devops.dingtalk.pojo.VO;

import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.issue.pojo.dto.UpdateIssueDto;
import com.fotile.devops.issue.pojo.entity.Issue;
import com.fotile.devops.issueRemarks.pojo.entity.IssueRemarks;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddRemarkVO  implements IssueEventVO {

    IssueRemarks issueRemarks;
    UserOutDto userOutDto;

    public AddRemarkVO(IssueRemarks issueRemarks, UserOutDto userOutDto) {
        this.issueRemarks = issueRemarks;
        this.userOutDto = userOutDto;
    }
}
