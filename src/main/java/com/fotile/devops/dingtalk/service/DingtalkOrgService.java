package com.fotile.devops.dingtalk.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.dingtalk.pojo.entity.DingtalkOrg;
import com.fotile.devops.dingtalk.dao.DingtalkOrgDAO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 钉钉机器人和org映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-24
 */
@Service
public class DingtalkOrgService extends ServiceImpl<DingtalkOrgDAO, DingtalkOrg> implements IService<DingtalkOrg> {
    @Autowired
    private DingtalkOrgDAO dingtalkOrgDAO;


    public List<Integer> selectOrgIdByDingId(Long id){
       List<Integer> orgCodes= dingtalkOrgDAO.selectOrgCodeByDingId(id);
       return orgCodes;
    }


}
