package com.fotile.devops.issue.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fotile.devops.client.SystemClientService;
import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.devopsUsers.service.DevopsUsersService;
import com.fotile.devops.devopsUsers.service.UsersProjectService;
import com.fotile.devops.dict.pojo.entity.Dict;
import com.fotile.devops.dict.service.DictService;
import com.fotile.devops.enums.BugLevel;
import com.fotile.devops.enums.IssuePriorityEnum;
import com.fotile.devops.enums.IssueStatus;
import com.fotile.devops.enums.IssueType;
import com.fotile.devops.issue.dao.IssueDAO;
import com.fotile.devops.issue.pojo.dto.IssueOutDTO;
import com.fotile.devops.issue.pojo.dto.IssueSelectDTO;
import com.fotile.devops.issue.pojo.dto.directTrain.AppealIssueMappingDTO;
import com.fotile.devops.issue.pojo.dto.excel.IssueExcelVO;
import com.fotile.devops.issue.pojo.entity.IssueUserMapping;
import com.fotile.devops.issueAssociation.pojo.entity.IssueAssociation;
import com.fotile.devops.issueAssociation.service.IssueAssociationService;
import com.fotile.devops.issueAttachments.pojo.entity.IssueAttachments;
import com.fotile.devops.issueAttachments.service.IssueAttachmentsService;
import com.fotile.devops.issueAttentionUsers.service.IssueAttentionUsersService;
import com.fotile.devops.issueBusiness.service.IssueBusinessService;
import com.fotile.devops.issueKeywords.service.IssueKeywordsService;
import com.fotile.devops.module.service.ModuleService;
import com.fotile.devops.utils.ExcelUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.Result;
import java.util.function.Function;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation("third")
public class ExcelService {
    @Autowired
    private IssueDAO issueDAO;
    @Autowired
    protected ModuleService moduleService;
    @Autowired
    private IssueKeywordsService issueKeywordsService;
    @Autowired
    private IssueAssociationService issueAssociationService;
    @Autowired
    private IssueBusinessService issueBusinessService;
    @Autowired
    private IssueService issueService;
    @Autowired
    private DevopsUsersService devopsUsersService;
    @Autowired
    SystemClientService systemClientService;
    @Autowired
    IssueAttentionUsersService issueAttentionUsersService;
    @Resource
    private IssueUserMappingDomainService issueUserMappingService;
    @Resource
    private DictService dictService;
    @Resource(name = "issueAttachmentsService")
    @Lazy
    private IssueAttachmentsService issueAttachmentsService;
    @Resource
    private UsersProjectService usersProjectService;
    @Resource
    @Lazy
    private DirectTrainAppealService directTrainAppealService;

    public Result exportIssue(@Valid IssueSelectDTO issueSelectDTO) {
        issueSelectDTO = issueService.modifyProperties(issueSelectDTO);
        log.info("*******IssueSelectDTO多参数查询" + issueSelectDTO.toString());
        //  IssueSelectDTO dto = Utils.setNullValue(issueSelectDTO);
        /**
         * 1.先把根据标签id和版本id把issueId查询出来放在ids列表里面
         */

        List<Long> ids1 = new ArrayList<>();
        List<Long> ids2 = new ArrayList<>();
        List<Long> ids3 = new ArrayList<>();

        if (!ObjectUtils.isEmpty(issueSelectDTO.getKeywordId())) {
            ids1 = issueKeywordsService.findIssueIdsByKeywords(issueSelectDTO.getKeywordId());
        }

        if (!ObjectUtils.isEmpty(issueSelectDTO.getKeywordIds())) {
            ids3 = issueKeywordsService.findIssueIdsByKeywordIds(issueSelectDTO.getKeywordIds());
        }
        //查询当前关注人关注的issue
        if (issueSelectDTO.isAttention()) {

            ids2 = issueAttentionUsersService.selectIssueIdsByUser(devopsUsersService.findUserId());
        }
        ids1.addAll(ids2);
        ids1.addAll(ids3);

        /**
         * 根据业务方查询issue
         */
        List<Long> ids4 = new ArrayList<>();
        if (!ObjectUtils.isEmpty(issueSelectDTO.getBusinessIds())) {
            ids4 = issueBusinessService.findIssueIdsByBusinesids(issueSelectDTO.getBusinessIds());
        }
        ids1.addAll(ids4);
        /**
         * 2.再根据关联issue'id查到id列表加进去
         */
        List<Long> ids5 = new ArrayList<>();
        if (!ObjectUtils.isEmpty(issueSelectDTO.getAssociationId())) {
            ids5 = issueAssociationService.findIssueIdsByassociationId(issueSelectDTO.getAssociationId());
        }
        ids1.addAll(ids5);
        if (!ObjectUtils.isEmpty(ids1)) {
            ids1.removeAll(Collections.singleton(null));
            List<Long> ids = ids1.stream().distinct().collect(Collectors.toList());
            issueSelectDTO.setIds(ids);
        }

        issueSelectDTO.setAuthProjectIds(usersProjectService.selectProjectsByUserIdWithoutThrow());
        PageInfo page = new PageInfo(issueSelectDTO.getPageNum(), issueSelectDTO.getPageSize());

        Long count = issueDAO.findByIssueCount(issueSelectDTO);
        List<IssueExcelVO> vos = new ArrayList<>();
        if (count > 0) {
            vos = issueDAO.findExportIssue(page, issueSelectDTO);
            if(!vos.isEmpty()){
                Map<Integer, String> tenantMap = dictService.getByTypeCode("tenant").stream().collect(Collectors.toMap(d->Integer.valueOf(d.getValueCode()), Dict::getValueName));
                List<Long> issueIds = vos.stream().map(IssueExcelVO::getId).collect(Collectors.toList());
                Map<String, String> stageMap = dictService.getByTypeCode("bug_found_stage").stream().collect(Collectors.toMap(Dict::getValueCode, Dict::getValueName));
                Map<Long, String> urlMapping = issueAttachmentsService.lambdaQuery().in(IssueAttachments::getIssueId, issueIds)
                        .eq(IssueAttachments::getIsDeleted, 0)
                        .list().stream().collect(Collectors.groupingBy(IssueAttachments::getIssueId, Collectors.mapping(IssueAttachments::getUrl, Collectors.joining(","))));
                Map<Long, String> appealMap = directTrainAppealService.listByIssueIds(issueIds).stream()
                        .collect(Collectors.groupingBy(AppealIssueMappingDTO::getIssueId, Collectors.mapping(AppealIssueMappingDTO::getAppealCode, Collectors.joining(","))));
                Map<Long, String> otherIssueMap = issueAssociationService.lambdaQuery()
                        .eq(IssueAssociation::getIsDeleted, 0)
                        .in(IssueAssociation::getIssueId, issueIds)
                        .list().stream()
                        .collect(Collectors.groupingBy(IssueAssociation::getIssueId, Collectors.mapping(oth -> oth.getOtherIssueId().toString(), Collectors.joining(","))));

                vos.stream().forEach(vo -> {
                    vo.setStatus(IssueStatus.getValue(vo.getStatus()));
                    vo.setType(IssueType.getValue(vo.getType()));
                    vo.setBugLevel(BugLevel.getValue(vo.getBugLevel()));
                    vo.setPriorityName(IssuePriorityEnum.getValue(vo.getPriority()));
                    vo.setTenantName(tenantMap.get(vo.getTenantId()));
                    vo.setAttachments(urlMapping.get(vo.getId()));
                    vo.setAppealCodes(appealMap.get(vo.getId()));
                    vo.setAssociationIssueIds(otherIssueMap.get(vo.getId()));
                    vo.setBugFoundStageName(stageMap.get(vo.getBugFoundStage()));
                });
                setUsers(vos);
            }
        }

        setBugCouseName(vos);
        //生成文件名称
        String userAccount = devopsUsersService.findUser().getAccount();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String timeStr2 = sf.format(new Date());
        String fileName = "issueExport_" + userAccount + timeStr2 + ".xlsx";
        String path = System.getProperty("user.dir") + File.separator + fileName;
        //FileUtils.createTmpFile(path);

      //  ByteArrayOutputStream ops=new ByteArrayOutputStream();
        ExcelUtils.resetCellMaxTextLength();

      //  ExcelWriter excelWriter = EasyExcel.write(ops, IssueExcelVO.class).build();
        ExcelWriter excelWriter = EasyExcel.write(path, IssueExcelVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("issue导出数据").build();

        excelWriter.write(vos, writeSheet);
        excelWriter.finish();
        log.info("生成excel临时文件: " + path);

        /**
         * 文件上传
         * 1.转为MultipartFile文件
         * 2.调用system上传文件服务上传到阿里云,接受返回的下载路径
         * 3.补充下载路径返回完整的可用的下载路径
         */

        File excelfile = new File(path);
        MultipartFile mfile = new CommonsMultipartFile(createFileItem(excelfile));

        ArrayList<String> urls = systemClientService.fileUpload(mfile);
        FileUtils.delete(excelfile);
        return Result.buildSuccess("导出成功", urls.get(0));
    }


    public FileItem createFileItem(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "exportFile";
        FileItem item = factory.createItem(textFieldName, "multipart/form-data", false, file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            log.error("上传excel文件过程失败", e);
        }
        return item;
    }

    private List<IssueExcelVO> setUsers(List<IssueExcelVO> list){
        if(CollectionUtils.isEmpty(list)){
            return list;
        }
        List<Long> ids = list.stream().map(IssueExcelVO::getId).collect(Collectors.toList());
        List<IssueUserMapping> mappings = issueUserMappingService.getByIssueIds(ids);
       /* if(CollectionUtils.isEmpty(mappings)){
            return list;
        }*/

        List<Long> respondPeopleIdList = list.stream().map(IssueExcelVO::getRespondPeopleId).filter(StringUtils::isNotBlank)
                .filter(StrUtil::isNumeric)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        List<Long> userIds = mappings.stream().map(IssueUserMapping::getUserId).distinct()
            .collect(Collectors.toList());
        Optional.of(respondPeopleIdList)
                .filter(CollectionUtils::isNotEmpty).ifPresent(userIds::addAll);
        List<UserOutDto> users = devopsUsersService.findUserByIds(userIds);
        if(CollectionUtils.isEmpty(users)){
            return list;
        }
        Map<Long, List<IssueUserMapping>> issueUserMap = mappings.stream()
            .collect(Collectors.groupingBy(IssueUserMapping::getIssueId));
        Map<Long, UserOutDto> userMap = users.stream()
            .collect(Collectors.toMap(UserOutDto::getId, Function.identity()));

        for (IssueExcelVO issueOutDTO : list) {
            List<IssueUserMapping> issueUserMappings = issueUserMap.getOrDefault(issueOutDTO.getId(),Lists.newArrayListWithExpectedSize(0));
          /*  if(CollectionUtils.isEmpty(issueUserMappings)){
                continue;
            }*/
            Map<Integer, List<IssueUserMapping>> typeMap = issueUserMappings.stream()
                .collect(Collectors.groupingBy(IssueUserMapping::getType));
            if(typeMap.containsKey(1)){
                issueOutDTO.setKaifaName(getNames(typeMap,userMap,1));
            }
            if(typeMap.containsKey(2)){
                issueOutDTO.setCeshiName(getNames(typeMap,userMap,2));
            }
            if(typeMap.containsKey(3)){
                issueOutDTO.setOwnerName(getNames(typeMap,userMap,3));
            }
            if(typeMap.containsKey(4)){
                String respondPeopleIds = typeMap.getOrDefault(4, Lists.newArrayListWithExpectedSize(0))
                        .stream()
                        .map(IssueUserMapping::getUserId)
                        .distinct()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                Optional.ofNullable(respondPeopleIds)
                                .filter(StringUtils::isNotBlank).ifPresent(issueOutDTO::setRespondPeopleId);
                issueOutDTO.setRespondPeopleName(getNames(typeMap,userMap,4));
            }
            if(StringUtils.isBlank(issueOutDTO.getRespondPeopleName())){
                if(StringUtils.isNotBlank(issueOutDTO.getRespondPeopleId())&&StrUtil.isNumeric(issueOutDTO.getRespondPeopleId())){
                    Optional.ofNullable(userMap.get(Long.valueOf(issueOutDTO.getRespondPeopleId())))
                                    .map(UserOutDto::getName)
                                    .ifPresent(issueOutDTO::setRespondPeopleName);
                }
            }
        }
        return list;
    }

    private String getNames(Map<Integer, List<IssueUserMapping>> typeMap,Map<Long, UserOutDto> userMap,Integer type){
        String names =null;
        if(typeMap.containsKey(type)){
            names=  typeMap.get(type).stream().map(IssueUserMapping::getUserId)
                .map(userMap::get)
                .filter(Objects::nonNull)
                .map(UserOutDto::getName)
                .collect(Collectors.joining(","));
        }
        return names;
    }

    private void setBugCouseName(List<IssueExcelVO> vos){
        if(vos!=null && !vos.isEmpty()){
            Map<String, String> causeMap = dictService.getByTypeCode("bug_cause").stream().collect(Collectors.toMap(Dict::getValueCode, Dict::getValueName));
            for (IssueExcelVO vo : vos) {
                if(IssueType.Type30.getValue().equals(vo.getType()) && StringUtils.isNotBlank(vo.getBugCause())){
                    String couseName = Stream.of(vo.getBugCause().split(",")).map(value -> causeMap.get(value)).filter(name -> StringUtils.isNotBlank(name)).collect(Collectors.joining("/"));
                    vo.setBugCauseName(couseName);
                }
            }

        }
    }

    private void changeMinute2Hours(Long minute){
        String hours=null;
        if(minute!=null){

        }
    }


}
