package com.fotile.devops.issue.service.mq;

import com.fotile.devops.config.MybatisPlusConfig;
import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.devopsUsers.service.DevopsUsersService;
import com.fotile.devops.dingtalk.pojo.dto.DingMailCarrier;
import com.fotile.devops.dingtalk.pojo.dto.OAMessage;
import com.fotile.devops.enums.IssueStatus;
import com.fotile.devops.enums.IssueType;
import com.fotile.devops.issue.mq.IssueStatusUpateChannel;
import com.fotile.devops.issue.pojo.dto.IssueDetailOutDTO;
import com.fotile.devops.issue.pojo.entity.Issue;
import com.fotile.devops.issue.service.IssueService;
import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Create By lubing on 2022/12/19.
 */
@Slf4j
@Service
public class IssueDingConsumer {

    @Resource
    private DevopsUsersService devopsUsersService;
    @Resource
    private DingMailCarrier dingMailCarrier;
    @Resource
    private IssueService issueService;

    @Value("${dingding.issueDetailUrl}")
    private String issueDetailUrl;
    @Value("${dingding.centerUrl}")
    private String centerUrl;


    @StreamListener(IssueStatusUpateChannel.ISSUE_DING_MSG_INPUT)
    public void  consumeIssueDingMsg(Message<Issue> msg) throws Exception {
        Issue payload = msg.getPayload();
        if(payload!=null){
            MybatisPlusConfig.setTenantId(-1);
            if(IssueType.Type10.getFiled().equals(payload.getType())){
                if(payload.getOwnerId()!=null){
                    UserOutDto user = devopsUsersService.findUser(payload.getOwnerId());
                    if(user!=null && StringUtils.isNotBlank(user.getNumber())){
                        OAMessage oaMessage = getOAMessage(payload.getId(), "issue经办人发生变更", user.getNumber());
                        String s = dingMailCarrier.sendOAMessage(oaMessage);
                        if(!"0".equals(s)){
                            Thread.sleep(3000); //如果发送失败，很可能是钉钉推送条数达到上限了，缓一下再从新发。
                            log.error("发送失败，钉钉返回状态码:"+s);
                            throw new BusinessException("发送失败");
                        }
                    }
                }
            }else if(IssueType.Type20.getFiled().equals(payload.getType())){
                if(payload.getKaifaId()!=null){
                    UserOutDto user = devopsUsersService.findUser(payload.getKaifaId());
                    if(user!=null && StringUtils.isNotBlank(user.getNumber())){
                        OAMessage oaMessage = getOAMessage(payload.getId(), "task分配开发", user.getNumber());
                        String s = dingMailCarrier.sendOAMessage(oaMessage);
                        if(!"0".equals(s)){
                            Thread.sleep(3000);
                            log.error("发送失败，钉钉返回状态码:"+s);
                            throw new BusinessException("发送失败");
                        }
                    }
                }else if(payload.getCeshiId()!= null){
                    UserOutDto user = devopsUsersService.findUser(payload.getCeshiId());
                    if(user!=null && StringUtils.isNotBlank(user.getNumber())){
                        OAMessage oaMessage = getOAMessage(payload.getId(), "task分配测试", user.getNumber());
                        String s = dingMailCarrier.sendOAMessage(oaMessage);
                        if(!"0".equals(s)){
                            Thread.sleep(3000);
                            log.error("发送失败，钉钉返回状态码:"+s);
                            throw new BusinessException("发送失败");
                        }
                    }
                }
            }
        }
    }

    private OAMessage getOAMessage(Long id,String titlePrefix,String number){
        IssueDetailOutDTO outDTO = issueService.selectByPrimaryKey(id);

        String title=titlePrefix+"\n";
        if(StringUtils.isNotBlank(outDTO.getProjectName())){
            title+="【"+outDTO.getProjectName()+"】";
        }
        title+=id+"-"+outDTO.getTitle();

        OAMessage oaMessage = new OAMessage()
                .setTitle(title);
        oaMessage.getEmployeeCodes().add(number);
        if(IssueType.Type10.getFiled().equals(outDTO.getType()) ){
            oaMessage.setUrl(issueDetailUrl + id)
                    .setPcMessageUrl(oaMessage.getUrl());
        }else if(IssueType.Type20.getFiled().equals(outDTO.getType())){
            oaMessage.setUrl(centerUrl+id)
                    .setPcMessageUrl(oaMessage.getUrl());
        }
        oaMessage.getContent().add(new OAMessage.KeyValContent("创建人:", outDTO.getCreatedName()));
        oaMessage.getContent().add(new OAMessage.KeyValContent("经办人:", outDTO.getOwnerName()));
        oaMessage.getContent().add(new OAMessage.KeyValContent("开发:", outDTO.getKaifaName()));
        oaMessage.getContent().add(new OAMessage.KeyValContent("测试:", outDTO.getCeshiName()));
        oaMessage.getContent().add(new OAMessage.KeyValContent("状态:", IssueStatus.getValue(outDTO.getStatus())));
        return oaMessage;
    }
}
