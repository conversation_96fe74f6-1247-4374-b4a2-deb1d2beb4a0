package com.fotile.devops.issue.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.fotile.devops.config.MybatisPlusConfig;
import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.devopsUsers.pojo.entity.DevopsUsers;
import com.fotile.devops.devopsUsers.service.DevopsUsersService;
import com.fotile.devops.enums.IssueStatus;
import com.fotile.devops.enums.IssueType;
import com.fotile.devops.issue.dao.DirectTrainLogDao;
import com.fotile.devops.issue.dao.IssueDAO;
import com.fotile.devops.issue.pojo.dto.directTrain.Tenant1Bug;
import com.fotile.devops.issue.pojo.dto.directTrain.Tenant1Story;
import com.fotile.devops.issue.pojo.dto.directTrain.Tenant1Task;
import com.fotile.devops.issue.pojo.entity.DirectTrainAppeal;
import com.fotile.devops.issue.pojo.entity.DirectTrainIssueMapping;
import com.fotile.devops.issue.pojo.entity.DirectTrainLog;
import com.fotile.devops.issue.pojo.entity.Issue;
import com.fotile.devops.issue.pojo.entity.IssueUserMapping;
import com.fotile.devops.issueAssociation.pojo.entity.IssueAssociation;
import com.fotile.devops.issueAssociation.service.IssueAssociationService;
import com.fotile.devops.issueLogsLine.pojo.entity.IssueLogsLine;
import com.fotile.devops.issueLogsLine.service.IssueLogsLineService;
import com.fotile.framework.web.BusinessException;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 同步信息技术部报表需要的数据
 *
 * <AUTHOR>
 * @data 2024/4/15 15:36
 */
@Slf4j
@Service
public class Tenant1SyncService {

  private static final String tenant1BugLastTime = "devopscenter-tenant1_bug_last_time";
  private static final String tenant1TaskLastTime = "devopscenter-tenant1_task_last_time";
  private static final Set<Integer> STATUS_SET = ImmutableSet.of(IssueStatus.Status40.getFiled(),
      IssueStatus.Status45.getFiled(),
      IssueStatus.Status60.getFiled(), IssueStatus.Status50.getFiled(),
      IssueStatus.Status90.getFiled(), IssueStatus.Status80.getFiled());
  @Resource
  private IssueLogsLineService issueLogsLineService;
  @Resource
  private IssueDAO issueDAO;
  @Resource
  @Lazy
  private IssueService issueService;
  @Resource
  private DevopsUsersService devopsUsersService;
  @Resource
  private DirectTrainIssueService directTrainIssueService;
  @Resource
  private DirectTrainAppealService directTrainAppealService;
  @Resource
  private DirectTrainLogService directTrainLogService;
  @Resource
  @Lazy
  private IssueAssociationService issueAssociationService;
  @Resource
  private DirectTrainLogDao directTrainLogDao;
  @Resource
  private IssueUserMappingDomainService issueUserMappingDomainService;
  @Resource
  private StringRedisTemplate stringRedisTemplate;
  @Value("${directTrain.upsertTask}")
  private String upsertTask;
  @Value("${directTrain.upsertBugs}")
  private String upsertBugs;
  @Value("${directTrain.upsertIssue}")
  private String upsertIssue;

  /**
   * issue变动统一调用的方法
   *
   * @param newIssue
   * @param oldIssue
   */
  public void issueChange(Issue newIssue, Issue oldIssue) {
    if (newIssue == null || !Integer.valueOf(1).equals(newIssue.getTenantId())) {
      return;
    }
    if (IssueType.Type10.getFiled().equals(newIssue.getType())) {
      storyChange(newIssue, oldIssue);
      storyCompleted(newIssue);
    }else if(IssueType.Type20.getFiled().equals(newIssue.getType())){
      handleTaskChange(newIssue,oldIssue);
    }else if(StringUtils.equals(IssueType.Type40.getFiled(),newIssue.getType())){
      handleRelease(newIssue,oldIssue);
    }
  }

  private void handleRelease(Issue newIssue, Issue oldIssue){
    if(Objects.isNull(oldIssue)){
      if(Objects.equals(newIssue.getStatus(),IssueStatus.Status90.getFiled())){
        handleReleaseStory(newIssue);
      }
      return;
    }
    if(Objects.equals(newIssue.getStatus(),IssueStatus.Status90.getFiled())){
      if(!Objects.equals(newIssue.getStatus(),oldIssue.getStatus())){
        handleReleaseStory(newIssue);
      }
    }
  }

  private void handleReleaseStory(Issue newIssue){
    List<Long> associationIds = issueAssociationService.findIssueIdsByassociationId(newIssue.getId());
    List<Issue> storyList = issueService.lambdaQuery()
            .in(Issue::getId, associationIds)
            .eq(Issue::getIsDeleted, 0)
            .eq(Issue::getType, IssueType.Type10.getFiled())
            .list();
    for (Issue issue : storyList) {
      storyChange(issue,null);
    }
  }

  private void handleTaskChange(Issue newIssue, Issue oldIssue){
    if(Objects.nonNull(newIssue) && Objects.isNull(oldIssue)){
      handleTaskCreated(newIssue);
      return;
    }

    if(Objects.equals(IssueStatus.Status60.getFiled(),newIssue.getStatus()) && !Objects.equals(IssueStatus.Status60.getFiled(),oldIssue.getStatus())){
      handleTaskClose(newIssue);
    }
  }

  private void handleTaskClose(Issue newIssue){
    Tenant1Task tenant1Task = convert2Tenant1Task(newIssue);
    if(Objects.isNull(tenant1Task)){
      return;
    }
    sendSyncTaskCall(Lists.newArrayList(tenant1Task), LocalDateTime.now());
  }

  private void handleTaskCreated(Issue newIssue){
    Tenant1Task tenant1Task = convert2Tenant1Task(newIssue);
    if(Objects.isNull(tenant1Task)){
      return;
    }
    sendSyncTaskCall(Lists.newArrayList(tenant1Task), LocalDateTime.now());
  }

  private Tenant1Task convert2Tenant1Task(Issue newIssue) {
    if(Objects.isNull(newIssue)){
      return null;
    }
    Tenant1Task tenant1Task = new Tenant1Task()
        .setCloseTime(
            Optional.ofNullable(newIssue.getDate60()).map(DateUtil::formatDateTime).orElse(null))
        .setCreateTime(Optional.ofNullable(newIssue.getCreatedDate()).map(DateUtil::formatDateTime)
            .orElse(null))
        .setDevHours(Optional.ofNullable(newIssue.getEstimateKaifaHours()).map(Long::doubleValue)
            .orElse(null))
        .setIssueId(newIssue.getParentId())
        .setPromiseFinishTime(
            Optional.ofNullable(newIssue.getPromiseFinishTime()).map(DateUtil::formatDateTime)
                .orElse(null))
        .setRealHours(
            Optional.ofNullable(newIssue.getKaifaHours()).map(Long::doubleValue).orElse(null))
        .setTaskId(newIssue.getId())
        .setTaskState(Optional.ofNullable(newIssue.getStatus()).map(String::valueOf).orElse(null));

    if (Objects.nonNull(newIssue.getKaifaId())) {
      Optional.ofNullable(newIssue.getKaifaId())
          .map(devopsUsersService::findUser)
          .map(UserOutDto::getNumber)
          .filter(StringUtils::isNotBlank)
          .ifPresent(tenant1Task::setDevEngineer);
    }
    return tenant1Task;
  }



  /**
   * issue上线触发的操作
   *
   * @param newIssue
   */
  public void storyCompleted(Issue newIssue) {
    if (newIssue != null &&(IssueStatus.Status90.getFiled().equals(newIssue.getStatus())
            ||IssueStatus.Status80.getFiled().equals(newIssue.getStatus())) ) {
      List<Long> otherIssueIds = issueAssociationService.lambdaQuery()
          .eq(IssueAssociation::getIssueId, newIssue.getId())
          .eq(IssueAssociation::getIsDeleted, 0)
          .select(IssueAssociation::getOtherIssueId)
          .list().stream().map(IssueAssociation::getOtherIssueId)
          .collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(otherIssueIds)) {
        List<Issue> tasks = issueService.lambdaQuery()
            .in(Issue::getId, otherIssueIds)
            .eq(Issue::getIsDeleted, 0)
            .eq(Issue::getType, IssueType.Type20.getFiled())
            .list();

        issueService.lambdaUpdate()
            .in(Issue::getId, otherIssueIds)
            .eq(Issue::getIsDeleted, 0)
            .eq(Issue::getType, IssueType.Type20.getFiled())
            .ne(Issue::getStatus, IssueStatus.Status60.getFiled())
            .set(Issue::getStatus, IssueStatus.Status60.getFiled())
            .set(Issue::getDate60, new Date())
            .update();

        //story上线，关联的task关闭，同步至直通车

          if(CollectionUtils.isNotEmpty(tasks)){
            for (Issue task : tasks) {
              if(Objects.equals(task.getStatus(),IssueStatus.Status60.getFiled())){
                continue;
              }
              handleTaskClose(issueService.getById(task.getId()));
            }
          }
      }
    }
  }

  /**
   * 处理story类型的变更
   *
   * @param newIssue
   * @param oldIssue
   */
  private void storyChange(Issue newIssue, Issue oldIssue) {
    //判断此状态是否是第一次出现，从日志判断
    if (isStoryNeedSync(newIssue, oldIssue)) {
      //  1、将newIssue转为同步需要的类型
      Tenant1Story tenant1Story = convert2Tenant1Story(newIssue);
      // 2、 同步数据
      DirectTrainLog trainLog = DirectTrainLog.builder()
          .createdTime(LocalDateTime.now())
          .requestParams(JSONObject.toJSONString(tenant1Story))
          .requestUrl(upsertIssue)
          .issueId(newIssue.getId())
          .build();
      try {
        ResponseEntity<JSONObject> responseEntity = directTrainIssueService.postWithRetry(
            upsertIssue, tenant1Story);
        trainLog.setResponseResult(responseEntity.getBody().toJSONString());
        trainLog.setResponseCode(responseEntity.getBody().getString("status_code"));
        directTrainLogService.saveWithTryCatch(trainLog);
        if (!"200".equals(responseEntity.getBody().getString("status_code"))) {
          throw new BusinessException(
              "同步story至直通车失败，请稍后重试，直通车返回信息：" + JSONObject.toJSONString(
                  responseEntity.getBody()));
        }
      } catch (Exception e) {
        trainLog.setResponseResult(e.getMessage());
        trainLog.setResponseCode("400");
        directTrainLogService.saveWithTryCatch(trainLog);
        throw new BusinessException("同步story至直通车失败，请稍后重试，原因：" + e.getMessage());
      }
    }
  }

  /**
   * 将issue转为Tenant1Story类型
   *
   * @param newIssue
   * @return
   */
  private Tenant1Story convert2Tenant1Story(Issue newIssue) {
    Tenant1Story tenant1Story = new Tenant1Story();
    setUsers(newIssue, tenant1Story);

    tenant1Story.setIssueState(IssueStatus.getValue(newIssue.getStatus()));
    if(Objects.equals(IssueStatus.Status80.getFiled(),newIssue.getStatus())){
      tenant1Story.setIssueState(IssueStatus.Status90.getValue());
    }
    //填充诉求编号
    List<DirectTrainIssueMapping> appealList = directTrainIssueService.lambdaQuery()
        .eq(DirectTrainIssueMapping::getIssueId, newIssue.getId())
        .eq(DirectTrainIssueMapping::getIsDeleted, 0).list();
    if (CollectionUtils.isNotEmpty(appealList)) {
      tenant1Story.setIssueNum(appealList.get(0).getAppealid());
    }
    if (newIssue.getEstimateKaifaHours() != null) {
      tenant1Story.setDevHours(
          NumberUtil.div(newIssue.getEstimateKaifaHours().doubleValue(), 60d, 2));
    }
    if (newIssue.getEstimateCeshiHours() != null) {
      tenant1Story.setTestHours(NumberUtil.div(newIssue.getEstimateCeshiHours().intValue(), 60, 2));
    }
    if (newIssue.getWorkingHours() != null) {
      tenant1Story.setTotalHours(NumberUtil.div(newIssue.getWorkingHours().intValue(), 60, 2));
    }
    return tenant1Story;
  }

  private Tenant1Story setUsers(Issue newIssue, Tenant1Story tenant1Story) {
    if (Objects.isNull(tenant1Story) || Objects.isNull(newIssue)) {
      return tenant1Story;
    }

    HashSet<Long> devopsUserIds = Sets.newHashSet(newIssue.getCeshiId(), newIssue.getKaifaId(),
        newIssue.getOwnerId());
    List<IssueUserMapping> userMappingList = issueUserMappingDomainService.getByIssueId(
        newIssue.getId());
    if (CollectionUtils.isNotEmpty(userMappingList)) {
      List<Long> mapUserIds = userMappingList.stream().map(IssueUserMapping::getUserId)
          .distinct()
          .collect(Collectors.toList());
      devopsUserIds.addAll(mapUserIds);//将mapping表的userid加入
    }

    //区分开发、测试和经办人
    Map<Integer, List<Long>> userMap = userMappingList.stream()
        .collect(Collectors.groupingBy(IssueUserMapping::getType,
            Collectors.mapping(IssueUserMapping::getUserId, Collectors.toList())));

    Map<Long, DevopsUsers> devopsUsersMap = devopsUsersService.lambdaQuery()
        .eq(DevopsUsers::getIsDeleted, 0).in(DevopsUsers::getId, devopsUserIds)
        .list().stream().collect(Collectors.toMap(DevopsUsers::getId, Function.identity()));
    tenant1Story.setIssueId(newIssue.getId());

    tenant1Story.setDevEngineer(
        getUserNumber(devopsUsersMap, userMap, newIssue.getKaifaId(), 1));
    tenant1Story.setTestEngineer(
        getUserNumber(devopsUsersMap, userMap, newIssue.getCeshiId(), 2));
    tenant1Story.setOperator(getUserNumber(devopsUsersMap, userMap, newIssue.getOwnerId(), 3));

    List<DevopsUsers> createdUsers = devopsUsersService.lambdaQuery()
        .eq(DevopsUsers::getIsDeleted, 0)
        .eq(DevopsUsers::getKeycloakId, newIssue.getCreatedBy()).list();
    if (CollectionUtils.isNotEmpty(createdUsers)) {
      DevopsUsers createdUser = createdUsers.get(0);
      tenant1Story.setCreator(createdUser.getNumber());
    }
    return tenant1Story;
  }


  /**
   * @param devopsUsersMap userId和用户映射
   * @param userMap        key:类型，是开发还是测试，经办人  value：具体人员的id
   * @param userId         原来表字段的userId，冗余使用，防止历史数据遗漏
   * @param type
   * @return
   */
  private String getUserNumber(Map<Long, DevopsUsers> devopsUsersMap,
      Map<Integer, List<Long>> userMap, Long userId, int type) {
    HashSet<String> developNumbers = Sets.newHashSet();

    DevopsUsers devopsUser = devopsUsersMap.get(userId);
    Optional.ofNullable(devopsUser)
        .map(DevopsUsers::getNumber)
        .filter(StringUtils::isNotBlank)
        .ifPresent(developNumbers::add);

    Optional.ofNullable(userMap.get(type))
        .filter(CollectionUtils::isNotEmpty)
        .orElse(Lists.newArrayListWithExpectedSize(0))
        .stream()
        .map(devopsUsersMap::get)
        .filter(Objects::nonNull)
        .map(DevopsUsers::getNumber)
        .filter(StringUtils::isNotBlank)
        .forEach(developNumbers::add);
    return Optional.of(developNumbers)
        .map(n -> StrUtil.join(",", developNumbers))
        .orElse(null);
  }

  public static void main(String[] args) {
    HashMap<Integer, List<Integer>> map = Maps.newHashMap();
    map.put(1, Lists.newArrayList(1, 2, 3));
    HashMap<Integer, String> map2 = Maps.newHashMap();
    map2.put(Integer.valueOf(1), "str1");
    map2.put(2, "str2");
    map2.put(3,"str3");
    Optional.ofNullable(map.get(1))
        .filter(CollectionUtils::isNotEmpty)
        .get()
        .stream()
        .peek(s->System.out.println("遍历map的元素"+s+"s.class"+s.getClass().getName()))
        .peek(i->System.out.println("我打印map2::get的key"+i))
        .peek(i->System.out.println("我打印map2::containsKey"+map2.containsKey(i)))
        .map(map2::get)
        .peek(s->System.out.println("遍历map2的元素"+s))
        .filter(StringUtils::isNotBlank)
        .forEach(System.out::println);
  }


  /**
   * 判断issue变更是否需要同步到直通车
   *
   * @param newIssue
   * @param oldIssue
   * @return
   */
  private boolean isStoryNeedSync(Issue newIssue, Issue oldIssue) {
    boolean result = false;
    if (newIssue != null && oldIssue == null) {
      return true;
    }
    if (newIssue.getEstimateKaifaHours() != null && oldIssue.getEstimateKaifaHours() == null) {
      return true;
    }
    if (newIssue.getEstimateCeshiHours() != null && oldIssue.getEstimateCeshiHours() == null) {
      return true;
    }
    if (newIssue.getWorkingHours() != null && oldIssue.getWorkingHours() == null) {
      return true;
    }
    if (newIssue.getOwnerId() != null && oldIssue.getOwnerId() == null) {
      return true;
    }
    if (newIssue.getKaifaId() != null && oldIssue.getKaifaId() == null) {
      return true;
    }
    if (newIssue.getCeshiId() != null && oldIssue.getCeshiId() == null) {
      return true;
    }
    List<IssueLogsLine> issueLogsLines = issueLogsLineService.selectByIssueId(newIssue.getId());
    Set<String> changedStateSet = issueLogsLines.stream().filter(l -> "状态".equals(l.getDomain()))
        .map(IssueLogsLine::getNewvalue).collect(Collectors.toSet());
    if (changedStateSet.contains(IssueStatus.Status90.getValue()) || changedStateSet.contains(
        IssueStatus.Status80.getValue())) {
      return result;
    }
    if (STATUS_SET.contains(newIssue.getStatus()) && !changedStateSet.contains(
        IssueStatus.getValue(newIssue.getStatus()))) {
      result = true;
    }
    return result;
  }


  @XxlJob(value = "sync2DirectTrain")
  public ReturnT<String> sync2DirectTrain(String s) {
    //1.同步task
   // syncTask2DirectTrain();
    //2.同步bug
    syncBug2DirectTrain();
    return ReturnT.SUCCESS;
  }

  public void compensateFailSync() {
    MybatisPlusConfig.setTenantId(-1);
    List<Tenant1Task> errorSyncTasks = selectErrorSyncTasks();
    sendSyncTaskCall(errorSyncTasks, null);
    List<Tenant1Bug> errorSyncBugs = selectErrorSyncBugs();
    sendSyncBugCall(errorSyncBugs, null);
  }


  private List<Tenant1Task> selectErrorSyncTasks() {
    List<DirectTrainLog> logs = directTrainLogDao.selectErrorSyncTasks(upsertTask);
    if (CollectionUtils.isEmpty(logs)) {
      return Lists.newArrayListWithExpectedSize(0);
    }
    List<Tenant1Task> list = logs.stream().map(DirectTrainLog::getRequestParams)
        .filter(StringUtils::isNotBlank)
        .map(JSONObject::parseObject)
        .map(Tenant1Task::new)
        .collect(Collectors.toList());
    return list;
  }

  private List<Tenant1Bug> selectErrorSyncBugs() {
   List<Long> logIds= directTrainLogDao.getErrorSyncBugLogIds(upsertBugs);
   if(CollectionUtils.isEmpty(logIds)){
     return Lists.newArrayListWithExpectedSize(0);
   }
    List<DirectTrainLog> logs = directTrainLogDao.selectErrorSyncBugs(logIds);
    if (CollectionUtils.isEmpty(logs)) {
      return Lists.newArrayListWithExpectedSize(0);
    }
    List<Tenant1Bug> list = logs.stream().map(DirectTrainLog::getRequestParams)
            .filter(StringUtils::isNotBlank)
            .map(JSONObject::parseObject)
            .map(Tenant1Bug::new)
            .collect(Collectors.toList());
    return list;
  }

  /**
   * 同步task到直通车
   */
  private void syncTask2DirectTrain() {
    MybatisPlusConfig.setTenantId(-1);
    String lastUpdateString = stringRedisTemplate.opsForValue().get(tenant1TaskLastTime);
    LocalDateTime lastSyncTime = null;
    if (StringUtils.isNotBlank(lastUpdateString)) {
      lastSyncTime = DateUtil.parseLocalDateTime(lastUpdateString);
    } else {
      lastSyncTime = LocalDateTime.now().minusDays(1).minusMinutes(5);
    }
    LocalDateTime thisSyncTime = LocalDateTime.now();
    List<Tenant1Task> list = issueDAO.selectNeedSync2TrainTask(lastSyncTime);
    // 同步数据到直通车
    sendSyncTaskCall(list, thisSyncTime);
  }

  /**
   * 发送请求
   *
   * @param list
   * @param thisSyncTime
   */
  private void sendSyncTaskCall(List<Tenant1Task> list, LocalDateTime thisSyncTime) {
    if (CollectionUtils.isNotEmpty(list)) {
      ArrayList<DirectTrainLog> logList = Lists.newArrayList();
      for (Tenant1Task tenant1Task : list) {
        DirectTrainLog trainLog = DirectTrainLog.builder()
            .createdTime(LocalDateTime.now())
            .requestParams(JSONObject.toJSONString(tenant1Task))
            .requestUrl(upsertTask)
            .issueId(tenant1Task.getTaskId())
            .build();
        try {
          ResponseEntity<JSONObject> responseEntity = directTrainIssueService.postWithRetry(
              upsertTask, tenant1Task);
          logList.add(trainLog);
          trainLog.setResponseResult(responseEntity.getBody().toJSONString());
          trainLog.setResponseCode(responseEntity.getBody().getString("status_code"));
          if (thisSyncTime != null) {
            stringRedisTemplate.opsForValue()
                .set(tenant1TaskLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
          }
        } catch (Exception e) {
          log.error("同步task到直通车失败", e);
          trainLog.setResponseResult(e.getMessage());
          trainLog.setResponseCode("400");
          logList.add(trainLog);
          if (thisSyncTime != null) {
            stringRedisTemplate.opsForValue()
                .set(tenant1BugLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
          }
        }
      }
      if (CollectionUtils.isNotEmpty(logList)) {
        directTrainLogService.saveWithTryCatch(logList);
      }
    } else {
      if (thisSyncTime != null) {
        stringRedisTemplate.opsForValue()
            .set(tenant1TaskLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
      }
    }
  }

  /**
   * 刚创建的story立马回传到直通车
   *
   * @param issues
   */
  @Async
  public void directTrainCreateIssueBack2DirectTrain(List<Issue> issues) {
    if (CollectionUtils.isEmpty(issues)) {
      return;
    }
    MybatisPlusConfig.setTenantId(-1);//给与管理员权限
    for (Issue issue : issues) {
      if (IssueType.Type10.getFiled().equals(issue.getType())) {
        storyChange(issue, null);
      }
    }
  }


  private void syncBug2DirectTrain() {
    MybatisPlusConfig.setTenantId(-1);
    String lastUpdateString = stringRedisTemplate.opsForValue().get(tenant1BugLastTime);
    LocalDateTime lastSyncTime = null;
    if (StringUtils.isNotBlank(lastUpdateString)) {
      lastSyncTime = DateUtil.parseLocalDateTime(lastUpdateString);
    } else {
      lastSyncTime = LocalDateTime.now().minusDays(1).minusMinutes(5);
    }
    LocalDateTime thisSyncTime = LocalDateTime.now();
    List<Tenant1Bug> list = issueDAO.selectNeedSync2TrainBug(lastSyncTime);
    fillSync2TrainBug(list);
    // 同步数据到直通车
    sendSyncBugCall(list, thisSyncTime);
  }

  private List<Tenant1Bug> fillSync2TrainBug(List<Tenant1Bug> list) {
    if (CollectionUtils.isEmpty(list)) {
      return list;
    }
    ArrayList<Tenant1Bug> fromDBugs = Lists.newArrayList();
    ArrayList<Tenant1Bug> notFromDBugs = Lists.newArrayList();
    for (Tenant1Bug tenant1Bug : list) {
      if (Integer.valueOf(1).equals(tenant1Bug.getFromDirectTrain())) {
        fromDBugs.add(tenant1Bug);
      } else {
        notFromDBugs.add(tenant1Bug);
      }
    }

    //处理直通车创建的bug
    if (CollectionUtils.isNotEmpty(fromDBugs)) {
      List<String> appealIdList = fromDBugs.stream()
              .map(Tenant1Bug::getAppealId)
              .collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(appealIdList)) {
        var trainAppealMap = directTrainAppealService.queryByAppealIdList(appealIdList)
                .stream()
                .collect(Collectors.toMap(DirectTrainAppeal::getAppealId, Function.identity(), (v1, v2) -> v1));
        for (Tenant1Bug fromDBug : fromDBugs) {
          if (trainAppealMap.containsKey(fromDBug.getAppealId())) {
            Optional.ofNullable(trainAppealMap.get(fromDBug.getAppealId()))
                    .ifPresent(trainAppeal -> fromDBug.setProcId(trainAppeal.getAppealId())
                            .setProcType(trainAppeal.getAppealType()));
          }
        }
      }
    }

    //处理中台创建的bug，先找到story，再找story关联的直通车
    if (CollectionUtils.isNotEmpty(notFromDBugs)) {
      List<Long> storyIds = notFromDBugs.stream()
          .map(Tenant1Bug::getIssueId)
          .filter(Objects::nonNull)
          .distinct()
          .collect(Collectors.toList());

      var appealIdList = directTrainIssueService.lambdaQuery()
          .in(DirectTrainIssueMapping::getIssueId, storyIds)
          .eq(DirectTrainIssueMapping::getIsDeleted, 0)
          .list()
            .stream()
            .map(DirectTrainIssueMapping::getAppealid)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

      var trainAppealMap = directTrainAppealService.queryByAppealIdList(appealIdList)
          .stream()
          .collect(Collectors.toMap(DirectTrainAppeal::getAppealId, Function.identity(),(v1, v2) -> v1));
      for (Tenant1Bug notFromDBug : notFromDBugs) {
        if (trainAppealMap.containsKey(notFromDBug.getAppealId())) {
          Optional.ofNullable(trainAppealMap.get(notFromDBug.getAppealId()))
              .ifPresent(trainAppeal -> notFromDBug.setProcId(trainAppeal.getAppealId())
                  .setProcType(trainAppeal.getAppealType()));
        }
      }
    }
    return list;
  }

  private void sendSyncBugCall(List<Tenant1Bug> list, LocalDateTime thisSyncTime) {
    if (CollectionUtils.isNotEmpty(list)) {
      ArrayList<DirectTrainLog> logList = Lists.newArrayList();
      for (Tenant1Bug tenant1Bug : list) {
        DirectTrainLog trainLog = DirectTrainLog.builder()
            .createdTime(LocalDateTime.now())
            .requestParams(JSONObject.toJSONString(tenant1Bug))
            .requestUrl(upsertBugs)
            .issueId(tenant1Bug.getBugId())
            .build();
        logList.add(trainLog);
      }
      try {
        ResponseEntity<JSONObject> responseEntity = directTrainIssueService.postWithRetry(
            upsertBugs, list);
        for (DirectTrainLog directTrainLog : logList) {
          directTrainLog.setResponseResult(responseEntity.getBody().toJSONString());
          directTrainLog.setResponseCode(responseEntity.getBody().getString("status_code"));
        }
        if (thisSyncTime != null) {
          stringRedisTemplate.opsForValue()
              .set(tenant1BugLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
        }
      } catch (Exception e) {
        log.error("同步bug到直通车失败", e);
        for (DirectTrainLog directTrainLog : logList) {
          directTrainLog.setResponseResult(e.getMessage());
          directTrainLog.setResponseCode("400");
          if (thisSyncTime != null) {
            stringRedisTemplate.opsForValue()
                .set(tenant1BugLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
          }
        }
      }
      directTrainLogService.saveWithTryCatch(logList);
    } else {
      if (thisSyncTime != null) {
        stringRedisTemplate.opsForValue()
            .set(tenant1BugLastTime, DateUtil.formatLocalDateTime(thisSyncTime));
      }
    }
  }
}
