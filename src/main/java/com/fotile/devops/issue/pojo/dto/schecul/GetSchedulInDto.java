package com.fotile.devops.issue.pojo.dto.schecul;

import com.fotile.devops.issue.pojo.dto.ComMultipleSelectVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GetSchedulInDto extends ComMultipleSelectVO implements Serializable {
    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum=1;

    @ApiModelProperty("每页数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize=300;



    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("租户id")
    private Integer tenantId;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("版本id")
    private Long sprintId;



    @ApiModelProperty("经办人id")
    private Long ownerId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目ids")
    private List<Long> projectIds;

    @ApiModelProperty("是否为线上bug,,0:不是,,1:是")
    private Integer isOnline;

    @ApiModelProperty("模块id")
    private Long moduleId;

    @ApiModelProperty("状态 10新建 20打开 25重新打开 30待处理 40进行中 45已完成 50已解决 60已关闭 70已拒绝 80已取消")
    private Integer status;

    @ApiModelProperty("父story 或者bug的id")
    private Long parentId;


    @ApiModelProperty("优先级")
    private Integer priority;


    @ApiModelProperty("标题")
    private String title;


    @ApiModelProperty("开发user id")
    private Long kaifaId;


    @ApiModelProperty("测试user id")
    private Long ceshiId;


    @ApiModelProperty("业务方business_id")
    private Long businessId;


    @ApiModelProperty("关键字id")
    private Long keywordId;


    private List<Long> ids;








}
