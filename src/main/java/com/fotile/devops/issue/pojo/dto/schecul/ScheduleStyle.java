package com.fotile.devops.issue.pojo.dto.schecul;

import com.fotile.devops.devopsUsers.pojo.dto.User;
import com.fotile.devops.sprint.pojo.dto.SprintOutDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class ScheduleStyle implements Serializable {

    List<User> userList;

    List<SprintOutDTO> sprintOutDTOS;
    //List<ProjectDTO> allProjectAndCurrentSprint;
}
