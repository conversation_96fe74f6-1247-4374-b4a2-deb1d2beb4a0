package com.fotile.devops.issue.pojo.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fotile.devops.serviceRequest.pojo.dto.UpdateServiceRequestAttachmentsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class UpdateRequestDto implements Serializable {

    @NotNull
    private Long id;


    @ApiModelProperty("类型 10story 20task 30bug 40上线单 50提测单")
    private String type;


    @ApiModelProperty(" 10新建 20打开 25重新打开 30待处理 40进行中 45已完成 50已解决 60已关闭 70已拒绝 80已取消")
    private Integer status;

    private Long ownerId;

    @ApiModelProperty("优先级")
    private Integer priority;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("是否一线需求")
    private Integer isFrontlineDemand;

    private Long projectId;


   // @ApiModelProperty("业务方列表")
  //  private List<BusinessDTO> businessList;
   private List<String> requestUserIds;

    @ApiModelProperty("业务方提出需求时间")
    private Date businessPutDate;


    @ApiModelProperty("业务方期望上线时间")
    private Date businessExpectDate;

    @ApiModelProperty("上线时间")
    // @JsonFormat(pattern = "yyyy-MM-ddTHH:mm:ss.fff",timezone="GMT+8")
    private Date releaseDate;

    @ApiModelProperty("关键字id列表")
    private List<Long> keywordList;

    @ApiModelProperty("关联issue")
    private List<Long> associationIssueIds;

    @ApiModelProperty("关联请求")
    private List<Long> associationRequestIds;

    @ApiModelProperty("需求评分")
    private Integer pmNps;

    /*请求人备注*/
    private String businessRemark;

    /*请求重要性注*/
    private Integer importance;

    /*请求相关菜单*/
    private Long menuId;

    private Long menuType;

    /*请求单类型*/
    private Integer requestType;

    @ApiModelProperty("预计开发时间(就是提测时间)")
    private Date predictCodingFinishDate;

    @ApiModelProperty("预计上线时间(上线时间)")
    private Date predictReleaseDate;

    @ApiModelProperty("关联的首个issue创建时间")
    private Date firstIssueCreatedDate;

    @ApiModelProperty("预计开发填报时间(首次提测时间的填报时间)")
    private Date predictFinishFirstSubmitDate;

    @ApiModelProperty("预计上线填报时间(首次填写预计上线时间的时间)")
    private Date predictReleaseFirstSubmitDate;

    /*关注人列表*/
    private List<String> associationUserIds;
    private List<UpdateServiceRequestAttachmentsDTO> issueAttachmentsList;
    private Integer dingNotice;

    private String like;

    //取消原因
    @TableField(exist = false)
    private String cancelCause;

    private String remark;
}
