package com.fotile.devops.issue.pojo.dto.report;

import java.io.Serializable;
import java.util.List;

public class ReportConditionOutDto implements Serializable {
    /**
     * categroy : ["开发01","开发02"]
     * series : [{"data":[1,2],"name":"幸福app"},{"data":[3,4],"name":"云管理"}]
     */
    private List<String> category;
    private List<SeriesEntity> series;

    public void setCategory(List<String> category) {
        this.category = category;
    }

    public void setSeries(List<SeriesEntity> series) {
        this.series = series;
    }

    public List<String> getCategory() {
        return category;
    }

    public List<SeriesEntity> getSeries() {
        return series;
    }

    public class SeriesEntity {

        private List<Long> data;
        private String name;

        public void setData(List<Long> data) {
            this.data = data;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Long> getData() {
            return data;
        }

        public String getName() {
            return name;
        }
    }
}
