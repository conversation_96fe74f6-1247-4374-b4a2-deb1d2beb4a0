package com.fotile.devops.issue.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.NoArgsConstructor;
import lombok.experimental.Tolerate;

/**
 * direct_train_log
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "direct_train_log", schema="devops")
public class DirectTrainLog implements Serializable {
    /**
     * 主键
     */
    @TableId(type=IdType.AUTO)
    private Long id;

    @TableField(value = "issue_id")
    private Long issueId;

    /**
     * 请求URL
     */
    @TableField(value = "request_url")
    private String requestUrl;

    /**
     * 响应代码
     */
    @TableField(value = "response_code")
    private String responseCode;

    /**
     * 调用时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 请求参数
     */
    @TableField(value = "request_params")
    private String requestParams;

    /**
     * 响应内容
     */
    @TableField(value = "response_result")
    private String responseResult;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}