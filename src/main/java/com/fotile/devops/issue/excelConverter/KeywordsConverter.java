package com.fotile.devops.issue.excelConverter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fotile.devops.keyword.pojo.entity.Keyword;

import java.util.List;
import java.util.stream.Collectors;

public class KeywordsConverter implements Converter<List<Keyword>> {
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public List convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(List<Keyword> value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String keywordlist=value.stream().map(Keyword::getName).collect(Collectors.joining(","));
        return new CellData(keywordlist);
    }


}
