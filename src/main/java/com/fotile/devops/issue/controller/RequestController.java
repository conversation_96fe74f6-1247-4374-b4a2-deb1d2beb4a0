package com.fotile.devops.issue.controller;

import com.fotile.devops.issue.pojo.dto.excel.ExportRequestOutDto;
import com.fotile.devops.issue.pojo.dto.request.*;
import com.fotile.devops.issue.service.RequestService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(value = "请求相关接口")
@RequestMapping("/api/requestIssue")
public class RequestController extends BaseController {
    @Autowired
    RequestService requestService;

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result updateRequest(@RequestBody UpdateRequestDto dto){

        log.debug("修改请求"+dto.toString());
        return requestService.updateRequest(dto);
    }

    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public Result updateRequest(@RequestBody InsertRequestDto dto){

        log.debug("插入请求"+dto.toString());
        return requestService.insertRequest(dto);
    }


    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("导出excel")
    public Result exportRequest(@RequestBody @Valid SelectRequestInDto inDto) {
        log.debug("导出报表"+inDto.toString());
        int i= requestService.findRuquestIssueListExport(inDto);
        if (i == 0){
            return success("导出任务已创建，请耐心等候", null);
        }else if (i == 1){
            return failure(1, "导出任务创建失败", null);
        }else {
            return success("导出任务已创建，请勿重复创建", null);
        }
    }

//    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @ApiOperation("导出excel")
//    public Result exportRequest(@RequestBody @Valid SelectRequestInDto inDto) {
//        log.debug("导出报表"+inDto.toString());
//        List<ExportRequestOutDto> list = requestService.getexportRequestList(inDto);
//        return success(list) ;
//    }

    @RequestMapping(value = "/findRequestByParamCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询条数")
    public Result findRequestByParamCount(@RequestBody @Valid SelectRequestInDto inDto){
        return success(requestService.findRequestByParamCount(inDto));
    }


    @RequestMapping(value = "/select", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询请求")
    public Result selectRequest(@RequestBody SelectRequestInDto selectRequestInDto) {
        log.debug("分页查询"+selectRequestInDto.toString());
        return success(requestService.selectRequest(selectRequestInDto)) ;
    }

    @RequestMapping(value = "/board", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询请求")
    public Result board(@RequestBody SelectRequestInDto selectRequestInDto) {
        log.debug("不分页查询"+selectRequestInDto.toString());
        return success(requestService.selectRequest2(selectRequestInDto)) ;
    }

    @RequestMapping(value = "/view", method = RequestMethod.GET)
    public Result viewRequest(@RequestParam Long id){
        log.debug("根据id查询"+id);
        return requestService.view(id);
    }


    @RequestMapping(value = "/distribut", method = RequestMethod.POST)
    public Result distribut(@RequestBody DistributDto dto){

        log.debug("分配经办人"+dto.toString());
        return requestService.distribut(dto);
    }

    @RequestMapping(value = "/selectAllByCreatedId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询我创建并且我提出的请求")
    public Result selectAllByCreatedId(@RequestBody SelectSpectacularsInDto inDto) {
        log.debug("分页查询"+inDto.toString());
        return success(requestService.selectAllByCreatedId(inDto)) ;
    }


}
