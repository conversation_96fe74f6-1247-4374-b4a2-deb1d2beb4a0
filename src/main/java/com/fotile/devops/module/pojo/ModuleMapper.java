package com.fotile.devops.module.pojo;

import com.fotile.devops.module.pojo.dto.ModuleInsertDTO;
import com.fotile.devops.module.pojo.dto.ModuleSelectDTO;
import com.fotile.devops.module.pojo.dto.ModuleSelectOutDTO;
import com.fotile.devops.module.pojo.dto.UpdateModuleDTO;
import com.fotile.devops.module.pojo.entity.Module;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ModuleMapper {

    ModuleMapper INSTANCE = Mappers.getMapper(ModuleMapper.class);

    Module ModuleInsertDTOTOModule(ModuleInsertDTO moduleInsertDTO);

    Module ModuleUpdateDTOTOModule(UpdateModuleDTO DTO);

    ModuleSelectDTO ModuleTOModuleUpdateAndSelectDTO(Module module);

    List<ModuleSelectOutDTO> Modules2outDTO(List<Module> modules);
}
