package com.fotile.devops.userOption.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;

/**
 * user_option
 * <AUTHOR>
/**
* Created by Mybatis Generator on 2020/02/14
*/
@Entity
@ApiModel("用户查询参数")
@TableName(value="user_option", schema="devops")
@Data
public class UserOption extends AuditingEntity {
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @TableField(value = "name")
    private String name;

    /**
     * json分类
     */
    @ApiModelProperty("json分类")
    @TableField(value = "category")
    private String category;

    /**
     * json字符串
     */
    @ApiModelProperty("json字符串")
    @TableField(value = "json_string")
    private String jsonString;
}