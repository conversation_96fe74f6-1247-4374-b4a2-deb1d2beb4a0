package com.fotile.devops.issueLogs.pojo.dto;

import com.fotile.devops.issueLogsLine.pojo.dto.IssueLogsLineInsertDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ApiModel("issue日志表头新增参数")
@Data
public class IssueLogsInsertDTO implements Serializable {

    @ApiModelProperty("issueid")
    private Long issueId;
    @ApiModelProperty("修改人id")
    private Long userId;
    @ApiModelProperty("状态 0创建 1修改")
    private Integer status;

    private Integer tenantId;

    private List<IssueLogsLineInsertDTO> lines;


    public void setline(IssueLogsLineInsertDTO insertDTO) {
        List<IssueLogsLineInsertDTO> liness = new ArrayList<>();
        liness.add(insertDTO);
        this.lines = liness;
    }

    public void addline(IssueLogsLineInsertDTO insertDTO) {
        if (this.lines == null) {
            this.lines = new ArrayList<>();
            this.lines.add(insertDTO);
        } else {
            this.lines.add(insertDTO);
        }
    }

}
