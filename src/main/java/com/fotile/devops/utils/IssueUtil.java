package com.fotile.devops.utils;

import com.fotile.devops.issue.pojo.dto.ComMultipleSelectVO;
import com.fotile.devops.issue.pojo.entity.Issue;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Data
public class IssueUtil {

    public static final Map<Integer, List<Integer>> statusMap = new HashMap<>();

    public static final List<Integer> kaifaList = Arrays.asList(new Integer[]{10, 30, 31, 32, 35});
    public static final List<Integer> ceshiList = Arrays.asList(new Integer[]{10, 20, 25});
    public static final List<Integer> chanpinList = Arrays.asList(new Integer[]{10, 40, 45});
    public static final List<Integer> yuiweiList = Arrays.asList(new Integer[]{10, 50});

    static {
        statusMap.put(10, Arrays.asList(new Integer[]{20}));
        statusMap.put(20, Arrays.asList(new Integer[]{30, 80}));
        statusMap.put(25, Arrays.asList(new Integer[]{30, 40, 80}));
        statusMap.put(30, Arrays.asList(new Integer[]{40, 70}));
        statusMap.put(40, Arrays.asList(new Integer[]{45}));
        statusMap.put(45, Arrays.asList(new Integer[]{50}));
        statusMap.put(50, Arrays.asList(new Integer[]{25, 60}));
        statusMap.put(60, Arrays.asList(new Integer[]{25}));
        statusMap.put(70, Arrays.asList(new Integer[]{80}));
    }



    public static Boolean equalIssue(Issue source, Issue target) {
        Boolean equal = true;
        if (!ObjectUtils.isEmpty(source.getSprintId()) && !source.getSprintId().equals(target.getSprintId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getOwnerId()) && !source.getOwnerId().equals(target.getOwnerId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getProjectId()) && !source.getProjectId().equals(target.getProjectId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getModuleId()) && !source.getModuleId().equals(target.getModuleId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getStatus()) && !source.getStatus().equals(target.getStatus())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getParentId()) && !source.getParentId().equals(target.getParentId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getBugReason()) && !source.getBugReason().equals(target.getBugReason())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getBugLevel()) && !source.getBugLevel().equals(target.getBugLevel())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getPriority()) && !source.getPriority().equals(target.getPriority())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getTitle()) && !source.getTitle().equals(target.getTitle())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getEstimateKaifaHours()) && !source.getEstimateKaifaHours().equals(target.getEstimateKaifaHours())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getEstimateCeshiHours()) && !source.getEstimateCeshiHours().equals(target.getEstimateCeshiHours())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getKaifaId()) && !source.getKaifaId().equals(target.getKaifaId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getCeshiId()) && !source.getCeshiId().equals(target.getCeshiId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getBusinessId()) && !source.getBusinessId().equals(target.getBusinessId())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getBusinessPutDate()) && !source.getBusinessPutDate().equals(target.getBusinessPutDate())) {
            equal = false;
        }
        if (!ObjectUtils.isEmpty(source.getBusinessExpectDate()) && !source.getBusinessExpectDate().equals(target.getBusinessExpectDate())) {
            equal = false;
        }

        return equal;
    }





}
