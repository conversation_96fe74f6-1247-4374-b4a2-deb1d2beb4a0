package com.fotile.devops.gitlab.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

@Entity
@ApiModel("commit信息中的issueid和文件关联表")
@TableName(value = "git_issue", schema = "devops")
@Data
public class GitIssue extends AuditingEntity {

    @TableField(value = "issue_id")
    private Long issueId;
    @TableField(value = "git_file_id")
    private Long gitlabFileId;

    public GitIssue() {
    }

    public GitIssue(Long issueId, Long gitlabFileId) {
        this.issueId = issueId;
        this.gitlabFileId = gitlabFileId;
    }
}
