package com.fotile.devops.enums;

/**
 * Create By lubing on 2022/10/18.
 */
public enum OpLogEnum {
    /**
     * 用例创建
     */
    CreateUseCase(100, "创建",100,  "创建用例"),
    /**
     * 用例修改
     */
    UpdateUseCase(110, "修改",100,  ""),
    /**
     * 用例附件
     */
    UseCaseAttachment(120, "附件",100,  "附件变更"),

    AddUseCaseDescription(130, "新增描述",100,  ""),
    /**
     * 回归库创建
     */
    CreateRegression(200,"创建",200,"创建回归用例库"),
    /**
     * 回归库修改
     */
    UpdateRegression(210, "修改",200,  ""),

    AddRegressionDescription(220,"新增描述",200,""),

    /**
     * 测试单创建
     */
    CreateTestOrder(300,"创建",300,"创建测试单"),
    /**
     * 测试单修改
     */
    UpdateTestOrder(310, "修改",300,  ""),

    AddTestOrdeDescription(320,"新增描述",300,""),
    ;

    /**
     * 操作日志类型值
     */
    private Integer type;
    /**
     * 操作日志类型名称
     */
    private String typeName;

    /**
     * 引用类型
     */
    private Integer refType;

    /**
     * 备注信息
     */
    private String remark;

    OpLogEnum(Integer type, String typeName, Integer refType, String remark) {
        this.type = type;
        this.typeName = typeName;
        this.refType = refType;
        this.remark = remark;
    }

    public static String findTypeName(Integer type){
        String typeName="";
        for (OpLogEnum value : values()) {
            if(value.type.equals(type)){
                typeName=value.typeName;
            }
        }
        return typeName;
    }

    public Integer getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

    public Integer getRefType() {
        return refType;
    }

    public String getRemark() {
        return remark;
    }
}
