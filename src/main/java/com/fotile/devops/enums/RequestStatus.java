package com.fotile.devops.enums;

import org.springframework.scheduling.annotation.Scheduled;

import java.util.HashMap;

public enum RequestStatus {
    Status10(10,"未确认"),
    Status20(20,"已确认"),
    //Status25(25,"重新打开"),
    Status29(29,"已分配"),
    Status35(35,"已设计"),
    Status40(40,"已排期"),
   // Status40(40,"进行中"),
    Status45(45,"已完成"),
   // Status50(50,"已解决"),
    Status60(60,"关闭"),
   // Status70(70,"拒绝"),
    Status80(80,"取消");


    private final Integer filed;
    private final String value;

    RequestStatus(Integer filed, String value) {
        this.filed = filed;
        this.value = value;
    }

    public Integer getFiled() {
        return filed;
    }
    @Scheduled
    public String getValue() {
        return value;
    }

    public static String getValue(Integer filed) {
        if (filed == null) {
            return "";
        }
        for (RequestStatus t: RequestStatus.values()) {
            if(t.getFiled().equals(filed) ){
                return t.getValue();
            }
        }
        return "";
    }
    public static String getValue(String filed) {
        if (filed == null) {
            return "";
        }
        for (RequestStatus t: RequestStatus.values()) {
            if(t.getFiled().equals(Integer.valueOf(filed)) ){
                return t.getValue();
            }
        }
        return "";
    }

    public static HashMap getMap(){
        RequestStatus[] issueStatuses= RequestStatus.values();
        HashMap<String, String> nameMap = new HashMap<>();
        for (RequestStatus issueStatus : issueStatuses) {

            nameMap.put(String.valueOf(issueStatus.filed),issueStatus.value);
        }
        return nameMap;
    }
}
