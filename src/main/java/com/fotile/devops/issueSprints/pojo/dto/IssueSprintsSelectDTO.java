package com.fotile.devops.issueSprints.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@ApiModel("多参数查询issue迭代版本记录")
@Data
public class IssueSprintsSelectDTO implements Serializable {

    @ApiModelProperty("页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;

    
    @ApiModelProperty("是否删除 0未删除 非0等于ID标示删除")
    private Long isDeleted;

    
    @ApiModelProperty("创建人keycloak系统里的账户ID")
    private String createdBy;

    
    @ApiModelProperty("创建时间")
    private Date createdDate;

    
    @ApiModelProperty("修改人keycloak系统里的账户ID")
    private String modifiedBy;

    
    @ApiModelProperty("修改时间")
    private Date modifiedDate;

    
    @ApiModelProperty("issueid")
    private Long issueId;

    
    @ApiModelProperty("迭代版本id")
    private Long sprintId;

       
}
