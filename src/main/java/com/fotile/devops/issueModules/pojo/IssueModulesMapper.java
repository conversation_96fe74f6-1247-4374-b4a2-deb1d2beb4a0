package com.fotile.devops.issueModules.pojo;

import com.fotile.devops.issueModules.pojo.dto.IssueModulesInsertDTO;
import com.fotile.devops.issueModules.pojo.dto.IssueModulesSelectDTO;
import com.fotile.devops.issueModules.pojo.entity.IssueModules;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface IssueModulesMapper {

    IssueModulesMapper INSTANCE = Mappers.getMapper(IssueModulesMapper.class);

    IssueModules IssueModulesInsertDTOTOIssueModules(IssueModulesInsertDTO issueModulesInsertDTO);

    IssueModulesSelectDTO IssueModulesTOIssueModulesUpdateAndSelectDTO(IssueModules issueModules);
}
