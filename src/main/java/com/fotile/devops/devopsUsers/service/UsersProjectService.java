package com.fotile.devops.devopsUsers.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.devops.config.MybatisPlusConfig;
import com.fotile.devops.devopsUsers.dao.DevopsUsersProjectDao;
import com.fotile.devops.devopsUsers.pojo.dto.DevopsUserSelectOutDTO;
import com.fotile.devops.devopsUsers.pojo.dto.DevopsUsersSelectDTO;
import com.fotile.devops.devopsUsers.pojo.dto.UsersProjectDTO;
import com.fotile.devops.devopsUsers.pojo.entity.DevopsUsers;
import com.fotile.devops.devopsUsers.pojo.entity.DevopsUsersProject;
import com.fotile.devops.project.pojo.entity.Project;
import com.fotile.devops.project.service.ProjectService;
import com.fotile.devops.requestUser.pojo.dto.CommonUserInfo;
import com.fotile.devops.utils.UserAuthorUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.web.BusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2023/2/20.
 * copilot 真的是个好东西
 */
@Service
@Slf4j
public class UsersProjectService extends ServiceImpl<DevopsUsersProjectDao, DevopsUsersProject> {
    @Resource
    private DevopsUsersProjectDao devopsUsersProjectDao;
    @Resource
    private ProjectService projectService;
    @Resource
    private UserAuthorUtils userAuthorUtils;
    @Resource
    @Lazy
    private DevopsUsersService devopsUsersService;


    /**
     * @param devopsUsersProjects 保存用户项目关系
     * @return
     */
    public int mySaveBatch(List<DevopsUsersProject> devopsUsersProjects) {
        if (devopsUsersProjects == null || devopsUsersProjects.size() == 0) {
            return 0;
        } else {
            CommonUserInfo commonUserInfo = userAuthorUtils.getCommonUserInfo();
            Date now = new Date();
            for (DevopsUsersProject devopsUsersProject : devopsUsersProjects) {
                devopsUsersProject.setCreatedDate(now);
                devopsUsersProject.setCreatedBy(commonUserInfo.getUserId());
                devopsUsersProject.setIsDeleted(0l);
            }
            return this.saveBatch(devopsUsersProjects) ? devopsUsersProjects.size() : 0;
        }
    }

    public int mySaveBatch(List<Long> userIds, Long projectId) {
        int result = 0;
        if (userIds != null && !userIds.isEmpty() && projectId != null) {
            List<DevopsUsersProject> devopsUsersProjects = Lists.newArrayList();
            for (Long userId : userIds) {
                DevopsUsersProject devopsUsersProject = new DevopsUsersProject();
                devopsUsersProject.setUserId(userId);
                devopsUsersProject.setProjectId(projectId);
                devopsUsersProjects.add(devopsUsersProject);
            }
            result = this.mySaveBatch(devopsUsersProjects);
        }
        return result;
    }

    /**
     * @param userId 根据用户ID删除用户项目关系
     * @return
     */
    public int deleteByUserId(Long userId) {
        return devopsUsersProjectDao.deleteByUserId(userId);
    }

    public boolean deleteByProjectId(Long projectId) {
        if (projectId == null) {
            return false;
        }
        return this.lambdaUpdate().eq(DevopsUsersProject::getProjectId, projectId).remove();
    }

    /**
     * @param usersProjectDTO 更新用户项目关系
     */
    public void updateUsersProject(UsersProjectDTO usersProjectDTO) {
        this.deleteByUserId(usersProjectDTO.getUserId());
        if (usersProjectDTO.getProjectIds() != null && usersProjectDTO.getProjectIds().size() > 0) {
            List<DevopsUsersProject> usersProjects = usersProjectDTO.getProjectIds().stream().map(projectId -> {
                DevopsUsersProject devopsUsersProject = new DevopsUsersProject();
                devopsUsersProject.setUserId(usersProjectDTO.getUserId());
                devopsUsersProject.setProjectId(projectId);
                devopsUsersProject.setTenantId(usersProjectDTO.getTenantId());
                return devopsUsersProject;
            }).collect(Collectors.toList());
            this.mySaveBatch(usersProjects);
        }
    }


    //根据用户id集合查询用户拥有的项目
    public Map<Long, List<Project>> selectProjectsByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.size() == 0)
            return null;
        else {
            Map<Long, List<Long>> userProjects = this.lambdaQuery()
                    .in(DevopsUsersProject::getUserId, userIds)
                    .eq(DevopsUsersProject::getIsDeleted, 0)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(DevopsUsersProject::getUserId, Collectors.mapping(DevopsUsersProject::getProjectId, Collectors.toList())));
            if (userProjects == null || userProjects.size() == 0)
                return null;
            else {
                //根据项目id查询出项目信息并转成map，然后遍历userProjects，将项目信息放入map中,key是userid，value是项目信息
                Map<Long, List<Project>> userProjectMap = new HashMap<>();
                List<Project> projects = projectService.lambdaQuery().in(Project::getId, userProjects.values().stream().flatMap(List::stream).collect(Collectors.toList())).list();
                Map<Long, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, project -> project));
                userProjects.forEach((userId, projectIds) -> {
                    List<Project> projectList = projectIds.stream().map(projectId -> projectMap.get(projectId)).filter(Objects::nonNull).collect(Collectors.toList());
                    userProjectMap.put(userId, projectList);
                });
                return userProjectMap;
            }
        }
    }

    //根据当前登录用户查询用户拥有的项目ids
    public List<Long> selectProjectsByUserId() {
        List<Long> projectIds = Lists.newArrayList();
        if (!Integer.valueOf(-1).equals(MybatisPlusConfig.getTenantId())) {
            CommonUserInfo commonUserInfo = userAuthorUtils.getCommonUserInfo();
            projectIds = devopsUsersProjectDao.queryByKeyClockId(commonUserInfo.getUserId()).stream()
                    .map(DevopsUsersProject::getProjectId)
                    .collect(Collectors.toList());
            if (projectIds.isEmpty()) {
                throw new BusinessException("您没有项目权限，请联系管理员");
            }
        } else {
            projectIds = projectService.lambdaQuery()
                    .eq(Project::getIsDeleted, 0)
                    .list().stream().map(Project::getId).collect(Collectors.toList());
        }
        return projectIds;
    }

    //根据当前登录用户查询用户拥有的项目ids
    public List<Long> selectProjectsByUserIdWithoutThrow() {
        List<Long> projectIds = Lists.newArrayList();
        if (!Integer.valueOf(-1).equals(MybatisPlusConfig.getTenantId())) {
            CommonUserInfo commonUserInfo = userAuthorUtils.getCommonUserInfo();
            projectIds = devopsUsersProjectDao.queryByKeyClockId(commonUserInfo.getUserId()).stream()
                    .map(DevopsUsersProject::getProjectId)
                    .collect(Collectors.toList());
        } else {
            projectIds = projectService.lambdaQuery()
                    .eq(Project::getIsDeleted, 0)
                    .list().stream().map(Project::getId).collect(Collectors.toList());
        }
        return projectIds;
    }


    //根据项目id集合查询项目成员
    public Map<Long, List<DevopsUsers>> selectUsersByProjectIds(List<Long> projectIds) {
        if (projectIds == null || projectIds.size() == 0)
            return Maps.newHashMap();
        else {
            Map<Long, List<Long>> projectUsers = this.lambdaQuery()
                    .in(DevopsUsersProject::getProjectId, projectIds)
                    .eq(DevopsUsersProject::getIsDeleted, 0)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(DevopsUsersProject::getProjectId, Collectors.mapping(DevopsUsersProject::getUserId, Collectors.toList())));
            if (projectUsers == null || projectUsers.size() == 0)
                return Maps.newHashMap();
            else {
                //根据项目id查询出项目信息并转成map，然后遍历userProjects，将项目信息放入map中,key是userid，value是项目信息
                Map<Long, List<DevopsUsers>> projectUserMap = new HashMap<>();
                List<DevopsUsers> users = devopsUsersService.lambdaQuery().in(DevopsUsers::getId, projectUsers.values().stream().flatMap(List::stream).collect(Collectors.toList())).list();
                Map<Long, DevopsUsers> userMap = users.stream().collect(Collectors.toMap(DevopsUsers::getId, user -> user));
                projectUsers.forEach((projectId, userIds) -> {
                    List<DevopsUsers> userList = userIds.stream().map(userId -> userMap.get(userId)).filter(Objects::nonNull).collect(Collectors.toList());
                    projectUserMap.put(projectId, userList);
                });
                return projectUserMap;
            }
        }
    }


    public int insert(UsersProjectDTO dto) {
        int result = 0;
        if (dto.getProjectId() != null && dto.getAccounts() != null && !dto.getAccounts().isEmpty()) {
            DevopsUsersSelectDTO queryUserParam = new DevopsUsersSelectDTO();
            queryUserParam.setAccounts(dto.getAccounts());
            queryUserParam.setPageNum(0);
            queryUserParam.setPageSize(dto.getAccounts().size());
            PageInfo<DevopsUserSelectOutDTO> userOutDtoPageInfo = devopsUsersService.selectByDevopsUsers(queryUserParam);
            Map<String, DevopsUserSelectOutDTO> userMap = new HashMap<>();
            if (userOutDtoPageInfo.getRecords() != null) {
                userMap = userOutDtoPageInfo.getRecords().stream().collect(Collectors.toMap(DevopsUserSelectOutDTO::getAccount, d -> d));
            }
            List<DevopsUsersProject> devopsUsersProjects = new ArrayList<>();
            for (String account : dto.getAccounts()) {
                if (userMap.containsKey(account)) {
                    DevopsUserSelectOutDTO devopsUserSelectOutDTO = userMap.get(account);
                    Long count = this.lambdaQuery()
                            .eq(DevopsUsersProject::getUserId, devopsUserSelectOutDTO.getId())
                            .eq(DevopsUsersProject::getProjectId, dto.getProjectId())
                            .eq(DevopsUsersProject::getIsDeleted, 0)
                            .eq(DevopsUsersProject::getTenantId, devopsUserSelectOutDTO.getTenantId())
                            .count();
                    if (count == 0) {
                        DevopsUsersProject devopsUsersProject = new DevopsUsersProject();
                        devopsUsersProject.setUserId(devopsUserSelectOutDTO.getId());
                        devopsUsersProject.setTenantId(devopsUserSelectOutDTO.getTenantId());
                        devopsUsersProject.setProjectId(dto.getProjectId());
                        devopsUsersProjects.add(devopsUsersProject);
                    }
                }
            }
            if (devopsUsersProjects.size() > 0) {
                result = this.mySaveBatch(devopsUsersProjects);
            }
        }
        return result;
    }

    public int pageCount(UsersProjectDTO dto) {
        return devopsUsersProjectDao.pageCount(dto);
    }

    public PageInfo<DevopsUserSelectOutDTO> myPage(UsersProjectDTO dto) {
        if (dto != null) {
            PageInfo<DevopsUserSelectOutDTO> pageInfo = new PageInfo<>(dto.getPageNum(), dto.getPageSize());
            int count = pageCount(dto);
            pageInfo.setTotal(count);
            if (count > 0) {
                List<DevopsUserSelectOutDTO> devopsUserSelectOutDTOS = devopsUsersProjectDao.getProjectUsers(dto, pageInfo);
                pageInfo.setRecords(devopsUserSelectOutDTOS);
            }
            return pageInfo;
        } else {
            return null;
        }
    }

    /**
     * 根据项目id和用户ids添加关联关系，先删后加
     *
     * @param dto
     * @return
     */
    public Integer addProductUsers(UsersProjectDTO dto) {
        if (dto == null || dto.getProjectId() == null || dto.getAccounts() == null || dto.getAccounts().isEmpty() || dto.getTenantId() == null) {
            return 0;
        }
        HashSet<String> accounts = Sets.newHashSet(dto.getAccounts());
        List<Long> userIds = devopsUsersService.lambdaQuery()
                .in(DevopsUsers::getAccount, accounts.stream().map(MybatisMateConfig::encrypt).collect(Collectors.toList()))
                .eq(DevopsUsers::getTenantId, dto.getTenantId())
                .eq(DevopsUsers::getIsDeleted, 0)
                .list()
                .stream().map(DevopsUsers::getId).collect(Collectors.toList());

        Integer count = 0;
        if (!userIds.isEmpty()) {
            this.lambdaUpdate()
                    .eq(DevopsUsersProject::getProjectId, dto.getProjectId())
                    .in(DevopsUsersProject::getUserId, userIds)
                    .remove();

            String userId1 = userAuthorUtils.getCommonUserInfo().getUserId();
            Date now = new Date();
            List<DevopsUsersProject> devopsUsersProjects = userIds.stream().map(userId -> {
                DevopsUsersProject devopsUsersProject = new DevopsUsersProject();
                devopsUsersProject.setUserId(userId);
                devopsUsersProject.setProjectId(dto.getProjectId());
                devopsUsersProject.setTenantId(dto.getTenantId());
                devopsUsersProject.setIsDeleted(0l);
                devopsUsersProject.setCreatedBy(userId1);
                devopsUsersProject.setCreatedDate(now);
                return devopsUsersProject;
            }).collect(Collectors.toList());
            if (!devopsUsersProjects.isEmpty()) {
                count = devopsUsersProjectDao.insertBatch(devopsUsersProjects);
            }
        }
        return count;
    }
}
