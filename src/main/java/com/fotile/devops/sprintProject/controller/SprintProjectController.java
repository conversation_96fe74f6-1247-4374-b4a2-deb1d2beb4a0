package com.fotile.devops.sprintProject.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.devops.sprintProject.pojo.dto.SprintProjectInsertDTO;
import com.fotile.devops.sprintProject.pojo.dto.SprintProjectSelectDTO;
import com.fotile.devops.sprintProject.pojo.entity.SprintProject;
import com.fotile.devops.sprintProject.service.SprintProjectService;
import com.fotile.framework.web.Result;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.core.common.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@Api(value = "迭代和项目关联表接口")
@RequestMapping("/api/sprintProject")
public class SprintProjectController extends BaseController {
    @Autowired
    private SprintProjectService sprintProjectService;

    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("新增迭代和项目关联表")
    public Result<SprintProjectInsertDTO> insert(@RequestBody @Valid SprintProjectInsertDTO sprintProject) {
        if(log.isDebugEnabled())log.debug("SprintProjectController.insert()-"+ JSON.toJSONString(sprintProject));
        sprintProjectService.insertSelective(sprintProject);
        return success(sprintProject);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("修改迭代和项目关联表")
    public Result<SprintProject> update(@RequestBody @Valid SprintProject sprintProject) {
        if(log.isDebugEnabled())log.debug("SprintProjectController.update()-"+ JSON.toJSONString(sprintProject));
        sprintProjectService.updateByPrimaryKeySelective(sprintProject);
        return success("修改成功");
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("删除迭代和项目关联表")
    public Result<SprintProject> delete(@ApiParam("迭代和项目关联表id") Long id) {

       int i= sprintProjectService.delete(id);
        return success("删除成功",i);
    }

    @RequestMapping(value = "/view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查看迭代和项目关联表")
    public Result<SprintProjectSelectDTO> view(@ApiParam("迭代和项目关联表id") Long id) {
        if(log.isDebugEnabled())log.debug("SprintProjectController.view()-"+ id);
        return success(sprintProjectService.selectByPrimaryKey(id));
    }

    @RequestMapping(value = "/select", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("查询迭代和项目关联表")
    public Result<PageInfo> select(@RequestBody @Valid SprintProjectSelectDTO sprintProjectSelectDTO) {
        if(log.isDebugEnabled())log.debug("SprintProjectController.select()-"+ JSON.toJSONString(sprintProjectSelectDTO));
        return success(sprintProjectService.selectBySprintProject(sprintProjectSelectDTO));
    }
}