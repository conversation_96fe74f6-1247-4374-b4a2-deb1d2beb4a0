package com.fotile.devops.config;

import org.apache.http.Header;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * Create By lubing on 2022/8/25.
 */
@Configuration
public class RestTemplateConfiguration {

    @Resource
    private RestTemplateBuilder restTemplateBuilder;

    @Bean(name = "restTemplate")
    public RestTemplate restTemplate(){
        return restTemplateBuilder.build();
    }

    /**
     * 5s超时 restTemplate
     */
    @Bean(name="timeout20sRestTemplate")
    public RestTemplate twentySecondTimeout(){
        HttpComponentsClientHttpRequestFactory requestFactory
                = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout((int) Duration.ofSeconds(10).toMillis());
        requestFactory.setReadTimeout((int)Duration.ofSeconds(10).toMillis());
        return new RestTemplate(requestFactory);
    }

    /**
     * HTTPS RestTemplate
     */
    @Bean(name="httpsRestTemplate")
    public RestTemplate httpsRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);


        //统一使用短连接
        List<Header> headers = new ArrayList<>();
        headers.add(new BasicHeader(HTTP.CONN_DIRECTIVE, HTTP.CONN_CLOSE));

        CloseableHttpClient httpClient
                = HttpClients.custom()
                .setDefaultHeaders(headers)
                .setSSLHostnameVerifier(new NoopHostnameVerifier())

                //3次超时重试，此处理器内部做了处理只对幂等操作进行重试
                .setRetryHandler(new StandardHttpRequestRetryHandler())
                .setSSLSocketFactory(sslConnectionSocketFactory)
//                .setDefaultCredentialsProvider(credsProvider)
                .build();
        HttpComponentsClientHttpRequestFactory requestFactory
                = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout((int) Duration.ofSeconds(15).toMillis());
        requestFactory.setReadTimeout((int)Duration.ofSeconds(15).toMillis());
        return new RestTemplate(requestFactory);
    }
}
