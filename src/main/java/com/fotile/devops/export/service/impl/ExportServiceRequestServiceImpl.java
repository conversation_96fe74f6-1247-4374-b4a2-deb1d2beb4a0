package com.fotile.devops.export.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.devops.client.DataClient;
import com.fotile.devops.client.MenuClient;
import com.fotile.devops.client.UserClientService;
import com.fotile.devops.client.dto.QueryAllMenuByRoleNamesOutDto;
import com.fotile.devops.client.dto.QueryAllOutDto;
import com.fotile.devops.client.pojo.ExportTaskRecord;
import com.fotile.devops.client.userDto.UserEntityExtend;
import com.fotile.devops.config.MybatisPlusConfig;
import com.fotile.devops.devopsUsers.pojo.dto.UserOutDto;
import com.fotile.devops.devopsUsers.pojo.entity.DevopsUsers;
import com.fotile.devops.devopsUsers.service.DevopsUsersService;
import com.fotile.devops.enums.MenuType;
import com.fotile.devops.enums.RequestStatus;
import com.fotile.devops.enums.RequestType;
import com.fotile.devops.export.entity.bo.PerformExportTaskBO;
import com.fotile.devops.export.entity.dto.ExportDTO;
import com.fotile.devops.export.service.ExportService;
import com.fotile.devops.issue.pojo.dto.excel.ExportRequestOutDto;
import com.fotile.devops.issue.pojo.dto.request.RequestOutDtos;
import com.fotile.devops.keyword.pojo.entity.Keyword;
import com.fotile.devops.project.service.ProjectService;
import com.fotile.devops.requestUser.pojo.entity.RequestUser;
import com.fotile.devops.requestUser.service.RequestUserService;
import com.fotile.devops.serviceRequest.dao.TServiceRequestAssociationMapper;
import com.fotile.devops.serviceRequest.dao.TServiceRequestMapper;
import com.fotile.devops.serviceRequest.enums.ImportanceEnum;
import com.fotile.devops.serviceRequest.enums.PriorityEnum;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequest;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestAssociation;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestAttention;
import com.fotile.devops.serviceRequest.pojo.entity.TServiceRequestIssueMapping;
import com.fotile.devops.serviceRequest.service.TServiceRequestAttentionService;
import com.fotile.devops.serviceRequest.service.TServiceRequestIssueMappingService;
import com.fotile.devops.serviceRequest.service.TServiceRequestKeywordsService;
import com.fotile.devops.utils.Html2Text;
import com.fotile.devops.utils.OssService;
import com.fotile.devops.utils.UserAuthorUtils;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 服务工单导出
 */
@Service("exportServiceRequestService")
@Slf4j
public class ExportServiceRequestServiceImpl implements ExportService {
    @Autowired
    UserAuthorUtils userAuthorUtils;
    @Autowired
    DataClient dataClient;
    @Autowired
    TServiceRequestMapper tServiceRequestMapper;
    @Autowired
    RequestUserService requestUserService;
    @Autowired
    OssService ossService;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    UserClientService userClientService;
    @Autowired
    TServiceRequestAttentionService tServiceRequestAttentionService;
    @Autowired
    DevopsUsersService devopsUsersService;
    @Autowired
    MenuClient menuClient;
    @Autowired
    TServiceRequestKeywordsService tServiceRequestKeywordsService;
    @Autowired
    TServiceRequestAssociationMapper tServiceRequestAssociationMapper;
    @Autowired
    TServiceRequestIssueMappingService tServiceRequestIssueMappingService;
    @Autowired
    private ProjectService projectService;


    @Override
    public int insertExportTask(ExportDTO exportDTO) {
        //获取当前用户
        UserAuthor userAuthor = userAuthorUtils.getUserAuthor();
        PerformExportTaskBO parse = JSONObject.parseObject(exportDTO.getParam(), PerformExportTaskBO.class);
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(parse.getOrderByFiled())|| com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(parse.getSort())){
            parse.setOrderByFiled("createdDate");
            parse.setSort("DESC");
        }
        List<QueryAllMenuByRoleNamesOutDto> queryAllMenuByRoleNamesOutDtoList = userClientService.queryAllMenuByRoleNames().getData();
        for(QueryAllMenuByRoleNamesOutDto queryAllMenuByRoleNamesOutDto:queryAllMenuByRoleNamesOutDtoList){
            if("data_request_list".equals(queryAllMenuByRoleNamesOutDto.getUri())){
                parse.setPermission("1");//可看所有请求的权限
                break;
            }
        }
        DevopsUsers userByKeycloakId = devopsUsersService.findUserByKeycloakId(devopsUsersService.findKeycloakId());
        if(userByKeycloakId==null){//用户不在devops系统中
            parse.setCurrentUserId(-1L);
        }else{
            parse.setCurrentUserId(userByKeycloakId.getId());
        }
        parse.setCurrentUserKeycloakId(devopsUsersService.findKeycloakId());
        parse.setCode(exportDTO.getCode());
        parse.setCount(parse.getCount());
        parse.setNum(parse.getPageNum());
        parse.setFileName(exportDTO.getFileName());
        ExportTaskRecord record = new ExportTaskRecord();

        record.setOperatorId(userAuthor.getUserId());
        record.setOperatorName(StringUtils.isEmpty(userAuthor.getFirstName()) ? userAuthor.getUsername() : userAuthor.getFirstName());
        record.setCreatedBy(userAuthor.getUserId());
        record.setCreatedDate(new Date());
        record.setModifiedBy(userAuthor.getUserId());
        record.setModifiedDate(new Date());
        record.setParamJson(JSONObject.toJSONString(parse));
        record.setTotalCount(parse.getCount());
        record.setType(String.valueOf(exportDTO.getCode()));
        record.setTaskName(exportDTO.getFileName());
        ExportTaskRecord exportTaskRecord = null;
        try {
            Result<ExportTaskRecord> exportTaskRecordResult = dataClient.insertTask(record);
            exportTaskRecord = exportTaskRecordResult.getData();
        } catch (Exception e) {
            log.error(String.format("创建导出任务失败,失败原因:%s", e.getCause()));
            throw new BusinessException("创建导出任务失败");
        }
        if (exportTaskRecord == null) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public List<RequestOutDtos> performExportTask(PerformExportTaskBO performExportTaskBO) {
        MybatisPlusConfig.setTenantId(-1);
        // 任务开始时
        ExportTaskRecord exportTaskRecord = new ExportTaskRecord();
        exportTaskRecord.setId(performExportTaskBO.getTaskId());
        try {
//        dataClient.startTask(exportTaskRecord);
            //生成exl
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            String sheetName = "服务请求列表报表";
            String fileName = performExportTaskBO.getFileName() + ".xlsx";

            ExcelWriterSheetBuilder sheet = EasyExcel.write(os, ExportRequestOutDto.class).sheet(sheetName);

            //获取总数-如果小于1000则不需要分页
            int times = 1;
            //每页数量
            //获取导出条数
            Integer pageSize = performExportTaskBO.getCount();
            int size = pageSize.compareTo(100) > 0 ? 100 : pageSize;
            if (performExportTaskBO.getCount() > size) {
                times = performExportTaskBO.getCount() / size + 1;
            }
            //计算开始条数
            int num = 0;
            if (performExportTaskBO.getNum() - 1 > 0) {
                num = performExportTaskBO.getNum() - 1;
            }
            int start = num * performExportTaskBO.getPageSize();
            List<ExportRequestOutDto> communityMemberExportOutDtos = new ArrayList<>();
            for (int i = 0; i < times; i++) {
                try {
                    List<ExportRequestOutDto> communityMemberExport = null;
                    try {
                        performExportTaskBO.setOffset2(start + i * size);
                        performExportTaskBO.setSize2(size);
                        communityMemberExport = getCommunityMemberExport(performExportTaskBO);
                    } catch (Exception e) {
                        log.error("错误信息" + e.getMessage());
                        communityMemberExport = getExportRequestOutDtos(e);
                    }
                    if (communityMemberExport != null && communityMemberExport.size() > 0) {
                        communityMemberExportOutDtos.addAll(communityMemberExport);
                    }
                } catch (Exception e) {
                    log.error("错误信息：" + e.getMessage());
                }
            }
                if (communityMemberExportOutDtos != null && communityMemberExportOutDtos.size() > 0) {
                    for(int i=0;i<communityMemberExportOutDtos.size();i++){
                        ExportRequestOutDto exportRequestOutDto = communityMemberExportOutDtos.get(i);
                        exportRequestOutDto.setOrderNumber(new Long(i+1));
                    }

                    sheet.doWrite(communityMemberExportOutDtos);
                }

            String s = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            log.error("s:" + s);
            //结束
            exportTaskRecord.setStatus(2);
            exportTaskRecord.setEndTime(new Date());
            exportTaskRecord.setFileUrl(s);
            dataClient.successTask(exportTaskRecord);

            //记录到日志表中
            dataClient.successTask(exportTaskRecord);
        } catch (Exception e) {

            exportTaskRecord.setStatus(3);
//            exportTaskRecord.setEndTime(new Date());
            exportTaskRecord.setFailReason(e.getMessage());
            //打印入参
            log.debug(JSON.toJSONString(exportTaskRecord));
            dataClient.failureTask(exportTaskRecord);
            e.printStackTrace();
        }
        return null;
    }

    private List<ExportRequestOutDto> getExportRequestOutDtos(Exception e) {
        List<ExportRequestOutDto> communityMemberExport;
        ExportRequestOutDto exportRequestOutDto = new ExportRequestOutDto();
        exportRequestOutDto.setTitle("错误信息" + e.getMessage());
        communityMemberExport = new ArrayList<>();
        communityMemberExport.add(exportRequestOutDto);
        return communityMemberExport;
    }

    private List<ExportRequestOutDto> getCommunityMemberExport(PerformExportTaskBO performExportTaskBO) {
        //通用的查询条件
        List<ExportRequestOutDto> communityMemberExportOutDtos = new ArrayList<>();

        List<TServiceRequest> tServiceRequests = tServiceRequestMapper.exportQueryRequest(performExportTaskBO);

        List<Long> requestIds = tServiceRequests.stream().map(TServiceRequest::getId).collect(Collectors.toList());
        List<String> kids = new ArrayList<>();
        List<RequestUser> requestUserList = new ArrayList<>();
        //获取所有提出人名称
        if(requestIds!=null&&requestIds.size()>0) {
            QueryWrapper<RequestUser> requestUserQueryWrapper = new QueryWrapper<>();
            requestUserQueryWrapper.in("issue_id", requestIds);
            requestUserQueryWrapper.eq("is_deleted", 0);
            requestUserList = requestUserService.selectList(requestUserQueryWrapper);

            List<String> requestKeycloakIds = null;
            if (requestUserList != null && requestUserList.size() > 0) {
                requestKeycloakIds = requestUserList.stream().filter(s -> StringUtils.isNotBlank(s.getKeycloakId())).map(RequestUser::getKeycloakId).distinct().collect(Collectors.toList());
                if (requestKeycloakIds != null && requestKeycloakIds.size() > 0) {
                    kids.addAll(requestKeycloakIds);
                }
            }
        }
        //获取所有关注人
        List<TServiceRequestAttention> tServiceRequestAttentions = new ArrayList<>();
        if(requestIds!=null&&requestIds.size()>0) {
            QueryWrapper<TServiceRequestAttention> tServiceRequestAttentionQueryWrapper = new QueryWrapper<>();
            tServiceRequestAttentionQueryWrapper.in("service_request_id", requestIds);
            tServiceRequestAttentionQueryWrapper.eq("is_deleted", 0);
            tServiceRequestAttentions = tServiceRequestAttentionService.list(tServiceRequestAttentionQueryWrapper);
            List<String> attentions = null;
            if (tServiceRequestAttentions != null && tServiceRequestAttentions.size() > 0) {
                attentions = tServiceRequestAttentions.stream().filter(s -> StringUtils.isNotBlank(s.getUserId())).map(TServiceRequestAttention::getUserId).collect(Collectors.toList());
                if (attentions != null && attentions.size() > 0) {
                    kids.addAll(attentions);
                }
            }
        }
        List<UserEntityExtend> userEntityExtends = null;
        if (kids.size() > 0) {
            kids = kids.stream().distinct().collect(Collectors.toList());
            Result<List<UserEntityExtend>> result1 = userClientService.findUserEntityExtendByUserIds2(kids);
            userEntityExtends = result1.getData();
        }
        //获取所有经办人
        List<Long> ownerIds = tServiceRequests.stream().filter(s -> s.getOwnerId() != null).map(TServiceRequest::getOwnerId).collect(Collectors.toList());
        List<UserOutDto> userByIds = null;
        if (ownerIds != null && ownerIds.size() > 0) {
            userByIds = devopsUsersService.findUserByIds(ownerIds);

        }
        //获取所有标签
        List<UserEntityExtend> finalUserEntityExtends = userEntityExtends;
        List<UserOutDto> finalUserByIds = userByIds;
        List<RequestUser> finalRequestUserList = requestUserList;
        List<TServiceRequestAttention> finalTServiceRequestAttentions = tServiceRequestAttentions;

        //获取项目信息
        Map<Long,String> projectMap = projectService.getMap(0);

        tServiceRequests.stream().forEach(tServiceRequest -> {
            ExportRequestOutDto exportRequestOutDto = new ExportRequestOutDto();
            exportRequestOutDto.setId(tServiceRequest.getId());
            if(tServiceRequest.getIsFrontlineDemand()!=null) {
                exportRequestOutDto.setIsFrontlineDemandName(
                        Integer.valueOf(0).equals(tServiceRequest.getIsFrontlineDemand())?"否":"是" );
            }
            if(tServiceRequest.getProjectId()!=null){
                exportRequestOutDto.setProjectName(projectMap.get(tServiceRequest.getProjectId()));
            }
            exportRequestOutDto.setTitle(tServiceRequest.getTitle());
            exportRequestOutDto.setType(RequestType.getValue(tServiceRequest.getRequestType()));
            exportRequestOutDto.setBusinessPutDate(tServiceRequest.getBusinessPutDate());
            exportRequestOutDto.setBusinessExpectDate(tServiceRequest.getBusinessExpectDate());
            exportRequestOutDto.setFirstIssueCreatedDate(tServiceRequest.getFirstIssueCreatedDate())
                    .setPredictCodingFinishDate(tServiceRequest.getPredictCodingFinishDate())
                    .setPredictFinishFirstSubmitDate(tServiceRequest.getPredictFinishFirstSubmitDate())
                    .setPredictReleaseDate(tServiceRequest.getPredictReleaseDate())
                    .setPredictReleaseFirstSubmitDate(tServiceRequest.getPredictReleaseFirstSubmitDate())
                    .setCompletedDate(tServiceRequest.getCompletedDate())
                    .setShutDownDate(tServiceRequest.getShutDownDate())
                    .setBusinessExpectDate(tServiceRequest.getBusinessExpectDate());


            //重要性
            exportRequestOutDto.setImportance(ImportanceEnum.getLotteryDrawResultDescByCode(tServiceRequest.getImportance()));
            //优先度

            exportRequestOutDto.setPriority(tServiceRequest.getPriority() == null ? null : PriorityEnum.getValueWithType(tServiceRequest.getRequestType(),tServiceRequest.getPriority()));
            exportRequestOutDto.setBusinessRemark(tServiceRequest.getBusinessRemark());
            exportRequestOutDto.setStatus(RequestStatus.getValue(tServiceRequest.getStatus()));
            //数据转化

            if (finalUserEntityExtends != null && finalUserEntityExtends.size() > 0) {
                //先获取该请求关联的提出人集合
                List<RequestUser> requestUserLists = finalRequestUserList.stream().filter(s -> s.getIssueId().equals(tServiceRequest.getId())).collect(Collectors.toList());

                //获取提出人名称
                if (requestUserLists != null && requestUserLists.size() > 0) {
                    List<String> collect = requestUserLists.stream().map(RequestUser::getKeycloakId).collect(Collectors.toList());
                    List<UserEntityExtend> userEntityExtendList = finalUserEntityExtends.stream().filter(s -> collect.contains(s.getUserEntityId())).collect(Collectors.toList());
                    if (userEntityExtendList != null && userEntityExtendList.size() > 0) {
                        exportRequestOutDto.setProposers(userEntityExtendList.stream().map(s -> StringUtils.isNotBlank(s.getFirstName()) ? s.getFirstName() : s.getUserName()).collect(Collectors.joining(",")));
                    }
                }
                //获取关注人集合
                List<TServiceRequestAttention> tServiceRequestAttentions1 = finalTServiceRequestAttentions.stream().filter(s -> s.getServiceRequestId().equals(tServiceRequest.getId())).collect(Collectors.toList());
                if (tServiceRequestAttentions1 != null && tServiceRequestAttentions1.size() > 0) {
                    List<String> collect = tServiceRequestAttentions1.stream().map(TServiceRequestAttention::getUserId).collect(Collectors.toList());
                    List<UserEntityExtend> userEntityExtendList = finalUserEntityExtends.stream().filter(s -> collect.contains(s.getUserEntityId())).collect(Collectors.toList());
                    if (userEntityExtendList != null && userEntityExtendList.size() > 0) {
                        //获取关注人名称
                        exportRequestOutDto.setAttentionUser(userEntityExtendList.stream().map(s -> StringUtils.isNotBlank(s.getFirstName()) ? s.getFirstName() : s.getUserName()).collect(Collectors.joining(",")));
                    }
                }

            }
            if (finalUserByIds != null && finalUserByIds.size() > 0) {
                //获取经办人
                UserOutDto userOutDto = finalUserByIds.stream().filter(s -> s.getId().equals(tServiceRequest.getOwnerId())).findFirst().orElse(null);
                if (userOutDto != null) {
                    exportRequestOutDto.setOwnerName(userOutDto.getName());
                }
            }
            //获取相关页面
            if (tServiceRequest.getMenuId() != null) {
                Result<QueryAllOutDto> queryAllOutDtoResult = menuClient.queryMenuById(tServiceRequest.getMenuId());
                if (queryAllOutDtoResult != null) {
                    QueryAllOutDto data = queryAllOutDtoResult.getData();
                    if (data != null) {
                        exportRequestOutDto.setMenuName(
                                data.getMenuType() != null ?
                                        MenuType.getValue(Integer.parseInt(data.getMenuType())).concat("-").concat(data.getName())
                                        : data.getName());
                    }
                }
            }
            //请求服务
//            if (tServiceRequest.getMenuId() != null) {
//                Result<QueryAllOutDto> queryAllOutDtoResult = menuClient.queryMenuById(tServiceRequest.getMenuId());
//                if (queryAllOutDtoResult != null) {
//                    QueryAllOutDto data = queryAllOutDtoResult.getData();
//                    if (data != null) {
//                        exportRequestOutDto.setMenuName(data.getName());
//                    }
//                }
//            }
            //关联请求
            QueryWrapper<TServiceRequestAssociation> tServiceRequestAssociationQueryWrapper = new QueryWrapper<>();
            tServiceRequestAssociationQueryWrapper.eq("service_request_id", tServiceRequest.getId());
            tServiceRequestAssociationQueryWrapper.eq("is_deleted", 0);
            List<TServiceRequestAssociation> associationList = tServiceRequestAssociationMapper.selectList(tServiceRequestAssociationQueryWrapper);
            if (associationList != null && associationList.size() > 0) {
                List<Long> collect = associationList.stream().map(TServiceRequestAssociation::getOtherServiceRequestId).collect(Collectors.toList());
                List<TServiceRequest> tServiceRequests1 = tServiceRequestMapper.selectBatchIds(collect);

                exportRequestOutDto.setOtherIssueId(tServiceRequests1.stream().map(s -> s.getId() +"").collect(Collectors.joining(",")));
            }
            //获取keyword
            List<Keyword> keywords = tServiceRequestKeywordsService.getKeywords(tServiceRequest);
            if (keywords != null && keywords.size() > 0) {
                exportRequestOutDto.setKeyWords(keywords.stream().map(s -> s.getName()).collect(Collectors.joining(",")));
            }
            //获取详细说明
            if(tServiceRequest.getDescription()!=null) {
                exportRequestOutDto.setDescription(Html2Text.getContent(tServiceRequest.getDescription()));
            }

            //关联issue
            exportRequestOutDto.setServiceRequestIssue("");
            List<TServiceRequestIssueMapping> tServiceRequestIssueMappings = tServiceRequestIssueMappingService.getMappingByServiceRequestId(exportRequestOutDto.getId());
            if(tServiceRequestIssueMappings!=null&&tServiceRequestIssueMappings.size()>0){
                List<String> tempLit = new ArrayList();
                for(TServiceRequestIssueMapping tServiceRequestIssueMapping:tServiceRequestIssueMappings){
                    tempLit.add(tServiceRequestIssueMapping.getIssueId().toString());
                }
                exportRequestOutDto.setServiceRequestIssue((String) tempLit.stream().collect(Collectors.joining(",")));
            }
            //创建人
            exportRequestOutDto.setCreatedName(tServiceRequest.getCreatedName());
            //创建时间
            exportRequestOutDto.setCreatedDate(tServiceRequest.getCreatedDate());
            //评价得分
            if(tServiceRequest.getPmNps()!=null) {
                exportRequestOutDto.setPmNps(tServiceRequest.getPmNps().toString());
            }
            communityMemberExportOutDtos.add(exportRequestOutDto);
        });

        return communityMemberExportOutDtos;
    }
}
