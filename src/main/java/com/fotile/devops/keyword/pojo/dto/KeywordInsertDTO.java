package com.fotile.devops.keyword.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;

@ApiModel("标签新增参数")
@Data
public class KeywordInsertDTO implements Serializable {
    @Autowired
    private Long id;
    @ApiModelProperty("标签")
    private String name;

}
