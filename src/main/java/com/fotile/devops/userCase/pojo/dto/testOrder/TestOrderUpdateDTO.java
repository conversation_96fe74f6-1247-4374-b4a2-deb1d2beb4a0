package com.fotile.devops.userCase.pojo.dto.testOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Create By lubing on 2022/9/30.
 */
@Data
public class TestOrderUpdateDTO {


    private Long id;


    private Integer tenantId;

    private String title;


    private Long sprintId;


    private Integer status;


    private Integer priority;


    private Long ownerId;


    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime implementStart;


    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime implementEnd;


    private Integer isDeleted;


    private String createdBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;


    private String modifiedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedDate;


    private String description;
}
