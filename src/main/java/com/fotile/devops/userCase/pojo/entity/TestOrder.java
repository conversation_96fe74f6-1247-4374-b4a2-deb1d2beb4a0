package com.fotile.devops.userCase.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * uc_test_order
 * <AUTHOR>
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "uc_test_order", schema="devops")
public class TestOrder implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Integer tenantId;

    /**
     * 测试单标题
     */
    @TableField("title")
    private String title;

    /**
     * 版本id
     */
    @TableField("sprint_id")
    private Long sprintId;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 经办人id
     */
    @TableField("owner_id")
    private Long ownerId;

    /**
     * 执行周期-起始
     */
    @TableField("implement_start")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime implementStart;

    /**
     * 执行周期-结束
     */
    @TableField("implement_end")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime implementEnd;

    /**
     * 是否删除0否，1删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建人keycloak系统里的账户ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;

    /**
     * 修改人keycloak系统里的账户ID
     */
    @TableField("modified_by")
    private String modifiedBy;

    /**
     * 修改时间
     */
    @TableField("modified_by")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifiedDate;

    /**
     * 详细说明
     */
    @TableField("description")
    private String description;

    private static final long serialVersionUID = 1L;


    public enum statusEnum{
        NotStarted(10,"未开始"),
        InProgress(20,"进行中"),
        Complete(30,"已结束");
        private final Integer filed;
        private final String value;

        statusEnum(Integer filed, String value) {
            this.filed = filed;
            this.value = value;
        }

        public static String getValue(Integer filed){
            String name="";
            for (statusEnum statusEnum : values()) {
                if(statusEnum.getFiled().equals(filed)){
                    name= statusEnum.getValue();
                }
            }
            return name;
        }

        public Integer getFiled() {
            return filed;
        }

        public String getValue() {
            return value;
        }
    }

}