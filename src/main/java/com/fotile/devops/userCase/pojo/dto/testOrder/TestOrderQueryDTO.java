package com.fotile.devops.userCase.pojo.dto.testOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Create By lubing on 2022/9/18.
 */
@Data
public class TestOrderQueryDTO {

    @ApiModelProperty(name = "所属团队集合")
    private List<Integer> tenantIds;

    @ApiModelProperty(name = "测试单标题")
    private String title;

    @ApiModelProperty(name = "版本")
    private List<Long> sprintIds;

    @ApiModelProperty(name = "状态")
    private List<Integer> statusList;


    /**
     * 创建人keycloak系统里的账户ID
     */
    @ApiModelProperty(name = "创建人")
    private String createdBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDateBegin;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDateEnd;

    @ApiModelProperty("页码")
    private Integer pageNum=0;

    @ApiModelProperty("每页数量")
    private Integer pageSize=10;

}
