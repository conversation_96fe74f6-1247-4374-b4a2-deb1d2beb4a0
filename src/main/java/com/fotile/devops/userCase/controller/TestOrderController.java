package com.fotile.devops.userCase.controller;

import com.fotile.devops.enums.OpLogEnum;
import com.fotile.devops.operationLog.service.OperationLogService;
import com.fotile.devops.userCase.pojo.dto.casedto.CaseOutDTO;
import com.fotile.devops.userCase.pojo.dto.testOrder.*;
import com.fotile.devops.userCase.pojo.entity.TestCase;
import com.fotile.devops.userCase.pojo.entity.TestOrder;
import com.fotile.devops.userCase.pojo.mapper.TestOrderMapper;
import com.fotile.devops.userCase.service.TestCaseService;
import com.fotile.devops.userCase.service.TestOrderService;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import com.google.common.base.Joiner;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Create By lubing on 2022/9/18.
 */
@Slf4j
@RestController
@Api(value = "测试单接口")
@RequestMapping("/api/uc/testOrder")
public class TestOrderController extends BaseController {
    @Resource
    private TestOrderService testOrderService;
    @Resource
    private TestCaseService testCaseService;
    @Resource
    private UserAuthorConfig userAuthorConfig;
    @Resource
    private OperationLogService operationLogService;

    @PostMapping("save")
    public Result save(@RequestBody TestOrderUpdateDTO testOrder){
        TestOrder entity = TestOrderMapper.INSTANCE.UpdateDTO2Entity(testOrder);
        entity.setCreatedBy(userAuthorConfig.getCurrentUserId());
        entity.setCreatedDate(LocalDateTime.now());
        boolean save = testOrderService.save(entity);
        operationLogService.addOperationLog(entity.getId(), OpLogEnum.CreateTestOrder, Joiner.on("").join(OpLogEnum.CreateTestOrder.getRemark(),"",entity.getId()) );
        return success("新增成功" ,testOrder.getId());
    }

    @PostMapping("update")
    public Result update(@RequestBody TestOrderUpdateDTO testOrder){
        testOrderService.update(testOrder);
        return success("更新成功");
    }

    @PostMapping("delete")
    public Result delete(@RequestBody @NotEmpty List<Long> ids){
         testOrderService.lambdaUpdate()
                .in(TestOrder::getId, ids)
                .remove();
        testCaseService.cascadeDeleteByTestId(ids);
         return success("删除成功");
    }

    @GetMapping("detail")
    public  Result detail(@RequestParam("id") Long id){
        TestOrder testOrder=  testOrderService.detail(id);
        return success(testOrder);
    }

    @PostMapping("page")
    public Result page(@RequestBody TestOrderQueryDTO dto){
      PageInfo<TestOrderOutDTO> pageInfo= testOrderService.queryPage(dto);
      return success("查询成功",pageInfo);
    }

    @PostMapping("case/save")
    public Result saveCase(@RequestBody UpdateCaseDTO dto){
      Integer count=  testCaseService.save(dto);
     return success("成功保存"+count+"条用例");
    }

    @PostMapping("case/delete")
    public Result deleteCase(@RequestBody @NotEmpty List<Long> caseIds){
        //todo 要删掉执行日志
        if(caseIds!=null&&!caseIds.isEmpty()){
            List<TestCase> list = testCaseService.lambdaQuery()
                    .in(TestCase::getId, caseIds)
                    .eq(TestCase::getIsDeleted, 0)
                    .select(TestCase::getTestId, TestCase::getUseCaseId)
                    .list();
            testCaseService.lambdaUpdate()
                    .in(TestCase::getId,caseIds)
                    .remove();
           if(!list.isEmpty()){
               operationLogService.addOperationLog(list.get(0).getTestId(),OpLogEnum.UpdateTestOrder,"删除用例"+Joiner.on(",").join(list.stream().map(TestCase::getUseCaseId).collect(Collectors.toList())));
           }
        }

        return  success();
    }

    /** 页面点击关联用例时调用接口
     * @param dto
     * @return
     */
    @PostMapping("case/queryUserCase")
    public Result queryUserCase(@RequestBody QueryUserCaseDTO dto){
       PageInfo<CaseOutDTO>  page= testCaseService.queryUserCase(dto);
       return success(page);
    }
    @PostMapping("case/getTestCase")
    public Result getTestCase(@RequestBody QueryUserCaseDTO dto){
        PageInfo<CaseOutDTO>  page=  testCaseService.getTestCase(dto);
        return success(page);
    }
    @PostMapping("case/setTestCaseExecuter")
    public Result setTestCaseExecuter(@RequestBody UpdateCaseDTO dto){
        if(dto.getExecuterId()!=null && dto.getTestCaseIds()!=null && !dto.getTestCaseIds().isEmpty()){
            boolean update = testCaseService.lambdaUpdate()
                    .set(TestCase::getExecuterId, dto.getExecuterId())
                    .in(TestCase::getId, dto.getTestCaseIds())
                    .update();
        }
        return success("分配成功");
    }
    @PostMapping("case/runTestCase")
    public Result runTestCase(@RequestBody UpdateCaseDTO dto){
        Long logId = testCaseService.runTestCase(dto);
        return success("执行成功",logId);
    }

    @PostMapping("case/setBug")
    public Result setBug(@RequestBody UpdateCaseDTO dto){
        testCaseService.setBug(dto);
        return success("bug关联成功");
    }

    @PostMapping("getIssueExecute")
    public Result getIssueExecute(@RequestBody ExecuteDTO dto){
        List<ExecuteDTO> list=testOrderService.getIssueExecute(dto);
      return success(list);
    }
}
