package com.fotile.devops.project.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel("多参数查询项目")
@Data
public class ProjectSelectDTO implements Serializable {

    private Long id;

    /**
     * 版本id
     */
    private Long sprintId;

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页数量")
    private Integer pageSize;

    private Integer tenantId;

    private List<Integer> tenantIds;

    //ids
    private List<Long> ids;

    @ApiModelProperty("是否删除 0未删除 非0等于ID标示删除")
    private Long isDeleted;

    private Long ownerId;

    
    @ApiModelProperty("创建人keycloak系统里的账户ID")
    private String createdBy;

    
    @ApiModelProperty("创建时间")
    private Date createdDate;

    
    @ApiModelProperty("修改人keycloak系统里的账户ID")
    private String modifiedBy;

    
    @ApiModelProperty("修改时间")
    private Date modifiedDate;

    
    @ApiModelProperty("项目名称")
    private String name;

    @ApiModelProperty("查询关键字")
    private String keyWords;

    @ApiModelProperty("排序字段")
    private String orderBy="sort";

    private String orderType="desc";


    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;


    //是否开启权限过滤
    private Boolean isFilter=true;

    private Long currentUserId;


}
