package com.fotile.systemcenter.sysFuncReview.service.impl;

import com.alibaba.excel.EasyExcel;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.util.JsonUtils;
import com.fotile.systemcenter.client.DataClient;
import com.fotile.systemcenter.client.pojo.ExportTaskRecord;
import com.fotile.systemcenter.sysFuncReview.dao.SysFuncStatisticsDao;
import com.fotile.systemcenter.sysFuncReview.pojo.dto.SysFuncStatisticsExportInDto;
import com.fotile.systemcenter.sysFuncReview.pojo.dto.SysFuncStatisticsExportOutDto;
import com.fotile.systemcenter.sysFuncReview.pojo.excel.SysFuncStatisticsExportRequestOutDto;
import com.fotile.systemcenter.sysFuncReview.pojo.mapper.SysFuncReviewExportMapper;
import com.fotile.systemcenter.sysFuncReview.service.SysFuncStatisticsExportImpl;
import com.fotile.systemcenter.utils.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component("912")
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class SysFuncStatisticsExport implements SysFuncStatisticsExportImpl {

    @Autowired
    private SysFuncStatisticsDao sysFuncStatisticsDao;

    @Autowired
    private DataClient dataClient;

    @Autowired
    OssService ossService;

    @Override
    public void exportFile(ExportTaskRecord exportTaskRecord) {

        log.info("进来导出try一try");
        //实体类进行转换
        SysFuncStatisticsExportInDto inDto=JsonUtils.parse(exportTaskRecord.getParamJson(), SysFuncStatisticsExportInDto.class);

        Integer totalCount = exportTaskRecord.getTotalCount();//批量查询总条数
        BigDecimal oldProgress = exportTaskRecord.getProgress();//进度
        Integer count = 5000;
        Integer start1 = inDto.getStart();

        Integer i = totalCount / count;
        Integer lastCount = totalCount % count;
        Integer size = 0;

        //生成Excel，并上传oss
        String sheetName = "评价汇总表";
        String fileName = inDto.getFileName() + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            List<SysFuncStatisticsExportOutDto> outDtoList=new ArrayList<SysFuncStatisticsExportOutDto>();
            for (int j = 0; j <= i; j++) {
                Integer start = start1 + (j) * count;
                size = count;
                if (j == i) {
                    size = lastCount;
                }
                inDto.setOffset(Long.valueOf(start));
                inDto.setPageSize(size);

                List<SysFuncStatisticsExportOutDto> outDtos = sysFuncStatisticsDao.export(inDto);
                outDtoList.addAll(outDtos);
                //更新任务进度
                BigDecimal newProgress = new BigDecimal(inDto.getPageSize()).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
                oldProgress = newProgress.add(oldProgress);
                ExportTaskRecord exportTask = new ExportTaskRecord();
                exportTask.setId(exportTaskRecord.getId());
                exportTask.setProgress(oldProgress.compareTo(BigDecimal.valueOf(1)) < 0 ? oldProgress.multiply(BigDecimal.valueOf(100)) : BigDecimal.valueOf(100));
                exportTask.setExt1(String.valueOf(outDtoList.size()));
                dataClient.updateTask(exportTask);

                /*List<String> typeCodes=new ArrayList<String>();
                typeCodes.add("gw");
                List<Dic> gwDicList=dicServiceImpl.queryAllByTypes(typeCodes);

                Stream<Dic> channelStream = gwDicList.stream().filter(d ->
                        "gw".equals(d.getTypeCode()));
                //二级渠道
                Map<String,Dic> gwMap=channelStream.collect(Collectors.toMap(Dic::getValueCode, s->s));


                for (SysFuncReviewExportOutDto review : outDtoList) {
                    if (!("").equals(review.getStaffStation())&& null!=review.getStaffStation()) {
                        review.setStationName(gwMap.get(review.getStaffStation()).getValueName());
                    }
                }*/
            }
            //转换导出类
            List<SysFuncStatisticsExportRequestOutDto> exportRequestOutDtos = SysFuncReviewExportMapper.INSTANCE.exportListToExportRequestList(outDtoList);

            //写入excel文件流
            EasyExcel.write(os, SysFuncStatisticsExportRequestOutDto.class).sheet(sheetName).doWrite(exportRequestOutDtos);
            String url = ossService.uploadOss(new ByteArrayInputStream(os.toByteArray()), fileName);
            log.info(url+"地址");
            //更新任务状态已完成
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFileUrl(url);
            dataClient.successTask(exportTask);
        }catch (Exception e){
            ExportTaskRecord exportTask = new ExportTaskRecord();
            exportTask.setId(exportTaskRecord.getId());
            exportTask.setFailReason(e.getMessage());
            dataClient.failureTask(exportTask);
            e.printStackTrace();
            log.debug("ERROR************** =" + e);
        }finally {
            try {
                if (os != null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

}
