package com.fotile.systemcenter.sysFuncReview.service;

import com.alibaba.fastjson.JSONObject;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.util.JsonUtils;
import com.fotile.systemcenter.client.pojo.ExportTaskRecord;
import com.fotile.systemcenter.sysFuncReview.dao.SysFuncStatisticsDao;
import com.fotile.systemcenter.sysFuncReview.mq.SysFuncReviewExportChannel;
import com.fotile.systemcenter.sysFuncReview.mq.SysFuncStatisticsExportChannel;
import com.fotile.systemcenter.sysFuncReview.pojo.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 汇总数据表service层
 *
 * <AUTHOR>
 * @since 2022-03-01 13:54:37
 */
@Service
@Slf4j
public class SysFuncStatisticsService {

    @Autowired
    private UserAuthorConfig userAuthorConfig;

    @Autowired
    private SysFuncStatisticsDao sysFuncStatisticsDao;

    @Resource(name = SysFuncStatisticsExportChannel.SYS_FUNC_STATISTICS_OUTPUT)
    private MessageChannel statisticsExportProduces;

    @Autowired
    private SysFuncReviewService sysFuncReviewService;
    /**
     * 分页查询
     *
     * @return 查询结果
     */
    public PageInfo<SysFuncStatisticsOutDto> findPageAll(SysFuncStatisticsInDto inDto) {

        PageInfo<SysFuncStatisticsOutDto> pageInfo=new PageInfo<>(inDto.getPageNum(),inDto.getPageSize());

        pageInfo.setTotal(sysFuncStatisticsDao.findPageAllTotal(inDto));

        if(pageInfo !=null && pageInfo.getTotal()>0){

            pageInfo.setRecords(sysFuncStatisticsDao.findPageAll(inDto,pageInfo));

        }

        return pageInfo;
    }

    public int reviewExportService(SysFuncStatisticsExportInDto inDto) {

        log.info("****导出****");

        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();

        //如果size大于50000代表excel生产失败  导出数量不可超过5万
        if (inDto.getPageSize()>50000){

            return 3;

        }

        try {

            //设置参数，存入任务列表
            ExportTaskRecord exportTaskRecord = sysFuncReviewService.insertTask("912", userAuthor.getUserId(),userAuthor.getUsername(),inDto.getFileName() ,inDto.getPageSize(), JsonUtils.toJson(inDto));

            if ("1".equals(exportTaskRecord.getExt1())) {

                return 2;//已经存在

            }

            String content = JSONObject.toJSON(exportTaskRecord).toString();

            statisticsExportProduces.send(MessageBuilder.withPayload(content).build());

            return 0;

        }catch (Exception e) {

            return 1;

        }

    }

    /**
     * 月份
     */
    public List<String> findMonthList(){
        return sysFuncStatisticsDao.findMonthList();
    }
}
