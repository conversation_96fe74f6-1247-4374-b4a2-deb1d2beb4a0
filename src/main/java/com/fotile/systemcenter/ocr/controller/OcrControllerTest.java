package com.fotile.systemcenter.ocr.controller;

import com.alibaba.fastjson.JSON;
import com.fotile.systemcenter.mail.service.MailSendService;
import com.fotile.systemcenter.ocr.pojo.dto.ParsingInvoiceDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class OcrControllerTest {
    @Autowired
    MailSendService ocrController;

    @Test
    public void test(){
        ocrController.sendEmail1("<EMAIL>","11","11","<EMAIL>");
    }
}