package com.fotile.systemcenter.media.controller;


import com.alibaba.fastjson.JSON;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.STSAssumeRoleSessionCredentialsProvider;
import com.aliyun.oss.model.*;
import com.fotile.framework.data.common.config.AliConfig;
import com.fotile.framework.data.common.pojo.dto.AliStsDTO;
import com.fotile.framework.util.StringUtils;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.fotile.systemcenter.media.client.AliyunOssStsUploadService;
import com.fotile.systemcenter.media.client.AliyunVodUploadService;
import com.fotile.systemcenter.media.enums.FileTypeEnum;
import com.fotile.systemcenter.media.pojo.VideoUrlMappingEntity;
import com.fotile.systemcenter.media.pojo.dto.*;
import com.fotile.systemcenter.media.service.VideoUrlMappingService;
import com.fotile.systemcenter.sms.common.CheckSumBuilder;
import com.fotile.systemcenter.utils.SystemRsaUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.security.core.parameters.P;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 媒体文件相关接口
 */
@RestController
@Api(value = "媒体文件相关接口", tags = {"系统模块"})
@RequestMapping(value = {"/api/media","/api/open/media",  "/api/m/media"})
@Validated
@Slf4j
public class MediaController extends BaseController {

    @Value("${endpoint}")
    String endpoint;
    @Value("${refererList}")
    String refererList;
    // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。
    @Value("${accessKeyId}")
    String accessKeyId;
    @Value("${accessKeySecret}")
    String accessKeySecret;
    @Value("${bucketName}")
    String bucketName;
    @Value("${md5Salt}")
    String md5Salt;
    @Autowired
    AliyunOssStsUploadService aliyunOssStsUploadService;
    String imgURL = "https://hsimage.fotile.com/";
    String imgStsURL = "https://hsimage.efotile.com/";
    String uploadRecognizeIdcard ="https://fotile-card.oss-cn-shanghai.aliyuncs.com/";
    private AliyunVodUploadService aliyunVodUploadService;

    @Autowired
    VideoUrlMappingService videoUrlMappingService;

    @Autowired
    public MediaController(AliyunVodUploadService aliyunVodUploadService) {
        this.aliyunVodUploadService = aliyunVodUploadService;
    }

    @RequestMapping(value = {"/uploadVideo", "/m/uploadVideo"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 获取阿里云视频点播上传地址
     */
    public Result<AliyunVodUploadReturnDTO> uploadVideo(@Validated @RequestBody AliyunVodUploadParamDTO aliyunVodUploadParamDTO) {
        AliyunVodUploadReturnDTO uploadVideo = aliyunVodUploadService.createUploadVideo(aliyunVodUploadParamDTO);
        return success(uploadVideo);
    }



    @RequestMapping(value = {"/uploadOssSts", "/m/uploadOssSts"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 获取阿里云图片上传地址
     */
    public Result<AliyunVodUploadReturnDTO> updateSts(HttpServletRequest request) {
//        if (request.getRequestURI().contains("/open")){
//            throw new BusinessException("拒绝访问", "找不到上传文件");
//        }
        AliyunOssUploadReturnDto ossUploadReturnDto = aliyunOssStsUploadService.createOssSts();
        return success(ossUploadReturnDto);
    }
    /**
     * 获取阿里云图片上传地址
     */
    @RequestMapping(value = {"/tempcertificate", "/m/tempcertificate"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<AliyunVodUploadReturnDTO> tempcertificate() {


        AliyunOssUploadReturnDto ossUploadReturnDto = aliyunOssStsUploadService.createOssSts();
        return success(ossUploadReturnDto);
    }
    /**
     * 获取阿里云图片上传地址
     */
    @RequestMapping(value = {"/gettempcertificate", "/m/gettempcertificate"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AliyunOssUploadReturnDto gettempcertificate() {


        AliyunOssUploadReturnDto ossUploadReturnDto = aliyunOssStsUploadService.createOssSts();
        return ossUploadReturnDto;
    }

    /**
     * 获取阿里云图片上传地址
     */
    @RequestMapping(value = {"/tempcertificateenc", "/m/tempcertificateenc"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<AliyunVodUploadReturnDTO> tempcertificateenc() throws Exception {
        String publickey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCPB3SLS4fCDesEk88oNlMfoABp0wv5JLKvEs13YPtGrPfJDMgU9KjWsA46cHuQEIih5WAeRVHrUN6M9Zel8bNBEnpu2b9fr4nMJPfSVJBvwukRKYkl0Spl6+5ustyGQU74CxeWfGnJJX2IA8Dx3ZoKdhzLqUCK2+7ax/9NpLIXOQIDAQAB";


        AliyunOssUploadReturnDto ossUploadReturnDto = aliyunOssStsUploadService.createOssSts();
        String jsonString = JSON.toJSONString(ossUploadReturnDto);
        return success("操作成功", SystemRsaUtils.encrypt(jsonString,publickey));
    }


    @RequestMapping(value = {"/getuploadOssSts", "/m/getuploadOssSts"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 获取阿里云图片上传地址
     */
    public AliyunOssUploadReturnDto getuploadOssSts(HttpServletRequest request) {
//        if (request.getRequestURI().contains("/open")){
//            throw new BusinessException("拒绝访问", "找不到上传文件");
//        }
        AliyunOssUploadReturnDto ossUploadReturnDto = aliyunOssStsUploadService.createOssSts();
        return ossUploadReturnDto;
    }


    @RequestMapping(value = {"/getPlayInfo", "/m/getPlayInfo"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 获取阿里云视频点播的 播放信息
     */
    public Result<AliyunVodGetPayinfoReturnDTO> getPlayInfo(@Validated @RequestBody AliyunGetPayinfoParamDTO aliyunGetPayinfoParamDTO) {
        AliyunVodGetPayinfoReturnDTO playInfo = aliyunVodUploadService.getPlayInfo(aliyunGetPayinfoParamDTO.getVideoId(), aliyunGetPayinfoParamDTO.getDefinition(), aliyunGetPayinfoParamDTO.getFormats());
        return success(playInfo);
    }

    @RequestMapping(value = {"/getPlayAuth", "/m/getPlayAuth"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 获取阿里云视频点播的 播放权限
     */
    public Result<AliyunPayAuthReturnDTO> getPlayAuth(@Validated @RequestBody AliyunGetPayinfoParamDTO aliyunGetPayinfoParamDTO) {
        AliyunPayAuthReturnDTO playInfo = aliyunVodUploadService.getPlayAuth(aliyunGetPayinfoParamDTO.getVideoId());
        return success(playInfo);
    }

    @ApiOperation("base编码转url")
    @RequestMapping(value="/uploadForBase64",method= RequestMethod.POST,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> uploadForBase64(@RequestBody PictureBase64DTO dto){
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        String fileName = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                .concat(
                        String.valueOf(ThreadLocalRandom.current()
                                .ints(1000, 9999).limit(1).findFirst().getAsInt()))
                .concat(".").concat(StringUtils.isEmpty(dto.getFileType()) ? "png" : dto.getFileType());
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] imageByte = decoder.decode(dto.getPictureBase64());
        InputStream inputStream = new ByteArrayInputStream(imageByte);
        ossClient.putObject(bucketName, fileName, inputStream);
        ossClient.shutdown();
        return success("success",imgURL + fileName);
    }


    @RequestMapping(value = {"/upload", "/m/upload"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public ArrayList<String> queryByType(HttpServletRequest request, @RequestParam(value = "md5", required = false) String md5) {
        String requestURI = request.getRequestURI();
        boolean open = requestURI.contains("open");
        if (open) {
            if (StringUtils.isEmpty(md5)){
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
            String referer = request.getHeader("referer");
            if (StringUtils.isEmpty(referer)) {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            } else {
                boolean flag = false;
                //获取refererList
                String[] split = refererList.split(",");
                if (split.length > 0) {
                    for (String s : split) {
                        if (referer.contains(s)) {
                            flag = true;
                        }
                    }
                } else {
                    throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
                }
                if (!flag) {
                    throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
                }
            }
        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");

        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<String> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (open) {

                    String md51 = CheckSumBuilder.getMD5(CheckSumBuilder.getMD5(filename) + md5Salt);
                    if (StringUtils.isEmpty(md5) || !md5.equals(md51)) {
                        continue;
                    }


                }
                if (!StringUtils.isEmpty(filename)) {
                    try {

                        String fileTyle = filename.substring(filename.lastIndexOf("."));
                        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
                        if (fileTypeAllowed(suffix, FileTypeEnum.getFileType())) {
                            throw new BusinessException("文件类型不允许", "文件类型不允许");
                        }
                        // 文件类型判断 - 校验文件头内容
                        try (InputStream inputStream = file.getInputStream()) {
                            String fileType;
                            // 获取到上传文件的文件头信息
                            String fileHeader = getFileHeader(inputStream);
                            if (StringUtils.isEmpty(fileHeader)) {
                                log.error("Failed to get file header content.");
                                throw new BusinessException("文件类型不允许", "文件类型不允许");
                            }
                            // 根据上传文件的文件头获取文件的真实类型
                            fileType = getFileType(fileHeader);
                            if (!StringUtils.isEmpty(fileType) && fileTypeAllowed(fileType, FileTypeEnum.getFileType())) {
                                log.error("Unsupported file type: [{}]", fileType);
                                throw new BusinessException("文件类型不允许", "文件类型不允许");
                            }
                        } catch (Exception e) {
                            log.error("Get file input stream failed.", e);
                            throw new BusinessException("文件类型不允许", "文件类型不允许");
                        }
                        String name = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                                .concat(
                                        String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                        if (open) {
                            name = "fotile/open/"+name;
                        }
                        PutObjectResult putObjectResult = ossClient.putObject(bucketName, name, file.getInputStream());
                        result.add(imgURL + name);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return result;
    }

    /**
     * 文件类型校验
     *
     * @param fileType    待校验的类型
     * @param allowedType 允许上传的文件类型
     * @return true - 满足，false - 不满足
     */
    private static boolean fileTypeAllowed(String fileType, List<String> allowedType) {
        if (StringUtils.isEmpty(fileType) || CollectionUtils.isEmpty(allowedType)) {
            return false;
        }
        return allowedType.contains(fileType);
    }

    /**
     * 获取文件的文件头信息
     *
     * @param inputStream 输入流
     * @return 文件头信息
     * @throws IOException 异常
     */
    private static String getFileHeader(InputStream inputStream) throws IOException {
        byte[] content = new byte[28];
        inputStream.read(content, 0, content.length);
        return bytesToHexString(content);
    }

    /**
     * 文件头字节数组转为十六进制编码
     *
     * @param content 文件头字节数据
     * @return 十六进制编码
     */
    private static String bytesToHexString(byte[] content) {
        StringBuilder builder = new StringBuilder();
        if (content == null || content.length <= 0) {
            return null;
        }
        String temp;
        for (byte b : content) {
            temp = Integer.toHexString(b & 0xFF).toUpperCase();
            if (temp.length() < 2) {
                builder.append(0);
            }
            builder.append(temp);
        }
        return builder.toString();
    }

    /**
     * 据文件的头信息获取文件类型
     *
     * @param fileHeader 文件头信息
     * @return 文件类型
     */
    public static String getFileType(String fileHeader) {
        if (fileHeader == null || fileHeader.length() == 0) {
            return null;
        }
        fileHeader = fileHeader.toUpperCase();
        FileTypeEnum[] fileTypes = FileTypeEnum.values();
        for (FileTypeEnum type : fileTypes) {
            boolean b = fileHeader.startsWith(type.getHeadCode());
            if (b) {
                return type.getSuffixName();
            }
        }
        return null;
    }

    /**
     * 活动
     *
     * @param request
     * @return
     */
    @RequestMapping(value = {"/activity/uploadKeepSourceName/{acitvityName}", "/activity/m/uploadKeepSourceName/{acitvityName}"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public ArrayList<String> uploadKeepSourceName(@PathVariable String acitvityName, HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<String> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {
                        String fileUrl = filename.substring(0, filename.lastIndexOf("."));
                        String fileType = filename.substring(filename.lastIndexOf("."));

                        String objectName = ObjectUtils.isEmpty(acitvityName) ? fileUrl : acitvityName + "/" + fileUrl;

                        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName).withPrefix(objectName);
                        ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
                        if (objectListing.getObjectSummaries().size() > 0) {
                            AtomicInteger num = new AtomicInteger();
                            objectListing.getObjectSummaries().forEach(s -> {
                                if (s.getKey().contains(fileUrl)) {
                                    num.getAndIncrement();
                                }
                            });

                            if (num.get() > 0) {
                                filename = fileUrl + "(" + num.get() + ")" + fileType;
                            }
                        }
                        String objectUrl = ObjectUtils.isEmpty(acitvityName) ? filename : acitvityName + "/" + filename;
                        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectUrl,file.getInputStream());
                        // 创建ObjectMetadata实例并设置ACL
                        ObjectMetadata metadata = new ObjectMetadata();
                        // 设置对象的访问权限为public-read
                        metadata.setHeader("x-oss-object-acl", "public-read");

                        // 在PutObjectRequest中设置ObjectMetadata
                        putObjectRequest.setMetadata(metadata);

                        PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest );
                        result.add(imgURL + objectUrl);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return result;
    }

    /**
     * 删除单文件
     *
     * @return
     */
    @RequestMapping(value = {"/activity/deleted/{acitvityName}", "/activity/deleted/{acitvityName}"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public Result activityDeletePic(@PathVariable String acitvityName, @RequestParam String fileName) {
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try {
            // 删除文件或目录。如果要删除目录，目录必须为空。
            ossClient.deleteObject(bucketName, acitvityName + "/" + fileName);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return success(1);
    }


    @RequestMapping(value = {"/upload2", "/m/upload2"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public Result queryByType2(HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<Map<String, String>> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {
                        String fileTyle = filename.substring(filename.lastIndexOf("."));
                        String name = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                                .concat(
                                        String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                        ObjectMetadata objectMetadata = new ObjectMetadata();
                        // 自定义的HTTP头部信息
                        Map<String, String> headers = new HashMap<>();
                        headers.put("Content-Disposition", "attachment;filename="+name);
                        // 将自定义的HTTP头部信息设置到元数据中
                        PutObjectRequest putObjectRequest =  new PutObjectRequest(bucketName, name, file.getInputStream());
                        putObjectRequest.setHeaders(headers);

                        ossClient.putObject(putObjectRequest);
                        Map<String, String> map = new HashMap<>();
                        map.put("name", name);
                        map.put("url", imgURL + name);
                        result.add(map);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return success(result);
    }

    @RequestMapping(value = "/uploadApp", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public Result uploadApp(HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<String> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {

                        String fileTyle = filename.substring(filename.lastIndexOf("."));
                        String name = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                                .concat(
                                        String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);

                        PutObjectResult putObjectResult = ossClient.putObject(bucketName, name, file.getInputStream());
                        result.add(imgURL + name);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return success(result);
    }

    @RequestMapping(value = "/uploadByUrl", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件 通过url 上传图片
     */
    public ArrayList<String> uploadByUrl(@ApiParam("外部图片路径") @RequestParam ArrayList<String> url) {

        ArrayList<String> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (String fileUrl : url) {
            if (!StringUtils.isEmpty(fileUrl)) {
                if (!"http".equals(fileUrl.substring(0, 4))) {
                    fileUrl = "https://" + fileUrl;
                }
                InputStream is = null;
                URL openurl = null;
                try {
                    // 构造URL
                    openurl = new URL(fileUrl);
                    // 打开连接
                    // URLConnection con = openurl.openConnection();
                    //con.setRequestProperty("User-Agent", "Mozilla/4.0 compatible; MSIE 5.0;Windows NT; DigExt)");
                    //设置请求超时为5s
                    //con.setConnectTimeout(5 * 1000);
                    // 输入流
                    //is = con.getInputStream();
                    HttpURLConnection http = (HttpURLConnection) openurl
                            .openConnection();
                    http.setRequestMethod("GET"); // 必须是get方式请求
                    http.setRequestProperty("Content-Type",
                            "application/x-www-form-urlencoded");
                    http.setDoOutput(true);
                    http.setDoInput(true);
                    http.setRequestProperty("User-Agent", "Mozilla/4.0 compatible; MSIE 5.0;Windows NT; DigExt)");
                    http.setReadTimeout(5000);
                    http.setConnectTimeout(5000);
                    http.connect();
                    // 获取文件转化为byte流
                    is = http.getInputStream();
                    String name = LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                            .concat(
                                    String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(".jpeg");

                    PutObjectResult putObjectResult = ossClient.putObject(bucketName, name, is);
                    result.add(imgURL + name);
                } catch (Exception e) {
                    throw new BusinessException("网络建立失败", "无法打开" + fileUrl);
                } finally {
                    if (is != null) {
                        try {
                            is.close();
                        } catch (Exception e) {

                        }
                    }
                }


            } else {
                throw new BusinessException("文件上传阿里云出错", "url为null");
            }
        }
        ossClient.shutdown();
        return result;
    }


    @RequestMapping(value = "/uploadTest", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public Result uploadTest(HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<Map<String, String>> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {
                        String fileTyle = filename.substring(filename.lastIndexOf("."));
                        String name = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                                .concat(
                                        String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                        long fileLength = file.getSize();
                        log.info("文件长度:" + fileLength);
                        InitiateMultipartUploadRequest multipartUploadRequest = new InitiateMultipartUploadRequest(bucketName, name);
                        InitiateMultipartUploadResult upresult = ossClient.initiateMultipartUpload(multipartUploadRequest);
                        String uploadId = upresult.getUploadId();
                        List<PartETag> partETags = new ArrayList<PartETag>();
                        final long partSize = 10 * 1024 * 1024L;
                        int partCount = (int) (fileLength / partSize);
                        if (fileLength % partSize != 0) {
                            partCount++;
                        }
                        log.info("分片次数：" + partCount);
                        // 遍历分片上传。
                        Long current = System.currentTimeMillis();
                        for (int i = 0; i < partCount; i++) {
                            long startPos = i * partSize;
                            long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
                            InputStream instream = file.getInputStream();
                            try {
                                // 跳过已经上传的分片。
                                instream.skip(startPos);
                            } catch (Exception e) {
                                log.error("异常信息：" + e.getMessage());
                                break;
                            }
                            UploadPartRequest uploadPartRequest = new UploadPartRequest();
                            uploadPartRequest.setBucketName(bucketName);
                            uploadPartRequest.setKey(name);
                            uploadPartRequest.setUploadId(uploadId);
                            uploadPartRequest.setInputStream(instream);
                            // 设置分片大小。除了最后一个分片没有大小限制，其他的分片最小为100 KB。
                            uploadPartRequest.setPartSize(curPartSize);
                            // 设置分片号。每一个上传的分片都有一个分片号，取值范围是1~10000，如果超出这个范围，OSS将返回InvalidArgument的错误码。
                            uploadPartRequest.setPartNumber(i + 1);
                            // 每个分片不需要按顺序上传，甚至可以在不同客户端上传，OSS会按照分片号排序组成完整的文件。
                            UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                            // 每次上传分片之后，OSS的返回结果包含PartETag。PartETag将被保存在partETags中。
                            partETags.add(uploadPartResult.getPartETag());
                        }
                        Long end = System.currentTimeMillis();
                        log.info("分片处理时长：" + (end - current));
                        CompleteMultipartUploadRequest completeMultipartUploadRequest =
                                new CompleteMultipartUploadRequest(bucketName, name, uploadId, partETags);
                        CompleteMultipartUploadResult completeMultipartUploadResult = ossClient.completeMultipartUpload(completeMultipartUploadRequest);
                        Map<String, String> res = new HashMap<>();
                        res.put("name", name);
                        res.put("url", imgURL + name);
                        result.add(res);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }
                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return success(result);
    }

    @RequestMapping(value = "/deleted", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result deleted(@RequestParam(value = "keys", required = true) List<String> keys) {
        if (CollectionUtils.isEmpty(keys)){
            throw new BusinessException("删除图片地址不能为空");
        }
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try {
            // 删除文件。
            // 填写需要删除的多个文件完整路径。文件完整路径中不能包含Bucket名称。

            DeleteObjectsResult deleteObjectsResult = ossClient.deleteObjects(new DeleteObjectsRequest("fotile-card").withKeys(keys).withEncodingType("url"));
            List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
            try {
                for (String obj : deletedObjects) {
                    String deleteObj = URLDecoder.decode(obj, "UTF-8");
                    log.error(deleteObj);
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return success();
    }

    @RequestMapping(value = "/addVideoUrl", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> addVideoUrl(@RequestBody VideoUrlMappingEntity videoUrlMappingEntity) {

        return success(videoUrlMappingService.insertOrUpdate(videoUrlMappingEntity));
    }

    @RequestMapping(value = "/findVideoUrl/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> findVideoUrl(@PathVariable Long id) {

        return success(videoUrlMappingService.getById(id));
    }

    @RequestMapping(value = "/findVideoUrlPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<?> findVideoUrlPage(Integer pageNum, Integer pageSize) {

        return success(videoUrlMappingService.findVideoUrlPage(pageNum, pageSize));
    }
    /**
     * 活动
     *
     * @param request
     * @return
     */
    @RequestMapping(value = {"/activity/uploadKeepSourceNameSts/{acitvityName}", "/activity/m/uploadKeepSourceNameSts/{acitvityName}"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public ArrayList<String> uploadKeepSourceNameSts(@PathVariable String acitvityName, HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<String> result = new ArrayList();
        AliStsDTO upload = AliConfig.getstsDTOMap("upload");
        if (ObjectUtils.isEmpty(upload)){
            throw new BusinessException("文件上传阿里云出错", "无法获取配置文件 ");
        }
        STSAssumeRoleSessionCredentialsProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
                    upload.getRegion(), upload.getAccessKeyId(), upload.getAccessKeySecret(), upload.getRoleArn());
        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new BusinessException("文件上传阿里云出错", "获取阿里基础信息失败");
        }
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(upload.getEndpoint(), credentialsProvider, null);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {
                        String fileUrl = filename.substring(0, filename.lastIndexOf("."));
                        String fileType = filename.substring(filename.lastIndexOf("."));

                        String objectName = ObjectUtils.isEmpty(acitvityName) ? fileUrl : acitvityName + "/" + fileUrl;

                        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(upload.getBucket()).withPrefix(objectName);
                        ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
                        if (objectListing.getObjectSummaries().size() > 0) {
                            AtomicInteger num = new AtomicInteger();
                            objectListing.getObjectSummaries().forEach(s -> {
                                if (s.getKey().contains(fileUrl)) {
                                    num.getAndIncrement();
                                }
                            });

                            if (num.get() > 0) {
                                filename = fileUrl + "(" + num.get() + ")" + fileType;
                            }
                        }
                        String objectUrl = ObjectUtils.isEmpty(acitvityName) ? filename : acitvityName + "/" + filename;
                        PutObjectRequest putObjectRequest = new PutObjectRequest(upload.getBucket(), objectUrl,file.getInputStream());
                        // 创建ObjectMetadata实例并设置ACL
                        ObjectMetadata metadata = new ObjectMetadata();
                        // 设置对象的访问权限为public-read
                        metadata.setHeader("x-oss-object-acl", "public-read");

                        // 在PutObjectRequest中设置ObjectMetadata
                        putObjectRequest.setMetadata(metadata);

                        PutObjectResult putObjectResult = ossClient.putObject(putObjectRequest );
                        result.add(imgStsURL + objectUrl);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        } catch (Exception e) {
            throw new BusinessException("文件上传阿里云出错", "网络原因报错");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        return result;
    }
    @RequestMapping(value = "/uploadPicSts", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ArrayList<String> uploadPicSts(HttpServletRequest request) {
//        if (request.getRequestURI().contains("/open")){
//            throw new BusinessException("拒绝访问", "找不到上传文件");
//        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        ArrayList<String> result = new ArrayList();

        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        AliStsDTO upload = AliConfig.getstsDTOMap("upload");
        if (ObjectUtils.isEmpty(upload)){
            throw new BusinessException("文件上传阿里云出错", "无法获取配置文件 ");
        }
        STSAssumeRoleSessionCredentialsProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
                    upload.getRegion(), upload.getAccessKeyId(), upload.getAccessKeySecret(), upload.getRoleArn());
        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new BusinessException("文件上传阿里云出错", "获取阿里基础信息失败");
        }
        OSS ossClient = null;
        try {


            ossClient =  new OSSClientBuilder().build(upload.getEndpoint(), credentialsProvider, null);

            OSS finalOssClient = ossClient;
            files.parallelStream().forEach(file -> {
                String filename = file.getOriginalFilename();
                String fileTyle = filename.substring(filename.lastIndexOf("."));
                String suffix = filename.substring(filename.lastIndexOf(".") + 1);
                if (fileTypeAllowed(suffix, FileTypeEnum.getFileType())) {
                    log.error("Unsupported file type: [{}]", suffix);
                }
                // 文件类型判断 - 校验文件头内容
                try (InputStream inputStream = file.getInputStream()) {
                    String fileType;
                    // 获取到上传文件的文件头信息
                    String fileHeader = getFileHeader(inputStream);
                    if (StringUtils.isEmpty(fileHeader)) {
                        log.error("Failed to get file header content.");

                    }
                    // 根据上传文件的文件头获取文件的真实类型
                    fileType = getFileType(fileHeader);
                    if (!StringUtils.isEmpty(fileType) && fileTypeAllowed(fileType, FileTypeEnum.getFileType())) {
                        log.error("Unsupported file type: [{}]", fileType);

                    }
                } catch (Exception e) {
                    log.error("Get file input stream failed.", e);

                }
                try {
                String name = LocalDateTime.now()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                        .concat("/")
                        .concat(LocalDateTime.now()
                        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")))
                        .concat(
                                String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                    PutObjectResult putObjectResult = finalOssClient.putObject(upload.getBucket(), name, file.getInputStream());
                    if (putObjectResult != null && !StringUtils.isEmpty(putObjectResult.getETag())) {
                        log.info("Upload file success. ETag: [{}]", putObjectResult.getETag());
                        result.add(upload.getImgUrl() +"/"+ name);
                    }
                } catch (OSSException e) {
                    log.error("Upload file success.400", e);
                } catch (IOException e) {
                    log.error("Upload file success.400", e);
                }
            });

        } catch (Exception e) {
            throw new BusinessException("上传异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return result;
    }

    @RequestMapping(value = {"/uploadPicStsFiles","/app/uploadPicStsFiles"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ArrayList<String> uploadPicStsFiles(HttpServletRequest request) {
//        if (request.getRequestURI().contains("/open")){
//            throw new BusinessException("拒绝访问", "找不到上传文件");
//        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        ArrayList<String> result = new ArrayList();

        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        AliStsDTO upload = AliConfig.getstsDTOMap("upload");
        if (ObjectUtils.isEmpty(upload)){
            throw new BusinessException("文件上传阿里云出错", "无法获取配置文件 ");
        }
        STSAssumeRoleSessionCredentialsProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
                    upload.getRegion(), upload.getAccessKeyId(), upload.getAccessKeySecret(), upload.getRoleArn());
        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new BusinessException("文件上传阿里云出错", "获取阿里基础信息失败");
        }
        OSS ossClient = null;
        try {


            ossClient =  new OSSClientBuilder().build(upload.getEndpoint(), credentialsProvider, null);

            OSS finalOssClient = ossClient;
            files.parallelStream().forEach(file -> {
                String filename = file.getOriginalFilename();
                String fileTyle = filename.substring(filename.lastIndexOf("."));
                String suffix = filename.substring(filename.lastIndexOf(".") + 1);
                if (fileTypeAllowed(suffix, FileTypeEnum.getFileType())) {
                    log.error("Unsupported file type: [{}]", suffix);
                }
                // 文件类型判断 - 校验文件头内容
                try (InputStream inputStream = file.getInputStream()) {
                    String fileType;
                    // 获取到上传文件的文件头信息
                    String fileHeader = getFileHeader(inputStream);
                    if (StringUtils.isEmpty(fileHeader)) {
                        log.error("Failed to get file header content.");

                    }
                    // 根据上传文件的文件头获取文件的真实类型
                    fileType = getFileType(fileHeader);
                    if (!StringUtils.isEmpty(fileType) && fileTypeAllowed(fileType, FileTypeEnum.getFileType())) {
                        log.error("Unsupported file type: [{}]", fileType);

                    }
                } catch (Exception e) {
                    log.error("Get file input stream failed.", e);

                }
                try {
                    String name = LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            .concat("/")
                            .concat(LocalDateTime.now()
                                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")))
                            .concat(
                                    String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                    PutObjectResult putObjectResult = finalOssClient.putObject(upload.getBucket(), name, file.getInputStream());
                    if (putObjectResult != null && !StringUtils.isEmpty(putObjectResult.getETag())) {
                        log.info("Upload file success. ETag: [{}]", putObjectResult.getETag());
                        result.add(upload.getImgUrl() +"/"+ name);
                    }
                } catch (OSSException e) {
                    log.error("Upload file success.400", e);
                } catch (IOException e) {
                    log.error("Upload file success.400", e);
                }
            });

        } catch (Exception e) {
            throw new BusinessException("上传异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return result;
    }

    @RequestMapping(value = "/app/uploadPicSts", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result appUploadPicSts(HttpServletRequest request) {
//        if (request.getRequestURI().contains("/open")){
//            throw new BusinessException("拒绝访问", "找不到上传文件");
//        }
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        ArrayList<String> result = new ArrayList();

        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        AliStsDTO upload = AliConfig.getstsDTOMap("upload");
        if (ObjectUtils.isEmpty(upload)){
            throw new BusinessException("文件上传阿里云出错", "无法获取配置文件 ");
        }
        STSAssumeRoleSessionCredentialsProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
                    upload.getRegion(), upload.getAccessKeyId(), upload.getAccessKeySecret(), upload.getRoleArn());
        } catch (com.aliyuncs.exceptions.ClientException e) {
            throw new BusinessException("文件上传阿里云出错", "获取阿里基础信息失败");
        }
        OSS ossClient = null;
        try{

            ossClient =   new OSSClientBuilder().build(upload.getEndpoint(), credentialsProvider, null);
            OSS finalOssClient = ossClient;
            files.parallelStream().forEach(file -> {
                String filename = file.getOriginalFilename();
                String fileTyle = filename.substring(filename.lastIndexOf("."));
                String suffix = filename.substring(filename.lastIndexOf(".") + 1);
                if (fileTypeAllowed(suffix, FileTypeEnum.getFileType())) {
                    log.error("Unsupported file type: [{}]", suffix);
                }
                // 文件类型判断 - 校验文件头内容
                try (InputStream inputStream = file.getInputStream()) {
                    String fileType;
                    // 获取到上传文件的文件头信息
                    String fileHeader = getFileHeader(inputStream);
                    if (StringUtils.isEmpty(fileHeader)) {
                        log.error("Failed to get file header content.");

                    }
                    // 根据上传文件的文件头获取文件的真实类型
                    fileType = getFileType(fileHeader);
                    if (!StringUtils.isEmpty(fileType) && fileTypeAllowed(fileType, FileTypeEnum.getFileType())) {
                        log.error("Unsupported file type: [{}]", fileType);

                    }
                } catch (Exception e) {
                    log.error("Get file input stream failed.", e);

                }
                try {
                    String name = LocalDateTime.now()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            .concat("/")
                            .concat(LocalDateTime.now()
                                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")))
                            .concat(
                                    String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);
                    PutObjectResult putObjectResult = finalOssClient.putObject(upload.getBucket(), name, file.getInputStream());
                    if (putObjectResult != null && !StringUtils.isEmpty(putObjectResult.getETag())) {
                        log.info("Upload file success. ETag: [{}]", putObjectResult.getETag());
                        result.add(upload.getImgUrl() +"/"+ name);
                    }
                } catch (OSSException e) {
                    log.error("Upload file success.400", e);
                } catch (IOException e) {
                    log.error("Upload file success.400", e);
                }
            });

        } catch (Exception e) {
            throw new BusinessException("上传异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return success(result);
    }

    /**
     * 交换页面可查看的附件地址
     */
    @RequestMapping(value = "/generatePresignedUrls", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<String>> generatePresignedUrls(@RequestBody List<String> urls) {
        AliStsDTO upload = AliConfig.getstsDTOMap("upload");
        // 以华东1（杭州）的外网Endpoint为例，其它Region请按实际情况填写。
        String endpoint = upload.getEndpoint();
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        AliyunOssUploadReturnDto ossSts = aliyunOssStsUploadService.createOssSts();


        // 填写Bucket名称，例如examplebucket。
        String bucketName = upload.getBucket();
        // 填写Object完整路径，例如exampleobject.txt。Object完整路径中不能包含Bucket名称。
        List<String> objectNames = urls.stream().map(url -> url.replace("https://encr-prod.oss-cn-shanghai.aliyuncs.com/","") ).collect(Collectors.toList());

        // 从STS服务获取临时访问凭证后，您可以通过临时访问密钥和安全令牌生成OSSClient。
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, ossSts.getAccessKeyId(), ossSts.getAccessKeySecret(), ossSts.getSecurityToken());
        List<String> stsurls = new ArrayList<>();
        try {
            objectNames.forEach(objectName -> {
                try {
                    // 设置签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
                    Date expiration = new Date(new Date().getTime() + 3600 * 1000L);
                    // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
                    URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
                    stsurls.add(url.toString());
                } catch (OSSException oe) {
                    System.out.println("Caught an OSSException, which means your request made it to OSS, "
                            + "but was rejected with an error response for some reason.");
                    System.out.println("Error Message:" + oe.getErrorMessage());
                    System.out.println("Error Code:" + oe.getErrorCode());
                    System.out.println("Request ID:" + oe.getRequestId());
                    System.out.println("Host ID:" + oe.getHostId());
                } catch (ClientException ce) {
                    System.out.println("Caught an ClientException, which means the client encountered "
                            + "a serious internal problem while trying to communicate with OSS, "
                            + "such as not being able to access the network.");
                    System.out.println("Error Message:" + ce.getMessage());
                }

            });
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return success(stsurls);
    }
    @RequestMapping(value = {"/uploadRecognizeIdcard", "/m/uploadRecognizeIdcard"}, method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    /**
     * 上传文件，文件input 标签的name 为file 可以多个input同时传递。 同时请求的content type=multipart/form-data
     */
    public ArrayList<String> uploadRecognizeIdcard(HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");

        if (files == null || files.size() == 0) {
            throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
        }
        ArrayList<String> result = new ArrayList();
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for (MultipartFile file : files) {
            if (null != file) {
                String filename = file.getOriginalFilename();
                if (!StringUtils.isEmpty(filename)) {
                    try {

                        String fileTyle = filename.substring(filename.lastIndexOf("."));
                        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
                        if (fileTypeAllowed(suffix, FileTypeEnum.getFileType())) {
                            throw new BusinessException("文件类型不允许", "文件类型不允许");
                        }
                        // 文件类型判断 - 校验文件头内容
                        try (InputStream inputStream = file.getInputStream()) {
                            String fileType;
                            // 获取到上传文件的文件头信息
                            String fileHeader = getFileHeader(inputStream);
                            if (StringUtils.isEmpty(fileHeader)) {
                                log.error("Failed to get file header content.");
                                throw new BusinessException("文件类型不允许", "文件类型不允许");
                            }
                            // 根据上传文件的文件头获取文件的真实类型
                            fileType = getFileType(fileHeader);
                            if (!StringUtils.isEmpty(fileType) && fileTypeAllowed(fileType, FileTypeEnum.getFileType())) {
                                log.error("Unsupported file type: [{}]", fileType);
                                throw new BusinessException("文件类型不允许", "文件类型不允许");
                            }
                        } catch (Exception e) {
                            log.error("Get file input stream failed.", e);
                            throw new BusinessException("文件类型不允许", "文件类型不允许");
                        }
                        String name = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
                                .concat(
                                        String.valueOf(ThreadLocalRandom.current().ints(1000, 9999).limit(1).findFirst().getAsInt())).concat(fileTyle);

                        PutObjectResult putObjectResult = ossClient.putObject("fotile-card", name, file.getInputStream());
                        result.add(uploadRecognizeIdcard + name);
                    } catch (Exception e) {
                        throw new BusinessException("文件上传阿里云出错", "网络原因报错");
                    }

                } else {
                    throw new BusinessException("文件上传阿里云出错", "获取不到文件名称");
                }

            } else {
                throw new BusinessException("文件上传阿里云出错", "找不到上传文件");
            }
        }
        ossClient.shutdown();
        return result;
    }

}