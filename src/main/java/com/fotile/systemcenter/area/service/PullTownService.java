package com.fotile.systemcenter.area.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.systemcenter.area.dao.AreaCountryMapper;
import com.fotile.systemcenter.area.pojo.dto.AreaCountry;
import com.fotile.systemcenter.area.pojo.dto.TownDTO;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
@Slf4j

public class PullTownService {


    @Autowired
    private AreaCountryMapper areaCountryMapper;

    public void pullAddress1(String param) {
        //对参数进行处理，查询数据
        String[] strs = param.split(":");
        if (strs == null || strs.length != 2) {
            throw new BusinessException("参数不正确，请检查参数");
        }
        String key = strs[0];
        Long status = Long.valueOf(strs[1]);
        List<AreaCountry> list = areaCountryMapper.selectList(status);
        if (list == null || list.size() <= 0) {
            log.error("无数据需要执行");
            return;
        }
        RestTemplate restTemplate = new RestTemplate();

        //调用接口，解析对象，更新数据
        for (AreaCountry bean : list) {
            try {
                //"https://apis.map.qq.com/ws/geocoder/v1/?smart_address=闵行区七宝宝龙城&region=上海&key=64FBZ-SUM63-JBL36-YRE36-3QDW7-7NFZT&added_fields=town,town_code";
                String TX_ADDRESS_URL = "https://apis.map.qq.com/ws/district/v1/getchildren";
                String id = bean.getAreaCode().toString().substring(0,6);
                String uri = TX_ADDRESS_URL + "?key=" + key + "&id=" + id;
                String strbody = restTemplate.exchange(uri, HttpMethod.GET, null, String.class).getBody();
                XxlJobLogger.log("获取TXtoken结果：" + strbody);
                JSONObject jsonObject = JSONObject.parseObject(strbody);
                AreaCountry updateBean = new AreaCountry();
                updateBean.setId(bean.getId());
                updateBean.setResponseBody(strbody);
                if (jsonObject == null || jsonObject.get("status") == null || !("0").equals(jsonObject.get("status").toString())) {
                    XxlJobLogger.log("获取TXtoken异常：" + strbody);
                    updateBean.setSyncStatus(2);
                } else {
                    updateBean.setSyncStatus(1);
                    //获取数据，更新信息
                    List<TownDTO> townList = JSONObject.parseArray(JSONArray.parseArray(jsonObject.getString("result")).get(0).toString(),TownDTO.class);
                    if(townList != null && townList.size() > 0){
                        //List<AreaCountry> insertList = new ArrayList<>();
                        for(TownDTO townDTO : townList){
                            if (townDTO != null && townDTO.getId() != null) {
                                AreaCountry insertTownBean = new AreaCountry();
                                BeanUtils.copyProperties(bean,insertTownBean);
                                Long areaCode =  townDTO.getId() *1000;
                                String fullname = townDTO.getFullname();
                                insertTownBean.setAreaCode(areaCode);
                                insertTownBean.setAreaName(fullname);
                                insertTownBean.setLevel(4);
                                insertTownBean.setParentId(bean.getAreaCode());
                                insertTownBean.setFullPathId(bean.getFullPathId()+"-"+areaCode);
                                insertTownBean.setFullPathName(bean.getFullPathName()+"-"+fullname);
                                areaCountryMapper.insert(insertTownBean);
                            }
                        }
                    }
                }
                areaCountryMapper.updateByPrimaryKeySelective(updateBean);
            } catch (Exception e) {
                log.error("拉去地址错误信息：" + bean.toString() + "error:" + e.getMessage());
            }
        }


    }

}
