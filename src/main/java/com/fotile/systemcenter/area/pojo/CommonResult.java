package com.fotile.systemcenter.area.pojo;

public class CommonResult<T> {

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 返回对象
     */
    private T data;
    /**
     * 错误码
     */
    private int errorCode = 200;
    /**
     * 错误消息
     */
    private String errorMsg;

    public CommonResult() {
        super();
    }

    public CommonResult(boolean success) {
        super();
        this.success = success;
    }

    /**
     * 设置正确结果数据对象，即表示返回成功
     *
     * @param data
     */
    public CommonResult(T data) {
        this.success = Boolean.TRUE;
        this.data = data;
    }

    /**
     * 设置错误信息，即表示返回失败
     *
     * @param errorCode
     * @param errorMsg
     */
    public CommonResult(int errorCode, String errorMsg) {
        this.success = Boolean.FALSE;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    /**
     * 设置错误信息，即表示返回失败
     *
     * @param error
     */


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }


    @Override
    public String toString() {
        return "CommonResult [success=" + success + ", data=" + data + ", errorCode=" + errorCode + ", errorMsg=" + errorMsg
                + "]";
    }

}
