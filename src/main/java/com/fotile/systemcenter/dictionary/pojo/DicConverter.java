package com.fotile.systemcenter.dictionary.pojo;


import com.fotile.systemcenter.dictionary.pojo.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DicConverter {

    public DicConverter INSTANCE = Mappers.getMapper(DicConverter.class);

    public List<DicOutDto> listEntityToDto(List<Dic> dicList);

    Dic dtoToEntity(DicOutDto dicOutDto);

    DicOutDto entityToDto(Dic dic);

    //修改字典
    public Dic dicUpdateInDtoToDic(DicUpdateInDto updateInDto);

    public List<QueryByParamsOutDto> DicToQueryByParamsOutDto(List<Dic> dicList);

    public List<QueryByParamsBasicOutDto> DicToQueryByParamsBasicOutDtoOutDto(List<Dic> dicList);

    public QueryByParamsOutDto DicToQueryByParamsOutDto2(Dic dic);

    public Dic insertInDtoToDic(InsertInDto insertInDto);


    DicDTO dicToDicDTO(Dic dic);
}
