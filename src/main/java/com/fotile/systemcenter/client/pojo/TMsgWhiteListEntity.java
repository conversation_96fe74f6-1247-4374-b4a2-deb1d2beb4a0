package com.fotile.systemcenter.client.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
    * 消息发送白名单
    */
@ApiModel(description="消息发送白名单")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "`msgcenter`.`t_msg_white_list`")
public class TMsgWhiteListEntity extends AuditingEntity {
    /**
     * 消息类型
     */
    @TableField(value = "`msg_type`")
    @ApiModelProperty(value="消息类型")
    private Integer msgType;

    /**
     * 账户类型
     */
    @TableField(value = "`account_type`")
    @ApiModelProperty(value="账户类型")
    private Integer accountType;

    /**
     * 消息接收者账户
     */
    @TableField(value = "`receiver_id`")
    @ApiModelProperty(value="消息接收者账户")
    private String receiverId;

    /**
     * 创建时间
     */
    @TableField(value = "`created_time`")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "`modified_time`")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime modifiedTime;

    public static final String COL_ID = "id";

    public static final String COL_MSG_TYPE = "msg_type";

    public static final String COL_ACCOUNT_TYPE = "account_type";

    public static final String COL_RECEIVER_ID = "receiver_id";

    public static final String COL_CREATED_TIME = "created_time";

    public static final String COL_CREATED_BY = "created_by";

    public static final String COL_MODIFIED_TIME = "modified_time";

    public static final String COL_MODIFIED_BY = "modified_by";

    public static final String COL_IS_DELETED = "is_deleted";
}