package com.fotile.systemcenter.client;

import com.fotile.framework.web.Result;
import com.fotile.systemcenter.client.pojo.ExportTaskRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@FeignClient(value = "data-center", path = "/api/exportTask")
public interface DataClient {

    @RequestMapping(value = "/insertTask", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> insertTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = {"/api/open/startTask"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> startTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/failureTask", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> failureTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/updateTask", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ExportTaskRecord> updateTask(@RequestBody ExportTaskRecord exportTaskRecord);

    @RequestMapping(value = "/api/open/successTask", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> successTask(@RequestBody ExportTaskRecord exportTaskRecord);


}