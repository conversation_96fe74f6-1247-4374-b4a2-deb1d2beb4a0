package com.fotile.systemcenter.mail.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fotile.systemcenter.client.MsgClient;
import com.fotile.systemcenter.client.pojo.TMsgWhiteListEntity;
import com.fotile.systemcenter.mail.common.UtilTool;
import com.fotile.systemcenter.mail.pojo.CluesInfo;
import com.fotile.systemcenter.mail.pojo.MailBody;
import com.fotile.systemcenter.mail.pojo.MailExcel;
import com.fotile.systemcenter.mail.pojo.dto.AdverExpirationReminderTreeVO;
import com.fotile.systemcenter.mail.pojo.dto.AdverExporationReminderMailDTO;
import com.fotile.systemcenter.mail.pojo.vo.SendEmailUserCluesVO;
import com.fotile.systemcenter.mail.service.MailSendService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Api(value = "邮件发送")
@RequestMapping("api/mail")
@Controller
public class MailProcess {


    @Autowired
    MailSendService mailSendService;


    @Value("${whiteListSwitch}")
    Boolean whiteListSwitch;

    @Autowired
    private MsgClient msgClient;




   /* @StreamListener(SystemChannel.MAIL_NOTICE_INPUT)
    public synchronized void receive(Message<String> message) {


        JSONArray jsonArrays = new JSONArray();

        try {
            jsonArrays = JSON.parseArray(message.getPayload());
            for (Iterator iterator = jsonArrays.iterator(); iterator.hasNext(); ) {


                JSONObject mailObjects = (JSONObject) iterator.next();
                if (mailObjects.containsKey("data")) {
                    praseJson(mailObjects);
                } else {
                    praseJson2(mailObjects);
                }
            }
        } catch (Exception e) {
            log.error("**************邮件JSON的本次异常为" + e.getMessage() + "JSON内容" + jsonArrays.toJSONString());
        }

    }*/

    public void praseJson2(JSONObject jsonObject) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM月-dd日");

        CluesInfo cluesInfo = new CluesInfo();
        String companyName = jsonObject.get("companyName").toString();
        String mailTo = jsonObject.get("mailTo").toString();
        String cc = ObjectUtils.isNotEmpty(jsonObject.get("cc"))? jsonObject.get("cc").toString() :"";
        String amount = jsonObject.get("amount").toString();
        String startTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("startTime").toString()));
        String startTime1 = StringUtils.substringBeforeLast(startTime, ":");
        String endTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("endTime").toString()));
        SendEmailUserCluesVO sendEmailUserCluesVO  = JSON.parseObject(jsonObject.toString(), SendEmailUserCluesVO.class);
        String endTime1 = StringUtils.substringBeforeLast(endTime, ":");
        //   String phone=(String) jsonObject.get("phone");
        if(whiteListSwitch){
            cc="";
            mailTo = getWhiteList(mailTo);
            if(StringUtils.isBlank(mailTo)){
                return;
            }
        }
        cluesInfo.setCompanyName(companyName);
        cluesInfo.setAmount(amount);
        cluesInfo.setCc(cc);
        cluesInfo.setStartTime(startTime1);
        cluesInfo.setSendEmailUserCluesVO(sendEmailUserCluesVO);
        // List<String> mailTos = Arrays.asList(mailTo.split(","));
        cluesInfo.setEndTime(endTime1);
        cluesInfo.setMailTo(mailTo);

        try {
            mailSendService.cluesRemind(cluesInfo);
        } catch (Exception e) {
            log.error("*************发送邮件失败" + cluesInfo.getMailTo() + "---" + e.getMessage());

        }

    }


    public void praseJson3(JSONObject jsonObject) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM月-dd日");

        CluesInfo cluesInfo = new CluesInfo();
        String companyName = jsonObject.get("districtValue").toString();
        String mailTo = jsonObject.get("mailTo").toString();
        String cc = ObjectUtils.isNotEmpty(jsonObject.get("cc"))? jsonObject.get("cc").toString() :"";
        String amount = jsonObject.get("amount").toString();
        String startTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("startTime").toString()));
        String startTime1 = StringUtils.substringBeforeLast(startTime, ":");
        String endTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("endTime").toString()));
        SendEmailUserCluesVO sendEmailUserCluesVO  = JSON.parseObject(jsonObject.toString(), SendEmailUserCluesVO.class);
        String endTime1 = StringUtils.substringBeforeLast(endTime, ":");
        //   String phone=(String) jsonObject.get("phone");
        cluesInfo.setDistrictValue(companyName);
        cluesInfo.setAmount(amount);
        cluesInfo.setCc(cc);
        cluesInfo.setStartTime(startTime1);
        cluesInfo.setSendEmailUserCluesVO(sendEmailUserCluesVO);
        // List<String> mailTos = Arrays.asList(mailTo.split(","));
        cluesInfo.setEndTime(endTime1);
        cluesInfo.setMailTo(mailTo);

        try {
            mailSendService.districtCluesRemind(cluesInfo);
        } catch (Exception e) {
            log.error("*************发送邮件失败" + cluesInfo.getMailTo() + "---" + e.getMessage());

        }

    }


    public void praseJson4(JSONObject jsonObject) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM月-dd日");

        CluesInfo cluesInfo = new CluesInfo();
        String companyName = jsonObject.get("districtValue").toString();
        String mailTo = jsonObject.get("mailTo").toString();
        String cc = ObjectUtils.isNotEmpty(jsonObject.get("cc"))? jsonObject.get("cc").toString() :"";
        String amount = jsonObject.get("amount").toString();
        String startTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("startTime").toString()));
        String startTime1 = StringUtils.substringBeforeLast(startTime, ":");
        String endTime = simpleDateFormat1.format(simpleDateFormat.parse(jsonObject.get("endTime").toString()));
        SendEmailUserCluesVO sendEmailUserCluesVO  = JSON.parseObject(jsonObject.toString(), SendEmailUserCluesVO.class);
        String endTime1 = StringUtils.substringBeforeLast(endTime, ":");
        //   String phone=(String) jsonObject.get("phone");
        cluesInfo.setDistrictValue(companyName);
        cluesInfo.setAmount(amount);
        cluesInfo.setCc(cc);
        cluesInfo.setStartTime(startTime1);
        cluesInfo.setSendEmailUserCluesVO(sendEmailUserCluesVO);
        // List<String> mailTos = Arrays.asList(mailTo.split(","));
        cluesInfo.setEndTime(endTime1);
        cluesInfo.setMailTo(mailTo);

        try {
            mailSendService.headquartersCluesRemind(cluesInfo);
        } catch (Exception e) {
            log.error("*************发送邮件失败" + cluesInfo.getMailTo() + "---" + e.getMessage());

        }

    }


    public void praseJson(JSONObject jsonObject) {

        MailBody mailBody = new MailBody();
        String companyName = jsonObject.get("companyName").toString();
        String mailTo = jsonObject.get("mailTo").toString();
        if(whiteListSwitch){
            mailTo = getWhiteList(mailTo);
            if(StringUtils.isBlank(mailTo)){
                return;
            }
        }
        String amount = jsonObject.get("amount").toString();
        String startTime = (String) jsonObject.get("startTime");
        String startTime1 = StringUtils.substringBeforeLast(startTime, ":");
        String endTime = (String) jsonObject.get("endTime");
        String endTime1 = StringUtils.substringBeforeLast(endTime, ":");
        mailBody.setCompanyName(companyName);
        mailBody.setAmount(amount);
        mailBody.setStartTime(startTime1);
        mailBody.setMailTo(mailTo);
        mailBody.setEndTime(endTime1);
        List<MailExcel> mailExcels = new ArrayList<>();
        JSONArray mailArray = jsonObject.getJSONArray("data");
        int i = 0;
        for (Iterator iterator = mailArray.iterator(); iterator.hasNext(); ) {
            JSONObject mailObject = (JSONObject) iterator.next();
//            log.error("*********************mailObject:"+mailObject.toString());
            i++;
            MailExcel mailExcel = new MailExcel();
            //mailExcel.setId((String) mailObject.get("id"));

            mailExcel.setId(String.valueOf(i));
            mailExcel.setName(String.valueOf(mailObject.get("name")));


            String time = String.valueOf(mailObject.get("time"));
            String time2 = UtilTool.timeStamp2Date(time);
            mailExcel.setTime(time2);

            mailExcel.setAddress((String) mailObject.get("address"));

            String state = String.valueOf(mailObject.get("state"));
            String state2 = UtilTool.stateConversion(state);
            mailExcel.setState(state2);

            mailExcel.setRelation(String.valueOf(mailObject.get("relation")));
            mailExcel.setProductDescr((String) mailObject.get("productDescr"));
            mailExcel.setActivityName((String) mailObject.get("activityName"));
            mailExcel.setActivityID(String.valueOf(mailObject.get("activityId")));
            mailExcel.setPhone(String.valueOf(mailObject.get("phone")));

            mailExcels.add(mailExcel);

        }
        mailBody.setMailExcel(mailExcels);

        log.debug("*********************mailBody:" + mailBody.toString());
        //System.out.println(mailBody.toString());
        mailSendService.clueNotice(mailBody);
    }


    /**
     * 判断目标邮箱是否在白名单内
     */
    private String getWhiteList(String mailTo){
        List<TMsgWhiteListEntity> list = msgClient.getListByType(4).getData();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        TMsgWhiteListEntity bean = list.parallelStream().filter(x -> x.getReceiverId().equals(mailTo)).findFirst().orElse(null);
        if(bean != null && StringUtils.isNotBlank(bean.getReceiverId())){
            return  mailTo;
        }else {
            return null;
        }
    }


    public void praseJson5(AdverExporationReminderMailDTO mailObjects) {
        mailSendService.adverSendMail(mailObjects);
    }
}
