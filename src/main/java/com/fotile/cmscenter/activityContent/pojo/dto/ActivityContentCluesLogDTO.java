package com.fotile.cmscenter.activityContent.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@ApiModel
@Data
@ToString
public class ActivityContentCluesLogDTO implements Serializable {


    private Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "栏目id")
    private Long subjectId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "留资标识")
    private Boolean cluesFlag;

    private Long createdBy;

    private Long modifiedBy;

    private Date createdDate;

    private Date modifiedDate;
}