package com.fotile.cmscenter.mallAdver.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface ClickAdvertChannel {

    String STATISTICS_SHARE_PER_CLICK_INPUT = "statistics_share_per_click_input";

    String STATISTICS_SHARE_PER_CLICK_OUTPUT = "statistics_share_per_click_output";

    /**
     * 统计广告点击(发送)
     */
    @Output(STATISTICS_SHARE_PER_CLICK_OUTPUT)
    MessageChannel StatisticsSharePerClickInput();

    /**
     * 统计广告点击(接受)
     */
    @Input(STATISTICS_SHARE_PER_CLICK_INPUT)
    SubscribableChannel StatisticsSharePerClickOutput();


    /**=============================收藏列表异步导出=================================================*/
    String COLLECTION_EXPORT_INPUT = "collection_export_input";

    String COLLECTION_EXPORT_OUTPUT = "collection_export_output";

    /**
     * 收藏列表异步导出(接受)
     */
    @Input(COLLECTION_EXPORT_INPUT)
    SubscribableChannel collectionExportInput();

    /**
     * 收藏列表异步导出(发送)
     */
    @Output(COLLECTION_EXPORT_OUTPUT)
    MessageChannel collectionExportOutput();


    /**=============================点赞列表异步导出=================================================*/
    String LIKE_EXPORT_INPUT = "like_export_input";

    String LIKE_EXPORT_OUTPUT = "like_export_output";

    /**
     * 点赞列表异步导出(接受)
     */
    @Input(LIKE_EXPORT_INPUT)
    SubscribableChannel likeExportInput();

    /**
     * 点赞列表异步导出(发送)
     */
    @Output(LIKE_EXPORT_OUTPUT)
    MessageChannel likeExportOutput();



    /**=============================问题回答列表异步导出=================================================*/
    String ANSWER_EXPORT_INPUT = "answer_export_input";

    String ANSWER_EXPORT_OUTPUT = "answer_export_output";

    /**
     * 问题回答列表异步导出(接受)
     */
    @Input(ANSWER_EXPORT_INPUT)
    SubscribableChannel answerExportInput();

    /**
     * 问题回答列表异步导出(发送)
     */
    @Output(ANSWER_EXPORT_OUTPUT)
    MessageChannel answerExportOutput();




    /*************************************************************************************************/

    /**
     * 场景化方案，选择顾客个性化方案时，修改线索对应的上门设计方案
     */
    String SCHEME_CLUES_DESIGN_OUTPUT = "scheme_clues_design_output";

    @Output(SCHEME_CLUES_DESIGN_OUTPUT)
    MessageChannel schemeCluesDesignOutput();


    /*************************************************************************************************/


    /**
     * 修改场景化交互修改标志
     */

    String CHANGE_CLUES_SCENE_OUTPUT = "change_clues_scene_output";

    @Output(CHANGE_CLUES_SCENE_OUTPUT)
    MessageChannel changeCluesSceneProducesOutput();


    /**
     * 场景化方案回退topic
     */
    String BACK_CHANGE_CLUES_SCENE_OUTPUT = "back_change_clues_scene_output";

    @Output(BACK_CHANGE_CLUES_SCENE_OUTPUT)
    MessageChannel backChangeCluesSceneProducesOutput();



    /*************************************************************************************************/

    String DEAL_SCHEME_AMOUNT_INPUT = "deal_scheme_amount_input";

    String DEAL_SCHEME_AMOUNT_OUTPUT = "deal_scheme_amount_output";

    /**
     * 问题回答列表异步导出(接受)
     */
    @Input(DEAL_SCHEME_AMOUNT_INPUT)
    SubscribableChannel dealSchemeConsumesInput();

    /**
     * 问题回答列表异步导出(发送)
     */
    @Output(DEAL_SCHEME_AMOUNT_OUTPUT)
    MessageChannel dealSchemeProducesOutput();

    /*************************************************************************************************/


    String DEAL_RECORD_AMOUNT_INPUT = "deal_record_amount_input";

    String DEAL_RECORD_AMOUNT_OUTPUT = "deal_record_amount_output";

    /**
     * 问题回答列表异步导出(接受)
     */
    @Input(DEAL_RECORD_AMOUNT_INPUT)
    SubscribableChannel dealRecordConsumesInput();

    /**
     * 问题回答列表异步导出(发送)
     */
    @Output(DEAL_RECORD_AMOUNT_OUTPUT)
    MessageChannel dealRecordProducesOutput();






    /*************************************************************************************************/


    String CLUES_CHANGE_AMOUNT_INPUT = "clues_change_amount_input";

    String CLUES_CHANGE_AMOUNT_OUTPUT = "clues_change_amount_output";

    /**
     * 问题回答列表异步导出(接受)
     */
    @Input(CLUES_CHANGE_AMOUNT_INPUT)
    SubscribableChannel cluesChangeAmountConsumesInput();

    /**
     * 问题回答列表异步导出(发送)
     */
    @Output(CLUES_CHANGE_AMOUNT_OUTPUT)
    MessageChannel cluesChangeAmountProducesOutput();


    /*************************************************************************************************/

    String CMS_EXPORT_INPUT = "cms_export_input";

    String CMS_EXPORT_OUTPUT = "cms_export_output";

    /**
     * cmscenter导出(接受)
     */
    @Input(CMS_EXPORT_INPUT)
    SubscribableChannel cmsExportInput();

    /**
     * cmscenter导出(发送)，统一定义，希望导出统一，不要在创建新的topic
     */
    @Output(CMS_EXPORT_OUTPUT)
    MessageChannel cmsExportOutput();



    /*************************************************************************************************/

    String KUJIALE_DESIGN_SYNC_INPUT="kujiale_design_sync_input";

    @Input(KUJIALE_DESIGN_SYNC_INPUT)
    SubscribableChannel kujialeDesignSyncinput();

//    /**
//     * 消费
//     */
//    String SYNC_CLUES_SCHEME_INPUT = "sync_clues_scheme_input";
//
//    /**
//     * 接收消息
//     */
//    @Input(SYNC_CLUES_SCHEME_INPUT)
//    MessageChannel syncCluesSchemeConsume();

    String AI_MATERIAL_EXPORT_INPUT = "ai_material_export_input";

    String AI_MATERIAL_EXPORT_OUTPUT = "ai_material_export_output";

    /**
     * AI素材列表导出 接收
     */
    @Input(AI_MATERIAL_EXPORT_INPUT)
    SubscribableChannel aiMaterialExportInput();

    /**
     * AI素材列表导出 发送
     */
    @Output(AI_MATERIAL_EXPORT_OUTPUT)
    MessageChannel aiMaterialExportOutput();

    /**=================酷家乐消息==========================*/
    String KUJIALE_EXPORT_OUTPUT = "kujiale_export_output";

    /**
     * 酷家乐消息 发送user
     */
    @Output(KUJIALE_EXPORT_OUTPUT)
    MessageChannel kujialeExportOutput();


    /**====================生产消息，customer-center消费，同步人群包信息===================================================*/
    String SYNCTOCUSTOMER_CDP_COMMON_GROUP_OUTPUT = "synctocustomer_cdp_common_group_output";
    /**
     * 同步customer-center人群包 发送
     */
    @Output(SYNCTOCUSTOMER_CDP_COMMON_GROUP_OUTPUT)
    MessageChannel synctocustomerCdpOutput();



}
