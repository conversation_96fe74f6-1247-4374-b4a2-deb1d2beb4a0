package com.fotile.cmscenter.compound.pojo.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 幸福大院动态纪念日实体
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.compound.pojo.dto.dto
 * @date 2020/3/26 11:08
 * @copyright ©2019-2029 方太集团·宁波方太营销有限公司
 */
@Data
@ApiModel(value = "幸福大院动态纪念日实体", description = "幸福大院动态纪念日输出实体")
public class CompoundMemorialDayVo {
    /**
     * 记录ID
     **/
    @ApiModelProperty(value = "记录ID")
    private Long id;

    /**
     * 动态ID(compound_information.id)
     **/
    @ApiModelProperty(value = "动态ID(compound_information.id)")
    private Long compoundId;

    /**
     * 纪念日名称
     **/
    @ApiModelProperty(value = "纪念日名称")
    private String memorialName;

    /**
     * 纪念日
     **/
    @ApiModelProperty(value = "纪念日")
    private String memorialDay;

    /**
     * 纪念日id
     **/
    @ApiModelProperty(value = "纪念日ID")
    private Long memorialId;
}