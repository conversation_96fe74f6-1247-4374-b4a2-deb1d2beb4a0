package com.fotile.cmscenter.compound.xxljob;

import com.fotile.cmscenter.compound.dao.CompoundDao;
import com.fotile.cmscenter.compound.dao.THomeReferralPoolDao;
import com.fotile.cmscenter.compound.pojo.dto.THomeReferralPoolVo;
import com.fotile.cmscenter.compound.pojo.dto.vo.CompoundInfoPictureVo;
import com.fotile.cmscenter.compound.pojo.entity.THomeReferralPool;
import com.fotile.cmscenter.compound.service.THomeReferralPoolService;
import com.fotile.cmscenter.content.dao.ContentDao;
import com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO;
import com.fotile.cmscenter.util.SplitUtils;
import com.fotile.cmscenter.weight.dao.TWeightInfoDao;
import com.fotile.cmscenter.weight.pojo.entity.TWeightInfo;
import com.fotile.cmscenter.weight.pojo.entity.TWeightInfoInDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UpdateHomeReferralPoolSyncStatusJob extends IJobHandler {
    @Autowired
    private THomeReferralPoolDao tHomeReferralPoolDao;
    @Autowired
    private CompoundDao compoundDao;
    @Autowired
    private TWeightInfoDao tWeightInfoDao;
    @Autowired
    private ContentDao contentDao;
    @Autowired
    private THomeReferralPoolService tHomeReferralPoolService;

    @Override
    @XxlJob(value = "UpdateHomeReferralPoolSyncStatusJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("更新幸福家首页推荐池基础得分开始");
        updateHomeReferralPoolSyncStatus(param);
        XxlJobLogger.log("更新幸福家首页推荐池基础得分结束");
        return ReturnT.SUCCESS;
    }

    private void updateHomeReferralPoolSyncStatus(String param) {
        //查询动态配置
        TWeightInfoInDto tWeightInfo = new TWeightInfoInDto();
        tWeightInfo.setOffset(0L);
        tWeightInfo.setSize(Integer.MAX_VALUE);
        List<TWeightInfo> tWeightInfos = this.tWeightInfoDao.queryAllByLimit(tWeightInfo);

        //查询需要同步基础得分的首页推荐池数据
        List<THomeReferralPoolVo> homeReferralPoolVoList = tHomeReferralPoolDao.selectHomeReferralPoolList(param);
        if (CollectionUtils.isNotEmpty(homeReferralPoolVoList)){
            //查询对应的动态列表数据
            List<String> compoundIdList = homeReferralPoolVoList.stream().map(h -> h.getCompoundId().toString()).collect(Collectors.toList());
            List<List<?>> lists = SplitUtils.splitList(compoundIdList, 100);
            for (List<?> list : lists) {
                List<String> idList = (List<String>) list;
                List<THomeReferralPool> homeReferralPoolList = compoundDao.selectByCompoundIds(idList);
                List<ContentQueryDTO> contentList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(homeReferralPoolList)) {
                    List<Long> contentIds = homeReferralPoolList.stream().filter(p -> "content".equals(p.getContentTableName())).map(THomeReferralPool::getContentId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(contentIds)){
                        contentList = contentDao.findContentsByIds(contentIds);
                    }
                    //图片
                    List<Long> compoundIds = homeReferralPoolList.stream().map(THomeReferralPool::getCompoundId).collect(Collectors.toList());
                    Map<Long, List<CompoundInfoPictureVo>> picListMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(compoundIds)) {
                        List<CompoundInfoPictureVo> picList = compoundDao.getPicList(compoundIds);
                        if (!org.springframework.util.CollectionUtils.isEmpty(picList)) {
                            picListMap = picList.stream().collect(Collectors.groupingBy(CompoundInfoPictureVo::getSourceId));
                        }
                    }
                    List<THomeReferralPool> updateHomeReferralPoolList = new ArrayList<>();
                    //计算基础得分
                    for (THomeReferralPool homeReferralPool : homeReferralPoolList) {
                        List<CompoundInfoPictureVo> pics = new ArrayList<>();
                        if (picListMap != null && picListMap.size() > 0) {
                            pics = picListMap.get(homeReferralPool.getCompoundId());
                        }
                        //开始计算
                        BigDecimal score = tHomeReferralPoolService.getScore(homeReferralPool,tWeightInfos,pics,contentList);
                        //最终得分
                        homeReferralPool.setBaseScore(score);
                        Optional<THomeReferralPoolVo> first1 = homeReferralPoolVoList.stream().filter(h -> homeReferralPool.getCompoundId().equals(h.getCompoundId())).findFirst();
                        if (first1.isPresent()) {
                            homeReferralPool.setId(first1.get().getId());
                            updateHomeReferralPoolList.add(homeReferralPool);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updateHomeReferralPoolList)) {
                        tHomeReferralPoolDao.updateBatchBaseScore(updateHomeReferralPoolList);
                    }
                } else {
                    log.error("未查询到动态:" + compoundIdList);
                }
            }
        }else {
            log.error("没有需要更新的数据:"+homeReferralPoolVoList);
        }
    }


}
