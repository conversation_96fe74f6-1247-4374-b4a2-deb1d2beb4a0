package com.fotile.cmscenter.compound.dao;

import com.fotile.cmscenter.compound.pojo.dto.*;
import com.fotile.cmscenter.compound.pojo.dto.vo.CompoundInfoPictureVo;
import com.fotile.cmscenter.compound.pojo.dto.vo.CompoundInfoVo;
import com.fotile.cmscenter.compound.pojo.dto.vo.CompoundLabelVo;
import com.fotile.cmscenter.compound.pojo.dto.vo.CompoundMemorialDayVo;
import com.fotile.cmscenter.compound.pojo.entity.THomeReferralPool;
import com.fotile.cmscenter.yard.dto.SetBestInDto;
import com.fotile.cmscenter.yard.dto.SetLabelsInDto;
import com.fotile.cmscenter.yard.dto.SetRecommendPoolInDto;
import com.fotile.cmscenter.yard.dto.tag;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.compound.dao
 * @date 2020/3/25 13:17
 * @copyright ©2019-2029 方太集团·宁波方太营销有限公司
 */
public interface CompoundDao {
    /*
     * 查询幸福大院动态列表
     * */
    List<CompoundInfoVo> queryCompoundInfo(QueryCompoundInfoDto query);

    Integer queryCompoundInfoCount(QueryCompoundInfoDto query);

    /*
     * 获取幸福大院动态详情
     * */
    CompoundInfoVo getCompoundInfo(@Param("id") Long id);

    /*
     * 幸福大院动态上下线操作
     * */
    Long updateCompoundOnlineStatus(@Param("id") Long id, @Param("online_status") Integer online_status, @Param("modifiedBy") String modifiedBy);

    /*
     * 获取幸福大院动态图片列表
     * */
    List<CompoundInfoPictureVo> getCompoundInfoPictures(@Param("id") Long id);

    /*
     * 获取幸福大院动态纪念日列表
     * */
    List<CompoundMemorialDayVo> getCompoundInfoMemorialDays(@Param("id") Long id);

    /*
     * 查询幸福大院动态标签列表
     * */
    List<CompoundLabelVo> queryCompoundLabel(QueryCompoundLabelDto query);

    Integer queryCompoundLabelCount(QueryCompoundLabelDto query);

    /*
     * 检查标签名称是否存在
     * */
    Long existsCompoundLabelName(@Param("tag_name") String tag_name);

    /*
     * 新增动态标签
     * */
    Long insertCompoundLabel(EditCompoundLabelDto edit);

    /*
     * 编辑动态标签
     * */
    Long updateCompoundLabel(EditCompoundLabelDto edit);

    /*
     * 幸福大院动态标签上下线操作
     * */
    Long updateCompoundLabelOnlineStatus(@Param("id") Long id, @Param("online_status") Integer online_status, @Param("modifiedBy") String modifiedBy);

    /*
     * 幸福大院动态标签排序操作
     * */
    Long updateCompoundLabelSort(@Param("id") Long id, @Param("sort_value") Integer sort_value, @Param("modifiedBy") String modifiedBy);

    List<CompoundInfoPictureVo> getPicList(@Param("ids") List<Long> ids);


    List<CompoundLabelVo> getTagList(@Param("ids") List<Long> ids);

    List<tag> getLabels(@Param("id") Long id);

    void delLabels(@Param("id") Long id);

    void addLabels(SetLabelsInDto setLabelsInDto);

    void setBest(SetBestInDto setBestInDto);

    void setRecommendPool(SetRecommendPoolInDto setRecommendPoolInDto);

    Integer setRecommond(SetBestInDto setBestInDto);

    List<CompoundIdContent> getCompoundIdContent(@Param("ids") List<Long> ids);

    List<CompoundIdContentDto> getCompoundIdsContent(@Param("ids") List<String> ids);

    int batchOffline(SetBestInDto setBestInDto);

    List<THomeReferralPool> selectByCompoundIds(@Param("ids") List<String> compoundIdList);
}
