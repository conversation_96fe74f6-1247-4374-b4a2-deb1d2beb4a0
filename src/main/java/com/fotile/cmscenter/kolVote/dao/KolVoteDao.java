package com.fotile.cmscenter.kolVote.dao;

import com.fotile.cmscenter.kolVote.pojo.dto.KolVoterDto;
import com.fotile.cmscenter.kolVote.pojo.vo.KolVO;
import org.apache.ibatis.annotations.Param;


public interface KolVoteDao {
    Integer queryTotalVotes();

    KolVO queryVotes(@Param("kolId") Long id);

    Integer updateKolNumById(@Param("kolId") Long id);

    void insertVoter(KolVoterDto voter);


    KolVO queryVoterByPhone(@Param("voterPhone")String voterPhone);
}
