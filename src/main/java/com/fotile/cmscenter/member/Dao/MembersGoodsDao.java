package com.fotile.cmscenter.member.Dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.member.pojo.MembersGoods;
import com.fotile.cmscenter.member.pojo.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MembersGoodsDao extends BaseMapper<MembersGoods> {

    /**
     * 查询会员商品信息
     *
     * @param id 会员商品ID
     * @return 会员商品信息
     */
     MemberGoodsOutDto selectMembersGoodsById(Long id);

    /**
     * 查询会员商品列表
     *
     * @param inDto 会员商品信息
     * @return 会员商品集合
     */
     List<MemberGoodsOutDto> selectMembersGoodsListByParam(MembersGoodsInDto inDto);

     Integer selectMembersGoodsCount(MembersGoodsInDto inDto);


    /**
     * 修改会员商品
     *
     * @param inDto 会员商品信息
     * @return 结果
     */
     int updateMembersGoods(SaveAndUpdateInDto inDto);

    /**
     * 修改商品状态
     *
     * @param membersGoods 会员商品信息
     * @return 结果
     */
    int updateMembersGoodsStatus(MembersGoods membersGoods);


    List<MemberGoodsOutDto>  selectMembersGoodsByIds(@Param("ids") List<Long> ids);


    Integer setSort(MembersGoodsSortInDto inDto);

    List<MemberGoodsOutDto> selectMembersGoodsList(MembersGoodsInDto inDto);

    List<MemberGoodsOutDto> selectMembersGoods(QueryMembersGoodsInDto inDto);
}
