package com.fotile.cmscenter.member.pojo.dto;

import com.fotile.cmscenter.member.pojo.MembersGoodsActivityPrice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class MemberGoodsOutDto {
    private Long id;
    /** 商品名称 */
    @ApiModelProperty(name = "商品名称")
    private String goodsName;
    /** 商品类型 1一次性购买2可重复购买 */
    @ApiModelProperty(name = "商品类型 1一次性购买2可重复购买")
    private String goodsType;
    /** 标题 */
    @ApiModelProperty(name = "标题")
    private String title;
    /** 副标题 */
    @ApiModelProperty(name = "副标题")
    private String subTitle;
    /** 状态  1：上架 0：下架 */
    @ApiModelProperty(name = "状态  1：上架 0：下架")
    private Integer stage;
    /** 序号 */
    @ApiModelProperty(name = "序号")
    private Integer sort;
    /** 商品图片 */
    @ApiModelProperty(name = "商品图片")
    private String goodsImgUrl;
    @ApiModelProperty(name = "商品备注")
    private String remark;
    /** 商品描述 */
    @ApiModelProperty(name = "商品描述")
    private String description;
    /** 划线价 */
    @ApiModelProperty(name = "划线价")
    private BigDecimal linePrice;
    /** 商品价格 */
    @ApiModelProperty(name = "商品价格")
    private BigDecimal realPrice;
    /** 所属渠道 */
    @ApiModelProperty(name = "所属渠道")
    private String channelCode;
    /** 有限开始时间 */
    @ApiModelProperty(name = "有限开始时间")
    private Date validStartTime;
    /** 有限结束时间 */
    @ApiModelProperty(name = "有限结束时间")
    private Date validEndTime;
    /** VIP会员天数 */
    @ApiModelProperty(name = "VIP会员天数")
    private Integer vipDay;

    private String goodsPurpose;

    private String usePlatform;

    private Date createdDate;

    private Date modifiedDate;

    private List<MembersGoodsActivityPrice> activityPrices;

    //是否积分兑换：0：否；1：是
    private Integer isPointExchange;
    //积分兑换数额
    private Long point;
}
