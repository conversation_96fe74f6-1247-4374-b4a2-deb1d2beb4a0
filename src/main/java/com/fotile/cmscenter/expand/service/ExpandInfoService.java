package com.fotile.cmscenter.expand.service;

import com.fotile.cmscenter.client.OrgClient;
import com.fotile.cmscenter.client.pojo.FindChannelUrlByIdOutDto;
import com.fotile.cmscenter.common.CurrentUser;
import com.fotile.cmscenter.content.dao.ContentOperatorLogDao;
import com.fotile.cmscenter.content.dao.LabelCmsDao;
import com.fotile.cmscenter.content.dao.LabelCmsMappingDao;
import com.fotile.cmscenter.content.pojo.ContentOperatorLog;
import com.fotile.cmscenter.content.pojo.LabelCms;
import com.fotile.cmscenter.content.pojo.LabelCmsMapping;
import com.fotile.cmscenter.content.pojo.vo.LabelVto;
import com.fotile.cmscenter.expand.dao.CourseMarkUserDao;
import com.fotile.cmscenter.expand.dao.ExpandInfoDao;
import com.fotile.cmscenter.expand.dao.ExpandSignUpDao;
import com.fotile.cmscenter.expand.dto.*;
import com.fotile.cmscenter.expand.dto.mapper.ExpandMapper;
import com.fotile.cmscenter.expand.entity.ExpandInfo;
import com.fotile.cmscenter.label.dto.AddLabelDto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 修炼营表(XExpandInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-05 13:27:13
 */

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class ExpandInfoService {
    @Autowired
    private ExpandInfoDao expandInfoDao;
    @Autowired
    private CourseCatalogService courseCatalogService;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private ExpandSignUpDao expandSignUpDao;
    @Autowired
    private CourseMarkUserDao courseMarkUserDao;
    @Autowired
    private ContentOperatorLogDao logDao;
    @Autowired
    private LabelCmsMappingDao labelCmsMappingDao;
    @Autowired
    private LabelCmsDao labelCmsDao;
    @Autowired
    private CurrentUser currentUser;

    /**
     * 通过ID查询单条数据
     */
    public ExpandInfoDto queryById(Long id) {
        ExpandInfo expandInfo = expandInfoDao.queryById(id);
        ExpandInfoDto expandInfoOutDto = ExpandMapper.INSTANCE.expandInfoToOutDto(expandInfo);
        List<CourseCatalogOutDto> courseCatalogOutDtos = courseCatalogService.selectByExpandId(expandInfoOutDto.getId());
        expandInfoOutDto.setCourseCatalog(courseCatalogOutDtos);
        List<LabelVto> labelVtos = labelCmsMappingDao.findLabelInfoBySourceTableAndSourceId("x_expand_info", id);
        if (!CollectionUtils.isEmpty(labelVtos)){
            List<String> names = labelVtos.stream().map(l -> l.getName()).collect(Collectors.toList());
            expandInfoOutDto.setLabel(StringUtils.join(names,","));
        }
        return expandInfoOutDto;
    }


    /**
     * 分页查询
     */
    public PageInfo<ExpandInfoListOutDto> queryByPage(ExpandInfoListInDto inDto) {
        PageInfo<ExpandInfoListOutDto> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        Long count = expandInfoDao.queryAllByCount(inDto);
        List<ExpandInfoListOutDto> expandInfoList = new ArrayList<>();
        if (count > 0 ) {
            inDto.setOffset(pageInfo.getOffset());
            expandInfoList = expandInfoDao.queryAllByLimit(inDto);
            if (!CollectionUtils.isEmpty(expandInfoList)){
                List<Long> ids = expandInfoList.stream().map(e -> e.getId()).collect(Collectors.toList());
                //根据修炼营id查询报名人数
                List<QuerySignUpListDto> enrollmentByExpandIds = expandSignUpDao.queryEnrollmentByExpandIds(ids);
                List<QuerySignUpListDto> challengeNumByExpandIds = expandSignUpDao.queryChallengeNumByExpandIds(ids);
                for (ExpandInfoListOutDto expandInfo : expandInfoList){
                    expandInfo.setEnrollment(0);
                    expandInfo.setChallengeSuccessNum(0);
                    expandInfo.setChallengeFailureNum(0);
                    Optional<QuerySignUpListDto> first = enrollmentByExpandIds.stream().filter(s -> expandInfo.getId().equals(s.getExpandInfoId())).findFirst();
                    if (first.isPresent()){
                        expandInfo.setEnrollment(first.get().getEnrollment());
                    }
                    if (!CollectionUtils.isEmpty(challengeNumByExpandIds)) {
                        List<QuerySignUpListDto> signUpListDtos = challengeNumByExpandIds.stream().filter(m -> expandInfo.getId().equals(m.getExpandInfoId())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(signUpListDtos)) {
                            Integer challengeSuccessNum = signUpListDtos.stream().filter(s -> s.getStatus() != null && !s.getStatus().equals(0)).collect(Collectors.toList()).size();
                            Integer challengeFailureNum = signUpListDtos.stream().filter(s -> s.getStatus() != null && s.getStatus().equals(0)).collect(Collectors.toList()).size();
                            expandInfo.setChallengeSuccessNum(challengeSuccessNum);
                            expandInfo.setChallengeFailureNum(challengeFailureNum);
                        }
                    }

                }
            }
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(expandInfoList);
        return pageInfo;
    }

    /**
     * 新增数据
     */
    public ExpandInfoDto addOrEdit(ExpandInfoDto expandInfoDto) {
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        expandInfoDto.setCreatedBy(userAuthor.getUserId());
        expandInfoDto.setModifiedBy(userAuthor.getUserId());
        if (expandInfoDto.getId() != null){
            ExpandInfo expandInfo = expandInfoDao.queryById(expandInfoDto.getId());
            if (expandInfoDto.getOnlineStatus() == 1 && !expandInfo.getOnlineStatus().equals(expandInfoDto.getOnlineStatus())){
                expandInfoDto.setOnlineTime(new Date());
            }
            expandInfoDao.update(expandInfoDto);
            courseCatalogService.updateBatch(expandInfoDto);
            //记录日志
            insertOperatorLog("x_expand_info", expandInfoDto.getId(), userAuthor, expandInfo, expandInfoDto);
        }else {
            expandInfoDao.insert(expandInfoDto);
            courseCatalogService.insertBatch(expandInfoDto);
            String code = expandInfoDto.getId().toString();
            if (code.length() < 2){
                code = "X00"+code;
            }else if (code.length() < 3){
                code = "X0"+code;
            }else {
                code = "X"+code;
            }
            expandInfoDao.updateCode(code,expandInfoDto.getId());
        }

        saveLabelCmsMapping("x_expand_info",expandInfoDto.getId(),expandInfoDto.getLabel(),userAuthor);

        return expandInfoDto;
    }

    public void saveLabelCmsMapping(String sourceTable, Long sourceId,String label,UserAuthor userAuthor) {
        //清除已存在的内容关键字关系
        labelCmsMappingDao.updateBySourceTableAndSourceId(sourceTable, sourceId, currentUser.getCurrentUserId());
        if (!StringUtils.isEmpty(label)) {
            List<String> labelLsit = Arrays.asList(label.split(","));
            List<LabelCms> labelCmsByNames = labelCmsDao.findLabelCmsByNames(labelLsit);
            List<LabelCmsMapping> labelCmsMappings = new ArrayList<>();
            List<AddLabelDto> insetLabel = new ArrayList<>();
            for (String name : labelLsit){
                Optional<LabelCms> first = labelCmsByNames.stream().filter(l -> name.equals(l.getName())).findFirst();
                if (first.isPresent()){
                    LabelCmsMapping labelCmsMapping = new LabelCmsMapping();
                    labelCmsMapping.setSourceId(sourceId);
                    labelCmsMapping.setSourceTableName(sourceTable);
                    labelCmsMapping.setIsDeleted(0L);
                    labelCmsMapping.setLabelId(first.get().getId());
                    labelCmsMapping.setCreatedBy(userAuthor.getUserId());
                    labelCmsMapping.setModifiedBy(userAuthor.getUserId());

                    labelCmsMappings.add(labelCmsMapping);
                }else {
                    AddLabelDto labelCms = new AddLabelDto();
                    labelCms.setName(name);
                    labelCms.setSourceTableName(sourceTable);
                    labelCms.setModifiedPerson(MybatisMateConfig.encrypt(userAuthor.getUsername()));
                    labelCms.setModifiedBy(userAuthor.getUserId());
                    labelCms.setModifiedDate(new Date());
                    String code = UUID.randomUUID().toString();
                    labelCms.setCode(code);
                    labelCms.setCreatedBy(userAuthor.getUserId());
                    labelCms.setCreatedDate(new Date());
                    insetLabel.add(labelCms);
                }
            }
            if (!CollectionUtils.isEmpty(insetLabel)) {
                labelCmsDao.insertBatch(insetLabel);
                for (AddLabelDto labelCms : insetLabel){
                    LabelCmsMapping labelCmsMapping = new LabelCmsMapping();
                    labelCmsMapping.setSourceId(sourceId);
                    labelCmsMapping.setSourceTableName(sourceTable);
                    labelCmsMapping.setIsDeleted(0L);
                    labelCmsMapping.setLabelId(labelCms.getId());
                    labelCmsMapping.setCreatedBy(userAuthor.getUserId());
                    labelCmsMapping.setModifiedBy(userAuthor.getUserId());

                    labelCmsMappings.add(labelCmsMapping);
                }
            }

            labelCmsMappingDao.insertBatch(labelCmsMappings);
        }
    }

    public void insertOperatorLog(String sourceTable, Long sourceId, UserAuthor userAuthor, ExpandInfo oldExpandInfoDto, ExpandInfoDto newExpandInfoDto) {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String description = "";
        if (!oldExpandInfoDto.getType().equals(newExpandInfoDto.getType())){
            String oldtypename = oldExpandInfoDto.getType() == 0 ? "个人幸福力" : "家庭幸福力";
            String newtypename = newExpandInfoDto.getType() == 0 ? "个人幸福力" : "家庭幸福力";
            description = "类型由" + oldtypename + "变更为" + newtypename + ";";
        }
        if (!oldExpandInfoDto.getYardInfoId().equals(newExpandInfoDto.getYardInfoId())){
            description = description + "关联院子id由" + oldExpandInfoDto.getYardInfoId() + "变更为" + newExpandInfoDto.getYardInfoId() + ";";
        }
        if (!oldExpandInfoDto.getTitle().equals(newExpandInfoDto.getTitle())){
            description = description + "标题由" + oldExpandInfoDto.getTitle() + "变更为" + newExpandInfoDto.getTitle() + ";";
        }
        if (!oldExpandInfoDto.getTakingClassesTime().equals(newExpandInfoDto.getTakingClassesTime())){
            description = description + "开课时间由" + sd.format(oldExpandInfoDto.getTakingClassesTime()) + "变更为" + sd.format(newExpandInfoDto.getTakingClassesTime()) + ";";
        }
        if (!oldExpandInfoDto.getSignUpEndTime().equals(newExpandInfoDto.getSignUpEndTime())){
            description = description + "报名截止时间由" + sd.format(oldExpandInfoDto.getSignUpEndTime()) + "变更为" + sd.format(newExpandInfoDto.getSignUpEndTime()) + ";";
        }
        if (!oldExpandInfoDto.getMarkNum().equals(newExpandInfoDto.getMarkNum())){
            description = description + "可打卡天数由" + oldExpandInfoDto.getMarkNum() + "变更为" + newExpandInfoDto.getMarkNum() + ";";
        }


        if (StringUtils.isNotBlank(description)) {
            ContentOperatorLog contentLog = new ContentOperatorLog();
            contentLog.setSourceTableName(sourceTable);
            contentLog.setSourceId(sourceId);
            contentLog.setDescription(sd.format(new Date()) + description);
            contentLog.setOperatorType(1L);
            if (StringUtils.isNotBlank(userAuthor.getUsername())) {
                contentLog.setCreatedByName(MybatisMateConfig.encrypt(userAuthor.getUsername()));
            }
            contentLog.setCreatedBy(userAuthor.getId());
            contentLog.setCreatedDate(new Date());
            contentLog.setModifiedBy(userAuthor.getId());
            contentLog.setModifiedDate(new Date());
            contentLog.setIsDeleted((long) 0);
            logDao.insert(contentLog);
        }
    }

    /**
     * 上下线
     */
    public int updateStatus(UpdateStatusDto updateStatusDto) {
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        updateStatusDto.setModifiedBy(userAuthor.getUserId());

        ExpandInfo expandInfo = expandInfoDao.queryById(updateStatusDto.getId());
        if (updateStatusDto.getOnlineStatus() == 1 && !expandInfo.getOnlineStatus().equals(updateStatusDto.getOnlineStatus())){
            updateStatusDto.setOnlineTime(new Date());
        }
        return expandInfoDao.updateStatus(updateStatusDto);
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.expandInfoDao.deleteById(id) > 0;
    }

    /**
     * 排序
     */
    public int updateSort(UpdateStatusDto updateStatusDto) {
        UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        updateStatusDto.setModifiedBy(userAuthor.getUserId());
        return expandInfoDao.updateStatus(updateStatusDto);
    }


    public List<FindChannelUrlByIdOutDto> findChannelUrlList(Long id) {

        List<FindChannelUrlByIdOutDto> channelUrlByIdOutDtos = orgClient.findByType(null, 6, "6-1").getData();
        if (channelUrlByIdOutDtos != null && channelUrlByIdOutDtos.size() > 0) {
            channelUrlByIdOutDtos.stream().forEach(x -> {
                x.setUrl(x.getUrl() + id);
            });
        }
        return channelUrlByIdOutDtos;
    }


}
