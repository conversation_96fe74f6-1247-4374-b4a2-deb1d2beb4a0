package com.fotile.cmscenter.cases.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.cases.pojo.entity.ContentCaseChangingImage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContentCaseChangingImageDao extends BaseMapper<ContentCaseChangingImage> {
    int deleteByPrimaryKey(Long id);

    int insert(ContentCaseChangingImage record);

    int insertSelective(ContentCaseChangingImage record);

    ContentCaseChangingImage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentCaseChangingImage record);

    int updateByPrimaryKey(ContentCaseChangingImage record);

    void insertBatch(List<ContentCaseChangingImage> changingImages);

    List<ContentCaseChangingImage> selectByCaseId(@Param("caseId") Long caseId);

    List<ContentCaseChangingImage> selectByCaseIds(@Param("caseIds") List<Long> caseIds);

}