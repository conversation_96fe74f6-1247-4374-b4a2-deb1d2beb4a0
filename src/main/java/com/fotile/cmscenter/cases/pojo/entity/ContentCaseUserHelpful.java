package com.fotile.cmscenter.cases.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户案例点赞表(cmscenter.content_case_user_helpful)实体类
 *
 * <AUTHOR>
 * @since 2023-12-13 19:25:24
 */
@Data
@TableName(value = "content_case_user_helpful", schema = "cmscenter")
public class ContentCaseUserHelpful implements Serializable {

    private static final long serialVersionUID = -86043625678897992L;

    /**
     * 主键id
     */
    @Id
    @TableField(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 用户类型 1：业务员 2：顾客id (customer_id) 3:设计师俱乐部小程序
     */
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * 内容id
     */
    @TableField(value = "content_id")
    private Long contentId;

    private Integer contentType;

    /**
     * 是否点赞 0否 1是
     */
    @TableField(value = "is_helpful")
    private Integer isHelpful;

    /**
     * 是否删除 0否 1是
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "created_date")
    private Date createdDate;

    @TableField(value = "modified_by")
    private String modifiedBy;

    @TableField(value = "modified_date")
    private Date modifiedDate;
}

