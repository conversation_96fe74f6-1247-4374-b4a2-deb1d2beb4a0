package com.fotile.cmscenter.cases.pojo.dto;

import com.fotile.framework.web.BusinessException;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@Data
public class GetProductListMappingCaseInDTO {

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer pageNum;

    /**
     * 每页显示大小
     */
    @NotNull(message = "页码显示条数不能为空")
    private Integer pageSize;

    /**
     * 模糊搜索关键字
     */
    private String matchContent;

}
