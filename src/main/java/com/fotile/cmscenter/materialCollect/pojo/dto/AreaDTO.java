package com.fotile.cmscenter.materialCollect.pojo.dto;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.fotile.cmscenter.materialCollect.pojo.entity.MaterialCollectAuditLog;
import com.fotile.cmscenter.materialCollect.pojo.entity.MaterialCollectProduct;
import com.fotile.cmscenter.materialCollect.pojo.entity.MaterialContentCollect;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 补填大区字段
*/
@Data
public class AreaDTO implements Serializable {

    /**
    *  大区code
    */
    private String areaCode;
    /**
     * 大区名称
     */
    private String areaName;
}


