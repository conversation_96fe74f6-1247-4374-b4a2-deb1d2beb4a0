package com.fotile.cmscenter.graph.service;

import com.alibaba.nacos.api.exception.NacosException;
import com.fotile.framework.client.KeycloakAdminClient;
import com.fotile.framework.client.response.AccessToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MqGetToken {

    @Autowired
    private KeycloakAdminClient keycloakAdminClient;


    public String getStringStringMap(String path) {
        AccessToken adminAccessToken = null;
        try {
            adminAccessToken = keycloakAdminClient.getAdminAccessToken(path);
            if (adminAccessToken != null) {
                return "Bearer " + adminAccessToken.getToken();
            }
        } catch (NacosException e) {
            log.error("mq获取token失败" + e.getMessage());
        }
        return null;
    }
}
