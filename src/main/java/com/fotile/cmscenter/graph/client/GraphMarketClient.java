package com.fotile.cmscenter.graph.client;


import com.fotile.cmscenter.graph.pojo.dto.AddBuyingGuideDto;
import com.fotile.framework.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * @ClassName GraphMarketClient
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/08/15
 * @Version
 **/
@FeignClient(value = "marketing-center", path = "/api")
public interface GraphMarketClient {

    /**
     * 根据产品id集合查询产品信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/open/buyingGuide/add")
    Result<String> add(AddBuyingGuideDto dto);
}
