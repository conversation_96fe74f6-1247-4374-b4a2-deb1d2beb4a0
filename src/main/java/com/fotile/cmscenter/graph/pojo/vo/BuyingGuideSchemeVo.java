package com.fotile.cmscenter.graph.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 厨房选购指南VO
 */
@Data
public class BuyingGuideSchemeVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "顾客姓名")
    private String customerName;

    @ApiModelProperty(value = "顾客手机号")
    private String phone;

    @ApiModelProperty(value = "顾客全路径地址")
    private String customerAddress;

    @ApiModelProperty(value = "顾客小区地址")
    private String villageName;

    @ApiModelProperty(value = "业务员id")
    private String saleManId;

    @ApiModelProperty(value = "业务员名称")
    private String saleManName;

    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "总预算金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "分享图")
    private String shareImage;

    @ApiModelProperty(value = "方案集合")
    private List<SchemeVo> schemeVoList;



}
