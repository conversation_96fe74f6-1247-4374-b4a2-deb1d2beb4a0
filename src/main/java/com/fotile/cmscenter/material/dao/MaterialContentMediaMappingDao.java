package com.fotile.cmscenter.material.dao;

import com.fotile.cmscenter.material.pojo.entity.MaterialContentMediaMapping;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.parameters.P;

import java.util.List;

/**
 * 聊天素材内容关联(MaterialContentMediaMapping)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-05 11:40:32
 */
public interface MaterialContentMediaMappingDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MaterialContentMediaMapping queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param tMaterialContentMediaMapping 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<MaterialContentMediaMapping> queryAllByLimit(MaterialContentMediaMapping tMaterialContentMediaMapping, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param tMaterialContentMediaMapping 查询条件
     * @return 总行数
     */
    long count(MaterialContentMediaMapping tMaterialContentMediaMapping);

    /**
     * 新增数据
     *
     * @param tMaterialContentMediaMapping 实例对象
     * @return 影响行数
     */
    int insert(MaterialContentMediaMapping tMaterialContentMediaMapping);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<MaterialContentMediaMapping> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<MaterialContentMediaMapping> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<MaterialContentMediaMapping> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<MaterialContentMediaMapping> entities);

    /**
     * 修改数据
     *
     * @param tMaterialContentMediaMapping 实例对象
     * @return 影响行数
     */
    int update(MaterialContentMediaMapping tMaterialContentMediaMapping);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<MaterialContentMediaMapping> queryByMaterialContentId(@Param("materialContentId") Integer materialContentId);

    int updateContentMedia(MaterialContentMediaMapping contentMediaMapping);

    MaterialContentMediaMapping selectByMaterialId(@Param("materialContentId") Integer materialContentId,
                                                   @Param("accountId") String accountId);

    List<MaterialContentMediaMapping> queryByMaterialContentIds(@Param("ids") List<Integer> ids);
}

