package com.fotile.cmscenter.material.dao;


import com.fotile.cmscenter.material.pojo.dto.*;
import com.fotile.cmscenter.material.pojo.entity.MaterialEventLog;
import com.fotile.cmscenter.scheme.pojo.dto.SchemeMaterialStatisticalDTO;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MaterialEventLogMapper extends MPJBaseMapper<MaterialEventLog> {

    Long findPageCount(MaterialEventLogQueryInputDTO dto);
    List<MaterialEventLogQueryOutputDTO> findPage(MaterialEventLogQueryInputDTO dto);

    MaterialEventLogCountDTO findSummaryCount(MaterialEventLogCountQueryDTO dto);

    /** 统计发送数量
     * @param dto
     * @return
     */
   List<MaterialEventLogQueryOutputDTO> queryRelayCount(MaterialEventLogQueryInputDTO dto);

    Long queryShareCountBySpecialIdAndUserId(@Param("specialId") Long specialId,@Param("salesmanId") Long salesmanId);

    List<MaterialEventLogDataOutDto> findCaseCountByCaseIds(@Param("relationParentIdList") List<Long> relationParentIdList ,
                                                            @Param("bizType") Integer bizType,
                                                            @Param("eventType") Integer eventType);

    List<Long> findCaseByUserId(@Param("relationParentIdList") List<Long> relationParentIdList ,
                                                            @Param("userId") Long userId,@Param("bizType") Integer bizType,
                                                      @Param("eventType") Integer eventType);

    List<SchemeMaterialStatisticalDTO> getSchemeMaterialStatistical(@Param("relationSourceType") Integer relationSourceType, @Param("relationParentIds") List<Long> relationParentIds);
}
