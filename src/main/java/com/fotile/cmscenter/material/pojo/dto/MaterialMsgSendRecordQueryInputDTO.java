package com.fotile.cmscenter.material.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MaterialMsgSendRecordQueryInputDTO implements Serializable {
    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    @NotNull(message = "每页显示条数不能为空")
    private Integer pageSize;

    /**
     * 发送日期
     */
    private String sendDate;

    /**
     * 内容树id
     */
    private Long treeId;

    /**
     * 上新发送记录id，用于过滤
     */
    private Long recordId;
}
