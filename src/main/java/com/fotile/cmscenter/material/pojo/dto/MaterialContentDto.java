package com.fotile.cmscenter.material.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 内容操作实体
 */
@ApiModel("内容操作实体")
@Data
public class MaterialContentDto implements Serializable {


    private static final long serialVersionUID = 7212783510404031671L;
    /**
     * 内容id
     */
    @ApiModelProperty(value = "内容id")
    private Integer materialId;
    /**
     * 收藏状态，默认0未收藏，1表示收藏
     */
    @ApiModelProperty(value = "收藏状态，默认0未收藏，1表示收藏")
    private Integer favStatus;
    /**
     * 分享人账号 发送必填
     */
    @ApiModelProperty(value = "分享人账号")
    private String userAccount;
    /**
     * 账户id
     */
    @ApiModelProperty(value = "账户id")
    private String accountId;
    /**
     * 成员userId
     */
    @ApiModelProperty(value = "成员userId")
    private String userId;
    /**
     * 成员userId
     */
    @ApiModelProperty(value = "成员userId")
    private String qywxUserId;

    /**
     * 分享人姓名 发送必填
     */
    @FieldEncrypt
    @ApiModelProperty(value = "分享人姓名")
    private String userName;

    /**
     * 分享人岗位 发送必填
     */
    @ApiModelProperty(value = "分享人岗位")
    private String station;

    /**
     * 内容标题 发送必填
     */
    @ApiModelProperty(value = "内容标题")
    private String contentTitle;

    /**
     * 内容所属分类 发送必填
     */
    @ApiModelProperty(value = "内容所属分类")
    private String contentTreePath;

    /**
     * 分享目标用户id 发送必填
     */
    @ApiModelProperty(value = "分享目标用户id")
    private String objectUserId;

    /**
     * 分享目标用户id 发送必填
     */
    @ApiModelProperty(value = "分享目标用户id")
    private List<String> objectUserIdList;

    /**
     * 分享目标用户名 发送必填
     */
    @FieldEncrypt
    @ApiModelProperty(value = "分享目标用户名")
    private String objectUserName;

    /**
     * 大区编码 发送必填
     */
    @ApiModelProperty(value = "大区编码")
    private String areaCode;

    /**
     * 大区名 发送必填
     */
    @ApiModelProperty(value = "大区名")
    private String areaName;

    /**
     * 分公司id 发送必填
     */
    @ApiModelProperty(value = "分公司id")
    private Long companyId;

    /**
     * 分公司名称 发送必填
     */
    @ApiModelProperty(value = "分公司名称")
    private String companyName;

    /**
     * 门店id 发送必填
     */
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    /**
     * 门店名称 发送必填
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 有用评价，1有用，0无用
     */
    @ApiModelProperty("有用评价，1有用，0无用")
    private Integer helpfulStatus;

    /**
     * 用户名
     */
    private String firstName;
    /**
     * 专题ID
     */
    private Long specialId;
    /**
     * 业务类型：1-内容工场 2-找案例 3-找产品
     */
    private Integer bizType;
    /**
     * 关联数据类型，4:内容专题
     */
    private Integer relationSourceType;
    /**
     * 内容id
     */
    @ApiModelProperty(value = "内容id")
    private List<Integer> materialIdList;

}
