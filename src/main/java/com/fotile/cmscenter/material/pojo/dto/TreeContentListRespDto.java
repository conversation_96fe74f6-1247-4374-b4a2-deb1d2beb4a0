package com.fotile.cmscenter.material.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 交互助手内容列表
 */
@ApiModel("交互助手内容列表")
@Data
public class TreeContentListRespDto implements Serializable {

    @ApiModelProperty(value = "内容列表")
    private List<TreeContentListDto> treeContentListDtoList;
    /**
     * tab类型
     */
    @ApiModelProperty("0:热门 1：常用 2：节点 节点id")
    private Integer tabNodeId;
    /**
     * tab排序
     */
    @ApiModelProperty("1:上级 2：当前 3：下级")
    private Integer tagSort;
}
