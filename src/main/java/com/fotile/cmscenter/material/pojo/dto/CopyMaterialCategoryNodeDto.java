package com.fotile.cmscenter.material.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class CopyMaterialCategoryNodeDto implements Serializable {
    private static final long serialVersionUID = 1L;

	
	 /**
     * 
     */
	private Integer id;
	
	 /**
     * 聊天素材树id
     */
	private Integer treeId;
	
	 /**
     * 聊天素材树节点名称
     */
	private String nodeName;
	
	 /**
     * 父级节点id
     */
	private Integer parentNodeId;
	
	 /**
     * 节点层级，目前包括1，2，3
     */
	private Integer level;
	
	 /**
     * 是否叶子节点
     */
	private Integer isLeaf;
	
	 /**
     * 排序
     */
	private Integer sort;
	
	 /**
     * 全路径id
     */
	private String fullPathId;
	
	 /**
     * 全路径名称
     */
	private String fullPathName;
	
	 /**
     * 节点图标地址
     */
	private String iconUrl;
	
	 /**
     * 推荐标识，1推荐，0不推荐，缺省为0
     */
	private Integer recommendFlag;
	
	 /**
     * 创建人
     */
	private String createdBy;
	 /**
     * 创建时间
     */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createdDate;
	
	 /**
     * 修改人
     */
	private String modifiedBy;
	
	 /**
     * 最近编辑人
     */
	@FieldEncrypt
	private String modifiedUsername;
	 /**
     * 修改时间
     */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date modifiedDate;

	 /**
     * 逻辑删除状态
     */
	private Integer isDeleted;


	@TableField(exist = false)
	private List<CopyMaterialCategoryNodeDto> categoryNodeDtos;

	@TableField(exist = false)
	private List<MaterialCopyDto> materialList;


}