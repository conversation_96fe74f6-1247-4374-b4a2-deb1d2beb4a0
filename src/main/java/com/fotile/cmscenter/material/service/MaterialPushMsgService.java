package com.fotile.cmscenter.material.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.base.Throwables;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.sql.StringEscape;
import com.fotile.cmscenter.channel.pojo.entity.Goods;
import com.fotile.cmscenter.client.ProductClient;
import com.fotile.cmscenter.client.UserEntityClient;
import com.fotile.cmscenter.client.pojo.UserEntityExtend;
import com.fotile.cmscenter.common.DisposeSendMsgDto;
import com.fotile.cmscenter.material.dao.*;
import com.fotile.cmscenter.material.job.MaterialContentPushNewMsgJob;
import com.fotile.cmscenter.material.mq.MaterialEventMq;
import com.fotile.cmscenter.material.pojo.dto.*;
import com.fotile.cmscenter.material.pojo.entity.Material;
import com.fotile.cmscenter.material.pojo.entity.TMaterialPushBasic;
import com.fotile.cmscenter.material.pojo.entity.TMaterialPushTask;
import com.fotile.cmscenter.material.pojo.entity.TPushBasicSendRecord;
import com.fotile.cmscenter.util.UserAuthorUtil;
import com.fotile.framework.core.common.BaseEntity;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.util.DateUtil;
import com.fotile.framework.util.JsonUtils;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.regex.Match;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/19 11:48
 * 企微内容工场上新消息推送服务
 */
@Slf4j
@Service
@EnableBinding(value = MaterialEventMq.class)
public class MaterialPushMsgService {

    @Autowired
    private MaterialCategoryTreeMapper materialCategoryTreeMapper;

    @Resource(name = MaterialEventMq.MATERIAL_MSG_EVENT_OUTPUT)
    private MessageChannel messageChannel;

    @Autowired
    private MaterialMsgSendRecordMapper materialMsgSendRecordMapper;

    @Autowired
    private MaterialMapper materialMapper;

    @Autowired
    private BasicMaterialService basicMaterialService;

    @Autowired
    private MaterialContentPushNewMsgJob materialContentPushNewMsgJob;
    @Autowired
    private ProductClient productClient;
    @Autowired
    private TMaterialPushTaskDao tMaterialPushTaskDao;
    @Autowired
    private TMaterialPushBasicDaoService materialPushBasicDaoService;
    @Autowired
    private UserEntityClient userEntityClient;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private TPushBasicSendRecordService tPushBasicSendRecordService;
    private static final String str= "[^'\\x22]+";

    /**
     * 手动立即发送上新消息
     * @param dto
     */
    public void pushMaterialNewMsgByHand(MaterialContentPushDTO dto) {
        String errorLog = "";
        if (!CollectionUtils.isEmpty(dto.getMaterialIds())){
            //推送素材上新
            errorLog+=pushMaterial(dto);
        }
        if (!CollectionUtils.isEmpty(dto.getGoodsCodes())){
            //推送内容上新
            errorLog+=pushGoods(dto);
        }
        if (StringUtils.isNotBlank(errorLog)){
            throw new BusinessException(errorLog);
        }
        //if (!CollectionUtils.isEmpty(dto.getMaterialIdList())||!CollectionUtils.isEmpty(dto.getGoodsCodes())){
            sendMessage(dto);
        //}
    }

    private String pushMaterial(MaterialContentPushDTO dto){
        //配置内容素材上新提醒
        if (CollectionUtils.isEmpty(dto.getMaterialIds())){
            return "";
        }
        //对内容id进行校验，非数字类型，超过长度限制等等做异常处理
        List<Long> materialIds = Lists.newArrayList();
        List<String> errorLogList = Lists.newArrayList();
        for (String id: dto.getMaterialIds()) {
            if (!StringUtils.isNumeric(id)){
                errorLogList.add(id);
            }else {
                try {
                    materialIds.add(Long.valueOf(id));
                }catch (Exception e){
                    log.error("输入的id超过Long长度");
                    errorLogList.add(id);
                }
            }
        }
        if (!CollectionUtils.isEmpty(errorLogList)){
            return "分类树内容ID为："+errorLogList+ "不符合上新推送要求，请检查；";
        }

        List<Long> idList = materialIds.stream().distinct().collect(Collectors.toList());
        dto.setMaterialIdList(idList);
        List<Long> materialIdList = materialCategoryTreeMapper.selectSendMsgMaterialIdByHand(dto);
        List<Long> checkMaterialIdList = Lists.newArrayList();
        String errorLog = "";
        if (materialIdList.size() != dto.getMaterialIdList().size()) {
            for (Long materialId : dto.getMaterialIdList()) {
                if (!materialIdList.contains(materialId)) {
                    checkMaterialIdList.add(materialId);
                }
            }
            if (!CollectionUtils.isEmpty(checkMaterialIdList)){
                //校验不符合的内容id组装不同的提醒信息
                errorLog = checkMaterial(checkMaterialIdList, dto.getTreeId());
            }
        }
        dto.setMaterialIds(materialIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        log.error("不符合素材的编码：{}",errorLog);
        return errorLog;
    }

    private String pushGoods(MaterialContentPushDTO dto){
        //配置内容上新提醒
        if (CollectionUtils.isEmpty(dto.getGoodsCodes())){
            return "";
        }
        //查询商品信息是否正确
        GoodsPushInputDto goodsPushInputDto = new GoodsPushInputDto();
        goodsPushInputDto.setCodes(dto.getGoodsCodes());
        goodsPushInputDto.setPushType(dto.getPushType());
        Result<List<Goods>> goodsNewListByGoodsCode = productClient.getGoodsNewListByGoodsCode(goodsPushInputDto);
        if (!goodsNewListByGoodsCode.getSuccess()&& CollectionUtils.isEmpty(goodsNewListByGoodsCode.getData())){
            return "商品编码为："+goodsPushInputDto.getCodes()+ "不存在，请检查；";
        }
        List<String> goodsCodeList = goodsNewListByGoodsCode.getData().stream().map(Goods::getCode).collect(Collectors.toList());
        //查询商品信息是否正确
        String errorLog = "";
        List<String> goodsCodes = dto.getGoodsCodes();
        goodsCodes.removeAll(goodsCodeList);
        if (!CollectionUtils.isEmpty(goodsCodes)){
            errorLog = "商品编码为："+goodsCodes+ "不符合推送要求，请检查；";
        }
        dto.setGoodsCodes(goodsCodeList);
        return errorLog;
    }


    private String buildMaterialPushErrorLog(String errorHandLog,String errorDesc,
                                             List<Long> materialIdErrorList){
        if (!CollectionUtils.isEmpty(materialIdErrorList)){
            String materialIdLog = materialIdErrorList.stream().map(String::valueOf).collect(Collectors.joining(","));
            return errorHandLog+materialIdLog+errorDesc;
        }
        return "";
    }

    private String checkMaterial(List<Long> checkMaterialIdList,Long treeId){
        //【1】内容id不存在的：“该分类树下，分类树内容ID不存在（13223 ），不符合推荐提醒要求请检查”
        //【2】未上架：“分类树内容ID为：13223 暂未上架，不符合推荐提醒要求请检查”
        //【3】已设置不通知的：“分类树内容ID为：13223 已设置为不通知，不符合推荐提醒要求请检查”
        //【4】不在有效期内：“分类树内容ID为：13223 不在有效期内，不符合推荐提醒要求请检查”

        //1,查询内容id是否在该分类树下存在
        String errorLog = "";
        List<Material> materials = materialMapper.selectList(Wrappers.lambdaQuery(Material.class)
                .eq(Material::getIsDeleted, 0).in(Material::getId, checkMaterialIdList));
        if (CollectionUtils.isEmpty(materials)){
            //存在不属于该树或不存在的内容id
            return buildMaterialPushErrorLog("该分类树下，分类树内容ID不存在（","），不符合推荐提醒要求请检查；",checkMaterialIdList);
        }
        List<Long> materialIdList = materials.stream().map(Material::getId).map(Integer::longValue).collect(Collectors.toList());
        checkMaterialIdList.removeAll(materialIdList);
        List<Long> materialIsNotNull = materialMapper.selectMaterialIsNotNull(materialIdList,treeId);
        if (!CollectionUtils.isEmpty(materialIsNotNull)||!CollectionUtils.isEmpty(checkMaterialIdList)){
            //存在不属于该树或不存在的内容id
            materialIsNotNull.addAll(checkMaterialIdList);
            errorLog=errorLog+buildMaterialPushErrorLog("该分类树下，分类树内容ID不存在（","），不符合推荐提醒要求请检查；",materialIsNotNull);
        }
        materialIdList.removeAll(materialIsNotNull);
        if (CollectionUtils.isEmpty(materialIdList)){
            return errorLog;
        }
        //2.查询剩余内容id是否上架
        List<Long> materialIsStatusUpList = materialMapper.selectMaterialIsStatusUp(materialIdList);
        errorLog=errorLog+buildMaterialPushErrorLog("分类树内容ID为：","暂未上架，不符合推荐提醒要求请检查；",materialIsStatusUpList);
        materialIdList.removeAll(materialIsStatusUpList);
        if (CollectionUtils.isEmpty(materialIdList)){
            return errorLog;
        }
        //3.内容id是否设置通知
        List<Long> materialIsRemindType = materialMapper.selectMaterialIsRemindType(materialIdList);
        errorLog=errorLog+buildMaterialPushErrorLog("分类树内容ID为：","已设置为不通知，不符合推荐提醒要求请检查；",materialIsRemindType);
        materialIdList.removeAll(materialIsRemindType);
        if (CollectionUtils.isEmpty(materialIdList)){
            return errorLog;
        }
        //4.内容id不在有效期
        List<Long> materialEffectiveTime = materialMapper.selectMaterialEffectiveTime(materialIdList);
        errorLog=errorLog+buildMaterialPushErrorLog("分类树内容ID为：","不在有效期内，不符合推荐提醒要求请检查；",materialEffectiveTime);
        materialIdList.removeAll(materialEffectiveTime);
        if (CollectionUtils.isEmpty(materialIdList)){
            return errorLog;
        }
        String materialIdLog = materialIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        return errorLog+"分类树内容ID为："+materialIdLog+ "不符合上新推送要求，请检查；";
    }


    /**
     * 消费企微内容上新立即发送消息，用来推送企微通知
     * @param message
     */
    @StreamListener(MaterialEventMq.MATERIAL_MSG_EVENT_INPUT)
    public void consumerMessage(Message<MaterialContentPushDTO> message) {
        MaterialContentPushDTO pushDTO = message.getPayload();
        log.error("企微内容上新立即发送消息:{}", JsonUtils.toJson(pushDTO));
        //区分推送类型，素材推送，内容推送
        pushMaterialContent(pushDTO);
    }
    public void pushMaterialContent(MaterialContentPushDTO pushDTO){
        //内容工场发送数据
        List<MaterialMsgTreeNodeDTO> materialList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(pushDTO.getMaterialIds())){
            materialList = materialCategoryTreeMapper.selectSendMsgMaterialByHand(pushDTO);
        }
        //找产品发送数据
        List<Goods> goodsList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(pushDTO.getGoodsCodes())){
            GoodsPushInputDto dto = new GoodsPushInputDto();
            dto.setPushType(1);
            dto.setCodes(pushDTO.getGoodsCodes());
            Result<List<Goods>> result = productClient.getGoodsNewListByGoodsCode(dto);
            if (result.getSuccess()&&!CollectionUtils.isEmpty(result.getData())){
                goodsList = result.getData();
            }
        }
        pushMsg(goodsList,materialList,2);
    }

    public void pushMsg(List<Goods> goodsList,List<MaterialMsgTreeNodeDTO> materialList,Integer type){
        List<Long> remindedMaterialIds = new ArrayList<>();
        List<Long> pushBasicIdList = new ArrayList<>();
        int count = 0;
        MaterialMsgSendRecordQueryInputDTO inputDTO = new MaterialMsgSendRecordQueryInputDTO();
        inputDTO.setPageSize(500);
        inputDTO.setSendDate(DateUtil.getTodayDate());
        do {
            List<MaterialMsgSendRecordQueryOutputDTO> list = materialMsgSendRecordMapper.findPage(inputDTO);
            log.error("推送内容员工：{}",list);
            if (CollectionUtils.isEmpty(list)){
                inputDTO.setRecordId(null);
                continue;
            }
            count = list.size();
            inputDTO.setRecordId(list.get(list.size() - 1).getId());
            // 获取list的treeId作为新的list
            Set<String> treeIdSet = Sets.newTreeSet();
            list.forEach(m->{
                if (org.apache.commons.lang.StringUtils.isNotBlank(m.getTreeId())){
                    Collections.addAll(treeIdSet, m.getTreeId().split(","));
                }
            });
            Set<String> basicTypeSet = Sets.newTreeSet();
            list.forEach(m->{
                if (org.apache.commons.lang.StringUtils.isNotBlank(m.getType())){
                    Collections.addAll(basicTypeSet, m.getType().split(","));
                }
            });
            log.error("推送内容类型：{}",basicTypeSet);
            if (!CollectionUtils.isEmpty(basicTypeSet)){
                List<String> basicTypeSalesman = org.apache.commons.compress.utils.Lists.newArrayList();
                StringBuilder goods = new StringBuilder();
                StringBuilder goodsSendLog = new StringBuilder();
                if (basicTypeSet.contains("2")){
                    // 获取内容要发送的对象列表
                    basicTypeSalesman = list.stream()
                            .filter(x -> x.getType().contains("2"))
                            .map(MaterialMsgSendRecordQueryOutputDTO::getSalesmanId).map(String::valueOf).collect(Collectors.toList());
                    // 组装上新找产品内容
                    sendGoods(goodsList,goodsSendLog,goods,pushBasicIdList);
                }
                Set<String> retainList = Sets.newTreeSet();
                log.error("推送内容树：{}",treeIdSet);
                if (basicTypeSet.contains("1")&&!CollectionUtils.isEmpty(materialList)){
                    for (String treeId : treeIdSet) {
                        List<String> copySalesman = new ArrayList<>(List.copyOf(basicTypeSalesman));
                        StringBuilder basicLog = new StringBuilder(goodsSendLog.toString());
                        StringBuilder basicIdList = new StringBuilder(goods.toString());
                        // 获取当前树下要发送的对象列表
                        List<String> treeSalesman = list.stream()
                                .filter(x -> x.getType().contains("1")&&x.getTreeId().contains(treeId))
                                .map(MaterialMsgSendRecordQueryOutputDTO::getSalesmanId).map(String::valueOf).collect(Collectors.toList());
                        // 组装上新内容工场内容
                        StringBuilder materialIdSb = new StringBuilder();
                        StringBuilder sb = new StringBuilder();
                        sendMaterial(treeId,materialList,sb,materialIdSb,remindedMaterialIds);
                        if (StringUtils.isNotBlank(basicLog.toString())&&StringUtils.isNotBlank(sb.toString())){
                            basicLog.append(sb);
                            // 发送找产品+内容工场组合数据的业务员
                            copySalesman.retainAll(treeSalesman);
                            if (!CollectionUtils.isEmpty(copySalesman)){
                                // 删除materialIdSb后缀多余的逗号
                                String basicIdListStr = basicIdList.deleteCharAt(basicIdList.length() - 1).toString();
                                String materialIdsStr = materialIdSb.deleteCharAt(materialIdSb.length() - 1).toString();
                                XxlJobLogger.log("内容上新推送的业务员id:{},发送内容:{},内容id:{}",copySalesman, basicLog.toString(), materialIdsStr);
                                materialContentPushNewMsgJob.sendMessage(copySalesman, basicLog.toString(), materialIdsStr,basicIdListStr);
                            }
                            retainList.addAll(copySalesman);
                        }

                        // 只发送内容工场数据
                        treeSalesman.removeAll(retainList);
                        if (!CollectionUtils.isEmpty(treeSalesman)&&StringUtils.isNotBlank(sb.toString())){
                            // 推送企微消息
                            // 删除materialIdSb后缀多余的逗号
                            String materialIdsStr = materialIdSb.deleteCharAt(materialIdSb.length() - 1).toString();
                            XxlJobLogger.log("内容上新推送的业务员id:{},发送内容:{},内容id:{}",treeSalesman, sb.toString(), materialIdsStr);
                            materialContentPushNewMsgJob.sendMessage(treeSalesman, sb.toString(), materialIdsStr,"");
                        }
                    }
                }
                basicTypeSalesman.removeAll(retainList);
                //只发送找产品数据
                if (!CollectionUtils.isEmpty(basicTypeSalesman)&&StringUtils.isNotBlank(goodsSendLog.toString())){
                    // 删除materialIdSb后缀多余的逗号
                    String goodsIdLog = goods.deleteCharAt(goods.length() - 1).toString();
                    XxlJobLogger.log("内容上新推送的业务员id:{},发送内容:{},内容id:{}",basicTypeSalesman, goodsSendLog.toString(),goodsIdLog);
                    materialContentPushNewMsgJob.sendMessage(basicTypeSalesman, goodsSendLog.toString(), "",goodsIdLog);
                }
            }
        } while (count == 500);
        // 更新已推送的节点的上新标识
        if (!CollectionUtils.isEmpty(remindedMaterialIds)) {
            basicMaterialService.updateBatchRemindFlag(remindedMaterialIds);
        }
        if (type.equals(1)&&!CollectionUtils.isEmpty(pushBasicIdList)){
            tPushBasicSendRecordService.updateBatchRemindFlagByBasicId(pushBasicIdList);
        }
    }

    private void sendMaterial(String treeId,List<MaterialMsgTreeNodeDTO> materialList,StringBuilder sb,StringBuilder materialIdSb,List<Long> remindedMaterialIds){
        if (!CollectionUtils.isEmpty(materialList)){
            for (MaterialMsgTreeNodeDTO dto : materialList) {
                if (Objects.equals(dto.getTreeId(),Long.parseLong(treeId))) {
                    sb.append("【").append(dto.getNodeName()).append("】")
                            .append(dto.getNodeCount()).append("条内容：").append("\n");

                    String[] materialIds = dto.getMaterialIds().split(",");
                    List<Material> materials = materialMapper.selectList(Wrappers.lambdaQuery(Material.class)
                            .eq(Material::getIsDeleted, 0)
                            .in(Material::getId, Arrays.asList(materialIds)));
                    for (Material material : materials) {
                        materialIdSb.append(material.getId()).append(",");
                        remindedMaterialIds.add(material.getId().longValue());
                        sb.append("《").append(isTitle(material.getTitle())).append("》").append("\n");
                    }
                    sb.append("\n\n");
                }
            }
        }
    }

    private void sendGoods(List<Goods> goodsList,StringBuilder sb,StringBuilder goodsId,List<Long> pushBasicIdList){
        if (!CollectionUtils.isEmpty(goodsList)){
            sb.append("【产品推荐】").append(goodsList.size()).append("条内容：").append("\n");
            for (Goods goods : goodsList) {
                goodsId.append(goods.getId()).append(",");
                pushBasicIdList.add(goods.getId());
                sb.append(goods.getName()).append("\n");
            }
        }
    }

    public void insertPushTask(PushTaskInputDto dto){
        String errorLog = "";
        MaterialContentPushDTO pushDTO = new MaterialContentPushDTO();
        int pushType = 1;
        if (!CollectionUtils.isEmpty(dto.getMaterialIds())){
            //推送素材上新
            pushDTO.setPushType(2);
            pushDTO.setTreeId(1L);
            pushDTO.setMaterialIds(dto.getMaterialIds());
            errorLog+=pushMaterial(pushDTO);
            pushType = 2;
        }
        if (StringUtils.isNotBlank(errorLog)){
            throw new BusinessException(errorLog);
        }
        if (!CollectionUtils.isEmpty(dto.getMaterialIds())){
            dto.setMaterialIdList(pushDTO.getMaterialIdList());
        }
        //校验当前配置素材是否已执行
        TMaterialPushTask task = new TMaterialPushTask();
        task.setStatus(0);
        task.setPushType(pushType);
        task.setTitle(dto.getTitle());
        task.setExecuteTime(dto.getExecuteTime());
        task.setIsDeleted(0L);
        String modifiedUserName = StringUtils.isNotBlank(userAuthorConfig.queryUserAuthor().getFirstName())?userAuthorConfig.queryUserAuthor().getFirstName():userAuthorConfig.queryUserAuthor().getUsername();
        task.setModifiedUsername(modifiedUserName);
        task.setCreatedName(modifiedUserName);
        tMaterialPushTaskDao.insert(task);
        List<TMaterialPushBasic> tMaterialPushBasics = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(dto.getGoodsCodes())){
            for (String goodsCode: dto.getGoodsCodes()) {
                TMaterialPushBasic basic = new TMaterialPushBasic();
                basic.setType(2);
                basic.setPushTaskId(task.getId());
                basic.setBasicId(goodsCode);
                basic.setIsDeleted(0L);
                basic.setModifiedUsername(modifiedUserName);
                tMaterialPushBasics.add(basic);
            }
        }
        if (!CollectionUtils.isEmpty(dto.getMaterialIdList())){
            for (Long materialId: dto.getMaterialIdList()) {
                TMaterialPushBasic basic = new TMaterialPushBasic();
                basic.setType(1);
                basic.setPushTaskId(task.getId());
                basic.setBasicId(String.valueOf(materialId));
                basic.setIsDeleted(0L);
                basic.setModifiedUsername(modifiedUserName);
                tMaterialPushBasics.add(basic);
            }
        }
        materialPushBasicDaoService.saveBatch(tMaterialPushBasics);
    }

    public PageInfo<PushTaskListOutDto> getPushTaskList(PushTaskListInputDto dto){
        PageInfo<PushTaskListOutDto> pageInfo = new PageInfo<>(dto.getPage(), dto.getSize());
        dto.setSecretKey(MybatisMateConfig.getPassword());
        int count = tMaterialPushTaskDao.selectAllCount(dto);
        if (count < 1){
            return pageInfo;
        }
        List<PushTaskListOutDto> pushTaskListOutDtos = tMaterialPushTaskDao.selectAll(pageInfo, dto);
        pageInfo.setTotal(count);
        pageInfo.setRecords(pushTaskListOutDtos);
        return pageInfo;
    }

    public void stopPushTask(Long id){
        TMaterialPushTask tMaterialPushTask = tMaterialPushTaskDao.selectOne(Wrappers.lambdaQuery(TMaterialPushTask.class)
                .eq(BaseEntity::getId, id).eq(BaseEntity::getIsDeleted, 0L));
        if (tMaterialPushTask == null){
            log.error("当前提醒定时任务【{}】不存在",id);
            throw new BusinessException("当前任务不存在");
        }
        if (!Objects.equals(tMaterialPushTask.getStatus(),0)){
            log.error("当前提醒定时任务【{}】状态为：{}",id,tMaterialPushTask.getStatus());
            throw new BusinessException("当前提醒任务非待执行状态不可取消");
        }
        String firstName = StringUtils.isBlank(userAuthorConfig.queryUserAuthor().getFirstName())?userAuthorConfig.queryUserAuthor().getUsername():userAuthorConfig.queryUserAuthor().getFirstName();
        tMaterialPushTask.setStatus(1);
        tMaterialPushTask.setModifiedUsername(firstName);
        tMaterialPushTaskDao.updateByPrimaryKey(tMaterialPushTask);
    }

    private static String isTitle(String title){
        if (!title.matches(str)){
            return title.replaceAll("\"", "\\\\\"");
        }
        return title;
    }

    /**
     * 发送企微内容工场上新kafka消息，用以推送企微通知
     * @param dto
     */
    public void sendMessage(MaterialContentPushDTO dto) {
        messageChannel.send(MessageBuilder.withPayload(dto).build());
    }
}
