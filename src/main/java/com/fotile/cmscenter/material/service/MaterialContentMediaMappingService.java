package com.fotile.cmscenter.material.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fotile.cmscenter.client.CustomerClient;
import com.fotile.cmscenter.client.pojo.QywxApiConfigVO;
import com.fotile.cmscenter.material.dao.MaterialContentMapper;
import com.fotile.cmscenter.material.dao.MaterialContentMediaMappingDao;
import com.fotile.cmscenter.material.dao.MaterialMapper;
import com.fotile.cmscenter.material.dao.BasicMaterialContentMapper;
import com.fotile.cmscenter.material.pojo.dto.GetTreeIdByMaterialIdOutDto;
import com.fotile.cmscenter.material.pojo.dto.UpdateMaterialContentDto;
import com.fotile.cmscenter.material.pojo.entity.MaterialContentMediaMapping;
import com.fotile.cmscenter.util.QywxCommonUtil;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天素材内容关联(MaterialContentMediaMapping)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-05 11:40:33
 */

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class MaterialContentMediaMappingService {

     @Autowired
     private MaterialContentMediaMappingDao contentMediaMappingDao;

     @Autowired
     private BasicMaterialContentMapper basicMaterialContentMapper;
     @Autowired
     private MaterialContentMapper materialContentMapper;
     @Autowired
     private QywxCommonUtil qywxCommonUtil;
     @Autowired
     private CustomerClient customerClient;
     @Autowired
     private MaterialMapper materialMapper;


    /**
     * 通过ID查询单条数据
     */
    public MaterialContentMediaMapping queryById(Integer id){
        return contentMediaMappingDao.queryById(id);
    }

    /**
     * 通过聊天素材内容id查询单条数据
     */
    public List<MaterialContentMediaMapping> queryByMaterialContentId(Integer materialContentId){
        return contentMediaMappingDao.queryByMaterialContentId(materialContentId);
    }

    /**
     * 通过聊天素材内容id查询数据
     */
    public List<MaterialContentMediaMapping> queryByMaterialContentIds(List<Integer> ids){
        return contentMediaMappingDao.queryByMaterialContentIds(ids);
    }


    @XxlJob(value = "updateContentMediaJob")
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("更新微信媒体id任务开始");
        Long materialId = null;
        if (StringUtils.isNotBlank(param)){
            try {
                materialId = Long.valueOf(param);
            }catch (Exception e){
                XxlJobLogger.log("入参转id失败："+param);
            }
        }
        List<UpdateMaterialContentDto> list = materialContentMapper.getNeedUpdateMediaIdList(materialId);
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Integer> materialIds = list.stream().map(m -> m.getMaterialId()).collect(Collectors.toSet());
            List<Integer> materialIdList = new ArrayList<>(materialIds);
            List<GetTreeIdByMaterialIdOutDto> treeDtoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(materialIdList)) {
                treeDtoList = materialMapper.selectTreeIdByMaterialIds(materialIdList);
            }
            for (UpdateMaterialContentDto dto : list) {
                Optional<GetTreeIdByMaterialIdOutDto> first = treeDtoList.stream().filter(t -> dto.getMaterialId().equals(t.getMaterialId())).findFirst();
                if (first.isPresent()) {
                    GetTreeIdByMaterialIdOutDto treeDto = first.get();
                    if (treeDto.getIsGlobal() != null){
                        if (treeDto.getIsGlobal() == 1) {
                            updateMediaInfo(dto);
                        } else {
                            if (StringUtils.isNotBlank(dto.getAccountId()) && dto.getAccountId().equals(treeDto.getEffectiveWechat()) ) {
                                updateMediaInfo(dto);
                            }
                        }
                    }else {
                        updateMediaInfo(dto);
                    }
                }
            }
            XxlJobLogger.log("更新微信媒体id任务结束");
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 更新数据
     */
    @Async
    public void updateContentMedia(Integer materialId){
        try {
            List<UpdateMaterialContentDto> contentCopyDtos = materialContentMapper.selectByMaterialId(materialId);
            log.error("聊天树内容组："+contentCopyDtos);
            if (CollectionUtils.isNotEmpty(contentCopyDtos)){
                for (UpdateMaterialContentDto contentDto : contentCopyDtos){
                    updateMediaInfo(contentDto);
                }
            }
        }catch (Exception e){
            log.error("更新微信素材id任务失败",e);
        }

    }

    public void SyncContentMedia(Integer materialId){
        try {
            List<UpdateMaterialContentDto> contentCopyDtos = materialContentMapper.selectByMaterialId(materialId);
            log.error("聊天树内容组："+contentCopyDtos);
            if (CollectionUtils.isNotEmpty(contentCopyDtos)){
                for (UpdateMaterialContentDto contentDto : contentCopyDtos){
                    updateMediaInfo(contentDto);
                }
            }
        }catch (Exception e){
            log.error("更新微信素材id任务失败",e);
        }

    }


    public void updateMediaInfo(UpdateMaterialContentDto dto) {
        log.error("更新入参dto:"+dto);
        if (dto != null && dto.getType() != null){
            String url = "";
            String urlName = "无标题";
            if (1 == dto.getType() || 2 == dto.getType()) {
                url = dto.getDisplayPictureUrl();
            }else if (3 == dto.getType() || 6 == dto.getType() || 4 == dto.getType()){
                url = dto.getMaterialUrl();
                if (StringUtils.isNotBlank(dto.getMaterialName())){
                    urlName = dto.getMaterialName();
                }
            }

            if (StringUtils.isNotBlank(url)) {
                List<QywxApiConfigVO> configList = customerClient.findConfigList().getData();
                if (configList == null || configList.size() <= 0) {
                    return;
                }
                String fileType = "image";
                if(3 == dto.getType()) {
                    fileType = "video";
                }else if (4 == dto.getType()){
                    fileType = "file";
                }
                //查询分类树所属企微
                GetTreeIdByMaterialIdOutDto outDto = materialMapper.selectTreeIdByMaterialId(dto.getMaterialId());
                //55067 剔除qywx_sh_1 的accountId
                List<QywxApiConfigVO> collect = configList.stream().filter(o -> !Objects.equals(o.getAccountId(), "qywx_sh_1")).collect(Collectors.toList());
                for (QywxApiConfigVO configVO : collect) {
                    //上传到不同企业微信
                    try {
                        String mediaId = "";
                        if (outDto.getIsGlobal() == 1) {
                            mediaId = qywxCommonUtil.getMediaIdFromUrl(configVO, url,urlName, fileType);
                        }else {
                            if (configVO.getAccountId().equals(outDto.getEffectiveWechat())){
                                mediaId = qywxCommonUtil.getMediaIdFromUrl(configVO, url,urlName, fileType);
                            }
                        }
                        if (StringUtils.isNotBlank(mediaId)) {
                            //存入数据库
                            MaterialContentMediaMapping contentMediaMapping = contentMediaMappingDao.selectByMaterialId(dto.getId(),configVO.getAccountId());
                            MaterialContentMediaMapping contentMedia = new MaterialContentMediaMapping();
                            contentMedia.setMaterialContentId(dto.getId());
                            contentMedia.setAccountId(configVO.getAccountId());
                            contentMedia.setWechatMaterialId(mediaId);
                            contentMedia.setUpdateTime(new Date());
                            contentMedia.setCreatedBy("anonymousUser");
                            contentMedia.setModifiedBy("anonymousUser");
                            if (contentMediaMapping!= null) {
                                contentMedia.setId(contentMediaMapping.getId());
                                contentMediaMappingDao.updateContentMedia(contentMedia);
                            }else {
                                contentMediaMappingDao.insert(contentMedia);
                            }
                        }
                    } catch (Exception e) {
                        log.error("上传素材信息报错：" + e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 更新数据
     */
    @Async
    public void updateContentMediaByIds(List<Integer> materialIds) {
        try {
            List<UpdateMaterialContentDto> contentCopyDtos = materialContentMapper.selectByMaterialId2(materialIds);
            if (CollectionUtils.isNotEmpty(contentCopyDtos)){
                for (UpdateMaterialContentDto contentDto : contentCopyDtos){
                    updateMediaInfo(contentDto);
                }
            }
        }catch (Exception e){
            log.error("更新微信素材id任务失败",e);
        }

    }

}
