package com.fotile.cmscenter.collection.pojo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("collection_detail")
public class CollectionDetail extends AuditingEntity {


    @ApiModelProperty(value = "收藏对象表名")
    @TableField(value = "`source_table_name`")
    private String sourceTableName;

    @ApiModelProperty(value = "收藏对象表-类型（content_1：内容-图文 content_4：内容-音视频 content_menu：内容-菜谱 special_info：专题）")
    @TableField(value = "`source_table_alias`")
    private String sourceTableAlias;

    @ApiModelProperty(value = "收藏对象ID")
    @TableField(value = "`source_id`")
    private String sourceId;

    @ApiModelProperty(value = "收藏对象标题")
    @TableField(value = "`source_name`")
    private String sourceName;

    @ApiModelProperty(value = "内容分类id 美食烹饪 居家装修 饮食起居 锻炼养生家庭关系 亲子教育 心灵成长 积善之家")
    @TableField(value = "`content_category_id`")
    private Long contentCategoryId;

    @ApiModelProperty(value = "渠道")
    @TableField(value = "`type`")
    private String type;

    @ApiModelProperty(value = "收藏时间")
    @TableField(value = "`collection_date`")
    private Date collectionDate;

    @ApiModelProperty(value = "用户id")
    @TableField(value = "`user_id`")
    private String userId;

    @ApiModelProperty(value = "customer_info id")
    @TableField(value = "`c_user_id`")
    private Long cUserId;


    @TableField(value = "`nickname`")
    private String nickname;

    @ApiModelProperty(value = "用户手机号码")
    @TableField(value = "`phone`")
    private String phone;

    @ApiModelProperty(value = "是否删除：0：否；其它：是")
    @TableField(value = "`is_deleted`")
    private Long isDeleted;

    @ApiModelProperty(value = "创建者")
    @TableField(value = "`created_by`")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`created_date`")
    private Date createdDate;

    @ApiModelProperty(value = "修改者")
    @TableField(value = "`modified_by`")
    private String modifiedBy;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "`modified_date`")
    private Date modifiedDate;

    @ApiModelProperty(value = "收藏对象表-类型")
    @TableField(value = "`source_table_type`")
    private String sourceTableType;

    @ApiModelProperty(value = "收藏对象类型显示名")
    @TableField(value = "`source_table_alias_dis`")
    private String sourceTableAliasDis;


    @TableField(exist = false)
    private String contentType;

    @TableField(exist = false)
    private String collectionDateStart;
    @TableField(exist = false)
    private String collectionDateEnd;
    //废弃用户id（暂时用于查询用户名称和电话）
    @TableField(exist = false)
    private String discardUserId;

    @TableField(exist = false)
    private Integer page;
    @TableField(exist = false)
    private Integer size;

    @TableField(exist = false)
    private String coverUrl;

    @TableField(exist = false)
    private String inspireVideoUrl;

    @TableField(exist = false)
    private String inspireVideoCoverUrl;

    /**
     * 浏览数
     */
    @TableField(exist = false)
    private Long browseCount;

    /**
     * 公众号链接
     */
    @TableField(exist = false)
    private String officialAccountUrl;
}
