package com.fotile.cmscenter.comment.service.client;

import com.fotile.cmscenter.comment.pojo.vo.ActivityClassificationVo;
import com.fotile.cmscenter.comment.pojo.vo.StatisticsVo;
import com.fotile.cmscenter.comment.service.client.pojo.ActivityByIdDto;
import com.fotile.cmscenter.comment.service.client.pojo.ActivityDto;
import com.fotile.cmscenter.comment.service.client.pojo.QueryTitleByIdsInDto;
import com.fotile.cmscenter.comment.service.client.pojo.QueryTitleByIdsOutDto;
import com.fotile.cmscenter.content.common.CommonResult;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "marketing-center")
public interface MarketingClientService {

    @GetMapping("/api/activity/findByIdTitle")
    public Result findByIdTitle(@RequestParam("activityId") Long activityId);

    @PostMapping("/api/activity/findByIdTitle2")
    Result<List<ActivityByIdDto>> findByIdTitle2(@RequestBody List<Long> ids);

    @PostMapping("/api/activitySpecial/queryTitleByIds")
    Result<List<QueryTitleByIdsOutDto>> queryTitleByIds(@RequestBody QueryTitleByIdsInDto queryTitleByIdsInDto);

    @GetMapping("/api/activity/getAllActivity")
    public Result getAllActivity(@RequestParam("title") String title);

    @PostMapping("/api/statistics/findOrUpdateByIdStatistics")
    public Result findOrUpdateByIdStatistics(@RequestParam("activityId") Long activityId,
                                             @RequestParam("type") int type);

    @GetMapping("/api/activityClassification/queryAllActivityClassification")
    public Result<List<ActivityClassificationVo>> queryAllActivityClassification(@RequestParam("type") String type);

    @GetMapping("/api/activitySpecial/queryIdOrTitle")
    public Result queryIdsByTitle(@RequestParam("title") String title);

    @PostMapping("/api/activitySpecial/updateCommentCountById")
    public Result updateCommentCountById(Map map);

    @GetMapping("/api/activitySpecial/queryTitleById")
    public Result queryTitleById(@RequestParam("id") Long id);


    //根据活动id集合查询活动列表
    @RequestMapping(value = "/api/activity/findActivityByIdAndType", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ActivityDto>> findActivityByIdAndType(@RequestParam("ids") List<Long> ids);

    @ApiOperation(value = "根据id编辑统计信息")
    @PostMapping(value = "/api/m/statistics/findOrUpdateByIdStatisticsUser")
    public Result<?> findOrUpdateByIdStatisticsUser(@RequestParam("activityId") Long activityId,
                                                    @RequestParam("type") int type,
                                                    @RequestParam("num") Integer num);

    @ApiOperation(value = "根据id编辑统计信息")
    @RequestMapping(value = "/api/open/statistics/findOrUpdateByIdStatistics",
            method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CommonResult<StatisticsVo> findOrUpdateByIdStatisticsOpen(@Valid @RequestBody StatisticsVo statisticsVo);

    @RequestMapping(value = "/api/m/statistics/findOrUpdateByIdStatistics",
            method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CommonResult<StatisticsVo> findOrUpdateByIdStatisticsM(@Valid @RequestBody StatisticsVo statisticsVo);


}
