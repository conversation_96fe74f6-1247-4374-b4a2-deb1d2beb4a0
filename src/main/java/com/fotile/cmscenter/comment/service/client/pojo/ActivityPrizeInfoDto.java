package com.fotile.cmscenter.comment.service.client.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ActivityPrizeInfoDto {
    private Long id;

    private Long activityId;

    //奖品名称
    @NotNull
    private String prizeName;

    //奖品类型('10'卡券，'20'积分，'30'实物)
    @NotNull
    private String prizeType;


    @ApiModelProperty(value = "奖品图片链接")
    @NotNull
    private String imageUrl;

    @ApiModelProperty(value = "奖品数量")
    @NotNull
    private int prizeCount;


    @ApiModelProperty(value = "卡券id")
    private Long cardId;

    @ApiModelProperty(value = "领取方式(10.门店自提 20.送货上门)")
    private String exchangeType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改人username")
    private String reviseName;

    @ApiModelProperty(value = "最后修改时间")
    private Date reviseTime;

    /**
     * 积分发放companyId对应pointcenter.point_company.company_id
     */
    @ApiModelProperty(value = "积分发放companyId对应pointcenter.point_company.company_id")
    private Long pointCompanyId;

    /**
     * 积分值
     */
    @ApiModelProperty(value = "积分值")
    private int point;


}
