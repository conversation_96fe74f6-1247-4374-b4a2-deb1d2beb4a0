package com.fotile.cmscenter.comment.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Date;
import java.util.List;

@Data
public class ActivityCommentAbstractVo {

    @ApiModelProperty(value = "评论用户列表")
    private List<String> sourceTableName;

    @ApiModelProperty(value = "最多点赞数")
    private Long maxLikeCount;

    @ApiModelProperty(value = "最多点赞评论内容")
    private String  maxLikeCommentContent;


}
