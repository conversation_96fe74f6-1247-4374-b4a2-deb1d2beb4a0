package com.fotile.cmscenter.menuscore.dao;

import com.fotile.cmscenter.menuscore.pojo.dto.BatchUpdateStatusInDto;
import com.fotile.cmscenter.menuscore.pojo.dto.FindMenuscorePageAllInDto;
import com.fotile.cmscenter.menuscore.pojo.dto.FindMenuscorePageAllOutDto;
import com.fotile.cmscenter.menuscore.pojo.dto.UpdateMenuscoreStatusInDto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import java.util.List;

@Validated
public interface MenuscoreDao {

    public int findMenuscorePageAllTotal(@Param("inDto")FindMenuscorePageAllInDto inDto);

    public List<FindMenuscorePageAllOutDto> findMenuscorePageAll(@Param("inDto")FindMenuscorePageAllInDto inDto, @Param("pg")PageInfo pg);

    public void updateStatus(@Param("inDto") UpdateMenuscoreStatusInDto inDto, @Param("userId")String userId);

    public void batchUpdateStatus(@Param("inDto") BatchUpdateStatusInDto inDto, @Param("userId")String userId);
}
