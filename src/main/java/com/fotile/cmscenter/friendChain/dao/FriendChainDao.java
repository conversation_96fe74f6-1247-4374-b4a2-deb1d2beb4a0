package com.fotile.cmscenter.friendChain.dao;

import com.fotile.cmscenter.friendChain.pojo.FriendChain;
import com.fotile.cmscenter.friendChain.pojo.dto.FriendChainInpDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FriendChainDao {
    List<FriendChain> findFriendChainPage(@Param("friendChainInpDto") FriendChainInpDto friendChainInpDto);

    Long findFriendChainPageCount(@Param("friendChainInpDto") FriendChainInpDto friendChainInpDto);

    List<FriendChain> findFriendChainByType(@Param("type") Integer type, @Param("offset") Long offset, @Param("size") Integer size);

    Long findFriendChainByTypeCount(@Param("type") Integer type);

    int addFriendChain(@Param("friendChain") FriendChain friendChain);

    int updFriendChain(@Param("friendChain") FriendChain friendChain);

    int updFriendChainDel(@Param("id") Long id, @Param("modifiedBy") String modifiedBy);

    int updFriendChainByStage(@Param("id") Long id,
                              @Param("stage") Integer stage,
                              @Param("modifiedBy") String modifiedBy
    );

    List<FriendChain> findPageAllByTypes(@Param("list") List<Integer> list);
}
