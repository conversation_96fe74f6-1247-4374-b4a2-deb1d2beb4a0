package com.fotile.cmscenter.yard.service;

import com.fotile.cmscenter.content.pojo.LabelCms;
import com.fotile.cmscenter.yard.dao.YardMarkCategoryDao;
import com.fotile.cmscenter.yard.dto.YardMarkCategory;
import com.fotile.cmscenter.yard.dto.YardMarkCategoryFirstDto;
import com.fotile.cmscenter.yard.dto.YardMarkCategoryMapper;
import com.fotile.cmscenter.yard.dto.YardMarkCategorySecondDto;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 幸福打卡分类(YardMarkCategory)表服务实现类
 */
 
@Service
@DruidTxcMultiDataSourceAnnontation(value = "first")
public class YardMarkCategoryService {
    @Autowired
    private YardMarkCategoryDao yardMarkCategoryDao;

    /**
     * 通过ID查询单条数据
     */
    public YardMarkCategory queryById(Long id) {
        return this.yardMarkCategoryDao.queryById(id);
    }


    /**
     * 新增数据
     */
    public YardMarkCategory insert(YardMarkCategory yardMarkCategory) {
        this.yardMarkCategoryDao.insert(yardMarkCategory);
        return yardMarkCategory;
    }

    /**
     * 修改数据
     */
    public int update(YardMarkCategory yardMarkCategory) {
        return yardMarkCategoryDao.update(yardMarkCategory);
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.yardMarkCategoryDao.deleteById(id) > 0;
    }

    public List<YardMarkCategoryFirstDto> queryYardMarkCategoryList(Long id) {
        List<YardMarkCategoryFirstDto> yardMarkCategories = yardMarkCategoryDao.queryFirstYardMarkCategoryList();
        if (CollectionUtils.isNotEmpty(yardMarkCategories)){
            List<YardMarkCategorySecondDto> yardMarkCategorySecondDtos = yardMarkCategoryDao.querySecondYardMarkCategoryList();
            for (YardMarkCategoryFirstDto firstDto : yardMarkCategories){
                if (CollectionUtils.isNotEmpty(yardMarkCategorySecondDtos)){
                    List<YardMarkCategorySecondDto> collect = yardMarkCategorySecondDtos.stream().filter(c -> firstDto.getId().equals(c.getParentId())).collect(Collectors.toList());
                    firstDto.setSubCategoryList(collect);
                }
            }
        }
        return yardMarkCategories;
    }



}
