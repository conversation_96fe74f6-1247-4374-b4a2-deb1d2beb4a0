package com.fotile.cmscenter.yard.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Auther: ldp
 * @Date: 2020/11/2
 * @Description:
 */
@Data
public class EditMarkInDto {
    /**
     * 主键id
     */
    private Long id;


    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;


    /**
     * 修改者
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 分类id
     */
    private Long categoryId;
    /**
     * 分类code
     */
    private String categoryCode;

    /**
     * 打卡名称
     */
    private String name;
    /**
     * 打卡icon
     */
    private String icon;
    /**
     * 特殊标识
     */
    private String specialFlag;
    /**
     * 有效期开始
     */
    private Date effectStartDate;
    /**
     * 有效期结束
     */
    private Date effectEndDate;

    /**
     * 0下线1上线
     */
    private Integer online = 0;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否默认，0否1是
     */
    private Integer defaultValue;


}
