package com.fotile.cmscenter.yard.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Auther: ldp
 * @Date: 2020/10/21
 * @Description:
 */
@Data
public class GetCompoundListInDto {
    /**
     * 页数
     */
    @NotNull
    private Integer page;
    /**
     * 条数
     */
    @NotNull
    private Integer size;
    /**
     * id
     */
    private Long id;

    private Long offset;
    /**
     * 标签id集合
     */
    private List<Long> tagIds;
    /**
     * 动态类型(1:普通成员,2:管理员)
     */
    private Integer dynamicType;
    /**
     * 在线状态(0:下线,1:上线)
     */
    private Integer onlineStatus;

    /**
     * 审核状态(1:草稿,2:待审核,3:已审核,4:审核拒绝)
     */
    private Integer auditStatus;
    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 发布时间开始
     */
    private Date publishDateStart;
    /**
     * 发布时间结束
     */
    private Date publishDateEnd;
    /**
     * 记录时间开始
     */
    private Date recordDateStart;
    /**
     * 记录时间结束
     */
    private Date recordDateEnd;
    /**
     * 院子分类
     */
    private Long yardCategoryId;
    /**
     * 院子名称
     */
    private String yardName;
    /**
     * 动态分类：1打卡动态2普通动态3话题动态
     */
    private Integer category;
}
