package com.fotile.cmscenter.yard.controller;

import com.fotile.cmscenter.yard.dto.YardMarkCategory;
import com.fotile.cmscenter.yard.dto.YardMarkCategoryFirstDto;
import com.fotile.cmscenter.yard.service.YardMarkCategoryService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 幸福打卡分类(YardMarkCategory)表控制层
 */
 
@RestController
@RequestMapping("/api/yardMarkCategory")
public class YardMarkCategoryController extends BaseController{
    /**
     * 服务对象
     */
    @Autowired
    private YardMarkCategoryService yardMarkCategoryService;


    /**
     * 通过主键查询单条数据
     */
    @RequestMapping(value = "/queryById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> queryById(@Valid Long id) {
        return success(this.yardMarkCategoryService.queryById(id));
    }

    /**
     * 新增数据
     */
//    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> add(@RequestBody YardMarkCategory yardMarkCategory) {
        return success(this.yardMarkCategoryService.insert(yardMarkCategory));
    }

    /**
     * 编辑数据
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> edit(@RequestBody YardMarkCategory yardMarkCategory) {
        int i = yardMarkCategoryService.update(yardMarkCategory);
        if(i > 0){
            return success("编辑成功");
        }else{
            return failure("编辑失败");
        }
    }

    /**
     * 查询打卡分类下拉信息接口
     */
    @RequestMapping(value = "/queryYardMarkCategoryList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<YardMarkCategoryFirstDto>> queryYardMarkCategoryList(@Valid Long id) {
        return success(this.yardMarkCategoryService.queryYardMarkCategoryList(id));
    }


}

