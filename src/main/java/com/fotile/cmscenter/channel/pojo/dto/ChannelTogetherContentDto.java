package com.fotile.cmscenter.channel.pojo.dto;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;


@Data
public class ChannelTogetherContentDto implements Serializable {
    /**
     * 关联表id
     */
    private Long togetherId;
    /**
     * 渠道树分类id
     */
    private Long channelCategoryId;

    /**
     * 关联表名
     */
    private String contentTable;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 创建人userID
     */
    private String createdBy;
    /**
     * 创建人userID
     */
    private String modifiedBy;
    /**
     * 创建时间
     */
    private Date createdDate;


    /*=================================关联表信息=======================================================================================*/
    /**
     * 关联ID
     */
    private Long id;
    /**
     * 关联编码
     */
    private String code;
    /**
     * 关联标题
     */
    private String title;
    /**
     * 关键字
     */
    private String keyWord;
    /**
     * 用户id
     */
    private Long customerId;
    /**
     * 用户昵称
     */
    @FieldEncrypt
    private String nickname;
    /**
     * 用户手机号
     */
    @FieldEncrypt
    private String customerPhone;

    /**
     * 菜谱数量（自定义菜谱专题列表）
     */
    private Integer contentCount = 0;


}
