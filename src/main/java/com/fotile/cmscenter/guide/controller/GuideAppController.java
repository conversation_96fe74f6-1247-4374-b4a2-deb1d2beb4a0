package com.fotile.cmscenter.guide.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fotile.cmscenter.guide.pojo.dto.GuideAppInfoDto;
import com.fotile.cmscenter.guide.pojo.dto.GuideAppListOutDto;
import com.fotile.cmscenter.guide.pojo.dto.GuideShareInputDto;
import com.fotile.cmscenter.guide.service.GuideService;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  攻略,云管理APP
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/guideApp")
@RestController
public class GuideAppController extends BaseController {

    @Autowired
    private GuideService guideService;

    /**
     * 云管理APP攻略列表查询
     */
    @GetMapping("/getAppList")
    public Result<PageInfo<GuideAppListOutDto>> getAppList(String fieldId,Integer page,Integer size,Long cluesId,Long companyId) {
        try {
            return Result.buildSuccess(guideService.getAppList(fieldId,page,size,cluesId,companyId));
        }catch (Exception e){
            log.error("查询异常：{}",e.getMessage());
        }
        return Result.buildFailure("查询异常");
    }

    /**
     * 根据线索id查询攻略列表
     */
    @GetMapping("/getAppListByCluesId")
    public Result<PageInfo<GuideAppListOutDto>> getAppListByCluesId(@RequestParam @Valid Long cluesId,Integer page,Integer size) {
        if (cluesId==null){
            return Result.buildFailure("线索ID不可为空");
        }
        try {
            return Result.buildSuccess(guideService.getAppListByCluesId(cluesId,page,size));
        }catch (Exception e){
            log.error("查询异常：{}",e.getMessage());
        }
        return Result.buildFailure("查询异常");
    }


    /**
     * 云管理APP攻略详情
     */
    @GetMapping("/getAppGuideInfo")
    public Result<GuideAppInfoDto> getAppGuideInfo(@RequestParam Long id) {
        if (id == null){
            return Result.buildFailure("id不能为空");
        }
        try {
            return Result.buildSuccess(guideService.getAppGuideInfo(id));
        }catch (Exception e){
            log.error("查询异常：{}",e.getMessage());
        }
        return Result.buildFailure("查询失败");
    }

    /**
     * 攻略查看
     */
    @GetMapping(value = "/showGuide", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> showGuide( @RequestParam Long id) {
        if (id == null){
            return Result.buildFailure("id不能为空");
        }
        guideService.showGuide(id);
        return Result.buildSuccess("查看成功");
    }
    /**
     * 攻略分享
     */
    @PostMapping(value = "/shareGuide", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> shareGuide( @RequestBody GuideShareInputDto guideShareInputDto) {
        if (guideShareInputDto == null){
            return Result.buildFailure("id不能为空");
        }
        guideService.shareGuide(guideShareInputDto);
        return Result.buildSuccess("分享成功");
    }

    /**
     * 根据场景化方案id查询攻略
     * @param schemeId
     * @return
     */
    @GetMapping(value = "/getGuideListBySchemeId", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<GuideAppListOutDto>> getGuideListBySchemeId(Long schemeId) {
        try {
            return Result.buildSuccess(guideService.getGuideListBySchemeId(schemeId));
        }catch (Exception e){
            log.error("查询异常：{}",e.getMessage());
        }
        return Result.buildFailure("查询异常");
    }
}
