package com.fotile.cmscenter.guide.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/28 10:29
 * 攻略导入excel实体类
 */
@Data
public class GuideGoodImportExcelDTO implements Serializable {

    @ExcelProperty(value = "序号")
    @ColumnWidth(5)
    private String id;
    @ExcelProperty(value = "攻略ID")
    @ColumnWidth(40)
    private String guideId;
    //-- 排烟烹饪场景 -- //
    @ExcelProperty(value = "排烟烹饪场景场景大类")
    @ColumnWidth(10)
    private String sceneCategoryName1;
    @ExcelProperty(value = "排烟烹饪场景场景小类一")
    @ColumnWidth(3)
    private String sceneSortName11;
    @ExcelProperty(value = "排烟烹饪场景场景小类一关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode11;
    @ExcelProperty(value = "排烟烹饪场景场景小类一关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName11;
    @ExcelProperty(value = "排烟烹饪场景场景小类一关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes11;
    @ExcelProperty(value = "排烟烹饪场景场景小类一关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames11;
    @ExcelProperty(value = "排烟烹饪场景场景小类二")
    @ColumnWidth(3)
    private String sceneSortName12;
    @ExcelProperty(value = "排烟烹饪场景场景小类二关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode12;
    @ExcelProperty(value = "排烟烹饪场景场景小类二关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName12;
    @ExcelProperty(value = "排烟烹饪场景场景小类二关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes12;
    @ExcelProperty(value = "排烟烹饪场景场景小类二关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames12;
    @ExcelProperty(value = "排烟烹饪场景场景小类三")
    @ColumnWidth(3)
    private String sceneSortName13;
    @ExcelProperty(value = "排烟烹饪场景场景小类三关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode13;
    @ExcelProperty(value = "排烟烹饪场景场景小类三关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName13;
    @ExcelProperty(value = "排烟烹饪场景场景小类三关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes13;
    @ExcelProperty(value = "排烟烹饪场景场景小类三关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames13;
    @ExcelProperty(value = "排烟烹饪场景场景小类四")
    @ColumnWidth(3)
    private String sceneSortName14;
    @ExcelProperty(value = "排烟烹饪场景场景小类四关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode14;
    @ExcelProperty(value = "排烟烹饪场景场景小类四关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName14;
    @ExcelProperty(value = "排烟烹饪场景场景小类四关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes14;
    @ExcelProperty(value = "排烟烹饪场景场景小类四关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames14;
    @ExcelProperty(value = "排烟烹饪场景场景小类五")
    @ColumnWidth(3)
    private String sceneSortName15;
    @ExcelProperty(value = "排烟烹饪场景场景小类五关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode15;
    @ExcelProperty(value = "排烟烹饪场景场景小类五关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName15;
    @ExcelProperty(value = "排烟烹饪场景场景小类五关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes15;
    @ExcelProperty(value = "排烟烹饪场景场景小类五关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames15;
    //-- 洗消饮用场景 -- //
    @ExcelProperty(value = "洗消饮用场景场景大类")
    @ColumnWidth(10)
    private String sceneCategoryName2;
    @ExcelProperty(value = "洗消饮用场景场景小类一")
    @ColumnWidth(3)
    private String sceneSortName21;
    @ExcelProperty(value = "洗消饮用场景场景小类一关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode21;
    @ExcelProperty(value = "洗消饮用场景场景小类一关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName21;
    @ExcelProperty(value = "洗消饮用场景场景小类一关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes21;
    @ExcelProperty(value = "洗消饮用场景场景小类一关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames21;
    @ExcelProperty(value = "洗消饮用场景场景小类二")
    @ColumnWidth(3)
    private String sceneSortName22;
    @ExcelProperty(value = "洗消饮用场景场景小类二关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode22;
    @ExcelProperty(value = "洗消饮用场景场景小类二关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName22;
    @ExcelProperty(value = "洗消饮用场景场景小类二关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes22;
    @ExcelProperty(value = "洗消饮用场景场景小类二关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames22;
    @ExcelProperty(value = "洗消饮用场景场景小类三")
    @ColumnWidth(3)
    private String sceneSortName23;
    @ExcelProperty(value = "洗消饮用场景场景小类三关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode23;
    @ExcelProperty(value = "洗消饮用场景场景小类三关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName23;
    @ExcelProperty(value = "洗消饮用场景场景小类三关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes23;
    @ExcelProperty(value = "洗消饮用场景场景小类三关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames23;
    @ExcelProperty(value = "洗消饮用场景场景小类四")
    @ColumnWidth(3)
    private String sceneSortName24;
    @ExcelProperty(value = "洗消饮用场景场景小类四关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode24;
    @ExcelProperty(value = "洗消饮用场景场景小类四关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName24;
    @ExcelProperty(value = "洗消饮用场景场景小类四关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes24;
    @ExcelProperty(value = "洗消饮用场景场景小类四关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames24;
    @ExcelProperty(value = "洗消饮用场景场景小类五")
    @ColumnWidth(3)
    private String sceneSortName25;
    @ExcelProperty(value = "洗消饮用场景场景小类五关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode25;
    @ExcelProperty(value = "洗消饮用场景场景小类五关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName25;
    @ExcelProperty(value = "洗消饮用场景场景小类五关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes25;
    @ExcelProperty(value = "洗消饮用场景场景小类五关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames25;
    //-- 保险冷储场景 -- //
    @ExcelProperty(value = "保鲜冷储场景场景大类")
    @ColumnWidth(10)
    private String sceneCategoryName3;
    @ExcelProperty(value = "保鲜冷储场景场景小类一")
    @ColumnWidth(3)
    private String sceneSortName31;
    @ExcelProperty(value = "保鲜冷储场景场景小类一关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode31;
    @ExcelProperty(value = "保鲜冷储场景场景小类一关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName31;
    @ExcelProperty(value = "保鲜冷储场景场景小类一关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes31;
    @ExcelProperty(value = "保鲜冷储场景场景小类一关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames31;
    @ExcelProperty(value = "保鲜冷储场景场景小类二")
    @ColumnWidth(3)
    private String sceneSortName32;
    @ExcelProperty(value = "保鲜冷储场景场景小类二关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode32;
    @ExcelProperty(value = "保鲜冷储场景场景小类二关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName32;
    @ExcelProperty(value = "保鲜冷储场景场景小类二关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes32;
    @ExcelProperty(value = "保鲜冷储场景场景小类二关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames32;
    @ExcelProperty(value = "保鲜冷储场景场景小类三")
    @ColumnWidth(3)
    private String sceneSortName33;
    @ExcelProperty(value = "保鲜冷储场景场景小类三关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode33;
    @ExcelProperty(value = "保鲜冷储场景场景小类三关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName33;
    @ExcelProperty(value = "保鲜冷储场景场景小类三关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes33;
    @ExcelProperty(value = "保鲜冷储场景场景小类三关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames33;
    @ExcelProperty(value = "保鲜冷储场景场景小类四")
    @ColumnWidth(3)
    private String sceneSortName34;
    @ExcelProperty(value = "保鲜冷储场景场景小类四关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode34;
    @ExcelProperty(value = "保鲜冷储场景场景小类四关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName34;
    @ExcelProperty(value = "保鲜冷储场景场景小类四关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes34;
    @ExcelProperty(value = "保鲜冷储场景场景小类四关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames34;
    @ExcelProperty(value = "保鲜冷储场景场景小类五")
    @ColumnWidth(3)
    private String sceneSortName35;
    @ExcelProperty(value = "保鲜冷储场景场景小类五关联商品-渠道编码")
    @ColumnWidth(1000)
    private String channelCategoryCode35;
    @ExcelProperty(value = "保鲜冷储场景场景小类五关联商品-渠道名称")
    @ColumnWidth(1000)
    private String channelCategoryName35;
    @ExcelProperty(value = "保鲜冷储场景场景小类五关联商品-商品编码")
    @ColumnWidth(1000)
    private String goodsCodes35;
    @ExcelProperty(value = "保鲜冷储场景场景小类五关联商品-商品名称")
    @ColumnWidth(1000)
    private String goodsNames35;
}
