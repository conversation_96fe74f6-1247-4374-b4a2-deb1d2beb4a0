package com.fotile.cmscenter.materialStatistics.service;

import com.fotile.cmscenter.materialStatistics.dao.OperationLogMapper;
import com.fotile.cmscenter.materialStatistics.pojo.dto.QueryAllLogListInDto;
import com.fotile.cmscenter.materialStatistics.pojo.dto.QueryAllLogListOutDto;
import com.fotile.cmscenter.materialStatistics.pojo.entity.OperationLog;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import lombok.extern.slf4j.Slf4j;
import mybatis.mate.annotation.FieldEncrypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class OperationLogService {
    @Autowired
    private OperationLogMapper logMapper;
    @Autowired
    private UserAuthorConfig userAuthorConfig;

    //新增日志
    public OperationLog insertOperationLog(OperationLog operationLog) {
        UserAuthor user = userAuthorConfig.queryUserEntityInfoByUserId();
        operationLog.setCreatedBy(user != null ? user.getUserId() : null);
        operationLog.setOperatorName(user != null ? user.getFirstName() : null);
        logMapper.insert(operationLog);
        return operationLog;
    }

    //新增日志2
    public int insertOperationLog2(Long refId, String refTable, Integer refType,
                                   Integer type, String typeName, String oldData,
                                   String newData, String remark, String attachmentUrl,
                                   String userId, String userName) {
        OperationLog operationLog = new OperationLog();
        operationLog.setRefId(refId);
        operationLog.setRefTable(refTable);
        operationLog.setRefType(refType);
        operationLog.setType(type);
        operationLog.setTypeName(typeName);
        operationLog.setOldData(oldData);
        operationLog.setNewData(newData);
        operationLog.setRemark(remark);
        operationLog.setAttachmentUrl(attachmentUrl);
        operationLog.setCreatedBy(userId);
        operationLog.setOperatorName(userName);
        int i = logMapper.insert(operationLog);
        return i;
    }

    //查询操作日志
    public PageInfo<QueryAllLogListOutDto> queryAllLogList(QueryAllLogListInDto inDto) {
        PageInfo<QueryAllLogListOutDto> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());

        inDto.setOffset(pageInfo.getOffset());
        Long count = logMapper.selectLogCount(inDto);
        List<QueryAllLogListOutDto> logList = new ArrayList<>();
        if (count > 0) {
            logList = logMapper.selectLogList(inDto);
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(logList);
        return pageInfo;
    }


}
