package com.fotile.cmscenter.integrateMarketing.controller;

import com.fotile.cmscenter.content.pojo.dto.CommonStoreDto;
import com.fotile.cmscenter.content.pojo.dto.FindPstoreAllOutDto;
import com.fotile.cmscenter.content.pojo.dto.FindPstoreContentAllOutDto;
import com.fotile.cmscenter.integrateMarketing.pojo.dto.FindContentTemplateDTO;
import com.fotile.cmscenter.integrateMarketing.pojo.dto.FindContentTemplateListDTO;
import com.fotile.cmscenter.integrateMarketing.pojo.entity.ImContentTemplateEntity;
import com.fotile.cmscenter.integrateMarketing.pojo.mapper.ImcontentTemplateConvertor;
import com.fotile.cmscenter.integrateMarketing.pojo.vo.FindContentTemplateByIdVO;
import com.fotile.cmscenter.integrateMarketing.pojo.vo.FindContentTemplateByPrefixVo;
import com.fotile.cmscenter.integrateMarketing.service.ImAPPContentTemplateService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * 营销活动栏目 app/h5
 *
 * @ClassName ImAppContentTemplateController
 * @Description: APP内容模板
 */
@Slf4j
@RestController
@RequestMapping({"/api/app/integrate", "/api/open/app/integrate", "/m/api/open/app/integrate"})
public class ImAppContentTemplateController extends BaseController {
    @Autowired
    ImAPPContentTemplateService imAPPContentTemplateService;


//    @PostMapping(value = "/findContentTemplate")
//    public Result<List<FindContentTemplateByIdVO>> findContentTemplate(@Valid @RequestBody FindContentTemplateDTO findContentTemplateDTO) {
//        return success(imAPPContentTemplateService.findContentTemplate(findContentTemplateDTO));
//    }

    /**
     * 根据 contentTemplateId 集合 获取模板信息
     */
    @PostMapping(value = "/findContentTemplateByIds")
    public Result<List<FindContentTemplateByIdVO>> findContentTemplateByIds(@RequestBody FindContentTemplateDTO findContentTemplateDTO) {
        return success(imAPPContentTemplateService.findContentTemplateByIds(findContentTemplateDTO.getIds(), findContentTemplateDTO.getPageNum(), findContentTemplateDTO.getPageSize()));
    }

    @PostMapping(value = "/findContentTemplateByIdsCount")
    public Result<Long> findContentTemplateByIdsCount(@RequestBody FindContentTemplateDTO findContentTemplateDTO) {
        return success(imAPPContentTemplateService.findContentTemplateByIdsCount(findContentTemplateDTO.getIds()));
    }

    /**
     * get请求根据contentTemplateId  获取模板信息
     */
    @GetMapping(value = "/findContentTemplateById")
    public Result<FindContentTemplateByIdVO> findContentTemplateById(Long contentTemplateId) {
        return success(imAPPContentTemplateService.findContentTemplateById(contentTemplateId));
    }

    @GetMapping(value = "/api/open/findContentTemplateById")
    public Result<FindContentTemplateByIdVO> findContentTemplateByIdOpen(Long contentTemplateId) {
        return success(imAPPContentTemplateService.findContentTemplateByTemplateId(contentTemplateId));
    }

    /**
     * get请求根据contentTemplateId  获取模板信息,不能抛异常版本
     */
    @GetMapping(value = "/findContentTemplateByIdNew")
    public Result<FindContentTemplateByIdVO> findContentTemplateByIdNew(Long contentTemplateId) {
        return success(imAPPContentTemplateService.findContentTemplateByIdNew(contentTemplateId));
    }

    /**
     * 根据省区市，查询活动列表
     */
    @PostMapping(value = "/findActivityByArea")
    public Result<List<FindContentTemplateByIdVO>> findActivityByArea(@RequestBody FindContentTemplateDTO findContentTemplateDTO) {
        List<FindContentTemplateByIdVO> result = imAPPContentTemplateService.findActivityByAreaByRedis(findContentTemplateDTO);
        return success(result);
    }

    /**
     * 根据省区市和经纬度，查询分公司下最近的门店和分公司页面模板列表
     */
    @PostMapping(value = "/findStoreByArea")
    public Result<FindPstoreContentAllOutDto> findStoreByArea(@RequestBody @Valid CommonStoreDto commonStoreDto) {
        FindPstoreContentAllOutDto result = imAPPContentTemplateService.findStoreByAreaByRedis(commonStoreDto);
        return success(result);
    }

    /**
     * 根据省区市，根据id查询活动详情
     */
    @GetMapping(value = "/findActivityByIdAndArea")
    public Result<FindContentTemplateByIdVO> findActivityByIdAndArea(@RequestParam("provinceId") Long provinceId,
                                                                     @RequestParam("cityId") Long cityId,
                                                                     @RequestParam("countyId") Long countyId,
                                                                     @RequestParam("contentTemplateId") Long contentTemplateId) {
        FindContentTemplateByIdVO result = imAPPContentTemplateService.findActivityByIdAndArea(contentTemplateId, provinceId,
                cityId, countyId);
        return success(result);
    }

    /**
     * 方太官方小程序门店详情页面查询门店活动列表
     * 参数示例：channelId为43，sellType为7，companyId为门店对应的公司id<br/>
     * {
     *     "channelId": "43",
     *     "sellType": "7",
     *     "companyId":"71"
     * }
     */
    @PostMapping(value = "/api/open/findContendTemplateList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ImContentTemplateEntity>> findContendTemplateList(@Valid @RequestBody FindContentTemplateListDTO findContentTemplateDTO) {
        if(findContentTemplateDTO.getCompanyId() == null && CollectionUtils.isEmpty(findContentTemplateDTO.getCompanyIdList())){
            throw new BusinessException("分公司id不能为空");
        }
        List<ImContentTemplateEntity> result = imAPPContentTemplateService.findContendTemplateList(findContentTemplateDTO);
        return success(result);
    }


    /**
     * 返回的是有活动的公司的id集合
     * 参数示例：channelId为43，sellType为7，companyId为门店对应的公司id<br/>
     * {
     *     "channelId": "43",
     *     "sellType": "7",
     *     "companyId":"71"
     * }
     */
    @PostMapping(value = "/api/open/findContendTemplateCompanyIdList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Set<Long>> findContendTemplateCompanyIdList(@Valid @RequestBody FindContentTemplateListDTO findContentTemplateDTO) {
        if(findContentTemplateDTO.getCompanyId() == null && CollectionUtils.isEmpty(findContentTemplateDTO.getCompanyIdList())){
            throw new BusinessException("分公司id不能为空");
        }
        Set<Long> result = imAPPContentTemplateService.findContendTemplateCompanyIdList(findContentTemplateDTO);
        return success(result);
    }

    /**
     * 根据前缀匹配，获取活动列表
     */
    @GetMapping(value = "/findActivityByPrefix")
    public Result<List<FindContentTemplateByPrefixVo>> findActivityByPrefix(@RequestParam("prefix") String prefix,
                                                                        @RequestParam(required = false, value = "withDetail") Boolean withDetail) {
//        prefix = "健康好水巡展线上推广-";
        List<FindContentTemplateByIdVO> result = imAPPContentTemplateService.findActivityByPrefix(prefix, withDetail);
        List<FindContentTemplateByPrefixVo> voList = ImcontentTemplateConvertor.CONVERTOR.byIdToPrefixVo(result);
        return success(voList);
    }

    /**
     * 根据 编码 获取模板信息
     */
    @PostMapping(value = "/findContentTemplateByCode")
    public Result<ImContentTemplateEntity> findContentTemplateByCode(@RequestBody FindContentTemplateDTO findContentTemplateDTO) {
        return success(imAPPContentTemplateService.findContentTemplateByCode(findContentTemplateDTO));
    }

}
