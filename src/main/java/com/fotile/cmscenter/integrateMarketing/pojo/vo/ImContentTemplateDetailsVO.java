package com.fotile.cmscenter.integrateMarketing.pojo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fotile.cmscenter.integrateMarketing.common.ImConstant;
import com.fotile.cmscenter.integrateMarketing.pojo.entity.*;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import mybatis.mate.annotation.FieldEncrypt;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 内容模板详细信息
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.integrateMarketing.pojo.vo
 * @date 2023/8/24 13:54
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ImContentTemplateDetailsVO implements Serializable {

    public static final Long defaultActivityId=7203L;
    /**
     * 内容模板ID
     */
    private Long templateId;

    /**
     * 内容模板编码
     */
    private String templateCode;

    /**
     * 内容模板信息
     */
    private ImContentTemplateEntity template;

    /**
     * 内容模板-栏目信息
     */
    private List<ImContentTemplateSubjectEntity> subjects;

    /**
     * 内容模板-分公司权限列表
     */
    private List<ImContentCompanyAuthMapping> authMappings;

    /**
     * 内容模板-站点渠道
     */
    private List<ImChannelRadioVO> channelRadios;

    /**
     * 阅读量
     */
    private Integer viewCount = 0;

    /**
     * 是否小程序创建，小程序创建的为复制，有些默认值，不需要源代码逻辑
     */
    private Boolean fromMiniProgram=false;

    /**
     * 操作类型(0:保存,1:保存并提交)
     */
    @NotNull(message = "操作类型不能为空！")
    @Range(min = 0, max = 1, message = "错误的操作类型")
    private Integer actionType;


    /**
     * 操作人id(内部参数)
     */
    @JsonIgnore
    private String operatorId;

    /**
     * 操作人姓名(内部参数)
     */
    @JsonIgnore
    @FieldEncrypt
    private String operatorName;

    public List<ImContentTemplateSubjectEntity> fillSubjects(Long templateId) {
        if (this.template != null && CollectionUtils.isNotEmpty(this.subjects)) {
            for (ImContentTemplateSubjectEntity subject : this.subjects) {

                subject.setContentTemplateId(templateId);
            }
        }
        return this.subjects;
    }

    public List<ImContentTemplateSubjectLandingEntity> fillLandings() {
        List<ImContentTemplateSubjectLandingEntity> landings = null;
        if (CollectionUtils.isNotEmpty(this.subjects)) {
            landings = new ArrayList<>();
            for (ImContentTemplateSubjectEntity subject : this.subjects) {
                if (subject.getId() != null && CollectionUtils.isNotEmpty(subject.getLandings())) {
                    int loop = subject.getLandings().size();
                    for (ImContentTemplateSubjectLandingEntity landing : subject.getLandings()) {
                        if(ImConstant.DisplayStyleEnum.INVITE.getValue().equals(subject.getDisplayStyle())){
                            landing.setActivityId(defaultActivityId.toString());
                        }
                        landing.setSubjectId(subject.getId());
                        landing.setLandingType(subject.getLandingType());
                        landing.setLandingSort(loop);
                        landings.add(landing);
                        loop--;
                    }
                }
            }
        }
        return landings;
    }

    public List<ImContentTemplateSubjectInvite> fillInviterFields() {
        return Optional.ofNullable(this.subjects)
                .map(sb -> sb.stream()
                        .filter(s -> CollectionUtils.isNotEmpty(s.getInviterFields()))
                        .peek(s -> s.getInviterFields().forEach(i -> i.setContentTemplateId(templateId).setSubjectId(s.getId())))
                        .flatMap(s -> s.getInviterFields().stream())
                        .collect(Collectors.toList()))
                .orElse(Lists.newArrayListWithExpectedSize(0));
    }

    public List<ImContentCompanyAuthMapping> fillAuthMappings(Long templateId) {
        List<ImContentCompanyAuthMapping> mappings = null;
        if (this.template != null && CollectionUtils.isNotEmpty(this.authMappings)) {
            mappings = new ArrayList<>();
            for (ImContentCompanyAuthMapping auth : this.authMappings) {
                auth.setContentTemplateId(templateId);
                if (auth.getCompanyOrgId() != null) {
                    if (auth.getCompanyOrgId() == -1) {
                        mappings = new ArrayList<>();
                        mappings.add(auth);
                        break;
                    } else {
                        mappings.add(auth);
                    }
                }
            }
        }
        return mappings;
    }

    public List<ImChannelCategoryContentMappingEntity> fillChannelRadios(Long templateId) {
        List<ImChannelCategoryContentMappingEntity> channelRadios = null;
        if (this.template != null && CollectionUtils.isNotEmpty(this.channelRadios)) {
            channelRadios = new ArrayList<>();
            ImChannelCategoryContentMappingEntity item = null;
            long loop = 1;
            for (ImChannelRadioVO cr : this.channelRadios) {
                item = new ImChannelCategoryContentMappingEntity();
                item.setChannelId(cr.getChannelId());
                item.setRadioId(cr.getRatioId());
                item.setSourceTableName("im_content_template");
                item.setSourceId(templateId);
                item.setSort(loop);

                channelRadios.add(item);
                loop++;
            }
        }
        return channelRadios;
    }


    public void resetTemplate() {
        this.templateId = null;
        this.templateCode = null;
        if (this.template != null) {
            this.template.setId(null);
            this.template.setTid(null);
            this.template.setOriginalId(null);
            this.template.setTemplateCode(null);
            this.template.setPublicAll(0);
        }
    }

    public void resetTemplateSubject() {
        if (CollectionUtils.isNotEmpty(this.subjects)) {
            for (ImContentTemplateSubjectEntity subject : this.subjects) {
                subject.setId(null);
                subject.setContentTemplateId(null);
                subject.setOriginalId(null);
                subject.setTid(null);
            }
        }
    }

    public void resetAuthMappings() {
        /*if (CollectionUtils.isNotEmpty(this.authMappings)) {
            for (ImContentCompanyAuthMapping authMapping : this.authMappings) {
                authMapping.setId(null);
                authMapping.setContentTemplateId(null);
                authMapping.setTid(null);
                authMapping.setOriginalId(null);
            }
        }*/
        this.authMappings = new ArrayList<>();
    }

    public void resetChannelRadios() {
        this.channelRadios = new ArrayList<>();
    }
}
