package com.fotile.cmscenter.integrateMarketing.pojo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.integrateMarketing.pojo.vo
 * @date 2023/8/24 20:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImChannelRadioVO {

    private String sourceTableName;

    private Long sourceId;
    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 频道ID
     */
    private Long ratioId;

    /**
     * 操作人id(内部参数)
     */
    @JsonIgnore
    private String operatorId;

    public ImChannelRadioVO(Long channelId, Long ratioId) {
        this.channelId = channelId;
        this.ratioId = ratioId;
    }
}
