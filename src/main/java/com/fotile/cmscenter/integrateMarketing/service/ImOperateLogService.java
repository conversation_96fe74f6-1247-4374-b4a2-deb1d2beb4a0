package com.fotile.cmscenter.integrateMarketing.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fotile.cmscenter.integrateMarketing.common.ImConstant;
import com.fotile.cmscenter.integrateMarketing.dao.ImOperateLogMapper;
import com.fotile.cmscenter.integrateMarketing.pojo.dto.ImOperateLogDTO;
import com.fotile.cmscenter.integrateMarketing.pojo.dto.QueryOperateLogDTO;
import com.fotile.cmscenter.integrateMarketing.pojo.entity.ImOperateLogEntity;
import com.fotile.cmscenter.util.CommonUtils;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class ImOperateLogService extends ServiceImpl<ImOperateLogMapper, ImOperateLogEntity> {


    public int updateBatch(List<ImOperateLogEntity> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<ImOperateLogEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<ImOperateLogEntity> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(ImOperateLogEntity record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(ImOperateLogEntity record) {
        return baseMapper.insertOrUpdateSelective(record);
    }


    /**
     * 获取操作日志总记录数
     */
    public Integer getOperateLogCount(QueryOperateLogDTO query) {
        if (query == null) {
            return 0;
        }

        return baseMapper.getOperateLogCount(query);
    }

    /**
     * 获取操作日志分页数据
     */
    public PageInfo<ImOperateLogDTO> getOperateLogList(QueryOperateLogDTO query) {
        if (query == null) {
            return null;
        }

        int totalRows = baseMapper.getOperateLogCount(query);
        if (totalRows <= 0) {
            return null;
        }

        PageInfo<ImOperateLogDTO> pageInfo = new PageInfo<>(query.getPage(), query.getSize(), totalRows);
        pageInfo.setRecords(baseMapper.getOperateLogList(query, pageInfo));

        return pageInfo;
    }

    /**
     * 新增操作日志
     */
    public Long addOperateLog(ImOperateLogDTO add, boolean needCheckAndInit) {
        if (add == null || add.getType() == null || add.getRefId() == null) {
            throw new BusinessException("参数错误！");
        }

        if (needCheckAndInit) {
            ImConstant.OpLogEnum logType = ImConstant.OpLogEnum.NULL.getOpLogEnum(add.getType());
            if (logType == null || logType == ImConstant.OpLogEnum.NULL) {
                throw new BusinessException("操作类型错误！");
            }
            if (StringUtils.isBlank(add.getCreatedBy())) {
                Map<String, String> user = CommonUtils.getAuthorUser();
                add.setCreatedBy(user.get("userid"));
                add.setOperatorName(user.get("name"));
            }
            if (StringUtils.isBlank(add.getRefTable())) {
                add.setRefTable(logType.getRefTable());
            }
            if (StringUtils.isBlank(add.getRefTable()) && logType.getRefType() == 100) {
                add.setRefTable("im_content_template");
            }
            add.setRefType(logType.getRefType());
            add.setType(logType.getType());
            add.setTypeName(logType.getTypeName());
            add.setCreatedDate(new Date());

            if (add.getRefId() == null || StringUtils.isBlank(add.getRefTable())) {
                throw new BusinessException("参数错误，新增日志操作失败！");
            }
        }

        baseMapper.addOperateLog(add);
        return add.getId();
    }

    /**
     * 批量插入操作日志
     */
    public void batchAddOperateLog(List<ImOperateLogDTO> batch, boolean needCheckAndInit) {
        if (CollectionUtils.isEmpty(batch)) {
            return;
        }
        long count = batch.stream().filter(x -> StringUtils.isBlank(x.getRefTable())).count();
        if (count > 0) {
            throw new BusinessException("参数错误，存在关联表名为空的数据！");
        }
        count = batch.stream().filter(x -> x.getRefId() == null || x.getRefId() <= 0).count();
        if (count > 0) {
            throw new BusinessException("参数错误，存在关联ID为空的数据！");
        }

        if (needCheckAndInit) {
            ImConstant.OpLogEnum logType;
            for (ImOperateLogDTO add : batch) {
                logType = ImConstant.OpLogEnum.NULL.getOpLogEnum(add.getType());
                if (logType == null || logType == ImConstant.OpLogEnum.NULL) {
                    throw new BusinessException("操作类型错误！");
                }
                if (StringUtils.isBlank(add.getCreatedBy())) {
                    Map<String, String> user = CommonUtils.getAuthorUser();
                    add.setCreatedBy(user.get("userid"));
                    add.setOperatorName(user.get("name"));
                }
                if (StringUtils.isBlank(add.getRefTable())) {
                    add.setRefTable(logType.getRefTable());
                }
                if (StringUtils.isBlank(add.getRefTable()) && logType.getRefType() == 100) {
                    add.setRefTable("im_content_template");
                }
                add.setRefType(logType.getRefType());
                add.setType(logType.getType());
                add.setTypeName(logType.getTypeName());
                add.setCreatedDate(new Date());

                if (add.getRefId() == null || StringUtils.isBlank(add.getRefTable())) {
                    throw new BusinessException("参数错误，新增日志操作失败！");
                }
            }
        }

        baseMapper.batchAddOperateLog(batch);
    }

}
