package com.fotile.cmscenter.integrateMarketing.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * 页面模板异步导出
 *
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.integrateMarketing.mq
 * @date 2023/8/27 23:56
 */
@Component
public interface AsyncExportContentTemplateChannel {
    /**
     * 消费
     */
    String CMS_CONTENT_TEMPLATE_EXPORT_INPUT = "cms_content_template_export_input";

    /**
     * 生产
     */
    String CMS_CONTENT_TEMPLATE_EXPORT_OUTPUT = "cms_content_template_export_output";


    @Output(CMS_CONTENT_TEMPLATE_EXPORT_OUTPUT)
    MessageChannel sendExportContentTemplateMassage();


    @Input(CMS_CONTENT_TEMPLATE_EXPORT_INPUT)
    SubscribableChannel disposeExportContentTemplateInput();
}
