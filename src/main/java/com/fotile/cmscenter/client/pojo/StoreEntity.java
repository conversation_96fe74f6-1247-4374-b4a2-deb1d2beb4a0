package com.fotile.cmscenter.client.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import javax.persistence.Entity;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 门店信息表
 */
@Data
@Entity
@TableName(value = "t_store")
public class StoreEntity extends AuditingEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 渠道大类ID
     */
    public Long channelCategoryId;

    /**
     * 渠道大类编码
     */
    public String channelCategoryCode;

    /**
     * 渠道大类名称
     */
    public String channelCategoryName;

    /**
     * 渠道细分ID
     */
    public Long channelSubdivideId;

    /**
     * 渠道细分编码
     */
    public String channelSubdivideCode;

    /**
     * 渠道细分名称
     */
    public String channelSubdivideName;

    private String planCode;
    /**
     * 渠道大类ID(多个半角逗号分隔)
     */
    public String channelCategoryIds;
    /**
     * 渠道细分ID(多个半角逗号分隔)
     */
    public String channelSubdivideIds;

    /**
     * 组织机构id
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 门店关键字(标签)
     */
    @TableField(value = "key_word")
    private String keyWord;

    /**
     * 编码
     */
    @NotEmpty(message = "门店编码不能为空")
    @TableField(value = "code")
    private String code;

    /**
     * 名称
     */
    @NotBlank(message = "门店名称不能为空")
    @TableField(value = "name")
    private String name;

    /**
     * 名称或编码
     */
    @TableField(exist = false)
    private String nameOrCode;

    /**
     * 封面图片
     */
    @NotBlank(message = "封面图不能为空")
    @TableField(value = "coverurl")
    private String coverurl;

    /**
     * 地址-省
     */
    @NotNull(message = "地址-省不能为空")
    @TableField(value = "provicenId")
    private Long provicenId;

    /**
     * 地址-省名称
     */
    @TableField(value = "provicenName")
    private String provicenName;

    /**
     * 地址-市
     */
    @NotNull(message = "地址-市不能为空")
    @TableField(value = "cityId")
    private Long cityId;

    /**
     * 地址-市名称
     */
    @TableField(value = "cityName")
    private String cityName;

    /**
     * 地址-区
     */
    @NotNull(message = "地址-区不能为空")
    @TableField(value = "countyId")
    private Long countyId;

    /**
     * 地址-区名称
     */
    @TableField(value = "countyName")
    private String countyName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    @TableField(value = "address")
    private String address;

    /**
     * 详细地址2
     */
    @TableField(value = "address2")
    private String address2;

    /**
     * 营业时间-开始时间
     */
    @NotNull(message = "营业时间-开始时间不能为空")
    @TableField(value = "shoppingStart")
    private String shoppingStart;

    /**
     * 营业时间-结束时间
     */
    @NotNull(message = "营业时间-结束时间不能为空")
    @TableField(value = "shoppingEnd")
    private String shoppingEnd;

    /**
     * 电话
     */
    @FieldEncrypt
    @TableField(value = "tel")
    private String tel;

    /**
     * 店长
     */
    @TableField(value = "leaderId")
    private Integer leaderId;

    /**
     * 总经理名称
     */
    @Transient // 该属性不是表的字段
    private String leaderName;

    /**
     * 支付宝收款账号
     */
    @TableField(value = "alipayno")
    private String alipayno;

    /**
     * 支付宝收款账号二维码图片地址
     */
    @TableField(value = "alipaynourl")
    private String alipaynourl;

    /**
     * 微信收款账号
     */
    @TableField(value = "wechatno")
    private String wechatno;

    /**
     * 微信收款账号二维码图片地址
     */
    @TableField(value = "wechatnourl")
    private String wechatnourl;

    /**
     * 备注
     */
    @TableField(value = "note")
    private String note;

    /**
     * 市场容量
     */
    @TableField(value = "market_capacity")
    private BigDecimal marketCapacity;

    /**
     * 人口
     */
    @TableField(value = "population")
    private BigDecimal population;

    /**
     * 门店级别
     */
    @TableField(value = "store_level")
    private String storeLevel;

    /**
     * 是否无人门店
     */
    @TableField(value = "is_unmanned")
    private Boolean isUnmanned;

    /**
     * 店招形状，关联字典表
     */
    @TableField(value = "sign_shape")
    private String signShape;

    /**
     * 店招面积
     */
    @TableField(value = "sign_size")
    private BigDecimal signSize;

    /**
     * 店招制作费
     */
    @TableField(value = "sign_cost")
    private BigDecimal signCost;

    /**
     * 店员服装是否统一
     */
    @TableField(value = "is_uniform")
//    @CompareField(name = "店员服装是否统一")
    private Boolean isUniform;

    /**
     * 服装照片地址
     */
    @TableField(value = "uniform_pic")
    private String uniformPic;

    /**
     * 厨电品牌前三
     */
    @TableField(value = "top_kitchens")
    private String topKitchens;

    /**
     * 门头尺寸
     */
    @TableField(exist = false)
    private String doorSize;

    /**
     * 门店状态，0：禁用；1：启用；2：筹备；3：清退
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 门店状态，0：禁用；1：启用；2：筹备；3：清退
     */
    @TableField(exist = false)
    private Set<Integer> statusSet;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private String longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private String latitude;

    /**
     * 距离
     */
    @Transient
    private String distance;

    /**
     * 是否开启体验店 0：不开启；1：开启
     */
    private Integer isOpenKpoint;

    /**
     * 所属公司id
     */
    @Transient
    @TableField(value = "company_id")
    private Long companyId;

    @TableField(exist = false)
    private String companyCode;

    /**
     * 是否支持现场演示，0-不支持，1-支持
     */
    private Integer liveDemo;

    /**
     * 户型编码，关联字典表
     */
    private String layoutCode;

    /**
     * 分公司id集合
     **/
    private String companyIds;

    /**
     * 所属公司名称
     */
    @Transient
    private String companyName;

    /**
     * 门店性质
     */
    @Transient
    private String character;

    /**
     * 装修状态
     */
    @Transient
    private Integer decorationType;

    /**
     * 所属部门id
     */
    @Transient
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 所属部门名称
     */
    @Transient
    private String parentName;

    /**
     * 所属部门编码
     */
    @Transient
    private String parentCode;

    /**
     * 部门列表中'上级部门'
     */
    private String fullPathName;

    /**
     * 部门列表中'上级部门'id
     */
    private String fullPathId;

    /**
     * 营业时间备注
     */
    private String shoppingRemark;

    /**
     * 电话备注
     */
    @FieldEncrypt
    private String telRemark;

    /**
     * 邮件
     */
    private String email;

    /**
     * 邮件备注
     */
    private String emailRemark;

    /**
     * 微信备注
     */
    @FieldEncrypt
    private String wechatRemark;

    /**
     * 支付宝备注
     */
    private String alipayRemark;

    /**
     * 渠道
     */
    private String channel;

    private String wechat;

    /**
     * 是否同步至DRP(1:不同步; 2:同步至总部DRP; 3:同步至事业上DRP; 4:同步至事业上DRP)
     */
    private String isToDrp;

    /**
     * 店长扫码线索是否进入公海，默认是。1：是、0：否
     */
    private Integer isManagerCluesIntoSea;

    /**
     * 订单传送至DRP状态(0:根据DRP设置; 1:需要经销商预审核; 2:不需要经销商预审核)
     */
    private String toDrpStage;

    /**
     * 门店类型，1：专卖店；2：KA店；3：社区服务店；4：家装定制主材厅；5：综合专柜店；6：百货门店
     */
    private Integer storeType;

    /**
     * 展示渠道
     */
    private String channelCode;

    /**
     * 展示渠道名称
     */
    private String channelName;

    //	private List<DistributorDto> distributorDtos;

    /**
     * 线索下发通知账号
     */
    private String cluesMsgUser;

    /**
     * 门店简称
     */
    private String abbreviation;

    //	/** * 经销商信息 */
//	private DistributorDto distributorDto;

    /**
     * 所属客户(经销商)ID
     */
    private Long distributorId;

    /**
     * 所属客户(经销商)ID集合
     */
    private String distributorIds;

    private Long channelSubdivide;

    //截单时间
    private String cutTime;

    private String storeTypeName;

    //门店类型code
    private String storeTypeCode;

    /**
     * 渠道大类
     */
    private Long channelCategory;

    /**
     * 大区id
     */
    private Integer area;

    private String areaCode;

    private String areaName;

    /**
     * 所属大区
     */
    private String areas;

    /**
     * 门店业务发展主管
     **/
    private Long developSalesmanId;

    private String developSalesmanCode;

    @FieldEncrypt
    private String developSalesmanName;

    @FieldEncrypt
    private String developSalesmanPhone;

    //所属客户名称
    private String distributorName;

    //所属客户小票类型
    private Integer receiptType;

    //是否开启财务自动审核
    private Integer financeAuditFlag;

    /**
     * 是否在云管理预审核 0：否；1：是
     **/
    private Integer preAudit;

    private String intro;

    @TableField(value = "consultation_pc")
    private String consultationPc;

    @TableField(value = "consultation_m")
    private String consultationM;

    /**
     * 渠道细分
     **/
    private String channelSubdivides;

    /**
     * 客户编码
     **/
    private String distributorCode;

    /**
     * 是否通过drp转单 0：否；1：是
     */
    private Integer isTransfer;

    /**
     * 是否进行订单审核
     */
    private Integer isToCheck;

    /**
     * 是否按编码排序 0：否；1：是
     */
    private Integer orderByCode;

    /**
     * 是否家装门店
     */
    private Integer ifDecorationStore;

    /**
     * 创建时间-开始
     */
    private String createdDateStart;

    /**
     * 创建时间-结束
     */
    private String createdDateEnd;

    /**
     * 门店装修状态(1:装修,2:验收)
     */
    private Integer decorateStatus;

    /**
     * 开店时间
     */
    private Date openDate;

    /**
     * 闭店时间
     */
    private Date closeDate;

    /**
     * 终端验收时间--重装
     */
    private Date terminalCheckDateAgain;

    /**
     * 终端验收时间--新装
     */
    private Date terminalCheckDate;

    /**
     * 闭店流程启动时间
     */
    private Date closeStartDate;

    /**
     * 更换经销商用，老的storeId
     */
    @TableField(exist = false)
    private Long oldStoreId;

    /**
     * 门店市场等级
     */
    private String storeMarketGrade;

    /**
     * 门店所属渠道编码(channel_category.code type_code=store_type)
     */
    private String storeChannelCode;

    private Integer storeChannelId;

    /**
     * 门店渠道名称，channel_category
     */
    private String storeChannelName;

    /**
     * 客户渠道编码
     */
    private String distributorChannel;

    /**
     * 客户渠道编码(废弃)
     */
    private String distributorChannelCode;

    /**
     * 关联历史门店
     */
    private String relatedStoreId;

    /**
     * 是否虚拟门店，0-否，1-是
     */
    private Integer isVirtual;

    /**
     * 是否需要终端建设，0-不需要，1-需要
     */
    private Integer needTerminalBuild;

    /**
     * 门店负责人姓名
     */
    @FieldEncrypt
    private String managerName;

    /**
     * 门店负责人联系方式
     */
    @FieldEncrypt
    private String managerPhone;

    /**
     * 门店业务类型，BBC，B2B，B2C
     */
    private String storeBizType;

    /**
     * 组织结构
     */
    private String staffs;

    /**
     * 门店产权类型，1-自有，2-租赁
     */
    private Integer propertyRight;

    /**
     * 租用年限
     */
    private Integer leaseTerm;

    /**
     * 流程类型(1:电商/米博/办事处;2:经销商且KA/家装;3:经销商且实体)
     */
    private Integer bpmType;

    /**
     * 实际使用面积
     */
    private BigDecimal usableArea;

    /**
     * 门店整体使用面积
     */
    private BigDecimal wholeUsableArea;

    /**
     * 门店渠道扩展信息类型(开店模式(1:专卖店/KA门店且是实体门店,2:社区服务店且是实体门店,3:其他渠道[默认]))
     * orgcenter.channel_category.open_type
     */
    private Integer openType;

    /**
     * 预计年销售额(万元)
     */
    private Short estimatedAnnualSales;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 门店关键字(支持门店名称/简称/编码模糊查询)
     */
    private String storeKeyword;

    /**
     * 门店搜索类型区别：1-根据门店名称/编码搜索, 2-根据门店渠道搜索, 默认为1。
     */
    private Integer storeKeywordType = 1;

    /**
     * 门店业务发展主管关键字(支持名称/手机号/编码模糊查询)
     */
    private String developSalesman;

    //迭代需求0414,added by zd,2021/04/02
    /**
     * 开店时间 - 开始时间
     */
    private Date openStartTime;

    /**
     * 开店时间 - 结束时间
     */
    private Date openEndTime;

    /**
     * 闭店时间 - 开始时间
     */
    private Date closeStartTime;

    /**
     * 闭店时间 - 结束时间
     */
    private Date closeEndTime;

    /**
     * 创建时间 - 开始时间
     */
    private Date createdStartTime;

    /**
     * 创建时间 - 结束时间
     */
    private Date createdEndTime;

    /**
     * 分公司Id(t_company.id)
     */
    private Long companyPkId;

    /**
     * 门店状态(0：禁用；1：启用；2：筹备；3：清退，支持多个状态查询，使用半角逗号分隔，eg:1,2,3)
     */
    private String statusMulti;

    /**
     * 门店细分渠道编码(来源字典表)
     */
    private String storeSubChannelCode;

    private String outsideStoreName;

    /**
     * 门店ID(t_store.id)
     */
    private Long storeId;

    /**
     * 组织结构id集合
     */
    private List<Long> orgIds;

    /**
     * 目标实体id集合(同type:分公司ID-t_company.id；部门ID-t_department.id；门店ID-t_store.id)
     */
    private List<Long> ids;

    /**
     * 门店状态，0：禁用；1：启用；2：筹备；3：清退
     */
    private String statusName;

    /**
     * EPS回传实际使用面积
     */
    private BigDecimal epsUsableArea;

    /**
     * 场馆介绍
     */
    private String stadiumsIntroduce;

    /**
     * 是否场地开放租赁
     */
    private String isOpenRent;

    /**
     * 出行提示
     */
    private String travelTips;

    /**
     * 门店层数
     */
    @TableField(exist = false)
    private Integer floors;

    @TableField(exist = false)
    private Boolean emptyPlanCode;

    /**
     * 门店初测面积下限
     */
    @TableField(exist = false)
    private BigDecimal usableAreaLower;

    /**
     * 门店初测面积上限
     */
    @TableField(exist = false)
    private BigDecimal usableAreaUpper;

    /**
     * 门店层数
     */
    @TableField(exist = false)
    private Set<Integer> floorSet;

    /**
     * 门店级别
     */
    @TableField(exist = false)
    private Set<String> storeLevels;

    public StoreEntity() {
        super();
    }

    /**
     * 是否进行线索跟进审核 0：否；1：是
     */
    private Integer cluesFollow;

    /**
     * 是否进行线索丢掉审核 0：否；1：是
     */
    private Integer cluesLose;

    /**
     * 年租金
     */
    private BigDecimal annualRent;

    /**
     * 未成交线索超期投入公海天数
     */
    private Integer cluesOverdue;

    private Integer depositCluesOverdue;

    /**
     * 订单校验关联引流渠道,0:是；1：否
     */
    private Integer orderCheckChannel;

    /**
     * 是否临街
     */
    @TableField(exist = false)
    private Integer isFrontage;

    /**
     * 是否使用pos机，0否，1是
     */
    private Integer usePos;

    /**
     * 终端形象评价得分
     */
    private Integer terminalImageScore;

    /**
     * 部门orgId集合（逗号分隔）
     */
    private String depOrgIds;

    /**
     * 门店整体使用面积开始
     */
    @TableField(exist = false)
    private BigDecimal wholeUsableAreaBegin;

    /**
     * 门店整体使用面积结束
     */
    @TableField(exist = false)
    private BigDecimal wholeUsableAreaEnd;

    /**
     * 是否启用场景化方案 0否1是
     */
    private Integer isSceneFlag;

    /**
     * 场景套系
     */
    private BigDecimal sceneSuit;

    @TableField(exist = false)
    private Date terminalCheckDateStart;

    @TableField(exist = false)
    private Date terminalCheckDateEnd;

    @TableField(exist = false)
    private Date terminalCheckDateAgainStart;

    @TableField(exist = false)
    private Date terminalCheckDateAgainEnd;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 关键字
     */
    private List<String> realKeyWordList;

    /**
     * 【仅后端使用】根据关键字获取门店主键id集合
     */
    private List<Long> realKeyWordStoreIdList;
}
