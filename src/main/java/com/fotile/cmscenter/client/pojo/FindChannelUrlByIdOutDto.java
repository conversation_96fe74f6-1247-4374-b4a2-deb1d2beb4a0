package com.fotile.cmscenter.client.pojo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindChannelUrlByIdOutDto implements Serializable {

    @ApiModelProperty(value="主键id",example="主键id")
    private Long id;

    @ApiModelProperty(value="渠道id",example="渠道id")
    private Long channelId;

    @ApiModelProperty(value="渠道名称",example="渠道名称")
    private String channelName;

    @ApiModelProperty(value="渠道code",example="渠道code")
    private String channelCode;

    @ApiModelProperty(value="链接",example="链接")
    private String url;

    @ApiModelProperty("父分类，1.内容；2.活动；3.专题")
    private Integer firstType;

    @ApiModelProperty("子分类")
    private String lastType;
}
