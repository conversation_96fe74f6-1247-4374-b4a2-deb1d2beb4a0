package com.fotile.cmscenter.client.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QywxApiConfigVO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "是否删除：0：否；其它：是")
    private Integer isDeleted;

    @ApiModelProperty(value = "")
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "")
    private Date createdDate;

    @ApiModelProperty(value = "")
    private String modifiedBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "")
    private Date modifiedDate;

    @ApiModelProperty(value = "api id")
    private String corpid;

    @ApiModelProperty(value = "api secret")
    private String corpsecret;

    @ApiModelProperty(value = "账户Id")
    private String accountId;

    @ApiModelProperty(value = "账户名称")
    private String accountName;

    /**
     * 默认查询部门的信息
     */
    @ApiModelProperty(value = "备用1")
    private String bk1;

    @ApiModelProperty(value = "备用2")
    private String bk2;

    @ApiModelProperty(value = "备用3")
    private String bk3;

}
