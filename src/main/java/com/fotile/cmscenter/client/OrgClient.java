package com.fotile.cmscenter.client;

import com.fotile.cmscenter.client.pojo.*;
import com.fotile.cmscenter.client.pojo.org.*;
import com.fotile.cmscenter.yard.dto.MdmStaffInfoVto;
import com.fotile.cmscenter.yard.dto.MdmStaffRto;
import com.fotile.cmscenter.yard.dto.MdmStaffVto;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(value = "org-center")
public interface OrgClient {

    /**
     * 根据分公司id及状态查询业务员分页列表
     * 分公司权限下状态为启用，
     * 暂不确定岗位为“办事处/大区门店业务发展主管、办事处/大区门店业务发展经理”的业务员
     */
    @RequestMapping(value = "/api/open/salesman/findSalesmanByCompanyIdAndStatusForPost", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<SalesmanDTO>> findSalesmanByCompanyIdAndStatus(@RequestBody FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);
    @RequestMapping(value = "/api/open/salesman/findSalesmanByCompanyIdAndStatusForPostCount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> findSalesmanByCompanyIdAndStatusForPostCount(@RequestBody FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);
    @RequestMapping(value = "/api/open/salesman/findSalesmanCountByStoreIdAndChannel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> findSalesmanCountByStoreIdAndChannel(@RequestBody FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);
    @RequestMapping(value = "/api/open/salesman/findSalesmanCountByPost", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> findSalesmanCountByPost(@RequestBody FindSalesmanByCompanyIdAndStatusDTO findSalesmanByCompanyIdAndStatusDTO);
    @RequestMapping(value = "/api/open/salesman/findSalesmanCountByCode", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<String>> findSalesmanCountByCode(@RequestBody FindSalesmanByCompanyIdAndStatusDTO findSalesmanCountByCodeDTO);
    /**
     * 根据公司OrgID集合，取得大区的code 大区的名称
     *
     * @param findCompanyByIdsInDto
     * @return
     */
    @GetMapping("/api/company/findCompanyInfoByOrgIdList")
    public Result<List<Map<String, String>>> findCompanyInfoByOrgIdList(@RequestBody FindCompanyByIdsInDto findCompanyByIdsInDto);

    //根据渠道id查询渠道信息
    @GetMapping("/api/channel/findById")
    public Result<FindChannelByIdOutDto> findChannelById(@RequestParam("id") Long id);

    @GetMapping("/api/open/channel/findChannelByCode")
    public Result<TChannelDTO> findChannelByCode(@RequestParam("code") String code);


    //根据渠道id集合查询渠道信息
    @PostMapping(value = "/api/channel/findByIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindChannelByIdOutDto>> findByIds(Map<String, List<Long>> map);

    //根据父分类查询对应的url 父分类，1.内容；2.活动；3.专题
    @GetMapping("/api/channelUrl/findByIds")
    public Result<List<FindChannelUrlByIdOutDto>> findByIds(@RequestParam("idList") List<Long> idList,
                                                            @RequestParam("firstType") Integer firstType,
                                                            @RequestParam("lastType") String lastType);

    //根据父分类查询对应的url 父分类，1.内容；2.活动；3.专题
    @GetMapping("/api/channelUrl/findByType")
    public Result<List<FindChannelUrlByIdOutDto>> findByType(@RequestParam("idList") List<Long> idList,
                                                             @RequestParam("firstType") Integer firstType,
                                                             @RequestParam("lastType") String lastType);


    @RequestMapping(value = "/api/serviceApp/api/open/findServiceListByStoreId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ServiceInfoWXDTO>> findServiceListByStoreId(@RequestParam("companyId") Long companyId,
                                                                   @RequestParam("storeOrgId") Long storeOrgId,
                                                                   @RequestParam("serviceName") String serviceName);

    /**
     * 获取mdm员工信息
     */
    @PostMapping(value = "/api/mdm/staffInfos", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<PageInfo<MdmStaffVto>> getStaffInfos(MdmStaffRto mdmStaffRto);

    /**
     * 获取mdm员工信息
     */
    @PostMapping(value = "/api/mdm/getMdmStaffSimpleInfoList", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<MdmStaffInfoVto>> getMdmStaffSimpleInfoList(MdmStaffInfoVto mdmStaffInfoVto);


    @GetMapping(value = "/api/open/findSalesmanById", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindSalesmanByIdOutDto> findSalesmanById(@RequestParam("id") Long id);

    @ApiOperation("根据门店id查询门店")
    @RequestMapping(value = "/api/open/findStoreInfoById", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindStoreByIdOutDto> findStoreInfoById(@RequestParam("id") Long id);

    @RequestMapping(value = "/api/open/findStoreInfoByIdList", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByIdOutDto>> findStoreInfoByIdList(@RequestBody List<Long> idList);

    @ApiOperation("根据公司id查询公司信息")
    @RequestMapping(value = "/api/company/api/open/findByOrgId", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindCompanyByIdOutDto> findByOrgId(@RequestParam("id") Long id);

    @ApiOperation("根据部门查询业务员集合")
    @RequestMapping(value = "/api/salesman/findSalesmanIdStrsByDepAuthor", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<String>> findSalesmanIdStrsByDepAuthor();


    @ApiOperation("根据手机号集合，取得业务员分公司，门店信息")
    @RequestMapping(value = "/api/salesman/api/open/getSalesmanInfoForQYWX", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanInfoForQYWX>> getSalesmanInfoForQYWX(@RequestBody List<String> phoneList);


    @ApiOperation("根据id集合，取得业务员分公司，门店信息")
    @RequestMapping(value = "/api/salesman/api/open/getSalesmanInfoByIdsForQYWX", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanInfoForQYWX>> getSalesmanInfoByIdsForQYWX(@RequestBody List<Long> ids);


    @RequestMapping(value = {"/api/store/api/open/findByOrgIds"}, method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindStoreByOrgIdOutDto>> findByOrgIds(@RequestBody FindStoreByOrgIdsInDto findStoreByOrgIdInDto);

    @ApiOperation("根据id集合查询渠道")
    @RequestMapping(value = "/api/open/findByChannelIds", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindChannelByIdOutDto>> findByChannelIds(@RequestBody FindChannelByIdsInDto findChannelByIdsInDto);

    @ApiOperation("根据部门查询业务员集合")
    @RequestMapping(value = "/api/salesman/findSalesmanIdStrsByDepId", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<String>> findSalesmanIdStrsByDepId(@RequestParam("departmentId") Long departmentId);

    @ApiOperation("查询全量业务员（可传业务员id集合）")
    @RequestMapping(value = "/api/salesman/listAllSimpleSalesman", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ListAllSimpleSalesmanDTO>> listAllSimpleSalesman(@RequestBody @Valid ListAllSimpleSalesmanInDTO dto);


    //根据业务员编码获取业务员信息
    @PostMapping(value = "/api/salesman/findByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindSalesmanPageAllOutDto>> findSalesmanByCodes(List<String> codeList);


    @RequestMapping(value = "/api/open/salesman/getSalesmanByStoreOrgIdsAndStation", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<GetSalesmanByStoreOrgIdsOutDTO>> getOpenSalesmanByStoreOrgIdsAndStation(@RequestParam("storeId") Long storeId);


    @RequestMapping(value = "/api/store/queryStoreByConditions", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<StoreEntity>> queryStoreByConditions(@RequestBody @Valid SelectStoreByStoreLevelsInDto inDto);


    @RequestMapping(value = "/api/open/findCompanyByOrgIdList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindCompanyAreaByOrgIdsOutDto>> findCompanyByOrgIdList(@RequestBody FindCompanyByIdsInDto findCompanyByIdsInDto);

    /**
     * 获取全量渠道列表
     */
    @RequestMapping(value = "/api/open/channel/getAllChannelList", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<ChannelInfo>> getAllChannelList();

    /**
     * 获取所有频道列表数据
     */
    @RequestMapping(value = "/api/radio/api/open/getAllRadioInfoList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<RadioInfo>> getAllRadioInfoList();

    @RequestMapping(value = {"/api/org/findOrgCombobox"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<PageInfo<OrgComboboxDto>> findOrgCombobox(@RequestBody @Valid FindOrgComboboxDto query);

    @RequestMapping(value = {"/api/org/api/open/findOrgCombobox"}, method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<PageInfo<OrgComboboxDto>> findOrgComboboxOpen(@RequestBody @Valid FindOrgComboboxDto query);


    @PostMapping(value = "/api/org/findNameByIds", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindOrgByIdOutDto>> findNameByIds(FindNameByIdsInDto findNameByIdsInDto);

    @GetMapping("/api/pstore/api/open/findByOrgId")
    public Result<FindPstoreByOrgIdOutDto> findStoreCategoryByOrgId(@RequestParam("orgId") Long orgId);

    @RequestMapping(value = "/api/pstore/api/open/findByStoreIdList", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE,consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindPstoreByOrgIdOutDto>> findByStoreIdList(@RequestBody FindPstoreByOrgIdInDto findPstoreByIdInDto);


    @RequestMapping(value = "/api/salesman/getSalesmanIdsByConditions", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<Long>> getSalesmanIdsByConditions(@RequestBody QuerySalesmanConditionDTO querySalesmanConditionDTO);

    @PostMapping(value = "/api/company/findByIds2")
    public Result<List<FindCompanyByIdOutDto>> findByIds2(@RequestBody List<Long> ids);

    @GetMapping("/api/pstore/api/open/findByAreaId")
    public Result<List<FindPstoreByAreaIdOutDto>> findByAreaId(@RequestParam("provicenId") Long provicenId,

                                                               @RequestParam("cityId") Long cityId,

                                                               @RequestParam("countyId") Long countyId,
                                                               @RequestParam("type") Integer type);


    @GetMapping("/api/decorate/common/api/open/getDecorateDesignerDataScope")
    Result<List<DecorateDesignerDataScopeDTO>> getDecorateDesignerDataScope(@RequestParam("phone") String phone);

    @GetMapping(value = "/api/radio/api/open/getAllChannelRadios",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<RadioInfo>> getAllChannelRadios(@RequestParam("channelId") Integer channelId);

    @RequestMapping(value={"/api/designerClub/getMemberVO"},method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<DesignerClubMemberVO> getMemberVO(@NotNull(message = "参数不能为空") @SpringQueryMap DesignerClubMemberVO vo);


    /**
     *
     * @param findDepAndSubDepUserInDto
     * @return
     */
    @RequestMapping(value="/api/dep/findDepUser2",method= RequestMethod.POST,consumes= MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<SalesmanEntity>> findDepAndsubDepUser2(@RequestBody FindDepAndSubDepUserInDto findDepAndSubDepUserInDto);

    /**
     * 根据id查询业务员 -- 业务员资产信息,(包含业务员信息)
     */
    @RequestMapping(value ="/api/salesman/findSalesmanAssetById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindSalesmanByIdOutDto> findSalesmanAssetById(@RequestParam Long id) ;

    @PostMapping(value="/api/org/api/open/findNameByIds",consumes=MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindOrgByIdOutDto>> findOpenNameByIds(@RequestBody FindNameByIdsInDto findNameByIdsInDto);

    /**
     * 查询渠道大类
     */
    @RequestMapping(value = "/api/distributor/findChannel", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ChannelCategoryDto>> findChannel() ;
    @RequestMapping(value = "/api/designer/club/api/open/member/create", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Long> createDesignerClubMember(@RequestBody CreateDesignerClubMemberDTO request);

    /**
     * 根据编码查询渠道
     */
    @GetMapping(value = "/api/channel/findByCode",consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ChannelEntity>> findByCode(@RequestParam String code);

    /**
     * 根据编码集合查询渠道
     */
    @PostMapping(value = "/api/channel/findByCodes", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ChannelEntity>> findByCodes(@RequestBody Collection<String> codeList);

    /**
     * 根据id集合查询渠道
     */
    @RequestMapping(value = "/api/channel/findByIds", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<FindChannelByIdOutDto>> findChannelByIds2(@RequestBody FindChannelByIdsInDto findChannelByIdsInDto);

    /**
     * 根据能力标签查询业务员id
     */
    @RequestMapping(value = "/api/salesman/api/open/findSalesmanIdByLabelValue", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Result<List<Long>> findSalesmanIdByLabelValue(@Valid @RequestBody FindSalesmanIdByLabelValueInDto dto);
}
