package com.fotile.cmscenter.content.service;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.cmscenter.cases.dao.ContentCaseUserHelpfulDao;
import com.fotile.cmscenter.cases.dao.StatisticsContentBjtMappingDao;
import com.fotile.cmscenter.cases.dao.StatisticsContentBjtMappingDetailDao;
import com.fotile.cmscenter.cases.pojo.entity.ContentCaseUserHelpful;
import com.fotile.cmscenter.cases.pojo.entity.StatisticsContentBjtMappingDTO;
import com.fotile.cmscenter.cases.pojo.entity.StatisticsContentBjtMappingDetailDTO;
import com.fotile.cmscenter.channel.dao.ChannelCategoryDao;
import com.fotile.cmscenter.channel.pojo.entity.Goods;
import com.fotile.cmscenter.client.ContentChannel;
import com.fotile.cmscenter.client.MarketingClient;
import com.fotile.cmscenter.client.OrgClient;
import com.fotile.cmscenter.client.ProductClient;
import com.fotile.cmscenter.client.pojo.QueryChannelGoodsByGoodsIdListDto;
import com.fotile.cmscenter.client.pojo.TChannelDTO;
import com.fotile.cmscenter.client.pojo.marketing.Activity;
import com.fotile.cmscenter.client.pojo.org.DesignerClubMemberVO;
import com.fotile.cmscenter.client.pojo.org.RadioInfo;
import com.fotile.cmscenter.collection.pojo.CollectionDetail;
import com.fotile.cmscenter.collection.service.CollectionDetailService;
import com.fotile.cmscenter.common.KvLl;
import com.fotile.cmscenter.common.constant.CommonConst;
import com.fotile.cmscenter.content.common.ContentConstant;
import com.fotile.cmscenter.content.dao.ContentDao;
import com.fotile.cmscenter.content.dao.ContentGoodsMappingDao;
import com.fotile.cmscenter.content.dao.LabelCmsMappingDao;
import com.fotile.cmscenter.content.dao.StatisticsCmsMappingDao;
import com.fotile.cmscenter.content.pojo.Content;
import com.fotile.cmscenter.content.pojo.StatisticsCmsMapping;
import com.fotile.cmscenter.content.pojo.dto.ChannelRatioDTO;
import com.fotile.cmscenter.content.pojo.dto.CollectionInDto;
import com.fotile.cmscenter.content.pojo.dto.ContentQueryDTO;
import com.fotile.cmscenter.content.pojo.dto.SaveContentInDTO;
import com.fotile.cmscenter.content.pojo.vo.ContentAllMiniVo;
import com.fotile.cmscenter.content.pojo.vo.ContentMiniVo;
import com.fotile.cmscenter.content.pojo.vo.ContentQueryVo;
import com.fotile.cmscenter.content.pojo.vo.designClub.DesignClubContentVO;
import com.fotile.cmscenter.picture.dao.PictureDao;
import com.fotile.cmscenter.picture.pojo.entity.PictureEntity;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2024/8/7 20:44
 */
@Slf4j
@Service
public class DesignClubService {
    private final String DESIGN_CLUE_CODE = "s0031";

    private final String DESIGN_CLUE_C0008 = "C0008";

    private final String DESIGN_CLUE_C0007 = "C0007";
    private final String CONTENT_TABLE_NAME = "content";

    private final Long DESIGNER_WORK_ALBUM=45L;//设计师作品集

    private final Long INK_FOUNTAIN_ID = 7669L;//红墨斗活动id
    @Resource
    private ContentService contentService;
    @Autowired
    private OrgClient orgClient;
    @Resource
    private CollectionDetailService collectService;

    @Autowired
    private MarketingClient marketingClient;
    @Autowired
    private ProductClient productClient;

    @Resource
    private ContentCategoryService contentCategoryService;
    @Resource
    private UserAuthorConfig userAuthorConfig;
    @Resource
    private ContentGoodsMappingDao contentGoodsMappingDao;

    @Resource
    private  ContentDao contentDao;
    @Resource
    private StatisticsCmsMappingDao statisticsCmsMappingDao;
    @Resource(name = ContentChannel.BENEFIT_EVENT_PRODUCER)
    private MessageChannel benefitEventProducer;

    @Resource
    private ChannelCategoryDao channelCategoryDao;
    @Resource
    private LabelCmsMappingDao labelCmsMappingDao;
    @Resource
    private PictureDao pictureDao;
    @Resource
    private ContentCaseUserHelpfulDao caseUserHelpfulDao;
    @Resource
    private StatisticsContentBjtMappingDao statisticsContentBjtMappingDao;
    @Resource
    private StatisticsContentBjtMappingDetailDao bjtMappingDetailDao;



    /**
     * 查询精彩资讯列表
     *
     * @param queryVo
     * @return
     */
    public PageInfo<DesignClubContentVO> queryContentInfoPage(ContentQueryVo queryVo) {
        queryVo.setType("1");
        setChannelId(queryVo);

        List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(queryVo.getChannelId()).getData();
        Long radioId = Optional.ofNullable(radioInfoList)
                .map(l -> l.stream().filter(r -> StringUtils.equals(r.getCode(), DESIGN_CLUE_C0008)).findFirst().orElse(null))
                .map(RadioInfo::getId)
                .orElseThrow(() -> new BusinessException("未找到精彩内容-精彩资讯渠道配置"));
        queryVo.setRadioId(radioId);

        String currentUserId = userAuthorConfig.getCurrentUserId();
        queryVo.setUserId(currentUserId);
        Integer count = contentService.queryDesignClubInfoContentCount(queryVo);
        var pageInfo = new PageInfo<DesignClubContentVO>(queryVo.getPage(), queryVo.getSize());
        pageInfo.setTotal(count);
        if (count > 0) {
            List<DesignClubContentVO> list = contentService.queryDesignClubInfoContent(queryVo, pageInfo);
            fillContentList(list,queryVo);
            pageInfo.setRecords(list);

        }
        return pageInfo;
    }

    /**
     * 查询精彩内容-厨房案例-屋主案例
     *
     * @param queryVo
     * @return
     */
    public PageInfo<DesignClubContentVO> queryHouseContent(ContentQueryVo queryVo){
        if (StringUtils.isBlank(queryVo.getType())) {
            queryVo.setType("1");//图文
        }
        queryVo.setSourceTableType("01");
        queryVo.setContentClassificationId(17l);
        setChannelId(queryVo);
        String currentUserId = userAuthorConfig.getCurrentUserId();
        queryVo.setUserId(currentUserId);


        List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(queryVo.getChannelId()).getData();
        Long radioId = Optional.ofNullable(radioInfoList)
                .map(l -> l.stream().filter(r -> StringUtils.equals(r.getCode(), DESIGN_CLUE_C0007)).findFirst().orElse(null))
                .map(RadioInfo::getId)
                .orElseThrow(() -> new BusinessException("未找到精彩内容-屋主案例频道配置"));
        queryVo.setRadioId(radioId);


        Integer count = contentService.queryDesignClubContentCount(queryVo);
        var pageInfo = new PageInfo<DesignClubContentVO>(queryVo.getPage(), queryVo.getSize());
        pageInfo.setTotal(count);
        if (count > 0) {
            List<DesignClubContentVO> list = contentService.queryDesignClubContent(queryVo, pageInfo);
            fillContentList(list,queryVo);
            pageInfo.setRecords(list);

        }
        return pageInfo;
    }

    public PageInfo<DesignClubContentVO> queryKitchenContent(ContentQueryVo queryVo) {
        if (StringUtils.isBlank(queryVo.getType())) {
            queryVo.setType("6");//厨房图库
        }
        queryVo.setSourceTableType("6");
        if(Objects.isNull(queryVo.getContentClassificationId())){
            queryVo.setContentClassificationId(17l);
        }
        setChannelId(queryVo);

        String currentUserId = userAuthorConfig.getCurrentUserId();
        queryVo.setUserId(currentUserId);

        Integer count = contentService.queryDesignClubContentCount(queryVo);
        var pageInfo = new PageInfo<DesignClubContentVO>(queryVo.getPage(), queryVo.getSize());
        pageInfo.setTotal(count);
        if (count > 0) {
            List<DesignClubContentVO> list = contentService.queryDesignClubContent(queryVo, pageInfo);
            fillContentList(list,queryVo);
            pageInfo.setRecords(list);

        }
        return pageInfo;
    }



    private List<DesignClubContentVO> fillContentList(List<DesignClubContentVO> list,ContentQueryVo queryVo){
        if(CollectionUtils.isEmpty(list)|| Objects.isNull(queryVo.getMemberId())){
            return list;
        }
        String sourceTableType=  Optional.ofNullable(queryVo)
                .map(ContentQueryVo::getSourceTableType)
                .filter(StringUtils::isNotBlank)
                .orElse("01");

        List<Long> contentIds = list.stream().map(DesignClubContentVO::getId).collect(Collectors.toList());
        Map<String, CollectionDetail> collect = collectService.lambdaQuery()
                .eq(CollectionDetail::getIsDeleted, 0)
                .eq(CollectionDetail::getCUserId, queryVo.getMemberId())
                .eq(CollectionDetail::getSourceTableType, sourceTableType)//01是图文类型
                .eq(CollectionDetail::getSourceTableName, CONTENT_TABLE_NAME)
                .in(CollectionDetail::getSourceId, contentIds)
                .list().stream().collect(Collectors.toMap(CollectionDetail::getSourceId, Function.identity()));
        for (DesignClubContentVO designClubContentVO : list) {
            if(collect.containsKey(designClubContentVO.getId().toString())){
                designClubContentVO.setIfCollect(true);
            }
        }

        return list;
    }

    /**
     * 查询厨房案例
     *
     * @param queryVo
     * @return
     */
    public PageInfo<DesignClubContentVO> queryCasePage(ContentQueryVo queryVo) {
        queryVo.setType("16");
          setChannelId(queryVo);
        if (CollectionUtils.isEmpty(queryVo.getSecondCategoryIdList())) {
            throw new BusinessException("参数错误，请传入灵感案例类型");
        }

        Integer count = contentService.queryDesignClubContentCount(queryVo);
        var pageInfo = new PageInfo<DesignClubContentVO>(queryVo.getPage(), queryVo.getSize());
        pageInfo.setTotal(count);
        if (count > 0) {
            List<DesignClubContentVO> list = contentService.queryDesignClubContent(queryVo, pageInfo);
            fillCaseList(list,queryVo);

            pageInfo.setRecords(list);
        }

        return pageInfo;
    }

    private  List<DesignClubContentVO> fillCaseList(List<DesignClubContentVO> list,ContentQueryVo queryVo){
        if(CollectionUtils.isEmpty(list)|| Objects.isNull(queryVo.getMemberId())){
            return list;
        }

        List<Long> caseIds = list.stream().map(DesignClubContentVO::getId).collect(Collectors.toList());
        Map<String, CollectionDetail> collect = collectService.lambdaQuery()
                .eq(CollectionDetail::getIsDeleted, 0)
                .eq(CollectionDetail::getCUserId, queryVo.getMemberId())
                .eq(CollectionDetail::getSourceTableType, "16")//16灵感案例
                .eq(CollectionDetail::getSourceTableName, CONTENT_TABLE_NAME)
                .in(CollectionDetail::getSourceId, caseIds)
                .list().stream().collect(Collectors.toMap(CollectionDetail::getSourceId, Function.identity()));
        for (DesignClubContentVO designClubContentVO : list) {
            if(collect.containsKey(designClubContentVO.getId().toString())){
                designClubContentVO.setIfCollect(true);
            }
        }
        return list;
    }



    public Long getChannelId() {
        TChannelDTO channelDTO = orgClient.findChannelByCode(DESIGN_CLUE_CODE).getData();
        return Optional.ofNullable(channelDTO).map(TChannelDTO::getId).map(Long::valueOf).orElse(null);
    }

    private void setChannelId(ContentQueryVo queryVo) {
        if (Objects.isNull(queryVo.getChannelId())) {
            TChannelDTO channelDTO = orgClient.findChannelByCode(DESIGN_CLUE_CODE).getData();
            Optional.ofNullable(channelDTO).map(TChannelDTO::getId).orElseThrow(() -> new BusinessException("未找到小程序渠道！"));
            queryVo.setChannelId(channelDTO.getId());
        }
        if(StringUtils.isNotBlank(queryVo.getRadioCode()) && Objects.isNull(queryVo.getRadioId())){
            if(Objects.nonNull(queryVo.getChannelId())){
                List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(queryVo.getChannelId()).getData();
                Optional.ofNullable(radioInfoList)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l->l.stream().filter(r->StringUtils.equals(r.getCode(),queryVo.getRadioCode())).findFirst().get())
                        .map(RadioInfo::getId)
                        .ifPresent(queryVo::setRadioId);
            }
        }
    }

    public void shareContent(DesignClubContentVO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getId())||Objects.isNull(dto.getMemberId())||StringUtils.isBlank(dto.getReceiverId())) {
            return;
        }

        //更新分享数量
        statisticsCmsMappingDao.updateShareCountOne(dto.getId(), "content");

        try {
            //发送设计师俱乐部会员分享事件
           /* DesignerClubMemberVO member = orgClient.getMemberVO(new DesignerClubMemberVO()).getData();
            if (Objects.isNull(member) || Objects.isNull(member.getId())) {
                return;
            }*/
            JSONObject param = new JSONObject();
            param.put("eventType", 700);
            param.put("eventJson", JSONObject.toJSONString(
                            new JSONObject()
                                    .fluentPut("contentId", dto.getId())
                                    .fluentPut("memberId", dto.getMemberId())
                                   // .fluentPut("companyOrgId", member.getCompanyOrgId())
                                    .fluentPut("receiverId", dto.getReceiverId())
                    )
            );
            benefitEventProducer.send(MessageBuilder.withPayload(param).build());
        } catch (Exception e) {
            log.error("shareContent:" + e);
        }
    }

    public void surContent(DesignClubContentVO dto) {
        if (Objects.isNull(dto)||Objects.isNull(dto.getId())) {
            return;
        }
        statisticsCmsMappingDao.updateBrowseCount(dto.getId(),"content");

        try {
            //浏览事件
            DesignerClubMemberVO member = orgClient.getMemberVO(new DesignerClubMemberVO()).getData();
            if (Objects.isNull(member)||Objects.isNull(member.getId())){
                return;
            }
            JSONObject param = new JSONObject();
            param.put("eventType", 600);
            param.put("eventJson", JSONObject.toJSONString(new JSONObject().fluentPut("contentId",dto.getId()).fluentPut("memberId",member.getId()).fluentPut("companyOrgId",member.getCompanyOrgId())) );
            benefitEventProducer.send(MessageBuilder.withPayload(param).build());
        }catch (Exception e){
            log.error("surContent:"+e);
        }
    }

    public boolean collect(CollectionInDto inDto) {
        if (Objects.isNull(inDto) || Objects.isNull(inDto.getSourceId())) {
            return false;
        }
        setCollect(inDto);
        Long id = collectService.designClubMemberCollect(inDto);
        return Objects.nonNull(id);
    }

    public void cancelCollect(CollectionInDto inDto) {
        if (Objects.isNull(inDto)) {
            return;
        }
        setCollect(inDto);
        collectService.designClubMemberCancelCollect(inDto);
    }

    private CollectionInDto setCollect(CollectionInDto inDto){
        inDto.setUserId(userAuthorConfig.getCurrentUserId());
       // inDto.setSourceName(CONTENT_TABLE_NAME);
        inDto.setSourceTableName(CONTENT_TABLE_NAME);
        Optional.ofNullable(inDto.getSourceTableType())
                .filter(NumberUtil::isNumber)
                .map(Long::valueOf)
                .ifPresent(inDto::setContentCategoryId);
        if (Objects.isNull(inDto.getType())) {
            inDto.setType(getChannelId());
        }
        return inDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void inkFountainSignUp(SaveContentInDTO saveDTO) throws Exception {
        Optional.ofNullable(saveDTO).orElseThrow(() -> new BusinessException("参数不能为空"));

        TChannelDTO channelDTO = orgClient.findChannelByCode(DESIGN_CLUE_CODE).getData();
        Optional.ofNullable(channelDTO).map(TChannelDTO::getId).orElseThrow(() -> new BusinessException("未配置设计师俱乐部渠道！"));
        List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(channelDTO.getId()).getData();
        Optional.ofNullable(radioInfoList).filter(CollectionUtils::isNotEmpty).orElseThrow(() -> new BusinessException("未配置设计师俱乐部频道！"));
        List<ChannelRatioDTO> channelRatioDTOS = radioInfoList.stream()
                .map(r -> new ChannelRatioDTO().setChannelId(channelDTO.getId().longValue()).setRatioId(r.getId()))
                .collect(Collectors.toList());
        Activity activity = marketingClient.getActivityEntityById(INK_FOUNTAIN_ID).getData();
        Optional.ofNullable(activity).orElseThrow(() -> new BusinessException("未找到红墨斗活动！"));
        saveDTO.setChannelRatioDTOS(channelRatioDTOS);
        saveDTO.setType("1");
        saveDTO.setCtype(0);
        saveDTO.setActivityId(INK_FOUNTAIN_ID);
        saveDTO.setContentClassificationId(DESIGNER_WORK_ALBUM);
        saveDTO.setIsOpenBookmark("0");
        saveDTO.setIsSupportQa("0");
        saveDTO.setIntelligentRecommend(0);
        saveDTO.setSupportSearch(1);
        saveDTO.setIsCard(0);
        saveDTO.setSource(activity.getCode()+"/"+activity.getTitle());
        saveDTO.setIsDeleted(0L);
        saveDTO.setIsSupportSend(1);  //其他情况默认为1
        saveDTO.setAuditingStatus("2");
        contentService.saveContentInfo(saveDTO);
    }

    public List<ContentAllMiniVo> findContentByGoodsId(Long goodsId) {
        List<ContentAllMiniVo> result = Lists.newArrayList();
        if(Objects.isNull(goodsId)){
            return result;
        }
        List<Long> contendIdList = contentGoodsMappingDao.findSourceIdByGoodsIdAndSourceTable(goodsId,"content");
        if(CollectionUtils.isNotEmpty(contendIdList)){
            result = contentDao.getContentListByIdList(contendIdList, ContentConstant.TypeEnum.inspire_case.getCode());
            if(CollectionUtils.isNotEmpty(result)){
                List<Long> sourceIdList = result.stream().map(t -> t.getId()).collect(Collectors.toList());
                List<StatisticsCmsMapping> statisticsDataList = statisticsCmsMappingDao.findBySourceTableAndSourceIdList("content", sourceIdList);
                if(CollectionUtils.isNotEmpty(statisticsDataList)){
                    Map<Long,Long> statisticsDataMap = statisticsDataList.stream().collect(Collectors.toMap(StatisticsCmsMapping::getSourceId,StatisticsCmsMapping :: getBrowseCount,(key1, key2) -> key1));
                    for (ContentAllMiniVo content : result) {
                        Long browseCount = (statisticsDataMap == null || statisticsDataMap.get(content.getId()) == null) ? 0 : statisticsDataMap.get(content.getId());
                        content.setBrowseCount(browseCount);
                    }
                }
            }
        }
        return result;
    }

    public boolean isCollection(ContentQueryVo query) {
      return   collectService.lambdaQuery()
                .eq(CollectionDetail::getIsDeleted, 0)
                .eq(CollectionDetail::getCUserId, query.getMemberId())
                .eq(CollectionDetail::getSourceTableType, query.getType())//16灵感案例
                .eq(CollectionDetail::getSourceTableName, CONTENT_TABLE_NAME)
                .eq(CollectionDetail::getSourceId, query.getId())
                .exists();
    }

    public PageInfo<DesignClubContentVO> queryCollectCasePage(ContentQueryVo queryVo) {
        //queryVo.setType("16");
        setChannelId(queryVo);
        if(Objects.nonNull(queryVo.getChannelId())){
            List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(queryVo.getChannelId()).getData();
            Optional.ofNullable(radioInfoList)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(l->l.stream().filter(r->StringUtils.equals(r.getCode(),DESIGN_CLUE_C0007)).findFirst().get())
                    .map(RadioInfo::getId)
                    .ifPresent(queryVo::setC0007RadioId);
        }


        String currentUserId = userAuthorConfig.getCurrentUserId();
        queryVo.setUserId(currentUserId);
        queryVo.setQueryCollected(true);

        Integer count = contentService.queryDesignClubContentCount(queryVo);
        var pageInfo = new PageInfo<DesignClubContentVO>(queryVo.getPage(), queryVo.getSize());
        pageInfo.setTotal(count);
        if (count > 0) {
            List<DesignClubContentVO> list = contentService.queryDesignClubContent(queryVo, pageInfo);
            fillCaseList(list,queryVo);

            pageInfo.setRecords(list);
        }

        return pageInfo;
    }

    public ContentMiniVo findMiniContentById(Long contentId) {
        ContentMiniVo contentMiniVo = contentDao.findMiniContentById(contentId);
        if (contentMiniVo != null) {
            if(contentMiniVo.getFirstCategoryId() != null){
                contentMiniVo.setFirstCategoryName(channelCategoryDao.findNameById(contentMiniVo.getFirstCategoryId()));
            }
            if(contentMiniVo.getSecondCategoryId() != null){
                contentMiniVo.setSecondCategoryName(channelCategoryDao.findNameById(contentMiniVo.getSecondCategoryId()));
            }
            contentMiniVo.setKeywords(labelCmsMappingDao.findLabelNameBySourceTableAndSourceId("content", contentId));
            List<PictureEntity> pictureEntityList = pictureDao.queryPicBySourceIdAndSourceTable(contentId, "content");
            contentMiniVo.setResourcePictures(pictureEntityList);
            //关联的商品
            List<Long> goodsIdList = contentGoodsMappingDao.findGoodsIdBySourceIdAndSourceTable(contentId, "content");
            if(CollectionUtils.isNotEmpty(goodsIdList)){
                TChannelDTO channelDTO = orgClient.findChannelByCode(DESIGN_CLUE_CODE).getData();
                Optional.ofNullable(channelDTO).map(TChannelDTO::getId).orElseThrow(() -> new BusinessException("未找到小程序渠道！"));
                QueryChannelGoodsByGoodsIdListDto build = QueryChannelGoodsByGoodsIdListDto.builder().channelId(channelDTO.getId().longValue()).goodsIdList(goodsIdList).build();

                Result<List<Goods>> result = productClient.queryChannelGoodsByGoodsIdList(build);
                if(result.getSuccess()){
                    if(CollectionUtils.isNotEmpty(result.getData())){
                        contentMiniVo.setProductCategoryNameList(result.getData().stream().map(Goods::getGoodsCategoryName).collect(Collectors.toSet()));
                    }
                    contentMiniVo.setGoodsList(result.getData());
                }
            }
        }
        return contentMiniVo;
    }

    public PageInfo<ContentQueryDTO> findContents(ContentQueryVo contentQueryVo) {
        PageInfo<ContentQueryDTO> pageInfo = new PageInfo<>(contentQueryVo.getPage(), contentQueryVo.getSize());
        log.info("内容分页请求参数：" + contentQueryVo);



        Integer count =  contentDao.findContentsCount4DesignClub(contentQueryVo);
        Long channelId = getChannelId();
        Optional.ofNullable(channelId)
                        .orElseThrow(() -> new BusinessException("未找到设计师小程序渠道！"));
        contentQueryVo.setChannelId(channelId.intValue());
        contentQueryVo.setSourceTableName(CONTENT_TABLE_NAME);

        pageInfo.setTotal(count);
        List<ContentQueryDTO> list = new ArrayList<>();
        pageInfo.setRecords(list);
        if (count > 0) {
            list = contentDao.findContentsList4DesignClub(contentQueryVo,pageInfo);
            pageInfo.setRecords(list);
            fillCaseContentList(list,contentQueryVo);
        }

        return pageInfo;
    }

    private List<ContentQueryDTO> fillCaseContentList(List<ContentQueryDTO> list,ContentQueryVo queryVo){
        if(CollectionUtils.isEmpty(list)){
            return list;
        }

        //收藏，只统计设计师小程序来源的
        Long channelId = getChannelId();
        List<Long> contentIds = list.stream().map(ContentQueryDTO::getId).collect(Collectors.toList());
        Map<String, CollectionDetail> collect =   Optional.ofNullable(queryVo.getMemberId())
                .map(memberId->
                                collectService.lambdaQuery()
                                        .eq(CollectionDetail::getIsDeleted, 0)
                                        .eq(CollectionDetail::getCUserId, memberId)
                                        .eq(CollectionDetail::getType, channelId)
                                        .eq(CollectionDetail::getSourceTableName, CONTENT_TABLE_NAME)
                                        .in(CollectionDetail::getSourceId, contentIds)
                                        .list().stream().collect(Collectors.toMap(CollectionDetail::getSourceId, Function.identity(),(e1, e2) -> e1))
                        )
                .orElse(Maps.newHashMapWithExpectedSize(0));
        //收藏总数
        CollectionInDto queryCollectCount = new CollectionInDto();
        queryCollectCount.setSourceIds(contentIds);
        queryCollectCount.setSourceTableName(CONTENT_TABLE_NAME);
        queryCollectCount.setType(channelId);
        Map<Long, Long> collectCountMap = collectService.getCollectCount(queryCollectCount).stream().collect(Collectors.toMap(KvLl::getKey, KvLl::getValue));



        Map<Long, Content> contentMap = contentService.lambdaQuery()
                .eq(Content::getIsDeleted, 0)
                .in(Content::getId, contentIds)
                .list().stream().collect(Collectors.toMap(Content::getId, Function.identity()));

        //点赞，只查contentType为7的(设计师小程序)
        Map<Long, ContentCaseUserHelpful> helpfulMap =Maps.newHashMapWithExpectedSize(0);
        if(Objects.nonNull(queryVo.getMemberId())){
            LambdaQueryWrapper<ContentCaseUserHelpful> helpfulQueryWrapper = new QueryWrapper<ContentCaseUserHelpful>()
                    .lambda()
                    .eq(ContentCaseUserHelpful::getIsDeleted, 0)
                    .eq(ContentCaseUserHelpful::getIsHelpful, CommonConst.IsHelpful.Y)
                    .eq(ContentCaseUserHelpful::getUserId, queryVo.getMemberId())
                    .eq(ContentCaseUserHelpful::getContentType,7)
                    .in(ContentCaseUserHelpful::getContentId, contentIds);
            helpfulMap = this.caseUserHelpfulDao.selectList(helpfulQueryWrapper)
                    .stream()
                    .collect(Collectors.toMap(ContentCaseUserHelpful::getContentId, Function.identity(), (e1, e2) -> e1));
        }
        //每个案例的点赞总数(小程序渠道)
        Map<Long, Long> helpTotalCountMap = this.caseUserHelpfulDao.queryCaseListHelpfulCount(contentIds, 7).stream().collect(Collectors.toMap(KvLl::getKey, KvLl::getValue));


        //todo  转发
        StatisticsContentBjtMappingDTO bjtMappingDTO = new StatisticsContentBjtMappingDTO();
        bjtMappingDTO.setContentIds(contentIds);
        bjtMappingDTO.setRelationSourceType(7);
        Map<Long, Long> shareCountMap = statisticsContentBjtMappingDao.selectByContentIds(bjtMappingDTO)
                .stream()
                .collect(Collectors.groupingBy(StatisticsContentBjtMappingDTO::getContentId,Collectors.summingLong(StatisticsContentBjtMappingDTO::getShareCount)));


        Set<Long> shareContentSet = Sets.newHashSet();
        if (Objects.nonNull(queryVo.getMemberId())) {
            StatisticsContentBjtMappingDetailDTO detailDTO = new StatisticsContentBjtMappingDetailDTO();
            detailDTO.setContentIds(contentIds);
            detailDTO.setUserId(queryVo.getMemberId());
            detailDTO.setRelationSourceType(7);
            shareContentSet = bjtMappingDetailDao.getList(detailDTO)
                    .stream()
                    .map(StatisticsContentBjtMappingDetailDTO::getContentId)
                    .collect(Collectors.toSet());
        }



        for (ContentQueryDTO designClubContentVO : list) {
            if(collect.containsKey(designClubContentVO.getId().toString())){
                designClubContentVO.setIfCollect(true);
            }
            if(collectCountMap.containsKey(designClubContentVO.getId())){
                designClubContentVO.setCollectionCount(collectCountMap.get(designClubContentVO.getId()));
            }

            if(helpfulMap.containsKey(designClubContentVO.getId())){
                designClubContentVO.setIfHelpful(true);
            }
            if(helpTotalCountMap.containsKey(designClubContentVO.getId())){
                designClubContentVO.setLikeCount(helpTotalCountMap.get(designClubContentVO.getId()));
            }

            if(shareCountMap.containsKey(designClubContentVO.getId())){
                designClubContentVO.setShareCount(shareCountMap.get(designClubContentVO.getId()));
            }
            if(shareContentSet.contains(designClubContentVO.getId())){
                designClubContentVO.setIfShare(true);
            }

            Content content = contentMap.get(designClubContentVO.getId());
            if(Objects.nonNull(content)&&StringUtils.isNotBlank(content.getOriginalAuthor())){
                designClubContentVO.setOriginalAuthor(content.getOriginalAuthor());
            }
        }
        return list;
    }


    @Transactional
    public Long saveContentInfo(SaveContentInDTO contentVo) throws Exception {
        if (Objects.isNull(contentVo.getActivityId())) {
            throw new BusinessException("活动ID不能为空");
        }

        //校验是否超过活动限制
        Activity activity = marketingClient.getActivityEntityById(contentVo.getActivityId()).getData();
        if (Objects.isNull(activity)) {
            throw new BusinessException("活动不存在");
        }

        if (Objects.nonNull(activity.getLotteryLimit())) {
            String currentUserId = userAuthorConfig.getCurrentUserId();
            Long count = contentService.lambdaQuery()
                    .eq(Content::getIsDeleted, 0)
                    .eq(Content::getActivityId, activity.getId())
                    .eq(Content::getCreatedBy, currentUserId)
                    .eq(Content::getType, 1)
                    .eq(Content::getUploadUserType, 2)
                    .count();
            if (count.intValue() >= activity.getLotteryLimit()) {
                throw new BusinessException("超过活动上传上限");
            }
        }

        Long channelId = getChannelId();
        if (Objects.isNull(channelId)) {
            throw new BusinessException("未找到渠道信息");
        }

        List<RadioInfo> radioInfoList = orgClient.getAllChannelRadios(channelId.intValue()).getData();
        Optional.ofNullable(radioInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .map(l ->
                        l.stream().map(r -> new ChannelRatioDTO().setChannelId(channelId).setRatioId(r.getId()))
                                .collect(Collectors.toList())
                )
                .ifPresent(contentVo::setChannelRatioDTOS);

        DesignerClubMemberVO member = null;
        try {
            member = orgClient.getMemberVO(new DesignerClubMemberVO()).getData();
            if(member!=null){
                contentVo.setMemberGrade(member.getGrade());
            }
        }catch (Exception e){
            log.error("saveContentInfo获取会员信息失败:{}", String.valueOf(e));
        }

        Long contentId = contentService.saveContentInfo(contentVo);
        contentService.lambdaUpdate()
                .eq(Content::getId, contentId)
                .set(Content::getUploadUserType, 2)
                .update();

        try {
            //发送设计师俱乐部会员分享事件
            if (Objects.isNull(member)||Objects.isNull(member.getId())){
                return contentId;
            }

            JSONObject param = new JSONObject();
            param.put("eventType", 800);
            param.put("eventJson",JSONObject.toJSONString(new JSONObject().fluentPut("activityId",activity.getId())
                    .fluentPut("memberId",member.getId())
                    .fluentPut("companyOrgId",member.getCompanyOrgId())
                    .fluentPut("activityType",activity.getType()))
                    );
            benefitEventProducer.send(MessageBuilder.withPayload(param).build());
        }catch (Exception e){
            log.error("saveContentInfo 发送设计师俱乐部会员分享事件异常:{}", String.valueOf(e));
        }

        return contentId;
    }

    public ContentQueryDTO getContentStatistics(ContentQueryVo queryVo) {
        if(Objects.isNull(queryVo)){
            return null;
        }
        ContentQueryDTO contentQueryDTO = new ContentQueryDTO();
        //1、收藏，只统计设计师小程序来源的
        Long channelId = getChannelId();
        boolean collectExists = collectService.lambdaQuery()
                .eq(CollectionDetail::getIsDeleted, 0)
                .eq(CollectionDetail::getCUserId, queryVo.getMemberId())
                .eq(CollectionDetail::getType, channelId)
                .eq(CollectionDetail::getSourceTableName, CONTENT_TABLE_NAME)
                .eq(CollectionDetail::getSourceId, queryVo.getId())
                .exists();
        if(collectExists){
            contentQueryDTO.setIfCollect(collectExists);
        }
        //收藏总数
        CollectionInDto queryCollectCount = new CollectionInDto();
        queryCollectCount.setSourceIds(Lists.newArrayList(queryVo.getId()));
        queryCollectCount.setSourceTableName(CONTENT_TABLE_NAME);
        queryCollectCount.setType(channelId);
        Map<Long, Long> collectCountMap = collectService.getCollectCount(queryCollectCount).stream().collect(Collectors.toMap(KvLl::getKey, KvLl::getValue));
        if(collectCountMap.containsKey(queryVo.getId())){
            contentQueryDTO.setCollectionCount(collectCountMap.get(queryVo.getId()));
        }

        //2、点赞，只查contentType为7的(设计师小程序)
            LambdaQueryWrapper<ContentCaseUserHelpful> helpfulQueryWrapper = new QueryWrapper<ContentCaseUserHelpful>()
                    .lambda()
                    .eq(ContentCaseUserHelpful::getIsDeleted, 0)
                    .eq(ContentCaseUserHelpful::getIsHelpful, CommonConst.IsHelpful.Y)
                    .eq(ContentCaseUserHelpful::getUserId, queryVo.getMemberId())
                    .eq(ContentCaseUserHelpful::getContentType,7)
                    .eq(ContentCaseUserHelpful::getContentId, queryVo.getId());
        boolean helpfulExists = this.caseUserHelpfulDao.exists(helpfulQueryWrapper);
        contentQueryDTO.setIfHelpful(helpfulExists);
        //每个案例的点赞总数(小程序渠道)
        Map<Long, Long> helpTotalCountMap = this.caseUserHelpfulDao.queryCaseListHelpfulCount(Lists.newArrayList(queryVo.getId()), 7)
                .stream()
                .collect(Collectors.toMap(KvLl::getKey, KvLl::getValue));
        if(helpTotalCountMap.containsKey(queryVo.getId())){
            contentQueryDTO.setLikeCount(helpTotalCountMap.get(queryVo.getId()));
        }

        //3、转发
        StatisticsContentBjtMappingDTO bjtMappingDTO = new StatisticsContentBjtMappingDTO();
        bjtMappingDTO.setContentIds(Lists.newArrayList(queryVo.getId()));
        bjtMappingDTO.setRelationSourceType(7);
        Map<Long, Long> shareCountMap = statisticsContentBjtMappingDao.selectByContentIds(bjtMappingDTO)
                .stream()
                .collect(Collectors.groupingBy(StatisticsContentBjtMappingDTO::getContentId, Collectors.summingLong(StatisticsContentBjtMappingDTO::getShareCount)));
        if(shareCountMap.containsKey(queryVo.getId())){
            contentQueryDTO.setShareCount(shareCountMap.get(queryVo.getId()));
        }

            StatisticsContentBjtMappingDetailDTO detailDTO = new StatisticsContentBjtMappingDetailDTO();
            detailDTO.setContentIds(Lists.newArrayList(queryVo.getId()));
            detailDTO.setUserId(queryVo.getMemberId());
            detailDTO.setRelationSourceType(7);
        Set<Long> shareSet = bjtMappingDetailDao.getList(detailDTO)
                .stream()
                .map(StatisticsContentBjtMappingDetailDTO::getContentId)
                .collect(Collectors.toSet());
        if(shareSet.contains(queryVo.getId())){
            contentQueryDTO.setIfShare(true);
        }
        return contentQueryDTO;
    }
}
