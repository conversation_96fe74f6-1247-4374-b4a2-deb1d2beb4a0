package com.fotile.cmscenter.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.content.pojo.FoodInfo;
import com.fotile.cmscenter.content.pojo.dto.FoodQueryDTO;
import com.fotile.cmscenter.content.pojo.vo.FoodQueryVo;
import io.swagger.annotations.Api;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;


import java.util.List;

@Validated
@Api(value = "食材管理实体data实现接口", tags = {"食材管理data实现接口"})
public interface FoodInfoDao extends BaseMapper<FoodInfo> {
    public List<FoodQueryVo> findFoodInfos(FoodQueryDTO foodQueryDTO);

    public Integer findFoodInfosCount(FoodQueryDTO foodQueryDTO);

    public FoodQueryVo findFoodInfoById(@Param("id") Long id);

    public List<FoodQueryVo> findFoodInfosByName(@Param("foodName") String foodName);

    public List<String> selectFoodNameByContentMenuId(@Param("contentMenuId") Long contentMenuId);
}
