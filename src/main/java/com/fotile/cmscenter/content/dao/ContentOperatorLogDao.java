package com.fotile.cmscenter.content.dao;
import org.apache.ibatis.annotations.Param;
import java.util.Collection;

import com.fotile.cmscenter.content.pojo.ContentLog;
import com.fotile.cmscenter.content.pojo.ContentOperatorLog;
import org.springframework.security.core.parameters.P;

import java.util.List;
import java.util.Map;

public interface ContentOperatorLogDao {
    int deleteByPrimaryKey(Long id);

    int insert(ContentOperatorLog record);


    int insertList(@Param("list") List<ContentOperatorLog> list);


    int insertSelective(ContentOperatorLog record);

    ContentOperatorLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContentOperatorLog record);

    int updateByPrimaryKey(ContentOperatorLog record);

    List<ContentOperatorLog> findContentOperatorLogs(Map<String,Object> param);

    List<ContentOperatorLog> findContentOperatorLogsByIds(@Param("sourceIds") List<Long> idList,@Param("code") int code);

    int findContentOperatorLogCount(Map<String,Object> param);

    ContentOperatorLog findalterContentPointLogs(@Param("sourceId") Long id, @Param("sourceTableName") String sourceTableName, @Param("code") int code);
}