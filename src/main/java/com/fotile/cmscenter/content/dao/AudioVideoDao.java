package com.fotile.cmscenter.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.content.pojo.AudioVideo;
import com.fotile.cmscenter.content.pojo.vo.AudioVideoVo;
import io.swagger.annotations.Api;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;

@Validated
@Api(value = "音频视屏管理实体data实现接口", tags = {"音频视屏管理data实现接口"})
public interface AudioVideoDao extends BaseMapper<AudioVideo> {
    public List<AudioVideoVo> selectAudiVideoBySourceId(@Param("sourceId") Long sourceId,
                                                        @Param("type") String type,
                                                        @Param("sourceTable") String sourceTable);

    public List<AudioVideoVo> selectAudiVideoBySourceIdAndSourceTable(@Param("sourceId") Long sourceId,
                                                        @Param("sourceTable") String sourceTable);

    public Integer deleteBySourceTable(@Param("sourceTable") String sourceTable, @Param("sourceId") Long sourceId,
                                       @Param("modifiedBy") String modifiedBy, @Param("modifiedDate") Date modifiedDate);

    public List<AudioVideoVo> selectAudiVideoBySourceIds(@Param("sourceIds") List<Long> sourceIds,
                                                        @Param("type") String type,
                                                        @Param("sourceTable") String sourceTable);


}
