package com.fotile.cmscenter.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.content.pojo.RecuperationMethodMapping;
import com.fotile.cmscenter.content.pojo.TContentSkillsEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TContentSkillsEntityMapper extends BaseMapper<TContentSkillsEntity> {
    int deleteByPrimaryKey(Long id);


    int insertOrUpdate(TContentSkillsEntity record);

    int insertOrUpdateSelective(TContentSkillsEntity record);

    int insertSelective(TContentSkillsEntity record);

    TContentSkillsEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TContentSkillsEntity record);

    int updateByPrimaryKey(TContentSkillsEntity record);

    int updateBatch(List<TContentSkillsEntity> list);

    int updateBatchSelective(List<TContentSkillsEntity> list);

    int batchInsert(@Param("list") List<TContentSkillsEntity> list);

    public Integer deleteContentSkillsByContentMenuId(@Param("contentMenuId") Long contentMenuId);

    /**
     * @Entity com.fotile.cmscenter.content.pojo.RecuperationMethodMapping
     */
    interface RecuperationMethodMappingMapper {

        int deleteByPrimaryKey(Long id);

        int insert(RecuperationMethodMapping record);

        int insertSelective(RecuperationMethodMapping record);

        RecuperationMethodMapping selectByPrimaryKey(Long id);

        int updateByPrimaryKeySelective(RecuperationMethodMapping record);

        int updateByPrimaryKey(RecuperationMethodMapping record);

    }
}