package com.fotile.cmscenter.content.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

@Data
@Entity
@ApiModel(value = "内容、内容关联商品分享、查看统计表-方太官方小程序", description = "内容、内容关联商品分享、查看统计表-方太官方小程序")
@TableName(value = "statistics_content_ft_applet_mapping_detail")
public class StatisticsContentFTAppletMappingDetailEntity extends AuditingEntity {
    @TableField("content_id")
    @ApiModelProperty(value = "内容表主键id")
    private Long contentId;

    @TableField("user_id")
    @ApiModelProperty(value = "导购id即业务员id")
    private Long userId;

    @TableField("open_id")
    @ApiModelProperty(value = "微信openid")
    private String openId;

    @TableField("external_user_id")
    @ApiModelProperty(value = "发送对象（微信externalUserId）")
    private String externalUserId;

    @TableField("type")
    @ApiModelProperty(value = "统计类型：1-分享 2-查看")
    private Integer type;

    @TableField("source_type")
    @ApiModelProperty(value = "分享的来源类型：1-导购端 2-顾客端 默认2")
    private Integer sourceType;

    @Column(name = "calc_time")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "统计时间")
    private Date calcTime;

    @TableField("first_category_id")
    @ApiModelProperty(value = "灵感分类一级id（冗余存储内容信息）")
    private Long firstCategoryId;

    @TableField("second_category_id")
    @ApiModelProperty(value = "灵感分类二级id（冗余存储内容信息）")
    private Long secondCategoryId;

    @TableField("first_category_name")
    @ApiModelProperty(value = "灵感分类一级名称")
    private String firstCategoryName;

    @TableField("second_category_name")
    @ApiModelProperty(value = "灵感分类二级名称")
    private String secondCategoryName;

    @TableField("good_code")
    @ApiModelProperty(value = "商品编码（冗余存储内容关联商品）")
    private String goodCode;
}
