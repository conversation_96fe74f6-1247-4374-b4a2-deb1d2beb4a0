package com.fotile.cmscenter.content.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @package com.fotile.cmscenter.content.pojo.dto
 * @date 2022/10/11 9:46
 */
@Data
public class QueryPropertyParameterInDTO implements Serializable {
    /**
     * 属性参数ID
     */
    private Long id;

    /**
     * 属性参数ID集合
     */
    private List<Long> idList;

    /**
     * 属性ID
     */
    private Long propertyId;

    /**
     * 属性ID集合
     */
    private List<Long> propertyIdList;

    /**
     * 是否需要返回删除的数据(0 or null:不需要,1:需要,默认不需要)
     */
    private Integer needReturnDeletedRecord;
}
