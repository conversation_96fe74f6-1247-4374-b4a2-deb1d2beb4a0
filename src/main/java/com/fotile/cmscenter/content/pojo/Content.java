package com.fotile.cmscenter.content.pojo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.cmscenter.util.CompareFieldLog;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@ApiModel(value = "内容实体类", description = "内容实体类")
@TableName(value = "content")
public class Content extends AuditingEntity {

    @TableId(value = "id")
    @ApiModelProperty(value = "关键字")
    private Long id;

    @TableField(value = "keyword")
    @ApiModelProperty(value = "关键字")
    private String keyword;

    @TableField(value = "type")
    @ApiModelProperty(value = "内容类型  1.图文 2.外链 3.活动 4:音视频 6：组图 7：直播 8:厨房案例")
    @NotNull(message = "类型不能为空")
    private String type;


    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    @NotNull(message = "标题不能为空")
    @CompareFieldLog(operateDetail = "标题")
    private String title;
    @TableField(value = "inside_title")
    @ApiModelProperty(value = "内标题")
    @CompareFieldLog(operateDetail = "内标题")
    private String insideTitle;
    @TableField(value = "cover_url")
    @ApiModelProperty(value = "封面图")
    @NotNull(message = "封面图不能为空")
    private String coverUrl;

    @TableField(value = "square_url")
    @ApiModelProperty(value = "方图地址")
    private String squareUrl;

    @TableField(value = "shared_url")
    @ApiModelProperty(value = "分享图地址")
    private String sharedUrl;
    @TableField(value = "shared_words")
    @ApiModelProperty(value = "分享文字")
    @CompareFieldLog(operateDetail = "分享文字")
    private String sharedWords;

    @TableField(value = "content_classification_id")
    @ApiModelProperty(value = "内容分类id")
    private Long contentClassificationId;

    @TableField(value = "activity_id")
    @ApiModelProperty(value = "活动Id")
    private Long activityId;

    @TableField(value = "sort")
    @ApiModelProperty(value = "序号 -- 排序")
    private Long sort;

    @TableField(value = "auditing_status")
    @ApiModelProperty(value = "审核状态  1:待提交 2:待审核 3:审核成功 4:审核失败")
    private String auditingStatus;

    @TableField(value = "online_status")
    @ApiModelProperty(value = "在线状态  0:下线 1:上线")
    private String onlineStatus;
    @TableField(value = "online_time")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    @TableField(value = "source")
    @ApiModelProperty(value = "来源")
    @CompareFieldLog(operateDetail = "来源")
    private String source;

    @TableField(value = "summary")
    @ApiModelProperty(value = "摘要")
    @CompareFieldLog(operateDetail = "摘要")
    private String summary;

    @TableField(value = "content_text")
    @ApiModelProperty(value = "正文")
    private String contentText;

    @TableField(value = "skip_url")
    @ApiModelProperty(value = "跳转地址")
    private String skipUrl;


    @TableField(value = "start_time")
    @ApiModelProperty(value = "内容开始时间")
    private Date startTime;

    @TableField(value = "end_time")
    @ApiModelProperty(value = "内容结束时间")
    private Date endTime;

    @TableField(value = "panorama_url")
    @ApiModelProperty(value = "全景图地址")
    private String panoramaUrl;

    @TableField(value = "is_open_bookmark")
    @ApiModelProperty(value = "是否开启书签 0：不开启 1：开始")
    private String isOpenBookmark;

    @TableField(value = "bookmark_title")
    @ApiModelProperty(value = "书签标题")
    private String bookmarkTitle;

    @TableField(value = "bookmark_content")
    @ApiModelProperty(value = "书签内容")
    private String bookmarkContent;

    @TableField(value = "bookmark_abstract")
    @ApiModelProperty(value = "书签摘要")
    private String bookmarkAbstract;

    @TableField(value = "bookmark_like_count")
    @ApiModelProperty(value = "书签点赞数")
    private Long bookmarkLikeCount;

    @TableField(value = "recommend_content_id")
    @ApiModelProperty(value = "推荐内容ID 填写内容 ID，使用,分割，最多取前5个")
    private String recommendContentId;

    @TableField(value = "is_support_qa")
    @ApiModelProperty(value = "是否支持问答 0：不支持 1：支持")
    private String isSupportQa;

    @TableField(value = "qa_id")
    @ApiModelProperty(value = "置顶问答ID 多个问题id用分号间隔")
    private String qaId;

    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    @CompareFieldLog(operateDetail = "备注")
    private String remark;

    @TableField(value = "original_author")
    @ApiModelProperty(value = "原作者")
    private String originalAuthor;

    @TableField(value = "panorama_cover_url")
    @ApiModelProperty(value = "全景图封面图")
    private String panoramaCoverUrl;

    @TableField(value = "created_person")
    @ApiModelProperty(value = "创建人")
    private String createdPerson;

    @TableField(value = "is_card")
    @ApiModelProperty(value = "是否为卡片,0不是1是")
    private Integer isCard;

    @TableField(value = "refuse_note")
    @ApiModelProperty("拒绝原因")
    private String refuseNote;

    @TableField(value = "ctype")
    @ApiModelProperty(value = "组图-类型")
    private Integer ctype;

    @TableField(value = "kit_area")
    @ApiModelProperty(value = "组图-厨房面积")
    private String kitArea;
    @TableField(value = "kit_layout")
    @ApiModelProperty(value = "组图布局")
    private String kitLayout;
    @TableField(value = "kit_style")
    @ApiModelProperty(value = "组图风格")
    private String kitStyle;
    @TableField(value = "poster")
    @ApiModelProperty(value = "海报图")
    private String poster;
    @TableField(value = "erect_url")
    @ApiModelProperty(value = "竖版封面图")
    private String erectUrl;
    @TableField(value = "kit_product")
    @ApiModelProperty(value = "厨电产品")
    private String kitProduct;
    @TableField(value = "kit_installation")
    @ApiModelProperty(value = "组图安装方式")
    private String kitInstallation;


    //2020/11/25迭代
    /**
     * 直播时长
     */
    @TableField(value = "live_time")
    private String liveTime;
    /**
     * 直播链接
     */
    @TableField(value = "live_url")
    private String liveUrl;
    /**
     * 直播开始时间
     */
    @TableField(value = "live_start_time")
    private Date liveStartTime;

    /**
     * 图文长图
     */
    @TableField(value = "long_picture")
    private String longPicture;
    /**
     * tag图id
     */
    @TableField(value = "tag_picture_id")
    private String tagPictureId;
    /**
     * tag图url
     */
    @TableField(value = "tag_picture")
    private String tagPicture;

    /**
     * 厨电类型
     */
    @TableField(value = "kit_type")
    private String kitType;

    /**
     * 视频版式 0 横版 1 竖版
     */
    @TableField(value = "format")
    private Integer format;

    /**
     * 标签 多个以,隔开
     */
    @TableField(value = "label")
    @CompareFieldLog(operateDetail = "标签")
    private String label;


    /**
     * 2021年4月19日迭代
     */

    @ApiModelProperty(value = "是否付费  1免费，2VIP会员免费，3全员付费")
    @TableField(value = "is_paid")
    private String isPaid;

    @ApiModelProperty(value = "原价")
    @TableField(value = "original_price")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "售价")
    @TableField(value = "selling_price")
    private BigDecimal sellingPrice;

    @ApiModelProperty(value = "售价说明")
    @TableField(value = "price_description")
    private String priceDescription;

    @ApiModelProperty(value = "试听/看时长，单位秒")
    @TableField(value = "trial_duration")
    private String trialDuration;

    @ApiModelProperty("搜索权重")
    private Integer searchWeight;


    @ApiModelProperty("形成原因")
    @TableField(value = "cause_formation")
    private String causeFormation;
    @ApiModelProperty("何时就医")
    @TableField(value = "medical_treatment_time")
    private String medicalTreatmentTime;
    @ApiModelProperty("生活建议")
    @TableField(value = "life_advice")
    private String lifeAdvice;
    @ApiModelProperty("食疗方法标题")
    @TableField(value = "therapy_method_title")
    private String therapyMethodTitle;
    @ApiModelProperty("食疗方法描述")
    @TableField(value = "therapy_method_description")
    private String therapyMethodDescription;
    @ApiModelProperty("动作方法标题")
    @TableField(value = "action_method_title")
    private String actionMethodTitle;
    @ApiModelProperty("动作方法描述")
    @TableField(value = "action_method_description")
    private String actionMethodDescription;
    @ApiModelProperty("外用方法标题")
    @TableField(value = "topical_method_title")
    private String topicalMethodTitle;
    @ApiModelProperty("外用方法描述")
    @TableField(value = "topical_method_description")
    private String topicalMethodDescription;

    @ApiModelProperty("会员售价类型 0免费 1付费")
    @TableField(value = "member_price_type")
    private Long memberPriceType;
    @ApiModelProperty("会员售价")
    @TableField(value = "member_price")
    private BigDecimal memberPrice;
//    @ApiModelProperty(value = "仅导购工作台可见 0否 1是")
//    private Integer isView;

    @ApiModelProperty(value = "需要智能推荐")
    private Integer intelligentRecommend;

    @ApiModelProperty(value = "厨电组图-面积")
    @TableField(value = "area_desc")
    private String areaDesc;
    @ApiModelProperty(value = "厨电组图-布局")
    @TableField(value = "layout_desc")
    private String layoutDesc;
    @ApiModelProperty(value = "厨电组图-风格")
    @TableField(value = "style_desc")
    private String styleDesc;
    @ApiModelProperty(value = "厨电组图-类型")
    @TableField(value = "type_desc")
    private String typeDesc;


    // 2022 08 09 新增
    @ApiModelProperty(value = "厨房案例类型 1：换装 2：新装")
    @TableField(value = "kitchen_case_type")
    @CompareFieldLog(operateDetail = "案例类型", options = "1|换装;2|新装")
    private Long kitchenCaseType;

    @ApiModelProperty(value = "门店编码")
    @TableField(value = "store_code")
    private String storeCode;

    @ApiModelProperty(value = "门店简称")
    @TableField(value = "store_name")
    @CompareFieldLog(operateDetail = "门店")
    private String storeName;

    @ApiModelProperty(value = "描述")
    @TableField(value = "case_describe")
    @CompareFieldLog(operateDetail = "案例描述")
    private String caseDescribe;

    @ApiModelProperty(value = "省id")
    @TableField(value = "province_id")
    private Long provinceId;

    @ApiModelProperty(value = "省名称")
    @TableField(value = "province_name")
    @CompareFieldLog(operateDetail = "省名称")
    private String provinceName;

    @ApiModelProperty(value = "市id")
    @TableField(value = "city_id")
    private Long cityId;

    @ApiModelProperty(value = "市名称")
    @TableField(value = "city_name")
    @CompareFieldLog(operateDetail = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区id")
    @TableField(value = "area_id")
    private Long areaId;

    @ApiModelProperty(value = "区/县名称")
    @TableField(value = "area_name")
    @CompareFieldLog(operateDetail = "区/县名称")
    private String areaName;

    @ApiModelProperty(value = "所在小区id")
    @TableField(value = "community_id")
    private Long communityId;

    @ApiModelProperty(value = "所在小区名称")
    @TableField(value = "community_name")
    @CompareFieldLog(operateDetail = "所在小区名称")
    private String communityName;

    @ApiModelProperty(value = "案例推荐 1是，2否")
    @TableField(value = "case_recommend")
    @CompareFieldLog(operateDetail = "产品相关案例推荐", options = "1|否;2|是")
    private Long caseRecommend;

    @ApiModelProperty(value = "是否推荐到首页 0否 1是")
    @TableField(value = "is_recommend_home")
//    @CompareFieldLog(operateDetail = "案例列表页推荐", options = "0|否;1|是")
    private Long isRecommendHome;

    @ApiModelProperty(value = "否支持发送 0-否 1-是 默认为是")
    @TableField(value = "is_support_send")
//    @CompareFieldLog(operateDetail = "支持发送", options = "0|否;1|是")
    private Integer isSupportSend;

    @ApiModelProperty(value = "厨房案例推荐时间")
    @TableField(value = "recommend_home_time")
    private Date recommendHomeTime;

    @TableField(value = "is_draft")
    @ApiModelProperty(value = "是否为草稿 0否 1是")
    private Integer isDraft;

    @TableField(value = "store_address")
    @ApiModelProperty(value = "门店地址")
    private String storeAddress;


    @TableField(value = "support_search")
    @ApiModelProperty(value = "支持被搜索 0:否 1:是")
    private Integer supportSearch;

    //25145-案例增加痛点维护
    @TableField(value = "pain_spot")
    @ApiModelProperty(value = "痛点维护")
    @CompareFieldLog(operateDetail = "痛点")
    private String painSpot;

    @TableField(value = "renovation_project")
    @ApiModelProperty(value = "改造项")
    @CompareFieldLog(operateDetail = "改造项")
    private String renovationProject;

    @TableField(value = "longitude")
    @ApiModelProperty(value = "小区经度")
    private BigDecimal longitude;

    @TableField(value = "latitude")
    @ApiModelProperty(value = "小区纬度")
    private BigDecimal latitude;

    /**
     * 案例视频url
     */
    @TableField(value = "case_video_url")
    @ApiModelProperty(value = "案例视频url")
    @CompareFieldLog(operateDetail = "案例头图视频")
    private String caseVideoUrl;

    /**
     * 案例视频封面图
     */
    @TableField(value = "case_video_cover_url")
    @ApiModelProperty(value = "案例视频封面图")
    @CompareFieldLog(operateDetail = "案例头图视频封面")
    private String caseVideoCoverUrl;

    /**
     * 案例详情视频url
     */
    @TableField(value = "case_detail_video_url")
    @ApiModelProperty(value = "案例详情视频url")
    @CompareFieldLog(operateDetail = "案例详情视频")
    private String caseDetailVideoUrl;

    /**
     * 案例详情视频封面图url
     */
    @TableField(value = "case_detail_video_cover_url")
    @ApiModelProperty(value = "案例详情视频封面图url")
    @CompareFieldLog(operateDetail = "案例详情视频封面")
    private String caseDetailVideoCoverUrl;

    @TableField(value = "upload_user_type")
    @ApiModelProperty(value = "上传类型 01:中台上传 02：用户上传")
    private String uploadUserType;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "user_entity_id")
    private String userId;

    /**
     * 显示类型
     */
    @TableField(value = "show_type")
    @ApiModelProperty(value = "显示类型（1分公司、2全国）")
//    @CompareFieldLog(operateDetail = "显示类型", options = "1|分公司;2|全国")
    private Long showType;


    /**
     * 灵感视频url
     */
    @TableField(value = "inspire_video_url")
    private String inspireVideoUrl;
    /**
     * 灵感视频封面url
     */
    @TableField(value = "inspire_video_cover_url")
    private String inspireVideoCoverUrl;
    /**
     * 灵感分类一级id
     */
    @TableField(value = "first_category_id")
    private Long firstCategoryId;
    /**
     * 灵感分类二级id
     */
    @TableField(value = "second_category_id")
    private Long secondCategoryId;
    /**
     * 是否产品使用内容 0:否 1:是
     */
    @TableField(value = "is_product_use_content")
    private Integer isProductUseContent;
    /**
     * 是否同步内容工场 0：否 1：是
     */
    @TableField(value = "is_sync_material")
    private Integer isSyncMaterial;
    /**
     * 同步的基础素材id
     */
    @TableField(value = "sync_basic_material_id")
    private Long syncBasicMaterialId;
    /**
     * 同步的内容树id
     */
    @TableField(value = "sync_material_id")
    private Integer syncMaterialId;

    /**
     * 是否授权 0:拒绝授权 1:同意授权
     */
    private Integer isAuthorization;

    /**
     * 公众号链接
     */
    @TableField(value = "official_account_url")
    private String officialAccountUrl;

    /**
     * VR实景链接-装前效果(同时beforeVrUrl和vrUrl 其中一个有值 则显示vr标签（非null 非字符串空表示有值）)
     */
    @TableField(value = "before_vr_url")
    private String beforeVrUrl;

    /**
     * VR实景封面图-装前效果
     */
    @TableField(value = "before_vr_cover_url")
    private String beforeVrCoverUrl;

    /**
     * VR实景链接-装后效果(同时beforeVrUrl和vrUrl 其中一个有值 则显示vr标签（非null 非字符串空表示有值）)
     */
    @TableField(value = "vr_url")
    private String vrUrl;

    /**
     * VR实景封面图-装后效果
     */
    @TableField(value = "vr_cover_url")
    private String vrCoverUrl;

    /**
     * 自定义跳转图片url
     */
    @TableField(value = "custom_jump_pic_url")
    private String customJumpPicUrl;

    /**
     * 自定义跳转url
     */
    @TableField(value = "custom_jump_url")
    private String customJumpUrl;

    /**
     * 自定义跳转渠道
     */
    @TableField(value = "custom_jump_channel")
    private String customJumpChannel;


    /**
     * 案例活动邀请报名所属业务员ID
     */
    @TableField(value = "invite_salesman_id")
    private Long inviteSalesmanId;

    /**
     * 设计师会员等级(1:注册会员; 2:认证会员; 默认1)
     */
    @TableField(value = "member_grade")
    private String memberGrade;
    /**
     * 是否显示封面图 0：否 1：是
     */
    @TableField(value = "is_show_cover_url")
    private Integer isShowCoverUrl;
}
