package com.fotile.cmscenter.content.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fotile.framework.core.common.AuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@ApiModel(value = "内容与作者关系表",description = "内容与作者关系表")
@TableName(value = "content_author")
public class ContentAuthor extends AuditingEntity {

    @TableField(value="content_id")
    @ApiModelProperty(value="内容Id")
    private Long contentId;
    @TableField(value="author_id")
    @ApiModelProperty(value="作者Id")
    private Long authorId;

    @TableField(value="author_name")
    @ApiModelProperty(value="作者名称")
    private String authorName;
    @TableField(value="author_type")
    @ApiModelProperty(value="作者类型  1.顾客 2.管理员")
    private Integer authorType;
}
