package com.fotile.cmscenter.content.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FoodQueryVo implements Serializable {
    @ApiModelProperty(value = "食材id")
    private Long id;
    @ApiModelProperty(value = "食材名称")
    private String foodName;
    @ApiModelProperty(value = "食材所属分类 00:空 01:猪肉 02：蛋类 03:蔬菜类 04：水果类")
    private String foodCategory;
    @ApiModelProperty(value = "食材图片")
    private String foodPicture;
    @ApiModelProperty(value = "食物描述")
    private String foodDescribe;
    @ApiModelProperty(value = "备注")
    private String note;
    @ApiModelProperty(value = "是否启用 0、禁用 1、启动")
    private String stage;
    @ApiModelProperty("创建时间")
    private Date createdDate;
}
