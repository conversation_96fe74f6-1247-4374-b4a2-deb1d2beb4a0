package com.fotile.cmscenter.content.controller;

import com.fotile.cmscenter.content.pojo.ContentOperatorLog;
import com.fotile.cmscenter.content.service.ContentService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RequestMapping("/api/conentOperatorLog")
@RestController
@Api(value = "内容分类接口", tags = {"内容分类接口"})
@Slf4j
public class ConentOperatorLogController  extends BaseController {
    @Autowired
    private ContentService contentService;

    @ApiOperation(value = "新增操作日志")
    @RequestMapping(value = "insertContnetOperatorLog", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> insertContnetOperatorLog(@RequestBody ContentOperatorLog contentOperatorLog) {
        log.info("ContentQueryVO:" + contentOperatorLog);
//        if (contentQueryVo.getChannelIds() == null || contentQueryVo.getChannelIds().size() == 0) {
//            List<AddChannelRatioDTO> channelRatioDTOS = contentService.getChannelRatioDTOs();
//            if (channelRatioDTOS != null && channelRatioDTOS.size() != 0) {
//                contentQueryVo.setChannelIds(channelRatioDTOS);
//            }
//        }
//        return success(contentService.findContents(contentQueryVo));
        contentService.insertContnetOperatorLog(contentOperatorLog.getSourceTableName(),contentOperatorLog.getSourceId(),String.valueOf(contentOperatorLog.getOperatorType()),  contentOperatorLog.getDescription());
        return success("操作成功");
    }
}
