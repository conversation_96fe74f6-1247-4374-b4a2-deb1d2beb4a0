package com.fotile.cmscenter.content.controller;

import com.fotile.cmscenter.content.common.ContentConstant;
import com.fotile.cmscenter.content.dao.ContentOperatorLogDao;
import com.fotile.cmscenter.content.kafka.ContentMenuPushService;
import com.fotile.cmscenter.content.kafka.ContentPushService;
import com.fotile.cmscenter.content.pojo.ContentMenu;
import com.fotile.cmscenter.content.pojo.ContentOperatorLog;
import com.fotile.cmscenter.content.pojo.dto.*;
import com.fotile.cmscenter.content.pojo.vo.ContentDetailVo;
import com.fotile.cmscenter.content.pojo.vo.ContentQueryVo;
import com.fotile.cmscenter.content.pojo.vo.GetContentVo;
import com.fotile.cmscenter.content.service.ContentAIService;
import com.fotile.cmscenter.content.service.ContentMenuService;
import com.fotile.cmscenter.content.service.ContentService;
import com.fotile.cmscenter.util.LogUtils;
import com.fotile.cmscenter.yard.dto.ContentOrSpecialIDS;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 内容接口
 */
@RequestMapping("/api/open/contentAI")
@RestController
@Slf4j
public class ContentAIController extends BaseController {


    @Autowired
    private ContentAIService contentAIService;

    @InitBinder
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * 内容列表分页查询
     * 提供给大数据部门智慧导购AI
     * @param type 1全量 2最近5分钟的数据
     * @return
     */
    @ApiOperation(value = "内容列表查询")
    @RequestMapping(value ="/queryContentsAI", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<ContentQueryAIDTO>> queryContentsAI(@RequestParam("type") String type) {

        String className = new Exception().getStackTrace()[0].getClassName();
        String methodName = new Exception().getStackTrace()[0].getMethodName();//a

        try {

            List<ContentQueryAIDTO> l = contentAIService.findContentsAI(type);
            return success("查询成功",l);
        } catch (Exception e) {
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw, true));
            log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", sw);
            return failure(e instanceof BusinessException ? e.getMessage() : "查询异常, 请联系管理员");
        }
    }





}
