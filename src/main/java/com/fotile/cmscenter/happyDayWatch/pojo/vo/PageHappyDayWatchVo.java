package com.fotile.cmscenter.happyDayWatch.pojo.vo;

import lombok.Data;

import java.util.Date;

@Data
public class PageHappyDayWatchVo {

    private Long id;
    /**
     * 创建人keycloak账户ID
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改时间
     */
    private Date modifiedDate;
    /**
     * 修改人keycloak账户ID
     */
    private String modifiedBy;

    private Integer isDeleted;
    /**
     * 编码
     */
    private String code;
    /**
     * 标题
     */
    private String title;
    /**
     * 封面图地址
     */
    private String coverUrl;
    /**
     * 分享图地址
     */
    private String sharedUrl;
    /**
     * 分享文字
     */
    private String sharedWords;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 有效时间-开始
     */
    private Date startTime;
    /**
     * 有效时间-结束
     */
    private Date endTime;
    /**
     * 审核状态  1:待提交 2:待审核 3:审核成功 4:审核失败
     */
    private Integer auditingStatus;
    /**
     * 在线状态  ：1.上线 0.下线
     */
    private Integer onlineStatus;
    /**
     * 上线时间
     */
    private Date onlineTime;
    /**
     * 序号
     */
    private Long sort;
    /**
     * 备注
     */
    private String remark;
    /**
     * 类型
     */
    private String type;
    /**
     * 色值
     */
    private String color;
}
