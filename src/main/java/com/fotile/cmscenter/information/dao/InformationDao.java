package com.fotile.cmscenter.information.dao;

import com.fotile.cmscenter.adviertisement.pojo.dto.SetSortInDto;
import com.fotile.cmscenter.content.pojo.LabelCms;
import com.fotile.cmscenter.content.pojo.dto.IsUsefulInDto;
import com.fotile.cmscenter.information.pojo.dto.*;
import com.fotile.cmscenter.special.pojo.entity.ChannelAndRadio;
import com.fotile.cmscenter.special.pojo.entity.ChannelRadio;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @ClassName InformationDao
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/2/27
 * @Version
 **/
public interface InformationDao {
    List<QueryAllInformationsOutDto> queryAllInformations(QueryAllInformationsInDto queryAllInformationsInDto);

    Long queryAllInformationsCount(QueryAllInformationsInDto queryAllInformationsInDto);

    List<LabelCms> queryAllLabels();

    QueryInformationByIdOutDto queryInformationById(QueryInformationByIdInDto queryInformationByIdInDto);

    List<LabelCms> queyLabel(QueryInformationByIdInDto queryInformationByIdInDto);

    List<ChannelRadio> queryRadioIds(QueryInformationByIdInDto queryInformationByIdInDto);

    void addInformation(SavaOrUpdateInformationInDto savaOrUpdateInformationInDto);

    void addLabel(LabelCms labelCms);

    void addLabelMapping(@Param("sourceId") Long sourceId, @Param("labelIds") Set<Long> labelIds, @Param("createdBy") String createdBy);

    void addChannelAndRadio(@Param("id") Long id, @Param("channelRadio") List<ChannelRadio> channelRadio, @Param("createdBy") String createdBy);

    void updateInformation(SavaOrUpdateInformationInDto savaOrUpdateInformationInDto);

    void delLabels(@Param("id") Long id, @Param("modifiedBy") String modifiedBy);

    void delChannelAndRadioMapping(@Param("id") Long id, @Param("modifiedBy") String modifiedBy);

    List<ChannelAndRadio> queryChannelAndRadio(@Param("id") Long id);

    Long setIsEssence(SetIsEssenceInDto setIsEssenceInDto);

    Long setStatus(SetStatusInDto setStatusInDto);

    List<Long> queryChannels(@Param("id") Long id);

    Long setShowBrower(SetShowBrowerInDto setUsefulInDto);

    void setStatics(@Param("id") Long id, @Param("createdBy") String createdBy);

    Long setSort(SetSortInDto sortInDto);

    List<QueryWebsiteInformationsOutDto> queryAllInformationsWebsite(QueryWebsiteInformationsInDto queryInformationsInDto);

    Long queryAllInformationsWebsiteCount(QueryWebsiteInformationsInDto queryInformationsInDto);

    List<QueryWebsiteInformationsOutDto> selectInformationByIds(@Param("ids") String ids);

    List<AdjacentPages> getAdjacentPages(@Param("id") Long id, @Param("firstType") Integer firstType, @Param("secondType") String secondType, @Param("channelId") Long channelId);

    Long isUseful(IsUsefulInDto isUsefulInDto);

    List<ChannelRadio> queryChannelRadios();

    List<QueryWebsiteInformationsOutDto> queryAllInformationsWebsite1(QueryWebsiteInformationsInDto queryInformationsInDto);

    List<QueryWebsiteInformationsOutDto> selectByIds(@Param("ids") List<Long> ids);
}
