package com.fotile.cmscenter.scheme.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExplainRecordForAmountDTO implements Serializable {

    private Long id;

    /**
     * 方案id
     */
    private Long schemeId;

    /**
     * 线索id
     */
    private Long cluesId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;



}
