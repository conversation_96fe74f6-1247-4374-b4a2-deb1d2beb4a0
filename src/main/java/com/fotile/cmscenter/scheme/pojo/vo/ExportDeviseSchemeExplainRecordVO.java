package com.fotile.cmscenter.scheme.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExportDeviseSchemeExplainRecordVO implements Serializable {

    @ColumnWidth(10)
    @ExcelProperty(value = {"方案编码"}, index = 0)
    @ApiModelProperty(value = "方案编码")
    private String projectCode;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案标题"}, index = 1)
    @ApiModelProperty(value = "方案标题")
    private String projectName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"省市区"}, index = 2)
    @ApiModelProperty(value = "省")
    private String provinceName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"小区名称"}, index = 3)
    @ApiModelProperty(value = "小区")
    private String villageName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"方案类型"}, index = 4)
    private String typeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"关联线索"}, index = 5)
    private String relationClues;


    /**
     * 关联线索时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"线索关联时间"}, index = 6)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date relatedCluesTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解次数"}, index = 7)
    private Integer explainCount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"总讲解时长（分）"}, index = 8)
    private String durationTimes;

    @ColumnWidth(30)
    @ExcelProperty(value = {"状态"}, index = 9)
    private String statusName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"分公司"}, index = 10)
    private String createdCompanyName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"创建人"}, index = 11)
    @ApiModelProperty(value = "创建人")
    @FieldEncrypt
    private String createdName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"创建时间"}, index = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;


    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解账号"}, index = 13)
    @ApiModelProperty(value = "讲解账号")
    @FieldEncrypt
    private String createdName1;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解业务员"}, index = 14)
    @ApiModelProperty(value = "提交人业务员名称")
    @FieldEncrypt
    private String chargeUserName;


    /**
     * 大区名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属大区"}, index = 15)
    private String regionName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属分公司"}, index = 16)
    @ApiModelProperty(value = "分公司名称")
    private String companyName;


    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解人所属门店"}, index = 17)
    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解平台"}, index = 18)
    private String sourceName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索"}, index = 19)
    @ApiModelProperty(value = "线索id")
    private String explainCluesResult;

    /**
     * 订单成交状态：1.成交  0无
     */
    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索是否成交"}, index = 20)
    private String orderStatus;

    @ColumnWidth(30)
    @ExcelProperty(value = {"讲解线索成交金额"}, index = 21)
    private BigDecimal orderAmount;

    @ColumnWidth(30)
    @ExcelProperty(value = {"开始讲解时间"}, index = 22)
    private Date startTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"结束讲解时间"}, index = 23)
    private Date endTime;

    @ColumnWidth(30)
    @ExcelProperty(value = {"结束讲解方式"}, index = 24)
    private String abnormalEndTypeName;

    @ColumnWidth(30)
    @ExcelProperty(value = {"单次讲解时长（分）"}, index = 25)
    private String singleDurationTime;


}
