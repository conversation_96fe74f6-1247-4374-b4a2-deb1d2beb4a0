package com.fotile.cmscenter.scheme.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DeviseSchemeAIVO implements Serializable {

    @ApiModelProperty(value = "方案编码")
    private String contentId;
    @ApiModelProperty(value = "方案标题")
    private String projectName;
    @ApiModelProperty(value = "省、市、区+小区")
    private String provinceCityCountyVillage;
    @ApiModelProperty(value = "方案标签")
    private String labelNames;
    @ApiModelProperty(value = "推荐产品")
    private String goodsName;
    /**
     * 启用状态 0禁用，1启用
     */
    @ApiModelProperty(value = "启用状态 0禁用，1启用")
    private Integer status;
    /**
     * 是否删除：0：否；其它：是
     */
    @ApiModelProperty(value = "是否删除：0：否；其它：是")
    private Long isDeleted;

    /**
     * 是否显示：0：否；1：是
     */
    @ApiModelProperty(value = "是否显示：0：否；1：是")
    private Integer isShow;


    /**
     * 发送数
     */
    @ApiModelProperty(value = "发送数")
    private Long sendNum;
}
