package com.fotile.cmscenter.scheme.pojo.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酷家乐方案商品导出清单记录表(TKujialeExportTaskRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-05-07 13:45:27
 */

@Data
public class TKujialeExportTaskRecord implements Serializable {
    private static final long serialVersionUID = 381371936783760668L;

    private Long id;

    private Integer isDeleted;

    private String createdBy;

    private Date createdDate;

    private String modifiedBy;

    private Date modifiedDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 方案id
     */
    private String designId;
    /**
     * 导出清单id
     */
    private String listingId;
    /**
     * 同步更新清单时间
     */
    private Date syncDate;
    /**
     * 导出任务id
     */
    private String taskId;
    /**
     * 返回结果
     */
    private String responseBody;


}

