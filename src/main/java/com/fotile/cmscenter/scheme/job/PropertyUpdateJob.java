package com.fotile.cmscenter.scheme.job;

import com.fotile.cmscenter.common.KujialeUtil;
import com.fotile.cmscenter.common.pojo.vo.CategoryTagVO;
import com.fotile.cmscenter.content.pojo.vo.BaseAttribute;
import com.fotile.cmscenter.content.pojo.vo.FieldVO;
import com.fotile.cmscenter.content.service.PropertyParameterService;
import com.fotile.cmscenter.content.service.PropertyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PropertyUpdateJob {
    final
    PropertyService propertyService;
    final
    PropertyParameterService propertyParameterService;

    public PropertyUpdateJob(PropertyService propertyService, PropertyParameterService propertyParameterService) {
        this.propertyService = propertyService;
        this.propertyParameterService = propertyParameterService;
    }

    /**
     * 标签更新
     *
     * @param s
     * @return
     */
    @XxlJob(value = "propertyUpdateJob")
    public ReturnT<String> execute(String s) {
        //获取酷家乐标签列表
        List<CategoryTagVO> categoryList = KujialeUtil.getTagCategory("false");
        //清空酷家乐标签
        propertyService.clearoutPropert();
        if (CollectionUtils.isNotEmpty(categoryList)) {
            //获取
            List<FieldVO> updateList = new ArrayList<>();
            List<BaseAttribute> updateParamList = new ArrayList<>();
            List<FieldVO> properties = propertyService.selectAllCategory();
            List<BaseAttribute> baseAttributes = propertyService.selectAllAttributeCategory();



            if (CollectionUtils.isNotEmpty(properties)) {
                for (FieldVO property : properties) {
                    for (CategoryTagVO categoryTagVO : categoryList) {
                        if (Objects.equals(property.getFieldValue(), categoryTagVO.getTagCategoryName())) {
                            property.setKujialePropertyCode(categoryTagVO.getTagCategoryId());
                            updateList.add(property);
                            List<CategoryTagVO.TagsDTO> tags = categoryTagVO.getTags();
                            List<BaseAttribute> propertyParameterList = baseAttributes.stream().filter(
                                    t -> t.getFieldId().equals(property.getFieldId())
                            ).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(tags) && CollectionUtils.isNotEmpty(propertyParameterList)) {
                                for (BaseAttribute propertyParameter : propertyParameterList) {
                                    for (CategoryTagVO.TagsDTO tag : tags) {
                                        if (Objects.equals(propertyParameter.getAttributeValue(), tag.getName())) {
                                            propertyParameter.setKujialePropertyCode(tag.getId());
                                            updateParamList.add(propertyParameter);
                                        }
                                    }
                                }

                            }
                        }
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                    propertyService.updatePropertyList(updateList);
                propertyParameterService.updatePropertyList(updateParamList);
            }


        }
        return new ReturnT<String>("成功!");
    }
}
