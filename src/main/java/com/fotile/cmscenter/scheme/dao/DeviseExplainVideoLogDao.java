package com.fotile.cmscenter.scheme.dao;

import com.fotile.cmscenter.scheme.pojo.dto.GetExplainVideoLogPageInDto;
import com.fotile.cmscenter.scheme.pojo.entity.DeviseExplainVideoLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 教学视频播放日志记录表(TDeviseExplainVideoLog)表数据库访问层
 */
public interface DeviseExplainVideoLogDao {

    /**
     * 通过ID查询单条数据
     */
    DeviseExplainVideoLog queryById(Long id);

    /**
     * 查询指定行数据
     */
    List<DeviseExplainVideoLog> queryAllByLimit(GetExplainVideoLogPageInDto tDeviseExplainVideoLog);

    /**
     * 统计总行数
     */
    long count(GetExplainVideoLogPageInDto tDeviseExplainVideoLog);

    /**
     * 新增数据
     */
    int insert(DeviseExplainVideoLog tDeviseExplainVideoLog);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     */
    int insertBatch(@Param("entities") List<DeviseExplainVideoLog> entities);

    /**
     * 修改数据
     */
    int update(DeviseExplainVideoLog tDeviseExplainVideoLog);

    /**
     * 通过主键删除数据
     */
    int deleteById(Long id);

}

