package com.fotile.cmscenter.happinessTest.controller;

import com.fotile.cmscenter.happinessTest.pojo.entity.THappinessTestConclusionMapping;
import com.fotile.cmscenter.happinessTest.service.THappinessTestConclusionMappingService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 幸福测试结论表(THappinessTestConclusionMapping)表控制层
 */
 
@RestController
@RequestMapping("/api/tHappinessTestConclusionMapping")
public class THappinessTestConclusionMappingController extends BaseController{
    /**
     * 服务对象
     */
    @Autowired
    private THappinessTestConclusionMappingService tHappinessTestConclusionMappingService;

    /**
     * 分页查询
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> queryByPage(@Valid THappinessTestConclusionMapping tHappinessTestConclusionMapping) {
        return success(this.tHappinessTestConclusionMappingService.queryByPage(tHappinessTestConclusionMapping));
    }

    /**
     * 通过主键查询单条数据
     */
    @RequestMapping(value = "/queryById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> queryById(@Valid Long id) {
        return success(this.tHappinessTestConclusionMappingService.queryById(id));
    }

    /**
     * 新增数据
     */
//    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> add(@RequestBody THappinessTestConclusionMapping tHappinessTestConclusionMapping) {
        return success(this.tHappinessTestConclusionMappingService.insert(tHappinessTestConclusionMapping));
    }

    /**
     * 编辑数据
     */
//    @RequestMapping(value = "/edit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> edit(@RequestBody THappinessTestConclusionMapping tHappinessTestConclusionMapping) {
        return success(this.tHappinessTestConclusionMappingService.update(tHappinessTestConclusionMapping));
    }

    /**
     * 删除数据
     */
//    @RequestMapping(value = "/deleteById", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> deleteById(@RequestBody Long id) {
        return success(this.tHappinessTestConclusionMappingService.deleteById(id));
    }

}

