package com.fotile.cmscenter.happinessTest.service;

import com.fotile.cmscenter.happinessTest.dao.THappinessTestOptionMappingDao;
import com.fotile.cmscenter.happinessTest.pojo.entity.THappinessTestOptionMapping;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 幸福测试题目选项得分配置表(THappinessTestOptionMapping)表服务实现类
 */
 
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class THappinessTestOptionMappingService {
    @Autowired
    private THappinessTestOptionMappingDao tHappinessTestOptionMappingDao;

    /**
     * 通过ID查询单条数据
     */
    public THappinessTestOptionMapping queryById(Long id) {
        return this.tHappinessTestOptionMappingDao.queryById(id);
    }

    /**
     * 分页查询
     */
    public PageInfo<THappinessTestOptionMapping> queryByPage(THappinessTestOptionMapping tHappinessTestOptionMapping) {
        PageInfo<THappinessTestOptionMapping> pageInfo = new PageInfo<>();
        long total = this.tHappinessTestOptionMappingDao.count(tHappinessTestOptionMapping);
        tHappinessTestOptionMappingDao.queryAllByLimit(tHappinessTestOptionMapping);
        return pageInfo;
    }

    /**
     * 新增数据
     */
    public THappinessTestOptionMapping insert(THappinessTestOptionMapping tHappinessTestOptionMapping) {
        this.tHappinessTestOptionMappingDao.insert(tHappinessTestOptionMapping);
        return tHappinessTestOptionMapping;
    }

    /**
     * 修改数据
     */
    public THappinessTestOptionMapping update(THappinessTestOptionMapping tHappinessTestOptionMapping) {
        this.tHappinessTestOptionMappingDao.update(tHappinessTestOptionMapping);
        return this.queryById(tHappinessTestOptionMapping.getId());
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.tHappinessTestOptionMappingDao.deleteById(id) > 0;
    }
}
