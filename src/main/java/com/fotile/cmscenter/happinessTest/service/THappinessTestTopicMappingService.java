package com.fotile.cmscenter.happinessTest.service;

import com.fotile.cmscenter.happinessTest.dao.THappinessTestTopicMappingDao;
import com.fotile.cmscenter.happinessTest.pojo.entity.THappinessTestTopicMapping;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 幸福测试题目表(THappinessTestTopicMapping)表服务实现类
 */
 
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class THappinessTestTopicMappingService {
    @Autowired
    private THappinessTestTopicMappingDao tHappinessTestTopicMappingDao;

    /**
     * 通过ID查询单条数据
     */
    public THappinessTestTopicMapping queryById(Long id) {
        return this.tHappinessTestTopicMappingDao.queryById(id);
    }

    /**
     * 分页查询
     */
    public PageInfo<THappinessTestTopicMapping> queryByPage(THappinessTestTopicMapping tHappinessTestTopicMapping) {
        PageInfo<THappinessTestTopicMapping> pageInfo = new PageInfo<>();
        long total = this.tHappinessTestTopicMappingDao.count(tHappinessTestTopicMapping);
        tHappinessTestTopicMappingDao.queryAllByLimit(tHappinessTestTopicMapping);
        return pageInfo;
    }

    /**
     * 新增数据
     */
    public THappinessTestTopicMapping insert(THappinessTestTopicMapping tHappinessTestTopicMapping) {
        this.tHappinessTestTopicMappingDao.insert(tHappinessTestTopicMapping);
        return tHappinessTestTopicMapping;
    }

    /**
     * 修改数据
     */
    public THappinessTestTopicMapping update(THappinessTestTopicMapping tHappinessTestTopicMapping) {
        this.tHappinessTestTopicMappingDao.update(tHappinessTestTopicMapping);
        return this.queryById(tHappinessTestTopicMapping.getId());
    }

    /**
     * 通过主键删除数据
     */
    public boolean deleteById(Long id) {
        return this.tHappinessTestTopicMappingDao.deleteById(id) > 0;
    }
}
