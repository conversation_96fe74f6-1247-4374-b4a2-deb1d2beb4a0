package com.fotile.cmscenter.fotilestyle.dao;

import com.fotile.cmscenter.client.pojo.CultureStatsDto;
import com.fotile.cmscenter.fotilestyle.pojo.DynamicDto;
import com.fotile.cmscenter.fotilestyle.pojo.Picture;
import com.fotile.cmscenter.fotilestyle.pojo.PictureMapping;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FotilestyleDao {

    void addDynamic(DynamicDto dynamicDto);

    void insertPicture(Picture picture);

    void insertPictureMapping(PictureMapping pictureMapping);

    void insertLabelMapping(@Param("sourceId") Long sourceId, @Param("labelId") Long labelId);

    void insertCultureStatisticsNew();
    
    void insertCultureStatBatch(@Param("list") List<CultureStatsDto> list,
            @Param("ctype") int ctype, @Param("energy") int energy);
    
    void insertCultureSports(@Param("userId") String userId, @Param("phone") String phone, @Param("energy") int energy);
    
    List<CultureStatsDto> getDynamicCultureStat(@Param("ctype") Integer ctype);
    List<CultureStatsDto> getExpandCultureStat();
    List<CultureStatsDto> getLikeCommCultureStat();
}
