package com.fotile.cmscenter.fotilestyle.pojo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.List;

@Data
public class RedmoWiningWorksVo {
    private Long id;

    // 设计师
    @FieldEncrypt
    private String competitionName;

    // 参赛者公司
    private String competitionCompany;

    // 得奖类型
    private String wardType;

    // 作品id
    private Long worksId;

    // 作品标题
    private String worksTitle;

    // 实景图
    private List<String> designsPictures;

    private Boolean editStatus;
}
