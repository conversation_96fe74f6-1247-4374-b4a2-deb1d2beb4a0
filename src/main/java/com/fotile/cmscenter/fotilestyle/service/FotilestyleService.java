package com.fotile.cmscenter.fotilestyle.service;

import com.fotile.cmscenter.common.CurrentUser;
import com.fotile.cmscenter.fotilestyle.dao.FotilestyleDao;
import com.fotile.cmscenter.fotilestyle.pojo.DynamicDto;
import com.fotile.cmscenter.fotilestyle.pojo.Picture;
import com.fotile.cmscenter.fotilestyle.pojo.PictureMapping;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class FotilestyleService {

    @Autowired
    private FotilestyleDao fotilestyleDao;
    @Autowired
    private CurrentUser currentUser;

    @Transactional
    public void addDynamic(DynamicDto dynamicDto){
        String userId = currentUser.getCurrentUserId();
        dynamicDto.setUserId(userId);
        if (!StringUtils.isEmpty(dynamicDto.getNickname())){
            dynamicDto.setNickname(MybatisMateConfig.encrypt(dynamicDto.getNickname()));
        }
        fotilestyleDao.addDynamic(dynamicDto);
        if(null != dynamicDto.getLabelIds() && !dynamicDto.getLabelIds().isEmpty()){
            insertLabelMapping(dynamicDto.getLabelIds(),dynamicDto.getId());
        }
        if(null != dynamicDto.getPictures() && !dynamicDto.getPictures().isEmpty()){
            insertPictures(dynamicDto.getPictures(),userId,dynamicDto.getId());
        }
    }

    private void insertPictures(List<String> pictures,String userId,Long id){
        for (String p : pictures){
            Picture picture = new Picture();
            picture.setName("用户发表动态图片");
            picture.setCoverUrl(p);
            picture.setUserId(userId);
            fotilestyleDao.insertPicture(picture);

            PictureMapping pictureMapping = new PictureMapping();
            pictureMapping.setSourceId(id);
            pictureMapping.setPictureId(picture.getId());
            pictureMapping.setSort(1L);
            pictureMapping.setUserId(userId);
            fotilestyleDao.insertPictureMapping(pictureMapping);
        }
    }

    private void insertLabelMapping(List<Long> labelIds,Long id){
        for (Long labelId : labelIds){
            fotilestyleDao.insertLabelMapping(id,labelId);
        }
    }

}
