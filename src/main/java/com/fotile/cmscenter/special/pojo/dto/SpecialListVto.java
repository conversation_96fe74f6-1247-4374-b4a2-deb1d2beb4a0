package com.fotile.cmscenter.special.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SpecialListVto implements Serializable {
    //专题id
    private Long id;
    //分类id
    private Long categoryId;
    //专题类型
    private String type;
    //专题标题
    private String title;
    //封面图
    private String coverUrl;
    @ApiModelProperty(value = "方图地址")
    private String squareUrl;
    //创建时间
    private Date createdDate;
    //创建人
    private String createdPerson;
    //审核状态
    private String auditStatus;
    //在线状态
    private Long onlineStatus;
    //浏览数
    private Long browseCount;
    //收藏数
    private Long collectionCount;
    //分类名称
    private String cateName;
    //mapping id
    private Long mappingId;
    private String tableName;
    private Long sort;
}
