package com.fotile.cmscenter.special.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.protobuf.MapEntry;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fotile.cmscenter.adver.dao.AdverMappingDao;
import com.fotile.cmscenter.cases.pojo.dto.QueryCaseWithPageInputDto;
import com.fotile.cmscenter.cases.pojo.dto.QueryCaseWithPageOutputDto;
import com.fotile.cmscenter.cases.service.CasesService;
import com.fotile.cmscenter.client.*;
import com.fotile.cmscenter.client.pojo.*;
import com.fotile.cmscenter.common.CurrentUser;
import com.fotile.cmscenter.content.dao.*;
import com.fotile.cmscenter.content.pojo.*;
import com.fotile.cmscenter.content.pojo.dto.*;
import com.fotile.cmscenter.content.pojo.vo.AudioVideoVo;
import com.fotile.cmscenter.content.pojo.vo.ContentInfoVo;
import com.fotile.cmscenter.content.pojo.vo.ContentQueryVo;
import com.fotile.cmscenter.content.service.ContentActivityPriceService;
import com.fotile.cmscenter.content.service.ContentGoodsMappingService;
import com.fotile.cmscenter.content.service.ContentService;
import com.fotile.cmscenter.customMenu.pojo.dto.GetCustomContentListOutDto;
import com.fotile.cmscenter.engineering.pojo.dto.QueryGoodsByIdOutDto;
import com.fotile.cmscenter.favorite.dao.repository.CmsContentFavoriteMappingRepo;
import com.fotile.cmscenter.favorite.pojo.entity.CmsContentFavoriteMapping;
import com.fotile.cmscenter.happinessTest.pojo.dto.HappinessTestOptionDto;
import com.fotile.cmscenter.integrateMarketing.common.ImConstant;
import com.fotile.cmscenter.material.dao.MaterialEventLogMapper;
import com.fotile.cmscenter.material.dao.MaterialMapper;
import com.fotile.cmscenter.material.pojo.dto.MaterialContentListDto;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryInputDTO;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryOutputDTO;
import com.fotile.cmscenter.material.pojo.dto.InteractiveContentListDto;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryInputDTO;
import com.fotile.cmscenter.material.pojo.dto.MaterialEventLogQueryOutputDTO;
import com.fotile.cmscenter.material.pojo.dto.TreeContentListDto;
import com.fotile.cmscenter.material.pojo.entity.NodeMaterialContentListDto;
import com.fotile.cmscenter.material.service.MaterialContentService;
import com.fotile.cmscenter.question.service.client.UserClientService;
import com.fotile.cmscenter.special.dao.*;
import com.fotile.cmscenter.special.pojo.QueryCluesCountDto;
import com.fotile.cmscenter.special.pojo.dto.*;
import com.fotile.cmscenter.special.pojo.entity.*;
import com.fotile.framework.core.common.PageInfo;
import com.fotile.framework.data.auth.dataAuthor.pojo.ChannelAuthor;
import com.fotile.framework.data.auth.dataAuthor.pojo.UserAuthor;
import com.fotile.framework.data.auth.dataAuthor.service.UserAuthorConfig;
import com.fotile.framework.data.secure.util.MybatisMateConfig;
import com.fotile.framework.db.configuration.DruidTxcMultiDataSourceAnnontation;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.swing.text.html.parser.Entity;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
@DruidTxcMultiDataSourceAnnontation(value = "second")
public class SpecialService {
    @Autowired
    private SpecialDao dao;
    @Autowired
    private ContentService contentService;
    @Autowired
    private OrgClient orgClient;
    @Autowired
    private RadioClient radioClient;
    @Autowired
    private UserAuthorConfig userAuthorConfig;
    @Autowired
    private ContentCategoryDao contentCategoryDao;
    @Autowired
    ChannelClient channelClient;
    @Autowired
    private CurrentUser currentUser;
    @Autowired
    private ContentDao contentDao;
    @Autowired
    private ContentMenuDao contentMenuDao;
    @Autowired
    private ContentActivityPriceDao contentActivityPriceDao;
    @Autowired
    private ContentActivityPriceService contentActivityPriceService;
    @Autowired
    private ContentOperatorLogDao contentOperatorLogDao;
    @Autowired
    private AdverMappingDao adverMappingDao;
    @Autowired
    private SpecialAdverGroupMappingMapper specialAdverGroupMappingDao;
    @Autowired
    private StatisticsCmsMappingDao statisticsCmsMappingDao;
    @Autowired
    private ContentGoodsMappingService contentGoodsMappingService;
    @Autowired
    private AudioVideoDao audioVideoDao;
    @Autowired
    private LabelCmsMappingDao labelCmsMappingDao;
    @Autowired
    private MaterialContentService materialContentService;

    @Autowired
    private SpecialMaterialContentMappingMapper specialMaterialContentMappingMapper;

    @Autowired
    private MaterialMapper materialMapper;
    @Autowired
    private CasesService casesService;

    @Resource
    private MaterialEventLogMapper eventLogMapper;
    @Autowired
    private ProductClient productClient;
    @Autowired
    private ContentGoodsMappingDao goodsMappingDao;
    @Autowired
    private CdpClient cdpClient;
    @Autowired
    private CustomerClient customerClient;
    @Resource
    private CmsContentFavoriteMappingRepo favoriteMappingRepo;
    @Autowired
    private MarketingClient marketingClient;
    @Autowired
    private TSpecialColumnMappingDao specialColumnMappingDao;
    @Autowired
    private TSpecialColumnModuleMappingDao specialColumnModuleMappingDao;
    @Autowired
    private TSpecialColumnModuleContentMappingDao specialColumnModuleContentMappingDao;
    @Autowired
    private TLandingMappingDao landingMappingDao;
    @Autowired
    private SystemClient systemClient;


    @Value("${specialContentMapping.appid}")
    private String specialContentMappingAppid;
    @Value("${specialContentMapping.articleTitle}")
    private String specialContentMappingArticleTitle;
    @Value("${specialContentMapping.title}")
    private String specialContentMappingTitle;
    @Value("${specialContentMapping.imageUrl}")
    private String specialContentMappingImageUrl;
    @Value("${specialContentMapping.shareUrl}")
    private String specialContentMappingShareUrl;
    @Value("${specialContentMapping.articleUrl}")
    private String specialContentMappingArticleUrl;
    @Value("${specialContentMapping.pagePath}")
    private String specialContentMappingPagePath;

    // @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void commit(UpdateStatusInDto inDto) {
        String userId = currentUser.getCurrentUserId();
        QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(inDto.getId());
        dao.commit(inDto);
        AddLogInDto addLogInDto = new AddLogInDto(inDto.getId(), "提交内容专题,状态由:" + queryHistoryStuatusOutDto.getAuditStatus() + "变为:待审核", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);
    }

    // @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void verify(UpdateStatusInDto inDto) {
        String userId = currentUser.getCurrentUserId();
        QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(inDto.getId());
        dao.verify(inDto);
        AddLogInDto addLogInDto = new AddLogInDto(inDto.getId(), "审核内容专题,状态由:" + queryHistoryStuatusOutDto.getAuditStatus() + "变为:已审核", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);
    }

    // @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void rejectVerify(UpdateStatusInDto inDto) {
        String userId = currentUser.getCurrentUserId();
        QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(inDto.getId());
        dao.rejectVerify(inDto);
        AddLogInDto addLogInDto = new AddLogInDto(inDto.getId(), "审核拒绝内容专题,状态由:" + queryHistoryStuatusOutDto.getAuditStatus() + "变为:审核拒绝", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);
    }

    // @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void upOnlineStatus(UpdateStatusInDto inDto) {
        String userId = currentUser.getCurrentUserId();
        QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(inDto.getId());
        dao.upOnlineStatus(inDto);
        AddLogInDto addLogInDto = new AddLogInDto(inDto.getId(), "上架内容专题,状态由:" + queryHistoryStuatusOutDto.getOnlineStatus() + "变为:上线", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);
    }

    // @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void downOnlineStatus(UpdateStatusInDto inDto) {
        String userId = currentUser.getCurrentUserId();
        QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(inDto.getId());
        dao.downOnlineStatus(inDto);
        AddLogInDto addLogInDto = new AddLogInDto(inDto.getId(), "下架内容专题,状态由:" + queryHistoryStuatusOutDto.getOnlineStatus() + "变为:下线", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);
    }

    /**
     * 获取渠道id
     */
    public List<Long> getChannelRatioDTOs() {
        /*UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        if (userAuthor == null) {
            return null;
        }*/
        List<Long> channels = new ArrayList<>();
        if (userAuthorConfig.queryChannelAuthorList() != null) {
            List<ChannelAuthor> channelAuthorList = userAuthorConfig.queryChannelAuthorList();
            for (ChannelAuthor channelAuthor : channelAuthorList) {
                if (-1 == channelAuthor.getChannelId() || channelAuthor.getChannelId() == null) {
                    break;
                }

                channels.add(channelAuthor.getChannelId());
            }
            return channels;
        }
        return null;
    }

    // @Cached(cacheType =CacheType.REMOTE,expire = 3600,name =
// "special_queryAll",area = "cmscenter")
    public PageInfo<QueryAllOutDto> queryAll(QueryAllInDto inDto) {
        // 设置用户渠道权限
        if (inDto.getChannelIds() == null || inDto.getChannelIds().size() == 0) {
            List<Long> channelIds = getChannelRatioDTOs();
            if (channelIds != null && channelIds.size() != 0) {
                inDto.setChannelIds(channelIds);
            }
        }
        if (StringUtils.isNotBlank(inDto.getCreatedPerson())){
            inDto.setCreatedPerson(MybatisMateConfig.encrypt(inDto.getCreatedPerson()));
        }
        PageInfo<QueryAllOutDto> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        Integer count = dao.queryAllCount(inDto);
        List<QueryAllOutDto> queryAllOutDtos = new ArrayList<>();
        if (count > 0) {
            inDto.setOffset(pageInfo.getOffset());
            queryAllOutDtos = dao.queryAll(inDto);
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(queryAllOutDtos);
        // 查询所有专题的ChannelRadio
        List<QueryAllOutDto> list = pageInfo.getList();
        if (list != null && list.size() > 0) {
            Set<Long> channelIds = new HashSet<>();
            Set<Long> radioIds = new HashSet<>();
            Set<Long> specialIds = new HashSet<>();
            list.stream().forEach(x -> {
                specialIds.add(x.getId());
            });
            Map<Long, FindChannelByIdOutDto> channelMap = new HashMap<>();
            Map<Long, FindRadioPageAllOutDto> radioMap = new HashMap<>();
            Map<Long, List<LabelCms>> labelMap = new HashMap();
            if (specialIds != null && specialIds.size() > 0) {
                //查询所有标签
                List<LabelCms> labelList = dao.queryLabelList(new ArrayList<>(specialIds));
                if (labelList != null && labelList.size() > 0) {
                    labelMap = labelList.stream().collect(Collectors.groupingBy(LabelCms::getSourceId));
                }
            }
            //查询所有分类
            Map<Long, ContentCatalog> categoryMap = new HashMap<>();
            List<ContentCatalog> allCategory = contentCategoryDao.findAllCategory();
            if (!CollectionUtils.isEmpty(allCategory)) {
                categoryMap = allCategory.stream().collect(Collectors.toMap(ContentCatalog::getId, x -> x));
            }
            //查询所有专题的渠道频道
            List<ChannelRadio> channelRadios = dao.queryChannelRadios(new ArrayList<>(specialIds));
            //专题渠道频道集合(根据专题id分组)
            Map<Long, List<ChannelRadio>> ChannelRadioMap = channelRadios.stream().collect(Collectors.groupingBy(ChannelRadio::getSourceId));
            //获取渠道/频道id集合
            channelRadios.stream().forEach(x -> {
                channelIds.add(x.getChannelId());
                radioIds.add(x.getRadioId());
            });
            //根据channelIds查询所有渠道和频道
            Map<String, List<Long>> channelIdsMap = new HashMap<>();
            channelIdsMap.put("idList", new ArrayList<>(channelIds));
            List<FindChannelByIdOutDto> channels = orgClient.findByIds(channelIdsMap).getData();
            List<FindRadioPageAllOutDto> radios = radioClient.findPageAll(1, 10000).getData().getList();
            //list转map
            if (channels != null && channels.size() > 0) {
                channelMap = channels.stream().collect(Collectors.toMap(FindChannelByIdOutDto::getId, x -> x));
            }
            if (radios != null && radios.size() > 0) {
                radioMap = radios.stream().collect(Collectors.toMap(FindRadioPageAllOutDto::getId, x -> x));
            }
            for (QueryAllOutDto m : list) {
                //查询专题分类名称
                if (m.getContentCategoryId() != null) {
                    ContentCatalog contentCatalog = categoryMap.get(m.getContentCategoryId());
                    String categoryName = categoryMap.get(m.getContentCategoryId()).getName();
                    if (StringUtils.isNotBlank(categoryName)) {
                        m.setContentCategoryName(categoryName);
                    }
                }
                //匹配标签
                if (labelMap.get(m.getId()) != null) {
                    List<LabelCms> labelCms = labelMap.get(m.getId());
                    List<String> collect = labelCms.stream().map(LabelCms::getName).collect(Collectors.toList());
                    String labels = StringUtils.join(collect, ",");
                    m.setKeyWord(labels);
                }
                //匹配渠道频道
                List<String> channelRadio = new ArrayList<>();
                List<ChannelRadio> channelRadioList = ChannelRadioMap.get(m.getId());
                if (channelRadioList != null && channelRadioList.size() > 0) {
                    for (ChannelRadio x : channelRadioList) {
                        String channelName = "";
                        String radioName = "";
                        FindChannelByIdOutDto channel = channelMap.get(x.getChannelId());
                        if (channel != null) {
                            channelName = channel.getName();
                            FindRadioPageAllOutDto radio = radioMap.get(x.getRadioId());
                            if (radio != null) {
                                radioName = "-" + radio.getName();
                            }
                            m.getChannelRadio().add(channelName + radioName + ",");
                        }
                    }
                }
                if (StringUtils.isNotBlank(m.getCreatedPerson())){
                    m.setCreatedPerson(MybatisMateConfig.decrypt(m.getCreatedPerson()));
                }
            }
        }
        return pageInfo;
    }

    public List<QueryAllOutDto> queryFromES(QueryAllInDto query) {
        if(Objects.isNull(query)|| org.apache.commons.collections4.CollectionUtils.isEmpty(query.getIds())){
            return Lists.newArrayListWithExpectedSize(0);
        }
        List<QueryAllOutDto> list = dao.queryFromES(query);
        return setQueryFromESResult(list,query);

    }

    private List<QueryAllOutDto> setQueryFromESResult( List<QueryAllOutDto> list,QueryAllInDto query){
        if(CollectionUtils.isEmpty(list) ||Objects.isNull(query)||Objects.isNull(query.getSalesmanId())){
            return list;
        }
        List<Long> ids = list.stream().map(QueryAllOutDto::getId).collect(Collectors.toList());
        MaterialEventLogQueryInputDTO queryRelayParam = new MaterialEventLogQueryInputDTO();
        queryRelayParam.setSalesmanId(query.getSalesmanId());
        queryRelayParam.setRelationParentIdList(ids);
        Map<Long, MaterialEventLogQueryOutputDTO> relayMap = eventLogMapper.queryRelayCount(queryRelayParam).stream().collect(Collectors.toMap(MaterialEventLogQueryOutputDTO::getRelationParentId, Function.identity()));

        // 现在先做上
        Set<Long> myCollectSet = favoriteMappingRepo.querySalesmanSpecialFavorite(query.getSalesmanId(), ids)
                .stream()
                .map(CmsContentFavoriteMapping::getSourceId)
                .collect(Collectors.toSet());

        for (QueryAllOutDto queryAllOutDto : list) {
            if(relayMap.containsKey(queryAllOutDto.getId())){
                MaterialEventLogQueryOutputDTO materialEventLogQueryOutputDTO = relayMap.get(queryAllOutDto.getId());
                queryAllOutDto.setMyRelayCount(materialEventLogQueryOutputDTO.getCount());
            }
            if(myCollectSet.contains(queryAllOutDto.getId())){
                queryAllOutDto.setIsCollected(1);
            }

        }
        return list;
    }

    // @Cached(cacheType =CacheType.REMOTE,expire = 3600,name =
// "special_queryById",area = "cmscenter")
    public QueryByIdOutDto queryById(QueryByIdInDto inDto) {
        QueryByIdOutDto queryByIdOutDto = dao.queryById(inDto);
        //处理封面小图
        if (queryByIdOutDto != null && StringUtils.isNotBlank(queryByIdOutDto.getCoverSmallUrl())) {
            String[] split = queryByIdOutDto.getCoverSmallUrl().split(",");
            List<String> coverSmallUrlList = Arrays.asList(split);
            queryByIdOutDto.setCoverSmallUrlList(coverSmallUrlList);
        }
        //查询标签
        List<KeyWord> labels = dao.selectById(inDto.getId());
        if (!CollectionUtils.isEmpty(labels)) {
            queryByIdOutDto.setKeyWord(labels);
        }
        // 查询所有专题的ChannelRadio
        List<ChannelRadio> queryRadioIds = dao.queryRadioIds(inDto.getId());
        queryByIdOutDto.setRadioIds(queryRadioIds);
        /**
         * 查询专栏  内容菜谱专题柔和在一起
         */
        if ("06".equals(queryByIdOutDto.getType())) {
            List<QueryStylesOutDto> specialStyles = dao.selectStyles(inDto.getId());
            if (!CollectionUtils.isEmpty(specialStyles)) {
                for (QueryStylesOutDto queryStylesOutDto : specialStyles) {
                    if (!"3".equals(queryStylesOutDto.getStyle())) {
                        //查询内容
                        List<ContentInfoDTO> list = dao.selectStyleMapping(queryStylesOutDto.getId());
                        List<ContentInfoDTO> styleContents = new ArrayList<>();
                        List<ContentInfoDTO> styleSpecials = new ArrayList<>();
                        if (list != null && list.size() > 0) {
                            list.stream().forEach(x -> {
                                if ("special_info".equals(x.getSourceTable())) {
                                    styleSpecials.add(x);
                                } else {
                                    styleContents.add(x);
                                }
                            });
                        }
                        final List<ContentInfoVo> contentInfoVos = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(styleContents)) {
                            List<ContentInfoVo> contentInfoVoList = getSpecialContent(styleContents);
                            if (contentInfoVoList != null && contentInfoVoList.size() > 0) {
                                contentInfoVos.addAll(contentInfoVoList);
                            }
                        }
                        //把专题转化成和内容一样的形式,存在一个数组中
                        if (!CollectionUtils.isEmpty(styleSpecials)) {
                            List<SpecialListVto> specialListVtos = getSpecialListVto(styleSpecials);
                            if (specialListVtos != null && specialListVtos.size() > 0) {
                                specialListVtos.stream().forEach(x -> {
                                    ContentInfoVo contentInfoVo = new ContentInfoVo();
                                    contentInfoVo.setTableName(x.getTableName());
                                    contentInfoVo.setAuditingStatus(x.getAuditStatus());
                                    contentInfoVo.setBrowseCount(x.getBrowseCount());
                                    contentInfoVo.setCollectionCount(x.getCollectionCount());
                                    contentInfoVo.setCoverUrl(x.getCoverUrl());
                                    contentInfoVo.setCreatedDate(x.getCreatedDate());
                                    if (StringUtils.isNotBlank(x.getCreatedPerson())) {
                                        contentInfoVo.setCreatedPerson(MybatisMateConfig.decrypt(x.getCreatedPerson()));
                                    }
                                    contentInfoVo.setId(x.getId());
                                    contentInfoVo.setMappingId(x.getMappingId());
                                    contentInfoVo.setName(x.getCateName());
                                    contentInfoVo.setOnlineStatus(x.getOnlineStatus() + "");
                                    contentInfoVo.setSort(x.getSort());
                                    contentInfoVo.setTitle(x.getTitle());
                                    contentInfoVo.setType(x.getType());
                                    contentInfoVos.add(contentInfoVo);
                                });
                            }
                        }
                        if (contentInfoVos != null && contentInfoVos.size() > 0) {
                            List<ContentInfoVo> newList = contentInfoVos.stream().sorted(Comparator.comparing(ContentInfoVo::getSort))
                                    .collect(Collectors.toList());
                            //Collections.reverse(newList);
                            queryStylesOutDto.setStyleContents(newList);
                        }
                    }
                }
                queryByIdOutDto.setSpecialStyles(specialStyles);
            }
        } else if ("07".equals(queryByIdOutDto.getType())) {//课程合辑
            List<ContentInfoDTO> list = dao.findContentBySpecialId(inDto.getId());
            if (list != null && list.size() > 0) {
                list.stream().forEach(c ->{
                    if (c.getSort() == null) {
                        c.setSort(9999L);
                    }
                });
                queryByIdOutDto.setSpecialList(getSpecialListVto(list));
                AtomicInteger index = new AtomicInteger();
                queryByIdOutDto.getSpecialList().stream().forEach(c ->{
                    c.setSort(Long.valueOf(index.getAndIncrement() + 1));
                });
            }
        }else if("09".equals(queryByIdOutDto.getType())){
            // 栏目，取广告列表
            queryByIdOutDto.setAdvers(getAdverList(queryByIdOutDto.getId().toString()));

            List<ContentInfoDTO> list = dao.findContentBySpecialId(inDto.getId());
            if (list != null && list.size() > 0) {
                queryByIdOutDto.setContent(getSpecialContent(list));
            }
        } else if ("10".equals(queryByIdOutDto.getType())) {
            //内容合辑
            queryByIdOutDto.setSpecialColumnMappingList(getColumnList(inDto.getId()));
            queryByIdOutDto.setLandingMappingList(getLandingMapping(inDto.getId()));
        } else {// 内容
            List<ContentInfoDTO> list = dao.findContentBySpecialId(inDto.getId());
            if (list != null && list.size() > 0) {
                queryByIdOutDto.setContent(getSpecialContent(list));
            }
        }

        // 判断专题是否是菜谱课程，音频课程、听书课程、专栏、课程合辑，并且是付费内容
        if(("03".equals(queryByIdOutDto.getType()) || "10".equals(queryByIdOutDto.getType()) || "04".equals(queryByIdOutDto.getType()) || "06".equals(queryByIdOutDto.getType())) || "02".equals(queryByIdOutDto.getType())|| "07".equals(queryByIdOutDto.getType())){
            // 查询定价列表
            ContentActivityPriceExample contentActivityPriceExample = new ContentActivityPriceExample();
            ContentActivityPriceExample.Criteria priceCriteria = contentActivityPriceExample.createCriteria();
            priceCriteria.andContentIdEqualTo(queryByIdOutDto.getId().toString());
            priceCriteria.andTypeEqualTo(getSpecialActivityPriceType(queryByIdOutDto.getType()));
            priceCriteria.andIsDeletedEqualTo("0");
            // 查询价格列表
            List<ContentActivityPrice> prices = contentActivityPriceDao.selectByExample(contentActivityPriceExample);
            if (!CollectionUtils.isEmpty(prices)) {
                prices.stream().forEach(p -> {
                    if (StringUtils.isNotBlank(p.getReserveItem1())){
                        p.setReserveItem1(MybatisMateConfig.decrypt(p.getReserveItem1()));
                    }
                    if (StringUtils.isNotBlank(p.getReserveItem2())){
                        p.setReserveItem2(MybatisMateConfig.decrypt(p.getReserveItem2()));
                    }
                });
            }
            queryByIdOutDto.setActivityPrices(prices);
            List<AudioVideoVo> audioOrVideo=new ArrayList<>();
            audioOrVideo.addAll( audioVideoDao.selectAudiVideoBySourceId(inDto.getId(), "03", "special_info"));
            audioOrVideo.addAll( audioVideoDao.selectAudiVideoBySourceId(inDto.getId(), "04", "special_info"));
            for (AudioVideoVo audioVideoVo : audioOrVideo) {
                audioVideoVo.setKeywords(labelCmsMappingDao.findLabelInfoBySourceTableAndSourceId("audio_video", audioVideoVo.getId()));
            }
            queryByIdOutDto.setTryAudioOrVideo(audioOrVideo);
        }
        if(("02".equals(queryByIdOutDto.getType()) || "03".equals(queryByIdOutDto.getType()))){
            queryByIdOutDto.setGoodsList(contentGoodsMappingService.findAllBySourceIdAndSourceTable(inDto.getId(),"special_info"));
        }


        // 查询专题对应分公司
        List<Long> queryCompanyId = dao.queryCompanyId(inDto.getId());
        queryByIdOutDto.setCompanyIds(queryCompanyId);
        StatisticsCmsMapping contentDetails = statisticsCmsMappingDao.findAllBySourceTableAndSourceId("special_info", inDto.getId());
        queryByIdOutDto.setSystemCollectionCount(null==contentDetails?null:contentDetails.getSystemCollectionCount());
        //添加系统浏览数
        queryByIdOutDto.setSystemBrowseCount(null==contentDetails?null:contentDetails.getSystemBrowseCount());

        return queryByIdOutDto;
    }

    private List<TLandingMapping> getLandingMapping(Long id) {
        List<TLandingMapping> landingMappingList = landingMappingDao.queryBySourceId(id, "special_info");
        return landingMappingList;
    }

    private List<SpecialColumnMappingDto> getColumnList(Long id) {
        QueryDictInDto search = new QueryDictInDto();
        search.setTypeCode("lrzt");
        List<Dic> dicList = systemClient.searchDict(search).getData();
        //查询栏目
        List<SpecialColumnMappingDto> columnMappingDtoList = specialColumnMappingDao.selectBySpecialId(id);
        if (!CollectionUtils.isEmpty(columnMappingDtoList)){
            //查询模块
            List<Long> columnIds = columnMappingDtoList.stream().map(c -> c.getId()).collect(Collectors.toList());
            List<SpecialColumnModuleMappingDto> moduleMappings = specialColumnModuleMappingDao.selectByColumnIds(columnIds);
            if (!CollectionUtils.isEmpty(moduleMappings)){
                //查询内容or产品
                List<Long> moduleIds = moduleMappings.stream().map(m -> m.getId()).collect(Collectors.toList());
                List<SpecialColumnModuleContentMappingDto> moduleContentMappingList = specialColumnModuleContentMappingDao.selectByModuleIds(moduleIds);

                List<GetCustomContentListOutDto> contentDtoList;
                List<GetChannelGoodsListOutDto> goodsList;
                if (!CollectionUtils.isEmpty(moduleContentMappingList)){
                    //分类
                    //商品
                    List<SpecialColumnModuleContentMappingDto> allProductList = moduleContentMappingList.stream().filter(c -> "channel_goods_mapping".equals(c.getSourceTableName())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(allProductList)){
                        List<String> channelGoodIds = allProductList.stream().map(p -> p.getSourceId().toString()).collect(Collectors.toList());
                        GetChannelGoodsListInDto channelGoodsDto = new GetChannelGoodsListInDto();
                        channelGoodsDto.setChannelGoodsMappingIdList(channelGoodIds);
                        channelGoodsDto.setPage(1);
                        channelGoodsDto.setSize(channelGoodIds.size());
                        goodsList = productClient.getChannelGoodsListWithPost(channelGoodsDto).getData().getRecords();
                    } else {
                        goodsList = new ArrayList<>();
                    }
                    //内容
                    List<SpecialColumnModuleContentMappingDto> allContentList = moduleContentMappingList.stream().filter(c -> "content_menu".equals(c.getSourceTableName()) || "content".equals(c.getSourceTableName())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(allContentList)) {
                        List<String> contentCodes = allContentList.stream().map(c -> c.getSourceCode()).collect(Collectors.toList());
                        contentDtoList = contentDao.queryByContentCodes(contentCodes);
                        if (!CollectionUtils.isEmpty(contentDtoList)) {
                            contentDtoList.stream().forEach(c -> {
                                Optional<Dic> first = dicList.stream().filter(d -> String.valueOf(c.getType()).equals(d.getValueCode())).findFirst();
                                if (first.isPresent()) {
                                    c.setTypeName(first.get().getValueName());
                                }
                            });
                        }
                    } else {
                        contentDtoList = new ArrayList<>();
                    }
                } else {
                    goodsList = new ArrayList<>();
                    contentDtoList = new ArrayList<>();
                }
                //循环匹配模块关联的内容
                moduleMappings.stream().forEach(m ->{
                    List<SpecialColumnModuleContentMappingDto> contentFirstList = new ArrayList<>();
                    List<SpecialColumnModuleContentMappingDto> contentSecondList = new ArrayList<>();
                    List<SpecialColumnModuleContentMappingDto> moduleContentMappingDtos = moduleContentMappingList.stream().filter(c -> m.getId().equals(c.getModuleId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(moduleContentMappingDtos)){
                        //分类
                        contentFirstList =  moduleContentMappingDtos.stream().filter(c -> c.getModuleContentType() == 1).collect(Collectors.toList());
                        contentSecondList = moduleContentMappingDtos.stream().filter(c -> c.getModuleContentType() == 2).collect(Collectors.toList());
                        //内容一
                        if (!CollectionUtils.isEmpty(contentFirstList)) {
                            if ( 1 == m.getModuleContentFirstType()) {
                                contentFirstList.stream().forEach(c -> {
                                        if (!CollectionUtils.isEmpty(contentDtoList)) {
                                            Optional<GetCustomContentListOutDto> first = contentDtoList.stream().filter(content -> content.getCode().equals(c.getSourceCode())).findFirst();
                                            if (first.isPresent()) {
                                                c.setTitle(first.get().getTitle());
                                                c.setType(first.get().getTypeName());
                                                c.setCoverUrl(first.get().getCoverUrl());
                                                c.setSummary(first.get().getSummary());
                                                c.setOnlineStatus(first.get().getOnlineStatus().equals(1) ? "上线" : "下线");
                                            }
                                        }
                                });
                            } else {
                                contentFirstList.stream().forEach(c -> {
                                    c.setUrl(c.getCoverUrl());
                                });
                            }
                            //排序
                            contentFirstList = contentFirstList.stream().sorted(Comparator.comparing(SpecialColumnModuleContentMappingDto::getSort)).collect(Collectors.toList());

                        }
                        //内容二
                        if (!CollectionUtils.isEmpty(contentSecondList)){
                            contentSecondList.stream().forEach(p -> {
                                if ( "channel_goods_mapping".equals(p.getSourceTableName())) {
                                    if (!CollectionUtils.isEmpty(goodsList)) {
                                        Optional<GetChannelGoodsListOutDto> first = goodsList.stream().filter(g -> p.getSourceId().equals(g.getId())).findFirst();
                                        if (first.isPresent()) {
                                            p.setGoodsId(first.get().getGoodsId());
                                            if (!StringUtils.isEmpty(first.get().getName())) {
                                                p.setTitle(first.get().getName());
                                            }
                                            if (!StringUtils.isEmpty(first.get().getModelNum())) {
                                                p.setModelNum(first.get().getModelNum());
                                            }
                                            if (!StringUtils.isEmpty(first.get().getGoodsCategoryName())) {
                                                p.setType(first.get().getGoodsCategoryName());
                                            }
                                            if (!StringUtils.isEmpty(first.get().getOnline())) {
                                                p.setOnlineStatus(first.get().getOnline());
                                            }
                                            if (!StringUtils.isEmpty(first.get().getLabels())) {
                                                p.setTagNames(first.get().getLabels());
                                            }
                                        }
                                    }
                                } else {
                                    if (!CollectionUtils.isEmpty(contentDtoList)) {
                                        Optional<GetCustomContentListOutDto> first = contentDtoList.stream().filter(content -> content.getCode().equals(p.getSourceCode())).findFirst();
                                        if (first.isPresent()) {
                                            p.setTitle(first.get().getTitle());
                                            p.setType(first.get().getTypeName());
                                            p.setCoverUrl(first.get().getCoverUrl());
                                            p.setSummary(first.get().getSummary());
                                            p.setOnlineStatus(first.get().getOnlineStatus().equals(1) ? "上线" : "下线");
                                        }
                                    }
                                }
                            });
                            //排序
                            contentSecondList = contentSecondList.stream().sorted(Comparator.comparing(SpecialColumnModuleContentMappingDto::getSort)).collect(Collectors.toList());

                        }
                    }
                    m.setModuleContentFirstMappingList(contentFirstList);
                    m.setModuleContentSecondMappingList(contentSecondList);
                });
                //循环匹配栏目关联模块
                columnMappingDtoList.forEach(c ->{
                    List<SpecialColumnModuleMappingDto> columnModuleList = moduleMappings.stream().filter(m -> c.getId().equals(m.getColumnId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(columnModuleList)) {
                        //模块排序
                        List<SpecialColumnModuleMappingDto> columnModules = columnModuleList.stream().sorted(Comparator.comparing(SpecialColumnModuleMappingDto::getIndexSort)).collect(Collectors.toList());
                        c.setSpecialColumnModuleList(columnModules);
                    }
                });
            }
            columnMappingDtoList = columnMappingDtoList.stream().sorted(Comparator.comparing(SpecialColumnMappingDto::getIndexSort)).collect(Collectors.toList());
        } else {
            columnMappingDtoList = new ArrayList<>();
        }
        return columnMappingDtoList;
    }

    public SpecialMaterialContentDTO queryMaterialContentById(Long id) {
        QueryByIdInDto inDto = new QueryByIdInDto();
        inDto.setId(id);
        QueryByIdOutDto queryByIdOutDto = dao.queryById(inDto);
        // 查询所有专题的ChannelRadio
        List<ChannelRadio> queryRadioIds = dao.queryRadioIds(id);
        //查询关联的内容
        List<SpecialMaterialContentMapping> contentMappingList = specialMaterialContentMappingMapper.getContentMappingBySpecialId(id);
        List<SpecialMaterialDetailDTO> materialDetailDTOList = new ArrayList<>();
        List<SpecialGoodsDetailDTO> goodsDetailDTOList = new ArrayList<>();
        List<SpecialCaseDetailDTO> caseDetailDTOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(contentMappingList)){
            //进行分组
            Map<Integer, List<SpecialMaterialContentMapping>> groupMap = contentMappingList.stream()
                    .collect(Collectors.groupingBy(SpecialMaterialContentMapping::getSourceType));
            if (groupMap != null) {
                groupMap.forEach((sourceType, list) -> {
                    if (!CollectionUtils.isEmpty(list)) {
                        List<Integer> sourceIds = list.parallelStream().filter(x -> x.getSourceId() != null).map(s -> s.getSourceId().intValue()).collect(Collectors.toList());
                        if (sourceType == 1) {
                            //查询内容工场
                            if (!CollectionUtils.isEmpty(sourceIds)) {
                                MaterialContentListDto materialContentListDto =new MaterialContentListDto();
                                List<NodeMaterialContentListDto> materialContentList = materialMapper.getExportMaterialContentList(materialContentListDto, sourceIds, null);
                                for(SpecialMaterialContentMapping specialMaterialContentMapping : list){
                                    SpecialMaterialDetailDTO specialMaterialDetailDTO = new SpecialMaterialDetailDTO();
                                    BeanUtils.copyProperties(specialMaterialContentMapping,specialMaterialDetailDTO);
                                    if (!CollectionUtils.isEmpty(materialContentList)) {
                                        NodeMaterialContentListDto nodeMaterialContentListDto = materialContentList.stream().filter(x -> x.getMaterialId().equals(specialMaterialDetailDTO.getSourceId().intValue())).findFirst().orElse(null);
                                        specialMaterialDetailDTO.setNodeMaterialContentListDto(nodeMaterialContentListDto);
                                    }
                                    materialDetailDTOList.add(specialMaterialDetailDTO);
                                }
                            }
                        } else if (sourceType == 2) {
                            //查询找产品内容
                            if (!CollectionUtils.isEmpty(sourceIds)) {
                                List<ContentQueryDTO> goodsContentQueryDTOList = getContentList(sourceIds);
                                for(SpecialMaterialContentMapping specialMaterialContentMapping : list){
                                    SpecialGoodsDetailDTO specialGoodsDetailDTO = new SpecialGoodsDetailDTO();
                                    BeanUtils.copyProperties(specialMaterialContentMapping,specialGoodsDetailDTO);
                                    if (!CollectionUtils.isEmpty(goodsContentQueryDTOList)) {
                                        ContentQueryDTO goodsContentListDto = goodsContentQueryDTOList.stream().filter(x -> x.getId().equals(specialGoodsDetailDTO.getSourceId())).findFirst().orElse(null);
                                        specialGoodsDetailDTO.setGoodsContentDTO(goodsContentListDto);
                                    }
                                    goodsDetailDTOList.add(specialGoodsDetailDTO);
                                }
                            }

                        } else if (sourceType == 3) {
                            //查询找案例工场
                            if (!CollectionUtils.isEmpty(sourceIds)) {
                                List<ContentQueryDTO> caseContentQueryDTOList = getContentList(sourceIds);
                                for(SpecialMaterialContentMapping specialMaterialContentMapping : list){
                                    SpecialCaseDetailDTO specialCaseDetailDTO = new SpecialCaseDetailDTO();
                                    BeanUtils.copyProperties(specialMaterialContentMapping,specialCaseDetailDTO);
                                    if (!CollectionUtils.isEmpty(caseContentQueryDTOList)) {
                                        ContentQueryDTO caseContentListDto = caseContentQueryDTOList.stream().filter(x -> x.getId().equals(specialCaseDetailDTO.getSourceId())).findFirst().orElse(null);
                                        specialCaseDetailDTO.setCaseContentDTO(caseContentListDto);
                                    }
                                    caseDetailDTOList.add(specialCaseDetailDTO);
                                }
                            }
                        }
                    }
                });
            }
        }

        SpecialMaterialContentDTO materialContentDTO = new SpecialMaterialContentDTO();
        materialContentDTO.setId(queryByIdOutDto.getId());
        materialContentDTO.setTitle(queryByIdOutDto.getTitle());
        materialContentDTO.setDescribe(queryByIdOutDto.getDescribe());
        materialContentDTO.setCoverUrl(queryByIdOutDto.getCoverUrl());
        materialContentDTO.setValidStartTime(queryByIdOutDto.getValidStartTime());
        materialContentDTO.setValidEndTime(queryByIdOutDto.getValidEndTime());
        materialContentDTO.setSpecialType(queryByIdOutDto.getSpecialType());
        materialContentDTO.setChannelRadio(queryRadioIds);
        materialContentDTO.setMaterialContentList(materialDetailDTOList);
        materialContentDTO.setGoodsContentList(goodsDetailDTOList);
        materialContentDTO.setCaseContentList(caseDetailDTOList);
        return materialContentDTO;
    }


    public List<ContentQueryDTO> getContentList(List<Integer> contentIds){
        List<ContentQueryDTO> list = new ArrayList<>();
        ContentQueryVo contentQueryVo = new ContentQueryVo();
        contentQueryVo.setPage(1);
        contentQueryVo.setSize(50);
        contentQueryVo.setPurview(1L);
        contentQueryVo.setContentIds(contentIds.stream().map(x -> String.valueOf(x)).collect(Collectors.toList()));
        PageInfo<ContentQueryDTO> pageInfo = contentService.findContentsForSpecial(contentQueryVo);
        list = pageInfo.getRecords();
        return list;
    }

    private List<SpecialListVto> getSpecialListVto(List<ContentInfoDTO> list) {
        Map<Long, ContentInfoDTO> map = new HashMap<>();
        List<Long> ids = new ArrayList<>();
        list.stream().forEach(x -> {
            if ("special_info".equals(x.getSourceTable())) {
                ids.add(x.getContentId());
            }
            map.put(x.getContentId(), x);
        });
        if (!CollectionUtils.isEmpty(ids)) {
            List<SpecialListVto> specialListVtos = dao.selectSpecialByIds(ids);
            if (specialListVtos != null && specialListVtos.size() > 0) {
                specialListVtos.stream().forEach(x -> {
                    if (map.get(x.getId()) != null) {
                        x.setCreatedDate(map.get(x.getId()).getCreatedDate());
                        x.setMappingId(map.get(x.getId()).getId());
                        x.setSort(map.get(x.getId()).getSort());
                    }
                    x.setTableName("special_info");
                    if (StringUtils.isNotBlank(x.getCreatedPerson())){
                        x.setCreatedPerson(MybatisMateConfig.decrypt(x.getCreatedPerson()));
                    }
                });
                try {
                    specialListVtos = specialListVtos.stream().sorted(Comparator.comparing(SpecialListVto::getSort)).collect(Collectors.toList());
                } catch (Exception e){
                    log.error("排序失败，序号空指针",e);
                }
                return specialListVtos;
            }
        }
        return null;
    }

    private List<ContentInfoVo> getSpecialContent(List<ContentInfoDTO> list) {
        Map<String, ContentInfoDTO> map = new HashMap<>();
        list.stream().forEach(x -> {
            map.put(x.getSourceTable() + "-" + x.getContentId(), x);
        });
        List<ContentInfoVo> contentInfo = findContentInfo(list);
        if (contentInfo != null && contentInfo.size() > 0) {
            contentInfo.stream().forEach(x -> {
                String flag = "";
                if ("5".equals(x.getType())) {
                    flag = "content_menu-" + x.getId();
                    x.setTableName("content_menu");
                } else {
                    flag = "content-" + x.getId();
                    x.setTableName("content");
                }
                if (map.get(flag) != null) {
                    x.setCreatedDate(map.get(flag).getCreatedDate());
                    x.setMappingId(map.get(flag).getId());
                    x.setSort(map.get(flag).getSort());
                    x.setNewTitle(map.get(flag).getNewTitle());
                }
            });
            List<ContentInfoVo> newList = contentInfo.stream().sorted(Comparator.comparing(ContentInfoVo::getSort))
                    .collect(Collectors.toList());
            Collections.reverse(newList);
            return newList;
        }
        return null;
    }

    @Transactional
// @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void savaOrUpdate(SavaOrUpdateInDto savaOrUpdateInDto) {
        savaOrUpdateInDto.setCreatedDate(new Date());
        String userId = currentUser.getCurrentUserId();
        savaOrUpdateInDto.setCreatedBy(userId);
        savaOrUpdateInDto.setModifiedBy(userId);
        savaOrUpdateInDto.setModifiedDate(new Date());
        //UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        //添加創建人參數
        String username = userAuthor.getUsername();
        if (StringUtils.isNotBlank(username)) {
            savaOrUpdateInDto.setCreatedPerson(MybatisMateConfig.encrypt(username));
        }
        //处理封面小图多张上传
        if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getCoverSmallUrlList())){
            savaOrUpdateInDto.setCoverSmallUrl(String.join(",",savaOrUpdateInDto.getCoverSmallUrlList()));
        }

        // 新增
        if (savaOrUpdateInDto.getId() == null) {
            if ("1".equals(savaOrUpdateInDto.getFlag())) {
                // 保存专题信息
                dao.addSpecial(savaOrUpdateInDto);
                //添加操作日志
                QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(savaOrUpdateInDto.getId());
                AddLogInDto addLogInDto = new AddLogInDto(savaOrUpdateInDto.getId(), "保存内容专题(新增),状态为:" + queryHistoryStuatusOutDto.getAuditStatus(), userId, new Date(), userId, new Date());
                dao.addLog(addLogInDto);

                // 判断是否付费
                if(!"0".equals(savaOrUpdateInDto.getIsPaid())){
                    // 增加活动价格
                    contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(savaOrUpdateInDto.getType()),"1",true,false);
                    //title传null的原因是视频的name不用根据标题的改变而改变
                    contentService.updateAudioVedio(savaOrUpdateInDto.getId(),savaOrUpdateInDto.getTryAudioOrVideo(),"special_info",null);
                }
                addAdverMapping(savaOrUpdateInDto.getAdvers(), savaOrUpdateInDto.getId());
            } else {

                // 保存并提交专题
                dao.addSpecialAndSubmit(savaOrUpdateInDto);
                QueryHistoryStuatusOutDto queryHistoryStuatusOutDto = dao.queryHistoryStatus(savaOrUpdateInDto.getId());
                AddLogInDto addLogInDto = new AddLogInDto(savaOrUpdateInDto.getId(), "保存并提交内容专题(新增),状态为:" + queryHistoryStuatusOutDto.getAuditStatus(), userId, new Date(), userId, new Date());
                dao.addLog(addLogInDto);

                // 判断是否付费
                if(!"0".equals(savaOrUpdateInDto.getIsPaid())){
                    // 增加活动价格
                    contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(savaOrUpdateInDto.getType()),"1",true,false);
                    contentService.updateAudioVedio(savaOrUpdateInDto.getId(),savaOrUpdateInDto.getTryAudioOrVideo(),"special_info",null);
                }

                addAdverMapping(savaOrUpdateInDto.getAdvers(), savaOrUpdateInDto.getId());
            }
            // 新增专题内容
            if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getContent())) {
                saveSpecialContentMapping(savaOrUpdateInDto.getContent(), savaOrUpdateInDto.getId(), userId, "content");
            }
            // 新增渠道站点
            List<ChannelRadio> channelRadio = savaOrUpdateInDto.getChannelRadio();
            if (!CollectionUtils.isEmpty(channelRadio)) {
                //dao.addChannelRadios(savaOrUpdateInDto.getId(), channelRadio, userId);
                saveChannelRadio(savaOrUpdateInDto.getId(), userId, channelRadio);
            }
            // 新增关键字
            if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getKeyWord())) {
                saveKeyWorlds(savaOrUpdateInDto, userId);
            }
            // 新增公司
            if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getCompanyIds())) {
                dao.addCompanyMappings(savaOrUpdateInDto.getId(), savaOrUpdateInDto.getCompanyIds(), userId);
            }

            //添加点赞数等
            contentService.saveStatisticsCmsMapping(savaOrUpdateInDto.getId(),"special_info", savaOrUpdateInDto.getSystemCollectionCount(),savaOrUpdateInDto.getSystemBrowseCount());

//            //初始化点赞数,评论数..............
//            dao.addStatissticsValue(savaOrUpdateInDto.getId(), userId);
            /**
             * 09/02专题新增专栏类型
             */
            if ("06".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getSpecialStyles())) {
                saveSpecialStyle(savaOrUpdateInDto.getSpecialStyles(), userId, savaOrUpdateInDto.getId());
            }
            /**
             * 课程合辑
             */
            if ("07".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getSpecialList())) {
                AtomicInteger index = new AtomicInteger();
                savaOrUpdateInDto.getSpecialList().stream().forEach(c ->{
                    c.setSort(Long.valueOf(index.getAndIncrement()+1));
                });
                saveSpecialContentMapping(savaOrUpdateInDto.getSpecialList(), savaOrUpdateInDto.getId(), userId, "special");
            }

            // 判断是否付费
//            if(!"0".equals(savaOrUpdateInDto.getIsPaid())){
//                // 增加活动价格
//                contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(savaOrUpdateInDto.getType()),"1",true);
//            }
            if(("02".equals(savaOrUpdateInDto.getType()) || "03".equals(savaOrUpdateInDto.getType()))){
                //添加商品
                contentService.saveContentGoodsMapping(savaOrUpdateInDto.getId(),"special_info",savaOrUpdateInDto.getGoodsList(), false);

            }
            /**
             * 内容合辑
             */
            if ("10".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getSpecialColumnMappingList())) {
                saveSpecialColumnMapping(savaOrUpdateInDto.getSpecialColumnMappingList(), savaOrUpdateInDto.getId(), userId);
            }
            /**
             * 落地内容
             */
            if("10".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getLandingMappingList())){
                saveLandingMapping(savaOrUpdateInDto.getLandingMappingList(), savaOrUpdateInDto.getId(), savaOrUpdateInDto.getLandingType(), userId);
            }
        } else {
            log.info("修改+" + JSON.toJSONString(savaOrUpdateInDto));
            // 查询专题
            QueryByIdInDto dto = new QueryByIdInDto();
            dto.setId(savaOrUpdateInDto.getId());
            QueryByIdOutDto sprcial = dao.queryById(dto);
            // 修改
            dao.updateSpecial(savaOrUpdateInDto);
            // 删除渠道站点
            dao.deleteChannelRadioMapping(savaOrUpdateInDto);
            List<ChannelRadio> channelRadio = savaOrUpdateInDto.getChannelRadio();
            if (!CollectionUtils.isEmpty(channelRadio)) {
                //dao.addChannelRadios(savaOrUpdateInDto.getId(), channelRadio, userId);
                saveChannelRadio(savaOrUpdateInDto.getId(), userId, channelRadio);
            }
            // 删除关键字
            dao.deleteLable(savaOrUpdateInDto.getId(), userId);
            if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getKeyWord())) {
                saveKeyWorlds(savaOrUpdateInDto, userId);
            }
            // 删除公司
            dao.deleteCompanyMapping(savaOrUpdateInDto.getId(), userId);
            if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getCompanyIds())) {
                dao.addCompanyMappings(savaOrUpdateInDto.getId(), savaOrUpdateInDto.getCompanyIds(), userId);
            }
            //记录变更日志
            AddLogInDto addLogInDto = new AddLogInDto(savaOrUpdateInDto.getId(), "状态不变,内容变更", userId, new Date(), userId, new Date());
            dao.addLog(addLogInDto);
            /**
             * 09/02专题新增专栏类型
             */
            if ("06".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getSpecialStyles())) {
                //删除专栏
                dao.delStyle(savaOrUpdateInDto.getId());
                saveSpecialStyle(savaOrUpdateInDto.getSpecialStyles(), userId, savaOrUpdateInDto.getId());
            }

            // 新增操作日志
            String contentPriceLog = "";

            // 判断是否付费
            if("0".equals(savaOrUpdateInDto.getIsPaid())){
                // 增加活动价格
                contentPriceLog = contentPriceLog + contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(savaOrUpdateInDto.getType()),"0",false,false);
            }else{
                contentPriceLog = contentPriceLog + contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(savaOrUpdateInDto.getType()),"1",false,false);
                contentService.updateAudioVedio(savaOrUpdateInDto.getId(),savaOrUpdateInDto.getTryAudioOrVideo(),"special_info",null);
            }



            // 判断是否修改收费状态
            if(!sprcial.getIsPaid().equals(savaOrUpdateInDto.getIsPaid())){
                contentPriceLog = contentPriceLog + "是否付费从\"" + getIsPaid(sprcial.getIsPaid()) + "\"改成\"" + getIsPaid(savaOrUpdateInDto.getIsPaid()) + "\";";
            }

            // 划线价修改为空
            if((sprcial.getOriginalPrice() == null && null != savaOrUpdateInDto.getOriginalPrice()) ||
                    (sprcial.getOriginalPrice() != null && null == savaOrUpdateInDto.getOriginalPrice())){
                contentPriceLog = contentPriceLog + "划线价从\"" + (null == sprcial.getOriginalPrice() ? "" : sprcial.getOriginalPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getOriginalPrice() ? "" : savaOrUpdateInDto.getOriginalPrice()) + "\";";
            }

            // 判断是否修改划线价
            if((null != sprcial.getOriginalPrice() && null != savaOrUpdateInDto.getOriginalPrice()) && (sprcial.getOriginalPrice().compareTo(savaOrUpdateInDto.getOriginalPrice()) != 0)){
                contentPriceLog = contentPriceLog + "划线价从\"" + (null == sprcial.getOriginalPrice() ? "" : sprcial.getOriginalPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getOriginalPrice() ? "" : savaOrUpdateInDto.getOriginalPrice()) + "\";";
            }

            if((sprcial.getSellingPrice() == null && null != savaOrUpdateInDto.getSellingPrice()) ||
                    (sprcial.getSellingPrice() != null && null == savaOrUpdateInDto.getSellingPrice())){
                contentPriceLog = contentPriceLog + "售价从\"" + (null == sprcial.getSellingPrice() ? "" : sprcial.getSellingPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getSellingPrice() ? "" : savaOrUpdateInDto.getSellingPrice()) + "\";";
            }

            // 判断是否修改售价
            if((null != sprcial.getSellingPrice() && null != savaOrUpdateInDto.getSellingPrice()) && (sprcial.getSellingPrice().compareTo(savaOrUpdateInDto.getSellingPrice()) != 0)){
                contentPriceLog = contentPriceLog + "售价从\"" + sprcial.getSellingPrice() + "\"改成\"" + savaOrUpdateInDto.getSellingPrice() + "\";";
            }

            // 循环活动价格，判断是否有新增
            if(null != savaOrUpdateInDto.getActivityPrices() && !savaOrUpdateInDto.getActivityPrices().isEmpty()){
                for (ContentActivityPrice activityPrice : savaOrUpdateInDto.getActivityPrices()) {
                    if(StringUtils.isEmpty(activityPrice.getId())){
                        contentPriceLog = contentPriceLog + "添加活动价" + activityPrice.getActivityActivityPrice() + ";";
                    }
                }
            }
            if(!StringUtils.isEmpty(contentPriceLog)){
                insertContnetOperatorLog("special_info", savaOrUpdateInDto.getId(), "1", contentPriceLog);
            }
            //添加点赞数等
            contentService.saveStatisticsCmsMapping(savaOrUpdateInDto.getId(),"special_info",savaOrUpdateInDto.getSystemCollectionCount(),savaOrUpdateInDto.getSystemBrowseCount());

            // 增加专栏广告
            addAdverMapping(savaOrUpdateInDto.getAdvers(), savaOrUpdateInDto.getId());

            if(("02".equals(savaOrUpdateInDto.getType()) || "03".equals(savaOrUpdateInDto.getType()))){
                //添加商品
                contentService.saveContentGoodsMapping(savaOrUpdateInDto.getId(),"special_info",savaOrUpdateInDto.getGoodsList(), false);

            }
            /**
             * 内容合辑
             */
            if ("10".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getSpecialColumnMappingList())) {
                saveSpecialColumnMapping(savaOrUpdateInDto.getSpecialColumnMappingList(), savaOrUpdateInDto.getId(), userId);
            }
            /**
             * 落地内容
             */
            landingMappingDao.deleteBySourceId(savaOrUpdateInDto.getId(),"special_info");
            if("10".equals(savaOrUpdateInDto.getType()) && !CollectionUtils.isEmpty(savaOrUpdateInDto.getLandingMappingList())){
                saveLandingMapping(savaOrUpdateInDto.getLandingMappingList(), savaOrUpdateInDto.getId(), savaOrUpdateInDto.getLandingType(), userId);
            }

        }
    }

    private void saveLandingMapping(List<TLandingMapping> landingMappingList, Long id, Integer landingType, String userId) {
        landingMappingList.stream().forEach(l ->{
            l.setSourceId(id);
            l.setSourceTableName("special_info");
            l.setLandingType(landingType);
            l.setIsDeleted(0);
            l.setCreatedBy(userId);
            l.setCreatedDate(new Date());
            l.setModifiedBy(userId);
            l.setModifiedDate(new Date());
        });

        landingMappingDao.insertBatch(landingMappingList);
    }

    private void saveSpecialColumnMapping(List<SpecialColumnMappingDto> newColumnMappingList, Long id, String userId) {
        List<SpecialColumnMappingDto> oldColumnMappings = specialColumnMappingDao.selectBySpecialId(id);

        if (CollectionUtils.isEmpty(oldColumnMappings)){
            log.info("新增关联栏目");
            //批量新增栏目
            AtomicInteger index1 = new AtomicInteger();
            newColumnMappingList.stream().forEach(c ->{
                c.setSpecialId(id);
                c.setIndexSort(index1.getAndIncrement());
                c.setIsDeleted(0);
                c.setCreatedBy(userId);
                c.setCreatedDate(new Date());
                c.setModifiedBy(userId);
                c.setModifiedDate(new Date());
            });
            specialColumnMappingDao.insertBatch(newColumnMappingList);
            //批量新增模块
            List<SpecialColumnModuleMappingDto> specialColumnModuleList = new ArrayList<>();
            newColumnMappingList.stream().forEach(c ->{
                if (!CollectionUtils.isEmpty(c.getSpecialColumnModuleList())) {
                    AtomicInteger index2 = new AtomicInteger();
                    c.getSpecialColumnModuleList().stream().forEach(m -> {
                        m.setColumnId(c.getId());
                        m.setIndexSort(index2.getAndIncrement());
                        m.setIsDeleted(0);
                        m.setCreatedBy(userId);
                        m.setCreatedDate(new Date());
                        m.setModifiedBy(userId);
                        m.setModifiedDate(new Date());
                    });
                    specialColumnModuleList.addAll(c.getSpecialColumnModuleList());
                }
            });
            if (!CollectionUtils.isEmpty(specialColumnModuleList)) {
                specialColumnModuleMappingDao.insertBatch(specialColumnModuleList);
                //新增内容or商品
                List<SpecialColumnModuleContentMappingDto> specialColumnModuleContentMappingList = new ArrayList<>();
                specialColumnModuleList.stream().forEach(m -> {
                    if (!CollectionUtils.isEmpty(m.getModuleContentFirstMappingList())) {
                        AtomicInteger index3 = new AtomicInteger();
                        m.getModuleContentFirstMappingList().stream().forEach(c -> {
                            c.setModuleId(m.getId());
                            c.setModuleContentType(1);
                            c.setStyle(m.getModuleContentSecondStyle());
                            if (1 == m.getModuleContentFirstType()) {
                                if (c.getSourceCode().contains("C")) {
                                    c.setSourceTableName("content_menu");
                                } else {
                                    c.setSourceTableName("content");
                                }
                            } else {
                                c.setCoverUrl(c.getUrl());
                                c.setSourceTableName("url");
                            }
                            c.setSort(index3.getAndIncrement());
                            c.setIsDeleted(0);
                            c.setCreatedBy(userId);
                            c.setCreatedDate(new Date());
                            c.setModifiedBy(userId);
                            c.setModifiedDate(new Date());
                        });
                        specialColumnModuleContentMappingList.addAll(m.getModuleContentFirstMappingList());
                    }
                    if (!CollectionUtils.isEmpty(m.getModuleContentSecondMappingList())) {
                        AtomicInteger index4 = new AtomicInteger();
                        m.getModuleContentSecondMappingList().stream().forEach(c -> {
                            c.setModuleId(m.getId());
                            c.setModuleContentType(2);
                            c.setStyle(m.getModuleContentSecondStyle());
                            if (c.getSourceCode().contains("C")){
                                c.setSourceTableName("content_menu");
                            } else if (c.getSourceCode().contains("N")){
                                c.setSourceTableName("content");
                            } else {
                                c.setSourceTableName("channel_goods_mapping");
                            }
                            c.setSort(index4.getAndIncrement());
                            c.setIsDeleted(0);
                            c.setCreatedBy(userId);
                            c.setCreatedDate(new Date());
                            c.setModifiedBy(userId);
                            c.setModifiedDate(new Date());
                        });
                        specialColumnModuleContentMappingList.addAll(m.getModuleContentSecondMappingList());
                    }
                });
                if (!CollectionUtils.isEmpty(specialColumnModuleContentMappingList)) {
                    specialColumnModuleContentMappingDao.insertBatch(specialColumnModuleContentMappingList);
                }
            }
        } else {
            log.info("更新关联栏目");
            List<Long> columnIds = oldColumnMappings.stream().map(c -> c.getId()).collect(Collectors.toList());
            List<SpecialColumnModuleMappingDto> oldColumnModuleMappings = specialColumnModuleMappingDao.selectByColumnIds(columnIds);
            List<SpecialColumnModuleContentMappingDto> oldColumnModuleContentMappings = new ArrayList<>();
            List<Long> moduleIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(oldColumnModuleMappings)){
                moduleIds = oldColumnModuleMappings.stream().map(m -> m.getId()).collect(Collectors.toList());
                oldColumnModuleContentMappings = specialColumnModuleContentMappingDao.selectByModuleIds(moduleIds);
            }
            //1比对栏目
            //1-1取出需要更新的
            AtomicInteger index1 = new AtomicInteger();
            newColumnMappingList.stream().forEach(c ->{
                c.setSpecialId(id);
                c.setIndexSort(index1.getAndIncrement());
                c.setIsDeleted(0);
                c.setCreatedBy(userId);
                c.setCreatedDate(new Date());
                c.setModifiedBy(userId);
                c.setModifiedDate(new Date());
            });
            List<SpecialColumnMappingDto> updateColumnMappings = newColumnMappingList.stream().filter(c -> c.getId() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateColumnMappings)){
                //去除保留的需要更新的数据
                List<Long> updateColumnIds = updateColumnMappings.stream().map(c -> c.getId()).collect(Collectors.toList());
                columnIds.removeAll(updateColumnIds);
                //批量更新栏目
                specialColumnMappingDao.updateBatch(updateColumnMappings);

            }
            //1-2如果有被删除的数据则删除,没有需要更新的就是全部删除
            if (!CollectionUtils.isEmpty(columnIds)){
                specialColumnMappingDao.deleteByIds(columnIds);
            }
            //1-3取出需要新增的
            List<SpecialColumnMappingDto> addColumnMappings = newColumnMappingList.stream().filter(c -> c.getId() == null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addColumnMappings)) {
                //批量新增栏目
                specialColumnMappingDao.insertBatch(addColumnMappings);
            }
            //2比对模块
            List<SpecialColumnModuleMappingDto> specialColumnModuleList = new ArrayList<>();
            newColumnMappingList.stream().forEach(c ->{
                if (!CollectionUtils.isEmpty(c.getSpecialColumnModuleList())) {
                    AtomicInteger index2 = new AtomicInteger();
                    c.getSpecialColumnModuleList().stream().forEach(m -> {
                        m.setColumnId(c.getId());
                        m.setIndexSort(index2.getAndIncrement());
                        m.setIsDeleted(0);
                        m.setCreatedBy(userId);
                        m.setCreatedDate(new Date());
                        m.setModifiedBy(userId);
                        m.setModifiedDate(new Date());
                    });
                    specialColumnModuleList.addAll(c.getSpecialColumnModuleList());
                }
            });
            if (!CollectionUtils.isEmpty(specialColumnModuleList)) {
                List<SpecialColumnModuleMappingDto> updateModules = specialColumnModuleList.stream().filter(m -> m.getId() != null).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(updateModules)) {
                    List<Long> updateModuleIds = updateModules.stream().map(m -> m.getId()).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(moduleIds)) {
                        moduleIds.removeAll(updateModuleIds);
                    }
                    specialColumnModuleMappingDao.updateBatch(updateModules);
                }
                //新增模块
                List<SpecialColumnModuleMappingDto> addModules = specialColumnModuleList.stream().filter(m -> m.getId() == null).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(addModules)) {
                    specialColumnModuleMappingDao.insertBatch(addModules);
                }
                //3比对内容or商品
                List<Long> moduleContentIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(oldColumnModuleContentMappings)){
                    moduleContentIds = oldColumnModuleContentMappings.stream().map(c -> c.getId()).collect(Collectors.toList());
                }
                List<SpecialColumnModuleContentMappingDto> specialColumnModuleContentMappingList = new ArrayList<>();
                specialColumnModuleList.stream().forEach(m -> {
                    if (!CollectionUtils.isEmpty(m.getModuleContentFirstMappingList())) {
                        AtomicInteger index3 = new AtomicInteger();
                        m.getModuleContentFirstMappingList().stream().forEach(c -> {
                            c.setModuleId(m.getId());
                            c.setModuleContentType(1);
                            c.setStyle(m.getModuleContentSecondStyle());
                            if (StringUtils.isNotBlank(c.getSourceCode())) {
                                if (c.getSourceCode().contains("C")) {
                                    c.setSourceTableName("content_menu");
                                } else {
                                    c.setSourceTableName("content");
                                }
                            } else {
                                c.setSourceTableName("cover_url");
                            }
                            c.setSort(index3.getAndIncrement());
                            c.setIsDeleted(0);
                            c.setCreatedBy(userId);
                            c.setCreatedDate(new Date());
                            c.setModifiedBy(userId);
                            c.setModifiedDate(new Date());
                        });
                        specialColumnModuleContentMappingList.addAll(m.getModuleContentFirstMappingList());
                    }
                    if (!CollectionUtils.isEmpty(m.getModuleContentSecondMappingList())) {
                        AtomicInteger index4 = new AtomicInteger();
                        m.getModuleContentSecondMappingList().stream().forEach(c -> {
                            c.setModuleId(m.getId());
                            c.setModuleContentType(2);
                            c.setStyle(m.getModuleContentSecondStyle());
                            if (c.getSourceCode().contains("C")){
                                c.setSourceTableName("content_menu");
                            } else if (c.getSourceCode().contains("N")){
                                c.setSourceTableName("content");
                            } else {
                                c.setSourceTableName("channel_goods_mapping");
                            }
                            c.setSort(index4.getAndIncrement());
                            c.setIsDeleted(0);
                            c.setCreatedBy(userId);
                            c.setCreatedDate(new Date());
                            c.setModifiedBy(userId);
                            c.setModifiedDate(new Date());
                        });
                        specialColumnModuleContentMappingList.addAll(m.getModuleContentSecondMappingList());
                    }
                });
                if (!CollectionUtils.isEmpty(specialColumnModuleContentMappingList)) {
                    //取出需要更新的
                    List<SpecialColumnModuleContentMappingDto> updateModuleContents = specialColumnModuleContentMappingList.stream().filter(c -> c.getId() != null).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(updateModuleContents)){
                        List<Long> updateModuleContentIds = updateModuleContents.stream().map(c -> c.getId()).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(moduleContentIds)){
                            moduleContentIds.removeAll(updateModuleContentIds);
                        }
                        specialColumnModuleContentMappingDao.updateBatch(updateModuleContents);
                    }
                    //新增内容
                    List<SpecialColumnModuleContentMappingDto> addModuleContents = specialColumnModuleContentMappingList.stream().filter(c -> c.getId() == null).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(addModuleContents)) {
                        specialColumnModuleContentMappingDao.insertBatch(addModuleContents);
                    }
                }
                //删除内容
                if (!CollectionUtils.isEmpty(moduleContentIds)){
                    specialColumnModuleContentMappingDao.deleteByIds(moduleContentIds);
                }
            }
            //删除模块
            if (!CollectionUtils.isEmpty(moduleIds)){
                specialColumnModuleMappingDao.deleteByIds(moduleIds);
            }
        }
    }


    @Transactional
// @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public Integer savaOrUpdateMaterial(SavaMaterialContentDto savaMaterialContentDto) {
        try{
        String userId = currentUser.getCurrentUserId();
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        String username = userAuthor.getUsername();
        // 新增
        if (savaMaterialContentDto.getId() == null) {
            SavaOrUpdateInDto savaOrUpdateInDto = new SavaOrUpdateInDto();
            savaOrUpdateInDto.setCreatedDate(new Date());
            savaOrUpdateInDto.setCreatedBy(userId);
            savaOrUpdateInDto.setModifiedBy(userId);
            savaOrUpdateInDto.setModifiedDate(new Date());
            savaOrUpdateInDto.setCreatedPerson(MybatisMateConfig.encrypt(username));

            savaOrUpdateInDto.setTitle(savaMaterialContentDto.getTitle());
            savaOrUpdateInDto.setDescribe(savaMaterialContentDto.getDescribe());
            savaOrUpdateInDto.setCoverUrl(savaMaterialContentDto.getCoverUrl());
            savaOrUpdateInDto.setValidStartTime(savaMaterialContentDto.getValidStartTime());
            savaOrUpdateInDto.setValidEndTime(savaMaterialContentDto.getValidEndTime());
            savaOrUpdateInDto.setSpecialType(savaMaterialContentDto.getSpecialType());
            // 保存专题信息
            dao.addSpecial(savaOrUpdateInDto);
            //添加操作日志
            AddLogInDto addLogInDto = new AddLogInDto(savaOrUpdateInDto.getId(), "保存内容工厂专题(新增),状态为:待审核", userId, new Date(), userId, new Date());
            dao.addLog(addLogInDto);
            // 新增渠道站点
            List<ChannelRadio> channelRadio = savaMaterialContentDto.getChannelRadio();
            if (!CollectionUtils.isEmpty(channelRadio)) {
                saveChannelRadio(savaOrUpdateInDto.getId(), userId, channelRadio);
            }
            //插入关联内容
            if(!CollectionUtils.isEmpty(savaMaterialContentDto.getSpecialMaterialContentMappingList())){
                //插入数据
                List<SpecialMaterialContentMapping> contentMappings = savaMaterialContentDto.getSpecialMaterialContentMappingList();
                for(SpecialMaterialContentMapping contentMapping : contentMappings){
                    contentMapping.setSpecialId(savaOrUpdateInDto.getId());
                    if(contentMapping.getSourceType() != null){
                        if(contentMapping.getSourceType() == 1){
                            contentMapping.setSourceTableName("t_material");
                        } else if (contentMapping.getSourceType() == 2) {
                            contentMapping.setSourceTableName("content");
                        } else if (contentMapping.getSourceType() == 3) {
                            contentMapping.setSourceTableName("content");
                        }
                    }
                    contentMapping.setIsDeleted(0L);
                    contentMapping.setCreatedDate(new Date());
                    contentMapping.setCreatedBy(userId);
                    contentMapping.setModifiedBy(userId);
                    contentMapping.setModifiedDate(new Date());
                }
                if(!CollectionUtils.isEmpty(contentMappings)){
                    specialMaterialContentMappingMapper.insertBatch(contentMappings);
                }
            }
            return 1;
        } else {
            //修改
            log.info("修改+" + JSON.toJSONString(savaMaterialContentDto));
            // 查询专题
            QueryByIdInDto dto = new QueryByIdInDto();
            dto.setId(savaMaterialContentDto.getId());
            QueryByIdOutDto sprcial = dao.queryById(dto);
            // 修改
            SavaOrUpdateInDto savaOrUpdateInDto = new SavaOrUpdateInDto();

            savaOrUpdateInDto.setId(savaMaterialContentDto.getId());
            savaOrUpdateInDto.setModifiedBy(userId);
            savaOrUpdateInDto.setModifiedDate(new Date());

            savaOrUpdateInDto.setTitle(savaMaterialContentDto.getTitle());
            savaOrUpdateInDto.setDescribe(savaMaterialContentDto.getDescribe());
            savaOrUpdateInDto.setCoverUrl(savaMaterialContentDto.getCoverUrl());
            savaOrUpdateInDto.setValidStartTime(savaMaterialContentDto.getValidStartTime());
            savaOrUpdateInDto.setValidEndTime(savaMaterialContentDto.getValidEndTime());
            dao.updateSpecial(savaOrUpdateInDto);

            // 删除渠道站点
            dao.deleteChannelRadioMapping(savaOrUpdateInDto);
            List<ChannelRadio> channelRadio = savaMaterialContentDto.getChannelRadio();
            if (!CollectionUtils.isEmpty(channelRadio)) {
                //dao.addChannelRadios(savaOrUpdateInDto.getId(), channelRadio, userId);
                saveChannelRadio(savaOrUpdateInDto.getId(), userId, channelRadio);
            }
            specialMaterialContentMappingMapper.deleteById(savaMaterialContentDto.getId(),userId);
            //插入关联内容
            if(!CollectionUtils.isEmpty(savaMaterialContentDto.getSpecialMaterialContentMappingList())){
                //插入数据
                List<SpecialMaterialContentMapping> contentMappings = savaMaterialContentDto.getSpecialMaterialContentMappingList();
                for(SpecialMaterialContentMapping contentMapping : contentMappings){
                    contentMapping.setSpecialId(savaOrUpdateInDto.getId());
                    if(contentMapping.getSourceType() != null){
                        if(contentMapping.getSourceType() == 1){
                            contentMapping.setSourceTableName("t_material");
                        } else if (contentMapping.getSourceType() == 2) {
                            contentMapping.setSourceTableName("content");
                        } else if (contentMapping.getSourceType() == 3) {
                            contentMapping.setSourceTableName("content");
                        }
                    }
                    contentMapping.setIsDeleted(0L);
                    contentMapping.setCreatedDate(new Date());
                    contentMapping.setCreatedBy(userId);
                    contentMapping.setModifiedBy(userId);
                    contentMapping.setModifiedDate(new Date());
                }
                if(!CollectionUtils.isEmpty(contentMappings)){
                    specialMaterialContentMappingMapper.insertBatch(contentMappings);
                }
            }
            // 新增操作日志
//            String contentPriceLog = "";
//
//            if(!StringUtils.isEmpty(contentPriceLog)){
//                insertContnetOperatorLog("special_info", savaOrUpdateInDto.getId(), "1", contentPriceLog);
//            }
            return 1;
        }}catch (Exception e){
            log.error("保存内容工场专题打印错误{}",e.toString());
            throw new BusinessException("保存内容工场专题错误");
        }
    }






    @Transactional
// @CacheInvalidateByExp(area = "cmscenter",patternKey = "cmscenter_special*")
    public void updateSpecialPrice(SavaOrUpdateInDto savaOrUpdateInDto) throws Exception {

        String userId = currentUser.getCurrentUserId();
        savaOrUpdateInDto.setModifiedBy(userId);
        savaOrUpdateInDto.setModifiedDate(new Date());
        //UserAuthor userAuthor = userAuthorConfig.queryUserAuthor();
//        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        //添加創建人參數
//        String username = userAuthor.getUsername();
//        savaOrUpdateInDto.setCreatedPerson(username);

        if (savaOrUpdateInDto.getId() == null) {
            throw new Exception("主键参数错误");
        }
        log.info("修改+" + JSON.toJSONString(savaOrUpdateInDto));
        // 查询专题
        QueryByIdInDto dto = new QueryByIdInDto();
        dto.setId(savaOrUpdateInDto.getId());
        QueryByIdOutDto sprcial = dao.queryById(dto);
        // 修改
        dao.updateSpecialPrice(savaOrUpdateInDto);

        //记录变更日志
        AddLogInDto addLogInDto = new AddLogInDto(savaOrUpdateInDto.getId(), "状态不变,内容变更", userId, new Date(), userId, new Date());
        dao.addLog(addLogInDto);

        // 新增操作日志
        String contentPriceLog = "";

        // 判断是否付费
        if("0".equals(savaOrUpdateInDto.getIsPaid())){
            // 增加活动价格
            contentPriceLog = contentPriceLog + contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(sprcial.getType()),"0",false,false);
        }else{
            contentPriceLog = contentPriceLog + contentActivityPriceService.addContentAvtivityPrice(savaOrUpdateInDto.getActivityPrices(),savaOrUpdateInDto.getId(),getSpecialActivityPriceType(sprcial.getType()),"1",false,false);
        }

        // 判断是否修改收费状态
        if(!sprcial.getIsPaid().equals(savaOrUpdateInDto.getIsPaid())){
            contentPriceLog = contentPriceLog + "是否付费从\"" + getIsPaid(sprcial.getIsPaid()) + "\"改成\"" + getIsPaid(savaOrUpdateInDto.getIsPaid()) + "\";";
        }

        // 划线价修改为空
        if((sprcial.getOriginalPrice() == null && null != savaOrUpdateInDto.getOriginalPrice()) ||
                (sprcial.getOriginalPrice() != null && null == savaOrUpdateInDto.getOriginalPrice())){
            contentPriceLog = contentPriceLog + "划线价从\"" + (null == sprcial.getOriginalPrice() ? "" : sprcial.getOriginalPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getOriginalPrice() ? "" : savaOrUpdateInDto.getOriginalPrice()) + "\";";
        }

        // 判断是否修改划线价
        if((null != sprcial.getOriginalPrice() && null != savaOrUpdateInDto.getOriginalPrice()) && (sprcial.getOriginalPrice().compareTo(savaOrUpdateInDto.getOriginalPrice()) != 0)){
            contentPriceLog = contentPriceLog + "划线价从\"" + (null == sprcial.getOriginalPrice() ? "" : sprcial.getOriginalPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getOriginalPrice() ? "" : savaOrUpdateInDto.getOriginalPrice()) + "\";";
        }

        if((sprcial.getSellingPrice() == null && null != savaOrUpdateInDto.getSellingPrice()) ||
                (sprcial.getSellingPrice() != null && null == savaOrUpdateInDto.getSellingPrice())){
            contentPriceLog = contentPriceLog + "售价从\"" + (null == sprcial.getSellingPrice() ? "" : sprcial.getSellingPrice()) + "\"改成\"" + (null == savaOrUpdateInDto.getSellingPrice() ? "" : savaOrUpdateInDto.getSellingPrice()) + "\";";
        }

        // 判断是否修改售价
        if((null != sprcial.getSellingPrice() && null != savaOrUpdateInDto.getSellingPrice()) && (sprcial.getSellingPrice().compareTo(savaOrUpdateInDto.getSellingPrice()) != 0)){
            contentPriceLog = contentPriceLog + "售价从\"" + sprcial.getSellingPrice() + "\"改成\"" + savaOrUpdateInDto.getSellingPrice() + "\";";
        }

        // 循环活动价格，判断是否有新增
        if(null != savaOrUpdateInDto.getActivityPrices() && !savaOrUpdateInDto.getActivityPrices().isEmpty()){
            for (ContentActivityPrice activityPrice : savaOrUpdateInDto.getActivityPrices()) {
                if(StringUtils.isEmpty(activityPrice.getId())){
                    contentPriceLog = contentPriceLog + "添加活动价" + activityPrice.getActivityActivityPrice() + ";";
                }
            }
        }
        if(!StringUtils.isEmpty(contentPriceLog)){
            insertContnetOperatorLog("special_info", savaOrUpdateInDto.getId(), "1", contentPriceLog);
        }

    }

    private void saveSpecialContentMapping(List<SpecialContents> specialContents, Long specialId, String userId, String flag) {
        List<SpecialContents> list = new ArrayList<>();
        Set<String> set = new HashSet<>();
        if ("content".equals(flag)) {
            for (SpecialContents li : specialContents) {
                if ("5".equals(li.getType())) {
                    li.setType("content_menu");
                } else {
                    li.setType("content");
                }
                //去重
                String temflag = li.getType() + "-" + li.getId();
                if (!set.contains(temflag)) {
                    list.add(li);
                    set.add(temflag);
                }
            }
        } else {//课程合辑
            for (SpecialContents li : specialContents) {
                if (("02".equals(li.getType()) || "03".equals(li.getType()) || "04".equals(li.getType())) && !specialId.equals(li.getId())) {//视频课程  音频课程 菜谱课程
                    li.setType("special_info");
                    //去重
                    String temflag = "special_info-" + li.getId();
                    if (!set.contains(temflag)) {
                        list.add(li);
                        set.add(temflag);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            dao.addContents(list, specialId, userId);
        }
        // 更新专题所含内容数量
        int size = list.size();
        long count = Long.parseLong((size + ""));
        dao.updateContentCounts(specialId, count, userId);
    }

    private void saveSpecialStyle(List<SpecialStyle> specialStyles, String userId, Long specialId) {
        for (SpecialStyle x : specialStyles) {
            x.setCreatedBy(userId);
            x.setCreatedDate(new Date());
            x.setModifiedBy(userId);
            x.setModifiedDate(new Date());
            x.setSpecialId(specialId);
            dao.insertStyles(x);
            if (!CollectionUtils.isEmpty(x.getStyleContents())) {
                List<SpecialStyleContentMapping> styleContents = new ArrayList<>();
                x.getStyleContents().forEach(m -> {
                    m.setCreatedBy(userId);
                    m.setCreatedDate(new Date());
                    m.setModifiedBy(userId);
                    m.setModifiedDate(new Date());
                    if (m.getSort() == null) {
                        m.setSort(1L);
                    }
                    if (StringUtils.isNotBlank(m.getStype())) {//专题类型不是空代表是专题
                        if (!specialId.equals(m.getSourceId())) {
                            m.setSourceTableName("special_info");
                            styleContents.add(m);
                        }
                    } else if ("5".equals(m.getType())) {
                        m.setSourceTableName("content_menu");
                        styleContents.add(m);
                    } else {
                        m.setSourceTableName("content");
                        styleContents.add(m);
                    }

                });
                if (styleContents != null && styleContents.size() > 0) {
                    dao.insertStyleContents(styleContents, x.getId());
                }
            }
        }
    }

    private void saveChannelRadio(Long specialId, String userId, List<ChannelRadio> channelRadio) {
        Set<String> set = new HashSet<>();
        List<ChannelRadio> list = new ArrayList<>();
        channelRadio.stream().forEach(x -> {
            if (!set.contains(x.getChannelId() + "-" + x.getRadioId())) {
                list.add(x);
                set.add(x.getChannelId() + "-" + x.getRadioId());
            }
        });
        if (list != null && list.size() > 0) {
            dao.addChannelRadios(specialId, list, userId);
        }
    }

    private void saveKeyWorlds(SavaOrUpdateInDto savaOrUpdateInDto, String userId) {
        if (!CollectionUtils.isEmpty(savaOrUpdateInDto.getKeyWord())) {
            dao.addLabelMapping(savaOrUpdateInDto.getKeyWords(), savaOrUpdateInDto.getId(), userId);
        }
    }

    // @Cached(cacheType =CacheType.REMOTE,expire = 3600,name =
// "special_queryAllLabels",area = "cmscenter")
    public PageInfo<Lablel> queryAllLabels(QueryAllLabelsInDto inDto) {

        PageInfo<Lablel> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        Integer count = dao.queryAllLabelsCount(inDto);
        List<Lablel> list = new ArrayList<>();
        if (count > 0) {
            inDto.setOffset(pageInfo.getOffset());
            list = dao.queryAllLabels(inDto);
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(list);
        return pageInfo;

    }

    public List<FindChannelUrlByIdOutDto> queryUrlList(QueryUrlInDto inDto) {
        QueryByIdInDto queryByIdInDto= new QueryByIdInDto();
        queryByIdInDto.setId(inDto.getId());
        QueryByIdOutDto outDto = dao.queryById(queryByIdInDto);
        if (inDto.getType().equals("01")) {
            inDto.setType("3-01");
        } else if (inDto.getType().equals("05")) {
            inDto.setType("3-02");
        } else if (inDto.getType().equals("06")) { //栏目
            inDto.setType("3-05");
        } else if(inDto.getType().equals("09")){  //专栏
            inDto.setType("3-09");
        } else if(inDto.getType().equals("02")||inDto.getType().equals("03")){  //视频
            if (null!=outDto.getIsPaid()&&outDto.getIsPaid().equals("1")) {
                inDto.setType("3-06");
            }else {
                inDto.setType("3-03");
            }
        } else if(inDto.getType().equals("10")){  //专栏
            inDto.setType("3-10");
        } else {
            inDto.setType("3-03");
        }
        List<Long> channelIds = dao.queryChannels(inDto.getId());
        List<FindChannelUrlByIdOutDto> res = new ArrayList<>();
        if (channelIds != null && channelIds.size() > 0) {
            Result<List<FindChannelUrlByIdOutDto>> result = channelClient.findByIds(channelIds, 3, inDto.getType());
            res = result.getData();
            res.stream().forEach(o -> {
                if (StringUtils.isNotBlank(o.getUrl())) {
                    o.setUrl(o.getUrl() + inDto.getId());
                }
            });
        }
        return res;
    }

    /**
     * 官网-搜索-专题/指南
     */
    public PageInfo<QueryWebsiteSpecialOutDto> queryAllSpecialWebsite(QueryWebsiteSpecialInDto queryWebsiteSpecialInDto) {
        PageInfo pageInfo = new PageInfo(queryWebsiteSpecialInDto.getPage(), queryWebsiteSpecialInDto.getSize());
        Long count = dao.queryAllSpecialWebsiteCount(queryWebsiteSpecialInDto);
        queryWebsiteSpecialInDto.setOffset(pageInfo.getOffset());
        List<QueryWebsiteSpecialOutDto> list = dao.queryAllSpecialWebsite(queryWebsiteSpecialInDto);
        pageInfo.setTotal(count);
        pageInfo.setRecords(list);
        return pageInfo;
    }

    @Transactional
    public void addContentMapping(AddContentMappingInDto addContentMappingInDto) {
        if (addContentMappingInDto == null) {
            return;
        }
        String userId = currentUser.getCurrentUserId();
        String type = dao.querySpecialTypeById(addContentMappingInDto.getId());
        List<SpecialContents> contentList = CollectionUtils.isEmpty(addContentMappingInDto.getContentList()) ? null : addContentMappingInDto.getContentList();
        if (contentList != null && contentList.size() > 0) {
            contentList.stream().forEach(x -> {
                if (!"07".equals(type)) {
                    if ("5".equals(x.getType())) {
                        x.setType("content_menu");
                    } else {
                        x.setType("content");
                    }
                }
            });

            //校验是否已经内容存在该专题
            contentList.stream().forEach(li -> {
                if ("07".equals(type) && !addContentMappingInDto.getId().equals(li.getId())) {//当专题类型为课程合辑时去除掉与专题id相同的专题
                    if ("02".equals(li.getType()) || "03".equals(li.getType()) || "04".equals(li.getType())) {//视频课程  音频课程 菜谱课程
                        Integer queryContentIsIn = dao.queryContentIsIn(addContentMappingInDto.getId(), li.getId(), "special_info");
                        if (queryContentIsIn == 0) {
                            dao.addContentMapping(li.getSort(), "special_info", li.getId(), addContentMappingInDto.getId(), userId,null);
                        }
                    }
                } else if (!"07".equals(type)) {
                    Integer queryContentIsIn = dao.queryContentIsIn(addContentMappingInDto.getId(), li.getId(), li.getType());
                    if (queryContentIsIn == 0) {
                        dao.addContentMapping(li.getSort(), li.getType(), li.getId(), addContentMappingInDto.getId(), userId,null);
                    }
                }
            });
        }
        long count = dao.queryContentIsIn(addContentMappingInDto.getId(), null, null);
        dao.updateContentCounts(addContentMappingInDto.getId(), count, userId);
        dao.updateModifyTimeById(new Date(), addContentMappingInDto.getId());
    }

    public void delContentMapping(List<Long> ids) {
        if (ids != null && ids.size() > 0) {
            String userId = currentUser.getCurrentUserId();
            Long specialId = dao.getSpecialIdByMapId(ids.get(0));
            dao.delContentMapping(ids);
            long count = dao.queryContentIsIn(specialId, null, null);
            dao.updateContentCounts(specialId, count, userId);
        }
    }

    public void setSort(List<SetSortsInDto> setSortList) {
        if (setSortList != null && setSortList.size() > 0) {
            setSortList.stream().forEach(x -> {
                dao.setSort(x);
            });
        }
    }

    public void sort(SetSortsInDto setSortsInDto) {
        dao.sort(setSortsInDto);
    }

    public List<ContentInfoVo> findContentInfo(List<ContentInfoDTO> contentInfoDTOS) {
        List<ContentInfoVo> result = new ArrayList<>();
        if (contentInfoDTOS != null && contentInfoDTOS.size() != 0) {
            for (ContentInfoDTO contentInfoDTO : contentInfoDTOS) {
                if ("content".equals(contentInfoDTO.getSourceTable())) {
                    ContentInfoVo contentInfoVo = contentDao.findSpecialContentBycontentId(contentInfoDTO.getContentId());
                    if (contentInfoVo != null) {
                        if (StringUtils.isNotBlank(contentInfoVo.getCreatedPerson())){
                            contentInfoVo.setCreatedPerson(MybatisMateConfig.decrypt(contentInfoVo.getCreatedPerson()));
                        }
                        result.add(contentInfoVo);
                    }
                } else if ("content_menu".equals(contentInfoDTO.getSourceTable())) {
                    ContentInfoVo contentInfoVo = contentMenuDao.findSpecialContentMenuByMenuId(contentInfoDTO.getContentId());
                    if (contentInfoVo != null) {
                        if (StringUtils.isNotBlank(contentInfoVo.getCreatedPerson())){
                            contentInfoVo.setCreatedPerson(MybatisMateConfig.decrypt(contentInfoVo.getCreatedPerson()));
                        }
                        result.add(contentInfoVo);
                    }
                }
            }
        }
        return result;
    }


    public String getIsPaid(String flag) {
        String status = "";
        switch (flag) {
            case "1":
                status = "全员付费";
                break;
            case "2":
                status = "VIP会员免费";
                break;
            default:
                status = "免费";
                break;
        }
        return status;
    }

    public String getSpecialActivityPriceType(String type) {
        String status = "";
        switch (type) {
            case "03":
                // 音频课程
                status = "3";
                break;
            case "04":
                // 菜谱课程
                status = "4";
                break;
            case "02":
                // 视频课程
                status = "5";
                break;
            case "06":
                // 内容专栏
                status = "6";
                break;
            case "07":
                // 课程合辑
                status = "7";
                break;
            case "10":
                // 听书课程
                status = "8";
                break;
            default:
                status = "";
                break;
        }
        return status;
    }

    /**
     * 添加操作日志
     * @param sourceTable
     * @param sourceId
     * @param operatorType
     * @param description
     */
    public void insertContnetOperatorLog(String sourceTable, Long sourceId, String operatorType, String description) {
        // 获取用户
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();
        ContentOperatorLog contentLog = new ContentOperatorLog();
        contentLog.setSourceTableName(sourceTable);
        contentLog.setSourceId(sourceId);
        contentLog.setDescription(description);
        contentLog.setOperatorType(Long.valueOf(operatorType));
        if (StringUtils.isNotBlank(userAuthor.getUsername())) {
            contentLog.setCreatedByName(MybatisMateConfig.encrypt(userAuthor.getUsername()));
        }
        contentLog.setCreatedBy(userAuthor.getId());
        contentLog.setCreatedDate(new Date());
        contentLog.setModifiedBy(userAuthor.getId());
        contentLog.setModifiedDate(new Date());
        contentLog.setIsDeleted((long) 0);
        contentOperatorLogDao.insert(contentLog);
    }


    public void addAdverMapping(List<AddSpecialAdverDTO> advers, Long specialInfoId){
        if(null == advers || advers.isEmpty()){
            return;
        }
        // 登陆用户信息
        UserAuthor userAuthor = userAuthorConfig.queryUserEntityInfoByUserId();

        // 先删除所有数据
//        Map<String,Object> delMap = new HashMap<>();
//        delMap.put("source_id", specialInfoId);
//        delMap.put("source_table_name", "special_info");
//        adverMappingDao.deleteByMap(delMap);
        QueryWrapper deleteQw = new QueryWrapper();
        deleteQw.eq("special_id", specialInfoId);
        deleteQw.in("source_table_name", "adver_group", "adver");
        specialAdverGroupMappingDao.delete(deleteQw);

        List<SpecialAdverGroupMapping> insertList = new ArrayList();
        for (AddSpecialAdverDTO adverDto : advers) {
            SpecialAdverGroupMapping adver = new SpecialAdverGroupMapping();
            adver.setSpecialId(specialInfoId);
            adver.setSort(adverDto.getSort());
            adver.setSourceId(adverDto.getAdverId());
            adver.setSourceTableName(adverDto.getTableName());
            adver.setIsDeleted((long) 0);
            adver.setCreatedBy(userAuthor.getId());
            adver.setCreatedDate(new Date());
            insertList.add(adver);
        }

        // 开始插入数据
        if(!insertList.isEmpty()){
            specialAdverGroupMappingDao.insertBatch(insertList);
        }

    }


    public List<Map<String,Object>> getAdverList(String specialInfoId){
        Map<String,Object> param = new HashMap<>();
        param.put("specialId", specialInfoId);
        List<Map<String,Object>> data = specialAdverGroupMappingDao.selectAdverList(param);

        List<Map<String,Object>> list = new ArrayList<>();
        for (Map<String, Object> map : data) {
            if(null != map.get("channelId") && !StringUtils.isEmpty(map.get("channelId").toString())){
                FindChannelByIdOutDto channelOutDto = orgClient.findChannelById(Long.valueOf(map.get("channelId").toString())).getData();
                if (channelOutDto != null) {
                    map.put("channelName", channelOutDto.getName());
                }
            }
            list.add(map);
        }
        return list;
    }


    //修改新标题
    public void setNewTitle(SetSortsInDto setSortList) {
        dao.setNewTitle(setSortList);
    }

    public MaterialSpecialResp getSpecialById(GetSpecialResp getSpecialResp) {
        //List<Long> channelIds = getChannelRatioDTOs();

        //1.查询当前专题信息
        QueryByIdInDto queryByIdInDto = new QueryByIdInDto();
        queryByIdInDto.setId(getSpecialResp.getSpecialId());
        queryByIdInDto.setRadioId(61L);
        QueryByIdOutDto queryByIdOutDto = dao.queryMaterialSpecialById(queryByIdInDto);
        //2.校验专题信息
        checkSpecialInfo(queryByIdOutDto);
        List<SpecialMaterialContentMapping> specialMaterialContentMappingList = specialMaterialContentMappingMapper.getContentMappingBySpecialId(getSpecialResp.getSpecialId());
        if (CollectionUtils.isEmpty(specialMaterialContentMappingList)){
            log.error("专题关联内容不存在");
            throw new BusinessException("该内容已下架");
        }
        MaterialSpecialResp materialSpecialResp = new MaterialSpecialResp();
        materialSpecialResp.setSpecialId(queryByIdOutDto.getId());
        materialSpecialResp.setCoverUrl(queryByIdOutDto.getCoverUrl());
        materialSpecialResp.setSpecialName(queryByIdOutDto.getTitle());
        materialSpecialResp.setDescribe(queryByIdOutDto.getDescribe());
        Long favoriteCount = favoriteMappingRepo.queryFavoriteCountBySpecialId(getSpecialResp.getSpecialId());
        materialSpecialResp.setFavoriteCount(favoriteCount);
        Integer isCollected = favoriteMappingRepo.queryFavoriteCountByUserId(getSpecialResp.getSpecialId(), userAuthorConfig.getCurrentUserId());
        materialSpecialResp.setIsCollected(isCollected);
        //导购发送数
        Long shareCount = eventLogMapper.queryShareCountBySpecialIdAndUserId(getSpecialResp.getSpecialId(), userAuthorConfig.queryUserAuthor().getSalesmanId());
        materialSpecialResp.setShareCount(shareCount);
        //顾客查看数量调用cdp

        QuerySpecialInsightInDTO querySpecialInsightInDTO = new QuerySpecialInsightInDTO();
        querySpecialInsightInDTO.setSpecialIds(Arrays.asList(getSpecialResp.getSpecialId()));
        Result<QueryWXUserInfoBychargeUserIdVO> queryWXUserInfoBychargeUserIdVOResult = customerClient.wxUserInfoByChargeUserId(userAuthorConfig.queryUserAuthor().getSalesmanId());
        if (queryWXUserInfoBychargeUserIdVOResult.getSuccess()&&queryWXUserInfoBychargeUserIdVOResult.getData()!=null){
            querySpecialInsightInDTO.setUserId(queryWXUserInfoBychargeUserIdVOResult.getData().getUserId());
            Result<List<QuerySpecialInsightOutDTO>> specialInsightCountResult = cdpClient.getSpecialInsightCount(querySpecialInsightInDTO);
            if (specialInsightCountResult.getSuccess()&&!CollectionUtils.isEmpty(specialInsightCountResult.getData())){
                materialSpecialResp.setShowCount(specialInsightCountResult.getData().get(0).getOpen());
            }
        }
        FindSalesmanByIdOutDto salesmanInfo = getSalesmanInfo(userAuthorConfig.queryUserAuthor().getSalesmanId());

        //3.查询专题关联内容树,专题列表中过滤出内容树关联id
        List<SpecialMaterialContentMapping> materialSpecialList = specialMaterialContentMappingList.stream()
                .filter(content -> Objects.equals(1, content.getSourceType())).collect(Collectors.toList());
        Map<Integer, TreeContentListDto> treeContentListDtoMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(materialSpecialList)){
            List<Long> materialIdList = materialSpecialList.stream().map(SpecialMaterialContentMapping::getSourceId).collect(Collectors.toList());
            //3.1 查询上架的内容树
            PageInfo<Object> pageInfo = new PageInfo<>(1,materialIdList.size());
            InteractiveContentListDto interactiveContentListDto = new InteractiveContentListDto();
            interactiveContentListDto.setMaterialIdList(materialIdList);
            List<TreeContentListDto> materialContentList = materialMapper.getMaterialContentList(pageInfo, interactiveContentListDto, null, "");
            if (!CollectionUtils.isEmpty(materialContentList)){
                materialContentService.buildBasicMaterialContent(materialContentList,userAuthorConfig.getCurrentUserId());
                treeContentListDtoMap = materialContentList.stream()
                        .collect(Collectors.toMap(TreeContentListDto::getMaterialId, Function.identity()));

            }
        }

        //4. 查询专题关联找产品, 产品列表中过滤出产品关联id
        List<SpecialMaterialContentMapping> productContentIdList = specialMaterialContentMappingList.stream()
                .filter(content -> Objects.equals(2, content.getSourceType())).collect(Collectors.toList());
        Map<Long, List<ContentDetailOutDto>> contentAndProductMap = Maps.newHashMap();
        Map<Long, ContentDetailOutDto> contentMap = Maps.newHashMap();
        Map<Long, List<Long>> contentAndGoodsMap= Maps.newHashMap();
        if (!CollectionUtils.isEmpty(productContentIdList)){
            //内容id
            List<Long> contentIds = productContentIdList.stream().map(SpecialMaterialContentMapping::getSourceId).collect(Collectors.toList());
            //内容商品关联
            List<ContentGoodsMapping> contentGoodsMappingList = goodsMappingDao.findGoodsBySourceBach(contentIds, "content");
            if (!contentGoodsMappingList.isEmpty()){
                List<Long> goodsIds = contentGoodsMappingList.stream().map(ContentGoodsMapping::getGoodsId).distinct().collect(Collectors.toList());
                GetProductListDto getProductListDto = new GetProductListDto();
                getProductListDto.setGoodsIds(goodsIds);
                getProductListDto.setUserId(userAuthorConfig.queryUserAuthor().getSalesmanId());
                if (salesmanInfo!=null){
                    getProductListDto.setSalesmanCompanyOrgId(getSpecialResp.getCompanyId());
                }
                getProductListDto.setPage(1L);
                getProductListDto.setSize((long) goodsIds.size());
                getProductListDto.setChannelId(1L);
                Result<PageInfo<GetProductListVo>> productListWithCaseNumResult = productClient.getProductListWithCaseNum(getProductListDto);

                if (productListWithCaseNumResult.getSuccess()&&productListWithCaseNumResult.getData()!=null&& !CollectionUtils.isEmpty(productListWithCaseNumResult.getData().getRecords())){
                    //找产品
                    List<GetProductListVo> productListVoList = productListWithCaseNumResult.getData()
                            .getRecords().stream().distinct().sorted(Comparator.comparing(GetProductListVo::getProductId)).collect(Collectors.toList());
                    //灵感案例与找产品对应关系 key 灵感案例 value 商品
                    contentAndGoodsMap = contentGoodsMappingList.stream()
                            .collect(Collectors.groupingBy(ContentGoodsMapping::getSourceId,Collectors.mapping(ContentGoodsMapping::getGoodsId, Collectors.toList())));
                    //灵感案例内容
                    List<Long> contentIdList = productContentIdList.stream().map(SpecialMaterialContentMapping::getSourceId).distinct().collect(Collectors.toList());
                    List<String> collect = contentIdList.stream().map(String::valueOf).collect(Collectors.toList());
                    List<ContentDetailOutDto> allContents = contentDao.getByIds(collect);
                    if (!CollectionUtils.isEmpty(allContents)){
                        contentMap=allContents.stream().collect(Collectors.toMap(ContentDetailOutDto::getId,Function.identity()));
                    }
                    for (Map.Entry<Long,List<Long>> map: contentAndGoodsMap.entrySet()) {
                        List<ContentDetailOutDto> contentDetailOutDtoList = Lists.newArrayList();
                        for (GetProductListVo getProductListVo: productListVoList) {
                            if (!CollectionUtils.isEmpty(contentDetailOutDtoList)){
                                continue;
                            }
                            if (map.getValue().contains(getProductListVo.getProductId())){
                                ContentDetailOutDto contentDetailOutDto = contentMap.get(map.getKey());
                                ContentDetailOutDto contentDetailOut = new ContentDetailOutDto();
                                BeanUtils.copyProperties(contentDetailOutDto,contentDetailOut);
                                GetProductListVo getProductList = new GetProductListVo();
                                BeanUtils.copyProperties(getProductListVo,getProductList);
                                contentDetailOut.setGetProductListVoList(getProductList);
                                contentDetailOutDtoList.add(contentDetailOut);
                            }
                        }
                        if (!CollectionUtils.isEmpty(contentDetailOutDtoList)){
                            contentAndProductMap.put(map.getKey(),contentDetailOutDtoList);
                        }
                    }
                }
            }
        }
        //5.查询专题关联找案例
        List<SpecialMaterialContentMapping> specialMaterialContentMappings = specialMaterialContentMappingList.stream()
                .filter(content -> Objects.equals(3, content.getSourceType())).collect(Collectors.toList());
        Map<Long, QueryCaseWithPageOutputDto> caseListDtoMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(specialMaterialContentMappings)){
            List<Long> caseIdList = specialMaterialContentMappings.stream().map(SpecialMaterialContentMapping::getSourceId).collect(Collectors.toList());
            QueryCaseWithPageInputDto queryCaseWithPageInputDto = new QueryCaseWithPageInputDto();
            queryCaseWithPageInputDto.setCaseIdList(caseIdList);
            queryCaseWithPageInputDto.setPage(1);
            queryCaseWithPageInputDto.setSize(caseIdList.size());
            queryCaseWithPageInputDto.setUserId(userAuthorConfig.queryUserAuthor().getSalesmanId());
            queryCaseWithPageInputDto.setCompanyId(getSpecialResp.getCompanyId());
            PageInfo<QueryCaseWithPageOutputDto> queryCaseWithPageOutputDtoPageInfo = casesService.queryCaseWithPage(queryCaseWithPageInputDto);
            if (!CollectionUtils.isEmpty(queryCaseWithPageOutputDtoPageInfo.getRecords())){
                List<QueryCaseWithPageOutputDto> caseWithPageOutputDtos = queryCaseWithPageOutputDtoPageInfo.getRecords();
                caseListDtoMap = caseWithPageOutputDtos.stream()
                        .collect(Collectors.toMap(QueryCaseWithPageOutputDto::getCaseId, Function.identity()));
            }
        }
        List<SpecialMaterialResp> specialMaterialRespList = Lists.newArrayList();
        for (SpecialMaterialContentMapping mapping:specialMaterialContentMappingList) {
            SpecialMaterialResp specialMaterialResp = new SpecialMaterialResp();
            specialMaterialResp.setSpecialId(mapping.getSpecialId());
            specialMaterialResp.setSort(mapping.getSort());
            specialMaterialResp.setSpecialMaterialType(mapping.getSourceType());

            //内容树
            if (!treeContentListDtoMap.isEmpty()&&treeContentListDtoMap.containsKey(mapping.getSourceId().intValue())){
                specialMaterialResp.setMaterialSpecialTreeContent(treeContentListDtoMap.get(mapping.getSourceId().intValue()));
                specialMaterialResp.setCreatedDate(treeContentListDtoMap.get(mapping.getSourceId().intValue()).getCreatedDate());
            }
            //案例
            if (!caseListDtoMap.isEmpty()&&caseListDtoMap.containsKey(mapping.getSourceId())){
                specialMaterialResp.setCaseSpecialContents(caseListDtoMap.get(mapping.getSourceId()));
                specialMaterialResp.setCreatedDate(caseListDtoMap.get(mapping.getSourceId()).getCreatedDate());
            }
            //找产品
            if (!contentAndProductMap.isEmpty()&&contentAndProductMap.containsKey(mapping.getSourceId())){
                specialMaterialResp.setContentDetailOutDto(contentAndProductMap.get(mapping.getSourceId()));
                specialMaterialResp.setCreatedDate(contentAndProductMap.get(mapping.getSourceId()).get(0).getCreatedDate());
            }

            if (specialMaterialResp.getMaterialSpecialTreeContent()!=null||
                    specialMaterialResp.getCaseSpecialContents()!=null||
                    !CollectionUtils.isEmpty(specialMaterialResp.getContentDetailOutDto())){
                specialMaterialRespList.add(specialMaterialResp);
            }
        }
        if (!CollectionUtils.isEmpty(specialMaterialRespList)){
            List<SpecialMaterialResp> collect = specialMaterialRespList.stream().distinct().sorted((o1, o2) -> {
                if (o1.getSort().equals(o2.getSort())){
                    return o2.getCreatedDate().compareTo(o1.getCreatedDate());
                }else {
                    return Math.toIntExact(o2.getSort() - o1.getSort());
                }
            }).collect(Collectors.toList());
            materialSpecialResp.setSpecialMaterialResps(collect);
        }
        //查询顾客产生线索数
/*        if (getSpecialResp.getActivityId()!=null&&StringUtils.isNotBlank(getSpecialResp.getUtmSource())){
            QueryCluesCountDto queryCluesCountDto = new QueryCluesCountDto();
            queryCluesCountDto.setUtmSource(getSpecialResp.getUtmSource());
            queryCluesCountDto.setSalesmanId(userAuthorConfig.queryUserAuthor().getSalesmanId());
            queryCluesCountDto.setActivityId(getSpecialResp.getActivityId());
            Result<Long> longResult = marketingClient.queryCluesCount(queryCluesCountDto);
            if (longResult.getSuccess()&&longResult.getData()!=null){
                materialSpecialResp.setCluesCount(longResult.getData());
            }
        }*/
        return materialSpecialResp;
    }

    public GetSpecialsContentDto querySpecialContentById(){
        GetSpecialsContentDto dto = new GetSpecialsContentDto();
        if (StringUtils.isNotBlank(specialContentMappingAppid)){
            dto.setAppid(specialContentMappingAppid);
        }else {
            dto.setAppid("wxc9fb43067fde0c84");
        }
        if (StringUtils.isNotBlank(specialContentMappingTitle)){
            dto.setTitle(specialContentMappingTitle);
        }else {
            dto.setTitle("方太邀请你 一起来发问");
        }
        if (StringUtils.isNotBlank(specialContentMappingImageUrl)){
            dto.setImageUrl(specialContentMappingImageUrl);
        }else {
            dto.setImageUrl("https://hsimage.fotile.com/202410210936236224350.jpeg");
        }
        if (StringUtils.isNotBlank(specialContentMappingShareUrl)){
            dto.setShareUrl(specialContentMappingShareUrl);
        }else {
            dto.setShareUrl("https://hsimage.fotile.com/202410210936236192804.jpeg");
        }
        if (StringUtils.isNotBlank(specialContentMappingArticleUrl)){
            dto.setArticleUrl(specialContentMappingArticleUrl);
        }else {
            dto.setArticleUrl("https://mp.weixin.qq.com/s/TgSk_nPvg5nCSDRpcvOapA");
        }
        if (StringUtils.isNotBlank(specialContentMappingPagePath)){
            dto.setPagePath(specialContentMappingPagePath);
        }else {
            dto.setPagePath("pages/test/webview");
        }
        if (StringUtils.isNotBlank(specialContentMappingArticleTitle)){
            dto.setArticleTitle(specialContentMappingArticleTitle);
        }else {
            dto.setArticleTitle("提出问题，才能解决问题；问什么，我们就改变什么；所有问题的答案，请关注 10月24日19:00#方太幸福洞见大会暨全球新一代高端全嵌冰箱发布");
        }
        return dto;
    }

    public FindSalesmanByIdOutDto getSalesmanInfo(Long salesmanId) {
        Result<FindSalesmanByIdOutDto> salesmanAssetById = orgClient.findSalesmanAssetById(salesmanId);
        if (salesmanAssetById.getSuccess()&&salesmanAssetById.getData()!=null){
            return salesmanAssetById.getData();
        }
        return null;
    }

    private void checkSpecialInfo(QueryByIdOutDto queryByIdOutDto){
        if (queryByIdOutDto==null){
            log.error("专题不存在");
            throw new BusinessException("该内容已下架");
        }
        if (!Objects.equals(queryByIdOutDto.getAuditStatus(),"3")){
            log.error("当前专题未审核通过");
            throw new BusinessException("该内容已下架");
        }
        if (!Objects.equals(queryByIdOutDto.getOnlineStatus(),"1")){
            log.error("当前专题已下线");
            throw new BusinessException("该内容已下架");
        }
        if (queryByIdOutDto.getValidStartTime()!=null&&queryByIdOutDto.getValidEndTime()!=null&&(
                queryByIdOutDto.getValidStartTime().after(new Date())||queryByIdOutDto.getValidEndTime().before(new Date())
                )){
            log.error("当前专题已结束");
            throw new BusinessException("该内容已下架");
        }
    }

    public PageInfo<QueryAllOutDto> queryAllByPost(QueryAllPostInDto inDto) {
        // 设置用户渠道权限
        if (inDto.getChannelIds() == null || inDto.getChannelIds().size() == 0) {
            List<Long> channelIds = getChannelRatioDTOs();
            if (channelIds != null && channelIds.size() != 0) {
                inDto.setChannelIds(channelIds);
            }
        }
        if (StringUtils.isNotBlank(inDto.getCreatedPerson())){
            inDto.setCreatedPerson(MybatisMateConfig.encrypt(inDto.getCreatedPerson()));
        }
        PageInfo<QueryAllOutDto> pageInfo = new PageInfo<>(inDto.getPage(), inDto.getSize());
        Integer count = dao.queryAllByPostCount(inDto);
        List<QueryAllOutDto> queryAllOutDtos = new ArrayList<>();
        if (count > 0) {
            inDto.setOffset(pageInfo.getOffset());
            queryAllOutDtos = dao.queryAllByPost(inDto);
        }
        pageInfo.setTotal(count);
        pageInfo.setRecords(queryAllOutDtos);
        return pageInfo;
    }




}
