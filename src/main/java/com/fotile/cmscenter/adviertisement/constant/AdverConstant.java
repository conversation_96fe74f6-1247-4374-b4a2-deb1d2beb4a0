package com.fotile.cmscenter.adviertisement.constant;

public class AdverConstant {

    /**
     * 跳转类型 01：URL 02：内容 03：活动 04：渠道分类树  05：专题  06：问答 07：内容菜谱  08问答-回答  09活动专题 10是无 11:小程序
     */
    public static enum JumpTypeEnum{
        url("01","URL"),
        content("02","内容"),
        activity("03","活动"),
        channel_category("04","渠道分类树"),
        special_subject("05","专题"),
        q_a("06","问答"),
        cookbook("07","内容菜谱"),
        answer("08","问答-回答"),
        activity_subject("09","活动专题"),
        none("10","无"),
        miniapp("11","小程序"),
        ;
        private String code;
        private String value;

        JumpTypeEnum(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static String getValueByCode(String code) {
            for (JumpTypeEnum jumpTypeEnum : JumpTypeEnum.values()) {
                if (jumpTypeEnum.getCode().equals(code)) {
                    return jumpTypeEnum.getValue();
                }
            }
            return "空";
        }
    }






}
