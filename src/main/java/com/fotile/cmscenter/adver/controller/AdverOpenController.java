package com.fotile.cmscenter.adver.controller;

import com.fotile.cmscenter.adver.pojo.dto.FindAdverByCodeInDto;
import com.fotile.cmscenter.adver.pojo.dto.FindAdverPageAllOutDto;
import com.fotile.cmscenter.adver.service.AdverService;
import com.fotile.cmscenter.util.LogUtils;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.BusinessException;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;

/**
 * 广告controller-open
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/open/adver")
@Api(value="广告接口",tags={"广告操作接口"})
@Slf4j
public class AdverOpenController extends BaseController {

	@Autowired
	private AdverService adverService;

	
	/**
	 * 根据广告位编码分页查询所有的广告
	 */
	@ApiOperation("根据广告位编码查询广告")
	@RequestMapping(value="/findAdverByCode",method=RequestMethod.GET,produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findAdverByCode(@Valid FindAdverByCodeInDto findAdverByCodeInDto){
		return success(adverService.findAdverByCode(findAdverByCodeInDto));
	}

	/**
	 * 获取UGC广告位
	 */
	@ApiOperation("获取UGC广告位")
	@RequestMapping(value="/findAdverForUGC",method=RequestMethod.GET,produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findAdverForUGC(){
		return success(adverService.findAdverForUGC());
	}

	/**
	 * 根据广告位编码分页查询所有的广告-设计师俱乐部小程序
	 */
	@ApiOperation("根据广告位编码查询广告-设计师俱乐部小程序")
	@RequestMapping(value="/findAdverByCode4DesignerClub",method=RequestMethod.GET,produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findAdverByCode4DesignerClub(@Valid FindAdverByCodeInDto findAdverByCodeInDto){
		return success(adverService.findAdverByCode4DesignerClub(findAdverByCodeInDto));
	}


	/**
	 * 根据广告位编码集合查询广告位
	 * 返回数据接口：广告位编码：广告数组  <br/>
	 * 返回值同下面接口：http://47.102.255.94:3000/project/42/interface/api/86993
	 */
	@PostMapping(value="/findAdverByCodes",produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<Map<String,List<FindAdverPageAllOutDto>>> findAdverByCodes(@RequestBody List<String> codes){
		return success(adverService.findAdverByCodes(codes));
	}


	/**
	 * 根据广告位编码获取最新一条广告数据
	 */
	@ApiOperation("根据广告位编码查询广告")
	@RequestMapping(value="/findOneAdverByCode",method=RequestMethod.GET,produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findOneAdverByCode(@Valid FindAdverByCodeInDto findAdverByCodeInDto){
		return success(adverService.findOneAdverByCode(findAdverByCodeInDto));
	}

	/**
	 * 根据广告位编码获取最新更新过的一条
	 */
	@ApiOperation("根据广告位编码查询广告")
	@RequestMapping(value="/findLatestOneAdverByCode",method=RequestMethod.GET,produces=MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findLatestOneAdverByCode(@Valid FindAdverByCodeInDto findAdverByCodeInDto){
		return success(adverService.findLatestOneAdverByCode(findAdverByCodeInDto));
	}


	/**
	 * 【一页纸报表】获取用户一页纸应用场景教学
	 */
	@RequestMapping(value = "/findOnePageApplyTeaching", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	public Result<List<FindAdverPageAllOutDto>> findOnePageApplyTeaching() {
		String className = new Exception().getStackTrace()[0].getClassName();
		String methodName = new Exception().getStackTrace()[0].getMethodName();

		try {
			return success("查询成功", adverService.findOnePageApplyTeaching());
		} catch (Exception e) {
			StringWriter sw = new StringWriter();
			e.printStackTrace(new PrintWriter(sw, true));
			log.error(LogUtils.getMethodPath(className, methodName) + ", errMsg = {}", sw);
			return failure(e instanceof BusinessException ? e.getMessage() : "查询异常, 请联系管理员");
		}
	}

}
