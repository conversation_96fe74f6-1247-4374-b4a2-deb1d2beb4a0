package com.fotile.cmscenter.adver.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fotile.cmscenter.adver.pojo.dto.*;
import com.fotile.cmscenter.adver.service.AdverPositionService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 广告位controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/adverposition")
@Api(value = "广告位接口", tags = {"广告位操作接口"})
@Slf4j
public class AdverPositionController extends BaseController {

    @Autowired
    private AdverPositionService adverPositionService;

    /**
     * 新增广告位
     *
     * @return
     */
    @ApiOperation("新增广告位")
    @RequestMapping(value = "/addAdverPosition", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<AddAdverPositionOutDto> addAdverPosition(@Valid @RequestBody AddAdverPositionInDto addAdverPositionInDto) {
        log.info("******AdverPositionController.addAdverPosition 条件：addAdverPositionInDto=" + addAdverPositionInDto);
        if (addAdverPositionInDto.getHappyOptRoleMgt()) {
            if (addAdverPositionInDto.getIsOpenContent() == 1) {
                if (addAdverPositionInDto.getContentGroupDto() == null) {
                    return failure("内容选择开启时，兜底内容与分组不能为空");
                } else {
                    if (addAdverPositionInDto.getContentGroupDto().getConsolationContent() == null) {
                        return failure("内容选择开启时，兜底内容不能为空");
                    }
                    if (CollectionUtil.isEmpty(addAdverPositionInDto.getContentGroupDto().getContentGroupList())) {
                        return failure("内容选择开启时，分组至少保留一个");
                    }
                }
            }
        }
        AddAdverPositionOutDto addAdverPositionOutDto = adverPositionService.addAdverPosition(addAdverPositionInDto);
        if (addAdverPositionOutDto != null && addAdverPositionOutDto.getId() != null) {
            return success("新增广告位成功", addAdverPositionOutDto);
        }
        return failure("新增广告位失败!");
    }

    /**
     * 分页查询所有的广告位
     */
    @ApiOperation("分页查询所有的广告位")
    @RequestMapping(value = "/findPageAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindAdverPositionPageAllOutDto> findPageAll(@Valid FindAdverPositionPageAllInDto adverPositionPageAllInDto) {
        log.info("******AdverPositionController.findPageAll 条件：adverPositionPageAllInDto=" + adverPositionPageAllInDto);
        return success(adverPositionService.findPageAll(adverPositionPageAllInDto));
    }


    /**
     * 根据id查询广告位
     */
    @ApiOperation("根据id查询广告位")
    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<FindAdverPositionByIdOutDto> findById(@Valid FindAdverPositionByIdInDto findAdverPositionByIdInDto) {
        log.info("******AdverPositionController.findById 条件：findAdverPositionByIdInDto=" + findAdverPositionByIdInDto);
        return success(adverPositionService.findById(findAdverPositionByIdInDto));
    }

    /**
     * 修改广告位
     */
    @ApiOperation("修改广告位信息")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result update(@Valid @RequestBody UpdateAdverPositionInDto updateAdverPositionInDto) {
        log.info("******AdverPositionController.update 条件：updateAdverPositionInDto=" + updateAdverPositionInDto);
        if (updateAdverPositionInDto.getHappyOptRoleMgt()) {
            if (updateAdverPositionInDto.getIsOpenContent() == 1) {
                if (updateAdverPositionInDto.getContentGroupDto() == null) {
                    return failure("内容选择开启时，兜底内容与分组不能为空");
                } else {
                    if (updateAdverPositionInDto.getContentGroupDto().getConsolationContent() == null) {
                        return failure("内容选择开启时，兜底内容不能为空");
                    }
                    if (CollectionUtil.isEmpty(updateAdverPositionInDto.getContentGroupDto().getContentGroupList())) {
                        return failure("内容选择开启时，分组至少保留一个");
                    }
                }
            }
        }
        int i = adverPositionService.update(updateAdverPositionInDto);
        if (i > 0) {
            return success("修改广告位成功");
        }
        return failure("修改广告位失败!");
    }


    /**
     * 修改广告位排序
     */
    @ApiOperation("修改广告位排序")
    @RequestMapping(value = "/sort", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result sort(@Valid @RequestBody SortAdverPositionInDto sortAdverPositionInDto) {
        log.info("******AdverPositionController.sort 条件：sortAdverPositionInDto=" + sortAdverPositionInDto);
        int i = adverPositionService.sort(sortAdverPositionInDto);
        if (i > 0) return success("修改广告位排序成功");
        return failure("修改广告位排序失败!");
    }
}
