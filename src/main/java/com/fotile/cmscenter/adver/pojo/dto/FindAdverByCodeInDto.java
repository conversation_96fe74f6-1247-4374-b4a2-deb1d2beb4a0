package com.fotile.cmscenter.adver.pojo.dto;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告实体类
 * <AUTHOR>
 *
 */
@Data
public class FindAdverByCodeInDto implements Serializable {
	
	  @NotNull(message="广告位编码不能为空")
	  @ApiParam(name="广告位编码",value="广告位编码")
	  private String code;

	/**
	 * 用户unionId
	 */
	private String unionId;

	/**
	 * type = 1查广告位下所有广告，type = 2查广告位下按排序倒序查最新一条广告，type = 3查广告位下按更新时间倒序查最新一条广告
	 */
	private Integer type;

	/**
	 * ab测试组id
	 */
	private Long trialGroupId;

}
