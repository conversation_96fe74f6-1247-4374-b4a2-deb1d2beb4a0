package com.fotile.cmscenter.adver.pojo.dto;

import com.fotile.cmscenter.content.pojo.vo.LabelVto;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 广告位实体类
 * <AUTHOR>
 *
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class FindAdverPositionByIdOutDto implements Serializable {
	
	  @ApiParam(name="广告位id",value="广告位id")
	  private Long id;
	
	  @ApiParam(name="广告位编码",value="广告位编码")
	  private String code;
	  
	  @ApiParam(name="广告位名称",value="广告位名称")
	  private String name;
	  
	  @ApiParam(name="所属渠道",value="所属渠道")
	  private Long channelId;
	  
	  @ApiParam(name="广告位宽",value="广告位宽")
	  private String width;
	  
	  @ApiParam(name="广告位高",value="广告位高")
	  private String height;
	  
	  @ApiParam(name="说明",value="说明")
	  private String note;
	  
	  @ApiParam(name="广告个数下限",value="广告个数下限")
	  private Integer upperLimit;
	  
	  @ApiParam(name="广告个数上限",value="广告个数上限")
	  private Integer lowerLimit;
	  
	  @ApiModelProperty(name="前端样式；0:无样式；1:半平滑(半铺)  2:卡片  3:轮循  4:半平滑(全铺) 5:半平滑（全铺-2）6:单个视频（全部）7:瀑布流（错位）8：列表（换一换）9：专栏 10：智能模块  11：导师列表 12：书单")
	  private Integer frontStyle;
	  
	  @ApiModelProperty(value="是否启用：0、禁用 1、启动")
	  private Integer stage;
	  
	  @ApiModelProperty(value="排序")
	  private Integer sort;

	@ApiModelProperty("广告显示数量")
	private Integer adverAccording;

	@ApiModelProperty("是否显示广告按钮 0否 1是")
	private Integer isAllButton;

	@ApiModelProperty("内容类型 1其他  2视频")
	private Integer contentType;

	@ApiModelProperty("内容分类id")
	private Long contentClassificationId;

	@ApiParam(name="页面停留时长",value="页面停留时长")
	private Integer stayLength;

	@ApiModelProperty("关键字")
	private List<LabelVto> keyWords;

	/**
	 * 0无，1广告位，2广告
	 */
	private Integer trialType;
	/**
	 * 测试id
	 */
	private Long trialId;
	private String trialName;
	/**
	 * 测试组id
	 */
	private Long trialGroupId;
	private String trialGroupName;
}
