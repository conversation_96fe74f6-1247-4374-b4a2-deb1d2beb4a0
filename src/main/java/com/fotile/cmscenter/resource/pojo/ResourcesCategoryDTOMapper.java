package com.fotile.cmscenter.resource.pojo;

import com.fotile.cmscenter.resource.pojo.dto.ResourcesCategoryOutDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel="spring")
public interface ResourcesCategoryDTOMapper {
    ResourcesCategoryDTOMapper INSTANCE = Mappers.getMapper(ResourcesCategoryDTOMapper.class );

    ResourcesCategoryOutDto resourcesCategoryOutDtoToResourcesCategory(ResourcesCategory resourcesCategory);
    List<ResourcesCategoryOutDto> resourcesCategoryOutDtoToResourcesCategoryList(List<ResourcesCategory> resourcesCategory);
}
