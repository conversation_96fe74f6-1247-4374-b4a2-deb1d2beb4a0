package com.fotile.cmscenter.resource.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.resource.pojo.Resources;
import com.fotile.cmscenter.resource.pojo.dto.ResourcesDTO;
import com.fotile.cmscenter.resource.pojo.vo.SelectResourcesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ResourcesDao extends BaseMapper<Resources> {

    List<ResourcesDTO> selectResources(@Param("selectResourcesVo") SelectResourcesVo selectResourcesVo);

    Integer selectResourcesCount(@Param("selectResourcesVo") SelectResourcesVo selectResourcesVo);

    int selectResourcesByCode(@Param("code") String code);

    /**
     * 根据资源分类ID查询资源
     */
    List<Resources> queryResourcesByCategoryId(@Param("resourcesCategoryId") Long resourcesCategoryId);

    int delResourcesById(@Param("id") Long id, @Param("modifiedBy") String modifiedBy);

    int batchAddResources(@Param("resources") List<Resources> resources);
}
