package com.fotile.cmscenter.resource.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fotile.cmscenter.resource.pojo.ResourcesCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源分类dao----一个资源对应一个分类
 *
 * <AUTHOR>
 * @date 2019-03-14
 */
public interface ResourcesCategoryMapper extends BaseMapper<ResourcesCategory> {
    /**
     * 根据资源分类编码、资源分类名称筛选
     */
    List<ResourcesCategory> queryResourcesCategoryByParam(@Param("name") String name);


//    @Query(value = "", nativeQuery = true)

    /**
     * 根据资源分类编码查询是否有数据
     */
    int findByCodeCount(@Param("code") String categoryCode);


    /**
     * 根据资源分类编码查询是否有数据,排除当前id
     */
    int findByCodeCountNotSelf(@Param("id") Long id);


    /**
     * 根据资源分类id查询是否有子分类
     */
    int findByChildCount(@Param("id") Long id);


    /**
     * 编辑资源分类
     */
    void updateResourcesCategory(ResourcesCategory ResourcesCategory);


    List<ResourcesCategory> selectAllNotDel();

    ResourcesCategory selectById(@Param("id") Long id);

    void insertCategory(ResourcesCategory pCategory);


    ResourcesCategory selectResourcesCategoryByName(@Param("id") String id, @Param("name") String name);
}
