package com.fotile.cmscenter.product.controller;

import com.fotile.cmscenter.product.pojo.dto.*;
import com.fotile.cmscenter.product.service.ShowProductService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 菜谱晒作品controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/product")
@Api(value = "菜谱晒作品接口", tags = {"菜谱晒作品操作接口"})
public class ShowProductController extends BaseController {
    @Autowired
    private ShowProductService service;

    @ApiOperation("推荐/批量推荐")
    @RequestMapping(value = "/recommend", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result recommend(@RequestBody RecommendInDto inDto) {
        Long recommend = service.recommend(inDto);
        if (recommend > 0) {
            return success("推荐成功");
        } else {
            return failure("推荐失败");
        }

    }

    @ApiOperation("隐藏/批量隐藏")
    @RequestMapping(value = "/conceal", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result conceal(@RequestBody ConceaInDto inDto) {
        Long conceal = service.conceal(inDto);
        if (conceal > 0) {
            return success("隐藏成功");
        } else {
            return failure("隐藏失败");
        }

    }

    @ApiOperation("根据id查询菜谱晒作品详情")
    @RequestMapping(value = "/queryShowProductById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ShowProductOutDto> queryShowProductById(@Valid QueryShowProductByIdInDto inDto) {

        return success(service.queryShowProductById(inDto));

    }

    @ApiOperation("菜谱晒作品分页查询")
    @RequestMapping(value = "/queryByParams", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<ShowProductOutDto> queryByParams(@Valid QueryByParamsInDto inDto) {

        return success(service.queryByParams(inDto));

    }

    @ApiOperation("取消隐藏/推荐")
    @RequestMapping(value = "/cancel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result cancel(@RequestBody @Valid CancelInDto inDto) {
        Long cancel = service.cancel(inDto.getId());
        if (cancel > 0) {
            return success("取消成功");
        } else {
            return failure("取消失败");
        }
    }
}
