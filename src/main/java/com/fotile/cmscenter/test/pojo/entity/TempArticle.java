package com.fotile.cmscenter.test.pojo.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName temp_article
 */
public class TempArticle implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * 
     */
    private Integer pid;

    /**
     * 属性
     */
    private String property;

    /**
     * 标题
     */
    private String title;

    /**
     * 标题颜色代码
     */
    private String titlecolour;

    /**
     * 副标题
     */
    private String subhead;

    /**
     * 副标题颜色代码
     */
    private String subheadcolour;

    /**
     * 链接URL
     */
    private String linkurl;

    /**
     * 是否使用转向链接
     */
    private Integer ifturntolink;

    /**
     * 作者
     */
    private String author;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * SEO标题
     */
    private String seotitle;

    /**
     * SEO关键词
     */
    private String seokeyword;

    /**
     * SEO页面描述
     */
    private String seodescribe;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 内容
     */
    private String contents;

    /**
     * 图片名称
     */
    private String picture;

    /**
     * 图片高
     */
    private String high;

    /**
     * 图片宽
     */
    private String wide;

    /**
     * 是否审核
     */
    private Integer audit;

    /**
     * 是否同时发布手机端
     */
    private Integer releasecellphone;

    /**
     * 是否允许评论
     */
    private Integer allowdiscuss;

    /**
     * 
     */
    private Integer isvalid;

    /**
     * 
     */
    private Integer sysbrandid;

    /**
     * 
     */
    private Date createdate;

    /**
     * 
     */
    private Date updatedate;

    /**
     * 
     */
    private String createloginid;

    /**
     * 
     */
    private String updateuserid;

    /**
     * 点赞
     */
    private Integer praise;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 
     */
    public Integer getPid() {
        return pid;
    }

    /**
     * 
     */
    public void setPid(Integer pid) {
        this.pid = pid;
    }

    /**
     * 属性
     */
    public String getProperty() {
        return property;
    }

    /**
     * 属性
     */
    public void setProperty(String property) {
        this.property = property;
    }

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 标题颜色代码
     */
    public String getTitlecolour() {
        return titlecolour;
    }

    /**
     * 标题颜色代码
     */
    public void setTitlecolour(String titlecolour) {
        this.titlecolour = titlecolour;
    }

    /**
     * 副标题
     */
    public String getSubhead() {
        return subhead;
    }

    /**
     * 副标题
     */
    public void setSubhead(String subhead) {
        this.subhead = subhead;
    }

    /**
     * 副标题颜色代码
     */
    public String getSubheadcolour() {
        return subheadcolour;
    }

    /**
     * 副标题颜色代码
     */
    public void setSubheadcolour(String subheadcolour) {
        this.subheadcolour = subheadcolour;
    }

    /**
     * 链接URL
     */
    public String getLinkurl() {
        return linkurl;
    }

    /**
     * 链接URL
     */
    public void setLinkurl(String linkurl) {
        this.linkurl = linkurl;
    }

    /**
     * 是否使用转向链接
     */
    public Integer getIfturntolink() {
        return ifturntolink;
    }

    /**
     * 是否使用转向链接
     */
    public void setIfturntolink(Integer ifturntolink) {
        this.ifturntolink = ifturntolink;
    }

    /**
     * 作者
     */
    public String getAuthor() {
        return author;
    }

    /**
     * 作者
     */
    public void setAuthor(String author) {
        this.author = author;
    }

    /**
     * 关键词
     */
    public String getKeyword() {
        return keyword;
    }

    /**
     * 关键词
     */
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    /**
     * SEO标题
     */
    public String getSeotitle() {
        return seotitle;
    }

    /**
     * SEO标题
     */
    public void setSeotitle(String seotitle) {
        this.seotitle = seotitle;
    }

    /**
     * SEO关键词
     */
    public String getSeokeyword() {
        return seokeyword;
    }

    /**
     * SEO关键词
     */
    public void setSeokeyword(String seokeyword) {
        this.seokeyword = seokeyword;
    }

    /**
     * SEO页面描述
     */
    public String getSeodescribe() {
        return seodescribe;
    }

    /**
     * SEO页面描述
     */
    public void setSeodescribe(String seodescribe) {
        this.seodescribe = seodescribe;
    }

    /**
     * 摘要
     */
    public String getSummary() {
        return summary;
    }

    /**
     * 摘要
     */
    public void setSummary(String summary) {
        this.summary = summary;
    }

    /**
     * 内容
     */
    public String getContents() {
        return contents;
    }

    /**
     * 内容
     */
    public void setContents(String contents) {
        this.contents = contents;
    }

    /**
     * 图片名称
     */
    public String getPicture() {
        return picture;
    }

    /**
     * 图片名称
     */
    public void setPicture(String picture) {
        this.picture = picture;
    }

    /**
     * 图片高
     */
    public String getHigh() {
        return high;
    }

    /**
     * 图片高
     */
    public void setHigh(String high) {
        this.high = high;
    }

    /**
     * 图片宽
     */
    public String getWide() {
        return wide;
    }

    /**
     * 图片宽
     */
    public void setWide(String wide) {
        this.wide = wide;
    }

    /**
     * 是否审核
     */
    public Integer getAudit() {
        return audit;
    }

    /**
     * 是否审核
     */
    public void setAudit(Integer audit) {
        this.audit = audit;
    }

    /**
     * 是否同时发布手机端
     */
    public Integer getReleasecellphone() {
        return releasecellphone;
    }

    /**
     * 是否同时发布手机端
     */
    public void setReleasecellphone(Integer releasecellphone) {
        this.releasecellphone = releasecellphone;
    }

    /**
     * 是否允许评论
     */
    public Integer getAllowdiscuss() {
        return allowdiscuss;
    }

    /**
     * 是否允许评论
     */
    public void setAllowdiscuss(Integer allowdiscuss) {
        this.allowdiscuss = allowdiscuss;
    }

    /**
     * 
     */
    public Integer getIsvalid() {
        return isvalid;
    }

    /**
     * 
     */
    public void setIsvalid(Integer isvalid) {
        this.isvalid = isvalid;
    }

    /**
     * 
     */
    public Integer getSysbrandid() {
        return sysbrandid;
    }

    /**
     * 
     */
    public void setSysbrandid(Integer sysbrandid) {
        this.sysbrandid = sysbrandid;
    }

    /**
     * 
     */
    public Date getCreatedate() {
        return createdate;
    }

    /**
     * 
     */
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    /**
     * 
     */
    public Date getUpdatedate() {
        return updatedate;
    }

    /**
     * 
     */
    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    /**
     * 
     */
    public String getCreateloginid() {
        return createloginid;
    }

    /**
     * 
     */
    public void setCreateloginid(String createloginid) {
        this.createloginid = createloginid;
    }

    /**
     * 
     */
    public String getUpdateuserid() {
        return updateuserid;
    }

    /**
     * 
     */
    public void setUpdateuserid(String updateuserid) {
        this.updateuserid = updateuserid;
    }

    /**
     * 点赞
     */
    public Integer getPraise() {
        return praise;
    }

    /**
     * 点赞
     */
    public void setPraise(Integer praise) {
        this.praise = praise;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TempArticle other = (TempArticle) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPid() == null ? other.getPid() == null : this.getPid().equals(other.getPid()))
            && (this.getProperty() == null ? other.getProperty() == null : this.getProperty().equals(other.getProperty()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getTitlecolour() == null ? other.getTitlecolour() == null : this.getTitlecolour().equals(other.getTitlecolour()))
            && (this.getSubhead() == null ? other.getSubhead() == null : this.getSubhead().equals(other.getSubhead()))
            && (this.getSubheadcolour() == null ? other.getSubheadcolour() == null : this.getSubheadcolour().equals(other.getSubheadcolour()))
            && (this.getLinkurl() == null ? other.getLinkurl() == null : this.getLinkurl().equals(other.getLinkurl()))
            && (this.getIfturntolink() == null ? other.getIfturntolink() == null : this.getIfturntolink().equals(other.getIfturntolink()))
            && (this.getAuthor() == null ? other.getAuthor() == null : this.getAuthor().equals(other.getAuthor()))
            && (this.getKeyword() == null ? other.getKeyword() == null : this.getKeyword().equals(other.getKeyword()))
            && (this.getSeotitle() == null ? other.getSeotitle() == null : this.getSeotitle().equals(other.getSeotitle()))
            && (this.getSeokeyword() == null ? other.getSeokeyword() == null : this.getSeokeyword().equals(other.getSeokeyword()))
            && (this.getSeodescribe() == null ? other.getSeodescribe() == null : this.getSeodescribe().equals(other.getSeodescribe()))
            && (this.getSummary() == null ? other.getSummary() == null : this.getSummary().equals(other.getSummary()))
            && (this.getContents() == null ? other.getContents() == null : this.getContents().equals(other.getContents()))
            && (this.getPicture() == null ? other.getPicture() == null : this.getPicture().equals(other.getPicture()))
            && (this.getHigh() == null ? other.getHigh() == null : this.getHigh().equals(other.getHigh()))
            && (this.getWide() == null ? other.getWide() == null : this.getWide().equals(other.getWide()))
            && (this.getAudit() == null ? other.getAudit() == null : this.getAudit().equals(other.getAudit()))
            && (this.getReleasecellphone() == null ? other.getReleasecellphone() == null : this.getReleasecellphone().equals(other.getReleasecellphone()))
            && (this.getAllowdiscuss() == null ? other.getAllowdiscuss() == null : this.getAllowdiscuss().equals(other.getAllowdiscuss()))
            && (this.getIsvalid() == null ? other.getIsvalid() == null : this.getIsvalid().equals(other.getIsvalid()))
            && (this.getSysbrandid() == null ? other.getSysbrandid() == null : this.getSysbrandid().equals(other.getSysbrandid()))
            && (this.getCreatedate() == null ? other.getCreatedate() == null : this.getCreatedate().equals(other.getCreatedate()))
            && (this.getUpdatedate() == null ? other.getUpdatedate() == null : this.getUpdatedate().equals(other.getUpdatedate()))
            && (this.getCreateloginid() == null ? other.getCreateloginid() == null : this.getCreateloginid().equals(other.getCreateloginid()))
            && (this.getUpdateuserid() == null ? other.getUpdateuserid() == null : this.getUpdateuserid().equals(other.getUpdateuserid()))
            && (this.getPraise() == null ? other.getPraise() == null : this.getPraise().equals(other.getPraise()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPid() == null) ? 0 : getPid().hashCode());
        result = prime * result + ((getProperty() == null) ? 0 : getProperty().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getTitlecolour() == null) ? 0 : getTitlecolour().hashCode());
        result = prime * result + ((getSubhead() == null) ? 0 : getSubhead().hashCode());
        result = prime * result + ((getSubheadcolour() == null) ? 0 : getSubheadcolour().hashCode());
        result = prime * result + ((getLinkurl() == null) ? 0 : getLinkurl().hashCode());
        result = prime * result + ((getIfturntolink() == null) ? 0 : getIfturntolink().hashCode());
        result = prime * result + ((getAuthor() == null) ? 0 : getAuthor().hashCode());
        result = prime * result + ((getKeyword() == null) ? 0 : getKeyword().hashCode());
        result = prime * result + ((getSeotitle() == null) ? 0 : getSeotitle().hashCode());
        result = prime * result + ((getSeokeyword() == null) ? 0 : getSeokeyword().hashCode());
        result = prime * result + ((getSeodescribe() == null) ? 0 : getSeodescribe().hashCode());
        result = prime * result + ((getSummary() == null) ? 0 : getSummary().hashCode());
        result = prime * result + ((getContents() == null) ? 0 : getContents().hashCode());
        result = prime * result + ((getPicture() == null) ? 0 : getPicture().hashCode());
        result = prime * result + ((getHigh() == null) ? 0 : getHigh().hashCode());
        result = prime * result + ((getWide() == null) ? 0 : getWide().hashCode());
        result = prime * result + ((getAudit() == null) ? 0 : getAudit().hashCode());
        result = prime * result + ((getReleasecellphone() == null) ? 0 : getReleasecellphone().hashCode());
        result = prime * result + ((getAllowdiscuss() == null) ? 0 : getAllowdiscuss().hashCode());
        result = prime * result + ((getIsvalid() == null) ? 0 : getIsvalid().hashCode());
        result = prime * result + ((getSysbrandid() == null) ? 0 : getSysbrandid().hashCode());
        result = prime * result + ((getCreatedate() == null) ? 0 : getCreatedate().hashCode());
        result = prime * result + ((getUpdatedate() == null) ? 0 : getUpdatedate().hashCode());
        result = prime * result + ((getCreateloginid() == null) ? 0 : getCreateloginid().hashCode());
        result = prime * result + ((getUpdateuserid() == null) ? 0 : getUpdateuserid().hashCode());
        result = prime * result + ((getPraise() == null) ? 0 : getPraise().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", pid=").append(pid);
        sb.append(", property=").append(property);
        sb.append(", title=").append(title);
        sb.append(", titlecolour=").append(titlecolour);
        sb.append(", subhead=").append(subhead);
        sb.append(", subheadcolour=").append(subheadcolour);
        sb.append(", linkurl=").append(linkurl);
        sb.append(", ifturntolink=").append(ifturntolink);
        sb.append(", author=").append(author);
        sb.append(", keyword=").append(keyword);
        sb.append(", seotitle=").append(seotitle);
        sb.append(", seokeyword=").append(seokeyword);
        sb.append(", seodescribe=").append(seodescribe);
        sb.append(", summary=").append(summary);
        sb.append(", contents=").append(contents);
        sb.append(", picture=").append(picture);
        sb.append(", high=").append(high);
        sb.append(", wide=").append(wide);
        sb.append(", audit=").append(audit);
        sb.append(", releasecellphone=").append(releasecellphone);
        sb.append(", allowdiscuss=").append(allowdiscuss);
        sb.append(", isvalid=").append(isvalid);
        sb.append(", sysbrandid=").append(sysbrandid);
        sb.append(", createdate=").append(createdate);
        sb.append(", updatedate=").append(updatedate);
        sb.append(", createloginid=").append(createloginid);
        sb.append(", updateuserid=").append(updateuserid);
        sb.append(", praise=").append(praise);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}