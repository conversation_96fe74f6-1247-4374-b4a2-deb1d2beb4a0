package com.fotile.cmscenter.declareAssets.controller;

import com.fotile.cmscenter.declareAssets.pojo.dto.QueryAssetsByPageInDto;
import com.fotile.cmscenter.declareAssets.pojo.dto.UpdateAssetsStatusInDto;
import com.fotile.cmscenter.declareAssets.pojo.entity.DeclareAssets;
import com.fotile.cmscenter.declareAssets.service.DeclareAssetsService;
import com.fotile.framework.web.BaseController;
import com.fotile.framework.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 资产申报表(DeclareAssets)表控制层
 */
 
@RestController
@RequestMapping("/api/tDeclareAssets")
public class DeclareAssetsController extends BaseController{
    /**
     * 服务对象
     */
    @Autowired
    private DeclareAssetsService declareAssetsService;

    /**
     * 分页查询
     */
    @RequestMapping(value = "/queryByPage", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> queryByPage(@Valid QueryAssetsByPageInDto inDto) {
        return success(this.declareAssetsService.queryByPage(inDto));
    }

    /**
     * 通过主键查询单条数据
     */
    @RequestMapping(value = "/queryById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> queryById(@Valid Long id) {
        return success(this.declareAssetsService.queryById(id));
    }

    /**
     * 新增数据
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> add(@RequestBody DeclareAssets declareAssets) {
        return success(this.declareAssetsService.insert(declareAssets));
    }

    /**
     * 编辑数据
     */
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> updateStatus(@RequestBody UpdateAssetsStatusInDto inDto) {
        return success(this.declareAssetsService.updateStatus(inDto));
    }

    /**
     * 删除数据
     */
    @RequestMapping(value = "/deleteById", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> deleteById(@RequestBody Long id) {
        return success(this.declareAssetsService.deleteById(id));
    }

}

