package com.fotile.paycenter.client;

import com.fotile.framework.web.Result;
import com.fotile.paycenter.client.pojo.dto.OrderPayFlowRecordNotifyDTO;
import com.fotile.paycenter.paymentHistory.pojo.vo.ChinaumsPayQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient("oms-center")
public interface OmsClient {
    @PostMapping(value = "/api/open/orderPayFlowRecord/notify",consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notify(@RequestBody ChinaumsPayQueryVO chinaumsCreateOrderResponseDTO );


}
