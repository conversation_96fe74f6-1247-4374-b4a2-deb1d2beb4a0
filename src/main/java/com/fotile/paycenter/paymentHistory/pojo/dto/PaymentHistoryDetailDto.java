package com.fotile.paycenter.paymentHistory.pojo.dto;

import com.fotile.paycenter.paymentHistory.pojo.PaymentHistoryDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentHistoryDetailDto extends PaymentHistoryDetail {
    @ApiModelProperty(value="支付类型")
    private String paymentType;

    @ApiModelProperty(value="实际支付价格")
    private BigDecimal paymentPrice;

    @ApiModelProperty(value="抵扣积分数")
    private Integer pointsNum;
}
