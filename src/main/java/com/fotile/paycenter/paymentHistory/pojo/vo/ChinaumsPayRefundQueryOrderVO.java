package com.fotile.paycenter.paymentHistory.pojo.vo;

import com.fotile.paycenter.common.pojo.vo.PayRefundQueryOrderVO;
import lombok.Data;

@Data
public class ChinaumsPayRefundQueryOrderVO extends PayRefundQueryOrderVO {
    private String errCode;

    private String errMsg;
    /**
     * 商户号
     */
    private String mid;
    /**
     * 终端号
     */
    private String tid;
    /**
     * 业务类型 MINIDEFAULT
     */
    private String instMid;
    /**
     * 附加数据
     */
    private String attachedData;
    /**
     * 支付银行信息
     */
    private String bankCardNo;
    /**
     * 资金渠道
     */
    private String billFunds;
    /**
     * 资金渠道描述
     */
    private String billFundsDesc;
    /**
     * 买家ID
     */
    private String buyerId;
    /**
     * 买家用户名
     */
    private String buyerUsername;
    /**
     * 实付金额
     */
    private String buyerPayAmount;
    /**
     * 订单金额，单位分
     */
    private String totalAmount;
    /**
     * 开票金额
     */
    private String invoiceAmount;
    /**
     * 商户订单号
     */
    private String merOrderId;
    /**
     * 	支付时间 格式yyyy-MM-dd HH:mm:ss
     */
    private String payTime;
    /**
     * 实收金额
     */
    private String receiptAmount;
    /**
     * 支付银行卡参考号
     */
    private String refId;
    /**
     * 	退款金额 退货交易
     */
    private String refundAmount;
    /**
     * 退款说明 退货交易
     */
    private String refundDesc;
    /**
     * 系统交易流水号
     */
    private String seqId;
    /**
     * 结算日期 格式yyyy-MM-dd
     */
    private String settleDate;
    /**
     * 	订单状态 取值说明
     */
    private String status;
    /**
     * 买家子ID
     */
    private String subBuyerId;
    /**
     * 渠道订单号
     */
    private String targetOrderId;
    /**
     * 支付渠道 取值说明
     */
    private String targetSys;
    /**
     * 签名
     */
    private String sign;
    /**
     * 商户出资优惠金额
     */
    private String couponMerchantContribute;
    /**
     * 其他出资优惠金额
     */
    private String couponOtherContribute;
    /**
     * 微信活动ID
     */
    private String activityIds;
    /**
     * 退货渠道订单号
     */
    private String refundTargetOrderId;
    /**
     * 退货时间
     */
    private String refundPayTime;
    /**
     * 退货时间
     */
    private String refundSettleDate;
    /**
     * 订单详情
     */
    private String orderDesc;
    /**
     * 	订单创建时间
     */
    private String createTime;
    /**
     * 		商户UUID
     */
    private String mchntUuid;
    /**
     * 转接系统
     */
    private String connectSys;
    /**
     * 商户所属分支机构代码
     */
    private String subInst;
    /**
     * 	联盟优惠金额
     */
    private String yxlmAmount;
    /**
     * 退货外部订单号
     */
    private String refundExtOrderId;
    /**
     * 	商品交易单号
     */
    private String goodsTradeNo;
    /**
     * 外部订单号
     */
    private String extOrderId;
    /**
     * 	担保交易状态
     */
    private String secureStatus;
    /**
     * 	担保完成金额
     */
    private String completeAmount;
    /**
     * 退货订单号
     */
    private String refundOrderId;
    /**
     * 渠道优惠金额 单位：分
     */
    private String couponAmount;
    /**
     * 银行信息
     */
    private String bankInfo;
    /**
     * 实付部分退款金额
     */
    private String refundInvoiceAmount;
    /**
     * 商户实退金额
     */
    private String sendBackAmount;

    /**
     *退款渠道描述
     */
    private String refundFundsDesc;
    /**
     * 退款渠道列表 示例：支付宝余额:33\
     */
    private String refundFunds;

    private String refundStatus;
    /**
     * 商户出资优惠金额 目前支持微信返回，其他渠道产品规划中
     */
    private String refundMerchantContribute;
    /**
     * 其他出资优惠金额 目前支持微信返回，其他渠道产品规划中
     */
    private String refundOtherContribute;
    private Integer falg = 0;
}
