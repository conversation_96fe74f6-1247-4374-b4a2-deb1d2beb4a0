package com.fotile.paycenter.fotilestyle.pojo;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fotile.framework.core.common.AuditingEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "支付履历明细表", description = "支付履历明细表")
@Table(name = "paycenter.payment_history_detail")
public class PaymenthistoryDto extends AuditingEntity {

    /**
     * 
     */
    private static final long serialVersionUID = 3367722863294513264L;

    @Column(name = "order_id", length = 50)
    @ApiModelProperty(value = "订单号ID")
    private String orderId;

    @Column(name = "payment_type", length = 2)
    @ApiModelProperty(value = "支付类型")
    private String paymentType;

    @Column(name = "payment_method", length = 2)
    @ApiModelProperty(value = "支付/退款方式")
    private String paymentMethod;

    @Column(name = "payment_date")
    @ApiModelProperty(value = "活动ID")
    private Date paymentDate;

    @Column(name = "original_price", length = 10)
    @ApiModelProperty(value = "订单原价")
    private BigDecimal originalPrice;

    @Column(name = "payment_price", length = 10)
    @ApiModelProperty(value = "实际支付价格")
    private BigDecimal paymentPrice;

    @Column(name = "points_price", length = 10)
    @ApiModelProperty(value = "积分抵扣价格")
    private BigDecimal pointsPrice;

    @Column(name = "points_num", length = 10)
    @ApiModelProperty(value = "抵扣积分数")
    private Long pointsNum;

    public BigDecimal getPointsPrice() {
        return pointsPrice;
    }

    public void setPointsPrice(BigDecimal pointsPrice) {
        this.pointsPrice = pointsPrice;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getPaymentPrice() {
        return paymentPrice;
    }

    public void setPaymentPrice(BigDecimal paymentPrice) {
        this.paymentPrice = paymentPrice;
    }

    public Long getPointsNum() {
        return pointsNum;
    }

    public void setPointsNum(Long pointsNum) {
        this.pointsNum = pointsNum;
    }

}
