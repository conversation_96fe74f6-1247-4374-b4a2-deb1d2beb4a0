package com.fotile.paycenter.fotilestyle.service;

import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fotile.paycenter.fotilestyle.dao.FotilestyleDao;
import com.fotile.paycenter.fotilestyle.pojo.PaymenthistoryDto;

@Service
public class FotilestyleService {

    @Autowired
    private FotilestyleDao fotilestyleDao;

    /**
     * 支付履历
     * @param paymentHistory
     */
    @Transactional
    public void insertPaymentHistory(PaymenthistoryDto paymentHistory) {
        Date dt = new Date();
        paymentHistory.setPaymentDate(dt);
        paymentHistory.setCreatedDate(dt);
        fotilestyleDao.insertPaymentHistory(paymentHistory);
    }
}
