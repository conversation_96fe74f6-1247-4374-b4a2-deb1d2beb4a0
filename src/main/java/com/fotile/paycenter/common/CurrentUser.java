package com.fotile.paycenter.common;

import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class CurrentUser {
    public String getCurrentUserId() {
        String user = "anonymousUser";
        try {
            SecurityContext context = SecurityContextHolder.getContext();
            user = context.getAuthentication().getPrincipal().toString();
        } catch (Exception var3) {
            user = "anonymousUser";
        }
        return user;
    }
}
