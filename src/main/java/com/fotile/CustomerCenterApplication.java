package com.fotile;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.fotile.customercenter.client.mq.FeginProducesChannel;
import com.fotile.customercenter.communitymember.mq.CommunityMemberExportConsumesChannel;
import com.fotile.customercenter.communitymember.mq.CommunityMemberExportProducesChannel;
import com.fotile.customercenter.communitymember.mq.CommunityMemberLogChannel;
import com.fotile.customercenter.communitymember.mq.CustomerMemberConsumesChannel;
import com.fotile.customercenter.customer.mq.UserAccountChannel;
import com.fotile.customercenter.customerExport.mq.RegisterCustomerExportConsumesChannel;
import com.fotile.customercenter.customerExport.mq.RegisterCustomerExportProducesChannel;
import com.fotile.customercenter.customerlogout.mq.AuditLogOutApply;
import com.fotile.customercenter.customerlogout.mq.AuditLogOutApplyMember;
import com.fotile.customercenter.drainage.mq.DrainageInterestsChannel;
import com.fotile.customercenter.drainage.mq.PointTimesChannel;
import com.fotile.customercenter.feedback.mq.ScheduleMsgChannel;
import com.fotile.customercenter.fission.mq.ActivityClockRedPacketChannel;
import com.fotile.customercenter.fission.mq.CustomerExamAddPointChannel;
import com.fotile.customercenter.fission.mq.CustomerSubscribeMsgChannel;
import com.fotile.customercenter.fotilestyle.mq.FotilestyleChannel;
import com.fotile.customercenter.qywx.mq.QywxCluesProducesChannel;
import com.fotile.customercenter.qywx.mq.QywxTagConfigConsumesChannel;
import com.fotile.customercenter.qywx.mq.QywxTagConfigProducesChannel;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"org.keycloak", "com.fotile"}, exclude = {DruidDataSourceAutoConfigure.class})
@EnableDiscoveryClient
@MapperScan(basePackages = {"com.fotile.**.dao","com.fotile.customercenter.*.mapper"})
//允许使用 spring cache
@EnableCaching
//引入mq kafka
@EnableBinding(value = {UserAccountChannel.class, ScheduleMsgChannel.class, DrainageInterestsChannel.class , PointTimesChannel.class,
        CommunityMemberExportConsumesChannel.class, CommunityMemberExportProducesChannel.class, FotilestyleChannel.class, CustomerMemberConsumesChannel.class,
        RegisterCustomerExportProducesChannel.class, RegisterCustomerExportConsumesChannel.class, QywxTagConfigConsumesChannel.class, QywxTagConfigProducesChannel.class,
        QywxCluesProducesChannel.class, AuditLogOutApply.class, AuditLogOutApplyMember.class, CommunityMemberLogChannel.class, ActivityClockRedPacketChannel.class,
        CustomerSubscribeMsgChannel.class, CustomerExamAddPointChannel.class, FeginProducesChannel.class
})
//有外部系统调用，需要配置
@EnableFeignClients(basePackages = "com.fotile")
//允许使用jetcache 在方法
@EnableMethodCache(basePackages = "com.fotile")
//允许使用jetcache 创建缓存
@EnableCreateCacheAnnotation
@EnableAsync
public class CustomerCenterApplication {


    public static void main(String[] args) {
        SpringApplication.run(CustomerCenterApplication.class, args);
    }

    @Bean
    public Executor executor() {
        Executor executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);
        return executor;
    }

}
