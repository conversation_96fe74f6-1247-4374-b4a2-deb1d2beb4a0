2023-06-21 13:44:52 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-66] <br>----------- xxl-job job execute start -----------<br>----------- Param:17,19,20,54
2023-06-21 13:44:59 [com.xxl.job.core.thread.JobThread#run]-[164]-[Thread-66] <br>----------- xxl-job job execute end(finish) -----------<br>----------- ReturnT:ReturnT [code=200, msg=, [作战地图]提报表单每月定期通知, 执行成功, 统计日期 = 2023-06-21, content=null]
2023-06-21 13:44:59 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[191]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2023-06-21 13:46:31 [com.xxl.job.core.thread.JobThread#run]-[175]-[Thread-66] <br>----------- JobThread toStop, stopReason:web container destroy and kill the job.
2023-06-21 13:46:31 [com.xxl.job.core.thread.JobThread#run]-[183]-[Thread-66] <br>----------- JobThread Exception:java.lang.InterruptedException
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.reportInterruptAfterWait(AbstractQueuedSynchronizer.java:2056)
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2133)
	at java.base/java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:458)
	at com.xxl.job.core.thread.JobThread.run(JobThread.java:112)
<br>----------- xxl-job job execute end(error) -----------
