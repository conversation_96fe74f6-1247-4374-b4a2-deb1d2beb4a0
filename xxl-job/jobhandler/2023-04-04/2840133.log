2023-04-04 16:45:29 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-51] <br>----------- xxl-job job execute start -----------<br>----------- Param:17,19,20,54
2023-04-04 16:45:56 [com.xxl.job.core.thread.JobThread#run]-[164]-[Thread-51] <br>----------- xxl-job job execute end(finish) -----------<br>----------- ReturnT:ReturnT [code=500, msg=, [作战地图]提报表单每月定期通知, 执行失败, 统计日期2023-04-03, errMsg = com.fotile.framework.web.BusinessException: 远程调用据岗位ids查询业务员相关信息的列表异常
	at com.fotile.datacenter.battlemap.common.job.service.BmDataFormNoticeJobService.listSalesmanFromDataFromByStationsRemote(BmDataFormNoticeJobService.java:247)
	at com.fotile.datacenter.battlemap.common.job.service.BmDataFormNoticeJobService.bmDataFormNoticeJobTask(BmDataFormNoticeJobService.java:91)
	at com.fotile.datacenter.battlemap.common.job.service.BmDataFormNoticeJobService$$FastClassBySpringCGLIB$$5c1bb67.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:47)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fotile.datacenter.battlemap.common.job.service.BmDataFormNoticeJobService$$EnhancerBySpringCGLIB$$20ec509c.bmDataFormNoticeJobTask(<generated>)
	at com.fotile.datacenter.battlemap.common.job.BmDataFormNoticeJobHandler.execute(BmDataFormNoticeJobHandler.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.xxl.job.core.handler.impl.MethodJobHandler.execute(MethodJobHandler.java:29)
	at com.xxl.job.core.thread.JobThread.run(JobThread.java:152)
, content=null]
2023-04-04 16:45:56 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[191]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
