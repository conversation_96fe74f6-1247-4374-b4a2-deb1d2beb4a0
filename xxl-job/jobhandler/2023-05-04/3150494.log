2023-05-04 11:58:43 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-49] <br>----------- xxl-job job execute start -----------<br>----------- Param:{
	"beginDate":"2023-04-25",
    "endDate":"2023-04-25",
	"mode":"all"
}
2023-05-04 12:00:15 [com.xxl.job.core.thread.JobThread#run]-[164]-[Thread-49] <br>----------- xxl-job job execute end(finish) -----------<br>----------- ReturnT:ReturnT [code=200, msg=同步百景图客体数据执行成功, 入参 = {
	"beginDate":"2023-04-25",
    "endDate":"2023-04-25",
	"mode":"all"
}, content=null]
2023-05-04 12:00:15 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[191]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
