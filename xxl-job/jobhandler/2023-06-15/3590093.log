2023-06-15 16:43:48 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-69] <br>----------- xxl-job job execute start -----------<br>----------- Param:{
  "beginDate": "2023-01-01",
  "pageSize": 2000,
  "mails": "<EMAIL>,<EMAIL>"
}
2023-06-15 16:45:24 [com.xxl.job.core.thread.JobThread#run]-[164]-[Thread-69] <br>----------- xxl-job job execute end(finish) -----------<br>----------- ReturnT:ReturnT [code=500, msg=[作战地图]门店选址记录每周通知, 执行失败, 统计日期范围[2023-01-01,2023-06-14], errMsg = org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 't1.is_empty_paving' in 'field list'
### The error may exist in file [D:\developerPro\Idea\Fotile\data\develop\data-center\target\classes\mapper\BattleMapSiteSearchDao.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             count(*)         FROM             datacenter.`bm_map_site_search` ss             inner join (                 SELECT                     t.*                 FROM                     (                     SELECT                         t1.store_address,                         t1.score,                         CASE WHEN t1.is_empty_paving = 1 THEN '有空铺' WHEN t1.is_empty_paving = 2 THEN '无空铺' ELSE NULL END as isEmptyPavingName,                         CASE WHEN t1.is_suitable = 1 THEN '非常合适' WHEN t1.is_suitable = 2 THEN '合适' WHEN t1.is_suitable = 3 THEN '不合适' ELSE NULL END as isSuitableName,                         t1.search_id,                         ( SELECT count(*) + 1 FROM datacenter.bm_map_site_search_result t2 WHERE t2.is_deleted = 0 and t2.search_id = t1.search_id AND t2.score > t1.score ) top                     FROM                         datacenter.bm_map_site_search_result t1                         where t1.is_deleted = 0                     ) t                 WHERE                     top <= 30             ) sr on sr.search_id = ss.id             LEFT JOIN usercenter.user_entity_extend uee ON uee.is_deleted = 0 and uee.user_entity_id = ss.oper_user_id             left join orgcenter.t_salesman s on s.is_deleted = 0 and uee.salesman_id = s.id             left join systemcenter.dic d on d.is_deleted = 0 and d.type_code = 'gw' and d.id = s.station             left join orgcenter.t_org o on o.is_deleted = 0 and o.type = 1 and o.id = s.company_id             left join orgcenter.t_company tc on tc.is_deleted = 0 and tc.org_id = o.id         WHERE             ss.oper_time >= ?             AND ss.oper_time <= ?
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 't1.is_empty_paving' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 't1.is_empty_paving' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy183.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy215.countBmMapSiteSearchDetail(Unknown Source)
	at com.fotile.datacenter.battlemap.common.dbservice.SixthDbCommonService.countBmMapSiteSearchDetail(SixthDbCommonService.java:74)
	at com.fotile.datacenter.battlemap.common.dbservice.SixthDbCommonService$$FastClassBySpringCGLIB$$3c5f2a41.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:47)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.fotile.datacenter.battlemap.common.dbservice.SixthDbCommonService$$EnhancerBySpringCGLIB$$7280b27b.countBmMapSiteSearchDetail(<generated>)
	at com.fotile.datacenter.battlemap.common.job.service.BmMapSiteSearchFormJobService.bmMapSiteSearchFormJobTask(BmMapSiteSearchFormJobService.java:69)
	at com.fotile.datacenter.battlemap.common.job.service.BmMapSiteSearchFormJobService$$FastClassBySpringCGLIB$$53719c1e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:47)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.fotile.datacenter.battlemap.common.job.service.BmMapSiteSearchFormJobService$$EnhancerBySpringCGLIB$$478b9a32.bmMapSiteSearchFormJobTask(<generated>)
	at com.fotile.datacenter.battlemap.common.job.BmMapSiteSearchFormJobHandler.execute(BmMapSiteSearchFormJobHandler.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.xxl.job.core.handler.impl.MethodJobHandler.execute(MethodJobHandler.java:29)
	at com.xxl.job.core.thread.JobThread.run(JobThread.java:152)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 't1.is_empty_paving' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor337.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy397.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:136)
	at jdk.internal.reflect.GeneratedMethodAccessor350.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy394.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 47 more
, content=null]
2023-06-15 16:45:24 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[191]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
