2023-05-09 09:47:01 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-45] <br>----------- xxl-job job execute start -----------<br>----------- Param:{
	"beginDate":"2023-04-25",
    "endDate":"2023-04-25",
	"mode":"day"
}
2023-05-09 09:47:05 [com.xxl.job.core.thread.JobThread#run]-[164]-[Thread-45] <br>----------- xxl-job job execute end(finish) -----------<br>----------- ReturnT:ReturnT [code=200, msg=同步百景图客体数据执行成功, 入参 = {
	"beginDate":"2023-04-25",
    "endDate":"2023-04-25",
	"mode":"day"
}, content=null]
2023-05-09 09:47:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[191]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
