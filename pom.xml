<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>service-public-pom</artifactId>
        <groupId>com.fotile</groupId>
        <version>3.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cdp-center</artifactId>

    <name>cdp-center</name>
    <!--<properties>-->
    <!--<elasticsearch.version>6.6.0</elasticsearch.version>-->
    <!--<java.version>1.8</java.version>-->
    <!--</properties>-->

    <properties>
        <elasticsearch>6.8.6</elasticsearch>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fotile</groupId>
            <artifactId>fcloud-data-common</artifactId>
            <version>3.1-SNAPSHOT</version>
        </dependency>

        <!--jetcache-->
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-springdata</artifactId>
        </dependency>
        <!-- kafka-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
            <version>3.0.3.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-airec</artifactId>
            <version>1.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!--发送报表数据至邮箱地址-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>

        <!-- 阿里Oss-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.28</version>
        </dependency>

    </dependencies>

<!--    <profiles>-->
<!--        <profile>-->
<!--            <id>nacos</id>-->
<!--            <dependencies>-->
<!--                <dependency>-->
<!--                    <groupId>com.alibaba.nacos</groupId>-->
<!--                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--                </dependency>-->
<!--            </dependencies>-->
<!--            <activation>-->
<!--                <activeByDefault>true</activeByDefault>-->
<!--            </activation>-->
<!--        </profile>-->
<!--        <profile>-->
<!--            <id>k8s</id>-->
<!--            <dependencies>-->
<!--                <dependency>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-starter-kubernetes-all</artifactId>-->
<!--                </dependency>-->
<!--            </dependencies>-->
<!--        </profile>-->
<!--    </profiles>-->


</project>
