<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>service-public-pom</artifactId>
        <groupId>com.fotile</groupId>
        <version>3.1-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>point-center</artifactId>
    <properties>
        <m2e.apt.activation>jdt_apt</m2e.apt.activation>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fotile</groupId>
            <artifactId>fcloud-data-common</artifactId>
            <version>3.1-SNAPSHOT</version>
        </dependency>

        <!--<dependency>
            <groupId>com.taobao.txc</groupId>
            <artifactId>txc-client</artifactId>
            <version>${gts.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.txc</groupId>
            <artifactId>txc-client-springcloud</artifactId>
            <version>${gts.sdk.version}</version>
        </dependency>-->

        <!--seata 1.8.0-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.0.M4</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>

        <!-- 阿里Oss-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
        </dependency>

    </dependencies>

    <!--<profiles>
        <profile>
            <id>nacos</id>
            <dependencies>
                <dependency>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>k8s</id>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-kubernetes-all</artifactId>
                </dependency>
            </dependencies>
        </profile>
    </profiles>-->

</project>